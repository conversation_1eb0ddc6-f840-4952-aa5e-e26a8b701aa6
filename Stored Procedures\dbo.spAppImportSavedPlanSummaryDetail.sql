-- ----------------------------------------------------------------------------------------------------------------------                      
-- PROCEDURE NAME: [dbo].[spAppImportSavedPlanSummaryDetail]            
--                      
-- TYPE: SAME                      
--                      
-- AUTHOR: <PERSON><PERSON><PERSON>                     
--                      
-- CREATED DATE:2023-11-21                     
--                      
-- DESCRIPTION: Procedure responsible for insert and update from Plan Summary Detail Import                  
--                      
--                      
-- PARAMETERS:                      
-- Input:                      
--     @ImportPlanSummaryDetailDataTableInput
---    @UserID
-- TABLES:                       
-- Read:                      
--                          
--                         
-- Write:                      
--                      
-- VIEWS:                      
--                      
-- FUNCTIONS:                      
--                        
-- STORED PROCS:                      
--                        
-- $HISTORY                       
-- ----------------------------------------------------------------------------------------------------------------------                      
-- DATE            VERSION   CHANGES MADE						      DEVELOPER                        
-- ----------------------------------------------------------------------------------------------------------------------                      
-- 2023-Nov       1     Initial Version									Latoya Garvey       
-- 2024-Sep       2     Added new columns								Surya Murthy  
-- 2024-Dec       3     Bit logic modifed								Surya Murthy  
-- 2025-FEB-28    4     Removed Bit logic for IsDelegated				Vikrant Bagal
-- ---------------------------------------------------------------------------------------------------------------------- 


CREATE PROCEDURE [dbo].[spAppImportSavedPlanSummaryDetail]
(
	@ImportSavedPlanSummaryDetailInput ImportSavedPlanSummaryDetailTypeNew READONLY,
	@UserID VARCHAR(7)
)
AS
BEGIN
      SET NOCOUNT ON;
    BEGIN TRY
	BEGIN TRANSACTION
	DECLARE @LastUpdatedDatetime DATETIME = GETDATE()

	   DELETE FROM [dbo].[SavedPlanSummaryDetail]  WHERE 
	   EXISTS 
	   (SELECT p.ForecastID FROM @ImportSavedPlanSummaryDetailInput p WHERE p.ForecastID = SavedPlanSummaryDetail.ForecastID  )

		INSERT INTO [dbo].[SavedPlanSummaryDetail]
		(
			ForecastID,
			Nickname,
			Notes,			
			Honor,
			VeteranBrandUSAA,
			HMOPOSDental,
			SubRegion,
			DSNPSubType,
			TargetedSegment,
			IsPassive,
			RRGiveback,
			IsRiskPlan,
			IsDelegated,
			LastUpdatedByID,
			LastUpdatedDatetime
		)
		 SELECT 
			   ForecastID,
               Nickname,
               Notes,			   
			   IIF(Honor='Yes',1,0),
			   IIF(VeteranBrandUSAA='Yes',1,0), 
			   IIF(HMOPOSDental='Yes',1,0), 
			   SubRegion,
				DSNPSubType,
				TargetedSegment,
				IIF(IsPassive='Yes',1,0),
				IIF(RRGiveback='Yes',1,0),	
				IIF(IsRiskPlan='Yes',1,0),
				IsDelegated,				
			   @UserID,
			   @LastUpdatedDatetime
        FROM @ImportSavedPlanSummaryDetailInput 

        COMMIT TRANSACTION 
    END TRY
    BEGIN CATCH

        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;
        DECLARE @ErrorException NVARCHAR(4000);
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
                @currentdate DATETIME = GETDATE();

        SELECT @ErrorMessage = ERROR_MESSAGE();
        SELECT @ErrorSeverity = ERROR_SEVERITY();
        SELECT @ErrorState = ERROR_STATE();

        RAISERROR(   
			   @ErrorMessage,  -- Message text.  
               @ErrorSeverity, -- Severity.  
               @ErrorState     -- State.  
        );

		ROLLBACK TRANSACTION;
		       ---Insert into app log for logging error------------------
        EXEC dbo.spAppAddLogEntry @log_date = @currentdate,
                                  @log_thread = '',
                                  @log_level = 'ERROR',
                                  @log_source = @errSrc,
                                  @log_message = @ErrorMessage,
                                  @exception = @ErrorException,
                                  @USER = @UserID

       END CATCH;

END;
GO
