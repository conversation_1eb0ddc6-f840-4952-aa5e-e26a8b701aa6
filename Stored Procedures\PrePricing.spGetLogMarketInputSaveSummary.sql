SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetMarketInputSaveSummary]
--    
-- AUTHOR: Surya <PERSON>rthy 
--    
-- CREATED DATE: 2025-Jan-7   
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Market Input Save Summary  
--    
-- PARAMETERS:    
-- Input: 
-- @DaysBack - Optional parameter to limit dataset to last 'x' days of data.
-- @UserID - Optional parameter to limit dataset to particular user.
   
-- TABLES:   
--  
 
-- Read:    
-- [PrePricing].[LogMarketInputSave] 

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-Jan-7		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetLogMarketInputSaveSummary]
(@DaysBack INT=NULL,
 @UserID VARCHAR(200)=NULL
)
AS    
BEGIN    
	SET NOCOUNT ON;
	SELECT 
	LogID,
	LogDate,
	UserID,
	UpdateSource, 
	(select count(value) FROM STRING_SPLIT(PlanList,',')) PlanCount,
	(select count(value) FROM STRING_SPLIT(BenefitList,',')) BenefitCount,
	InputValue,
	InputType,
	CostShareType,
	Notes
	FROM [PrePricing].[LogMarketInputSave] WITH (NOLOCK)
	WHERE 
	(LogDate >= DATEADD(DAY, @DaysBack * -1, GETDATE()) OR @DaysBack IS NULL)
	AND
	(UserID = @UserID OR @UserID IS null)
	
END
GO
