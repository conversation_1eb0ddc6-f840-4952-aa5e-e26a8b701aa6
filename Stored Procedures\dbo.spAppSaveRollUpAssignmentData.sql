SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  Pooja Dahiya     
-- Create date: 22-11-2017
-- Description:  Get Scenario Setup Service Area & Revenue  data
--      
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-June-19  1		  Initial version.											Kritika Singh
-- 2018-Nov-09   2        Rollup validation											Pooja Dahiya
-- 2018-Nov-29   3		  Updated rollup message							        Kritika Singh
-- 2018-Nov-29   4        Excluding new counties from crosswalk validation			Pooja Dahiya
-- 2018-Dec-19	 5		  Updated Error message and included logging exception	    Kritika Singh
-- 2019-Jan-16	 6		  Messaging												    Deepali Mittal 
-- 2020-feb-24   7        Added new parameter isDelete                              Srikanth Reddy
--2020-Dec-28    8		 Added NOLOCK												Mahendran Chinnaiah
-- ----------------------------------------------------------------------------------------------------------------------        
         
CREATE PROCEDURE [dbo].[spAppSaveRollUpAssignmentData]
  @ForecastID INT ,
  @RollUpIdList varchar(max),
  @LastUpdateByID varchar(7),
  @IsDelete BIT,
  @MessageFromBackend NVARCHAR(500) OUTPUT,
  @Result BIT OUT,
  @CheckValidation BIT
AS
   BEGIN     
			
    BEGIN TRANSACTION;
    BEGIN TRY 
    
     IF OBJECT_ID('tempdb..#tmpMasterForecastDetails', 'U') IS NOT NULL
	 DROP TABLE #tmpMasterForecastDetails; 
       CREATE TABLE #tmpMasterForecastDetails(RollupID smallint ,RollupName varchar(max),ForecastID INT,FromPlanInfoID smallint,SSStateCountyCD char(5),MapID int)
       INSERT INTO #tmpMasterForecastDetails
       SELECT DISTINCT srinfo.RollupID ,srinfo.RollupName, sfs.ForecastID, spc.FromPlanInfoID,spc.SSStateCountyCD ,spc.MapID
       FROM SavedRollupInfo srinfo WITH(NOLOCK) 
	   INNER JOIN dbo.SavedRollupForecastMap sfs WITH(NOLOCK) on srinfo.RollupID=sfs.RollupID
	   INNER JOIN dbo.SavedForecastSetup sfp WITH(NOLOCK) on sfs.ForecastID=sfp.ForecastID
	   INNER JOIN dbo.SavedProjectedCrosswalk spc WITH(NOLOCK) on sfp.PlanInfoID=spc.PlanInfoID and ISNULL(sfp.ServiceAreaOptionID,'')=spc.ServiceAreaOptionID
	   WHERE  RollupName in(select value from dbo.fnStringSplit(@RollUpIdList,',')) and spc.FromPlanInfoID<>0
    
	 IF OBJECT_ID('tempdb..#tmpSelectedForecastDetails', 'U') IS NOT NULL
	 DROP TABLE #tmpSelectedForecastDetails; 
	 CREATE TABLE #tmpSelectedForecastDetails(ForecastID INT,FromPlanInfoID smallint,SSStateCountyCD char(5),MapID int)
	 INSERT INTO #tmpSelectedForecastDetails
	 SELECT DISTINCT f.ForecastID ,FromPlanInfoID,SSStateCountyCD ,c.MapID
	 FROM SavedProjectedCrosswalk c WITH(NOLOCK)
	 INNER JOIN SavedForecastSetup f WITH(NOLOCK) on f.PlanInfoID=c.PlanInfoID and c.ServiceAreaOptionID=ISNULL(f.ServiceAreaOptionID,'')
	 INNER JOIN SavedPlanInfo p WITH(NOLOCK) on f.PlanInfoID=p.PlanInfoID
	 WHERE ForecastID=@ForecastID and c.FromPlanInfoID<>0
		
		 IF OBJECT_ID('tempdb..#tmpForecastsViolatingRollupValidation', 'U') IS NOT NULL
			DROP TABLE #tmpForecastsViolatingRollupValidation; 
			CREATE TABLE #tmpForecastsViolatingRollupValidation(ForecastID INT, RollupID smallint ,RollupName varchar(max),MapID int, isMultipleScenaro bit)
			INSERT INTO #tmpForecastsViolatingRollupValidation
			SELECT DISTINCT t.ForecastID,t.RollupID,t.RollupName ,t.MapID,0
			FROM #tmpMasterForecastDetails t 
			INNER JOIN #tmpSelectedForecastDetails t2
			on cast(t.FromPlanInfoID as varchar(30))+'-'+t.SSStateCountyCD = cast(t2.FromPlanInfoID as varchar(30))+'-'+t2.SSStateCountyCD
			
			DELETE FROM #tmpForecastsViolatingRollupValidation
			WHERE MapID in(SELECT MapID FROM #tmpSelectedForecastDetails) and isMultipleScenaro=0
			--segmented and cms exception plans share same mapid, these can have same rollup assigned. So, they dont make it to the violation list

			Declare @rollupist1 varchar(max)
			Declare @forecastName varchar(max)
			Declare @originalScenario varchar(max)
			DECLARE @ExistingForecastID varchar(max)

			DECLARE @PlanInfoID int
			SET @PlanInfoID= ( select p.PlanInfoID from SavedForecastSetup f WITH(NOLOCK) inner join SavedPlanInfo p WITH(NOLOCK)
			on p.PlanInfoID=f.PlanInfoID where f.ForecastID=@ForecastID)

			INSERT INTO #tmpForecastsViolatingRollupValidation
			SELECT DISTINCT f.ForecastID,r.RollupID,r.RollupName ,0,1
			FROM SavedRollupForecastMap m WITH(NOLOCK) 
			inner join SavedForecastSetup f WITH(NOLOCK)
			on m.ForecastID=f.ForecastID
			inner join SavedPlanInfo p WITH(NOLOCK)
			on p.PlanInfoID=f.PlanInfoID
			inner join SavedRollupInfo r WITH(NOLOCK)
			on r.RollupID=m.RollupID
			where p.PlanInfoID=@PlanInfoID
			and r.RollupName in(select value from dbo.fnStringSplit(@RollUpIdList,',')) 						
			and f.ForecastID<>@ForecastID
			SET @rollupist1 =(select  ISNULL(STUFF((SELECT DISTINCT ', ' +a.RollupName
			from #tmpForecastsViolatingRollupValidation a
			FOR XML PATH('')), 1, 1, ''), ''))

			select @forecastName= sp.CPS+':'+cast(ss.ScenarioNbr as varchar(max))+':'+ss.ScenarioName
			from SavedPlanInfo sp WITH(NOLOCK) 
			inner join  dbo.SavedForecastSetup ss WITH(NOLOCK) 
			on sp.PlanInfoID=ss.PlanInfoID 
			where ss.ForecastID=@ForecastID
		
			SET @originalScenario=(select ISNULL(STUFF((SELECT DISTINCT ', ' + sp.CPS+':'+cast(ss.ScenarioNbr as varchar(max))+':'+ss.ScenarioName
			from SavedPlanInfo sp WITH(NOLOCK) 
			inner join  dbo.SavedForecastSetup ss WITH(NOLOCK) 
			on sp.PlanInfoID=ss.PlanInfoID 
			inner join  #tmpForecastsViolatingRollupValidation t on t.ForecastID=ss.ForecastID 
			where t.RollupName in (select value from
			dbo.fnStringSplit(@rollupist1,',')) 
			FOR XML PATH('')), 1, 1, ''), ''))
			
		BEGIN	
		IF(@CheckValidation = 1)
			BEGIN 
			IF EXISTS(SELECT * FROM #tmpForecastsViolatingRollupValidation)
			BEGIN
			
			SET @rollupist1 =(select  ISNULL(STUFF((SELECT DISTINCT ', ' +a.RollupName
			from #tmpForecastsViolatingRollupValidation a
			FOR XML PATH('')), 1, 1, ''), ''))
			
			SELECT @forecastName= sp.CPS+':'+cast(ss.ScenarioNbr as varchar(max))+':'+ss.ScenarioName
			FROM SavedPlanInfo sp WITH(NOLOCK) 
			INNER JOIN  dbo.SavedForecastSetup ss WITH(NOLOCK) 
			ON sp.PlanInfoID=ss.PlanInfoID 
			where ss.ForecastID=@ForecastID
				
			 SET @Result = 0;

			 IF((select top 1 isMultipleScenaro from #tmpForecastsViolatingRollupValidation)=1)
			BEGIN
			SET @originalScenario=(SELECT ISNULL(STUFF((SELECT DISTINCT ', ' + sp.CPS+':'+cast(ss.ScenarioNbr as varchar(max))+':'+ss.ScenarioName
			from SavedPlanInfo sp WITH(NOLOCK) 
			INNER JOIN  dbo.SavedForecastSetup ss WITH(NOLOCK) 
			ON sp.PlanInfoID=ss.PlanInfoID 
			INNER JOIN  #tmpForecastsViolatingRollupValidation t on t.ForecastID=ss.ForecastID and isMultipleScenaro=1 
			WHERE t.RollupName in (SELECT value from
			dbo.fnStringSplit(@rollupist1,',')) 
			FOR XML PATH('')), 1, 1, ''), ''))
			
			SET @MessageFromBackend='Selected scenario cannot be assiged to '+@rollupist1+' rollup. Clicking confirm will remove '+ @originalScenario+' and assign '+@forecastName+' to the rollup. Reason: Rollup can only contain one scenario per plan.'
			
			END
			ELSE
			BEGIN
			SET @originalScenario=(SELECT ISNULL(STUFF((SELECT DISTINCT ', ' + sp.CPS+':'+cast(ss.ScenarioNbr as varchar(max))+':'+ss.ScenarioName
			from SavedPlanInfo sp WITH(NOLOCK) 
			INNER JOIN  dbo.SavedForecastSetup ss WITH(NOLOCK) 
			ON sp.PlanInfoID=ss.PlanInfoID 
			INNER JOIN  #tmpForecastsViolatingRollupValidation t on t.ForecastID=ss.ForecastID and isMultipleScenaro=0 
			WHERE t.RollupName in (SELECT value from
			dbo.fnStringSplit(@rollupist1,',')) 
			FOR XML PATH('')), 1, 1, ''), ''))
			
			SET @MessageFromBackend='Selected scenario cannot be assiged to '+@rollupist1+' rollup. Clicking confirm will remove '+ @originalScenario+' and assign '+@forecastName+' to the rollup. Reason: Violates the condition of rollup needing to have unique crosswalks.'
			END

			
			 END
			
			END
      ELSE
        BEGIN
			IF EXISTS(SELECT * FROM #tmpForecastsViolatingRollupValidation)
			BEGIN
			SET @rollupist1 =(select  ISNULL(STUFF((SELECT DISTINCT ', ' +a.RollupName
			from #tmpForecastsViolatingRollupValidation a
			FOR XML PATH('')), 1, 1, ''), ''))
			
			IF(@IsDelete = 1)   
			begin
			  DELETE FROM dbo.SavedRollupForecastMap 
			  WHERE RollupID in (SELECT ISNULL(RollupID,'') FROM #tmpForecastsViolatingRollupValidation t
				WHERE t.RollupName in ((select value from
				dbo.fnStringSplit(@rollupist1,','))))and 
			  ForecastID in (SELECT ISNULL(ForecastID,-1) FROM  #tmpForecastsViolatingRollupValidation t
				WHERE t.RollupName in ((select value from
				dbo.fnStringSplit(@rollupist1,','))))

				INSERT INTO dbo.SavedRollupForecastMap
			   (RollupID,
				ForecastID,
				LastUpdateByID,
				LastUpdateDateTime)
				select 
				RollUpId,
				@ForecastID,
				@LastUpdateByID,
				GETDATE()
				FROM  
				SavedRollupInfo 
				WHERE RollupName in(SELECT value FROM dbo.fnStringSplit(@RollUpIdList,',')) 
				AND CAST(RollUpId AS VARCHAR)+':'+CAST(@ForecastID AS VARCHAR) NOT IN (SELECT DISTINCT CAST(RollupID AS VARCHAR)+':'+CAST(ForecastID AS VARCHAR) FROM SavedRollupForecastMap WHERE ForecastID=@ForecastID)
			END
			--DECLARE @deletionList VARCHAR(MAX);
			--SET @deletionList =(select  ISNULL(STUFF((SELECT DISTINCT ', ' +(cast(p.PlanYear as varchar(max)) +' : '+ p.CPS +' : '+ cast(f.ScenarioNbr as varchar(max)) +' : '+ f.ScenarioName)
			--from #tmpForecastsViolatingRollupValidation t 
			--Inner join SavedForecastSetup f on t.ForecastID=f.ForecastID inner join SavedPlanInfo p on p.PlanInfoID=f.PlanInfoID
			--FOR XML PATH('')), 1, 1, ''), ''));
			--IF((select top 1 isMultipleScenaro from #tmpForecastsViolatingRollupValidation)=1)
			--BEGIN
			--SET @MessageFromBackend='. Scenario: ' + @deletionList+' have been removed from the Rollup: ' 
			--+ @rollupist1 + ' as each rollup can only contain 1 scenario per plan.';
			--END
			--ELSE
			--BEGIN
			--SET @MessageFromBackend='. Scenario: ' + @deletionList+' have been removed from the Rollup: ' 
			--+ @rollupist1 + ' as rollups could have only unique crosswalks contained in them.';
			--END
			SET @MessageFromBackend='';
			SET @Result = 1; 
			
			
          END
                    
		ELSE
		BEGIN
		
		IF(@IsDelete = 1)   
			begin
			 DELETE FROM dbo.SavedRollupForecastMap WHERE ForecastID=@ForecastID
          
			  INSERT INTO dbo.SavedRollupForecastMap
			  ( RollupID,
				ForecastID,
				LastUpdateByID,
				LastUpdateDateTime)
				SELECT 
				RollUpId,
				@ForecastID,
				@LastUpdateByID,
				GETDATE()
				FROM  
				SavedRollupInfo WHERE RollupName in(SELECT value FROM
				dbo.fnStringSplit(@RollUpIdList,','))
				 SET @Result = 1; 
				  SET @MessageFromBackend='';
			  END
		END
       
          END ;
     END       
      
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        SET @Result = 0;
        SET @MessageFromBackend='Scenario save failed. Please try again <NAME_EMAIL>.' ;
		 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
        ROLLBACK TRANSACTION; 
			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
    END CATCH;  

END;
GO
