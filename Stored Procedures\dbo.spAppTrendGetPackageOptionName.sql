SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

  -- =============================================              
-- Author:  <PERSON><PERSON><PERSON> Upadhayay            
-- Create date: 07-02-2020        
-- Description:  Get Projection Update      
--              
--              
-- PARAMETERS:              
-- Input:                
            
-- TABLES:              
-- Read:              
-- Write:              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:               
             
        
-- $HISTORY                 
        
-- ----------------------------------------------------------------------------------------------------------------------                
-- DATE   VERSION   CHANGES MADE                                              DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------                
-- 2020-Feb-07  1    Initial version.                                           Ranjana Upadhayay       
-- 2020-Apr-09  2    Modify logic for selection value 3 and 4                   Satyam Singhal
--2020- May-06	3    Changed length for PackageOptionName						Deepali Mittal
--2020-May- 18  4    Removed component parameter                                 Ramandeep Saini
-- ----------------------------------------------------------------------------------------------------------------------                
 -- =============================================                
               
CREATE PROCEDURE [dbo].[spAppTrendGetPackageOptionName]  
 @Selection CHAR(7) ,  
 @packageOptName VARCHAR(100),  
 @LastUpdateByID CHAR(7)         
AS         
  BEGIN         
      BEGIN TRY         
  IF  @Selection=1      
   BEGIN      
    SELECT '' UNION SELECT  DISTINCT PackageOptionName FROM  dbo.Trend_SavedPackageOption      
     WHERE ProjectionName = (SELECT DISTINCT ProjectionName  FROM  dbo.LkpProjectionVersion WHERE IsLiveProjection = 1);      
   END      
  ELSE IF  @Selection=2      
   BEGIN      
    SELECT DISTINCT IsLivePackage FROM    dbo.Trend_SavedPackageOption WHERE   PackageOptionName =@packageOptName;  
   END      
  ELSE IF  @Selection=3 -- 'Component'       
  
  BEGIN      
    SELECT DISTINCT Component, '' AS "FileName" FROM dbo.Trend_SavedComponentInfo  WHERE IsPartOfPackage = 1;  
   END      
  
  ELSE IF  @Selection=4 --Get file Name      
   BEGIN 
   CREATE TABLE  #tempvwComponentImportUI(component VARCHAR(MAX), ImportFileName varchar(MAX),  componentVersionID1 INT)
	   INSERT INTO #tempvwComponentImportUI
	   SELECT DISTINCT TCu.Component,TCU.ImportFileName,CAST(TCU.ComponentVersionID AS VARCHAR(MAX))
	    FROM dbo.Trend_vwComponentImportUI TCU 

 CREATE TABLE  #tempvwPackageOptionUI(component VARCHAR(MAX),PackageOptionName VARCHAR(MAX), ImportFileName varchar(MAX),  componentVersionID1 INT)
	   INSERT INTO #tempvwPackageOptionUI
	    SELECT  DISTINCT TCu1.Component,TCU1.PackageOptionName,TCU1.ImportFileName,CAST(PO1.ComponentVersionID AS VARCHAR(MAX))
			 FROM dbo.Trend_vwPackageOptionUI TCU1 
       left JOIN Trend_SavedPackageOption PO1 on PO1.Component = TCU1.Component AND PO1.PackageOptionName=TCU1.PackageOptionName
      WHERE 
	  PO1.PackageOptionName=@packageOptName


SELECT distinct	x.ImportFileName+'|'+ CAST(x.ComponentVersionID1 AS VARCHAR(MAX))+'|'+CAST(x.ComponentVersionID2 AS VARCHAR(MAX))+'|'+x.component
FROM (  SELECT a.component,a.ImportFileName,a.componentVersionID1 AS componentVersionID1,ISNULL(b.componentVersionID1,0)AS componentVersionID2 FROM #tempvwComponentImportUI a
	   LEFT JOIN #tempvwPackageOptionUI b
	   ON a.component = b.component)x
END
        
   END TRY         
        
      BEGIN CATCH         
          --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'          
          DECLARE @ErrorMessage NVARCHAR(4000);         
          DECLARE @ErrorSeverity INT;         
          DECLARE @ErrorState INT;         
          DECLARE @ErrorException NVARCHAR(4000);         
          DECLARE @errSrc VARCHAR(max) =Isnull(Error_procedure(), 'SQL'),         
                  @currentdate DATETIME=Getdate()         
        
          SELECT @ErrorMessage = Error_message(),         
                 @ErrorSeverity = Error_severity(),         
                 @ErrorState = Error_state(),         
                 @ErrorException = 'Line Number :'         
                                   + Cast(Error_line() AS VARCHAR)         
                                   + ' .Error Severity :'         
                                   + Cast(@ErrorSeverity AS VARCHAR)         
                                   + ' .Error State :'         
                                   + Cast(@ErrorState AS VARCHAR)         
        
          RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)         
        
          ROLLBACK TRANSACTION;         
        
          ---Insert into app log for logging error------------------           
          EXEC Spappaddlogentry         
            @currentdate,         
            '',         
            'ERROR',         
            @errSrc,         
            @ErrorMessage,         
            @ErrorException,         
            @LastUpdateByID         
      END CATCH;         
  END 
GO
