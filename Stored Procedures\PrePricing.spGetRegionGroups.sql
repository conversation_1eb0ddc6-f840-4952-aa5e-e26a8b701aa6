SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetRegionGroups]   
--    
-- AUTHOR: <PERSON><PERSON>rthy 
--    
-- CREATED DATE: 2024-Oct-28    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Regions  
--    
-- PARAMETERS:    
-- Input: 
-- @RegionID
   
-- TABLES:   
-- PrePricing.PlanGroup 
 
-- Read:    
--  

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-28		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetRegionGroups]
(
	@RegionID INT 
)
AS    
BEGIN    
	SELECT PlanGroupID,PlanGroupName FROM PrePricing.PlanGroup WITH(NOLOCK) 
	WHERE RegionID=@RegionID ORDER BY LastUpdateDateTime DESC
END
GO
