SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- PROCEDURE NAME: Trend_Data_spCalcBaseData

-- AUTHOR: <PERSON> Cannon                              
--
-- DESCRIPTION:
--   Get ExperienceYear and CurrentYear data for each BidYear plan for RateTypeID 1 (Experience) and RateTypeID 2 (Manual)
--             
-- PARAMETERS:
--  Input  : NONE
--  Output : NONE
--                                                      
-- TABLES:                                                                  
-- Read:                                                                 
--   Trend_CalcHistoricMembership
--   Trend_CalcHistoricCostAndUse
--   Trend_SavedManualCutPlanAssignment
--   Trend_SavedManualCutPlanComposition
--   LkpIntPlanYear
--   LkpIntBenefitCategory
--   SavedForecastSetup
--   SavedRollupForecastMap
--   SavedRollupInfo
-- 
-- Write:                                                                  
--   Trend_Data_CalcBaseMembership
--   Trend_Data_CalcBaseCostAndUse
--   
-- VIEWS: Read: vwSAMCrosswalks
--
-- FUNCTIONS: None
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ------------------------------------------------------------------------------------------------------------------------------------
-- DATE         VERSION     CHANGE MADE																			DEVELOPER(S)            
-- ------------------------------------------------------------------------------------------------------------------------------------
-- 2020-03-26   1           Initial Version																		Clay Cannon
-- 2020-06-18   2           Join tables needed to filter SAM on version											Michael Manes
-- 2020-09-11   3           Fixed sonar qube Fixes																Deepali
-- 2024-01-24	4			Removed duplication in steps 1d & 2d: added temp tables #xwalks_BsCr & #CYInfo;  
--							For each INSERT INTO added table column names; removed "SELECT *" and string 
--							literals as column aliases in #vwSAMCrosswalks; modified step #vwSAMCrosswalks
--							from "SELECT ... INTO" to "CREATE TABLE ... INSERT INTO"							Aleksandar Dimitrijevic
-- ------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_Data_spCalcBaseData]
AS
    BEGIN

SET NOCOUNT ON; 
SET ANSI_WARNINGS OFF;

        DECLARE @LastUpdateByID CHAR(7) = RIGHT(USER, 7);
        DECLARE @LastUpdateDateTime DATETIME = GETDATE ();


        --Clear tables
        DELETE  FROM dbo.Trend_Data_CalcBaseMembership
		WHERE 1=1;
        DELETE  FROM dbo.Trend_Data_CalcBaseCostAndUse
		WHERE 1=1;

        --Setup the bid year plan list based on the parameters passed to the procedure
        --Create a temp three year crosswalk table from vwSAMCrosswalks
        DROP TABLE IF EXISTS #vwSAMCrosswalks;
		
		CREATE TABLE #vwSAMCrosswalks
			(BidYearPlanInfoID SMALLINT,
			 CurrentYearPlanInfoID SMALLINT,
			 BaseYearPlanInfoID SMALLINT);
		
		INSERT INTO #vwSAMCrosswalks
			(BidYearPlanInfoID,
			 CurrentYearPlanInfoID,
			 BaseYearPlanInfoID)
        SELECT DISTINCT sam.BidYearPlanInfoID
					   ,sam.CurrentYearPlanInfoID
					   ,sam.BaseYearPlanInfoID
				   FROM dbo.vwSAMCrosswalks sam WITH(NOLOCK)       
	   --Only bring in service area scenarios that are live
       INNER JOIN (SELECT DISTINCT sfs.PlanInfoID, sfs.ServiceAreaOptionID
                     FROM dbo.SavedForecastSetup sfs WITH(NOLOCK)
					 INNER JOIN dbo.SavedRollupForecastMap srf WITH(NOLOCK)
					 ON sfs.ForecastID = srf.ForecastID
					 INNER JOIN dbo.SavedRollupInfo sr WITH(NOLOCK)
					 ON srf.RollupID = sr.RollupID AND sr.RollupName = 'Live') sfs
               ON sfs.ServiceAreaOptionID = sam.ServiceAreaOptionID
              AND sfs.PlanInfoID = sam.BidYearPlanInfoID;

		--get base to current crosswalks
		DROP TABLE IF EXISTS #xwalks_BsCr;
		
		CREATE TABLE #xwalks_BsCr
			(BaseYearPlanInfoID SMALLINT,
			 CurrentYearPlanInfoID SMALLINT);
		
		INSERT INTO #xwalks_BsCr
			(BaseYearPlanInfoID,
			 CurrentYearPlanInfoID)
		SELECT DISTINCT BaseYearPlanInfoID
					   ,CurrentYearPlanInfoID
				   FROM #vwSamCrosswalks WITH(NOLOCK);
					
		--narrow down to manual rate CY PlanInfoIDs
		DROP TABLE IF EXISTS #CYInfo;
		CREATE TABLE #CYInfo
			(CurrentYearPlanInfoID SMALLINT,
			 PlanInfoID SMALLINT);

		INSERT INTO #CYInfo
			(CurrentYearPlanInfoID,
			 PlanInfoID)
		SELECT DISTINCT x.CurrentYearPlanInfoID 
					   ,mpa.PlanInfoID
				   FROM dbo.LkpProjectionVersion pv WITH(NOLOCK)
			 INNER JOIN dbo.Trend_SavedManualCutPlanAssignment mpa WITH(NOLOCK)
					 ON mpa.ProjectionID=pv.ProjectionID
			 INNER JOIN dbo.Trend_SavedManualCutPlanComposition	mpc WITH(NOLOCK)
					 ON mpa.AssignedManualCutID=mpc.ManualCutID
			 INNER JOIN #xwalks_BsCr x WITH(NOLOCK)
					 ON mpc.PlanInfoID=x.BaseYearPlanInfoID
				  WHERE pv.IsLiveProjection=1;

        --1: Populate Membership
        --a: Get Experience (RateTypeID 1) Membership for Experience Year
        INSERT INTO dbo.Trend_Data_CalcBaseMembership
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 MemberMonths,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
        SELECT p.PlanInfoID
              ,1 AS RateType
              ,m.IncurredMonth
              ,SUM (m.MemberMonths) AS MemberMonths
              ,@LastUpdateByID AS LastUpdateByID
              ,@LastUpdateDateTime AS LastUpdateDateTime
          FROM dbo.LkpIntPlanYear y WITH(NOLOCK)
         INNER JOIN dbo.SavedPlanInfo p WITH(NOLOCK)
            ON p.PlanYear = y.PlanYearID
           AND   y.IsBidYear = 1
         INNER JOIN (SELECT DISTINCT BaseYearPlanInfoID
									,BidYearPlanInfoID
								FROM #vwSamCrosswalks WITH(NOLOCK)) x
            ON p.PlanInfoID = x.BidYearPlanInfoID
           AND   x.BaseYearPlanInfoID IS NOT NULL
         INNER JOIN   dbo.Trend_CalcHistoricMembership m WITH(NOLOCK)
            ON m.PlanInfoID = x.BaseYearPlanInfoID
         GROUP BY p.PlanInfoID
                 ,m.IncurredMonth;

        --b: Get Experience (RateTypeID 1) Membership for Current Year
        INSERT INTO dbo.Trend_Data_CalcBaseMembership
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 MemberMonths,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
        SELECT p.PlanInfoID
              ,1 AS RateType
              ,m.IncurredMonth
              ,SUM (m.MemberMonths) AS MemberMonths
              ,@LastUpdateByID AS LastUpdateByID
              ,@LastUpdateDateTime AS LastUpdateDateTime
          FROM dbo.LkpIntPlanYear y WITH(NOLOCK)
         INNER JOIN   dbo.SavedPlanInfo p WITH(NOLOCK)
            ON p.PlanYear = y.PlanYearID
           AND y.IsBidYear = 1
         INNER JOIN (SELECT DISTINCT CurrentYearPlanInfoID
									,BidYearPlanInfoID
								FROM #vwSamCrosswalks WITH(NOLOCK)) x
            ON p.PlanInfoID = x.BidYearPlanInfoID
           AND x.CurrentYearPlanInfoID IS NOT NULL
         INNER JOIN dbo.Trend_CalcHistoricMembership m WITH(NOLOCK)
            ON m.PlanInfoID = x.CurrentYearPlanInfoID
         GROUP BY p.PlanInfoID
                 ,m.IncurredMonth;

        --c: Get Manual Cut (RateTypeID 2) Membership for Experience Year
        INSERT INTO dbo.Trend_Data_CalcBaseMembership
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 MemberMonths,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
        SELECT mpa.PlanInfoID
              ,2 AS RateType
              ,m.IncurredMonth
              ,SUM (m.MemberMonths) AS MemberMonths
              ,@LastUpdateByID AS LastUpdateByID
              ,@LastUpdateDateTime AS LastUpdateDateTime
          FROM dbo.LkpProjectionVersion pv WITH(NOLOCK)
         INNER JOIN   dbo.Trend_SavedManualCutPlanAssignment mpa WITH(NOLOCK)
            ON mpa.ProjectionID = pv.ProjectionID
           AND pv.IsLiveProjection = 1
         INNER JOIN dbo.Trend_SavedManualCutPlanComposition mpc WITH(NOLOCK)
            ON mpa.AssignedManualCutID = mpc.ManualCutID
         INNER JOIN dbo.Trend_CalcHistoricMembership m WITH(NOLOCK)
            ON m.PlanInfoID = mpc.PlanInfoID
         GROUP BY mpa.PlanInfoID
                 ,m.IncurredMonth;

        --d: Get Manual Cut (RateTypeID 2) Membership for Current Year
        INSERT INTO dbo.Trend_Data_CalcBaseMembership
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 MemberMonths,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
        SELECT cy.PlanInfoID
              ,2 AS RateType
              ,m.IncurredMonth
              ,SUM (m.MemberMonths) AS MemberMonths
              ,@LastUpdateByID AS LastUpdateByID
              ,@LastUpdateDateTime AS LastUpdateDateTime
          FROM #CYInfo cy WITH(NOLOCK)
         INNER JOIN dbo.Trend_CalcHistoricMembership m WITH(NOLOCK)
            ON m.PlanInfoID = cy.CurrentYearPlanInfoID
         GROUP BY cy.PlanInfoID
                 ,m.IncurredMonth;

        --2: Populate Cost and Utilization
        --a: Get Experience (RateTypeID 1) Cost and Utilization for Experience Year
        INSERT INTO dbo.Trend_Data_CalcBaseCostAndUse
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 BenefitCategoryID,
			 Allowed,
			 Utilization,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
        SELECT p.PlanInfoID
              ,1 AS RateType
              ,c.IncurredMonth
              ,c.BenefitCategoryID
              ,SUM (c.Allowed) AS Allowed
              ,SUM (c.Utilization) AS Utilization
              ,@LastUpdateByID AS LastUpdateByID
              ,@LastUpdateDateTime AS LastUpdateDateTime
          FROM dbo.LkpIntPlanYear y WITH(NOLOCK)
         INNER JOIN dbo.SavedPlanInfo p WITH(NOLOCK)
            ON p.PlanYear = y.PlanYearID
           AND y.IsBidYear = 1
         INNER JOIN (SELECT DISTINCT BaseYearPlanInfoID
								    ,BidYearPlanInfoID
							    FROM #vwSamCrosswalks WITH(NOLOCK)) x
            ON p.PlanInfoID = x.BidYearPlanInfoID
           AND x.BaseYearPlanInfoID IS NOT NULL
         INNER JOIN dbo.Trend_CalcHistoricCostAndUse c WITH(NOLOCK)
            ON c.PlanInfoID = x.BaseYearPlanInfoID
         GROUP BY p.PlanInfoID
                 ,c.IncurredMonth
                 ,c.BenefitCategoryID;

        --b: Get Experience (RateTypeID 1) Cost and Utilization for Current Year
        INSERT INTO dbo.Trend_Data_CalcBaseCostAndUse
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 BenefitCategoryID,
			 Allowed,
			 Utilization,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
        SELECT p.PlanInfoID
              ,1 AS RateType
              ,c.IncurredMonth
              ,c.BenefitCategoryID
              ,SUM (c.Allowed) AS Allowed
              ,SUM (c.Utilization) AS Utilization
              ,@LastUpdateByID AS LastUpdateByID
              ,@LastUpdateDateTime AS LastUpdateDateTime
          FROM dbo.LkpIntPlanYear y WITH(NOLOCK)
         INNER JOIN dbo.SavedPlanInfo p WITH(NOLOCK)
            ON p.PlanYear = y.PlanYearID
           AND y.IsBidYear = 1
         INNER JOIN (SELECT DISTINCT CurrentYearPlanInfoID
									,BidYearPlanInfoID
								FROM #vwSamCrosswalks WITH(NOLOCK)) x
            ON p.PlanInfoID = x.BidYearPlanInfoID
           AND x.CurrentYearPlanInfoID IS NOT NULL
         INNER JOIN dbo.Trend_CalcHistoricCostAndUse c WITH(NOLOCK)
            ON c.PlanInfoID = x.CurrentYearPlanInfoID
         GROUP BY p.PlanInfoID
                 ,c.IncurredMonth
                 ,c.BenefitCategoryID;

        --c: Get Manual Cut (RateTypeID 2) Cost and Utilization for Experience Year
        INSERT INTO dbo.Trend_Data_CalcBaseCostAndUse
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 BenefitCategoryID,
			 Allowed,
			 Utilization,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
        SELECT mpa.PlanInfoID
              ,2 AS RateType
              ,c.IncurredMonth
              ,c.BenefitCategoryID
              ,SUM (c.Allowed) AS Allowed
              ,SUM (c.Utilization) AS Utilization
              ,@LastUpdateByID AS LastUpdateByID
              ,@LastUpdateDateTime AS LastUpdateDateTime
          FROM dbo.LkpProjectionVersion pv WITH(NOLOCK)
         INNER JOIN dbo.Trend_SavedManualCutPlanAssignment mpa WITH(NOLOCK)
            ON mpa.ProjectionID = pv.ProjectionID
           AND pv.IsLiveProjection = 1
         INNER JOIN dbo.Trend_SavedManualCutPlanComposition mpc WITH(NOLOCK)
            ON mpa.AssignedManualCutID = mpc.ManualCutID
         INNER JOIN dbo.Trend_CalcHistoricCostAndUse c WITH(NOLOCK)
            ON c.PlanInfoID = mpc.PlanInfoID
         GROUP BY mpa.PlanInfoID
                 ,c.IncurredMonth
                 ,c.BenefitCategoryID;

        --d: Get Manual Cut (RateTypeID 2) Cost and Utilization for Current Year
        INSERT INTO dbo.Trend_Data_CalcBaseCostAndUse
			(
			 PlanInfoID,
			 RateType,
			 IncurredMonth,
			 BenefitCategoryID,
			 Allowed,
			 Utilization,
			 LastUpdateByID,
			 LastUpdateDateTime
			)
		     SELECT cy.PlanInfoID
				   ,2 AS RateType
				   ,c.IncurredMonth
				   ,c.BenefitCategoryID
				   ,SUM (c.Allowed) AS Allowed
				   ,SUM (c.Utilization) AS Utilization
				   ,@LastUpdateByID AS LastUpdateByID
				   ,@LastUpdateDateTime AS LastUpdateDateTime
		       FROM #CYInfo cy WITH(NOLOCK)
		 INNER JOIN dbo.Trend_CalcHistoricCostAndUse c WITH(NOLOCK)
				 ON c.PlanInfoID = cy.CurrentYearPlanInfoID
		   GROUP BY cy.PlanInfoID
				   ,c.IncurredMonth
				   ,c.BenefitCategoryID
		   ORDER BY BenefitCategoryID
				   ,IncurredMonth;

    END;
GO