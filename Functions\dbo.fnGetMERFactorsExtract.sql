SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO


-- HISTORY:  
-- ---------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER  
-- ---------------------------------------------------------------------------------------------------------------------  
-- 2024-Oct-10        1         Initial Version                                                 Harini <PERSON>durai
-- 2024-DEC-06	      2	        Adding ForecastID to the SavedMERActAdj assumptions table  		<PERSON>

-- ----------------------------------------------------------------------------------------------------------------------  
CREATE FUNCTION [dbo].[fnGetMERFactorsExtract]  
  (  
    @WhereIN VARCHAR(MAX) = NULL  
   )  
RETURNS @Results TABLE  
 ( ForecastID INT, 
   ContractPBP VARCHAR(13),  
   Component INT,
   BenefitCategoryNumber INT,
   C2PMERUseMultAdj DECIMAL(24,15),
   C2PMERCostMultAdj DECIMAL(24,15),
   C2PMERUseAddAdj DECIMAL(24,15),
   C2PMERCostAddAdj DECIMAL(24,15),
   UserID CHAR(7),
   LastUpdateDateTime DATETIME
  ) AS  

BEGIN  

 IF @WhereIn IS NULL  
  INSERT @Results 
   SELECT 
    DISTINCT
     sma.ForecastID 
    ,sma.ContractPBP   
    ,sma.Component
    ,sma.BenefitCategoryNumber
    ,sma.C2PMERUseMultAdj
    ,sma.C2PMERCostMultAdj
    ,sma.C2PMERUseAddAdj
    ,sma.C2PMERCostAddAdj
    ,sma.UserID
    ,sma.LastUpdateDateTime
    FROM dbo.SavedMERActAdj sma WITH(NOLOCK)
    INNER JOIN dbo.SavedPlanHeader sph WITH(NOLOCK)  ON sma.ForecastID = sph.ForecastID 
    AND sma.PlanID = sph.PlanID 
    AND sma.SegmentId = sph.SegmentId   
 ELSE  
  INSERT @Results  
     SELECT
     DISTINCT
       sma.ForeCastID 
      ,sma.ContractPBP     
      ,sma.Component
      ,sma.BenefitCategoryNumber
      ,sma.C2PMERUseMultAdj
      ,sma.C2PMERCostMultAdj
      ,sma.C2PMERUseAddAdj
      ,sma.C2PMERCostAddAdj
      ,sma.UserID
      ,sma.LastUpdateDateTime
      FROM dbo.SavedMERActAdj sma WITH(NOLOCK)
     INNER JOIN dbo.SavedPlanHeader sph WITH(NOLOCK)  ON sma.ForecastID = sph.ForecastID 
    AND sma.PlanID = sph.PlanID 
    AND sma.SegmentId = sph.SegmentId  
	  WHERE sph.ForecastID  IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))  
      ORDER BY sma.ForeCastID ,sma.ContractPBP
RETURN  
END
GO
