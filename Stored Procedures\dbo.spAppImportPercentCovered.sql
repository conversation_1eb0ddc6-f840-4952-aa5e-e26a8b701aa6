SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Description:	Stored procedure responsible for inserting the profit percernt data into SavedPlanAssumptions
-- and update reprice flag in SaveForecastSetup
-- PARAMETERS:  
-- Input:  
--
-- Output:  
--  
-- TABLES:  
-- Read:
--	   ImportDataStaging
--     
-- Write:

--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION        CHANGES MADE                                                      DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-Dec-16        1          Initial Version                                                  Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------  
-- =======================================================================================================================

CREATE PROCEDURE [dbo].[spAppImportPercentCovered]
(
	@StageId VARCHAR(100)
)
AS
BEGIN
SET NOCOUNT ON;

		DECLARE @jsonData VARCHAR(MAX);
		DECLARE @UserId VARCHAR(7);
		SELECT @jsonData = JsonData, @UserId = UserId
		FROM dbo.ImportDataStaging WITH (NOLOCK)
		WHERE StageId = @StageId;


/***************Import script for the SavedPlanAssumptions Table*****************************/		
		DECLARE @tbl__importData TABLE
		(
			[ForecastID][Int] NOT NULL,
			[PlanInfoID][Int] NOT NULL,
			[PlanYearID] [Int] NOT NULL,
			[Contract] CHAR(5) NOT NULL,
			[PlanID] CHAR(3) NOT NULL,
			[SegmentID] CHAR(3) NOT NULL,
			[IsBenefitYearCurrentYear] [Int] NOT NULL,
			[BenefitCategoryID] [Int] NOT NULL,			
			[PercentCoveredAllowed] [decimal]  NOT NULL,
			[PercentCoveredCostShare] [decimal]  NOT NULL,
			[LastUpdateByID] CHAR(7) NOT NULL,
			[LastUpdateDateTime] DATETIME NOT NULL
		);

		INSERT INTO @tbl__importData 
		SELECT 
		    sp.ForecastID,
		    p.PlanInfoID,
		    ptypes.PlanYearID,
            ptypes.[Contract],		
			ptypes.PlanID,
			ptypes.SegmentID,
			ptypes.IsBenefitYearCurrentYear,
			ptypes.BenefitCategoryID,
			ptypes.PercentCoveredAllowed,
			ptypes.PercentCoveredCostShare,
			@UserId,
			GETDATE()	
		FROM  OPENJSON(@jsonData, '$.PercentCovered')
		WITH
	    (
			[PlanYearID] [Int],
			[Contract] CHAR(5),
			[PlanID] CHAR(3),
			[SegmentID] CHAR(3),				
			[IsBenefitYearCurrentYear] [Int],
			[BenefitCategoryID] [Int],			
			[PercentCoveredAllowed] [decimal],
			[PercentCoveredCostShare] [decimal]
		) AS ptypes
		INNER JOIN dbo.SavedPlanInfo AS p ON p.CPS = CONCAT(pTypes.[Contract], +'-'+ pTypes.PlanID, +'-'+ pTypes.SegmentID)
		 AND ptypes.PlanYearID = p.PlanYear
		INNER JOIN dbo.SavedPlanHeader sp ON sp.PlanInfoID = p.PlanInfoID  AND sp.PlanYearID = p.PlanYear
        INNER JOIN dbo.SavedForecastSetup sfs ON sfs.ForecastID = sp.ForecastID AND sfs.PlanYear = sp.PlanyearID


		UPDATE	[dbo].[SavedPlanBenefitDetail]
		SET 
			PlanYearID = source.PlanYearID,
			IsBenefitYearCurrentYear = source.IsBenefitYearCurrentYear,
			BenefitCategoryID = source.BenefitCategoryID,			
			PercentCoveredAllowed = source.PercentCoveredAllowed,
			PercentCoveredCostShare = source.PercentCoveredCostShare,
			LastUpdateByID = source.LastUpdateByID,
			LastUpdateDateTime = source.LastUpdateDateTime
		FROM @tbl__importData AS source
		INNER JOIN [dbo].[SavedPlanBenefitDetail] AS target ON 
		target.PlanYearID = source.PlanYearID AND source.ForecastID = target.ForecastID AND target.PlanInfoID = source.PlanInfoID AND target.BenefitCategoryID = source.BenefitCategoryID
		AND target.IsBenefitYearCurrentYear = source.IsBenefitYearCurrentYear
		INNER JOIN dbo.SavedPlanHeader p ON p.ForecastID = target.ForecastID
        INNER JOIN dbo.SavedForecastSetup sfs ON sfs.ForecastID = p.ForecastID



 --Trigger Reprice Notification

		UPDATE dbo.SavedForecastSetup
		SET 
		   IsToReprice = 1,
		   LastUpdateByID = source.LastUpdateByID,
		   LastUpdateDateTime = source.LastUpdateDateTime
		FROM @tbl__importData AS source
        INNER JOIN dbo.SavedForecastSetup AS sfs ON sfs.PlanYear = source.PlanYearID AND sfs.ForecastID = source.ForecastID	AND
		sfs.PlanInfoID = source.PlanInfoID


		DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;

END
GO