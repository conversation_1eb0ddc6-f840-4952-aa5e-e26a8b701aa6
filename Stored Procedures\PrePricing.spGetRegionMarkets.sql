SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetRegionMarkets]   
--    
-- AUTHOR: <PERSON><PERSON>y 
--    
-- CREATED DATE: 2024-Nov-07    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Markets for regions  
--    
-- PARAMETERS:    
-- Input: 
-- @RegionID
   
-- TABLES:   
--  
 
-- Read:    
--  dbo.SavedMarketInfo

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Nov-07		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetRegionMarkets]
(
	@RegionID INT 
)
AS    
BEGIN     
	 SELECT ActuarialMarketID,ActuarialMarket FROM dbo.SavedMarketInfo WITH(NOLOCK) 
	 WHERE ActuarialRegionID=@RegionID ORDER BY LastUpdateDateTime DESC;
END
GO
