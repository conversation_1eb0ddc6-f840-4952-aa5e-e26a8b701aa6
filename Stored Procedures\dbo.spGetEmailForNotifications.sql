SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- Stored Procedure

-- ----------------------------------------------------------------------------------------------------------------------       

-- PROCEDURE NAME: [[spGetEmailForNotifications]]        

--        

-- AUTHOR: Gowri.G    

--        

-- CREATED DATE: 2011-Aug-03        

--        

-- DESCRIPTION: Procedure responsible for Retriving the emailID for CU data load packages    



-- TYPE: New    

--     

-- PARAMETERS:        

-- Input:        

--       

-- TABLES:         

-- Read:        

--   LkpIntDataLoadEmailNotification       



-- Write:        

--             



-- VIEWS:        

--        

-- FUNCTIONS:        

--          

-- STORED PROCS:            



-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        

-- DATE    VERSION  CHANGES MADE              DEVELOPER          

-- ----------------------------------------------------------------------------------------------------------------------        

-- 2013-oct-11   1  Initial Version taken from MAPDCU database.   Vivek 
-- 2017-Feb-16   2  Changes logic to fectch ToEmailList			  Deepali			    

-- ----------------------------------------------------------------------------------------------------------------------        

CREATE PROCEDURE [dbo].[spGetEmailForNotifications]    

@Level INT    

AS        

BEGIN    



SET NOCOUNT ON;  
IF OBJECT_ID(N'tempdb..#Temp') IS NOT NULL
BEGIN
DROP TABLE #Temp
END

CREATE TABLE #TEMP(
FROMEMAIL VARCHAR(255) NULL,
TOEMAIL VARCHAR (8000) NULL
)



DECLARE @LCLLEVEL AS INT   

SET @LCLLEVEL = @LEVEL    



INSERT INTO #TEMP(FROMEMAIL)

SELECT REPLACE(REPLACE(REPLACE(FROMEMAIL,' ',';'),',',';'),':',';') AS FROMEMAIL
FROM 

LKPINTNOTIFICATIONEMAIL WHERE EMAILNOTIFICATIONID = @LCLLEVEL 

DECLARE @STR VARCHAR(MAX)

SELECT @STR= COALESCE(@STR + ', ', '') + A.USEREMAIL 
FROM (SELECT DISTINCT USEREMAIL FROM ADMINUSERHEADER WHERE ROLEID=0 ) A

UPDATE #TEMP
SET TOEMAIL=@STR
SELECT * FROM #TEMP

END
GO
