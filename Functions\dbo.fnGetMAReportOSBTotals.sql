SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportOSBTotals
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2010-Feb-04
-- HEADER UPDATED: 2010-Oct-05
--
-- DESCRIPTION: Pulls Optional Supplemental Benefit information for bidable and active plans (used for Auditing Optional
--              Supplemental Benefits)
--
-- PARAMETERS:
--	Input:
--      @PlanYearID
--      @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--      ArcPerIntOptionalPackageDetail
--      ArcPerIntOptionalPackageHeader
--      Arc<PERSON><PERSON>d<PERSON><PERSON><PERSON>eader
--      ArcSavedPlanOptionalPackageDetail
--      PerIntOptionalPackageDetail
--      PerIntOptionalPackageHeader
--      SavedPlanHeader
--      SavedPlanOptionalPackageDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Feb-04		1		    Initial Version										                Lawrence Choi
-- 2010-Mar-15      2           Rounding of OSB Premium to nearest dime                             Lawrence Choi
-- 2010-Mar-30      3           To exclude removed OSB packages                                     Lawrence Choi
-- 2010-Mar-30      4           Change the format of PackageIndex                                   Lawrence Choi  
-- 2010-Apr-23      5           Corrected OSB Description.                                          Casey Sanders
-- 2010-Oct-05      6           Added Arc tables for past year reference. Removed @PlanVersion      Joe Casey
--                                  as a parameter.  Default PlanVersion = 1
-- 2013-Oct-13		7			Modified to Include SegmentId										Anubhav Mishra
--2015-Dec-23		8			Changed the "Union All" to "Union" to remove multiple counts		Jordan Purdue
--2015-Dec-23		9			Changed Select * to Select only the needed columns from each pull	Jordan Purdue
--									to remove the rest of the multiple counts
-- 2016-Feb-25		10			Changed varchar(50) to varchar(200)in name field in @results table  Pooja  Dahiya
-- 2019-Feb-04		11			PackageIndex and PlanPackageID data type changes					Keith Galloway
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportOSBTotals]
    (
    @PlanYearID SMALLINT,
    @ForecastID INT
    )
RETURNS @Results TABLE
    (
    PlanYearID SMALLINT,
    ContractNumber CHAR(5),
    PBPID CHAR(3),
    SegmentId CHAR(3),  --ADDED SegmentId
    ForecastID INT,
    PlanVersion INT,
    PlanPackageID TINYINT,
    PackageIndex INT,  -- changed back to INT in v11 as it was before V4
    Name VARCHAR(200),							
    OSBDescription VARCHAR(500),
    TotalExpense DECIMAL(14,2),
    TotalGainLoss DECIMAL(14,2),
    PackageAllowedPMPM DECIMAL(14,2),
    PackageCostSharePMPM DECIMAL(14,2),
    PackageNetPMPM DECIMAL(14,2), 
    OSBPremium DECIMAL(14,2)
    )
AS
    BEGIN
        INSERT @Results
            
            SELECT
                PlanYearID,
		        ContractNumber,
		        PlanID,
		        SegmentId,  --Added SegmentId
		        ForecastID,
		        PlanVersion,
		        PlanPackageID,
                PackageIndex,
                [Name],							
		        OSBDescription = packagetotals.Description,
                TotalExpense,
                TotalGainLoss,
                PackageAllowedPMPM,
                PackageCostSharePMPM,
                PackageNetPMPM = PackageAllowedPMPM - PackageCostSharePMPM,
                PackagePremium = ROUND(PackageAllowedPMPM - PackageCostSharePMPM + TotalExpense + TotalGainLoss, 1)
             FROM	 
                (SELECT
                    sph.PlanYearID,
			        sph.ContractNumber,
			        sph.PlanID,
			        sph.SegmentId,  --Added SegmentId
			        sph.ForecastID,
			        PlanVersion = 1,
			        saved.PlanPackageID,
                    perHeader.PackageIndex,
                    perHeader.Name,
                    perHeader.Description,							
			        perHeader.TotalExpense,
                    perHeader.TotalGainLoss,
                    PackageAllowedPMPM = 
			            ROUND(
                        SUM(
			            (perDetail.AllowedUtilzationPer1000/1000)*
			            (perDetail.AllowedAverageCost/12)
			            ), 2
                        ),
			        PackageCostSharePMPM =
			            ROUND(
                        SUM(
			            CASE WHEN perDetail.MeasurementUnitCode = 'Coin' THEN
				            perDetail.EnrolleeCostShareUtilization * 
				            perDetail.EnrolleeAverageCostShare
			            ELSE 
				            perDetail.EnrolleeCostShareUtilization * 
				            perDetail.EnrolleeAverageCostShare / 12000
			            END
			            ), 2
                        )
	            FROM 
	                (SELECT planYearID, ForecastID, PlanPackageID, PackageIndex, IsHidden FROM SavedPlanOptionalPackageDetail
	                UNION 
	                SELECT planYearID, ForecastID, PlanPackageID, PackageIndex, IsHidden FROM ArcSavedPlanOptionalPackageDetail) saved
	            INNER JOIN 
	                (SELECT PlanYearID,ContractNumber,PlanID,SegmentId,ForecastID  FROM SavedPlanHeader
	                UNION 
	                SELECT PlanYearID,ContractNumber,PlanID,SegmentId,ForecastID  FROM ArcSavedPlanHeader) sph
		            ON saved.PlanYearID = sph.PlanYearID
		            AND saved.ForecastID = sph.ForecastID
	            INNER JOIN 
	                (SELECT PlanYearID,PackageIndex,AllowedUtilzationPer1000,AllowedAverageCost,MeasurementUnitCode,EnrolleeCostShareUtilization,EnrolleeAverageCostShare,IsHidden FROM PerIntOptionalPackageDetail
	                UNION 
	                SELECT PlanYearID,PackageIndex,AllowedUtilzationPer1000,AllowedAverageCost,MeasurementUnitCode,EnrolleeCostShareUtilization,EnrolleeAverageCostShare,IsHidden FROM ArcPerIntOptionalPackageDetail) perDetail
		            ON saved.PlanYearID = perDetail.PlanYearID
		            AND saved.PackageIndex = perDetail.PackageIndex
	            INNER JOIN
	                (SELECT PlanYearID,PackageIndex,Name,Description,TotalExpense,TotalGainLoss FROM PerIntOptionalPackageHeader
	                UNION 
	                SELECT PlanYearID,PackageIndex,Name,Description,TotalExpense,TotalGainLoss FROM ArcPerIntOptionalPackageHeader) perHeader
		            ON perDetail.PlanYearID = perHeader.PlanYearID
		            AND perDetail.PackageIndex = perHeader.PackageIndex
	            WHERE saved.PlanYearID = @PlanYearID
	                AND saved.ForecastID = @ForecastID
	                AND saved.IsHidden = 0
	                AND perDetail.IsHidden = 0
	            GROUP BY
			            sph.PlanYearID,
			            sph.ContractNumber,
			            sph.PlanID,
			            sph.SegmentId,  --Added SegmentId
			            sph.ForecastID,
                        saved.PlanPackageID,
			            perHeader.PackageIndex,
                        perHeader.Description,
                        perHeader.Name,
                        perHeader.TotalExpense,
                        perHeader.TotalGainLoss
                ) packagetotals
            WHERE PlanYearID = @PlanYearID
                AND ForecastID = @ForecastID
        RETURN
    END 

GO
