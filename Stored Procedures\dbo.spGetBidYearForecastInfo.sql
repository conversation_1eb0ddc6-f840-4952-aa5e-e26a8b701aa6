SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------        
-- SP NAME: [spGetBidYearForecastInfo]        
--        
-- AUTHOR: Sur<PERSON>y
--        
-- CREATED DATE: 2023-09-20       
--        
-- DESCRIPTION: Procedure responsible to get valid forecast ids ,year, scenario nor and CPS     
--        
-- PARAMETERS:         
--        
-- TABLES:         
-- Read:        

-- Write:        
--        
-- VIEWS:        
--        
-- FUNCTIONS:       
--        
-- STORED PROCS:        
--        
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE          VERSION      CHANGES MADE								 DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2023-09-20    1          Initial Version								Surya Murthy
--							
-- ---------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[spGetBidYearForecastInfo]
AS
BEGIN
	SET NOCOUNT ON;
	SELECT sf.ForecastID,sf.PlanYear,sp.CPS AS HPASID,sf.ScenarioNbr 
	FROM dbo.SavedForecastSetup sf WITH(NOLOCK)
	INNER JOIN dbo.SavedPlanInfo sp WITH(NOLOCK) ON sp.PlanInfoID=sf.PlanInfoID
	WHERE sf.PlanYear=dbo.fnGetBidYear() ORDER BY sf.ForecastID;
END;
GO
