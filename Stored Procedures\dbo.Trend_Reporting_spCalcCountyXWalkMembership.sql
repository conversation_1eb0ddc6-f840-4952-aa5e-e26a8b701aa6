SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_Reporting_spCalcCountyXWalkMembership
--
-- CREATOR: <PERSON>
--
-- CREATED DATE: Sept-4-2020
--
-- DESCRIPTION: Calculate historic period and projected period membership with county crosswalking logic implemented
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntPlanYear
--					SavedForecastSetup
--					SavedHistoricalCrosswalk
--					SavedProjectedCrosswalk
--					SavedRollupForecastMap
--					SavedRollupInfo
--				    Trend_CalcHistoricMembership
--					Trend_PerPopulationMRACurrentYear
--					
--          Write:  Trend_Reporting_CalcCountyXWalkMembership
--                  Trend_Reporting_Log
--
-- VIEWS: Read:		vwSAMCrosswalks
--					vwPlanInfo
--
-- FUNCTIONS:		Trend_fnSafeDivide
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 

/*All crosswalks based on   SELECT * FROM maamodels_maast.dbo.lkprenewaltype */
/*
RenewalTypeID	RenewalType									RenewalTypeDescription	
0				NA												Not applicable or not set.	
1				New Plan										New plan in bid year - no members will be rolled into this plan.	
2				Terminated Plan									The plan is no longer offered in bid year.	
3				CMS Exception									This crosswalk includes both a segmentation and consolidation. Multiple current year plans segment and/or consolidate into multiple bid year plans.	

4				Plan to Plan Renewal (no SA change)				1 to 1 plan mapping with no service area changes.	
5				Plan to Plan Renewal (w SAE)					1 to 1 plan mapping with a service area expansion.	
6				Plan to Plan Renewal (w SAR)					1 to 1 plan mapping with a service area reduction.	
7				Plan to Plan Renewal (w SAE and SAR)			1 to 1 plan mapping with a service area expansion and reduction.	

8				Plan Consolidation (wo SAE and SAR)				All counties are mapped from multiple current year plans into a single bid year plan with no service area expansion (SAE) or reduction (SAR).	
9				Plan Consolidation (w SAE)						All counties are mapped from multiple current year plans into a single bid year plan with service area expansion (SAE) and no service area reduction (SAR).
10				Plan Consolidation (w SAR)						All counties are mapped from multiple current year plans into a single bid year plan with service area reduction (SAR) and no service area expansion (SAE). 	
11				Plan Consolidation (w SAE and SAR)				All counties are mapped from multiple current year plans into a single bid year plan with both service area reduction (SAR) and service area expansion (SAE).	

12				Plan Segmentation (wo SAE and SAR)				All counties from a single current year plan map into multiple bid year plans (no SAR or SAE to the bid year plan).	
13				Plan Segmentation (w SAE)						All counties from a single current year plan map into multiple bid year plans (at least one bid year plan mapped contains new counties not in the current year plan and all current year counties are mapped).	
14				Plan Segmentation (w SAR)						All counties from a single current year plan map into multiple bid year plans (at least one county from current year plan is not renewed but no expansion is in any bid year plan).	
15				Plan Segmentation (w SAE and SAR)				All counties from a single current year plan map into multiple bid year plans (at least one bid year plan mapped contains new counties not in the current year plan and at least one county from current year plan is not renewed).
*/

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER  
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- SEP-04-2020		1           Initial Version																								Michelle Gagne
-- OCT-02-2020      2           Ln 325 added "AND a.CPS IS NOT null"                                										Aleksandar Dimitrijevic
--								Ln 408 added "WHERE a.CPS IS NOT NULL"
--								Ln 628 added "WHERE a.CPS IS NOT NULL"
--								Ln 795 added "AND a.RenewalType <> 'Terminated Plan'"
-- OCT-05-2020		3			Added WHERE clause to lines 212 and 243																		Jake Lewis
--								Removed nested select statements / general code cleanup
-- OCT-20-2020		4			Fixed issues with sonar qube																				Deepali Mittal
-- DEC-17-2020		5			Ensure leading zero is brought in on SSStateCountyCD field													Jake Lewis
-- MAR-15-2021		6			Code adjustments to improve efficiency and decrease runtime													Jake Lewis
-- SEP-17-2021		7			Correct joins to consider projected MM NULL values															Craig Nielsen
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_Reporting_spCalcCountyXWalkMembership]
@LastUpdateByID CHAR(7)
AS
    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

		DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            ------------------------------------------------------
            -- 0. Declare variables and set up some temp tables --
            ------------------------------------------------------
            DECLARE @BidYear INT = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);
            DECLARE @CurrentYear INT = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);
            DECLARE @BaseYear INT = (SELECT PlanYearID FROM dbo.LkpIntPlanYear WHERE IsExperienceYear = 1);
            DECLARE @MinHistoricYear INT = @BaseYear - 3;
            DECLARE @RepCat VARCHAR(50) = 'IP'; -- Membership is duplicated for each RepCat in the current year MRA table; only need to select one.
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);

            -- Historic membership (county level)
            IF (SELECT  OBJECT_ID ('tempdb..#HistoricMMCounty')) IS NOT NULL
                DROP TABLE #HistoricMMCounty;
            SELECT      PlanInfoID
                       ,RIGHT('00000' + CONVERT (VARCHAR(5), SSStateCountyCD), 5) AS SSStateCountyCD
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
            INTO        #HistoricMMCounty
            FROM        dbo.Trend_CalcHistoricMembership
            GROUP BY    PlanInfoID
                       ,RIGHT('00000' + CONVERT (VARCHAR(5), SSStateCountyCD), 5);

            -- Historic membership (plan level)
            IF (SELECT  OBJECT_ID ('tempdb..#HistoricMM')) IS NOT NULL
                DROP TABLE #HistoricMM;
            SELECT      PlanInfoID
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
            INTO        #HistoricMM
            FROM        #HistoricMMCounty
            GROUP BY    PlanInfoID;

            -- Projected membership (county level)
            IF (SELECT  OBJECT_ID ('tempdb..#ProjectedMMCounty')) IS NOT NULL
                DROP TABLE #ProjectedMMCounty;
            SELECT      vpi.PlanInfoID
                       ,RIGHT('00000' + CONVERT (VARCHAR(5), a.SSStateCountyCD), 5) AS SSStateCountyCD
                       ,SUM (ISNULL (a.CY_MM, 0)) AS MemberMonths
            INTO        #ProjectedMMCounty
            FROM        dbo.Trend_PerPopulationMRACurrentYear a
           INNER JOIN   dbo.vwPlanInfo vpi
                   ON vpi.CPS = a.CPS
                      AND   vpi.PlanYear = @CurrentYear
            WHERE       a.ReportingCategory = @RepCat
            GROUP BY    vpi.PlanInfoID
                       ,RIGHT('00000' + CONVERT (VARCHAR(5), a.SSStateCountyCD), 5);

            -- Projected membership (plan level)
            IF (SELECT  OBJECT_ID ('tempdb..#ProjectedMM')) IS NOT NULL
                DROP TABLE #ProjectedMM;
            SELECT      PlanInfoID
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
            INTO        #ProjectedMM
            FROM        #ProjectedMMCounty
            GROUP BY    PlanInfoID;

            -- Combine historic and projected plan level membership
            IF (SELECT  OBJECT_ID ('tempdb..#AllMM')) IS NOT NULL DROP TABLE #AllMM;
            SELECT      PlanInfoID
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
            INTO    #AllMM
            FROM        dbo.Trend_CalcHistoricMembership
            WHERE       PlanYearID < @CurrentYear
            GROUP BY    PlanInfoID
            UNION
            SELECT  PlanInfoID, MemberMonths FROM   #ProjectedMM;

            --PlanInfoID List
            IF (SELECT  OBJECT_ID ('tempdb..#PlanInfoID')) IS NOT NULL
                DROP TABLE #PlanInfoID;
            --Non-bid year plans
            SELECT  DISTINCT
                    PlanInfoID
            INTO    #PlanInfoID
            FROM    dbo.vwPlanInfo
            WHERE   PlanYear >= @MinHistoricYear
                    AND PlanYear < @BidYear
                    AND IsHidden = 0
                    AND IsOffMAModel = 'No'
                    AND Region NOT IN ('Unmapped')
            UNION
            --Bid year plans
            SELECT      vsc.BidYearPlanInfoID
            FROM        dbo.vwSAMCrosswalks vsc
           INNER JOIN   dbo.SavedForecastSetup sfs
                   ON sfs.PlanInfoID = vsc.BidYearPlanInfoID
                      AND   sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID
           INNER JOIN   dbo.SavedRollupForecastMap map
                   ON map.ForecastID = sfs.ForecastID
           INNER JOIN   dbo.SavedRollupInfo sri
                   ON sri.RollupID = map.RollupID
            LEFT JOIN   dbo.vwPlanInfo vpi
                   ON vpi.PlanInfoID = sfs.PlanInfoID
            WHERE       sri.RollupName = 'Live'
                        AND vsc.CurrentYearCPS IS NOT NULL
                        AND vsc.BidYearCPS IS NOT NULL
                        AND vpi.IsHidden = 0
                        AND vpi.IsOffMAModel = 'No'
                        AND vpi.Region NOT IN ('Unmapped')
            GROUP BY    vsc.BidYearPlanInfoID;

            --Plan Info Shell (for termed, concurrent, and consolidated plans / not county level)
            --Historic
            IF (SELECT  OBJECT_ID ('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans;
            SELECT      DISTINCT
                        a.FromPlanInfoID
                       ,a.RenewalTypeID
                       ,a.PlanInfoID
            INTO    #Plans
            FROM        dbo.SavedHistoricalCrosswalk a
            LEFT JOIN   #PlanInfoID b
                   ON b.PlanInfoID = a.FromPlanInfoID
            WHERE       b.PlanInfoID IS NOT NULL
            UNION
            --Projected
            SELECT      DISTINCT
                        a.CurrentYearPlanInfoID
                       ,a.BidYearRenewalTypeID AS RenewalTypeID
                       ,b.PlanInfoID
            FROM        dbo.vwSAMCrosswalks a
           INNER JOIN   dbo.SavedProjectedCrosswalk b
                   ON a.CurrentYearPlanInfoID = b.FromPlanInfoID
                      AND   a.BidYearPlanInfoID = b.PlanInfoID
            LEFT JOIN   #PlanInfoID c
                   ON c.PlanInfoID = b.PlanInfoID
            WHERE       a.IsActive = 1
                        AND a.BidYearCPS IS NOT NULL
                        AND c.PlanInfoID IS NOT NULL;

            --Plan Info Shell (for segmented plans and CMS exceptions /  county level)
            --Historic
            IF (SELECT  OBJECT_ID ('tempdb..#PlansCountyLevel')) IS NOT NULL
                DROP TABLE #PlansCountyLevel;
            SELECT      DISTINCT
                        a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,RIGHT('00000' + CONVERT (VARCHAR(5), a.SSStateCountyCD), 5) AS SSStateCountyCD
                       ,a.RenewalTypeID
            INTO    #PlansCountyLevel
            FROM        dbo.SavedHistoricalCrosswalk a
            LEFT JOIN   #PlanInfoID b
                   ON b.PlanInfoID = a.FromPlanInfoID
            WHERE       b.PlanInfoID IS NOT NULL
            UNION
            --Projected
            SELECT      DISTINCT
                        a.CurrentYearPlanInfoID
                       ,b.PlanInfoID
                       ,RIGHT('00000' + CONVERT (VARCHAR(5), a.SSStateCountyCD), 5) AS SSStateCountyCD
                       ,a.BidYearRenewalTypeID AS RenewalTypeID
            FROM        dbo.vwSAMCrosswalks a
           INNER JOIN   dbo.SavedProjectedCrosswalk b
                   ON a.CurrentYearPlanInfoID = b.FromPlanInfoID
                      AND   a.BidYearPlanInfoID = b.PlanInfoID
                      AND   b.SSStateCountyCD = a.SSStateCountyCD
            LEFT JOIN   #PlanInfoID c
                   ON c.PlanInfoID = b.PlanInfoID
            WHERE       a.IsActive = 1
                        AND a.BidYearCPS IS NOT NULL
                        AND c.PlanInfoID IS NOT NULL;

            ---------------------
            -- 1. Termed Plans --
            ---------------------
            IF (SELECT  OBJECT_ID ('tempdb..#TermedPlans')) IS NOT NULL
                DROP TABLE #TermedPlans;
            SELECT      p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.RenewalTypeID
                       ,SUM (ISNULL (hm.MemberMonths, 0)) AS MemberMonths
            INTO        #TermedPlans
            FROM        #Plans p
            LEFT JOIN   #HistoricMM hm
                   ON hm.PlanInfoID = p.FromPlanInfoID
            WHERE       hm.MemberMonths IS NOT NULL
                        AND p.RenewalTypeID = 2
            GROUP BY    p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.RenewalTypeID;

            -------------------------
            -- 2. Concurrent Plans --
            -------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#ConcurrentPlans')) IS NOT NULL
                DROP TABLE #ConcurrentPlans;
            SELECT      p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.RenewalTypeID
                       ,SUM (COALESCE (pm.MemberMonths, hm.MemberMonths, 0)) AS MemberMonths
            INTO        #ConcurrentPlans
            FROM        #Plans p
            LEFT JOIN   #HistoricMM hm
                   ON hm.PlanInfoID = p.FromPlanInfoID
            LEFT JOIN   #ProjectedMM pm
                   ON pm.PlanInfoID = p.FromPlanInfoID
            WHERE        COALESCE (pm.PlanInfoID, hm.PlanInfoID) IS NOT NULL
                        AND p.PlanInfoID <> 0
                        AND p.RenewalTypeID IN (4, 5, 6, 7)
            GROUP BY    p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.RenewalTypeID;

            ----------------------------------
            -- 3. Select Consolidated Plans --
            ----------------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#ConsolidatedPlans')) IS NOT NULL
                DROP TABLE #ConsolidatedPlans;
            SELECT      p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.RenewalTypeID
                       ,SUM (COALESCE (pm.MemberMonths, hm.MemberMonths, 0)) AS MemberMonths
            INTO        #ConsolidatedPlans
            FROM        #Plans p
            LEFT JOIN   #HistoricMM hm
                   ON hm.PlanInfoID = p.FromPlanInfoID
            LEFT JOIN   #ProjectedMM pm
                   ON pm.PlanInfoID = p.FromPlanInfoID
            WHERE       COALESCE (pm.PlanInfoID, hm.PlanInfoID) IS NOT NULL
                        AND p.PlanInfoID <> 0
                        AND p.RenewalTypeID IN (8, 9, 10, 11)
            GROUP BY    p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.RenewalTypeID;

            -------------------------------
            -- 4. Select Segmented Plans --
            -------------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#SegmentedPlans')) IS NOT NULL
                DROP TABLE #SegmentedPlans;
            SELECT      p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.SSStateCountyCD
                       ,p.RenewalTypeID
                       ,SUM (COALESCE (pm.MemberMonths, hm.MemberMonths, 0)) AS MemberMonths
            INTO        #SegmentedPlans
            FROM        #PlansCountyLevel p
            LEFT JOIN   #HistoricMMCounty hm
                   ON hm.PlanInfoID = p.FromPlanInfoID
                      AND   hm.SSStateCountyCD = p.SSStateCountyCD
            LEFT JOIN   #ProjectedMMCounty pm
                   ON pm.PlanInfoID = p.FromPlanInfoID
                      AND   pm.SSStateCountyCD = p.SSStateCountyCD
            WHERE      COALESCE (pm.PlanInfoID, hm.PlanInfoID) IS NOT NULL
                        AND p.PlanInfoID <> 0
                        AND p.RenewalTypeID IN (12, 13, 14, 15)
            GROUP BY    p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.SSStateCountyCD
                       ,p.RenewalTypeID;

            --Sum #SegementedPlans by both FromPlanInfoID and PlanInfoID
            IF (SELECT  OBJECT_ID ('tempdb..#SegmentedSumIDAndFromID')) IS NOT NULL
                DROP TABLE #SegmentedSumIDAndFromID;
            SELECT      FromPlanInfoID
                       ,PlanInfoID
                       ,SUM (MemberMonths) AS MemberMonths
            INTO        #SegmentedSumIDAndFromID
            FROM        #SegmentedPlans
            GROUP BY    FromPlanInfoID
                       ,PlanInfoID;

            --Sum #SegmentedPlans by FromPlanInfoID
            IF (SELECT  OBJECT_ID ('tempdb..#SegmentedSumFromID')) IS NOT NULL
                DROP TABLE #SegmentedSumFromID;
            SELECT      FromPlanInfoID
                       ,SUM (MemberMonths) AS MemberMonths
            INTO        #SegmentedSumFromID
            FROM        #SegmentedPlans
            GROUP BY    FromPlanInfoID;

            --Segmented renewal type
            IF (SELECT  OBJECT_ID ('tempdb..#SegmentedRenewal')) IS NOT NULL
                DROP TABLE #SegmentedRenewal;
            SELECT  DISTINCT
                    FromPlanInfoID
                   ,PlanInfoID
                   ,RenewalTypeID
            INTO    #SegmentedRenewal
            FROM    #SegmentedPlans;

            --Break apart OOA and Non-CW Membership into plans
            IF (SELECT  OBJECT_ID ('tempdb..#SegmentedPlansFinal')) IS NOT NULL
                DROP TABLE #SegmentedPlansFinal;
            SELECT      a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,sr.RenewalTypeID
                       ,dbo.Trend_fnSafeDivide (SUM (ISNULL (a.MemberMonths, 0)), SUM (ISNULL (b.MemberMonths, 0)), 0)
                        * SUM (ISNULL (amm.MemberMonths, 0)) AS MemberMonths
            INTO        #SegmentedPlansFinal
            FROM        #SegmentedSumIDAndFromID a
            LEFT JOIN   #SegmentedSumFromID b
                   ON a.FromPlanInfoID = b.FromPlanInfoID
            LEFT JOIN   #AllMM amm
                   ON a.FromPlanInfoID = amm.PlanInfoID
            LEFT JOIN   #SegmentedRenewal sr
                   ON sr.FromPlanInfoID = a.FromPlanInfoID
                      AND   sr.PlanInfoID = a.PlanInfoID
            GROUP BY    a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,sr.RenewalTypeID;

            ------------------------------
            -- 5. Select CMS Exceptions --
            ------------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#CMSExceptions')) IS NOT NULL
                DROP TABLE #CMSExceptions;
            SELECT      p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.SSStateCountyCD
                       ,p.RenewalTypeID
                       ,SUM (COALESCE (pm.MemberMonths, hm.MemberMonths, 0)) AS MemberMonths
            INTO        #CMSExceptions
            FROM        #PlansCountyLevel p
            LEFT JOIN   #HistoricMMCounty hm
                   ON hm.PlanInfoID = p.FromPlanInfoID
                      AND   hm.SSStateCountyCD = p.SSStateCountyCD
            LEFT JOIN   #ProjectedMMCounty pm
                   ON pm.PlanInfoID = p.FromPlanInfoID
                      AND   pm.SSStateCountyCD = p.SSStateCountyCD
            WHERE       COALESCE (pm.PlanInfoID, hm.PlanInfoID) IS NOT NULL
                        AND p.PlanInfoID <> 0
                        AND p.RenewalTypeID IN (3)
            GROUP BY    p.FromPlanInfoID
                       ,p.PlanInfoID
                       ,p.SSStateCountyCD
                       ,p.RenewalTypeID;

            --Sum #CMSExceptions by both FromPlanInfoID and PlanInfoID
            IF (SELECT  OBJECT_ID ('tempdb..#CMSExceptionSumIDAndFromID')) IS NOT NULL
                DROP TABLE #CMSExceptionSumIDAndFromID;
            SELECT      FromPlanInfoID
                       ,PlanInfoID
                       ,SUM (MemberMonths) AS MemberMonths
            INTO        #CMSExceptionSumIDAndFromID
            FROM        #CMSExceptions
            GROUP BY    FromPlanInfoID
                       ,PlanInfoID;

            --Sum #CMSExceptions by FromPlanInfoID
            IF (SELECT  OBJECT_ID ('tempdb..#CMSExceptionSumFromID')) IS NOT NULL
                DROP TABLE #CMSExceptionSumFromID;
            SELECT      FromPlanInfoID
                       ,SUM (MemberMonths) AS MemberMonths
            INTO        #CMSExceptionSumFromID
            FROM        #CMSExceptions
            GROUP BY    FromPlanInfoID;

            --Renewal Type Lookup
            IF (SELECT  OBJECT_ID ('tempdb..#CMSExceptionRenewal')) IS NOT NULL
                DROP TABLE #CMSExceptionRenewal;
            SELECT  DISTINCT
                    FromPlanInfoID
                   ,PlanInfoID
                   ,RenewalTypeID
            INTO    #CMSExceptionRenewal
            FROM    #CMSExceptions;

            --Break apart OOA and Non-CW Membership into plans
            IF (SELECT  OBJECT_ID ('tempdb..#CMSExceptionsFinal')) IS NOT NULL
                DROP TABLE #CMSExceptionsFinal;
            SELECT      a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,cer.RenewalTypeID
                       ,dbo.Trend_fnSafeDivide (SUM (ISNULL (a.MemberMonths, 0)), SUM (ISNULL (b.MemberMonths, 0)), 0)
                        * SUM (ISNULL (amm.MemberMonths, 0)) AS MemberMonths
            INTO        #CMSExceptionsFinal
            FROM        #CMSExceptionSumIDAndFromID a
            LEFT JOIN   #CMSExceptionSumFromID b
                   ON a.FromPlanInfoID = b.FromPlanInfoID
            LEFT JOIN   #AllMM amm
                   ON a.FromPlanInfoID = amm.PlanInfoID
            LEFT JOIN   #CMSExceptionRenewal cer
                   ON cer.FromPlanInfoID = a.FromPlanInfoID
                      AND   cer.PlanInfoID = a.PlanInfoID
            GROUP BY    a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,cer.RenewalTypeID;

            -------------------------------------------
            -- 6. Delete from / write to final table --
            -------------------------------------------
            DELETE  FROM dbo.Trend_Reporting_CalcCountyXWalkMembership WHERE    1 = 1;
            IF (SELECT  OBJECT_ID ('tempdb..#OutputCalcCountyXWalkMembership')) IS NOT NULL
                DROP TABLE #OutputCalcCountyXWalkMembership;
            CREATE TABLE #OutputCalcCountyXWalkMembership
                (FromPlanInfoID        SMALLINT   NOT NULL
                ,PlanInfoID            SMALLINT   NOT NULL
                ,RenewalTypeID         INT        NOT NULL
                ,ReportingMemberMonths DEC(20, 10)
                ,MemberMonths          DEC(20, 10)
                ,LastUpdateByID        CHAR(7)    NOT NULL
                ,LastUpdateDateTime    DATETIME);

            --Termed plans
            INSERT INTO #OutputCalcCountyXWalkMembership
            SELECT      a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID
                       ,SUM (ISNULL (a.MemberMonths, 0)) AS ReportingMemberMonths
                       ,SUM (COALESCE (c.MemberMonths, b.MemberMonths, 0)) AS MemberMonths
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        #TermedPlans a
            LEFT JOIN   #HistoricMM b
                   ON b.PlanInfoID = a.FromPlanInfoID
            LEFT JOIN   #ProjectedMM c
                   ON c.PlanInfoID = a.FromPlanInfoID
            WHERE       a.MemberMonths IS NOT NULL
                        AND a.RenewalTypeID <> 2
            GROUP BY    a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID;

            --Concurrent plans
            INSERT INTO #OutputCalcCountyXWalkMembership
            SELECT      a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID
                       ,SUM (ISNULL (a.MemberMonths, 0)) AS ReportingMemberMonths
                       ,SUM (COALESCE (c.MemberMonths, b.MemberMonths, 0)) AS MemberMonths
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        #ConcurrentPlans a
            LEFT JOIN   #HistoricMM b
                   ON b.PlanInfoID = a.FromPlanInfoID
            LEFT JOIN   #ProjectedMM c
                   ON c.PlanInfoID = a.FromPlanInfoID
            WHERE       a.MemberMonths IS NOT NULL
                        AND a.RenewalTypeID <> 2
            GROUP BY    a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID;

            --Consolidated plans
            INSERT INTO #OutputCalcCountyXWalkMembership
            SELECT      a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID
                       ,SUM (ISNULL (a.MemberMonths, 0)) AS ReportingMemberMonths
                       ,SUM (COALESCE (c.MemberMonths, b.MemberMonths, 0)) AS MemberMonths
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        #ConsolidatedPlans a
            LEFT JOIN   #HistoricMM b
                   ON b.PlanInfoID = a.FromPlanInfoID
            LEFT JOIN   #ProjectedMM c
                   ON c.PlanInfoID = a.FromPlanInfoID
            WHERE       a.MemberMonths IS NOT NULL
                        AND a.RenewalTypeID <> 2
            GROUP BY    a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID;

            --Segmented plans
            INSERT INTO #OutputCalcCountyXWalkMembership
            SELECT      a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID
                       ,SUM (ISNULL (a.MemberMonths, 0)) AS ReportingMemberMonths
                       ,SUM (COALESCE (c.MemberMonths, b.MemberMonths, 0)) AS MemberMonths
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        #SegmentedPlansFinal a
            LEFT JOIN   #HistoricMM b
                   ON b.PlanInfoID = a.FromPlanInfoID
            LEFT JOIN   #ProjectedMM c
                   ON c.PlanInfoID = a.FromPlanInfoID
            WHERE       a.MemberMonths IS NOT NULL
                        AND a.RenewalTypeID <> 2
            GROUP BY    a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID;

            --CMS exceptions
            INSERT INTO #OutputCalcCountyXWalkMembership
            SELECT      a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID
                       ,SUM (ISNULL (a.MemberMonths, 0)) AS ReportingMemberMonths
                       ,SUM (COALESCE (c.MemberMonths, b.MemberMonths, 0)) AS MemberMonths
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        #CMSExceptionsFinal a
            LEFT JOIN   #HistoricMM b
                   ON b.PlanInfoID = a.FromPlanInfoID
            LEFT JOIN   #ProjectedMM c
                   ON c.PlanInfoID = a.FromPlanInfoID
            WHERE       a.MemberMonths IS NOT NULL
                        AND a.RenewalTypeID <> 2
            GROUP BY    a.FromPlanInfoID
                       ,a.PlanInfoID
                       ,a.RenewalTypeID;
					   
            -- Write output to table
            INSERT INTO dbo.Trend_Reporting_CalcCountyXWalkMembership
                (FromPlanInfoID
                ,PlanInfoID
                ,RenewalTypeID
                ,ReportingMemberMonths
                ,MemberMonths
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  FromPlanInfoID
                   ,PlanInfoID
                   ,RenewalTypeID
                   ,ReportingMemberMonths
                   ,MemberMonths
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #OutputCalcCountyXWalkMembership;
            COMMIT TRANSACTION transactionMain;
        END TRY
        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@LastUpdateByID
               ,GETDATE ();
    END;
GO
