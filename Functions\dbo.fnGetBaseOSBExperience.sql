SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [BEAR].[fnGetBaseOSBExperience] ******/

-- User Defined Function 

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetBaseOSBExperience
--
-- AUTHOR: <PERSON> Allen
--
-- CREATED DATE:   2013-Jan-24
-- HEADER UPDATED: 2013-Jan-24
--
-- DESCRIPTION: Displays OSB experience by retreiving sum of contract level OSB experience of plans
-- listed in base. If contract is fully manual displays OSB experience of bid year contract.
--
-- PARAMETERS: 
--  Input: 
--      @ForecastID 
--  Output:
--
-- TABLES: 
--  Read:
--      SavedForecastSetup
--      SavedPlanInfo
--      PerIntBaseOSBExperience
--
--  Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2013-Jan-24      1           Initial version.													Lindsay Allen
-- 2013-May-14      2           Created new table, contract logic, manual case.                     Lindsay Allen
-- 2013-May-17      3           Will now display same OSB experience for all 2014 contracts         Lindsay Allen
-- 2015-May-11      4           changed OSBNetMedicalExpenses, OSBNonBenefitExpenses, OSBPremium,   Sharath Chandra 
--                                data type to decimal(16,6) while declaring @results table.	
-- 2020-Jun-29      5           Backend Alignment and Restructuring                                 Keith Galloway
-- 2023-Aug-03      6           Added NOLOCK and Internal parameter                                 Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnGetBaseOSBExperience] (@ForecastID INT)
RETURNS @Results TABLE
    (
      ForecastID INT ,
      OSBNetMedicalExpenses DECIMAL(16, 6),
      OSBNonBenefitExpenses DECIMAL(16, 6),
      OSBPremium DECIMAL(16, 6),
      OSBMemberMonths INT
    )
    BEGIN

  DECLARE @XForecastID INT = @ForecastID 

    INSERT  INTO @Results
		SELECT  SFS.ForecastID,
				OSBNetMedicalExpenses = ISNULL(OSB.NetMedicalExpenses,0),
				OSBNonBenefitExpenses = ISNULL(OSB.NonBenefitExpenses,0),
				OSBPremium = ISNULL(OSB.Premium,0),
				OSBMemberMonths = ISNULL(OSB.MemberMonths,0)
		FROM    dbo.SavedForecastSetup SFS WITH (NOLOCK)
		LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
			ON SFS.PlanInfoID = SPI.PlanInfoID
		LEFT JOIN dbo.PerIntBaseOSBExperience OSB WITH (NOLOCK)
			ON LEFT(SPI.CPS,5) = OSB.ContractNumber
		WHERE   SFS.ForecastID = @XForecastID

    RETURN

    END
GO
