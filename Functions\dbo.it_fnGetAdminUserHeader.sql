SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: it_fnGetAdminUserHeader
--
-- AUTHOR: <PERSON><PERSON><PERSON>
--
-- CREATED DATE: 2011-Jan-13
--
-- DESCRIPTION: Function responsible for returning selectable user records from the admin table. 
--				Depending on what type of users eed to be retrieved parameters @isContact and @isCertifying 
--				can be assigned 1 or 0. If none of the parameters are assigned values all selectable users
--				are returned. 
--
-- PARAMETERS:
--	Input:
--	    @isContact
--		@isCertifying
-- TABLES: 
--	Read:
--		CalcDeductibleMOOPCategoryFactors
--		LkpIntMOOP<PERSON>ategoryHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-13		1			Initial Version														Catalin Tomescu
-- 2019-Oct-25      2           Alter UserID size                                                   Manisha Tyagi
-- ----------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[it_fnGetAdminUserHeader]
(
    @isContact BIT = NULL,
	@isCertifying BIT = NULL
)
RETURNS @Results TABLE 
(
	[UserID] [char](7) NOT NULL,
	[UserLastName] [varchar](25) NOT NULL,
	[UserFirstName] [varchar](25) NOT NULL,
	[UserMiddleInitial] [char](1) NULL,
	[UserContactTitle] [varchar](35) NULL,
	[UserCertifyingTitle] [varchar](35) NULL,
	[UserHumanaTitle] [varchar](35) NOT NULL,
	[UserEmail] [varchar](25) NOT NULL,
	[UserPhone] [varchar](12) NOT NULL,
	[UserFax] [varchar](12) NOT NULL,
	[IsSelectable] [bit] NOT NULL,
	[IsContact] [bit] NOT NULL,
	[IsCertifying] [bit] NOT NULL,
	[IsEnabled] [bit] NOT NULL,
	[UserHomePage] [tinyint] NOT NULL,
	[RoleID] [tinyint] NOT NULL
) AS
BEGIN
	INSERT @Results
	SELECT 
		[UserID],
		[UserLastName],
		[UserFirstName],
		[UserMiddleInitial],
		[UserContactTitle],
		[UserCertifyingTitle],
		[UserHumanaTitle],
		[UserEmail],
		[UserPhone],
		[UserFax],
		[IsSelectable],
		[IsContact],
		[IsCertifying],
		[IsEnabled],
		[UserHomePage],
		[RoleID]
	FROM dbo.AdminUserHeader
	WHERE IsSelectable = 1 
		AND (@isContact IS NULL OR (@isContact IS NOT NULL AND IsContact = @isContact))
		AND (@isCertifying IS NULL OR (@isCertifying IS NOT NULL AND IsCertifying = @isCertifying))
	RETURN 
END
GO
