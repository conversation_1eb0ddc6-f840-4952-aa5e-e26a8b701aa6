SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetStateTerritoryCountyForMarket]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetStateTerritoryCountyForMarket
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE: 2008-Feb-25
-- HEADER UPDATED: 2010-Sep-24
--
-- DESCRIPTION: Returns a recordset containing all StateTerritoryID & CountyCode combinations that are part of the
--              specified market and plan year.
--
-- PARAMETERS:
--  Input:
--      @MarketID
--  Output:
--
-- TABLES: 
--  Read:
--      LkpExtCMSMARegionDetail
--      LkpExtCMSStateCounty
--      SavedMarketDetail
--  Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-Feb-25      1           Initial version.                                                    Tonya Cockrell
-- 2010-Sep-24      2           Removed @PlanYearID                                                 Joe Casey
-- 2018-Aug-27      3           Removed SavedMarketDetail filter                                    Apoorva Nasa                                     
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetStateTerritoryCountyForMarket]()
RETURNS @Result TABLE
(
    StateTerritoryID TINYINT NOT NULL,
    CountyCode CHAR(3) NOT NULL
)
AS
BEGIN 

            INSERT @Result
            SELECT DISTINCT
                sc.StateTerritoryID,
                sc.CountyCode
            FROM LkpExtCMSMARegionDetail rd
            INNER JOIN LkpExtCMSStateCounty sc
                ON rd.StateTerritoryID = sc.StateTerritoryID
           
        

    RETURN

END
GO
