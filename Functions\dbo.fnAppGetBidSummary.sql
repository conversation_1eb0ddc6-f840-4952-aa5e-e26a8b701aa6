SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetBidSummary
--
-- AUTHOR: Christian Cofie 
--
-- CREATED DATE: 2007-Mar-19
-- HEADER UPDATED: 2010-Sep-21 
--
-- DESCRIPTION: Function responsible for listing values on the Bid Summary tab in the MAPD Model.
--
-- PARAMETERS:
--	Input: 
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		CalcBenchmarkSummary 
--		CalcFinalPremium
--      CalcMedicareCoveredFinalPricing
--		Calc<PERSON>lanProjection
--		CalcPlanESRDSubsidy
--		PerEx<PERSON><PERSON><PERSON><PERSON>ues
--      SavedPlanAssumptions
--		Saved<PERSON><PERSON><PERSON>eader
--		SavedPlanAddedBenefits
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnAppGetBenefitsChangeMOOPDedImpact
--		fnAppGetPlanExpenseProfit
--		fnGetCredibilityFactor
--		fnAppGetMABPTWS3
--		fnPlanCountyProjectedMemberMonths
--		fnAppGetBidSummaryFactors
--		fnAppGetMABPTWS4CostShares
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
---
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007-Mar-19		1			Initial Version														Christian Cofie
-- 2007-Sep-10		2			Revised code to bring it into coding standards.						Shannon Boykin
-- 2007-Sep-17		3			Additional revisions to bring code into coding standards.			Shannon Boykin
-- 2007-Sep-24		4			Additional revisions to bring code into coding standards -			Shannon Boykin
--									Lined up paranthesis differently to conserve line space.
-- 2008-Feb-25		5			Added in CalcRxPremium for Rx premiums								Sandy Ellis
-- 2008-Feb-28		6			Added version to the fnGetPlanExpenseProfit call					Brian Lake
-- 2008-Apr-11		7			Added div by 0 error catch to StandardizedCostShare					Brian Lake
--									calculation
-- 2008-Apr-21		8			Added Join on SavedMAPDOffModelRxDetail for Rx Basic/Supp			Brian Lake
--									"OFF MODEL" Rx Adjustments.  Replaced fnGetPlanExpenseProfit 
--									with fnAppGetPlanExpenseProfit
-- 2008-Apr-30		9			Updated Credibility calculation, with significant digits.			Brian Lake
-- 2008-May-15		10			Replaced CostAndUseCredibility calculation with call to				Tonya Cockrell
--									fnGetCredibilityFactor.
-- 2008-May-16		11			Improve CS and Supp prem calc to 2 decimals to match bid			Sandy Ellis
--									form.
-- 2008-Dec-02		12			Removed all references to CalcRxPremium.  Simplified syntax			Tonya Cockrell
--									for join with fnAppGetPlanExpenseProfit.
-- 2009-Feb-23		13			Replaced instances of CalcPlanCountySummary with					Brian Lake
--									CalcBenchmarkSummary
-- 2009-Mar-11		14			Added PlanExpMembers, PlanProjMembers, & PlanRiskFactor				Keith Galloway
-- 2009-Mar-17		15			Data Types															Sandy Ellis          
-- 2009-Mar-16		16			Added CurrentYearCostShare, ProjectedYearCostShare,					Lawrence Choi
--									CurrentYearAllowed, ProjectedYearAllowed,        
--									CurrentYearBenefitFactor and ProjectedYearBenefitFactor
--									by joining to vwFinalBenefitPricing (for PlanYearID <=2009)
--									or vwFinalBenefitPricingV01 (2010+)
-- 2009-Mar-27		17			Added condition to restrict DualEligibleTypeID = 3					Keith Galloway
-- 2009-Mar-29		18			Put conditions on Off model Rx to exclude it when not used			Sandy Ellis
-- 2009-Apr-21		19			Added CalcMedicareCov'd Final Pricing.Dual Type 3					Brian Lake
-- 2009-Apr-30		20			Added ExpensePMPM													Keith Galloway
-- 2009-May-01		21			Added PlanProjected DE & NonDE Membership							Keith Galloway
-- 2010-Mar-11		22			Added 2011 and on methodology, specifically using					James Wu													
--									FnAppGetBenefitsChangeMOOPDedImpact to get 
--									FinalCostShare values, and changing how current/bid
--									benefit factors are calculated
-- 2010-Jul-20		23			Moved to 2012														Joe Casey
-- 2010-Aug-23		24			Removed PlanVersion													Joe Casey
-- 2010-Sep-21      35          Removed SavedMAPDHeader, SavedMAPDOffModelRxDetail and added        Joe Casey
--                                  SavedPlanAssumptions
-- 2010-Sep-28      36          Adjusted for rename of LkpExtCMSValues to PerExtCMSValues           Jake Gaecke
-- 2010-Oct-06      37          Changed the DualEligibleTypeID on CalcPlanProjection from 3 to 2    Joe Casey
-- 2011-Jan-24      38          Changed Projected Membership from INT to Decimal                    Michael Siekerka
-- 2012-Aug-27		39			Deleted CS and Sup_prem calculation for partB Only to be like		Tim Gao
--								regular plans (original CS=0 and Sup_prem=TotalReqRev
--								* (1- MedicareCoveredPct)
-- 2013-Mar-25		40			Added in Insurer Fee												Mike Deren
-- 2014-May-15		11			Modified for Related parties and SQS								Mike Deren
-- 2015-May-07		42			Applied NOLOCK to fetched Tables									Manish Shukla
-- 2018-Mar-06		43			Added the 9 SCT Allowed Values to the data pull						Jordan Purdue
-- 2018-Mar-06		44			Added WS3TotalCostShare	for Actuarial Summary Changes				Chris Fleming
-- 2018-Mar-22		45			Changed WS3TotalCostShare to equal ProjectedYearCostShare			Chris Fleming
-- 2018-Mar-23      46          Changed for SCT values If null get 0 for                            Manisha Tyagi
-- 2018-Mar-26		47			Modified the Net Formula to capture non Safe Harbor plans			Jordan Purdue 
-- 2018-Apr-05		48			Modified WS3 Cost Share and changed AddedBenefit section			Chris Fleming
-- 2018-May-02		49			Modifed WS3 Cost Share to include a where IsHidden = 0				Jordan Purdue 	
-- 2018-Oct-25      50          Removed the 9 SCT Allowed Values from data pull                     Apoorva Nasa
-- 2020-Jan-03		51			Added ESRD Subsidy row, to TotalReqRev, and TotalNet row			Jordan Purdue
-- 2022-May-02		52			MAAUI migration; replaced input variable from @PlanIndex to 
--								@ForecastID; returning ForecastID instead on PlanIndex;
--								for table CalcMedicareCoveredFinalPricing, replaced reference
--								from PlanIndex to ForecastID; removed nested queries(function
--								dropped and recreated); removed "CASE WHEN sph.IsPartBOnly = 1"		Aleksandar Dimitrijevic
-- 2022-Dec-16		53			4119454 Deployment objects from Dev-Grow							Aleksandar Dimitrijevic
-- 2023-Aug-01		54			Added Nolock, internal variable										Sheetal Patil 
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetBidSummary]
    (
    @ForecastID INT
    )
RETURNS @Results TABLE 
    (
    ForecastID INT,
    IsMAPD BIT, 
    IsSNP BIT, 
    Rebate DECIMAL(8,2), 
    RetainedPremium DECIMAL(8,2),
    Savings DECIMAL(8,2), 
    StandardizedBenchmark FLOAT, 
    StandardizedBid DECIMAL(7,2), 
    PlanBenchmark FLOAT, 
    PlanBid FLOAT, 
    TotalReqRev FLOAT, 
    MedicareCoveredPct FLOAT, 
    Profit FLOAT, 
    ValueAdded INT, 
    Expenses FLOAT, 
    Reins FLOAT, 
    UserFee DECIMAL(7,6), 
    UncollectedPremium FLOAT, 
    NetNonESRD FLOAT,
	ESRDSubsidy FLOAT,
	TotalNet FLOAT,
    Allowed FLOAT, 
    MedicareCoveredAllowed DECIMAL(20,12),
    ProfitPercent DECIMAL(10,8),
    ExpensePercent DECIMAL(10,8),
	ExpensePMPM DECIMAL(18, 12),
    CostAndUseCredibility FLOAT, 
    PartBMaxRebateAllocation DECIMAL(12,6), 
    CoveredActEquivNet DECIMAL(14,6), 
    CostShareReduction DECIMAL(14,6), 
    AddedBenefitNet DECIMAL(14,6), 
    SupplementalBenefitTotal DECIMAL(14,6), 
    AddedBenefitPct FLOAT, 
    CostShareReductionPct FLOAT, 
    ReinsPercent INT, 
    BasicMemberPremium FLOAT, 
    GovtPremiumAdj FLOAT, 
    RxBasicPremium DECIMAL(9,2),
    RxSuppPremium DECIMAL(9,2),
    FFSEquivCostShare DECIMAL(23,15),
    StandardizedCostShare DECIMAL(23,15), 
	PlanExperienceMembership INT,
	PlanProjectedMembership DECIMAL(23,15),
	PlanProjectedNonDualMembership DECIMAL(23,15),
	PlanProjectedDualMembership DECIMAL(23,15),
	PlanRiskFactor DECIMAL(23,15),
    PartBPremiumBuydown DECIMAL(9,1),
    CS DECIMAL(10,2),
    Sup_Prem DECIMAL(10,2),
    CurrentYearCostShare DECIMAL(23,15),
    ProjectedYearCostShare DECIMAL(23,15),
    CurrentYearAllowed DECIMAL(23,15),
    ProjectedYearAllowed DECIMAL(23,15),        
    CurrentYearBenefitFactor DECIMAL(14,6),  
    ProjectedYearBenefitFactor DECIMAL(14,6),
    InsurerFee DECIMAL(8,6),
	WS3TotalCostShare DECIMAL(23,15)
    ) AS

BEGIN  
DECLARE @XForecastID INT = @ForecastID 
		-- Added Benefits
		DECLARE @AB TABLE
			(
			 ForecastID INT,
			 INAddedBenefitNet FLOAT,
			 INAddedBenefitCostShare FLOAT
			)

		INSERT INTO @AB
		SELECT ForecastID, 
			   INAddedBenefitNet = SUM(INAddedBenefitAllowed - INAddedBenefitCostShare),
			   INAddedBenefitCostShare = SUM(INAddedBenefitCostShare) 
		FROM dbo.SavedPlanAddedBenefits WITH (NOLOCK)
		WHERE ForecastID = @XForecastID and IsHidden = 0
		GROUP BY ForecastID

		--Bid Summary Cost Sharing

			--CS
			DECLARE @CS TABLE
				(
				ForecastID INT,
				CS FLOAT
				)

				--MM
				DECLARE @MM TABLE
					(
					ForecastID INT,
					NonDEMM FLOAT,
					DEMM FLOAT,
					TotalMM FLOAT
					)
				INSERT INTO @MM
				SELECT DE.ForecastID, 
					   NonDEMM = SUM(NonDE.ProjectedMemberMonths), 
					   DEMM = SUM(DE.ProjectedMemberMonths), 
					   TotalMM = SUM(Total.ProjectedMemberMonths)
				FROM dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,0) NonDE
				INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,1) DE
					ON NonDE.ForecastID = DE.ForecastID
				   AND NonDE.StateTerritoryID = DE.StateTerritoryID
				   AND NonDE.CountyCode = DE.CountyCode
				INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,2) Total
					ON Total.ForecastID = DE.ForecastID
				   AND Total.StateTerritoryID = DE.StateTerritoryID
				   AND Total.CountyCode = DE.CountyCode
				GROUP BY DE.ForecastID

			-------------------------------------------------------------------------------------------
			INSERT INTO @CS
			SELECT cs.ForecastID,
				   CS = SUM(CASE WHEN DualEligibleTypeID = 1 THEN (cs.CostShare * mm.DEMM) / mm.TotalMM
								 ELSE (cs.CostShare * mm.NonDEMM) / mm.TotalMM END)
			FROM dbo.fnAppGetMABPTWS4CostShares(@XForecastID) cs
			INNER JOIN @MM mm
				ON cs.ForecastID = mm.ForecastID
			GROUP BY cs.ForecastID

			--------------------------------------------------------------------------------------------

			--Allowed
			DECLARE @Allowed TABLE
				(
				ForecastID INT,
				Allowed FLOAT
				)

			INSERT INTO @Allowed
			SELECT ForecastID,
				   Allowed = INAllowed + OONAllowed 
			FROM dbo.CalcPlanProjection WITH (NOLOCK)
			WHERE ForecastID = @XForecastID 
			  AND DualEligibleTypeID = 2
			----------------------------------------

			--FinalCostShareSummary
			DECLARE @FinalCostShareSummary TABLE
				(
				ForecastID INT,
				IsBenefitYearCurrentYear BIT,
				FinalCostShare FLOAT
				)

			INSERT INTO @FinalCostShareSummary
			SELECT ForecastID,
				   IsBenefitYearCurrentYear,
				   FinalCostShare = SUM(TotalAfterMoopCS)
			FROM dbo.fnAppGetBenefitsChangeMOOPDedImpact(@XForecastID)
			GROUP BY ForecastID,
				     IsBenefitYearCurrentYear

		---------------------------------------------------------------------------

		--BidSummaryCostSharing
		DECLARE @BidSummaryCostSharing TABLE
			(
			ForecastID INT,
			CurrentYearCostShare FLOAT,
			ProjectedYearCostShare FLOAT,
			CurrentYearAllowed FLOAT,
			ProjectedYearAllowed FLOAT,
			CurrentYearBenefitFactor FLOAT,
			ProjectedYearBenefitFactor FLOAT
			)

		INSERT INTO @BidSummaryCostSharing
		SELECT FinalCostShareSummary.ForecastID,
			   CurrentYearCostShare = 
					SUM(CASE
							WHEN FinalCostShareSummary.IsBenefitYearCurrentYear = 1
								THEN FinalCostShareSummary.FinalCostShare 
							ELSE NULL
						END), 
               ProjectedYearCostShare = 
					SUM(CASE
							WHEN FinalCostShareSummary.IsBenefitYearCurrentYear = 0
								THEN FinalCostShareSummary.FinalCostShare 
							ELSE NULL
						END), 
               CurrentYearAllowed =                              
					SUM(CASE
							WHEN FinalCostShareSummary.IsBenefitYearCurrentYear = 1
								THEN mcfp.Allowed 
							ELSE NULL
						END),
               ProjectedYearAllowed = 
					SUM(CASE
							WHEN FinalCostShareSummary.IsBenefitYearCurrentYear = 0
								THEN mcfp.Allowed 
							ELSE NULL
						END),
               CurrentYearBenefitFactor = 
					SUM(CASE                        
							WHEN FinalCostShareSummary.IsBenefitYearCurrentYear = 1
								THEN 
									1 -
									(
									CASE
										WHEN cpp.INAllowed + ISNULL(cpp.OONAllowed,0) = 0
											THEN 0
										ELSE (FinalCostShareSummary.FinalCostShare
											/ (cpp.INAllowed + ISNULL(cpp.OONAllowed,0)))
									END
									)
							ELSE NULL 
						END),
               ProjectedYearBenefitFactor = 
					1 - (dbo.fnGetSafeDivisionResult(cs.CS,alld.Allowed))
        FROM dbo.CalcMedicareCoveredFinalPricing mcfp WITH(NOLOCK)
		INNER JOIN @CS cs
			ON mcfp.ForecastID = cs.ForecastID
		INNER JOIN @Allowed alld
			ON mcfp.ForecastID = alld.ForecastID
        INNER JOIN @FinalCostShareSummary FinalCostShareSummary  --This table was changed for 2011 and on methodology.
			ON mcfp.ForecastID = FinalCostShareSummary.ForecastID
		INNER JOIN dbo.CalcPlanProjection cpp WITH(NOLOCK)
			ON mcfp.ForecastID = cpp.ForecastID
			AND mcfp.DualEligibleTypeID = cpp.DualEligibleTypeID
        WHERE mcfp.DualEligibleTypeID = 2
        GROUP BY FinalCostShareSummary.ForecastID,
				 cs.CS,
				 alld.Allowed

		--Projected Allowed
		DECLARE @ProjAlld TABLE
			(
			ForecastID INT,
			ProjectedAllowed FLOAT
			)

		INSERT INTO @ProjAlld
		SELECT ForecastID,
			   ProjectedAllowed 
		FROM dbo.fnAppGetBidSummaryFactors(@XForecastID,3)

		--MABPTWS3
		DECLARE @MABPTWS3 TABLE
			(
			ForecastID INT,
			TotalCS FLOAT
			)

		INSERT INTO @MABPTWS3
		SELECT	ForecastID, 
				TotalCS = SUM(INCostShare + OONCostShare) 
		FROM dbo.fnAppGetMABPTWS3(@XForecastID) 
		GROUP BY ForecastID
	---------------------------------------------------------------------------------------------------------------------

	INSERT @Results 
		SELECT
            sph.ForecastID,
            sph.IsMAPD, 
            sph.IsSNP, 
            cfp.Rebate, 
            RetainedPremium = (ISNULL(cfp.Savings,0) - ISNULL(cfp.Rebate,0)), 
            cfp.Savings, 
            cfp.StandardizedBenchmark, 
            cfp.StandardizedBid, 
            cfp.PlanBenchmark, 
            cfp.PlanBid, 
            TotalReqRev = cfp.TotalReqRev + ISNULL(cpes.TotalESRDSubsidy,0), 
            cfp.MedicareCoveredPct, 
            cfp.Profit, 
            ValueAdded = 0, 
            cfp.Expenses, 
            cfp.Reins, 
            cfp.UserFee, 
            cfp.UncollectedPremium, 
            cfp.NetNonESRD,
			ISNULL(cpes.TotalESRDSubsidy,0),
			TotalNet = cfp.NetNonESRD + ISNULL(cpes.TotalESRDSubsidy,0),
            cfp.Allowed, 
            MedicareCoveredAllowed = mcfp.Allowed,
            ProfitPercent = PlanExpenseProfit.ProfitPercent,
            ExpensePercent = PlanExpenseProfit.ExpensePercent,
			ExpensePMPM	= PlanExpenseProfit.ExpensePMPM,
            CostAndUseCredibility = dbo.fnGetCredibilityFactor(@XForecastID), 
            Ext.PartBMaxRebateAllocation, 
            mcfp.CoveredActEquivNet, 
            mcfp.CostShareReduction, 
            mcfp.AddedBenefitNet, 
            mcfp.SupplementalBenefitTotal, 
            cfp.AddedBenefitPct, 
			cfp.CostShareReductionPct,
            ReinsPercent = 0, 
            cfp.BasicMemberPremium, 
            cfp.GovtPremiumAdj, 
            RxBasicPremium = ISNULL(rx.RxBasicPremium, 0),
            RxSuppPremium = ISNULL(rx.RxSuppPremium, 0),
            FFSEquivCostShare = cbs.PlanPartAEquivCostShare + cbs.PlanPartBEquivCostShare,
            StandardizedCostShare = CASE WHEN cbs.PlanRiskFactor = 0
										 THEN 0
										 ELSE (mcfp.CostShare / cbs.PlanRiskFactor)
									END, 
            cbs.PlanExperienceMembership,
            cbs.PlanProjectedMembership,
			cbs.PlanProjectedNonDualMembership,
			cbs.PlanProjectedDualMembership,
            cbs.PlanRiskFactor,
            sph.PartBPremiumBuydown,
            -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  -- -- -- -- --  
            --These two are used on the form for further calculations
            CS = CONVERT(DECIMAL(10,2),
				(

					ROUND((cfp.TotalReqRev * cfp.CostShareReductionPct),2)

				)),
            Sup_Prem = CONVERT(DECIMAL(10,2),
				ROUND(        
						((cfp.TotalReqRev
							* (1- cfp.MedicareCoveredPct)) + ISNULL(cpes.TotalESRDSubsidy,0)
							-
							ROUND(
								(cfp.TotalReqRev
								* cfp.CostShareReductionPct),
							2))

						,2)),
            BidSummaryCostSharing.CurrentYearCostShare,
            BidSummaryCostSharing.ProjectedYearCostShare,
            BidSummaryCostSharing.CurrentYearAllowed,
            BidSummaryCostSharing.ProjectedYearAllowed,        
            BidSummaryCostSharing.CurrentYearBenefitFactor,  
            BidSummaryCostSharing.ProjectedYearBenefitFactor,
            cfp.InsurerFee,
			WS3TotalCostShare = CASE WHEN dbo.fnIsSafeHarborExemption(@XForecastID) = 0
									 THEN ISNULL(pa.ProjectedAllowed - cfp.NetNonESRD + ab.INAddedBenefitNet,0)
									 ELSE ISNULL(WS3TotalCS.TotalCS - ab.INAddedBenefitCostShare,0) 
								END 
        FROM dbo.SavedPlanHeader sph WITH(NOLOCK)
		LEFT JOIN dbo.CalcPlanESRDSubsidy cpes WITH(NOLOCK)
			ON sph.ForecastID = cpes.ForecastID
        INNER JOIN dbo.CalcPlanProjection  cpp WITH(NOLOCK)
            ON sph.ForecastID = cpp.ForecastID
			AND cpp.DualEligibleTypeID = 2
        INNER JOIN dbo.CalcFinalPremium cfp WITH(NOLOCK)
            ON sph.ForecastID = cfp.ForecastID
        INNER JOIN dbo.CalcBenchmarkSummary cbs WITH(NOLOCK)
            ON cbs.ForecastID = sph.ForecastID
        INNER JOIN dbo.CalcMedicareCoveredFinalPricing mcfp WITH(NOLOCK)
            ON sph.ForecastID = mcfp.ForecastID
		INNER JOIN @AB ab
			ON sph.ForecastID = ab.ForecastID
        INNER JOIN @BidSummaryCostSharing BidSummaryCostSharing    
			ON sph.ForecastID = BidSummaryCostSharing.ForecastID

		INNER JOIN @ProjAlld pa
			ON sph.ForecastID = pa.ForecastID
		LEFT JOIN @MABPTWS3 WS3TotalCS
			ON sph.ForecastID = WS3TotalCS.ForecastID
        LEFT JOIN dbo.SavedPlanAssumptions rx WITH(NOLOCK)
            ON rx.ForecastID = sph.ForecastID

            AND sph.IsMAPD = 1
            AND sph.IsOffModelRx = 1
        LEFT JOIN dbo.fnAppGetPlanExpenseProfit(@XForecastID) PlanExpenseProfit
            ON PlanExpenseProfit.ForecastID = sph.ForecastID
        CROSS JOIN dbo.PerExtCMSValues Ext WITH (NOLOCK) 
        WHERE
			sph.ForecastID = @XForecastID
            AND mcfp.DualEligibleTypeID = 2
    RETURN
END
GO
