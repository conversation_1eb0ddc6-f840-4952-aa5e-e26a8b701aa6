SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  <PERSON><PERSON>lmi      
-- Create date: 10-02-2018
-- Description:  spAppSyncBidModelDataAudit
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:    
-- Read:      

-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE    VERSION   CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Oct-11  1   Initial version.											Nagarani Kolanchelmi
-- 2018-Dec-18	2   Updated Error message and included logging exception	    Pooja Dahiya
-- 2018-Dec-21  3	Added UserId                                        	    Kritika
-- 2018-Aug-29  4 	removed code related to spSyncStructureSetup                Kritika
---2021-Jan-20  5  removed Cursor logic,MarketId and moved BeginTran commands   Mahendran Chinnaiah
-- ----------------------------------------------------------------------------------------------------------------------        
  
-- =============================================           
CREATE PROCEDURE [dbo].[spAppSyncBidModelDataAudit]
@syncSAForecastIDList VARCHAR(MAX) = NULL,
@Result VARCHAR(MAX) OUTPUT,
@LastUpdateByID CHAR(7)
AS
BEGIN 
	SET NOCOUNT ON
    SET XACT_ABORT ON
	BEGIN TRY
	BEGIN		
		IF(	ISNULL(@syncSAForecastIDList,'') <> '')
		BEGIN
				BEGIN TRANSACTION;  
			   IF OBJECT_ID('tempdb.dbo.#Forecastid_List') IS NOT NULL  
					DROP TABLE #Forecastid_List 
		
				  SELECT ROW_NUMBER() OVER(ORDER BY (SELECT NULL)) Rownumber ,CAST(Value AS int) ForecastID INTO #Forecastid_List FROM dbo.fnStringSplit(@syncSAForecastIDList,',') 

				DECLARE @Count INT;SET @Count= (SELECT COUNT(1) FROM #Forecastid_List);
				DECLARE @RowNo INT;SET @RowNo=1;
				DECLARE @ForecastID INT; 
				WHILE(@RowNo<=@Count)
					BEGIN
						 SELECT @ForecastID=ForecastId FROM #Forecastid_List WHERE Rownumber=@Rowno;
				  
						  EXEC dbo.spSyncStateCountyDetail  @ForecastID  
						 SET @RowNo=@RowNo+1;
					END
			 COMMIT TRANSACTION;  

		END;
	
		
	END

	SET @Result = '1';
	END TRY
	BEGIN CATCH
			ROLLBACK TRANSACTION;
			SET @Result = '0:Failed to Sync Service Area for Forecast ID:'+CAST(ISNULL(@ForecastID,0) AS VARCHAR)+':'+CAST(ERROR_MESSAGE() AS VARCHAR)+'\n';
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
	END CATCH;
END;
GO
