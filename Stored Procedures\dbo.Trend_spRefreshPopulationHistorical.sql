SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_spRefreshPopulationHistorical
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: Jan-23-2020
--
-- DESCRIPTION: This stored procedure takes historical membership from data foundation and adds on the population regression relativites from the SAS model
--        
-- PARAMETERS:
--  Input  : @CPS
--	         @RegionString
--	         @ProductString	
--	         @LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_CalcPopulationRelativitiesOutput
--                  Trend_PerPopulationImplementation

--          Write:  Trend_CalcPopulationHistorical
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS: Trend_fnCalcStringToTable
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 4/26/18    		0		    Initial Version																	Andy Blink
-- 6/25/18			1			With actual tables 																Josh Gutzmer 
-- 7/27/18			2		    Updated county level output table to match bid year sp							Andy Blink
--									Rolled up final county level table to IsDEPound
-- 									Changed 'MMCat' field to 'BarcClass'
--							  		Changed output table names
-- 12/17/18			3			Updates to reduce runtime														Andy Blink	  
--									Insert 1.0 factors to geographic input table
--									Filter out ESRD/Hospice members from the implementation dataset
-- 7/26/19			4			Updating code for prior year MMCat being added to the implementation dataset	Andy Blink
--									Moving the pop factor into the use columns instead of allowed
-- 1/23/20          5           Adding the sp to MAAModels for Trend Simplification                             Andy Blink
--                                  Updating column header, table, and sp names
-- 6/11/20          6           Remove RiskInd                                                                  Andy Blink
-- 6/24/20          7           Create temporary planinfo view to add SNPtype to product field                  Michael Manes
-- 8/2020			8			Create parameter @RunPopulationProcess for running pop process from NAR		    Craig Nielsen
-- 10/27/2020		9			Added functionality for the dbo.Trend_SavedRelativityPopulation table           Aleksandar Dimitrijevic
--								to be updated based on the #Transpose table if old methodology selected or the
--								dbo.Trend_CalcPopulationHistorical if new methodology selected
-- 12/22/2020		10			Adjusted the sp to handle factor projection for the new reporting category 
--								PartBRxMedical																	Aleksandar Dimitrijevic
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_spRefreshPopulationHistorical]
--Declare
@CPS AS          VARCHAR(MAX) = NULL    --list of plans separated by commas, EX: 'H1036-130-000,H1019-079-000,...' 
,@RegionString   VARCHAR(MAX) = NULL
,@ProductString  VARCHAR(MAX) = NULL
,@LastUpdateByID CHAR(13)

AS

--DateTime variable so that timestamp on each table is identical 
DECLARE @LastUpdateDateTime AS DATETIME;
DECLARE @BaseYear SMALLINT = NULL;
DECLARE @RunPopulationProcess AS INT;

SET @LastUpdateDateTime = (SELECT   GETDATE ());
SET @BaseYear = (SELECT PlanYearID - 2 FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);
SET @RunPopulationProcess = (SELECT RunPopulationProcessParameter FROM Trend_lkp_RunPopulationProcess WHERE LivePopMethod = 1)

--Create temp planinfo table to adjust product granularity
IF (SELECT  OBJECT_ID ('tempdb..#vwPlanInfo')) IS NOT NULL
    DROP TABLE #vwPlanInfo;
SELECT  DISTINCT
        [CPS]
       ,[PlanYear]
       ,[Division]
       ,[Region]
       ,CASE WHEN [SNPType] = 'NA' THEN Product
             WHEN [SNPType] = 'Dual Eligible' THEN 'D-SNP'
             ELSE 'Other SNP' END AS Product
INTO    #vwPlanInfo
FROM    dbo.vwPlanInfo;

--Setup plan list for sp 
----Based on parameters: Plan,region,product
IF (SELECT  OBJECT_ID ('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans;
CREATE TABLE #Plans
    (CPS VARCHAR(13));
INSERT INTO #Plans
SELECT      DISTINCT
            CPS
FROM        #vwPlanInfo
WHERE       (CPS IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@CPS, ',', 1) )
             OR @CPS IS NULL)
            AND (Region IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@RegionString, ',', 1) )
                 OR @RegionString IS NULL)
            AND (Product IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@ProductString, ',', 1) )
                 OR @ProductString IS NULL)
ORDER BY    CPS;

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;



--Create temp table with 5 reporting categories and risk indicator (used for crossjoin to transpose factors from 5 col into 1 and add the risk indicator split to duplicate factors)
IF (SELECT  OBJECT_ID ('tempdb..#RepCats')) IS NOT NULL DROP TABLE #RepCats;
CREATE TABLE #RepCats
    (ReportingCategory VARCHAR(50) NOT NULL);

INSERT INTO #RepCats
    (ReportingCategory)
VALUES ('IP')
      ,('OP')
      ,('PHYS')
      ,('Ancil')
      ,('SNF')
	  ,('Part B Rx Midical');



----Create Temp relativities table: 
IF (SELECT  OBJECT_ID ('tempdb..#Relativities')) IS NOT NULL
    DROP TABLE #Relativities;
CREATE TABLE #Relativities
    (ProductType		   VARCHAR(20)    NOT NULL
    ,SexCD				   VARCHAR(1)     NOT NULL
    ,IsDualEligible		   BIT            NOT NULL
    ,IsAgeddisabled		   BIT            NOT NULL
    ,AgeBin				   VARCHAR(25)    NOT NULL
    ,DiseaseScoreBin       VARCHAR(25)    NOT NULL
    ,IPAllowed			   DECIMAL(18, 8) NULL
    ,OPAllowed			   DECIMAL(18, 8) NULL
    ,PhysAllowed		   DECIMAL(18, 8) NULL
    ,AncilAllowed          DECIMAL(18, 8) NULL
    ,SNFAllowed			   DECIMAL(18, 8) NULL
	,PartBRxMedicalAllowed DECIMAL(18, 8) NULL);

INSERT INTO #Relativities
SELECT  ProductType
       ,SexCD
       ,IsDualEligible
       ,IsAgeddisabled
       ,AgeBin nvarchar
       ,DiseaseScoreBin
       ,IPAllowed
       ,OPAllowed
       ,PhysAllowed
       ,AncilAllowed
       ,SNFAllowed
	   ,PartBRxMedicalAllowed
FROM    dbo.Trend_CalcPopulationRelativitiesOutput;


----Create Temp Implementation table: 
IF (SELECT  OBJECT_ID ('tempdb..#Implementation_w_relativities')) IS NOT NULL
    DROP TABLE #Implementation_w_relativities;
CREATE TABLE #Implementation_w_relativities
    (PlanYearID			   INT            NOT NULL
    ,QuarterID			   TINYINT        NOT NULL
    ,CPS				   CHAR(13)       NOT NULL
    ,IsDualEligible		   BIT            NOT NULL
    ,IsDEPound			   BIT            NOT NULL
    ,IsAgeddisabled		   BIT            NOT NULL
    ,BarcClass			   VARCHAR(1)     NOT NULL
    ,SSStateCountyCD	   VARCHAR(5)     NOT NULL
    ,SexCD				   VARCHAR(1)     NOT NULL
    ,AgeBin				   VARCHAR(25)    NOT NULL
    ,DiseaseScoreBin	   VARCHAR(25)    NOT NULL
    ,MM					   DECIMAL(18, 8) NULL
    ,ProductType		   VARCHAR(15)    NOT NULL
    ,IPAllowed			   DECIMAL(18, 8) NULL
    ,OPAllowed			   DECIMAL(18, 8) NULL
    ,PhysAllowed		   DECIMAL(18, 8) NULL
    ,AncilAllowed		   DECIMAL(18, 8) NULL
    ,SNFAllowed			   DECIMAL(18, 8) NULL
	,PartBRxMedicalAllowed DECIMAL(18, 8) NULL);;

INSERT INTO #Implementation_w_relativities
SELECT      i.PlanYearID
           ,i.QuarterID
           ,i.CPS
           ,i.IsDualEligible
           ,i.IsDEPound
           ,i.IsAgedDisabled
           ,CASE WHEN i.MMCat IN ('CC+', 'CC0', 'I') THEN 'A'
                 WHEN i.MMCat IN ('CN+', 'CN0') THEN 'B'
                 WHEN i.MMCat = 'EN' THEN 'C'
                 ELSE 'D' END
           ,i.SSStateCountyCD
           ,i.SexCD
           ,i.AgeBin
           ,i.DiseaseScoreBin
           ,SUM (i.MM)
           ,vpi.Product
           ,SUM (rel.IPAllowed * i.MM)
           ,SUM (rel.OPAllowed * i.MM)
           ,SUM (rel.PhysAllowed * i.MM)
           ,SUM (rel.AncilAllowed * i.MM)
           ,SUM (rel.SNFAllowed * i.MM)
		   ,SUM (rel.PartBRxMedicalAllowed * i.MM)
FROM        dbo.Trend_PerPopulationImplementation i

LEFT JOIN   #vwPlanInfo vpi
       ON i.CPS = vpi.CPS
          AND   i.PlanYearID = vpi.PlanYear

LEFT JOIN   #Relativities rel
       ON vpi.Product = rel.ProductType
          AND   i.SexCD = rel.SexCD
          AND   i.IsAgedDisabled = rel.IsAgeddisabled
          AND   i.IsDualEligible = rel.IsDualEligible
          AND   i.DiseaseScoreBin = rel.DiseaseScoreBin
          AND   i.AgeBin = rel.AgeBin

WHERE       i.CPS IN (SELECT    * FROM #Plans)
            AND i.IsESRD = 0
            AND i.IsHospice = 0 --AND i.IsRisk = 0	--filtering out risk members
GROUP BY    i.PlanYearID
           ,i.QuarterID
           ,i.CPS
           ,i.IsDualEligible
           ,i.IsDEPound
           ,i.IsAgedDisabled
           ,CASE WHEN i.MMCat IN ('CC+', 'CC0', 'I') THEN 'A'
                 WHEN i.MMCat IN ('CN+', 'CN0') THEN 'B'
                 WHEN i.MMCat = 'EN' THEN 'C'
                 ELSE 'D' END
            --i.IsRisk, 
           ,i.SSStateCountyCD
           ,i.SexCD
           ,i.AgeBin
           ,i.DiseaseScoreBin
           ,vpi.Product;



----Summarize the Implementation table to reduce the size before transposing 
IF (SELECT  OBJECT_ID ('tempdb..#Implementation_w_relativities_sum')) IS NOT NULL
    DROP TABLE #Implementation_w_relativities_sum;
CREATE TABLE #Implementation_w_relativities_sum
    (PlanYearID			   INT            NOT NULL
    ,QuarterID			   TINYINT        NOT NULL
    ,CPS				   CHAR(13)       NOT NULL
    ,SSStateCountyCD	   VARCHAR(5)     NOT NULL
    ,ProductType		   VARCHAR(20)    NOT NULL
    ,IsDEPound			   BIT            NOT NULL
    ,BarcClass			   VARCHAR(1)     NOT NULL
    ,MM					   DECIMAL(18, 8) NULL
    ,IPAllowed			   DECIMAL(18, 8) NULL
    ,OPAllowed			   DECIMAL(18, 8) NULL
    ,PhysAllowed		   DECIMAL(18, 8) NULL
    ,AncilAllowed		   DECIMAL(18, 8) NULL
    ,SNFAllowed			   DECIMAL(18, 8) NULL
	,PartBRxMedicalAllowed DECIMAL(18, 8) NULL);

INSERT INTO #Implementation_w_relativities_sum
SELECT      PlanYearID
           ,QuarterID
           ,CPS
           ,SSStateCountyCD
           ,ProductType
           ,IsDEPound
           ,BarcClass
           ,SUM (MM)
           ,SUM (IPAllowed)
           ,SUM (OPAllowed)
           ,SUM (PhysAllowed)
           ,SUM (AncilAllowed)
           ,SUM (SNFAllowed)
		   ,SUM (PartBRxMedicalAllowed)
FROM        #Implementation_w_relativities
GROUP BY    PlanYearID
           ,QuarterID
           ,CPS
           ,SSStateCountyCD
           ,ProductType
           ,IsDEPound
           ,BarcClass;



--Create temp combined table that joins the regression relativities to the implementation dataset
IF (SELECT  OBJECT_ID ('tempdb..#Transpose')) IS NOT NULL
    DROP TABLE #Transpose;
CREATE TABLE #Transpose
    (PlanYearID        INT            NOT NULL
    ,QuarterID         TINYINT        NOT NULL
    ,CPS               CHAR(13)       NOT NULL
    ,ProductType       VARCHAR(15)    NOT NULL
    ,IsDEPound         BIT            NOT NULL
    ,BarcClass         VARCHAR(1)     NOT NULL
    ,ReportingCategory VARCHAR(50)    NOT NULL
    ,SSStateCountyCD   VARCHAR(5)     NOT NULL
    ,MM                DECIMAL(18, 8) NULL
    ,Allowed           DECIMAL(18, 8) NULL);

-- Join DeMo factors, Geo factors, and any info needed from New_AnnualPlanMapping
INSERT INTO #Transpose
SELECT      imp.PlanYearID
           ,imp.QuarterID
           ,imp.CPS
           ,imp.ProductType
           ,imp.IsDEPound
           ,imp.BarcClass
           ,r.ReportingCategory
           ,imp.SSStateCountyCD
           ,imp.MM
           ,CASE WHEN r.ReportingCategory = 'IP' THEN imp.IPAllowed
                 WHEN r.ReportingCategory = 'OP' THEN imp.OPAllowed
                 WHEN r.ReportingCategory = 'Phys' THEN imp.PhysAllowed
                 WHEN r.ReportingCategory = 'Ancil' THEN imp.AncilAllowed
                 WHEN r.ReportingCategory = 'SNF' THEN imp.SNFAllowed
				 WHEN r.ReportingCategory = 'Part B Rx Midical' THEN imp.PartBRxMedicalAllowed END
FROM        #Implementation_w_relativities_sum imp
CROSS JOIN  #RepCats r;


IF @RunPopulationProcess = 1
	BEGIN
		--Delete rows from the county level base data table and repopulate (this table will feed the credibility fill sp and the bid year sp)
		DELETE  FROM dbo.Trend_CalcPopulationHistorical
		WHERE   CPS IN (SELECT  * FROM  #Plans);
		INSERT INTO dbo.Trend_CalcPopulationHistorical
		SELECT      cs.CPS
				   ,cs.SSStateCountyCD
				   ,vpi.Division
				   ,vpi.Region
				   ,vpi.Product
				   ,cs.PlanYearID
				   ,cs.QuarterID
				   ,cs.ReportingCategory
				   ,cs.IsDEPound
				   ,cs.BarcClass
				   ,'T'
				   ,SUM (cs.MM) AS BY_MM
				   ,SUM (cs.MM) AS BY_AllowedFactor     --setting the allowed factor equal to 1.0
				   ,SUM (cs.Allowed) AS BY_UseFactor    --the population factor is an adjustment to utilization
				   ,'axd2831'
				   ,GETDATE ()
		FROM        #Transpose cs
		LEFT JOIN   #vwPlanInfo vpi
			   ON cs.CPS = vpi.CPS
				  AND   cs.PlanYearID = vpi.PlanYear
		GROUP BY    cs.CPS
				   ,cs.SSStateCountyCD
				   ,vpi.Division
				   ,vpi.Region
				   ,vpi.Product
				   ,cs.PlanYearID
				   ,cs.QuarterID
				   ,cs.ReportingCategory
				   ,cs.IsDEPound
				   ,cs.BarcClass;

		--Delete and populate the population relativity table
		DELETE  FROM dbo.Trend_SavedRelativityPopulation
		WHERE   CPS IN (SELECT  * FROM  #Plans)
				AND PlanYearID <= @BaseYear;

		INSERT INTO dbo.Trend_SavedRelativityPopulation
		SELECT      CPS
				   ,PlanYearID
				   ,QuarterID
				   ,ReportingCategory
				   ,1 AS 'CostRelativity'
				   ,dbo.Trend_fnSafeDivide (SUM (Allowed), SUM (MM), 1) AS 'UseRelativity'
				   ,@LastUpdateByID
				   ,@LastUpdateDateTime
		FROM        #Transpose
		GROUP BY    CPS
				   ,PlanYearID
				   ,QuarterID
				   ,ReportingCategory;
	END

IF @RunPopulationProcess = 0
	BEGIN
		--Delete and populate the population relativity table
		DELETE  FROM dbo.Trend_SavedRelativityPopulation
		WHERE   CPS IN (SELECT  * FROM  #Plans)
				AND PlanYearID <= @BaseYear;

		INSERT INTO dbo.Trend_SavedRelativityPopulation
		SELECT      CPS
				   ,PlanYearID
				   ,QuarterID
				   ,ReportingCategory
				   ,1 AS 'CostRelativity'
				   ,dbo.Trend_fnSafeDivide (SUM (BY_UseFactor), SUM (BY_MM), 1) AS 'UseRelativity'
				   ,@LastUpdateByID
				   ,@LastUpdateDateTime
		FROM        dbo.Trend_CalcPopulationHistorical
		GROUP BY    CPS
				   ,PlanYearID
				   ,QuarterID
				   ,ReportingCategory;
	END
GO
