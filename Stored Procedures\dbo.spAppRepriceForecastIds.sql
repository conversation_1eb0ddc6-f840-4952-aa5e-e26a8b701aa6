SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO




-- PROCEDURE NAME: spAppRepriceForecastIds  
--  
-- CREATOR: <PERSON><PERSON><PERSON> 
--  
-- CREATED DATE: 2019-June-19  
-- HEADER UPDATED: 2017-Sept-11 -- <PERSON> Fleming
--  
-- DESCRIPTION: Stored Procedure responsible for re-pricing MA Plans  
--  
--		Return value of 0 indicates success.  
--		If error occurs, code consists of 2 pieces.  
--		1st piece is the actual error code that caused the proc to fail.   
--		2nd piece is the "0 + ith" proc that caused the error.  
--		This is always 2 digits long, with a leading 0 if needed.  
--		List of errors: USE MASTER SELECT * FROM SYSMESSAGES WHERE ERROR = <ERROR>  
--  
-- PARAMETERS:  
--  Input:   
--@ForecastedList CHAR(13),

--@LastUpdateByID VARCHAR(7),
--@MessageFromBackend VARCHAR(max) OUT,
--@Result BIT OUT 
  
-- StoredProcs:  dbo.spPlanRefresh
-- $HISTORY   
-- ------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE												Developer													  
-- -------------------------------------------------------------------------------------------------------
-- 2019-June-19       1          Initial Version											Satyam Singhal
-- 2019-June-25       2          Included cursor 											Kritika Singh	
-- 2019-June-28       3          Updated spPlanrefersh call 
--								 with 1 instead of 18 										Pooja Dahiya
-- 2019-Jul-05	      4		     Added productpairing changes and messages   				Pooja Dahiya
-- 2019-Aug-20        5          Removed productpairing changes								Kiran Pant
-- 2019-Oct-10		  6          Reprice error message issue fix							Pooja Dahiya
-- 2019-Nov-01		  7			 Removed @UserID								            Chhavi Sinha	
-- 2019-Nov-13		  8			 Added logic to reprice plans which are required			Deepali		   
-- ------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[spAppRepriceForecastIds]
@ForecastIDList varchar(max),
@LastUpdateByID VARCHAR(7), 
@MessageFromBackend VARCHAR(max) OUT,
@Result BIT OUT

AS

BEGIN    
BEGIN TRANSACTION;
BEGIN TRY       
     
Declare  @ForecastID int
, @ErrorMsg varchar(max)=''
set @MessageFromBackend='';



DECLARE ForecastCursor CURSOR FOR

SELECT p.value FROM dbo.fnStringSplit(@ForecastIDList,',') p INNER JOIN dbo.SavedForecastSetup f ON p.Value=f.ForecastID
WHERE f.IsToReprice=1

OPEN ForecastCursor  
FETCH NEXT FROM ForecastCursor INTO @ForecastID

WHILE @@Fetch_Status = 0 

BEGIN

EXEC dbo.spPlanRefresh @ForecastID,@LastUpdateByID,@ErrorMsg out,1  

if(@ErrorMsg <> ''AND @ErrorMsg <>'N/A')
begin
DECLARE @Plan varchar(30) 
SELECT @Plan=cps + ' : '+cast(ScenarioNbr as varchar) FROM SavedForecastSetup f
INNER JOIN savedplaninfo p
ON f.PlanInfoID=p.PlanInfoID
where ForecastID=@ForecastID

set @MessageFromBackend=@MessageFromBackend+'Reprice failed for scenario : '+@Plan +'. '+ @ErrorMsg ;

end 
FETCH NEXT FROM ForecastCursor INTO @ForecastID

End 

Close ForecastCursor
Deallocate ForecastCursor

		
if(@MessageFromBackend='')
begin
SET @Result = 1; 
end
else
begin
SET @Result = 0; 
end

COMMIT TRANSACTION;
END TRY
BEGIN CATCH
SET @Result = 0;
SET @MessageFromBackend= 'ExecutionFailed. <NAME_EMAIL>.';
DECLARE @ErrorMessage NVARCHAR(4000);  
DECLARE @ErrorSeverity INT;  
DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
ROLLBACK TRANSACTION; 

---Insert into app log for logging error------------------
Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID


END CATCH;  


END;
GO
