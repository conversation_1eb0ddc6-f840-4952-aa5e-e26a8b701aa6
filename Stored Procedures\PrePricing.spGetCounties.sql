SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetCounties]   
--    
-- AUTHOR: Sur<PERSON>y 
--    
-- CREATED DATE: 2024-Dec-02    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving counties
--    
-- PARAMETERS:    
-- Input: 
-- 
   
-- TABLES:   
--  
 
-- Read:    
-- dbo.LkpStateCounty

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Dec-02		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetCounties]
AS    
BEGIN    
	SELECT a.SSStateCountyCD,a.StateTerritoryID,a.CountyName FROM dbo.LkpStateCounty a WITH(NOLOCK) ORDER BY a.LastUpdateDateTime DESC;	 
END


EXEC [dbo].[spAppGetMSBExtractInfo] NULL    
GO
