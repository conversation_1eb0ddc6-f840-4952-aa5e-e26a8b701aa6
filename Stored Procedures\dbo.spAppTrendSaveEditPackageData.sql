SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
     
-- =============================================            
-- Author:  <PERSON><PERSON><PERSON> Upadhayay          
-- Create date: 03 March 2020      
-- Description: save changes for Trend Package       
--            
--            
-- PARAMETERS:            
-- Input:              
      
-- TABLES:          
-- Read:           
-- Write:            
-- VIEWS:            
--            
-- FUNCTIONS:            
--              
-- STORED PROCS:             
      
      
-- $HISTORY               
      
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE        VERSION   CHANGES MADE                                                DEVELOPER              
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Mar-03  1        Initial version.                                         Ranjana Upadhayay   
--2020-May04	2		Corrected condition	for #temp1								Deepali  
--2020- May-06	3		Changed length for PackageOptionName						Deepali Mittal     
------------------------------------------------------------------------------------------------------------------------      
      
CREATE PROCEDURE [dbo].[spAppTrendSaveEditPackageData]   
    @islive VARCHAR(50),    
    @PackageOptionName VARCHAR(100),      
 @ComponentData VARCHAR(MAX) ,  
    @LastUpdateByID CHAR(7),      
    @MessageFromBackend NVARCHAR(MAX) OUTPUT,      
    @Result BIT OUT      
AS      
BEGIN      
    BEGIN TRANSACTION;      
    BEGIN TRY 
	CREATE TABLE #temp(Component VARCHAR(MAX))
	INSERT INTO #temp
	(  Component)
		(SELECT  Value FROM    dbo.fnStringSplit(@ComponentData, ',') ) 
		
		DELETE FROM #temp WHERE Component = ' '
		CREATE TABLE #temp1(Component VARCHAR(MAX),ComponentversionID int)
		INSERT INTO #temp1
		(
		    Component,
		    ComponentversionID
		)
	select 
    case when CHARINDEX('-',Component)>0 
         then SUBSTRING(Component,1,CHARINDEX('-',Component)-1) 
         else Component end Component, 
    CASE WHEN CHARINDEX('-',Component)>0 
         THEN SUBSTRING(Component,CHARINDEX('-',Component)+1,len(Component))  
         ELSE NULL END as ComponentversionID
from #temp

	  IF EXISTS (SELECT COUNT(*)  FROM dbo.Trend_SavedPackageOption WHERE  PackageOptionName=@PackageOptionName)      
		BEGIN      
			IF UPPER(@islIve)='TRUE'  
				BEGIN  
					UPDATE dbo.Trend_SavedPackageOption SET IsLivePackage=1 
					WHERE  PackageOptionName=@PackageOptionName;  
       
					UPDATE dbo.Trend_SavedPackageOption SET IsLivePackage=0  
					WHERE  PackageOptionName <> @PackageOptionName;  
				END  
        END  
      
     IF EXISTS (SELECT * FROM #temp1)  
		BEGIN   
			IF EXISTS (SELECT COUNT(*)  FROM dbo.Trend_SavedPackageOption po inner JOIN #temp1 tm on po.Component = tm.Component AND po.PackageOptionName=@PackageOptionName)      
				BEGIN      
					

						UPDATE a  SET a.ComponentVersionID=b.ComponentversionID FROM Trend_SavedPackageOption a INNER JOIN #temp1 b
						 ON a.Component=b.Component AND a.PackageOptionName=@PackageOptionName WHERE b.ComponentversionID!=0
						UPDATE a  SET a.ComponentVersionID=null FROM Trend_SavedPackageOption a left JOIN #temp1 b  ON a.ComponentVersionID=b.ComponentversionID AND a.PackageOptionName=@PackageOptionName
						 WHERE b.ComponentversionID IS NULL AND a.PackageOptionName=@PackageOptionName
						
					
				END;   
		END  ;
    
 
           
   COMMIT TRANSACTION;     
   SET @MessageFromBackend = 'Your changes were saved';    -- SET @MessageFromBackend = 'Your changes were not saved' ;  
         SET @Result = 1;     
    END TRY      
    BEGIN CATCH      
    SET @Result=0;  
 SET @MessageFromBackend='Your changes were not saved';  
        --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'      
        DECLARE @ErrorMessage NVARCHAR(4000);      
        DECLARE @ErrorSeverity INT;      
        DECLARE @ErrorState INT;      
        DECLARE @ErrorException NVARCHAR(4000);      
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),      
                @currentdate DATETIME = GETDATE();      
      
        SELECT @ErrorMessage = ERROR_MESSAGE(),      
               @ErrorSeverity = ERROR_SEVERITY(),      
               @ErrorState = ERROR_STATE(),      
               @ErrorException      
             = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'      
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);      
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);      
      
        ROLLBACK TRANSACTION;      
      
        ---Insert into app log for logging error------------------      
 EXEC spAppAddLogEntry @currentdate,      
                              '',      
                              'ERROR',      
                              @errSrc,      
                              @ErrorMessage,      
                              @ErrorException,      
                              @LastUpdateByID;      
      
    END CATCH;      
END;      

GO
