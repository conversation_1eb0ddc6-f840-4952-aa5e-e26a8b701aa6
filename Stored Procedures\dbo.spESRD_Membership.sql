SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO




--------------------------------------------------------------------------------------------------------------------------            
------ Stored Procedure NAME: [spESRD_Default]       
------ ----------------------------------------------------------------------------------------------------------------           
------ DATE			VERSION		CHANGES MADE														DEVELOPER              
------ ----------------------------------------------------------------------------------------------------------------            
------ 2023-01-27 		1		Initial Version														Adam Keach
------ 2023-01-22 		1		Initial Version														Adam Keach
------ 2023-02-02		2		Updated the setup for parameters and to use #PLans table			Tingyao Wang
------							instead of dbo.ESRDClaims_ActivePlanList to allow multiple 
------							users to run the code at the same time
------ 2023-05-09		3		Changed new plan membership to account for new in 2024 Hospice membership Adam Keach

CREATE PROCEDURE [dbo].[spESRD_Membership]
(
   @RegionId INT,
  @ForecastID VARCHAR(MAX),		-- updated 2/2
  @DivisionID INT,
  @SubRegion VARCHAR(MAX)		-- updated 2/2
)
AS
SET NOCOUNT ON
SET ANSI_WARNINGS OFF


--DECLARE @RegionId INT = 0 --comment out for actual stored procedure, only use for testing
--DECLARE @ForecastID INT = 0
--DECLARE @DivisionID INT = 0

DECLARE @Local_RegionId INT = @RegionId					-- added 2/2
DECLARE @Local_ForecastID VARCHAR(MAX) = @ForecastID	-- added 2/2
DECLARE @Local_DivisionID INT = @DivisionID				-- added 2/2
DECLARE @Local_Subregion VARCHAR(MAX) = @Subregion		-- added 2/2

DECLARE @UserID VARCHAR(13)
DECLARE @BaseYear INT
DECLARE @BidYear INT
DECLARE @Iteration VARCHAR(13)

/************ Replace Hardcoding before Savings **************************/
SET @BaseYear =(SELECT MIN(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive='N') 
SET @BidYear = (SELECT MAX(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive = 'N'); /* For Q3, Q4, FLP, these years will likely need to be hardcoded */
SET @Iteration = CONCAT(
                 (
                     SELECT Year  FROM dbo.SCT_CurrentToolVersion
                 ),
                 'Q',
                 (
                     SELECT Quarter FROM dbo.SCT_CurrentToolVersion
                 )
                       )

-- SET @UserID = 'HUMAD\SCTAPP';

/* ************************************************************************************************************************************************* *
Added 2/2 to create #Plans temp table instead of using dbo.ESRDClaims_ActivePlanList
Get Plan List corresponding to chosen plan/region/division.  For Q3/Q4/FLP purposes, all plans are chose by setting regionID, ForecastID, and DivisionID to 0 
* =================================================================================================================================================== */
IF (SELECT OBJECT_ID('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans		

SELECT PlanName,RFI.Region													
INTO #Plans																	
FROM dbo.SCT_vwRollupForecastInfo_SCT   RFI WITH (NOLOCK)

INNER JOIN 
	(SELECT PlanYearContractPBPSegment
	FROM dbo.SCT_SavedPlanList		
	WHERE PlanYearID = @BidYear) b
				ON RFI.PlanName = b.PlanYearContractPBPSegment
				
WHERE RFI.RollupName = 'LIVE'
      AND RFI.IsSctPlan = 1
      AND RFI.IsToReprice = 0--Leave in - Don't remove for Bids
      AND RFI.IsLiveIndex = 1
      AND (@Local_RegionId = 0 OR RFI.ActuarialRegionID = @Local_RegionId)													
      AND (@Local_ForecastID IS NULL OR RFI.ForecastID IN (Select value from dbo.fnStringSplit(@Local_ForecastID,',')))		
      AND (@Local_DivisionID = 0 OR RFI.ActuarialDivisionID = @Local_DivisionID)											
	  AND (@Local_Subregion IS NULL OR RFI.Subregion IN (Select value from dbo.fnStringSplit(@Local_Subregion,',')));		

/* MEMBERSHIP Section */
/* high level process - we get membership from barc at barclass plan de# level.  We need to have membership at the barcclass, plan, de#, dialysissubtype, and vendor level.
We have CurrentYear (MidYear) indicators that show the distribution of plan membership among all of these cuts.
We need to crosswalk these CurrentYear plan indicators to the bidyear.
We then apply this crosswalked distribution to get projected membership at the desired level.
High level example- if BARC projects 100 ESRD MM for a bid year plan, and crosswalked indicators show 30% legacy DaVita dialysis DE# and 70% New in 2022 dialysis Neither Non-DE# then
	we'd end up with 30 MM for legacy dialysis Davita DE# and 70 MM for New in 2022 dialysis neither non-DE#
We have a similar but separate process for New Plans in the bid year.  We roll up the membership distributions at the region/product/de# level and apply to the BARC membership for new plans
	to get the projected membership at the appropriate granularity
*/ 


/* ************************************************************************************************************************************************* *
Get SAM crosswalks at county level from Current Year to Bid Year.  We use current year because we have several months of current year data at time of bids.
* =================================================================================================================================================== */
IF (SELECT OBJECT_ID('tempdb..#ESRDCYtoBYPlanXWALK')) IS NOT NULL DROP TABLE #ESRDCYtoBYPlanXWALK
--Select * from #ESRDCYtoBYPlanXWalk
select a.BidYearCPS,b.CurrentYearCPS 
into #ESRDCYtoBYPlanXWALK
from 
(
	select distinct BidYearCPS 
	from dbo.vwSamCrosswalks 
	where BidYear=@BidYear and Isactive=1 and BidYearCPS is not NULL
) a
inner join #Plans p					-- updated to use #Plans 2/2
on a.BidYearCPS=p.PlanName 
left join 
	(
		select distinct BidYearCPS,CurrentYearCPS from dbo.vwSamCrosswalks 
		where BidYear=@BidYear and IsActive=1 and currentYearCPS is not NULL
	) b
on a.BidYearCPS=b.BidYearCPS
order by a.BidYearCPS, b.CurrentYearCPS 




IF (SELECT OBJECT_ID('tempdb..#ESRDMbrshp_CYtoBYIndicators')) IS NOT NULL DROP TABLE #ESRDMbrshp_CYtoBYIndicators

--Select * from #IndicatorXwalkStep1 order by BidYearCPS,Cohort,Class,DEPound
/* ************************************************************************************************************************************************* *
Join the crosswalk to the current year indicators table
* =================================================================================================================================================== */
select d.BidYearCPS,  
	ISNULL(d.cohort,'Dialysis') AS Cohort,
	ISNULL(a.cohortStatus,'Legacy') AS CohortStatus,
	ISNULL(d.Class,'All Other Dialysis') AS Class,
	ISNULL(d.DEPound,'NonDE#') AS DEPound,
	SUM(ISNULL(a.BaseMbrMths,.00001) ) as MbrMths
into #ESRDMbrshp_CYtoBYIndicators from 
  (
		SELECT * from 
			(
				select distinct BidYearCPS,CurrentYearCPS  
				from #ESRDCYtoBYPlanXWalk   
				where CurrentYearCPS is not NULL
			) b , 
			(
				SELECT distinct Cohort, Class, DEPound 
				FROM dbo.ESRDInpt_BaseClaims
			) c
	) d
 left join
 dbo.ESRDInpt_BaseYrMbrIndicators a
on d.CurrentYearCPS=a.CPS AND 
	d.cohort=a.cohort AND 
	d.class=a.class AND 
	d.depound=a.depound
where  BIDYearCPS is not NULL 
group by d.BidYearCPS, d.Cohort,a.CohortStatus,d.class,d.dePound
order by BidYearCPS,Cohort,Class

/* ************************************************************************************************************************************************* *
Sum up the member months at the plan, cohort, DE# level (drops county) and 
	then develop member distribution percent by class for each cut.
* =================================================================================================================================================== */



IF (SELECT OBJECT_ID('tempdb..#ESRDMbrshp_IndicatorXWalkDistr')) IS NOT NULL DROP TABLE #ESRDMbrshp_IndicatorXWalkDistr

SELECT	a.bidyearCPS,
		a.cohort, 
		a.cohortStatus,
		a.Class, 
		a.DEPound,
		Case 
			When b.MbrMthsPlan=0 then 0 
			Else (cast(a.mbrMths as Decimal(11,5))/cast(b.MbrMthsPlan as decimal(11,5))) 
		END as MbrPrcnt 
INTO #ESRDMbrshp_IndicatorXWalkDistr

FROM #ESRDMbrshp_CYtoBYIndicators a 
inner join 
(
	SELECT BidYearCPS, cohort,cohortStatus,DEPound, SUM(MbrMths) as MbrMthsPlan
	FROM #ESRDMbrshp_CYtoBYIndicators
	GROUP by BidYearCPS, cohort, CohortStatus, DEPound
) b on 
a.BidYearCPS=b.BidYearCPS and a.Cohort=b.Cohort and A.cohortStatus=B.cohortStatus and a.DEPound=b.DEPound





IF (SELECT OBJECT_ID('tempdb..#ESRDMbrshp_BarcCohortMbrshp')) IS NOT NULL DROP TABLE #ESRDMbrshp_BarcCohortMbrshp
--select Cohort,SUM(BidYearMemberMOnths) as MM From #ESRDMbrshp_BARCCohortMbrshp group by Cohort order by CPS,Cohort,DEPound
SELECT 
	w.CPS,
	w.cohort,
	w.IsDEPound as DEPound,
	w.BY_Product,
	w.BY_region,
	w.BY_Division,
	w.BidYearMemberMonths
	
INTO #ESRDMbrshp_BarcCohortMbrshp

FROM  
	(SELECT CPS,
	Cohort,
	CASE 
		WHEN IsDEPound='N' THEN 'NonDE#' 
		ELSE 'DE#' 
	END AS ISDEPound,
	CASE WHEN rfi.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN rfi.SNPType = 'Chronic' THEN 'CSNP'
		WHEN rfi.SNPType = 'Institutional' THEN 'CSNP'
		Else spl.Product 
	End as BY_Product,
	spl.region as BY_Region, 
	spl.division as BY_Division,

	SUM(BidYearMemberMonths) AS BidYearMemberMonths
	FROM dbo.ESRDInpt_BARCMembership mbr
	left join
		dbo.ESRDInpt_BARCCohortMap cm
	on mbr.BidYearClass = cm.BidYearClass
	left join 
	dbo.sct_savedPlanList spl
		on mbr.CPS=spl.ContractPBPSegment
	left join	
	dbo.vwRollupForecastInfo rfi
	on mbr.CPS=rfi.PlanName
	where spl.PlanYearID=@BidYear
	group by CPS,
	Cohort,
	CASE 
		WHEN ISDEPound='N' THEN 'NonDE#' 
		ELSE 'DE#' 
	END,
	CASE WHEN rfi.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN rfi.SNPType = 'Chronic' THEN 'CSNP'
		WHEN rfi.SNPType = 'Institutional' THEN 'CSNP'
		Else spl.Product 
	End ,
	spl.region, 
	spl.division
	) W
order by CPS,COhort,IsDEPound


IF (SELECT OBJECT_ID('tempdb..#ESRDMbrshp_ProjNonNewPlanMbrs')) IS NOT NULL DROP TABLE #ESRDMbrshp_ProjNonNewPlanMbrs
--select * from #ProjNonNewPlanMbrs where cohort='Transplant' order by CPS,cohortstatus
select	a.CPS ,
		a.cohort, 
		ISNULL(b.class,concat('All Other ',a.cohort)) as class,   
		a.DEPound,  
		c.Region, 
		CASE WHEN c.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN c.SNPType = 'Chronic' THEN 'CSNP'
		WHEN c.SNPType = 'Institutional' THEN 'CSNP'
		ELSE c.Product END AS Product, 
		b.MbrPrcnt,
		a.bidYearMemberMOnths,
		a.BidYearMemberMonths*ISNULL(b.MbrPrcnt,1) as MbrMths
into #ESRDMbrshp_ProjNonNewPlanMbrs
 FROM 
 (
	select distinct bidyearCPS 
	from #ESRDCYtoBYPlanXWALK 
	where currentyearCPS is not NULL
	) e
	inner join  #ESRDMbrshp_BarcCohortMbrshp a
	on a.CPS=e.bidYearCPS
	left join #ESRDMbrshp_IndicatorXWalkDistr b
	on a.CPS=b.BidYearCPS AND 
		a.cohort=b.cohort AND 
		A.DEPound=B.DePound
	left join dbo.vwRollupForecastInfo c
	on a.CPS=c.PlanName 
	where  c.isSCTPlan=1 and c.ISLiveIndex=1 

--Select SUM(MbrMths) as MM from #ProjNonNewPlanMbrs Select SUM(MbrMths) as MM from #ProjNewPlanMbrs
--



IF (SELECT OBJECT_ID('tempdb..#ESRDMbrshp_ProjNewPlanMbrs')) IS NOT NULL DROP TABLE #ESRDMbrshp_ProjNewPlanMbrs

--select * from #ProjNewPlanMbrs  order by CPS,Cohort,CohortStatus,DEPound,Class
select 
a.CPS, 
e.Cohort,
e.cohortStatus,
e.class, 
e.DEPound, 
a.BidYearMemberMonths,
d.adjindicprcnt,
a.BidYearMemberMonths*ISNULL(d.AdjIndicPrcnt,0) as MbrMths, 
c.Region, 
CASE WHEN c.SNPType = 'Dual Eligible' THEN 'DSNP'
	WHEN c.SNPType = 'Chronic' THEN 'CSNP'
	WHEN c.SNPType = 'Institutional' THEN 'CSNP'
	ELSE c.Product 
END AS Product 
Into #ESRDMbrshp_ProjNewPlanMbrs
from
(
	SELECT * from 
	(
		select distinct BidYearCPS,CurrentYearCPS  
		from #ESRDCYtoBYPlanXWALK   
	) b , 
	(
		SELECT distinct Cohort,CohortStatus,Class,DEPound 
		FROM dbo.ESRDInpt_BaseYrMbrIndicators
			union --Updated 5/9 to include New in 2024 Hospice Data
		Select 'Hospice' as Cohort,'Legacy' as cohortStatus,'All Other Hospice' as Class, 'DE#' as DEPound --Updated 5/9 to include New in 2024 Hospice Data
		union --Updated 5/9 to include New in 2024 Hospice Data
		Select 'Hospice' as Cohort,'Legacy' as cohortStatus,'All Other Hospice' as Class, 'NonDE#' as DEPound --Updated 5/9 to include New in 2024 Hospice Data
	) c 
	where currentyearCPS is NULL
) e
inner join #ESRDMbrshp_BarcCohortMbrshp a
on a.CPS=e.bidYearCPS AND 
	a.cohort=e.cohort AND 
	a.depound=e.DEPound
left join dbo.vwRollupForecastInfo c
on a.CPS=c.PlanName 
left join  dbo.ESRDInpt_NewPlanMbrDist d 
	on  d.region=c.region AND
	d.product=CASE WHEN c.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN c.SNPType = 'Chronic' THEN 'CSNP'
		WHEN c.SNPType = 'Institutional' THEN 'CSNP'
		ELSE c.Product END 	AND 
		d.depound=a.DEPound AND 
		d.cohort=e.cohort AND 
		d.class=e.class
where  --a.cohort<>'Hospice' AND --Updated 5/9 to include New in 2024 Hospice data
	c.isSCTPlan=1 and 
	c.ISLiveIndex=1 
	ORDER BY CPS,e.cohort,e.cohortstatus,e.DEPound,e.Class



/*******************************************************************************************************/
/**************************************ESRD Final Membership Table *************************************/
/*******************************************************************************************************/

--select * from #ProjNonNewPlanMbrs



--Select * from dbo.ESRDMbrshp_projESRDMembership order by CPS,Cohort,CohortStatus,DEPound,Class where cps='H5216-259-000'
--select Sum(MbrMths) as SumMbrMths from #ProjESRDMembership where DialysisSUbtypeOrig is not NULL

--IF (SELECT OBJECT_ID('dbo.ESRDMbrshp_ProjESRDMembership')) IS NOT NULL DROP TABLE dbo.ESRDMbrshp_ProjESRDMembership

Delete From dbo.ESRDMbrshp_ProjESRDMembership
insert into dbo.ESRDMbrshp_ProjESRDMembership
Select 
	c.Region as BY_Region,
	Product as BY_Product,
	CPS,
	Cohort,
		Class,
		DEPound,
		SUM(MbrMths) AS MbrMths

from 
(
	Select  Region,
	Product,
	CPS,
		Cohort,
		Class,
		DEPound,
		MbrMths 
	from #ESRDMbrshp_ProjNonNewPlanMbrs a 
	UNION ALL 
	(
		SELECT Region,
			Product,
			cps,
				cohort,
				class,
				Depound,
				MbrMths 
		FROM #ESRDMbrshp_ProjNewPlanMbrs B
	)

) C 
inner join dbo.#Plans Z					-- updated to use #Plans 2/2
on c.CPS=z.PlanName
GROUP BY c.Region,product,CPS,COhort,Class,DEPound

order by CPS, cohort,Class,DEPound



-- MRA DATA 
GO
