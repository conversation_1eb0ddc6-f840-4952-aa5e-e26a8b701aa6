SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetBenefitSpotlightStatus]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: fnGetBenefitSpotlightStatus
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Oct-12
-- HEADER UPDATED: 2010-Aug-23
--
-- DESCRIPTION: This function returns the IN/OON spotlight status of a benefit for a plan
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@BenefitCategoryID
--		@Network
--	Output:
--
-- TABLES:
--	Read:
--		SavedPlan<PERSON>enefitDetail 
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007-Oct-12		1			Initial Version														Christian Cofie
-- 2010-Jul-28		2			Moved to 2012														Joe Casey
-- 2010-Aug-23		3			Removed PlanVersion													Joe Casey
-- 2022-Jun-22		4			Should be turned off; set return to 0								Aleksandar Dimitrijevic
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBenefitSpotlightStatus]
(
    @ForecastID int,
    @BenefitCategoryID smallint,
    @Network tinyint
)

RETURNS BIT AS  

BEGIN 

    DECLARE @SpotlightStatus bit

    SET @SpotlightStatus = 0
		--ISNULL
		--	(
		--		(
		--		SELECT 
		--			TOP 1 (Case When @Network = 1 Then IsINSpotlight Else IsOONSpotlight End )
		--		FROM SavedPlanBenefitDetail
		--		WHERE IsBenefitYearCurrentYear = 0
		--			AND ForecastID = @ForecastID
		--			AND BenefitCategoryID = @BenefitCategoryID
		--			AND SynchronizedBenefitCategoryID=0
		--		)
		--	,0)

    RETURN @SpotlightStatus

END

GO
