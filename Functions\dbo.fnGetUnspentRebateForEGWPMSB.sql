SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetUnspentRebateForEGWPMSB
--
-- AUTHOR:  Sule <PERSON> 
--
-- CREATED DATE: 2009-Dec-30
--
-- DESCRIPTION: Returns the unspent rebate for group MSB's
--             
-- PARAMETERS:
--  Input:
--      @ForecastID 
--       
--  Output: @Results
--
-- RETURNS: 
--  UnspentRebate FLOAT
--  EGWPMSB FLOAT 
--
-- TABLES:
--  Read:
--	PerExtCMSValues
--	SavedPlanHeader
--	SavedPlanAddedBenefits
--	SavedPlanAssumptions
--	LkpIntAddedBenefitType
--	LkpIntAddedBenefitCategory
--	CalcMedicareCoveredFinalPricing
--	CalcPlanProjection
--	CalcPlanAdminBlend
--	CalcFinalPremium
--
--  Write:
--    None
--
-- VIEWS: Read: None
--
-- STORED PROCS: Executed: None
--
-- Functions:
--  fnGetPlanBenchmark
--	fnAppGetPlanExpenseProfit
--	fnGetRebatePercent
--	fnGetMARebateAllocation
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE         VERSION     CHANGES MADE                                                DEVELOPER                        
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2009-Dec-29      1       Initial version.                                            Sule Dauda
-- 2010-Sep-21      2       Updated for 2012 DB and revised for coding standards        Michael Siekerka
-- 2010-Sep-29      3       Revised for fnGetRebatePercent, and coding standards        Jake Gaecke
-- 2010-Dec-09      4       Adjusted @Variable2 for MA Profit Percentage                Michael Siekerka 
-- 2011-Jan-03		5		Removed Profit from the @Loading variable					Craig Wright
-- 2011-Feb-03      6       Removed call to fnAppGetGeneralMABPTValues that was         Michael Siekerka
--                              causing circular reference
-- 2011-Feb-07      7       Removed unnecessary variables                               Michael Siekerka
-- 2011-Feb-08      8       Added rounding to Variables 1 & 2                           Michael Siekerka
-- 2011-Feb-08      9       Added b and c to output for verification purposes           Michael Siekerka
-- 2012-Feb-24		10		Made changes to account for new admin buckets Quality and	Alex Rezmerski
--							TaxesAndFees
-- 2012-Apr-6		11		Added a case code to eliminiate negative  EGWPMSB			Tim Gao
-- 2012-aUG-09		12		Change code to get data from different sources for			Tim Gao
--							calculating EGWPMSB
-- 2013-Jan-31		13		Restore UnspentRebate										Tim Gao
-- 2018-Sep-11		14		Modified PerIntAdminExpenses to be CalcPlanAdminBlend		Jordan Purdue
-- 2020-Dec-14      15      Removed case statement for AltRebateOrder                   Brent Osantowski 
-- 2022-May-18		16		MAAUI migration; replaced input variable @PlanIndex with
--							@ForecastID; changed reference to table 
--							CalcMedicareCoveredFinalPricing from PlanIndex to  
--							ForecastID; removed nested queries							Aleksandar Dimitrijevic
-- 2023-Aug-03      17     Added  NOLOCK and Internal Parameter							Sheetal Patil 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetUnspentRebateForEGWPMSB]
(
     @ForecastID INT
)

RETURNS @Results TABLE
(  
    UnspentRebate FLOAT,
    EGWPMSB FLOAT,
    b FLOAT,
    c FLOAT,
    RevisedTotalPMPM FLOAT
) AS

BEGIN
	DECLARE @XForecastID INT = @ForecastID;
    DECLARE @Variable2 FLOAT,
            @Variable3 FLOAT,
            @EGWPMSB   DECIMAL(8,4),
            @UserFee DECIMAL(3,2),
            @UnspentRebate FLOAT,
            @ABCostShare  FLOAT,
            @ABSuppBenefit FLOAT,
            @PtBbuydown   FLOAT,
            @PtDbuydown   FLOAT,
            @PtSuppBuydown  FLOAT,
            @TotalPMPM FLOAT,
            @MedPMPM FLOAT,
            @NonESRDNet FLOAT,
            @SupPMPM FLOAT,
            @BenchMark FLOAT,
            @Rebate FLOAT,
            @DrugPremium FLOAT,
            @Loading FLOAT,
            @RebatePercent FLOAT,
            @MAProfitPercent FLOAT

	SET @UserFee = (SELECT UserFee FROM dbo.PerExtCMSValues WITH (NOLOCK))
	SET @BenchMark = (SELECT PlanBenchmark FROM dbo.fnGetPlanBenchmark(@XForecastID))

	--Added Benefit Total
	DECLARE @AddedBenefitTotal TABLE


		(
		 ForecastID INT,
		 AddedBenefitAllowed FLOAT,
		 AddedBenefitCostShare FLOAT
		)

	INSERT INTO @AddedBenefitTotal
	SELECT sph.ForecastID,
		   AddedBenefitAllowed = ROUND(SUM
										(CASE WHEN labt.IsStandard = 1
											  THEN (labt.INAddedBenefitAllowed
													+
													(CASE WHEN sph.PlanTypeID = 1
														  THEN 0
														  ELSE ISNULL(labt.OONAddedBenefitAllowed,0)
													 END))
											  ELSE (pab.INAddedBenefitAllowed
													+
													(CASE WHEN sph.PlanTypeID = 1
														  THEN 0
														  ELSE ISNULL(pab.OONAddedBenefitAllowed,0)
													 END))
										 END),2),
		   AddedBenefitCostShare = ROUND(SUM
											(CASE WHEN labt.IsStandard = 1
												  THEN (labt.INAddedBenefitCostShare
														+
														(CASE WHEN sph.PlanTypeID = 1
																THEN 0
																ELSE ISNULL(labt.OONAddedBenefitCostShare,0)
														END))
												  ELSE (pab.INAddedBenefitCostShare
														+
														(CASE WHEN sph.PlanTypeID = 1
																THEN 0
																ELSE ISNULL(pab.OONAddedBenefitCostShare,0)
														END))
											 END) ,2)
	FROM dbo.SavedPlanHeader sph WITH (NOLOCK)
	INNER JOIN dbo.SavedPlanAddedBenefits pab WITH (NOLOCK)
		ON pab.ForecastID = sph.ForecastID
	   AND pab.IsHidden = 0
	INNER JOIN dbo.LkpIntAddedBenefitType labt WITH (NOLOCK)
		ON labt.AddedBenefitTypeID = pab.AddedBenefitTypeID
	INNER JOIN dbo.LkpIntAddedBenefitCategory labc WITH (NOLOCK)
		ON labc.AddedBenefitCatID = labt.AddedBenefitCatID
	WHERE sph.ForecastID = @XForecastID
	  AND sph.IsHidden = 0
	GROUP BY sph.ForecastID,
			 sph.PlanTypeID

	------------------------------------------------------------------------------------------------------------------------------------
	SELECT @DrugPremium  = (ISNULL(RxBasicPremium,0) + ISNULL(RxSuppPremium,0)) FROM dbo.SavedPlanAssumptions WITH (NOLOCK) WHERE ForecastID = @XForecastID
	SELECT @MAProfitPercent = ProfitPercent FROM dbo.fnAppGetPlanExpenseProfit(@XForecastID)
	SELECT @MedPMPM = CoveredActEquivNet FROM dbo.CalcMedicareCoveredFinalPricing WITH (NOLOCK) WHERE ForecastID = @XForecastID AND DualEligibleTypeID = 2
	SET @NonESRDNet = (SELECT NetNonESRD = (CONVERT(DECIMAL(20,12),(mcfp.Net))
											+ CONVERT(DECIMAL(14,6),(ISNULL(abt.AddedBenefitAllowed,0)
										    - ISNULL(abt.AddedBenefitCostShare,0))))    
					   FROM dbo.CalcMedicareCoveredFinalPricing mcfp WITH (NOLOCK)
					   INNER JOIN dbo.CalcPlanProjection cpp WITH (NOLOCK)
							ON mcfp.ForecastID = cpp.ForecastID
						   AND mcfp.DualEligibleTypeID = cpp.DualEligibleTypeID
						LEFT JOIN @AddedBenefitTotal abt   -- End of temp qry-AddedBenefitTotal Table
							ON mcfp.ForecastID = abt.ForecastID
						WHERE mcfp.ForecastID = @ForecastID
							AND mcfp.DualEligibleTypeID = 2 --Combined
						)


	SET @SupPMPM = @NonESRDNet - @MedPMPM
    SELECT @Loading = SUM(pae.MAMarketingAdminPMPM + pae.MADirectAdminPMPM + @UserFee + pae.MAIndirectAdminPMPM
						  + ISNULL(pae.MAQualityAdminPMPM,0) + ISNULL(pae.MATaxesAndFeesAdminPMPM,0))   
	FROM dbo.SavedPlanHeader sph WITH (NOLOCK)
	INNER JOIN dbo.CalcPlanAdminBlend pae WITH (NOLOCK)
		ON sph.ForecastID = pae.ForecastID
	   AND sph.PlanYearID = pae.PlanYearID
	WHERE sph.ForecastID = @XForecastID

    SELECT @RebatePercent = ROUND(dbo.fnGetRebatePercent(@XForecastID),15)

    SET @Variable2 = (@Loading + (@DrugPremium - ROUND(@BenchMark * @RebatePercent, 2)) * (1 - @MAProfitPercent) + ROUND(@MedPMPM * (@RebatePercent - 1), 2))
    SET @Variable3 = ROUND(@MedPMPM * @Loading * (@RebatePercent - 1), 2)
    SET @TotalPMPM = (-1 * (@Variable2) + 
            ROUND(POWER((dbo.fnSignificantDigits((@Variable2 * @Variable2 - 4.0* @Variable3), 15)), 0.5),6))/2.0

    SET @EGWPMSB = CASE WHEN @TotalPMPM - (@MedPMPM + @SupPMPM) < 0 THEN 0
							ELSE ISNULL(@TotalPMPM - (@MedPMPM + @SupPMPM), 0)
					END

    SET @Rebate = ISNULL((SELECT rebate FROM dbo.CalcFinalPremium WITH (NOLOCK) WHERE ForecastID = @XForecastID),0) 

    SELECT 
        @ABCostShare =  Rebate.RedABCostShare,
        @ABSuppBenefit =  Rebate.SuppPremBuydown,
        @PtBbuydown = PartBPremiumBuydown,
        @PtDbuydown = RxBasicBuydown,
        @PtSuppBuydown = RxsuppBuydown
    FROM SavedPlanHeader sph WITH (NOLOCK)
    INNER JOIN dbo.fnGetMARebateAllocation(@XForecastID) rebate
        ON rebate.ForecastID = sph.ForecastID          

    SET @ABCostShare = ISNULL(@ABCostShare,0) 

    SET @ABSuppBenefit = ISNULL(@ABSuppBenefit,0)

    SET @PtDbuydown = ISNULL(@PtDbuydown,0)

    SET @PtSuppBuydown = ISNULL(@PtSuppBuydown,0) 

    SET @UnspentRebate = ROUND((@Rebate -(@ABCostShare + @ABSuppBenefit + ROUND(@PtBbuydown,1)+ ROUND(@PtDbuydown,1)   + ROUND(@PtSuppBuydown,1)) ),2)

    BEGIN
        INSERT @Results
        VALUES(@UnspentRebate, @EGWPMSB, @Variable2, @Variable3, @TotalPMPM)
    END
    RETURN               
END

GO
