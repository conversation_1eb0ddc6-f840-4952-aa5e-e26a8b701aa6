SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetMABPTWS3AddedBenCSDesc]    ******/

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetMABPTWS3CSDesc
--
-- AUTHOR: Joe Casey
--
-- CREATED DATE: 2011-Mar-25 
-- HEADER UPDATED: 2011-Mar-30
--
-- DESCRIPTION: This function returns IN/OON cost share description for added benefits.  
--
-- PARAMETERS:
--	Input:
--	Output:
-- 
-- TABLES: 
--	Read:
--		LkpExtCMSBidServiceCategory
--		LkpIntAddedBenefitType
--		SavedPlanAddedBenefits
--		SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Mar-03      1           Initial Version                                                     Joe Casey
-- 2011-Mar-30		2			Removed 'Missing Description' for OON so it is blank				Joe Casey
-- 2015-May-20		3			Used rtrim function to remove white spaces							Deepali Mittal
-- 2023-Aug-02		4			Added WITH (NOLOCK), Internal Parameters and Table schema			Sheetal Patil 
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS3AddedBenCSDesc]
(
    @ForecastID INT,
    @BidServiceCategoryID SMALLINT,
    @UtilType VARCHAR(1),
    @IsIN BIT
)
RETURNS VARCHAR(MAX)
AS
BEGIN
    DECLARE @XForecastID INT = @ForecastID ,
	@XBidServiceCategoryID SMALLINT = @BidServiceCategoryID,
	@XUtilType VARCHAR(1) = @UtilType,
	@XIsIN BIT = @IsIN
	
    DECLARE @ResultVar VARCHAR(MAX)
    SELECT
		@ResultVar = COALESCE(@ResultVar,'') + RTRIM(CAST(AddedBenDesc AS VARCHAR(MAX))) + CHAR(10)
	FROM (
		SELECT AddedBenDesc =
			CASE WHEN @XIsIN = 1 THEN
				ISNULL(spab.INAddedBenefitDescription,'Missing Description')
			ELSE
				CASE WHEN ISNULL(spab.OONAddedBenefitDescription,'') = '' 
						AND sph.PlanTypeID IN (2,3) --'LPPO, RPPO
					THEN ISNULL(spab.OONAddedBenefitDescription,'')
					ELSE spab.OONAddedBenefitDescription
				END
			END
		FROM dbo.SavedPlanAddedBenefits spab WITH (NOLOCK)
		INNER JOIN dbo.SavedPlanHeader sph WITH (NOLOCK)
			ON spab.ForecastID = sph.ForecastID
		INNER JOIN dbo.LkpIntAddedBenefitType abt WITH (NOLOCK)
			ON spab.AddedBenefitTypeID = abt.AddedBenefitTypeID
		INNER JOIN dbo.LkpExtCMSBidServiceCategory bsc WITH (NOLOCK)
			ON spab.BidServiceCatID = bsc.BidServiceCategoryID
		WHERE spab.ForecastID = @XForecastID
			AND spab.IsHidden = 0
			AND bsc.BidServiceCategoryID = @XBidServiceCategoryID
			AND bsc.UtilType = @XUtilType
		GROUP BY
			sph.PlanTypeID,
			spab.INAddedBenefitDescription,
			spab.OONAddedBenefitDescription
		) AddedBenDesc
		
	--To remove the carriage return
	SET @ResultVar = LEFT(@ResultVar,LEN(@ResultVar)-1)
		
    RETURN @ResultVar
END
GO
