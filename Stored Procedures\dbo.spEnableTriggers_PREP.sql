SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO







/****** Object:  StoredProcedure [dbo].[EnableTriggersP_PREP]  ******/





-- -------------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE														DEVELOPER		
-- -------------------------------------------------------------------------------------------------------------------------
-- 2020-01-28		1			Initial Build Test Run												<PERSON>
-- 2020-04-029		2			Comment out the trigger line involving tr_<PERSON><PERSON><PERSON><PERSON><PERSON> due to     <PERSON>
--								trigger being dropped	
-- 2021-08-30		3			Add other PREP triggers so that all can be enabled						Craig Nielsen
----------------------------------------------------------------------------------------------------------------------------
--Stored Procedure that helps with PREP model turnover
CREATE PROCEDURE [dbo].[spEnableTriggers_PREP] WITH EXECUTE AS OWNER 
AS

    BEGIN
		SET NOCOUNT ON;

	ENABLE TRIGGER dbo.trUserAdjustments_PREP ON dbo.UserAdjustments_PREP;
	ENABLE TRIGGER dbo.tr_CrosswalkUpload ON dbo.CW_Crosswalks;
	ENABLE TRIGGER dbo.tr_BidModelISAR ON dbo.BidModel_ISAR;
	ENABLE TRIGGER dbo.tr_FEM_BaseBenefitFactor ON dbo.W_FEM_BenefitFactorBase;
	ENABLE TRIGGER dbo.tr_MAProjUpload ON dbo.W_FEM_MAProjUpload;
	ENABLE TRIGGER dbo.tr_PDProjUpload ON dbo.W_FEM_PDProjUpload;
	ENABLE TRIGGER dbo.tr_FEM_TrendFactors ON dbo.W_FEM_TrendFactors;
	ENABLE TRIGGER dbo.tr_ProviderFund_NoGrouper ON dbo.FEM_ProviderFunding_NoGrouper;
	--ENABLE TRIGGER dbo.tr_BidModelManual ON dbo.W_FEM_BidModelManualBasePlans; -- Commented out on 4-29 after trigger is dropped
	ENABLE TRIGGER dbo.tr_PlanMapping ON dbo.W_FEM_PlanMapping;
	--Update 8/30
	ENABLE TRIGGER dbo.tr_GrouperUpload ON dbo.FEM_GrouperUpload;
	ENABLE TRIGGER dbo.tr_AccessLog ON dbo.W_FEM_AccessLog;
	ENABLE TRIGGER dbo.tr_FEM_Credibility ON dbo.W_FEM_Credibility;
	ENABLE TRIGGER dbo.tr_DOFR ON dbo.W_FEM_DOFR;
	ENABLE TRIGGER dbo.tr_NewVersion ON dbo.W_FEM_VersionLog;

	END;
GO
