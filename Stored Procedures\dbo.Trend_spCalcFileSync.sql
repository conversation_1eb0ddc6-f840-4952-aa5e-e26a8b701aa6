SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_spCalcFileSync
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: FEB-25-2020
--
-- DESCRIPTION:  This procedures reads from the saved component import tables, and then writes the data for only those ComponentVersionIDs that are included in the 
--				live package option (Trend_SavedPackageOption) to the calc file sync tables. 
--  
--              
-- PARAMETERS:
--  Input  : @LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_SavedComponentImportAnnual
--					Trend_SavedComponentImportQuarterly
--					Trend_SavedPackageOption
--
--          Write:  Trend_CalcFileSync_Annual
--					Trend_CalcFileSync_Quarterly
--                  
--
-- VIEWS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FEB-25-2020      1           Initial Version                                                     Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_spCalcFileSync]

@LastUpdateByID CHAR(13)

AS
    BEGIN
        SET NOCOUNT ON;


        -- Declare variables
        DECLARE @LivePackageOption INT;
        SET @LivePackageOption = (SELECT    DISTINCT
                                            PackageOptionID
                                  FROM      dbo.Trend_SavedPackageOption
                                  WHERE     IsLivePackage = 1); -- There will only ever be one live package, so this selects a single value. 


        -- Get list of ComponentVersionIDs in the live package.
        IF (SELECT  OBJECT_ID ('tempdb..#ComponentVersionID')) IS NOT NULL
            DROP TABLE #ComponentVersionID;
        SELECT  DISTINCT
                ComponentVersionID
        INTO    #ComponentVersionID
        FROM    dbo.Trend_SavedPackageOption
        WHERE   IsLivePackage = 1;


        -- Delete and insert into final output table (Annual)
        DELETE  FROM dbo.Trend_CalcFileSync_Annual;
        INSERT INTO dbo.Trend_CalcFileSync_Annual
            (PackageOptionID
            ,ComponentVersionID
            ,ComponentVersionName
            ,ProjectionName
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,Component
            ,PlanYearID
            ,ReportingCategory
            ,BenefitCategoryID
            ,IsNationwide
            ,ActuarialDivision
            ,ActuarialRegion
            ,ActuarialMarket
            ,ProductType
            ,SNPType
            ,PlanType
            ,MAPlanDesign
            ,AssumptionName
            ,CostAdjustment
            ,UseAdjustment
            ,ImportFileName
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @LivePackageOption
               ,ComponentVersionID
               ,ComponentVersionName
               ,ProjectionName
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,Component
               ,PlanYearID
               ,ReportingCategory
               ,BenefitCategoryID
               ,IsNationwide
               ,ActuarialDivision
               ,ActuarialRegion
               ,ActuarialMarket
               ,ProductType
               ,SNPType
               ,PlanType
               ,MAPlanDesign
               ,AssumptionName
               ,CostAdjustment
               ,UseAdjustment
               ,ImportFileName
               ,@LastUpdateByID
               ,GETDATE ()
        FROM    dbo.Trend_SavedComponentImportAnnual
        WHERE   ComponentVersionID IN (SELECT   ComponentVersionID FROM #ComponentVersionID);


        -- Delete and insert into final output table (Quarterly)
        DELETE  FROM dbo.Trend_CalcFileSync_Quarterly;
        INSERT INTO dbo.Trend_CalcFileSync_Quarterly
            (PackageOptionID
            ,ComponentVersionID
            ,ComponentVersionName
            ,ProjectionName
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,Component
            ,PlanYearID
            ,QuarterID
            ,ReportingCategory
            ,BenefitCategoryID
            ,IsNationwide
            ,ActuarialDivision
            ,ActuarialRegion
            ,ActuarialMarket
            ,ProductType
            ,SNPType
            ,PlanType
            ,MAPlanDesign
            ,AssumptionName
            ,CostAdjustment
            ,UseAdjustment
            ,ImportFileName
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @LivePackageOption
               ,ComponentVersionID
               ,ComponentVersionName
               ,ProjectionName
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,Component
               ,PlanYearID
               ,QuarterID
               ,ReportingCategory
               ,BenefitCategoryID
               ,IsNationwide
               ,ActuarialDivision
               ,ActuarialRegion
               ,ActuarialMarket
               ,ProductType
               ,SNPType
               ,PlanType
               ,MAPlanDesign
               ,AssumptionName
               ,CostAdjustment
               ,UseAdjustment
               ,ImportFileName
               ,@LastUpdateByID
               ,GETDATE ()
        FROM    dbo.Trend_SavedComponentImportQuarterly
        WHERE   ComponentVersionID IN (SELECT   ComponentVersionID FROM #ComponentVersionID);


    END;
GO
