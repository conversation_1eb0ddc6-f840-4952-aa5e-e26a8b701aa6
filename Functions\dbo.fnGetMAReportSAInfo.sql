SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetMAReportSAInfo]   ******/

/*===============================================================================*/
/****** Object:  UserDefinedFunction [dbo].[fnGetMAReportSAInfo]    Script Date: 10/16/2013 13:27:42 ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportSAInfo
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2009-Mar-16
-- HEADER UPDATED: 2010-Oct-05
--
-- DESCRIPTION:  To pull counties included in bidable and active plans (used for Auditing Service Area)
--
-- PARAMETERS:
--	Input:
--      @PlanYearID
--      @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--      ArcLkpExtCMSStateCounty
--      ArcLkpExtCMSStateTerritory
--      ArcSavedPlanHeader
--      ArcSavedPlanStateCountyDetail
--      LkpExtCMSStateCounty
--      LkpExtCMSStateTerritory
--		MAReportPlanLevel
--      SavedPlanHeader
--      SavedPlanStateCountyDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-16		1		    Initial Version										                Keith Galloway
-- 2010-Oct-05      2           Added Arc tables for past year reference. Removed @PlanVersion      Joe Casey
--                                  as a parameter.
-- 2010-Nov-10		3		    Added PlanTypeName													Jiao Chen
-- 2013-Oct-13		4			Modified to Include SegmentId										Anubhav Mishra
-- 2015-Oct-09      5           Modified logic to exclude bidYear from Arc Tables                   Kritika Singh
-- 2022-Jun-17		6			MAAUI migration; SavedPlanHeader structure change related coding	Aleksandar Dimitrijevic								
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportSAInfo]
    (
    @PlanYearID SMALLINT,
    @ForecastID INT
    )
RETURNS TABLE RETURN
	(
	SELECT
	    spcd.PlanYearID,
		sph.ContractNumber,
		sph.PlanID,
		sph.SegmentId,  --Added SegmentId
		spcd.ForecastID,
		cmsState.StateTerritoryCode,
		cmsState.StateTerritoryName,
		spcd.CountyCode,
		cmsCnty.CountyName,
		pd.PlanTypeName	
	FROM 
	    (SELECT * FROM SavedPlanStateCountyDetail
	    UNION ALL
	    SELECT * FROM ArcSavedPlanStateCountyDetail where PlanYearID != dbo.fnGetBidYear()) spcd
	INNER JOIN
	    (SELECT * FROM SavedPlanHeader
	    UNION ALL
	    SELECT PlanyearID	
			  ,ForecastID
			  ,0
			  ,0
			  ,MarketID
			  ,PlanTypeID
			  ,ContractNumber
			  ,PlanID
			  ,SegmentID
			  ,CPDID
			  ,IsSNP
			  ,ContactID
			  ,SecondaryContactID
			  ,PartBPremiumBuyDown
			  ,MARegionID
			  ,Notes
			  ,isMAPD
			  ,IsOffModelRx
			  ,IsHidden
			  ,LastUpdateByID
			  ,LastUpdateDateTime
			  ,IsToReprice
			  ,SNPTypeID
			  ,AltRebateOrder
			  ,IsLiveIndex
			  ,PlanDescription
			  ,IsMLA
			  ,QualityInitDescrip
			  ,IsCombinedDeductible
			  ,IsSkipInducedUtilizationMapping
			  ,IsRiskPlan
			  ,IsSctPlan
			  ,IsFiledPlan
			  ,IsLocked
			  ,PlanName
			  ,CertifyingActuaryUserID 
		FROM ArcSavedPlanHeader where PlanYearID != dbo.fnGetBidYear()) sph
		ON spcd.PlanYearID = sph.PlanYearID
		AND spcd.ForecastID = sph.ForecastID
	INNER JOIN dbo.MAReportPlanLevel pd
		ON pd.ForecastID =  spcd.ForecastID 
        AND pd.PlanYearID = spcd.PlanYearID
	LEFT JOIN 
	    (SELECT * FROM LkpExtCMSStateCounty
	    UNION ALL
	    SELECT * FROM ArcLkpExtCMSStateCounty where PlanYearID != dbo.fnGetBidYear()) cmsCnty
		ON spcd.PlanYearID = cmsCnty.PlanYearID
		AND spcd.StateTerritoryID = cmsCnty.StateTerritoryID
		AND spcd.CountyCode = cmsCnty.CountyCode
	LEFT JOIN
	    (SELECT * FROM LkpExtCMSStateTerritory
	    UNION ALL
	    SELECT * FROM ArcLkpExtCMSStateTerritory where PlanYearID != dbo.fnGetBidYear()) cmsState
		ON spcd.PlanYearID = cmsState.PlanYearID
		AND spcd.StateTerritoryID = cmsState.StateTerritoryID
	WHERE spcd.PlanYearID = @PlanYearID
	    AND spcd.ForecastID = @ForecastID
	    AND spcd.IsCountyExcludedFromBPTOutput = 0
	)

GO
