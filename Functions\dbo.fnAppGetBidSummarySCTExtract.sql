SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetBidSummarySCTExtract]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetBidSummarySCTExtract
--
-- AUTHOR: Apoorva Nasa
--
-- CREATED DATE: 2018-Oct-24
--
-- DESCRIPTION: Designed to extract fields for SCT Actuarial Summary
--
-- PARAMETERS:
--	Input: @WhereIN
--     
--  Output:
--
-- TABLES: 
--	Read:
--		SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnStringSplit
--      fnAppGetBidSummarySCT
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION	    CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2018-Oct-24		1			Initial Version							                           Apoorva Nasa

----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetBidSummarySCTExtract]
    (
      @WhereIN VARCHAR(MAX) = NULL
    )
RETURNS @Results TABLE
    (
      ForecastID INT ,
      SCTBaseExp DECIMAL(23, 15) ,
      SCTBaseMan DECIMAL(23, 15) ,
      SCTBaseBlend DECIMAL(23, 15) ,
      SCTCurrentExp DECIMAL(23, 15) ,
      SCTCurrentMan DECIMAL(23, 15) ,
      SCTCurrentBlend DECIMAL(23, 15) ,
      SCTProjExp DECIMAL(23, 15) ,
      SCTProjMan DECIMAL(23, 15) ,
      SCTProjBlend DECIMAL(23, 15)
    )
AS
    BEGIN
	
	
        DECLARE @MyCursor CURSOR;
        DECLARE @ForecastID INT;
        BEGIN
            SET @MyCursor = CURSOR FOR
    SELECT Value FROM dbo.fnStringSplit (@WhereIN, ',');      

            OPEN @MyCursor; 
            FETCH NEXT FROM @MyCursor 
    INTO @ForecastID;

            WHILE @@FETCH_STATUS = 0
                BEGIN
                    INSERT  INTO @Results
                            SELECT  S.*
                            FROM    dbo.fnAppGetBidSummarySCT(@ForecastID) S
                                    INNER JOIN dbo.SavedPlanHeader P ON P.ForecastID = S.ForecastID
                            WHERE   P.ForecastID = @ForecastID;
                    FETCH NEXT FROM @MyCursor 
      INTO @ForecastID; 
                END; 

            CLOSE @MyCursor;
            DEALLOCATE @MyCursor;
        END;
		
        RETURN;
    END;
GO
