SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------- 
-- FUNCTION NAME: spCopybenefitsModelTurnOver 
--
-- AUTHOR: Sumit Kumar   
--  
-- CREATED DATE: 2015-06-29 
--   
-- DESCRIPTION: Procedure responsible for running the Third step in ModelTurnOver(Run by package) 
--  
-- PARAMETERS: NONE 
-- 
-- TABLES: 
-- Read: 
-- Write: 
-- 
-- VIEWS: 
--   
-- FUNCTIONS: 
--  
-- STORED PROCS: 
--  
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------- 
-- DATE          VERSION      CHANGES MADE              DEVELOPER 
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2015-06-29    1          Initial Version            Sumit Kumar   
-- 2015-Jul-03	 2			Added columns UpdatedBy,UpdatedDateTime for table SavedPlanBenefitDetail			Deepali Mittal
-- 2015-Oct-14   3          Replace UpdatedBy by LastUpdateByID and UpdatedDateTime by
--                          LastUpdateDateTime for table SavedPlanBenefitDetail                                 Manisha Tyagi
-- 2015-Nov-13   4          Removed updation of PerExtContractNumberHeader                                      Kritika Singh
-- 2015-Nov-16   5          Appended dynamic PlanYearId to bkp_SavedPlanBenefitDetail                           Kritika Singh
-- 2020-Jan-13   6          [20.02] Added DedApplies fields and general schema cleanup                          Keith Galloway
-- 2020-Feb-07   7          Adjusting for removal of "\HUMAD" prefix                                            Brent Osantowski
-- 2022-Jun-22	 8			Removed old structure fields														Aleksandar Dimitrijevic
-- ---------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[spCopybenefitsModelTurnOver]
AS

BEGIN

DECLARE	@PlanYearID varchar(10);
SELECT @PlanYearID = PlanYearID FROM dbo.lkpIntPlanyear where IsBidYear = 1


	--Update SavedCUHeader - Increase the Years on the Incurred and PaidThrough Dates to enable trend to not be jacked. 

--	-- Backup
DECLARE @SqlQuery nvarchar(max);
SET @SqlQuery ='IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N''[dbo].[bkp_SavedPlanBenefitDetail'+ @PlanYearID +'_Update]'') AND type in (N''U'')) '
				+ ' DROP TABLE [dbo].[bkp_SavedPlanBenefitDetail'+ @PlanYearID +'_Update]' 
				+ ' SELECT	* INTO	[dbo].[bkp_SavedPlanBenefitDetail'+ @PlanYearID +'_Update] FROM [dbo].[SavedPlanBenefitDetail];'

PRINT @SqlQuery
EXEC sp_executesql @SqlQuery


	-- Delete old projected year (or Current year for last bid season)

	DELETE FROM	[dbo].[SavedPlanBenefitDetail]
	WHERE	IsBenefitYearCurrentYear = 1;


	-- Copy projected year to current year

    INSERT  INTO [dbo].[SavedPlanBenefitDetail]
            ( [PlanYearID],
              [ForecastID],
              [IsBenefitYearCurrentYear],
              [BenefitCategoryID],
              [BenefitOrdinalID],
              [INBenefitTypeID],
              [INBenefitValue],
              [INDayRangeBegin],
              [INDayRangeEnd],
              [INDedApplies],
              [OONBenefitTypeID],
              [OONBenefitValue],
              [OONDayRangeBegin],
              [OONDayRangeEnd],
              [OONDedApplies],
              [PercentCoveredAllowed],
              [PercentCoveredCostShare],
              [LastUpdateByID],
              [LastUpdateDateTime]
            )
            SELECT  [PlanYearID],
                    [ForecastID],
                    1 --[IsBenefitYearCurrentYear]
                    ,
                    [BenefitCategoryID],
                    [BenefitOrdinalID],
                    [INBenefitTypeID],
                    [INBenefitValue],
                    [INDayRangeBegin],
                    [INDayRangeEnd],
                    [INDedApplies],
                    [OONBenefitTypeID],
                    [OONBenefitValue],
                    [OONDayRangeBegin],
                    [OONDayRangeEnd],
                    [OONDedApplies],
                    [PercentCoveredAllowed],
                    [PercentCoveredCostShare],
                    RIGHT(USER,7),
                    GETDATE()
            FROM    [dbo].[SavedPlanBenefitDetail];		

		PRINT '[SavedPlanBenefitDetail] IS UPDATED'

		END
GO
