SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spGetAllRoles
--
-- Author:		T Y Bhavana
--
-- Create date: 27-Oct-2021
-- HEADER UPDATED: 27-Oct-2021
--
-- DESCRIPTION:Validating if user exists or not
--
-- PARAMETERS:
--	Input:
--		
--
-- RETURNS: 
--		
--
-- TABLES:
--  Read:AdminRoleHeader
--
--Write:

--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--    sp_helprolemember (system)
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
--2021-oct-27			1			Intial version													 Bhavana                       
-- ----------------------------------------------------------------------------------------------------------------------




-- Description:	validate  AdminRoleHeader
-- =============================================


CREATE PROCEDURE [dbo].[spGetAllRoles]

AS
BEGIN TRY
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN

SELECT RoleID,RoleName FROM dbo.AdminRoleHeader
	END
END TRY

BEGIN CATCH
   

    SELECT
        ERROR_NUMBER() AS ErrorNumber,
        ERROR_SEVERITY() AS ErrorSeverity,
        ERROR_STATE() AS ErrorState,
        ERROR_PROCEDURE() AS ErrorProcedure,
        ERROR_LINE() AS ErrorLine,
        ERROR_MESSAGE() AS ErrorMessage;
END CATCH









GO
