﻿-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	TrendAdj_spUploadsFromExcel
--
-- CREATOR:			<PERSON><PERSON><PERSON><PERSON>
--
-- CREATED DATE:	2024-Sep-04
--
-- DESCRIPTION:		This sp is executed by the Unified Actuarial Adjustment Compiler tool; 
-- It feeds all TrendAdj input tables, initial adjustment table as well as permanent key infor and rep/ben cat adjustment tables.
--		
-- PARAMETERS:
--  Input  :		@Table
--					@JsonData
--					@LastUpdateByID
--					@Region
--					@PlanList
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			TrendAdj_Controls_SavedSelections
--					SavedPlanInfo
--					LkpIntBenefitCategory
--					SCT_SavedRevClaimsAdjustments
--					
--  Write:			TrendAdj_Controls_SavedSelections
--					TrendAdj_SAR_LkpDiffThreshold
--					TrendAdj_SAR_LkpMethodology
--					TrendAdj_OSNP_LkpSNPType
--					TrendAdj_SAR_CalcKeyInfo
--					TrendAdj_OSNP_CalcKeyInfo
--					TrendAdj_CalcPlanAdjmt
--					TrendAdj_CalcPlanAdjmt_Prior
--					SCT_SavedRevClaimsAdjustments
--
-- VIEWS: 
--	Read:			vwPlanInfo
--
-- FUNCTIONS:		fnGetBidYear
--					Trend_fnCalcStringToTable
--
-- STORED PROCS:	spAppImportTrendUploadActuarialAdjReportingCategoryStagedData
--					spAppImportTrendUploadActuarialAdjBenefitCategoryStagedData
--					SCT_spUpdAddAdjsRevClaims
--					SCT_spMAClaimTrend
--					SCT_spMAForecast
--
-- $HISTORY
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-Sep-04      1           Initial Version                                                     Aleksandar Dimitrijevic
-- 2024-Sep-13      2           Change input param from CSV to JSON and downstream changes          Michael Manes
-- 2024-Oct-10      3           Added NOLOCK & optimazation	                                        Kumar Jalendran
-- 2024-Oct-17      4           Dropped all temp tables at end of procedure                         Kumar Jalendran
-- 2024-Oct-24      5           Added filter to ignore the validation when all fields data are empty.               Kumar Jalendran
-- 2024-Oct-25      6           Included additional Logs into dbo.TrendAdjLog and code formating.                   Kumar Jalendran
-- 2024-Nov-11		7			Replace CPS with PlanInfoID in permanent tables						Michael Manes
-- 2025-May-06      8           Updated to remove TrendAdj_HOAVBid_CalcKeyInfo reference			Kumar Jalendran
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[TrendAdj_spUploadsFromExcel]
@Table           VARCHAR(18)
,@JsonData       NVARCHAR(MAX)
,@LastUpdateByID CHAR(7)
,@Region         VARCHAR(50)
,@PlanList       VARCHAR(MAX)
AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages

        -- Declare Variables
        DECLARE @xJsonData       NVARCHAR(MAX) = @JsonData
               ,@XLastUpdateByID CHAR(7)       = @LastUpdateByID
               ,@XRegion         VARCHAR(50)   = @Region
               ,@XPlanList       VARCHAR(MAX)  = @PlanList
               ,@XTable          VARCHAR(18)   = @Table
               ,@BidYear         INT           = (SELECT    dbo.fnGetBidYear ());
        DECLARE @stringUserDefined     VARCHAR(30) = 'User-Defined'
               ,@stringMissingCPS      VARCHAR(30) = 'Missing CPS'
               ,@stringInvalidCPS      VARCHAR(30) = 'Invalid CPS'
               ,@stringValidationError VARCHAR(30) = 'ValidationError'
               ,@stringBenCat          VARCHAR(30) = 'BenCat'
               ,@stringRepCat          VARCHAR(30) = 'RepCat'
               ,@stringCHAR10          VARCHAR(30) = 'CHAR(10)'
               ,@stringUpload          VARCHAR(30) = 'Upload:'
               ,@stringReturn          VARCHAR(30) = 'Return:';


        BEGIN TRY

            BEGIN TRANSACTION transaction_TrendAdj;

            DROP TABLE IF EXISTS #ValidationErrors;
            CREATE TABLE #ValidationErrors
                (ValidationError VARCHAR(MAX));

            -- Rudimentary check for excel errors in JSON data
            IF (CHARINDEX ('#N/A', @xJsonData) > 0
                OR  CHARINDEX ('#DIV/0!', @xJsonData) > 0
                OR  CHARINDEX ('#VALUE!', @xJsonData) > 0
                OR  CHARINDEX ('#REF!', @xJsonData) > 0
                OR  CHARINDEX ('#NAME?', @xJsonData) > 0
                OR  CHARINDEX ('#NUM!', @xJsonData) > 0
                OR  CHARINDEX ('#NULL!', @xJsonData) > 0)
                BEGIN
                    INSERT INTO dbo.TrendAdjLog
                        (AdjGroupID
                        ,ProcName
                        ,Region
                        ,UserID
                        ,AuditTime
                        ,AuditMessage)
                    VALUES (0                               -- AdjGroupID - tinyint
                           ,@stringUpload + LEFT(@XTable, 13)   -- ProcName - varchar(20)
                           ,@XRegion                        -- Region - varchar(40)
                           ,@XLastUpdateByID                -- UserID - char(7)
                           ,GETDATE ()                      -- AuditTime - datetime
                           ,'Return:Excel Formula Errors');
                    COMMIT TRANSACTION;

                    SELECT  'ValidationErrorHmm, there appears to be an Excel formula error in the data. Please review the source Excel file for corrections.';
                    RETURN;
                END;

            --If PlanList is provided, use them, otherwise grab region plan list
            DROP TABLE IF EXISTS #PlanList;
            SELECT  PlanYear
                   ,CPS
				   ,PlanInfoID
            INTO    #PlanList
            FROM    dbo.vwPlanInfo
            WHERE   Region = @XRegion
                    AND PlanYear >= @BidYear - 1
                    AND IsHidden = 0
                    AND IsOffMAModel = 'No'
                    AND (@XPlanList IS NULL
                         OR CPS IN (SELECT  Val AS CPS FROM dbo.Trend_fnCalcStringToTable (@XPlanList, ',', 1) ));

            -- Copying Saved Selection for the selected Region
            IF @XTable = 'SavedSelections'

                BEGIN

                    -- Clearing prior selections for the selected Region
                    DELETE      ss
                    FROM        dbo.TrendAdj_Controls_SavedSelections ss WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.PlanInfoID = ss.PlanInfoID
                           AND vw.PlanYear=@BidYear
                    WHERE       vw.Region = @XRegion;

                    -- Copying the data into the staging table
                    SELECT  c1 AS CPS
                           ,c2 AS SAR
                           ,c7 AS OSNP
                    INTO    #SavedSelections
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 CHAR (13)
                                 ,c2 CHAR (1)
                                 ,c7 CHAR (1));

                    -- Copying the data into the saved selections table for the SAR
                    INSERT INTO dbo.TrendAdj_Controls_SavedSelections
                        (PlanYearID
                        ,PlanInfoID
                        ,AdjTypeID
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT      @BidYear
                               ,vw.PlanInfoID
                               ,1
                               ,@XLastUpdateByID
                               ,GETDATE ()
                    FROM        #SavedSelections tt WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = tt.CPS
                    WHERE       vw.Region = @Region
                                AND tt.SAR = 'Y'
                                AND vw.PlanYear = @BidYear;

                    -- Copying the data into the saved selections for the OSNP
                    INSERT INTO dbo.TrendAdj_Controls_SavedSelections
                        (PlanYearID
                        ,PlanInfoID
                        ,AdjTypeID
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT      @BidYear
                               ,vw.PlanInfoID
                               ,2
                               ,@XLastUpdateByID
                               ,GETDATE ()
                    FROM        #SavedSelections tt WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = tt.CPS
                    WHERE       vw.Region = @Region
                                AND tt.OSNP = 'Y'
                                AND vw.PlanYear = @BidYear;

                END;

            -- Copying SAR Difference Threshold for the selected Region
            IF @XTable = 'SAR_DiffThreshold'

                BEGIN
                    -- Copying the data into the staging table
                    SELECT  c1 AS DiffThreshold
                    INTO    #SAR_DiffThreshold
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 NVARCHAR (5));

                    -- Copying the data into the staging table
                    UPDATE  dbo.TrendAdj_SAR_LkpDiffThreshold
                    SET     DiffThreshold = CONVERT (DECIMAL(5, 4), tt.DiffThreshold)
                           ,LastUpdateByID = @XLastUpdateByID
                           ,LastUpdateDateTime = GETDATE ()
                    FROM    #SAR_DiffThreshold tt WITH (NOLOCK)
                    WHERE   Region = @XRegion;

                END;

            -- Copying SAR Methodology for the selected Region
            IF @XTable = 'SAR_LkpMethodology'

                BEGIN

                    -- Copying the data into the staging table
                    SELECT  c1 AS CPS
                           ,c2 AS DescriptionofSAR
                           ,c3 AS DampeningMethod_B
                           ,c4 AS Dampening_B
                           ,c5 AS CommentDampening_B
                           ,c6 AS CostUseSplit_B
                           ,c7 AS CostProportion_B
                           ,c8 AS CommentProportion_B
                           ,c9 AS DampeningMethod_C
                           ,c10 AS Dampening_C
                           ,c11 AS CommentDampening_C
                           ,c12 AS CostUseSplit_C
                           ,c13 AS CostProportion_C
                           ,c14 AS CommentProportion_C
                    INTO    #SAR_LkpMethodology
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 CHAR (13)
                                 ,c2 VARCHAR (255)
                                 ,c3 VARCHAR (30)
                                 ,c4 VARCHAR (5)
                                 ,c5 VARCHAR (255)
                                 ,c6 VARCHAR (21)
                                 ,c7 VARCHAR (5)
                                 ,c8 VARCHAR (255)
                                 ,c9 VARCHAR (30)
                                 ,c10 VARCHAR (5)
                                 ,c11 VARCHAR (255)
                                 ,c12 VARCHAR (21)
                                 ,c13 VARCHAR (5)
                                 ,c14 VARCHAR (255));

                    --Validation - check for empty or null plan before innerjoining plan list		
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT  CASE WHEN CPS IS NULL OR TRIM (sar.CPS) = '' THEN @stringMissingCPS ELSE NULL END AS ValidationError
                    FROM    #SAR_LkpMethodology sar WITH (NOLOCK)
                    WHERE   (sar.CPS IS NULL
                             OR TRIM (sar.CPS) = '')
                            AND TRIM (
                                sar.CPS + DescriptionofSAR + DampeningMethod_B + Dampening_B + CommentDampening_B
                                + CostUseSplit_B + CostProportion_B + CommentProportion_B + DampeningMethod_C
                                + Dampening_C + CommentDampening_C + CostUseSplit_C + CostProportion_C
                                + CommentProportion_C) <> '';
                    --Validations	
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT      CASE WHEN sar.CPS IS NULL
                                          OR TRIM (sar.CPS) = '' THEN @stringMissingCPS
                                     WHEN DescriptionofSAR IS NULL
                                          OR DescriptionofSAR = '' THEN 'Missing Description'
                                     WHEN DampeningMethod_B IS NULL
                                          OR DampeningMethod_B = '' THEN 'Missing Base DampeningMethod'
                                     WHEN DampeningMethod_C IS NULL
                                          OR DampeningMethod_C = '' THEN 'Missing Current DampeningMethod'
                                     WHEN CostUseSplit_B IS NULL
                                          OR CostUseSplit_B = '' THEN 'Missing Base CostUseSplit'
                                     WHEN CostUseSplit_C IS NULL
                                          OR CostUseSplit_C = '' THEN 'Missing Current CostUseSplit'
                                     WHEN TRIM (DampeningMethod_B) = @stringUserDefined
                                          AND   (Dampening_B IS NULL
                                                 OR Dampening_B = '') THEN 'Missing Base UserDefined Dampening'
                                     WHEN TRIM (DampeningMethod_B) = @stringUserDefined
                                          AND   (CommentDampening_B IS NULL
                                                 OR CommentDampening_B = '') THEN
                                         'Missing Base UserDefined Dampening Comment'
                                     WHEN TRIM (DampeningMethod_C) = @stringUserDefined
                                          AND   (Dampening_C IS NULL
                                                 OR Dampening_C = '') THEN 'Missing Current UserDefined Dampening'
                                     WHEN TRIM (DampeningMethod_C) = @stringUserDefined
                                          AND   (CommentDampening_C IS NULL
                                                 OR CommentDampening_C = '') THEN
                                         'Missing Current UserDefined Dampening Comment'
                                     WHEN TRIM (CostUseSplit_B) = @stringUserDefined
                                          AND   (CostProportion_B IS NULL
                                                 OR CostProportion_B = '') THEN 'Missing Base UserDefined CU Adj'
                                     WHEN TRIM (CostUseSplit_B) = @stringUserDefined
                                          AND   (CommentProportion_B IS NULL
                                                 OR CommentProportion_B = '') THEN
                                         'Missing Base UserDefined CU Adj Comment'
                                     WHEN TRIM (CostUseSplit_C) = @stringUserDefined
                                          AND   (CostProportion_C IS NULL
                                                 OR CostProportion_C = '') THEN 'Missing Current UserDefined CU Adj'
                                     WHEN TRIM (CostUseSplit_C) = @stringUserDefined
                                          AND   (CommentProportion_C IS NULL
                                                 OR CommentProportion_C = '') THEN
                                         'Missing Current UserDefined CU Adj Comment'
                                     WHEN pl.CPS IS NULL THEN @stringInvalidCPS
                                     ELSE NULL END AS ValidationError
                    FROM        #SAR_LkpMethodology sar WITH (NOLOCK)
                    LEFT JOIN   #PlanList pl WITH (NOLOCK)
                           ON sar.CPS = pl.CPS
                              AND   pl.PlanYear = @BidYear
                    WHERE       (pl.CPS IS NULL
                                 OR (sar.CPS IS NULL
                                     OR TRIM (sar.CPS) = '')
                                 OR (DescriptionofSAR IS NULL
                                     OR DescriptionofSAR = '')
                                 OR (DampeningMethod_B IS NULL
                                     OR DampeningMethod_B = '')
                                 OR (DampeningMethod_C IS NULL
                                     OR DampeningMethod_C = '')
                                 OR (CostUseSplit_B IS NULL
                                     OR CostUseSplit_B = '')
                                 OR (CostUseSplit_C IS NULL
                                     OR CostUseSplit_C = '')
                                 OR (TRIM (DampeningMethod_B) = @stringUserDefined
                                     AND (Dampening_B IS NULL
                                          OR Dampening_B = ''))
                                 OR (TRIM (DampeningMethod_B) = @stringUserDefined
                                     AND (CommentDampening_B IS NULL
                                          OR CommentDampening_B = ''))
                                 OR (TRIM (DampeningMethod_C) = @stringUserDefined
                                     AND (Dampening_C IS NULL
                                          OR Dampening_C = ''))
                                 OR (TRIM (DampeningMethod_C) = @stringUserDefined
                                     AND (CommentDampening_C IS NULL
                                          OR CommentDampening_C = ''))
                                 OR (TRIM (CostUseSplit_B) = @stringUserDefined
                                     AND (CostProportion_B IS NULL
                                          OR CostProportion_B = ''))
                                 OR (TRIM (CostUseSplit_B) = @stringUserDefined
                                     AND (CommentProportion_B IS NULL
                                          OR CommentProportion_B = ''))
                                 OR (TRIM (CostUseSplit_C) = @stringUserDefined
                                     AND (CostProportion_C IS NULL
                                          OR CostProportion_C = ''))
                                 OR (TRIM (CostUseSplit_C) = @stringUserDefined
                                     AND (CommentProportion_C IS NULL
                                          OR CommentProportion_C = '')))
                                AND TRIM (
                                    sar.CPS + DescriptionofSAR + DampeningMethod_B + Dampening_B + CommentDampening_B
                                    + CostUseSplit_B + CostProportion_B + CommentProportion_B + DampeningMethod_C
                                    + Dampening_C + CommentDampening_C + CostUseSplit_C + CostProportion_C
                                    + CommentProportion_C) <> '';

                    IF EXISTS (SELECT   1 FROM  #ValidationErrors)
                        BEGIN
                            INSERT INTO dbo.TrendAdjLog
                                (AdjGroupID
                                ,ProcName
                                ,Region
                                ,UserID
                                ,AuditTime
                                ,AuditMessage)
                            SELECT  0                               -- AdjGroupID - tinyint
                                   ,@stringUpload + LEFT(@XTable, 13)   -- ProcName - varchar(20)
                                   ,@XRegion                        -- Region - varchar(40)
                                   ,@XLastUpdateByID                -- UserID - char(7)
                                   ,GETDATE ()                      -- AuditTime - datetime
                                   ,CONCAT (@stringReturn, ValidationError)
                            FROM    #ValidationErrors;
                            COMMIT TRANSACTION;

                            SELECT  CONCAT (@stringValidationError, ValidationError)
                            FROM    #ValidationErrors;
                            RETURN;
                        END;

                    -- Clearing prior SAR Methodology inputs for the selected Region
                    DELETE      lm
                    FROM        dbo.TrendAdj_SAR_LkpMethodology lm
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.PlanInfoID = lm.PlanInfoID
                    WHERE       vw.Region = @XRegion;

                    -- Copying the data into the SAR Methodology table for the base year
                    INSERT INTO dbo.TrendAdj_SAR_LkpMethodology
                        (PlanInfoID
                        ,PlanYearID
                        ,DescriptionofSAR
                        ,DampeningMethod
                        ,Dampening
                        ,CommentDampening
                        ,CostUseSplit
                        ,CostProportion
                        ,CommentProportion
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT      vw.PlanInfoID
                               ,@BidYear - 2
                               ,DescriptionofSAR
                               ,DampeningMethod_B
                               ,CASE WHEN Dampening_B = '' THEN 0 ELSE CAST(Dampening_B AS DECIMAL(5, 4))END
                               ,CommentDampening_B
                               ,CostUseSplit_B
                               ,CASE WHEN CostProportion_B = '' THEN 0 ELSE CAST(CostProportion_B AS DECIMAL(5, 4))END
                               ,CommentProportion_B
                               ,@XLastUpdateByID
                               ,GETDATE ()
                    FROM        #SAR_LkpMethodology tt WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = tt.CPS
						   AND vw.PlanYear = @BidYear
                    WHERE       vw.Region = @XRegion;

                    -- Copying the data into the SAR Methodology table for the current year
                    INSERT INTO dbo.TrendAdj_SAR_LkpMethodology
                        (PlanInfoID
                        ,PlanYearID
                        ,DescriptionofSAR
                        ,DampeningMethod
                        ,Dampening
                        ,CommentDampening
                        ,CostUseSplit
                        ,CostProportion
                        ,CommentProportion
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT      vw.PlanInfoID
                               ,@BidYear - 1
                               ,DescriptionofSAR
                               ,DampeningMethod_C
                               ,CASE WHEN Dampening_C = '' THEN 0 ELSE CAST(Dampening_C AS DECIMAL(5, 4))END
                               ,CommentDampening_C
                               ,CostUseSplit_C
                               ,CASE WHEN CostProportion_C = '' THEN 0 ELSE CAST(CostProportion_C AS DECIMAL(5, 4))END
                               ,CommentProportion_C
                               ,@XLastUpdateByID
                               ,GETDATE ()
                    FROM        #SAR_LkpMethodology tt WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = tt.CPS
						   AND vw.PlanYear=@BidYear
                    WHERE       vw.Region = @XRegion;

                END;

            -- Copying SNP Lookalike plans for the selected Region
            IF @XTable = 'OSNP_LkpSNPType'

                BEGIN

                    -- Clearing prior OSNP lookalike inputs for the selected Region
                    DELETE      lkp
                    FROM        dbo.TrendAdj_OSNP_LkpSNPType lkp
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.PlanInfoID = lkp.PlanInfoID
                              AND   vw.PlanYear = @BidYear
                    WHERE       vw.Region = @XRegion;

                    -- Copying the data into the staging table
                    SELECT  c1 AS CPS
                           ,c2 AS SNPTypeDetail
                    INTO    #OSNP_LkpSNPType
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 CHAR (13)
                                 ,c2 VARCHAR (50));

                    -- Copying the data into the OSNP table with SNP lookalike planas for the OSNP
                    INSERT INTO dbo.TrendAdj_OSNP_LkpSNPType
                        (PlanInfoID
                        ,PlanYearID
                        ,SNPTypeDetail
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT      vw.PlanInfoID
                               ,@BidYear
                               ,tt.SNPTypeDetail
                               ,@XLastUpdateByID
                               ,GETDATE ()
                    FROM        #OSNP_LkpSNPType tt WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = tt.CPS
                           AND vw.PlanYear=@BidYear 
                    WHERE       vw.Region = @XRegion;

                END;

            -- Copying Compiler Data for the selected Region
            IF @XTable = 'Compiler'

                BEGIN

                    -- Copying the data into the staging table
                    SELECT  c1 AS SessionID
                           ,c2 AS Granularity
                           ,c3 AS AdjustmentDescription
                           ,c4 AS CPS
                           ,c5 AS PlanYearID
                           ,c6 AS TrendYearID
                           ,c7 AS RateType
                           ,c8 AS ReportingCategory
                           ,c9 AS BenefitCategoryID
                           ,c10 AS CostAdjustment
                           ,c11 AS UseAdjustment
                    INTO    #Compiler
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 VARCHAR (19)
                                 ,c2 CHAR (6)
                                 ,c3 VARCHAR (100)
                                 ,c4 CHAR (13)
                                 ,c5 SMALLINT
                                 ,c6 SMALLINT
                                 ,c7 SMALLINT
                                 ,c8 VARCHAR (50)
                                 ,c9 NVARCHAR (10)
                                 ,c10 NVARCHAR (18)
                                 ,c11 NVARCHAR (18));
                    --Validation - check for empty or null plan before innerjoining plan list		
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT  CASE WHEN adj.CPS IS NULL OR TRIM (adj.CPS) = '' THEN @stringMissingCPS ELSE NULL END AS ValidationError
                    FROM    #Compiler adj WITH (NOLOCK)
                    WHERE   adj.CPS IS NULL
                            OR  TRIM (adj.CPS) = '';
                    --Validations
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT      CASE WHEN adj.CPS IS NULL
                                          OR TRIM (adj.CPS) = '' THEN @stringMissingCPS
                                     WHEN Granularity = @stringBenCat
                                          AND   (adj.BenefitCategoryID IS NULL
                                                 OR adj.BenefitCategoryID = '') THEN 'Missing BenCatID'
                                     WHEN Granularity = @stringRepCat
                                          AND   (adj.ReportingCategory IS NULL
                                                 OR adj.ReportingCategory = '') THEN 'Missing Reporting Category'
                                     WHEN adj.TrendYearID IS NULL
                                          OR adj.TrendYearID = '' THEN 'Missing Trend Year'
                                     WHEN adj.RateType IS NULL
                                          OR adj.RateType = '' THEN 'Missing Rate Type'
                                     WHEN spi.CPS IS NULL THEN @stringInvalidCPS
                                     WHEN Granularity = @stringBenCat
                                          AND   libc.BenefitCategoryID IS NULL THEN 'Invalid BenCatID'
                                     WHEN Granularity = @stringRepCat
                                          AND   lirc.ReportingCategory IS NULL THEN 'Invalid Reporting Category'
                                     WHEN adj.TrendYearID < @BidYear - 1
                                          OR adj.TrendYearID > @BidYear THEN 'Invalid Trend Year'
                                     WHEN NOT (adj.RateType = 1
                                               OR   adj.RateType = 2) THEN 'Invalid Rate Type'
                                     WHEN EXISTS ( --check for compiled adj matches granularity of SQL calculated adjustments
                                                 SELECT 1
                                                 FROM   dbo.TrendAdj_CalcPlanAdjmt cp
                                                 WHERE  cp.SessionID = adj.SessionID
                                                        AND cp.AdjustmentDescription = adj.AdjustmentDescription
                                                        AND cp.CPS = adj.CPS
                                                        AND cp.Granularity = adj.Granularity
                                                        AND cp.TrendYearID = adj.TrendYearID
                                                        AND cp.ReportingCategory = adj.ReportingCategory
                                                        AND cp.BenefitCategoryID = adj.BenefitCategoryID) THEN
                                         'Duplicate Adjustments between Compiled and SQL data found'
                                     ELSE NULL END AS ValidationError
                    FROM        #Compiler adj WITH (NOLOCK)
                    LEFT JOIN   dbo.SavedPlanInfo spi WITH (NOLOCK)
                           ON adj.CPS = spi.CPS
                              AND   spi.PlanYear = @BidYear
                    LEFT JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                           ON adj.BenefitCategoryID = libc.BenefitCategoryID
                    LEFT JOIN   dbo.LkpIntBenefitCategory lirc WITH (NOLOCK)
                           ON adj.ReportingCategory = lirc.ReportingCategory
                    WHERE       adj.CPS IS NULL
                                OR  TRIM (adj.CPS) = ''
                                OR  (Granularity = @stringBenCat
                                     AND (adj.BenefitCategoryID IS NULL
                                          OR adj.BenefitCategoryID = ''))
                                OR  (Granularity = @stringRepCat
                                     AND (adj.ReportingCategory IS NULL
                                          OR adj.ReportingCategory = ''))
                                OR  adj.TrendYearID IS NULL
                                OR  adj.TrendYearID = ''
                                OR  adj.RateType IS NULL
                                OR  adj.RateType = ''
                                OR  spi.CPS IS NULL
                                OR  (Granularity = @stringBenCat
                                     AND libc.BenefitCategoryID IS NULL)
                                OR  (Granularity = @stringRepCat
                                     AND lirc.ReportingCategory IS NULL)
                                OR  adj.TrendYearID < @BidYear - 1
                                OR  adj.TrendYearID > @BidYear
                                OR  NOT (adj.RateType = 1
                                         OR adj.RateType = 2);

                    IF EXISTS (SELECT   1 FROM  #ValidationErrors)
                        BEGIN
                            INSERT INTO dbo.TrendAdjLog
                                (AdjGroupID
                                ,ProcName
                                ,Region
                                ,UserID
                                ,AuditTime
                                ,AuditMessage)
                            SELECT  0                               -- AdjGroupID - tinyint
                                   ,@stringUpload + LEFT(@XTable, 13)   -- ProcName - varchar(20)
                                   ,@XRegion                        -- Region - varchar(40)
                                   ,@XLastUpdateByID                -- UserID - char(7)
                                   ,GETDATE ()                      -- AuditTime - datetime
                                   ,CONCAT (@stringReturn, ValidationError)
                            FROM    #ValidationErrors;
                            COMMIT TRANSACTION;

                            SELECT  CONCAT (@stringValidationError, ValidationError)
                            FROM    #ValidationErrors;
                            RETURN;
                        END;

                    -- Copying the compiler data into the TrendAdj_CalcPlanAdjmt
                    INSERT INTO dbo.TrendAdj_CalcPlanAdjmt
                        (SessionID
                        ,Granularity
                        ,AdjustmentDescription
                        ,CPS
                        ,PlanInfoID
                        ,TrendYearID
                        ,RateType
                        ,ReportingCategory
                        ,BenefitCategoryID
                        ,CostAdjustment
                        ,UseAdjustment)
                    SELECT      tt.SessionID
                               ,tt.Granularity
                               ,tt.AdjustmentDescription
                               ,tt.CPS
                               ,vw.PlanInfoID
                               ,tt.TrendYearID
                               ,tt.RateType
                               ,CASE WHEN tt.ReportingCategory = '' THEN NULL ELSE tt.ReportingCategory END
                               ,CONVERT (INT, CASE WHEN tt.BenefitCategoryID = '' THEN NULL ELSE tt.BenefitCategoryID END)
                               ,CONVERT (DECIMAL(18, 8), tt.CostAdjustment)
                               ,CONVERT (DECIMAL(18, 8), tt.UseAdjustment)
                    FROM        #Compiler tt WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = tt.CPS
                              AND   vw.PlanYear = @BidYear
                    WHERE       vw.Region = @XRegion;

                END;

            -- Copying SAR Key Info Data for the selected Region
            IF @XTable = 'SAR_KeyInfo'

                BEGIN

                    -- Deleting prior records
                    DELETE      sar
                    FROM        dbo.TrendAdj_SAR_CalcKeyInfo sar
                   INNER JOIN   #PlanList pl WITH (NOLOCK)
                           ON pl.PlanInfoID = sar.PlanInfoID
                    WHERE       pl.PlanYear = @BidYear;


                    -- Copying the data into the staging table
                    SELECT  c1 AS CPS
                           ,c2 AS DescriptionofSAR
                           ,c3 AS PlanYearID
                           ,c4 AS IsOOA
                           ,c5 AS MemberMonths
                           ,c6 AS Allowed
                    INTO    #SAR_KeyInfo
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 CHAR (13)
                                 ,c2 VARCHAR (100)
                                 ,c3 SMALLINT
                                 ,c4 BIT
                                 ,c5 NVARCHAR (38)
                                 ,c6 NVARCHAR (20));

                    -- Copying the key info data into the TrendAdj_SAR_CalcKeyInfo
                    INSERT INTO dbo.TrendAdj_SAR_CalcKeyInfo
                        (PlanInfoID
                        ,DescriptionofSAR
                        ,PlanYearID
                        ,IsOOA
                        ,MemberMonths
                        ,Allowed
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT      pl.PlanInfoID
                               ,tt.DescriptionofSAR
                               ,tt.PlanYearID
                               ,tt.IsOOA
                               ,CONVERT (DECIMAL(38, 8), tt.MemberMonths)
                               ,CONVERT (DECIMAL(20, 12), tt.Allowed)
                               ,@LastUpdateByID
                               ,GETDATE ()
                    FROM        #SAR_KeyInfo tt WITH (NOLOCK)
                   INNER JOIN   #PlanList pl WITH (NOLOCK)
                           ON pl.CPS = tt.CPS
                    WHERE       TRIM (tt.CPS) <> ''
							AND pl.PlanYear=@BidYear;

                END;

            -- Copying OSNP Key Info Data for the selected Region
            IF @XTable = 'OSNP_KeyInfo'

                BEGIN

                    -- Deleting prior records
                    DELETE      snp
                    FROM        dbo.TrendAdj_OSNP_CalcKeyInfo snp
                   INNER JOIN   #PlanList pl WITH (NOLOCK)
                           ON pl.PlanInfoID = snp.PlanInfoID
                    WHERE       pl.PlanYear = @BidYear;


                    -- Copying the data into the staging table
                    SELECT  c1 AS CPS
                           ,c2 AS Method
                           ,c3 AS PlanYearID
                           ,c4 AS Relativity
                    INTO    #OSNP_KeyInfo
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 CHAR (13)
                                 ,c2 VARCHAR (50)
                                 ,c3 SMALLINT
                                 ,c4 NVARCHAR (13));

                    -- Copying the key info data into the TrendAdj_OSNP_CalcKeyInfo
                    INSERT INTO dbo.TrendAdj_OSNP_CalcKeyInfo
                        (PlanInfoID
                        ,Method
                        ,PlanYearID
                        ,Relativity
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT      pl.PlanInfoID
                               ,tt.Method
                               ,tt.PlanYearID
                               ,CONVERT (DECIMAL(13, 12), tt.Relativity)
                               ,@LastUpdateByID
                               ,GETDATE ()
                    FROM        #OSNP_KeyInfo tt WITH (NOLOCK)
                   INNER JOIN   #PlanList pl WITH (NOLOCK)
                           ON pl.CPS = tt.CPS
                    WHERE       TRIM (tt.CPS) <> ''
							AND pl.PlanYear=@BidYear;

                END;

            -- Copying final adjustments for the selected Region
            IF LEFT(@XTable, 7) = 'Act_Adj'

                BEGIN


                    -- Copying the data into the staging table
                    DROP TABLE IF EXISTS #act_adj;
                    SELECT  c1 AS AdjustmentID
                           ,c2 AS Granularity
                           ,c3 AS AdjustmentDescription
                           ,c4 AS CPS
                           ,c5 AS TrendYearID
                           ,c6 AS RateType
                           ,c7 AS ReportingCategory
                           ,c8 AS BenCatID
                           ,c9 AS CostAdj
                           ,c10 AS UseAdj
                    INTO    #Act_Adj
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 INT
                                 ,c2 CHAR (6)
                                 ,c3 VARCHAR (100)
                                 ,c4 CHAR (13)
                                 ,c5 SMALLINT
                                 ,c6 SMALLINT
                                 ,c7 VARCHAR (50)
                                 ,c8 INT
                                 ,c9 NVARCHAR (18)
                                 ,c10 NVARCHAR (18));
                    --Validation - check for empty or null plan before innerjoining plan list		
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT  CASE WHEN adj.CPS IS NULL OR TRIM (adj.CPS) = '' THEN @stringMissingCPS ELSE NULL END AS ValidationError
                    FROM    #Act_Adj adj WITH (NOLOCK)
                    WHERE   adj.CPS IS NULL
                            OR  TRIM (adj.CPS) = '';
                    --Validations
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT      CASE WHEN adj.CPS IS NULL
                                          OR TRIM (adj.CPS) = '' THEN @stringMissingCPS
                                     WHEN Granularity = @stringBenCat
                                          AND   (adj.BenCatID IS NULL
                                                 OR adj.BenCatID = '') THEN 'Missing BenCatID'
                                     WHEN Granularity = @stringRepCat
                                          AND   (adj.ReportingCategory IS NULL
                                                 OR adj.ReportingCategory = '') THEN 'Missing Reporting Category'
                                     WHEN adj.TrendYearID IS NULL
                                          OR adj.TrendYearID = '' THEN 'Missing Trend Year'
                                     WHEN adj.RateType IS NULL
                                          OR adj.RateType = '' THEN 'Missing Rate Type'
                                     WHEN spi.CPS IS NULL THEN @stringInvalidCPS
                                     WHEN Granularity = @stringBenCat
                                          AND   libc.BenefitCategoryID IS NULL THEN 'Invalid BenCatID'
                                     WHEN Granularity = @stringRepCat
                                          AND   lirc.ReportingCategory IS NULL THEN 'Invalid Reporting Category'
                                     WHEN adj.TrendYearID < @BidYear - 1
                                          OR adj.TrendYearID > @BidYear THEN 'Invalid Trend Year'
                                     WHEN NOT (adj.RateType = 1
                                               OR   adj.RateType = 2) THEN 'Invalid Rate Type'
                                     ELSE NULL END AS ValidationError
                    FROM        #Act_Adj adj WITH (NOLOCK)
                   INNER JOIN   #PlanList pl WITH (NOLOCK)
                           ON pl.CPS = adj.CPS
                    LEFT JOIN   dbo.SavedPlanInfo spi WITH (NOLOCK)
                           ON adj.CPS = spi.CPS
                              AND   spi.PlanYear = @BidYear
                    LEFT JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                           ON adj.bencatID = libc.BenefitCategoryID
                    LEFT JOIN   dbo.LkpIntBenefitCategory lirc WITH (NOLOCK)
                           ON adj.ReportingCategory = lirc.ReportingCategory
                    WHERE       adj.CPS IS NULL
                                OR  TRIM (adj.CPS) = ''
                                OR  (Granularity = @stringBenCat
                                     AND (adj.BenCatID IS NULL
                                          OR adj.BenCatID = ''))
                                OR  (Granularity = @stringRepCat
                                     AND (adj.ReportingCategory IS NULL
                                          OR adj.ReportingCategory = ''))
                                OR  adj.TrendYearID IS NULL
                                OR  adj.TrendYearID = ''
                                OR  adj.RateType IS NULL
                                OR  adj.RateType = ''
                                OR  spi.CPS IS NULL
                                OR  (Granularity = @stringBenCat
                                     AND libc.BenefitCategoryID IS NULL)
                                OR  (Granularity = @stringRepCat
                                     AND lirc.ReportingCategory IS NULL)
                                OR  adj.TrendYearID < @BidYear - 1
                                OR  adj.TrendYearID > @BidYear
                                OR  NOT (adj.RateType = 1
                                         OR adj.RateType = 2);

                    IF EXISTS (SELECT   1 FROM  #ValidationErrors)
                        BEGIN
                            INSERT INTO dbo.TrendAdjLog
                                (AdjGroupID
                                ,ProcName
                                ,Region
                                ,UserID
                                ,AuditTime
                                ,AuditMessage)
                            SELECT  0                   -- AdjGroupID - tinyint
                                   ,'Upload:MAAUI'      -- ProcName - varchar(20)
                                   ,@XRegion            -- Region - varchar(40)
                                   ,@XLastUpdateByID    -- UserID - char(7)
                                   ,GETDATE ()          -- AuditTime - datetime
                                   ,CONCAT (@stringReturn, ValidationError)
                            FROM    #ValidationErrors;
                            COMMIT TRANSACTION;

                            SELECT  CONCAT (@stringValidationError, ValidationError)
                            FROM    #ValidationErrors;
                            RETURN;
                        END;

                    -- Copying final adjustment data for Reporing Category granularity
                    DROP TABLE IF EXISTS #CalcPlanAdjmt_RepCat;

                    CREATE TABLE #CalcPlanAdjmt_RepCat
                        (AdjustmentID          INT
                        ,AdjustmentDescription VARCHAR(100)  NOT NULL
                        ,CPS                   CHAR(13)      NOT NULL
                        ,PlanYearID            INT
                        ,TrendYearID           INT
                        ,RateType              SMALLINT
                        ,ReportingCategory     VARCHAR(50)
                        ,CostAdjustment        DECIMAL(18, 8)
                        ,UseAdjustment         DECIMAL(18, 8));

                    INSERT INTO #CalcPlanAdjmt_RepCat
                        (AdjustmentID
                        ,AdjustmentDescription
                        ,CPS
                        ,PlanYearID
                        ,TrendYearID
                        ,RateType
                        ,ReportingCategory
                        ,CostAdjustment
                        ,UseAdjustment)
                    SELECT      tt.AdjustmentID
                               ,tt.AdjustmentDescription
                               ,tt.CPS
                               ,@BidYear
                               ,tt.TrendYearID
                               ,tt.RateType
                               ,tt.ReportingCategory
                               ,CONVERT (DECIMAL(18, 8), tt.CostAdj) AS CostAdj
                               ,CONVERT (DECIMAL(18, 8), tt.UseAdj) AS UseAdj
                    FROM        #Act_Adj tt WITH (NOLOCK)
                   INNER JOIN   #PlanList pl WITH (NOLOCK)
                           ON pl.CPS = tt.CPS
                              AND   pl.PlanYear = @BidYear
                    WHERE       tt.Granularity = @stringRepCat;

                    -- Copying final adjustment data for Benefit Category granularity
                    DROP TABLE IF EXISTS #CalcPlanAdjmt_BenCat;

                    CREATE TABLE #CalcPlanAdjmt_BenCat
                        (AdjustmentID          INT
                        ,AdjustmentDescription VARCHAR(100)  NOT NULL
                        ,CPS                   CHAR(13)      NOT NULL
                        ,PlanYearID            INT
                        ,TrendYearID           INT
                        ,RateType              SMALLINT
                        ,BenefitCategoryID     INT
                        ,CostAdjustment        DECIMAL(18, 8)
                        ,UseAdjustment         DECIMAL(18, 8));
                    INSERT INTO #CalcPlanAdjmt_BenCat
                        (AdjustmentID
                        ,AdjustmentDescription
                        ,CPS
                        ,PlanYearID
                        ,TrendYearID
                        ,RateType
                        ,BenefitCategoryID
                        ,CostAdjustment
                        ,UseAdjustment)
                    SELECT      tt.AdjustmentID
                               ,tt.AdjustmentDescription
                               ,tt.CPS
                               ,@BidYear
                               ,tt.TrendYearID
                               ,tt.RateType
                               ,tt.BenCatID
                               ,CONVERT (DECIMAL(18, 8), tt.CostAdj) AS CostAdj
                               ,CONVERT (DECIMAL(18, 8), tt.UseAdj) AS UseAdj
                    --INTO #CalcPlanAdjmt_BenCat
                    FROM        #Act_Adj tt WITH (NOLOCK)
                   INNER JOIN   #PlanList pl WITH (NOLOCK)
                           ON pl.CPS = tt.CPS
                              AND   pl.PlanYear = @BidYear
                    WHERE       tt.Granularity = @stringBenCat;



                    DECLARE @HasRepCat BIT = (SELECT    COUNT (*) FROM #CalcPlanAdjmt_RepCat)
                           ,@HasBenCat BIT = (SELECT    COUNT (*) FROM #CalcPlanAdjmt_BenCat);

                    DECLARE @timestamp AS DATETIME = GETDATE ();
                    DECLARE @StageIDRep AS VARCHAR(40) = CONCAT (@XLastUpdateByID, 'UTATRepCat', @timestamp);
                    DECLARE @StageIDBen AS VARCHAR(40) = CONCAT (@XLastUpdateByID, 'UTATBenCat', @timestamp);
                    DECLARE @isConfirm BIT = RIGHT(@XTable, 1);

                    DECLARE @MessageFromBackendRep VARCHAR(MAX);
                    DECLARE @MessageFromBackendBen VARCHAR(MAX);

                    DECLARE @returnmessage VARCHAR(MAX) = 'Reporting Category Upload:' + @stringCHAR10;

                    IF @HasRepCat > 0
                        BEGIN
                            INSERT INTO dbo.ImportDataStaging
                                (StageId
                                ,UserId
                                ,JsonData)
                            SELECT  @StageIDRep AS StageId
                                   ,@XLastUpdateByID AS UserId
                                   ,(SELECT *
                                     FROM   #CalcPlanAdjmt_RepCat WITH (NOLOCK)
                                    FOR JSON AUTO, ROOT('ActAdjRepCat')) AS JsonData;

                            EXEC dbo.spAppImportTrendUploadActuarialAdjReportingCategoryStagedData @StageIDRep
                                                                                                      ,@isConfirm
                                                                                                      ,@MessageFromBackend = @MessageFromBackendRep OUTPUT;
                            SET @MessageFromBackendRep = REPLACE (
                                                         @MessageFromBackendRep, '.Y', '.' + @stringCHAR10 + 'Y');

                        END;

                    SET @returnmessage = @returnmessage + COALESCE (@MessageFromBackendRep, 'EMPTY') + @stringCHAR10
                                         + 'Benefit Category Upload:' + @stringCHAR10;

                    IF @HasBenCat > 0
                        BEGIN
                            INSERT INTO dbo.ImportDataStaging
                                (StageId
                                ,UserId
                                ,JsonData)
                            SELECT  @StageIDBen AS StageId
                                   ,@XLastUpdateByID AS UserId
                                   ,(SELECT *
                                     FROM   #CalcPlanAdjmt_BenCat WITH (NOLOCK)
                                    FOR JSON AUTO, ROOT('ActAdjBenCat')) AS JsonData;

                            EXEC dbo.spAppImportTrendUploadActuarialAdjBenefitCategoryStagedData @StageIDBen
                                                                                                    ,@isConfirm
                                                                                                    ,@MessageFromBackend = @MessageFromBackendBen OUTPUT;

                            SET @MessageFromBackendBen = REPLACE (
                                                         @MessageFromBackendBen, '.Y', '.' + @stringCHAR10 + 'Y');

                        END;
                    SET @returnmessage = @returnmessage + COALESCE (@MessageFromBackendBen, 'EMPTY');
                    SELECT  @returnmessage;

                    IF @isConfirm = 1
                        BEGIN
                            INSERT INTO dbo.TrendAdjLog
                                (AdjGroupID
                                ,ProcName
                                ,Region
                                ,UserID
                                ,AuditTime
                                ,AuditMessage)
                            VALUES (0                   -- AdjGroupID - tinyint
                                   ,'Upload:MAAUI'      -- ProcName - varchar(20)
                                   ,@XRegion            -- Region - varchar(40)
                                   ,@XLastUpdateByID    -- UserID - char(7)
                                   ,GETDATE ()          -- AuditTime - datetime
                                   ,NULL);
                        END;
                END;

            IF @XTable = 'Archive'
                BEGIN
                    DELETE      cpa
                    FROM        dbo.TrendAdj_CalcPlanAdjmt_Prior cpa
                   INNER JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                           ON vpi.PlanInfoID = cpa.PlanInfoID
                    WHERE       vpi.Region = @XRegion;

                    DROP TABLE IF EXISTS #NewAdjForPrior;
                    SELECT      alladj.Granularity
                               ,alladj.AdjustmentDescription
                               ,alladj.PlanInfoID
                               ,alladj.CPS
                               ,alladj.PlanYearID
                               ,alladj.TrendYearID
                               ,alladj.RateType
                               ,alladj.ReportingCategory
                               ,alladj.BenefitCategoryID
                               ,alladj.CostAdjustment
                               ,alladj.UseAdjustment
                    INTO        #NewAdjForPrior
                    FROM        (SELECT @stringRepCat AS Granularity
                                       ,AdjustmentDescription
                                       ,PlanInfoID
                                       ,CPS
                                       ,PlanYearID
                                       ,TrendYearID
                                       ,RateType
                                       ,ReportingCategory
                                       ,NULL AS BenefitCategoryID
                                       ,CostAdjustment
                                       ,UseAdjustment
                                 FROM   dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat WITH (NOLOCK)
                                 UNION ALL
                                 SELECT @stringBenCat AS Granularity
                                       ,AdjustmentDescription
                                       ,PlanInfoID
                                       ,CPS
                                       ,PlanYearID
                                       ,TrendYearID
                                       ,RateType
                                       ,NULL AS ReportingCategory
                                       ,BenefitCategoryID
                                       ,CostAdjustment
                                       ,UseAdjustment
                                 FROM   dbo.Trend_ProjProcess_CalcPlanAdjmt_BenCat WITH (NOLOCK)) alladj
                   INNER JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                           ON vpi.PlanInfoID = alladj.PlanInfoID
                              AND   vpi.IsHidden = 0
                    WHERE       vpi.Region = @XRegion;

                    INSERT INTO dbo.TrendAdj_CalcPlanAdjmt_Prior
                        (Granularity
                        ,AdjustmentDescription
                        ,PlanInfoID
                        ,CPS
                        ,PlanYearID
                        ,TrendYearID
                        ,RateType
                        ,ReportingCategory
                        ,BenefitCategoryID
                        ,CostAdjustment
                        ,UseAdjustment
                        ,LastUpdateDateTime)
                    SELECT  Granularity
                           ,AdjustmentDescription
                           ,PlanInfoID
                           ,CPS
                           ,PlanYearID
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,BenefitCategoryID
                           ,CostAdjustment
                           ,UseAdjustment
                           ,GETDATE ()
                    FROM    #NewAdjForPrior;

                    INSERT INTO dbo.TrendAdjLog
                        (AdjGroupID
                        ,ProcName
                        ,Region
                        ,UserID
                        ,AuditTime
                        ,AuditMessage)
                    VALUES (0                   -- AdjGroupID - tinyint
                           ,'Archive'           -- ProcName - varchar(20)
                           ,@XRegion            -- Region - varchar(40)
                           ,@XLastUpdateByID    -- UserID - char(7)
                           ,GETDATE ()          -- AuditTime - datetime
                           ,NULL);
                END;

            IF LEFT(@XTable, 7) = 'SCT_Adj'

                BEGIN

                    DECLARE @SCTIteration CHAR(7) = (SELECT DISTINCT
                                                            Iteration
                                                     FROM   dbo.SCT_CurrentIterations WITH (NOLOCK)
                                                     WHERE  Archive = 'N');
                    DECLARE @CPSList VARCHAR(MAX);
                    DECLARE @YearList VARCHAR(MAX);

                    -- Copying the data into the staging table
                    DROP TABLE IF EXISTS #sct_adj;
                    SELECT  c1 AS PlanYearID
                           ,c2 AS ContractPBPSegment
                           ,c3 AS NonESRDHospiceRevenueAdj
                           ,c4 AS NonESRDHospiceClaimAdjRiskOffset
                           ,c5 AS NonESRDHospiceClaimAdjOther
                           ,c6 AS ESRDHospiceRevenueAdj
                           ,c7 AS ESRDHospiceClaimAdj
                           ,c8 AS QualityAdj
                           ,c9 AS AdjustmentType
                           ,c10 AS Comment
                    INTO    #sct_adj
                    FROM
                            OPENJSON (@xJsonData)
                            WITH (c1 INT
                                 ,c2 CHAR (13)
                                 ,c3 NVARCHAR (14)
                                 ,c4 NVARCHAR (14)
                                 ,c5 NVARCHAR (14)
                                 ,c6 NVARCHAR (14)
                                 ,c7 NVARCHAR (14)
                                 ,c8 NVARCHAR (14)
                                 ,c9 VARCHAR (255)
                                 ,c10 VARCHAR (255));

                    --Validation - check for empty or null plan before innerjoining plan list
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT  CASE WHEN adj.ContractPBPSegment IS NULL
                                      OR TRIM (adj.ContractPBPSegment) = '' THEN @stringMissingCPS
                                 ELSE NULL END AS ValidationError
                    FROM    #sct_adj adj WITH (NOLOCK)
                    WHERE   adj.ContractPBPSegment IS NULL
                            OR  adj.ContractPBPSegment = '';

                    --Define field formats and add additional columns
                    DROP TABLE IF EXISTS #CalcPlanAdjmt_SCT;
                    CREATE TABLE #CalcPlanAdjmt_SCT
                        (PlanYearID                       [INT]            NOT NULL
                        ,ContractPBPSegment               [CHAR](13)       NOT NULL
                        ,NonESRDHospiceRevenueAdj         [DECIMAL](14, 6) NOT NULL
                        ,NonESRDHospiceClaimAdjRiskOffset [DECIMAL](14, 6) NOT NULL
                        ,NonESRDHospiceClaimAdjOther      [DECIMAL](14, 6) NOT NULL
                        ,ESRDHospiceRevenueAdj            [DECIMAL](14, 6) NOT NULL
                        ,ESRDHospiceClaimAdj              [DECIMAL](14, 6) NOT NULL
                        ,QualityAdj                       [DECIMAL](14, 6) NOT NULL
                        ,AdjustmentType                   [VARCHAR](255)   NOT NULL
                        ,Comment                          [VARCHAR](255)   NOT NULL
                        ,Iteration                        [VARCHAR](7)     NOT NULL
                        ,LastUpdateByID                   [CHAR](13)       NOT NULL
                        ,LastUpdateDateTime               [DATETIME]       NOT NULL);
                    INSERT INTO #CalcPlanAdjmt_SCT
                    SELECT      sct.PlanYearID
                               ,sct.ContractPBPSegment
                               ,CONVERT (DECIMAL(14, 6), NonESRDHospiceRevenueAdj) AS NonESRDHospiceRevenueAdj
                               ,CONVERT (DECIMAL(14, 6), NonESRDHospiceClaimAdjRiskOffset) AS NonESRDHospiceClaimAdjRiskOffset
                               ,CONVERT (DECIMAL(14, 6), NonESRDHospiceClaimAdjOther) AS NonESRDHospiceClaimAdjOther
                               ,CONVERT (DECIMAL(14, 6), ESRDHospiceRevenueAdj) AS ESRDHospiceRevenueAdj
                               ,CONVERT (DECIMAL(14, 6), ESRDHospiceClaimAdj) AS ESRDHospiceClaimAdj
                               ,CONVERT (DECIMAL(14, 6), QualityAdj) AS QualityAdj
                               ,sct.AdjustmentType
                               ,sct.Comment
                               ,@SCTIteration AS Iteration
                               ,@XLastUpdateByID
                               ,GETDATE ()
                    FROM        #sct_adj sct WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = sct.ContractPBPSegment
                              AND   vw.PlanYear = sct.PlanYearID
                    WHERE       vw.Region = @XRegion;

                    --Validations
                    INSERT INTO #ValidationErrors
                        (ValidationError)
                    SELECT      CASE WHEN adj.ContractPBPSegment IS NULL
                                          OR TRIM (adj.ContractPBPSegment) = '' THEN @stringMissingCPS
                                     WHEN adj.Comment IS NULL
                                          OR adj.Comment = '' THEN 'Missing Comment'
                                     WHEN adj.PlanYearID IS NULL
                                          OR adj.PlanYearID = '' THEN 'Missing Plan Year'
                                     WHEN adj.AdjustmentType IS NULL
                                          OR adj.AdjustmentType = '' THEN 'Missing Adj Type'
                                     WHEN vpi.CPS IS NULL THEN @stringInvalidCPS
                                     WHEN (adj.NonESRDHospiceRevenueAdj > 99999999
                                           OR   adj.NonESRDHospiceClaimAdjRiskOffset > 99999999
                                           OR   adj.NonESRDHospiceClaimAdjOther > 99999999
                                           OR   adj.ESRDHospiceRevenueAdj > 99999999
                                           OR   adj.ESRDHospiceClaimAdj > 99999999
                                           OR   adj.QualityAdj > 99999999) THEN 'Invalid Adj - Too Large'
                                     WHEN dup_check > 1 THEN 'Duplicate adjustments'
                                     ELSE NULL END AS ValidationError
                    FROM        #CalcPlanAdjmt_SCT adj WITH (NOLOCK)
                    LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                           ON adj.ContractPBPSegment = vpi.CPS
                              AND   vpi.PlanYear = @BidYear
                              AND   vpi.Region = @XRegion
                    LEFT JOIN   (SELECT     PlanYearID
                                           ,ContractPBPSegment
                                           ,Comment
                                           ,AdjustmentType
                                           ,COUNT (*) AS dup_check
                                 FROM       #sct_adj
                                 GROUP BY   planyearid
                                           ,contractpbpsegment
                                           ,comment
                                           ,adjustmenttype) dupe
                           ON adj.ContractPBPSegment = dupe.ContractPBPSegment
                              AND   adj.PlanYearID = dupe.PlanYearID
                              AND   adj.Comment = dupe.Comment
                              AND   adj.AdjustmentType = dupe.AdjustmentType
                    WHERE       adj.ContractPBPSegment IS NULL
                                OR  TRIM (adj.ContractPBPSegment) = ''
                                OR  adj.Comment IS NULL
                                OR  adj.Comment = ''
                                OR  adj.PlanYearID IS NULL
                                OR  adj.PlanYearID = ''
                                OR  adj.AdjustmentType IS NULL
                                OR  adj.AdjustmentType = ''
                                OR  vpi.CPS IS NULL
                                OR  dupe.dup_check > 1
                                OR  (adj.NonESRDHospiceRevenueAdj > 99999999
                                     OR adj.NonESRDHospiceClaimAdjRiskOffset > 99999999
                                     OR adj.NonESRDHospiceClaimAdjOther > 99999999
                                     OR adj.ESRDHospiceRevenueAdj > 99999999
                                     OR adj.ESRDHospiceClaimAdj > 99999999
                                     OR adj.QualityAdj > 99999999);

                    --if validation error, return message and exit
                    IF EXISTS (SELECT   1 FROM  #ValidationErrors)
                        BEGIN
                            INSERT INTO dbo.TrendAdjLog
                                (AdjGroupID
                                ,ProcName
                                ,Region
                                ,UserID
                                ,AuditTime
                                ,AuditMessage)
                            SELECT  0                   -- AdjGroupID - tinyint
                                   ,'Upload:SCT'        -- ProcName - varchar(20)
                                   ,@XRegion            -- Region - varchar(40)
                                   ,@XLastUpdateByID    -- UserID - char(7)
                                   ,GETDATE ()          -- AuditTime - datetime
                                   ,CONCAT (@stringReturn, ValidationError)
                            FROM    #ValidationErrors;
                            COMMIT TRANSACTION;

                            SELECT  CONCAT (@stringValidationError, ValidationError)
                            FROM    #ValidationErrors;
                            RETURN;
                        END;

                    --Year + Plans in region that currently have relevant adjustment(s) in SCT to be deleted
                    DROP TABLE IF EXISTS #plansCurrentlyInSCT;
                    SELECT      sct.PlanYearID
                               ,sct.ContractPBPSegment
                    INTO        #plansCurrentlyInSCT
                    FROM        dbo.SCT_SavedRevClaimsAdjustments sct WITH (NOLOCK)
                   INNER JOIN   (SELECT DISTINCT AdjustmentType, Comment FROM   #CalcPlanAdjmt_SCT) adj
                           ON sct.AdjustmentType = adj.AdjustmentType
                              AND   sct.Comment = adj.Comment
                              AND   sct.Iteration = @SCTIteration
                   INNER JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                           ON vpi.CPS = sct.ContractPBPSegment
                              AND   vpi.PlanYear = sct.PlanYearID
                              AND   vpi.Region = @XRegion;

                    -- Year + Plans in region that have new adjustments to be inserted
                    DROP TABLE IF EXISTS #plansToBeAdded;
                    SELECT      sct.PlanYearID
                               ,sct.ContractPBPSegment
                    INTO        #plansToBeAdded
                    FROM        #CalcPlanAdjmt_SCT sct WITH (NOLOCK)
                   INNER JOIN   dbo.vwPlanInfo vw WITH (NOLOCK)
                           ON vw.CPS = sct.ContractPBPSegment
                    WHERE       vw.PlanYear = sct.PlanYearID
                                AND vw.Region = @XRegion;

                    -- table of distinct plan and year combinations that will be fed into downstream SP's
                    DROP TABLE IF EXISTS #DistinctPlanYear;
                    SELECT  DISTINCT
                            a.ContractPBPSegment
                           ,a.PlanYearID
                    INTO    #DistinctPlanYear
                    FROM    (SELECT ContractPBPSegment
                                   ,PlanYearID
                             FROM   #plansCurrentlyInSCT
                             UNION ALL
                             SELECT ContractPBPSegment
                                   ,PlanYearID
                             FROM   #plansToBeAdded) a;

                    --Assigning CPS list for sp execution below
                    SET @CPSList = (SELECT  STRING_AGG (ContractPBPSegment, ',') FROM   #DistinctPlanYear);

                    --Assigning plan year list for sp execution below
                    SET @YearList = (SELECT STRING_AGG (PlanYearID, ',') FROM   #DistinctPlanYear WITH (NOLOCK));

                    --Delete Old Adjustments
                    DELETE      sct
                    FROM        dbo.SCT_SavedRevClaimsAdjustments sct WITH (NOLOCK)
                   INNER JOIN   #DistinctPlanYear dpy
                           ON dpy.PlanYearID = sct.PlanYearID
                              AND   dpy.ContractPBPSegment = sct.ContractPBPSegment
                    WHERE       sct.Iteration = @SCTIteration
                                AND TRIM (sct.Comment) IN (SELECT   TRIM (Comment) FROM #CalcPlanAdjmt_SCT);

                    --Insert New Adjustments
                    INSERT INTO dbo.SCT_SavedRevClaimsAdjustments
                        (PlanYearID
                        ,ContractPBPSegment
                        ,NonESRDHospiceRevenueAdj
                        ,NonESRDHospiceClaimAdjRiskOffset
                        ,NonESRDHospiceClaimAdjOther
                        ,ESRDHospiceRevenueAdj
                        ,ESRDHospiceClaimAdj
                        ,QualityAdj
                        ,AdjustmentType
                        ,Comment
                        ,Iteration
                        ,LastUpdateByID
                        ,LastUpdateDateTime)
                    SELECT  PlanYearID
                           ,ContractPBPSegment
                           ,NonESRDHospiceRevenueAdj
                           ,NonESRDHospiceClaimAdjRiskOffset
                           ,NonESRDHospiceClaimAdjOther
                           ,ESRDHospiceRevenueAdj
                           ,ESRDHospiceClaimAdj
                           ,QualityAdj
                           ,AdjustmentType
                           ,Comment
                           ,Iteration
                           ,LastUpdateByID
                           ,LastUpdateDateTime
                    FROM    #CalcPlanAdjmt_SCT WITH (NOLOCK);

                    --Execute Dependent Procedures			
                    EXEC dbo.SCT_spUpdAddAdjsRevClaims @CPSList, @YearList;

                    EXEC dbo.SCT_spMAClaimTrend @CPSList, @YearList;

                    EXEC dbo.SCT_spMAForecast @CPSList
                                             ,@YearList
                                             ,NULL
                                             ,NULL
                                             ,NULL
                                             ,NULL
                                             ,@XLastUpdateByID;
                    INSERT INTO dbo.TrendAdjLog
                        (AdjGroupID
                        ,ProcName
                        ,Region
                        ,UserID
                        ,AuditTime
                        ,AuditMessage)
                    VALUES (0                   -- AdjGroupID - tinyint
                           ,'Upload:SCT'        -- ProcName - varchar(20)
                           ,@XRegion            -- Region - varchar(40)
                           ,@XLastUpdateByID    -- UserID - char(7)
                           ,GETDATE ()          -- AuditTime - datetime
                           ,NULL);
                END;

            COMMIT TRANSACTION transaction_TrendAdj;

            -- Clean temp table.
            DROP TABLE IF EXISTS #DistinctPlanYear;
            DROP TABLE IF EXISTS #plansToBeAdded;
            DROP TABLE IF EXISTS #plansCurrentlyInSCT;
            DROP TABLE IF EXISTS #CalcPlanAdjmt_SCT;
            DROP TABLE IF EXISTS #sct_adj;
            DROP TABLE IF EXISTS #NewAdjForPrior;
            DROP TABLE IF EXISTS #CalcPlanAdjmt_BenCat;
            DROP TABLE IF EXISTS #CalcPlanAdjmt_RepCat;
            DROP TABLE IF EXISTS #Act_Adj;
            DROP TABLE IF EXISTS #PlanList;
            DROP TABLE IF EXISTS #ValidationErrors;

        END TRY

        BEGIN CATCH

            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;

            SELECT  @ErrorMessage = ERROR_MESSAGE ();
            SELECT  @ErrorSeverity = ERROR_SEVERITY ();
            SELECT  @ErrorState = ERROR_STATE ();

            RAISERROR (@ErrorMessage    -- Message text.  
                      ,@ErrorSeverity   -- Severity.  
                      ,@ErrorState      -- State.  
            );

            ROLLBACK TRANSACTION;

            --- Insert into app log for logging error------------------
            INSERT INTO dbo.TrendAdjLog
                (AdjGroupID
                ,ProcName
                ,Region
                ,UserID
                ,AuditTime
                ,AuditMessage)
            VALUES (0                               -- AdjGroupID - tinyint
                   ,@stringUpload + LEFT(@XTable, 13)   -- ProcName - varchar(20)
                   ,@XRegion                        -- Region - varchar(40)
                   ,@XLastUpdateByID                -- UserID - char(7)
                   ,GETDATE ()                      -- AuditTime - datetime
                   ,@ErrorMessage);

        END CATCH;

        SELECT  @returnmessage;

    END;
GO