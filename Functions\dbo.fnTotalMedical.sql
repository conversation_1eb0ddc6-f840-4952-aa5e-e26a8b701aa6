SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnTotalMedical
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Feb-03
--
-- DESCRIPTION: Function responsible for listing Required Revenue Totals --- Total Medicals 
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--
-- RETURNS: Table
--
-- TABLES: 
--	Read:
--      CalcMedicareCoveredFinalPricing
--      CalcPlanProjection
--		LkpIntAddedBenefitCategory
--		LkpIntAddedBenefitType
--		SavedPlanAddedBenefits
--		SavedPlanHeader   
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE   (SELECT EGWPMSB FROM fnGetUnspentRebateForEGWPMSB(@ForecastID))
--	Called: fnSignificantDigits
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		CHANGES MADE						                                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Feb-03		1			Initial Version							                            Christian Cofie
-- 2007-Nov-27		2			Added IsHidden=0 to where condition for Added Benefits.	            Christian Cofie
-- 2007-Dec-19		3			Revised code to bring it into coding standards.			            Shannon Boykin                            
-- 2008-Apr-29		4			added Significant digits to NetESRD and Allowed			            Brian Lake
-- 2008-May-04      5			Converted data to decimal(x,y) and added fn.SignificantDigits	    Sandy Ellis
-- 2009-Mar-17      6           Data types                                                          Sandy Ellis
-- 2009-Mar-19      7           Added Dual TypeID = 3 temporarily                                   Brian Lake
-- 2009-Apr-01      8           Updated documentation                                               Sandy Ellis
-- 2009-Apr-21      9           Added DualEligibleTypeID = 3 to join and where on Final Pricing     Sandy Ellis
-- 2010-Jul-26      10          Revised for 2012 database and coding standards                      Jake Gaecke
-- 2010-Sep-28      11          Removed PlanVersion                                                 Michael Siekerka
-- 2012-Aug-09		12			Added code to handle EGWPMSB issue for group plans					Tim Gao
-- 2012-Aug-15		13			Added code for getting NetNonESRD, CoveredActEquivNetNonESRD		Tim Gao
--								and CostShareReduction from a different source
-- 2014-May-15		14			Used values from ws4CS to bring in Net amounts						Mike Deren
-- 2014-July-11		15			New function to prevent sum of products vs product of sum			Mike Deren
-- 2015-July-08     16			Optimized the function as part of SCT								Manish Shulka
-- 2022-Jun-22		17			Removed @IsEGWP														Aleksandar Dimitrijevic
-- 2024-OCT-01		18			Remove function call to fnGetProfitAdjForGroup as this is not 
--									actually being used anywhere in the code.						Jake Lewis
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnTotalMedical]
    (@ForecastID INT

)
RETURNS @Result TABLE
    (ForecastID                INT
    ,Allowed                   DECIMAL(20, 12)
    ,CostShare                 DECIMAL(14, 6)
    ,NetNonESRD                DECIMAL(20, 12)
    ,CoveredActEquivNetNonESRD DECIMAL(14, 8)
    ,AddedBenefitNet           DECIMAL(14, 2)
    ,CostShareReduction        DECIMAL(14, 10)
    ,SuppBenefitTotalNonESRD   DECIMAL(14, 6))
AS
    BEGIN
        DECLARE @EGWPMSB FLOAT;
        SELECT  @EGWPMSB = EGWPMSB FROM fnGetUnspentRebateForEGWPMSB (@ForecastID);
        --Returns a result set that lists Total Medicals for given selection parameters
        INSERT INTO @Result
        SELECT      mcfp.ForecastID
                   ,Allowed = CONVERT (DECIMAL(20, 12), ISNULL (abt.AddedBenefitAllowed, 0))
                              + CONVERT (
                                DECIMAL(20, 12)
                               ,(dbo.fnSignificantDigits (ISNULL (cpp.INAllowed, 0) + ISNULL (cpp.OONAllowed, 0), 15)))
                   ,CostShare = (ISNULL (abt.AddedBenefitCostShare, 0) + mcfp.CostShare)
                   ,NetNonESRD = (CONVERT (DECIMAL(20, 12), (ws4.net))
                                  + CONVERT (
                                    DECIMAL(14, 6)
                                   ,(ISNULL (abt.AddedBenefitAllowed, 0) - ISNULL (abt.AddedBenefitCostShare, 0))))
                   ,CoveredActEquivNetNonESRD = ws4MedCov.MedicareCoveredNet
                   ,AddedBenefitNet = (mcfp.AddedBenefitNet
                                       + (ISNULL (abt.AddedBenefitAllowed - abt.AddedBenefitCostShare, 0)))
                   ,CostShareReduction = mcfp.CostShareReduction
                   ,SuppBenefitTotalNonESRD = mcfp.SupplementalBenefitTotal

        FROM        CalcMedicareCoveredFinalPricing mcfp
       INNER JOIN   (SELECT     mm.ForecastID
                               ,net = SUM (CASE WHEN DualEligibleTypeID = 1 THEN (cs.Net * mm.DEMM) / mm.TotalMM
                                                ELSE (cs.Net * mm.NonDEMM) / mm.TotalMM END)
                     FROM       dbo.fnAppGetMABPTWS4CostShares (@ForecastID) cs
                    INNER JOIN  (SELECT     DE.ForecastID
                                           ,NonDEMM = SUM (NonDE.ProjectedMemberMonths)
                                           ,DEMM = SUM (DE.ProjectedMemberMonths)
                                           ,TotalMM = SUM (Total.ProjectedMemberMonths)
                                 FROM       fnPlanCountyProjectedMemberMonths (@ForecastID, 0) NonDE
                                INNER JOIN  fnPlanCountyProjectedMemberMonths (@ForecastID, 1) DE
                                        ON NonDE.ForecastID = DE.ForecastID
                                           AND  NonDE.StateTerritoryID = DE.StateTerritoryID
                                           AND  NonDE.CountyCode = DE.CountyCode
                                INNER JOIN  fnPlanCountyProjectedMemberMonths (@ForecastID, 2) Total
                                        ON Total.ForecastID = DE.ForecastID
                                           AND  Total.StateTerritoryID = DE.StateTerritoryID
                                           AND  Total.CountyCode = DE.CountyCode
                                 GROUP BY   DE.ForecastID) mm
                            ON cs.ForecastID = mm.ForecastID
                     GROUP BY   mm.ForecastID) ws4
               ON ws4.ForecastID = mcfp.ForecastID
       INNER JOIN   fnAppGetMABPTWS4TotalMedicareCovered (@ForecastID) ws4MedCov
               ON ws4.ForecastID = mcfp.ForecastID
       INNER JOIN   CalcPlanProjection cpp
               ON mcfp.ForecastID = cpp.ForecastID
                  AND   mcfp.DualEligibleTypeID = cpp.DualEligibleTypeID
        LEFT JOIN --abt
                    (SELECT -- Start of temp qry-AddedBenefitTotal Table
                                        sph.ForecastID
                                       ,AddedBenefitAllowed = ROUND (
                                                              SUM (
                                                              CASE WHEN labt.IsStandard = 1 THEN
                                                              (labt.INAddedBenefitAllowed
                                                               + (CASE WHEN sph.PlanTypeID = 1 THEN 0 ELSE ISNULL (labt.OONAddedBenefitAllowed, 0) END))
                                                                   ELSE
                                                              (pab.INAddedBenefitAllowed
                                                               + (CASE WHEN sph.PlanTypeID = 1 THEN 0 ELSE ISNULL (pab.OONAddedBenefitAllowed, 0) END)) END)
                                                             ,2)
                                       ,AddedBenefitCostShare = ROUND (
                                                                SUM (
                                                                CASE WHEN labt.IsStandard = 1 THEN
                                                                (labt.INAddedBenefitCostShare
                                                                 + (CASE WHEN sph.PlanTypeID = 1 THEN 0 ELSE ISNULL (labt.OONAddedBenefitCostShare, 0) END))
                                                                     ELSE
                                                                (pab.INAddedBenefitCostShare
                                                                 + (CASE WHEN sph.PlanTypeID = 1 THEN 0 ELSE ISNULL (pab.OONAddedBenefitCostShare, 0) END)) END)
                                                               ,2)
                     FROM               SavedPlanHeader sph
                    INNER JOIN          SavedPlanAddedBenefits pab
                            ON pab.ForecastID = sph.ForecastID
                               AND  pab.IsHidden = 0
                    INNER JOIN          LkpIntAddedBenefitType labt
                            ON labt.AddedBenefitTypeID = pab.AddedBenefitTypeID
                    INNER JOIN          LkpIntAddedBenefitCategory labc
                            ON labc.AddedBenefitCatID = labt.AddedBenefitCatID
                     WHERE              sph.ForecastID = @ForecastID
                                        AND sph.IsHidden = 0
                     GROUP      BY      sph.ForecastID
                                       ,sph.PlanTypeID) abt -- End of temp qry-AddedBenefitTotal Table
               ON mcfp.ForecastID = abt.ForecastID
        WHERE       mcfp.ForecastID = @ForecastID
                    AND mcfp.DualEligibleTypeID = 2;    --Combined

        RETURN;
    END;
GO
