SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------        
-- SP NAME: [spGetInvalidScriptIdForCurrentYearMaaui]        
--        
-- AUTHOR: Surya <PERSON>rthy
--        
-- CREATED DATE: 2023-08-15       
--        
-- DESCRIPTION: Procedure responsible to get valid script IDs that can be executed for current Year.    
--        
-- PARAMETERS:         
--        
-- TABLES:         
-- Read:     
-- dbo.SavedUtilityRequestHeader
-- dbo.SavedUtilityRequestDetail

-- Write:        
--        
-- VIEWS:        
--        
-- FUNCTIONS:       
--        
-- STORED PROCS:        
--        
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE          VERSION      CHANGES MADE								 DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2023-08-15    1          Initial Version								Surya Murthy
-- 2024-12-18    2          Added pre validation logic					Surya Murthy
--							
-- ---------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[spGetInvalidScriptIdForCurrentYearMaaui]
AS
BEGIN
	SET NOCOUNT ON;
	SELECT   SDCRH.RequestID AS RequestId,SMTORD.ExecutionStatus, SDCRH.RequestType FROM dbo.SavedUtilityRequestHeader SDCRH
	INNER JOIN dbo.SavedUtilityRequestDetail SMTORD
	ON SDCRH.RequestID=SMTORD.RequestID
	WHERE SDCRH.RequestType IN( 'ArchiveData',
								'ModelTurn_Script0',
								'ModelTurn_Script1',
								'ModelTurn_Script2',
								'ModelTurn_Script3',
								'ModelTurn_Script4',
								'ModelTurn_Script5'
								)
	AND SMTORD.ExecutionStatus NOT IN ('Failed','Cancelled')
	AND SUBSTRING(CAST(SDCRH.RequestedDateTime AS VARCHAR(20)),8,4) = YEAR(GETDATE())
	ORDER BY SDCRH.RequestedDateTime DESC
END;
GO

