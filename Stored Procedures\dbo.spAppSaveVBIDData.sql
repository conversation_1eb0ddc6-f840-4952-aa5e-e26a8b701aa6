SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- =============================================      
-- Author:  K<PERSON><PERSON> Singh     
-- Create date: 13 June 2019
-- Description:  save VBID  data
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:    
-- Read:     
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE        VERSION   CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2019-Jun-13  1        Initial version.											 Kritika
-- 2019-Jun-13  1        Added getdate() and @LastUpdateByID while intserting 
--						 data to savedScenarioVBID  .								 Pooja Dahiya		 
-- ----------------------------------------------------------------------------------------------------------------------
             
CREATE PROCEDURE [dbo].[spAppSaveVBIDData]-- 660,'KXS7297'
 @ForecastID INT,
 @VBID varchar(max),
 @LastUpdateByID CHAR(7) ,
 @MessageFromBackend NVARCHAR(500) OUTPUT,
 @Result BIT OUT
AS
BEGIN   
        BEGIN TRANSACTION;
        BEGIN TRY  
        
		delete from SavedScenarioVBID where ForecastID = @ForecastID

		if (@VBID<>'')
		begin
		Insert into SavedScenarioVBID 
		SELECT @ForecastID, Value,@LastUpdateByID,GETDATE() FROM dbo.fnStringSplit(@VBID, ',') 
		end
      
		set @Result=1
        SET @MessageFromBackend='';


    COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
				
			--SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
			ROLLBACK TRANSACTION; 

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
            
        END CATCH; 
    END;
GO
