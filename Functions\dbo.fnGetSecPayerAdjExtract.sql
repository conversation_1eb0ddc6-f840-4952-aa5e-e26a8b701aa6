SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetSecPayerAdjExtract]  ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetSecPayerAdjExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-21
-- HEADER UPDATED: 2011-Dec-21
--
-- DESCRIPTION: Designed to extract fields for Medicare Secondary Payer Adjustment - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	
--		SavedPlanAssumptions
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-21		1			Initial Version														Bobby Jaegers
-- 2024-Dec-06		2			Add CPS to the export												Ramaraj Kumar
----------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetSecPayerAdjExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	CPS Char(13),
	SecondaryPayerAdjustment Decimal(7,6)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
	BEGIN
		INSERT @Results
			SELECT  SPA.ForecastID,			
					SPI.CPS,
					SPA.SecondaryPayerAdjustment

			FROM SavedPlanAssumptions SPA
					JOIN SavedForecastSetup SFS ON SFS.ForecastID = SPA.ForecastID
					JOIN SavedPlanInfo SPI ON SPI.PlanInfoID = SFS.PlanInfoID
    END

	ELSE
	BEGIN
		INSERT @Results
			SELECT  SPA.ForecastID,			
					SPI.CPS,
					SPA.SecondaryPayerAdjustment

			FROM SavedPlanAssumptions SPA
					JOIN SavedForecastSetup SFS ON SFS.ForecastID = SPA.ForecastID
					JOIN SavedPlanInfo SPI ON SPI.PlanInfoID = SFS.PlanInfoID
			WHERE SPA.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
	END
RETURN
END