SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author: Deepali Mittal    
-- Create date: 2018-Oct-03
-- Description:  spAppGetRequestDetails
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:      
-- Read:  LkpIntServerList
--          
-- Write: [] 
--            
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Oct-03    1		  Initial version.											Deepali Mittal                                    
-- 2018-Nov-07    2       Added lastupdatedatetime for header table- using column 
--						  to provide Request start time								Pooja Dahiya
-- 2018-Dec-19    3       Updated Error message                                     Kritika Singh
--                        and included logging exception	   
-- 2019-Apr-03    4       Updated the field names for header and detail             Kritika Singh
--                        table	  
-- 2019-Apr-15    5       #tempRequestID should have int request Id                 Kritika Singh
--                        
-- ----------------------------------------------------------------------------------------------------------------------    

CREATE PROCEDURE [dbo].[spAppStatusUpdateRequest] 
@RequestIDList varchar(max),
@Status varchar(100),
@ReviewerComments VARCHAR(MAX),
@LastUpdateByID VARCHAR(7),
@Result BIT OUT
AS

BEGIN  
	BEGIN TRANSACTION;
        BEGIN TRY  
		Declare @RequestID int;
		Declare @tempReqID varchar(max)
        SET @tempReqID=@RequestIDList;
		
		create table #tempRequestID(RequestID int, isUpdated bit);
		insert into #tempRequestID  
	            select  cast(value as int),0  from dbo.fnStringSplit(@tempReqID,',');

		WHILE(SELECT COUNT(*) FROM #tempRequestID where isUpdated=0)>0
		BEGIN
		SELECT TOP 1 @RequestID=[RequestID]
                   FROM #tempRequestID 
				   where isUpdated=0		
		If(@ReviewerComments is not null)
		 BEGIN
		UPDATE SavedUtilityRequestHeader
			SET 
			RequestStatus=@Status,
			ReviewedBy=@LastUpdateByID,
			ReviewedDateTime=GetDate(),
			ReviewerComments=@ReviewerComments,
			LastUpdateByID=@LastUpdateByID
			where RequestID=@RequestID

			UPDATE SavedUtilityRequestDetail
			SET 
			ExecutionStatus=@Status
			where RequestID=@RequestID
		END
		ELSE
		BEGIN
		
		IF(@Status='Queued' or @Status='Cancelled' or @Status='Failed')
		BEGIN
		UPDATE SavedUtilityRequestHeader
			SET 
			RequestStatus=@Status
			where RequestID=@RequestID		
			
		END	
		ELSE IF(@Status='In Progress')
		BEGIN
			UPDATE SavedUtilityRequestHeader
			SET 
			RequestStatus=@Status,
			RequestStartDateTime=getdate()
			where RequestID=@RequestID
			
		END
		ELSE IF(@Status='Completed')
		BEGIN		
			UPDATE SavedUtilityRequestHeader
			SET 
			RequestStatus=@Status,
			RequestEndDateTime=getdate()
			where RequestID=@RequestID			
		END
		UPDATE SavedUtilityRequestDetail
			SET 
			ExecutionStatus=@Status
		where RequestID=@RequestID

			

			END
			update #tempRequestID
			set isUpdated=1
			where RequestID=@RequestID
		END
		
		
		
		 SET @Result = 1;
   COMMIT TRANSACTION;
END TRY
        BEGIN CATCH
   
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 Declare @User varchar(7)
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()
			 if(@LastUpdateByID is null)
			 set @User= right(USER,7)
			 else
			  set @User=@LastUpdateByID
			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
			ROLLBACK TRANSACTION; 

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@User
            
        END CATCH; 
		END
GO
