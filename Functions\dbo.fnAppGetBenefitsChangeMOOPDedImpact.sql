SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:	fnAppGetBenefitsChangeMOOPDedImpact
--
-- CREATOR:			<PERSON> Wu
--
-- CREATED DATE:	2010-MAR-25
--
-- DESCRIPTION:		Function responsible for listing IN and OON MOOP/Deductible impact for current/bid year.
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT
--
--  Output :		@Results TABLE
--
-- TABLES : 
--	Read :			CalcBenefitProjectionPreMbrCS (DE# Allowed Claims)
--					CalcTotalCostShare			  (Non-DE# Allowed and Cost Share claims)
--					SavedPlanMemberMonthDetail	  (Non-DE#/DE# member month weighting)
--					LkpIntDemogIndicators		  (Non-DE#/DE# member month weighting)
--					SavedPlanStateCountyDetail	  (Non-DE#/DE# member month weighting)
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetSafeDivisionResult
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE															DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2010-MAR-25      1           Initial Version															James Wu
-- 2010-APR-09		2			Changed to point to	CalcDeductibleMOOPCategoryFactors					Sule Dauda
-- 2010-JUL-27		3			Moved to 2012															Joe Casey
-- 2010-JUL-27		4			Changed BenefitYearID to IsBenefitYearCurrentYear						Joe Casey
-- 2010-AUG-23		5			Removed PlanVersion														Joe Casey
-- 2011-FEB-04		6			Updated CSDetail query.  Added Join to LkpIntMOOPCategoryDetail			Nate Jacoby
--									to remove case statements for MOOP Categories					
-- 2012-JAN-19		7			Added code to handle PartB only plan									Tim Gao
-- 2014-MAY-05		8			Modified for 2015 Part B Only Logic, now automated						Nick Koesters
-- 2018-APR-24		9			Modified LkpExtCMSPlanType for new UI table modifications				Jordan Purdue

-- 2022-MAY-02		10			MAAUI migration; replaced input variable from @PlanIndex to
--								@ForecastID; for CalcDeductibleMOOPFactors, replaced reference
--								from PlanIndex to ForecastID; removed nested queries (function
--								dropped and recreated); removed fnIsPartBOnly							Aleksandar Dimitrijevic
-- 2023-JAN-13		11			Calculation changes for Deductible impact project						Mike Lovely 
-- 2023-FEB-02		12			Added @XVariable, (NOLOCK)												Sheetal Patil 
-- 2023-FEB-23		13			Fixed - TotalINDeductibleImpact was using CSDetail.OONCurrentDedApplies	Mike Lovely 
-- 2023-MAR-06     	14          Changes for isolating deductible impact overhauled the code				Mike Lovely
-- 2023-JUN-01		15			Remove MOOP Categories project removes the need to bring in MOOP
--								Categories to this calculation and also breaks the dependency with
--								fnAppGetBenefitsChangeDetail since both that function and this one
--								can get what they need from CalcTotalCostShareByBenCat					Mike Lovely
-- 2024-AUG-28		16			Renamed dbo.CalcTotalCostShareByBenCat to dbo.CalcTotalCostShare		Franklin Fu
-- 2024-OCT-08		17			Cost Share Basis: add handling to only pull in 
--									IsIncludeInCostShareBasis=1 from CalcBenefitProjectionPreMbrCS		Jake Lewis
-- 2025-FEB-12		18			Correct an error in the Total Cost Share formula in lines 126:132		Jake Lewis
-- 2025-APR-07		19			Correct an issue in the MOOPImpact formulas								Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnAppGetBenefitsChangeMOOPDedImpact]
    (@ForecastID INT)

RETURNS @Results TABLE
    (ForecastID               INT
    ,IsBenefitYearCurrentYear BIT
    ,TotalINPreMoopCS         FLOAT
    ,TotalOONPreMoopCS        FLOAT
    ,TotalPreMoopCS           FLOAT
    ,TotalINMoopImpact        FLOAT
    ,TotalOONMoopImpact       FLOAT
    ,TotalMoopImpact          FLOAT
    ,TotalINDeductibleImpact  FLOAT
    ,TotalOONDeductibleImpact FLOAT
    ,TotalDeductibleImpact    FLOAT
    ,TotalINAfterMoopCS       FLOAT
    ,TotalOONAfterMoopCS      FLOAT
    ,TotalAfterMoopCS         FLOAT)

AS

    BEGIN

        DECLARE @XForecastID INT = @ForecastID;

        --DE# Cost Share by Benefit Category (calculated same way as Non-DE# in spCalcTotalCostShare)
        DECLARE @DETotalCostShareByBenCat TABLE
            (ForecastID               INT
            ,IsBenefitYearCurrentYear BIT
            ,BenefitCategoryID        SMALLINT
            ,INAllowed                FLOAT
            ,INDeductibleFactor       FLOAT
            ,INEffectiveCoinsurance   FLOAT
            ,INMOOPFactor             FLOAT
            ,INTotalCostShare         FLOAT
            ,OONAllowed               FLOAT
            ,OONDeductibleFactor      FLOAT
            ,OONEffectiveCoinsurance  FLOAT
            ,OONMOOPFactor            FLOAT
            ,OONTotalCostShare        FLOAT);

        INSERT INTO @DETotalCostShareByBenCat
            (ForecastID
            ,IsBenefitYearCurrentYear
            ,BenefitCategoryID
            ,INAllowed
            ,INDeductibleFactor
            ,INEffectiveCoinsurance
            ,INMOOPFactor
            ,INTotalCostShare
            ,OONAllowed
            ,OONDeductibleFactor
            ,OONEffectiveCoinsurance
            ,OONMOOPFactor
            ,OONTotalCostShare)
        SELECT      TCS.ForecastID
                   ,TCS.IsBenefitYearCurrentYear
                   ,TCS.BenefitCategoryID
                   ,INAllowed = CBP.INAllowed   --Make sure to pull in from CBP and not TCS as the latter houses Non-DE#
                   ,TCS.INDeductibleFactor
                   ,TCS.INEffectiveCoinsurance
                   ,TCS.INMOOPFactor
                   ,INTotalCostShare = CBP.INAllowed * TCS.INDeductibleFactor
                                       + --Don't need deductible exclusions CASE WHEN because already built into TCS
                   CBP.INAllowed       * (1 - TCS.INDeductibleFactor) * TCS.INMOOPFactor * TCS.INEffectiveCoinsurance
                   ,OONAllowed = CBP.OONAllowed --Make sure to pull in from CBP and not TCS as the latter houses Non-DE#
                   ,TCS.OONDeductibleFactor
                   ,TCS.OONEffectiveCoinsurance
                   ,TCS.OONMOOPFactor
                   ,OONTotalCostShare = CBP.OONAllowed * TCS.OONDeductibleFactor
                                        + --Don't need deductible exclusions CASE WHEN because already built into TCS
                   CBP.OONAllowed       * (1 - TCS.OONDeductibleFactor) * TCS.OONMOOPFactor * TCS.OONEffectiveCoinsurance
        FROM        dbo.CalcTotalCostShare TCS WITH (NOLOCK)
       INNER JOIN   dbo.CalcBenefitProjectionPreMbrCS CBP WITH (NOLOCK)
               ON TCS.ForecastID = CBP.ForecastID
                  AND   TCS.BenefitCategoryID = CBP.BenefitCategoryID
        WHERE       TCS.ForecastID = @XForecastID
                    AND CBP.DualEligibleTypeID = 1 --DE#
                    AND CBP.MARatingOptionID = 3 --Blended experience and manual
                    AND CBP.IsIncludeInCostShareBasis = 1;  --Cost Share Basis only

        --DE# Cost Share By Plan
        DECLARE @DETotalCostShare TABLE
            (ForecastID               INT
            ,IsBenefitYearCurrentYear BIT
            ,TotalINPreMoopCS         FLOAT
            ,TotalOONPreMoopCS        FLOAT
            ,TotalPreMoopCS           FLOAT
            ,TotalINMoopImpact        FLOAT
            ,TotalOONMoopImpact       FLOAT
            ,TotalMoopImpact          FLOAT
            ,TotalINDeductibleImpact  FLOAT
            ,TotalOONDeductibleImpact FLOAT
            ,TotalDeductibleImpact    FLOAT
            ,TotalINAfterMoopCS       FLOAT
            ,TotalOONAfterMoopCS      FLOAT
            ,TotalAfterMoopCS         FLOAT);

        INSERT INTO @DETotalCostShare
            (ForecastID
            ,IsBenefitYearCurrentYear
            ,TotalINPreMoopCS
            ,TotalOONPreMoopCS
            ,TotalPreMoopCS
            ,TotalINMoopImpact
            ,TotalOONMoopImpact
            ,TotalMoopImpact
            ,TotalINDeductibleImpact
            ,TotalOONDeductibleImpact
            ,TotalDeductibleImpact
            ,TotalINAfterMoopCS
            ,TotalOONAfterMoopCS
            ,TotalAfterMoopCS)
        SELECT      DECS.ForecastID
                   ,DECS.IsBenefitYearCurrentYear
                   ,TotalINPreMoopDECS = SUM (DECS.INAllowed * (1 - DECS.INDeductibleFactor) * DECS.INEffectiveCoinsurance)
                   ,TotalOONPreMoopDECS = SUM (
                                          DECS.OONAllowed * (1 - DECS.OONDeductibleFactor) * DECS.OONEffectiveCoinsurance)
                   ,TotalPreMoopDECS = SUM (
                                       DECS.INAllowed * (1 - DECS.INDeductibleFactor) * DECS.INEffectiveCoinsurance
                                       + DECS.OONAllowed * (1 - DECS.OONDeductibleFactor) * DECS.OONEffectiveCoinsurance)
                   ,TotalINMoopImpact = SUM (
                                        DECS.INTotalCostShare
                                        - (DECS.INAllowed * (1 - DECS.INDeductibleFactor) * DECS.INEffectiveCoinsurance)
                                        - (DECS.INAllowed * DECS.INDeductibleFactor))
                   ,TotalOONMoopImpact = SUM (
                                         DECS.OONTotalCostShare
                                         - (DECS.OONAllowed * (1 - DECS.OONDeductibleFactor) * DECS.OONEffectiveCoinsurance)
                                         - (DECS.OONAllowed * DECS.OONDeductibleFactor))
                   ,TotalMoopImpact = SUM (
                                      DECS.INTotalCostShare
                                      - (DECS.INAllowed * (1 - DECS.INDeductibleFactor) * DECS.INEffectiveCoinsurance)
                                      - (DECS.INAllowed * DECS.INDeductibleFactor) + DECS.OONTotalCostShare
                                      - (DECS.OONAllowed * (1 - DECS.OONDeductibleFactor) * DECS.OONEffectiveCoinsurance)
                                      - (DECS.OONAllowed * DECS.OONDeductibleFactor))
                   ,TotalINDeductibleImpact = SUM (DECS.INAllowed * DECS.INDeductibleFactor)
                   ,TotalOONDeductibleImpact = SUM (DECS.OONAllowed * DECS.OONDeductibleFactor)
                   ,TotalDeductibleImpact = SUM (
                                            DECS.INAllowed * DECS.INDeductibleFactor + DECS.OONAllowed
                                            * DECS.OONDeductibleFactor)
                   ,TotalINAfterMoopDECS = SUM (DECS.INTotalCostShare)
                   ,TotalOONAfterMoopDECS = SUM (DECS.OONTotalCostShare)
                   ,TotalAfterMoopDECS = SUM (DECS.INTotalCostShare + DECS.OONTotalCostShare)
        FROM        @DETotalCostShareByBenCat DECS
        GROUP BY    DECS.ForecastID
                   ,DECS.IsBenefitYearCurrentYear;


        --Non-DE# Cost Share By Plan
        DECLARE @NonDETotalCostShare TABLE
            (ForecastID               INT
            ,IsBenefitYearCurrentYear BIT
            ,TotalINPreMoopCS         FLOAT
            ,TotalOONPreMoopCS        FLOAT
            ,TotalPreMoopCS           FLOAT
            ,TotalINMoopImpact        FLOAT
            ,TotalOONMoopImpact       FLOAT
            ,TotalMoopImpact          FLOAT
            ,TotalINDeductibleImpact  FLOAT
            ,TotalOONDeductibleImpact FLOAT
            ,TotalDeductibleImpact    FLOAT
            ,TotalINAfterMoopCS       FLOAT
            ,TotalOONAfterMoopCS      FLOAT
            ,TotalAfterMoopCS         FLOAT);

        INSERT INTO @NonDETotalCostShare
            (ForecastID
            ,IsBenefitYearCurrentYear
            ,TotalINPreMoopCS
            ,TotalOONPreMoopCS
            ,TotalPreMoopCS
            ,TotalINMoopImpact
            ,TotalOONMoopImpact
            ,TotalMoopImpact
            ,TotalINDeductibleImpact
            ,TotalOONDeductibleImpact
            ,TotalDeductibleImpact
            ,TotalINAfterMoopCS
            ,TotalOONAfterMoopCS
            ,TotalAfterMoopCS)
        SELECT      NonDECS.ForecastID
                   ,NonDECS.IsBenefitYearCurrentYear
                   ,TotalINPreMoopNonDECS = SUM (
                                            NonDECS.INAllowed * (1 - NonDECS.INDeductibleFactor)
                                            * NonDECS.INEffectiveCoinsurance)
                   ,TotalOONPreMoopNonDECS = SUM (
                                             NonDECS.OONAllowed * (1 - NonDECS.OONDeductibleFactor)
                                             * NonDECS.OONEffectiveCoinsurance)
                   ,TotalPreMoopNonDECS = SUM (
                                          NonDECS.INAllowed * (1 - NonDECS.INDeductibleFactor)
                                          * NonDECS.INEffectiveCoinsurance + NonDECS.OONAllowed
                                          * (1 - NonDECS.OONDeductibleFactor) * NonDECS.OONEffectiveCoinsurance)
                   ,TotalINMoopImpact = SUM (
                                        NonDECS.INTotalCostShare
                                        - (NonDECS.INAllowed * (1 - NonDECS.INDeductibleFactor)
                                           * NonDECS.INEffectiveCoinsurance)
                                        - (NonDECS.INAllowed * NonDECS.INDeductibleFactor))
                   ,TotalOONMoopImpact = SUM (
                                         NonDECS.OONTotalCostShare
                                         - (NonDECS.OONAllowed * (1 - NonDECS.OONDeductibleFactor)
                                            * NonDECS.OONEffectiveCoinsurance)
                                         - (NonDECS.OONAllowed * NonDECS.OONDeductibleFactor))
                   ,TotalMoopImpact = SUM (
                                      NonDECS.INTotalCostShare
                                      - (NonDECS.INAllowed * (1 - NonDECS.INDeductibleFactor)
                                         * NonDECS.INEffectiveCoinsurance)
                                      - (NonDECS.INAllowed * NonDECS.INDeductibleFactor) + NonDECS.OONTotalCostShare
                                      - (NonDECS.OONAllowed * (1 - NonDECS.OONDeductibleFactor)
                                         * NonDECS.OONEffectiveCoinsurance)
                                      - (NonDECS.OONAllowed * NonDECS.OONDeductibleFactor))
                   ,TotalINDeductibleImpact = SUM (NonDECS.INAllowed * NonDECS.INDeductibleFactor)
                   ,TotalOONDeductibleImpact = SUM (NonDECS.OONAllowed * NonDECS.OONDeductibleFactor)
                   ,TotalDeductibleImpact = SUM (
                                            NonDECS.INAllowed * NonDECS.INDeductibleFactor + NonDECS.OONAllowed
                                            * NonDECS.OONDeductibleFactor)
                   ,TotalINAfterMoopNonDECS = SUM (NonDECS.INTotalCostShare)
                   ,TotalOONAfterMoopNonDECS = SUM (NonDECS.OONTotalCostShare)
                   ,TotalAfterMoopNonDECS = SUM (NonDECS.INTotalCostShare + NonDECS.OONTotalCostShare)
        FROM        dbo.CalcTotalCostShare NonDECS WITH (NOLOCK)
        WHERE       NonDECS.ForecastID = @XForecastID
        GROUP BY    NonDECS.ForecastID
                   ,NonDECS.IsBenefitYearCurrentYear;


        --NonDE# and DE# Member Months For Weighting (NonDE#/DE# weighting is now done here instead of fnAppGetBenefitsChangeDetail. The details are in the other fn's description.)
        DECLARE @NonDE FLOAT;
        DECLARE @DE FLOAT;
        DECLARE @CalcDENonDEMemberMonths TABLE
            (NonDEMemberMonths DECIMAL(13, 6)
            ,DEMemberMonths    DECIMAL(13, 6));

        INSERT INTO @CalcDENonDEMemberMonths
            (NonDEMemberMonths
            ,DEMemberMonths)
        SELECT      NonDEMemberMonths = SUM (CASE WHEN DualEligibleTypeID = 0 THEN MemberMonths ELSE 0 END)
                   ,DEMemberMonths = SUM (CASE WHEN DualEligibleTypeID = 1 THEN MemberMonths ELSE 0 END)
        FROM        dbo.SavedPlanMemberMonthDetail MMD (NOLOCK)
       INNER JOIN   dbo.LkpIntDemogIndicators LKP (NOLOCK)
               ON LKP.DemogIndicator = MMD.DemogIndicator
       INNER JOIN   dbo.SavedPlanStateCountyDetail SCD (NOLOCK)
               ON SCD.ForecastID = MMD.ForecastID
                  AND   SCD.StateTerritoryID = MMD.StateTerritoryID
                  AND   SCD.CountyCode = MMD.CountyCode
        WHERE       MMD.ForecastID = @XForecastID
                    AND IsBiddable = 1;

        SET @NonDE = (SELECT    NonDEMemberMonths FROM @CalcDENonDEMemberMonths);
        SET @DE = (SELECT   DEMemberMonths FROM @CalcDENonDEMemberMonths);


        -------------------------------------------------------------------------------------------------------------------------------------------------
        INSERT INTO @Results
            (ForecastID
            ,IsBenefitYearCurrentYear
            ,TotalINPreMoopCS
            ,TotalOONPreMoopCS
            ,TotalPreMoopCS
            ,TotalINMoopImpact
            ,TotalOONMoopImpact
            ,TotalMoopImpact
            ,TotalINDeductibleImpact
            ,TotalOONDeductibleImpact
            ,TotalDeductibleImpact
            ,TotalINAfterMoopCS
            ,TotalOONAfterMoopCS
            ,TotalAfterMoopCS)

        SELECT      NonDESum.ForecastID
                   ,NonDESum.IsBenefitYearCurrentYear
                   ,TotalINPreMoopCS = dbo.fnGetSafeDivisionResult (
                                       NonDESum.TotalINPreMoopCS * @NonDE + DESum.TotalINPreMoopCS * @DE, @NonDE + @DE)
                   ,TotalOONPreMoopCS = dbo.fnGetSafeDivisionResult (
                                        NonDESum.TotalOONPreMoopCS * @NonDE + DESum.TotalOONPreMoopCS * @DE, @NonDE + @DE)
                   ,TotalPreMoopCS = dbo.fnGetSafeDivisionResult (
                                     NonDESum.TotalINPreMoopCS * @NonDE + DESum.TotalINPreMoopCS * @DE, @NonDE + @DE)
                                     + dbo.fnGetSafeDivisionResult (
                                       NonDESum.TotalOONPreMoopCS * @NonDE + DESum.TotalOONPreMoopCS * @DE, @NonDE + @DE)
                   ,TotalINMoopImpact = dbo.fnGetSafeDivisionResult (
                                        NonDESum.TotalINMoopImpact * @NonDE + DESum.TotalINMoopImpact * @DE, @NonDE + @DE)
                   ,TotalOONMoopImpact = dbo.fnGetSafeDivisionResult (
                                         NonDESum.TotalOONMoopImpact * @NonDE + DESum.TotalOONMoopImpact * @DE
                                        ,@NonDE + @DE)
                   ,TotalMoopImpact = dbo.fnGetSafeDivisionResult (
                                      NonDESum.TotalINMoopImpact * @NonDE + DESum.TotalINMoopImpact * @DE, @NonDE + @DE)
                                      + dbo.fnGetSafeDivisionResult (
                                        NonDESum.TotalOONMoopImpact * @NonDE + DESum.TotalOONMoopImpact * @DE, @NonDE + @DE)
                   ,TotalINDeductibleImpact = dbo.fnGetSafeDivisionResult (
                                              NonDESum.TotalINDeductibleImpact * @NonDE + DESum.TotalINDeductibleImpact
                                              * @DE
                                             ,@NonDE + @DE)
                   ,TotalOONDeductibleImpact = dbo.fnGetSafeDivisionResult (
                                               NonDESum.TotalOONDeductibleImpact * @NonDE + DESum.TotalOONDeductibleImpact
                                               * @DE
                                              ,@NonDE + @DE)
                   ,TotalDeductibleImpact = dbo.fnGetSafeDivisionResult (
                                            NonDESum.TotalINDeductibleImpact * @NonDE + DESum.TotalINDeductibleImpact * @DE
                                           ,@NonDE + @DE)
                                            + dbo.fnGetSafeDivisionResult (
                                              NonDESum.TotalOONDeductibleImpact * @NonDE + DESum.TotalOONDeductibleImpact
                                              * @DE
                                             ,@NonDE + @DE)
                   ,TotalINAfterMoopCS = dbo.fnGetSafeDivisionResult (
                                         NonDESum.TotalINAfterMoopCS * @NonDE + DESum.TotalINAfterMoopCS * @DE
                                        ,@NonDE + @DE)
                   ,TotalOONAfterMoopCS = dbo.fnGetSafeDivisionResult (
                                          NonDESum.TotalOONAfterMoopCS * @NonDE + DESum.TotalOONAfterMoopCS * @DE
                                         ,@NonDE + @DE)
                   ,TotalAfterMoopCS = dbo.fnGetSafeDivisionResult (
                                       NonDESum.TotalINAfterMoopCS * @NonDE + DESum.TotalINAfterMoopCS * @DE, @NonDE + @DE)
                                       + dbo.fnGetSafeDivisionResult (
                                         NonDESum.TotalOONAfterMoopCS * @NonDE + DESum.TotalOONAfterMoopCS * @DE
                                        ,@NonDE + @DE)
        FROM        @NonDETotalCostShare NonDESum
       INNER JOIN   @DETotalCostShare DESum
               ON DESum.ForecastID = NonDESum.ForecastID
                  AND   DESum.IsBenefitYearCurrentYear = NonDESum.IsBenefitYearCurrentYear;

        RETURN;

    END;
GO
