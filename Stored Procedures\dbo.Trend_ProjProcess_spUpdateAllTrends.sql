SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: [Trend_ProjProcess_spUpdateAllTrends]
--
-- CREATOR: <PERSON>
--
-- CREATED DATE: Mar-04-2020
--
-- DESCRIPTION:   This SP updates trends used in the bid model for pricing            
--              
-- PARAMETERS:
--  Input  :	@PlanInfoID
--              @LastUpdateByID
--              @WriteIndicator
--              @Result
--
--  Output : NONE
--
-- TABLES : Read :  
--					SavedPlanInfo
--                  Trend_ProjProcess_CalcPlanTrendsFinal
--                  		
--          Write:  
--
--
-- VIEWS: Read: 
--
-- STORED PROCS: Executed: 
--Trend_ProjProcess_spCalcNotPlanLevel_Trends
--Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault
--Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides
--Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal
--Trend_ProjProcess_spCalcPlanTrends_RepCat
--Trend_ProjProcess_spCalcPlanTrends_BenCat
--Trend_ProjProcess_spCalcPlanAdjmt_RepCat_PlanExp
--Trend_ProjProcess_spCalcPlanTrendsFinal
--spUpdatePricingTrends
--spAppAddLogEntry
--
-- FUNCTIONS: fnGetBidYear
--            Trend_fnCalcStringToTable
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MAR-04-2020      1           Initial Version                                                     Brent Osantowski
-- MAY-20-2020      2           Update content with IT SP                                           Manisha Tyagi
-- June-03-2020     3           Passed null to WriteIndicator parameter                             Manisha Tyagi
-- June-04-2020     4           Pass NULL PlanList and header cleanup                               Brent Osantowski
-- APR-10-2023		5			Changed #PlanInfoList to be VARCHAR(6) rather than Char(4) now  
--								that planinfoID's have reached 5 characters and was causing errors	Stephen Stempky
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spUpdateAllTrends]
 @PlanList VARCHAR(MAX) = NULL,
 @LastUpdateByID CHAR(7) ,
 @WriteIndicator TINYINT , 
 @Result BIT OUT 
AS
BEGIN  
IF OBJECT_ID('tempdb..#PlanInfoList') IS NOT NULL 
BEGIN
DROP TABLE #PlanInfoList
END
CREATE TABLE #PlanInfoList
(
PlanInfoID VARCHAR(6) NOT NULL
)

DECLARE @PlanInfoIDList VARCHAR(MAX)='',   @PlanYearID SMALLINT  
SELECT @PlanYearID = dbo.fnGetBidYear()  

INSERT INTO #PlanInfoList
SELECT DISTINCT SP.PlanInfoID
 FROM dbo.SavedPlanInfo SP
 WHERE SP.PlanYear=@PlanYearID 
  AND (SP.CPS IN (SELECT VALUE FROM dbo.fnStringSplit(@PlanList,',')) OR @PlanList IS NULL)

 SELECT @PlanInfoIDList = CASE WHEN @PlanInfoIDList='' THEN PlanInfoID ELSE @PlanInfoIDList + coalesce(',' + PlanInfoID, '')  END from #PlanInfoList 

BEGIN TRANSACTION;
BEGIN TRY
BEGIN
EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_Trends @LastUpdateByID

EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault @PlanInfoIDList,@LastUpdateByID

EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides @PlanInfoIDList,@LastUpdateByID

EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal @PlanInfoIDList,@LastUpdateByID 

EXEC dbo.Trend_ProjProcess_spCalcPlanTrends_RepCat @PlanInfoIDList,@WriteIndicator,@LastUpdateByID

EXEC dbo.Trend_ProjProcess_spCalcPlanTrends_BenCat @PlanInfoIDList,@LastUpdateByID 

EXEC dbo.Trend_ProjProcess_spCalcPlanAdjmt_RepCat_PlanExp @PlanInfoIDList,@WriteIndicator,@LastUpdateByID

EXEC dbo.Trend_ProjProcess_spCalcPlanTrendsFinal @PlanInfoIDList, @LastUpdateByID 

EXEC dbo.spUpdatePricingTrends @PlanInfoIDList,@LastUpdateByID 
END
SET @Result = 1;
COMMIT TRANSACTION;

END TRY
BEGIN CATCH
ROLLBACK TRANSACTION
SET @Result = 0;

DECLARE @ErrorMessage NVARCHAR(4000);  
DECLARE @ErrorSeverity INT;  
DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
---Insert into app log for logging error------------------
Exec [dbo].[spAppAddLogEntry] @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID;

END CATCH;  
END

GO
