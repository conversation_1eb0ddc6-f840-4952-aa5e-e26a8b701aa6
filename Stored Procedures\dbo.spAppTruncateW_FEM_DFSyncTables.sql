﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =======================================================================================================================
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppTruncateW_FEM_DFSyncTables
--
-- AUTHOR: 	SET NOCOUNT ON;
--
-- CREATED DATE: 2022 October 12 
--
-- DESCRIPTION: Procedure responsible for trucating data from that will be executed to truncate the two PREP base data tables
--- (i) W_FEM_SavedCUFinance_DFSync] and (ii) [W_FEM_SavedCUClaims_DFSync]        

-- TYPE: Same
--
-- PARAMETERS:
--    Input:


-- TABLES: 
--    Read:
--    W_FEM_SavedCUFinance_DFSync
---   W_FEM_SavedCUClaims_DFSync
--    Write:
--       
--   

-- VIEWS:
--
-- FUNCTIONS:
--    
      
-- STORED PROCS:
--   

      
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE                       VERSION           CHANGES MADE             DEVELOPER         
-- ----------------------------------------------------------------------------------------------------------------------
-- 2022 October 12              1                                          Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------
-- =========================================================================================================================

CREATE PROCEDURE spAppTruncateW_FEM_DFSyncTables


WITH EXECUTE AS OWNER
AS  
BEGIN


    TRUNCATE TABLE dbo.W_FEM_SavedCUClaims_DFSync

    TRUNCATE TABLE dbo.W_FEM_SavedCUFinance_DFSync
	
 END
GO


