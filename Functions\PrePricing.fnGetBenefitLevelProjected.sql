SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [PrePricing].[fnGetB<PERSON>fitLevelProjected]   ******/
-------------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnGetBenefitLevelProjected  
--  
-- AUTHOR: <PERSON><PERSON> 
--  
-- CREATED DATE: 2024-Nov-19  
-- HEADER UPDATED: 2024-Nov-19  
--  
-- DESCRIPTION: To get Plan level Projected Benefits Information.
--  
-- PARAMETERS:  
--  Input:  
--         
--  Output:  
--  @Results
-- TABLES:  
--  Read: 
--	prepricing.MarketInputValue
--  prepricing.MarketInputSubCategory
--	prepricing.PlanInfo
--	dbo.SavedPlanInfo 
--	dbo.SavedForecastSetup
--  dbo.Benefits_IsDeductibleApplies
--  dbo.LkpIntBenefitCategory
--  Write:  
--  
-- VIEWS:  
--        
--  
-- FUNCTIONS:  
--  dbo.fnGetBidYear()
-- 
-- STORED PROCS:  
--  
-- HISTORY:  
-- ---------------------------------------------------------------------------------------------
-- DATE        VERSION      CHANGES MADE										DEVELOPER 
------------------------------------------------------------------------------------------------  
-- 2024-Nov-25  1			Initial Version										Surya Murthy
-- 2025-Feb-27  2			Cost share logic added for ordinal 1				Surya Murthy
-- 2025-Feb-28  3			Replacing JOIN with LEFT JOIN						Surya Murthy
-- 2025-Mar-05  4			CPS Logic Added										Surya Murthy
-- 2025-Mar-05  5			Cost Share type Logic Added							Surya Murthy
-- 2025-Apr-21  6			invalid Deductible type handling					Adam Gilbert
-- ---------------------------------------------------------------------------------------------
CREATE FUNCTION [PrePricing].[fnGetBenefitLevelProjected] 
(  
)  
RETURNS @Results TABLE  
(    
	[BenefitYearID] INT NOT NULL, 
	[ForecastID] INT NOT NULL, 
	[CPS] VARCHAR(13) NULL, 
	[BenefitCategoryID] INT NOT NULL, 
	[BenefitCategoryName] VARCHAR(200) NULL, 
	[BenefitOrdinalID] INT NULL, 
	[INBundleID] INT NULL, 
	[INBenefitDayRange] INT NULL, 
	[INDedApplies]  INT NULL, 
	[INCoinsurance] VARCHAR(100) NULL, 
	[INPerAdmitCopay] VARCHAR(100) NULL, 
	[INCopay] VARCHAR(100) NULL, 
	[OONBundleID] INT NULL, 
	[OONBenefitDayRange] INT NULL, 
	[OONDedApplies] INT NULL, 
	[OONCoinsurance] VARCHAR(100) NULL, 
	[OONPerAdmitCopay] VARCHAR(100) NULL, 
	[OONCopay] VARCHAR(100) NULL 
) 
AS  
BEGIN  
   WITH 
	innetworkforcat72 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[72] AS invalue,[77] AS indayrangebegin, [78] AS indayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue		
			FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(72,77,78) AND  a.INCostShareType IS NOT NULL	
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([72],[77],[78])
		) AS pvt
	),
	OutOfNetworkforcat72 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[72] AS onnvalue,[77] AS oondayrangebegin, [78] AS oondayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(72,77,78)	AND  a.OONCostShareType IS NOT NULL		 
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid IN([72],[77],[78])
		) AS pvt
	),
	innetworkforcat73 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[73] AS invalue,[79] AS indayrangebegin, [80] AS indayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(73,79,80) 
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([73],[79],[80])
		) AS pvt
	),
	OutOfNetworkforcat73 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[73] AS onnvalue,[79] AS oondayrangebegin, [80] AS oondayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(73,79,80)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([73],[79],[80])
		) AS pvt
	),
	innetworkforcat74 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[74] AS invalue,[81] AS indayrangebegin, [82] AS indayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(74,81,82)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([74],[81],[82])
		) AS pvt
	),
	OutOfNetworkforcat74 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[74] AS onnvalue,[81] AS oondayrangebegin, [82] AS oondayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(74,81,82)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([74],[81],[82])
		) AS pvt
	),
	innetworkforcat75 AS (
	SELECT 
		planinfoid,INCostShareType, pvt.ABBenefitCategoryID,[75] AS invalue,[83] AS indayrangebegin, [84] AS indayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(75,83,84)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([75],[83],[84])
		) AS pvt
	),
	OutOfNetworkforcat75 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[75] AS onnvalue,[83] AS oondayrangebegin, [84] AS oondayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(75,83,84)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([75],[83],[84])
		) AS pvt
	),
	innetworkforcat85 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[85] AS invalue,[90] AS indayrangebegin, [91] AS indayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue		
			FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(85,90,91) AND  a.INCostShareType IS NOT NULL	
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([85],[90],[91])
		) AS pvt
	),
	OutOfNetworkforcat85 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[85] AS onnvalue,[90] AS oondayrangebegin, [91] AS oondayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(85,90,91) AND  a.OONCostShareType IS NOT NULL				 
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid IN([85],[90],[91])
		) AS pvt
	),
	innetworkforcat86 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[86] AS invalue,[92] AS indayrangebegin, [93] AS indayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(86,92,93)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([86],[92],[93])
		) AS pvt
	),
	OutOfNetworkforcat86 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[86] AS onnvalue,[92] AS oondayrangebegin, [93] AS oondayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(86,92,93)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([86],[92],[93])
		) AS pvt
	),
	innetworkforcat87 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[87] AS invalue,[94] AS indayrangebegin, [95] AS indayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(87,94,95)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([87],[94],[95])
		) AS pvt
	),
	OutOfNetworkforcat87 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[87] AS onnvalue,[94] AS oondayrangebegin, [95] AS oondayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(87,94,95)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([87],[94],[95])
		) AS pvt
	),
	innetworkforcat88 AS (
	SELECT 
		planinfoid,INCostShareType, pvt.ABBenefitCategoryID,[88] AS invalue,[96] AS indayrangebegin, [97] AS indayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(88,96,97)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([88],[96],[97])
		) AS pvt
	),
	OutOfNetworkforcat88 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[88] AS onnvalue,[96] AS oondayrangebegin, [97] AS oondayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(88,96,97)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([88],[96],[97])
		) AS pvt
	),
	innetworkforcat98 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[98] AS invalue,[103] AS indayrangebegin, [104] AS indayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue		
			FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(98,103,104) AND  a.INCostShareType IS NOT NULL	
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([98],[103],[104])
		) AS pvt
	),
	OutOfNetworkforcat98 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[98] AS onnvalue,[103] AS oondayrangebegin, [104] AS oondayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(98,103,104)	AND  a.OONCostShareType IS NOT NULL			 
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid IN([98],[103],[104])
		) AS pvt
	),
	innetworkforcat99 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[99] AS invalue,[105] AS indayrangebegin, [106] AS indayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(99,105,106)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([99],[105],[106])
		) AS pvt
	),
	OutOfNetworkforcat99 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[99] AS onnvalue,[105] AS oondayrangebegin, [106] AS oondayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(99,105,106)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([99],[105],[106])
		) AS pvt
	),
	innetworkforcat100 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[100] AS invalue,[107] AS indayrangebegin, [108] AS indayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(100,107,108)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([100],[107],[108])
		) AS pvt
	),
	OutOfNetworkforcat100 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[100] AS onnvalue,[107] AS oondayrangebegin, [108] AS oondayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(100,107,108)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([100],[107],[108])
		) AS pvt
	),
	innetworkforcat101 AS (
	SELECT 
		planinfoid,INCostShareType, pvt.ABBenefitCategoryID,[101] AS invalue,[109] AS indayrangebegin, [110] AS indayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(101,109,110)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([101],[109],[110])
		) AS pvt
	),
	OutOfNetworkforcat101 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[101] AS onnvalue,[109] AS oondayrangebegin, [110] AS oondayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(101,109,110)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([101],[109],[110])
		) AS pvt
	),
	innetworkforcat111 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[111] AS invalue,[116] AS indayrangebegin, [117] AS indayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue		
			FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(111,116,117) AND  a.INCostShareType IS NOT NULL	
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([111],[116],[117])
		) AS pvt
	),
	OutOfNetworkforcat111 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[111] AS onnvalue,[116] AS oondayrangebegin, [117] AS oondayrangeend, 1 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(111,116,117) AND  a.OONCostShareType IS NOT NULL				 
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid IN([111],[116],[117])
		) AS pvt
	),
	innetworkforcat112 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[112] AS invalue,[118] AS indayrangebegin, [119] AS indayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(112,118,119)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([112],[118],[119])
		) AS pvt
	),
	OutOfNetworkforcat112 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[112] AS onnvalue,[118] AS oondayrangebegin, [119] AS oondayrangeend, 2 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(112,118,119)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([112],[118],[119])
		) AS pvt
	),
	innetworkforcat113 AS (
	SELECT 
		planinfoid, INCostShareType,pvt.ABBenefitCategoryID,[113] AS invalue,[120] AS indayrangebegin, [121] AS indayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(113,120,121)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([113],[120],[121])
		) AS pvt
	),
	OutOfNetworkforcat113 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[113] AS onnvalue,[120] AS oondayrangebegin, [121] AS oondayrangeend, 3 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(113,120,121)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([113],[120],[121])
		) AS pvt
	),
	innetworkforcat114 AS (
	SELECT 
		planinfoid,INCostShareType, pvt.ABBenefitCategoryID,[114] AS invalue,[122] AS indayrangebegin, [123] AS indayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as INCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(invalue,'') AS invalue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(114,122,123)
		) p
		PIVOT
		(
			MAX(p.invalue) FOR p.subcategoryid IN([114],[122],[123])
		) AS pvt
	),
	OutOfNetworkforcat114 AS (
	SELECT 
		planinfoid, OONCostShareType,pvt.ABBenefitCategoryID,[114] AS onnvalue,[122] AS oondayrangebegin, [123] AS oondayrangeend, 4 AS ordinallevel
		FROM 
		(
			SELECT  a.PlanInfoID,null as OONCostShareType,a.subcategoryid,b.ABBenefitCategoryID ,ISNULL(a.OONValue,'') AS OONValue FROM prepricing.MarketInputValue a WITH(NOLOCK) 
			JOIN prepricing.MarketInputSubCategory b WITH(NOLOCK) ON b.SubCategoryID=a.SubcategoryID AND b.SubCategoryID IN(114,122,123)
		) p
		PIVOT
		(
			MAX(p.OONValue) FOR p.subcategoryid  IN([114],[122],[123])
		) AS pvt
	),
	othercats AS(
	SELECT  planinfoid,INCostShareType,sc.ABBenefitCategoryID, ISNULL(invalue,'') AS invalue , '' AS indayrangebegin,'' AS indayrangeend,1 AS ordinallevel,
			ISNULL(OONValue,'') AS OONValue , '' AS oondayrangebegin,'' AS oondayrangeend,OONCostShareType,mi.SubcategoryID
			FROM prepricing.MarketInputValue mi WITH(NOLOCK)
			JOIN prepricing.MarketInputSubCategory sc WITH(NOLOCK)  ON mi.SubcategoryID =sc.SubCategoryID 
			WHERE sc.CategoryID IN (4,8) AND sc.ABBenefitCategoryID NOT IN (65,66,131,83)		
	)
	, combined AS (
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,72 as ParentSubcat FROM innetworkforcat72 Inn LEFT JOIN OutOfNetworkforcat72 oon ON inn.PlanInfoID = oon.planinfoid	
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,73 as ParentSubcat FROM innetworkforcat73 Inn LEFT JOIN OutOfNetworkforcat73 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,74 as ParentSubcat FROM innetworkforcat74 Inn LEFT JOIN OutOfNetworkforcat74 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,75 as ParentSubcat FROM innetworkforcat75 Inn LEFT JOIN OutOfNetworkforcat75 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,85 as ParentSubcat FROM innetworkforcat85 Inn LEFT JOIN OutOfNetworkforcat85 oon ON inn.PlanInfoID = oon.planinfoid	
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,86 as ParentSubcat FROM innetworkforcat86 Inn LEFT JOIN OutOfNetworkforcat86 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,87 as ParentSubcat FROM innetworkforcat87 Inn LEFT JOIN OutOfNetworkforcat87 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,88 as ParentSubcat FROM innetworkforcat88 Inn LEFT JOIN OutOfNetworkforcat88 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,98 as ParentSubcat FROM innetworkforcat98 Inn LEFT JOIN OutOfNetworkforcat98 oon ON inn.PlanInfoID = oon.planinfoid	
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,99 as ParentSubcat FROM innetworkforcat99 Inn LEFT JOIN OutOfNetworkforcat99 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,100 as ParentSubcat FROM innetworkforcat100 Inn LEFT JOIN OutOfNetworkforcat100 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,101 as ParentSubcat FROM innetworkforcat101 Inn LEFT JOIN OutOfNetworkforcat101 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,111 as ParentSubcat FROM innetworkforcat111 Inn LEFT JOIN OutOfNetworkforcat111 oon ON inn.PlanInfoID = oon.planinfoid	
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,112 as ParentSubcat FROM innetworkforcat112 Inn LEFT JOIN OutOfNetworkforcat112 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,113 as ParentSubcat FROM innetworkforcat113 Inn LEFT JOIN OutOfNetworkforcat113 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT inn.*, oon.onnvalue,oon.oondayrangebegin,oon.oondayrangeend,oon.OONCostShareType,114 as ParentSubcat FROM innetworkforcat114 Inn LEFT JOIN OutOfNetworkforcat114 oon ON inn.PlanInfoID = oon.planinfoid
		UNION
		SELECT * FROM othercats
	), 
	-- Select the cost share type from the main benefit record. Ignore types that are set for the begin/end ranges.
	costShareFix AS (
	SELECT c.PlanInfoID,
           mi.INCostShareType ,
           c.ABBenefitCategoryID,
           c.invalue,
           c.indayrangebegin,
           c.indayrangeend,
           c.ordinallevel,
           c.onnvalue,
           c.oondayrangebegin,
           c.oondayrangeend,
		   mi.OONCostShareType
	FROM combined c
	LEFT JOIN PrePricing.MarketInputValue mi 
	ON c.PlanInfoID = mi.planinfoid 
	AND c.ParentSubcat = mi.SubcategoryID
	),
	dedType AS (
		SELECT PlanInfoID,INValue AS DeductTypeDesc FROM prepricing.MarketInputValue WHERE SubcategoryID=4
	)
	INSERT @Results
	SELECT d.PlanYear AS BenefitYearID,
	d.ForecastID AS ForecastID,
	c.CPS AS CPS,
	combined.ABBenefitCategoryID AS BenefitCategoryID,
	e.BenefitCategoryName AS BenefitCategoryName,
	combined.ordinallevel AS BenefitOrdinalID,
	0 AS INBundleID,
	CASE WHEN combined.indayrangeend ='' THEN NULL ELSE combined.indayrangeend END AS INBenefitDayRange,
	CASE WHEN (indedapplies.IsDeductibleApplies IS NULL) THEN NULL WHEN (indedapplies.IsDeductibleApplies='TRUE') THEN 1 ELSE 0 END AS INDedApplies,
	CASE WHEN(combined.INCostShareType=1)THEN combined.invalue ELSE '' END AS INCoinsurance,
	CASE WHEN(combined.INCostShareType=3)THEN combined.invalue ELSE '' END AS INPerAdmitCopay,
	CASE WHEN(combined.INCostShareType IN(2,5))THEN combined.invalue ELSE '' END AS INCopay,
	0 AS OONBundleID,
	CASE WHEN combined.oondayrangeend ='' THEN NULL ELSE combined.oondayrangeend END AS OONBenefitDayRange,
	CASE WHEN (oondedapplies.IsDeductibleApplies IS NULL) THEN NULL WHEN (oondedapplies.IsDeductibleApplies='TRUE') THEN 1 ELSE 0 END AS OONDedApplies,	
	CASE WHEN(combined.OONCostShareType=1)THEN combined.onnvalue ELSE '' END AS OONCoinsurance,
	CASE WHEN(combined.OONCostShareType=3)THEN combined.onnvalue ELSE '' END AS OONPerAdmitCopay,
	CASE WHEN(combined.OONCostShareType IN(2,5))THEN combined.onnvalue ELSE '' END AS OONCopay
	FROM CostShareFix AS combined 
	JOIN dedType  ON dedType.PlanInfoID = combined.PlanInfoID
	LEFT JOIN dbo.Benefits_IsDeductibleApplies indedapplies WITH(NOLOCK)  ON indedapplies.DeductTypeDesc=dedType.DeductTypeDesc AND indedapplies.BenefitCategoryID=combined.ABBenefitCategoryID AND indedapplies.IsOON=0
	LEFT JOIN dbo.Benefits_IsDeductibleApplies oondedapplies WITH(NOLOCK)  ON oondedapplies.DeductTypeDesc=dedType.DeductTypeDesc AND oondedapplies.BenefitCategoryID=combined.ABBenefitCategoryID AND oondedapplies.IsOON=1
	JOIN prepricing.PlanInfo b WITH(NOLOCK)  ON b.PlanInfoID=combined.PlanInfoID
	JOIN dbo.SavedPlanInfo c WITH(NOLOCK)  ON c.CPS=b.CPS AND c.PlanYear=b.PlanYear
	JOIN dbo.SavedForecastSetup d  WITH(NOLOCK) ON d.PlanInfoID=c.PlanInfoID
	JOIN dbo.LkpIntBenefitCategory e WITH(NOLOCK)  ON e.BenefitCategoryID=combined.ABBenefitCategoryID 
	WHERE	
	b.PlanYear=dbo.fnGetBidYear()
	ORDER BY d.ForecastID,e.BenefitCategoryID,BenefitOrdinalID 		

	--Remove benefits with no values.
	DELETE FROM @Results WHERE INCoinsurance = '' AND INPerAdmitCopay ='' AND INCopay = '' AND OONCoinsurance = '' AND OONPerAdmitCopay ='' AND OONCopay = ''


	RETURN  
END
GO
