SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO


-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: fnIsSafeHarborExemption
--
-- AUTHOR: Joe Casey 
--
-- CREATED DATE: 2011-Jan-06
-- HEADER UPDATED: 2011-Jan-06
--
-- DESCRIPTION: Determines whether or not a plan qualifies for the safe harbor exemption.
--
-- PARAMETERS:
--	Inputs:
--      @ForecastID
--
-- TABLES:
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnPlanCountyProjectedMemberMonths
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION	    CHANGES MADE														DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-06		1		    Initial Version														Joe Casey
-- 2010-Feb-08      2           Updated to return safe harbor for 100% Non-DE#                      Casey Sanders
-- 2015-Jul-22		3			Added a lower percentage of 10% and added to conditional statment	Mark Freel
-- 2023-Aug-02		4			Added @XVariables and Object Schema									Sheetal Patil 
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnIsSafeHarborExemption]
    (
	@ForecastID INT
    )
RETURNS BIT AS  
BEGIN 
    DECLARE @UpperPercentLimit DECIMAL(8,7),
            @LowerPercentLimit DECIMAL(8,7),
            @IsSafeHarbor BIT,
            @ProjDE#MMPercent DECIMAL(8,7),
			@XForecastID INT = @ForecastID 
    -------------------------------------------------------------            
    --Set these percentages when safe harbor regulations change--
    SET @UpperPercentLimit = .9
    SET @LowerPercentLimit = .1
    
    -------------------------------------------------------------
    -------------------------------------------------------------

    SELECT @ProjDE#MMPercent = dbo.fnGetSafeDivisionResult(
                                SUM(ISNULL(DE.ProjectedMemberMonths,0))
                                ,SUM(ISNULL(Total.ProjectedMemberMonths,0)))
    FROM dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,1) DE
    INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,2) Total
        ON Total.ForecastID = DE.ForecastID
        AND Total.StateTerritoryID = DE.StateTerritoryID
        AND Total.CountyCode = DE.CountyCode
    
    --Adding precision to the upper limit to make sure the value is contained within 7 decimal places.
    --Between is like @Lower <= @ProjDE# < @Upper.  Adding precision makes it @Lower <= @ProjDE# <= @Upper
    IF (@ProjDE#MMPercent > @UpperPercentLimit) OR (@ProjDE#MMPercent < @LowerPercentLimit) 
        SET @IsSafeHarbor = 1
    ELSE
        SET @IsSafeHarbor = 0

    RETURN @IsSafeHarbor

END
GO
