SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnBenefitDataForClaimTool]    ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnBenefitDataForClaimTool
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE: 2008-Jan-28
-- HEADER UPDATED: 2010-Oct-07
--
-- DESCRIPTION: Returns added benefit and benefit factor values for the
--              specified plan.  This is used for claim tool output in the
--              MAPD model.  These values are based on the parent plan
--              (from the previous plan year).
--
-- PARAMETERS:
--  Input: 
--         @ForecastID - The index of the projected plan.
--         
-- RETURNS: 
--
-- TABLES: 
--  Read:  Saved<PERSON><PERSON><PERSON>eader
--  Write: None
--
-- VIEWS:
--  Read: None.
--
-- FUNCTIONS:
--  Read:   fnAppGetAddedBenefits
--          fnAppGetBidSummary
--  Called: None
--
-- STORED PROCS: 
--  Executed: None.
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE       VERSION   CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-Jan-28      1   Initial version.                                                    Tonya Cockrell 
-- 2010-Apr-7		2	Added 2011 specific logic to pull revised benefit factor            Casey Sanders
-- 2010-Oct-06      3   Updated to 2012 structure                                           Nate Jacoby
-- 2011-Jun-02		4	Replaced LkpIntPlanYear with dbo.fnGetBidYear()						Bobby Jaegers
-- 2011-Jun-14		5	Changed @PlanYearID to return SMALLINT instead of INT				Bobby Jaegers
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnBenefitDataForClaimTool]
(
    @ForecastID INT
)

RETURNS @Results TABLE
(
    AddedBenefits DECIMAL(14,6) NULL,
    BenefitFactor DECIMAL (8,6) NULL
)
AS
    BEGIN
-------------------------------------------------------------------------------------------------------------------------    
-- Declarations ---------------------------------------------------------------------------------------------------------
    DECLARE @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear()

    DECLARE @AddedBenefits DECIMAL (14,6)
	DECLARE @BenefitFactor DECIMAL (8,6)
-------------------------------------------------------------------------------------------------------------------------    
-- Define @AddedBenefits ------------------------------------------------------------------------------------------------


        SELECT @AddedBenefits
	        = SUM
	        (
		        CASE
			        WHEN InAddedBenefitAllowed IS NULL
				        THEN 0
			        ELSE InAddedBenefitAllowed
		        END
	        )
	        - SUM
	        (
		        CASE
			        WHEN InAddedBenefitCostShare IS NULL
				        THEN 0
			        ELSE InAddedBenefitCostShare
		        END
	        )
	        + SUM
	        (
		        CASE
			        WHEN OONAddedBenefitAllowed IS NULL
				        THEN 0
			        ELSE OONAddedBenefitAllowed
		        END
	        )
	        - SUM
	        (
		        CASE
			        WHEN OONAddedBenefitCostShare IS NULL
				        THEN 0
			        ELSE OONAddedBenefitCostShare
		        END
	        )
        FROM dbo.fnAppGetAddedBenefits (@ForecastID)
				
-------------------------------------------------------------------------------------------------------------------------    
-- Define @BenefitFactor ------------------------------------------------------------------------------------------------		    
		    
        SELECT
	        @BenefitFactor = ProjectedYearBenefitFactor 
        FROM dbo.fnAppGetBidSummary (@ForecastID)
  			
-------------------------------------------------------------------------------------------------------------------------    
-- Results Table --------------------------------------------------------------------------------------------------------

        INSERT @Results
            (
            AddedBenefits,
            BenefitFactor
            )
        
        SELECT
            @AddedBenefits,
            @BenefitFactor
     
     RETURN 
END
GO
