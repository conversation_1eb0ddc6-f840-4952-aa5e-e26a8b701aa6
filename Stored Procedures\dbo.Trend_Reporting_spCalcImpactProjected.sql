SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: dbo.Trend_Reporting_spCalcImpactProjected 
--
-- CREATOR: <PERSON> Lewis 
--
-- CREATED DATE: 2020-09-17
--
-- DESCRIPTION: Computes incremental impact to allowed and utilization of each component trend using membership 
--				that is allocated at the county level by Plan and RateType to be used in reporting
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--				@PlanInfoID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntBenefitCategory
--  			    LkpIntPlanYear
--					PerExtCMSValues
--					SavedForecastSetup
--					SavedRollupForecastMap
--					SavedRollupInfo
--					Trend_Data_CalcBaseCostAndUse
--				    Trend_Data_CalcBaseMembership
--				    Trend_ProjProcess_CalcPlanTrendsFinal
--					Trend_Reporting_CalcCountyXWalkMembership
--					Trend_Reporting_CalcImpactProjected_forRollup
--				    Trend_Reporting_CalcProjectedMembership 
--				    Trend_SavedComponentInfo
--					
--          Write:	Trend_Reporting_CalcImpactProjected_forPlan
--					Trend_Reporting_CalcImpactProjected_forRollup 
--                  Trend_Reporting_Log
--
-- VIEWS: Read: vwSAMCrosswalks
--				vwPlanInfo
--
-- FUNCTIONS:		Trend_fnCalcStringToTable
--					Trend_fnSafeDivide
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																									DEVELOPER  
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- SEP-17-2020			1		Initial Version																									Jake Lewis
-- SEP-21-2020			2		Updated reported MM queries.  Need to account for plans that segemented											Jake Lewis
--								in the bid year and had a plan to plan renewal in the current year. 
-- OCT-19-2020			3		Fixed issues with sonar qube																					Deepali Mittal
-- NOV-04-2020			4		Added MemberMonths to output																					Jake Lewis
--								Shifted component order for 'Membership'
--								Renamed 'Base' component to 'Projection Base' and modified incurred date
-- NOV-12-2020			5		Set MM=0 for all components and years when no MM exists in the Projection Base									Jake Lewis
-- DEC-10-2020			6		Name changes for the SP and some of the referenced tables:														Jake Lewis
--								NEW SP = Trend_Reporting_spCalcImpactProjected, OLD SP = Trend_Reporting_spCalcImpactProjected_CountyXWalk
--								NEW = Trend_Reporting_CalcImpactProjected_forRollup, OLD = Trend_Reporting_CalcImpactProjected_CountyXWalk
-- DEC-14-2020			7		Added code to write to Trend_Reporting_CalcImpactProjected_forPlan												Jake Lewis
--								This populates a new tab in the Trend Impact Report 
-- MAR-15-2021			8		Code adjustments to improve efficiency and decrease runtime														Jake Lewis
--								Add PlanInfoID input parameter
-- Aug-03-2023          9       Added NOLOCK, Inner variables and using temp table to store result                                              Sheetal Patil    
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_Reporting_spCalcImpactProjected]

@LastUpdateByID CHAR(7)
,@PlanInfoID    VARCHAR(MAX) = NULL

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

		DECLARE @XLastUpdateByID CHAR(7) = @LastUpdateByID,
                @XPlanInfoID VARCHAR(MAX) = @PlanInfoID		
        DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            ---------------------------------------------------------------
            -- A. Populate Trend_Reporting_CalcImpactProjected_forRollup --
            ---------------------------------------------------------------

            ---------------------------------------------------------------------
            -- A0. Declare / set variables and pull parameters and source data --
            ---------------------------------------------------------------------

            -- Variables
            DECLARE @ExperienceYear INT = (SELECT   PlanYearID FROM dbo.LkpIntPlanYear WITH (NOLOCK) WHERE IsExperienceYear = 1);
            DECLARE @CurrentYear INT = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WITH (NOLOCK) WHERE IsCurrentYear = 1);
            DECLARE @BidYear INT = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WITH (NOLOCK) WHERE IsBidYear = 1);
            DECLARE @ProjectionBaseComponentOrder INT = 0;
            DECLARE @ProjectionBaseComponentReporting VARCHAR(50) = 'Projection Base';
            DECLARE @MembershipComponentOrder INT = 1;
            DECLARE @MembershipComponentReporting VARCHAR(50) = 'Membership';
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);

            -- Cost and use
            IF (SELECT  OBJECT_ID ('tempdb..#CostAndUse')) IS NOT NULL
                DROP TABLE #CostAndUse;
            SELECT      PlanInfoID
                       ,RateType
                       ,IncurredMonth / 100 AS TrendYearID
                       ,BenefitCategoryID
                       ,SUM (ISNULL (Allowed, 0)) AS Allowed
                       ,SUM (ISNULL (Utilization, 0)) AS Utilization
            INTO        #CostAndUse
            FROM        dbo.Trend_Data_CalcBaseCostAndUse WITH (NOLOCK)
            WHERE       IncurredMonth / 100 = @ExperienceYear
            GROUP BY    IncurredMonth / 100
                       ,PlanInfoID
                       ,RateType
                       ,BenefitCategoryID;

            -- Claim category mapping
            IF (SELECT  OBJECT_ID ('tempdb..#ClaimCatMap')) IS NOT NULL
                DROP TABLE #ClaimCatMap;
            SELECT  ReportingCategory
                   ,BenefitCategoryID
            INTO    #ClaimCatMap
            FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
            WHERE   ReportingCategory IS NOT NULL;

            -- Component order
            IF (SELECT  OBJECT_ID ('tempdb..#ComponentOrder')) IS NOT NULL
                DROP TABLE #ComponentOrder;
            SELECT      ComponentOrder + @MembershipComponentOrder AS ComponentOrder    -- Membership is first, so shift other components
                       ,ComponentReporting
            INTO        #ComponentOrder
            FROM        dbo.Trend_SavedComponentInfo WITH (NOLOCK)
            WHERE       Component NOT LIKE '%Rx%'   -- Exclude Rx components for historic impacts
            ORDER BY    ComponentOrder;

            -- Max order
            DECLARE @MaxComponentOrder INT = (SELECT    MAX (ComponentOrder) FROM  #ComponentOrder);

            -- Component trends 
            IF (SELECT  OBJECT_ID ('tempdb..#ComponentTrend')) IS NOT NULL
                DROP TABLE #ComponentTrend;
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,BenefitCategoryID
                   ,ComponentReporting
                   ,(1 + CostAdjustment) * (1 + UseAdjustment) - 1 AS AllowedAdjustment
                   ,UseAdjustment
            INTO    #ComponentTrend
            FROM    dbo.Trend_ProjProcess_CalcPlanTrendsFinal WITH (NOLOCK);

            -- RateType
            IF (SELECT  OBJECT_ID ('tempdb..#RateType')) IS NOT NULL
                DROP TABLE #RateType;
            SELECT  DISTINCT
                    number AS RateType
            INTO    #RateType
            FROM    master..spt_values
            WHERE   number BETWEEN 1 AND 2;

            -- Projection years
            IF (SELECT  OBJECT_ID ('tempdb..#Year')) IS NOT NULL DROP TABLE #Year;
            SELECT  PlanYearID AS TrendYearID
            INTO    #Year
            FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
            WHERE   IsCurrentYear = 1
                    OR  IsBidYear = 1;

            -- Plan List
            IF (SELECT  OBJECT_ID ('tempdb..#PlanList')) IS NOT NULL
                DROP TABLE #PlanList;
            SELECT      vsc.BidYearPlanInfoID
                       ,vsc.CurrentYearPlanInfoID
                       ,vsc.BaseYearPlanInfoID
                       ,vsc.BidYearRenewalTypeID
                       ,vsc.CurrentYearRenewalTypeID
            INTO        #PlanList
            FROM        dbo.vwSAMCrosswalks vsc WITH (NOLOCK)
           INNER JOIN   dbo.SavedForecastSetup sfs WITH (NOLOCK)
                   ON sfs.PlanInfoID = vsc.BidYearPlanInfoID
                      AND   sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID
           INNER JOIN   dbo.SavedRollupForecastMap map WITH (NOLOCK)
                   ON map.ForecastID = sfs.ForecastID
           INNER JOIN   dbo.SavedRollupInfo sri WITH (NOLOCK)
                   ON sri.RollupID = map.RollupID
            LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = sfs.PlanInfoID
            WHERE       sri.RollupName = 'Live'
                        AND vsc.BidYearPlanInfoID IS NOT NULL
                        AND vsc.BidYearRenewalTypeID <> 2 --Exclude plans that termed in the bid year
                        AND vpi.IsOffMAModel = 'No'
                        AND vpi.IsHidden = 0
                        AND vpi.Region NOT IN ('Unmapped')
            GROUP BY    vsc.BidYearPlanInfoID
                       ,vsc.CurrentYearPlanInfoID
                       ,vsc.BaseYearPlanInfoID
                       ,vsc.BidYearRenewalTypeID
                       ,vsc.CurrentYearRenewalTypeID;

	IF (SELECT OBJECT_ID('tempdb..#CalcStringToTable')) IS NOT NULL
	BEGIN
        DROP TABLE #CalcStringToTable;
    END

	CREATE TABLE #CalcStringToTable
	(
	[Val] [varchar](4000) NULL
	);

     INSERT INTO #CalcStringToTable
     SELECT   Val FROM dbo.Trend_fnCalcStringToTable (@XPlanInfoID, ',', 1)
    

            -- Distinct bid year PlanInfoIDs
            IF (SELECT  OBJECT_ID ('tempdb..#BidPlans')) IS NOT NULL
                DROP TABLE #BidPlans;
            SELECT  DISTINCT
                    BidYearPlanInfoID
            INTO    #BidPlans
            FROM    #PlanList
    	    WHERE   (BidYearPlanInfoID IN (SELECT   Val FROM #CalcStringToTable )          
                     OR @XPlanInfoID IS NULL);

            -- Bid year plans with segmentations
            IF (SELECT  OBJECT_ID ('tempdb..#BidSegPlans')) IS NOT NULL
                DROP TABLE #BidSegPlans;
            SELECT  DISTINCT
                    BidYearPlanInfoID
            INTO    #BidSegPlans
            FROM    #PlanList
            WHERE   BidYearRenewalTypeID IN ('3', '12', '13', '14', '15')
                    AND CurrentYearRenewalTypeID NOT IN ('3', '12', '13', '14', '15')
                    AND (BidYearPlanInfoID IN (SELECT   Val FROM #CalcStringToTable )
                         OR @XPlanInfoID IS NULL);

            -- Shell
            IF (SELECT  OBJECT_ID ('tempdb..#Shell')) IS NOT NULL DROP TABLE #Shell;
            SELECT      bp.BidYearPlanInfoID AS PlanInfoID
                       ,@ExperienceYear AS TrendYearID
                       ,rt.RateType
                       ,ccm.BenefitCategoryID
            INTO        #Shell
            FROM        #BidPlans bp
           INNER JOIN   #ClaimCatMap ccm
                   ON 1 = 1
           INNER JOIN   #RateType rt
                   ON 1 = 1;


            -------------------------
            -- A1. Base Membership --
            -------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#TempMembership')) IS NOT NULL
                DROP TABLE #TempMembership;
            CREATE TABLE #TempMembership
                (PlanInfoID            INT
                ,RateType              SMALLINT
                ,TrendYearID           INT
                ,MemberMonths          FLOAT
                ,ReportingMemberMonths FLOAT);

            -- RateType 1

            -- Plans with Bid Year Segmentations
            INSERT INTO #TempMembership
            SELECT      a.BidYearPlanInfoID AS PlanInfoID
                       ,1 AS RateType
                       ,@ExperienceYear AS TrendYearID
                       ,c.MemberMonths
                       ,dbo.Trend_fnSafeDivide (
                        SUM (ISNULL (c.ReportingMemberMonths, 0)) * SUM (ISNULL (b.ReportingMemberMonths, 0))
                       ,ISNULL (b.MemberMonths, 0)
                       ,0) AS ReportingMemberMonths --Apply the same ratio of reporting MM/total MM in the current year to the base year membership
            FROM        #PlanList a
            LEFT JOIN   dbo.Trend_Reporting_CalcCountyXWalkMembership b WITH (NOLOCK)
                   ON a.CurrentYearPlanInfoID = b.FromPlanInfoID
                      AND   a.BidYearPlanInfoID = b.PlanInfoID
            LEFT JOIN   dbo.Trend_Reporting_CalcCountyXWalkMembership c WITH (NOLOCK)
                   ON a.BaseYearPlanInfoID = c.FromPlanInfoID
                      AND   a.CurrentYearPlanInfoID = c.PlanInfoID
            WHERE       a.BidYearPlanInfoID IN (SELECT  BidYearPlanInfoID FROM  #BidSegPlans)
            GROUP BY    a.BidYearPlanInfoID
                       ,c.MemberMonths
                       ,b.MemberMonths;

            -- All Other Bid Year Plans
            INSERT INTO #TempMembership
            SELECT      a.BidYearPlanInfoID AS PlanInfoID
                       ,1 AS RateType
                       ,@ExperienceYear AS TrendYearID
                       ,b.MemberMonths
                       ,SUM (ISNULL (b.ReportingMemberMonths, 0)) AS ReportingMemberMonths
            FROM        #PlanList a
            LEFT JOIN   dbo.Trend_Reporting_CalcCountyXWalkMembership b WITH (NOLOCK)
                   ON a.BaseYearPlanInfoID = b.FromPlanInfoID
                      AND   a.CurrentYearPlanInfoID = b.PlanInfoID
            WHERE       a.BidYearPlanInfoID NOT IN (SELECT  BidYearPlanInfoID FROM  #BidSegPlans)
            GROUP BY    a.BidYearPlanInfoID
                       ,b.MemberMonths;

            -- RateType 2
            INSERT INTO #TempMembership
            SELECT      PlanInfoID AS PlanInfoID
                       ,RateType
                       ,IncurredMonth / 100 AS TrendYearID
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
                       ,SUM (ISNULL (MemberMonths, 0)) AS ReportingMemberMonths --Set membermonths the same for reporting and total membership
            FROM        dbo.Trend_Data_CalcBaseMembership WITH (NOLOCK)
            WHERE       RateType = 2
                        AND IncurredMonth / 100 = @ExperienceYear
            GROUP BY    PlanInfoID
                       ,RateType
                       ,IncurredMonth / 100;

            -- Base membership
            IF (SELECT  OBJECT_ID ('tempdb..#BaseMembership')) IS NOT NULL
                DROP TABLE #BaseMembership;
            CREATE TABLE #BaseMembership
                (PlanInfoID            INT      NOT NULL
                ,RateType              SMALLINT NOT NULL
                ,TrendYearID           INT      NOT NULL
                ,MemberMonths          FLOAT    NOT NULL
                ,ReportingMemberMonths FLOAT    NOT NULL);
            INSERT INTO #BaseMembership
            SELECT      PlanInfoID
                       ,RateType
                       ,TrendYearID
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
                       ,SUM (ISNULL (ReportingMemberMonths, 0)) AS ReportingMemberMonths
            FROM        #TempMembership
            GROUP BY    PlanInfoID
                       ,RateType
                       ,TrendYearID;


            ------------------------------
            -- A2. Projected Membership --
            ------------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#ProjectedMembership')) IS NOT NULL
                DROP TABLE #ProjectedMembership;
            CREATE TABLE #ProjectedMembership
                (PlanInfoID            INT      NOT NULL
                ,RateType              SMALLINT NOT NULL
                ,TrendYearID           INT      NOT NULL
                ,MemberMonths          FLOAT    NOT NULL
                ,ReportingMemberMonths FLOAT    NOT NULL);

            -- Current year
            INSERT INTO #ProjectedMembership
            SELECT      PlanInfoID
                       ,1 AS RateType
                       ,@CurrentYear AS TrendYearID
                       ,SUM (ISNULL (MemberMonths, 0)) AS Membermonths
                       ,SUM (ISNULL (ReportingMemberMonths, 0)) AS ReportingMemberMonths
            FROM        dbo.Trend_Reporting_CalcCountyXWalkMembership WITH (NOLOCK)
            GROUP BY    PlanInfoID;

            -- Bid year
            INSERT INTO #ProjectedMembership
            SELECT      PlanInfoID
                       ,RateType
                       ,TrendYearID
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
                       ,SUM (ISNULL (MemberMonths, 0)) AS ReportingMemberMonths --Set ReportingMemberMonths = MemberMonths for the bid year
            FROM        dbo.Trend_Reporting_CalcProjectedMembership WITH (NOLOCK)
            WHERE       TrendYearID = @BidYear
                        AND RateType = 1
            GROUP BY    PlanInfoID
                       ,TrendYearID
                       ,RateType;


            -------------------------------------
            -- A3. Get projection base metrics --
            -------------------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#Metrics')) IS NOT NULL DROP TABLE #Metrics;
            SELECT      0 AS IncrementControl
                       ,s.PlanInfoID
                       ,s.RateType
                       ,s.TrendYearID
                       ,s.BenefitCategoryID
                       ,@ProjectionBaseComponentReporting AS ComponentReporting
                       ,@ProjectionBaseComponentOrder AS ComponentOrder
                       ,dbo.Trend_fnSafeDivide (cu.Allowed, bm.MemberMonths, 0) AS AllowedPMPM
                       ,dbo.Trend_fnSafeDivide (cu.Utilization, bm.MemberMonths, 0) * 12000 AS UPTMPY
            INTO        #Metrics
            FROM        #Shell s
            LEFT JOIN   #CostAndUse cu
                   ON cu.PlanInfoID = s.PlanInfoID
                      AND   cu.RateType = s.RateType
                      AND   cu.TrendYearID = s.TrendYearID
                      AND   cu.BenefitCategoryID = s.BenefitCategoryID
            LEFT JOIN   #BaseMembership bm
                   ON bm.PlanInfoID = s.PlanInfoID
                      AND   bm.RateType = s.RateType
                      AND   bm.TrendYearID = s.TrendYearID;


            ---------------------------------------
            -- A4. Iterate over Component Trends --
            ---------------------------------------
            DECLARE @CurIncrementControl INT;
            DECLARE @CurComponentReporting VARCHAR(50);
            DECLARE @CurComponentOrder INT;
            DECLARE @CurTrendYearID INT;

            DECLARE Component_Cursor CURSOR LOCAL FAST_FORWARD FOR

            -- Cursor iterates over all components in current year
            SELECT  ROW_NUMBER () OVER (ORDER BY a.TrendYearID, a.ComponentOrder) AS IncrementControl
                   ,a.TrendYearID
                   ,a.ComponentOrder
                   ,a.ComponentReporting
            FROM    (SELECT     ComponentReporting
                               ,ComponentOrder
                               ,TrendYearID
                     FROM       #ComponentOrder
                    CROSS JOIN  #Year
                     UNION ALL
                     SELECT @MembershipComponentReporting AS ComponentReporting
                           ,@MembershipComponentOrder AS ComponentOrder
                           ,TrendYearID
                     FROM   #Year) a;

            -- Open cursor and begin iteration			  
            OPEN Component_Cursor;
            FETCH NEXT FROM Component_Cursor
            INTO @CurIncrementControl
                ,@CurTrendYearID
                ,@CurComponentOrder
                ,@CurComponentReporting;

            WHILE @@Fetch_Status = 0

                BEGIN

                    INSERT INTO #Metrics

                    SELECT      @CurIncrementControl AS IncrementControl
                               ,m.PlanInfoID
                               ,m.RateType
                               ,@CurTrendYearID AS TrendYearID
                               ,m.BenefitCategoryID
                               ,@CurComponentReporting AS ComponentReporting
                               ,@CurComponentOrder AS ComponentOrder
                               ,m.AllowedPMPM * (1 + ISNULL (ct.AllowedAdjustment, 0)) AS AllowedPMPM
                               ,m.UPTMPY * (1 + ISNULL (ct.UseAdjustment, 0)) AS UPTMPY
                    FROM        #Metrics m
                    LEFT JOIN   #ComponentTrend ct
                           ON ct.PlanInfoID = m.PlanInfoID
                              AND   ct.RateType = m.RateType
                              AND   ct.TrendYearID = @CurTrendYearID
                              AND   ct.BenefitCategoryID = m.BenefitCategoryID
                              AND   ct.ComponentReporting = @CurComponentReporting
                    WHERE       m.IncrementControl + 1 = @CurIncrementControl;

                    FETCH NEXT FROM Component_Cursor
                    INTO @CurIncrementControl
                        ,@CurTrendYearID
                        ,@CurComponentOrder
                        ,@CurComponentReporting;

                END;

            CLOSE Component_Cursor;
            DEALLOCATE Component_Cursor;


            --------------------------
            -- A5. Apply membership --
            --------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#Totals')) IS NOT NULL DROP TABLE #Totals;

            -- RateType 1
            SELECT      m.PlanInfoID
                       ,m.RateType
                       ,m.TrendYearID
                       ,m.BenefitCategoryID
                       ,m.ComponentReporting
                       ,m.ComponentOrder
                       ,COALESCE (bm.ReportingMemberMonths, pm.ReportingMemberMonths, 0) AS MemberMonths
                       ,m.AllowedPMPM * COALESCE (bm.ReportingMemberMonths, pm.ReportingMemberMonths, 0) AS Allowed
                       ,m.UPTMPY * COALESCE (bm.ReportingMemberMonths, pm.ReportingMemberMonths, 0) / 12000 AS Utilization
            INTO        #Totals
            FROM        #Metrics m
            LEFT JOIN   #BaseMembership bm
                   ON bm.PlanInfoID = m.PlanInfoID
                      AND   bm.RateType = m.RateType
                      AND   bm.TrendYearID = m.TrendYearID
            LEFT JOIN   #ProjectedMembership pm
                   ON pm.PlanInfoID = m.PlanInfoID
                      AND   pm.RateType = m.RateType
                      AND   pm.TrendYearID = m.TrendYearID
            WHERE       m.RateType = 1;

            -- RateType 2
            INSERT INTO #Totals
            SELECT      m.PlanInfoID
                       ,m.RateType
                       ,m.TrendYearID
                       ,m.BenefitCategoryID
                       ,m.ComponentReporting
                       ,m.ComponentOrder
                       ,ISNULL (bm.ReportingMemberMonths, 0) AS MemberMonths
                       ,m.AllowedPMPM * ISNULL (bm.ReportingMemberMonths, 0) AS Allowed
                       ,m.UPTMPY * ISNULL (bm.ReportingMemberMonths, 0) / 12000 AS Utilization
            FROM        #Metrics m
            LEFT JOIN   #BaseMembership bm
                   ON bm.PlanInfoID = m.PlanInfoID
                      AND   bm.RateType = m.RateType
                      AND   bm.TrendYearID = @ExperienceYear
            WHERE       m.RateType = 2;


            -- Get list of PlanInfoIDs with no membership for the projection base
            IF (SELECT  OBJECT_ID ('tempdb..#NoBaseMM')) IS NOT NULL
                DROP TABLE #NoBaseMM;
            SELECT  DISTINCT
                    PlanInfoID
                   ,RateType
            INTO    #NoBaseMM
            FROM    #Totals
            WHERE   ComponentReporting = @ProjectionBaseComponentReporting
                    AND MemberMonths = 0;


            ----------------------------------------
            -- A6. Clear table and insert results --
            ----------------------------------------
            -- If @XPlanInfoID input parameter is NULL, delete everything. 
            IF @XPlanInfoID IS NULL
                BEGIN
                    DELETE  FROM dbo.Trend_Reporting_CalcImpactProjected_forRollup WHERE    1 = 1;
                END;
            ELSE -- If @XPlanInfoID input parameter is not NULL, only delete records for which the PlanInfoID exists in @XPlanInfoID
                BEGIN
                    DELETE  t
                    FROM    dbo.Trend_Reporting_CalcImpactProjected_forRollup t
                    INNER JOIN    (SELECT Val AS PlanInfoID FROM #CalcStringToTable  ) p
                      ON p.PlanInfoID = t.PlanInfoID
                    WHERE   p.PlanInfoID IS NOT NULL;
                END;

            DECLARE @LastUpdateDateTime DATETIME = GETDATE ();

            IF (SELECT  OBJECT_ID ('tempdb..#OutputForRollup')) IS NOT NULL
                DROP TABLE #OutputForRollup;
            SELECT      t.PlanInfoID
                       ,t.RateType
                       ,CASE WHEN t.ComponentOrder = @ProjectionBaseComponentOrder THEN t.TrendYearID + 1
                             ELSE t.TrendYearID END AS TrendYearID
                       ,t.BenefitCategoryID
                       ,t.ComponentReporting
                       ,t.ComponentOrder
                       ,CASE WHEN b.PlanInfoID IS NULL THEN t.MemberMonths ELSE 0 END AS MemberMonths
                       ,CASE WHEN b.PlanInfoID IS NULL THEN t.Allowed ELSE 0 END AS Allowed
                       ,CASE WHEN b.PlanInfoID IS NULL THEN t.Utilization ELSE 0 END AS Utilization
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            INTO        #OutputForRollup
            FROM        #Totals t
            LEFT JOIN   #NoBaseMM b
                   ON b.PlanInfoID = t.PlanInfoID
                      AND   b.RateType = t.RateType;

            -- Write to table 
            INSERT INTO dbo.Trend_Reporting_CalcImpactProjected_forRollup
                (PlanInfoID
                ,RateType
                ,TrendYearID
                ,BenefitCategoryID
                ,ComponentReporting
                ,ComponentOrder
                ,MemberMonths
                ,Allowed
                ,Utilization
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,BenefitCategoryID
                   ,ComponentReporting
                   ,ComponentOrder
                   ,MemberMonths
                   ,Allowed
                   ,Utilization
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #OutputForRollup;



            -------------------------------------------------------------
            -- B. Populate Trend_Reporting_CalcImpactProjected_forPlan --
            -------------------------------------------------------------

            -----------------------------------
            -- B0. Declare and set variables --
            -----------------------------------
            DECLARE @CredibilityMetric VARCHAR(50) = 'Credibility';
            DECLARE @SafeHarborCredibilityMetric VARCHAR(50) = 'Safe Harbor Credibility';
            DECLARE @AllowedMetric VARCHAR(50) = 'Allowed';
            DECLARE @UseMetric VARCHAR(50) = 'Use';
            DECLARE @CostMetric VARCHAR(50) = 'Cost';
            DECLARE @BaseComponent VARCHAR(50) = 'Base';
            DECLARE @BasePlusOneComponent VARCHAR(50) = 'Base + 1';
            DECLARE @BasePlusTwoComponent VARCHAR(50) = 'Base + 2';
            DECLARE @TotalRepCat VARCHAR(50) = 'Total';
            DECLARE @MinCredibleMM INT = (SELECT    MinimumCredibleMemberMonths FROM   dbo.PerExtCMSValues WITH (NOLOCK));
            DECLARE @MaxCredibleMM INT = (SELECT    MaximumCredibleMemberMonths FROM   dbo.PerExtCMSValues WITH (NOLOCK));
            DECLARE @CredibleMM INT = (SELECT   CredibleMemberMonths FROM   dbo.PerExtCMSValues WITH (NOLOCK));
            SET @MaxComponentOrder = (SELECT    MAX (ComponentOrder)
                                      FROM      dbo.Trend_Reporting_CalcImpactProjected_forRollup);

            -- ComponentOrder, ComponentReporting
            IF (SELECT  OBJECT_ID ('tempdb..#Component')) IS NOT NULL
                DROP TABLE #Component;
            SELECT  DISTINCT
                    ComponentOrder
                   ,ComponentReporting
            INTO    #Component
            FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
            UNION ALL -- Base
            SELECT  0 AS ComponentOrder, @BaseComponent AS ComponentReporting
            UNION ALL -- Base + 1
            SELECT  @MaxComponentOrder AS ComponentOrder
                   ,@BasePlusOneComponent AS ComponentReporting
            UNION ALL -- Base + 2
            SELECT  @MaxComponentOrder AS ComponentOrder
                   ,@BasePlusTwoComponent AS ComponentReporting;
				   

            ---------------------
            -- B1. Credibility --
            ---------------------
            IF (SELECT  OBJECT_ID ('tempdb..#Credibility')) IS NOT NULL
                DROP TABLE #Credibility;

            -- Credibility straight calculation
            SELECT      DISTINCT
                        cip.PlanInfoID
                       ,rt.RateType
                       ,@ExperienceYear AS TrendYearID
                       ,@CredibilityMetric AS MetricType
                       ,bm.MemberMonths
                       ,CASE WHEN bm.MemberMonths >= @CredibleMM THEN 1
                             ELSE ROUND (POWER (dbo.Trend_fnSafeDivide (bm.MemberMonths, @CredibleMM, 0), 0.5), 6) END AS MetricValue
            INTO        #Credibility 
            FROM        dbo.Trend_Reporting_CalcImpactProjected_forRollup cip WITH (NOLOCK)
            LEFT JOIN   #BaseMembership bm
                   ON bm.PlanInfoID = cip.PlanInfoID
                      AND   bm.RateType = 1
                      AND   bm.TrendYearID = @ExperienceYear
           CROSS JOIN   #RateType rt
            WHERE       cip.RateType = 1
                        AND cip.ComponentOrder = 0 -- Use Projection Base for credibility calculations
                        AND (cip.PlanInfoID IN (SELECT Val FROM #CalcStringToTable)
                             OR @XPlanInfoID IS NULL)
            GROUP BY    cip.PlanInfoID
                       ,rt.RateType
                       ,cip.TrendYearID
                       ,bm.MemberMonths;

            -- Credibility Safe Harbor 
            INSERT INTO #Credibility
            SELECT      DISTINCT
                        cip.PlanInfoID
                       ,rt.RateType
                       ,@ExperienceYear AS TrendYearID
                       ,@SafeHarborCredibilityMetric AS MetricType
                       ,bm.MemberMonths
                       ,CASE WHEN bm.MemberMonths >= @MaxCredibleMM THEN 1
                             WHEN bm.MemberMonths <= @MinCredibleMM THEN 0
                             ELSE ROUND (POWER (dbo.Trend_fnSafeDivide (bm.MemberMonths, @CredibleMM, 0), 0.5), 6) END AS MetricValue
            FROM        dbo.Trend_Reporting_CalcImpactProjected_forRollup cip WITH (NOLOCK)
            LEFT JOIN   #BaseMembership bm
                   ON bm.PlanInfoID = cip.PlanInfoID
                      AND   bm.RateType = 1
                      AND   bm.TrendYearID = @ExperienceYear
           CROSS JOIN   #RateType rt
            WHERE       cip.RateType = 1
                        AND cip.ComponentOrder = 0 -- Use Projection Base for credibility calculations
                        AND (cip.PlanInfoID IN (SELECT Val FROM #CalcStringToTable)
                             OR @XPlanInfoID IS NULL)
            GROUP BY    cip.PlanInfoID
                       ,rt.RateType
                       ,cip.TrendYearID
                       ,bm.MemberMonths;


            -----------------------
            -- B2. Metric Values --
            -----------------------
            -- Pull data from forRollup table
            IF (SELECT  OBJECT_ID ('tempdb..#Data')) IS NOT NULL DROP TABLE #Data;
            SELECT      cip.PlanInfoID
                       ,cip.RateType
                       ,cip.TrendYearID
                       ,libc.ReportingCategory
                       ,cip.ComponentReporting
                       ,cip.ComponentOrder
                       ,cip.MemberMonths
                       ,SUM (cip.Allowed) AS Allowed
                       ,SUM (cip.Utilization) AS Utilization
            INTO        #Data
            FROM        dbo.Trend_Reporting_CalcImpactProjected_forRollup cip WITH (NOLOCK)
            LEFT JOIN   dbo.LkpIntBenefitCategory libc
                   ON libc.BenefitCategoryID = cip.BenefitCategoryID
            WHERE       cip.ComponentOrder <> 0 -- Don't include Projection Base component
                        AND (cip.PlanInfoID IN (SELECT Val FROM #CalcStringToTable )
                             OR @XPlanInfoID IS NULL)
            GROUP BY    cip.PlanInfoID
                       ,cip.RateType
                       ,cip.TrendYearID
                       ,libc.ReportingCategory
                       ,cip.ComponentReporting
                       ,cip.ComponentOrder
                       ,cip.MemberMonths;

            -- Calculate metric values
            IF (SELECT  OBJECT_ID ('tempdb..#CalcValues')) IS NOT NULL
                DROP TABLE #CalcValues;
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,ComponentReporting
                   ,ComponentOrder
                   ,dbo.Trend_fnSafeDivide (Allowed, MemberMonths, 0) AS AllowedMetricValue
                   ,dbo.Trend_fnSafeDivide (Utilization, MemberMonths, 0) * 12000 AS UseMetricValue
                   ,dbo.Trend_fnSafeDivide (Allowed, Utilization, 0) AS CostMetricValue
            INTO    #CalcValues
            FROM    #Data;

            -- Build values
            IF (SELECT  OBJECT_ID ('tempdb..#MetricValues')) IS NOT NULL
                DROP TABLE #MetricValues;

            -- Allowed
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,@AllowedMetric AS MetricType
                   ,CASE WHEN ComponentOrder = 1 THEN
                             CASE WHEN TrendYearID = @CurrentYear THEN @BaseComponent ELSE @BasePlusOneComponent END
                         ELSE ComponentReporting END AS ComponentReporting
                   ,ComponentOrder
                   ,AllowedMetricValue AS MetricValue
            INTO    #MetricValues
            FROM    #CalcValues;

            -- Allowed Base+2
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID + 1
                   ,ReportingCategory
                   ,@AllowedMetric AS MetricType
                   ,@BasePlusTwoComponent AS MetricType
                   ,@MaxComponentOrder + 1 AS ComponentOrder
                   ,AllowedMetricValue AS MetricValue
            FROM    #CalcValues
            WHERE   TrendYearID = @BidYear
                    AND ComponentOrder = @MaxComponentOrder;

            -- Use
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,@UseMetric AS MetricType
                   ,CASE WHEN ComponentOrder = 1 THEN
                             CASE WHEN TrendYearID = @CurrentYear THEN @BaseComponent ELSE @BasePlusOneComponent END
                         ELSE ComponentReporting END AS ComponentReporting
                   ,ComponentOrder
                   ,UseMetricValue AS MetricValue
            FROM    #CalcValues;

            -- Use Base+2
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID + 1
                   ,ReportingCategory
                   ,@UseMetric AS MetricType
                   ,@BasePlusTwoComponent AS MetricType
                   ,@MaxComponentOrder + 1 AS ComponentOrder
                   ,UseMetricValue AS MetricValue
            FROM    #CalcValues
            WHERE   TrendYearID = @BidYear
                    AND ComponentOrder = @MaxComponentOrder;

            -- Cost
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,@CostMetric AS MetricType
                   ,CASE WHEN ComponentOrder = 1 THEN
                             CASE WHEN TrendYearID = @CurrentYear THEN @BaseComponent ELSE @BasePlusOneComponent END
                         ELSE ComponentReporting END AS ComponentReporting
                   ,ComponentOrder
                   ,CostMetricValue AS MetricValue
            FROM    #CalcValues;

            -- Cost Base+2
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID + 1
                   ,ReportingCategory
                   ,@CostMetric AS MetricType
                   ,@BasePlusTwoComponent AS MetricType
                   ,@MaxComponentOrder + 1 AS ComponentOrder
                   ,CostMetricValue AS MetricValue
            FROM    #CalcValues
            WHERE   TrendYearID = @BidYear
                    AND ComponentOrder = @MaxComponentOrder;


            --------------------------------
            -- B3. Metric Values - Totals --
            --------------------------------
            -- Pull data (sum across ReportingCategory)
            IF (SELECT  OBJECT_ID ('tempdb..#DataTotals')) IS NOT NULL
                DROP TABLE #DataTotals;
            SELECT      PlanInfoID
                       ,RateType
                       ,TrendYearID
                       ,@TotalRepCat AS ReportingCategory
                       ,ComponentReporting
                       ,ComponentOrder
                       ,MemberMonths
                       ,SUM (Allowed) AS Allowed
                       ,SUM (Utilization) AS Utilization
            INTO        #DataTotals
            FROM        #Data
            GROUP BY    PlanInfoID
                       ,RateType
                       ,TrendYearID
                       ,ComponentReporting
                       ,ComponentOrder
                       ,MemberMonths;

            -- Calculate metric values
            IF (SELECT  OBJECT_ID ('tempdb..#CalcValuesTotal')) IS NOT NULL
                DROP TABLE #CalcValuesTotal;
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,ComponentReporting
                   ,ComponentOrder
                   ,dbo.Trend_fnSafeDivide (Allowed, MemberMonths, 0) AS AllowedMetricValue
                   ,dbo.Trend_fnSafeDivide (Utilization, MemberMonths, 0) * 12000 AS UseMetricValue
                   ,dbo.Trend_fnSafeDivide (Allowed, Utilization, 0) AS CostMetricValue
            INTO    #CalcValuesTotal
            FROM    #DataTotals;

            -- Allowed
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,@AllowedMetric AS MetricType
                   ,CASE WHEN ComponentOrder = 1 THEN
                             CASE WHEN TrendYearID = @CurrentYear THEN @BaseComponent ELSE @BasePlusOneComponent END
                         ELSE ComponentReporting END AS ComponentReporting
                   ,ComponentOrder
                   ,AllowedMetricValue AS MetricValue
            FROM    #CalcValuesTotal;

            -- Allowed Base+2
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID + 1
                   ,ReportingCategory
                   ,@AllowedMetric AS MetricType
                   ,@BasePlusTwoComponent AS MetricType
                   ,@MaxComponentOrder + 1 AS ComponentOrder
                   ,AllowedMetricValue AS MetricValue
            FROM    #CalcValuesTotal
            WHERE   TrendYearID = @BidYear
                    AND ComponentOrder = @MaxComponentOrder;

            -- Use
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,@UseMetric AS MetricType
                   ,CASE WHEN ComponentOrder = 1 THEN
                             CASE WHEN TrendYearID = @CurrentYear THEN @BaseComponent ELSE @BasePlusOneComponent END
                         ELSE ComponentReporting END AS ComponentReporting
                   ,ComponentOrder
                   ,UseMetricValue AS MetricValue
            FROM    #CalcValuesTotal;

            -- Use Base+2
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID + 1
                   ,ReportingCategory
                   ,@UseMetric AS MetricType
                   ,@BasePlusTwoComponent AS MetricType
                   ,@MaxComponentOrder + 1 AS ComponentOrder
                   ,UseMetricValue AS MetricValue
            FROM    #CalcValuesTotal
            WHERE   TrendYearID = @BidYear
                    AND ComponentOrder = @MaxComponentOrder;

            -- Cost
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,@CostMetric AS MetricType
                   ,CASE WHEN ComponentOrder = 1 THEN
                             CASE WHEN TrendYearID = @CurrentYear THEN @BaseComponent ELSE @BasePlusOneComponent END
                         ELSE ComponentReporting END AS ComponentReporting
                   ,ComponentOrder
                   ,CostMetricValue AS MetricValue
            FROM    #CalcValuesTotal;

            -- Cost Base+2
            INSERT INTO #MetricValues
            SELECT  PlanInfoID
                   ,RateType
                   ,TrendYearID + 1
                   ,ReportingCategory
                   ,@CostMetric AS MetricType
                   ,@BasePlusTwoComponent AS MetricType
                   ,@MaxComponentOrder + 1 AS ComponentOrder
                   ,CostMetricValue AS MetricValue
            FROM    #CalcValuesTotal
            WHERE   TrendYearID = @BidYear
                    AND ComponentOrder = @MaxComponentOrder;


            -----------------------
            -- B4. Impact Values --
            ----------------------- 
            IF (SELECT  OBJECT_ID ('tempdb..#ImpactValues')) IS NOT NULL
                DROP TABLE #ImpactValues;
            SELECT      mv.PlanInfoID
                       ,mv.RateType
                       ,mv.TrendYearID
                       ,mv.ReportingCategory
                       ,mv.MetricType
                       ,mv.ComponentReporting
                       ,mv.ComponentOrder
                       ,dbo.Trend_fnSafeDivide (mv.MetricValue, mv2.MetricValue, NULL) - 1 AS ImpactValue
            INTO        #ImpactValues
            FROM        #MetricValues mv
            LEFT JOIN   #MetricValues mv2
                   ON mv2.PlanInfoID = mv.PlanInfoID
                      AND   mv2.RateType = mv.RateType
                      AND   mv2.TrendYearID = mv.TrendYearID
                      AND   mv2.ReportingCategory = mv.ReportingCategory
                      AND   mv2.MetricType = mv.MetricType
                      AND   mv2.ComponentOrder + 1 = mv.ComponentOrder;


            ----------------
            -- B5. Output --
            ----------------
            SET @LastUpdateDateTime = GETDATE ();

            IF (SELECT  OBJECT_ID ('tempdb..#OutputForPlan')) IS NOT NULL
                DROP TABLE #OutputForPlan;
            CREATE TABLE #OutputForPlan
                (Division           VARCHAR(50)
                ,Region             VARCHAR(50)
                ,Market             VARCHAR(50)
                ,Product            VARCHAR(50)
                ,SNPType            VARCHAR(50)
                ,PlanInfoID         INT
                ,PlanYearID         INT
                ,CPS                CHAR(13)
                ,RateType           SMALLINT
                ,TrendYearID        INT
                ,ReportingCategory  VARCHAR(50)
                ,MetricType         VARCHAR(50)
                ,ComponentReporting VARCHAR(50)
                ,ComponentOrder     INT
                ,MetricValue        DEC(18, 8)
                ,ImpactValue        DEC(18, 8)
                ,LastUpdateByID     CHAR(7)
                ,LastUpdateDateTime DATETIME);

            -- Credibility
            INSERT INTO #OutputForPlan
            SELECT      vpi.Division
                       ,vpi.Region
                       ,vpi.Market
                       ,vpi.Product
                       ,vpi.SNPType
                       ,c.PlanInfoID
                       ,vpi.PlanYear AS PlanYearID
                       ,vpi.CPS
                       ,c.RateType
                       ,c.TrendYearID
                       ,'' AS ReportingCategory     -- Use empty string rather than NULL for the report
                       ,c.MetricType
                       ,'' AS ComponentReporting    -- Use empty string rather than NULL for the report
                       ,NULL AS ComponentOrder
                       ,c.MetricValue
                       ,NULL AS ImpactValue
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        #Credibility c
            LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = c.PlanInfoID;

            -- Metric values
            INSERT INTO #OutputForPlan
            SELECT      vpi.Division
                       ,vpi.Region
                       ,vpi.Market
                       ,vpi.Product
                       ,vpi.SNPType
                       ,mv.PlanInfoID
                       ,vpi.PlanYear AS PlanYearID
                       ,vpi.CPS
                       ,mv.RateType
                       ,mv.TrendYearID - 1 AS TrendYearID
                       ,mv.ReportingCategory
                       ,mv.MetricType
                       ,mv.ComponentReporting
                       ,c.ComponentOrder
                       ,mv.MetricValue      -- Base components have a MetricValue.
                       ,NULL AS ImpactValue -- Base components do not have an ImpactValue.
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        #MetricValues mv
            LEFT JOIN   #Component c
                   ON c.ComponentReporting = mv.ComponentReporting
            LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = mv.PlanInfoID
            WHERE       mv.ComponentReporting IN (@BaseComponent, @BasePlusOneComponent, @BasePlusTwoComponent);

            -- Impact values
            INSERT INTO #OutputForPlan
            SELECT      vpi.Division
                       ,vpi.Region
                       ,vpi.Market
                       ,vpi.Product
                       ,vpi.SNPType
                       ,iv.PlanInfoID
                       ,vpi.PlanYear AS PlanYearID
                       ,vpi.CPS
                       ,iv.RateType
                       ,iv.TrendYearID
                       ,iv.ReportingCategory
                       ,iv.MetricType
                       ,iv.ComponentReporting
                       ,c.ComponentOrder
                       ,NULL AS MetricValue             -- Non-Base components do not have a MetricValue.
                       ,iv.ImpactValue AS ImpactValue   -- Non-Base components have an ImpactValue.
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        #ImpactValues iv
            LEFT JOIN   #Component c
                   ON c.ComponentReporting = iv.ComponentReporting
            LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = iv.PlanInfoID
            WHERE       iv.ComponentReporting NOT IN (@BaseComponent, @BasePlusOneComponent, @BasePlusTwoComponent);


            ---------------------------
            -- B5. DELETE and INSERT --
            ---------------------------
            -- If @XPlanInfoID input parameter is NULL, delete everything. 
            IF @XPlanInfoID IS NULL
                BEGIN
                    DELETE  FROM dbo.Trend_Reporting_CalcImpactProjected_forPlan WHERE  1 = 1;
                END;
            ELSE -- If @XPlanInfoID input parameter is not NULL, only delete records for which the PlanInfoID exists in @XPlanInfoID
                BEGIN
                    DELETE  t
                    FROM    dbo.Trend_Reporting_CalcImpactProjected_forPlan t
                    JOIN    (SELECT Val AS PlanInfoID FROM #CalcStringToTable ) p
                      ON p.PlanInfoID = t.PlanInfoID
                    WHERE   p.PlanInfoID IS NOT NULL;
                END;

            INSERT INTO dbo.Trend_Reporting_CalcImpactProjected_forPlan
                (Division
                ,Region
                ,Market
                ,Product
                ,SNPType
                ,PlanInfoID
                ,PlanYearID
                ,CPS
                ,RateType
                ,TrendYearID
                ,ReportingCategory
                ,MetricType
                ,ComponentReporting
                ,ComponentOrder
                ,MetricValue
                ,ImpactValue
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  Division
                   ,Region
                   ,Market
                   ,Product
                   ,SNPType
                   ,PlanInfoID
                   ,PlanYearID
                   ,CPS
                   ,RateType
                   ,TrendYearID
                   ,ReportingCategory
                   ,MetricType
                   ,ComponentReporting
                   ,ComponentOrder
                   ,MetricValue
                   ,ImpactValue
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #OutputForPlan;

            COMMIT TRANSACTION transactionMain;

        END TRY

        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@XLastUpdateByID
               ,GETDATE ();

	IF (SELECT OBJECT_ID('tempdb..#CalcStringToTable')) IS NOT NULL
	BEGIN
        DROP TABLE #CalcStringToTable;
    END
	   
    END;
GO
