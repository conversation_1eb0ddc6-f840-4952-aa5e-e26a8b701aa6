SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetMAReportSBInfo]   ******/

/*===============================================================================*/
-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportSBInfo
--
-- AUTHOR: <PERSON> Galloway
--
-- CREATED DATE: 2009-Mar-16
-- HEADER UPDATED: 2010-Oct-05
--
-- DESCRIPTION:  Pulls supplemental benefit information for bidable and active plans (used for Auditing
--               Supplemental Benefits)
--
-- PARAMETERS:
--	Input:
--      @PlanYearID
--      @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--      ArcSavedPlanAddedBenefits
--      ArcSavedPlanHeader
--		MAReportPlanLevel
--      SavedPlanAddedBenefits
--      SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-16		1		    Initial Version										                Keith Galloway
-- 2009-Apr-01		2		    Added Benefit Code for Prod Development audit		                Keith Galloway
-- 2009-May-11		3		    Added missing where IsHidden = 0					                Keith Galloway
-- 2010-Oct-05      4           Added Arc tables for past year reference. Removed @PlanVersion      Joe Casey
--                                  as a parameter.  Default PlanVersion = 1
-- 2010-Nov-10		5		    Added PlanTypeName									                Jiao Chen
-- 2013-Apr-17		6			<8000 added so MSB's dont show as required by Prod. Dev.			Mike Deren
-- 2013-Oct-07		7			Modified to Include SegmentId										Anubhav Mishra
-- 2015-Oct-09      8           Modified logic to exclude bidYear from Arc Tables                   Kritika Singh
-- 2018-Oct-25		9			Replaced <8000 MSB logic to service category 35						Alex Beruscha
-- 2022-Jun-17		10			MAAUI migration; SavedPlanHeader structure change coding			Aleksandar Dimitrijevic
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportSBInfo]
    (
    @PlanYearID SMALLINT,
    @ForecastID INT
    )
RETURNS TABLE RETURN
	(
	SELECT
	    sph.PlanYearID,
		sph.ContractNumber,
		sph.PlanID,
		sph.SegmentId,  --Added SegmentId
		sph.ForecastID,
		PlanVersion = 1,
		added.AddedBenefitName, 
		BenefitCode = LEFT(added.AddedBenefitName,6),
		added.INAddedBenefitDescription,
		added.INAddedBenefitAllowed,
		added.INAddedBenefitUtilization,
		added.INAddedBenefitCostShare,
		added.OONAddedBenefitDescription,
		added.OONAddedBenefitAllowed,
		added.OONAddedBenefitUtilization,
		added.OONAddedBenefitCostShare,
		added.IsNetwork,
		pd.PlanTypeName
	FROM
	    (SELECT * FROM SavedPlanAddedBenefits
	    UNION ALL
	    SELECT * FROM ArcSavedPlanAddedBenefits where PlanYearID != dbo.fnGetBidYear()) added
	INNER JOIN
	    (SELECT * FROM SavedPlanHeader
	    UNION ALL
	    SELECT PlanyearID,
			   ForecastID,
			   0,
			   0,
			   MarketID,
			   PlanTypeID,
			   ContractNumber,
			   PlanID,
			   SegmentID,
			   CPDID,
			   IsSNP,
			   ContactID,
			   SecondaryContactID,
			   PartBPremiumBuyDown,
			   MARegionID,
			   Notes,
			   isMAPD,
			   IsOffModelRx,
			   IsHidden,
			   LastUpdateByID,
			   LastUpdateDateTime,
			   IsToReprice,
			   SNPTypeID,
			   AltRebateOrder,
			   IsLiveIndex,
			   PlanDescription,
			   IsMLA,
			   QualityInitDescrip,
			   IsCombinedDeductible,
			   IsSkipInducedUtilizationMapping,
			   IsRiskPlan,
			   IsSctPlan,
			   IsFiledPlan,
			   IsLocked,
			   PlanName,
			   CertifyingActuaryUserID 
		FROM ArcSavedPlanHeader WHERE PlanYearID != dbo.fnGetBidYear()) sph
		ON sph.PlanYearID = added.PlanYearID
		AND sph.ForecastID = added.ForecastID
	INNER JOIN dbo.MAReportPlanLevel pd
         ON pd.ForecastID =  added.ForecastID
         AND pd.PlanYearID = added.PlanYearID
	WHERE sph.PlanYearID = @PlanYearID
	    AND sph.ForecastID = @ForecastID
	    AND added.IsHidden = 0
	    AND added.BidServiceCatID <> 35
)

GO
