SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetMAReportLevel]    ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportLevel
--
-- AUTHOR: <PERSON><PERSON><PERSON>
--
-- CREATED DATE: 2009-Mar-20
-- HEADER UPDATED: 2010-Nov-05
--
-- DESCRIPTION:  Get list of Available Report Leves for MAMBA MAReport feature
--              Test: select * from fnGetMAReportLevel()
-- PARAMETERS:
--	Input:
--  Output:
--
-- TABLES: 
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-20		1		    Initial Version										                Aleksey Titievsky
-- 2016-Nov-17		2			Removed ReportLevelID 6												Chris Fleming
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportLevel]()
RETURNS TABLE
RETURN
SELECT * FROM
    (
        SELECT	1               ReportLevelID,
                'Plan Level'        Description
        UNION
        SELECT	2               ReportLevelID,
                'Benefit Level'    Description
        UNION
        SELECT	3               ReportLevelID,
                'Service Area'    Description
        UNION
        SELECT	4               ReportLevelID,
                'Supplemental Benefits'    Description
        UNION
        SELECT	5               ReportLevelID,
                'Optional Supplemental Benefits'    Description
    ) l
GO
