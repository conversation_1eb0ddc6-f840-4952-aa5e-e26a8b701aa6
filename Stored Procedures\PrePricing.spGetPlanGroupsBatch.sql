SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetPlanGroups]   
--    
-- AUTHOR: <PERSON><PERSON>y 
--    
-- CREATED DATE: 2024-Oct-30    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Regions  
--    
-- PARAMETERS:    
-- Input: 
-- @RegionID
   
-- TABLES:   
--  
 
-- Read:    
--  PrePricing.PlanGroup
--	PrePricing.PlanGroupMapping

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-30		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetPlanGroupsBatch]
(
	@RegionID VARCHAR(max) 
)
AS    
BEGIN    
	SELECT a.PlanGroupID,a.PlanGroupName,a.RegionID,
	GroupPlanInfoIds=STUFF((SELECT ',' + CAST(c.PlanInfoID AS VARCHAR(7))
           FROM PrePricing.PlanGroupMapping c WITH(NOLOCK)
		   WHERE c.PlanGroupID=a.PlanGroupID       
          FOR XML PATH('')), 1, 1, ''),
	GroupsCPS=STUFF((SELECT ',' + CAST(d.CPS AS VARCHAR(13))
    FROM PrePricing.PlanGroupMapping c WITH(NOLOCK)
	JOIN PrePricing.PlanInfo d on d.PlanInfoID=c.PlanInfoID
	WHERE c.PlanGroupID=a.PlanGroupID       
    FOR XML PATH('')), 1, 1, '')
	FROM PrePricing.PlanGroup  a WITH(NOLOCK)
	JOIN PrePricing.PlanGroupMapping b WITH(NOLOCK) ON b.PlanGroupID=a.PlanGroupID AND a.RegionID IN (SELECT value  FROM STRING_SPLIT(@RegionID,','))
	GROUP BY a.PlanGroupID,a.PlanGroupName,a.LastUpdateDateTime,a.RegionID
	ORDER BY a.LastUpdateDateTime DESC
END
 
GO
