SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Stored Procedure 
-- ---------------------------------------------------------------------------------------------------------------------- 
-- PROCEDURE NAME: spGetContractPBPSegmentList   
--                
-- AUTHOR: DEEPALI  
--               
-- CREATED DATE: 2015-6-29    
--             
-- DESCRIPTION: Returns distinct Contract-PBP ,segmentID,IsBenefitYearCurrentYear and BenefitCategoryID from  savedplanbenefitdetail and  savedplanheader   
--      
-- PARAMETERS:  
-- Input:      
-- 
-- TABLES:    
-- Read:         
--[dbo].[SAVEDPLANBENEFITDETAIL]  
--[dbo].[SAVEDPLANHEADER]  
-- Write:  
--          
-- VIEWS:   
--          
-- FUNCTIONS:   
--         
-- STORED PROCS:   
--      
-- $HISTORY    
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE    VERSION  CHANGES MADE									DEVELOPER                    
-- ---------------------------------------------------------------------------------------------------------------------- 
-- 2015-6-29  1   Initial Version									Deepali 
-- ----------------------------------------------------------------------------------------------------------------------    
  
CREATE PROCEDURE [dbo].[spGetContractPBPSegmentList]
AS 
    BEGIN
       SELECT  DISTINCT p.contractnumber + '-'+ p.planid + '-'+ p.segmentid + '-' + cast(S.IsBenefitYearCurrentYear as varchar(1)) + '-' +  cast(S.BenefitCategoryID as varchar(10))  AS ContractPBPSegmentBenYearList 
		FROM Savedplanbenefitdetail s
		INNER JOIN Savedplanheader p
		ON p.ForecastID=s.ForecastID
		WHERE ContractNumber IS NOT NULL
		AND planid  IS NOT NULL
		AND segmentid IS NOT NULL
		  
    END
GO
