SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  <PERSON>      
-- Create date: 07-08-2020
-- Description:  spAppSyncBaseExperience
--               Used by MAA-UI > Plan Level Main > Sync Button > "Experience Cost & Use - From Significance to Forecasting" - Radio option, Validate, & Then Sync Button
--               Takes a table of PlanInfoIDs and ServiceAreaOptionIDs that need to be sync'd.  Deletes the existing experience
--               build used for pricing/projecting in SavedPlanDFSummary. Then inserts the experience build that meets or exceeds
--               the significance threshold. 
--
-- PARAMETERS:      
-- Input:        

-- TABLES:    
-- Read:      SavedForecastSetup
--            SavedPlanInfo
--            SavedServiceAreaOption
--            CalcSignificance
--            SavedPlanDFSummary
--
-- Write:      
-- VIEWS:      
--            
--
-- FUNCTIONS:      
--
--        
-- STORED PROCS:       
--            spCalcPlanBase
--
-- $HISTORY         
--
--
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION		CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2020-Sep-15	1			Initial Version                                             Keith Galloway
-- 2020-Feb-19	2			Updated error trapping                                      Keith Galloway
-- 2021-Feb-25	3			Added RowNo,looped Rowno in #SyncForecasts 
								--&Renamed [dbo].[spCalcPlanBase] into dbo schema       Mahendran Chinnaiah
-- 2021-Jul-01	4			Optimization in second half of Sp							Deepali
-- 2021-Jul-15	5			Error Handling update to recompile base						Alex Beruscha
-- 2022-Feb-18	6			Return Message set to empty									Surya Murthy
-- 2022-Mar-01	7			Delete forecastid from SavedPlanDFSummary when error		Surya Murthy
-- 2024-Oct-20  8	        	Updated error contact messaging.							Hannah Harmon
 
----------------------------------------------------------------------------------------------------------------------        
CREATE PROCEDURE [dbo].[spAppSyncBaseExperience]
@BaseData VARCHAR(MAX)  ,  
@LastUpdateByID AS CHAR(7),
@MessageFromBackend VARCHAR(MAX) OUT,
@Result CHAR(1) OUT


AS
BEGIN
	SET NOCOUNT ON
    SET XACT_ABORT ON

	BEGIN TRY
		BEGIN TRANSACTION;
			SAVE TRANSACTION SPBeginSync
				IF  OBJECT_ID ('tempdb.dbo.#temp') IS NOT NULL
					BEGIN
						DROP table #temp
					END
			
	CREATE TABLE #temp(Component VARCHAR(50))
	INSERT INTO #temp
	(  Component)
		(SELECT  Value FROM    dbo.fnStringSplit(@BaseData, ',') ) 
		Declare @SyncList PlanInfoOption
		INSERT INTO @SyncList
		(
		    PlanInfoID,
		    OptionID
		)
		select 
		case when CHARINDEX(':',Component)>0 
			 then SUBSTRING(Component,1,CHARINDEX(':',Component)-1) 
			 else Component end PlanInfoID, 
		CASE WHEN CHARINDEX(':',Component)>0 
			 THEN SUBSTRING(Component,CHARINDEX(':',Component)+1,len(Component))  
			 ELSE NULL END as OptionID
			from #temp

		--	IF (SELECT DISTINCT 1 FROM @SyncList) IS NOT NULL
				DECLARE @LastUpdateDateTime AS DATETIME2
				SET @LastUpdateDateTime = GETDATE()
				BEGIN	
				---------------------------------------------------------------------------------------------------
				-- Get the list of Forecast IDs to sync given a list of PlanInfoIDs and Service Area Option IDs  --
				---------------------------------------------------------------------------------------------------
				IF  OBJECT_ID ('tempdb.dbo.#SyncForecasts') IS NOT NULL
					BEGIN
						DROP table #SyncForecasts
					END
				CREATE TABLE #SyncForecasts(
				    RowNo SMALLINT,
					ForecastID INT, 
					PlanInfoID SMALLINT,
					ServiceAreaOptionID  INT,
						DFVersionID INT NULL,
						CPSwithDetails VARCHAR(25)
						)
				
				--ForecastIDs that use a PlanInfoID+ServiceAreaOptionID that exits in SavedForecastSetup and in CalcSignificance
				--Where @SyncList has a value for both PlanInfoID and ServiceAreaOption
				INSERT INTO #SyncForecasts(RowNo,ForecastID,PlanInfoID,ServiceAreaOptionID,DFVersionID,CPSwithDetails)
					SELECT DISTINCT 
					   ROW_NUMBER() OVER(ORDER BY SFS.ForecastID),
						SFS.ForecastID, 
						SL.PlanInfoID,
						SL.OptionID ServiceAreaOptionID,
						SFS.DFVersionID,
						CAST(SPI.PlanYear AS VARCHAR)+':'+SPI.CPS+':'+CAST(SFS.ScenarioNbr AS VARCHAR) AS CPSwithDetails
					FROM dbo.SavedForecastSetup SFS WITH(NOLOCK)
					INNER JOIN @SyncList SL
						ON SFS.PlanInfoID = SL.PlanInfoID
						AND SFS.ServiceAreaOptionID = SL.OptionID
					INNER JOIN dbo.SavedPlanInfo SPI WITH(NOLOCK)
						ON SFS.PlanInfoID = SPI.PlanInfoID
					WHERE SFS.IsHidden = 0
					AND SFS.IsLiveIndex = 1
					ORDER BY SFS.ForecastID, SL.PlanInfoID, SL.OptionID

				-- STEP 1.  Delete From SavedPlanDFSummary all SyncForecast IDs where MARationgOptionID = 1
				DELETE DFS
				FROM dbo.SavedPlanDFSummary DFS
				INNER JOIN #SyncForecasts SF
					ON SF.ForecastID = DFS.ForecastID
				WHERE DFS.MARatingOptionID = 1
				
				--STEP 2.  Insert the experience cut defined with Significance into that table defining the experience cut for pricing/projecting.
				INSERT INTO dbo.SavedPlanDFSummary
					SELECT 
						SF.ForecastID,
						1 MARatingOptionID,
						Sig.BasePlanInfoID PlanInfoID,
						@LastUpdateByID,
						@LastUpdateDateTime
					FROM #SyncForecasts SF
					LEFT JOIN dbo.CalcSignificance Sig WITH(NOLOCK)
						ON SF.PlanInfoID = Sig.PlanInfoID
						AND SF.ServiceAreaOptionID = Sig.ServiceAreaOptionID
					WHERE Sig.IsWks1BasePlan = 1 AND SF.ServiceAreaOptionID IS NOT NULL
					GROUP BY SF.ForecastID, Sig.BasePlanInfoID

				END
					COMMIT TRANSACTION;
					Set @MessageFromBackend='';
	                Set @Result=1;
	END TRY

    BEGIN CATCH
			DECLARE @ErrorMessage NVARCHAR(4000);  
			DECLARE @ErrorSeverity INT;  
			DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000);
			DECLARE @ErrorContactEmail VARCHAR(25)
						 SET @ErrorContactEmail = (SELECT UserEmail FROM dbo.AdminUserHeader WHERE UserFirstName = 'iRSTeam')
   
			DECLARE @errSrc VARCHAR(MAX) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate DATETIME=GETDATE()
                    	Set @MessageFromBackend='Sync failed to Update SavedPlanDFSummary in step 1 and 2; Please try again or contact ' + @ErrorContactEmail;
			Set @Result=0;
		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)
		RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
		ROLLBACK TRANSACTION SPBeginSync; 

		---Insert into app log for logging error------------------
		EXEC dbo.spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
          
    END CATCH;
	
	--STEP 3.  Recompile the base experience cut for all the ForecastIDs just redefined
	BEGIN TRY
		BEGIN TRANSACTION;
			SAVE TRANSACTION SPBeginCalcPlanBase


			DECLARE @ForecastID INT = 0
			DECLARE @CPSwithDetails VARCHAR(25)
			
		
			BEGIN

			/* Created temp Tabe to avoid duplicacy*/
			IF  OBJECT_ID ('tempdb.dbo.#temptable') IS NOT NULL
					BEGIN
						DROP table #temptable
					END
			SELECT DISTINCT DFS.PlanInfoID, SF.DFVersionID,SF.ForecastID 
			INTO #temptable
					FROM #SyncForecasts  SF
					INNER JOIN dbo.SavedPlanDFSummary DFS WITH(NOLOCK)
						ON DFS.ForecastID = SF.ForecastID
						AND DFS.MARatingOptionID = 1
					WHERE CAST(DFS.PlanInfoID AS VARCHAR(10))+'-'+CAST(SF.DFVersionID AS VARCHAR(10))
						NOT IN(  SELECT DISTINCT CAST(DFF.PlanInfoID AS VARCHAR(10))+'-'+CAST(DFF.DFVersionID AS VARCHAR(10))
						         FROM dbo.SavedDFFinance DFF WITH (NOLOCK) 
								 WHERE DFF.DemogIndicator IN (1,2))
			
				END
				COMMIT TRANSACTION
			END TRY
		BEGIN CATCH

		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)
		RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
		SET @MessageFromBackend='Sync failed; Please try again or contact ' + @ErrorContactEmail;
			SET @Result=0;	
		ROLLBACK TRANSACTION SPBeginCalcPlanBase; 

		---Insert into app log for logging error------------------
		EXEC dbo.spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
	
            
    END CATCH
	 DECLARE @Rowno AS int, @ForecastID_Count INT;SET @Rowno = 1;
	  DECLARE @Succ_forecastids VARCHAR(MAX); SET @Succ_forecastids ='';
      SET @ForecastID_Count = (SELECT COUNT(1) FROM #SyncForecasts);
			-- Iterate over all ForecastIDs
	

IF(@Result = 1)
  BEGIN    
	  IF(@ForecastID_Count>0)    
	 BEGIN
		--Error temp table    
		IF  OBJECT_ID ('tempdb.dbo.#ForecastidsErrosTable') IS NOT NULL 
		   DROP TABLE #ForecastidsErrosTable;    
			 CREATE TABLE #ForecastidsErrosTable    
			 (       
				ForecastID INT,
				CPSwithDetails VARCHAR(25)  
			 );   
	  
		WHILE (@Rowno<=@ForecastID_Count)  
		  BEGIN
			 BEGIN TRY
				   -- Get next ForecastID   
					SELECT TOP 1 @ForecastID = SyncList.ForecastID ,
							@CPSwithDetails = synclist.CPSwithDetails     
					 FROM #SyncForecasts SyncList          
					 WHERE SyncList.RowNo = @Rowno
					
					IF EXISTS	(
							SELECT TOP 1 SyncList.ForecastID          
							FROM #SyncForecasts SyncList          
							WHERE SyncList.RowNo = @Rowno   
							AND SyncList.ForecastID NOT IN 
									( SELECT DISTINCT ForecastID
									FROM #temptable
								)
							)
					BEGIN 
					 EXEC dbo.spCalcPlanBase  @ForecastID, 1, @LastUpdateByID  
					  
					 SET @Succ_forecastids = (SELECT CASE WHEN @Succ_forecastids<>'' THEN @Succ_forecastids +','+ CAST(@ForecastID AS VARCHAR(50))
												ELSE CAST(@ForecastID AS VARCHAR(50) ) END)
					END
					ELSE 
         			BEGIN
						INSERT INTO #ForecastidsErrosTable(ForecastID,CPSwithDetails) VALUES (@ForecastID,@CPSwithDetails);
						DELETE DFS FROM dbo.SavedPlanDFSummary DFS WHERE DFS.ForecastID = @ForecastID AND DFS.MARatingOptionID = 1;
					END 

			 END TRY
		  BEGIN CATCH
			INSERT INTO #ForecastidsErrosTable(ForecastID,CPSwithDetails) VALUES (@ForecastID,@CPSwithDetails);  
		  END CATCH
		   SET @Rowno= @Rowno+1; 
		  END
    END 


	DECLARE @Err_Count INT;SET @Err_Count = (SELECT COUNT(1) FROM #ForecastidsErrosTable)
	 IF @Err_Count>0
		BEGIN
			DECLARE @Err_forecastids VARCHAR(MAX); 
			SET @Err_forecastids =(SELECT DISTINCT  STUFF((SELECT ', ' + CAST(CPSwithDetails AS VARCHAR(MAX)) [text()] FROM #ForecastidsErrosTable FOR XML PATH(''), TYPE)
								.value('.','VARCHAR(MAX)'),1,2,'') CPSwithDetails FROM #ForecastidsErrosTable )
			IF(@ForecastID_Count-@Err_Count>0)
			  BEGIN
				SET @MessageFromBackend=  'Sync successful for ' + CAST((@ForecastID_Count-@Err_Count) AS VARCHAR(50))  +' Scenario(s).' +' Your Scenario(s) will need to be repriced.'; 
			  END

			SET @MessageFromBackend =  @MessageFromBackend + 'Sync failed for the following ' + CAST(@Err_Count AS VARCHAR(50))  +' Scenarios (ForecastYear:CPS:ScenarioNumber) '+@Err_forecastids + '. Please try updating Claims Run, try Sync again, or contact ' + @ErrorContactEmail
			
			SET @Result=1; 
		END
	 ELSE
	   BEGIN
			SET @Result=1;  
			SET @MessageFromBackend= 'Sync successful for ' + CAST((@ForecastID_Count) AS VARCHAR(50))  +' Scenario(s).' +' Your Scenario(s) will need to be repriced.'; 
	   END
	 END

END
GO
