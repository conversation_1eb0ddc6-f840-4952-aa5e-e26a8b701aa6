SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateOrInsertBPTExceptionHeader
--
-- AUTHOR: <PERSON><PERSON>rell
--
-- CREATED DATE: 2008-May-05
-- HEADER UPDATED: 2010-Oct-13
--
-- DESCRIPTION: Updates or inserts the BPT exception header data for the specified plan index.
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--      @IsBasePlan
--        1 = This is the only plan in the base (no detail records will
--        be needed).  0 = Multiple plans in the base (detail records
--        required).  Detail records are NOT added by this procedure.
--      @Credibility
--        The % credibility.  Must be 0 if @IsBasePlan = 0 unless overidden.
--	Output: 
--
-- TABLES:
--	Read:
--      SavedPlanBPTExceptionHeader
--	Write:
--      SavedPlanBPTExceptionHeader
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                             	        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-May-05      1           Initial version.                                                    Tonya Cockrell
-- 2008-Aug-26      2           Added @UserID to the list of parameters and replaced all	        Shannon Boykin
--                                  occurrences of SUSER_SNAME with @UserID.
-- 2010-Mar-11		3	        Added IsOveride column to allow Credibility to be Overidden	        Jake Gaecke
--						            for non-IsBasePlan=1 plans
-- 2010-Oct-13      4           Removed @PlanYearID                                                 Joe Casey
-- 2010-Dec-30      5           Updated PlanYearID                                                  Nate Jacoby
-- 2011-Jan-18      6           Added @IsHidden                                                     Nate Jacoby
-- 2019-Oct-30	    7           Replace @UserID from char(13) to char(7)							Chhavi Sinha
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROC [dbo].[spUpdateOrInsertBPTExceptionHeader]
    @ForecastID INT,
    @IsBasePlan BIT,
	@IsOverride BIT,
    @Credibility DECIMAL(8,6),
    @IsHidden BIT,
    @UserID CHAR(7)
AS
    DECLARE @PlanYearID SMALLINT
    SET @PlanYearID = dbo.fnGetBidYear()

    IF EXISTS (SELECT 1 FROM SavedPlanBPTExceptionHeader WHERE ForecastID = @ForecastID)
        --The record exists, so overwrite it.
        UPDATE SavedPlanBPTExceptionHeader
        SET IsBasePlan = @IsBasePlan,
			IsOverride = @IsOverride,
            Credibility = @Credibility,
            IsHidden = @IsHidden,
            LastUpdateByID = @UserID,
            LastUpdateDateTime = GETDATE()
        WHERE ForecastID = @ForecastID
    ELSE
        --The record doesn't exist, so insert a new one.
        INSERT SavedPlanBPTExceptionHeader
            (
            PlanYearID,
            ForecastID,
            IsBasePlan,
			IsOverride,
            Credibility,
            IsHidden,
            LastUpdateByID,
            LastUpdateDateTime
            )
        VALUES
            (
            @PlanYearID,
            @ForecastID,
            @IsBasePlan,
			@IsOverride,
            @Credibility,
            @IsHidden,
            @UserID,
            GETDATE()
            )
GO
