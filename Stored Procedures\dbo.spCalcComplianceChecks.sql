SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--------------------------------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: spCalcComplianceChecks 
--  
-- AUTHOR: <PERSON> Fu  
--  
-- CREATED DATE: 2021 Oct-25  
--  
-- DESCRIPTION: Stored procedure responsible for Compliance Checks related to 
--1)Mini Peer Review Window  2)PD Prioritization
--  
-- PARAMETERS:  
--    Input:   
--  @RollupList  
--  @DivisionList  
--  @RegionList  
--      @LastUpdateByID  
--  
--    Output:  
--  
-- RETURNS:   
--  
-- TABLES:  
--    Read:  
--      LkpComplianceCheckVariableReference  
--    Write:   
--  CalcComplianceChecks  
--  Comp<PERSON><PERSON><PERSON><PERSON><PERSON>eader  
--  
-- VIEWS:  
--    Read:   
--  PerExtCMSValues  
--  
-- STORED PROCS:  
--    Executed: NONE  
--  
-- FUNCTIONS:  
--  fnAppGetGeneralMABPTValues  
--  fnAppGetBenchmarkSummary  
--  fnAppGetMABPTWS1Summary  
--  fnAppGetMABPTWS2  
--  fnAppGetMABPTWS3  
--  fnAppGetMABPTWS4Expenses  
--  Trend_fnCalcStringToTable  
--  fnGetCredibilityFactor  
--      fnGetVariableBoundLkpComplianceCheck  
--  
-- OVERRIDE EXAMPLE:  
--  
-- $HISTORY   
-- -------------------------------------------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION   CHANGES MADE                                                                            DEVELOPER  
-- -------------------------------------------------------------------------------------------------------------------------------------------------------  
-- 2021 Oct-25   1   Initial Version                                                                         Franklin Fu  
-- 2022 Feb-22   2   Added logic to handle NULLS                Franklin Fu  
-- 2022 Jan-10   3   Insert error log entry                                                                  Shivgopal  
-- 2022 Mar-10   4   Update final select(DF-9552)                                                            Shivgopal  
-- 2022 Oct-25   5   Updated procedure for story 3845707                                                     Zoey Glenn  
-- 2023-Jan-05   6   Added PlanYear in the Where Clause to Pull Bid Year ForecastIDs                         Abraham Ndabian  
-- 2023 Jan-12   7   Updated Procedure for Story 4121357                                                     Zoey Glenn  
-- 2023-Jan-26   8   Added data in temp table to avoid execution of a single function multiple times.        Sheetal Patil  
-- 2023-Feb-23   9   Minor Code Updates For Accuracy                                                         Zoey Glenn  
-- 2023-Mar-04   10  Updates to return Plan Level Checks Results                                             Kiran Kola  
-- 2023-Aug-22   11  Delete data from CalcComplicanceChecks once results are returned                        Kiran Kola  
-- 2023-Oct-25   12  Updates to align with the 2024 BiRT                                                     Zoey Glenn  
-- 2023-Oct-30	 13  Added CheckType Parameter to handle different types of checks							 Kiran Kola
-- 2023-Nov-16   14  Using UserId Parameter to filter the resultset correctly								 Kiran Kola
-- 2023-Dec-05   15  Added code to return 46 and 24 rows for MPRW and PD 2)Added Forecastid as Parameter	 Zoey/Kiran
-- 2023-Dec-20   16  Removed a check and changed some formulas												 Zoey
-- 2024-Jan-17   17  Fix for data truncation issue                                                           Zoey      
-- 2024-Jan-31   18 SavedForecastSetup  WHEREServiceAreaOptionID = 0 Ignored								 Kiran
-- 2024-Mar-06  19  Fix for DivideByZero issue																 Zoey  
-- 2024-Jun-11  20 Fix for forecastids with ServiceAreaOptionID = 0 in forecastsetup table					 Kiran
-- 2024-Oct-28  21 Batch Processing using ForecastIds Removed Cursor                                         Kiran
-- 2024-Nov-15	22 Fix for multiple results for same ForecastID                                              Kiran  
-- 2024-Nov-22	23 Returning error message and empty tables for forecastid having ServiceAreaOptionID = 0	 Kiran
-- 2025-Jan-23  24 Updates for 2026 Bid Season methodology changes                                           Zoey
-- 2025-Feb-14  25 Update to accomdate changes made in fnAppGetMABPTWS1Summary								 Zoey
---------------------------------------------------------------------------------------------------------------------------------------------------------  
CREATE PROCEDURE [dbo].[spCalcComplianceChecks]    
(    

@ForecastIdIn INT,  
@CheckType VARCHAR(20),
@LastUpdateByID CHAR(7),  
@ReturnErrorMessage VARCHAR(MAX) OUTPUT  
)    
AS   
BEGIN    
SET NOCOUNT ON;     

DECLARE @XForecastId INT = @ForecastIdIn
DECLARE @cols AS NVARCHAR(MAX)='';
DECLARE @query AS NVARCHAR(MAX)='';


BEGIN TRY    
--PRINT 'In 1st Begin Try'
DELETE FROM dbo.CalcComplianceChecks WHERE UserID = @LastUpdateByID; 

   
DECLARE @ForecastID INT;    
DECLARE @CPS VARCHAR(13);   
DECLARE @DualEligible VARCHAR(20) = 'Dual-Eligible'  
DECLARE @ServiceAreaOptionID  TINYINT =0


SELECT   @ForecastID = fs.ForecastID,
		 @CPS= sp.CPS,
		 @ServiceAreaOptionID = fs.ServiceAreaOptionID
   FROM            dbo.SavedForecastSetup fs WITH (NOLOCK)    
   INNER JOIN      dbo.SavedPlanInfo sp WITH (NOLOCK)    
     ON fs.PlanInfoID = sp.PlanInfoID  AND sp.PlanYear = dbo.fnGetBidYear()   
   
   WHERE   fs.ForecastID = @XForecastId

	 

IF (@ServiceAreaOptionID =0)
	BEGIN		--- if service area Id is 0 then do not process just retuer emapty tables with Error message 
	    SET @ReturnErrorMessage = ISNULL(@ReturnErrorMessage,'') + 'Error occured for Forecast ID:' + CAST(ISNULL(@ForecastID,0) AS VARCHAR(100)) + ' and CPS Code ' + ISNULL(@CPS,'') + '|'  
		DELETE FROM dbo.CalcComplianceChecks WHERE UserID = @LastUpdateByID AND ForecastID = @XForecastId; 
	END 
ELSE
	BEGIN -- else begin 
	   BEGIN TRY
	--PRINT 'in second begin try'
	--PRINT  ISNULL(@CPS,'CPS IS NULL') + ' ' + CAST (ISNULL(@ForecastID,0) AS VARCHAR(10))

		DECLARE @CredibilityFactor FLOAT;    
		SELECT  @CredibilityFactor = dbo.fnGetCredibilityFactor (@ForecastID);    
--------------------------------------------TempTables--------------------------------------------------------------    
		--Updated 11/9 -- Removed WS1BasePlans data pull  

 
		DROP TABLE IF EXISTS #BaseTable;    
  

		SELECT @ForecastID AS ForecastID, TestID
		INTO #BaseTable
		FROM dbo.ComplianceChecksHeader
		WHERE CheckType = @CheckType

  
		 DROP TABLE IF EXISTS #MABPTValues;    
     

			SELECT  ForecastID    
				,SNPTypeName 
				,MAPD
				,PlanName
				,PlanTypeName
				,OrganizationName
				,RiskScore
				,MemberMonths    
				,NonDualMemberMonths  
				,MarketingandSalesAdminExpense    
				,DirectAdminExpense    
				,IndirectAdminExpense    
				,Reins    
				,ESRDMemberMonths    
				,HospiceMemberMonths    
				,CMSESRDRevenue  
				,ESRDPremium  
				,CMSOtherRevenue  
				,OtherPremium  
				,CMSHospiceRevenue  
				,HospicePremium  
				,HospiceNetMedicalExpenses  
				,ESRDNetMedicalExpenses  
				,OtherNetMedicalExpenses  
				,CompletionFactor    
				,SecondaryPayerAdjustment  
				,ESRDMembers  
				,HospiceMembers  
				,TotalReduceCostShare  
				,TotalOtherSuppBen  
				,RxBasicPremium
				,RxBasicBuyDown
				,RxSuppPremium
				,RxSuppBuyDown
			INTO    #MABPTValues    
			FROM    dbo.fnAppGetGeneralMABPTValues (@ForecastID);    

			CREATE CLUSTERED INDEX IX_MABPTValues_ForecastID ON #MABPTValues(ForecastID)  

			  
			DROP TABLE IF EXISTS #BenchmarkSummary;    
    

			SELECT  ForecastID    
				   ,ProjNonDE#RiskScore    
				   ,ProjDE#RiskScore    
				   ,ProjW8edRiskScore    
			INTO    #BenchmarkSummary    
			FROM    dbo.fnAppGetBenchmarkSummary (@ForecastID);    

			CREATE CLUSTERED INDEX IX_BenchmarkSummary_ForecastID ON #BenchmarkSummary(ForecastID)  
  
			DROP TABLE IF EXISTS #PlanRegion;  
    

			SELECT ForecastID,   
			  Region   
			INTO #PlanRegion  
			FROM dbo.vwPlanInformation  
			WHERE ForecastID = @ForecastID 
			GROUP BY ForecastID, Region  

			CREATE NONCLUSTERED INDEX IX_PlanRegion_ForecastID ON #PlanRegion(ForecastID,Region) 
			        
			DROP TABLE IF EXISTS #fnAppGetMABPTWS1Summary;    
   

			CREATE TABLE   #fnAppGetMABPTWS1Summary(  
				ForecastID INT,  
				ServiceCategoryCode VARCHAR(4),  
				UtilType CHAR(1),  
				BaseUnits DECIMAL(30, 22),  
				AvgCost DECIMAL(30, 22),  
				BaseAllowed DECIMAL(30, 22),  
				BaseNetPMPM DECIMAL(30, 22),  
				UtilizationTrend DECIMAL(30, 22),  
				BenefitChange DECIMAL(30, 22),  
				PopulationChange DECIMAL(30, 22),  
				OtherFactor DECIMAL(30, 22),  
				ProviderPaymentChange DECIMAL(30, 22),  
				CostTrend DECIMAL(30, 22),  
				UtilAdditiveAdjustment DECIMAL(38, 6),  
				PMPMAdditiveAdjustment DECIMAL(38, 6)  
			 )  

			 CREATE CLUSTERED INDEX IX_fnAppGetMABPTWS1Summary_ForecastID ON #fnAppGetMABPTWS1Summary(ForecastID)  

			INSERT INTO #fnAppGetMABPTWS1Summary  
			SELECT ForecastID, ServiceCategoryCode, UtilType, BaseUnits, AvgCost, BaseAllowed, BaseNetPMPM, UtilizationTrend, BenefitChange, PopulationChange, 
			OtherFactor, ProviderPaymentChange, CostTrend, UtilAdditiveAdjustment, PMPMAdditiveAdjustment
			FROM   dbo.fnAppGetMABPTWS1Summary (@ForecastID)  
			
			
			IF (SELECT  OBJECT_ID ('tempdb..#MABPTWS1Summary')) IS NOT NULL    
			BEGIN     
				DROP TABLE #MABPTWS1Summary;    
			END  

			SELECT  ForecastID    
				,ServiceCategoryCode  
				   ,BaseAllowed    
				,BaseUnits  
				   ,BaseNetPMPM    
			INTO    #MABPTWS1Summary    
			FROM    #fnAppGetMABPTWS1Summary;    

			CREATE CLUSTERED INDEX IX_MABPTWS1Summary_ForecastID ON #MABPTWS1Summary(ForecastID)  


			IF (SELECT  OBJECT_ID ('tempdb..#ProjAllowedPMPM')) IS NOT NULL    
			BEGIN  
				DROP TABLE #ProjAllowedPMPM;   
			END   

			SELECT ForecastID  
			  , ServiceCategoryCode  
			  , (SUM(BaseAllowed) * AVG(UtilizationTrend) * AVG(BenefitChange) * AVG(PopulationChange) * AVG(OtherFactor) * AVG(ProviderPaymentChange) * AVG(CostTrend))  
						+ ISNULL(SUM(PMPMAdditiveAdjustment),0) as ProjectedAllowedPMPM   
			  INTO #ProjAllowedPMPM   
			  FROM #fnAppGetMABPTWS1Summary  
			  GROUP BY ForecastID, ServiceCategoryCode  
			CREATE CLUSTERED INDEX IX_ProjAllowedPMPM_ForecastID ON #ProjAllowedPMPM(ForecastID)  

			IF (SELECT  OBJECT_ID ('tempdb..#MABPTWS2')) IS NOT NULL    
			BEGIN   
				DROP TABLE #MABPTWS2;    
			END   
			  SELECT  ForecastID    
				   ,ServiceCategoryCode    
				   ,ProjectedAllowed    
				   ,ManualAllowed    
				   ,BlendedAllowed    
				   ,BlendedUtil    
				,DualBlendedAllowedPMPM  
				,NonDualBlendedAllowedPMPM  
			INTO    #MABPTWS2    
			FROM    dbo.fnAppGetMABPTWS2 (@ForecastID);    
			CREATE CLUSTERED INDEX IX_MABPTWS2_ForecastID ON #MABPTWS2(ForecastID)  

			IF (SELECT  OBJECT_ID ('tempdb..#MABPTWS3')) IS NOT NULL    
			BEGIN  
				DROP TABLE #MABPTWS3;        
			END   

			SELECT  ForecastID    
				   ,INCostShare    
				   ,OONCostShare    
			INTO    #MABPTWS3    
			FROM    dbo.fnAppGetMABPTWS3 (@ForecastID);    
			CREATE CLUSTERED INDEX IX_MABPTWS3_ForecastID ON #MABPTWS3(ForecastID)  

			IF (SELECT  OBJECT_ID ('tempdb..#MABPTWS4Expenses')) IS NOT NULL    
			BEGIN  
				DROP TABLE #MABPTWS4Expenses;    
			END   

			SELECT ForecastID    
				   ,MarketingSales    
				   ,DirectAdmin    
				   ,InDirectAdmin    
				   ,ReinsCoveredNet    
			INTO    #MABPTWS4Expenses    
			FROM    dbo.fnAppGetMABPTWS4Expenses (@ForecastID);    
			CREATE CLUSTERED INDEX IX_MABPTWS4Expenses_ForecastID ON #MABPTWS4Expenses(ForecastID)  

			IF (SELECT  OBJECT_ID ('tempdb..#WS4Percent')) IS NOT NULL  
			BEGIN  
				DROP TABLE dbo.#WS4Percent;  
			END   
			SELECT   
				 @ForecastID as ForecastID,  
				 DualEligibleTypeID,    
							   ServiceCategoryCode,    
							   CostShare = SUM(CostShare),    
				   PercentCoveredAllowed = SUM(PercentCoveredAllowed),    
				   PercentCoveredCostShare = SUM(PercentCoveredCostShare)    
			   INTO #WS4Percent  
						FROM dbo.fnAppGetMABPTWS4Percent(@ForecastID)    
						GROUP BY ServiceCategoryCode ,    
								 DualEligibleTypeID  
			CREATE NONCLUSTERED INDEX IX_WS4Percent_SCC_DETI ON #WS4Percent(ServiceCategoryCode,DualEligibleTypeID)  

			IF (SELECT  OBJECT_ID ('tempdb..#fnAppGetMABPTWS5')) IS NOT NULL  
			BEGIN  
			DROP TABLE #fnAppGetMABPTWS5;  
			END  

			CREATE TABLE #fnAppGetMABPTWS5  
			(  
				PlanYearID SMALLINT,  
				ForecastID INT,  
				CountyCode CHAR(5),  
				StateTerritoryShortName CHAR(2),  
				CountyName VARCHAR(30),  
				MemberMonths DECIMAL (28,6),  
				RiskFactor DECIMAL(9, 6),  
				RiskRate DECIMAL(8, 4),  
				AdjRiskRate DECIMAL(12, 8),   
				ISARScale DECIMAL(16, 15),  
				ISARBid DECIMAL(10, 6),  
				RiskPaymentRateA DECIMAL (10, 6),  
				RiskPaymentRateB DECIMAL (10, 6),  
				CMSIPCostShare DECIMAL(7, 6),  
				CMSSNFCostShare DECIMAL(7, 6),  
				PartBCostShare DECIMAL(7, 6),  
				CMSIPFFSCosts DECIMAL(8, 4),  
				CMSSNFCosts DECIMAL (8, 4),  
				PartBCosts DECIMAL (8, 4),  
				CMSPartAEquivCostShare DECIMAL (8, 4),  
				CMSPartBEquivCostShare DECIMAL (8, 4)  
			 )  

			INSERT INTO #fnAppGetMABPTWS5  
			Select PlanYearID, ForecastID, CountyCode, StateTerritoryShortName, CountyName, MemberMonths, RiskFactor, RiskRate, AdjRiskRate, ISARScale, 
			ISARBid, RiskPaymentRateA, RiskPaymentRateB, 
			CMSIPCostShare, CMSSNFCostShare, PartBCostShare, CMSIPFFSCosts, CMSSNFCosts, PartBCosts, CMSPartAEquivCostShare, CMSPartBEquivCostShare
			FROM  dbo.fnAppGetMABPTWS5(@ForecastID)  

			CREATE CLUSTERED INDEX IX_fnAppGetMABPTWS5_ForecastID ON #fnAppGetMABPTWS5(ForecastID)  

			IF (SELECT  OBJECT_ID ('tempdb..#BPTWS5')) IS NOT NULL  
			BEGIN  
			DROP TABLE #BPTWS5;  
			END   

			SELECT  @ForecastID as ForecastID,   
			  SUM(MemberMonths) as MemberMonths,   
			  AVG(RiskFactor) as RiskFactor  
			INTO #BPTWS5  
			FROM   #fnAppGetMABPTWS5  
			CREATE CLUSTERED INDEX IX_BPTWS5_ForecastID ON #BPTWS5(ForecastID)  


			IF (SELECT  OBJECT_ID ('tempdb..#ProjMbrMths')) IS NOT NULL  
			BEGIN  
				DROP TABLE #ProjMbrMths;  
			END  

			SELECT  @ForecastID as ForecastID,   
			  SUM(ProjectedMemberMonths) as ProjectedNonDEMemberMonths   
			INTO #ProjMbrMths  
			FROM dbo.fnPlanCountyProjectedMemberMonths(@ForecastID, 0) b  
			CREATE CLUSTERED INDEX IX_ProjMbrMths_ForecastID ON #ProjMbrMths(ForecastID)  

			IF (SELECT  OBJECT_ID ('tempdb..#ProjDEMbrMths')) IS NOT NULL  
			BEGIN  
				DROP TABLE #ProjDEMbrMths;  
			END  

			SELECT  @ForecastID as ForecastID,   
			  SUM(ProjectedMemberMonths) as ProjectedDEMemberMonths   
			INTO #ProjDEMbrMths  
			FROM dbo.fnPlanCountyProjectedMemberMonths(@ForecastID, 1) b  
			CREATE CLUSTERED INDEX IX_ProjDEMbrMths_ForecastID ON #ProjDEMbrMths(ForecastID)  

			IF (SELECT  OBJECT_ID ('tempdb..#OOAProjectedMM')) IS NOT NULL  
			 BEGIN  
			 DROP TABLE #OOAProjectedMM;  
			 END   

			SELECT ForecastID,   
			 MemberMonths   
			INTO #OOAProjectedMM  
			FROM  #fnAppGetMABPTWS5  
			WHERE CountyName = 'OOA'  
			CREATE CLUSTERED INDEX IX_OOAProjectedMM_ForecastID ON #OOAProjectedMM(ForecastID)  

			IF @CheckType = 'PD' BEGIN

			IF (SELECT  OBJECT_ID ('tempdb..#PDAuditExhibit')) IS NOT NULL  
			 BEGIN  
			 DROP TABLE #PDAuditExhibit;  
			 END 

			SELECT b.ForecastID,
				b.AltRebateOrder,
				a.ContractPBP, 
				a.SegmentID,
				a.OrganizationName,
				a.PlanName,
				a.ProductType, 
				a.SnpTypeName, 
				a.ProjectedMM, 
				a.PlanYearLIPercent,
				a.BaseMemberMonths, 
				a.BasicPremiumRounded, 
				a.SupplementalPremiumRounded
			 INTO #PDAuditExhibit
			 FROM dbo.SavedPDAuditExhibit a
			 JOIN dbo.SavedPlanHeader b ON a.ContractPBP + a.SegmentID = b.ContractNumber + b.PlanID + b.SegmentID
			 WHERE b.ForecastID = @ForecastID

			 CREATE CLUSTERED INDEX IX_PDAuditExhibit_ForecastID ON #PDAuditExhibit(ForecastID)

			 IF (SELECT  OBJECT_ID ('tempdb..#PDAuditExhibit_Contract')) IS NOT NULL  
			 BEGIN  
			 DROP TABLE #PDAuditExhibit_Contract;  
			 END 

			SELECT b.ForecastID,
				a.ContractPBP, 
				a.OrganizationName,
				a.PlanName,
				a.ProductType, 
				a.SnpTypeName, 
				a.ProjectedMM, 
				a.PlanYearLIPercent,
				a.BaseMemberMonths, 
				a.BasicPremiumRounded, 
				a.SupplementalPremiumRounded
			 INTO #PDAuditExhibit_Contract
			 FROM dbo.SavedPDAuditExhibit a
			 JOIN dbo.SavedPlanHeader b ON a.ContractPBP  = b.ContractNumber + b.PlanID
			 WHERE b.ForecastID = @ForecastID

			 CREATE CLUSTERED INDEX IX_PDAuditExhibit_Contract_ForecastID ON #PDAuditExhibit_Contract(ForecastID)

			 IF (SELECT  OBJECT_ID ('tempdb..#ContractList')) IS NOT NULL  
			BEGIN  
			DROP TABLE #ContractList;  
			END  

			CREATE TABLE #ContractList  
			(    
				ForecastID INT  
			 ) 

			INSERT INTO #ContractList
			SELECT ForecastID FROM dbo.SavedPlanHeader
			WHERE ContractNumber + PlanID IN (
			SELECT a.ContractNumber + a.PlanID FROM SavedPlanHeader a
			WHERE a.ForecastID = @ForecastID )

			 IF (SELECT  OBJECT_ID ('tempdb..#MABPTValuesContract')) IS NOT NULL    
			BEGIN  
			   DROP TABLE #MABPTValuesContract;    
			END   

			SELECT  @ForecastID AS ForecastID 
				,MAX(b.MAPD) AS MAPD
				,SUM(b.ESRDMembers)  AS ESRD
				,SUM(b.HospiceMembers)  AS Hospice
			INTO    #MABPTValuesContract    
			FROM  #ContractList a CROSS APPLY  dbo.fnAppGetGeneralMABPTValues (a.ForecastID) b;    

			 IF (SELECT  OBJECT_ID ('tempdb..#WS5Contract')) IS NOT NULL  
			BEGIN  
			DROP TABLE #WS5Contract;  
			END 

			SELECT
				@ForecastID AS ForecastID,   
				SUM(b.MemberMonths) AS MemberMonths
			INTO #WS5Contract
			FROM #ContractList a CROSS APPLY dbo.fnAppGetMABPTWS5(a.ForecastID) b;

			 IF (SELECT  OBJECT_ID ('tempdb..#SavedPlanHeader')) IS NOT NULL    
			BEGIN  
			   DROP TABLE #SavedPlanHeader;    
			END  

			SELECT a.ForecastID, a.AltRebateOrder
			INTO #SavedPlanHeader
			FROM dbo.SavedPlanHeader a
			WHERE a.ForecastID = @ForecastID

			END





			---------********Start of CheckType MPRW********-------
			IF @CheckType = 'MPRW'  
			BEGIN


			--------------------------------------------TestIndex3.02--------------------------------------------------------------    
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex3_02')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex3_02;    
			END   
			  SELECT  b.ForecastID    
				   , b.TestID ,   
				   CASE WHEN (ROUND (a.ProjNonDE#RiskScore, 4) <> 0    
							   AND  ROUND (a.ProjDE#RiskScore, 4) <> 0)    
							  OR ROUND (a.ProjNonDE#RiskScore, 4) = ROUND (a.ProjW8edRiskScore, 4)    
							  OR ROUND (a.ProjDE#RiskScore, 4) = ROUND (a.ProjW8edRiskScore, 4) THEN 'Pass'   
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex3_02   
			FROM #BaseTable b   
			LEFT JOIN #BenchmarkSummary a ON a.ForecastID = b.ForecastID  
			WHERE b.TestID = 3.02



			--------------------------------------------TestIndex9.02--------------------------------------------------------------    
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex9_02')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex9_02;    
			END   

			SELECT      b.ForecastID  
					   ,b.TestID    
					   ,CASE WHEN @CredibilityFactor <= 0.2    
								  OR @CredibilityFactor >= 0.9   
				  THEN 'N/A'    
							 WHEN SUM (a.ManualAllowed) = 0    
								  OR (SUM (a.ProjectedAllowed) - SUM (a.ManualAllowed)) / SUM (a.ManualAllowed) IS NULL THEN 'Fail'    
							 WHEN ((SUM (a.ProjectedAllowed) - SUM (a.ManualAllowed)) / SUM (a.ManualAllowed))   
									BETWEEN (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(9.02,'x_1'))   
											AND (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(9.02,'y_1')) THEN  'Pass'    
							 ELSE 'Fail' END AS Result    
					   ,CASE WHEN @CredibilityFactor <= 0.2    
								  OR @CredibilityFactor >= 0.9 THEN 'N/A'    
							 WHEN SUM (a.ManualAllowed) = 0    
								  OR (SUM (a.ProjectedAllowed) - SUM (a.ManualAllowed)) / SUM (a.ManualAllowed) IS NULL THEN 'NULL'    
							 ELSE LEFT(CAST(ROUND(((SUM (a.ProjectedAllowed) - SUM (a.ManualAllowed)) / SUM (a.ManualAllowed)), 7) AS VARCHAR(MAX)), 10) END AS Test_Value    
			INTO        #TestIndex9_02    
			FROM   #BaseTable b  
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK)    
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 9.02
			GROUP BY b.ForecastID, b.TestID;    

			--------------------------------------------TestIndex9.05--------------------------------------------------------------    

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex9_05')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex9_05;    
			END   

			SELECT  c.ForecastID    
				   ,c.TestID  
				   ,CASE WHEN @CredibilityFactor <= 0.2 THEN 'N/A'    
						 WHEN ((a.INCostShare + a.OONCostShare) - (b.BaseAllowed - b.BaseNetPMPM)) IS NULL THEN 'Fail'    
						 WHEN ((a.INCostShare + a.OONCostShare) - (b.BaseAllowed - b.BaseNetPMPM))   
						 BETWEEN (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(9.05,'x_1')) AND (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(9.05,'y_1')) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,CASE WHEN @CredibilityFactor <= 0.2 THEN 'N/A'    
						 WHEN ((a.INCostShare + a.OONCostShare) - (b.BaseAllowed - b.BaseNetPMPM)) IS NULL THEN 'NULL'    
						 ELSE LEFT(CAST(ROUND(((a.INCostShare + a.OONCostShare) - (b.BaseAllowed - b.BaseNetPMPM)), 7) AS VARCHAR(MAX)), 10) END AS Test_Value    
			INTO    #TestIndex9_05   
			FROM    #BaseTable c
			LEFT JOIN (SELECT    ForecastID    
							   ,SUM (INCostShare) AS INCostShare    
							   ,SUM (OONCostShare) AS OONCostShare    
					 FROM       #MABPTWS3 WITH (NOLOCK)    
					 GROUP BY   ForecastID) a   
				ON c.ForecastID = a.ForecastID
			LEFT JOIN    (SELECT     ForecastID    
							   ,SUM (BaseAllowed) AS BaseAllowed    
							   ,SUM (BaseNetPMPM) AS BaseNetPMPM    
					 FROM       #MABPTWS1Summary WITH (NOLOCK)    
					 GROUP BY   ForecastID) b    
			  ON a.ForecastID = b.ForecastID
			  WHERE c.TestID = 9.05;    

			--------------------------------------------TestIndex13.03--------------------------------------------------------------    
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex13_03')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex13_03;    
			END  

			SELECT  c.ForecastID    
				   ,c.TestID
				   ,CASE WHEN @CredibilityFactor <= 0.2 THEN 'N/A'    
						 WHEN (a.MemberMonths + a.ESRDMemberMonths + a.HospiceMemberMonths) = 0    
							  OR (a.MarketingandSalesAdminExpense + a.DirectAdminExpense + a.IndirectAdminExpense + a.Reins) = 0    
							  OR ((b.MarketingSales + b.DirectAdmin + b.InDirectAdmin + b.ReinsCoveredNet)    
								  / ((a.MarketingandSalesAdminExpense + a.DirectAdminExpense + a.IndirectAdminExpense + a.Reins)    
									 / (a.MemberMonths + a.ESRDMemberMonths + a.HospiceMemberMonths)) - 1) IS NULL THEN 'Fail'    
						 WHEN ((b.MarketingSales + b.DirectAdmin + b.InDirectAdmin + b.ReinsCoveredNet)    
							   / ((a.MarketingandSalesAdminExpense + a.DirectAdminExpense + a.IndirectAdminExpense + a.Reins)    
								  / (a.MemberMonths + a.ESRDMemberMonths + a.HospiceMemberMonths)) - 1)   
								  BETWEEN (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(13.03,'x_1')) AND (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(13.03,'y_1')) THEN    
							 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,CASE WHEN @CredibilityFactor <= 0.2 THEN 'N/A'    
						 WHEN (a.MemberMonths + a.ESRDMemberMonths + a.HospiceMemberMonths) = 0    
							  OR (a.MarketingandSalesAdminExpense + a.DirectAdminExpense + a.IndirectAdminExpense + a.Reins) = 0    
							  OR ((b.MarketingSales + b.DirectAdmin + b.InDirectAdmin + b.ReinsCoveredNet)    
								  / ((a.MarketingandSalesAdminExpense + a.DirectAdminExpense + a.IndirectAdminExpense + a.Reins)    
									 / (a.MemberMonths + a.ESRDMemberMonths + a.HospiceMemberMonths)) - 1) IS NULL THEN 'NULL'    
						 ELSE    
							 LEFT(CAST(ROUND(((b.MarketingSales + b.DirectAdmin + b.InDirectAdmin + b.ReinsCoveredNet)    
								   / ((a.MarketingandSalesAdminExpense + a.DirectAdminExpense + a.IndirectAdminExpense + a.Reins)    
									  / (a.MemberMonths + a.ESRDMemberMonths + a.HospiceMemberMonths)) - 1), 7) AS VARCHAR(MAX)), 10) END AS Test_Value    
			INTO    #TestIndex13_03    
			FROM    #BaseTable c
			LEFT JOIN #MABPTValues a WITH (NOLOCK)    
				ON c.ForecastID = a.ForecastID
			LEFT JOIN    #MABPTWS4Expenses b WITH (NOLOCK)    
			  ON a.ForecastID = b.ForecastID
			  WHERE c.TestID = 13.03;    

			--------------------------------------------TestIndex29.01--------------------------------------------------------------    
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex29_01')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex29_01;    
			END   

			SELECT  b.ForecastID    
				   ,b.TestID   
				   ,CASE WHEN ISNULL(a.ForecastID,0) = 0 THEN 'N/A'  
				WHEN (a.BlendedAllowed = 0    
							   AND  a.BlendedUtil = 0)    
							  OR (a.BlendedAllowed <> 0    
								  AND   a.BlendedUtil <> 0) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex29_01   
			FROM    #BaseTable b  
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK)    
			ON a.ForecastID = b.ForecastID AND a.ServiceCategoryCode = 'k.' 
			WHERE b.TestID = 29.01;


			--------------------------------------------TestIndex7.01--------------------------------------------------------------    
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_01')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex7_01;    
			END   

			SELECT b.ForecastID,     
			  b.TestID,     
			  CASE WHEN SUM(a.MemberMonths) = 0 THEN 'N/A'    
			  WHEN a.CompletionFactor > (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(7.01,'x_1')) THEN 'Pass'    
			  ELSE 'Fail' END AS Result,     
			  CASE WHEN SUM(a.MemberMonths) = 0 THEN 'N/A'    
			  ELSE LEFT(CAST(ROUND(a.CompletionFactor, 7) AS VARCHAR(MAX)), 10) END AS Test_Value    
			INTO #TestIndex7_01    
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON b.ForecastID = a.ForecastID
			WHERE b.TestID = 7.01
			GROUP BY b.ForecastID, b.TestID, a.CompletionFactor    


			--------------------------------------------TestIndex4.02--------------------------------------------------------------    

			-- Updated 11/9  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_02')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex4_02;    
			END   

			SELECT a.ForecastID,     
			  a.TestID,     
			  CASE WHEN SUM(b.MemberMonths) = 0 THEN 'N/A'    
			  WHEN ((CAST(SUM(b.MemberMonths) as decimal) - CAST(SUM(b.NonDualMemberMonths) as decimal)) / CAST(SUM(b.MemberMonths) AS decimal)) > 0 THEN 'Pass'    
			  ELSE 'Fail' END AS Result,     
			  CASE WHEN SUM(b.MemberMonths) = 0 THEN 'N/A'    
			  Else LEFT(CAST(  
			  CAST(ROUND(((CAST(SUM(b.MemberMonths) as decimal) - CAST(SUM(b.NonDualMemberMonths) as decimal)) / CAST(SUM(b.MemberMonths) AS decimal)),5  

			  )AS DECIMAL (18,5))AS VARCHAR(MAX)), 15) END AS Test_Value    

			INTO #TestIndex4_02    
			FROM #BaseTable a 
			LEFT JOIN #MABPTValues b
			ON a.ForecastID = b.ForecastID
			WHERE a.TestID = 4.02
			GROUP BY a.ForecastID, a.TestID   

			--------------------------------------------TestIndex7.08--------------------------------------------------------------    
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_08')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex7_08;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN SUM(a.HospiceMemberMonths) = 0 THEN 'N/A'   
						 WHEN ((SUM(a.CMSHospiceRevenue) + SUM(a.HospicePremium)) / SUM(a.HospiceMemberMonths)) > 0 THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  CASE WHEN SUM(a.HospiceMemberMonths) = 0 THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND(((SUM(a.CMSHospiceRevenue) + SUM(a.HospicePremium)) / SUM(a.HospiceMemberMonths)), 7) AS VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex7_08  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 7.08
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex7.07--------------------------------------------------------------    
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_07')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex7_07;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN SUM(a.HospiceMemberMonths) = 0 THEN 'N/A'   
						 WHEN (SUM(a.HospiceNetMedicalExpenses)/SUM(a.HospiceMemberMonths)) > 0 THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  CASE WHEN SUM(a.HospiceMemberMonths) = 0 THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND((SUM(a.HospiceNetMedicalExpenses)/SUM(a.HospiceMemberMonths)), 7) AS VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex7_07  
			FROM #BaseTable b 
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 7.07
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex7.04--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_04')) IS NOT NULL  
			BEGIN   
				DROP TABLE #TestIndex7_04;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN SUM(a.ESRDMemberMonths) = 0 THEN 'N/A'   
						 WHEN ((SUM(a.CMSESRDRevenue) + SUM(a.ESRDPremium))/SUM(a.ESRDMemberMonths)) > 0 THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  CASE WHEN SUM(a.ESRDMemberMonths) = 0 THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND(((SUM(a.CMSESRDRevenue) + SUM(a.ESRDPremium))/SUM(a.ESRDMemberMonths)), 7) AS VARCHAR), 10)   
						 END AS Test_Value  
			INTO #TestIndex7_04  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON b.ForecastID = a.ForecastID
			WHERE b.TestID = 7.04
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex7.03--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_03')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex7_03;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN SUM(a.ESRDMemberMonths) = 0 THEN 'N/A'   
						 WHEN (SUM(a.ESRDNetMedicalExpenses)/SUM(a.ESRDMemberMonths)) > 0 THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  CASE WHEN SUM(a.ESRDMemberMonths) = 0 THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND((SUM(a.ESRDNetMedicalExpenses)/SUM(a.ESRDMemberMonths)), 7) AS VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex7_03  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a 
			ON b.ForecastID = a.ForecastID
			WHERE b.TestID = 7.03
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex4.03--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_03')) IS NOT NULL  
			BEGIN     
			DROP TABLE #TestIndex4_03;  
			END   

			SELECT c.ForecastID,   
			  c.TestID,   
			  Case When SUM(a.MemberMonths) = 0 THEN 'N/A'  WHEN Round(((Sum(a.MemberMonths) - Sum(b.ProjectedNonDEMemberMonths)) / Sum(a.MemberMonths)), 7) > 0 Then 'Pass'   
						 Else 'Fail' END AS Result,   
			  CASE WHEN SUM(a.MemberMonths) = 0 THEN 'N/A' ELSE LEFT(CAST(Round(((Sum(a.MemberMonths) - Sum(b.ProjectedNonDEMemberMonths)) / Sum(a.MemberMonths)), 7)AS VARCHAR), 10) END as Test_Value  
			INTO #TestIndex4_03   
			FROM #BaseTable c
			LEFT JOIN #BPTWS5 a
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #ProjMbrMths  b   
			ON a.ForecastID = b.ForecastID  
			WHERE c.TestID = 4.03
			GROUP BY c.ForecastID, c.TestID 

			--------------------------------------------TestIndex12.01--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex12_01')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex12_01;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,  
			  CASE WHEN SUM(a.ESRDMemberMonths) < 600 OR (SUM(a.ESRDMemberMonths) = 0 AND ((SUM(a.CMSESRDRevenue) + SUM(a.ESRDPremium))/SUM(a.ESRDMemberMonths)) = 0) THEN 'N/A'  
						 WHEN (((SUM(a.CMSESRDRevenue) + SUM(a.ESRDPremium))/SUM(a.ESRDMemberMonths)) / ((SUM(a.CMSOtherRevenue) + SUM(a.OtherPremium))/SUM(a.MemberMonths))) >=   
								( SELECT dbo.fnGetVariableBoundLkpComplianceCheck (12.01,'x_1'))   
									AND (((SUM(a.CMSESRDRevenue) + SUM(a.ESRDPremium))/SUM(a.ESRDMemberMonths)) / ((SUM(a.CMSOtherRevenue) + SUM(a.OtherPremium))/SUM(a.MemberMonths)))   
									<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (12.01,'y_1')) THEN 'Pass'  
						 ELSE 'Fail' END AS Result,  
			  CASE WHEN SUM(a.ESRDMemberMonths) < 600 OR (SUM(a.ESRDMemberMonths) = 0 AND ((SUM(a.CMSESRDRevenue) + SUM(a.ESRDPremium))/SUM(a.MemberMonths)) = 0) THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND((((SUM(a.CMSESRDRevenue) + SUM(a.ESRDPremium))/SUM(a.ESRDMemberMonths))   
										/ ((SUM(a.CMSOtherRevenue) + SUM(a.OtherPremium))/SUM(a.MemberMonths))), 7) AS VARCHAR), 10)   
						 END AS Test_Value  
			INTO #TestIndex12_01  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 12.01
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex4.04--------------------------------------------------------------   
			--Updated 2/11  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_04')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex4_04;  
			END   

			SELECT d.ForecastID,   
			  d.TestID,   
			  Case When SUM(a.MemberMonths) = 0 THEN 'N/A'   
						 WHEN ABS(ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) / SUM(c.MemberMonths)),7)   
											- (CAST((SUM(a.MemberMonths) - SUM(a.NonDualMemberMonths))AS FLOAT) / CAST(SUM(a.MemberMonths) AS FLOAT)))  
											<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (4.04,'x_1')) THEN 'Pass' ELSE 'Fail' END AS Result,  
			  Case When SUM(a.MemberMonths) = 0 THEN 'N/A'   
						  ELSE LEFT(CAST(ROUND(ABS(ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))   
												/ SUM(c.MemberMonths)),7) - (CAST((SUM(a.MemberMonths) - SUM(a.NonDualMemberMonths)) AS FLOAT)   
												/ CAST(SUM(a.MemberMonths) AS FLOAT))), 7) AS VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex4_04  
			FROM #BaseTable d
			LEFT JOIN #MABPTValues a  
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #ProjMbrMths b   
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BPTWS5 c   
			ON a.ForecastID = c.ForecastID  
			WHERE d.TestID = 4.04
			GROUP BY d.ForecastID, d.TestID


			--------------------------------------------TestIndex4.05--------------------------------------------------------------   

			-- Updated 11/9  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_05')) IS NOT NULL  
			BEGIN     
				DROP TABLE #TestIndex4_05;  
			END   

			SELECT d.ForecastID,   
			  d.TestID,  
			  Case When a.SNPTypeName <> @DualEligible OR SUM(c.MemberMonths) = 0 THEN 'N/A'   
						 WHEN ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) / SUM(c.MemberMonths)),7)   
								>= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (4.05,'x_1')) THEN 'Pass'  
						 ELSE 'Fail' END AS Result,  
			  Case When a.SNPTypeName <> @DualEligible OR SUM(c.MemberMonths) = 0 THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) / SUM(c.MemberMonths)),7)AS VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex4_05  
			FROM #BaseTable d
			LEFT JOIN #MABPTValues a  
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #ProjMbrMths b   
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BPTWS5 c   
			ON a.ForecastID = c.ForecastID  
			WHERE d.TestID = 4.05
			GROUP BY d.ForecastID, d.TestID, a.SNPTypeName  

			--------------------------------------------TestIndex30.01--------------------------------------------------------------   
			--Updated 11/15  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex30_01')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex30_01;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  Case When (SUM(ISNULL(a.BaseAllowed, 0)) = 0 AND SUM(ISNULL(a.BaseUnits,0)) = 0) OR (SUM(ISNULL(a.BaseAllowed,0)) <> 0 AND SUM(ISNULL(a.BaseUnits,0)) <> 0) THEN 'Pass'   
					ELSE 'Fail' END AS Result,   
			  'N/A' as Test_Value  
			INTO #TestIndex30_01  
			FROM #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'k.') a  
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 30.01
			GROUP BY b.ForecastID, b.TestID  

			--------------------------------------------TestIndex30.02--------------------------------------------------------------   
			--Updated 11/15  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex30_02')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex30_02;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  Case When (SUM(ISNULL(a.BaseAllowed, 0)) = 0 AND SUM(ISNULL(a.BaseUnits,0)) = 0) OR (SUM(ISNULL(a.BaseAllowed,0)) <> 0 AND SUM(ISNULL(a.BaseUnits,0)) <> 0) THEN 'Pass'  
					ELSE 'Fail' END AS Result,   
			  'N/A' as Test_Value  
			INTO #TestIndex30_02  
			FROM #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'l.') a  
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 30.02
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex30.03--------------------------------------------------------------   
			--Updated 11/15  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex30_03')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex30_03;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  Case When (SUM(ISNULL(a.BaseAllowed, 0)) = 0 AND SUM(ISNULL(a.BaseUnits,0)) = 0) OR (SUM(ISNULL(a.BaseAllowed,0)) <> 0 AND SUM(ISNULL(a.BaseUnits,0)) <> 0) THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  'N/A' as Test_Value  
			INTO #TestIndex30_03  
			FROM #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'm.') a  
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 30.03
			GROUP BY b.ForecastID, b.TestID  

			--------------------------------------------TestIndex30.04--------------------------------------------------------------   
			--Updated 11/15  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex30_04')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex30_04;  
			END  

			SELECT b.ForecastID,   
			  b.TestID,   
			  Case When (SUM(ISNULL(a.BaseAllowed, 0)) = 0 AND SUM(ISNULL(a.BaseUnits,0)) = 0) OR (SUM(ISNULL(a.BaseAllowed,0)) <> 0 AND SUM(ISNULL(a.BaseUnits,0)) <> 0) THEN 'Pass'  
						 ELSE 'Fail' END AS Result,   
			  'N/A' as Test_Value  
			INTO #TestIndex30_04  
			FROM #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'n.') a  
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 30.04
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex30.05--------------------------------------------------------------   
			--Updated 11/15  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex30_05')) IS NOT NULL  
			BEGIN      
			DROP TABLE #TestIndex30_05;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  Case When (SUM(ISNULL(a.BaseAllowed, 0)) = 0 AND SUM(ISNULL(a.BaseUnits,0)) = 0) OR (SUM(ISNULL(a.BaseAllowed,0)) <> 0 AND SUM(ISNULL(a.BaseUnits,0)) <> 0) THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  'N/A' as Test_Value  
			INTO #TestIndex30_05  
			FROM #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'o.') a  
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 30.05
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex30.06--------------------------------------------------------------   
			--Updated 11/15  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex30_06')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex30_06;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  Case When (SUM(ISNULL(a.BaseAllowed, 0)) = 0 AND SUM(ISNULL(a.BaseUnits,0)) = 0) OR (SUM(ISNULL(a.BaseAllowed,0)) <> 0 AND SUM(ISNULL(a.BaseUnits,0)) <> 0) THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  'N/A' as Test_Value  
			INTO #TestIndex30_06  
			FROM #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'p.') a  
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 30.06
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex5.03--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex5_03')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex5_03;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,  
			  CASE WHEN (SUM(a.MemberMonths) + SUM(a.ESRDMemberMonths) + SUM(a.HospiceMemberMonths)) = 0 THEN 'N/A'   
						 WHEN (SUM(a.MemberMonths) <> 0 AND SUM(a.ESRDMemberMonths) <> 0 AND  SUM(a.HospiceMemberMonths) <> 0) THEN 'Pass'   
						 ELSE 'Fail' END AS Result,  
			  'N/A' AS Test_Value  
			INTO #TestIndex5_03  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 5.03
			GROUP BY b.ForecastID, b.TestID

			DECLARE @puertorico Varchar(8)='Puerto Rico'  

			--------------------------------------------TestIndex7.06--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_06')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex7_06   
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE 
				WHEN SUM(a.HospiceMemberMonths)/12 < 50 
			  OR SUM(a.HospiceMemberMonths) = 0 
			  OR SUM(a.MemberMonths) = 0  
			  OR ((SUM(a.CMSOtherRevenue)+ SUM(a.OtherPremium)) = 0 OR SUM(a.MemberMonths) = 0)
			  THEN 'N/A'   


						 WHEN (((SUM(a.CMSHospiceRevenue)+ SUM(a.HospicePremium))/SUM(a.HospiceMemberMonths))/ ((SUM(a.CMSOtherRevenue)+ SUM(a.OtherPremium))/SUM(a.MemberMonths)))  
									>= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (7.06,'x_1'))   
										AND (((SUM(a.CMSHospiceRevenue)+ SUM(a.HospicePremium))/SUM(a.HospiceMemberMonths))  
											  / ((SUM(a.CMSOtherRevenue)+ SUM(a.OtherPremium))/SUM(a.MemberMonths)))   
									<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (7.06,'y_1'))   
										THEN 'Pass'     
						 ELSE 'Fail' END AS Result,  
			 CASE 
				WHEN SUM(a.HospiceMemberMonths)/12 < 50 
					OR SUM(a.HospiceMemberMonths) = 0 
					OR SUM(a.MemberMonths) = 0 
					OR ((SUM(a.CMSOtherRevenue) + SUM(a.OtherPremium)) = 0 OR SUM(a.MemberMonths) = 0) 
				THEN 'N/A' 
				ELSE LEFT(CAST(ROUND((((SUM(a.CMSHospiceRevenue) + SUM(a.HospicePremium)) / SUM(a.HospiceMemberMonths)) 
						   / ((SUM(a.CMSOtherRevenue) + SUM(a.OtherPremium)) / SUM(a.MemberMonths))), 7) AS VARCHAR), 10) 
			END AS Test_Value

			INTO #TestIndex7_06  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 7.06
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex7.05--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_05')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex7_05   
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN SUM(a.HospiceMemberMonths)/12 < 50 or SUM(a.MemberMonths) = 0 OR (SUM(a.OtherNetMedicalExpenses) = 0 OR SUM(a.MemberMonths) = 0) THEN 'N/A'       
						 WHEN ((SUM(a.HospiceNetMedicalExpenses)/SUM(a.HospiceMemberMonths))/ (SUM(a.OtherNetMedicalExpenses)/SUM(a.MemberMonths)))   
							>= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (7.05,'x_1'))   
								AND ((SUM(a.HospiceNetMedicalExpenses)/SUM(a.HospiceMemberMonths))/ (SUM(a.OtherNetMedicalExpenses)/SUM(a.MemberMonths)))   
							<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (7.05,'y_1'))  
							   THEN 'Pass'     
						 ELSE 'Fail' END AS Result,   
			  CASE WHEN SUM(a.HospiceMemberMonths)/12 < 50 or SUM(a.MemberMonths) = 0 OR (SUM(a.OtherNetMedicalExpenses) = 0 OR SUM(a.MemberMonths) = 0)  THEN 'N/A'   
					ELSE LEFT(CAST(ROUND(((SUM(a.HospiceNetMedicalExpenses)/SUM(a.HospiceMemberMonths))/ (SUM(a.OtherNetMedicalExpenses)/SUM(a.MemberMonths))), 7) as VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex7_05  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 7.05
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex7.02------------------------------------------------------------  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_02')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex7_02   
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN SUM(a.ESRDMemberMonths)/12 < 50 or SUM(a.ESRDMemberMonths) = 0 OR ((SUM(a.ESRDPremium) + SUM(a.CMSESRDRevenue))=0 OR SUM(a.ESRDMemberMonths) = 0) THEN 'N/A'   
						WHEN ((SUM(a.ESRDNetMedicalExpenses)/SUM(a.ESRDMemberMonths))/ ((SUM(a.ESRDPremium) + SUM(a.CMSESRDRevenue))/SUM(a.ESRDMemberMonths))   
								>= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (7.02,'x_1'))   
										AND ((SUM(a.ESRDNetMedicalExpenses)/SUM(a.ESRDMemberMonths))  
												/ ((SUM(a.ESRDPremium) + SUM(a.CMSESRDRevenue))/SUM(a.ESRDMemberMonths)))   
								<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (7.02,'y_1'))) THEN 'Pass'   
						ELSE 'Fail' END AS Result,   
			  CASE WHEN SUM(a.ESRDMemberMonths)/12 < 50 or SUM(a.ESRDMemberMonths) = 0 OR ((SUM(a.ESRDPremium) + SUM(a.CMSESRDRevenue))=0 OR SUM(a.ESRDMemberMonths) = 0)  THEN 'N/A'   
						ELSE LEFT(CAST(ROUND(((SUM(a.ESRDNetMedicalExpenses)/SUM(a.ESRDMemberMonths))  
									/ ((SUM(a.ESRDPremium) + SUM(a.CMSESRDRevenue))/SUM(a.ESRDMemberMonths))), 7) as VARCHAR), 10)  
					END AS Test_Value  
			INTO #TestIndex7_02  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 7.02
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex17.01------------------------------------------------------------  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex17_01')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex17_01   
			END   

			SELECT b.ForecastID,   
			  b.TestID,  
			  Case When SUM(a.SecondaryPayerAdjustment) > (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (17.01,'x_1'))   
							  AND SUM(a.SecondaryPayerAdjustment) <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (17.01,'y_1')) THEN 'Pass'   
					ELSE 'Fail' END AS Result,  
			  LEFT(CAST(ROUND(SUM(a.SecondaryPayerAdjustment), 7)AS VARCHAR), 10) AS Test_Value  
			INTO #TestIndex17_01  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 17.01
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex6.05------------------------------------------------------------  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_05')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex6_05   
			END   

			SELECT d.ForecastID,   
			  d.TestID,   
			  CASE WHEN SUM(a.MemberMonths) = 0 AND SUM(b.MemberMonths) = 0 OR (SUM(c.MemberMonths) + SUM(a.ESRDMembers) + SUM(a.HospiceMembers)) = 0 THEN 'N/A'   
						 WHEN SUM(b.MemberMonths)/(SUM(c.MemberMonths) + SUM(a.ESRDMembers) + SUM(a.HospiceMembers))   
									> (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(6.05,'x_1'))   
										AND SUM(b.MemberMonths)/(SUM(c.MemberMonths) + SUM(a.ESRDMembers) + SUM(a.HospiceMembers))   
									<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(6.05,'y_1')) THEN 'Pass'   
						 ELSE 'Fail' END AS Result,  
			  CASE WHEN SUM(a.MemberMonths) = 0 AND SUM(b.MemberMonths) = 0 OR (SUM(c.MemberMonths) + SUM(a.ESRDMembers) + SUM(a.HospiceMembers)) = 0 THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND(SUM(b.MemberMonths)/(SUM(c.MemberMonths) + SUM(a.ESRDMembers) + SUM(a.HospiceMembers)), 7) AS VARCHAR), 10) 
						 END AS Test_Value  
			INTO #TestIndex6_05  
			FROM #BaseTable d 
			LEFT JOIN #MABPTValues a  
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #OOAProjectedMM b  
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BPTWS5 c  
			ON c.ForecastID = a.ForecastID  
			WHERE d.TestID = 6.05
			GROUP BY d.ForecastID, d.TestID

			--------------------------------------------TestIndex3.04------------------------------------------------------------  
			--Updated 2/11  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex3_04')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex3_04 
			END   

			SELECT d.ForecastID,   
			  d.TestID,  
			  CASE WHEN ABS(((c.ProjNonDE#RiskScore * SUM(b.ProjectedNonDEMemberMonths))   
						+ ( Case When (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) = 0 THEN AVG(a.RiskFactor)  
								 ELSE (AVG(a.RiskFactor)*SUM(a.MemberMonths) - c.ProjNonDE#RiskScore * SUM(b.ProjectedNonDEMemberMonths))   
										/ (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))   
								 END * (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)))) / SUM(a.MemberMonths) - AVG(a.RiskFactor))   
									<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.04,'x_1')) THEN 'Pass'   
						 ELSE 'Fail' END as Result,   
			   LEFT(CAST(ROUND(ABS(((c.ProjNonDE#RiskScore * SUM(b.ProjectedNonDEMemberMonths))   
					 + ( Case When (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) = 0 THEN AVG(a.RiskFactor)   
							  ELSE (AVG(a.RiskFactor)*SUM(a.MemberMonths) - c.ProjNonDE#RiskScore   
									* SUM(b.ProjectedNonDEMemberMonths)) / (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))   
							  END * (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))  
						)) / SUM(a.MemberMonths) - AVG(a.RiskFactor)), 7) AS VARCHAR), 10) as Test_Value  
			INTO #TestIndex3_04 
			FROM #BaseTable d
			LEFT JOIN #BPTWS5 a  
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #ProjMbrMths  b   
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BenchmarkSummary c   
			ON a.ForecastID = c.ForecastID  
			WHERE d.TestID = 3.04
			GROUP BY d.ForecastID, d.TestID, c.ProjNonDE#RiskScore 

			--------------------------------------------TestIndex9.03------------------------------------------------------------  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex9_03')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex9_03   
			END   

			SELECT a.ForecastID,   
			  a.TestID  
			  ,CASE WHEN @CredibilityFactor <= .2 OR ISNULL(SUM(c.BaseAllowed),0) = 0 THEN 'N/A'   
						  WHEN (SUM(b.ProjectedAllowedPMPM) - SUM(c.BaseAllowed)) / SUM(c.BaseAllowed)   
								>= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(9.03,'x_1'))   
										AND (SUM(b.ProjectedAllowedPMPM) - SUM(c.BaseAllowed)) / SUM(c.BaseAllowed)   
								<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(9.03,'y_1')) THEN 'Pass'  
						  ELSE 'Fail' END AS Result  

			  ,CASE WHEN @CredibilityFactor <= .2 OR ISNULL(SUM(c.BaseAllowed),0) = 0 THEN 'N/A'   
						  ELSE LEFT(CAST(ROUND((SUM(b.ProjectedAllowedPMPM) - SUM(c.BaseAllowed)) / SUM(c.BaseAllowed), 7) AS VARCHAR), 10) END AS Test_Value  

			INTO #TestIndex9_03  
			FROM #BaseTable a  
			LEFT JOIN #ProjAllowedPMPM b  
			ON a.ForecastID = b.ForecastID AND b.ServiceCategoryCode IN ('a.', 'b.', 'c.', 'd.', 'e.', 'f.', 'g.', 'h.', 'i.', 'j.', 'k.')  
			LEFT JOIN #MABPTWS1Summary c  
			ON b.ServiceCategoryCode = c.ServiceCategoryCode AND c.ServiceCategoryCode IN ('a.', 'b.', 'c.', 'd.', 'e.', 'f.', 'g.', 'h.', 'i.', 'j.', 'k.')  
			WHERE a.TestID = 9.03
			GROUP BY a.ForecastID, a.TestID;  

			--------------------------------------------TestIndex4.08------------------------------------------------------------  

			--Updated 2/11  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_08')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex4_08;    
			END   

			SELECT  e.ForecastID    
				   ,e.TestID  
				   , CASE   
				WHEN b.SNPTypeName = @DualEligible AND (SUM(a.DualBlendedAllowedPMPM)/SUM(a.NonDualBlendedAllowedPMPM))   
				<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(4.08,'y_1'))   
				AND (SUM(a.DualBlendedAllowedPMPM)/SUM(a.NonDualBlendedAllowedPMPM)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(4.08,'x_1'))   
				THEN 'Pass'  
				WHEN (SUM(a.DualBlendedAllowedPMPM)/SUM(a.NonDualBlendedAllowedPMPM)) <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(4.08,'y_2'))   
				AND (SUM(a.DualBlendedAllowedPMPM)/SUM(a.NonDualBlendedAllowedPMPM)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(4.08,'x_2'))   
				THEN 'Pass'  
				ELSE 'Fail'   
				END AS Result  
				, CASE 
				WHEN CAST(((SUM(c.MemberMonths) - SUM(d.ProjectedNonDEMemberMonths)) / SUM(c.MemberMonths)) AS FLOAT) < 0.1 
					AND CAST((SUM(a.DualBlendedAllowedPMPM) / SUM(a.NonDualBlendedAllowedPMPM)) AS VARCHAR) > 1.0 
				THEN CAST(1.0 AS VARCHAR) 
				  ELSE LEFT(CAST(ROUND((SUM(a.DualBlendedAllowedPMPM)/SUM(a.NonDualBlendedAllowedPMPM)), 7)AS varchar), 10) END AS Test_Value  
			INTO    #TestIndex4_08  
			FROM  #BaseTable e
			LEFT JOIN #MABPTValues b 
			ON e.ForecastID = b.ForecastID
			LEFT JOIN #MABPTWS2 a   
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BPTWS5 c   
			ON c.ForecastID = b.ForecastID  
			LEFT JOIN #ProjMbrMths d   
			ON d.ForecastID = c.ForecastID  
			WHERE e.TestID = 4.08
			GROUP BY e.ForecastID, e.TestID, b.SNPTypeName  

			--------------------------------------------TestIndex29.02------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex29_02')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex29_02;    
			END   

			SELECT  b.ForecastID    
				   ,b.TestID  
				   ,CASE WHEN (a.BlendedAllowed = 0    
							   AND  a.BlendedUtil = 0)    
							  OR (a.BlendedAllowed <> 0    
								  AND   a.BlendedUtil <> 0) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex29_02   
			FROM  #BaseTable b  
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK)  
			ON b.ForecastID = a.ForecastID AND a.ServiceCategoryCode IN ('l.')
			WHERE  b.TestID = 29.02;   

			--------------------------------------------TestIndex29.03------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex29_03')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex29_03;    
			END   

			SELECT  b.ForecastID    
				   ,b.TestID 
				   ,CASE WHEN (a.BlendedAllowed = 0    
							   AND  a.BlendedUtil = 0)    
							  OR (a.BlendedAllowed <> 0    
								  AND   a.BlendedUtil <> 0) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex29_03  
			FROM #BaseTable b
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK)    
			ON a.ForecastID = b.ForecastID AND a.ServiceCategoryCode IN ('m.')
			WHERE  b.TestID = 29.03 ;  

			--------------------------------------------TestIndex29.04------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex29_04')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex29_04;    
			END   

			SELECT  b.ForecastID    
				   ,b.TestID 
				   ,CASE WHEN (a.BlendedAllowed = 0    
							   AND  a.BlendedUtil = 0)    
							  OR (a.BlendedAllowed <> 0    
								  AND   a.BlendedUtil <> 0) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex29_04  
			FROM  #BaseTable b
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK) 
			ON b.ForecastID = a.ForecastID AND a.ServiceCategoryCode IN ('n.')
			WHERE b.TestID = 29.04;    

			--------------------------------------------TestIndex29.05------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex29_05')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex29_05;    
			END   

			SELECT  b.ForecastID   
				   ,b.TestID 
				   ,CASE WHEN (a.BlendedAllowed = 0    
							   AND  a.BlendedUtil = 0)    
							  OR (a.BlendedAllowed <> 0    
								  AND   a.BlendedUtil <> 0) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex29_05  
			FROM #BaseTable b 
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK)   
			ON b.ForecastID = a.ForecastID AND a.ServiceCategoryCode IN ('o.')
			WHERE b.TestID = 29.05  ;    

			--------------------------------------------TestIndex29.06------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex29_06')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex29_06;    
			END   

			SELECT  b.ForecastID   
				   ,b.TestID  
				   ,CASE WHEN (a.BlendedAllowed = 0    
							   AND  a.BlendedUtil = 0)    
							  OR (a.BlendedAllowed <> 0    
								  AND   a.BlendedUtil <> 0) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex29_06  
			FROM #BaseTable b
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK)  
			ON b.ForecastID = a.ForecastID AND a.ServiceCategoryCode IN ('p.')
			WHERE b.TestID = 29.06;  

			--------------------------------------------TestIndex29.07------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex29_07')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex29_07;    
			END   

			SELECT  b.ForecastID    
				   ,b.TestID 
				   ,CASE WHEN (a.BlendedAllowed = 0    
							   AND  a.BlendedUtil = 0)    
							  OR (a.BlendedAllowed <> 0    
								  AND   a.BlendedUtil <> 0) THEN 'Pass'    
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex29_07  
			FROM  #BaseTable b
			LEFT JOIN #MABPTWS2 a WITH (NOLOCK)   
			ON b.ForecastID = a.ForecastID AND  a.ServiceCategoryCode IN ('q.')
			WHERE b.TestID = 29.07 ;   

			--------------------------------------------TestIndex32.02------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex32_02')) IS NOT NULL  
			BEGIN  
				DROP TABLE dbo.#TestIndex32_02;  
			END  
			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'a.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'b.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'c.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'd.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'e.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'f.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'g.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'h.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'i.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'j.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'k.' AND b.DualEligibleTypeID = 0) <= 1  
			   THEN 'Pass' ELSE 'Fail' END AS Result ,   
			  Test_Value = 'N/A'   
			INTO #TestIndex32_02  
			FROM #BaseTable b
			LEFT JOIN #WS4Percent a  
			ON b.ForecastID = a.ForecastID
			WHERE  b.TestID = 32.02
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex32.01------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex32_01')) IS NOT NULL  
			BEGIN  
				DROP TABLE dbo.#TestIndex32_01;  
			END  
			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'a.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'b.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'c.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'd.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'e.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'f.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'g.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'h.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'i.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'j.' AND b.DualEligibleTypeID = 0) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'k.' AND b.DualEligibleTypeID = 0) <= 1  
			   THEN 'Pass' ELSE 'Fail' END AS Result ,   
			  Test_Value = 'N/A'   
			INTO #TestIndex32_01  
			FROM #BaseTable b
			LEFT JOIN #WS4Percent a  
			ON b.ForecastID = a.ForecastID
			WHERE b.TestID = 32.01
			GROUP BY b.ForecastID, b.TestID

			--------------------------------------------TestIndex6.04------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_04')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_04;    
			END   

			SELECT  b.ForecastID    
				   ,b.TestID  
				   ,CASE WHEN a.HospiceMembers > 0 THEN 'Pass'    
						 WHEN a.MemberMonths > 0 AND a.HospiceMemberMonths = 0 THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,Test_Value = LEFT(CAST(ROUND(a.HospiceMembers, 7) AS VARCHAR), 10)  
			INTO    #TestIndex6_04  
			FROM  #BaseTable b
			LEFT JOIN #MABPTValues a   
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 6.04

			--------------------------------------------TestIndex6.03------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_03')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_03;    
			END   

			SELECT  b.ForecastID   
				   ,b.TestID 
				   ,CASE WHEN a.ESRDMembers > 0 THEN 'Pass'    
						 WHEN a.MemberMonths > 0 AND a.ESRDMemberMonths = 0 THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,Test_Value = LEFT(CAST(ROUND(a.ESRDMembers, 7) AS VARCHAR), 10)  
			INTO    #TestIndex6_03  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a   
			ON b.ForecastID = a.ForecastID
			WHERE b.TestID = 6.03

			--------------------------------------------TestIndex32.04------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex32_04')) IS NOT NULL  
			BEGIN  
				DROP TABLE dbo.#TestIndex32_04;  
			END  
			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'a.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'b.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'c.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'd.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'e.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'f.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'g.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'h.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'i.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'j.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredCostShare FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'k.' AND b.DualEligibleTypeID = 1) <= 1  
			   THEN 'Pass' ELSE 'Fail' END AS Result ,   
			  Test_Value = 'N/A'   
			INTO #TestIndex32_04  
			FROM #BaseTable b
			LEFT JOIN #WS4Percent a  
			ON b.ForecastID = a.ForecastID
			WHERE b.TestID = 32.04
			GROUP BY b.ForecastID, b.TestID 

			--------------------------------------------TestIndex32.03------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex32_03')) IS NOT NULL  
			BEGIN  
				DROP TABLE dbo.#TestIndex32_03;  
			END  
			SELECT b.ForecastID,   
			  b.TestID,   
			  CASE WHEN (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'a.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'b.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'c.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'd.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'e.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'f.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'g.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'h.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'i.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'j.' AND b.DualEligibleTypeID = 1) <= 1  
			  AND (SELECT b.PercentCoveredAllowed FROM #WS4Percent b WHERE b.ServiceCategoryCode = 'k.' AND b.DualEligibleTypeID = 1) <= 1  
			   THEN 'Pass' ELSE 'Fail' END AS Result ,   
			  Test_Value = 'N/A'   
			INTO #TestIndex32_03  
			FROM #BaseTable b
			LEFT JOIN #WS4Percent a  
			ON b.ForecastID = a.ForecastID
			WHERE  b.TestID = 32.03
			GROUP BY b.ForecastID, b.TestID

			----------------------------------------------TestIndex12.02------------------------------------------------------------  

			--------------------------------------------TestIndex30.07------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex30_07')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex30_07;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,   
			  Case When (SUM(ISNULL(a.BaseAllowed, 0)) = 0 AND SUM(ISNULL(a.BaseUnits,0)) = 0) OR (SUM(ISNULL(a.BaseAllowed,0)) <> 0 AND SUM(ISNULL(a.BaseUnits,0)) <> 0) THEN 'Pass'   
						 ELSE 'Fail' END AS Result,   
			  'N/A' as Test_Value  
			INTO #TestIndex30_07  
			FROM #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'q.') a  
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 30.07
			GROUP BY b.ForecastID, b.TestID  

			--------------------------------------------TestIndex3.03------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex3_03')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex3_03;    
			END   

			SELECT  c.ForecastID   
				   ,c.TestID   
				   , CASE WHEN ISNULL(a.ProjNonDE#RiskScore, 0) = 0 THEN 'Fail'
					WHEN b.SNPTypeName = @DualEligible AND (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4))   
				   <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'y_2'))   
				   AND (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'x_2'))   
					THEN 'Pass'   
			  WHEN (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)) <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'y_1'))   
			  AND (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'x_1'))   
			  THEN 'Pass'   
			  ELSE 'Fail'   
			  END AS Result  
				   ,CASE WHEN ISNULL(a.ProjNonDE#RiskScore, 0) = 0 THEN 'Fail' 
				   ELSE LEFT(CAST(ROUND((ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)), 4) AS VARCHAR), 10) END AS Test_Value   
			INTO    #TestIndex3_03  
			FROM #BaseTable c 
			LEFT JOIN #MABPTValues b  
			ON c.ForecastID = b.ForecastID
			LEFT JOIN #BenchmarkSummary a WITH (NOLOCK)  
			ON a.ForecastID = b.ForecastID  
			WHERE c.TestID = 3.03

			--------------------------------------------TestIndex7.13------------------------------------------------------------  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_13')) IS NOT NULL  
			BEGIN
			DROP TABLE #TestIndex7_13;  
			END 

			SELECT      b.ForecastID 
					   ,b.TestID  
					   ,CASE WHEN @CredibilityFactor <= 0.2  OR ISNULL(SUM(a.BaseUnits), 0) = 0 OR c.Region = @puertorico
								 THEN 'N/A'  
							 WHEN SUM (a.BaseUnits) > (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(7.13,'x_1'))  THEN  'Pass'  
							 ELSE 'Fail' END AS Result  
					   ,CASE WHEN @CredibilityFactor <= 0.2  OR ISNULL(SUM(a.BaseUnits), 0) = 0 THEN 'N/A'   
							 ELSE LEFT(CAST(ROUND(SUM(a.BaseUnits),4) AS VARCHAR), 10) END AS Test_Value  
			INTO        #TestIndex7_13  
			FROM   #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'b.') a
			ON b.ForecastID = a.ForecastID  
			LEFT JOIN #PlanRegion c 
			ON c.ForecastID = b.ForecastID
			WHERE b.TestID = 7.13
			GROUP BY b.ForecastID, b.TestID, c.Region;

			--------------------------------------------TestIndex7.14------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex7_14')) IS NOT NULL  
			BEGIN
			DROP TABLE #TestIndex7_14;  
			END 

			SELECT      b.ForecastID 
					   ,b.TestID 
					   ,CASE WHEN @CredibilityFactor <= 0.2  OR ISNULL(SUM(a.BaseUnits), 0) = 0
								 THEN 'N/A'  
							 WHEN SUM (a.BaseUnits) > (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(7.14,'x_1'))  THEN  'Pass'  
							 ELSE 'Fail' END AS Result  
					   ,CASE WHEN @CredibilityFactor <= 0.2 OR ISNULL(SUM(a.BaseUnits), 0) = 0 THEN 'N/A'   
							 ELSE LEFT(CAST(ROUND(SUM (a.BaseUnits), 7) AS VARCHAR), 10) END AS Test_Value  
			INTO        #TestIndex7_14  
			FROM   #BaseTable b  
			LEFT JOIN (SELECT * FROM #MABPTWS1Summary WHERE ServiceCategoryCode = 'a.') a
			ON b.ForecastID = a.ForecastID  
			WHERE b.TestID = 7.14
			GROUP BY b.ForecastID, b.TestID;  
			--------------------------------------------Result-------------------------------------------------------------- 
			IF (SELECT  OBJECT_ID ('tempdb..#CalcComplianceChecks_Temp')) IS NOT NULL    
			BEGIN   
			 DROP TABLE #CalcComplianceChecks_Temp   
			END     

			SELECT  a.ForecastID    
				   ,a.TestID    
				   ,a.Result    
				   ,a.Test_Value

			INTO    #CalcComplianceChecks_Temp    
			FROM    (SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex3_02     
					 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex9_02     
					 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex9_05     
					 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
					   ,Test_Value    
					 FROM   #TestIndex13_03     
					 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex29_01    
			   UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_01     
			   UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_02    
			   UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_08   
			   UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_07   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_04   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex12_01  
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_04   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_05   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex30_01   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex30_02   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex30_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex30_04   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex30_05   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex30_06   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex5_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_06   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_05   
			 UNION ALL    
					SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_02   
			 UNION ALL    
					SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex17_01   
			 UNION ALL    
					SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_05   
			 UNION ALL    
					SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex3_04   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						 ,Test_Value    
					 FROM   #TestIndex9_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM  #TestIndex4_08   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex29_02   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex29_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex29_04   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex29_05  
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex29_06   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex29_07   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex32_02   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex32_01   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_04   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex32_04   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex32_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex30_07   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex3_03   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_13   
			 UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex7_14   
			   ) a; 
			   INSERT INTO dbo.CalcComplianceChecks    
				(ForecastID    
				,TestID    
				,Result    
				,Test_Value
				,UserID)    
			SELECT  ForecastID    
				   ,TestID    
				   ,Result    
				   ,Test_Value,
				   @LastUpdateByID 
			FROM   #CalcComplianceChecks_Temp ;    

			END
			 ------********Start of CheckType PD********-----------
			ELSE 
			IF @CheckType = 'PD'

			--------------------------------------------TestIndex3.02------------------------------------------------------------ 

			BEGIN
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex3_02_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex3_02_PD;    
			END   
			  SELECT  b.ForecastID    
				   , b.TestID ,   
				   CASE WHEN (ROUND (a.ProjNonDE#RiskScore, 4) <> 0    
							   AND  ROUND (a.ProjDE#RiskScore, 4) <> 0)    
							  OR ROUND (a.ProjNonDE#RiskScore, 4) = ROUND (a.ProjW8edRiskScore, 4)    
							  OR ROUND (a.ProjDE#RiskScore, 4) = ROUND (a.ProjW8edRiskScore, 4) THEN 'Pass'   
						 ELSE 'Fail' END AS Result    
				   ,Test_Value = 'N/A'    
			INTO    #TestIndex3_02_PD 
			FROM #BaseTable b   
			LEFT JOIN #BenchmarkSummary a ON a.ForecastID = b.ForecastID  
			WHERE b.TestID = 3.02


			--------------------------------------------TestIndex3.03------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex3_03_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex3_03_PD;    
			END   

			SELECT  c.ForecastID   
				   ,c.TestID   
				   ,CASE WHEN ISNULL(a.ProjNonDE#RiskScore, 0) = 0 THEN 'Fail'
				   WHEN b.SNPTypeName = @DualEligible AND (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4))   
				   <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'y_2'))   
				   AND (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'x_2'))   
					THEN 'Pass'   
			  WHEN (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)) <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'y_1'))   
			  AND (ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.03,'x_1'))   
			  THEN 'Pass'   
			  ELSE 'Fail'   
			  END AS Result  
				   ,CASE WHEN ISNULL(a.ProjNonDE#RiskScore, 0) = 0 THEN 'Fail' 
				   ELSE LEFT(CAST(ROUND((ROUND(a.ProjDE#RiskScore,4)/ROUND(a.ProjNonDE#RiskScore,4)), 4) AS VARCHAR), 10) END AS Test_Value   
			INTO    #TestIndex3_03_PD  
			FROM #BaseTable c 
			LEFT JOIN #MABPTValues b  
			ON c.ForecastID = b.ForecastID
			LEFT JOIN #BenchmarkSummary a WITH (NOLOCK)  
			ON a.ForecastID = b.ForecastID  
			WHERE c.TestID = 3.03 

			--------------------------------------------TestIndex3.04------------------------------------------------------------  
			--Updated 2/11  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex3_04_PD')) IS NOT NULL  
			BEGIN   
			 DROP TABLE #TestIndex3_04_PD 
			END   

			SELECT d.ForecastID,   
			  d.TestID,  
			  CASE WHEN ABS(((c.ProjNonDE#RiskScore * SUM(b.ProjectedNonDEMemberMonths))   
						+ ( Case When (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) = 0 THEN AVG(a.RiskFactor)  
								 ELSE (AVG(a.RiskFactor)*SUM(a.MemberMonths) - c.ProjNonDE#RiskScore * SUM(b.ProjectedNonDEMemberMonths))   
										/ (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))   
								 END * (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)))) / SUM(a.MemberMonths) - AVG(a.RiskFactor))   
									<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.04,'x_1')) THEN 'Pass'   
						 ELSE 'Fail' END as Result,   
			   LEFT(CAST(ROUND(ABS(((c.ProjNonDE#RiskScore * SUM(b.ProjectedNonDEMemberMonths))   
					 + ( Case When (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) = 0 THEN AVG(a.RiskFactor)   
							  ELSE (AVG(a.RiskFactor)*SUM(a.MemberMonths) - c.ProjNonDE#RiskScore   
									* SUM(b.ProjectedNonDEMemberMonths)) / (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))   
							  END * (SUM(a.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))  
						)) / SUM(a.MemberMonths) - AVG(a.RiskFactor)), 7) AS VARCHAR), 10) as Test_Value  
			INTO #TestIndex3_04_PD
			FROM #BaseTable d
			LEFT JOIN #BPTWS5 a  
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #ProjMbrMths  b   
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BenchmarkSummary c   
			ON a.ForecastID = c.ForecastID  
			WHERE d.TestID = 3.04
			GROUP BY d.ForecastID, d.TestID, c.ProjNonDE#RiskScore 

			--------------------------------------------TestIndex4.04--------------------------------------------------------------   
			--Updated 2/11  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_04_PD')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex4_04_PD;  
			END   

			SELECT d.ForecastID,   
			  d.TestID,   
			  Case When SUM(a.MemberMonths) = 0 THEN 'N/A'   
						 WHEN ABS(ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) / SUM(c.MemberMonths)),7)   
											- (CAST((SUM(a.MemberMonths) - SUM(a.NonDualMemberMonths))AS FLOAT) / CAST(SUM(a.MemberMonths) AS FLOAT)))  
											<= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (4.04,'x_1')) THEN 'Pass' ELSE 'Fail' END AS Result,  
			  Case When SUM(a.MemberMonths) = 0 THEN 'N/A'   
						  ELSE LEFT(CAST(ROUND(ABS(ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths))   
												/ SUM(c.MemberMonths)),7) - (CAST((SUM(a.MemberMonths) - SUM(a.NonDualMemberMonths)) AS FLOAT)   
												/ CAST(SUM(a.MemberMonths) AS FLOAT))), 7) AS VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex4_04_PD 
			FROM #BaseTable d
			LEFT JOIN #MABPTValues a  
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #ProjMbrMths b   
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BPTWS5 c   
			ON a.ForecastID = c.ForecastID  
			WHERE d.TestID = 4.04
			GROUP BY d.ForecastID, d.TestID

			--------------------------------------------TestIndex4.05--------------------------------------------------------------   

			-- Updated 11/9  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_05_PD')) IS NOT NULL  
			BEGIN     
				DROP TABLE #TestIndex4_05_PD;  
			END   

			SELECT d.ForecastID,   
			  d.TestID,  
			  Case When a.SNPTypeName <> @DualEligible OR SUM(c.MemberMonths) = 0 THEN 'N/A'   
						 WHEN ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) / SUM(c.MemberMonths)),7)   
								>= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (4.05,'x_1')) THEN 'Pass'  
						 ELSE 'Fail' END AS Result,  
			  Case When a.SNPTypeName <> @DualEligible OR SUM(c.MemberMonths) = 0 THEN 'N/A'   
						 ELSE LEFT(CAST(ROUND(((SUM(c.MemberMonths) - SUM(b.ProjectedNonDEMemberMonths)) / SUM(c.MemberMonths)),7)AS VARCHAR), 10) END AS Test_Value  
			INTO #TestIndex4_05_PD 
			FROM #BaseTable d
			LEFT JOIN #MABPTValues a  
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #ProjMbrMths b   
			ON a.ForecastID = b.ForecastID  
			LEFT JOIN #BPTWS5 c   
			ON a.ForecastID = c.ForecastID  
			WHERE d.TestID = 4.05
			GROUP BY d.ForecastID, d.TestID, a.SNPTypeName  


			--------------------------------------------TestIndex5.03--------------------------------------------------------------   
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex5_03_PD')) IS NOT NULL  
			BEGIN  
			DROP TABLE #TestIndex5_03_PD;  
			END   

			SELECT b.ForecastID,   
			  b.TestID,  
			  CASE WHEN (SUM(a.MemberMonths) + SUM(a.ESRDMemberMonths) + SUM(a.HospiceMemberMonths)) = 0 THEN 'N/A'   
						 WHEN (SUM(a.MemberMonths) <> 0 AND SUM(a.ESRDMemberMonths) <> 0 AND  SUM(a.HospiceMemberMonths) <> 0) THEN 'Pass'   
						 ELSE 'Fail' END AS Result,  
			  'N/A' AS Test_Value  
			INTO #TestIndex5_03_PD
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a  
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 5.03
			GROUP BY b.ForecastID, b.TestID  

			--------------------------------------------TestIndex6.03------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_03_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_03_PD;    
			END   

			SELECT  b.ForecastID   
				   ,b.TestID 
				   ,CASE WHEN a.ESRDMembers > 0 THEN 'Pass'    
						 WHEN a.MemberMonths > 0 AND a.ESRDMemberMonths = 0 THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,Test_Value = LEFT(CAST(ROUND(a.ESRDMembers, 7) AS VARCHAR), 10) 
			INTO    #TestIndex6_03_PD  
			FROM #BaseTable b
			LEFT JOIN #MABPTValues a   
			ON b.ForecastID = a.ForecastID
			WHERE b.TestID = 6.03

			--------------------------------------------TestIndex6.04------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_04_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_04_PD;    
			END   

			SELECT  b.ForecastID    
				   ,b.TestID  
				   ,CASE WHEN a.HospiceMembers > 0 THEN 'Pass'    
						 WHEN a.MemberMonths > 0 AND a.HospiceMemberMonths = 0 THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,Test_Value = LEFT(CAST(ROUND(a.HospiceMembers, 7) AS VARCHAR), 10)  
			INTO    #TestIndex6_04_PD 
			FROM  #BaseTable b
			LEFT JOIN #MABPTValues a   
			ON a.ForecastID = b.ForecastID
			WHERE b.TestID = 6.04

			--------------------------------------------TestIndex2.05------------------------------------------------------------  
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex2_05_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex2_05_PD;    
			END   

			SELECT  c.ForecastID    
				   ,c.TestID
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN b.ContractPBP IS NOT NULL THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex2_05_PD  
			FROM  #BaseTable  c
			LEFT JOIN #MABPTValues a   
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 2.05
			GROUP BY c.ForecastID, c.TestID,a.MAPD, b.ContractPBP

			--------------------------------------------TestIndex2.06------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex2_06_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex2_06_PD;    
			END   

			SELECT  c.ForecastID    
				   ,c.TestID 
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN b.ProductType = a.PlanTypeName THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex2_06_PD  
			FROM #BaseTable c 
			LEFT JOIN #MABPTValues a   
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 2.06
			GROUP BY c.ForecastID, c.TestID, a.MAPD, b.ProductType, a.PlanTypeName

			--------------------------------------------TestIndex2.07------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex2_07_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex2_07_PD;    
			END   

			SELECT  c.ForecastID   
				   ,c.TestID
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN UPPER(b.OrganizationName) = UPPER(a.OrganizationName) AND b.OrganizationName = UPPER(b.OrganizationName) AND a.OrganizationName = UPPER(a.OrganizationName) THEN 'Pass' 
				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex2_07_PD  
			FROM  #BaseTable c
			LEFT JOIN #MABPTValues a  
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 2.07
			GROUP BY c.ForecastID, c.TestID, a.MAPD, a.OrganizationName, b.OrganizationName

			--------------------------------------------TestIndex2.08------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex2_08_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex2_08_PD;    
			END   

			SELECT  c.ForecastID   
				   ,c.TestID
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN b.PlanName = a.PlanName THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex2_08_PD  
			FROM #BaseTable c
			LEFT JOIN #MABPTValues a   
			ON a.ForecastID = c.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 2.08
			GROUP BY c.ForecastID, c.TestID, a.MAPD, b.PlanName, a.PlanName

			--------------------------------------------TestIndex2.09------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex2_09_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex2_09_PD;    
			END   

			SELECT  c.ForecastID  
				   ,c.TestID  
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN b.SnpTypeName = 'N/A' AND a.SNPTypeName = 'N/A' THEN 'Pass' 
						 WHEN (b.SnpTypeName = 'DE' OR b.SnpTypeName = 'Full DE' OR b.SnpTypeName = 'Partial DE') AND a.SNPTypeName = 'Dual-Eligible' THEN 'Pass'
						 WHEN b.SnpTypeName = 'ISNP' AND a.SNPTypeName = 'Institutional' THEN 'Pass'
						 WHEN b.SnpTypeName <> 'N/A' 
							AND b.SnpTypeName <> 'DE' 
							AND b.SnpTypeName <> 'Full DE' 
							AND b.SnpTypeName <> 'Partial DE' 
							AND b.SnpTypeName <> 'ISNP' 
							AND a.SNPTypeName = 'Chronic or Disabling Condition' 
					THEN 'Pass'

				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex2_09_PD  
			FROM  #BaseTable c  
			LEFT JOIN #MABPTValues a 
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 2.09
			GROUP BY c.ForecastID, c.TestID, a.MAPD, b.SnpTypeName, a.SNPTypeName


			--------------------------------------------TestIndex3.01------------------------------------------------------------  

			-- Updated 3/6 for DIV/0 Error
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex3_01_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex3_01_PD;    
			END   

			SELECT
				c.ForecastID,
				c.TestID,
			CASE
				WHEN @CredibilityFactor <= 0.2 THEN 'N/A'
				WHEN ISNULL(AVG(b.ProjW8edRiskScore),0) = 0 OR ISNULL(AVG(a.RiskScore),0) = 0 THEN 'Fail'
				WHEN ((ROUND(AVG(b.ProjW8edRiskScore), 4) - AVG(a.RiskScore)) / AVG(a.RiskScore)) <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.01, 'y_1'))
						AND ((ROUND(AVG(b.ProjW8edRiskScore), 4) - AVG(a.RiskScore)) / AVG(a.RiskScore)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck(3.01, 'x_1'))
				THEN 'Pass'
				ELSE 'Fail'
			END AS Result,

				CASE
					WHEN @CredibilityFactor <= 0.2 THEN 'N/A'
							WHEN ISNULL(AVG(a.RiskScore),0) = 0 THEN 'Fail'
					ELSE LEFT(CAST(ROUND((ROUND(AVG(b.ProjW8edRiskScore), 4) - AVG(a.RiskScore)) / AVG(a.RiskScore), 4) AS VARCHAR(MAX)), 10)
				END AS Test_Value
			INTO #TestIndex3_01_PD
			FROM #BaseTable c
			LEFT JOIN #MABPTValues a
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #BenchmarkSummary b ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 3.01
			GROUP BY c.ForecastID, c.TestID


			--------------------------------------------TestIndex4.06------------------------------------------------------------  

			-- Updated 1/23
			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_06_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex4_06_PD;    
			END   

			SELECT  d.ForecastID    
				   ,d.TestID
				   ,CASE WHEN a.SNPTypeName = @DualEligible OR a.SNPTypeName = 'Chronic or Disabling Condition' OR a.SNPTypeName = 'Institutional' THEN 'N/A'    
					 WHEN SUM(b.MemberMonths) = 0 THEN 'N/A'
					 WHEN (SUM(c.ProjectedDEMemberMonths) / SUM(b.MemberMonths)) <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (4.06,'x_1')) THEN 'Pass'
				ELSE 'Fail' END AS Result    
				   , CASE WHEN a.SNPTypeName = @DualEligible OR a.SNPTypeName = 'Chronic or Disabling Condition' OR a.SNPTypeName = 'Institutional' THEN 'N/A' 
				   WHEN SUM(b.MemberMonths) = 0 THEN 'N/A'
				   ELSE LEFT(CAST(ROUND((SUM(c.ProjectedDEMemberMonths) / SUM(b.MemberMonths)), 7) AS VARCHAR(MAX)), 10)
				   END AS Test_Value 
			INTO    #TestIndex4_06_PD  
			FROM #BaseTable d   
			LEFT JOIN #MABPTValues a   
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #BPTWS5 b  
			ON b.ForecastID = a.ForecastID  
			LEFT JOIN #ProjDEMbrMths  c  
			ON a.ForecastID = c.ForecastID 
			WHERE d.TestID = 4.06
			GROUP BY d.ForecastID, d.TestID, a.SNPTypeName

			--------------------------------------------TestIndex4.07------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex4_07_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex4_07_PD;    
			END   

			SELECT  d.ForecastID   
				   ,d.TestID 
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN (SUM(b.ProjectedMM) * SUM(b.PlanYearLIPercent)) = 0 AND SUM(c.ProjectedDEMemberMonths) = 0 THEN 'Pass' 
						 WHEN (SUM(b.ProjectedMM) * SUM(b.PlanYearLIPercent)) = 0 AND SUM(c.ProjectedDEMemberMonths) <> 0 THEN 'Fail'
						 WHEN SUM(c.ProjectedDEMemberMonths) / (SUM(b.ProjectedMM) * SUM(b.PlanYearLIPercent) ) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (4.07,'x_1')) AND 
						 SUM(c.ProjectedDEMemberMonths) / (SUM(b.ProjectedMM) * SUM(b.PlanYearLIPercent) ) <= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (4.07,'y_1')) THEN 'Pass'
				ELSE 'Fail' END AS Result    
				   , CASE WHEN a.MAPD = 'N' THEN 'N/A' 
				   WHEN (SUM(b.ProjectedMM) * SUM(b.PlanYearLIPercent)) = 0 AND SUM(c.ProjectedDEMemberMonths) = 0 THEN 'N/A'
				   WHEN (SUM(b.ProjectedMM) * SUM(b.PlanYearLIPercent)) = 0 AND SUM(c.ProjectedDEMemberMonths) <> 0 THEN 'N/A'
				   ELSE LEFT(CAST(ROUND(SUM(c.ProjectedDEMemberMonths) / (SUM(b.ProjectedMM) * SUM(b.PlanYearLIPercent) ), 7) AS VARCHAR(MAX)), 10) END AS Test_Value 
			INTO    #TestIndex4_07_PD  
			FROM #BaseTable d   
			LEFT JOIN #MABPTValues a 
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			LEFT JOIN #ProjDEMbrMths  c  
			ON a.ForecastID = c.ForecastID 
			WHERE d.TestID = 4.07
			GROUP BY d.ForecastID, d.TestID, a.MAPD

			--------------------------------------------TestIndex5.01------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex5_01_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex5_01_PD;    
			END   

			SELECT  c.ForecastID   
				   ,c.TestID
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN (SUM(a.MemberMonths) + SUM(a.ESRDMemberMonths) + SUM(a.HospiceMemberMonths)) = 0 AND SUM(b.BaseMemberMonths) <> 0 THEN 'Fail'
						 WHEN (SUM(a.MemberMonths) + SUM(a.ESRDMemberMonths) + SUM(a.HospiceMemberMonths)) = SUM(b.BaseMemberMonths) THEN 'Pass'
				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex5_01_PD  
			FROM #BaseTable c   
			LEFT JOIN #MABPTValues a  
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 5.01
			GROUP BY c.ForecastID, c.TestID, a.MAPD

			--------------------------------------------TestIndex6.01------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_01_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_01_PD;    
			END   

			SELECT d.ForecastID    
				   ,d.TestID
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
				   WHEN (SUM(c.MemberMonths) + SUM(a.ESRDMembers) + SUM(a.HospiceMembers)) >= (SELECT dbo.fnGetVariableBoundLkpComplianceCheck (6.01,'x_1')) THEN 'Pass'
				ELSE 'Fail' END AS Result    
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A' 
				   ELSE LEFT(CAST(ROUND((SUM(c.MemberMonths) + SUM(a.ESRDMembers) + SUM(a.HospiceMembers)), 7) AS VARCHAR(MAX)), 10) END AS Test_Value 
			INTO    #TestIndex6_01_PD  
			FROM #BaseTable d  
			LEFT JOIN #MABPTValues a
			ON d.ForecastID = a.ForecastID 
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			LEFT  JOIN #BPTWS5 c  
			ON c.ForecastID = a.ForecastID 
			WHERE d.TestID = 6.01
			GROUP BY d.ForecastID, d.TestID, a.MAPD

			--------------------------------------------TestIndex6.02------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_02_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_02_PD;    
			END   

			SELECT  d.ForecastID    
				   ,d.TestID
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A' 
					   WHEN  c.SegmentID <> '000' AND (ROUND(SUM(e.MemberMonths) + SUM(a.ESRD) + SUM(a.Hospice), 0) - ROUND(SUM(c.ProjectedMM),0)) = 0 THEN 'Pass'
					   WHEN c.SegmentID = '000' AND (ROUND(SUM(e.MemberMonths) + SUM(a.ESRD) + SUM(a.Hospice), 0) = ROUND(SUM(c.ProjectedMM),0)) THEN 'Pass'
				   ELSE 'Fail' END AS Result 
				   ,'N/A' AS Test_Value
			INTO    #TestIndex6_02_PD  
			FROM #BaseTable d
			LEFT JOIN #WS5Contract e
			ON e.ForecastID = d.ForecastID
			LEFT JOIN #MABPTValuesContract a
			ON a.ForecastID = e.ForecastID
			LEFT JOIN #PDAuditExhibit c
			ON a.ForecastID = c.ForecastID
			WHERE d.TestID = 6.02
			GROUP BY d.ForecastID, d.TestID, a.MAPD, c.SegmentID

			--------------------------------------------TestIndex20.07------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex20_07_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex20_07_PD;    
			END   

			SELECT c.ForecastID    
				   ,c.TestID 
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN SUM(a.RxBasicPremium) = SUM(b.BasicPremiumRounded) AND SUM(a.RxSuppPremium) = SUM(b.SupplementalPremiumRounded) THEN 'Pass'   
				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex20_07_PD  
			FROM #BaseTable c   
			LEFT JOIN #MABPTValues a
			ON c.ForecastID = a.ForecastID 
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 20.07
			GROUP BY c.ForecastID, c.TestID, a.MAPD

			--------------------------------------------TestIndex6.001------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_001_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_001_PD;    
			END   

			SELECT  c.ForecastID    
				   ,c.TestID
				   ,CASE WHEN b.AltRebateOrder = 0 THEN 'Pass'    
				ELSE 'Fail' END AS Result    
				   ,CASE WHEN b.AltRebateOrder = 0 THEN 'N/A' 
				   ELSE 'Confirm Intentional' 
				   END AS Test_Value 
			INTO    #TestIndex6_001_PD  
			FROM #BaseTable c   
			LEFT JOIN #MABPTValues a
			ON c.ForecastID = a.ForecastID
			LEFT JOIN #SavedPlanHeader b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 6.001
			GROUP BY c.ForecastID, c.TestID, b.AltRebateOrder

			--------------------------------------------TestIndex6.005------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex6_005_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex6_005_PD;    
			END   

			SELECT  d.ForecastID   
				   ,d.TestID
				   ,CASE WHEN ISNULL(COUNT(c.ForecastID),0) = 0 THEN 'N/A'
				   WHEN SUM(c.BasicPremiumRounded) / COUNT(c.ForecastID) = SUM(a.RxBasicPremium) AND SUM(c.SupplementalPremiumRounded) / COUNT(c.ForecastID) = SUM(a.RxSuppPremium) THEN 'Pass'    
				ELSE 'Fail' END AS Result    
				   , 'N/A' AS Test_Value 
			INTO    #TestIndex6_005_PD  
			FROM #BaseTable d   
			LEFT JOIN #MABPTValues a
			ON d.ForecastID = a.ForecastID
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			LEFT JOIN #PDAuditExhibit_Contract c
			ON c.ForecastID = a.ForecastID
			WHERE d.TestID = 6.005
			GROUP BY d.ForecastID, d.TestID

			--------------------------------------------TestIndex20.08------------------------------------------------------------  

			IF (SELECT  OBJECT_ID ('tempdb..#TestIndex20_08_PD')) IS NOT NULL    
			BEGIN  
			DROP TABLE #TestIndex20_08_PD;    
			END   

			SELECT  c.ForecastID   
				   ,c.TestID
				   ,CASE WHEN a.MAPD = 'N' THEN 'N/A'    
						 WHEN ROUND(SUM(a.RxBasicPremium) - SUM(a.RxBasicBuyDown), 2) >= 0 THEN 'Pass'   
						 WHEN ROUND(SUM(a.RxBasicPremium) - SUM(a.RxBasicBuyDown), 2) < 0 AND ROUND(SUM(a.RxSuppPremium) - SUM(a.RxSuppBuyDown), 2) + ROUND(SUM(a.RxBasicPremium) - SUM(a.RxBasicBuyDown), 2) >= 0 THEN 'Pass'
				ELSE 'Fail' END AS Result    
				   ,'N/A' AS Test_Value 
			INTO    #TestIndex20_08_PD  
			FROM #BaseTable c   
			LEFT JOIN #MABPTValues a
			ON c.ForecastID = a.ForecastID 
			LEFT JOIN #PDAuditExhibit b
			ON a.ForecastID = b.ForecastID
			WHERE c.TestID = 20.08
			GROUP BY c.ForecastID, c.TestID, a.MAPD

			--------------------------------------------Result--------------------------------------------------------------    
			  IF (SELECT  OBJECT_ID ('tempdb..#CalcComplianceChecks_Temp_PD')) IS NOT NULL    
				BEGIN   
				 DROP TABLE #CalcComplianceChecks_Temp_PD  
				END     

			SELECT  a.ForecastID    
				   ,a.TestID    
				   ,a.Result    
				   ,a.Test_Value    
			INTO    #CalcComplianceChecks_Temp_PD    
			FROM    (SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex3_02_PD  
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex3_03_PD 
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex3_04_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_04_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_05_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex5_03_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_03_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_04_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex2_05_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex2_06_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex2_07_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex2_08_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex2_09_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex3_01_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_06_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex4_07_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex5_01_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_01_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_02_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex20_07_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex20_08_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_001_PD
				UNION ALL    
					 SELECT ForecastID    
						   ,TestID    
						   ,Result    
						   ,Test_Value    
					 FROM   #TestIndex6_005_PD
					 )a;

			INSERT INTO dbo.CalcComplianceChecks    
				(ForecastID    
				,TestID    
				,Result    
				,Test_Value,UserID)    
			SELECT  ForecastID    
				   ,TestID    
				   ,Result    
				   ,Test_Value ,@LastUpdateByID   
			FROM    #CalcComplianceChecks_Temp_PD ;  
			END

			ELSE
			BEGIN
			PRINT 'Use in future';
			END  

	END TRY  

	BEGIN CATCH      
	-- PRINT 'in inner begin catch'
	 DECLARE @InnerErrorMessage NVARCHAR(4000)        
	 DECLARE @InnerErrorSrc VARCHAR(MAX) = ISNULL(Error_procedure(), 'SQL'),         
                  @Innercurrentdate DATETIME= GETDATE() ,  
      @InnerException VARCHAR(MAX) = ERROR_MESSAGE()  
		  --PRINT @InnerException 

		IF (ISNULL(@CPS, '') <> '' OR ISNULL(@ForecastID, 0) <> 0)
		BEGIN
		  SET @ReturnErrorMessage = 'Error occured for Forecast ID:' + CAST(@ForecastID AS VARCHAR(100)) + ' and CPS Code ' + @CPS + '|'  
		END
	   SET @InnerErrorMessage =    @ReturnErrorMessage + ' ' + Error_message() +  'Line Number :'+ CAST (Error_line() AS VARCHAR(5))  


		 --PRINT @InnerErrorMessage  
		 --PRINT @ReturnErrorMessage
	
	  
	  ---Insert into app log for logging error------------------           
	       EXEC Spappaddlogentry         
            @Innercurrentdate,         
            '',         
            'ERROR',         
            @InnerErrorSrc,         
            @InnerErrorMessage,         
            @InnerException,         
            @LastUpdateByID         
	  END CATCH;       
	
	END -- else END  
   

SELECT b.HPASID AS CPS    
   ,a.ForecastID    
   ,a.TestID    
   ,c.TestName    
   ,a.Result    
   ,a.Test_Value    
FROM dbo.CalcComplianceChecks a with (NOLOCK)  
Inner JOIN dbo.SavedForecastSetup b with (NOLOCK)  
ON a.ForecastID = b.ForecastID  AND b.PlanYear = dbo.fnGetBidYear()  
LEFT JOIN dbo.SavedPlanInfo sp with (NOLOCK)  
ON b.PlanInfoID = sp.PlanInfoID    
LEFT JOIN dbo.ComplianceChecksHeader c with (NOLOCK)  
ON a.TestID = c.TestID AND c.CheckType = @CheckType
WHERE a.UserID = @LastUpdateByID AND a.ForecastID = @XForecastId




SELECT @cols = STRING_AGG(QUOTENAME(TestID), ',') FROM (SELECT DISTINCT TestID FROM dbo.ComplianceChecksHeader WHERE CheckType = @CheckType) AS tmp;

SET @query = N'
SELECT *
FROM
(
    SELECT sfs.HPASID AS CPS, ISNULL(fail.TotalFails,0) AS TotalFails, ccc.Result, ccc.TestID
    FROM dbo.CalcComplianceChecks ccc
    LEFT JOIN
    (
        SELECT ForecastID, ISNULL(COUNT(Result),0) AS TotalFails
        FROM dbo.CalcComplianceChecks
        WHERE Result = ''Fail''
        GROUP BY ForecastID
    ) fail
    ON fail.ForecastID = ccc.ForecastID
    LEFT JOIN SavedForecastSetup sfs
    ON sfs.ForecastID = ccc.ForecastID
	where ccc.UserID = @LastUpdateByID and ccc.ForecastID = @XForecastId
) src
PIVOT
(
    MAX(Result) FOR TestID IN (' + @cols + ')
) piv';


EXECUTE sp_executesql @query, N'@LastUpdateByID char(7),@XForecastId Int', @LastUpdateByID = @LastUpdateByID,@XForecastId =@XForecastId;
DELETE FROM dbo.CalcComplianceChecks WHERE UserID = @LastUpdateByID AND ForecastID = @XForecastId;  

END TRY  

BEGIN CATCH      
--    PRINT 'in final begin catch'
     DECLARE @ErrorMessage NVARCHAR(4000);         
          DECLARE @ErrorSeverity INT;         
          DECLARE @ErrorState INT;         
          DECLARE @ErrorException NVARCHAR(4000);         
          DECLARE @errSrc      VARCHAR(MAX) =ISNULL(ERROR_PROCEDURE(), 'SQL'),         
                  @currentdate DATETIME=GETDATE()         

          SELECT @ErrorMessage = ERROR_MESSAGE(),         
                 @ErrorSeverity = ERROR_SEVERITY(),         
                 @ErrorState = ERROR_STATE(),         
                 @ErrorException = 'Line Number :'         
                                   + CAST(ERROR_LINE() AS VARCHAR)         
         + ' .Error Severity :'         
                                   + CAST(@ErrorSeverity AS VARCHAR)         
                                   + ' .Error State :'         
                                   + CAST(@ErrorState AS VARCHAR)         

    EXEC dbo.Spappaddlogentry         
            @currentdate,         
            '',         
            'ERROR',         
            @errSrc,         
            @ErrorMessage,         
            @ErrorException,         
            @LastUpdateByID    

      END CATCH;     

    END;
GO