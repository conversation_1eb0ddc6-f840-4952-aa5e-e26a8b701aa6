SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author: <PERSON> Smith  
-- Create date: 31-08-2018
-- Description:  Remove a ForecastId from Filter User Prefrence
--      
--      
-- PARAMETERS:      
-- Input:   ForecastId, UserID     
    
-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Aug-31  1		  Initial version.											Rodney Smith 
-- 2018-DEC-17  3         Added SELECT ERROR_MESSAGE() AS ErrorMessage              Kritika Singh
--                        in catch block
-- 2018-Dec-18	4		  Updated Error message and included logging exception	    Pooja Dahiya
--2019-June-03 5          Updated the left condition								Kiran Pant
-- ----------------------------------------------------------------------------------------------------------------------       

CREATE PROCEDURE [dbo].[spAppRemoveForecastIdFromFilter]
    @ForecastId VARCHAR(7),
    @LastUpdateByID VARCHAR(7),
	@Result BIT OUT
AS
    BEGIN   
        BEGIN TRANSACTION;
        BEGIN TRY        

			DECLARE @NewForecastIdList VARCHAR(MAX);

			Set @NewForecastIdList=''
			Select @NewForecastIdList=@NewForecastIdList + Coalesce([Value]+ ',','') from (Select Value FROM dbo.fnStringSplit((select SelectedForecastIdList from [dbo].[AppSavedFilterUserPreference] where UserID = @LastUpdateByID) ,',') WHERE  Value <> @ForecastId  ) A
			--Set @NewForecastIdList=Left(@NewForecastIdList,LEN(@NewForecastIdList)-1)
			Set @NewForecastIdList= LEFT(@NewForecastIdList, 
            CASE WHEN LEN(@NewForecastIdList) < 0 
                 THEN LEN(@NewForecastIdList) 
                 ELSE LEN(@NewForecastIdList) END) 
		 IF (@NewForecastIdList != '')
                BEGIN
                    UPDATE  [dbo].[AppSavedFilterUserPreference]
                    SET     SelectedForecastIdList = @NewForecastIdList ,
                            LastUpdateDateTime = GETDATE()
                    WHERE   UserID = @LastUpdateByID;
	            END;
            SET @Result = 1; 
            COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
				SET @Result = 0;
			--SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
			ROLLBACK TRANSACTION; 

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
            
        END CATCH; 
    END;
GO
