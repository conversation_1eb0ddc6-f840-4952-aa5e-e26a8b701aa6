-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportOtherSNPAdjustmentStagedData
-- This sp is executed by the MAAUI => Data Owner Inputs screen,
-- to import the valid Other SNP Adjustment data into TrendAdj_OSNP_SavedPopRegression table. 
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-Oct-21      1           Initial Version                                                     Kumar Jalendran
-- 2025-Jan-23      2           Changed TRUNCATE to DELETE                                          Kumar Jalendran
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spAppImportOtherSNPAdjustmentStagedData]
(
    @StageId VARCHAR(100)
)
AS
BEGIN
    SET NOCOUNT ON; -- Suppress row count messages
    SET ANSI_WARNINGS OFF; -- Suppress ANSI warning messages 

    BEGIN TRY
        BEGIN TRANSACTION trans_ImportOtherSNPAdjustment; 

        DECLARE @jsonData VARCHAR(MAX);
        DECLARE @UserId VARCHAR(7);

        SELECT @jsonData = JsonData, @UserId = UserId
        FROM dbo.ImportDataStaging WITH (NOLOCK)
        WHERE StageId = @StageId;

        DECLARE @tbl__importData TABLE
        (
            PlanYear SMALLINT,
            CPS VARCHAR(13),
            SNPTypeDetail NVARCHAR(50),
            MemberMonths DECIMAL(38, 8),	
            WtdRelativity DECIMAL(38, 8),	
            LastUpdateByID CHAR(7),
            LastUpdateDateTime DATETIME
        );
        
        INSERT INTO @tbl__importData
        (
            PlanYear,
            CPS,
            SNPTypeDetail,
            MemberMonths,
            WtdRelativity,
            LastUpdateByID, 
            LastUpdateDateTime
        )
        SELECT 
            PlanYear,
            SUBSTRING(TRIM(CPS), 1, 13),
            SUBSTRING(TRIM(SNPTypeDetail), 1, 50),
            MemberMonths,
            WtdRelativity,							
            @UserId,
            GETDATE()
        FROM OPENJSON(@jsonData, '$.OtherSNPAdjustment')
        WITH
        (
            PlanYear SMALLINT,
            CPS VARCHAR(13),
            SNPTypeDetail NVARCHAR(50),
            MemberMonths DECIMAL(38, 8),	
            WtdRelativity DECIMAL(38, 8),	
            LastUpdateByID CHAR(7),
            LastUpdateDateTime DATETIME
        );

        DELETE FROM dbo.TrendAdj_OSNP_SavedPopRegression WHERE 1=1;

        INSERT INTO dbo.TrendAdj_OSNP_SavedPopRegression
        (
            PlanYear,
            CPS,
            SNPTypeDetail,
            MemberMonths,
            WtdRelativity,
            LastUpdateByID, 
            LastUpdateDateTime           
        )
        SELECT 
            imp.PlanYear,
            TRIM(imp.CPS),
            TRIM(imp.SNPTypeDetail),
            imp.MemberMonths,
            imp.WtdRelativity,
            TRIM(imp.LastUpdateByID), 
            imp.LastUpdateDateTime
        FROM @tbl__importData imp;

        DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;

        COMMIT TRANSACTION trans_ImportOtherSNPAdjustment; 

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;

        SELECT  
            @ErrorMessage = ERROR_MESSAGE(),
            @ErrorSeverity = ERROR_SEVERITY(),
            @ErrorState = ERROR_STATE();

        ROLLBACK TRANSACTION; 

        RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH;
END;
GO