SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateOrInsertBenchmark
--
-- CREATOR: Joe Casey
--
-- CREATED DATE: 2010-Dec-27
-- HEADER UPDATED: 2010-Dec-27
--
-- DESCRIPTION: Stored Procedure responsible for saving Benchmark information

-- PARAMETERS:
--  Input:
--      @ForecastID
--      @SecPayAdj
--      @ExpNonDE#RiskScore
--      @ExpDE#RiskScore
--      @ProjNonDE#RiskScore
--      @ProjDE#RiskScore
--      @Est<PERSON>lanBidComponent
--		@OOAMemberMonths 
--      @UserID
--  Output:
--
-- TABLES:
--  Read:
--      SavedPlanRiskFactorDetail
--      SavedPlanStateCountyDetail
--  Write:
--      SavedPlanAssumptions
--      SavedPlanDetail
--      SavedPlanHeader
--      SavedPlanRiskFactorDetail
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnGetSafeDivisionResult
--      fnPlanCountyProjectedMemberMonths
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------
-- 2010-Dec-27      1           Initial Version                                                     Joe Casey
-- 2012-Mar-22		2			Included OOA Member Months											Trevor Mahoney
-- 2012-Mar-23		3			Changed precision of OOA member months to (28,6)					Trevor Mahoney
-- 2019-Jun-28		4			Replace SavedPlanHeader with SavedForecastSetup			            Pooja Dahiya
-- 2019-Oct-30	    5           Replace @UserID from char(13) to char(7)							Chhavi Sinha
-- 2020-Dec-14      6           Increasing scale to align BPT risk scores with MRA                  Brent Osantowski
-- 2021-Sep-28      7           Fixing overwriting of the SavedPlanRiskFactorDetail table with the 
--                              weighted average RiskScore values on change made to Bid Model 
--                              Benchmark tab on (Secdary Payer Adj,PBC,OOAMM) fields               Abraham Ndabian
-- ---------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUpdateOrInsertBenchmark]
    @ForecastID INT,
    @SecPayAdj DECIMAL(7,6),
    @ExpNonDE#RiskScore DECIMAL(18,15),
    @ExpDE#RiskScore DECIMAL(18,15),
    @ProjNonDE#RiskScore DECIMAL(18,15),
    @ProjDE#RiskScore DECIMAL(18,15),
    @EstPlanBidComponent DECIMAL(7,2) = NULL,
    @OOAMemberMonths DECIMAL(28,15) = 0,
    @UserID CHAR(7)
AS
SET NOCOUNT ON;
BEGIN
    BEGIN
        UPDATE SavedPlanAssumptions
        SET SecondaryPayerAdjustment = @SecPayAdj
        WHERE ForecastID = @ForecastID
    END
    
    BEGIN
        UPDATE SavedPlanDetail
        SET EstimatedPlanBidComponent = @EstPlanBidComponent,
            LastUpdateByID = @UserID,
            LastUpdateDateTime = GETDATE()            
        WHERE ForecastID = @ForecastID
    END
    
    -- Update OOA Member Months
    BEGIN
    
		UPDATE SavedPlanOOAMemberMonthDetail 
		SET MemberMonths = @OOAMemberMonths,
			LastUpdateByID = @UserID,
			LastUpdateDateTime = GETDATE() 
		WHERE ForecastID = @ForecastID    			 
    END
    
    BEGIN
        --Get the weighted risk score for projected. If it varies by more than 6 decimals, then update all counties to have the new risk score.
        --If it doesn't vary, it means that the risk score hasn't changed.
        DECLARE @IsProjNonDE#RiskScoreDifferent BIT,
                @IsProjDE#RiskScoreDifferent BIT,
                @CalcProjNonDE#RiskScore DECIMAL(18,15),
                @CalcProjDE#RiskScore DECIMAL(18,15)

 -- Removing the ability to update SavedPlanRiskFactorDetail table based of updating (Secondary Payer Adj,PBC and OOAMemberMonths) in Bid Model Benchmark tab on 10/8/2021
	 
        SELECT
            @CalcProjNonDE#RiskScore = (SELECT ProjNonDE#RiskScore FROM fnAppGetBenchmarkSummary(@ForecastID)),
            @CalcProjDE#RiskScore = (SELECT ProjDE#RiskScore FROM fnAppGetBenchmarkSummary(@ForecastID))
        --FROM
        --    (SELECT
        --        ProjNonDE#RiskScoreMM = SUM(ISNULL(projNonDE#.RiskFactor,1) * ISNULL(NonDE#MM.ProjectedMemberMonths,0)),
        --        ProjDE#RiskScoreMM = SUM(ISNULL(projDE#.RiskFactor,1) * ISNULL(DE#MM.ProjectedMemberMonths,0)),
        --        ProjNonDE#MM = SUM(ISNULL(NonDE#MM.ProjectedMemberMonths,0)),
        --        ProjDE#MM = SUM(ISNULL(DE#MM.ProjectedMemberMonths,0))
        --    FROM SavedPlanStateCountyDetail scd
        --    INNER JOIN SavedPlanRiskFactorDetail expNonDE#
        --        ON expNonDE#.ForecastID = scd.ForecastID
        --        AND expNonDE#.StateTerritoryID = scd.StateTerritoryID
        --        AND expNonDE#.CountyCode = scd.CountyCode
        --        AND expNonDE#.IsExperience = 1
        --        AND expNonDE#.DemogIndicator = 1
        --    INNER JOIN SavedPlanRiskFactorDetail expDE#
        --        ON expDE#.ForecastID = scd.ForecastID
        --        AND expDE#.StateTerritoryID = scd.StateTerritoryID
        --        AND expDE#.CountyCode = scd.CountyCode
        --        AND expDE#.IsExperience = 1
        --        AND expDE#.DemogIndicator = 2
        --    INNER JOIN SavedPlanRiskFactorDetail projNonDE#
        --        ON projNonDE#.ForecastID = scd.ForecastID
        --        AND projNonDE#.StateTerritoryID = scd.StateTerritoryID
        --        AND projNonDE#.CountyCode = scd.CountyCode
        --        AND projNonDE#.IsExperience = 0
        --        AND projNonDE#.DemogIndicator = 1
        --    INNER JOIN SavedPlanRiskFactorDetail projDE#
        --        ON projDE#.ForecastID = scd.ForecastID
        --        AND projDE#.StateTerritoryID = scd.StateTerritoryID
        --        AND projDE#.CountyCode = scd.CountyCode
        --        AND projDE#.IsExperience = 0
        --        AND projDE#.DemogIndicator = 2
        --    INNER JOIN fnPlanCountyProjectedMemberMonths(@ForecastID ,0) NonDE#MM         
        --        ON scd.ForecastID = NonDE#MM.ForecastID
        --        AND scd.StateTerritoryID = NonDE#MM.StateTerritoryID
        --        AND scd.CountyCode = NonDE#MM.CountyCode
        --    INNER JOIN fnPlanCountyProjectedMemberMonths(@ForecastID ,1) DE#MM             
        --        ON scd.ForecastID = DE#MM.ForecastID
        --        AND scd.StateTerritoryID = DE#MM.StateTerritoryID
        --        AND scd.CountyCode = DE#MM.CountyCode
        --    WHERE scd.ForecastID =@ForecastID                                                       
        --        AND scd.IsCountyExcludedFromBPTOutput = 0
        --    ) weighted
    
        IF ROUND(@CalcProjNonDE#RiskScore,6) <> ROUND(@ProjNonDE#RiskScore,6)
            BEGIN
                UPDATE SavedPlanRiskFactorDetail
                SET RiskFactor = @ProjNonDE#RiskScore,
                    LastUpdateByID = @UserID,
                    LastUpdateDateTime = GETDATE()
                WHERE ForecastID = @ForecastID
                    AND DemogIndicator = 1
                    AND IsExperience = 0
            END
        
        IF ROUND(@CalcProjDE#RiskScore,6) <> ROUND(@ProjDE#RiskScore,6)  
            BEGIN
                UPDATE SavedPlanRiskFactorDetail
                SET RiskFactor = @ProjDE#RiskScore,
                    LastUpdateByID = @UserID,
                    LastUpdateDateTime = GETDATE()
                WHERE ForecastID = @ForecastID
                    AND DemogIndicator = 2
                    AND IsExperience = 0
            END
        
        UPDATE SavedPlanRiskFactorDetail
        SET RiskFactor = @ExpNonDE#RiskScore,
            LastUpdateByID = @UserID,
            LastUpdateDateTime = GETDATE()
        WHERE ForecastID = @ForecastID
            AND DemogIndicator = 1
            AND IsExperience = 1
            
        UPDATE SavedPlanRiskFactorDetail
        SET RiskFactor = @ExpDE#RiskScore,
            LastUpdateByID = @UserID,
				LastUpdateDateTime = GETDATE()
        WHERE ForecastID = @ForecastID
            AND DemogIndicator = 2
            AND IsExperience = 1
    END
    
    BEGIN
        UPDATE SavedForecastSetup
        SET IsToReprice = 1,
		LastUpdateByID=@UserID,
		LastUpdateDateTime=GETDATE()
        WHERE ForecastID = @ForecastID
    END

END
GO
