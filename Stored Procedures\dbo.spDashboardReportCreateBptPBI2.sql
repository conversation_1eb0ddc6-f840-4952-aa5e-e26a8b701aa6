SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- PROCEDURE NAME: spDashboardReportCreateBptPBI2

-- DESCRIPTION: This SP returns extract for "BPT creation" user action from dm_exec_procedure_stats_snapshot table 
--
-- PARAMETERS:
--    Input: 
--		@LastSuc<PERSON>fullRunLogid 
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--		[dbo].[dm_exec_procedure_stats_snapshot]
--		
-- Example 
-- Exec [dbo].[spDashboardReportCreateBptPBI2] 19586083, '2023-09-25 10:26:27.367'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-May-24			1			Initial Version                                                                         Sheetal Patil
---------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spDashboardReportCreateBptPBI2] 
 (@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


IF (SELECT  OBJECT_ID ('tempdb..#AppLogBptTemp')) IS NOT NULL DROP TABLE #AppLogBptTemp;

SELECT	
	[snapshot_datatime] AS [Date]
	,[procedure_name] AS ProcName
	,COUNT([procedure_name]) AS Counts
	,SUM([execution_count]) AS ExecutionCount 
	,CONVERT(FLOAT, SUM([total_elapsed_time]))/1000000/60 AS ExecutionTImeinMinutes ,
	CONVERT(FLOAT, SUM([total_elapsed_time]))/1000000 AS ExecutionTimeinSeconds 
INTO #AppLogBptTemp
FROM 
	(
		SELECT 
			GETDATE() AS snapshot_datatime
			,[procedure_name]
			,[execution_count]
			,[total_elapsed_time]
		FROM (
				SELECT 
					DB_NAME() AS source_database
					,DB_NAME(database_id) database_name
					,OBJECT_NAME(object_id) procedure_name 
					,[execution_count]
					,[total_elapsed_time]
				FROM sys.dm_exec_procedure_stats
			 ) AS T1
		WHERE source_database = database_name
	) AS T2
WHERE 
	[procedure_name] LIKE '%spAppGetMABPTCreation%' 
	AND [snapshot_datatime] BETWEEN  DATEADD(DAY,-1,GETDATE()) AND GETDATE()
GROUP BY 
	[procedure_name],[snapshot_datatime]


IF (SELECT  OBJECT_ID ('tempdb..#AppLogBptTemp1')) IS NOT NULL DROP TABLE #AppLogBptTemp1;

SELECT 
	CONVERT(DATE,[Date],23) AS [Date]
	,MIN([Date]) AS StartDate
	,COUNT(ProcName) AS Counts
	,SUM(ExecutionCount) AS ExecutionCount
	,CONVERT(FLOAT, SUM(ExecutionTImeinMinutes))  AS ExecutionTImeinMinutes
	,CONVERT(FLOAT, SUM(ExecutionTimeinSeconds))  AS ExecutionTimeinSeconds
INTO #AppLogBptTemp1
FROM #AppLogBptTemp    
GROUP BY CONVERT(DATE,[Date],23)


SELECT 
	'BPT creation' AS [UserActions]
	,'Multiple Users' AS [UserID]
	,StartDate AS [Run Date]
	,'BPT creation' AS [Type]
	,'NULL' AS [Plans]
	,0 AS [Plan Count]
	,ExecutionTimeinSeconds
	,0 AS characterEvenCount
	,0 AS ErrorCount
	,' ' AS [ErrorMessage]
FROM #AppLogBptTemp1 

END
GO
