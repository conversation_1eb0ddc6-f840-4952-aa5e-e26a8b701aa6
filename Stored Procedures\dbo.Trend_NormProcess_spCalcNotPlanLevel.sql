SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_NormProcess_spCalcNotPlanLevel
--
-- CREATOR: <PERSON> Lewis
--
-- CREATED DATE: JAN-22-2020
--
-- DESCRIPTION: Pull quarterly Region and Nationwide trends from Trend_CalcFileSync_Quarterly
--				for all combinations of Component / PlanYearID / QuarterID / Reporting Category with the following criteria: 
--		  			(1) exclude 'Part B Rx Pharmacy' reporting category, 
--					(2) exclude projected trend (i.e. anything past the current years' last quarter of actuals), and 
--					(3) include only a 5 year history (most recent year may be partial)
--				Where regional trends do not exist, map Nationwide trends to the regional rows in the shell.
--  
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_CalcFileSync_Quarterly
--					LkpIntPlanYear
--					LkpProjectionVersion
--					LkpIntBenefitCategory
--					Trend_SavedComponentInfo
--
--          Write:  Trend_NormProcess_CalcNotPlanLevel
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MAR-02-2020      1           Initial Version                                                     Jake Lewis
-- OCT-12-2022	`	2			Added column names and NEWID() column in insert statement			Sheetal
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_NormProcess_spCalcNotPlanLevel]

@LastUpdateByID CHAR(13)

AS
    BEGIN
        SET NOCOUNT ON;

        -- Declare variables
        DECLARE @CurrentYear INT;
        SET @CurrentYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);

        DECLARE @LastCurrentYearQuarter INT;
        SET @LastCurrentYearQuarter = (SELECT   LastCurrentYearQuarter
                                       FROM     dbo.LkpProjectionVersion
                                       WHERE    IsLiveProjection = 1);

        DECLARE @OldestHistoricYear INT;
        SET @OldestHistoricYear = CASE WHEN @LastCurrentYearQuarter = 0 -- Bid/FLP projection (i.e. no data in current year)
        THEN                               @CurrentYear - 5
                                       ELSE @CurrentYear - 4 -- Non-Bid season projection (i.e. have some data in current year)
                                  END;

        -- Create shell
        IF (SELECT  OBJECT_ID ('tempdb..#Shell')) IS NOT NULL DROP TABLE #Shell;
        SELECT      e.Component
                   ,c.PlanYearID
                   ,d.QuarterID
                   ,a.Region AS ActuarialRegion
                   ,b.ReportingCategory
        INTO        #Shell
        FROM        (SELECT DISTINCT -- unique regions, add 'Nationwide'
                            Region
                     FROM   dbo.vwPlanInfo
                     WHERE  IsHidden = 0
                            AND IsOffMAModel = 'No'
                            AND Region NOT IN ('Unmapped')
                     UNION ALL
                     SELECT 'Nationwide') AS a
       CROSS JOIN   (SELECT DISTINCT -- unique reporting categories, excluding 'Part B Rx Pharmacy'
                            ReportingCategory
                     FROM   dbo.LkpIntBenefitCategory
                     WHERE  ReportingCategory IS NOT NULL
                            AND ReportingCategory <> 'Part B Rx Pharmacy') AS b
       CROSS JOIN   (SELECT DISTINCT -- range of plan years
                            number AS PlanYearID
                     FROM   master..spt_values
                     WHERE  number BETWEEN @CurrentYear - 5 AND @CurrentYear) AS c
       CROSS JOIN   (SELECT DISTINCT -- unique quarters (i.e. numbers 1-4)
                            number AS QuarterID
                     FROM   master..spt_values
                     WHERE  number BETWEEN 1 AND 4) AS d
       CROSS JOIN   (SELECT DISTINCT -- unique components in normalized analysis
                            Component
                     FROM   dbo.Trend_SavedComponentInfo
                     WHERE  Component NOT LIKE '%Rx%'
                            AND IsPartOfPackage = 1
                            AND Component NOT LIKE '%Normalized%') AS e
        WHERE       c.PlanYearID >= @OldestHistoricYear
                    AND c.PlanYearID < @CurrentYear
                    OR  (c.PlanYearID = @CurrentYear
                         AND d.QuarterID <= @LastCurrentYearQuarter);

        -- Map PlanTypeGranularity and PlanTypeGranularityValue
        -- This logic assumes that all components have nationwide assumptions. Therefore we either map the region assumption or default to nationwide.
        IF (SELECT  OBJECT_ID ('tempdb..#Granularity')) IS NOT NULL
            DROP TABLE #Granularity;
        SELECT      DISTINCT
                    a.Component
                   ,CASE WHEN b.PlanTypeGranularity IS NOT NULL THEN b.PlanTypeGranularity ELSE 'IsNationwide' END AS PlanTypeGranularity
                   ,CASE WHEN b.PlanTypeGranularityValue IS NOT NULL THEN b.PlanTypeGranularityValue ELSE '1' END AS PlanTypeGranularityValue
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ActuarialRegion
                   ,a.ReportingCategory
        INTO        #Granularity
        FROM        #Shell a
        LEFT JOIN   dbo.Trend_CalcFileSync_Quarterly b
               ON b.Component = a.Component
                  AND   b.PlanTypeGranularityValue = a.ActuarialRegion;

        -- Delete and insert into final output table, mapping in ComponentVersionID, CostAdjustment, and UseAdjustment
        DELETE  FROM dbo.Trend_NormProcess_CalcNotPlanLevel;
        INSERT INTO dbo.Trend_NormProcess_CalcNotPlanLevel
        (ComponentVersionID
		,Component
		,PlanTypeGranularity
		,PlanTypeGranularityValue
		,PlanYearID
		,QuarterID
		,ActuarialRegion
		,ReportingCategory
		,CostAdjustment
		,UseAdjustment
		,LastUpdateByID
		,LastUpdateDateTime
		,ID)
        SELECT      b.ComponentVersionID
                   ,a.Component
                   ,a.PlanTypeGranularity
                   ,a.PlanTypeGranularityValue
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ActuarialRegion
                   ,a.ReportingCategory
                   ,b.CostAdjustment
                   ,b.UseAdjustment
                   ,@LastUpdateByID
                   ,GETDATE ()
				   ,NEWID()
        FROM        #Granularity a
        LEFT JOIN   dbo.Trend_CalcFileSync_Quarterly b
               ON b.Component = a.Component
                  AND   b.PlanTypeGranularity = a.PlanTypeGranularity
                  AND   b.PlanTypeGranularityValue = a.PlanTypeGranularityValue
                  AND   b.PlanYearID = a.PlanYearID
                  AND   b.QuarterID = a.QuarterID
                  AND   b.ReportingCategory = a.ReportingCategory
        WHERE       a.PlanYearID >= @OldestHistoricYear
                    AND (a.PlanYearID < @CurrentYear
                         OR (a.PlanYearID = @CurrentYear
                             AND a.QuarterID <= @LastCurrentYearQuarter));

    END;
GO
