SET AN<PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spLoadMarketInputJson]   
--    
-- AUTHOR: Surya Murthy 
--    
-- CREATED DATE: 2025-Mar-05   
-- Type: 
-- DESCRIPTION: Import data 
--    
-- PARAMETERS:    
-- Input: 
--  @Input
--  @LastUpdateByID

-- TABLES:   
--

-- Read:    
--  prepricing.marketinputcategory
--  prepricing.MarketInputSubCategory
--  dbo.LkpIntBenefitType
--  dbo.LkpIntBenefitType

-- Write:    
--  PrePricing.MarketInputValue   
-- 
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE							DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-Mar-05		1		Initial Version							Surya Murthy
-- 2025-Mar-11		2		BenefitChangeValue insert logic         Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spLoadMarketInputJson]
(
	@Input VARCHAR(MAX),
	@LastUpdateByID VARCHAR(200)
)
AS    
BEGIN    
	SET NOCOUNT ON
	BEGIN TRY
	BEGIN TRANSACTION saveimportdata			 
		DECLARE	@OutPutResult VARCHAR(MAX);
		DECLARE	@OutPutResultCode VARCHAR(20);
		DECLARE	@LastUpdateSource VARCHAR(20);
		SET @LastUpdateSource = 'Import'

		--insert Json data into the table
		DROP TABLE if EXISTS #FormatJSON
		SELECT * INTO #FormatJSON
		FROM 
			OPENJSON(@input)
			WITH (
				--MAP names from JSON to the columns 
				CPS VARCHAR(13) '$.CPS', 
				BenefitCategory VARCHAR(100) '$.BenefitCategory',
				Benefit VARCHAR(100) '$.Benefit',
				INValue VARCHAR(1000) '$.INValue',
				INCostShareType VARCHAR(100) '$.INCostShareType',
				OONValue VARCHAR(1000) '$.OONValue',
				OONCostShareType VARCHAR(100) '$.OONCostShareType',
				BenefitChangeValue varchar(1000) '$.BenefitChangeValue',
				Note VARCHAR(1000) '$.Note'
			)
			-- TRIM logic
			UPDATE #FormatJSON 
			SET 
			CPS=TRIM(CPS),
			BenefitCategory=TRIM(BenefitCategory),
			Benefit=TRIM(Benefit),
			INValue=TRIM(INValue),
			INCostShareType=TRIM(INCostShareType),
			OONValue=TRIM(OONValue),
			OONCostShareType=TRIM(OONCostShareType),
			BenefitChangeValue=TRIM(BenefitChangeValue),
			Note=TRIM(Note)
			WHERE 1=1

		--insert Json data into the table
		DROP TABLE if EXISTS #FormatJSONtoActualData

		SELECT j.cps,j.benefit,ppi.planinfoid,subcategoryid, 
		INValue, inbentype.BenefitTypeID AS INCostShareType,
		OONValue,oonbentype.BenefitTypeID AS OONCostShareType,
		BenefitChangeValue,Note
		INTO #FormatJSONtoActualData
		FROM #FormatJSON j
		JOIN prepricing.PlanInfo ppi 
		ON j.cps = ppi.cps
		JOIN prepricing.marketinputcategory cat
		ON j.benefitcategory = cat.CategoryName 
		JOIN prepricing.MarketInputSubCategory subcat
		ON j.benefit = subcat.SubCategoryName
		LEFT JOIN dbo.LkpIntBenefitType inbentype
		ON inbentype.Name=INCostShareType
		LEFT JOIN dbo.LkpIntBenefitType oonbentype
		ON oonbentype.Name=OONCostShareType

		--IF input is an MSB or OSB, capitalize all characters.
		UPDATE t
		SET INValue = UPPER(invalue), OONValue = UPPER(oonvalue)
		FROM #FormatJSONtoActualData t
		JOIN PrePricing.MarketInputSubCategory s
		ON t.SubCategoryID=s.subcategoryid
		WHERE s.CategoryID IN ( 2 ,9)

		--Prevent cost share selections from being applied to non-costshare fields.
		UPDATE t
		SET INCostShareType = NULL, OONCostShareType = NULL
		FROM #FormatJSONtoActualData t
		JOIN Prepricing.MarketInputSubCategory sc ON t.SubCategoryID = sc.SubCategoryID
		WHERE sc.IsCostShareType = 0

		--Merge Statement
		MERGE prepricing.MarketInputValue t
		USING #FormatJSONtoActualData s
		ON t.PlanInfoID = s.PlanInfoID
		AND t.SubcategoryID = s.subcategoryid
		WHEN MATCHED THEN
		UPDATE 
			SET 
			t.INValue = ISNULL (s.INValue,''),
			t.OONValue = ISNULL(s.OONValue,''),
			t.INCostShareType = s.INCostShareType,
			t.OONCostShareType = s.OONCostShareType,
			t.BenefitChangeValue = CASE WHEN s.BenefitChangeValue = '' THEN NULL ELSE s.BenefitChangeValue END ,
			t.Note =ISNULL(s.Note,'') ,
			t.LastUpdateByID = @LastUpdateByID,
			t.LastUpdateSource = @LastUpdateSource,
			t.LastUpdateDateTime = GETDATE()
		WHEN NOT MATCHED THEN
			INSERT (PlanInfoID, Subcategoryid, INvalue, OONValue, BenefitChangeValue,Note, LastUpdateByID , LastUpdateDateTime,INCostShareType,OONCostShareType,LastUpdateSource)
			VALUES (s.PlanInfoID, s.Subcategoryid,ISNULL(s.invalue,''), ISNULL(s.oonvalue,''), CASE WHEN s.BenefitChangeValue = '' THEN NULL ELSE s.BenefitChangeValue END,ISNULL(s.Note,''), @LastUpdateByID,GETDATE(),s.INCostShareType,s.OONCostShareType,@LastUpdateSource);

			SET @OutPutResult='Import is completed.';	
		    SET @OutPutResultCode='Success';

		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT TRANSACTION saveimportdata;
	END TRY
	BEGIN CATCH		
		DECLARE @ErrorMessage NVARCHAR(4000);                    
        DECLARE @ErrorSeverity INT;                    
        DECLARE @ErrorState INT;                    
        DECLARE @ErrorException NVARCHAR(4000);                    
		DECLARE @errSrc VARCHAR(MAX) =ISNULL( ERROR_PROCEDURE(),'SQL')                    
		DECLARE @currentdate DATETIME=GETDATE()                    
		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
		' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)                
		ROLLBACK TRANSACTION saveimportdata;       
		SET @OutPutResult= 'Import failed. Please review the data input under the orange header on the Data tab in your export for any unacceptable inputs and reupload.'+@ErrorMessage;	
		SET @OutPutResultCode='Error';
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH

END
GO
