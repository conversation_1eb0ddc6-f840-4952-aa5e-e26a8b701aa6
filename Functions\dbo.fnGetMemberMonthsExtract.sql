SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnGetMemberMonthsExtract]  ******/


-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMemberMonthsExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2012-Apr-05
-- HEADER UPDATED: 2012-Apr-05
--
-- DESCRIPTION: Designed to extract fields for Membership - Will match the upload
--
-- PARAMETERS:
--	Input:
--      @WhereIN
--		@IsPlanLevel
--  Output:
--
-- TABLES: 
--	Read:
--      SavedPlanHeader
--      SavedPlanDetail
--      SavedPlanMemberMonthDetail
--      SavedPlanRiskFactorDetail
--      SavedMarketInfo
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnStringSplit
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION	    CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2012-Apr-05		1			Initial Version							                            Alex Rezmerski
-- 2012-May-02		2			Changed OOA Membermonths to be scaled for sales membership			Alex Rezmerski
-- 2013-Oct-04		3			Modified to Include SegmentId										Anubhav Mishra
-- 2017-Jun-20		4			Removed PerExtContractNumberPlanIDDetail and changed PlanName		Chris Fleming
--									to pull from SavedPlanHeader				
-- 2018-Apr-26		5			Modified LkpExtCMSPlanType for new UI table modifications			Jordan Purdue
-- 2019-Jul-08      6           Made changes in market table.										Kritika Singh 
-- 2020-Jan-06      7           Modified code for statecounycode									Deepali Mittal
-- 2020-Dec-18      8           Increasing scale to align BPT risk scores with MRA                  Brent Osantowski
-- 2022-Apr-22      9           Change @WhereIN from 4000 to MAX                                    Manisha Tyagi
-- 2023-Sep-05		10			LastUpdateDateTime field added while export							Surya Murthy
-- 2024-Jul-02		11		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- 2025-Apr-01		12			Added CPS & LastUpdateByID Column									Archana Sahu
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMemberMonthsExtract]
    (
    @WhereIN VARCHAR(MAX)=NULL,
    @IsPlanLevel BIT
    ) 
RETURNS @Results TABLE 
(
    FilePath VARCHAR(MAX),
	LastUpdateDateTime DATETIME,
    ForecastID INT,
    MarketName VARCHAR(256),
    PlanName VARCHAR(256),
    PlanTypeID INT,
    PlanTypeName VARCHAR(10),
    ContractNumber VARCHAR(5),
	PlanID VARCHAR(3),
	SegmentId VARCHAR(3),
    CountyCode CHAR(5) ,
    CountyName VARCHAR(128), 
    NonDualAged DECIMAL (28,15),
    NonDualESRD DECIMAL (28,15),
    NonDualHospice DECIMAL (28,15), 
    DualAged DECIMAL (28,15),
    DualESRD DECIMAL (28,15),
    DualHospice DECIMAL (28,15),
    OOAMemberMonths DECIMAL (28,15),
	CPS CHAR(13),
	LastUpdateByID CHAR(7)
) AS
BEGIN


-----------------------------------------------------------------------------------------------------------
        IF @WhereIN IS NULL --Extract All Plans
        BEGIN
-----------------------------------------------------------------------------------------------------------
            INSERT @Results 								
                SELECT 
					MMD.FilePath,
					MMD.LastUpdateDateTime,
					SCD.ForecastID, 												
	                ActuarialMarket, 						
	                PlanName = 
	                    CASE LEN(P.PlanName) 						
		                    WHEN 0 THEN 'None' 					
		                    ELSE P.PlanName
		                END, 					
	                P.PlanTypeID, 						
	                ProductType, 
	                P.ContractNumber,
	                P.PlanID,
	                P.SegmentId ,					
	                StateCountyCode = dbo.fnPadInteger(SC.StateTerritoryID, 2) + SC.CountyCode, 						
	                SC.CountyName, 	
                    NonDualAged =

                        SUM(
                            CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                WHEN 1 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    NonDualESRD =

						SUM(
                            CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                WHEN 7 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    NonDualHospice =

						SUM(
                            CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                WHEN 4 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    DualAged =

						SUM(
                            CASE DI.DemogIndicator
                                WHEN 2 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    DualESRD =

						SUM(
                            CASE DI.DemogIndicator
                                WHEN 8 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    DualHospice =

						SUM(
                            CASE DI.DemogIndicator
                                WHEN 5 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    OOAMemberMonths = OOA.MemberMonths,
					spi.CPS,
					MMD.LastUpdateByID
                FROM SavedPlanHeader P 							
                INNER JOIN SavedMarketInfo M 							
	                ON P.MarketID = M.ActuarialMarketID		
				INNER JOIN dbo.SavedPlanInfo spi
					ON P.PlanInfoID = spi.PlanInfoID
                INNER JOIN LkpProductType PT 							
	                ON PT.ProductTypeID = P.PlanTypeID 						
                INNER JOIN SavedPlanStateCountyDetail SCD							
	                ON SCD.ForecastID = P.ForecastID											
	                AND SCD.IsCountyExcludedFromBPTOutput = 0						
                INNER JOIN LkpExtCMSStateCounty SC 							
	                ON SCD.StateTerritoryID = SC.StateTerritoryID 					
	                AND CASE WHEN LEN(SCD.CountyCode)=2 THEN '0'+scd.CountyCode ELSE scd.CountyCode END = SC.CountyCode
	            INNER JOIN SavedPlanOOAMemberMonthDetail OOA
					ON OOA.ForecastID = P.ForecastID 		
                LEFT JOIN SavedPlanMemberMonthDetail MMD 							
	                ON MMD.ForecastID = P.ForecastID
	                AND MMD.StateTerritoryID = SC.StateTerritoryID					
	                AND MMD.CountyCode = SC.CountyCode
	            LEFT JOIN LkpIntDemogIndicators DI
	                ON MMD.DemogIndicator = DI.DemogIndicator
                WHERE
                    P.ForecastID > 0							
	                AND P.IsHidden = 0	
	                AND DI.DemogIndicator NOT IN (3,6,9,12)																	
                GROUP BY
					MMD.FilePath,
					MMD.LastUpdateDateTime,
                    SCD.ForecastID, 							
	                ActuarialMarket, 						
	                P.PlanName, 						
	                P.PlanTypeID, 						
	                PT.ProductType, 						
	                P.ContractNumber, 						
	                P.PlanId,
	                P.SegmentId, 	  --Added SegmentId					
	                SCD.StateTerritoryID, 						
	                SCD.CountyCode, 						
	                SC.StateTerritoryID, 						
	                SC.CountyCode, 						
	                SC.CountyName,
	                OOA.MemberMonths,
					spi.CPS,
					MMD.LastUpdateByID
                ORDER BY
                    ActuarialMarket, 							
	                SCD.ForecastID, 						
	                SCD.StateTerritoryID, 						
	                SCD.CountyCode   
        END
        ELSE -- filtered
        BEGIN
-----------------------------------------------------------------------------------------------------------
            IF @IsPlanLevel = 0 --Market Level (Membership)
            BEGIN
-----------------------------------------------------------------------------------------------------------
                INSERT @Results 								
                    SELECT
						MMD.FilePath,
						mmd.LastUpdateDateTime,
                        SCD.ForecastID, 												
	                    ActuarialMarket, 						
	                    PlanName =
	                        CASE LEN(P.PlanName) 						
		                        WHEN 0 THEN 'None' 					
		                        ELSE P.PlanName
		                    END, 					
	                    P.PlanTypeID, 						
	                    ProductType, 						
	                    P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId,	--Added SegmentId				
		                StateCountyCode = dbo.fnPadInteger(SC.StateTerritoryID, 2) + SC.CountyCode, 						
	                    SC.CountyName, 						
	                    NonDualAged =

							SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 1 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualESRD =

							SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 7 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualHospice =

							SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 4 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualAged =

							SUM(
                                CASE DI.DemogIndicator
                                    WHEN 2 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualESRD =

							SUM(
                                CASE DI.DemogIndicator
                                    WHEN 8 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualHospice =

							SUM(
                                CASE DI.DemogIndicator
                                    WHEN 5 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        OOAMemberMonths = OOA.MemberMonths,
					  spi.CPS, MMD.LastUpdateByID
                    FROM SavedPlanHeader P  		
                    INNER JOIN SavedPlanStateCountyDetail SCD		
	                    ON SCD.ForecastID = P.ForecastID 	
	                    AND SCD.IsCountyExcludedFromBPTOutput = 0	
					INNER JOIN dbo.SavedPlanInfo spi
					ON P.PlanInfoID = spi.PlanInfoID
                    INNER JOIN SavedMarketInfo M 	
	                    ON P.MarketID = M.ActuarialMarketID
                    INNER JOIN LkpExtCMSStateCounty SC 	
	                    ON SCD.StateTerritoryID = SC.StateTerritoryID 
	                    AND CASE WHEN LEN(SCD.CountyCode)=2 THEN '0'+scd.CountyCode ELSE scd.CountyCode END= SC.CountyCode 
                    INNER JOIN LkpProductType PT 	
	                    ON PT.ProductTypeID = P.PlanTypeID 
	                INNER JOIN SavedPlanOOAMemberMonthDetail OOA
						ON OOA.ForecastID = P.ForecastID
                    LEFT OUTER JOIN SavedPlanMemberMonthDetail MMD	
	                    ON MMD.ForecastID = P.ForecastID
	                    AND MMD.StateTerritoryID = SC.StateTerritoryID
	                    AND MMD.CountyCode = SC.CountyCode
	                LEFT JOIN LkpIntDemogIndicators DI
	                    ON MMD.DemogIndicator = DI.DemogIndicator
                    WHERE
                        P.MarketID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))	
	                    AND P.IsHidden = 0
	                    AND DI.DemogIndicator NOT IN (3,6,9,12)			
                    GROUP BY
                        MMD.FilePath,
						MMD.LastUpdateDateTime,
                        SCD.ForecastID, 			
	                    ActuarialMarket, 		
	                    P.PlanName, 		
	                    P.PlanTypeID, 		
	                    PT.ProductType, 		
	                    P.ContractNumber, 		
	                    P.PlanId, 
	                    P.SegmentId,		--Added SegmentId
	                    SCD.StateTerritoryID, 		
	                    SCD.CountyCode, 						
	                    SC.StateTerritoryID, 						
	                    SC.CountyCode, 						
	                    SC.CountyName,
	                    OOA.MemberMonths,
						spi.CPS,
						MMD.LastUpdateByID
                    ORDER BY
                        ActuarialMarket, 							
	                    SCD.ForecastID, 						
	                    SCD.StateTerritoryID, 						
	                    SCD.CountyCode 
             END            
-----------------------------------------------------------------------------------------------------------
            ELSE -- Plan Level (Membership)
            BEGIN
-----------------------------------------------------------------------------------------------------------
                INSERT @Results							
                    SELECT
                        MMD.FilePath,
						mmd.LastUpdateDateTime,
                        SCD.ForecastID,			
	                    ActuarialMarket, 					
	                    PlanName = 
	                        CASE LEN(P.PlanName) 					
		                        WHEN 0 THEN 'None' 				
		                        ELSE P.PlanName
		                    END, 				
	                    P.PlanTypeID, 					
	                    ProductType, 
	                    P.ContractNumber,	
	                    P.PlanID,	
	                    P.SegmentId,		--Added SegmentId	
	                  	StateCountyCode = dbo.fnPadInteger(SC.StateTerritoryID, 2) + SC.CountyCode, 					
	                    SC.CountyName, 					
	                    NonDualAged =

                            SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 1 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualESRD =

                            SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 7 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualHospice =

                            SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 4 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualAged =

                            SUM(
                                CASE DI.DemogIndicator
                                    WHEN 2 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualESRD =

                            SUM(
                                CASE DI.DemogIndicator
                                    WHEN 8 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualHospice =

                            SUM(
                                CASE DI.DemogIndicator
                                    WHEN 5 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                       OOAMemberMonths = OOA.MemberMonths,
					   spi.CPS,
					   MMD.LastUpdateByID
                    FROM SavedPlanHeader P  		
                    INNER JOIN SavedPlanStateCountyDetail SCD		
	                    ON SCD.ForecastID = P.ForecastID 	
	                    AND SCD.IsCountyExcludedFromBPTOutput = 0
					INNER JOIN dbo.SavedPlanInfo spi
					ON P.PlanInfoID = spi.PlanInfoID
                    INNER JOIN SavedMarketInfo M 	
	                    ON P.MarketID = M.ActuarialMarketID
                    INNER JOIN LkpExtCMSStateCounty SC 	
	                    ON SCD.StateTerritoryID = SC.StateTerritoryID 
	                    AND CASE WHEN LEN(SCD.CountyCode)=2 THEN '0'+scd.CountyCode ELSE scd.CountyCode end = SC.CountyCode 
                    INNER JOIN LkpProductType PT 	
	                    ON PT.ProductTypeID = P.PlanTypeID 
	                INNER JOIN SavedPlanOOAMemberMonthDetail OOA
						ON OOA.ForecastID = P.ForecastID
                    LEFT OUTER JOIN SavedPlanMemberMonthDetail MMD	
	                    ON MMD.ForecastID = P.ForecastID
	                    AND MMD.StateTerritoryID = SC.StateTerritoryID
	                    AND MMD.CountyCode = SC.CountyCode
	                LEFT JOIN LkpIntDemogIndicators DI
	                    ON MMD.DemogIndicator = DI.DemogIndicator
                    WHERE
                        P.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))	
	                    AND P.IsHidden = 0
	                    AND DI.DemogIndicator NOT IN (3,6,9,12)	
                    GROUP BY
                        MMD.FilePath,
						MMD.LastUpdateDateTime,
                        SCD.ForecastID, 			
	                    ActuarialMarket, 		
	                    P.PlanName, 		
	                    P.PlanTypeID, 		
	                    PT.ProductType, 		
	                    P.ContractNumber, 		
	                    P.PlanId, 		
	                    P.SegmentId,     --Added SegmentId
	                    SCD.StateTerritoryID, 		
	                    SCD.CountyCode, 
	                    SC.StateTerritoryID, 
	                    SC.CountyCode, 
	                    SC.CountyName,
	                    OOA.MemberMonths,
						spi.CPS,
						MMD.LastUpdateByID
                    ORDER BY
                        ActuarialMarket, 	
	                    SCD.ForecastID, 
	                    SCD.StateTerritoryID, 
	                    SCD.CountyCode 
            END
    END
	RETURN
END
GO
