SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spUpdatePricingTrends
--
-- CREATOR:			<PERSON>
--
-- CREATED DATE:	2020-APR-03
--
-- DESCRIPTION:		This SP updates trends used in the bid model for pricing
--		
-- PARAMETERS:
--  Input  :		@PlanInfoID
--  Output :		NONE
--
-- TABLES : 
--	Read :			SavedPlanInfo
--					Trend_ProjProcess_CalcPlanTrendsFinal
--  Write:			SavedMATrendData
--					SavedClaimFactorHeader
--					SavedPlanDetail
--					SavedMERActAdj
--					SavedForecastSetup
--					PlanChangeLog
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetBidYear
--					Trend_fnCalcStringToTable
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-APR-03      1           Initial Version                                                     Brent Osantowski
-- 2020-APR-21		2			Change @LastUpdateByID from 13 to 7									Chhavi Sinha
-- 2020-APR-21      3           Replace LEFT(@LastUpdateByID,7) by @LastUpdateByID                  Manisha Tyagi
-- 2020-APR-29      4           No longer exec spAppRepriceAfterTrendUpload an accept NULL          Brent Osantowski
--                              PlanInfoID
-- 2020-JUN-12      5           Aggregating Actuarial Adjustments and including Plan Exp Adj        Brent Osantowski
-- 2020-JUN-16      6           Retaining MER adjustments and inserting 1's for new plans           Brent Osantowski
-- 2020-OCT-09      7           Delete statement updated for SavedMATrendData table                 Surya Murthy
-- 2020-OCT-10		8			Map COVID component to the Geographic								Craig Nielsen
-- 2020-NOV-19      9           Include NOLOCK & ROWLOCK                                            Manisha Tyagi
-- 2020-NOV-30		10			updated covid logic													Deepali On behalf of Craig, added header
-- 2021-MAR-02		11			Change actuarial adjustment logic to allow zeroing out proj cost	Michael Manes
-- 2021-MAR-17		12			DF:5255(Unable to update trend with -0.99999 adjustment) code fixes	Surya Murthy
-- 2021-AUG-08		13			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's		    Franklin Fu
-- 2021-AUG-30		14			Ensure SavedMERActAdj table has sets (exper comp 1/manual comp 2)	Bob Knadler
--								of records matching trends; Update MER Adj on added set to existing
-- 2022-OCT-25      15          Savedforecast PlanYear column changes             	                Surya Murthy
-- 2023-JAN-03		16			Remove Dual Population adjustment and legacy trend fields		    Joe Wang
-- 2024-DEC-06	    17	        Adding ForecastID to the SavedMERActAdj assumptions table  		    Abraham Ndabian
-- 2024-Dec-20      18          Adding ForecastID in the Inner Join to correct DF:24775             Kiran
-- 2025-MAR-03	    19	        Clearing old data before updating SavedMERActAdj assumptions table  Abraham Ndabian
-- 2025-Mar-06	    20			Delete logic changed												Abraham Ndabian
-- 2025-Apr-10	    21			Ensuring MER Factors to NOT be deleted when MER <> 1 while completing					
--                              MAAUI >> Update Trend process                                        Abraham Ndabian
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUpdatePricingTrends]
@PlanInfoID      VARCHAR(MAX) = NULL
,@LastUpdateByID CHAR(7)    --;
AS


SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;
DECLARE @Bidyear AS INT = (SELECT   dbo.fnGetBidYear ());
DECLARE @LastUpdateByDateTime AS DATETIME = (SELECT GETDATE ());


IF (SELECT  OBJECT_ID ('tempdb..#CPS')) IS NOT NULL DROP TABLE #CPS;
SELECT      DISTINCT
            spi.PlanInfoID
           ,spi.CPS
           ,sfs.ForecastID
INTO        #CPS
FROM        dbo.SavedPlanInfo spi WITH (NOLOCK)
INNER JOIN  dbo.SavedForecastSetup sfs WITH (NOLOCK)
        ON sfs.PlanInfoID = spi.PlanInfoID
WHERE       spi.PlanYear IN (@Bidyear - 1, @Bidyear)
            AND (spi.PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@PlanInfoID, ',', 1) )
                 OR @PlanInfoID IS NULL)

ORDER BY    PlanInfoID;


IF (SELECT  OBJECT_ID ('tempdb..#InitialTrendPull')) IS NOT NULL
    DROP TABLE #InitialTrendPull;

SELECT      trc.CPS AS ContractPBPSegmentID
           ,LEFT(trc.CPS, 5) AS ContractNumber
           ,SUBSTRING (trc.CPS, 7, 3) AS PlanID
           ,RIGHT(trc.CPS, 3) AS SegmentID
           ,trc.TrendYearID
           ,trc.RateType AS Component
           ,trc.BenefitCategoryID AS BenefitCategoryNumber
           ,trc.ComponentReporting AS FactorName
           ,(1 + trc.CostAdjustment) AS CostAdj
           ,(1 + trc.UseAdjustment) AS UseAdj
INTO        #InitialTrendPull
FROM        dbo.Trend_ProjProcess_CalcPlanTrendsFinal trc WITH (NOLOCK)
INNER JOIN  #CPS cps
        ON cps.PlanInfoID = trc.PlanInfoID;


IF (SELECT  OBJECT_ID ('tempdb..#InitialTrendPivot')) IS NOT NULL
    DROP TABLE #InitialTrendPivot;
SELECT      x.ContractPBPSegmentID
           ,x.ContractNumber
           ,x.PlanID
           ,x.SegmentID
           ,x.Component
           ,x.BenefitCategoryNumber
           --================================== BASE TO CURRENT========================================================================
           ,CASE WHEN FactorName = 'Normalized' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CNormalizedUse
           ,CASE WHEN FactorName = 'Normalized' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CNormalizedCost
           ,CASE WHEN FactorName = 'Population' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CMorbidityUse
           ,CASE WHEN FactorName = 'Population' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CMorbidityCost
           ,1 AS B2CDemographicUse
           ,1 AS B2CDemographicCost
           ,CASE WHEN FactorName = 'Busters' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CBustersUse
           ,CASE WHEN FactorName = 'Busters' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CBustersCost
           ,1 AS B2CProductUse
           ,1 AS B2CProductCost
           ,CASE WHEN x.FactorName = 'COVID' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CGeographicUse
           ,CASE WHEN x.FactorName = 'COVID' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CGeographicCost
           ,CASE WHEN FactorName = 'CMS Reimbursement'
                      AND   x.TrendYearID = @Bidyear - 1 THEN x.UseAdj
                 ELSE NULL END AS B2CCMSReimbursementUse
           ,CASE WHEN FactorName = 'CMS Reimbursement'
                      AND   x.TrendYearID = @Bidyear - 1 THEN x.CostAdj
                 ELSE NULL END AS B2CCMSReimbursementCost
           ,CASE WHEN FactorName = 'Contractual' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CContractualUse
           ,CASE WHEN FactorName = 'Contractual' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CContractualCost
           ,CASE WHEN FactorName = 'Benders' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CBendersUse
           ,CASE WHEN FactorName = 'Benders' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CBendersCost
           ,CASE WHEN FactorName = 'Workday' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CWorkdayUse
           ,CASE WHEN FactorName = 'Workday' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CWorkdayCost
           ,CASE WHEN FactorName = 'Flu' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CFluUse
           ,CASE WHEN FactorName = 'Flu' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CFluCost
           ,CASE WHEN FactorName = 'Induced Utilization'
                      AND   x.TrendYearID = @Bidyear - 1 THEN x.UseAdj
                 ELSE NULL END AS B2CInducedUtilizationUse
           ,CASE WHEN FactorName = 'Induced Utilization'
                      AND   x.TrendYearID = @Bidyear - 1 THEN x.CostAdj
                 ELSE NULL END AS B2CInducedUtilizationCost
           --=============================================================================================================================================================================================================
           --========================================Actauarial Adjustment Logic==========================================================================================================================================
           -- New logic to zero out adjustments - clean this up later
           --  ,CASE WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear - 1 AND x.UseAdj <= .0000001 THEN .0000001 
           --        WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear - 1 AND x.UseAdj > .0000001 THEN x.UseAdj
           --        ELSE NULL 
           --END AS 'B2CActAdjUse'
           --  ,CASE WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear - 1 AND x.CostAdj <= .0000001 THEN .0000001 
           --        WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear - 1 AND x.CostAdj > .0000001 THEN x.CostAdj
           --        ELSE NULL 
           --END AS 'B2CActAdjCost'


           ,CASE WHEN LEFT(FactorName, 6) = 'ActAdj' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CActAdjUse
           ,CASE WHEN LEFT(FactorName, 6) = 'ActAdj'
                      AND   x.TrendYearID = @Bidyear - 1 THEN x.CostAdj
                 ELSE NULL END AS B2CActAdjCost

           --=============================================================================================================================================================================================================
           ,CASE WHEN FactorName = 'PlanExpAdj' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CNonActAdjUse
           ,CASE WHEN FactorName = 'PlanExpAdj' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CNonActAdjCost
           ,CASE WHEN FactorName = 'Outlier Claims' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CPoolingUse
           ,CASE WHEN FactorName = 'Outlier Claims' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CPoolingCost
           ,CASE WHEN FactorName = 'HTP' AND x.TrendYearID = @Bidyear - 1 THEN x.UseAdj ELSE NULL END AS B2CHTPUse
           ,CASE WHEN FactorName = 'HTP' AND x.TrendYearID = @Bidyear - 1 THEN x.CostAdj ELSE NULL END AS B2CHTPCost
           ,1 AS B2CCompoundingAdjUse
           ,1 AS B2CCompoundingAdjCost
           --================================== CURRENT TO BID========================================================================
           ,CASE WHEN FactorName = 'Normalized' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PNormalizedUse
           ,CASE WHEN FactorName = 'Normalized' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PNormalizedCost
           ,CASE WHEN FactorName = 'Population' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PMorbidityUse
           ,CASE WHEN FactorName = 'Population' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PMorbidityCost
           ,1 AS C2PDemographicUse
           ,1 AS C2PDemographicCost
           ,CASE WHEN FactorName = 'Busters' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PBustersUse
           ,CASE WHEN FactorName = 'Busters' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PBustersCost
           ,1 AS C2PProductUse
           ,1 AS C2PProductCost
           ,CASE WHEN FactorName = 'COVID' AND  x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PGeographicUse
           ,CASE WHEN FactorName = 'COVID' AND  x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PGeographicCost
           ,CASE WHEN FactorName = 'CMS Reimbursement' AND  x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PCMSReimbursementUse
           ,CASE WHEN FactorName = 'CMS Reimbursement'
                      AND   x.TrendYearID = @Bidyear THEN x.CostAdj
                 ELSE NULL END AS C2PCMSReimbursementCost
           ,CASE WHEN FactorName = 'Contractual' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PContractualUse
           ,CASE WHEN FactorName = 'Contractual' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PContractualCost
           ,CASE WHEN FactorName = 'Benders' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PBendersUse
           ,CASE WHEN FactorName = 'Benders' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PBendersCost
           ,CASE WHEN FactorName = 'Workday' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PWorkdayUse
           ,CASE WHEN FactorName = 'Workday' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PWorkdayCost
           ,CASE WHEN FactorName = 'Flu' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PFluUse
           ,CASE WHEN FactorName = 'Flu' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PFluCost
           ,CASE WHEN FactorName = 'Induced Utilization'
                      AND   x.TrendYearID = @Bidyear THEN x.UseAdj
                 ELSE NULL END AS C2PInducedUtilizationUse
           ,CASE WHEN FactorName = 'Induced Utilization'
                      AND   x.TrendYearID = @Bidyear THEN x.CostAdj
                 ELSE NULL END AS C2PInducedUtilizationCost
           --=============================================================================================================================================================================================================
           --========================================Actauarial Adjustment Logic==========================================================================================================================================
           -- New logic to zero out adjustments - clean this up later
           -- ,CASE WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear AND x.UseAdj <= .0000001 THEN .0000001
           --        WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear AND x.UseAdj > .0000001 THEN x.UseAdj
           --        ELSE NULL 
           --END AS 'C2PActAdjUse'
           --  ,CASE WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear AND x.CostAdj <= .0000001 THEN .0000001
           --        WHEN LEFT(FactorName,6) = 'ActAdj' AND x.TrendYearID = @Bidyear AND x.CostAdj > .0000001 THEN x.CostAdj
           --        ELSE NULL 
           --END AS 'C2PActAdjCost'
           ,CASE WHEN LEFT(FactorName, 6) = 'ActAdj' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PActAdjUse
           ,CASE WHEN LEFT(FactorName, 6) = 'ActAdj' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PActAdjCost

           --=============================================================================================================================================================================================================
           ,CASE WHEN FactorName = 'PlanExpAdj' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PNonActAdjUse
           ,CASE WHEN FactorName = 'PlanExpAdj' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PNonActAdjCost
           ,CASE WHEN FactorName = 'Outlier Claims' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PPoolingUse
           ,CASE WHEN FactorName = 'Outlier Claims' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PPoolingCost
           ,CASE WHEN FactorName = 'HTP' AND x.TrendYearID = @Bidyear THEN x.UseAdj ELSE NULL END AS C2PHTPUse
           ,CASE WHEN FactorName = 'HTP' AND x.TrendYearID = @Bidyear THEN x.CostAdj ELSE NULL END AS C2PHTPCost
           ,1 AS C2PCompoundingAdjUse
           ,1 AS C2PCompoundingAdjCost
INTO        #InitialTrendPivot
FROM        #InitialTrendPull x
GROUP BY    x.ContractPBPSegmentID
           ,x.ContractNumber
           ,x.PlanID
           ,x.SegmentID
           ,x.TrendYearID
           ,x.Component
           ,x.BenefitCategoryNumber
           ,x.FactorName
           ,x.UseAdj
           ,x.CostAdj;

DELETE  FROM dbo.SavedMATrendData WITH (ROWLOCK)
WHERE   ContractPBP IN (SELECT  c.ContractPBPSegmentID FROM #InitialTrendPivot c);
INSERT INTO dbo.SavedMATrendData WITH (ROWLOCK)
SELECT      final.ContractPBPSegmentID
           ,final.ContractNumber
           ,final.PlanID
           ,final.SegmentID
           ,final.Component
           ,final.BenefitCategoryNumber
           ,COALESCE (MAX (B2CNormalizedUse), 1) AS B2CNormalizedUse
           ,COALESCE (MAX (B2CNormalizedCost), 1) AS B2CNormalizedCost
           ,COALESCE (MAX (B2CMorbidityUse), 1) AS B2CMorbidityUse
           ,COALESCE (MAX (B2CMorbidityCost), 1) AS B2CMorbidityCost
           ,COALESCE (MAX (B2CDemographicUse), 1) AS B2CDemographicUse
           ,COALESCE (MAX (B2CDemographicCost), 1) AS B2CDemographicCost
           ,COALESCE (MAX (B2CBustersUse), 1) AS B2CBustersUse
           ,COALESCE (MAX (B2CBustersCost), 1) AS B2CBustersCost
           ,COALESCE (MAX (B2CProductUse), 1) AS B2CProductUse
           ,COALESCE (MAX (B2CProductCost), 1) AS B2CProductCost
           ,COALESCE (MAX (B2CGeographicUse), 1) AS B2CGeographicUse
           ,COALESCE (MAX (B2CGeographicCost), 1) AS B2CGeographicCost
           ,COALESCE (MAX (B2CCMSReimbursementUse), 1) AS B2CCMSReimbursementUse
           ,COALESCE (MAX (B2CCMSReimbursementCost), 1) AS B2CCMSReimbursementCost
           ,COALESCE (MAX (B2CContractualUse), 1) AS B2CContractualUse
           ,COALESCE (MAX (B2CContractualCost), 1) AS B2CContractualCost
           ,COALESCE (MAX (B2CBendersUse), 1) AS B2CBendersUse
           ,COALESCE (MAX (B2CBendersCost), 1) AS B2CBendersCost
           ,COALESCE (MAX (B2CWorkdayUse), 1) AS B2CWorkdayUse
           ,COALESCE (MAX (B2CWorkdayCost), 1) AS B2CWorkdayCost
           ,COALESCE (MAX (B2CFluUse), 1) AS B2CFluUse
           ,COALESCE (MAX (B2CFluCost), 1) AS B2CFluCost
           ,COALESCE (MAX (B2CInducedUtilizationUse), 1) AS B2CInducedUtilizationUse
           ,COALESCE (MAX (B2CInducedUtilizationCost), 1) AS B2CInducedUtilizationCost
           ,CASE WHEN MIN (B2CActAdjUse) = 0 THEN 0
                 ELSE EXP (SUM (LOG (COALESCE (NULLIF(B2CActAdjUse, 0), 1)))) END AS B2CActAdjUse
           ,CASE WHEN MIN (B2CActAdjCost) = 0 THEN 0
                 ELSE EXP (SUM (LOG (COALESCE (NULLIF(B2CActAdjCost, 0), 1)))) END AS B2CActAdjCost
           ,COALESCE (MAX (B2CNonActAdjUse), 1) AS B2CNonActAdjUse
           ,COALESCE (MAX (B2CNonActAdjCost), 1) AS B2CNonActAdjCost
           ,COALESCE (MAX (B2CPoolingUse), 1) AS B2CPoolingUse
           ,COALESCE (MAX (B2CPoolingCost), 1) AS B2CPoolingCost
           ,COALESCE (MAX (B2CHTPUse), 1) AS B2CHTPUse
           ,COALESCE (MAX (B2CHTPCost), 1) AS B2CHTPCost
           ,COALESCE (MAX (B2CCompoundingAdjUse), 1) AS B2CCompoundingAdjUse
           ,COALESCE (MAX (B2CCompoundingAdjCost), 1) AS B2CCompoundingAdjCost
           ,COALESCE (MAX (C2PNormalizedUse), 1) AS C2PNormalizedUse
           ,COALESCE (MAX (C2PNormalizedCost), 1) AS C2PNormalizedCost
           ,COALESCE (MAX (C2PMorbidityUse), 1) AS C2PMorbidityUse
           ,COALESCE (MAX (C2PMorbidityCost), 1) AS C2PMorbidityCost
           ,COALESCE (MAX (C2PDemographicUse), 1) AS C2PDemographicUse
           ,COALESCE (MAX (C2PDemographicCost), 1) AS C2PDemographicCost
           ,COALESCE (MAX (C2PBustersUse), 1) AS C2PBustersUse
           ,COALESCE (MAX (C2PBustersCost), 1) AS C2PBustersCost
           ,COALESCE (MAX (C2PProductUse), 1) AS C2PProductUse
           ,COALESCE (MAX (C2PProductCost), 1) AS C2PProductCost
           ,COALESCE (MAX (C2PGeographicUse), 1) AS C2PGeographicUse
           ,COALESCE (MAX (C2PGeographicCost), 1) AS C2PGeographicCost
           ,COALESCE (MAX (C2PCMSReimbursementUse), 1) AS C2PCMSReimbursementUse
           ,COALESCE (MAX (C2PCMSReimbursementCost), 1) AS C2PCMSReimbursementCost
           ,COALESCE (MAX (C2PContractualUse), 1) AS C2PContractualUse
           ,COALESCE (MAX (C2PContractualCost), 1) AS C2PContractualCost
           ,COALESCE (MAX (C2PBendersUse), 1) AS C2PBendersUse
           ,COALESCE (MAX (C2PBendersCost), 1) AS C2PBendersCost
           ,COALESCE (MAX (C2PWorkdayUse), 1) AS C2PWorkdayUse
           ,COALESCE (MAX (C2PWorkdayCost), 1) AS C2PWorkdayCost
           ,COALESCE (MAX (C2PFluUse), 1) AS C2PFluUse
           ,COALESCE (MAX (C2PFluCost), 1) AS C2PFluCost
           ,COALESCE (MAX (C2PInducedUtilizationUse), 1) AS C2PInducedUtilizationUse
           ,COALESCE (MAX (C2PInducedUtilizationCost), 1) AS C2PInducedUtilizationCost
           ,CASE WHEN MIN (C2PActAdjUse) = 0 THEN 0
                 ELSE EXP (SUM (LOG (COALESCE (NULLIF(C2PActAdjUse, 0), 1)))) END AS C2PActAdjUse
           ,CASE WHEN MIN (C2PActAdjCost) = 0 THEN 0
                 ELSE EXP (SUM (LOG (COALESCE (NULLIF(C2PActAdjCost, 0), 1)))) END AS C2PActAdjCost
           ,COALESCE (MAX (C2PNonActAdjUse), 1) AS C2PNonActAdjUse
           ,COALESCE (MAX (C2PNonActAdjCost), 1) AS C2PNonActAdjCost
           ,COALESCE (MAX (C2PPoolingUse), 1) AS C2PPoolingUse
           ,COALESCE (MAX (C2PPoolingCost), 1) AS C2PPoolingCost
           ,COALESCE (MAX (C2PHTPUse), 1) AS C2PHTPUse
           ,COALESCE (MAX (C2PHTPCost), 1) AS C2PHTPCost
           ,COALESCE (MAX (C2PCompoundingAdjUse), 1) AS C2PCompoundingAdjUse
           ,COALESCE (MAX (C2PCompoundingAdjCost), 1) AS C2PCompoundingAdjCost
           ,LEFT(@LastUpdateByID, 7)
           ,@LastUpdateByDateTime
FROM        #InitialTrendPivot final
GROUP BY    final.ContractPBPSegmentID
           ,final.ContractNumber
           ,final.PlanID
           ,final.SegmentID
           ,final.Component
           ,final.BenefitCategoryNumber;

DECLARE @MaxClaimForecastID INT = (SELECT   MAX (ClaimForecastID + 1) FROM  dbo.SavedClaimFactorHeader);
INSERT INTO dbo.SavedClaimFactorHeader WITH (ROWLOCK)
    (PlanYearID
    ,ClaimForecastID
    ,ForecastID
    ,Name
    ,Description
    ,IsHidden
    ,IsAtPlanLevel
    ,LastUpdateByID
    ,LastUpdateDateTime)
SELECT      PlanYearid = @Bidyear
           ,ClaimfForecastID = @MaxClaimForecastID + ROW_NUMBER () OVER (ORDER BY rt.PlanInfoID ASC, rt.RateType ASC)
           ,ForecastID = cps.ForecastID
           ,Name = cps.CPS + ' ' + CASE WHEN rt.RateType = 1 THEN cps.CPS + ' Experience' ELSE 'MANUAL' END
           ,Description = CASE WHEN rt.RateType = 1 THEN cps.CPS + ' Experience' ELSE 'MANUAL' END
           ,isHidden = 0
           ,isAtPlanLevel = 0
           ,LastUpdateByID = @LastUpdateByID
           ,LastUpdateDateTime = @LastUpdateByDateTime
FROM        #CPS cps
INNER JOIN  (SELECT     PlanInfoID
                       ,RateType
             FROM       dbo.Trend_ProjProcess_CalcPlanTrendsFinal WITH (NOLOCK)
             WHERE      PlanYearID = @Bidyear
             GROUP BY   PlanInfoID
                       ,RateType
                       ,LastUpdateByID) rt
        ON rt.PlanInfoID = cps.PlanInfoID;


UPDATE      spd WITH (ROWLOCK)
SET         ClaimForecastID = fid.ClaimForecastID
FROM        dbo.SavedPlanDetail spd
INNER JOIN  (SELECT     scfh.ForecastID
                       ,CASE WHEN LEFT(scfh.Description, 6) = 'MANUAL' THEN 2 ELSE 1 END AS MARatingOptionID
                       ,MAX (scfh.ClaimForecastID) AS ClaimForecastID
             FROM       dbo.SavedClaimFactorHeader scfh WITH (NOLOCK)
            INNER JOIN  #CPS cps
                    ON cps.ForecastID = scfh.ForecastID
             GROUP BY   scfh.ForecastID
                       ,CASE WHEN LEFT(scfh.Description, 6) = 'MANUAL' THEN 2 ELSE 1 END) fid
        ON fid.ForecastID = spd.ForecastID
           AND  fid.MARatingOptionID = spd.MARatingOptionID;

INSERT INTO dbo.SavedMERActAdj WITH (ROWLOCK)
    (ForecastID              -- adding new ForecastID into SavedMERActAdj table by Abe 12/06/2024
	,ContractPBP
    ,ContractNumber
    ,PlanID
    ,SegmentID
    ,Component
    ,BenefitCategoryNumber
    ,C2PMERUseMultAdj
    ,C2PMERCostMultAdj
    ,C2PMERUseAddAdj
    ,C2PMERCostAddAdj
    ,UserID
    ,LastUpdateDateTime)
SELECT      ForecastID = cps.ForecastID        -- adding new ForecastID into SavedMERActAdj table by Abe 12/06/2024
           ,ContractPBP = smt.ContractPBP
           ,ContractNumber = smt.ContractNumber
           ,PlanId = smt.PlanID
           ,SegmentID = smt.SegmentID
           ,Component = smt.Component
           ,BenefitCategoryNumber = smt.BenefitCategoryNumber
           ,C2PMERUseMultAdj = 1
           ,C2PMERCostMultAdj = 1
           ,C2PMERUseAddAdj = NULL
           ,C2PMERCostAddAdj = NULL
           ,UserID = @LastUpdateByID
           ,LastUpdateDateTime = @LastUpdateByDateTime
FROM        dbo.SavedMATrendData smt WITH (NOLOCK)
INNER JOIN  (SELECT CPS, ForecastID FROM #CPS GROUP BY  CPS, ForecastID) cps
        ON cps.CPS = smt.ContractPBP
WHERE       NOT EXISTS (SELECT  1
                        FROM    dbo.SavedMERActAdj mer WITH (NOLOCK)
                        WHERE   cps.CPS = mer.ContractPBP

                                --Update 8/30
                                AND smt.Component = mer.Component);
/*** Update MER Adj value on newly added component to match that for existing component ******/
UPDATE      dbo.SavedMERActAdj
SET         C2PMERCostMultAdj = A.C2PMERCostMultAdj
FROM        (SELECT     ma.ForecastID                            -- adding ctAdj table by Abe 1new ForecastID into SavedMERA2/06/2024
                       ,ma.ContractPBP
                       ,ma.ContractNumber
                       ,ma.PlanID
                       ,ma.SegmentID
                       ,ma.Component
                       ,ma.BenefitCategoryNumber
                       ,ma.C2PMERCostMultAdj
             FROM       dbo.SavedMERActAdj ma
            INNER JOIN  #CPS p
                    ON ma.ContractPBP = p.CPS AND ma.ForecastID = p.ForecastID   -- adding ctAdj table by Abe 1new ForecastID into SavedMERA2/06/2024
             WHERE      ma.C2PMERCostMultAdj <> 1
                        AND ma.LastUpdateDateTime < @LastUpdateByDateTime   --existing
) A
INNER JOIN  dbo.SavedMERActAdj B
        ON B.ContractPBP = A.ContractPBP
           AND  B.BenefitCategoryNumber = A.BenefitCategoryNumber
           AND  B.Component <> A.Component
           AND  B.C2PMERCostMultAdj = 1
           AND  B.LastUpdateDateTime = @LastUpdateByDateTime;

/*** End 8/30 Update ******/

UPDATE      sfs WITH (ROWLOCK)
SET         sfs.IsToReprice = 1
FROM        dbo.SavedForecastSetup sfs
INNER JOIN  #CPS cps
        ON cps.ForecastID = sfs.ForecastID;


INSERT INTO dbo.PlanChangeLog WITH (ROWLOCK)
    (ForecastID
    ,ProcName
    ,Value
    ,AuditUserID
    ,AuditTime)
SELECT  ForecastID = cps.ForecastID
       ,ProcName = 'MACTAPT'
       ,NULL
       ,AuditUserID = @LastUpdateByID
       ,AuditTime = GETDATE ()
FROM    #CPS cps;
GO
