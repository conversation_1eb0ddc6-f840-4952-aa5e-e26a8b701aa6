SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetBenefitBundles]   
--    
-- AUTHOR: Sur<PERSON>y 
--    
-- CREATED DATE: 2024-Oct-31
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Benefit Bundles info  
--    
-- PARAMETERS:    
-- Input: 
-- @RegionID

-- TABLES:   
--  

-- Read:    
--  PrePricing.BenefitBundle
--	PrePricing.BenefitBundleMapping

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-31		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetBenefitBundles]
AS    
BEGIN     
	SELECT a.BundleID,a.BundleName,
	AssignedBenefits=STUFF((SELECT ',' + CAST(c.SubCategoryID AS VARCHAR(7))	
           FROM PrePricing.BenefitBundleMapping c WITH(NOLOCK)		  
		   WHERE c.BundleID=a.BundleID     		   
          FOR XML PATH('')), 1, 1, ''),	
	AssignedBenefitNames=STUFF((SELECT ',' + CAST(d.SubCategoryName AS VARCHAR(max))
           FROM PrePricing.BenefitBundleMapping c WITH(NOLOCK)
		   JOIN PrePricing.MarketInputSubCategory d WITH(NOLOCK) ON d.SubCategoryID=c.SubCategoryID
		   WHERE c.BundleID=a.BundleID     		   
          FOR XML PATH('')), 1, 1, '')
	FROM PrePricing.BenefitBundle a WITH (NOLOCK) 
	LEFT JOIN PrePricing.BenefitBundleMapping b WITH (NOLOCK) ON a.BundleID=b.BundleID
	GROUP BY a.BundleID,a.BundleName,a.LastUpdateDateTime	ORDER BY a.LastUpdateDateTime DESC
END
GO
