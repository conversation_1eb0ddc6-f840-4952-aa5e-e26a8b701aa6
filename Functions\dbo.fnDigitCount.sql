SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnDigitCount] ******/

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnDigitCount
--
-- AUTHOR: Tonya Cockrell
--
-- CREATED DATE: 2007-Jan-12
--
-- DESCRIPTION: Returns the number of digits in the specified integer.
--
-- PARAMETERS:
--	Input: @Number - The number whose digits will be counted.
--
-- RETURNS: The number of digits in @Number.
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		    CHANGES MADE						                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Jan-12		1			Initial Version							                Tonya Cockrell
-- 2007-Oct-09		2			Revised code to bring it into coding standards.			Shannon Boykin
-- 2010-Oct-05      3           Moved to 2012 database                                  Jake Gaecke
--
--
--
--
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnDigitCount](@Number INT)
RETURNS INT AS
BEGIN 
    IF @Number = 0
        RETURN 1
    RETURN ROUND(LOG10(ABS(@Number)), 0, 1) + 1
END
GO
