SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetRelatedPartiesAdjustment] ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetRelatedPartiesAdjustment
--
-- AUTHOR: <PERSON> 
--
-- CREATED DATE: 2014-Apr-21
-- HEADER UPDATED: 2014-Apr-21
--
-- DESCRIPTION: Function responsible for listing values on the Bid Summary tab in the MAPD Model.
--
-- PARAMETERS:
--	Input: 
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read: 
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
---
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2014-Apr-21		1			Initial Version														Mike Deren
-- 2014-May-4		2			Limited addedBenefitTypeID >= 8000; cleaned up group by				Matthew Evans
-- 2015-Mar-11      3           Added code related to AddedBenefitSales 							Jordan Purdue
-- 2018-Feb-13		4			With new base data feed, changed RPadj to be only ProfitClaims		Keith Galloway
-- 2018-Mar-14		5			Included AdminClaims and QualityClaims with ProfitClaims			Jordan Purdue
-- 2018-Oct-25		6			Replaced <8000 MSB logic to service category 35						Alex Beruscha
-- 2023-Aug-03		7			Added NOLOCK, internal parameter and table schema					Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetRelatedPartiesAdjustment]
    (
    @ForecastID INT
    )
RETURNS @Results TABLE 
    (
    ForecastID INT,
    BenefitCategoryID INT, 
    RelatedPartiesAdjustment DECIMAL(11,8) 
    ) AS

BEGIN  
DECLARE @XForecastID INT = @ForecastID
    INSERT @Results  
      
	SELECT  lkp.ForecastID ,
			lkp.benefitcategoryid ,
			RelatedPartiesAdjustment = CASE WHEN total.BenefitCategoryID IS NULL THEN 0
				 ELSE SUM(ProfitClaimsAdj)
			END
	FROM    ( SELECT    sph.ForecastID ,
						lkp.AddedBenefitTypeID ,
						spr.benefitcategoryid ,
						ProfitClaimsAdj = (lkp.ProfitClaims + lkp.AdminClaims + lkp.QualityClaims) * spr.Distribution
			  FROM      dbo.SavedPlanAddedBenefits spa WITH (NOLOCK)
						INNER JOIN ( SELECT Name = LEFT(AddedBenefitName, 3) ,
											AddedBenefitTypeID,
											ProfitClaims,
											AdminClaims,
											QualityClaims
									 FROM   dbo.LkpIntAddedBenefitExpenseDetail WITH (NOLOCK)
								   ) lkp 
							ON lkp.AddedBenefitTypeID = spa.addedbenefittypeid
						INNER JOIN ( SELECT Name = LEFT(relatedparty,3),
											BenefitCategoryID,
											[BenefitCategory Name],
											Distribution
									 FROM   dbo.SavedRelatedPartyDist WITH (NOLOCK)
								   ) spr 
							ON spr.Name = lkp.Name
						INNER JOIN dbo.savedplanheader sph WITH (NOLOCK)
							ON sph.ForecastID = spa.ForecastID
							AND sph.ForecastID = @XForecastID
			  WHERE     spa.IsHidden = 0 AND spa.BidServiceCatID = 35
			) Total
			RIGHT JOIN ( SELECT plans.ForecastID ,
								benefits.BenefitCategoryName ,
								benefits.benefitcategoryid
						 FROM   ( SELECT    ForecastID ,
											[key] = 1
								  FROM      dbo.savedplanheader sph WITH (NOLOCK)
								  WHERE     isliveindex = 1	AND IsHidden = 0 AND ForecastID = @XForecastID
								) plans
								INNER JOIN ( SELECT benefitcategoryid ,
													BenefitCategoryName ,
													[key] = 1
											 FROM   dbo.LkpIntBenefitCategory WITH (NOLOCK)
											 WHERE  IsUsed = 1
										   ) benefits 
									ON plans.[key] = benefits.[key]
					   ) lkp 
				ON total.benefitcategoryid = lkp.BenefitCategoryID
					AND total.ForecastID = lkp.ForecastID
	GROUP BY lkp.ForecastID ,
			lkp.benefitcategoryid ,
			total.BenefitCategoryID 
	ORDER BY lkp.BenefitCategoryID
    RETURN
END
GO
