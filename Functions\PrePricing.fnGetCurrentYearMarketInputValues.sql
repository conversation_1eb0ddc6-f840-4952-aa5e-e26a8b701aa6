SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-------------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: [fnGetCurrentYearMarketInputValues]  
--  
-- AUTHOR: <PERSON> Gilbert
--  
-- CREATED DATE: 2024-Nov-25  
--  
-- DESCRIPTION: Get Current Year values from the pricing model to use for comparisons.
--  
-- PARAMETERS:  
--  Input:  
--         
--  Output:  
--  @Results
-- TABLES:  
--  Read: 

--  Write:  
--  
-- VIEWS:  
--        
--  
-- FUNCTIONS:  
--  dbo.fnGetBidYear()
-- 
-- STORED PROCS:  
--  
-- HISTORY:  
-- ---------------------------------------------------------------------------------------------
-- DATE        VERSION      CHANGES MADE						DEVELOPER 
------------------------------------------------------------------------------------------------  
-- 2024-Nov-25  1			Initial Version                     Adam Gilbert
-- 2024-Jan-27  2			Removed unused PD Audit Fields      Adam Gilbert
-- 2025-Feb-06  3			Defect fix- Rx Benefit name change  Priyadarshini Deshmukh
-- 2025-Feb-20  4			OSB Name instead of flag; Part A/B 
--							trailing "0" formatting fix			Adam Gilbert
-- 2025-Mar-3   5			Lead 0 removal						Adam Gilbert
-- 2025-Mar-12  6			A/B Fix for when OON exists but     Adam Gilbert
-- 2025-Mar-24  7			OSBCode instead of Name			    Adam Gilbert
-- ---------------------------------------------------------------------------------------------
CREATE FUNCTION [PrePricing].[fnGetCurrentYearMarketInputValues]
    (
    )
RETURNS @Results TABLE
    (
	PlanInfoID INT,
	SubCategoryID INT,
	CurrentYearINValue VARCHAR(1000),
	CurrentYearOONValue VARCHAR(1000)
    ) 
    AS
	BEGIN

		--Plan Premium Historical
				WITH planpremium AS (SELECT 
				ppi.planinfoid,
				dbo.fngetbidyear()-1 AS [PlanYearID],
				spi.CPS,
				mrp.PartBPremiumBuyDown,
				mrp.MemberPremiumRounded,
				mrp.ProfitPercent
				FROM dbo.MAReportPlanLevel mrp   WITH(NOLOCK)
				INNER JOIN SavedForecastSetup sfs  WITH(NOLOCK)
				ON mrp.ForecastID = sfs.ForecastID 
				INNER JOIN dbo.savedplaninfo spi  WITH(NOLOCK)
				ON sfs.planinfoid = spi.PlanInfoID
				JOIN prepricing.vwCurrentYearPlanMapping ppi  WITH(NOLOCK)
				ON spi.CPS = ppi.CYCPS --crosswalk consideration likely needed here as well.
				WHERE  mrp.PlanYearID = dbo.fngetbidyear()-1
			)
			INSERT INTO @Results (PlanInfoID ,SubCategoryID ,CurrentYearINValue ,CurrentYearOONValue )
			SELECT Planinfoid,  (select subcategoryid from prepricing.marketinputsubcategory where subcategoryname = 'Part B Giveback') as SubCatgeoryID,Cast(PartBPremiumBuydown as VARCHAR(100))  as INValue, ''asOONValue  FROM planpremium WHERE PartBPremiumBuydown IS NOT null
			UNION all
			SELECT Planinfoid,  (select subcategoryid from prepricing.marketinputsubcategory where subcategoryname = 'Member Premium') as SubCatgeoryID,  CASE WHEN MemberPremiumRounded = 0 THEN '0' ELSE FORMAT(MemberPremiumRounded,'#.######') END as Invalue, ''asOONValue  FROM planpremium WHERE MemberPremiumRounded IS NOT NULL
            UNION all
			SELECT Planinfoid,  (select subcategoryid from prepricing.marketinputsubcategory where subcategoryname = 'Bid Profit %') as SubCatgeoryID, Cast(FORMAT(ProfitPercent,'P2') as VARCHAR(100)) as Invalue, ''asOONValue  FROM planpremium WHERE ProfitPercent IS NOT null
			;

			--Hist MSB
		WITH msb AS (
			SELECT
				spi.cps,
			 CASE WHEN LEFT(spab.AddedBenefitName,3) IN ('CNB','HSH','WDE','NBS') 
				  THEN  '-' + CAST(ROW_NUMBER() OVER(PARTITION BY spab.PlanYearID, spi.cps, LEFT(spab.AddedBenefitName,3) ORDER BY spab.PlanYearID, spi.cps) AS VARCHAR(10)) + ' ('+LEFT(spab.AddedBenefitName,3) +')'
			 ELSE  '(' + LEFT(spab.AddedBenefitName,3) + ')' END AS LookupValue,
				spab.AddedBenefitTypeID,
				spab.AddedBenefitName,
				LEFT(spab.AddedBenefitName,6) AS MSBCode
			FROM dbo.ArcSavedPlanAddedBenefits spab  WITH(NOLOCK)
			INNER JOIN dbo.LkpExtCMSBidServiceCategory cat  WITH(NOLOCK)
				ON spab.BidServiceCatID = cat.BidServiceCategoryID
			JOIN SavedForecastSetup sfs   WITH(NOLOCK)
				ON spab.ForecastID = sfs.forecastid
			JOIN SavedPlanInfo spi WITH(NOLOCK)
				ON spi.planinfoid = sfs.planinfoid
			WHERE cat.ServiceCategory <> 'Related Parties'
				AND spab.IsHidden = 0
				AND spab.PlanYearID = dbo.fngetbidyear()-1 
				)
			INSERT INTO @Results (PlanInfoID ,SubCategoryID ,CurrentYearINValue ,CurrentYearOONValue )
			SELECT ppi.PlanInfoID,SubCategoryID,MSBCode AS INValue, '' AS OONValue
			FROM msb 			
			LEFT JOIN PrePricing.MarketInputSubCategory  WITH(NOLOCK)
			ON subcategoryname LIKE '%' + LookUpValue+ '%'
			JOIN PrePricing.vwCurrentYearPlanMapping ppi  WITH(NOLOCK)
			ON msb.cps = ppi.cycps -- prior year, crosswalk logic may be needed.
			WHERE 1=1
			AND SubCategoryName IS NOT NULL ;--ADM and MUS excluded atm


			--MOOP DED
		WITH CYPlanLevel AS (
		SELECT 
			ppi.planinfoid, vpi.planyear
			, vpi.CPS AS CPS
			, DeductTypeDesc
			, CASE WHEN (sbo.IsPartBDeductible = 0) THEN sbo.INDeductible ELSE NULL END INDeductible
			, sbo.INMOOP
			, CASE WHEN (vpi.Product != 'HMO' AND sbo.IsPartBDeductible = 0) THEN sbo.OONDeductible ELSE NULL END OONDeductible
			, sbo.OONMOOP
			, CASE WHEN (sbo.IsPartBDeductible = 0 AND vpi.Product != 'HMO') THEN sbo.CombinedDeductible ELSE NULL END CombinedDeductible
			, sbo.CombinedMOOP
			, CASE WHEN (sbo.IsPartBDeductible = 1 AND vpi.Product = 'HMO') THEN sbo.INDeductible
			   WHEN (sbo.IsPartBDeductible = 1 AND vpi.Product != 'HMO') THEN sbo.CombinedDeductible
			   ELSE NULL END PartBDeductible
			FROM dbo.vwplaninfo vpi  WITH(NOLOCK)
			JOIN dbo.SavedForecastSetup sfs  WITH(NOLOCK)
			ON vpi.PlanInfoID = sfs.planinfoid
			JOIN dbo.Benefits_SavedBenefitOption sbo  WITH(NOLOCK)
			ON vpi.PlanInfoID = sbo.PlanInfoID
			AND sfs.BenefitOptionID = sbo.BenefitOptionID
			JOIN PrePricing.vwCurrentYearPlanMapping ppi  WITH(NOLOCK)
			ON ppi.cyCPS = vpi.cps
			WHERE 1=1
			AND vpi.planyear=dbo.fngetbidyear() -1
		),
		formatdata AS (
		SELECT PlanInfoID,CPS,  CAST(DeductTypeDesc AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory WITH(NOLOCK) where subcategoryname = 'Deductible Type') SubcategoryID FROM cyplanlevel
		WHERE DeductTypeDesc IS NOT NULL
		UNION ALL			
		SELECT PlanInfoID,CPS,  CAST(INDeductible AS VARCHAR(1000))   INVALUE,CAST(OONDeductible AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory WITH(NOLOCK) where subcategoryname = 'Deductible') SubcategoryID FROM cyplanlevel
		WHERE INDeductible IS NOT NULL OR OONDeductible IS NOT null
		UNION ALL		
		SELECT PlanInfoID,CPS,  CAST(INMOOP AS VARCHAR(1000))         INVALUE,CAST(OONMoop AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory WITH(NOLOCK) where subcategoryname = 'MOOP') SubcategoryID FROM cyplanlevel
		WHERE INMOOP IS NOT NULL OR OONMOOP IS NOT null
		UNION ALL				
		SELECT PlanInfoID,CPS,  CAST(CombinedDeductible AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory WITH(NOLOCK) where subcategoryname = 'Combined Deductible') SubcategoryID FROM cyplanlevel
		WHERE CombinedDeductible IS NOT NULL
		UNION ALL			
		SELECT PlanInfoID,CPS,  CAST(CombinedMoop AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory WITH(NOLOCK) where subcategoryname = 'Combined MOOP') SubcategoryID FROM cyplanlevel
		WHERE CombinedMoop IS NOT NULL
		UNION ALL			  
		SELECT PlanInfoID,CPS,  CAST(PartBDeductible AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory WITH(NOLOCK) where subcategoryname = 'Part B Deductible') SubcategoryID FROM cyplanlevel
		WHERE PartBDeductible IS NOT NULL
		)
		INSERT INTO @Results (PlanInfoID ,SubCategoryID ,CurrentYearINValue ,CurrentYearOONValue )
		SELECT planinfoid,subcategoryid,ISNULL(invalue,'') INValue,ISNULL(OONValue,'') OONValue FROM formatdata;


		--AB Benefits CY
		WITH ActuarialBenefits AS (
		SELECT  ppi.CPS, bc.BenefitCategoryName , sbod.isoon, CASE WHEN sbod.benefitvalue = 0 THEN '0' ELSE FORMAT(sbod.benefitvalue, '#.######')END as benefitvalue, spi.planinfoid sourcePlanID, ppi.planinfoid,sbod.BenefitTypeID, 
		sbod.BenefitCategoryID, sbodrd.isoon drIsOON,  CASE WHEN sbodrd.benefitvalue = 0 THEN '0' ELSE FORMAT(sbodrd.benefitvalue, '#.######') END AS drBenefitValue, BenefitOrdinalID ,dayrangebegin ,dayrangeend,
		[NameMapping] = bc.BenefitCategoryName +
			CASE WHEN sbod.BenefitCategoryID IN (83, 65, 66, 131) THEN ': Level ' + CAST(ISNULL(sbodrd.BenefitOrdinalID,1) AS VARCHAR) 
					ELSE ''
			END,
		dayRangeNameMapping =  parsename(replace(bc.BenefitCategoryName,'-','.'),2)
		 +
			CASE WHEN sbod.BenefitCategoryID IN (83, 65, 66, 131) AND sbod.BenefitTypeID = 5 THEN 'L'+CAST(ISNULL(sbodrd.BenefitOrdinalID,1) AS VARCHAR)+': Day Range -'  
					ELSE ''
			END
		FROM PrePricing.vwCurrentYearPlanMapping  ppi
		JOIN SavedPlanInfo spi WITH(NOLOCK) ON ppi.CYCPS = spi.CPS AND spi.PlanYear = dbo.fngetbidyear()-1 -- current year - may need crosswalk?
		JOIN SavedForecastSetup sfs WITH(NOLOCK) ON spi.planinfoid =sfs.planinfoid
		JOIN Benefits_SavedBenefitOption sbo WITH(NOLOCK) ON sfs.PlanInfoID = sbo.PlanInfoID AND sfs.BenefitOptionID = sbo.BenefitOptionID 
		JOIN Benefits_SavedBenefitOptionDetail sbod WITH(NOLOCK) ON spi.PlanInfoID = sbod.planinfoid AND sbo.BenefitOptionID = sbod.benefitoptionid
		JOIN LkpIntBenefitCategory bc WITH(NOLOCK) ON sbod.BenefitCategoryID = bc.BenefitCategoryID 
		LEFT JOIN Benefits_SavedBenefitOptionDayRangeDetail SBODRD WITH(NOLOCK) 
		ON sbod.BenefitCategoryID =sbodrd.BenefitCategoryID 
		AND sbod.PlanInfoID = sbodrd.PlanInfoID 
		AND sbod.BenefitOptionID = sbodrd.BenefitOptionID
		AND sbod.IsOON = sbodrd.isoon
		--WHERE ppi.PlanInfoid = 26 AND BenefitTypeID = 5
		) , formatdata AS (
		SELECT --*
		planinfoid,isoon , CAST(ISNULL(drbenefitvalue,benefitvalue) AS varchar(1000)) InputValue, SubCategoryID,NameMapping,SubCategoryName
		FROM ActuarialBenefits  AB
		LEFT JOIN PrePricing.MarketInputSubCategory sc WITH(NOLOCK) ON ab.NameMapping =sc.SubCategoryName
		--ORDER BY cps,BenefitCategoryID
		UNION all
		SELECT 
		planinfoid,isoon , CAST(DayRangeBegin AS VARCHAR(1000)) as InputValue, SubCategoryID,dayRangeNameMapping + ' Begin', SubCategoryName
		FROM ActuarialBenefits  AB
		LEFT JOIN PrePricing.MarketInputSubCategory sc WITH(NOLOCK) ON ab.dayRangeNameMapping + ' Begin' =sc.SubCategoryName
		WHERE BenefitCategoryID IN (83, 65, 66, 131) AND BenefitTypeID = 5
		--ORDER BY cps,BenefitCategoryID
		UNION all
		SELECT 
		planinfoid,isoon , CAST(DayRangeEnd AS VARCHAR(1000)) InputValue, SubCategoryID,dayRangeNameMapping + ' End', SubCategoryName
		FROM ActuarialBenefits  AB
		LEFT JOIN PrePricing.MarketInputSubCategory sc WITH(NOLOCK) ON ab.dayRangeNameMapping + ' END' =sc.SubCategoryName
		WHERE BenefitCategoryID IN (83, 65, 66, 131) AND BenefitTypeID = 5
		),
		INN AS (SELECT planinfoid,subcategoryid,inputvalue INValue FROM formatdata WHERE isoon = 0),
		OON AS (SELECT planinfoid,subcategoryid,inputvalue OONValue FROM formatdata WHERE isoon = 1)
		INSERT INTO @Results (PlanInfoID ,SubCategoryID ,CurrentYearINValue ,CurrentYearOONValue )
		SELECT ISNULL(INN.PlanInfoID, OON.PlanInfoID),
			   ISNULL(INN.SubCategoryID, OON.SubCategoryID),
			   ISNULL(INN.INValue,'')  INValue,
			   ISNULL(OON.OONValue,'') OONValue
		FROM INN 
		FULL OUTER JOIN OON
		ON INN.PlanInfoID = OON.planinfoid
		AND INN.SubCategoryID = oon.SubCategoryID ;

	/*OSB */
	WITH OSB AS (
	SELECT ppi.planinfoid,misc.subcategoryid, LEFT(osbh.[description],6) AS CurrentYearInValue
	FROM SavedForecastSetup sfs WITH(NOLOCK)
	JOIN SavedPlanInfo spi WITH(NOLOCK) ON sfs.PlanInfoID = spi.planinfoid
	JOIN arcSavedPlanOptionalPackageDetail spopd WITH(NOLOCK) ON sfs.ForecastID = spopd.forecastid AND sfs.planyear=spopd.PlanYearID 
	JOIN arcPerIntOptionalPackageHeader osbH WITH(NOLOCK) ON spopd.PackageIndex = osbH.packageindex AND sfs.PlanYear = osbH.planyearid
	JOIN PrePricing.PlanInfo ppi WITH(NOLOCK) ON ppi.CPS = spi.cps 
	LEFT JOIN PrePricing.MarketInputSubCategory misc WITH(NOLOCK) ON osbH.Description = misc.SubCategoryName
	WHERE sfs.PlanYear = dbo.fngetbidyear()-1 AND ppi.PlanYear = dbo.fngetbidyear()
	)
	INSERT INTO @Results (PlanInfoID ,SubCategoryID ,CurrentYearINValue ,CurrentYearOONValue )
	SELECT planinfoid,subcategoryid,CurrentYearInValue,'' AS CurrentYearOONValue FROM OSB;

	/*Arc PD Audit Exhibit*/

WITH PDAE
AS (
SELECT ppi.planinfoid,
LEFT(ContractPBP, 5) + '-' + RIGHT(ContractPBP, 3) + '-' + spd.SegmentID AS CPS,
           dbo.fnGetBidYear()-1 AS [YEAR],
           spd.PlanType AS DrugPlanType,
           Formulary,
           FLOOR(Deductible) AS Deductible,
           BenefitString1MonthStandardRetail,
           ZeroDollarRxVBID,
           COPDVBID,
           DoacVbid,
           ErectileDysfunction,
           AntiObesityDrugCoverage,
           PrescriptionVitamins,
		   DeductibleExcludeAnyTiers
    FROM dbo.ArcSavedPDAuditExhibit spd WITH(NOLOCK)
        JOIN dbo.SavedPlanInfo spi WITH(NOLOCK)
            ON (spd.ContractPBP + spd.SegmentID) = REPLACE(spi.CPS, '-', '')
        JOIN dbo.LkpPlanType lpt WITH(NOLOCK)
            ON spi.PlanTypeID = lpt.PlanTypeID
		 JOIN PrePricing.PlanInfo ppi WITH(NOLOCK)
            ON ppi.CPS = spi.CPS
            AND ppi.PlanYear = dbo.fngetbidyear()
    WHERE spi.PlanYear = dbo.fnGetBidYear()-1
          AND lpt.PlanType = 'MAPD'
		  ),formatdata AS (
SELECT 1 AS subselect,PlanInfoID, CAST(ZeroDollarRxVBID AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Zero Dollar DSNP') AS subcategoryid FROM PDAE
UNION ALL
SELECT 2 AS subselect,PlanInfoID, CAST(AntiObesityDrugCoverage AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Anti Obesity') AS subcategoryid FROM PDAE
UNION ALL
SELECT 3 AS subselect,PlanInfoID, CAST(COPDVBID AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'COPD VBID') AS subcategoryid FROM PDAE
UNION ALL
SELECT 4 AS subselect,PlanInfoID, CAST(DoacVbid AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'DOAC VBID') AS subcategoryid FROM PDAE
UNION ALL
SELECT 5 AS subselect, PlanInfoID, CAST(DrugPlanType AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Drug Plan TYPE') AS subcategoryid FROM PDAE
UNION ALL
SELECT 6 AS subselect,PlanInfoID, CAST(ErectileDysfunction AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Erectile Dysfuntion') AS subcategoryid FROM PDAE
UNION ALL
SELECT 7 AS subselect,PlanInfoID, CAST(Formulary AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Formulary') AS subcategoryid FROM PDAE
UNION ALL
SELECT 8 AS subselect,PlanInfoID, CAST(PrescriptionVitamins AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Prescription Vitamins') AS subcategoryid FROM PDAE
UNION ALL
SELECT 9 AS subselect,PlanInfoID, CAST(Deductible AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'RX Deductible') AS subcategoryid FROM PDAE
UNION ALL
SELECT 10 AS subselect,PlanInfoID, CAST(BenefitString1MonthStandardRetail  AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Standard Rx String (Retail)') AS subcategoryid FROM PDAE
UNION ALL
SELECT 11 AS subselect, PlanInfoID, CAST(DeductibleExcludeAnyTiers  AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Tiers excluded from deductible') AS subcategoryid FROM PDAE
)
INSERT INTO @Results (PlanInfoID ,SubCategoryID ,CurrentYearINValue ,CurrentYearOONValue )
SELECT PlanInfoId,SubcategoryID,Invalue,OONValue FROM formatdata;

WITH CurrentYearSCT AS (
	SELECT ppi.planinfoid, sct.* 
	FROM prepricing.vwCurrentYearPlanMapping ppi 
	JOIN prepricing.PricingModelSCTValues sct  WITH(NOLOCK)
	ON ppi.CYCPS = sct.contractpbpsegment
	AND ppi.PlanYear -1 = sct.planyear
	WHERE sct.PlanYear = dbo.fngetbidyear()-1
), formatted AS (
	SELECT PlanInfoID, CAST(CAST(ROUND(AvgMembers,0) AS INT)  AS VARCHAR(1000)) AS CurrentYearINValue, '' AS CurrentYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Average Members') AS subcategoryid FROM CurrentYearSCT
	UNION ALL
	SELECT PlanInfoID, CAST(FORMAT(MER,'P1')  AS VARCHAR(1000)) AS CurrentYearINValue, '' AS CurrentYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'MER') AS subcategoryid FROM CurrentYearSCT
	UNION ALL
	SELECT PlanInfoID, CAST(CAST(ROUND(NetGrowth,0) AS INT)  AS VARCHAR(1000)) AS CurrentYearINValue, '' AS CurrentYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Net Growth') AS subcategoryid FROM CurrentYearSCT
	UNION ALL
	SELECT PlanInfoID, CAST(CAST(ROUND(UM,0)AS INT)  AS VARCHAR(1000)) AS CurrentYearINValue, '' AS CurrentYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'UM (in $000s)') AS subcategoryid FROM CurrentYearSCT
)
INSERT INTO @Results (PlanInfoID ,SubCategoryID ,CurrentYearINValue ,CurrentYearOONValue )
SELECT PlanInfoId,SubcategoryID,CurrentYearINValue,CurrentYearOONValue FROM formatted;



DELETE FROM @Results WHERE PlanInfoID IS NULL OR SubCategoryID IS NULL;
		RETURN
	END
GO
