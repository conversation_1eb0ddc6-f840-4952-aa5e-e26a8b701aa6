SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO


----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetCrosswalkInfo]   
--    
-- AUTHOR: Sur<PERSON>y 
--    
-- CREATED DATE: 2024-Nov-11   
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving cross walk info  
--    
-- PARAMETERS:   
	--@RegionID
-- Input: 
   
-- TABLES:   
-- PrePricing.PlanCrossWalkMapping
-- dbo.SavedMarketInfo
-- PrePricing.PlanCrossWalkType
-- PrePricing.PlanInfo
 
-- Read:    
--  

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Nov-11		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetCrosswalkInfo]
(
	@RegionID INT
)
AS    
BEGIN   
	SELECT * FROM(
	SELECT   a.CrossWalkID,c.CrosswalkTypeID,c.CrossWalkTypeName,e.CPS AS sourceplans, d.CPS AS targetplans,
	a.IsDeleted AS crosswalkdeleted, a.CrosswalkNotes AS CrosswalkNotes, a.LastUpdateDateTime AS LastUpdateDateTime,
	a.LastUpdateByID AS LastUpdateByID, NULL AS CurrentYearCPS
	FROM PrePricing.PlanCrossWalkHeader a WITH(NOLOCK)
	JOIN PrePricing.PlanCrossWalkDetail b WITH(NOLOCK) ON a.CrossWalkID = b.CrossWalkID
	JOIN PrePricing.PlanCrossWalkType c WITH(NOLOCK) ON c.CrosswalkTypeID=a.CrosswalkTypeID
	JOIN PrePricing.PlanInfo d WITH(NOLOCK) ON d.PlanInfoID=b.TargetPlanInfoID 
	JOIN PrePricing.PlanInfo e WITH(NOLOCK) ON e.PlanInfoID=b.ContributingPlanInfoID
	JOIN dbo.SavedMarketInfo f WITH(NOLOCK) ON f.ActuarialMarketID=d.MarketID AND f.ActuarialRegionID = @RegionID
	WHERE  a.CrosswalkTypeID IN(1,4)	
	UNION ALL
	SELECT   a.CrossWalkID,c.CrosswalkTypeID,c.CrossWalkTypeName,STRING_AGG(e.CPS,',') WITHIN GROUP (ORDER BY IsCurrentYearPlan DESC, e.CPS ASC)  AS sourceplans, d.CPS AS targetplans,
	a.IsDeleted AS crosswalkdeleted, a.CrosswalkNotes AS CrosswalkNotes, a.LastUpdateDateTime AS LastUpdateDateTime,
	a.LastUpdateByID AS LastUpdateByID,[PrePricing].[fnGetCrosswalkCurrentYearPlan](a.CrossWalkID) AS CurrentYearCPS
	FROM PrePricing.PlanCrossWalkHeader a WITH(NOLOCK)
	JOIN PrePricing.PlanCrossWalkDetail b WITH(NOLOCK) ON a.CrossWalkID = b.CrossWalkID
	JOIN PrePricing.PlanCrossWalkType c WITH(NOLOCK) ON c.CrosswalkTypeID=a.CrosswalkTypeID
	JOIN PrePricing.PlanInfo d WITH(NOLOCK) ON d.PlanInfoID=b.TargetPlanInfoID 
	JOIN PrePricing.PlanInfo e WITH(NOLOCK) ON e.PlanInfoID=b.ContributingPlanInfoID
	JOIN dbo.SavedMarketInfo f WITH(NOLOCK) ON f.ActuarialMarketID=d.MarketID AND f.ActuarialRegionID = @RegionID 
	WHERE  a.CrosswalkTypeID = 2	 
	GROUP BY a.CrossWalkID ,a.IsDeleted,a.CrosswalkNotes, c.CrosswalkTypeID,c.CrossWalkTypeName, d.cps,a.LastUpdateDateTime,a.LastUpdateByID
	UNION ALL
	SELECT   a.CrossWalkID,c.CrosswalkTypeID,c.CrossWalkTypeName,e.CPS AS sourceplans, STRING_AGG(d.CPS,',') AS targetplans,
	a.IsDeleted AS crosswalkdeleted, a.CrosswalkNotes AS CrosswalkNotes, a.LastUpdateDateTime AS LastUpdateDateTime,
	a.LastUpdateByID AS LastUpdateByID, NULL AS CurrentYearCPS
	FROM PrePricing.PlanCrossWalkHeader a WITH(NOLOCK)
	JOIN PrePricing.PlanCrossWalkDetail b WITH(NOLOCK) ON a.CrossWalkID = b.CrossWalkID
	JOIN PrePricing.PlanCrossWalkType c WITH(NOLOCK) ON c.CrosswalkTypeID=a.CrosswalkTypeID
	JOIN PrePricing.PlanInfo d WITH(NOLOCK) ON d.PlanInfoID=b.TargetPlanInfoID 
	JOIN PrePricing.PlanInfo e WITH(NOLOCK) ON e.PlanInfoID=b.ContributingPlanInfoID
	JOIN dbo.SavedMarketInfo f WITH(NOLOCK) ON f.ActuarialMarketID=d.MarketID AND f.ActuarialRegionID = @RegionID
	WHERE  c.CrosswalkTypeID = 3
	GROUP BY a.CrossWalkID ,a.IsDeleted,a.CrosswalkNotes, c.CrosswalkTypeID,c.CrossWalkTypeName, e.cps,a.LastUpdateDateTime,a.LastUpdateByID
	UNION ALL	
	SELECT  a.PlanFlipID AS CrossWalkID,5 AS CrosswalkTypeID, 'Plan Flip' AS CrossWalkTypeName,a.PreviousCPS AS sourceplans, a.NewCPS AS targetplans,
	0 AS crosswalkdeleted, '' AS CrosswalkNotes, a.LastUpdateDateTime AS LastUpdateDateTime,a.LastUpdateByID AS LastUpdateByID, NULL AS CurrentYearCPS
	FROM PrePricing.PlanFlip a WITH(NOLOCK)
	JOIN PrePricing.PlanInfo AS b WITH(NOLOCK) ON b.PlanInfoID=a.PlanInfoID
	JOIN dbo.SavedMarketInfo e WITH(NOLOCK) ON e.ActuarialMarketID=b.MarketID AND e.ActuarialRegionID = @RegionID
	) dum
	ORDER BY dum.LastUpdateDateTime DESC
END


GO
