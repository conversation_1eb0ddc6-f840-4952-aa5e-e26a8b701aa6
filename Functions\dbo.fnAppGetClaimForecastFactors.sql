SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:	fnAppGetClaimForecastFactors
--
-- CREATOR:			<PERSON><PERSON>
--
-- CREATED DATE:	2009-FEB-21
--
-- DESCRIPTION:		Function responsible for retreiving ClaimForecastValues in a "user-friendly" format
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT
--
--  Output :		FactorName VARCHAR(50)
--					,WS1Column  VARCHAR(1)
--					,FactorType VARCHAR(50)
--					,Man_E2C    DECIMAL(8, 6)
--					,Man_C2P    DECIMAL(8, 6)
--					,Exp_E2C    DECIMAL(8, 6)
--					,Exp_C2P    DECIMAL(8, 6)
--
-- TABLES : 
--	Read :			CalcPlanExperienceByBenefitCat
--					LkpExtCMSWorksheet1Mapping
--					LkpIntTrendType
--					PerIntClaimFactorType
--					SavedBenefitLevelClaimFactors
--					SavedPlanDetail
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetSafeDivisionResultReturnOne
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2009-FEB-21		1			Initial Version							                            Mallika Eyunni					
-- 2009-FEB-23      2           Join on Plan Version					                            Sandy Ellis
-- 2009-FEB-24      3           Added ForecastID to Where Clause			                            Brian Lake
-- 2009-MAR-17      4           Data Types								                            Sandy Ellis
-- 2010-FEB-05  	5			Added PlanYearID distinction to join	                            Michael Siekerka
-- 2010-OCT-05      6           Updated to 2012 structure. Included @MARatingOptionID parameter     Nate Jacoby
-- 2010-OCT-14      7           Fixed so output would not contain duplicates                        Michael Siekerka
-- 2010-DEC-22      8           Rebuilt to fit format of new design in the model                    Joe Casey
-- 2010-DEC-29      9           Updated to deal with benefit level claim factors and then weight    Joe Casey
--                                  them on Allowed.  Include IN and OON.
-- 2012-NOV-14		10			Changed from SavedClaimFactorBenefitLevel to						Mike Deren
--								SavedBenefitLevelClaimFactors for updated trend				
-- 2012-NOV-28		11			Changed PerIntClaimFactorType to PerIntClaimFactorTypeTest			Mike Deren
--									for updated trend. Commented out join.
-- 2014-JAN-09		12			Changind Table Name													Mike Deren 
-- 2024-MAY-05      13			Added NOLOCK Table Hint												Kiran Kola
-- 2024-OCT-07		14			Cost Share Basis: add handling for IsIncludeInCostShareBasis flag
--									in the base data tables											Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnAppGetClaimForecastFactors]
    (@ForecastID AS INT)

RETURNS @Results TABLE
    (FactorName VARCHAR(50)
    ,WS1Column  VARCHAR(1)
    ,FactorType VARCHAR(50)
    ,Man_E2C    DECIMAL(8, 6)
    ,Man_C2P    DECIMAL(8, 6)
    ,Exp_E2C    DECIMAL(8, 6)
    ,Exp_C2P    DECIMAL(8, 6))

AS

    BEGIN

        DECLARE @XForecastID INT = @ForecastID;

        --In Network Claim Factors
        DECLARE @INClaimFactors TABLE
            (ClaimForecastID   INT
            ,ClaimFactorTypeID INT
            ,ProjectionYearID  INT
            ,BenefitCategoryID INT
            ,ClaimFactor       DECIMAL(8, 6));

        INSERT INTO @INClaimFactors
            (ClaimForecastID
            ,ClaimFactorTypeID
            ,ProjectionYearID
            ,BenefitCategoryID
            ,ClaimFactor)
        SELECT      DISTINCT
                    bl.ClaimForecastID
                   ,bl.ClaimFactorTypeID
                   ,bl.ProjectionYearID
                   ,bl.BenefitCategoryID
                   ,bl.ClaimFactor
        FROM        dbo.SavedBenefitLevelClaimFactors bl WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanDetail spd WITH (NOLOCK)
               ON bl.ClaimForecastID = spd.ClaimForecastID
        WHERE       spd.ForecastID = @XForecastID
                    AND bl.IsInNetwork = 1; --In Network

        --Out Of Network Claim Factors
        DECLARE @OONClaimFactors TABLE
            (ClaimForecastID   INT
            ,ClaimFactorTypeID INT
            ,ProjectionYearID  INT
            ,BenefitCategoryID INT
            ,ClaimFactor       DECIMAL(8, 6));

        INSERT INTO @OONClaimFactors
            (ClaimForecastID
            ,ClaimFactorTypeID
            ,ProjectionYearID
            ,BenefitCategoryID
            ,ClaimFactor)
        SELECT      DISTINCT
                    bl.ClaimForecastID
                   ,bl.ClaimFactorTypeID
                   ,bl.ProjectionYearID
                   ,bl.BenefitCategoryID
                   ,bl.ClaimFactor
        FROM        dbo.SavedBenefitLevelClaimFactors bl WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanDetail spd WITH (NOLOCK)
               ON bl.ClaimForecastID = spd.ClaimForecastID
        WHERE       spd.ForecastID = @XForecastID
                    AND bl.IsInNetwork = 0; --Out Of Network

        --Experience Allowed
        DECLARE @ExpAllowed TABLE
            (BenefitCategoryID INT
            ,INAllowed         DECIMAL(14, 6)
            ,OONAllowed        DECIMAL(14, 6));

        INSERT INTO @ExpAllowed
            (BenefitCategoryID
            ,INAllowed
            ,OONAllowed)
        SELECT      BenefitCategoryID
                   ,SUM (INAllowed) AS INAllowed
                   ,SUM (OONAllowed) AS OONAllowed
        FROM        dbo.CalcPlanExperienceByBenefitCat WITH (NOLOCK)
        WHERE       MARatingOptionID = 1 --Experience
                    AND DualEligibleTypeID = 2 --Both Dual and NonDual
                    AND ForecastID = @XForecastID
        GROUP BY    BenefitCategoryID;

        --Manual Allowed
        DECLARE @ManAllowed TABLE
            (BenefitCategoryID INT
            ,INAllowed         DECIMAL(14, 6)
            ,OONAllowed        DECIMAL(14, 6));

        INSERT INTO @ManAllowed
            (BenefitCategoryID
            ,INAllowed
            ,OONAllowed)
        SELECT      BenefitCategoryID
                   ,SUM (INAllowed) AS INAllowed
                   ,SUM (OONAllowed) AS OONAllowed
        FROM        dbo.CalcPlanExperienceByBenefitCat WITH (NOLOCK)
        WHERE       MARatingOptionID = 2 --Manual
                    AND DualEligibleTypeID = 2 --Both Dual and NonDual
                    AND ForecastID = @XForecastID
        GROUP BY    BenefitCategoryID;

        --Results
        -- Define CTE for base data aggregation
        WITH BaseData
        AS (SELECT      cft.[Name] AS ClaimFactorName
                       ,ws.ColumnLetter
                       ,tt.[Name] AS TrendName
                       ,spd.MARatingOptionID
                       ,INcfd.ProjectionYearID
                       ,ClaimFactorTotal = CASE WHEN spd.MARatingOptionID = 1 THEN -- Experience
                                                    ISNULL (
                                                    SUM (
                                                    exper.INAllowed * INcfd.ClaimFactor + exper.OONAllowed
                                                    * OONcfd.ClaimFactor)
                                                   ,0)
                                                WHEN spd.MARatingOptionID = 2 THEN -- Manual
                                                    ISNULL (
                                                    SUM (
                                                    man.INAllowed * INcfd.ClaimFactor + man.OONAllowed * OONcfd.ClaimFactor)
                                                   ,0)
                                                ELSE NULL END
                       ,UtilAllowedTotal = CASE WHEN spd.MARatingOptionID = 1 THEN -- Experience
                                                    ISNULL (SUM (exper.INAllowed + exper.OONAllowed), 0)
                                                WHEN spd.MARatingOptionID = 2 THEN -- Manual
                                                    ISNULL (SUM (man.INAllowed + man.OONAllowed), 0)
                                                ELSE NULL END
            FROM        @INClaimFactors INcfd
           INNER JOIN   @OONClaimFactors OONcfd
                   ON INcfd.ClaimForecastID = OONcfd.ClaimForecastID
                      AND   INcfd.ClaimFactorTypeID = OONcfd.ClaimFactorTypeID
                      AND   INcfd.ProjectionYearID = OONcfd.ProjectionYearID
                      AND   INcfd.BenefitCategoryID = OONcfd.BenefitCategoryID
           INNER JOIN   dbo.SavedPlanDetail spd WITH (NOLOCK)
                   ON INcfd.ClaimForecastID = spd.ClaimForecastID
           INNER JOIN   dbo.PerIntClaimFactorTypeTest cft WITH (NOLOCK)
                   ON INcfd.ClaimFactorTypeID = cft.ClaimFactorTypeID
           INNER JOIN   dbo.LkpIntTrendType tt WITH (NOLOCK)
                   ON cft.TrendTypeID = tt.TrendTypeID
           INNER JOIN   dbo.LkpExtCMSWorksheet1Mapping ws WITH (NOLOCK)
                   ON cft.ColumnIDBPT = ws.ColumnID
            LEFT JOIN   @ManAllowed man
                   ON INcfd.BenefitCategoryID = man.BenefitCategoryID
            LEFT JOIN   @ExpAllowed exper
                   ON INcfd.BenefitCategoryID = exper.BenefitCategoryID
            WHERE       spd.ForecastID = @ForecastID
            GROUP BY    cft.[Name]
                       ,ws.ColumnLetter
                       ,tt.[Name]
                       ,spd.MARatingOptionID
                       ,INcfd.ProjectionYearID)

            -- Define CTE for source table preparation
            ,SourceData
        AS (SELECT  ClaimFactorName
                   ,ColumnLetter AS WS1Column
                   ,TrendName AS FactorType
                   ,ColumnCount = CASE WHEN MARatingOptionID = 2 THEN CASE WHEN ProjectionYearID = 1 THEN 1 ELSE 2 END
                                       WHEN MARatingOptionID = 1 THEN CASE WHEN ProjectionYearID = 1 THEN 3 ELSE 4 END
                                       ELSE NULL END
                   ,dbo.fnGetSafeDivisionResultReturnOne (ClaimFactorTotal, UtilAllowedTotal) AS ClaimFactor
            FROM    BaseData)

        -- Inserting results into @Results table with PIVOT operation
        INSERT INTO @Results
        SELECT      ClaimFactorName AS FactorName
                   ,TRIM (WS1Column) AS WS1Column
                   ,FactorType
                   ,CAST([1] AS DECIMAL(8, 6)) AS Man_E2C
                   ,CAST([2] AS DECIMAL(8, 6)) AS Man_C2P
                   ,CAST([3] AS DECIMAL(8, 6)) AS Exp_E2C
                   ,CAST([4] AS DECIMAL(8, 6)) AS Exp_C2P
        FROM        SourceData
        PIVOT (MAX(ClaimFactor)
               FOR ColumnCount IN ([1], [2], [3], [4])) AS PivotTable
        ORDER BY    ClaimFactorName
                   ,FactorType;

        RETURN;

    END;
GO
