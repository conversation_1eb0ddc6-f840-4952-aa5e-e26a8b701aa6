-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	TrendAdj_spCalcOSNP
--
-- CREATOR:			<PERSON><PERSON><PERSON><PERSON>
--
-- CREATED DATE:	2024-Jul-30
--
-- DESCRIPTION:		This sp is executed by the TrendAdj_spCalcSAR; it populates the TrendAdj_OSNP_CalcKeyInfo_stage table.
--		
-- PARAMETERS:
--  Input  :		@SessionID
--					@PlanInfoIdList
--					@LastUpdateByID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			LkpIntBenefitCategory
--					SavedForecastSetup
--					SavedRollupForecastMap
--					SavedRollupInfo
--					Trend_SavedPopulationBarcBidYearMembership
--					Trend_SavedRelativityPopulation
--					Trend_ProjProcess_CalcIsPlanLevel_Population
--					TrendAdj_OSNP_LkpSNPType
--					TrendAdj_OSNP_SavedPopRegression
--					TrendAdj_OSNP_CalcKeyInfo_stage
--
--  Write:			TrendAdj_OSNP_CalcKeyInfo_stage
--
-- VIEWS: 
--	Read:			vwPlanInfo
--					vwSAMCrosswalks
--
-- FUNCTIONS:		Trend_fnCalcStringToTable
--					Trend_fnSafeDivide
--					fngetbidyear
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-Jul-30      1           Initial Version                                                     Aleksandar Dimitrijevic
-- 2024-Oct-10      2           Added NOLOCK & optimazation	                                        Kumar Jalendran
-- 2024-Oct-17      3           Dropped all temp tables at end of procedure                         Kumar Jalendran
-- 2024-Oct-25      4           Updated the error loging to dbo.TrendAdjLog                         Kumar Jalendran
-- 2024-Nov-11		5			Replace CPS with PlanInfoID in permanent tables						Michael Manes
-- 2025-Jan-09		6			Remove the MMThreshold condition									Kumar Jalendran
-- 2025-Feb-22		7			Added Plan filter as getting duplicate Plans   						Kumar Jalendran
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[TrendAdj_spCalcOSNP]

--Input parameters and data types
@SessionID VARCHAR(19),
@PlanInfoIdList	VARCHAR(MAX),
@LastUpdateByID CHAR(7)

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY


            BEGIN TRANSACTION transaction_TrendAdj_spCalcOSNP;

			-- Declare local variables to use in place of input parameters to negate parameter sniffing.
			DECLARE @xPlanList VARCHAR(MAX) = @PlanInfoIdList;
			DECLARE @xLastUpdateByID CHAR(7) = @LastUpdateByID;
			DECLARE @xSessionID VARCHAR(19) = @SessionID;

            -- Declare variables
			DECLARE @BaseYear INT = (SELECT dbo.fngetbidyear() - 2);
			DECLARE @stringChronic VARCHAR(20) ='Chronic';
			DECLARE @stringInstitutional VARCHAR(20) ='Institutional';
			DECLARE @stringAncil VARCHAR(20) ='Ancil';

			-- Create a temp table with Reporting Categories
			DROP TABLE IF EXISTS #RepCat;

			CREATE TABLE #RepCat 
				(
				 ReportingCategory VARCHAR(50)
				);
				
			INSERT INTO #RepCat
			SELECT DISTINCT ReportingCategory
					   FROM dbo.LkpIntBenefitCategory WITH (NOLOCK)
					  WHERE ReportingCategory IS NOT NULL;

            -- Creating a CPS list, based on the PlanInfoID list
			DROP TABLE IF EXISTS #CPS;

			SELECT HPASID AS CPS
				  ,PlanInfoID
			  INTO #CPS
			  FROM dbo.SavedForecastSetup WITH (NOLOCK)
			 WHERE PlanInfoID IN(SELECT Val AS PlanInfoID
								   FROM dbo.Trend_fnCalcStringToTable (@xPlanList, ',', 1));

			/********************************************/
			/*****          Current Method          *****/
			/********************************************/

			-- Creating a table with Current/Bid year Population Trends
			DROP TABLE IF EXISTS #sqlTrends;

			SELECT DISTINCT a.CPS,
							a.PlanYearID,
							a.TrendYearID,
							a.RateType,
							a.UseAdjustment
					   INTO #sqlTrends
					   FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_Population a WITH (NOLOCK)
				 INNER JOIN #CPS cps WITH (NOLOCK) 
						 ON a.CPS = cps.CPS
				 INNER JOIN dbo.vwSAMCrosswalks vsc WITH (NOLOCK)
						 ON vsc.BidYearPlanInfoID = a.PlanInfoID
				 INNER JOIN dbo.SavedForecastSetup sfs WITH (NOLOCK)
						 ON sfs.PlanInfoID = vsc.BidYearPlanInfoID 
						AND sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID
				 INNER JOIN dbo.SavedRollupForecastMap map WITH (NOLOCK)
						 ON map.ForecastID = sfs.ForecastID 
				 INNER JOIN dbo.SavedRollupInfo sri WITH (NOLOCK)
						 ON sri.RollupID = map.RollupID
				  LEFT JOIN dbo.vwPlanInfo vpi WITH (NOLOCK)
						 ON vpi.PlanInfoID = sfs.PlanInfoID 
					  WHERE sri.RollupName = 'Live' 
						AND vsc.BidYearRenewalTypeID = 1
						AND vpi.SNPType IN (@stringChronic, @stringInstitutional) 
						AND a.RateType = 2 
						AND a.ReportingCategory = @stringAncil 

			UNION ALL

			SELECT DISTINCT a.CPS,
							a.PlanYearID,
							a.TrendYearID,
							a.RateType,
							a.UseAdjustment
					   FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_Population a WITH (NOLOCK)
				 INNER JOIN #CPS cps WITH (NOLOCK) 
						 ON a.CPS = cps.CPS
				 INNER JOIN dbo.vwSAMCrosswalks vsc WITH (NOLOCK)
						 ON vsc.BidYearPlanInfoID = a.PlanInfoID
				 INNER JOIN dbo.SavedForecastSetup sfs WITH (NOLOCK)
						 ON sfs.PlanInfoID = vsc.BidYearPlanInfoID 
						AND sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID
				 INNER JOIN dbo.SavedRollupForecastMap map WITH (NOLOCK)
						 ON map.ForecastID = sfs.ForecastID 
				 INNER JOIN dbo.SavedRollupInfo sri WITH (NOLOCK)
						 ON sri.RollupID = map.RollupID
				  LEFT JOIN dbo.vwPlanInfo vpi WITH (NOLOCK)
						 ON vpi.PlanInfoID = sfs.PlanInfoID 
					  WHERE sri.RollupName = 'Live' 
						AND vsc.BidYearRenewalTypeID NOT IN(1,2) 
						AND vpi.SNPType IN ( @stringChronic, @stringInstitutional )
						AND a.RateType = 2 
						AND a.ReportingCategory = @stringAncil;

			-- Ordering Population trends to calculate the two year trends
			DROP TABLE IF EXISTS #sqlTrends_Ordered;

			SELECT CPS,
				   TrendYearID,
				   UseAdjustment,
				   LAG(UseAdjustment, 1, 0) OVER (PARTITION BY CPS ORDER BY TrendYearID) AS PriorAdjustment
			  INTO #sqlTrends_Ordered
			  FROM #sqlTrends WITH (NOLOCK);

			-- Calculating Population two year trend
			DROP TABLE IF EXISTS #twoyear_Trends;

			SELECT CPS,
				   (1 + PriorAdjustment) * (1 + UseAdjustment) - 1 AS TwoYear_Trend
			  INTO #twoyear_Trends
			  FROM #sqlTrends_Ordered WITH (NOLOCK)
			 WHERE TrendYearID =  @BaseYear + 2;

			-- Creating a table with Bid Year Population Relativities
			DROP TABLE IF EXISTS #BidYear_RelCur;

			SELECT a.CPS,
				   a.PlanYearID,
				   AVG(a.UseRelativity) AS UseRelativity 
			  INTO #BidYear_RelCur
			  FROM dbo.Trend_SavedRelativityPopulation a WITH (NOLOCK)
			 INNER JOIN #CPS cps WITH (NOLOCK) 
			    ON a.CPS = cps.CPS
			 INNER JOIN dbo.vwSAMCrosswalks vsc WITH (NOLOCK)
				ON vsc.BidYearCPS = a.CPS 
			   AND vsc.BidYear = a.PlanYearID 
			 INNER JOIN dbo.SavedForecastSetup sfs WITH (NOLOCK)
				ON sfs.PlanInfoID = vsc.BidYearPlanInfoID 
			   AND sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID 
			 INNER JOIN dbo.SavedRollupForecastMap map WITH (NOLOCK)
			    ON map.ForecastID = sfs.ForecastID 
			 INNER JOIN dbo.SavedRollupInfo sri WITH (NOLOCK)
			    ON sri.RollupID = map.RollupID 
			  LEFT JOIN dbo.vwPlanInfo vpi WITH (NOLOCK)
			    ON vpi.PlanInfoID = sfs.PlanInfoID 
			 WHERE a.ReportingCategory = @stringAncil 
			   AND a.PlanYearID = @BaseYear + 2 
			   AND sri.RollupName = 'Live' 
			   AND vsc.BidYearRenewalTypeID = 1 
			   AND vpi.SNPType IN (@stringChronic, @stringInstitutional) 
			 GROUP BY a.CPS,a.PlanYearID 

			UNION ALL 
				
			SELECT a.CPS,
				   a.PlanYearID,
				   AVG(a.UseRelativity) AS UseRelativity 
			  FROM dbo.Trend_SavedRelativityPopulation a WITH (NOLOCK)
			 INNER JOIN dbo.vwSAMCrosswalks vsc WITH (NOLOCK)
				ON vsc.BidYearCPS = a.CPS 
			   AND vsc.BidYear = a.PlanYearID 
			 INNER JOIN dbo.SavedForecastSetup sfs WITH (NOLOCK)
				ON sfs.PlanInfoID = vsc.BidYearPlanInfoID 
			   AND sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID 
			 INNER JOIN dbo.SavedRollupForecastMap map WITH (NOLOCK)
				ON map.ForecastID = sfs.ForecastID 
			 INNER JOIN dbo.SavedRollupInfo sri WITH (NOLOCK)
				ON sri.RollupID = map.RollupID 
			  LEFT JOIN dbo.vwPlanInfo vpi WITH (NOLOCK)
				ON vpi.PlanInfoID = sfs.PlanInfoID
			 INNER JOIN #CPS cps WITH (NOLOCK) 
			    ON cps.CPS = a.CPS
			 WHERE a.ReportingCategory = @stringAncil 
			   AND a.PlanYearID = @BaseYear + 2 
			   AND sri.RollupName = 'Live' 
			   AND vsc.BidYearRenewalTypeID NOT IN ( 1, 2 ) 
			   AND vpi.SNPType IN (@stringChronic, @stringInstitutional) 
			 GROUP BY a.CPS,a.PlanYearID;

			/********************************************/
			/*****            New Method            *****/
			/********************************************/

			-- Checking if any ISNP lookalike plans in the list
			DROP TABLE IF EXISTS #SNPlkl;

			SELECT cps.CPS
				  ,SNPTypeDetail
			  INTO #SNPlkl
			  FROM dbo.TrendAdj_OSNP_LkpSNPType ts WITH (NOLOCK)
			  INNER JOIN #CPS cps WITH (NOLOCK) ON ts.PlanInfoID = cps.PlanInfoID;

			-- Creating a table with SNP Details for selected plans
			DROP TABLE IF EXISTS #SNPType;

			SELECT DISTINCT vpi.CPS,
							SNPTypeDetail
					   INTO #SNPType
					   FROM dbo.vwPlanInfo vpi WITH (NOLOCK)
					   INNER JOIN #CPS cps WITH (NOLOCK) ON vpi.CPS = cps.CPS
					  WHERE SNPType IN(@stringChronic,@stringInstitutional)
						AND PlanYear = @BaseYear + 2 ;

			-- Updating the SNP type if lkl plan match
			MERGE INTO #SNPType t
			USING (SELECT CPS,
						  SNPTypeDetail
					 FROM #SNPlkl WITH (NOLOCK)) l
					   ON (l.CPS = t.CPS)
					 WHEN MATCHED 
					 THEN UPDATE
					  SET t.SNPTypeDetail = l.SNPTypeDetail;

			-- SNP Relativities by Category by Year
			DROP TABLE IF EXISTS #SNPRels;

			SELECT PlanYear,
				   SNPTypeDetail,
				   dbo.Trend_fnSafeDivide(SUM(WtdRelativity), SUM(MemberMonths), 1) AS Relativity
			  INTO #SNPRels
			  FROM dbo.TrendAdj_OSNP_SavedPopRegression WITH (NOLOCK)
			 GROUP BY PlanYear,
				      SNPTypeDetail;

			-- Ordering SNP Relativities to calculate trends
			DROP TABLE IF EXISTS #SNPRels_Ordered;

			SELECT PlanYear,
				   SNPTypeDetail,
				   Relativity,
				   LAG(Relativity, 1, 0) OVER (PARTITION BY SNPTypeDetail ORDER BY PlanYear) AS PriorRelativity
			  INTO #SNPRels_Ordered
			  FROM #SNPRels WITH (NOLOCK);

			-- SNP Trends by Category by Year
			DROP TABLE IF EXISTS #SNPTrends;

			SELECT SNPTypeDetail,
				   AVG(dbo.Trend_fnSafeDivide(Relativity, PriorRelativity, 1) - 1) AS Trend
			  INTO #SNPTrends
			  FROM #SNPRels_Ordered WITH (NOLOCK)
			 WHERE PlanYear > @BaseYear - 4
			 GROUP BY SNPTypeDetail;

			-- Modifying trend for category D-SNP DEMZCS to be equal to I-SNP
			INSERT INTO #SNPTrends
			SELECT 'D-SNP DEMZCS' AS SNPTypeDetail,
					Trend = (SELECT Trend
							   FROM #SNPTrends WITH (NOLOCK)
							  WHERE SNPTypeDetail = 'I-SNP');

			--Calculating the base year relativity
			DROP TABLE IF EXISTS #BaseYear_RelCur;

			SELECT t.CPS,
				   dbo.Trend_fnSafeDivide(rs.Relativity, rt.Relativity, 1) AS UseRelativity
			  INTO #BaseYear_RelCur
			  FROM #SNPRels rs WITH (NOLOCK)
			 CROSS JOIN #SNPRels rt WITH (NOLOCK)
			 INNER JOIN #SNPType t WITH (NOLOCK)
				ON t.SNPTypeDetail = rs.SNPTypeDetail
			 WHERE rs.PlanYear = @BaseYear
			   AND rt.PlanYear = @BaseYear
			   AND rt.SNPTypeDetail = 'Total';

			--Calculating the bid year relativity
			DROP TABLE IF EXISTS #BidYear_RelNew;

			SELECT t.CPS,
				   POWER((1 + st.Trend), 2) * rc.UseRelativity AS UseRelativity
			  INTO #BidYear_RelNew
			  FROM #SNPTrends st WITH (NOLOCK)
			 INNER JOIN #SNPType t WITH (NOLOCK)
				ON t.SNPTypeDetail = st.SNPTypeDetail
			 INNER JOIN #BaseYear_RelCur rc WITH (NOLOCK)
				ON rc.CPS = t.CPS;

			/********************************************/
			/***** Calculating the final adjustment *****/
			/********************************************/

			INSERT INTO dbo.TrendAdj_OSNP_CalcKeyInfo_stage ([SessionID], [CPS], [Method], [PlanYearID], [Relativity])
				 SELECT @xSessionID,
						CPS,
					    'Standard',
					    @BaseYear + 2,
					    UseRelativity
				   FROM #BidYear_RelCur WITH (NOLOCK);

			-- Inserting Base Year Relativity for the current method
			INSERT INTO dbo.TrendAdj_OSNP_CalcKeyInfo_stage ([SessionID], [CPS], [Method], [PlanYearID], [Relativity])
				 SELECT @xSessionID,
						cki.CPS,
					    'Standard',
					    @BaseYear,
					    cki.Relativity / (1 + tt.TwoYear_Trend)
				   FROM dbo.TrendAdj_OSNP_CalcKeyInfo_stage cki WITH (NOLOCK)
				  INNER JOIN #twoyear_Trends tt WITH (NOLOCK)
					 ON tt.CPS = cki.CPS
				  WHERE cki.PlanYearID = @BaseYear + 2;

			-- Inserting Base Year Relativity for the new method
			INSERT INTO dbo.TrendAdj_OSNP_CalcKeyInfo_stage ([SessionID], [CPS], [Method], [PlanYearID], [Relativity])
				 SELECT @xSessionID,
						CPS,
					    'New',
					    @BaseYear,
					    UseRelativity
				   FROM #BaseYear_RelCur WITH (NOLOCK);

			-- Inserting Bid Year Relativity for the new method
			INSERT INTO dbo.TrendAdj_OSNP_CalcKeyInfo_stage ([SessionID], [CPS], [Method], [PlanYearID], [Relativity])
				 SELECT @xSessionID,
						cki.CPS,
					    'New',
					    @BaseYear + 2,
					    cki.Relativity * POWER(1 + Trend, 2)
				   FROM dbo.TrendAdj_OSNP_CalcKeyInfo_stage cki WITH (NOLOCK)
				  INNER JOIN #SNPType stp WITH (NOLOCK)
					 ON stp.CPS = cki.CPS
				  INNER JOIN #SNPTrends sts WITH (NOLOCK)
					 ON sts.SnpTypeDetail = stp.SnpTypeDetail
				  WHERE cki.Method = 'New'
				    AND cki.PlanYearID = @BaseYear;
			
			-- Final output
			SELECT 
				   'RepCat' AS Granularity,
				   'Other SNP Population Adjustment' AS AdjustmentDescription,
				   np.CPS,
				   @BaseYear + 2 AS TrendYearID,
				   2 AS RateType,
				   rc.ReportingCategory,
				   NULL AS BenefitCategoryID,
				   CostAdjustment = 0,
				   UseAdjustment = (np.Relativity / sb.RElativity) / (1 + tt.TwoYear_Trend) - 1
			  FROM dbo.TrendAdj_OSNP_CalcKeyInfo_stage np WITH (NOLOCK)
			 CROSS JOIN #RepCat rc WITH (NOLOCK)
			 INNER JOIN dbo.TrendAdj_OSNP_CalcKeyInfo_stage sb WITH (NOLOCK)
				ON sb.CPS = np.CPS
			 INNER JOIN #twoyear_Trends tt WITH (NOLOCK)
				ON tt.CPS = np.CPS
			 WHERE np.PlanYearID = @BaseYear + 2
			   AND np.Method = 'New'
			   AND sb.PlanYearID = @BaseYear
			   AND sb.Method = 'Standard'
			   AND rc.ReportingCategory NOT IN ('Part B Rx Pharmacy','MOC');

            COMMIT TRANSACTION transaction_TrendAdj_spCalcOSNP;

			-- Clean all temp tables.
			DROP TABLE IF EXISTS #RepCat;
			DROP TABLE IF EXISTS #CPS;
			DROP TABLE IF EXISTS #sqlTrends;
			DROP TABLE IF EXISTS #sqlTrends_Ordered;
			DROP TABLE IF EXISTS #twoyear_Trends;
			DROP TABLE IF EXISTS #BidYear_RelCur;
			DROP TABLE IF EXISTS #SNPlkl;
			DROP TABLE IF EXISTS #SNPType;
			DROP TABLE IF EXISTS #SNPRels;
			DROP TABLE IF EXISTS #SNPRels_Ordered;
			DROP TABLE IF EXISTS #SNPTrends;
			DROP TABLE IF EXISTS #BaseYear_RelCur;
			DROP TABLE IF EXISTS #BidYear_RelNew;

        END TRY

        BEGIN CATCH

			DECLARE @ErrorMessage NVARCHAR(4000);
		    DECLARE @ErrorSeverity INT;
		    DECLARE @ErrorState INT;
		    DECLARE @ErrorException NVARCHAR(4000);
		    DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
				    @currentdate DATETIME = GETDATE();

			SELECT @ErrorMessage = ERROR_MESSAGE();
			SELECT @ErrorSeverity = ERROR_SEVERITY();
			SELECT @ErrorState = ERROR_STATE();

			RAISERROR(   
				@ErrorMessage,  -- Message text.  
				@ErrorSeverity, -- Severity.  
				@ErrorState     -- State.  
			);

			ROLLBACK TRANSACTION;
			--- Insert into app log for logging error------------------
			INSERT INTO dbo.TrendAdjLog
                (AdjGroupID
                ,ProcName
                ,Region
                ,UserID
                ,AuditTime
                ,AuditMessage)
            VALUES (1
                   ,'Calc:OSNP'
                   ,(SELECT TOP 1 region FROM dbo.vwPlanInfo WITH(NOLOCK) WHERE PlanInfoID IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable(@xPlanList, ',', 1)))
                   ,@xLastUpdateByID
                   ,GETDATE ()
                   ,@ErrorMessage);

		END CATCH;
		
    END;
GO