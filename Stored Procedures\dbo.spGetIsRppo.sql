SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------    
-- Stored Procedure NAME: [[spGetIsRppo]]    
--    
-- AUTHOR: Sivachidambaram    
-- Type : New Sp created by useing existing Inline Query from MODEL    
-- CREATED DATE: 2011-Sep-30    
--    
-- DESCRIPTION: Stored Procedure is responsible for select the data from the function fnAppMABPTCreation    
--    
-- PARAMETERS:    
-- Input:    
-- @ForecastID    
-- Output:    
-- @IsRunable    
-- TABLES:     
-- Read:    
     
-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
--  fnAppMABPTCreation    
-- STORED PROCS:    
--    
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE    VERSION  CHANGES MADE              DEVELOPER      
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2011-Sep-30  1   Initial Version              Sivachidambaram    
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [dbo].[spGetIsRppo]    
@ForecastID int,    
@IsRPPO BIT OUTPUT    
    
AS    
    
BEGIN    
 IF EXISTS     
    
 (     
    
 SELECT 1    
 FROM dbo.SavedPlanDetail spd     
 INNER JOIN dbo.SavedPlanHeader sph     
 ON sph.ForecastID = spd.ForecastID     
 WHERE spd.ForecastID = @ForecastID     
 AND sph.PlanTypeID = 3 --Check for RPPO     
    
 )     
 BEGIN    
 SET @IsRPPO=1    
 END    
 ELSE    
 BEGIN    
 SET @IsRPPO=0    
 END    
 select @IsRPPO as N'IsRPPO'    
    
    
END     

GO
