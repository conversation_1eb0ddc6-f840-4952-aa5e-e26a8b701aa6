SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-------------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: [fnGetBidYearMarketInputValues]  
--  
-- AUTHOR: <PERSON> Gilbert
--  
-- CREATED DATE: 2024-Jan-25  
-- HEADER UPDATED: 2024-Jan-25 
--  
-- DESCRIPTION: Get Bid Year values from the pricing model to use for comparisons.
--  
-- PARAMETERS:  
--  Input:  
--         
--  Output:  
--  @Results
-- TABLES:  
--  Read: 

--  Write:  
--  
-- VIEWS:  
--        
--  
-- FUNCTIONS:  
--  dbo.fnGetBidYear()
-- 
-- STORED PROCS:  
--  
-- HISTORY:  
-- ---------------------------------------------------------------------------------------------
-- DATE        VERSION      CHANGES MADE						DEVELOPER 
------------------------------------------------------------------------------------------------  
-- 2024-Nov-25  1			Initial Version                     Adam Gilbert
-- 2024-Jan-27  2			Removed unused PD Audit Fields      Adam Gilbert
-- 2025-Feb-06  3			Defect fix- Rx Benefit name change  Priyadarshini Deshmukh
-- 2025-Feb-20  4			Part A/B "0" format fix; OSB Fix	Adam Gilbert
-- 2025-Mar-3   5			Lead 0 removal						Adam Gilbert
-- 2025-Mar-12  6			A/B Fix for when OON exists but     Adam Gilbert
--							IN benefit does not.
-- 2025-Mar-24  7			OSBCode instead of Name			    Adam Gilbert
-- ---------------------------------------------------------------------------------------------
CREATE FUNCTION [PrePricing].[fnGetBidYearMarketInputValues]
    (
    )
RETURNS @Results TABLE
    (
	PlanInfoID INT,
	SubCategoryID INT,
	BidYearINValue VARCHAR(1000),
	BidYearOONValue VARCHAR(1000),
	INCostShareType INT,
	OONCostShareType INT
    ) 
    AS
	BEGIN
		--MSB has a bug with OTC
		WITH msb AS (
		SELECT
				spi.cps,
			 CASE WHEN LEFT(spab.AddedBenefitName,3) IN ('CNB','HSH','WDE','NBS') 
				  THEN  '-' + CAST(ROW_NUMBER() OVER(PARTITION BY spab.PlanYearID, spi.cps, LEFT(spab.AddedBenefitName,3) ORDER BY spab.PlanYearID, spi.cps) AS VARCHAR(10)) + ' ('+LEFT(spab.AddedBenefitName,3) +')'
			 ELSE  '(' + LEFT(spab.AddedBenefitName,3) + ')' END AS LookupValue,
				spab.AddedBenefitTypeID,
				spab.AddedBenefitName,
				LEFT(spab.AddedBenefitName,6) AS MSBCode
			FROM dbo.SavedPlanAddedBenefits spab  WITH(NOLOCK)
			INNER JOIN dbo.LkpExtCMSBidServiceCategory cat  WITH(NOLOCK)
				ON spab.BidServiceCatID = cat.BidServiceCategoryID
			JOIN SavedForecastSetup sfs  WITH(NOLOCK)
				ON spab.ForecastID = sfs.forecastid
			JOIN SavedPlanInfo spi WITH(NOLOCK)
				ON spi.planinfoid = sfs.planinfoid
			WHERE cat.ServiceCategory <> 'Related Parties'
				AND spab.IsHidden = 0
				)
			INSERT INTO @Results (PlanInfoID ,SubCategoryID ,BidYearINValue ,BidYearOONValue )
			SELECT ppi.planinfoid,subcategoryid,MSBCode AS INValue , '' AS OONValue
			FROM msb 			
			LEFT JOIN PrePricing.MarketInputSubCategory  WITH(NOLOCK)
			ON subcategoryname LIKE '%' + LookUpValue+ '%'
			JOIN PrePricing.PlanInfo ppi WITH(NOLOCK)
			ON msb.cps = ppi.cps 
			WHERE 1=1
			AND SubCategoryName IS NOT NULL ;--ADM and MUS excluded atm

		--Premiums
		WITH premiums AS (
			SELECT
			spi.cps,
			mrp.PartBPremiumBuyDown,
			mrp.MemberPremiumRounded,
			mrp.RxBasicPremium,
			mrp.RxSuppPremium,
			mrp.ProfitPercent,
			ppi.planinfoid
			FROM dbo.MAReportPlanLevel mrp WITH(NOLOCK)
			INNER JOIN dbo.SavedForecastSetup sfs WITH(NOLOCK)
			ON mrp.ForecastID = sfs.ForecastID
			INNER JOIN dbo.SavedPlanInfo spi WITH(NOLOCK)
			ON spi.PlanInfoID = sfs.planinfoid
			INNER JOIN prepricing.PlanInfo ppi WITH(NOLOCK)
			ON spi.cps = ppi.CPS AND mrp.PlanYearID = ppi.planyear
			WHERE  mrp.PlanYearID = dbo.fngetbidyear()
		)
		INSERT INTO @Results (PlanInfoID ,SubCategoryID ,BidYearINValue ,BidYearOONValue )
		SELECT Planinfoid,  (select subcategoryid from prepricing.marketinputsubcategory where subcategoryname = 'Part B Giveback') as subcategoryid,Cast(PartBPremiumBuydown as VARCHAR(100))  as INValue , '' as OONValue FROM premiums WHERE PartBPremiumBuydown IS NOT null
		UNION all
		SELECT Planinfoid,  (select subcategoryid from prepricing.marketinputsubcategory where subcategoryname = 'Member Premium') as subcategoryid,  CASE WHEN MemberPremiumRounded = 0 THEN '0' ELSE FORMAT(MemberPremiumRounded,'#.######') END as Invalue , '' as OONValue FROM premiums WHERE MemberPremiumRounded IS NOT NULL
        UNION all
		SELECT Planinfoid,  (select subcategoryid from prepricing.marketinputsubcategory where subcategoryname = 'Bid Profit %') as SubCatgeoryID, Cast(FORMAT(ProfitPercent,'P2') as VARCHAR(100)) as Invalue, ''asOONValue  FROM premiums WHERE ProfitPercent IS NOT null
		;

		--Plan Level
		WITH BYPlanLevel AS (
			SELECT 
			ppi.planinfoid
			, vpi.ContractPBPSeg AS CPS
			, DeductTypeDesc
			, CASE WHEN (sbo.IsPartBDeductible = 0) THEN sbo.INDeductible ELSE NULL END INDeductible
			, sbo.INMOOP
			, CASE WHEN (vpi.Product != 'HMO' AND sbo.IsPartBDeductible = 0) THEN sbo.OONDeductible ELSE NULL END OONDeductible
			, sbo.OONMOOP
			, CASE WHEN (sbo.IsPartBDeductible = 0 AND vpi.Product != 'HMO') THEN sbo.CombinedDeductible ELSE NULL END CombinedDeductible
			, sbo.CombinedMOOP
			, CASE WHEN (sbo.IsPartBDeductible = 1 AND vpi.Product = 'HMO') THEN sbo.INDeductible
			   WHEN (sbo.IsPartBDeductible = 1 AND vpi.Product != 'HMO') THEN sbo.CombinedDeductible
			   ELSE NULL END PartBDeductible
			FROM dbo.SavedPlanHeader sph   WITH(NOLOCK)
			LEFT JOIN dbo.Benefits_SavedBenefitOption sbo WITH(NOLOCK)
			ON sph.PlanInfoID = sbo.PlanInfoID
			AND sph.BenefitOptionID = sbo.BenefitOptionID
			INNER JOIN dbo.vwPlanInformation vpi WITH(NOLOCK)
			ON sph.ForecastID = vpi.ForecastID
			LEFT JOIN PrePricing.PlanInfo ppi WITH(NOLOCK)
			ON ppi.CPS = vpi.ContractPBPSeg
			WHERE 1=1
			AND sph.isHidden = 0 AND sph.IsFIledPlan = 1  AND IsLiveIndex = 1
			AND sph.planyearid=dbo.fngetbidyear() 
		),
		formatdata AS (
		SELECT PlanInfoID,CPS,  CAST(DeductTypeDesc AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory where subcategoryname = 'Deductible Type') SubcategoryID FROM byplanlevel
		WHERE DeductTypeDesc IS NOT NULL
		UNION ALL			
		SELECT PlanInfoID,CPS,  CAST(INDeductible AS VARCHAR(1000))   INVALUE,CAST(OONDeductible AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory where subcategoryname = 'Deductible') SubcategoryID FROM byplanlevel
		WHERE INDeductible IS NOT NULL OR OONDeductible IS NOT null
		UNION ALL		
		SELECT PlanInfoID,CPS,  CAST(INMOOP AS VARCHAR(1000))         INVALUE,CAST(OONMoop AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory where subcategoryname = 'MOOP') SubcategoryID FROM byplanlevel
		WHERE INMOOP IS NOT NULL OR OONMOOP IS NOT null
		UNION ALL				
		SELECT PlanInfoID,CPS,  CAST(CombinedDeductible AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory where subcategoryname = 'Combined Deductible') SubcategoryID FROM byplanlevel
		WHERE CombinedDeductible IS NOT NULL
		UNION ALL			
		SELECT PlanInfoID,CPS,  CAST(CombinedMoop AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory where subcategoryname = 'Combined MOOP') SubcategoryID FROM byplanlevel
		WHERE CombinedMoop IS NOT NULL
		UNION ALL			  
		SELECT PlanInfoID,CPS,  CAST(PartBDeductible AS VARCHAR(1000)) INVALUE,CAST(NULL AS VARCHAR(1000)) AS OONValue, (select SubcategoryID from Prepricing.MarketInputSubcategory where subcategoryname = 'Part B Deductible') SubcategoryID FROM byplanlevel
		WHERE PartBDeductible IS NOT NULL
		)
		INSERT INTO @Results (PlanInfoID ,SubCategoryID ,BidYearINValue ,BidYearOONValue )
		SELECT planinfoid,subcategoryid,ISNULL(invalue,'') INValue,ISNULL(OONValue,'') OONValue FROM formatdata;



							/*AB BENEFITS*/
		-- Add BenefitCategoryID to subcategory table?
		WITH ActuarialBenefits AS (
		SELECT  ppi.CPS, bc.BenefitCategoryName , sbod.isoon, CASE WHEN sbod.benefitvalue = 0 THEN '0' ELSE FORMAT(sbod.benefitvalue, '#.######') END AS benefitvalue, spi.planinfoid sourcePlanID, ppi.planinfoid,sbod.BenefitTypeID, 
		sbod.BenefitCategoryID, sbodrd.isoon drIsOON, CASE WHEN sbodrd.benefitvalue = 0 THEN '0' ELSE FORMAT(sbodrd.benefitvalue, '#.######') END AS drBenefitValue, BenefitOrdinalID ,dayrangebegin ,dayrangeend,
		[NameMapping] = bc.BenefitCategoryName +
			CASE WHEN sbod.BenefitCategoryID IN (83, 65, 66, 131) THEN ': Level ' + CAST(ISNULL(sbodrd.BenefitOrdinalID,1) AS VARCHAR) 
					ELSE ''
			END,
		dayRangeNameMapping =  parsename(replace(bc.BenefitCategoryName,'-','.'),2)
		 +
			CASE WHEN sbod.BenefitCategoryID IN (83, 65, 66, 131) AND sbod.BenefitTypeID = 5 THEN 'L'+CAST(ISNULL(sbodrd.BenefitOrdinalID,1) AS VARCHAR)+': Day Range -'  
					ELSE ''
			END
		FROM PrePricing.PlanInfo ppi WITH(NOLOCK)
		JOIN SavedPlanInfo spi  WITH(NOLOCK) ON ppi.CPS = spi.CPS AND spi.PlanYear = dbo.fngetbidyear()
		JOIN SavedForecastSetup sfs WITH(NOLOCK) ON spi.planinfoid =sfs.planinfoid 
		JOIN Benefits_SavedBenefitOption sbo WITH(NOLOCK) ON sfs.PlanInfoID = sbo.PlanInfoID AND sfs.BenefitOptionID = sbo.BenefitOptionID 
		JOIN Benefits_SavedBenefitOptionDetail sbod  WITH(NOLOCK) ON spi.PlanInfoID = sbod.planinfoid AND sbo.BenefitOptionID = sbod.benefitoptionid
		JOIN LkpIntBenefitCategory bc  WITH(NOLOCK) ON sbod.BenefitCategoryID = bc.BenefitCategoryID 
		LEFT JOIN Benefits_SavedBenefitOptionDayRangeDetail SBODRD  WITH(NOLOCK)
		ON sbod.BenefitCategoryID =sbodrd.BenefitCategoryID 
		AND sbod.PlanInfoID = sbodrd.PlanInfoID 
		AND sbod.BenefitOptionID = sbodrd.BenefitOptionID
		AND sbod.IsOON = sbodrd.isoon
		--WHERE ppi.PlanInfoid = 26 AND BenefitTypeID = 5
		) , formatdata AS (
		SELECT --*
		planinfoid,isoon , CAST(ISNULL(drbenefitvalue,benefitvalue) AS varchar(1000)) InputValue, SubCategoryID,NameMapping,SubCategoryName,BenefitTypeID
		FROM ActuarialBenefits  AB
		LEFT JOIN PrePricing.MarketInputSubCategory sc  WITH(NOLOCK) ON ab.NameMapping =sc.SubCategoryName
		UNION all
		SELECT 
		planinfoid,isoon , CAST(DayRangeBegin AS VARCHAR(1000)) as InputValue, SubCategoryID,dayRangeNameMapping + ' Begin', SubCategoryName,BenefitTypeID
		FROM ActuarialBenefits  AB
		LEFT JOIN PrePricing.MarketInputSubCategory sc  WITH(NOLOCK) ON ab.dayRangeNameMapping + ' Begin' =sc.SubCategoryName
		WHERE BenefitCategoryID IN (83, 65, 66, 131) AND BenefitTypeID = 5
		UNION all
		SELECT 
		planinfoid,isoon , CAST(DayRangeEnd AS VARCHAR(1000)) InputValue, SubCategoryID,dayRangeNameMapping + ' End', SubCategoryName,BenefitTypeID
		FROM ActuarialBenefits  AB
		LEFT JOIN PrePricing.MarketInputSubCategory sc WITH(NOLOCK) ON ab.dayRangeNameMapping + ' END' =sc.SubCategoryName
		WHERE BenefitCategoryID IN (83, 65, 66, 131) AND BenefitTypeID = 5
		),
		INN AS (SELECT planinfoid,subcategoryid,inputvalue INValue,BenefitTypeID AS INCostShareType FROM formatdata WHERE isoon = 0),
		OON AS (SELECT planinfoid,subcategoryid,inputvalue OONValue,BenefitTypeID AS OONCostShareType FROM formatdata WHERE isoon = 1)
		INSERT INTO @Results (PlanInfoID ,SubCategoryID ,BidYearINValue ,BidYearOONValue,INCostShareType,OONCostShareType )
		SELECT ISNULL(INN.PlanInfoID, OON.PlanInfoID),
			   ISNULL(INN.SubCategoryID, OON.SubCategoryID),
			   ISNULL(INN.INValue,'')  INValue,
			   ISNULL(OON.OONValue,'') OONValue,
			   INCostShareType,
			   OONCostShareType
		FROM INN 
		FULL OUTER JOIN OON
		ON INN.PlanInfoID = OON.planinfoid
		AND INN.SubCategoryID = oon.SubCategoryID ;


		--PD
WITH PDAE
AS (
SELECT 
ppi.PlanInfoID
,LEFT(ContractPBP, 5) + '-' + RIGHT(ContractPBP, 3) + '-' + spd.SegmentID AS CPS,
           dbo.fnGetBidYear() AS [YEAR],
           spd.PlanType AS DrugPlanType,
           Formulary,
           FLOOR(Deductible) AS Deductible,
           BenefitString1MonthStandardRetail,
           ZeroDollarRxVBID,
           COPDVBID,
           DoacVbid,
           ErectileDysfunction,
           AntiObesityDrugCoverage,
           PrescriptionVitamins,
		   DeductibleExcludeAnyTiers
    FROM dbo.SavedPDAuditExhibit spd WITH(NOLOCK)
        JOIN dbo.SavedPlanInfo spi WITH(NOLOCK)
            ON (spd.ContractPBP + spd.SegmentID) = REPLACE(spi.CPS, '-', '')
        JOIN dbo.LkpPlanType lpt WITH(NOLOCK)
            ON spi.PlanTypeID = lpt.PlanTypeID
        JOIN PrePricing.vwBidYearPlanMapping ppi WITH(NOLOCK)
            ON ppi.BYCPS = spi.CPS
               AND ppi.PlanYear = spi.PlanYear
    WHERE spi.PlanYear = dbo.fnGetBidYear()
          AND lpt.PlanType = 'MAPD'
		  ),formatdata AS (
SELECT 1 AS subselect, PlanInfoID, CAST(ZeroDollarRxVBID AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Zero Dollar DSNP') AS subcategoryid FROM PDAE
UNION ALL
SELECT 2 AS subselect, PlanInfoID, CAST(AntiObesityDrugCoverage AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Anti Obesity') AS subcategoryid FROM PDAE
UNION ALL
SELECT 3 AS subselect, PlanInfoID, CAST(COPDVBID AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'COPD VBID') AS subcategoryid FROM PDAE
UNION ALL
SELECT 4 AS subselect, PlanInfoID, CAST(DoacVbid AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'DOAC VBID') AS subcategoryid FROM PDAE
UNION ALL
SELECT 5 AS subselect, PlanInfoID, CAST(DrugPlanType AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Drug Plan TYPE') AS subcategoryid FROM PDAE
UNION ALL
SELECT 6 AS subselect, PlanInfoID, CAST(ErectileDysfunction AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Erectile Dysfuntion') AS subcategoryid FROM PDAE
UNION ALL
SELECT 7 AS subselect, PlanInfoID, CAST(Formulary AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Formulary') AS subcategoryid FROM PDAE
UNION ALL
SELECT 8 AS subselect, PlanInfoID, CAST(PrescriptionVitamins AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Prescription Vitamins') AS subcategoryid FROM PDAE
UNION ALL
SELECT 9 AS subselect, PlanInfoID, CAST(Deductible AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'RX Deductible') AS subcategoryid FROM PDAE
UNION ALL
SELECT 10 AS subselect, PlanInfoID, CAST(BenefitString1MonthStandardRetail  AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Standard Rx String (Retail)') AS subcategoryid FROM PDAE
UNION ALL
SELECT 11 AS subselect, PlanInfoID, CAST(DeductibleExcludeAnyTiers  AS VARCHAR(1000)) AS INvalue, '' AS OONValue , (SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Tiers excluded from deductible') AS subcategoryid FROM PDAE
)
INSERT INTO @Results (PlanInfoID ,SubCategoryID ,BidYearINValue ,BidYearOONValue )
SELECT PlanInfoId,SubcategoryID,Invalue,OONValue FROM formatdata;

--OSB
WITH OSB AS (
	SELECT ppi.planinfoid,misc.subcategoryid, LEFT(osbh.[description],6) AS BidYearInValue
	FROM SavedForecastSetup sfs WITH(NOLOCK)
	JOIN SavedPlanInfo spi WITH(NOLOCK) ON sfs.PlanInfoID = spi.planinfoid
	JOIN SavedPlanOptionalPackageDetail spopd  WITH(NOLOCK)ON sfs.ForecastID = spopd.forecastid
	JOIN PerIntOptionalPackageHeader osbH  WITH(NOLOCK)ON spopd.PackageIndex = osbH.packageindex
	JOIN PrePricing.PlanInfo ppi WITH(NOLOCK) ON ppi.CPS = spi.cps
	LEFT JOIN PrePricing.MarketInputSubCategory misc  WITH(NOLOCK)ON osbH.Description = misc.SubCategoryName
	WHERE sfs.ishidden=0 AND sfs.isliveindex=1 AND sfs.IsFiledPlan=1 AND sfs.PlanYear = dbo.fngetbidyear()
)
INSERT INTO @Results (PlanInfoID ,SubCategoryID ,BidYearINValue ,BidYearOONValue )
SELECT planinfoid,subcategoryid,BidYearInValue,'' AS BidYearOONValue FROM OSB;



--SCT Metrics
WITH BidYearSCT AS (
	SELECT ppi.planinfoid, sct.* 
	FROM prepricing.planinfo ppi  WITH(NOLOCK)
	JOIN prepricing.PricingModelSCTValues sct  WITH(NOLOCK)
	ON ppi.cps = sct.contractpbpsegment
	AND ppi.PlanYear = sct.planyear
	WHERE ppi.PlanYear = dbo.fngetbidyear()
), 
formatted AS (
	SELECT PlanInfoID, CAST(CAST(ROUND(AvgMembers,0) AS INT)  AS VARCHAR(1000)) AS BidYearINValue, '' AS BidYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory  WITH(NOLOCK) WHERE SubCategoryName = 'Average Members') AS subcategoryid FROM BidYearSCT
	UNION ALL
	SELECT PlanInfoID, CAST(FORMAT(MER,'P2')  AS VARCHAR(1000)) AS BidYearINValue, '' AS BidYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'MER') AS subcategoryid FROM BidYearSCT
	UNION ALL
	SELECT PlanInfoID, CAST(CAST(ROUND(NetGrowth,0) AS INT)  AS VARCHAR(1000)) AS BidYearINValue, '' AS BidYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'Net Growth') AS subcategoryid FROM BidYearSCT
	UNION ALL
	SELECT PlanInfoID, CAST(CAST(ROUND(UM,0)AS INT)  AS VARCHAR(1000)) AS BidYearINValue, '' AS BidYearOONValue ,
	(SELECT subcategoryid FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryName = 'UM (in $000s)') AS subcategoryid FROM BidYearSCT
)
INSERT INTO @Results (PlanInfoID ,SubCategoryID ,BidYearINValue ,BidYearOONValue )
SELECT planinfoid,subcategoryid,BidYearInValue,'' AS BidYearOONValue FROM formatted;





--Select * from @Results where planinfoid is null --Problem records to be investigated.
DELETE FROM @Results WHERE PlanInfoID IS NULL OR SubCategoryID IS NULL -- cleanup excess

		RETURN
	END
GO
