SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spDeleteCrosswalkMapping]   
--    
-- AUTHOR: Sur<PERSON>rthy 
--    
-- CREATED DATE: 2024-Nov-10   
-- Type: 
-- DESCRIPTION: Procedure responsible for deleting cross walk mapping 
--    
-- PARAMETERS:    
-- Input: 
-- @CrosswalkID
-- @OutPutResult
-- @CrosswalkTypeID

-- TABLES:   
--
-- Read:    
--  
-- Write:    
--  PrePricing.PlanCrossWalkMapping

-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Nov-11		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- 2025-Mar-28		3		New plan full delete		    Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spDeleteCrosswalkMapping]
(
	@CrosswalkID INT,
	@CrosswalkTypeID INT,
	@CrosswalkNotes VARCHAR(200),
	@LastUpdateByID VARCHAR(200)
)
AS    
BEGIN    
	SET NOCOUNT ON
	DECLARE	@OutPutResultCode VARCHAR(20)
    DECLARE @OutPutResult VARCHAR(200)
	BEGIN TRY
	BEGIN TRANSACTION crosswalkdelete	
	    SET @OutPutResultCode='Success'
		SET @OutPutResult='Crosswalk Mapping Deleted Successfully.'	 
		DECLARE @PlanInfoID INT;

		IF @CrosswalkTypeID = 5
		BEGIN
			--Plan flip logic
			DECLARE @originalName VARCHAR(13);
			SELECT @originalName = PreviousCPS, @PlanInfoID = PlanInfoID FROM PrePricing.PlanFlip  WHERE PlanFlipID=@CrosswalkID;			
			UPDATE PrePricing.PlanInfo SET CPS=@originalName,LastUpdateDateTime = GETDATE() ,LastUpdateByID=@LastUpdateByID WHERE PlanInfoID=@PlanInfoID;
			DELETE FROM PrePricing.PlanFlip WHERE PlanFlipID=@CrosswalkID;	
			DELETE FROM PrePricing.PlanCrossWalkDetail WHERE CrossWalkID = @crosswalkid
			DELETE FROM PrePricing.PlanCrossWalkHeader WHERE CrossWalkID = @crosswalkid
		END
		IF @CrosswalkTypeID = 1
		BEGIN
			--New Plan Logic
			SELECT TOP 1 @PlanInfoID = targetplaninfoid FROM PrePricing.PlanCrossWalkDetail WHERE CrossWalkID = @CrosswalkID

			--If the New plan has been used in another crosswalk, term
			IF EXISTS (SELECT 1 FROM PrePricing.PlanCrossWalkDetail WHERE ContributingPlanInfoID = @planinfoid)
				BEGIN
				UPDATE PrePricing.PlanInfo SET IsEnabled=0 ,LastUpdateDateTime = GETDATE() ,LastUpdateByID=@LastUpdateByID WHERE PlanInfoID=@PlanInfoID;
				END
			--Otherwise, delete the new plan and all associated data
			ELSE 
				BEGIN
					DELETE FROM PrePricing.PlanNote WHERE planinfoid IN (@PlanInfoID)
					DELETE FROM PrePricing.PlanGroupMapping WHERE planinfoid IN (@PlanInfoID)
					DELETE FROM prepricing.MarketInputValue WHERE planinfoid IN (@PlanInfoID)
					DELETE FROM prepricing.ServiceAreaMapping WHERE PlanInfoID IN (@PlanInfoID)
					DELETE FROM prepricing.PlanCrossWalkDetail WHERE TargetPlanInfoID IN (@PlanInfoID)
					DELETE FROM PrePricing.PlanCrossWalkHeader WHERE CrossWalkID IN (@CrosswalkID )
					DELETE FROM prepricing.PricingModelValueCurrentYear WHERE planinfoid IN (@PlanInfoID)
					DELETE FROM prepricing.PricingModelValueBidYear WHERE planinfoid IN (@PlanInfoID)
					DELETE FROM Prepricing.Planinfo WHERE planinfoid IN (@PlanInfoID)
				END


		END
		IF @CrosswalkTypeID = 2
		BEGIN
			--Consolidation Logic
			UPDATE PrePricing.PlanInfo SET IsEnabled=1,LastUpdateDateTime = GETDATE() ,LastUpdateByID=@LastUpdateByID WHERE PlanInfoID IN 
			(SELECT a.ContributingPlanInfoID FROM [PrePricing].[PlanCrossWalkDetail] a WITH(NOLOCK)
			JOIN [PrePricing].[PlanCrossWalkHeader] b WITH(NOLOCK) ON b.CrossWalkID=a.CrossWalkID AND a.CrossWalkID=@CrosswalkID);			
		END
		IF @CrosswalkTypeID = 3
		BEGIN
			--Segmentaion Logic
			UPDATE PrePricing.PlanInfo SET IsEnabled=0,LastUpdateDateTime = GETDATE() ,LastUpdateByID=@LastUpdateByID WHERE PlanInfoID IN 
			(SELECT a.TargetPlanInfoID FROM [PrePricing].[PlanCrossWalkDetail] a WITH(NOLOCK)
			JOIN [PrePricing].[PlanCrossWalkHeader] b WITH(NOLOCK) ON b.CrossWalkID=a.CrossWalkID AND a.CrossWalkID=@CrosswalkID);	
			UPDATE PrePricing.PlanInfo SET IsEnabled=1,LastUpdateDateTime = GETDATE() ,LastUpdateByID=@LastUpdateByID WHERE PlanInfoID IN 
			(SELECT a.ContributingPlanInfoID FROM [PrePricing].[PlanCrossWalkDetail] a WITH(NOLOCK)
			JOIN [PrePricing].[PlanCrossWalkHeader] b WITH(NOLOCK) ON b.CrossWalkID=a.CrossWalkID AND a.CrossWalkID=@CrosswalkID);
		END

		IF @CrosswalkTypeID = 4
		BEGIN
			--Term Plan Logic
			SELECT TOP 1 @PlanInfoID = targetplaninfoid FROM PrePricing.PlanCrossWalkDetail WHERE CrossWalkID = @CrosswalkID
			UPDATE PrePricing.PlanInfo SET IsEnabled=1,LastUpdateDateTime = GETDATE() ,LastUpdateByID=@LastUpdateByID WHERE PlanInfoID=@PlanInfoID;	
			DELETE FROM PrePricing.PlanCrossWalkDetail WHERE CrossWalkID = @crosswalkid
			DELETE FROM PrePricing.PlanCrossWalkHeader WHERE CrossWalkID = @crosswalkid
		END

		IF @CrosswalkTypeID <> 5 --ignore pln flip
		BEGIN
			UPDATE PrePricing.PlanCrossWalkHeader SET 
			IsDeleted=1,
			CrosswalkNotes=@CrosswalkNotes,
			LastUpdateByID=@LastUpdateByID,
			LastUpdateDateTime = GETDATE()
			WHERE CrosswalkID=@CrosswalkID;
		END
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
		COMMIT TRANSACTION crosswalkdelete;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION crosswalkdelete;
		SET @OutPutResultCode='Error'
		SET @OutPutResult='Error while deleting Crosswalk Mapping.';
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH
END
GO
