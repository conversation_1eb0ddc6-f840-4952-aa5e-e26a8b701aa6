SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
  
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spGetBPTExceptionHeader
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE: 2008-May-05
-- HEADER UPDATED: 2010-Oct-12
--
-- DESCRIPTION: Returns the BPT exception header data for the specified
--              plan, if any exists.
--
-- PARAMETERS:
--	Input:
--    @ForecastID
--	Output:
--
-- TABLES:
--	Read:
--    SavedPlanBPTExceptionHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
-- 
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                             	        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-May-05      1           Initial version.                                                    Tonya Cockrell
-- 2010-Mar-12		2	        Added IsOverride for overriden credibility						    Jake Gaecke
-- 2010-Oct-12      3           Removed @PlanYearID                                                 Joe Casey
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROC [dbo].[spGetBPTExceptionHeader]
    @ForecastID INT
AS
    SELECT
        IsBasePlan,
		IsOverride,
        Credibility
    FROM SavedPlanBPTExceptionHeader
    WHERE ForecastID = @ForecastID
        AND IsHidden = 0
GO
