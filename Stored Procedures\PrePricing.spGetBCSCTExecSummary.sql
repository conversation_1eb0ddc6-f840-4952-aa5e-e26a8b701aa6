SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
CREATE   PROC [PrePricing].[spGetBCSCTExecSummary] AS 

SELECT
PlanYearContractPBPSegment AS [ContractPBPSegment],
PlanYearID AS [Year],
AverageMembers AS [AvgMembers],
NetGrowth,
MER = TotalClaimsPMPM / TotalRevenuePMPM,
UM = (TotalRevenue - TotalClaims) / 1000
From
	(SELECT 
	PlanYearID,
	PlanYearContractPBPSegment,
	AverageMembers = TotalMemberMonths/12,
	NetGrowth = TotalGrowth,
	TotalRevenuePMPM = (MARevenue * MAMemberMonths 
						+ PDRevenue * PartDMemberMonths
						+ ESRDRevenue * TotalESRDMemberMonths  
						+ HospRevenue * TotalHospMemberMonths)/TotalMemberMonths,
	TotalRevenue = (MARevenue * MAMemberMonths 
					+ PDRevenue * PartDMemberMonths
					+ ESRDRevenue * TotalESRDMemberMonths  
					+ HospRevenue * TotalHospMemberMonths),
	TotalClaimsPMPM = (MAClaims * MAMemberMonths 
						+ PDClaims * PartDMemberMonths
						+ ESRDClaims * TotalESRDMemberMonths  
						+ HospClaims * TotalHospMemberMonths)/TotalMemberMonths,
	TotalClaims = (MAClaims * MAMemberMonths 
					+ PDClaims * PartDMemberMonths
					+ ESRDClaims * TotalESRDMemberMonths  
					+ HospClaims * TotalHospMemberMonths)
	FROM	(SELECT 
		maf.PlanYearContractPBPSegment, 
		maf.PlanYearID, 
		maf.Iteration,
		Sum(maf.AdjTotalMM / maf.PlanCount) as TotalMemberMonths,
		Sum(maf.Total_Growth / maf.PlanCount) as TotalGrowth,
		IsNull(Sum(ptd.AdjTotalMM / maf.PlanCount),0) as PartDMemberMonths,
		SUm(maf.NonESRDHospiceMemberMonths / maf.PlanCount + maf.OOAMemberMonths / maf.PlanCount) as MAMemberMonths,
		Sum(maf.ESRDMemberMonths / maf.PlanCount) as TotalESRDMemberMonths,
		Sum(maf.HospiceMemberMonths / maf.PlanCount) as TotalHospMemberMonths,
		Sum(maf.RiskAdjABRevenue * maf.NonESRDHospiceOOAMM 
			+ maf.BasicMemberPremium * maf.NonESRDHospiceOOAMM
			+ maf.RedCSRebate * maf.NonESRDHospiceOOAMM
			+ maf.RedCSPrem * maf.NonESRDHospiceOOAMM
			+ maf.RedMandRebate * maf.NonESRDHospiceOOAMM 
			+ maf.RedMandPrem * maf.NonESRDHospiceOOAMM
			+ maf.UncollectedMbrPrem * maf.NonESRDHospiceOOAMM 
			+ maf.SeqRevenue * maf.NonESRDHospiceOOAMM 
			+ maf.MSP * maf.NonESRDHospiceOOAMM 
			+ maf.OtherRevTotalAdj * maf.NonESRDHospiceOOAMM)
		/Sum(maf.NonESRDHospiceMemberMonths + maf.OOAMemberMonths) as MARevenue,
		IsNull(Sum(ptd.WeightedDirectSubsidy * ptd.AdjTotalMM
					+ ptd.WeightedBasicPrem * ptd.AdjTotalMM
					+ ptd.WeightedSuppPrem * ptd.AdjTotalMM
					+ ptd.WeightedSequestrationDirectSubsidy * ptd.AdjTotalMM
					+ ptd.WeightedSequestrationMARebates * ptd.AdjTotalMM
					+ ptd.WeightedRevenuePlatinoWrap * ptd.AdjTotalMM
					+ ptd.WeightedRiskShare * ptd.AdjTotalMM 
					+ ptd.WeightedOtherRevenueAdj * ptd.AdjTotalMM)/sum(ptd.AdjTotalMM),0) as PDRevenue,
		Case When sum(maf.ESRDMemberMonths) = 0 Then 0 
		Else Sum(maf.ESRDPremium * maf.ESRDMemberMonths)/sum(maf.ESRDMemberMonths)
		End as ESRDRevenue,
		Case When sum(maf.HospiceMemberMonths) = 0 Then 0 
		Else Sum(maf.HospPremium * maf.HospiceMemberMonths)/sum(maf.HospiceMemberMonths)
		End as HospRevenue,
		Round(Sum(maf.BasicMemberPremium * maf.NonESRDHospiceOOAMM
					+ maf.RedCSPrem * maf.NonESRDHospiceOOAMM
					+ maf.RedMandPrem * maf.NonESRDHospiceOOAMM)/Sum(maf.NonESRDHospiceMemberMonths + maf.OOAMemberMonths),1)*Sum(maf.AdjTotalMM)/sum(maf.AdjTotalMM)
					+ isnull(Round(Sum(ptd.WeightedBasicPrem * ptd.AdjTotalMM - ptd.WeightedBasicRebate * ptd.AdjTotalMM)/sum(ptd.AdjTotalMM),1),0)
					+ isnull(Round(Sum(ptd.WeightedSuppPrem * ptd.AdjTotalMM - ptd.WeightedSuppRebate * ptd.AdjTotalMM)/sum(ptd.AdjTotalMM),1)*Sum(ptd.AdjTotalMM),0)/sum(maf.AdjTotalMM) as MAPDPremium,
		Sum(maf.TotalMANetCovered * maf.NonESRDHospiceOOAMM
			+ maf.OtherClaimsAdjs * maf.NonESRDHospiceOOAMM
			+ maf.FCFAdj * maf.NonESRDHospiceOOAMM
			+ maf.CostShareAdj * maf.NonESRDHospiceOOAMM
			+ maf.RiskProviderOffsets * maf.NonESRDHospiceOOAMM
			+ maf.MSBFinance * maf.NonESRDHospiceOOAMM
			+ maf.MSBAdj * maf.NonESRDHospiceOOAMM
			+ maf.HumanaAtHomeExpPlusSNP * maf.NonESRDHospiceOOAMM
			+ maf.HumanaatHomeAdj * maf.NonESRDHospiceOOAMM
			+ maf.CoreQuality * maf.NonESRDHospiceOOAMM
			+ maf.QualityNonHAHAdj * maf.NonESRDHospiceOOAMM
			+ maf.SelectableQuality * maf.NonESRDHospiceOOAMM
			+ maf.RxRebates * maf.NonESRDHospiceOOAMM)/Sum(maf.NonESRDHospiceMemberMonths + maf.OOAMemberMonths) as MAClaims,
		isnull(Sum(ptd.WeightedBasicPaid * ptd.AdjTotalMM
				- ptd.WeightedReinsurance * ptd.AdjTotalMM
				- ptd.WeightedRebates * ptd.AdjTotalMM
				- ptd.StretchRebate * ptd.AdjTotalMM
				- ptd.WeightedOtherDIR * ptd.AdjTotalMM
				- ptd.HPGERDIR * ptd.AdjTotalMM
				- ptd.RiskDealDIR * ptd.AdjTotalMM
				+ ptd.WeightedSuppPaid * ptd.AdjTotalMM
				+ ptd.WeightedNonTrOOP * ptd.AdjTotalMM
				+ ptd.PDMSP * ptd.AdjTotalMM
				+ ptd.WeightedFinancialQuality * ptd.AdjTotalMM
				+ ptd.WeightedLiabilityPlatinoWrap * ptd.AdjTotalMM
				+ ptd.WeightedRiskDealAdj * ptd.AdjTotalMM
				+ ptd.PDAdjustment1 * ptd.AdjTotalMM
				+ ptd.PDAdjustment2 * ptd.AdjTotalMM
				+ ptd.PDAdjustment3 * ptd.AdjTotalMM
				+ ptd.PDAdjustment4 * ptd.AdjTotalMM
				+ ptd.PDAdjustment5 * ptd.AdjTotalMM)/sum(ptd.AdjTotalMM),0) as PDClaims,
		Case When sum(maf.ESRDMemberMonths) = 0 Then 0 
			  ELSE Sum(maf.ESRDLiab * maf.ESRDMemberMonths)/sum(maf.ESRDMemberMonths)
		End as ESRDClaims,
		Case When sum(maf.HospiceMemberMonths) = 0 Then 0 
			 ELSE Sum(maf.HospLiab * maf.HospiceMemberMonths)/sum(maf.HospiceMemberMonths)
		End as HospClaims
		FROM ( SELECT *,
					COUNT(*) OVER(PARTITION BY IncYrCPSIteration) AS PlanCount
					FROM (SELECT DISTINCT * FROM MAAModels.dbo.SCT_MAForecast WITH(NOLOCK)) a
			  ) maf
		LEFT JOIN (SELECT DISTINCT * FROM MAAModels.dbo.SCT_vwPartD) ptd 
			ON ptd.ContractPBPSegment = maf.ContractPBPSegment
			And ptd.PlanYearID = maf.PlanYearID
			And ptd.Iteration = maf.Iteration
			And ptd.planyearcontractpbpsegment = maf.planyearcontractpbpsegment
		Where maf.Iteration IN (SELECT DISTINCT Iteration FROM MAAModels.dbo.SCT_CurrentIterations WHERE Archive = 'N')
			AND maf.PlanYearID IN (dbo.fngetbidyear() - 1, dbo.fngetbidyear()) 
		--AND maf.Region = '@Region'
		Group by maf.planyearcontractpbpsegment, maf.PlanYearID, maf.Iteration
	
		)  AS DataPrep
		)AS DataPrep2
		WHERE planyearid = 2025 
GO
