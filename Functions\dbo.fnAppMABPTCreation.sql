-- ---------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnAppMABPTCreation  
--  
-- AUTHOR: Joe Casey  
--  
-- CREATED DATE: 2011-May-19  
-- HEADER UPDATED: 2011-Jun-01  
--  
-- DESCRIPTION:  
--  
-- PARAMETERS:  
-- Input:  
--      @ForecastID  
--  Output:  
--  
-- TABLES:   
-- Read:  
--      LkpIntMABPTMapping  
--      LkpExtCMSBidServiceCategory  
--      SavedForecastSetup  
--      SavedPlanInfo  
--      SavedPlanProjectedMedicaidRevenue  
--		Benefits_SavedBenefitOption
--		CalcTotalCostShare
--  
--  Write:  
--  
-- VIEWS:  
--  
-- FUNCTIONS:  
--      fnAppGetBenchmarkSummary  
--      fnAppGetGeneralMABPTValues  
--      fnAppGetMABPTWS1BasePlans  
--      fnAppGetMABPTWS1Summary  
--      fnAppGetMABPTWS2  
--      fnAppGetMABPTWS3  
--      fnAppGetMABPTWS4Percent  
--      fnAppGetMABPTWS5  
--      fnAppGetMABPTWS7  
--      fnAppGetMABPTWS7Sec2  
--      fnPlanCountyProjectedMemberMonths 
--  
-- STORED PROCS:  
--  
-- $HISTORY   
-- ---------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER  
-- ---------------------------------------------------------------------------------------------------------------------  
-- 2011-May-19      1           Initial Version                                                     Joe Casey  
-- 2011-Jun-01      2           Changes to remove cursors                                           Michael Siekerka  
-- 2011-Aug-25      3           Changed to order WS1, section 2 by member months DESC               Rachael Potash  
-- 2011-Aug-30      4           Inserted IF statements to not populate WS1 if plan is               Trevor Mahoney  
--                                fully manual  
-- 2011-Sep-06      5           Changed dated prepared to show only date, not time, and             Rachael Potash  
--                                put in proper format to eliminate red circle errors  
-- 2011-Sep-16      6           In the first grouping of section 3 in wks 3, changed the Join       Craig Wright  
--                                to pull the MIN(MeasUnitCode) instead of the MAX.  This   
--                                corrects the issue of out of order benefits on the BPT    
-- 2011-Oct-07      7           Changed issues using a pbp less than or greater than 800 for        Rachael Potash  
--                                identifying group plans, to use GroupPlan to identify instead   
-- 2011-Dec-05      8           Inserted RowNumber in WS7 code to account for OSB combo packages    Trevor Mahoney  
-- 2011-Dec-09      9           Updated coding for Part B deductible and IN Deductible              Craig Wright        
-- 2011-Dec-13      10          Updated coding for OON Part B Deductible (always no)                Craig Wright   
-- 2011-Dec-15      11          Updated Group Profit formula to account for MSBs > 0 on WKS 4       Tim Gao  
-- 2011-Dec-30      12          Added code to deal with Part B Only plans                           Tim Gao  
-- 2012-Jan-04      13          Added code to get new MOOP impact for part B only plans             Tim Gao  
-- 2012-Jan-24      14          Corrected calculation for ManualUtil on WS2                         Trevor Mahoney  
-- 2012-Jan-24      15          Added fnGetSafeDivisionResultReturnOne for NewMoopImpact on WKS 3   Craig Wright  
-- 2012-Feb-07      16          Corrected the H and M columns display for group plans on WKS 3      Tim Gao  
-- 2012-Feb-09      17          Removed row 43 for part b only plans for WKS 3 Col H and M          Tim Gao  
-- 2012-Feb-24      18          Made changes to account for new admin buckets Quality and           Alex Rezmerski  
--                              TaxesAndFees  
-- 2012-Feb-28      19          Added new PD admin buckets                                          Alex Rezmerski  
-- 2012-Mar-01      20          Removed the MMs for base plan periods (WS1 S2) inputs               Mason Roberts  
-- 2012-Mar-01      21          Updated the DECLARE list for the table fnAppGetGeneralMAPBTValues   Alex Rezmerski  
-- 2012-Mar-01      22          Removed the ESRD subsidy MMs (WS4 S3) inputs                        Mason Roberts  
-- 2012-Mar-01      23          Added new QualityInitDescrip for WS3                                Tim Gao  
-- 2012-Mar-15      24          Change BidNotes1, BidNotes2, BidNotes3, BidNotes4, BidNotes5, &     Mason Roberts  
--                                QualityInitDescrip to VARCHAR(MAX)  
-- 2012-Mar-19      25          Added GainLossMarginLevel for WS4                                   Trevor Mahoney  
-- 2012-Mar-22      26          Added ESRD, Hospice, and OOA Membership to WS5                      Trevor Mahoney  
-- 2012-Mar-26      27          Added Utilizers to WS1 Sec. III                                     Alex Rezmerski  
-- 2012-Apr-11      28          Adjusted Direct Admin to include Quality and Taxes                  Mike Deren  
-- 2012-Apr-12      29          Added ProfitAdjustment to WS4 profit for eliminating unspentrebate  Tim Gao  
-- 2012-Apr-18      30          Added hard insert for combined deductible on WS3 and changed        Alex Rezmerski  
--                                Is Ded part B only to accomodate the hard insert for OON Ded.  
-- 2012-May-01      31          Added hard insert to WS6 to account for Cell H41 being merged       Mike Deren  
-- 2012-May-03      32          Adjusted Direct Admin on WS6, as well as spacing                    Mike Deren   
-- 2012-May-07      33          Added "No" to WS5, G31 if the plan is an RPPO                       Mike Deren  
-- 2012-May-08      34          Commented out the Group ProfitAdjustment piece...                   Craig Wright  
-- 2012-May-11      35          Changed IN and ONNCostShareDesc to return 'Medicare FFS Cost        Mason Roberts  
--                                Sharing' for rows 25-27 on WS3  
-- 2012-May-14      36          In WS 4 Sec 2b changed PercentCoveredAllowed to                     Alex Rezmerski  
--                                PercentCoveredCostShare in case for PercentCoveredCostShare  
-- 2012-May-16      37          Delete the 0.05 * BaseNetPMPM for PartBOnlyPlans                    Tim Gao  
-- 2012-May-22      38          WS 2 Sec. 2 Added Case for Part B only SNF plans (Row 21)           Alex Rezmerski  
-- 2012-May 23      39          In WS 4 Sec 2b changed PercentCoveredCostShare to                   Craig Wright  
--                                PercentCoveredAllowed in case for PercentCoveredCostShare (undid  
--                                Alex's original change)  
-- 2012-Jul-20      40          Updated WS 3 to populate OON Ded as Combined Ded for LPPO's only    Alex Rezmerski  
-- 2012-Jul-27      41          Updated WS 3 to populate OON Ded as Combined Ded For RPPO's         Alex Rezmerski  
-- 2012-Aug-15      42          Removed the code for handling EGWPMSB in WS4 and WS6                Tim Gao  
-- 2012-Aug-16      43          Deleted "AND UnspentRebate>0" from WS4 EGWPMSB population           Tim Gao  
-- 2013-Mar-01      44          0379 Added InsurerFee to WS4 information                            Mason Roberts  
-- 2013-Mar-15      45          Added InsurerFee to DirectAdmin and TaxesAndFees                    Mason Roberts  
-- 2013-Apr-10      46          Added OSB Base Period Experience data to WS7 per CMS                Lindsay Allen   
-- 2013-Apr-16      47          Added InsurerFeeAdmin, medicaid revenuve, expenses                  Tim Gao  
-- 2013-Apr-18      48          Comment out "QualityInitDescrip" in WS4                             Tim Gao  
-- 2013-May-01      49          Added in is not null in WS3 "NOT" IN clause                         Mike & Lindsay  
-- 2013-Oct-10      50           Modified to Include SegmentId                                      Anubhav Mishra  
-- 2014-Mar-18      51          Commented out 'Quality Initiatives' and 'Taxes and Fees'            Siliang Hu  
--                                for WS4,'QualityInitiativesAdminExpense' and  
--                                'TaxesAndFeesAdminExpense' on WS1  
-- 2014-Mar-18      52          2015 Regulatory WS5 Changes                                         Lindsay Allen  
-- 2014-Mar-19      53          Changed the format and information of WS7                           Mason Roberts  
-- 2014-Mar-20      54          Commented out WS3 H, K, and M12 and wrote to Regulatory for         Mason Roberts  
--                                guidance  
-- 2014-Mar-20      55          Changed WS 5 to being populating at ID + 37 per OOA Risk Score      Lindsay Allen  
-- 2014-Apr-23      56          Added in a hard insert for WS4 R123, R125, and R126                 Mason Roberts  
-- 2015-Mar-06      57          Commented out date prepared, which is no longer a BPT input         Mark Freel  
--                                Changed Low Enrollment contracts to have a blank star rating  
-- 2015-Mar-09      58          Commented out two lines that populate extra service cats. on WS 3   Mark Freel  
-- 2015-Mar-31      59          Commented out 'GainLossMarginLevel' in code populating WS4 so that  Chris McAuley  
--                                it wouldn't automatically be populated (shell does this)  
-- 2015-May-11      60          changed OSBNetMedicalExpenses, OSBNonBenefitExpenses, OSBPremium,   Sharath Chandra  
--                                data type to decimal(16,6) while declaring @fnAppGetGeneralMABPTValues  
-- 2015-Oct-14      61          WS5 Risk Score only rounded to 6 decimal places                     Mark Freel  
-- 2015-Nov-24      62          Added MLA Indicator to use in Deductible statements                 Jordan Purdue  
-- 2015-Nov-24      63          Updated IN Deductible and OON Deductible statements to follow BPT   Jordan Purdue  
--                                Instructions  
-- 2015-Nov-24      64          Updated cells H67, K67, and N67 on Worksheet 3 for MLA Logic        Jordan Purdue  
-- 2015-Nov-24      65          Updated the Combined Deductible to follow BPT Instructions          Jordan Purdue  
-- 2016-Jan-28      66          Updated H66, K66, and N66 to be blank when zero. H67, K67, and N67  Jordan Purdue  
--                                were also updated to calculate correctly with the new logic.  
-- 2016-Feb-12      67          Updated county code sort logic on WS5 section 6                     Eric Deterville  
-- 2016-Mar-08      68          Added VBID cell, commented WS3 PartBOnly Deductible Cells,          Chris McAuley  
--                                uncommented WS3 extra rows  
-- 2016-Mar-09      69          Added product pairing cells for WS4 Section 2c lines z4-z5          Chris McAuley   
-- 2018-Mar-13      70          Added Medicaid WS4 Section 6 population                             Alex Beruscha  
-- 2018-Mar-15      71          Ensure Dual-Eligible Puerto Rico appears as Dual-Eligible SNPName   Alex Beruscha  
-- 2018-Mar-19      72          Define INCostShareDesc to be VARCHAR(MAX) in @fnAppGetMABPTWS3      Chris Fleming  
-- 2018-Apr-04      73          Modified Join on fnAppGetMABPTWS4Percent to SUM on service cat      Chris Fleming  
-- 2018-Apr-25      74          Updated PaidThroughDate to VARCHAR(10) for proper BPT creation      Chris Fleming  
-- 2018-May-11      75          Updated Section 2a for WS4 to be Dual Elgible Type 0                Jordan Purdue  
-- 2018-Jun-20      76          Updated logic for 10 Product Pairings                               Apoorva Nasa  
-- 2018-Sep-04      77          Removing Admin Name from function because of Model Blending         Jordan Purdue  
-- 2019-Feb-04      78          PackageIndex and PlanPackageID data type changes                    Keith Galloway  
-- 2019-Sep-06      79          Replaced SavedPlanHeader with SavedForecastSetup                    Kritika  
-- 2019-Nov-05      80          Replace savedforecastsetup to savedplanheader                       Chhavi Sinha  
-- 2020-Jan-23      81          Updated section 2 of WS3 logic for the Combined Deductible          Abraham Ndabian  
-- 2020-Jan-28      82          Include ESRD values in section 6 of WS4                             Keith Galloway  
-- 2020-Feb-26      83          Remove Utilizers from WS1 Population                                Abraham Ndabian  
-- 2020-Feb-26      84          Update for VBID to two fields, VBID-C and VBID-H                    Alex Beruscha  
-- 2020-Feb-27      85          Removed Insurer Fee population on BPT WS 4                          Alex Beruscha  
-- 2020-Sep-01      86          Changed AltRebateOrder datatype to TinyInt                          Brent Osantowski  
-- 2020-Sep-28      87          Backend Alignment and Restructuring                                 Keith Galloway  
-- 2020-Dec-14      88          Increasing scale to align BPT risk scores with MRA                  Brent Osantowski  
-- 2021-Feb-10      89          Increase scale for WS1 risk scores                                  Brent Osantowski  
-- 2021-Mar-12      90         Medical and Non Medical Related Party values in cells z4 and z5( WS4) Abraham Ndabian  
--                              Commented out Product Pairing codes based off CMS BPT changes 
-- 2021-SEP-23      91         Need to deploy this function in all other environment                 Zeeshan  
-- 2021-Sep-27		92		   Added CorpMargReq field. Value will be placed in WS4 cell M100        Phillip Leigh
-- 2022-Jan-10		93		   Removed CorpMargReq field per CMS rule change						 Phillip Leigh	
 --2022-Jan-18		94		   Removed InsurerFeeAdminExpense per CMS rule Change					 Phillip Leigh
 --2022-Feb-07      95         Fixing Part B Deductible issue in WS3, section 3 of BPT               Abraham Ndabian 
--2022-May-30		96			MAAUI migration; removed SavedIsCombinedDeductiblePlan; changed
 --								logic for @IsCombinedDeductible; removed 
 --								SavedPlanDeductibleMOOPDetail; changed logic for @PartB; removed
 --								nested queries														 Aleksandar Dimitrijevic
-- 2022-Sep-15		97			Combined deductable changes  										 Aleksandar Dimitrijevic	
--2023-May-5		98			@fnAppGetMABPTWS3 size increase for OONCostShareDesc to 1000  		 Adam Gilbert
--2023-Sep-13		99			batch Bpt Optimizations										  		 Surya Murthy
-- 2024-Jan-22	   100			Changed MedicareSecondaryPayer from FLOAT to Decimal(7,6)			 Adam Gilbert
--2024-Apr-15	   101			Truncate logic added for worksheet#3 Cell#H53						 Surya Murthy
--2024-Apr-16	   102			Truncate logic added for worksheet#3 For H and M columns			 Surya Murthy
-- 2024-May-05     103			Added NOLOCK Table Hint												 Kiran Kola
--2023-May-5	   104			@fnAppGetMABPTWS3 size increase for OONCostShareDesc to MAX  		 Adam Gilbert
--2024-SEP-6		105			Added in logic to remove fnGetNewINMOOPImpact dependency; removed
--								INPreMOOPCostshare and INMOOPCostShare and replaced 
--								PreMOOPINEffCopayCoins with INPreMOOPEffCostShare and INEffCopayCoins
--								with INMOOPEffCostShare and removed the additional
--								OONEffectiveDeductible, OONPreMOOPCostShare, and OONMOOPCostShare
--								from fnAppGetMABPTWS3												Franklin Fu
--2024-SEP-24	   106			Added RPCoreNBE to z2 calculation							  		 Adam Gilbert
--2025-Jan-23	   107			Removed VBIDs per CMS               						  		 Phillip Leigh
--2025-Jan-24	   108			Added Significance to populate WS1 cell I17. 
--										  If @WS1Populate=0 this will be blank otherwise 
--								   it will show the Signifiance Level from table lkpmodelsettings.       Phillip Leigh
--2025-Feb-12	   109			Added Suplus Deficit Allocation to WS1 and WS4			      			 Phillip Leigh
--2025-Mar-17	   110			WS4 LkpExtCMSBidServiceCategory restrction to 36 Wk4Row			      	 Phillip Leigh
--2025-Apr-07	   111			WS4 SurplusDeficit decimal issue								      	 Phillip Leigh
-- ---------------------------------------------------------------------------------------------------------------------  
CREATE FUNCTION [dbo].[fnAppMABPTCreation]
	( 
	 @ForecastID INT 
	)  

RETURNS @Results TABLE
    ( 
      Worksheet TINYINT ,  
      Cell VARCHAR(5) ,  
      Value VARCHAR(MAX) PRIMARY KEY ( Worksheet, Cell )  
    ) AS
	BEGIN

        DECLARE @row INT ,  
				@column INT  
        DECLARE @ResultsTemp AS TABLE  
            (  
              Worksheet TINYINT ,  
              Cell VARCHAR(10) ,  
              Value VARCHAR(MAX)  
            )  
        DECLARE @MemberMonths INT  
        DECLARE @BasePlan INT  
        DECLARE @WS1Populate INT  
        DECLARE @PartBOnly BIT  
        DECLARE @IsMLA BIT  
        DECLARE @SurplusDeficitString VARCHAR(30) = 'SurplusDeficit'


		DECLARE @fnAppGetGeneralMABPTValues TABLE  
            (  
              ForecastID INT ,  
			  PlanName VARCHAR(100) ,  
              ContractNumber CHAR(5) ,  
              PlanID CHAR(3) ,  
              SegmentID CHAR(3) ,  
              MARatingOptionID TINYINT ,  
              IsClaimBucket2Output BIT ,  
              ContractMMPercent FLOAT ,  
              PlanTypeName VARCHAR(50) ,  
              MAPD CHAR(1) ,  
              SNP CHAR(1) ,  
              SNPTypeName VARCHAR(50) ,  
              ActuarialSwap CHAR(1) ,  
              SecondaryPayerAdjustment DECIMAL(7,6) ,  
              DualEligiblePercent FLOAT ,  
              PartBPremiumBuyDown FLOAT ,  
              AltRebateOrder TINYINT ,  
              CompletionFactor FLOAT ,  
              PaidThroughDate VARCHAR(10) ,  
              OrganizationName VARCHAR(100),  
			  CorpMargReq Decimal(7,4),
             -- VBIDC CHAR(1), --added VBID  
             -- VBIDH CHAR(1),  
              GroupPlan CHAR(1) ,  
              EnrolleeType VARCHAR(12) ,  
              DatePrepared SMALLDATETIME ,  
              MARegion VARCHAR(100) ,  
              MARegionName VARCHAR(500) ,  
              MandatorySupPackage BIT ,  
              IncurredFrom SMALLDATETIME ,  
              IncurredTo SMALLDATETIME ,  
              PartDTargetPremiumMessage VARCHAR(500) ,  
              UserID CHAR(13) ,  
              CertifyingName VARCHAR(100) ,  
              CertifyingPhone VARCHAR(15) ,  
              CertifyingEmail VARCHAR(100) ,  
              ContactName VARCHAR(100) ,  
              ContactPhone VARCHAR(15) ,  
              ContactEmail VARCHAR(100) ,  
              SecondaryContactName VARCHAR(100) ,  
              SecondaryContactPhone VARCHAR(15) ,  
              SecondaryContactEmail VARCHAR(100) ,  
              MemberMonths INT ,  
              NonDualMemberMonths INT ,  
              DualMemberMonths INT ,  
              RiskScore DECIMAL(18,15) ,  
              NonDualRiskScore DECIMAL(18,15) ,  
              DualRiskScore DECIMAL(18,15) ,  
              ExpBaseContractPlanID VARCHAR(10),  
              ExpBaseMemberMonths INT,  
              ExperienceBaseNonDualMemberMonths INT ,  
              ExperienceBaseDualMemberMonths INT ,  
              ExpBaseRiskScore DECIMAL(9,6),  
              ExpBaseNonDualRiskScore DECIMAL(9,6),  
              ExpBaseDualRiskScore DECIMAL(9,6),  
              MarketingSales FLOAT,  
              DirectAdmin FLOAT,  
              InDirectAdmin FLOAT ,  
              QualityInitiatives FLOAT ,  
              TaxesAndFees FLOAT ,  
              Reins FLOAT ,  
              Profit FLOAT ,
              TotalReduceCostShare DECIMAL(7, 2) ,  
              TotalOtherSuppBen DECIMAL(7, 2) ,  
              ESRDMembers DECIMAL(28, 6) ,  
              HospiceMembers DECIMAL(28, 6) ,  
              OOAMembers DECIMAL(28, 6) ,  
              ExperienceESRDMemberMonths INT ,  
              RxBasicPremium DECIMAL(7, 2) ,  
              RxSuppPremium DECIMAL(7, 2) ,  
              RxBasicBuyDown DECIMAL(7, 2) ,  
              RxSuppBuyDown DECIMAL(7, 2) ,  
              IsINMOOP VARCHAR(3) ,  
              IsOONMOOP VARCHAR(3) ,  
              IsCombinedMOOP VARCHAR(3) ,  
              INMOOP FLOAT ,  
              OONMOOP FLOAT ,  
              CombinedMOOP FLOAT ,  
              INDeductible FLOAT ,  
              OONDeductible FLOAT , 
			  CombinedDeductible  FLOAT , 
              PartBDeductible INT ,  
              INMOOPImpact FLOAT ,  
              OONMOOPImpact FLOAT ,  
              EstimatedPlanBidComponent DECIMAL(7, 2) ,  
              BidNotes1 VARCHAR(MAX) ,  
              BidNotes2 VARCHAR(MAX) ,  
              BidNotes3 VARCHAR(MAX) ,  
              BidNotes4 VARCHAR(MAX) ,  
              BidNotes5 VARCHAR(MAX) ,  
              CMSESRDRevenue DECIMAL(18, 2) ,  
              CMSHospiceRevenue DECIMAL(18, 2) ,  
              CMSOtherRevenue DECIMAL(18, 2) ,  
              ESRDPremium DECIMAL(18, 2) ,  
              HospicePremium DECIMAL(18, 2) ,  
              OtherPremium DECIMAL(18, 2) ,  
              ESRDNetMedicalExpenses DECIMAL(18, 2) ,  
              HospiceNetMedicalExpenses DECIMAL(18, 2) ,  
              OtherNetMedicalExpenses DECIMAL(18, 2) ,  
              ESRDMemberMonths INT ,  
              HospiceMemberMonths INT ,  
              MarketingandSalesAdminExpense DECIMAL(17, 2) ,  
              DirectAdminExpense DECIMAL(17, 2) ,  
              IndirectAdminExpense DECIMAL(17, 2) ,  
              QualityInitiativesAdminExpense DECIMAL(17, 2) ,  
              TaxesAndFeesAdminExpense DECIMAL(17, 2) ,  
              NetCostofPrivateInsuranceExpense DECIMAL(17, 2) ,  
              InsurerFeesAdminExpense DECIMAL(18, 2) ,     ----------------  
              MedicaidRevenue DECIMAL(18, 2) ,     ----------------  
              MedicaidBenefitExpenses DECIMAL(18, 2) ,  ---  
              MedicaidNonBenefitExpenses DECIMAL(18, 2) ,   -----------------  
              RedABCostShare DECIMAL(6, 2) ,  
              UnspentRebate FLOAT ,  
              EGWPMSB FLOAT ,  
              CMSQualityStars DECIMAL(2, 1) ,  
              CMSEnrollmentType VARCHAR(3) ,  
              MAMarketingExp DECIMAL(12, 6) ,  
              MADirectAdminExp DECIMAL(12, 6) ,  
              MAIndirectAdminExp DECIMAL(12, 6) ,  
              MAQualityAdminExp DECIMAL(12, 6) ,  
              MATaxesAndFeesAdminExp DECIMAL(12, 6) ,  
              PDMarketingExp DECIMAL(12, 6) ,  
              PDDirectAdminExp DECIMAL(12, 6) ,  
              PDIndirectAdminExp DECIMAL(12, 6) ,  
              PDQualityAdminExp DECIMAL(12, 6) ,  
              PDTaxesAndFeesAdminExp DECIMAL(12, 6) ,  
              InsurerFee DECIMAL(8, 6) ,  
              OSBNetMedicalExpenses DECIMAL(16, 6) ,  
              OSBNonBenefitExpenses DECIMAL(16, 6) ,  
              OSBPremium DECIMAL(16, 6) ,  
              OSBMembermonths INT ,
              Significance  DECIMAL(5,4)
            )  



		INSERT  INTO @fnAppGetGeneralMABPTValues  
        SELECT ForecastID,  
			   PlanName,  
               ContractNumber,  
               PlanID,  
               SegmentID,  
               MARatingOptionID,  
               IsClaimBucket2Output,  
               ContractMMPercent,  
               PlanTypeName,  
               MAPD,  
               SNP,  
               SNPTypeName,  
               ActuarialSwap,  
               SecondaryPayerAdjustment,  
               DualEligiblePercent,  
               PartBPremiumBuyDown,  
               AltRebateOrder,  
               CompletionFactor,  
               PaidThroughDate,  
               OrganizationName,  
			   CorpMargReq,
             --  VBIDC, --added VBID  
             -- VBIDH,  
               GroupPlan,  
               EnrolleeType,  
               DatePrepared,  
               MARegion,  
               MARegionName,  
               MandatorySupPackage,  
               IncurredFrom,  
               IncurredTo,  
               PartDTargetPremiumMessage,  
               UserID,  
               CertifyingName,  
               CertifyingPhone,  
               CertifyingEmail,  
               ContactName,  
               ContactPhone,  
               ContactEmail,  
               SecondaryContactName,  
               SecondaryContactPhone,  
               SecondaryContactEmail,  
               MemberMonths,  
               NonDualMemberMonths,  
               DualMemberMonths,  
               RiskScore,  
               NonDualRiskScore,  
               DualRiskScore,  
               ExpBaseContractPlanID,  
               ExpBaseMemberMonths,  
               ExperienceBaseNonDualMemberMonths,  
               ExperienceBaseDualMemberMonths,  
               ExpBaseRiskScore,  
               ExpBaseNonDualRiskScore,  
               ExpBaseDualRiskScore,  
               MarketingSales,  
               DirectAdmin,  
               InDirectAdmin,  
               QualityInitiatives,  
               TaxesAndFees,  
               Reins,  
               Profit,  
               TotalReduceCostShare,  
               TotalOtherSuppBen,  
               ESRDMembers,  
               HospiceMembers,  
               OOAMembers,  
               ExperienceESRDMemberMonths,  
               RxBasicPremium,  
               RxSuppPremium,  
               RxBasicBuyDown,  
               RxSuppBuyDown,  
               IsINMOOP,  
               IsOONMOOP,  
               IsCombinedMOOP,  
               INMOOP,  
               OONMOOP,  
               CombinedMOOP,  
               INDeductible,  
               OONDeductible,  
			   CombinedDeductible,
               PartBDeductible,  
               INMOOPImpact,  
               OONMOOPImpact,  
               EstimatedPlanBidComponent,  
               BidNotes1,  
               BidNotes2,  
               BidNotes3,  
               BidNotes4,  
               BidNotes5,  
               CMSESRDRevenue,  
               CMSHospiceRevenue,  
               CMSOtherRevenue,  
               ESRDPremium,  
               HospicePremium,  
               OtherPremium,  
               ESRDNetMedicalExpenses,  
               HospiceNetMedicalExpenses,  
               OtherNetMedicalExpenses,  
               ESRDMemberMonths,  
               HospiceMemberMonths,  
               MarketingandSalesAdminExpense,  
               DirectAdminExpense,  
               IndirectAdminExpense,  
               QualityInitiativesAdminExpense,  
               TaxesAndFeesAdminExpense,  
               NetCostofPrivateInsuranceExpense,  
               InsurerFeesAdminExpense,     
               MedicaidRevenue,     
               MedicaidBenefitExpenses,  
               MedicaidNonBenefitExpenses,   
               RedABCostShare,  
               UnspentRebate,  
               EGWPMSB,  
               CMSQualityStars,  
               CMSEnrollmentType,  
               MAMarketingExp,  
               MADirectAdminExp,  
               MAIndirectAdminExp,  
               MAQualityAdminExp,  
               MATaxesAndFeesAdminExp,  
               PDMarketingExp,  
               PDDirectAdminExp,  
               PDIndirectAdminExp,  
               PDQualityAdminExp,  
               PDTaxesAndFeesAdminExp,  
               InsurerFee,  
               OSBNetMedicalExpenses,  
               OSBNonBenefitExpenses,  
               OSBPremium,  
               OSBMembermonths,
               Significance
        FROM    dbo.fnAppGetGeneralMABPTValues(@ForecastID) 	

 --Variables used to determine if WS1 is to be populated or not  
        SET @MemberMonths = (SELECT MemberMonths FROM @fnAppGetGeneralMABPTValues)  
        IF EXISTS ( SELECT  ContractNumber  
                    FROM    dbo.fnAppGetMABPTWS1BasePlans(@ForecastID) )  
            BEGIN  
                SET @BasePlan = 1  
            END  
        ELSE  
            BEGIN  
                SET @BasePlan = 0  
            END  

 -- Used in case statements for WS1   
        IF @MemberMonths <> 0  
            OR @BasePlan = 1  
            BEGIN  
                SET @WS1Populate = 1  
            END  
        ELSE  
            BEGIN  
                SET @WS1Populate = 0  
            END  

 -- Get Part B Only plan indicator  
        SET @PartBOnly = 0  
    -- Get MLA Plan indicator  
  SELECT @IsMLA = CAST(CASE WHEN spi.MAPlanDesignID = 2 THEN 1  
						    ELSE 0                              
					   END AS BIT)  
  FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)
  LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK) 
	ON SFS.PlanInfoID = SPI.PlanInfoID  
  WHERE SFS.ForecastID = @ForecastID 

  ----------------------------------------------------------------------------------------------------------------  
  -- All BPT Temp tables used for speed concerns ------------------------------------------------------- Tables --  
  ----------------------------------------------------------------------------------------------------------------  

        DECLARE @fnAppGetMABPTWS3 TABLE  
            (  

              PlanYearID SMALLINT ,  
              ForecastID INT ,  
              CostShareServiceCategoryCode CHAR(4) ,  
              ServiceCategory VARCHAR(200) ,  
              SubServiceCategory VARCHAR(200) ,  
              MeasUnitCode VARCHAR(10) ,  
              INEffectiveDeductible DECIMAL(18, 10) ,  
              INUtilOrPMPM DECIMAL(18, 10) ,  
              INCostShareDesc VARCHAR(MAX) ,  
              INPreMOOPEffCostShare DECIMAL(18, 10) ,  
              INMOOPEffCostShare DECIMAL(18, 10) ,    
              INCostShare DECIMAL(18, 10) ,  
              OONCostShare DECIMAL(18, 10) ,  
              OONCostShareDesc VARCHAR(1000)  
            )  

        DECLARE @fnAppGetMABPTWS7 TABLE  
            (  
              PlanPackageID TINYINT NOT NULL ,  
              [Description] VARCHAR(MAX) NOT NULL ,  
              PackageIndex INT NOT NULL,  
              TotalExpense DECIMAL(5, 2) ,  
              TotalGainLoss DECIMAL(5, 2) ,  
              TotalProjectedMemberMonths DECIMAL(20, 6)  
            )  

		INSERT  INTO @fnAppGetMABPTWS3  
        SELECT  PlanYearID,
				ForecastID,
				CostShareServiceCategoryCode,
				ServiceCategory,
				SubServiceCategory,
				MeasUnitCode,
				INEffectiveDeductible,
				INUtilOrPMPM,
				INCostShareDesc,
				INPreMOOPEffCostShare,
				INMOOPEffCostShare,
				INCostShare,
				OONCostShare,
				OONCostShareDesc
        FROM dbo.fnAppGetMABPTWS3(@ForecastID)   
		WHERE INCostShareDesc != '0'

		INSERT INTO @fnAppGetMABPTWS7  
        SELECT PlanPackageID,
			   Description,
			   PackageIndex,
			   TotalExpense,
			   TotalGainLoss,
			   TotalProjectedMemberMonths
        FROM dbo.fnAppGetMABPTWS7(@ForecastID)  


  ----------------------------------------------------------------------------------------------------------------  
  -- All information on WS1 ------------------------------------------------------------------------------- WS1 --  
  ----------------------------------------------------------------------------------------------------------------  
				--Columns
				DECLARE @Columns TABLE
					(
					 ColumnName VARCHAR(MAX)
					)

				INSERT INTO @Columns
				SELECT ColumnName = c.name  
                FROM sys.objects o  
                FULL OUTER JOIN sys.columns c 
					ON o.object_id = c.object_id  
                WHERE o.Name = 'fnAppGetGeneralMABPTValues'  

			--Setup
			DECLARE @Setup TABLE
				(
				 ValueName VARCHAR(100),
				 Worksheet INT,
				 Cell VARCHAR(5)
				)

			INSERT INTO @Setup
			SELECT map.ValueName,
				   map.Worksheet,
				   map.Cell
            FROM @Columns cols
            INNER JOIN dbo.LkpIntMABPTMapping map WITH(NOLOCK)            
				ON cols.ColumnName = map.ValueName  

 -- Section 1, 2, 5, 6  
        INSERT  INTO @ResultsTemp  
                SELECT DISTINCT  
                        setup.Worksheet ,  
                        setup.Cell ,  
                        Value = CASE setup.ValueName  
                                  WHEN 'ContractNumber'  
                                  THEN CAST(ContractNumber AS VARCHAR(MAX))  
                                  WHEN 'PlanID'  
                                  THEN '''' + CAST(PlanID AS VARCHAR(MAX))  
                                  WHEN 'SegmentID'  
                                  THEN '''' + CAST(SegmentID AS VARCHAR(MAX))  
                                  WHEN 'OrganizationName'  
                                  THEN CAST(OrganizationName AS VARCHAR(MAX))  
                                  WHEN 'PlanName'  
                                  THEN CAST(PlanName AS VARCHAR(MAX))  
                                  WHEN 'PlanTypeName'  
                                  THEN CAST(PlanTypeName AS VARCHAR(MAX))  
                                  WHEN 'MAPD' THEN CAST(MAPD AS VARCHAR(MAX))  
                                  WHEN 'EnrolleeType'  
                                  THEN CASE WHEN PlanTypeName <> 'RPPO'  
                                            THEN CAST(EnrolleeType AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'MARegion'  
                                  THEN CASE WHEN PlanTypeName = 'RPPO'  
                                            THEN '''' + RIGHT('00'  
                                                              + CAST(LTRIM(MARegion) AS VARCHAR(MAX)),  
                                                              2)  
                                       END
                                  WHEN 'ActuarialSwap'  
                                  THEN CAST(ActuarialSwap AS VARCHAR(MAX))  
                                  WHEN 'SNP' THEN CAST(SNP AS VARCHAR(MAX))  
                                  WHEN 'SNPTypeName'  
                                  THEN CASE WHEN SNP = 'Y'  
											THEN   
											CASE WHEN CAST(SNPTypeName AS VARCHAR(MAX)) = 'Dual-Eligible Puerto Rico'  
												 THEN 'Dual-Eligible'  
												 ELSE CAST(SNPTypeName AS VARCHAR(MAX))  
										    END   
                                       END  
								  -- WHEN 'VBIDC' THEN CAST(VBIDC AS VARCHAR (MAX)) --added VBIDC distinction  
								  -- WHEN 'VBIDH' THEN CAST(VBIDH AS VARCHAR (MAX)) --added VBIDH distinction  
     --Begin optional population  
                                  WHEN 'PaidThroughDate'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE PaidThroughDate  
                                       END  
                                  WHEN 'NonDualMemberMonths'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(NonDualMemberMonths AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'RiskScore'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(RiskScore AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'NonDualRiskScore'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(NonDualRiskScore AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'CompletionFactor'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(CompletionFactor AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'BidNotes1'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(BidNotes1 AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'BidNotes2'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(BidNotes2 AS VARCHAR(MAX))  
                                       END  
     --End optional population  
                                  WHEN 'CMSESRDRevenue'  
                                  THEN CAST(CMSESRDRevenue AS VARCHAR(MAX))  
                                  WHEN 'CMSHospiceRevenue'  
                                  THEN CAST(CMSHospiceRevenue AS VARCHAR(MAX))  
                                  WHEN 'CMSOtherRevenue'  
                                  THEN CAST(CMSOtherRevenue AS VARCHAR(MAX))  
                                  WHEN 'ESRDPremium'  
                                  THEN CAST(ESRDPremium AS VARCHAR(MAX))  
                                  WHEN 'HospicePremium'  
                                  THEN CAST(HospicePremium AS VARCHAR(MAX))  
                                  WHEN 'OtherPremium'  
                                  THEN CAST(OtherPremium AS VARCHAR(MAX))  
                                  WHEN 'ESRDNetMedicalExpenses'  
                                  THEN CAST(ESRDNetMedicalExpenses AS VARCHAR(MAX))  
                                  WHEN 'HospiceNetMedicalExpenses'  
                                  THEN CAST(HospiceNetMedicalExpenses AS VARCHAR(MAX))  
                                  WHEN 'OtherNetMedicalExpenses'  
                                  THEN CAST(OtherNetMedicalExpenses AS VARCHAR(MAX))  
                                  WHEN 'ESRDMemberMonths'  
                                  THEN CAST(ESRDMemberMonths AS VARCHAR(MAX))  
                                  WHEN 'HospiceMemberMonths'  
                                  THEN CAST(HospiceMemberMonths AS VARCHAR(MAX))  
                                  WHEN 'MarketingandSalesAdminExpense'  
                                  THEN CAST(MarketingandSalesAdminExpense AS VARCHAR(MAX))  
                                  WHEN 'DirectAdminExpense'  
                                  THEN CAST(DirectAdminExpense AS VARCHAR(MAX))  
                                  WHEN 'IndirectAdminExpense'  
                                  THEN CAST(IndirectAdminExpense AS VARCHAR(MAX))  
                                  WHEN 'QualityInitiativesAdminExpense'  
                                  THEN CAST(QualityInitiativesAdminExpense AS VARCHAR(MAX))  
                                  WHEN 'TaxesAndFeesAdminExpense'  
                                  THEN CAST(TaxesAndFeesAdminExpense AS VARCHAR(MAX))  
                                  WHEN 'NetCostofPrivateInsuranceExpense'  
                                  THEN CAST(NetCostofPrivateInsuranceExpense AS VARCHAR(MAX))  
                                 -- WHEN 'InsurerFeesAdminExpense'  
                                --  THEN CAST(InsurerFeesAdminExpense AS VARCHAR(MAX))      ----------------  
                                  WHEN 'MedicaidRevenue'  
                                  THEN CAST(MedicaidRevenue AS VARCHAR(MAX))  
                                  WHEN 'MedicaidBenefitExpenses'  
                                  THEN CAST(MedicaidBenefitExpenses AS VARCHAR(MAX))  
                                  WHEN 'MedicaidNonBenefitExpenses'  
                                  THEN CAST(MedicaidNonBenefitExpenses AS VARCHAR(MAX))     ----------------  
                                  WHEN 'Significance'
								  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE   CAST (Significance AS VARCHAR(MAX))
                                    END 
								END  
                FROM @fnAppGetGeneralMABPTValues header  
                CROSS JOIN @Setup setup  

  -- Section 2 - Base plans
			--WS1
			DECLARE @WS1 TABLE
				(
				 Id	INT,
				 ContractNumber	CHAR(5),
				 PlanID	CHAR(3),
				 SegmentId CHAR(3),
				 MemberMonths INT,
				 NonDualMemberMonths INT
				)

			INSERT INTO @WS1
			SELECT DISTINCT RANK() OVER ( ORDER BY MemberMonths DESC ) AS Id,  
                            w1.ContractNumber,
							w1.PlanID,
							w1.SegmentId,
							w1.MemberMonths,
							w1.NonDualMemberMonths
            FROM dbo.fnAppGetMABPTWS1BasePlans(@ForecastID) w1  
            WHERE w1.ContractNumber IS NOT NULL  
			-----------------------------------------------------------------

			--Setup
			DECLARE @SetupTemp TABLE
				(
				 columnName VARCHAR(MAX)
				)

			INSERT INTO @SetupTemp
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE  o.Name = 'fnAppGetMABPTWS1BasePlans'



		-------------------------------------------------------------

        INSERT  @ResultsTemp  
                SELECT DISTINCT  
                        Worksheet = 1 ,  
                        Cell = CASE setup.columnName  
                                 WHEN 'ContractNumber'  
                                 THEN CASE WHEN ws1.Id <= 4 THEN 'N'  
                                           ELSE 'P'  
                                      END + CAST(Value AS VARCHAR(2))  
                                 WHEN 'MemberMonths'  
                                 THEN CASE WHEN ws1.Id <= 4 THEN 'O'  
                                           ELSE 'Q'  
                                      END + CAST(Value AS VARCHAR(2))  
                               END,  
                        Value = CASE setup.ColumnName  
                                  WHEN 'ContractNumber'  
                                  THEN ContractNumber + '-' + PlanID + '-'  
                                       + SegmentId --Added SegmentId  
                                  WHEN 'MemberMonths'  
                                  THEN CAST(MemberMonths AS VARCHAR(MAX))  
                                END  
                FROM @WS1 ws1  
                INNER JOIN dbo.fnStringSplit('14,15,16,17,14,15,16,0',',') col 
					ON col.Id = ws1.Id  
                CROSS JOIN @SetupTemp setup  



  -- In case there are 8 or more plans in the base experience  
        INSERT  @ResultsTemp  
                SELECT DISTINCT  
                        Worksheet = 1 ,  
                        Cell = CASE setup.ColumnName  
                                 WHEN 'ContractNumber' THEN 'P17'  
                                 WHEN 'MemberMonths' THEN 'Q17'  
                               END ,  
                        Value = CASE setup.ColumnName  
                                  WHEN 'ContractNumber' THEN 'All Other'  
                                  WHEN 'MemberMonths'  
                                  THEN CAST(MemberMonths AS VARCHAR(MAX))  
                                END  
                FROM dbo.fnAppGetMABPTWS1BasePlans(@ForecastID) w1  
                CROSS JOIN @SetupTemp setup  
                WHERE w1.ContractNumber IS NULL  

  -- Section 3, 4  
			--SetupSummary
			DECLARE @SetupSummary TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupSummary
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE  o.Name = 'fnAppGetMABPTWS1Summary'
		----------------------------------------------------------------------------------------------


        INSERT  @ResultsTemp  
                SELECT DISTINCT  
                        Worksheet = 1 ,  
                        Cell = CASE setup.ColumnName  
                                 WHEN 'BaseNetPMPM'  
                                 THEN 'D' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'BaseUnits'  
                                 THEN 'G' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'BaseAllowed'  
                                 THEN 'I' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'UtilizationTrend'  
                                 THEN 'J' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'BenefitChange'  
                                 THEN 'K' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'PopulationChange'  
                                 THEN 'L' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'OtherFactor'  
                                 THEN 'M' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'ProviderPaymentChange'  
                                 THEN 'N' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'CostTrend'  
                                 THEN 'O' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'UtilAdditiveAdjustment'  
                                 THEN 'P' + CAST(bs.Wk1Row AS VARCHAR(2))  
                                 WHEN 'PMPMAdditiveAdjustment'  
                                 THEN 'Q' + CAST(bs.Wk1Row AS VARCHAR(2)) 
								 WHEN @SurplusDeficitString
								 THEN 'V' + CAST(bs.Wk1Row AS VARCHAR(2))
                               END ,  
                        Value = CASE setup.ColumnName  
                                  WHEN 'BaseNetPMPM'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(0.0  
                                                           * BaseNetPMPM AS VARCHAR(MAX))  
                                                      WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 28  
                                                      THEN CAST(BaseNetPMPM AS VARCHAR(MAX))  
                                                      ELSE CAST(BaseNetPMPM AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'BaseUnits'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(0.0  
                                                           * BaseUnits AS VARCHAR(MAX))  
                                                      WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 28  
                                                      THEN CAST(0.05  
                                                           * BaseUnits AS VARCHAR(MAX))  
                                                      ELSE CAST(BaseUnits AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'BaseAllowed'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                       ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(0.0  
                                                           * BaseAllowed AS VARCHAR(MAX))  
                                                      WHEN @PartBOnly = 1  
													   AND bs.Wk1Row = 28  
                                                      THEN CAST(0.05  
                                                           * BaseAllowed AS VARCHAR(MAX))  
                                                      ELSE CAST(BaseAllowed AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'UtilizationTrend'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(UtilizationTrend  
                                                           / UtilizationTrend AS VARCHAR(MAX))  
                                                      ELSE CAST(UtilizationTrend AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'BenefitChange'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(BenefitChange  
                                                           / BenefitChange AS VARCHAR(MAX))  
                                                      ELSE CAST(BenefitChange AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'PopulationChange'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(PopulationChange  
                                                           / PopulationChange AS VARCHAR(MAX))  
                                                      ELSE CAST(PopulationChange AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'OtherFactor'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(OtherFactor  
                                                           / OtherFactor AS VARCHAR(MAX))  
                                                      ELSE CAST(OtherFactor AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'ProviderPaymentChange'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(ProviderPaymentChange  
                                                           / ProviderPaymentChange AS VARCHAR(MAX))  
                  ELSE CAST(ProviderPaymentChange AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'CostTrend'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
													  THEN CAST(CostTrend  
                                                           / CostTrend AS VARCHAR(MAX))  
                                                      ELSE CAST(CostTrend AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                  WHEN 'UtilAdditiveAdjustment'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(ISNULL(UtilAdditiveAdjustment,  
                                                             0) AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'PMPMAdditiveAdjustment'  
                                  THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CAST(ISNULL(PMPMAdditiveAdjustment,  
                                                             0) AS VARCHAR(MAX)) 
									END
									WHEN @SurplusDeficitString  
                                     THEN CASE WHEN @WS1Populate = 0 THEN ''  
                                            ELSE CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk1Row = 27  
                                                      THEN CAST(0.0  
                                                           * SurplusDeficit AS VARCHAR(MAX))  
                                                      WHEN @PartBOnly = 1  
													   AND bs.Wk1Row = 28  
                                                      THEN CAST(0.05  
                                                           * SurplusDeficit AS VARCHAR(MAX))  
                                                      ELSE CAST(SurplusDeficit AS VARCHAR(MAX))  
                                                 END  
                                       END  
                                END  
                FROM dbo.LkpExtCMSBidServiceCategory bs  
                LEFT JOIN dbo.fnAppGetMABPTWS1Summary(@ForecastID) w1 
					ON w1.ServiceCategoryCode = bs.ServiceCategoryCode  
                CROSS JOIN @SetupSummary setup  
                WHERE bs.Wk1Row <> 44 --We don't use COB/Subrg. (outside claim system) on the BPT, so make sure it's excluded  



  ----------------------------------------------------------------------------------------------------------------  
  -- All information on WS2 ------------------------------------------------------------------------------- WS2 --  
  ----------------------------------------------------------------------------------------------------------------  
  -- Section 2  
        INSERT  INTO @ResultsTemp  
                SELECT DISTINCT  
                        setup.Worksheet ,  
                        setup.Cell ,  
                        Value = CASE setup.ValueName  
                                  WHEN 'BidNotes3'  
                                  THEN CAST(BidNotes3 AS VARCHAR(MAX)) --CASE WHEN GroupPlan = 'Y' THEN CAST(BidNotes5 AS VARCHAR(MAX)) ELSE CAST(BidNotes3 AS VARCHAR(MAX)) END  
                                END  
                FROM    @fnAppGetGeneralMABPTValues header  
                CROSS JOIN @Setup setup  

  -- Section 2 
			DECLARE @SetupWS2 TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupWS2
			SELECT ColumnName = c.name  
			FROM sys.objects o  
			FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
			WHERE  o.Name = 'fnAppGetMABPTWS2'  


		-----------------------------------------------------------------

		INSERT  @ResultsTemp  
                SELECT DISTINCT  
                        Worksheet = 2 ,  
                        Cell = CASE setup.ColumnName  
                                 WHEN 'UtilType'  
                                 THEN 'E' + CAST(bs.Wk2Row AS VARCHAR(2))  
                                 WHEN 'ManualUtil'  
                                 THEN 'I' + CAST(bs.Wk2Row AS VARCHAR(2))  
                                 WHEN 'ManualAllowed'  
                                 THEN 'K' + CAST(bs.Wk2Row AS VARCHAR(2))  
                                 WHEN 'ExperienceCredibility'  
                                 THEN 'L' + CAST(bs.Wk2Row AS VARCHAR(2))  
                                 WHEN 'NonDualBlendedAllowedPMPM'  
                                 THEN 'P' + CAST(bs.Wk2Row AS VARCHAR(2))  
                                 WHEN 'DualBlendedAllowedPMPM'  
                                 THEN 'Q' + CAST(bs.Wk2Row AS VARCHAR(2))  
                                 WHEN 'PercentOON'  
                                 THEN 'R' + CAST(bs.Wk2Row AS VARCHAR(2))  
                               END ,  
                        Value = CASE setup.ColumnName  
                                  WHEN 'UtilType'  
                                  THEN CAST(w2.UtilType AS VARCHAR(MAX))  
                                  WHEN 'ManualUtil'  
                                  THEN CASE WHEN ExperienceCredibility <> 1  
                                            THEN CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk2Row = 20  
                                                      THEN CAST(0.0  
                                                           * ManualUtil AS VARCHAR(MAX))  
                                                      WHEN @PartBOnly = 1  
                                                           AND bs.Wk2Row = 21  
                                                      THEN CAST(0.05  
                                                           * ManualUtil AS VARCHAR(MAX))  
                                                      ELSE CAST(ManualUtil AS VARCHAR(MAX))  
                                                 END  
                                            ELSE NULL  
                                       END  
                                  WHEN 'ManualAllowed'  
                                  THEN CASE WHEN ExperienceCredibility <> 1  
                                            THEN CASE WHEN @PartBOnly = 1  
                                                           AND bs.Wk2Row = 20  
                                                      THEN CAST(0.0  
                                                           * ManualAllowed AS VARCHAR(MAX))  
                                                      WHEN @PartBOnly = 1  
                                                           AND bs.Wk2Row = 21  
                                                      THEN CAST(0.05  
                                                           * ManualAllowed AS VARCHAR(MAX))  
                                                      ELSE CAST(ManualAllowed AS VARCHAR(MAX))  
                                                 END  
                                            ELSE NULL  
                                       END  
                                  WHEN 'ExperienceCredibility'  
                                  THEN CAST(ExperienceCredibility AS VARCHAR(MAX))  
                                  WHEN 'NonDualBlendedAllowedPMPM'  
                                  THEN CASE WHEN @PartBOnly = 1  
                                                 AND bs.Wk2Row = 20  
                                            THEN CAST(0.0  
                           * NonDualBlendedAllowedPMPM AS VARCHAR(MAX))  
                                            WHEN @PartBOnly = 1  
                                                 AND bs.Wk2Row = 21  
                                            THEN CAST(0.05  
                                                 * NonDualBlendedAllowedPMPM AS VARCHAR(MAX))  
                                            ELSE CAST(NonDualBlendedAllowedPMPM AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'DualBlendedAllowedPMPM'  
                                  THEN CASE WHEN @PartBOnly = 1  
                                                 AND bs.Wk2Row = 20  
                                            THEN CAST(0.0  
                                                 * DualBlendedAllowedPMPM AS VARCHAR(MAX))  
                                            WHEN @PartBOnly = 1  
                                                 AND bs.Wk2Row = 21  
                                            THEN CAST(0.05  
                                                 * DualBlendedAllowedPMPM AS VARCHAR(MAX))  
                                            ELSE CAST(DualBlendedAllowedPMPM AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'PercentOON'  
                                  THEN CASE WHEN @PartBOnly = 1  
                                                 AND bs.Wk2Row = 20  
                                            THEN CAST(0.0 * PercentOON AS VARCHAR(MAX))  
                                            WHEN @PartBOnly = 1  
                                                 AND bs.Wk2Row = 21  
                                            THEN CAST(0.05 * PercentOON AS VARCHAR(MAX))  
                                            ELSE CAST(PercentOON AS VARCHAR(MAX))  
                                       END  
                                END  
                FROM dbo.LkpExtCMSBidServiceCategory bs WITH(NOLOCK)
                LEFT JOIN dbo.fnAppGetMABPTWS2(@ForecastID) w2 ON w2.ServiceCategoryCode = bs.ServiceCategoryCode  
                CROSS JOIN @SetupWS2 setup  


  ----------------------------------------------------------------------------------------------------------------  
  -- All information on WS3 ------------------------------------------------------------------------------- WS3 --  
  ----------------------------------------------------------------------------------------------------------------  
  -- Section 2, bottom of Section 3  

	   DECLARE @IsCombinedDeductible BIT   
	   SELECT @IsCombinedDeductible = CASE WHEN CombinedDeductible IS NOT NULL 
										  THEN 1
										  ELSE 0
									  END  
	   FROM dbo.Benefits_SavedBenefitOption sbo WITH(NOLOCK)
	   INNER JOIN dbo.SavedForecastSetup sfs WITH(NOLOCK)
		 ON sfs.PlanInfoID = sbo.PlanInfoID
		AND sfs.BenefitOptionID = sbo.BenefitOptionID
	   WHERE ForecastID = @ForecastID  
   ------------------------------------------------------------------------
---new logic and removing function dependecy fnGetNewINMOOPImpact  starts here ---
	DECLARE @Results_fn TABLE 
	(




	 NewINMOOPImpact DECIMAL(18, 10)
	)
	INSERT INTO @Results_fn
	SELECT NewINMOOPImpact = SUM(INAllowed * (1-INDeductibleFactor) * INEffectiveCoinsurance - (INAllowed * (1-INDeductibleFactor) * INEffectiveCoinsurance * INMOOPFactor))
	FROM dbo.CalcTotalCostShare
	WHERE ForecastID = @ForecastID
	AND IsBenefitYearCurrentYear = 0

---new logic and removing function dependecy fnGetNewINMOOPImpact  ends here ---

        INSERT  INTO @ResultsTemp  
        SELECT DISTINCT setup.Worksheet ,  
						setup.Cell ,  
						Value = CASE setup.ValueName  
									WHEN 'IsINMOOP'  
									THEN CASE WHEN PlanTypeName = 'PFFS'  
												AND IsCombinedMOOP <> 'YES'  
												THEN 'NO'  
												ELSE CAST(IsINMOOP AS VARCHAR(MAX))  
											END  
									WHEN 'INMOOP'  
									THEN CASE WHEN IsINMOOP = 'YES'  
												THEN CAST(INMOOP AS VARCHAR(MAX))  
											END  
									WHEN 'IsOONMOOP'  
									THEN CAST(IsOONMOOP AS VARCHAR(MAX))  
									WHEN 'OONMOOP'  
									THEN CASE WHEN IsOONMOOP = 'YES'  
												THEN CAST(OONMOOP AS VARCHAR(MAX))  
											END  
									WHEN 'IsCombinedMOOP'  
									THEN CASE WHEN PlanTypeName = 'PFFS'  
												AND IsCombinedMOOP <> 'YES'  
												THEN CAST(IsINMOOP AS VARCHAR(MAX))  
												ELSE CAST(IsCombinedMOOP AS VARCHAR(MAX))  
											END  
									WHEN 'CombinedMOOP'  
									THEN CASE WHEN PlanTypeName = 'PFFS'  
												AND IsCombinedMOOP <> 'YES'  
												THEN CAST(INMOOP AS VARCHAR(MAX))  
												ELSE CASE WHEN IsCombinedMOOP = 'YES'  
														THEN CAST(CombinedMOOP AS VARCHAR(MAX))  
													END  
											END  
									WHEN 'BidNotes4'  
									THEN CAST(BidNotes4 AS VARCHAR(MAX))  
									WHEN 'INDeductible'  
									THEN CASE WHEN ( PlanTypeName = 'HMO' )  
												AND (@IsMLA = 1)  
												THEN 'Medicare FFS'  
												WHEN ( PlanTypeName = 'HMO' )  
												AND ( ISNULL(PartBDeductible,0) <> 0 )  
												AND (@IsMLA = 0)  
												THEN 'Medicare FFS'  
												WHEN ( PlanTypeName = 'HMO' )  
												AND ( ISNULL(PartBDeductible,0) = 0 )  
												AND (@IsMLA = 0)  
												AND ( ISNULL(INDeductible, 0) <> 0)  
												THEN CAST(ISNULL(INDeductible, 0) AS VARCHAR(MAX))  
												WHEN ( PlanTypeName = 'HMO' )  
												AND ( ISNULL(PartBDeductible,0) = 0 )  
												AND (@IsMLA = 0)  
												AND ( ISNULL(INDeductible, 0) = 0)  
												THEN Null                                               
												WHEN ( PlanTypeName <> 'HM0' )  
												AND ( @IsMLA = 1 )  
												THEN Null   
												WHEN ( PlanTypeName <> 'HMO' )  
												AND ( ISNULL(PartBDeductible, 0) = 0 )  
												AND (ISNULL(INDeductible, 0) <> 0)               
												THEN CAST(ISNULL(INDeductible, 0) AS VARCHAR(MAX))  
												WHEN ( PlanTypeName <> 'HMO' )  
												AND ( ISNULL(PartBDeductible, 0) = 0 )  
												AND( ISNULL(INDeductible,0) = 0)               
												THEN Null                                             
											END  
									WHEN 'OONDeductible'  
									THEN CASE WHEN PlanTypeName = 'HMO'  
												THEN NULL  
												WHEN PlanTypeName IN ( 'LPPO', 'RPPO' )  
												AND (@IsMLA = 1)  
												THEN 'Medicare FFS'  
												WHEN PlanTypeName = 'PFFS'  
												AND ( @IsMLA = 1)  
												THEN Null  
												WHEN PlanTypeName = 'PFFS'  
												AND (@IsMLA = 0) 
												AND OONDeductible <> 0  
												THEN CAST(ISNULL(OONDeductible, 0) AS VARCHAR(MAX))  
												WHEN PlanTypeName IN ( 'LPPO','RPPO' )  
												AND (@IsMLA = 0)   
												AND IsNull(OONDeductible,0) <> 0  
												THEN CAST( ISNULL(OONDeductible, 0) AS VARCHAR(MAX))  
												WHEN PlanTypeName IN ( 'LPPO','RPPO' )  
												AND (@IsMLA = 0) 
												AND IsNull(PartBDeductible,0) <> 0  
												THEN 'Medicare FFS'  
												ELSE Null  
											END  
									WHEN 'INMOOPImpact'  
									THEN CASE WHEN @PartBOnly = 1  
												THEN CAST(dbo.fnGetSafeDivisionResultReturnOne(ISNULL(INMOOPImpact,0)  
														,ISNULL(INMOOPImpact,0))  
														* (SELECT NewINMOOPImpact  
															FROM @Results_fn)AS VARCHAR(MAX))  
												ELSE CAST(ISNULL(INMOOPImpact, 0) AS VARCHAR(MAX))  
											END  
									WHEN 'OONMOOPImpact'  
									THEN CASE WHEN @PartBOnly = 1  
												THEN CAST(dbo.fnGetSafeDivisionResultReturnOne(ISNULL(OONMOOPImpact,0)  
																							,ISNULL(INMOOPImpact, 0))  
														* (SELECT NewINMOOPImpact
															FROM @Results_fn) AS VARCHAR(MAX))  
												ELSE CASE WHEN PlanTypeName <> 'HMO'  
														THEN CAST(ISNULL(OONMOOPImpact, 0) AS VARCHAR(MAX))  
													END  
											END  
								END  
        FROM @fnAppGetGeneralMABPTValues header  
        CROSS JOIN @Setup setup  


  -- First grouping of Section 3  

			--Setup WS3
			DECLARE @SetupWS3 TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupWS3
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c ON o.object_id = c.object_id  
            WHERE  o.Name = 'fnAppGetMABPTWS3'
		--------------------------------------------------------------------------

        INSERT  @ResultsTemp  
        SELECT DISTINCT Worksheet = 3 ,  
						Cell = CASE setup.ColumnName  
									WHEN 'MeasUnitCode'  
									THEN 'E' + CAST(bs.Wk3Row AS VARCHAR(2))  
									WHEN 'INEffectiveDeductible'  
									THEN 'F' + CAST(bs.Wk3Row AS VARCHAR(2))  
									WHEN 'INUtilOrPMPM'  
									THEN 'G' + CAST(bs.Wk3Row AS VARCHAR(2))  
									WHEN 'INCostShareDesc'  
									THEN 'H' + CAST(bs.Wk3Row AS VARCHAR(2))  
									WHEN 'INPreMOOPEffCostShare'  
									THEN 'I' + CAST(bs.Wk3Row AS VARCHAR(2))  
									WHEN 'INMOOPEffCostShare'  
									THEN 'J' + CAST(bs.Wk3Row AS VARCHAR(2))  
									WHEN 'OONCostShareDesc'  
									THEN 'M' + CAST(bs.Wk3Row AS VARCHAR(2))  
									WHEN 'OONCostShare'  
									THEN 'N' + CAST(bs.Wk3Row AS VARCHAR(2))  
							   END ,  
						Value = CASE setup.ColumnName  
									WHEN 'MeasUnitCode'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CAST(MeasUnitCode AS VARCHAR(MAX))  
										 END  
									WHEN 'INEffectiveDeductible'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CAST(INEffectiveDeductible AS VARCHAR(MAX))  
										 END  
									WHEN 'INUtilOrPMPM'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CAST(INUtilOrPMPM AS VARCHAR(MAX))  
										 END  
									WHEN 'INCostShareDesc'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CASE WHEN gen.GroupPlan = 'Y'  
														 AND bs.Wk3Row BETWEEN 25 AND 42  
														THEN 'Medicare FFS Cost Sharing' --Was 28 AND 42  
														WHEN gen.GroupPlan = 'Y'  
														 AND bs.Wk3Row BETWEEN 44 AND 46  
														THEN 'Medicare FFS Cost Sharing'  
														ELSE CAST(INCostShareDesc AS VARCHAR(MAX))  
												   END  
										 END  
									WHEN 'INPreMOOPEffCostShare'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CAST(INPreMOOPEffCostShare AS VARCHAR(MAX))  
										 END  
									WHEN 'INMOOPEffCostShare'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CAST(INMOOPEffCostShare AS VARCHAR(MAX))  
										 END  
									WHEN 'OONCostShareDesc'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CASE WHEN gen.PlanTypeName <> 'HMO'  
														THEN CASE WHEN gen.GroupPlan = 'Y'  
																   AND bs.Wk3Row BETWEEN 25 AND 42  
																  THEN 'Medicare FFS Cost Sharing' --Was 28 AND 42  
																  WHEN gen.GroupPlan = 'Y'  
																   AND bs.Wk3Row BETWEEN 44 AND 46  
																  THEN 'Medicare FFS Cost Sharing'  
																  WHEN w3.CostShareServiceCategoryCode IN ('m.', 'n.1.', 'n.2.' )  
																  THEN CAST(INCostShareDesc AS VARCHAR(MAX))  
																  ELSE CASE WHEN CAST(OONCostShareDesc AS VARCHAR(MAX)) <> '0'  
																			THEN CAST(OONCostShareDesc AS VARCHAR(MAX))  
																	   END  
															 END  
												   END  
										 END  
									WHEN 'OONCostShare'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk3Row IN ( 25, 26, 27 )  
											  THEN ''  
											  ELSE CASE WHEN gen.PlanTypeName <> 'HMO'  
														THEN CASE WHEN CAST(OONCostShareDesc AS VARCHAR(MAX)) <> '0'  
																  THEN CAST(OONCostShare AS VARCHAR(MAX))  
															 END  
												   END  
										 END  
								END  
        FROM @fnAppGetGeneralMABPTValues gen,  
        dbo.LkpExtCMSBidServiceCategory bs  WITH(NOLOCK)
        LEFT JOIN @fnAppGetMABPTWS3 w3 
			ON w3.CostShareServiceCategoryCode = bs.CostShareServiceCategoryCode  
           AND (w3.CostShareServiceCategoryCode + w3.MeasUnitCode ) IN(SELECT CostShareServiceCategoryCode + MIN(MeasUnitCode)  
																	   FROM @fnAppGetMABPTWS3  
																	   GROUP BY CostShareServiceCategoryCode)  
        CROSS JOIN @SetupWS3 setup  

  -- Any additional groupings of Section 3  

			-- WS3
			DECLARE @WS3 TABLE
				(
				 Id INT,
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 CostShareServiceCategoryCode CHAR(4),	
				 ServiceCategory VARCHAR(200),	
				 SubServiceCategory	VARCHAR(200),
				 MeasUnitCode VARCHAR(10),	
				 INEffectiveDeductible DECIMAL(18, 10),
				 INUtilOrPMPM DECIMAL(18, 10),
				 INCostShareDesc VARCHAR(MAX),
				 INPreMOOPEffCostShare DECIMAL(18, 10),
				 INMOOPEffCostShare DECIMAL(18, 10),
				 INCostShare DECIMAL(18, 10),	
				 OONCostShare DECIMAL(18, 10),
				 OONCostShareDesc VARCHAR(MAX)
				)

			INSERT INTO @WS3
			SELECT DISTINCT RANK() OVER ( ORDER BY CostShareServiceCategoryCode ) AS Id,  
                            w3.PlanYearID,
							w3.ForecastID,	
							w3.CostShareServiceCategoryCode,
							w3.ServiceCategory,
							w3.SubServiceCategory,
							w3.MeasUnitCode,
							w3.INEffectiveDeductible,
							w3.INUtilOrPMPM,
							w3.INCostShareDesc,
							w3.INPreMOOPEffCostShare,
							w3.INMOOPEffCostShare,
							w3.INCostShare,
							w3.OONCostShare,
							w3.OONCostShareDesc
            FROM      @fnAppGetMABPTWS3 w3  
            WHERE (CostShareServiceCategoryCode  
                   + MeasUnitCode ) NOT IN (  
											SELECT DISTINCT  
													CostShareServiceCategoryCode  
													+ MIN(MeasUnitCode)  
											FROM @fnAppGetMABPTWS3  
											WHERE CostShareServiceCategoryCode IS NOT NULL --- MIKE adding not null
											GROUP BY CostShareServiceCategoryCode 
										   )
		------------------------------------------------------------------------------------------------------------------------

        INSERT  @ResultsTemp  
        SELECT DISTINCT Worksheet = 3 ,  
                        Cell = CASE setup.ColumnName  
								 WHEN 'CostShareServiceCategoryCode'   
								 THEN 'B' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'ServiceCategory'  
                                 THEN 'C' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'SubServiceCategory'  
                                 THEN 'D' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'MeasUnitCode'  
                                 THEN 'E' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'INEffectiveDeductible'  
                                 THEN 'F' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'INUtilOrPMPM'  
                                 THEN 'G' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'INCostShareDesc'  
                                 THEN 'H' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'INPreMOOPEffCostShare'  
            THEN 'I' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'INMOOPEffCostShare'  
                                 THEN 'J' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'OONCostShareDesc'  
                                 THEN 'M' + CAST(Value AS VARCHAR(5))  
                                 WHEN 'OONCostShare'  
                                 THEN 'N' + CAST(Value AS VARCHAR(5))  
                               END ,  
                        Value = CASE setup.ColumnName  
								  WHEN 'CostShareServiceCategoryCode'   
								  THEN CAST(CostShareServiceCategoryCode AS VARCHAR(MAX))  
                                  WHEN 'ServiceCategory'  
                                  THEN CAST(ServiceCategory AS VARCHAR(MAX))  
                                  WHEN 'SubServiceCategory'  
                                  THEN CAST(SubServiceCategory AS VARCHAR(MAX))  
                                  WHEN 'MeasUnitCode'  
                                  THEN CAST(MeasUnitCode AS VARCHAR(MAX))  
                                  WHEN 'INEffectiveDeductible'  
                                  THEN CAST(INEffectiveDeductible AS VARCHAR(MAX))  
                                  WHEN 'INUtilOrPMPM'  
                                  THEN CAST(INUtilOrPMPM AS VARCHAR(MAX))  
                                  WHEN 'INCostShareDesc'  
                                  THEN CAST(INCostShareDesc AS VARCHAR(MAX))  
                                  WHEN 'INPreMOOPEffCostShare'  
                                  THEN CAST(INPreMOOPEffCostShare AS VARCHAR(MAX))  
                                  WHEN 'INMOOPEffCostShare'  
                                  THEN CAST(INMOOPEffCostShare AS VARCHAR(MAX))  
                                  WHEN 'OONCostShareDesc'  
                                  THEN CASE WHEN gen.PlanTypeName <> 'HMO'  
                                            THEN CAST(OONCostShareDesc AS VARCHAR(MAX))  
                                       END  
                                  WHEN 'OONCostShare'  
                                  THEN CASE WHEN gen.PlanTypeName <> 'HMO'  
                                            THEN CAST(OONCostShare AS VARCHAR(MAX))  
                                       END  
                                END  
		FROM @fnAppGetGeneralMABPTValues gen ,  
             @WS3 ws3  
             INNER JOIN dbo.fnStringSplit('55,56,57,58,59,60,61,62,63,64',',') col 
				ON col.Id = ws3.Id  
             CROSS JOIN @SetupWS3 setup  

------ Part B Deductible in WKS3  

		  DECLARE @PartB VARCHAR(15)  
		  SELECT @PartB = (SELECT CASE WHEN IsPartBDeductible = 1 THEN
									   CASE WHEN INDeductible IS NULL
											THEN CombinedDeductible
											ELSE INDeductible
									   END
									   ELSE NULL
								  END
						   FROM dbo.Benefits_SavedBenefitOption sbo WITH(NOLOCK)
						   INNER JOIN dbo.SavedForecastSetup sfs WITH(NOLOCK)
								ON sfs.PlanInfoID = sbo.PlanInfoID
							   AND sfs.BenefitOptionID = sbo.BenefitOptionID
						   WHERE ForecastID = @ForecastID)  


  -- Combined Deductible   

        DECLARE @CombinedDeductible VARCHAR(15)   
        SET @CombinedDeductible =   
								(SELECT CASE WHEN PlanTypeName = 'PFFS' 
											  AND @IsMLA = 1  
											 THEN 'Medicare FFS'  
											 WHEN @IsCombinedDeductible = 1 
											  AND OONDeductible IS NOT NULL  
											 THEN CAST(CombinedDeductible AS VARCHAR(15))  
											 ELSE Null   
										END  
								 FROM @fnAppGetGeneralMABPTValues)  

		INSERT INTO @ResultsTemp  
		VALUES (3, 'H66', @CombinedDeductible)  

		IF (@IsCombinedDeductible = 1 AND IsNull(@PartB,0) <> 0)  --@PartB IS NOT null)  --update done by Abe 2/7/2022  
		INSERT INTO @ResultsTemp  
        VALUES (3, 'H66', 'Medicare FFS')  

	    IF ( @IsCombinedDeductible = 1)   
	    UPDATE @ResultsTemp  
	    SET   Value = NULL           
	    WHERE Worksheet = 3 
		  AND cell IN ('N66','K66')  


  ----------------------------------------------------------------------------------------------------------------  
  -- All information on WS4 ------------------------------------------------------------------------------- WS4 --  
  ----------------------------------------------------------------------------------------------------------------  
  -- Section 2c, 3, 4   
        INSERT  INTO @ResultsTemp  
        SELECT DISTINCT  
                setup.Worksheet ,  
                setup.Cell ,  
                Value = CASE setup.ValueName  
                            WHEN 'MarketingSales'  
                            THEN CAST(MarketingSales AS VARCHAR(MAX))  
                            WHEN 'DirectAdmin'  
                            THEN CAST(DirectAdmin AS VARCHAR(MAX))  
                            WHEN 'IndirectAdmin'  
                            THEN CAST(IndirectAdmin AS VARCHAR(MAX))  
                            WHEN 'QualityInitiatives'  
                            THEN CAST(QualityInitiatives AS VARCHAR(MAX))  
                            WHEN 'TaxesAndFees'  
                            THEN CAST(TaxesAndFees + InsurerFee AS VARCHAR(MAX))  
                            WHEN 'Reins'  
                            THEN CAST(Reins AS VARCHAR(MAX))  
                            WHEN 'Profit'  
                            THEN CAST(Profit AS VARCHAR(MAX))  
                            WHEN 'EGWPMSB'  
                            THEN CASE WHEN GroupPlan = 'Y'  
                                    THEN CAST(EGWPMSB AS VARCHAR(MAX))  
                                END  
                        END  
        FROM @fnAppGetGeneralMABPTValues header  
        CROSS JOIN @Setup setup                               

  --Section 2a  
			--WS4
			DECLARE @WS4 TABLE
				(
				 DualEligibleTypeID TINYINT,
				 ServiceCategoryCode CHAR(2),
				 CostShare DECIMAL(10, 6),
				 PercentCoveredAllowed DECIMAL(7, 6),
				 PercentCoveredCostShare DECIMAL(7, 6)
				)

			INSERT INTO @WS4
			SELECT DualEligibleTypeID,  
                   ServiceCategoryCode,  
                   CostShare = SUM(CostShare),  
				   PercentCoveredAllowed = SUM(PercentCoveredAllowed),  
				   PercentCoveredCostShare = SUM(PercentCoveredCostShare)  
            FROM dbo.fnAppGetMABPTWS4Percent(@ForecastID)  
            GROUP BY ServiceCategoryCode ,  
                     DualEligibleTypeID 
			-------------------------------------------------------------

			--Setup WS4
			DECLARE @SetupWS4 TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupWS4
			SELECT ColumnName = c.name
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE  o.name = 'fnAppGetMABPTWS4Percent'
		--------------------------------------------------------------------

        INSERT  @ResultsTemp  
        SELECT DISTINCT Worksheet = 4 ,  
						Cell = CASE setup.ColumnName  
									WHEN 'PercentCoveredAllowed'  
									THEN 'I' + CAST(bs.Wk4Row AS VARCHAR(5))  
									WHEN 'PercentCoveredCostShare'  
									THEN 'J' + CAST(bs.Wk4Row AS VARCHAR(5))  
							   END ,  
						Value = CASE setup.ColumnName  
									WHEN 'PercentCoveredAllowed'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk4Row = 20  
											  THEN CAST(0.0 * PercentCoveredAllowed AS VARCHAR(MAX))  
											  WHEN @PartBOnly = 1  
											   AND bs.Wk4Row = 21  
											  THEN CAST(0.05 * PercentCoveredAllowed  
														/ PercentCoveredAllowed AS VARCHAR(MAX))  
											  ELSE CASE WHEN bs.Wk4Row BETWEEN 31 AND 36  
														THEN NULL  
														ELSE CAST(PercentCoveredAllowed AS VARCHAR(MAX))  
												   END  
										 END  
									WHEN 'PercentCoveredCostShare'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk4Row = 20  
											  THEN CAST(0.0 * PercentCoveredCostShare AS VARCHAR(MAX))  
											  WHEN @PartBOnly = 1  
											   AND bs.Wk4Row = 21  
											  THEN CAST(0.05 * PercentCoveredCostShare  
														/ PercentCoveredCostShare AS VARCHAR(MAX))  
											  ELSE CASE WHEN bs.Wk4Row BETWEEN 31 AND 37  
														THEN NULL  
														ELSE CAST(PercentCoveredCostShare AS VARCHAR(MAX))  
												   END  
										 END  
								END  
        FROM    dbo.LkpExtCMSBidServiceCategory bs WITH(NOLOCK)
        LEFT JOIN @WS4 w4 
			ON w4.ServiceCategoryCode = bs.ServiceCategoryCode  
           AND w4.DualEligibleTypeID = 0  
        CROSS JOIN @SetupWS4 setup  

  -- Section 2b  
        INSERT  @ResultsTemp  
        SELECT DISTINCT Worksheet = 4,  
						Cell = CASE setup.ColumnName  
									WHEN 'CostShare'  
									THEN 'F' + CAST(bs.Wk4Row + 29 AS VARCHAR(5))  
									WHEN 'PercentCoveredAllowed'  
									THEN 'I' + CAST(bs.Wk4Row + 29 AS VARCHAR(5))  
									WHEN 'PercentCoveredCostShare'  
									THEN 'J' + CAST(bs.Wk4Row + 29 AS VARCHAR(5))  
							   END,  
						Value = CASE setup.ColumnName  
									WHEN 'CostShare'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk4Row + 29 IN(49, 50 ) 
											  THEN ''  
											  ELSE CAST(CostShare AS VARCHAR(MAX))  
										 END  
									WHEN 'PercentCoveredAllowed'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk4Row + 29 = 49  
											  THEN CAST(0.0 * PercentCoveredAllowed AS VARCHAR(MAX))  
											  WHEN @PartBOnly = 1  
											   AND bs.Wk4Row + 29 = 50  
											  THEN CAST(0.05 * PercentCoveredAllowed AS VARCHAR(MAX))  
											  ELSE CASE WHEN bs.Wk4Row + 29 BETWEEN 60 AND 65  
														THEN NULL  
														ELSE CAST(PercentCoveredAllowed AS VARCHAR(MAX))  
												   END  
										 END  
									WHEN 'PercentCoveredCostShare'  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk4Row + 29 = 49  
											  THEN CAST(0.0 * PercentCoveredAllowed AS VARCHAR(MAX))  
											  WHEN @PartBOnly = 1  
											   AND bs.Wk4Row + 29 = 50  
											  THEN CAST(0.05 * PercentCoveredAllowed AS VARCHAR(MAX))  
											  ELSE CASE WHEN bs.Wk4Row + 29 BETWEEN 60 AND 66  
														THEN NULL  
														ELSE CAST(PercentCoveredCostShare AS VARCHAR(MAX))  
												   END  
										 END  
								END  
        FROM dbo.LkpExtCMSBidServiceCategory bs WITH(NOLOCK) 
        LEFT JOIN @WS4 w4 
			ON w4.ServiceCategoryCode = bs.ServiceCategoryCode  
           AND w4.DualEligibleTypeID = 1  
        CROSS JOIN @SetupWS4 setup 

		-- Surplus Deficit

		DECLARE @WS4SD TABLE
		(
		ServiceCategoryCode CHAR(2),
		SurplusDeficit DECIMAL (14,8)
		)

		INSERT INTO @WS4SD
			SELECT  ServiceCategoryCode,  
                 SurplusDeficit=SurplusDeficit  
				FROM dbo.fnAppGetMABPTWS4SurplusDeficitAllocation(@ForecastID)  
            GROUP BY ServiceCategoryCode,SurplusDeficit

				DECLARE @SetupWS4_SuplusDeficit TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

	INSERT INTO @SetupWS4_SuplusDeficit
			SELECT ColumnName = c.name
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE  o.name = 'fnAppGetMABPTWS4SurplusDeficitAllocation'

			 INSERT  @ResultsTemp  
        SELECT DISTINCT Worksheet = 4 ,  
						Cell = CASE setup.ColumnName  
									WHEN @SurplusDeficitString  
									THEN 'U' + CAST(bs.Wk4Row +58 AS VARCHAR(5))  
									  END ,  
						Value = CASE setup.ColumnName  
									WHEN @SurplusDeficitString  
									THEN CASE WHEN @PartBOnly = 1  
											   AND bs.Wk4Row = 20  
											  THEN CAST(0.0 * SurplusDeficit AS VARCHAR(MAX))  
											  WHEN @PartBOnly = 1  
											  AND bs.Wk4Row = 21  
											  THEN CAST(0.05 * SurplusDeficit  
														/ SurplusDeficit AS VARCHAR(MAX))  
											  ELSE
									 		    CAST(ISNULL(SurplusDeficit,0)  AS VARCHAR(MAX))
											 END 													 
								END  
        FROM    dbo.LkpExtCMSBidServiceCategory bs WITH(NOLOCK)
        LEFT JOIN @WS4SD w4 
			ON w4.ServiceCategoryCode = bs.ServiceCategoryCode  
            CROSS JOIN @SetupWS4_SuplusDeficit Setup 
            WHERE bs.Wk4Row < 37

  --section 6
			-- Setup ESRD Subidy
			DECLARE @SetupESRD TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupESRD
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE o.Name = 'LoadPlanESRDSubsidyInfo'
		--------------------------------------------------------------

        INSERT  INTO @ResultsTemp  
	    SELECT DISTINCT Worksheet = 4 ,  
						Cell = CASE setup.ColumnName  
									WHEN 'CMSCapitation'  
										THEN 'F121'   
									WHEN 'MedicalExpense'  
										THEN 'F123'  
									WHEN 'NonBenefitExpense'  
										THEN 'F124'  
									WHEN 'CostShareReductions'  
										THEN 'M124'  
									WHEN 'AddedBenefits'  
										THEN 'M125'  
							   END,  
						Value = CASE setup.ColumnName  
									WHEN 'CMSCapitation'  
										THEN ESRDSub.CMSCapitation  
									WHEN 'MedicalExpense'  
										THEN ESRDSub.MedicalExpense  
									WHEN 'NonBenefitExpense'  
										THEN ESRDSub.NonBenefitExpense  
									WHEN 'CostShareReductions'  
										THEN ESRDSub.CostShareReductions  
									WHEN 'AddedBenefits'  
										THEN ESRDSub.AddedBenefits  
							    END  
		FROM dbo.LoadPlanESRDSubsidyInfo ESRDSub WITH(NOLOCK)
		CROSS JOIN @SetupESRD setup  
		WHERE ESRDSub.PlanInfoID = (SELECT DISTINCT PlanInfoID 
									FROM dbo.SavedForecastSetup  WITH (NOLOCK)
									WHERE ForecastID = @ForecastID)  
		-----------------------------------------------------------

			--Setup Medicaid
			DECLARE @SetupMdcaid TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupMdcaid
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE o.Name = 'SavedPlanProjectedMedicaidRevenue'
		----------------------------------------------------------------------

        INSERT  INTO @ResultsTemp  
		SELECT DISTINCT Worksheet = 4 ,  
					    Cell = CASE setup.ColumnName  
								 WHEN 'MedicaidProjectedRevenue'  
								 THEN 'R123'   
								 WHEN 'MedicaidProjectedCostBenefitExpense'  
								 THEN 'R125'  
								 WHEN 'MedicaidProjectedCostNonBenefitExpense'  
								 THEN 'R126'  
						       END,  
						Value = CASE setup.ColumnName  
								 WHEN 'MedicaidProjectedRevenue'  
								 THEN med.MedicaidProjectedRevenue  
								 WHEN 'MedicaidProjectedCostBenefitExpense'  
								 THEN med.MedicaidProjectedCostBenefitExpense  
								 WHEN 'MedicaidProjectedCostNonBenefitExpense'  
								 THEN med.MedicaidProjectedCostNonBenefitExpense  
								END  
		FROM dbo.SavedPlanProjectedMedicaidRevenue med WITH(NOLOCK) 
		CROSS JOIN @SetupMdcaid setup  
		WHERE med.ForecastID = @ForecastID  
		------------------------------------------------------------------------------------------------------
				--Related Party Medical Benefits
				DECLARE @RPMedBen TABLE
					(
					 ForecastID INT,
					 RelatedPartyMedicalBenefit DECIMAL(6,2)
					)

				INSERT INTO @RPMedBen
				SELECT spa.ForecastID,
					   lkp.RelatedPartyMedicalBenefit 
				FROM dbo.SavedPlanAddedBenefits spa WITH(NOLOCK)  
				LEFT JOIN dbo.LkpIntAddedBenefitExpenseDetail lkp WITH(NOLOCK)
					ON spa.AddedBenefitTypeID=lkp.AddedBenefitTypeID   
				WHERE spa.ForecastID = @ForecastID 
			      AND spa.IsHidden <> 1
				----------------------------------------------------

				--Related Party NonMedical Benefits
				DECLARE @RPNMedBen TABLE
					(
					 ForecastID INT,
					 RelatedPartyNonMedicalBenefit DECIMAL(6,2)
					)

				INSERT INTO @RPNMedBen
				SELECT spa.ForecastID,
					   lkp.RelatedPartyNonMedicalBenefit
				FROM dbo.SavedPlanAddedBenefits spa  WITH(NOLOCK)
				LEFT JOIN dbo.LkpIntAddedBenefitExpenseDetail lkp WITH(NOLOCK)
					ON spa.AddedBenefitTypeID=lkp.AddedBenefitTypeID   
				WHERE spa.ForecastID = @ForecastID  
				  AND spa.IsHidden <> 1  

				INSERT INTO @RPNMedBen
					SELECT ForecastID, MARelatedPartyCoreNBEPMPM
					FROM dbo.CalcPlanAdminBlend cpab WITH(NOLOCK) --Legal Entity Changes RP Core NBE
					where cpab.forecastid = @ForecastID
		----------------------------------------------------------------------------------------------------

        INSERT  INTO @ResultsTemp  
		--- values of z4 to the BPT WS4   
		VALUES (4, 'M104', (SELECT SUM(RelatedPartyMedicalBenefit) AS RelatedPartyMedicalBenefit 
						    FROM @RPMedBen))  

        ---  Values of z5 to the BPT WS4  
		INSERT  INTO @ResultsTemp  
        VALUES (4, 'M105', (SELECT  SUM(RelatedPartyNonMedicalBenefit) AS RelatedPartyNonMedicalBenefit  
							FROM @RPNMedBen))  

  -- The following require a hard insert:  

        INSERT  INTO @ResultsTemp  
		VALUES  ( 4, 'I37', 0 )  

		INSERT  INTO @ResultsTemp  
        VALUES  ( 4, 'I66', 0 )  

        SET @row = 49  
        WHILE @row <= 66  
            BEGIN  
                INSERT  INTO @ResultsTemp  
                VALUES  ( 4, 'K' + CAST(@row AS VARCHAR(5)), 0 )  
                SET @row = @row + 1  
            END  

  ----------------------------------------------------------------------------------------------------------------  
  -- All information on WS5 ------------------------------------------------------------------------------- WS5 --  
  ----------------------------------------------------------------------------------------------------------------  
  -- Section 2, 3, 4, 5, 8  
        INSERT  INTO @ResultsTemp  
        SELECT DISTINCT setup.Worksheet ,  
						setup.Cell ,  
						Value = CASE setup.ValueName  
									WHEN 'SecondaryPayerAdjustment'  
									THEN CAST(SecondaryPayerAdjustment AS VARCHAR(MAX))  
									WHEN 'EstimatedPlanBidComponent'  
									THEN CASE WHEN PlanTypeName = 'RPPO'  
											  THEN CASE WHEN ISNULL(EstimatedPlanBidComponent, 0) = 0  
														THEN '=E19'  
														ELSE CAST(EstimatedPlanBidComponent AS VARCHAR(MAX))  
													END  
										 END  
									WHEN 'CMSQualityStars'  
									THEN CASE WHEN CMSEnrollmentType = 'LOW'  
											  THEN ''  
											  WHEN CMSQualityStars <> 0  
											  THEN CAST(CMSQualityStars AS VARCHAR(MAX))  
										 END  
									WHEN 'CMSEnrollmentType'  
									THEN CASE WHEN CMSEnrollmentType = 'NEW'  
											  THEN 'New contract under existing parent org'  
											  WHEN CMSEnrollmentType = 'LOW'  
											  THEN 'Low'  
										 END  
									WHEN 'ESRDMembers'  
									THEN CAST(ESRDMembers AS VARCHAR(MAX))  
									WHEN 'HospiceMembers'  
									THEN CAST(HospiceMembers AS VARCHAR(MAX))  
								END  
        FROM @fnAppGetGeneralMABPTValues header  
        CROSS JOIN @Setup setup  

  -- Section 6 
			--WS5
			DECLARE @WS5 TABLE
				(
				 Id	INT,
				 PlanYearID	SMALLINT,
				 ForecastID INT,
				 CountyCode CHAR(5),
				 StateTerritoryShortName CHAR(2),
				 CountyName VARCHAR(30),
				 MemberMonths DECIMAL (28,6),
				 RiskFactor DECIMAL(9, 6),
				 RiskRate DECIMAL(8, 4),
				 AdjRiskRate DECIMAL(12, 8), 
				 ISARScale DECIMAL(16, 15),
				 ISARBid DECIMAL(10, 6),
				 RiskPaymentRateA DECIMAL (10, 6),
				 RiskPaymentRateB DECIMAL (10, 6),
				 CMSIPCostShare DECIMAL(7, 6),
				 CMSSNFCostShare DECIMAL(7, 6),
				 PartBCostShare DECIMAL(7, 6),
				 CMSIPFFSCosts DECIMAL(8, 4),
				 CMSSNFCosts DECIMAL (8, 4),
				 PartBCosts DECIMAL (8, 4),
				 CMSPartAEquivCostShare DECIMAL (8, 4),
				 CMSPartBEquivCostShare DECIMAL (8, 4)
				)

			INSERT INTO @WS5
			SELECT DISTINCT  RANK() OVER ( ORDER BY CASE WHEN countycode IS NULL 
														 THEN 1 
														 ELSE 2 
													END, CountyCode DESC) AS Id ,                            
							 PlanYearID,
							 ForecastID,
							 CountyCode,
							 StateTerritoryShortName,
							 CountyName,
							 MemberMonths,
							 RiskFactor,
							 RiskRate,
							 AdjRiskRate, 
							 ISARScale,
							 ISARBid,
							 RiskPaymentRateA,
							 RiskPaymentRateB,
							 CMSIPCostShare,
							 CMSSNFCostShare,
							 PartBCostShare,
							 CMSIPFFSCosts,
							 CMSSNFCosts,
							 PartBCosts,
							 CMSPartAEquivCostShare,
							 CMSPartBEquivCostShare 
            FROM dbo.fnAppGetMABPTWS5(@ForecastID)
			----------------------------------------

			--Setup WS5
			DECLARE @SetupWS5 TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupWS5
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE  o.Name = 'fnAppGetMABPTWS5' 

		-------------------------------------------------------------------------
        INSERT  INTO @ResultsTemp  
        SELECT DISTINCT Worksheet = 5 ,  
						Cell = CASE setup.ColumnName  
									WHEN 'CountyCode'  
									THEN 'B' + CAST(Id + 37 AS VARCHAR(5))  
									WHEN 'MemberMonths'  
									THEN 'E' + CAST(Id + 37 AS VARCHAR(5))  
									WHEN 'RiskFactor'  
									THEN 'F' + CAST(Id + 37 AS VARCHAR(5))  
								END ,  
						Value = CASE setup.ColumnName  
									WHEN 'CountyCode'  
									THEN '''' + CAST(CountyCode AS VARCHAR(MAX))  
									WHEN 'MemberMonths'  
									THEN CAST(MemberMonths AS VARCHAR(MAX))  
									WHEN 'RiskFactor'  
									THEN CAST(RiskFactor AS VARCHAR(MAX))  
								END  
        FROM @WS5 w5  
        CROSS JOIN @SetupWS5 setup  
		-------------------------------------------------------------------------

        INSERT  INTO @ResultsTemp  
                SELECT  '5' ,  
                        'G31' ,  
                        CASE ( SELECT   PlanTypeName  
                               FROM     @fnAppGetGeneralMABPTValues  
                             )  
                          WHEN 'RPPO' THEN 'NO'  
                          ELSE NULL  
                        END  

  -- The following require a hard insert:  
        DECLARE @ProjNonDEMM DECIMAL(28, 6) ,  
				@ProjNonDERS DECIMAL(16, 6)  

		SELECT  @ProjNonDEMM = SUM(ProjectedMemberMonths)  
        FROM    dbo.fnPlanCountyProjectedMemberMonths(@ForecastID, 0)  

		SELECT  @ProjNonDERS = ProjNonDE#RiskScore  
        FROM    dbo.fnAppGetBenchmarkSummary(@ForecastID)  

        INSERT INTO @ResultsTemp  
        SELECT '5' ,  
               'F12' ,  
               CAST(@ProjNonDEMM AS VARCHAR(MAX))  

		INSERT INTO @ResultsTemp  
        SELECT '5' ,  
               'F15' ,  
               CAST(@ProjNonDERS AS VARCHAR(MAX))  

  ----------------------------------------------------------------------------------------------------------------  
  -- All information on WS6 ------------------------------------------------------------------------------- WS6 --  
  ----------------------------------------------------------------------------------------------------------------  
  -- Section 2, 3, 4, 5  
        INSERT  INTO @ResultsTemp  
        SELECT DISTINCT setup.Worksheet ,  
						setup.Cell ,  
						Value = CASE setup.ValueName  
									WHEN 'PartBPremiumBuyDown'  
									THEN CAST(PartBPremiumBuyDown AS VARCHAR(MAX))  
									WHEN 'TotalReduceCostShare'  
									THEN CAST(TotalReduceCostShare AS VARCHAR(MAX))  
									WHEN 'TotalOtherSuppBen'  
									THEN CAST(TotalOtherSuppBen AS VARCHAR(MAX))  
									WHEN 'ContactName'  
									THEN CAST(ContactName AS VARCHAR(MAX))  
									WHEN 'ContactPhone'  
									THEN CAST(ContactPhone AS VARCHAR(MAX))  
									WHEN 'ContactEmail'  
									THEN CAST(ContactEmail AS VARCHAR(MAX))  
									WHEN 'CertifyingName'  
								    THEN CAST(CertifyingName AS VARCHAR(MAX))  
									WHEN 'CertifyingPhone'  
									THEN CAST(CertifyingPhone AS VARCHAR(MAX))  
									WHEN 'CertifyingEmail'  
									THEN CAST(CertifyingEmail AS VARCHAR(MAX))  
									WHEN 'SecondaryContactName'  
									THEN CAST(SecondaryContactName AS VARCHAR(MAX))  
									WHEN 'SecondaryContactPhone'  
									THEN CAST(SecondaryContactPhone AS VARCHAR(MAX))  
									WHEN 'SecondaryContactEmail'  
									THEN CAST(SecondaryContactEmail AS VARCHAR(MAX))  
									WHEN 'RxBasicPremium'  
									THEN CASE WHEN MAPD = 'Y'  
											   AND GroupPlan = 'N'  
											  THEN CAST(RxBasicPremium AS VARCHAR(MAX))  
										 END  
									WHEN 'RxBasicBuyDown'  
									THEN CASE WHEN MAPD = 'Y'  
											   AND GroupPlan = 'N'  
											  THEN CAST(RxBasicBuyDown AS VARCHAR(MAX))  
										 END  
									WHEN 'RxSuppPremium'  
									THEN CASE WHEN MAPD = 'Y'  
											   AND GroupPlan = 'N'  
											  THEN CAST(RxSuppPremium AS VARCHAR(MAX))  
										 END  
									WHEN 'RxSuppBuyDown'  
									THEN CASE WHEN MAPD = 'Y'  
											   AND GroupPlan = 'N'  
											  THEN CAST(RxSuppBuyDown AS VARCHAR(MAX))  
										 END  
									WHEN 'PartDTargetPremiumMessage'  
									THEN CASE WHEN MAPD = 'Y'  
											   AND GroupPlan = 'N'  
											  THEN RTRIM(CAST(PartDTargetPremiumMessage AS VARCHAR(MAX)))  
										 END  
								END  
        FROM @fnAppGetGeneralMABPTValues header  
        CROSS JOIN @Setup setup  

  -- The following require a hard insert:  
        DECLARE @H41 VARCHAR(MAX)  
        SELECT  @H41 = 'Admin='   
                + '                 
                                                                                                                                             '  
                + '.                               ' + 'MA'  
                + '        |       ' + 'Rx' + '    '
				+ 'Marketing=' + '         ' + CAST(MAMarketingExp AS VARCHAR(MAX))  
                + '  |  ' + CAST(PDMarketingExp AS VARCHAR(MAX))  
                + '                                          ' + 'Direct='  
                + '                   ' + CAST(DirectAdmin AS VARCHAR(MAX))  
                + '  |  ' + CAST(PDDirectAdminExp AS VARCHAR(MAX))  
                + '                                          ' + 'Indirect='  
                + '               ' + CAST(MAIndirectAdminExp AS VARCHAR(MAX))  
                + '  |  ' + CAST(PDIndirectAdminExp AS VARCHAR(MAX))  
                + '                                          ' + 'Quality='  
                + '                '  
                + CAST(QualityInitiatives AS VARCHAR(MAX)) + '  |  '  
                + CAST(PDQualityAdminExp AS VARCHAR(MAX))  
                + '                                          '  
                + 'TaxesandFees=' + '   '  
                + CAST(MATaxesAndFeesAdminExp + InsurerFee AS VARCHAR(MAX))  
                + '  |  ' + CAST(PDTaxesAndFeesAdminExp AS VARCHAR(MAX))  
                + '                                          '  
        FROM @fnAppGetGeneralMABPTValues  

        INSERT INTO @ResultsTemp  
                SELECT '6' ,  
                       'H41' ,  
                       @H41  

  ----------------------------------------------------------------------------------------------------------------  
  -- All information on WS7 ------------------------------------------------------------------------------- WS7 --  
  ----------------------------------------------------------------------------------------------------------------  

			--Setup WS7
			DECLARE @SetupWS7 TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupWS7
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c ON o.object_id = c.object_id  
            WHERE o.Name = 'fnAppGetMABPTWS7'
		-------------------------------------------------------------------------------

		INSERT  INTO @ResultsTemp  
        SELECT DISTINCT Worksheet = 7 ,  
						Cell = CASE setup.ColumnName  
									WHEN 'Description'  
									THEN 'C'  
										+ CAST(16 + ( PlanPackageID - 1 ) AS VARCHAR(5))  
									WHEN 'TotalExpense'  
									THEN 'G'  
										+ CAST(16 + ( PlanPackageID - 1 ) AS VARCHAR(5))  
									WHEN 'TotalGainLoss'  
									THEN 'H'  
										+ CAST(16 + ( PlanPackageID - 1 ) AS VARCHAR(5))  
									WHEN 'TotalProjectedMemberMonths'  
									THEN 'J'  
										+ CAST(16 + ( PlanPackageID - 1 ) AS VARCHAR(5))  
							   END,  
						Value = CASE setup.ColumnName  
									WHEN 'Description'  
									THEN CAST([Description] AS VARCHAR(MAX))  
									WHEN 'TotalExpense'  
									THEN CAST(TotalExpense AS VARCHAR(MAX))  
									WHEN 'TotalGainLoss'  
									THEN CAST(TotalGainLoss AS VARCHAR(MAX))  
									WHEN 'TotalProjectedMemberMonths'  
									THEN CAST(TotalProjectedMemberMonths AS VARCHAR(MAX))  
								END  
        FROM @fnAppGetMABPTWS7  
        CROSS JOIN @SetupWS7 setup  
		---------------------------------------------------------------------------------

			--Setup WS7sec2
			DECLARE @SetupWS7sec2 TABLE
				(
				 ColumnName VARCHAR(MAX)
				)

			INSERT INTO @SetupWS7sec2
			SELECT ColumnName = c.name  
            FROM sys.objects o  
            FULL OUTER JOIN sys.columns c 
				ON o.object_id = c.object_id  
            WHERE  o.Name = 'fnAppGetMABPTWS7Sec2'
		-----------------------------------------------------------------------------------

        INSERT  INTO @ResultsTemp
        SELECT DISTINCT Worksheet = 7 ,  
						Cell = CASE setup.ColumnName  
									WHEN 'AllowedAverageCost'  
									THEN 'D'  
										+ CAST(16 + ( w7.PlanPackageID - 1 ) AS VARCHAR(5))  
									WHEN 'EnrolleeAverageCostShare'  
									THEN 'E'  
										+ CAST(16 + ( w7.PlanPackageID - 1 ) AS VARCHAR(5))  
							   END ,  
						Value = CASE setup.ColumnName  
									WHEN 'AllowedAverageCost'  
									THEN CAST(SUM(AllowedAverageCost) / 12 AS VARCHAR(MAX))  
									WHEN 'EnrolleeAverageCostShare'  
									THEN CAST(SUM(EnrolleeAverageCostShare) / 12 AS VARCHAR(MAX))  
								END  
        FROM @fnAppGetMABPTWS7 w7  
        CROSS APPLY (SELECT PlanYearID,
							ForecastID,       
							PlanPackageID,
							PackageIndex,
							[Name],
							[Description],
							TotalExpense,
							TotalGainLoss,
							TotalProjectedMemberMonths,
							ServiceCategory,    
							PricingComponentDescription,
							AllowedUtilizationType,
							AllowedUtilzationPer1000,    
							AllowedAverageCost,
							MeasurementUnitCode,
							EnrolleeCostShareUtilization,
							EnrolleeAverageCostShare,
							AllowedPMPM,
							EnrolleePMPM,
							Expense,
							GainLossMargin,
							Premium,
							ProjectedMemberMonths,
                            ROW_NUMBER() OVER ( ORDER BY ServiceCategory ) RowNumber  
                     FROM dbo.fnAppGetMABPTWS7Sec2(@ForecastID, w7.PackageIndex)  
                     ) w72
        CROSS JOIN @SetupWS7sec2 setup  
        GROUP BY setup.ColumnName ,  
                 w7.PlanPackageID  

  --SECTION IV Added 2014  
        INSERT INTO @ResultsTemp  
        SELECT DISTINCT setup.Worksheet ,  
						setup.Cell ,  
						Value = CASE setup.ValueName  
									WHEN 'OSBNetMedicalExpenses'  
									THEN CAST(OSBNetMedicalExpenses AS VARCHAR(MAX))  
									WHEN 'OSBNonBenefitExpenses'  
									THEN CAST(OSBNonBenefitExpenses AS VARCHAR(MAX))  
									WHEN 'OSBPremium'  
									THEN CAST(OSBPremium AS VARCHAR(MAX))  
									WHEN 'OSBMembermonths'  
									THEN CAST(OSBMembermonths AS VARCHAR(MAX))  
								END  
        FROM @fnAppGetGeneralMABPTValues header  
        CROSS JOIN @Setup setup  

        --truncate logic added
		UPDATE @ResultsTemp SET Value=SUBSTRING(Value,1,999) WHERE Worksheet=3 AND cell >= 'H25' AND cell LIKE 'H%'
        UPDATE @ResultsTemp SET Value=SUBSTRING(Value,1,999) WHERE Worksheet=3 AND cell >= 'M25' AND cell LIKE 'M%'

  INSERT  @Results  
        SELECT DISTINCT Worksheet,  
						Cell,  
						Value 
        FROM @ResultsTemp  
        WHERE Value IS NOT NULL  
          AND Cell IS NOT NULL  
          AND Worksheet IS NOT NULL  
        ORDER BY Worksheet ,  
                 Cell  

 RETURN  
    END
GO
