-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	dbo.TrendAdj_spCalcSAR
--
-- CREATOR:			<PERSON><PERSON><PERSON><PERSON>
--
-- CREATED DATE:	2024-Jul-30
--
-- DESCRIPTION:		This sp is executed by the TrendAdj_spRefresh stored procedure; it populates the TrendAdj_SAR_CalcKeyInfo_stage table.
--		
-- PARAMETERS:
--  Input  :		@SessionID
--					@PlanInfoIdList
--					@LastUpdateByID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			LkpIntBenefitCategory
--					Trend_CalcPopulationHistorical
--					TrendAdj_SAR_LkpMethodology
--					TrendAdj_SAR_SavedSuperBIDE
--					TrendAdj_SAR_SavedUCDB
--					TrendAdj_SAR_SavedOutlier
--					TrendAdj_SAR_CalcKeyInfo_stage
--
--  Write:			TrendAdj_SAR_CalcKeyInfo_stage
--
-- VIEWS: 
--	Read:			vwSAMCrosswalks
--					vwPlanInfo
--
-- FUNCTIONS:		fngetbidyear
--					Trend_fnCalcStringToTable
--					Trend_fnSafeDivide
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-Jul-30      1           Initial Version                                                     Aleksandar Dimitrijevic
-- 2024-Oct-10      2           Added NOLOCK & optimazation	                                        Kumar Jalendran
-- 2024-Oct-17      3           Dropped all temp tables at end of procedure                         Kumar Jalendran
-- 2024-Oct-25      4           Updated the error loging to dbo.TrendAdjLog                         Kumar Jalendran
-- 2024-Oct-29		5			Increased division accurracy by using Trend_fnSafeDivide in calcs that feed TrendAdj_SAR_CalcKeyInfo_stage	Aleksandar Dimitrijevic
-- 2024-Nov-11		6			Replace CPS with PlanInfoID in permanent tables						Michael Manes
-- 2024-Nov-13		7			Log description corrected while insert TrendAdjLog					Kumar Jalendran
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[TrendAdj_spCalcSAR]

--Input parameters and data types
@SessionID VARCHAR(19),
@PlanInfoIdList	VARCHAR(MAX),
@LastUpdateByID CHAR(7)

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY


            BEGIN TRANSACTION transaction_TrendAdj_spCalcSAR;

			-- Declare local variables to use in place of input parameters to negate parameter sniffing.
			DECLARE @xPlanList VARCHAR(MAX) = @PlanInfoIdList;
			DECLARE @xLastUpdateByID CHAR(7) = @LastUpdateByID;
			DECLARE @xSessionID VARCHAR(19) = @SessionID;

            -- Declare variables
			DECLARE @BaseYear INT = (SELECT dbo.fngetbidyear() - 2);
			DECLARE @FullAdjDampening CHAR(30)='Full Adjustment (No Dampening)';
			DECLARE @CredDampening CHAR(20)='Credibility-Weighted';
			DECLARE @UserDampening CHAR(12)='User-Defined';
			DECLARE @DefaultSplit CHAR(20) = 'Default (Squareroot)';
			DECLARE @OnlyCostSplit CHAR(9) = 'Only Cost';
			DECLARE @OnlyUseSplit CHAR(8) = 'Only Use';
			DECLARE @UserDefinedSplit CHAR(12) = 'User-Defined';
			DECLARE @max_segment_cnt TINYINT;
			DECLARE @cnt TINYINT = 2;

			-- Create a temp table with Reporting Categories
			DROP TABLE IF EXISTS #RepCat;

			CREATE TABLE #RepCat 
				(
				 ReportingCategory VARCHAR(50)
				);
				
			INSERT INTO #RepCat
			SELECT DISTINCT ReportingCategory
					   FROM dbo.LkpIntBenefitCategory WITH(NOLOCK)
					  WHERE ReportingCategory IS NOT NULL;

			-- Create a temp three year crosswalk table from vwSAMCrosswalks
			DROP TABLE IF EXISTS #vwSAMCrosswalks;

			SELECT DISTINCT BaseYearCPS AS 'BaseCPS'
						   ,CurrentYearCPS AS 'CurrentCPS'
						   ,BidYearCPS AS 'BidCPS'
						   ,SSStateCountyCD
					  INTO #vwSamCrosswalks
					  FROM dbo.vwSAMCrosswalks WITH(NOLOCK)
					 WHERE BidYearPlanInfoID IN(SELECT Val AS PlanInfoID
												  FROM dbo.Trend_fnCalcStringToTable (@xPlanList, ',', 1))
					   AND BidYearCPS IS NOT NULL
					   AND IsActive = 1;

			-- Create a temp table with Base Year Plans
			DROP TABLE IF EXISTS #BasePlans;

			SELECT DISTINCT BaseCPS
					   INTO #BasePlans
					   FROM #vwSamCrosswalks WITH(NOLOCK)
					  WHERE BaseCPS IS NOT NULL;

			-- Create a temp table with Base Year Plans
			DROP TABLE IF EXISTS #CurrentPlans;

			SELECT DISTINCT CurrentCPS
					   INTO #CurrentPlans
					   FROM #vwSamCrosswalks WITH(NOLOCK)
					  WHERE CurrentCPS IS NOT NULL;

			--Create a temp table with Bid Year Plans
			DROP TABLE IF EXISTS #BidPlans;

			SELECT DISTINCT BidCPS
					   INTO #BidPlans
					   FROM #vwSamCrosswalks WITH(NOLOCK)
					  WHERE BidCPS IS NOT NULL;

			-- Create a list of distinct plans from base/current/bid crosswalks
			DROP TABLE IF EXISTS #CPS;
				
			SELECT BaseCPS AS CPS
			  INTO  #CPS
			  FROM #BasePlans WITH(NOLOCK)

			UNION ALL

			SELECT CurrentCPS AS CPS
			  FROM #CurrentPlans WITH(NOLOCK)
				
			UNION ALL
				
			SELECT BidCPS AS CPS
			  FROM #BidPlans WITH(NOLOCK);

			DROP TABLE IF EXISTS #CPSs;
				
			SELECT DISTINCT CPS
					   INTO #CPSs
				       FROM #CPS WITH(NOLOCK);

			--Pull in plan level crosswalks from base year to current year
			DROP TABLE IF EXISTS #Base2CurrentCrosswalk;

			SELECT DISTINCT BaseCPS
						   ,CurrentCPS
				       INTO #Base2CurrentCrosswalk
				       FROM #vwSamCrosswalks WITH(NOLOCK)
					  WHERE CurrentCPS IS NOT NULL
						AND BaseCPS IS NOT NULL;

			-- counting the number of Base CPSs assigned to each Current CPS (to prevent duplication when joining with Super Bide data set)
			DROP TABLE IF EXISTS #Base2Current;

			SELECT BaseCPS
				  ,CurrentCPS
				  ,ROW_NUMBER() OVER(Partition BY BaseCPS Order BY CurrentCPS) as Segment
			  INTO #Base2Current
			  FROM #Base2CurrentCrosswalk WITH(NOLOCK);

			--Pull in plan level crosswalks from current year to bid year
			DROP TABLE IF EXISTS #Current2BidCrosswalk;

			SELECT DISTINCT CurrentCPS
						   ,BidCPS
					   INTO #Current2BidCrosswalk
					   FROM #vwSamCrosswalks WITH(NOLOCK)
					  WHERE CurrentCPS IS NOT NULL
						AND BidCPS IS NOT NULL;

			--Pull in plan level crosswalks from base year to bid year
			DROP TABLE IF EXISTS #Base2BidCrosswalk;

			SELECT DISTINCT BaseCPS
						   ,BidCPS
					   INTO #Base2BidCrosswalk
					   FROM #vwSamCrosswalks WITH(NOLOCK)
					  WHERE BidCPS IS NOT NULL
						AND BaseCPS IS NOT NULL;

			--  counting the number of Base CPSs assigned to each Bid CPS (to prevent duplication when joining with Super Bide data set)
			DROP TABLE IF EXISTS #Base2Bid;

			SELECT BaseCPS
				  ,BidCPS
				  ,ROW_NUMBER() OVER(Partition BY BaseCPS Order BY BidCPS) as Segment
			  INTO #Base2Bid
			  FROM #Base2BidCrosswalk WITH(NOLOCK);

			-- Create a temp base year SA table from vwSAMCrosswalks
			DROP TABLE IF EXISTS #Base_SA;

			SELECT DISTINCT xw.BaseYearCPS AS CPS, 
							xw.SSStateCountyCD
					   INTO #Base_SA
					   FROM dbo.vwSamCrosswalks xw WITH(NOLOCK)
				 INNER JOIN dbo.vwPlanInfo vpi WITH(NOLOCK)
						 ON xw.BaseYearCPS = vpi.CPS 
						 OR xw.CurrentYearCPS = vpi.CPS 
						 OR xw.BidYearCPS = vpi.CPS
				 INNER JOIN #CPSs cs WITH(NOLOCK)
						 ON cs.CPS = vpi.CPS
					  WHERE vpi.PlanYear = @BaseYear
					    AND xw.BaseYearCPS IS NOT NULL;

			-- Create a temp current year SA table from vwSAMCrosswalks
			DROP TABLE IF EXISTS #Current_SA;

			SELECT DISTINCT xw.CurrentYearCPS AS CPS, 
							xw.SSStateCountyCD
					   INTO #Current_SA
					   FROM dbo.vwSAMCrosswalks xw WITH(NOLOCK)
				 INNER JOIN dbo.vwPlanInfo vpi WITH(NOLOCK)
						 ON xw.BaseYearCPS = vpi.CPS 
						 OR xw.CurrentYearCPS = vpi.CPS 
						 OR xw.BidYearCPS = vpi.CPS
				 INNER JOIN #CurrentPlans cs WITH(NOLOCK)
					     ON cs.CurrentCPS = vpi.CPS
					  WHERE vpi.PlanYear = @BaseYear + 1 
					    AND xw.CurrentYearCPS IS NOT NULL;

			-- Create a temp current year SA table from vwSAMCrosswalks
			DROP TABLE IF EXISTS #Bid_SA;

			SELECT DISTINCT xw.BidYearCPS AS CPS, 
							xw.SSStateCountyCD
					   INTO #Bid_SA
					   FROM dbo.vwSAMCrosswalks xw WITH(NOLOCK)
			     INNER JOIN dbo.vwPlanInfo vpi WITH(NOLOCK)
						 ON xw.BaseYearCPS = vpi.CPS 
						 OR xw.CurrentYearCPS = vpi.CPS 
						 OR xw.BidYearCPS = vpi.CPS
				 INNER JOIN #BidPlans bp WITH(NOLOCK)
					     ON bp.BidCPS = vpi.CPS
					  WHERE vpi.PlanYear =  @BaseYear + 2  
					    AND xw.IsActive = 1 
					    AND xw.BidYearCPS IS NOT NULL;

			-- Create a table with details from Super Bide and determine SA/OON counties
			DROP TABLE IF EXISTS #TrendAdj_SAR_SavedSuperBIDE;

			SELECT sb.CPS,
				   pc.CurrentCPS,
				   pc.BidCPS,
				   sb.SSStateCountyCD, 
				   sb.CMSMemberMonths, 
				   TotalAllowed = CASE WHEN CMSMemberMonths = 0 THEN 0
									   ELSE AllowedNonSQS + AllowedOtherPartB
								  END
			  INTO #TrendAdj_SAR_SavedSuperBIDE
			  FROM dbo.TrendAdj_SAR_SavedSuperBIDE sb WITH(NOLOCK)
			 INNER JOIN #BasePlans bp WITH(NOLOCK)
				ON bp.BaseCPS = sb.CPS
			  LEFT OUTER JOIN #vwSamCrosswalks pc WITH(NOLOCK)
				ON pc.BaseCPS = sb.CPS
			   AND pc.SSStateCountyCD = sb.SSStateCountyCD;

			-- Crosswalking base to current/bid CPS for cases where Segment = 1			
			DROP TABLE IF EXISTS #superbide_cw_pre;

			SELECT CPS,
				   CASE WHEN sb.CurrentCPS IS NULL THEN bc.CurrentCPS
					    ELSE sb.CurrentCPS
				   END AS CurrentCPS,
				   CASE WHEN sb.BidCPS IS NULL THEN bb.BidCPS
						ELSE sb.BidCPS
				   END AS BidCPS,
				   SSStateCountyCD,
				   CMSMemberMonths,
				   TotalAllowed
			  INTO #superbide_cw_pre
			  FROM #TrendAdj_SAR_SavedSuperBIDE sb WITH(NOLOCK)
			  LEFT JOIN #Base2Current bc WITH(NOLOCK)
				ON bc.BaseCPS = sb.CPS
			   AND bc.Segment = 1
			  LEFT JOIN #Base2Bid bb WITH(NOLOCK)
				ON bb.BaseCPS = sb.CPS
			   AND bb.Segment = 1;
			
			-- Matching the SA for current/bid after the crosswalk 1
			DROP TABLE IF EXISTS #superbide_cw_post;

			SELECT sb.CPS,
				   cs.CPS AS CurrentCPS,
				   bs.CPS AS BidCPS,
				   sb.SSStateCountyCD,
				   CMSMemberMonths,
				   TotalAllowed
			  INTO #superbide_cw_post
			  FROM #superbide_cw_pre sb WITH(NOLOCK)
			  LEFT OUTER JOIN #Current_SA cs WITH(NOLOCK)
			    ON cs.CPS = sb.CurrentCPS
			   AND cs.SSStateCountyCD = sb.SSStateCountyCD
			  LEFT OUTER JOIN #Bid_SA bs WITH(NOLOCK)
			    ON bs.CPS = sb.BidCPS
			   AND bs.SSStateCountyCD = sb.SSStateCountyCD;

			-- Determining the maximum segment count for the loop
			DROP TABLE IF EXISTS #segment;

			SELECT segment
			  INTO #segment
			  FROM #Base2Current WITH(NOLOCK)

			UNION ALL

			SELECT segment 
			  FROM #Base2Bid WITH(NOLOCK);

			SELECT @max_segment_cnt = MAX(Segment) 
			  FROM #segment WITH(NOLOCK);
			
			-- Repeat crosswalking base to current/bid CPS for cases where higher segment count
			WHILE (@cnt <= @max_segment_cnt)
				BEGIN

					-- Crosswalking base to current/bid CPS for cases for current segment count
					TRUNCATE TABLE #superbide_cw_pre;

					INSERT INTO #superbide_cw_pre

					SELECT CPS,
							CASE WHEN sb.CurrentCPS IS NULL THEN bc.CurrentCPS
								ELSE sb.CurrentCPS
							END AS CurrentCPS,
							CASE WHEN sb.BidCPS IS NULL THEN bb.BidCPS
								ELSE sb.BidCPS
							END AS BidCPS,
							SSStateCountyCD,
							CMSMemberMonths,
							TotalAllowed
						FROM #superbide_cw_post sb WITH(NOLOCK)
						LEFT JOIN #Base2Current bc WITH(NOLOCK)
						ON bc.BaseCPS = sb.CPS
						AND bc.Segment = @cnt
						LEFT JOIN #Base2Bid bb WITH(NOLOCK)
						ON bb.BaseCPS = sb.CPS
						AND bb.Segment = @cnt;
					
					-- Matching the SA for current/bid after the crosswalk 
					TRUNCATE TABLE #superbide_cw_post;

					INSERT INTO #superbide_cw_post

					SELECT sb.CPS,
							cs.CPS AS CurrentCPS,
							bs.CPS AS BidCPS,
							sb.SSStateCountyCD,
							CMSMemberMonths,
							TotalAllowed
						FROM #superbide_cw_pre sb WITH(NOLOCK)
						LEFT OUTER JOIN #Current_SA cs WITH(NOLOCK)
						ON cs.CPS = sb.CurrentCPS
						AND cs.SSStateCountyCD = sb.SSStateCountyCD
						LEFT OUTER JOIN #Bid_SA bs WITH(NOLOCK)
						ON bs.CPS = sb.BidCPS
						AND bs.SSStateCountyCD = sb.SSStateCountyCD;

					SET @cnt=@cnt+1

				END;

			-- for not matched cases assigning the base CPS as current/bid CPS (for OOA counties only)
			DROP TABLE IF EXISTS #SavedSuperBIDE7;

			SELECT DISTINCT CPS,
							CASE WHEN CurrentCPS IS NULL THEN CPS
									ELSE CurrentCPS
							END AS CurrentCPS,
							CASE WHEN BidCPS IS NULL THEN CPS
									ELSE BidCPS
							END AS BidCPS,
							SSStateCountyCD,
							CMSMemberMonths,
							TotalAllowed
					   INTO #SavedSuperBIDE7
					   FROM #superbide_cw_post WITH(NOLOCK);
			   
			-- Adding SA/OOA details to super bide data
			DROP TABLE IF EXISTS #SQLSavedSuperBide;

			SELECT sb.CPS AS BaseCPS,
				   sb.CurrentCPS,
				   sb.BidCPS,
				   sb.SSStateCountyCD, 
				   sb.CMSMemberMonths, 
				   TotalAllowed,
				   CASE WHEN bas.CPS IS NULL THEN 'Y' 
						ELSE 'N'
				   END AS BaseYearOOA,
				   CASE WHEN cs.CPS IS NULL THEN 'Y' 
						ELSE 'N'
				   END AS CurrentYearOOA,
				   CASE WHEN bis.CPS IS NULL THEN 'Y' 
						ELSE 'N'
				   END AS BidYearOOA
			  INTO #SQLSavedSuperBide 
			  FROM #SavedSuperBIDE7 sb WITH(NOLOCK)
			  LEFT OUTER JOIN #Base_SA bas WITH(NOLOCK)
				ON bas.CPS = sb.CPS	   
			   AND bas.SSStateCountyCD = sb.SSStateCountyCD
			  LEFT OUTER JOIN #Current_SA cs WITH(NOLOCK)
				ON cs.CPS = sb.CurrentCPS
			   AND cs.SSStateCountyCD = sb.SSStateCountyCD
			  LEFT OUTER JOIN #Bid_SA bis WITH(NOLOCK)
				ON bis.CPS = sb.BidCPS
			   AND bis.SSStateCountyCD = sb.SSStateCountyCD;

			-- Create temp table w/ outlier factors
			DROP TABLE IF EXISTS #SQLPopulation;

			SELECT ph.CPS,
				   ph.SSStateCountyCD,
				   CASE WHEN SUM (ph.BY_MM) = 0 THEN 1 
						ELSE dbo.Trend_fnSafeDivide(SUM (ph.BY_UseFactor), SUM (ph.BY_MM), 1)
				   END AS Factor
			  INTO #SQLPopulation
			  FROM dbo.Trend_CalcPopulationHistorical ph WITH (NOLOCK)
			 INNER JOIN  #BasePlans sam WITH(NOLOCK)
				ON sam.BaseCPS = ph.CPS
			 WHERE PlanYearID = @BaseYear
			 GROUP BY ph.CPS,
					  ph.PlanYearID,
					  ph.SSStateCountyCD;

			-- Create temp table w/ UCDB factors
			DROP TABLE IF EXISTS #SQLSavedUCDB;

			SELECT CPS,
				   SSStateCountyCD,
				   SU.BaseYearRelativity
			  INTO #SQLSavedUCDB
			  FROM dbo.TrendAdj_SAR_SavedUCDB SU WITH (NOLOCK)
			 INNER JOIN  #BasePlans sam WITH(NOLOCK)
				ON sam.BaseCPS = SU.CPS;

			-- Create temp table w/ UCDB factors
			DROP TABLE IF EXISTS #SQLSavedOutlier;

			SELECT CPS,
				   SSStateCountyCD,
				   SO.Allowed
			  INTO #SQLSavedOutlier
			  FROM dbo.TrendAdj_SAR_SavedOutlier SO WITH (NOLOCK)
			 INNER JOIN  #BasePlans sam WITH(NOLOCK)
				ON sam.BaseCPS = SO.CPS;

			-- Create a table that normalizes Super Bide Allowed for Population and Outlier Claims
			DROP TABLE IF EXISTS #SQLSuperBide;

			SELECT SSB.BidCPS,
				   SSB.CurrentCPS,
				   SSB.BaseCPS,
				   SSB.SSStateCountyCD,
				   CMSMemberMonths,
				   (SSB.TotalAllowed - ISNULL(SO.Allowed, 0)) / ISNULL(SSO.Factor, 1) / ISNULL(SU.BaseYearRelativity, 1) AS Allowed,
				   BaseYearOOA,
				   CurrentYearOOA,
				   BidYearOOA
			  INTO #SQLSuperBide
			  FROM #SQLSavedSuperBide SSB WITH(NOLOCK)
			  LEFT OUTER JOIN #SQLPopulation SSO WITH(NOLOCK)
				ON SSO.CPS = SSB.BaseCPS 
			   AND SSO.SSStateCountyCD = SSB.SSStateCountyCD
			  LEFT OUTER JOIN #SQLSavedUCDB SU WITH(NOLOCK)
				ON SU.CPS = SSB.BaseCPS
			   AND SU.SSStateCountyCD = SSB.SSStateCountyCD
			  LEFT OUTER JOIN #SQLSavedOutlier SO WITH(NOLOCK)
				ON SO.CPS = SSB.BaseCPS
			   AND SO.SSStateCountyCD = SSB.SSStateCountyCD;

			--Get Methodology details from TrendAdj_SAR_LkpMethodology
			DROP TABLE IF EXISTS #Methodology;

			SELECT vw.CPS,
				   meth.PlanYearID,
				   meth.DescriptionofSAR,
				   meth.DampeningMethod,
				   meth.Dampening,
				   meth.CostUseSplit,
				   meth.CostProportiON
			  INTO #Methodology
			  FROM dbo.TrendAdj_SAR_LkpMethodology meth WITH (NOLOCK)
			  INNER JOIN dbo.vwPlanInfo vw 
			    ON vw.PlanInfoID=meth.PlanInfoID
			 INNER JOIN #BidPlans pln WITH(NOLOCK)
				ON vw.CPS=pln.BidCPS;

			/****************************************************/
			/********** Writing into key info table *************/
			/****************************************************/
			
			--Base Year SA Calculations
			INSERT INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage ([SessionID], [CPS], [DescriptionofSAR], [PlanYearID], [IsOOA], [MemberMonths], [Allowed])		
				 SELECT @xSessionID,
				        bb.BidCPS,
					    m.DescriptionofSAR,
					    @BaseYear,
					    0,
					    SUM(CMSMemberMonths),
					    dbo.Trend_fnSafeDivide(SUM(Allowed), SUM(CMSMemberMonths), 0)
				   FROM #SQLSuperBide sb WITH(NOLOCK)
				  INNER JOIN #Base2BidCrosswalk bb WITH(NOLOCK)
					 ON bb.BaseCPS = sb.BaseCPS
				  INNER JOIN #Methodology m WITH(NOLOCK)
					 ON m.CPS = bb.BidCPS
				  WHERE sb.BaseYearOOA = 'N'
				    AND m.PlanYearID = @BaseYear
				  GROUP BY bb.BidCPS,
						   m.DescriptionofSAR;
						  
			--Base Year OOA Calculations
			INSERT INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage ([SessionID], [CPS], [DescriptionofSAR], [PlanYearID], [IsOOA], [MemberMonths], [Allowed])
				SELECT @xSessionID,
				       bb.BidCPS,
					   m.DescriptionofSAR,
					   @BaseYear,
					   1,
					   SUM(CMSMemberMonths),
					   dbo.Trend_fnSafeDivide(SUM(Allowed), SUM(CMSMemberMonths), 0)
				  FROM #SQLSuperBide sb WITH(NOLOCK)
				 INNER JOIN #Base2BidCrosswalk bb WITH(NOLOCK)
					ON bb.BaseCPS = sb.BaseCPS
				 INNER JOIN #Methodology m WITH(NOLOCK)
				    ON m.CPS = bb.BidCPS
				 WHERE sb.BaseYearOOA = 'Y'
				   AND m.PlanYearID = @BaseYear
				 GROUP BY bb.BidCPS,
						  m.DescriptionofSAR;

			--Current Year SA Calculations
			INSERT INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage ([SessionID], [CPS], [DescriptionofSAR], [PlanYearID], [IsOOA], [MemberMonths], [Allowed])		
				 SELECT @xSessionID,
				        bb.BidCPS,
					    m.DescriptionofSAR,
					    @BaseYear + 1,
					    0,
					    SUM(CMSMemberMonths),
					    dbo.Trend_fnSafeDivide(SUM(Allowed), SUM(CMSMemberMonths), 0)
				   FROM #SQLSuperBide sb WITH(NOLOCK)
				  INNER JOIN #Current2BidCrosswalk bb WITH(NOLOCK)
					 ON bb.CurrentCPS = sb.CurrentCPS
				  INNER JOIN #Methodology m WITH(NOLOCK)
				     ON m.CPS = bb.BidCPS
				  WHERE CurrentYearOOA = 'N'
				    AND m.PlanYearID = @BaseYear
				  GROUP BY bb.BidCPS,
						   m.DescriptionofSAR;
		  
			--Current Year OOA Calculations
			INSERT INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage ([SessionID], [CPS], [DescriptionofSAR], [PlanYearID], [IsOOA], [MemberMonths], [Allowed])				
				 SELECT @xSessionID,
				        byo.CPS,
					    byo.DescriptionofSAR,
					    @BaseYear + 1,
					    1,
						ISNULL(dbo.Trend_fnSafeDivide(byo.MemberMonths, bys.MemberMonths, 0), 0) * cys.MemberMonths,
					    byo.Allowed
				   FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage byo WITH(NOLOCK)
				  INNER JOIN dbo.TrendAdj_SAR_CalcKeyInfo_stage bys WITH(NOLOCK)
					 ON bys.CPS = byo.CPS
				    AND bys.PlanYearID = byo.PlanYearID
					AND bys.SessionID = byo.SessionID
				  INNER JOIN dbo.TrendAdj_SAR_CalcKeyInfo_stage cys WITH(NOLOCK)
					 ON cys.CPS = byo.CPS
					AND cys.SessionID = byo.SessionID
				  WHERE byo.PlanYearID = @BaseYear
				    AND cys.PlanYearID = @BaseYear + 1
				    AND byo.IsOOA = 1
				    AND bys.IsOOA = 0;

			--Bid Year SA Calculations
			INSERT INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage ([SessionID], [CPS], [DescriptionofSAR], [PlanYearID], [IsOOA], [MemberMonths], [Allowed])				
				 SELECT @xSessionID,
				        sb.BidCPS,
					    m.DescriptionofSAR,
					    @BaseYear + 2,
					    0,
					    SUM(CMSMemberMonths),
					    dbo.Trend_fnSafeDivide(SUM(Allowed), SUM(CMSMemberMonths), 0)
				   FROM #SQLSuperBide sb WITH(NOLOCK)
				  INNER JOIN #Methodology m WITH(NOLOCK)
					 ON m.CPS = sb.BidCPS
				  WHERE BidYearOOA = 'N'
				    AND m.PlanYearID = @BaseYear + 1
				  GROUP BY sb.BidCPS,
						   m.DescriptionofSAR;

			--Bid Year OOA Calculations
			INSERT INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage ([SessionID], [CPS], [DescriptionofSAR], [PlanYearID], [IsOOA], [MemberMonths], [Allowed])
				 SELECT @xSessionID,
				        byo.CPS,
					    byo.DescriptionofSAR,
					    @BaseYear + 2,
						1,
						ISNULL(dbo.Trend_fnSafeDivide(byo.MemberMonths, bys.MemberMonths, 0), 0) * bdys.MemberMonths,
						ISNULL(dbo.Trend_fnSafeDivide(byo.Allowed,bys.Allowed, 0), 0) * bdys.Allowed
				   FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage byo WITH(NOLOCK)
				  INNER JOIN dbo.TrendAdj_SAR_CalcKeyInfo_stage bys WITH(NOLOCK)
					 ON bys.CPS = byo.CPS
				    AND bys.PlanYearID = byo.PlanYearID
					AND bys.SessionID = byo.SessionID
				  INNER JOIN dbo.TrendAdj_SAR_CalcKeyInfo_stage bdys WITH(NOLOCK)
					 ON bdys.CPS = byo.CPS
					AND bdys.SessionID = byo.SessionID
				  WHERE byo.PlanYearID = @BaseYear
				    AND bdys.PlanYearID = @BaseYear + 2
				    AND byo.IsOOA = 1
				    AND bys.IsOOA = 0
				    AND bdys.IsOOA = 0;

			-- Updating table TrendAdj_SAR_CalcKeyInfo_stage for NULL values
							-- Replacing SA Base Year Allowed with SA Bid Year Allowed
							MERGE INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage cki
							USING (SELECT SessionID,
										  CPS,
										  PlanYearID,
										  IsOOA,
										  Allowed,
										  MemberMonths
									 FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage WITH (NOLOCK)
									WHERE PlanYearID = @BaseYear + 2
									  AND IsOOA = 0) ck
									   ON (ck.CPS = cki.CPS)
									  AND (cki.PlanYearID = @BaseYear)
									  AND (ck.SessionID = cki.SessionID)
									  AND (cki.IsOOA = 0)
									 WHEN MATCHED AND cki.Allowed IS NULL
									 THEN UPDATE
									  SET cki.Allowed = ck.Allowed;

							-- Replacing SA Base Year MM with SA Bid Year MM
							MERGE INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage cki
							USING (SELECT SessionID,
										  CPS,
										  PlanYearID,
										  IsOOA,
										  Allowed,
										  MemberMonths
									 FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage WITH (NOLOCK)
									WHERE PlanYearID = @BaseYear + 2
									  AND IsOOA = 0) ck
									   ON (ck.CPS = cki.CPS)
									  AND (cki.PlanYearID = @BaseYear)
									  AND (ck.SessionID = cki.SessionID)
									  AND (cki.IsOOA = 0)
									 WHEN MATCHED AND cki.MemberMonths IS NULL
									 THEN UPDATE
									  SET cki.MemberMonths = ck.MemberMonths;

							-- Replacing OOA Base Year MM with OOA Bid Year MM
							MERGE INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage cki
							USING (SELECT SessionID,
										  CPS,
										  PlanYearID,
										  IsOOA,
										  Allowed,
										  MemberMonths
									 FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage WITH (NOLOCK)
									WHERE PlanYearID = @BaseYear + 2
									  AND IsOOA = 1) ck
									   ON (ck.CPS = cki.CPS)
									  AND (cki.PlanYearID = @BaseYear)
									  AND (ck.SessionID = cki.SessionID)
									  AND (cki.IsOOA = 1)
									 WHEN MATCHED AND cki.MemberMonths IS NULL
									 THEN UPDATE
									  SET cki.MemberMonths = ck.MemberMonths;

							-- Replacing SA Current Year Allowed with SA Bid Year Allowed
							MERGE INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage cki
							USING (SELECT SessionID,
										  CPS,
										  PlanYearID,
										  IsOOA,
										  Allowed,
										  MemberMonths
									 FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage WITH (NOLOCK)
									WHERE PlanYearID = @BaseYear + 2
									  AND IsOOA = 0) ck
									   ON (ck.CPS = cki.CPS)
									  AND (cki.PlanYearID = @BaseYear + 1)
									  AND (ck.SessionID = cki.SessionID)
									  AND (cki.IsOOA = 0)
									 WHEN MATCHED AND cki.Allowed IS NULL
									 THEN UPDATE
									  SET cki.Allowed = ck.Allowed;

							-- Replacing SA Current Year MM with SA Bid Year MM
							MERGE INTO dbo.TrendAdj_SAR_CalcKeyInfo_stage cki
							USING (SELECT SessionID,
										  CPS,
										  PlanYearID,
										  IsOOA,
										  Allowed,
										  MemberMonths
									 FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage WITH (NOLOCK)
									WHERE PlanYearID = @BaseYear + 2
									  AND IsOOA = 0) ck
									   ON (ck.CPS = cki.CPS)
									  AND (cki.PlanYearID = @BaseYear + 1)
									  AND (ck.SessionID = cki.SessionID)
									  AND (cki.IsOOA = 0)
									 WHEN MATCHED AND cki.MemberMonths IS NULL
									 THEN UPDATE
									  SET cki.MemberMonths = ck.MemberMonths;

			/****************************************************************/
			/********** Writing into temp Adjustment table *************/
			/****************************************************************/

			--Calculating total Allowed and MM for Plan/Year
			DROP TABLE IF EXISTS #KeyInfoTotal;

			SELECT CPS,
				   PlanYearID,
				   dbo.Trend_fnSafeDivide(SUM(Allowed * MemberMonths), SUM(MemberMonths), 0) AS Allowed,
				   SUM(MemberMonths) AS MemberMonths
			  INTO #KeyInfoTotal
			  FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage WITH(NOLOCK)
			 GROUP BY CPS,
				      PlanYearID;
			
			-- Calculating initial adjustment before dampening impact
			DROP TABLE IF EXISTS #Initial;

			SELECT ba.CPS,
				   vw.Region,
				   dbo.Trend_fnSafeDivide(cu.Allowed, ba.Allowed, 1) AS B2C_Initial,
				   dbo.Trend_fnSafeDivide(bi.Allowed, cu.Allowed, 1) AS C2P_Initial
			  INTO #Initial
			  FROM #KeyInfoTotal ba WITH(NOLOCK)
			 INNER JOIN #KeyInfoTotal cu WITH(NOLOCK)
				ON cu.CPS = ba.CPS
			 INNER JOIN #KeyInfoTotal bi WITH(NOLOCK)
				ON bi.CPS = cu.CPS
			 INNER JOIN dbo.vwPlanInfo vw WITH(NOLOCK)
				ON vw.CPS = bi.CPS
			 WHERE ba.PlanYearID = @BaseYear
			   AND cu.PlanYearID = @BaseYear +1
			   AND bi.PlanYearID = @BaseYear + 2
			   AND vw.PlanYear = @BaseYear + 2;

			--checking if initial adjustment exceeds the threshold for the region
			DROP TABLE IF EXISTS #Setup;

			SELECT i.CPS,
				   CASE WHEN ba.MemberMonths = cu.MemberMonths THEN 0
						WHEN ABS(B2C_Initial - 1) > DiffThreshold
						 AND cu.MemberMonths <> 0 THEN 1
						ELSE 0
				   END AS ApplyFactor_C,
				   CASE WHEN ABS(C2P_Initial - 1) > DiffThreshold
						AND bi.MemberMonths <> 0 THEN 1
					   ELSE 0
				   END AS ApplyFactor_P,
				   B2C_Initial,
				   C2P_Initial
			  INTO #Setup
			  FROM #Initial i WITH(NOLOCK)
			 INNER JOIN dbo.TrendAdj_SAR_LkpDiffThreshold lkp WITH(NOLOCK)
				ON lkp.Region = i.Region
			 INNER JOIN dbo.TrendAdj_SAR_CalcKeyInfo_stage ba WITH(NOLOCK)
				ON ba.CPS = i.CPS
			 INNER JOIN dbo.TrendAdj_SAR_CalcKeyInfo_stage cu WITH(NOLOCK)
				ON cu.CPS = i.CPS
			 INNER JOIN dbo.TrendAdj_SAR_CalcKeyInfo_stage bi WITH(NOLOCK)
				ON bi.CPS = i.CPS
			 WHERE ba.PlanYearID = @BaseYear
			   AND ba.IsOOA = 0
			   AND cu.PlanYearID = @BaseYear + 1
			   AND cu.IsOOA = 0
			   AND bi.PlanYearID = @BaseYear + 2
			   AND bi.IsOOA = 0;

			-- Calculating the final adjustment if any dampening methodology used, exclude if both years are below dampening threshold
			DROP TABLE IF EXISTS #Final;

			SELECT s.CPS,
				   CASE WHEN ApplyFactor_C = 0 THEN 1
						ELSE (B2C_Initial - 1) * (CASE WHEN lkpc.DampeningMethod = @FullAdjDampening THEN 1
													   WHEN lkpc.DampeningMethod = @CredDampening THEN IIF(SQRT(kitc.MemberMonths / 24000) < 1, SQRT(kitb.MemberMonths / 24000), 1)
													   WHEN lkpc.DampeningMethod = @UserDampening THEN lkpc.Dampening
													   ELSE 0
												  END) + 1
				   END AS B2C_Final,
				   CASE WHEN ApplyFactor_P = 0 THEN 1
						ELSE (C2P_Initial - 1) * (CASE WHEN lkpp.DampeningMethod = @FullAdjDampening THEN 1
													   WHEN lkpp.DampeningMethod = @CredDampening THEN IIF(SQRT(kitb.MemberMonths / 24000) < 1, SQRT(kitc.MemberMonths / 24000), 1)
													   WHEN lkpp.DampeningMethod = @UserDampening THEN lkpp.Dampening
													   ELSE 0
												  END) + 1
				   END AS C2P_Final
			  INTO #Final
			  FROM #Setup s WITH(NOLOCK)
			 INNER JOIN #Methodology lkpc WITH(NOLOCK)
				ON lkpc.CPS = s.CPS
			 INNER JOIN #Methodology lkpp WITH(NOLOCK)
				ON lkpp.CPS = s.CPS
			 INNER JOIN #KeyInfoTotal kitc WITH(NOLOCK)
				ON kitc.CPS = s.CPS
			 INNER JOIN #KeyInfoTotal kitb WITH(NOLOCK)
				ON kitb.CPS = s.CPS
			 WHERE lkpc.PlanYearID = @BaseYear
			   AND lkpp.PlanYearID = @BaseYear + 1
			   AND kitc.PlanYearID = @BaseYear + 1
			   AND kitb.PlanYearID = @BaseYear + 2
			   AND NOT (s.ApplyFactor_C=0 AND s.ApplyFactor_P=0);
			
			-- Final output, based on Cost/Use split methodology
			SELECT 'RepCat' AS Granularity,
				   'Service Area Reduction' AS AdjustmentDescription,BP.BidCPS AS CPS,
				   @BaseYear + 1 AS TrendYearID,
				   1 AS RateType,
				   rc.ReportingCategory,
				   NULL AS BenefitCategoryID,
				   CASE WHEN lkpc.CostUseSplit = @DefaultSplit THEN SQRT(B2C_Final) - 1
						WHEN lkpc.CostUseSplit = @OnlyCostSplit THEN B2C_Final - 1
						WHEN lkpc.CostUseSplit = @OnlyUseSplit THEN 0
						WHEN lkpc.CostUseSplit = @UserDefinedSplit THEN lkpc.CostProportion - 1
						ELSE 0
				   END AS CostAdjustment,
				   CASE WHEN lkpc.CostUseSplit = @DefaultSplit THEN SQRT(B2C_Final) - 1
						WHEN lkpc.CostUseSplit = @OnlyUseSplit THEN B2C_Final - 1
						WHEN lkpc.CostUseSplit = @OnlyCostSplit THEN 0
						WHEN lkpc.CostUseSplit = @UserDefinedSplit THEN lkpc.CostProportion - 1
						ELSE 0
				   END AS UseAdjustment
			  FROM #Final f WITH(NOLOCK)
			 CROSS JOIN #RepCat rc WITH(NOLOCK)
			 INNER JOIN #Methodology lkpc WITH(NOLOCK)
				ON lkpc.CPS = f.CPS
			 INNER JOIN #BidPlans bp WITH(NOLOCK)	 
				ON bp.BidCPS = f.CPS
			 WHERE lkpc.PlanYearID = @BaseYear
			   AND rc.ReportingCategory NOT in ('Part B Rx Pharmacy','MOC')

			UNION ALL

			SELECT 
				   'RepCat' AS Granularity,
				   'Service Area Reduction' AS AdjustmentDescription,
				   BP.BidCPS AS CPS,
				   @BaseYear + 2 AS TrendYearID,
				   1 AS RateType,
				   rc.ReportingCategory,
				   NULL AS BenefitCategoryID,
				   CASE WHEN lkpp.CostUseSplit = @DefaultSplit THEN SQRT(C2P_Final) - 1
						WHEN lkpp.CostUseSplit = @OnlyCostSplit THEN C2P_Final - 1
						WHEN lkpp.CostUseSplit = @OnlyUseSplit THEN 0
						WHEN lkpp.CostUseSplit = @UserDefinedSplit THEN lkpp.CostProportion - 1
						ELSE 0
				   END AS CostAdjustment,
				   CASE WHEN lkpp.CostUseSplit = @DefaultSplit THEN SQRT(C2P_Final) - 1
						WHEN lkpp.CostUseSplit = @OnlyUseSplit THEN C2P_Final - 1
						WHEN lkpp.CostUseSplit = @OnlyCostSplit THEN 0
						WHEN lkpp.CostUseSplit = @UserDefinedSplit THEN lkpp.CostProportion - 1
						ELSE 0
				   END AS UseAdjustment
			  FROM #Final f WITH(NOLOCK)
			 CROSS JOIN #RepCat rc WITH(NOLOCK)
			 INNER JOIN #Methodology lkpp WITH(NOLOCK)
				ON lkpp.CPS = f.CPS
			 INNER JOIN #BidPlans bp WITH(NOLOCK)
				ON bp.BidCPS = f.CPS
			 WHERE lkpp.PlanYearID = @BaseYear + 1
			   AND rc.ReportingCategory NOT in ('Part B Rx Pharmacy','MOC');

            COMMIT TRANSACTION transaction_TrendAdj_spCalcSAR;

			-- Clean all temp tables.
			DROP TABLE IF EXISTS #Final;
			DROP TABLE IF EXISTS #Setup;
			DROP TABLE IF EXISTS #Initial;
			DROP TABLE IF EXISTS #KeyInfoTotal;
			DROP TABLE IF EXISTS #Methodology;
			DROP TABLE IF EXISTS #SQLSuperBide;
			DROP TABLE IF EXISTS #SQLSavedOutlier;
			DROP TABLE IF EXISTS #SQLSavedUCDB;
			DROP TABLE IF EXISTS #SQLPopulation;
			DROP TABLE IF EXISTS #SQLSavedSuperBide;
			DROP TABLE IF EXISTS #SavedSuperBIDE7;
			DROP TABLE IF EXISTS #segment;
			DROP TABLE IF EXISTS #superbide_cw_post;
			DROP TABLE IF EXISTS #superbide_cw_pre;
			DROP TABLE IF EXISTS #TrendAdj_SAR_SavedSuperBIDE;
			DROP TABLE IF EXISTS #Bid_SA;
			DROP TABLE IF EXISTS #Current_SA;
			DROP TABLE IF EXISTS #Base_SA;
			DROP TABLE IF EXISTS #Base2Bid;
			DROP TABLE IF EXISTS #Base2BidCrosswalk;
			DROP TABLE IF EXISTS #Current2BidCrosswalk;
			DROP TABLE IF EXISTS #Base2Current;
			DROP TABLE IF EXISTS #Base2CurrentCrosswalk;
			DROP TABLE IF EXISTS #CPSs;
			DROP TABLE IF EXISTS #CPS;
			DROP TABLE IF EXISTS #BidPlans;
			DROP TABLE IF EXISTS #CurrentPlans;
			DROP TABLE IF EXISTS #BasePlans;
			DROP TABLE IF EXISTS #vwSAMCrosswalks;
			DROP TABLE IF EXISTS #RepCat;

        END TRY

        BEGIN CATCH

			DECLARE @ErrorMessage NVARCHAR(4000);
		    DECLARE @ErrorSeverity INT;
		    DECLARE @ErrorState INT;
		    DECLARE @ErrorException NVARCHAR(4000);
		    DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
				    @currentdate DATETIME = GETDATE();

			SELECT @ErrorMessage = ERROR_MESSAGE();
			SELECT @ErrorSeverity = ERROR_SEVERITY();
			SELECT @ErrorState = ERROR_STATE();

			RAISERROR(   
				@ErrorMessage,  -- Message text.  
				@ErrorSeverity, -- Severity.  
				@ErrorState     -- State.  
			);

			ROLLBACK TRANSACTION;
			--- Insert into app log for logging error------------------
			INSERT INTO dbo.TrendAdjLog
                (AdjGroupID
                ,ProcName
                ,Region
                ,UserID
                ,AuditTime
                ,AuditMessage)
            VALUES (1
                   ,'Calc:SAR'
                   ,(SELECT TOP 1 region FROM dbo.vwPlanInfo WITH(NOLOCK) WHERE PlanInfoID IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable(@xPlanList, ',', 1)))
                   ,@xLastUpdateByID
                   ,GETDATE ()
                   ,@ErrorMessage);

		END CATCH;
		
    END;
GO