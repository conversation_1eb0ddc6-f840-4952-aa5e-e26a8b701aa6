SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_NormProcess_spIsIncludeInTrendSnapshot
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: MAY-05-2021
--
-- DESCRIPTION:		Take a snapshot of the the IsIncludeInTrend flag (SavedPlanInfo table) and use this in the normalized process.
--					This will ensure we can always run clean audits on the normalized process, even if the IsIncludeInTrend flag later changes. 
--					Trend_NormProcess stored procedures and audit files will reference Trend_NormProcess_IsIncludeInTrendSnapshot.IsIncludeInTrend 
--						instead of SavedPlanInfo.IsIncludeInTrend.
--              
--              
-- PARAMETERS:
--  Input  :	@LastCopyByID
--
--  Output : NONE
--
-- TABLES : Read :  SavedPlanInfo
--					
--          Write:  Trend_NormProcess_IsIncludeInTrendSnapshot
--                  
--
-- VIEWS: Read: NONE
--
-- FUNCTIONS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER   
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MAY-05-2021      1           Initial Version																								Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_NormProcess_spIsIncludeInTrendSnapshot]
@LastCopyByID CHAR(7)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        DECLARE @tranCount INT = @@TranCount; -- Current open transaction count

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            -- Remove existing snapshot
            DELETE  FROM dbo.Trend_NormProcess_IsIncludeInTrendSnapshot WHERE   1 = 1;

            -- Take snapshop
            INSERT INTO dbo.Trend_NormProcess_IsIncludeInTrendSnapshot
                (PlanInfoID
                ,PlanYear
                ,CPS
                ,LastCPS
                ,PlanName
                ,ActuarialMarketID
                ,MARegionID
                ,PlanTypeID
                ,ProductTypeID
                ,PFFSNetworkID
                ,SNPTypeID
                ,MAPlanDesignID
                ,IsOffMAModel
                ,IsIncludeInTrend
                ,IsCalcPlanExpAdj
                ,LastUpdateByID
                ,LastUpdateDateTime
                ,LastCopyByID
                ,LastCopyDateTime)
            SELECT  PlanInfoID
                   ,PlanYear
                   ,CPS
                   ,LastCPS
                   ,PlanName
                   ,ActuarialMarketID
                   ,MARegionID
                   ,PlanTypeID
                   ,ProductTypeID
                   ,PFFSNetworkID
                   ,SNPTypeID
                   ,MAPlanDesignID
                   ,IsOffMAModel
                   ,IsIncludeInTrend
                   ,IsCalcPlanExpAdj
                   ,LastUpdateByID
                   ,LastUpdateDateTime
                   ,@LastCopyByID AS LastCopyByID
                   ,GETDATE () AS LastCopyDateTime
            FROM    dbo.SavedPlanInfo;

            COMMIT TRANSACTION transactionMain;

        END TRY

        -- CATCH block will roll back the transaction if an error occurs in the TRY block
        BEGIN CATCH
            IF (@@TranCount > @tranCount) -- If transactionMain was not committed in the TRY block (i.e. an error occurred), there will be an extra open transaction.
                BEGIN
                    ROLLBACK TRANSACTION transactionMain; -- Undo all data modifications that occurred inside of transactionMain
                END;
        END CATCH;

    END;


GO


