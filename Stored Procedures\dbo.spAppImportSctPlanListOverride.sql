﻿SET <PERSON><PERSON>_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- Stored Procedure
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportSctPlanListOverride
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-Oct-23      1			Initial Version						                                Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE  PROCEDURE [dbo].[spAppImportSctPlanListOverride]
    @StageId VARCHAR(100)
AS
BEGIN
	SET NOCOUNT ON;
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

    DECLARE @tbl__CpsPlanList TABLE
    (
         [BidYear] SMALLINT
	    ,[ContractPBPSegment] VARCHAR(13)
	    ,[PlanYearID] SMALLINT
	    ,[PlanYearContractPBPSegment] VARCHAR(17)
	)
	DECLARE @tbl__CpsRemove TABLE
    (         
	    [CPS] VARCHAR(13)	     
	)
	DECLARE @tbl__CpsData TABLE
	(
		[ContractPBPSegment] VARCHAR(13)
	    ,[Division] VARCHAR(50)
        ,[Region] VARCHAR(50)
        ,[SubRegion] VARCHAR(50)  
        ,[PrimaryState] VARCHAR(50)
        ,[Market] VARCHAR(50)
        ,[PlanType] VARCHAR(30)
	    ,[IsSNP] BIT
        ,[SNPType] VARCHAR(30)      
        ,[DSNPSubType] VARCHAR(50)
	    ,[Product] VARCHAR(30)
	    ,[PlanCategory] VARCHAR(50)
	    ,[TargetedSegment] VARCHAR(100)
	    ,[IsPassive] BIT
	    ,[GivebackRange] VARCHAR(50)
	    ,[INNMedicalDeductibleRange] VARCHAR(100)
	    ,[IsRiskPlan] BIT
	    ,[IsDelegated] VARCHAR(25)
	    ,[NewPlanFlag] VARCHAR(11)
	    ,[ConcurrentPlan] VARCHAR(8)
	    ,LastUpdateByID CHAR(7)
        ,LastUpdateDateTime DATETIME
    );

    INSERT INTO @tbl__CpsPlanList
    SELECT 
           [BidYear]
          ,[ContractPBPSegment]
          ,[PlanYearID]
          ,[PlanYearContractPBPSegment]          
    FROM
        OPENJSON(@jsonData, '$.CPSPlanList')
        WITH
        (
            [BidYear] SMALLINT
	        ,[ContractPBPSegment] VARCHAR(13)
	        ,[PlanYearID] SMALLINT
	        ,[PlanYearContractPBPSegment] VARCHAR(17)	        
        );
	INSERT INTO @tbl__CpsRemove
    SELECT            
          [CPS]        
    FROM
        OPENJSON(@jsonData, '$.CPSRemove')
        WITH
        (             
	        [CPS] VARCHAR(13)	                 
        );



	INSERT INTO @tbl__CpsData
    SELECT 
	 [ContractPBPSegment]
		,[Division]
          ,[Region]
          ,[SubRegion]
          ,[PrimaryState]
          ,[Market]
          ,[PlanType]
          ,[IsSNP]
          ,[SNPType]
          ,[DSNPSubType]
          ,[Product]
          ,[PlanCategory]
          ,[TargetedSegment]
          ,[IsPassive]
          ,[GivebackRange]
          ,[INNMedicalDeductibleRange]
          ,[IsRiskPlan]
          ,[IsDelegated]
          ,[NewPlanFlag]
          ,[ConcurrentPlan]
           ,@UserId
           ,GETDATE()
		FROM
        OPENJSON(@jsonData, '$.CPSData')
        WITH
        (
		[ContractPBPSegment] VARCHAR(13)
		   ,[Division] VARCHAR(50)
            ,[Region] VARCHAR(50)
            ,[SubRegion] VARCHAR(50)  
            ,[PrimaryState] VARCHAR(50)
            ,[Market] VARCHAR(50)
            ,[PlanType] VARCHAR(30)
	        ,[IsSNP] BIT
            ,[SNPType] VARCHAR(30)      
            ,[DSNPSubType] VARCHAR(50)
	        ,[Product] VARCHAR(30)
	        ,[PlanCategory] VARCHAR(50)
	        ,[TargetedSegment] VARCHAR(100)
	        ,[IsPassive] BIT
	        ,[GivebackRange] VARCHAR(50)
	        ,[INNMedicalDeductibleRange] VARCHAR(100)
	        ,[IsRiskPlan] BIT
	        ,[IsDelegated] VARCHAR(25)
	        ,[NewPlanFlag] VARCHAR(11)
	        ,[ConcurrentPlan] VARCHAR(8)
			);		

		DELETE a FROM  dbo.SCTPlanListOverride  a
		INNER JOIN @tbl__CpsPlanList b ON a.BidYear=b.BidYear AND a.ContractPBPSegment =b.ContractPBPSegment
		AND a.PlanYearID = b.PlanYearID AND a.PlanYearContractPBPSegment = b.PlanYearContractPBPSegment

		INSERT INTO dbo.SCTPlanListOverride
        (
            [BidYear]
          ,[ContractPBPSegment]
          ,[PlanYearID]
          ,[PlanYearContractPBPSegment]
          ,[Division]
          ,[Region]
          ,[SubRegion]
          ,[PrimaryState]
          ,[Market]
          ,[PlanType]
          ,[IsSNP]
          ,[SNPType]
          ,[DSNPSubType]
          ,[Product]
          ,[PlanCategory]
          ,[TargetedSegment]
          ,[IsPassive]
          ,[GivebackRange]
          ,[INNMedicalDeductibleRange]
          ,[IsRiskPlan]
          ,[IsDelegated]
          ,[NewPlanFlag]
          ,[ConcurrentPlan]
          ,[LastUpdateByID]
          ,[LastUpdateDateTime]
        )
		SELECT 
            [BidYear]
          ,a.[ContractPBPSegment]
          ,[PlanYearID]
          ,[PlanYearContractPBPSegment]
          ,[Division]
          ,[Region]
          ,[SubRegion]
          ,[PrimaryState]
          ,[Market]
          ,[PlanType]
          ,[IsSNP]
          ,[SNPType]
          ,[DSNPSubType]
          ,[Product]
          ,[PlanCategory]
          ,[TargetedSegment]
          ,[IsPassive]
          ,[GivebackRange]
          ,[INNMedicalDeductibleRange]
          ,[IsRiskPlan]
          ,[IsDelegated]
          ,[NewPlanFlag]
          ,[ConcurrentPlan]
          ,[LastUpdateByID]
          ,[LastUpdateDateTime]
        FROM @tbl__CpsData a
		JOIN @tbl__CpsPlanList b ON b.ContractPBPSegment = a.ContractPBPSegment		

        --remove sheet logic
		DELETE FROM dbo.SCTPlanListOverride WHERE ContractPBPSegment IN(SELECT CPS FROM @tbl__CpsRemove);
	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
GO
