SET <PERSON><PERSON>_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---      
-- PROCEDURE NAME: Trend_spDeleteAll      
--      
-- CREATOR: Andy Blink      
--      
-- CREATED DATE: MAY-20-2020      
--      
-- DESCRIPTION: The purpose of this procedure is to clear out all necessary trend tables at the start of a new quarterly forecast      
--                    
--                    
-- PARAMETERS:      
--  Input  : NONE       
--      
--  Output : @ValidationMessage      
--      
-- TABLES : Read :  See below for tables deleted from      
--           
--          Write:  NONE      
--                        
--      
-- VIEWS: Read: NONE   
--      
-- STORED PROCS: Executed: NONE      
--      
-- $HISTORY       
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------   
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER              
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------  
-- MAY-20-2020      1           Initial Version                                                     Andy Blink      
-- 2020-dEC-07      2			Added otput parameter												Anurodh Pandey  
-- DEC-11-2020		3			Removed reporting tables from this SP.								Jake Lewis
--								Removed cost seasonality table from this SP, since it is no
--								longer used and will be dropped.    
-- JUN-15-2021		4			Add Trend_NormProcess_IsIncludeInTrendSnapshot						Jake Lewis
-- OCT-26-2022		5			Replaced Trend_CalcPopulationCredibilityRegionProduct,				Tanvi Khanna
--								Trend_CalcPopulationCredibilityProduct,							
--								and Trend_CalcPopulationCredibilityProductDE with 
--								Trend_CalcPopulationCredibility and Trend_CalcPopulationBidYearAudit
--								with Trend_PopulationReporting_CalcRelativity
--JUN-20-2023		6			Removed Trend_SavedPopulationMarketAdjustment,						Hannah Harmon
--								Trend_SavedPopulationBarcBidYearMembership;
--								Trend_SavedPackageOption, 
--								Trend_SavedComponentImportAnnual,
--								Trend_SavedComponentImportQuarterly, 
--								Trend_PopulationReporting_CalcRelativity,
--								Trend_CalcPopulationCredibility, 
--								Trend_CalcPopulationHistorical,
--								Trend_ProjProcess_CalcPlanAdjmt_BenCat, 
--								Trend_ProjProcess_CalcPlanAdjmt_RepCat,
--								Trend_ProjProcess_CalcSeasonalityAspt_Use these are no longer in
--								use or have been moved into other parts of the trend process.
--OCT-20-2023		7			Removed Trend_CalcHistoricCostAndUse and Trend_CalcHistoricMembership;     Hannah Harmon
--JUL-23-2024		8			Removed Trend-NormProcess_IsIncludeInTrendSnapshot                         Hannah Harmon

-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_spDeleteAll]
@ValidationMessage VARCHAR(MAX) OUT

WITH EXEC AS OWNER
AS
    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        BEGIN TRY

            BEGIN TRANSACTION;


            TRUNCATE TABLE dbo.Trend_SavedRelativityCMSReimb;
            TRUNCATE TABLE dbo.Trend_SavedRelativityContractual;
            TRUNCATE TABLE dbo.Trend_SavedRelativityInducedUtilization;
            TRUNCATE TABLE dbo.Trend_SavedRelativityOutlierClaims;
            TRUNCATE TABLE dbo.Trend_SavedRelativityPopulation;
            TRUNCATE TABLE dbo.Trend_CalcFileSync_Annual;
            TRUNCATE TABLE dbo.Trend_CalcFileSync_Quarterly;
            TRUNCATE TABLE dbo.Trend_CalcPopulationCurrentYear;
            TRUNCATE TABLE dbo.Trend_Data_CalcBaseCostAndUse;
            TRUNCATE TABLE dbo.Trend_Data_CalcBaseMembership;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcCombined;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcIsPlanlevel_CMSReimb;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcIsPlanlevel_Contractual;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcIsPlanLevel_InducedUtilization;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcIsPlanlevel_OutlierClaims;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcIsPlanLevel_Population;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcNormalized;
            TRUNCATE TABLE dbo.Trend_NormProcess_CalcNotPlanLevel;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcIsPlanLevel_Population;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingOverrides;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcPlanTrends_BenCat;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcPlanTrends_RepCat;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcPlanTrends_RepCat_staged;
            TRUNCATE TABLE dbo.Trend_ProjProcess_CalcPlanTrendsFinal;
            TRUNCATE TABLE dbo.Trend_PerPopulationImplementationSync;

            COMMIT TRANSACTION;

        END TRY

        BEGIN CATCH
            ROLLBACK TRANSACTION;
            SET @ValidationMessage = 'Execution Failed. <NAME_EMAIL>.';
            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @ErrorException NVARCHAR(4000);
            DECLARE @errSrc      VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL')
                   ,@currentdate DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS

                   VARCHAR);
            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);

            ROLLBACK TRANSACTION;

            ---Insert into app log for logging error------------------    
            EXEC spAppAddLogEntry @currentdate
                                 ,''
                                 ,'ERROR'
                                 ,@errSrc
                                 ,@ErrorMessage
                                 ,@ErrorException
                                 ,'';

        END CATCH;

    END;
GO
