SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSaveRevertMarketInputValues]
--    
-- AUTHOR: Surya <PERSON>rthy 
--    
-- CREATED DATE: 2025-Jan-7   
-- Type: 
-- DESCRIPTION: Procedure responsible for resetting marketinputvalue to a prior value based on log entries.
--    
-- PARAMETERS:    
-- Input: 
-- 
--

-- TABLES:   
--  

-- Read:    
-- 

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-Jan-7		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- 2025-Feb-03		3		Revert blank notes			    Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSaveRevertMarketInputValues]
( 
	 @MarketInputValueID VARCHAR(MAX) , --may need protections to ensure these id are part of the logid
	 @LogID INT ,
	 @UserID VARCHAR(200)
)
AS    
BEGIN TRY
	SET NOCOUNT ON;
	DECLARE	@OutPutResultCode VARCHAR(20)
	DECLARE @OutPutResult VARCHAR(200)
	BEGIN TRANSACTION
	DECLARE @PlanList VARCHAR(MAX)= (SELECT PlanList FROM prepricing.logMarketInputSave WHERE LogID = @LogID);
	DECLARE @BenefitList VARCHAR(MAX) = (SELECT BenefitList FROM prepricing.logMarketInputSave WHERE LogID = @LogID);
	DECLARE @InputType VARCHAR(100) = (SELECT InputType FROM prepricing.logMarketInputSave WHERE LogID = @LogID );
	DECLARE @LogDate DATETIME = (SELECT LogDate FROM [PrePricing].[LogMarketInputSave] WHERE LogID = @LogID);
	DECLARE @UTCLogDate DATETIME = DATEADD(HOUR, DATEDIFF(HOUR,SYSDATETIME(),SYSUTCDATETIME()), @LogDate);	

	--Create Log Record for Reversion
	DECLARE @NewLogDate DATETIME =  DATEADD(SECOND,-1,GETDATE()); --create gap between log date and record update date
	INSERT INTO [PrePricing].[LogMarketInputSave] 
	(LogDate,UserID,UpdateSource,PlanList,BenefitList,InputValue,InputType,CostShareType, Notes)
	SELECT @NewLogDate,@UserID, 'Revert', @PlanList,@BenefitList,'Prior Value',@InputType,NULL,CONCAT('Reverted LogID: ',@LogID);

	--Parse MarketInputValueId
	DROP TABLE IF EXISTS #UpdateRecords
	SELECT value AS MarketInputValueID INTO #UpdateRecords 
	FROM STRING_SPLIT(@MarketInputValueID,',');

	--Get Prior Year values for each record. Set up fields for update.
	WITH PriorValue AS (
		SELECT ur.MarketInputValueID AS RecordID,miv.*,
		CASE WHEN @InputType IN ( 'IN', 'BOTH') THEN ISNULL(INValue,'') ELSE NULL END AS priorINValue, 
		CASE WHEN @InputType IN ( 'OON', 'BOTH') THEN ISNULL(OONValue,'') ELSE NULL END AS priorOONValue, 
		CASE WHEN @InputType IN ('$ Change') THEN ISNULL(BenefitChangeValue,-1000) ELSE NULL END AS priorBenefitChangeValue, --use -1000 as indicator that value should be null during update.
		CASE WHEN @InputType IN ( 'IN', 'BOTH') THEN ISNULL(INCostShareType,'') ELSE NULL END AS priorINCostShareType, 
		CASE WHEN @InputType IN ( 'OON', 'BOTH') THEN ISNULL(OONCostShareType,'') ELSE NULL END AS priorOONCostShareType
		FROM #UpdateRecords ur
		LEFT JOIN prepricing.MarketInputValue 
		FOR SYSTEM_TIME AS OF @UTCLogDate  MIV 
		ON ur.MarketInputValueID = MIV.MarketInputValueID
	)
	UPDATE t
	SET 
		t.INValue = ISNULL (s.priorINValue,t.INValue),
		t.OONValue = ISNULL(s.priorOONValue,t.OONValue),
		t.INCostShareType = ISNULL (s.priorINCostShareType,t.INCostShareType),
		t.OONCostShareType = ISNULL(s.priorOONCostShareType,t.OONCostShareType),
		t.BenefitChangeValue = CASE WHEN s.priorBenefitChangeValue=-1000 THEN NULL ELSE ISNULL(s.priorBenefitChangeValue,t.BenefitChangeValue) END,
		t.Note = ISNULL(s.note,''),
		t.LastUpdateByID = @UserID,
		t.LastUpdateSource = 'Revert',
		t.LastUpdateDateTime = GETDATE()
	FROM PrePricing.MarketInputValue t
	JOIN PriorValue s 
	ON s.RecordID = t.MarketInputValueID
	SET @OutPutResultCode='Success'
    SET @OutPutResult='Values Reverted Successfully.'
	SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT
END TRY
BEGIN CATCH
	ROLLBACK;
	SET @OutPutResultCode='Error'    
   SELECT @OutPutResultCode AS OutputCode,ERROR_MESSAGE() AS OutputMessage;
END CATCH
GO
