SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSaveBenefitBundles]   
--    
-- AUTHOR: Surya <PERSON>rthy 
--    
-- CREATED DATE: 2024-Oct-30   
-- Type: 
-- DESCRIPTION: Procedure responsible for saving benefit bundles
--    
-- PARAMETERS:    
-- Input: 
-- @BundleID
-- @BundleName
-- @SubCategories
-- @LastUpdatedByUserId

-- TABLES:   
--
-- Read:    
--  PrePricing.BenefitBundle
--
-- Write:    
--  PrePricing.BenefitBundle
--  PrePricing.BenefitBundleMapping

-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-30		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSaveBenefitBundles]
(
	@BundleID INT,
	@BundleName VARCHAR(200),
	@SubCategories VARCHAR(MAX), 	
	@LastUpdatedByUserId VARCHAR(100)
)
AS    
BEGIN    
	SET NOCOUNT ON
	BEGIN TRY
	BEGIN TRANSACTION bundleSave
	DECLARE	@OutPutResultCode VARCHAR(20)
	DECLARE @OutPutResult VARCHAR(200);
	SET @OutPutResultCode='Success'
	SET @OutPutResult='Bundle:'+@BundleName+' Saved Successfully.'
	IF EXISTS (SELECT 1 FROM PrePricing.BenefitBundle WITH(NOLOCK) WHERE BundleID=@BundleID)	
	BEGIN
		--First updaed group details
		UPDATE  PrePricing.BenefitBundle SET
		BundleName=@BundleName,		
		LastUpdateByID=@LastUpdatedByUserId,
		LastUpdateDateTime=GETDATE()
		WHERE BundleID=@BundleID;
		DELETE FROM PrePricing.BenefitBundleMapping WHERE BundleID=@BundleID;
		--Next update group mapping			 
		INSERT INTO PrePricing.BenefitBundleMapping
		(
		    BundleID,		    
		    LastUpdateDateTime,
		    LastUpdateByID,
			SubCategoryID
		) 		
	    SELECT 		
		@BundleID
		,GETDATE()
		,@LastUpdatedByUserId		
		,VALUE AS SubCategoryID FROM STRING_SPLIT(@SubCategories,',');			
	END
	ELSE
	BEGIN		
		IF NOT EXISTS (SELECT 1 FROM PrePricing.BenefitBundle WITH(NOLOCK) WHERE BundleName=@BundleName)
		BEGIN
			INSERT INTO PrePricing.BenefitBundle
			(
				BundleName,
				BundleDescription,
				LastUpdateDateTime,
				LastUpdateByID
			)
			VALUES
			(   
				@BundleName,
				NULL,
				GETDATE(),
				@LastUpdatedByUserId
			)

			 SELECT @BundleID = SCOPE_IDENTITY();
			 --Next update group mapping			 
			INSERT INTO PrePricing.BenefitBundleMapping
			(
				BundleID,		    
				LastUpdateDateTime,
				LastUpdateByID,
				SubCategoryID
			) 		
			SELECT 		
			@BundleID
			,GETDATE()
			,@LastUpdatedByUserId		
			,VALUE AS SubCategoryID FROM STRING_SPLIT(@SubCategories,',');			
		 END
		 ELSE
		 BEGIN
			SET @OutPutResultCode='Error'
			SET @OutPutResult='Error Bundle Name '+@BundleName+' Already Exists.';					
		 END
	END			
	SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT TRANSACTION bundleSave;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION bundleSave;
		SET @OutPutResultCode='Error'
		SET @OutPutResult='Error while saving bundle:'+@BundleName;		
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH
END
GO
