SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


/****** Object:  UserDefinedFunction [dbo].[fnGetMAExtract]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2008-Apr-10
-- HEADER UPDATED: 2011-Jan-12
--
-- DESCRIPTION: Function responsible for populating the queries used in the Batch -> MA Extract tab, which allows for
--              output of Membership, Risk Factors, Duals, & Secondary Payer.  The WHERE clause is built from the
--              @WhereIN parameter, which is a string "array" of values.  This will either be Plan Indexes or Market
--              IDs, both will take the form of '1, 2, 3, 4'
--
-- PARAMETERS:
--	Input:
--      @WhereIN
--      @Membership
--      @RiskFactor
--      @Duals
--      @SecondaryPayer
--      @IsPlanLevel
--  Output:
--
-- TABLES: 
--	Read:
--      SavedPlanHeader
--      SavedPlanDetail
--      SavedPlanMemberMonthDetail
--      SavedPlanRiskFactorDetail
--      SavedMarketInfo
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnStringSplit
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION	    CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-Apr-10		1			Initial Version							                            Brian Lake
-- 2008-Apr-30		2			Added SavedPlanDetail.MARatingOptionID=1 for 2nd payer extract      Brian Lake
-- 2008-May-07		3			Updated joins between sph and the detail tables to include version	Brian Lake
-- 2008-Oct-03      4           Added joins to SavedPlanStateCountyDetail, allows for county        Brian Lake
--                                  selection
-- 2009-Feb-16		5			Added Dual eligibility experience for membership by seperating      Sulemana Dauda
--                                  PlanYearID <= 2009 from PlanYearID > 2009 observations:  
-- 2009-Feb-20      6           Fixed MembershipTypes and Added Risk Factor Types                   Nick Skeen
-- 2009-Feb-26      7			Ensure that counties for Risk factors are selected when a plan      Sule Dauda
--                                  is deleted
-- 2009-Mar-09		8			Ensure that counties are selected when a given plan is deleted      Sule Dauda
--                                  from SavedPlanMemberMonthDetail
-- 2009-Mar-17      9           Data types                                                          Sandy Ellis
-- 2009-Mar-31      10          Added join on LkpIntMarketPlanTypeDetail.PlanYearID                 Brian Lake
-- 2009-Apr-03      11          Removed FLOAT, reorganized to use 2009 & 2010 MM and Risk Types,    Brian Lake
--                                  expanded PlanTypeName to accomodate larger names like PFFSOON
-- 2010-Sep-15      12          Updated for Coding Standards and ported to 2012                     Jake Gaecke
-- 2010-Sep-29      13          Renamed LkpIntMarketHeader to SavedMarketHeader                     Michael Siekerka
-- 2010-Oct-13      14          Changed PlanName to come from PerExtContractNumberPlanIDDetail      Joe Casey
-- 2011-Jan-12		15			LkpIntMarketPlanTypeDetail removed as it is no longer used, 		Jiao Chen
--									all eight instance of membermonths are now Decimal
-- 2011-Jul-06		16			Plan level membership extract updated to account for sales			Bobby Jaegers
--									adjustments														
-- 2011-Jul-07		17			Updated all and market level membership extracts to account			Bobby Jaegers
--									for sales adjustments
-- 2011-Aug-25		18			Changed to account for Sales membership returning a table			Craig Wright
-- 2011-Sep-08		19			Changed to pull SalesAdjustmentFactor from SavedPlanAssumptions		Alex Rezmerski
-- 2011-Sep-09		20			Changed to account for SalesAdjustmentFactor being null				Alex Rezmerski
-- 2011-Oct-10		21			Changed to get FilePath Column for Membership and Risk data			Craig Wright
-- 2011-Oct-12		22			Removed @ForecastID as an input parameter							Craig Wright
-- 2012-Mar-14		23			Added code to narrow down Demog Indicators in Risk and Membership	Trevor Mahoney
-- 2012-Mar-20		24			Added JOIN on StateTerritoryID to Extract for all member months		Trevor Mahoney
-- 2012-Mar-21		25			Deleted NonDualESRDHospice and DualESRDHospice from membership.		Trevor Mahoney
--									Added OOAMemberMonths.
-- 2013-Oct-07		26			Modified to Include SegmentId										Anubhav Mishra
-- 2017-Jun-20		27			Removed PerExtContractNumberPlanIDDetail and changed Plan Name		Chris Fleming
--									to pull from new column in SavedPlanHeader	
-- 2018-Apr-25		28			Modified LkpExtCMSPlanType for new UI table modifications			Jordan Purdue
-- 2019-Jul-05      29          Made changes in market table.                                       Satyam Singhal	
-- 2022-Apr-22      30          Change @WhereIN from 4000 to MAX                                    Manisha Tyagi
-- 2022-Jun-29		31			Replaced DualEligiblePercent with 0									Aleksandar Dimitrijevic
-- 2024-Jul-02		32		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAExtract]
    (
    @WhereIN VARCHAR(MAX)=NULL,
    @Membership BIT,
    @RiskFactor BIT,
    @Duals BIT,
    @SecondaryPayer BIT,
    @IsPlanLevel BIT
    ) 
RETURNS @Results TABLE 
(
    FilePath VARCHAR(MAX),
    ForecastID INT,
    MarketName VARCHAR(256),
    PlanName VARCHAR(256),
    PlanTypeID INT,
    PlanTypeName VARCHAR(10),
    ContractNumber CHAR(5),
    PlanID CHAR(3),
    SegmentId CHAR(3),  --Added SegmentId	
    CountyCode CHAR(5) ,
    CountyName VARCHAR(128), 
    NonDualAged DECIMAL (28,6),
    NonDualESRD DECIMAL (28,6),
    NonDualHospice DECIMAL (28,6), 
    DualAged DECIMAL (28,6),
    DualESRD DECIMAL (28,6),
    DualHospice DECIMAL (28,6),
    OOAMemberMonths DECIMAL (28,6),
    NonDualExperienceRiskFactor DECIMAL (9, 6),
    NonDualProjectionRiskFactor DECIMAL (9, 6),
    DualExperienceRiskFactor DECIMAL (9, 6),
    DualProjectionRiskFactor DECIMAL (9, 6),
    SecondaryPayerAdjustment DECIMAL(7, 6),
    DualEligiblePercent DECIMAL(9, 4)    
) AS
BEGIN

    IF @Membership = 1 -- This is a membership extract
	BEGIN
-----------------------------------------------------------------------------------------------------------
        IF @WhereIN is NULL --Extract All Plans
-----------------------------------------------------------------------------------------------------------
		BEGIN
            INSERT @Results 								
                SELECT 
					MMD.FilePath,
					SCD.ForecastID, 												
	                ActuarialMarket, 						
	                PlanName = 
	                    CASE LEN(P.PlanName) 						
		                    WHEN 0 THEN 'None' 					
		                    ELSE P.PlanName
		                END, 					
	                P.PlanTypeID, 						
	                ProductType, 	
	                P.ContractNumber,
	                P.PlanID,
	                P.SegmentId	,	--Added SegmentId	
	               	StateCountyCode = dbo.fnPadInteger(SC.StateTerritoryID, 2) + SC.CountyCode, 						
	                SC.CountyName, 	
                    NonDualAged =

                        SUM(
                            CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                WHEN 1 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    NonDualESRD =

						SUM(
                            CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                WHEN 7 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    NonDualHospice =

						SUM(
                            CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                WHEN 4 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    DualAged =

						SUM(
                            CASE DI.DemogIndicator
                                WHEN 2 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    DualESRD =

						SUM(
                            CASE DI.DemogIndicator
                                WHEN 8 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    DualHospice =

						SUM(
                            CASE DI.DemogIndicator
                                WHEN 5 THEN MMD.MemberMonths 
                                ELSE 0 
                            END
                        ),
                    OOAMemberMonths = OOA.MemberMonths,
	                NonDualExperienceRiskFactor = NULL,
	                NonDualProjectionRiskFactor = NULL,					
	                DualExperienceRiskFactor = NULL,						
	                DualProjectionRiskFactor = NULL,  					
	                SecondaryPayerAdjustment = NULL,						
	                DualEligiblePercent = NULL    						
                FROM SavedPlanHeader P 							
                INNER JOIN SavedMarketInfo M 							
	                ON P.MarketID = M.ActuarialMarketID					
                INNER JOIN LkpProductType PT 							
	                ON PT.ProductTypeID = P.PlanTypeID 						
                INNER JOIN SavedPlanStateCountyDetail SCD							
	                ON SCD.ForecastID = P.ForecastID											
	                AND SCD.IsCountyExcludedFromBPTOutput = 0						
                INNER JOIN LkpExtCMSStateCounty SC 							
	                ON SCD.StateTerritoryID = SC.StateTerritoryID 						
	                AND SCD.StateTerritoryID = SC.StateTerritoryID						
	                AND SCD.CountyCode = SC.CountyCode	
	            INNER JOIN SavedPlanOOAMemberMonthDetail OOA
					ON OOA.ForecastID = P.ForecastID 		
                LEFT JOIN SavedPlanMemberMonthDetail MMD 							
	                ON MMD.ForecastID = P.ForecastID
	                AND MMD.StateTerritoryID = SC.StateTerritoryID					
	                AND MMD.CountyCode = SC.CountyCode
	            LEFT JOIN LkpIntDemogIndicators DI
	                ON MMD.DemogIndicator = DI.DemogIndicator
                WHERE
                    P.ForecastID > 0							
	                AND P.IsHidden = 0	
	                AND DI.DemogIndicator NOT IN (3,6,9,12)																	
                GROUP BY
					MMD.FilePath,
                    SCD.ForecastID, 							
	                ActuarialMarket, 						
	                P.PlanName, 						
	                P.PlanTypeID, 						
	                PT.ProductType, 						
	                P.ContractNumber, 						
	                P.PlanId, 	
	                P.SegmentId,		--Added SegmentId			
	                SCD.StateTerritoryID, 						
	                SCD.CountyCode, 						
	                SC.StateTerritoryID, 						
	                SC.CountyCode, 						
	                SC.CountyName,
	                OOA.MemberMonths					
                ORDER BY
                    ActuarialMarket, 							
	                SCD.ForecastID, 						
	                SCD.StateTerritoryID, 						
	                SCD.CountyCode               						
        END

		ELSE -- filtered
-----------------------------------------------------------------------------------------------------------
            IF @IsPlanLevel = 0 --Market Level (Membership)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results 								
                    SELECT
						MMD.FilePath,
                        SCD.ForecastID, 												
	                    ActuarialMarket, 						
	                    PlanName =
	                        CASE LEN(P.PlanName) 						
		                        WHEN 0 THEN 'None' 					
		                        ELSE P.PlanName
		                    END, 					
	                    P.PlanTypeID, 						
	                    ProductType, 						
	                    P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	, 		--Added SegmentId				
	                    StateCountyCode = dbo.fnPadInteger(SC.StateTerritoryID, 2) + SC.CountyCode, 						
	                    SC.CountyName, 						
	                    NonDualAged =

							SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 1 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualESRD =

							SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 7 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualHospice =

							SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 4 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualAged =

							SUM(
                                CASE DI.DemogIndicator
                                    WHEN 2 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualESRD =

							SUM(
                                CASE DI.DemogIndicator
                                    WHEN 8 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualHospice =

							SUM(
                                CASE DI.DemogIndicator
                                    WHEN 5 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        OOAMemberMonths = OOA.MemberMonths,
	                    NonDualExperienceRiskFactor = NULL,
	                    NonDualProjectionRiskFactor = NULL,					
	                    DualExperienceRiskFactor = NULL,						
	                    DualProjectionRiskFactor = NULL,  					
	                    SecondaryPayerAdjustment = NULL,						
	                    DualEligiblePercent = NULL    						
                    FROM SavedPlanHeader P  		
                    INNER JOIN SavedPlanStateCountyDetail SCD		
	                    ON SCD.ForecastID = P.ForecastID 	
	                    AND SCD.IsCountyExcludedFromBPTOutput = 0	
                    INNER JOIN SavedMarketInfo M 	
	                    ON P.MarketID = M.ActuarialMarketID
                    INNER JOIN LkpExtCMSStateCounty SC 	
	                    ON SCD.StateTerritoryID = SC.StateTerritoryID 
	                    AND SCD.CountyCode = SC.CountyCode 
                    INNER JOIN LkpProductType PT 	
	                    ON PT.ProductTypeID = P.PlanTypeID 
	                INNER JOIN SavedPlanOOAMemberMonthDetail OOA
						ON OOA.ForecastID = P.ForecastID
                    LEFT OUTER JOIN SavedPlanMemberMonthDetail MMD	
	                    ON MMD.ForecastID = P.ForecastID
	                    AND MMD.StateTerritoryID = SC.StateTerritoryID
	                    AND MMD.CountyCode = SC.CountyCode
	                LEFT JOIN LkpIntDemogIndicators DI
	                    ON MMD.DemogIndicator = DI.DemogIndicator
                    WHERE
                        P.MarketID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))	
	                    AND P.IsHidden = 0
	                    AND DI.DemogIndicator NOT IN (3,6,9,12)			
                    GROUP BY
                        MMD.FilePath,
                        SCD.ForecastID, 			
	                    ActuarialMarket, 		
	                    P.PlanName, 		
	                    P.PlanTypeID, 		
	                    PT.ProductType, 		
	                    P.ContractNumber, 		
	                    P.PlanId, 
	                    P.SegmentId,   --Added SegmentId			
	                    SCD.StateTerritoryID, 		
	                    SCD.CountyCode, 						
	                    SC.StateTerritoryID, 						
	                    SC.CountyCode, 						
	                    SC.CountyName,
	                    OOA.MemberMonths			
                    ORDER BY
                        ActuarialMarket, 							
	                    SCD.ForecastID, 						
	                    SCD.StateTerritoryID, 						
	                    SCD.CountyCode    						     
			END
-----------------------------------------------------------------------------------------------------------
            ELSE -- Plan Level (Membership)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results							
                    SELECT
                        MMD.FilePath,
                        SCD.ForecastID,			
	                    ActuarialMarket, 					
	                    PlanName = 
	                        CASE LEN(P.PlanName) 					
		                        WHEN 0 THEN 'None' 				
		                        ELSE P.PlanName
		                    END, 				
	                    P.PlanTypeID, 					
	                    ProductType, 					
	                    P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	,  --Added SegmentId					
	                    StateCountyCode = dbo.fnPadInteger(SC.StateTerritoryID, 2) + SC.CountyCode, 					
	                    SC.CountyName, 					
	                    NonDualAged =

                            SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 1 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualESRD =

                            SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 7 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        NonDualHospice =

                            SUM(
                                CASE DI.DemogIndicator --MMD.MemberMonthTypeID
                                    WHEN 4 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualAged =

                            SUM(
                                CASE DI.DemogIndicator
                                    WHEN 2 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualESRD =

                            SUM(
                                CASE DI.DemogIndicator
                                    WHEN 8 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        DualHospice =

                            SUM(
                                CASE DI.DemogIndicator
                                    WHEN 5 THEN MMD.MemberMonths 
                                    ELSE 0 
                                END
                            ),
                        OOAMemberMonths = OOA.MemberMonths,
	                    NonDualExperienceRiskFactor = NULL,
	                    NonDualProjectionRiskFactor = NULL,					
	                    DualExperienceRiskFactor = NULL,						
	                    DualProjectionRiskFactor = NULL,  					
	                    SecondaryPayerAdjustment = NULL,						
	                    DualEligiblePercent = NULL    		
                    FROM SavedPlanHeader P  		
                    INNER JOIN SavedPlanStateCountyDetail SCD		
	                    ON SCD.ForecastID = P.ForecastID 	
	                    AND SCD.IsCountyExcludedFromBPTOutput = 0	
                    INNER JOIN SavedMarketInfo M 	
	                    ON P.MarketID = M.ActuarialMarketID
                    INNER JOIN LkpExtCMSStateCounty SC 	
	                    ON SCD.StateTerritoryID = SC.StateTerritoryID 
	                    AND SCD.CountyCode = SC.CountyCode 
                    INNER JOIN LkpProductType PT 	
	                    ON PT.ProductTypeID = P.PlanTypeID 
	                INNER JOIN SavedPlanOOAMemberMonthDetail OOA
						ON OOA.ForecastID = P.ForecastID
                    LEFT OUTER JOIN SavedPlanMemberMonthDetail MMD	
	                    ON MMD.ForecastID = P.ForecastID
	                    AND MMD.StateTerritoryID = SC.StateTerritoryID
	                    AND MMD.CountyCode = SC.CountyCode
	                LEFT JOIN LkpIntDemogIndicators DI
	                    ON MMD.DemogIndicator = DI.DemogIndicator
                    WHERE
                        P.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))	
	                    AND P.IsHidden = 0
	                    AND DI.DemogIndicator NOT IN (3,6,9,12)	
                    GROUP BY
                        MMD.FilePath,
                        SCD.ForecastID, 			
	                    ActuarialMarket, 		
	                    P.PlanName, 		
	                    P.PlanTypeID, 		
	                    PT.ProductType, 		
	                    P.ContractNumber, 		
	                    P.PlanId, 	
	                    P.SegmentId,	--Added SegmentId	
	                    SCD.StateTerritoryID, 		
	                    SCD.CountyCode, 
	                    SC.StateTerritoryID, 
	                    SC.CountyCode, 
	                    SC.CountyName,
	                    OOA.MemberMonths
                    ORDER BY
                        ActuarialMarket, 	
	                    SCD.ForecastID, 
	                    SCD.StateTerritoryID, 
	                    SCD.CountyCode   
			END
	END

	IF @RiskFactor = 1
	BEGIN
-----------------------------------------------------------------------------------------------------------
        IF @WhereIN is NULL --All Plans 
-----------------------------------------------------------------------------------------------------------
		BEGIN
            INSERT @Results -- No filter (Risk Factors)
                SELECT
                    RFD.FilePath,
                    P.ForecastID, 
                    ActuarialMarket,
                    PlanName = 
                        CASE LEN(P.PlanName)
                            WHEN 0 THEN 'None'
                            ELSE P.PlanName
                        END,
                    P.PlanTypeID,
                    ProductType,
                    P.ContractNumber,
	                P.PlanID,
	                P.SegmentId	,   --Added SegmentId	
                    StateCountyCode = dbo.fnPadInteger(SCD.StateTerritoryID, 2) + SCD.CountyCode,
                    CountyName,
                    NULL,  -- Membership							
                    NULL, 
                    NULL, 
                    NULL, 
                    NULL, 
                    NULL, 
                    NULL,
                    NonDualExperienceRiskFactor =
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 1 THEN CASE RFD.IsExperience
                                                    WHEN 1 THEN RiskFactor
                                                END
                                END	
                            ),
                        0),
                    NonDualProjectedRiskFactor =							
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 1 THEN CASE RFD.IsExperience
                                                    WHEN 0 THEN RiskFactor
                                                END
                                END	
                            ),
                        0), 							
                    DualExperienceRiskFactor =							
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 2 THEN CASE RFD.IsExperience
                                                    WHEN 1 THEN RiskFactor
                                                END
                                END	
                            ),
                        0),						
                    DualProjectionRiskFactor =							
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 2 THEN CASE RFD.IsExperience
                                                    WHEN 0 THEN RiskFactor
                                                END
                                END	
                            ),
                        0),						
                    SecondaryPayerAdjustment = NULL,							
                    DualEligiblePercent = NULL   							
                FROM SavedPlanHeader P 							
                INNER JOIN SavedMarketInfo M 							
                    ON M.ActuarialMarketID = P.MarketID					
                INNER JOIN LkpProductType cpt 							
                    ON P.PlanTypeID = cpt.ProductTypeID							
                INNER JOIN SavedPlanStateCountyDetail SCD							
                    ON SCD.ForecastID = P.ForecastID					
                    AND SCD.IsCountyExcludedFromBPTOutput = 0							
                INNER JOIN LkpExtCMSStateCounty SC 							
                    ON SCD.StateTerritoryID = SC.StateTerritoryID 							
                    AND SCD.CountyCode = SC.CountyCode					
                LEFT JOIN SavedPlanRiskFactorDetail RFD 							
                    ON RFD.ForecastID = P.ForecastID					
                    AND RFD.StateTerritoryID = SC.StateTerritoryID						
                    AND RFD.CountyCode = SC.CountyCode  		
                LEFT JOIN LkpIntDemogIndicators DI
	                ON RFD.DemogIndicator = DI.DemogIndicator
                WHERE
                    P.ForecastID > 0
                    AND P.IsHidden = 0
                    AND DI.DemogIndicator IN (1,2)		
                GROUP BY
                    RFD.FilePath,
                    P.ForecastID,
                    ActuarialMarket, 	
                    P.PlanName, 	
                    P.PlanTypeID, 	
                    ProductType, 	
                    P.ContractNumber, 	
                    P.PlanID,
                    P.SegmentId,	--Added SegmentId	
                    SCD.StateTerritoryID, 	
                    SCD.CountyCode, 	
                    SC.CountyName	
                ORDER BY
                    ActuarialMarket, 	
                    P.ForecastID,
                    SCD.StateTerritoryID, 	
                    SCD.CountyCode	
        END

		ELSE
-----------------------------------------------------------------------------------------------------------
            IF @IsPlanLevel = 0 --Market Level (Risk Factors)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results 
                    SELECT 
						RFD.FilePath,
						P.ForecastID,									
                        ActuarialMarket, 									
                        PlanName = 
                            CASE LEN(P.PlanName) 									
                                WHEN 0 THEN 'None' 									
                                ELSE P.PlanName
                            END, 									
                        P.PlanTypeID, 									
                        ProductType, 									
                        P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	, 	--Added SegmentId									
                        StateCountyCode = dbo.fnPadInteger(SCD.StateTerritoryID, 2) + SCD.CountyCode, 									
                        CountyName, 									
                        NULL,  -- Membership									
                        NULL, 
                        NULL, 
                        NULL, 
                        NULL, 
                        NULL,
                        NULL, 
                        NonDualExperienceRiskFactor =
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
                        NonDualProjectedRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0), 							
                        DualExperienceRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),						
                        DualProjectionRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
                        SecondaryPayerAdjustment = NULL,									
                        DualEligiblePercent = NULL   									
                    FROM SavedPlanHeader P 									
			        INNER JOIN SavedPlanStateCountyDetail SCD					
                        ON SCD.ForecastID = P.ForecastID								
                        AND SCD.IsCountyExcludedFromBPTOutput = 0									
                    INNER JOIN SavedMarketInfo M 									
                        ON M.ActuarialMarketID = P.MarketID								
                    INNER JOIN LkpProductType cpt 									
                        ON P.PlanTypeID = cpt.ProductTypeID 									
                    INNER JOIN LkpExtCMSStateCounty SC 									
                        ON SCD.StateTerritoryID = SC.StateTerritoryID									
                        AND SCD.CountyCode = SC.CountyCode									
                    LEFT OUTER JOIN SavedPlanRiskFactorDetail RFD 									
                        ON RFD.ForecastID = P.ForecastID							
					    AND RFD.StateTerritoryID = SC.StateTerritoryID 			
                        AND RFD.CountyCode = SC.CountyCode
                    LEFT JOIN LkpIntDemogIndicators DI
	                    ON RFD.DemogIndicator = DI.DemogIndicator	
                    WHERE
                        P.MarketID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))																
                        AND P.IsHidden = 0
                        AND DI.DemogIndicator IN (1,2)									
                    GROUP BY
                        RFD.FilePath,
                        P.ForecastID,							
                        ActuarialMarket, 									
                        P.PlanName, 									
                        P.PlanTypeID, 									
                        ProductType, 									
                        P.ContractNumber, 									
                        P.PlanID,
                        P.SegmentId,	--Added SegmentId									
                        SCD.StateTerritoryID, 									
                        SCD.CountyCode, 									
                        SC.CountyName									
                    ORDER BY
                        ActuarialMarket, 									
                        P.ForecastID,							
                        SCD.StateTerritoryID, 									
                        SCD.CountyCode									
			END
-----------------------------------------------------------------------------------------------------------
            ELSE -- Plan Level (Risk Factors)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results -- Plan Level Risk Factors									
                    SELECT
                        RFD.FilePath,
                        P.ForecastID,							
                        ActuarialMarket, 									
                        PlanName = 
                            CASE LEN(P.PlanName) 									
                                WHEN 0 THEN 'None' 									
                                ELSE P.PlanName
                            END,									
                        P.PlanTypeID, 									
                        ProductType, 									
                        P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	, 	--Added SegmentId									
                        StateCountyCode = dbo.fnPadInteger(SCD.StateTerritoryID, 2) + SCD.CountyCode, 									
                        CountyName, 									
                        NULL,  -- Membership									
                        NULL,									
                        NULL,									
                        NULL,									
                        NULL, 									
                        NULL,
                        NULL, 									 									
                        NonDualExperienceRiskFactor =
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
                        NonDualProjectedRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0), 							
                        DualExperienceRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),						
                        DualProjectionRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
                        SecondaryPayerAdjustment = NULL,									
                        DualEligiblePercent = NULL    									
                    FROM SavedPlanHeader P 									
			        INNER JOIN SavedPlanStateCountyDetail SCD					
                        ON SCD.ForecastID = P.ForecastID								
                        AND SCD.IsCountyExcludedFromBPTOutput = 0									
                    INNER JOIN SavedMarketInfo M 									
                        ON M.ActuarialMarketID = P.MarketID								
                    INNER JOIN LkpProductType cpt 									
                        ON P.PlanTypeID = cpt.ProductTypeID 									
                    INNER JOIN LkpExtCMSStateCounty SC 									
                        ON SCD.StateTerritoryID = SC.StateTerritoryID									
                        AND SCD.CountyCode = SC.CountyCode									
                    LEFT OUTER JOIN SavedPlanRiskFactorDetail RFD 									
                        ON RFD.ForecastID = P.ForecastID								
					    AND RFD.StateTerritoryID = SC.StateTerritoryID 			
                        AND RFD.CountyCode = SC.CountyCode
                    LEFT JOIN LkpIntDemogIndicators DI
	                    ON RFD.DemogIndicator = DI.DemogIndicator		
                    WHERE
                        P.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))								
                        AND P.IsHidden = 0
                        AND DI.DemogIndicator IN (1,2)									
                    GROUP BY
                        RFD.FilePath,
                        P.ForecastID,								
                        ActuarialMarket, 									
                        P.PlanName, 									
                        P.PlanTypeID, 									
                        ProductType, 									
                        P.ContractNumber, 									
                        P.PlanID,
                        P.SegmentId,  --Added SegmentId	
                        SCD.StateTerritoryID,
                        SCD.CountyCode,
                        SC.CountyName
                    ORDER BY
                        ActuarialMarket, 									
                        P.ForecastID,								
                        SCD.StateTerritoryID, 									
                        SCD.CountyCode									
			END
	END

	IF @SecondaryPayer = 1
	BEGIN
-----------------------------------------------------------------------------------------------------------
        IF @WhereIN IS NULL --All Plans (SecondaryPayer)
-----------------------------------------------------------------------------------------------------------
        BEGIN
			INSERT @Results --Market Level Secondary Payer					
                SELECT
                    NULL,
                    SPD.ForecastID,					
                    M.ActuarialMarket, 					
                    PlanName = 
                        CASE LEN(P.PlanName) 									
                            WHEN 0 THEN 'None' 									
                            ELSE P.PlanName
                        END, 					
                    P.PlanTypeID, 					
                    ProductType, 					
                    P.ContractNumber,
	                P.PlanID,
	                P.SegmentId	,	--Added SegmentId	
                    StateCountyCode = NULL, 
                    CountyName = NULL,
                    NULL, --Membership					
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
				    NULL, --Risk Factors
                    NULL,
                    NULL,
                    NULL,
                    SPA.SecondaryPayerAdjustment,					
                    DualEligiblePercent = NULL
                FROM SavedPlanDetail SPD 					
                INNER JOIN SavedPlanHeader P 					
                    ON SPD.ForecastID = P.ForecastID				
                INNER JOIN SavedMarketInfo M 					
                    ON M.ActuarialMarketID = P.MarketID			
                INNER JOIN LkpProductType CPT
                    ON P.PlanTypeID = CPT.ProductTypeID
                INNER JOIN SavedPlanAssumptions SPA
                    ON SPD.ForecastID = SPA.ForecastID
                WHERE
                    P.ForecastID > 0
                    AND P.IsHidden = 0
                    AND SPD.MARatingOptionID = 1 --1 = experience rated, which is the default value and will always exist.					
                ORDER BY
                    ActuarialMarket,
                    SPD.ForecastID
        END

		ELSE
-----------------------------------------------------------------------------------------------------------
            IF @IsPlanLevel = 0 --Market Level (SecondaryPayer)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results 
                    SELECT
                        NULL,
                        SPD.ForecastID,				
                        M.ActuarialMarket, 						
                        PlanName = 
                            CASE LEN(P.PlanName) 									
                                WHEN 0 THEN 'None' 									
                                ELSE P.PlanName
                            END,						
                        P.PlanTypeID, 						
                        ProductType, 						
                        P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	,	--Added SegmentId				
                        StateCountyCode = NULL, 
                        CountyName = NULL,
                        NULL, --Membership					
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
				        NULL, --Risk Factors
                        NULL,
                        NULL,
                        NULL,
                        SPA.SecondaryPayerAdjustment,					
                        DualEligiblePercent = NULL
                    FROM SavedPlanDetail SPD 						
                    INNER JOIN SavedPlanHeader P 						
                        ON SPD.ForecastID = P.ForecastID					
                    INNER JOIN SavedMarketInfo M 						
                        ON M.ActuarialMarketID = P.MarketID 						
                    INNER JOIN LkpProductType CPT 						
                        ON P.PlanTypeID = CPT.ProductTypeID
                    INNER JOIN SavedPlanAssumptions SPA
                        ON SPD.ForecastID = SPA.ForecastID
                    WHERE
                        M.ActuarialMarketID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))						
                        AND P.IsHidden = 0
                        AND SPD.MARatingOptionID = 1 --1 = experience rated, which is the default value and will always exist.
                    ORDER BY ActuarialMarket, SPD.ForecastID
			END
-----------------------------------------------------------------------------------------------------------
            ELSE -- Plan Level (SecondaryPayer)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results 
                    SELECT
                        NULL,
                        SPD.ForecastID,					
                        M.ActuarialMarket, 						
                        PlanName = 
                            CASE LEN(P.PlanName) 									
                                WHEN 0 THEN 'None' 									
                                ELSE P.PlanName
                            END,						
                        P.PlanTypeID, 						
                        ProductType, 						
                        P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	,	 --Added SegmentId				
                        StateCountyCode = NULL, 
                        CountyName = NULL,
                        NULL, --Membership					
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
				        NULL, --Risk Factors
                        NULL,
                        NULL,
                        NULL,
                        SPA.SecondaryPayerAdjustment,					
                        DualEligiblePercent = NULL
                    FROM SavedPlanDetail SPD 						
                    INNER JOIN SavedPlanHeader P 						
                        ON SPD.ForecastID = P.ForecastID					
                    INNER JOIN SavedMarketInfo M 						
                        ON M.ActuarialMarketID = P.MarketID 						
                    INNER JOIN LkpProductType CPT 						
                        ON P.PlanTypeID = CPT.ProductTypeID 	
                    INNER JOIN SavedPlanAssumptions SPA
                        ON SPD.ForecastID = SPA.ForecastID	
                    WHERE
                        P.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))						
                        AND P.IsHidden = 0				
                        AND SPD.MARatingOptionID = 1 --1 = experience rated, which is the default value and will always exist.						
                    ORDER BY ActuarialMarket, SPD.ForecastID						
			END
	END

	IF @Duals = 1
	BEGIN
-----------------------------------------------------------------------------------------------------------
        IF @WhereIN IS NULL --All Plans (Duals)
-----------------------------------------------------------------------------------------------------------
		BEGIN
            INSERT @Results 
                SELECT
                    NULL,
                    P.ForecastID,
                    ActuarialMarket, 
                    PlanName = 
                        CASE LEN(P.PlanName) 									
                            WHEN 0 THEN 'None' 									
                            ELSE P.PlanName
                        END,
                    P.PlanTypeID, 
                    ProductType, 
                    P.ContractNumber,
	                P.PlanID,
	                P.SegmentId	,   --Added SegmentId	
                    StateCountyCode = NULL, 
                    CountyName = NULL,
                    NULL, --Membership
                    NULL, 
                    NULL, 
                    NULL, 
                    NULL, 
                    NULL, 
                    NULL,
			        NULL, -- Risk Factors
                    NULL,
                    NULL,
                    NULL,
                    NULL, -- Secondary Payer
                    CAST(0 AS DECIMAL(9,4))
                FROM SavedPlanHeader P 
                INNER JOIN SavedMarketInfo M 
                    ON M.ActuarialMarketID = P.MarketID
                INNER JOIN LkpProductType CPT 
                    ON P.PlanTypeID = CPT.ProductTypeID
                WHERE
                    P.ForecastID > 0
                    AND P.IsHidden = 0
                GROUP BY 
                    P.ForecastID,
                    ActuarialMarket,
                    P.PlanName, 
                    P.PlanTypeID, 
                    ProductType,
                    P.ContractNumber, 
                    P.PlanID, 
                    P.SegmentId   --Added SegmentId	
                ORDER BY 
                    ActuarialMarket, 
                    P.ForecastID
        END

		ELSE
-----------------------------------------------------------------------------------------------------------
            IF @IsPlanLevel = 0 --Market Level (Duals)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results 
                    SELECT
                        NULL,
                        P.ForecastID,
                        ActuarialMarket, 
                        PlanName = 
                            CASE LEN(P.PlanName) 									
                                WHEN 0 THEN 'None' 									
                                ELSE P.PlanName
                            END,
                        P.PlanTypeID, 
                        ProductType, 
                        P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	,   --Added SegmentId	
                        StateCountyCode = NULL, 
                        CountyName = NULL,
                        NULL, --Membership
                        NULL, 
                        NULL, 
                        NULL, 
                        NULL, 
                        NULL,
                        NULL,  
				        NULL, -- Risk Factors
                        NULL,
                        NULL,
                        NULL,
                        NULL, -- Secondary Payer
                        CAST(0 AS DECIMAL(9,4)) 
                    FROM SavedPlanHeader P 
                    INNER JOIN SavedMarketInfo M 
                        ON M.ActuarialMarketID = P.MarketID
                    INNER JOIN LkpProductType CPT 
                        ON P.PlanTypeID = CPT.ProductTypeID
                    WHERE
                        M.ActuarialMarketID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
                        AND P.IsHidden = 0 
                    GROUP BY 
                        P.ForecastID,
                        ActuarialMarket,
                        P.PlanName, 
                        P.PlanTypeID, 
                        ProductType,
                        P.ContractNumber, 
                        P.PlanID, 
                        P.SegmentId   --Added SegmentId	
                    ORDER BY 
                        ActuarialMarket, 
                        P.ForecastID
			END
-----------------------------------------------------------------------------------------------------------
            ELSE -- Plan Level (Duals)
-----------------------------------------------------------------------------------------------------------
			BEGIN
                INSERT @Results 
                    SELECT
                        NULL,
                        P.ForecastID,
                        ActuarialMarket, 
                        PlanName = 
                            CASE LEN(P.PlanName) 									
                                WHEN 0 THEN 'None' 									
                                ELSE P.PlanName
                            END,
                        P.PlanTypeID, 
                        ProductType, 
                        P.ContractNumber,
	                    P.PlanID,
	                    P.SegmentId	,   --Added SegmentId	
                        StateCountyCode = NULL, 
                        CountyName = NULL,
                        NULL, --Membership
                        NULL, 
                        NULL, 
                        NULL, 
                        NULL, 
                        NULL,
                        NULL, 
				        NULL, -- Risk Factors
                        NULL,
                        NULL,
                        NULL,
                        NULL, -- Secondary Payer
                        CAST(0 AS DECIMAL(9,4))
                    FROM SavedPlanHeader P 
                    INNER JOIN SavedMarketInfo M 
                        ON M.ActuarialMarketID = P.MarketID
                    INNER JOIN LkpProductType CPT 
                        ON P.PlanTypeID = CPT.ProductTypeID
                    WHERE
                        P.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
                        AND P.IsHidden = 0
                    GROUP BY 
                        P.ForecastID,
                        ActuarialMarket, 
                        P.PlanName, 
                        P.PlanTypeID, 
                        ProductType, 
                        P.ContractNumber, 
                        P.PlanID, 
                        P.SegmentId --Added SegmentId	
                    ORDER BY 
                        ActuarialMarket, 
                        P.ForecastID 
			END
	END
	RETURN
END
GO
