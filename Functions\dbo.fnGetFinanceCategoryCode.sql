SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetFinanceCategoryCode]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: fnGetFinanceCategoryCode
--
-- CREATOR:  C COFIE
--
-- CREATED DATE:  OCT 13 2007
-- HEADER UPDATED: DEC 09 2010
--
-- DESCRIPTION: Returns Finance Category Code
--
-- PARAMETERS:
--	Input:	@BenefitCategoryID 
--	Output:	@FinanceCategoryCode
--
-- TABLES:
--	Read:	LkpIntFinanceCategory, 
--			LkpIntBenefitCategory
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007 OCT 13			1		Initial Version														C. Cofie
-- 2010 DEC 09			2		Updated for coding standards; removed plan year						Craig Wright
-- 
-- ----------------------------------------------------------------------------------------------------------------------



CREATE FUNCTION [dbo].[fnGetFinanceCategoryCode]
(
	@BenefitCategoryID SMALLINT
)
RETURNS VARCHAR(10) AS  
BEGIN 
	DECLARE @FinanceCategoryCode VARCHAR(10)

	SET  @FinanceCategoryCode = ISNULL(
	(
		SELECT f.FinanceCategoryCode
		FROM LkpIntFinanceCategory f 
		INNER JOIN LkpIntBenefitCategory b 
			ON b.FinanceCategoryID = f.FinanceCategoryID
		WHERE b.BenefitCategoryID = @BenefitCategoryID)
	,'NA')
	
	RETURN  @FinanceCategoryCode

END
GO
