SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author: <PERSON><PERSON><PERSON>   
-- Create date: 05-01-2018
-- Description:  Update  Service Area
--      
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES: SavedForecastSetup,SavedServiceAreaOption,SavedMarketInfo,LkpPlanType , SavedPlanInfo , LkpPFFSNetwork,LkpSNPType,LkpMAPlanDesign
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-------------------------------------------------------------------------------------------------------------------------
-- 2019-June-26  1         Initial Version                                         Kritika Singh

-- ----------------------------------------------------------------------------------------------------------------------                
CREATE PROCEDURE [dbo].[spAppSaveRollupHeaderAndGeneralCharacteristicsData]
 @RollupId smallint ,
 @RollUpName VARCHAR(max),
 @RollUpDescription VARCHAR(max),
 @isSCT bit,
 @isDefault bit,
 @isLocked bit,
 @LastUpdateByID CHAR(7),
 @MessageFromBackend NVARCHAR(500) OUTPUT,
 @Result BIT OUT

 
 AS
BEGIN        
BEGIN TRY 
    BEGIN TRANSACTION;
	SAVE TRANSACTION MySavePoint;
      
        
			
			
			
				BEGIN
				
				Update SavedRollupInfo
				set RollupName=@RollUpName,RollupDescription=@RollUpDescription,
				IsSCT=@isSCT,IsDefaultList=@isDefault,IsRollupLocked=@isLocked
				where RollupID=@RollupId
				
				
				   
			
            
          END ;
       
      
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        SET @Result = 0;
        SET @MessageFromBackend='An error occurred while processing your request. <NAME_EMAIL>.'
        
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
		
		ROLLBACK TRANSACTION MySavePoint; 
		
			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
    END CATCH;  

END;
GO
