SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportTrendUploadActuarialAdjReportingCategoryStagedData
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-Sep-16      1			Initial Version						                               Chaitanya Durga
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spAppImportTrendUploadActuarialAdjReportingCategoryStagedData] 	
(@StageId VARCHAR(100), @isConfirm BIT = 0, @MessageFromBackend VARCHAR(MAX) OUTPUT)
	
AS
BEGIN

	DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;
	DECLARE @AdjustmentType VARCHAR(50);
	DECLARE @InsertCount int=0;
	DECLARE @DeleteCount int=0;
	DECLARE @UpdateCount int=0;
	DECLARE @ModifiedCount int=0;
	DECLARE @ErrorCount int=0;
	DECLARE @Del1 int =0;
	DECLARE @Del2 int=0;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

	   DECLARE @tbl__importData TABLE
    (
        AdjustmentID INT,
        AdjustmentDescription VARCHAR(100),
        CPS CHAR(13),
        PlanYearID INT,
        TrendYearID INT,
		RateType SMALLINT,
		ReportingCategory VARCHAR(50),
		CostAdjustment DECIMAL(18,8),
		UseAdjustment DECIMAL(18,8),		
        LastUpdateByID CHAR(7),
		LastUpdateDateTime DATETIME
    );
	  INSERT INTO @tbl__importData
	  (
	   AdjustmentID ,
        AdjustmentDescription ,
        CPS ,
        PlanYearID ,
        TrendYearID ,
		RateType ,
		ReportingCategory ,
		CostAdjustment,
		UseAdjustment ,		
        LastUpdateByID ,
		LastUpdateDateTime
	  )
    SELECT ISNULL(AdjustmentID,0),
        AdjustmentDescription,
        CPS,
        PlanYearID ,
        TrendYearID ,
		RateType ,
		ReportingCategory ,
		CostAdjustment ,
		UseAdjustment , 	   
           @UserId,
		   GETDATE()
    FROM
        OPENJSON(@jsonData, '$.ActAdjRepCat')
        WITH
        (
           AdjustmentID INT,
        AdjustmentDescription VARCHAR(100),
        CPS CHAR(13),
        PlanYearID INT,
        TrendYearID INT,
		RateType SMALLINT,
		ReportingCategory VARCHAR(50),
		CostAdjustment DECIMAL(18,8),
		UseAdjustment DECIMAL(18,8)
 )

  SET @AdjustmentType = (SELECT DISTINCT TOP(1) ISNULL(ComponentReporting,'Blank') FROM dbo.Trend_SavedComponentInfo WHERE ComponentID = 12)

	   SELECT *  INTO #Temp FROM  @tbl__importData
	   DECLARE @BaseYearID INT =(SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsExperienceYear = 1)
	    DECLARE @AdjustmentIDs [varchar](max);

BEGIN TRY
    BEGIN TRANSACTION


 BEGIN

  if (@isConfirm=0)
  begin 

 SELECT a.AdjustmentID,a.CPS,a.PlanYearID INTO #Update1 FROM #Temp  a 
 LEFT JOIN dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat b ON a.PlanYearID=b.PlanYearID AND a.CPS=b.CPS
 WHERE a.AdjustmentID=b.AdjustmentID AND a.CPS=b.CPS AND a.PlanYearID=b.PlanYearID AND a.TrendYearID=b.TrendYearID 
 AND a.RateType=b.RateType AND a.ReportingCategory=b.ReportingCategory

SELECT DISTINCT a.AdjustmentID,c.CPS INTO #DeleteData1 FROM Trend_ProjProcess_CalcPlanAdjmt_RepCat  a
INNER JOIN SavedPlanInfo c ON a.planinfoid = c.planinfoid  
INNER JOIN #Update1 b ON c.CPS=b.CPS AND a.PlanYearID=b.PlanYearID
 WHERE a.AdjustmentID  NOT IN (SELECT DISTINCT AdjustmentID FROM #Update1 WHERE AdjustmentID IS NOT NULL)  

 SELECT DISTINCT a.AdjustmentID, c.CPS  into #DeleteData2 FROM   Trend_ProjProcess_CalcPlanAdjmt_RepCat  a 
 INNER JOIN SavedPlanInfo c ON a.planinfoid = c.planinfoid 
 INNER JOIN #Temp b ON c.cps=b.cps AND a.PlanYearID=b.PlanYearID
 WHERE  a.AdjustmentID  NOT IN (SELECT DISTINCT AdjustmentID FROM #Update1 WHERE AdjustmentID IS NOT NULL) AND b.AdjustmentID=0 
 AND a.AdjustmentID NOT IN (SELECT DISTINCT AdjustmentID FROM #DeleteData1 WHERE AdjustmentID IS NOT NULL)

  Create table #MsgCps
(Id int identity(1,1),CPS char(13))
insert into #MsgCps
SELECT Distinct CPS  FROM #Temp
DECLARE @Counter INT 
SET @Counter=1
Declare @MSGCPS char(13)
Declare @NoOfCPS int 
set @NoOfCPS= (SELECT COUNT(Distinct CPS) FROM #Temp)

     
	  while ( @Counter <=@NoOfCPS)
	  Begin 
	  
	  SET @ModifiedCount = 0
	  select @MSGCPS=a.CPS from #MsgCps  as a where a.Id=@Counter
	  SELECT @ModifiedCount = COUNT(*)
	  FROM #Temp tmp 
	  INNER JOIN dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat mstr ON mstr.AdjustmentID = tmp.AdjustmentID
	  AND ( mstr.CostAdjustment <> cast(tmp.CostAdjustment as decimal(18,8)) or mstr.UseAdjustment <> cast(tmp.UseAdjustment as decimal(18,8)) ) AND tmp.CPS=@MSGCPS
	  
	  SET @Del1=(SELECT COUNT(*) FROM #DeleteData1 where CPS=@MSGCPS)
	   SET @Del2=(SELECT COUNT(*) FROM #DeleteData2 where CPS=@MSGCPS)
            SET @InsertCount = (SELECT COUNT(*) FROM #Temp WHERE AdjustmentID=0 and CPS=@MSGCPS)
			SET @UpdateCount = (SELECT COUNT(*) FROM #Update1 where CPS=@MSGCPS)
			SET @DeleteCount = (@Del1+@Del2 )
	  Set @MessageFromBackend =Concat(@MessageFromBackend ,'You are about to add-' +Convert(varchar, @InsertCount)+ ' new adjustment(s), modify-' +Convert(varchar, @ModifiedCount)+' adjustment(s), delete-' +Convert(varchar, @DeleteCount)+' adjustment(s), and leave-' +Convert(varchar, (@UpdateCount - @ModifiedCount))+' adjustment(s) unchanged for CPS-'+ @MSGCPS+'.')
	  SET @Counter  = @Counter  + 1
	  SET @ModifiedCount = 0
	  END 

  end
Else
Begin

SELECT @ModifiedCount = COUNT(*)
	        FROM #Temp tmp 
	        INNER JOIN dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat mstr ON mstr.AdjustmentID = tmp.AdjustmentID
	        AND ( mstr.CostAdjustment <> cast(tmp.CostAdjustment as decimal(18,8)) or mstr.UseAdjustment <> cast(tmp.UseAdjustment as decimal(18,8)) )
	
UPDATE b SET b.AdjustmentDescription=a.AdjustmentDescription,b.CostAdjustment=a.CostAdjustment,b.UseAdjustment=a.UseAdjustment,b.AdjustmentType=@AdjustmentType,b.LastUpdateByID=@UserID,LastUpdateDateTime=GETDATE()
 FROM #Temp  a left JOIN dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat b 
 ON a.PlanYearID=b.PlanYearID AND a.CPS=b.CPS
 WHERE a.AdjustmentID=b.AdjustmentID AND a.CPS=b.CPS AND a.PlanYearID=b.PlanYearID
AND a.TrendYearID=b.TrendYearID AND a.RateType=b.RateType
 AND a.ReportingCategory=b.ReportingCategory

SELECT a.AdjustmentID,a.CPS,a.PlanYearID INTO #Update FROM #Temp  a left JOIN dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat b ON a.PlanYearID=b.PlanYearID AND a.CPS=b.CPS
 WHERE a.AdjustmentID=b.AdjustmentID AND a.CPS=b.CPS AND a.PlanYearID=b.PlanYearID AND a.TrendYearID=b.TrendYearID AND a.RateType=b.RateType AND a.ReportingCategory=b.ReportingCategory

SELECT DISTINCT a.AdjustmentID INTO #DeleteData FROM Trend_ProjProcess_CalcPlanAdjmt_RepCat  a inner JOIN #Update b ON a.CPS=b.CPS AND a.PlanYearID=b.PlanYearID
 WHERE a.AdjustmentID  NOT IN (SELECT DISTINCT AdjustmentID FROM #Update)

 DELETE FROM Trend_ProjProcess_CalcPlanAdjmt_RepCat WHERE AdjustmentID IN (SELECT DISTINCT AdjustmentID FROM #DeleteData)

  SELECT a.CPS,a.PlanYearID INTO #DeleteExist FROM #Temp a WHERE a.AdjustmentID=0
 
  SET  @Del2=(SELECT COUNT  ( DISTINCT a.AdjustmentID) FROM Trend_ProjProcess_CalcPlanAdjmt_RepCat  a 
  INNER JOIN SavedPlanInfo c ON a.planinfoid = c.planinfoid 
  INNER JOIN #Temp b ON c.cps=b.cps AND a.PlanYearID=b.PlanYearID
  WHERE  a.AdjustmentID  NOT IN (SELECT DISTINCT AdjustmentID FROM #Update) AND b.AdjustmentID=0)

  DELETE a  FROM Trend_ProjProcess_CalcPlanAdjmt_RepCat  a 
  INNER JOIN SavedPlanInfo c ON a.planinfoid = c.planinfoid 
  INNER JOIN #Temp b ON c.cps=b.cps AND a.PlanYearID=b.PlanYearID
  WHERE  a.AdjustmentID  NOT IN (SELECT DISTINCT AdjustmentID FROM #Update) AND b.AdjustmentID=0

 INSERT INTO dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat
	(
	    AdjustmentType,
	    AdjustmentSubType,
	    AdjustmentDescription,
	    ActAdjOptionID,
	    BaseYearID,
	    PlanInfoID,
	    CPS,
	    PlanYearID,
	    TrendYearID,
	    RateType,
	    ReportingCategory,
	    CostAdjustment,
	    UseAdjustment,
	    LastUpdateByID,
	    LastUpdateDateTime
	)
	             SELECT  @AdjustmentType,
				     	NULL,
						a.AdjustmentDescription, 
						NULL,
						@BaseYearID,
						b.PlanInfoID,
						a.CPS,
						a.PlanYearID,  
						a.TrendYearId,
						a.RateType,
						a.ReportingCategory,
						a.CostAdjustment,
					    a.UseAdjustment,
						@UserID,
						GETDATE()
						FROM #Temp a
						LEFT JOIN dbo.SavedPlanInfo b ON a.CPS=b.CPS AND a.PlanYearID=b.PlanYear
						WHERE a.AdjustmentID=0
 
			SET @Del1=(SELECT COUNT(*) FROM #DeleteData )
            SET @InsertCount = (SELECT COUNT(*) FROM #Temp WHERE AdjustmentID=0)
			SET @UpdateCount = (SELECT COUNT(*) FROM #Update )
			SET @DeleteCount = (@Del1+@Del2 ) 

	
	
	 SET @MessageFromBackend='Successfully Inserted-'+Convert(varchar,@InsertCount)+' Row(s), Successfully Updated-' +Convert(varchar,@ModifiedCount)+' Row(s), Successfully Deleted-' +Convert(varchar,@DeleteCount)+' Row(s) and '+ convert(varchar, (@UpdateCount - @ModifiedCount))+' Row(s) Unchanged.';
     
	  End
	  	
	  END
	    COMMIT TRANSACTION
		
    END TRY
   
BEGIN CATCH

		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()
		
	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			

	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
	ROLLBACK TRANSACTION;
	
     
	---Insert into app log for logging error------------------
	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@UserID;
 END CATCH;
 DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
 END;


GO
