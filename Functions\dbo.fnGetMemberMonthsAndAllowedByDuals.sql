SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	fnGetMemberMonthsAndAllowedByDuals
--
-- CREATOR:			Sandy Ellis
--
-- CREATED DATE:	2009-MAR-19
--
-- DESCRIPTION:		Returns the member month count and allowed split by dual eligible type
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT,
--				    @MARatingOptionID SMALLINT
--
--  Output :		@Results TABLE
--
-- TABLES : 
--	Read :			CalcPlanExperienceByBenefitCat
--					SavedForecastSetup
--					SavedPlanDFSummary
--					SavedPlanRiskFactorDetail 
--					SavedDFFinance
--					SavedPlanStateCountyDetail
--					LkpIntDemogIndicators
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2009-MAR-19      1           Initial Version.                                                    Sandy Ellis
-- 2009-APR-02      2           Added experience risk factors to the output                         Sandy Ellis
-- 2009-APR-04      3           Corrected dual and nondual allowed amounts which were flipped       Sandy Ellis
--                                  and recalc'd memmonths using c&u membership
-- 2009-DEC-02      4           Added a statement for 2011 and changed table from                   Nick Skeen 
--                                  CalcExperiecneSummary to CalcPlanExperienceByBenefitCat
-- 2010-JAN-12      5           Added Hospice                                                       Joe Casey
-- 2010-FEB-11      6           Changed source of Hospice and ESRD experience membership            Casey Sanders
-- 2010-FEB-23      7           Changed call to fnGet[Plan]ESRDDualEligibleMemberMonths  to use 
--                                  ContractNumber and PlanID for PlanyearID <=2010                 Aleksey Titievsky 
-- 2010-MAR-09      8           Implemented new PlanYear methodology and independence for 2012      Joe Casey
-- 2010-JUN-24      9           Added functionality                                                 John Michael Dixon
-- 2010-JUL-06      10          Added prf.IsExperience to Risk Factor Where statement               Joe Casey
-- 2010-AUG-11      11          Removed PlanVersion                                                 Joe Casey
-- 2010-SEP-20      12          Changed 'withoutESRDandHospice' to 'Biddable' in variable and       Joe Casey
--                                  output names.
-- 2010-OCT-14      13          Added ISNULL to calcs so 0's would be returned instead              Michael Siekerka
-- 2010-OCT-14      14          Added reference to fnCUDEPoundMembershipCombined for DE mbrshp      Michael Siekerka
-- 2011-FEB-04      15          Changed join in query1.                                             Casey Sanders
-- 2011-FEB-07      16          Changed reference to fnCUDEPoundMembershipCombined back to          Joe Casey
--                                  SavedCUDEPoundMembership to make sure we gather all membership,
--                                  not just the biddable.
-- 2011-FEB-08      17          Changed query1 to account for DEPound adjustments                   Joe Casey
-- 2012-FEB-02      18          Changed references from SavedCUDEPoundMembership to                 Trevor Mahoney
--                                  SavedCUMembership
-- 2014-FEB-27      19          Modified for Sequestration                                          Mike Deren
-- 2014-NOV-26      20          Renamed tables Saved*CUDetail9mth and SavedCUMembership9mth         Amit Tiwari
--                                  to Saved*CUDetailPrtl and SavedCUMembershipPrtl respectivily
-- 2017-SEP-13      21          Removed SavedCUMembershipPrtl and replaced with SavedCUMembership   Chris Fleming
--                                  removed SQS ID parameter and SQS ID (3,4) logic
--                                  removed reference to SQS ID (1,2) no need for it
-- 2020-JUN-29      22          Backend Alignment and Restructuring                                 Keith Galloway
-- 2020-DEC-14      23          Increasing scale to align BPT risk scores with MRA                  Brent Osantowski
-- 2022-SEP-27		24			@XVariables, WITH (NOLOCK)											Phani Adduri
-- 2024-OCT-08		25			Remove references to SavedCUOverrideDEPound, as this override
--									is no longer used.												Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnGetMemberMonthsAndAllowedByDuals]
    (@ForecastID       INT
    ,@MARatingOptionID SMALLINT)

RETURNS @Results TABLE
    (ForecastID                  INT             NOT NULL
    ,MARatingOptionID            SMALLINT        NOT NULL
    ,NonDualBiddableMemberMonths INT             NOT NULL
    ,DualBiddableMemberMonths    INT             NOT NULL
    ,ESRDNonDualMemberMonths     INT             NOT NULL
    ,ESRDDualMemberMonths        INT             NOT NULL
    ,HospiceNonDualMemberMonths  INT             NOT NULL
    ,HospiceDualMemberMonths     INT             NOT NULL
    ,NonDualAllowedAmt           DECIMAL(24, 6)  NULL
    ,DualAllowedAmt              DECIMAL(24, 6)  NOT NULL
    ,TotalAllowedAmt             DECIMAL(24, 6)  NOT NULL
    ,NonDualRiskFactor           DECIMAL(18, 15)
    ,DualRiskFactor              DECIMAL(18, 15) PRIMARY KEY (
                                                 ForecastID
                                                ,MARatingOptionID))
AS

    BEGIN

        DECLARE @XForecastID                 INT      = @ForecastID
               ,@XMARatingOptionID           SMALLINT = @MARatingOptionID
               ,@DE                          TINYINT
               ,@NonDE                       TINYINT
               ,@NonDualBiddableMemberMonths INT
               ,@DualBiddableMemberMonths    INT
               ,@ESRDNonDualMemberMonths     INT
               ,@ESRDDualMemberMonths        INT
               ,@HospiceNonDualMemberMonths  INT
               ,@HospiceDualMemberMonths     INT
               ,@NonDualAllowedAmt           DECIMAL(24, 6)
               ,@DualAllowedAmt              DECIMAL(24, 6)
               ,@TotalAllowedAmt             DECIMAL(24, 6)
               ,@NonDualRiskFactor           DECIMAL(18, 15)
               ,@DualRiskFactor              DECIMAL(18, 15);

        SET @NonDE = 0;
        SET @DE = 1;

        DECLARE @SavedDFFinance TABLE
            (PlanInfoID     SMALLINT
            ,DemogIndicator TINYINT
            ,MemberMonths   INT);

        INSERT INTO @SavedDFFinance
        SELECT      DFS.PlanInfoID
                   ,Mbrs.DemogIndicator
                   ,MemberMonths = SUM (Mbrs.MemberMonths)
        FROM        dbo.SavedForecastSetup SFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
               ON DFS.ForecastID = SFS.ForecastID
       INNER JOIN   dbo.SavedDFFinance Mbrs WITH (NOLOCK)
               ON Mbrs.DFVersionID = SFS.DFVersionID
                  AND   Mbrs.PlanInfoID = DFS.PlanInfoID
       INNER JOIN   dbo.LkpIntDemogIndicators DI WITH (NOLOCK)
               ON DI.DemogIndicator = Mbrs.DemogIndicator
        WHERE       SFS.ForecastID = @XForecastID
                    AND DFS.MARatingOptionID = @XMARatingOptionID
                    AND DI.DualEligibleTypeID <> 2  --Excluding pre-summed values for Demog Indicators as not all values are stored in SavedDFFinance
        GROUP BY    DFS.PlanInfoID
                   ,Mbrs.DemogIndicator;

        BEGIN
            SELECT      @NonDualBiddableMemberMonths = ISNULL (
                                                       SUM (
                                                       CASE DI.IsBiddable WHEN 1 THEN
                                                                              CASE DI.DualEligibleTypeID WHEN 0 THEN
                                                                                                     query1.MemberMonthCount ELSE
                                                                                                                             0 END
                                                                          ELSE 0 END)
                                                      ,0)
                       ,@DualBiddableMemberMonths = ISNULL (
                                                    SUM (
                                                    CASE DI.IsBiddable WHEN 1 THEN
                                                                           CASE DI.DualEligibleTypeID WHEN 1 THEN
                                                                                                  query1.MemberMonthCount ELSE
                                                                                                                          0 END
                                                                       ELSE 0 END)
                                                   ,0)
                       ,@ESRDNonDualMemberMonths = ISNULL (
                                                   SUM (
                                                   CASE DI.IsESRD WHEN 1 THEN
                                                                      CASE DI.DualEligibleTypeID WHEN @NonDE THEN
                                                                                             query1.MemberMonthCount ELSE
                                                                                                                     0 END
                                                                  ELSE 0 END)
                                                  ,0)
                       ,@ESRDDualMemberMonths = ISNULL (
                                                SUM (
                                                CASE DI.IsESRD WHEN 1 THEN
                                                                   CASE DI.DualEligibleTypeID WHEN @DE THEN
                                                                                          query1.MemberMonthCount ELSE
                                                                                                                  0 END
                                                               ELSE 0 END)
                                               ,0)
                       ,@HospiceNonDualMemberMonths = ISNULL (
                                                      SUM (
                                                      CASE DI.IsHospice WHEN 1 THEN
                                                                            CASE DI.DualEligibleTypeID WHEN @NonDE THEN
                                                                                                   query1.MemberMonthCount ELSE
                                                                                                                           0 END
                                                                        ELSE 0 END)
                                                     ,0)
                       ,@HospiceDualMemberMonths = ISNULL (
                                                   SUM (
                                                   CASE DI.IsHospice WHEN 1 THEN
                                                                         CASE DI.DualEligibleTypeID WHEN @DE THEN
                                                                                                query1.MemberMonthCount ELSE
                                                                                                                        0 END
                                                                     ELSE 0 END)
                                                  ,0)
            FROM
            --Combining Member Months.  We are not focused on just biddable members
                        (SELECT             DI.DemogIndicator
                                           ,MemberMonthCount = SUM (Mbrs.MemberMonths)
                         FROM               @SavedDFFinance Mbrs
                        RIGHT JOIN          dbo.LkpIntDemogIndicators DI WITH (NOLOCK)
                                ON Mbrs.DemogIndicator = DI.DemogIndicator
                         LEFT JOIN          (SELECT         Mbrs.PlanInfoID
                                                           ,DI.IsESRD
                                                           ,DI.IsHospice
                                                           ,CombinedMM = SUM (Mbrs.MemberMonths)
                                             FROM           @SavedDFFinance Mbrs
                                            INNER JOIN      dbo.LkpIntDemogIndicators DI WITH (NOLOCK)
                                                    ON DI.DemogIndicator = Mbrs.DemogIndicator
                                             GROUP  BY      Mbrs.PlanInfoID
                                                           ,DI.IsESRD
                                                           ,DI.IsHospice) MT
                                ON MT.PlanInfoID = Mbrs.PlanInfoID
                                   AND  DI.IsESRD = MT.IsESRD
                                   AND  DI.IsHospice = MT.IsHospice
                         GROUP      BY      DI.DemogIndicator
                                           ,DI.DualEligibleTypeID) query1
           INNER JOIN   dbo.LkpIntDemogIndicators DI WITH (NOLOCK)
                   ON query1.DemogIndicator = DI.DemogIndicator;
        END;
        --Allowed Amts--------------------------------------------------
        SELECT  @NonDualAllowedAmt = ISNULL (
                                     SUM (
                                     CASE WHEN PEB.DualEligibleTypeID = 0 THEN PEB.INAllowed + PEB.OONAllowed ELSE 0 END)
                                    ,0)
               ,@DualAllowedAmt = ISNULL (
                                  SUM (
                                  CASE WHEN PEB.DualEligibleTypeID = 1 THEN PEB.INAllowed + PEB.OONAllowed ELSE 0 END)
                                 ,0)
               ,@TotalAllowedAmt = ISNULL (
                                   SUM (
                                   CASE WHEN PEB.DualEligibleTypeID = 2 THEN PEB.INAllowed + PEB.OONAllowed ELSE 0 END)
                                  ,0)
        FROM    dbo.CalcPlanExperienceByBenefitCat PEB WITH (NOLOCK)
        WHERE   PEB.ForecastID = @XForecastID
                AND PEB.MARatingOptionID = @XMARatingOptionID;

        --Risk Factors--------------------------------------------------
        --NonDual
        SELECT      @NonDualRiskFactor = ISNULL (MAX (prf.RiskFactor), 0)
        FROM        dbo.SavedPlanRiskFactorDetail prf
       INNER JOIN   (SELECT DISTINCT
                            StateTerritoryID
                           ,CountyCode
                     FROM   dbo.SavedPlanStateCountyDetail WITH (NOLOCK)
                     WHERE  ForecastID = @XForecastID
                            AND IsCountyExcludedFromBPTOutput = 0) spscd
               ON spscd.StateTerritoryID = prf.StateTerritoryID
                  AND   spscd.CountyCode = prf.CountyCode
       INNER JOIN   dbo.LkpIntDemogIndicators d WITH (NOLOCK)
               ON d.DemogIndicator = prf.DemogIndicator
        WHERE       prf.ForecastID = @XForecastID
                    AND d.DualEligibleTypeID = 0
                    AND prf.IsExperience = 1;

        --Dual
        SELECT      @DualRiskFactor = ISNULL (MAX (prf.RiskFactor), 0)
        FROM        dbo.SavedPlanRiskFactorDetail prf WITH (NOLOCK)
       INNER JOIN   (SELECT DISTINCT
                            StateTerritoryID
                           ,CountyCode
                     FROM   dbo.SavedPlanStateCountyDetail WITH (NOLOCK)
                     WHERE  ForecastID = @XForecastID
                            AND IsCountyExcludedFromBPTOutput = 0) spscd
               ON spscd.StateTerritoryID = prf.StateTerritoryID
                  AND   spscd.CountyCode = prf.CountyCode
       INNER JOIN   dbo.LkpIntDemogIndicators d WITH (NOLOCK)
               ON d.DemogIndicator = prf.DemogIndicator
        WHERE       prf.ForecastID = @XForecastID
                    AND d.DualEligibleTypeID = 1
                    AND prf.IsExperience = 1;

        ----------------------------------------------------------------
        INSERT INTO @Results
        SELECT  @XForecastID AS ForecastID
               ,@XMARatingOptionID AS MARatingOptionID
               ,@NonDualBiddableMemberMonths AS NonDualBiddableMemberMonths
               ,@DualBiddableMemberMonths AS DualBiddableMemberMonths
               ,@ESRDNonDualMemberMonths AS ESRDNonDualMemberMonths
               ,@ESRDDualMemberMonths AS ESRDDualMemberMonths
               ,@HospiceNonDualMemberMonths AS HospiceNonDualMemberMonths
               ,@HospiceDualMemberMonths AS HospiceDualMemberMonths
               ,@NonDualAllowedAmt AS NonDualAllowedAmt
               ,@DualAllowedAmt AS DualAllowedAmt
               ,@TotalAllowedAmt AS TotalAllowedAmt
               ,@NonDualRiskFactor AS NonDualRiskFactor
               ,@DualRiskFactor AS DualRiskFactor;

        RETURN;

    END;
GO
