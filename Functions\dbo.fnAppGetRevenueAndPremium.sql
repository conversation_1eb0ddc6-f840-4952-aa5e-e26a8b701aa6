SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetRevenueandPremium
--
-- AUTHOR: Josh Pace
--
-- CREATED DATE: 2020-May-12
--
-- DESCRIPTION: Pulls fields needed to populate the Revenue & Premium section of Plan Level Main in MAAUI.
--
-- PARAMETERS:
--  Input: 
--		@ForecastID INT
--
-- RETURNS:
--      A recordset containing the rebate allocation information.
--
-- TABLES: 
--  Read:   CalcFinalPremium
--          SavedForecastSetup
--          SavedPlanAssumptions
--          PerExtCMSValues
--          CalcPlanESRDSubsidy
--          SavedPlanInfo
--          CalcPlanAdminBlend
--          SavedPlanAddedBenefits
--          LkpIntAddedBenefitExpenseDetail
--          MAReportPlanLevel
--
--  Write:  NONE
--
-- VIEWS:
--  Read:   NONE
--
-- FUNCTIONS:
--  Read:   fnGetMARebateAllocation
--          fnAppGetMABPTWS4Expenses
--  Called: NONE
--
-- STORED PROCS: 
--  Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                             	    DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-May-12          1       Initial version.                                                Josh Pace
-- 2020-Jun-26			2		Correct formula for @LISMemberPremium							Josh Pace
-- 2020-Jul-06          3       Updated logic for PlanIntentionPDBasicPremium                   Satyam Singhal
-- 2020-Sep-01          4       Updated logic for Rebate Order                                  Brent Osantowski
-- 2020-Nov-09          4       Pull rebate values from MAReportPlanLevel                       Brent Osantowski
-- 2024-Jan-29			5		Use same DirectAdmin field as BPT								Adam Gilbert
-- 2024-Feb-2			6		Safe Division for MERPCT and TotalReqRevenue					Adam Gilbert
-- 2024-May-05      	7		Added NOLOCK Table Hint											Kiran Kola
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetRevenueAndPremium]
(
	@ForecastID INT
)
RETURNS @Results TABLE
    (
	 ForecastID INT,
	 RebateAllocationOption CHAR(15),
	 ReduceABCostShare DECIMAL(9,2),
	 ReduceABCostShareMax DECIMAL(10,2),
	 PartBPremiumBuydown DECIMAL(9,2),
	 PartBPremiumBuydownMax DECIMAL(9,2),
	 SuppPremiumBuydown DECIMAL(9,2),
	 SuppPremiumBuydownMax DECIMAL(10,2),
	 RxBasicPremiumBuydown DECIMAL(8,1),
	 RxBasicPremiumBuydownMax DECIMAL(9,2),
	 RxSuppBuydown DECIMAL(9,2),
	 RxSuppBuydownMax DECIMAL(9,2),
	 TotalRebate DECIMAL(8,2),
	 UnallocatedRebate DECIMAL(10,2),
	 BasicMemberPremium DECIMAL(9,2),
	 ReduceABCostSharePrem DECIMAL(9,2),
	 SuppPremium DECIMAL(9,2),
	 RxBasicPremium DECIMAL(9,2),
	 RxSuppPremium DECIMAL(9,2),
	 TotalMemberPremium DECIMAL(9,2),
	 RoundedMemberPrem DECIMAL(9,2),
	 LISMemberPremium DECIMAL(9,2),
	 PlanIntentionPDBasicPremium CHAR(40),
	 Medicare2ndPayerAdj DECIMAL(7,6),
	 PlanBid DECIMAL(9,2),
	 PlanBenchmark DECIMAL(9,2),
	 StandardBid DECIMAL(9,2),
	 StandardBenchmark DECIMAL(9,2),
	 TotalRevenueRequired DECIMAL(9,2),
	 TotalAdmin DECIMAL(9,2),
	 TotalDirectAdmin DECIMAL(9,2),
	 DirectAdmin DECIMAL(9,2),
	 QualityInitiatives DECIMAL(9,2),
	 TaxesandFees DECIMAL(9,2),
	 UncollectedPrem DECIMAL(9,2),
	 UserFees DECIMAL(9,2),
	 IndirectAdmin DECIMAL(9,2),
	 MarketingandSales DECIMAL(9,2),
	 InsurerFees DECIMAL(9,2),
	 Profit DECIMAL(9,2),
	 TotalNetClaims DECIMAL(9,2),
     TotalAdminPct DECIMAL(10,8),
	 ProfitPct DECIMAL(10,8),
	 MERPCt DECIMAL(10,8)
    )

AS
BEGIN

    DECLARE
     @RebateAllocationOption CHAR(15),
	 @ReduceABCostShare DECIMAL(9,2),
	 @ReduceABCostShareMax DECIMAL(10,2),
	 @PartBPremiumBuydown DECIMAL(9,2),
	 @PartBPremiumBuydownMax DECIMAL(9,2),
	 @SuppPremiumBuydown DECIMAL(10,2),
	 @SuppPremiumBuydownMax DECIMAL(9,2),
	 @RxBasicPremiumBuydown DECIMAL(8,1),
	 @RxBasicPremiumBuydownMax DECIMAL(9,2),
	 @RxSuppBuydown DECIMAL(9,2),
	 @RxSuppBuydownMax DECIMAL(9,2),
	 @TotalRebate DECIMAL(8,2),
	 @UnallocatedRebate DECIMAL(10,2),
	 @BasicMemberPremium DECIMAL(9,2),
	 @ReduceABCostSharePrem DECIMAL(9,2),
	 @SuppPremium DECIMAL(9,2),
	 @RxBasicPremium DECIMAL(9,2),
	 @RxSuppPremium DECIMAL(9,2),
	 @TotalMemberPremium DECIMAL(9,2),
	 @RoundedMemberPrem DECIMAL(9,2),
	 @LISMemberPremium DECIMAL(9,2),
	 @PlanIntentionPDBasicPremium CHAR(40),
	 @Medicare2ndPayerAdj DECIMAL(7,6),
	 @PlanBid DECIMAL(9,2),
	 @PlanBenchmark DECIMAL(9,2),
	 @StandardBid DECIMAL(9,2),
	 @StandardBenchmark DECIMAL(9,2),
	 @TotalRevenueRequired DECIMAL(9,2),
	 @TotalAdmin DECIMAL(9,2),
	 @TotalDirectAdmin DECIMAL(9,2),
	 @DirectAdmin DECIMAL(9,2),
	 @QualityInitiatives DECIMAL(9,2),
	 @TaxesandFees DECIMAL(9,2),
	 @UncollectedPrem DECIMAL(9,2),
	 @UserFees DECIMAL(9,2),
	 @IndirectAdmin DECIMAL(9,2),
	 @MarketingandSales DECIMAL(9,2),
	 @InsurerFees DECIMAL(9,2),
	 @Profit DECIMAL(9,2),
	 @TotalNetClaims DECIMAL(9,2),
	 @TotalAdminPct DECIMAL(10,8),
	 @ProfitPct DECIMAL(10,8),
	 @MERPCt DECIMAL(10,8)

	SELECT 
		@RebateAllocationOption =  roi.RebateOrderDescription,
	    @PartBPremiumBuydown = ROUND(sfs.PartBPremiumBuydown,2)
	FROM dbo.SavedForecastSetup sfs WITH (NOLOCK)
		INNER JOIN lkpRebateOrderID roi ON roi.RebateOrderID = sfs.AltRebateOrder
	WHERE sfs.ForecastID=@ForecastID 

	SELECT 
	    @PartBPremiumBuydownMax = pec.PartBMaxRebateAllocation
	FROM dbo.PerExtCMSValues pec WITH (NOLOCK)

	SELECT   
		@ReduceABCostShare = mrp.CostShareReduction,
		@SuppPremiumBuydown = mrp.TotalOtherSuppBen,
		@RxBasicPremiumBuydown = mrp.RxBasicBuyDown,
	    @RxSuppBuydown = mrp.RxSuppBuyDown 
	FROM dbo.MAReportPlanLevel  mrp WITH (NOLOCK)
        INNER JOIN dbo.SavedForecastSetup sfs WITH (NOLOCK) ON sfs.ForecastID = mrp.ForecastID
		INNER JOIN dbo.SavedPlanInfo spi WITH (NOLOCK)
			ON spi.PlanInfoID = sfs.PlanInfoID		
			AND spi.planyear = mrp.PlanYearID
	WHERE mrp.ForecastID = @ForecastID

	SELECT 
	    @RxBasicPremiumBuydownMax = ROUND(spa.RxBasicPremium,2),
	    @RxSuppBuydownMax = ROUND(spa.RxSuppPremium,2),
		@Medicare2ndPayerAdj = SecondaryPAyerAdjustment,
		@ProfitPct = ProfitPercent
	FROM dbo.SavedPlanAssumptions spa WITH (NOLOCK)
	WHERE spa.ForecastID=@ForecastID

	SELECT 
	    @TotalRebate = ROUND(cfp.Rebate,2),
		@BasicMemberPremium = ROUND(cfp.BasicMemberPremium,2),
		@PlanBid = ROUND(cfp.PlanBid,2),
	    @PlanBenchmark = ROUND(cfp.PlanBenchmark,2),
	    @StandardBid = ROUND(cfp.StandardizedBid,2),
	    @StandardBenchmark = ROUND(cfp.StandardizedBenchmark,2),
		@TotalRevenueRequired = ROUND(cfp.TotalReqRev + ISNULL(cpes.TotalESRDSubsidy,0),2),
		@UncollectedPrem = ROUND(cfp.UncollectedPremium,2),
	    @UserFees = ROUND(cfp.UserFee,2),
		@TotalNetClaims = ROUND(cfp.NetNonESRD,2),
		@MERPCt = dbo.fnGetSafeDivisionResult(cfp.NetNonESRD,(cfp.TotalReqRev + ISNULL(cpes.TotalESRDSubsidy,0))),
		@InsurerFees = ROUND(cfp.InsurerFee,2),
		@Profit = ROUND(cfp.Profit,2),
		@ReduceABCostShareMax = CONVERT(DECIMAL(10,2), ROUND((cfp.TotalReqRev * cfp.CostShareReductionPct),2)),
		@SuppPremiumBuydownMax = CONVERT(DECIMAL(10,2),
				       ROUND((cfp.TotalReqRev * (1-cfp.MedicareCoveredPct)) + ISNULL(cpes.TotalESRDSubsidy,0)
					   - ROUND((cfp.TotalReqRev * cfp.CostShareReductionPct),2)
					   ,2))
	FROM dbo.CalcFinalPremium cfp WITH (NOLOCK)
		LEFT JOIN dbo.CalcPlanESRDSubsidy cpes ON cpes.ForecastID=cfp.ForecastID
	WHERE cfp.ForecastID=@ForecastID

	SELECT
	    @TotalDirectAdmin = ROUND(DirectAdmin,2),
		@MarketingandSales = ROUND(MarketingSales,2),
		@DirectAdmin = ROUND(Direct_BasicAndSelectableAdmin,2),
		@QualityInitiatives = ROUND(Direct_BasicAndSelectableQuality,2),
		@TaxesandFees = ROUND(Direct_TaxesAndFees,2),
		@IndirectAdmin = ROUND(InDirectAdmin,2),
		@TotalAdmin = ROUND(TotalNonBenefitExpense,2)
    FROM dbo.fnAppGetMABPTWS4Expenses(@ForecastID)

	SET @ReduceABCostSharePrem = @ReduceABCostShareMax-@ReduceABCostShare --@ReduceABCostSharePrem DECIMAL (9,2)
	SET @SuppPremium = @SuppPremiumBuydownMax-@SuppPremiumBuydown --@SuppPremium DECIMAL(9,2),
	SET @RxBasicPremium = @RxBasicPremiumBuydownMax-@RxBasicPremiumBuydown  --@RxBasicPremium DECIMAL(9,2),
	SET @RxSuppPremium = @RxSuppBuydownMax-@RxSuppBuydown --@RxSuppPremium DECIMAL(9,2),
	SET @TotalMemberPremium = @BasicMemberPremium + @ReduceABCostSharePrem + @SuppPremium + @RxBasicPremium + @RxSuppPremium
	SET @RoundedMemberPrem = ROUND (@TotalMemberPremium,1)
	SET @UnallocatedRebate = CONVERT (DECIMAL (10,2),ROUND(@TotalRebate - @ReduceABCostShare - @SuppPremiumBuydown - ROUND(@PartBPremiumBuydown,1) - ROUND(@RxBasicPremiumBuydown,1) - ROUND(@RxSuppBuydown,1),2))
	SET @LISMemberPremium = ROUND(@BasicMemberPremium + @ReduceABCostSharePrem + @SuppPremium + @RxSuppPremium,1)
	SET @TotalAdminPct = dbo.fnGetSafeDivisionResult(@TotalAdmin,@TotalRevenueRequired)

	SELECT 
	    @PlanIntentionPDBasicPremium = lpi.PlanIntentionMessage
	FROM dbo.SavedForecastSetup sfs WITH (NOLOCK)
		Left JOIN	dbo.LkpPlanIntention lpi ON sfs.PlanIntentionID=lpi.PlanIntentionID
	WHERE sfs.ForecastID=@ForecastID 

    INSERT @Results
    VALUES
        (
		@ForecastID,
		@RebateAllocationOption,
        @ReduceABCostShare,
	    @ReduceABCostShareMax,
	    @PartBPremiumBuydown,
	    @PartBPremiumBuydownMax,
    	@SuppPremiumBuydown,
	    @SuppPremiumBuydownMax,
	    @RxBasicPremiumBuydown,
	    @RxBasicPremiumBuydownMax,
	    @RxSuppBuydown,
    	@RxSuppBuydownMax,
	    @TotalRebate,
	    @UnallocatedRebate,
	    @BasicMemberPremium,
	    @ReduceABCostSharePrem, 
	    @SuppPremium, 
	   	@RxBasicPremium,  
	    @RxSuppPremium, 
	    @TotalMemberPremium,
	    @RoundedMemberPrem,
	    @LISMemberPremium,
	    @PlanIntentionPDBasicPremium,
	    @Medicare2ndPayerAdj,
	    @PlanBid,
	    @PlanBenchmark,
	    @StandardBid,
    	@StandardBenchmark,
	    @TotalRevenueRequired,
	    @TotalAdmin,
	    @TotalDirectAdmin,
	    @DirectAdmin,
	    @QualityInitiatives,
	    @TaxesandFees,
	    @UncollectedPrem,
    	@UserFees,
	    @IndirectAdmin,
	    @MarketingandSales,
    	@InsurerFees,
	    @Profit,
	    @TotalNetClaims,
        @TotalAdminPct,
		@ProfitPct,
		@MERPct
        )
    RETURN
END
GO
