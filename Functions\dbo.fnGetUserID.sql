SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---  
-- FUNCTION NAME: fnGetUserID  
--  
-- AUTHOR:  <PERSON><PERSON><PERSON>  
--  
-- CREATED DATE: 2009-Mar-25  
--  
-- DESCRIPTION: Returns to MAMBA the Network UserID set in MAPD.AdminUserHeader  a user  
--              when correct username or UserEmail  is passed.  
--              Based on spGetElementPermissions.   
--              Test: select  dbo.fnGetUserID ('Aleksey','Titievky','ati')  
--               
-- PARAMETERS:  
--  Input:  
--      @UserFirstName VARCHAR(25) = '' optional,  
--      @UserLastName VARCHAR(25) = '' optional,  
--      @UserEmail VARCHAR(25) = '' optional,  
--         
--  Output:   
--  
-- RETURNS:   
--    USERID VARCHAR  
--  
-- TABLES:  
--  Read:  
--    AdminUserHeader  
  
--  Write:  
--    None  
--  
-- VIEWS: Read: None  
--  
-- STORED PROCS: Executed: None  
--  
-- $HISTORY   
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---  
-- DATE         VERSION    CHANGES MADE                                              DEVELOPER    
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---  
-- 2009-Mar-25        1    Initial version.                                             Aleksey Titievsky  
--2019-oct-30		  2	   Removed 'HUMAD\' to UserID									Chhavi Sinha
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---  
CREATE FUNCTION [dbo].[fnGetUserID]  
    (  
    @UserFirstName VARCHAR(25) = '',  
    @UserLastName VARCHAR(25) = '',  
    @UserEmail VARCHAR(25) = ''  
    )  
    RETURNS VARCHAR(13)  
  
AS  
BEGIN  
    DECLARE @UserID VARCHAR(7)  
    SELECT @UserID =   
        (  
        SELECT   TOP 1 UserId  
        FROM AdminUserHeader h  
        WHERE h.IsEnabled = 1  
            AND   
            (  
                (  
                    UserLastName = RTRIM(@UserLastName)   
                    AND  
                    UserFirstName = RTRIM(@UserFirstName)  
                )      
                OR   
                (  
                    CHARINDEX(@UserEmail,UserEmail) = 1    
                    AND  
                    LEN(@UserEmail) > 2  
                )  
            )  
        )   
    RETURN @UserID  
END
GO
