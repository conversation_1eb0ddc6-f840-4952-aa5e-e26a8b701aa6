SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetServiceAreaExtract]  ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetServiceAreaExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-14
-- HEADER UPDATED: 2011-Dec-15
--
-- DESCRIPTION: Designed to extract fields for the Service Area - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	SavedPlanStateCountyDetail
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-14		1			Initial Version														Alex Rezmerski
-- 2011-Dec-15		2			Updated for coding standards.  Added Case statement to account		Craig Wright
--									for @WhereIn being Null. Added StateTerritoryID to CtyCode.
--									Added IsCountyExcludedFromBPTOutput
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetServiceAreaExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(
    ForecastID [int] NOT NULL,
	CountyCode VARCHAR(5)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT ForecastID,
				  CountyCode = CAST(dbo.fnPadInteger(StateTerritoryID, 2) + CountyCode AS VARCHAR(5))
			FROM SavedPlanStateCountyDetail
			WHERE IsCountyExcludedFromBPTOutput = 0
       
	ELSE
		INSERT @Results
			SELECT ForecastID,
				  CountyCode = CAST(dbo.fnPadInteger(StateTerritoryID, 2) + CountyCode AS VARCHAR(5))
			FROM SavedPlanStateCountyDetail
			WHERE IsCountyExcludedFromBPTOutput = 0
				AND ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))

RETURN
END
GO
