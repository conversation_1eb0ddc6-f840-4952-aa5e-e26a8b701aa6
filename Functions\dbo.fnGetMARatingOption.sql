SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetMARatingOption]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:fnGetMARatingOption
--
-- AUTHOR: Christian Cofie 
--
-- CREATED DATE: 2008-May-02
-- HEADER UPDATED: 2010-Aug-23
--
-- DESCRIPTION: Function responsible for getting MA Rating Option ID based on Member Month Credibility.
--
-- PARAMETERS:
--	Input:
--		@ForecastID 
--	Output:
--
-- TABLES: 
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetCredibilityFactor
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-May-02		1			Initial Version														Christian Cofie
-- 2008-May-24		2			As per Sandy's email - Modified to get credibilityfactor			Sumana Bellary
--									 from dbo.fnGetCredibilityFactor and removed "FROM
--									 CalcPlanCountySummary c and where" clause
-- 2009-Mar-17      3			Data types															Sandy Ellis
-- 2010-Aug-23		4			Removed PlanYearID and PlanVersion									Joe Casey
-- 2023-Aug-03		5			Added Internal parameter											Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMARatingOption]
    (
    @ForecastID INT
    )
RETURNS TINYINT AS  

BEGIN

    DECLARE @MARatingOptionID AS TINYINT
    DECLARE @CredibilityFactor AS FLOAT
	DECLARE @XForecastID INT = @ForecastID 

    SET @CredibilityFactor = dbo.fnGetCredibilityFactor(@XForecastID)
    SELECT
        @MARatingOptionID = 
            CASE
                WHEN @CredibilityFactor= 1
                        THEN 1 --Experience: Cred=100%
                WHEN @CredibilityFactor = 0
                        THEN 2 --Manual: Cred=0%
                ELSE 3 --Blended: Cred between 0% and 100%
            END
    
    RETURN @MARatingOptionID

END
GO
