SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO


-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportRelatedPartySubstantiationStagedData
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-Nov-18      1			Initial Version						                                Archana Sahu
-- 2025-Apr-28		2			Removed IDENTITY Columns											Vikrant Bagal
-- ----------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spAppImportRelatedPartySubstantiationStagedData]
(@StageId VARCHAR(100))
AS
BEGIN
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

    DECLARE @tbl__importData TABLE
    (
        RelatedParty VARCHAR(50),
        PlanYearID INT,
        AddedBenefitName VARCHAR(100),
        Totalz1 decimal(6,2),
        Totalz2 DECIMAL(6,2),
        UserID CHAR(7)
    );

    INSERT INTO @tbl__importData
    SELECT 				
			RelatedParty,
			PlanYearID,
			AddedBenefitName,
			Totalz1,
			Totalz2,
			@UserId
    FROM
        OPENJSON(@jsonData, '$.RelatedPartySubstantiation')
        WITH
        (
            RelatedParty VARCHAR(50),
			PlanYearID INT,
			AddedBenefitName VARCHAR(100),
			Totalz1 decimal(6,2),
			Totalz2 DECIMAL(6,2)
        );

		DELETE FROM dbo.SavedRelatedPartyDetail WHERE 1=1;
		DELETE FROM	dbo.LkpIntRelatedParty WHERE 1=1;		  

	INSERT INTO dbo.LkpIntRelatedParty (RelatedPartyID, RelatedPartyName, LastUpdateByID, LastUpdateDateTime) 
	SELECT DISTINCT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)),RelatedParty, @UserId, GETDATE() FROM @tbl__importData

	INSERT INTO dbo.SavedRelatedPartyDetail
	(RelatedPartyDetailID,PlanYearID, AddedBenefitTypeID, RelatedPartyID, Totalz1, Totalz2, LastUpdateByID, LastUpdateDateTime)
	SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL))	  RelatedPartyDetailID,tmp.PlanYearID, benefit.AddedBenefitTypeID, relatedpty.RelatedPartyID, tmp.Totalz1, tmp.Totalz2, @UserId, GETDATE() 
	FROM @tbl__importData tmp INNER JOIN dbo.LkpIntAddedBenefitType benefit ON tmp.AddedBenefitName = benefit.AddedBenefitName
	INNER JOIN dbo.LkpIntRelatedParty relatedpty ON tmp.RelatedParty = relatedpty.RelatedPartyName
	
	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
GO
