SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: dbo.Trend_Reporting_spCalcCompiledImpacts
--
-- CREATOR: <PERSON> Lewis 
--
-- CREATED DATE: 2020-09-17
--
-- DESCRIPTION: This procedure aggregates many tables for the final table that feeds the Trend Impact Report.
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--				@PlanInfoID
--
--  Output : NONE
--
-- TABLES : Read :	LkpIntBenefitCategory
--					LkpIntPlanYear
--					LkpProjectionVersion
--					Trend_CalcHistoricCostAndUse
--					Trend_CalcHistoricMembership
--					Trend_Reporting_CalcImpactHistoric
--					Trend_Reporting_CalcImpactProjected_forRollup
--					Trend_SavedComponentInfo
--					
--          Write: Trend_Reporting_CalcCompiledImpacts
--                 Trend_Reporting_Log
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS:	Trend_fnCalcStringToTable
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER  
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- OCT-15-2020			1		Initial Version																								Jake Lewis				
-- OCT-18-2020			2		Changed lines 725 and 689 to include membership																Tanvi Khanna		
-- OCT-19-2020			3		Fixed issues with sonar qube																				Deepali Mittal
-- OCT-27-2020			4		Added lines 374 and 493 to exclude a NULL BidCPS															Tanvi Khanna
-- NOV-10-2020			5		Bring in member months directly from the CalcImpactProjected												Jake Lewis
--								and CalcPlanImpactHistoric tables		
-- DEC-10-2020			6		Name changes for the SP and some of the referenced tables:													Jake Lewis
--								NEW SP = Trend_Reporting_spCalcCompiledImpacts, OLD SP = Trend_Reporting_spCalcCompiledAllImpacts
--								NEW = Trend_Reporting_CalcCompiledImpacts, OLD = Trend_Reporting_CalcCompiledAllImpacts
--								NEW = Trend_Reporting_CalcImpactProjected_forRollup, OLD = Trend_Reporting_CalcImpactProjected_CountyXWalk
--								NEW = Trend_Reporting_CalcImpactHistoric, OLD = Trend_Reporting_CalcPlanImpactHistoric
-- DEC-29-2020			7		Add WITH(TABLOCK) to DELETE and INSERT statements to avoid													Jake Lewis
--								data issues when users refresh the trend reports.  
-- MAR-15-2021			8		Code adjustments to improve efficiency and decrease runtime													Jake Lewis
--								Add PlanInfoID input parameter
-- Aug-03-2023          9       Added internal parameters,Added WITH (NOLOCK) to tables and Dropped temp                                    Sowmya K
--                              tables which are not further used in SP.
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_Reporting_spCalcCompiledImpacts]

@LastUpdateByID CHAR(7)
,@PlanInfoID    VARCHAR(MAX) = NULL

AS

    BEGIN

		DECLARE @XLastUpdateByID CHAR(7)=@LastUpdateByID
		DECLARE @XPlanInfoID    VARCHAR(MAX) = @PlanInfoID  
        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

		DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            ----------------------------------------------------
            -- 0. Declare / set variables and pull parameters --
            ----------------------------------------------------
            --Get year bounds applicable to reporting
            DECLARE @MinHistoricYear INT;
            DECLARE @CurrentYear INT;
            DECLARE @BidYear INT;
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);
            DECLARE @LastUpdateDateTime DATETIME;

            SELECT      @MinHistoricYear = CASE WHEN pv.LastCurrentYearQuarter = 0 THEN PlanYearID - 6 --when there is not at least one quarter of current year data
                                                ELSE py.PlanYearID - 5 END
                       ,@CurrentYear = py.PlanYearID - 1
                       ,@BidYear = py.PlanYearID
            FROM        dbo.LkpIntPlanYear py WITH (NOLOCK)
           CROSS JOIN   dbo.LkpProjectionVersion pv WITH (NOLOCK)
            WHERE       py.IsBidYear = 1
                        AND pv.IsLiveProjection = 1;

            --Get the quarter which contains the last full quarter of actuals
            DECLARE @LastCurrentYearQuarter INT;
            SELECT  DISTINCT
                    @LastCurrentYearQuarter = LastCurrentYearQuarter
            FROM    dbo.LkpProjectionVersion WITH (NOLOCK)
            WHERE   IsLiveProjection = 1;

            --Get max component order
            DECLARE @MaxComponentOrder INT = (SELECT    MAX (ComponentOrder)
                                              FROM      dbo.Trend_Reporting_CalcImpactProjected_forRollup);
											  
            --Total and Membership components
            DECLARE @Total VARCHAR(50) = 'Total';
            DECLARE @Membership VARCHAR(50) = 'Membership';

            --Year
            IF (SELECT  OBJECT_ID ('tempdb..#Year')) IS NOT NULL DROP TABLE #Year;
            SELECT  DISTINCT
                    number AS TrendYearID
            INTO    #Year
            FROM    master..spt_values
            WHERE   number >= @MinHistoricYear
                    AND number <= @BidYear;

            --Quarter
            IF (SELECT  OBJECT_ID ('tempdb..#Quarter')) IS NOT NULL DROP TABLE #Quarter;
            SELECT  DISTINCT
                    number AS QuarterID
            INTO    #Quarter
            FROM    master..spt_values
            WHERE   number BETWEEN 0 AND 4;

            --RepCat
            IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
            SELECT  DISTINCT
                    BenefitCategoryID
                   ,ReportingCategory
            INTO    #RepCat
            FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
            WHERE   ReportingCategory IS NOT NULL;

            --Component excl. base
            IF (SELECT  OBJECT_ID ('tempdb..#Component')) IS NOT NULL
                DROP TABLE #Component;
            SELECT  DISTINCT
                    ComponentReporting
            INTO    #Component
            FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
            WHERE   ComponentReporting IS NOT NULL
            UNION ALL
            SELECT  @Membership
            UNION ALL
            SELECT  @Total;

            --Create PlanInfoID list
            IF (SELECT  OBJECT_ID ('tempdb..#PlanInfoIDList')) IS NOT NULL
                DROP TABLE #PlanInfoIDList;
            SELECT  PlanInfoID
                   ,CPS
                   ,PlanYear
            INTO    #PlanInfoIDList
            FROM    dbo.vwPlanInfo WITH (NOLOCK)
            WHERE   IsHidden = 0
                    AND IsOffMAModel = 'No'
                    AND Region NOT IN ('Unmapped')
                    AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@XPlanInfoID, ',', 1) )
                         OR @XPlanInfoID IS NULL);


            -----------------------------------------------------------
            -- 1. Create shell for historic totals (quarterly basis) --
            -----------------------------------------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#AllHistoricCombinationsTotals')) IS NOT NULL
                DROP TABLE #AllHistoricCombinationsTotals;
            SELECT      p.PlanInfoID
                       ,p.CPS
                       ,1 AS RateType
                       ,@Total AS ComponentReporting
                       ,p.PlanYear AS PlanYearID
                       ,q.QuarterID
                       ,p.PlanYear AS TrendYearID
                       ,rc.ReportingCategory
                       ,rc.BenefitCategoryID
            INTO        #AllHistoricCombinationsTotals
            FROM        #PlanInfoIDList p
           CROSS JOIN   #RepCat rc
           CROSS JOIN   #Quarter q
            WHERE       (p.PlanYear BETWEEN @MinHistoricYear AND @CurrentYear)
                        AND p.PlanYear * 10 + q.QuarterID <= @CurrentYear * 10 + @LastCurrentYearQuarter
                        AND p.PlanYear * 10 + q.QuarterID <> @CurrentYear * 10;
		 IF (SELECT  OBJECT_ID ('tempdb..#Quarter')) IS NOT NULL DROP TABLE #Quarter;
		IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
			

            ---------------------------
            -- 2. Pull Historic Data --
            ---------------------------

            --Historic Membership - quarterly
            IF (SELECT  OBJECT_ID ('tempdb..#HistoricMembership')) IS NOT NULL
                DROP TABLE #HistoricMembership;
            SELECT      PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,QuarterID
                       ,SUM (CASE WHEN IsRisk = 1 THEN ISNULL (MemberMonths, 0) ELSE 0 END) AS RiskMemberMonths
                       ,SUM (CASE WHEN IsRisk = 0 THEN ISNULL (MemberMonths, 0) ELSE 0 END) AS NonRiskMemberMonths
            INTO        #HistoricMembership
            FROM        dbo.Trend_CalcHistoricMembership WITH (NOLOCK)
            GROUP BY    PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,QuarterID;

            --Historic Membership - annual totals
            INSERT INTO #HistoricMembership
            SELECT      PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,0 AS QuarterID
                       ,SUM (CASE WHEN IsRisk = 1 THEN ISNULL (MemberMonths, 0) ELSE 0 END) AS RiskMemberMonths
                       ,SUM (CASE WHEN IsRisk = 0 THEN ISNULL (MemberMonths, 0) ELSE 0 END) AS NonRiskMemberMonths
            FROM        dbo.Trend_CalcHistoricMembership WITH (NOLOCK)
            GROUP BY    PlanInfoID
                       ,CPS
                       ,PlanYearID;

            -- Historic Cost and Use - quarterly
            IF (SELECT  OBJECT_ID ('tempdb..#HistoricTotals')) IS NOT NULL
                DROP TABLE #HistoricTotals;
            SELECT      h.CPS
                       ,1 AS RateType
                       ,@Total AS ComponentReporting
                       ,h.PlanYearID
                       ,h.QuarterID
                       ,h.ReportingCategory
                       ,h.BenefitCategoryID
                       ,SUM (ISNULL (h.Allowed, 0)) AS Allowed
                       ,SUM (ISNULL (h.Utilization, 0)) AS Utilization
            INTO        #HistoricTotals
            FROM        dbo.Trend_CalcHistoricCostAndUse h WITH (NOLOCK)
            GROUP BY    h.CPS
                       ,h.PlanYearID
                       ,h.QuarterID
                       ,h.ReportingCategory
                       ,h.BenefitCategoryID;

            -- Historic Cost and Use - annual totals
            INSERT INTO #HistoricTotals
            SELECT      h.CPS
                       ,1 AS RateType
                       ,@Total AS ComponentReporting
                       ,h.PlanYearID
                       ,0 AS QuarterID
                       ,h.ReportingCategory
                       ,h.BenefitCategoryID
                       ,SUM (ISNULL (h.Allowed, 0)) AS Allowed
                       ,SUM (ISNULL (h.Utilization, 0)) AS Utilization
            FROM        dbo.Trend_CalcHistoricCostAndUse h WITH (NOLOCK)
            GROUP BY    h.CPS
                       ,h.PlanYearID
                       ,h.ReportingCategory
                       ,h.BenefitCategoryID;


            --------------------------------------------------------------
            -- 3. Join results to combinations and insert final results --
            --------------------------------------------------------------

            -- If @XPlanInfoID input parameter is NULL, delete everything. 
            IF @XPlanInfoID IS NULL
                BEGIN
                    DELETE  FROM dbo.Trend_Reporting_CalcCompiledImpacts WHERE  1 = 1;
                END;
            ELSE -- If @XPlanInfoID input parameter is not NULL, only delete records for which the PlanInfoID exists in @XPlanInfoID
                BEGIN
                    DELETE  t
                    FROM    dbo.Trend_Reporting_CalcCompiledImpacts t WITH (NOLOCK)
                    INNER JOIN    (SELECT Val AS PlanInfoID
                             FROM   dbo.Trend_fnCalcStringToTable (@XPlanInfoID, ',', 1) ) p
                      ON p.PlanInfoID = t.PlanInfoID
                    WHERE   p.PlanInfoID IS NOT NULL;
                END;

            SET @LastUpdateDateTime = GETDATE ();

            IF (SELECT  OBJECT_ID ('tempdb..#OutputCalcCompiledImpacts')) IS NOT NULL
                DROP TABLE #OutputCalcCompiledImpacts;

            --Final Historic Impacts
            SELECT      CASE WHEN vpi.IsIncludeInTrend = 1 THEN 'Y' ELSE 'N' END AS IncludeInTrend
                       ,vpi.Division
                       ,vpi.Region
                       ,vpi.Product
                       ,vpi.SNPType
                       ,vpi.Market
                       ,vpi.PlanInfoID
                       ,vpi.CPS
                       ,cpih.RateType
                       ,cpih.ComponentReporting
                       ,vpi.PlanYear * 10 + 0 AS IncurredDate
                       ,vpi.PlanYear AS PlanYearID
                       ,0 AS QuarterID
                       ,vpi.PlanYear AS TrendYearID
                       ,bc.ReportingCategory
                       ,bc.BenefitCategoryName AS BenefitCategory
                       ,cpih.Allowed
                       ,cpih.Utilization
                       ,cpih.MemberMonths
                       ,NULL AS MemberMonths_Risk
                       ,NULL AS MemberMonths_NonRisk
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            INTO        #OutputCalcCompiledImpacts
            FROM        dbo.Trend_Reporting_CalcImpactHistoric cpih WITH (NOLOCK)
            LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = cpih.PlanInfoID
            LEFT JOIN   dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
                   ON bc.BenefitCategoryID = cpih.BenefitCategoryID
            LEFT JOIN   #PlanInfoIDList p
                   ON p.PlanInfoID = cpih.PlanInfoID
            WHERE       p.PlanInfoID IS NOT NULL;

            --Final Historic Totals
            INSERT INTO #OutputCalcCompiledImpacts
            SELECT      CASE WHEN p.IsIncludeInTrend = 1 THEN 'Y' ELSE 'N' END AS IncludeInTrend
                       ,p.Division
                       ,p.Region
                       ,p.Product
                       ,p.SNPType
                       ,p.Market
                       ,p.PlanInfoID
                       ,hc.CPS
                       ,hc.RateType
                       ,hc.ComponentReporting
                       ,hc.TrendYearID * 10 + hc.QuarterID
                       ,hc.PlanYearID
                       ,hc.QuarterID
                       ,hc.TrendYearID
                       ,hc.ReportingCategory
                       ,bc.BenefitCategoryName
                       ,ISNULL (ht.Allowed, 0) AS Allowed
                       ,ISNULL (ht.Utilization, 0) AS Utilization
                       ,ISNULL (m.RiskMemberMonths + m.NonRiskMemberMonths, 0) AS MemberMonths
                       ,ISNULL (m.RiskMemberMonths, 0) AS MemberMonths_Risk
                       ,ISNULL (m.NonRiskMemberMonths, 0) AS MemberMonths_NonRisk
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        #AllHistoricCombinationsTotals hc WITH (NOLOCK)
            LEFT JOIN   #HistoricTotals ht
                   ON ht.CPS = hc.CPS
                      AND   ht.RateType = hc.RateType
                      AND   ht.ComponentReporting = hc.ComponentReporting
                      AND   ht.PlanYearID = hc.PlanYearID
                      AND   ht.QuarterID = hc.QuarterID
                      AND   ht.ReportingCategory = hc.ReportingCategory
                      AND   ht.BenefitCategoryID = hc.BenefitCategoryID
            LEFT JOIN   #HistoricMembership m
                   ON m.CPS = hc.CPS
                      AND   m.PlanYearID = hc.PlanYearID
                      AND   m.QuarterID = hc.QuarterID
           INNER JOIN   dbo.vwPlanInfo p WITH (NOLOCK)
                   ON p.CPS = hc.CPS
                      AND   p.PlanYear = hc.PlanYearID
           INNER JOIN   dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
                   ON bc.BenefitCategoryID = hc.BenefitCategoryID;

          IF (SELECT  OBJECT_ID ('tempdb..#HistoricTotals')) IS NOT NULL
                DROP TABLE #HistoricTotals;

		IF (SELECT  OBJECT_ID ('tempdb..#HistoricMembership')) IS NOT NULL
                DROP TABLE #HistoricMembership;

            --Final Projected Impacts
            INSERT INTO #OutputCalcCompiledImpacts
            SELECT      CASE WHEN vpi.IsIncludeInTrend = 1 THEN 'Y' ELSE 'N' END AS IncludeInTrend
                       ,vpi.Division
                       ,vpi.Region
                       ,vpi.Product
                       ,vpi.SNPType
                       ,vpi.Market
                       ,cip.PlanInfoID
                       ,vpi.CPS
                       ,cip.RateType
                       ,cip.ComponentReporting
                       ,cip.TrendYearID * 10 + 0 AS IncurredDate
                       ,@BidYear AS PlanYearID
                       ,0 AS QuarterID
                       ,cip.TrendYearID
                       ,bc.ReportingCategory
                       ,bc.BenefitCategoryName
                       ,cip.Allowed
                       ,cip.Utilization
                       ,cip.MemberMonths
                       ,NULL AS MemberMonths_Risk
                       ,NULL AS MemberMonths_NonRisk
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        dbo.Trend_Reporting_CalcImpactProjected_forRollup cip WITH (NOLOCK)
            LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = cip.PlanInfoID
           INNER JOIN   dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
                   ON bc.BenefitCategoryID = cip.BenefitCategoryID
            LEFT JOIN   #PlanInfoIDList p
                   ON p.PlanInfoID = cip.PlanInfoID
            WHERE       cip.RateType = 1
                        AND p.PlanInfoID IS NOT NULL;

            -- Final Projected Totals
            INSERT INTO #OutputCalcCompiledImpacts
            SELECT      CASE WHEN vpi.IsIncludeInTrend = 1 THEN 'Y' ELSE 'N' END AS IncludeInTrend
                       ,vpi.Division
                       ,vpi.Region
                       ,vpi.Product
                       ,vpi.SNPType
                       ,vpi.Market
                       ,cip.PlanInfoID
                       ,vpi.CPS
                       ,cip.RateType
                       ,@Total AS ComponentReporting
                       ,cip.TrendYearID * 10 + 0 AS IncurredDate
                       ,@BidYear AS PlanYearID
                       ,0 AS QuarterID
                       ,cip.TrendYearID
                       ,bc.ReportingCategory
                       ,bc.BenefitCategoryName
                       ,cip.Allowed
                       ,cip.Utilization
                       ,cip.MemberMonths
                       ,NULL AS MemberMonths_Risk
                       ,NULL AS MemberMonths_NonRisk
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        dbo.Trend_Reporting_CalcImpactProjected_forRollup cip WITH (NOLOCK)
            LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = cip.PlanInfoID
           INNER JOIN   dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
                   ON bc.BenefitCategoryID = cip.BenefitCategoryID
            LEFT JOIN   #PlanInfoIDList p
                   ON p.PlanInfoID = cip.PlanInfoID
            WHERE       cip.RateType = 1
                        AND cip.ComponentOrder = @MaxComponentOrder
                        AND p.PlanInfoID IS NOT NULL;
           IF (SELECT  OBJECT_ID ('tempdb..#PlanInfoIDList')) IS NOT NULL DROP TABLE #PlanInfoIDList;
            -- Write output to table
            INSERT INTO dbo.Trend_Reporting_CalcCompiledImpacts WITH (TABLOCK)
                (IncludeInTrend
                ,Division
                ,Region
                ,Product
                ,SNPType
                ,Market
                ,PlanInfoID
                ,CPS
                ,RateType
                ,ComponentReporting
                ,IncurredDate
                ,PlanYearID
                ,QuarterID
                ,TrendYearID
                ,ReportingCategory
                ,BenefitCategory
                ,Allowed
                ,Utilization
                ,MemberMonths
                ,MemberMonths_Risk
                ,MemberMonths_NonRisk
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  IncludeInTrend
                   ,Division
                   ,Region
                   ,Product
                   ,SNPType
                   ,Market
                   ,PlanInfoID
                   ,CPS
                   ,RateType
                   ,ComponentReporting
                   ,IncurredDate
                   ,PlanYearID
                   ,QuarterID
                   ,TrendYearID
                   ,ReportingCategory
                   ,BenefitCategory
                   ,Allowed
                   ,Utilization
                   ,MemberMonths
                   ,MemberMonths_Risk
                   ,MemberMonths_NonRisk
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #OutputCalcCompiledImpacts;

            COMMIT TRANSACTION transactionMain;

        END TRY

        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@XLastUpdateByID
               ,GETDATE ();

    END;
GO
