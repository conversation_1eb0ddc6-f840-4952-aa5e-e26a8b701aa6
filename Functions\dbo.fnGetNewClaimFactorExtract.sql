SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGet<PERSON><PERSON><PERSON><PERSON>mFactorExtract]  ******/

/****** Object:  UserDefinedFunction [dbo].[fnGetNew<PERSON>laimFactorExtract]    Script Date: 01/30/2014 13:36:14 ******/

-------------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnGetNewClaimFactorExtract  
--  
-- AUTHOR: <PERSON>  
--  
-- CREATED DATE: 2012-Nov-30  
-- HEADER UPDATED: 2012-Nov-30  
--  
-- DESCRIPTION: Designed to extract fields for Claim Factors - will match the upload  
--  
-- PARAMETERS:  
--  Input:  
--      @WhereIN  
--        
--  Output:  
--  
-- TABLES:  
--  Read:   
--  SavedClaimFactorHeader  
--  SavedClaimFactorDetail  
--  SavedMATrendData  
--  Write:  
--  
-- VIEWS:  
--        
--  
-- FUNCTIONS:  
--  
-- STORED PROCS:  
--  
-- HISTORY:  
-- ---------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER  
-- ---------------------------------------------------------------------------------------------------------------------  
-- 2012-Nov-30		1			Initial Version														Mike Deren  
-- 2013-Jan-04		2			Added is live and is hidden											Mike Deren  
-- 2013-Oct-04		3			Modified to Include SegmentId										Anubhav Mishra
-- 2014-Jan-09		4			Changing Table Name													Mike Deren
-- ----------------------------------------------------------------------------------------------------------------------  
CREATE FUNCTION [dbo].[fnGetNewClaimFactorExtract]  
    (  
    @WhereIN Varchar(MAX) = NULL  
    )  
RETURNS @Results TABLE  
 (      
 ContractNumber VARCHAR(5),
 PlanID VARCHAR(3),
 SegmentId VARCHAR(3), 
    Component INT,   
 BenefitCategoryNumber INT,   
 B2CNormalizedUse DECIMAL(8,6),   
 B2CNormalizedCost DECIMAL(8,6),  
 B2CMorbidityUse DECIMAL(8,6),  
 B2CMorbidityCost DECIMAL(8,6),  
 B2CDemographicUse DECIMAL(8,6),  
 B2CDemographicCost DECIMAL(8,6),  
 B2CBustersUse DECIMAL(8,6),  
 B2CBustersCost DECIMAL(8,6),  
 B2CProductUse DECIMAL(8,6),   
 B2CProductCost DECIMAL(8,6),  
 B2CGeographicUse DECIMAL(8,6),  
 B2CGeographicCost DECIMAL(8,6),  
 B2CCMSReimbursementUse DECIMAL(8,6),  
 B2CCMSReimbursementCost DECIMAL(8,6),  
 B2CContractualUse DECIMAL(8,6),  
 B2CContractualCost DECIMAL(8,6),  
 B2CBendersUse DECIMAL(8,6),    
 B2CBendersCost DECIMAL(8,6),  
 B2CWorkdayUse DECIMAL(8,6),  
 B2CWorkdayCost DECIMAL(8,6),  
 B2CFluUse DECIMAL(8,6),  
 B2CFluCost DECIMAL(8,6),   
 B2CInducedUtilizationUse DECIMAL(8,6),  
 B2CInducedUtilizationCost DECIMAL(8,6),  
 B2CActAdjUse DECIMAL(8,6),   
 B2CActAdjCost DECIMAL(8,6),  
 B2CNonActAdjUse DECIMAL(8,6),  
 B2CNonActAdjCost DECIMAL(8,6),
 B2CPoolingUse DECIMAL(8,6),
 B2CPoolingCost DECIMAL(8,6),
 B2CHTPUse DECIMAL(8,6),
 B2CHTPCost DECIMAL(8,6),
 B2CCompoundingAdjUse DECIMAL(8,6),
 B2CCompoundingAdjCost DECIMAL(8,6),   
 C2PNormalizedUse DECIMAL(8,6),   
 C2PNormalizedCost DECIMAL(8,6),  
 C2PMorbidityUse DECIMAL(8,6),  
 C2PMorbidityCost DECIMAL(8,6),  
 C2PDemographicUse DECIMAL(8,6),  
 C2PDemographicCost DECIMAL(8,6),  
 C2PBustersUse DECIMAL(8,6),  
 C2PBustersCost DECIMAL(8,6),  
 C2PProductUse DECIMAL(8,6),   
 C2PProductCost DECIMAL(8,6),  
 C2PGeographicUse DECIMAL(8,6),  
 C2PGeographicCost DECIMAL(8,6),  
 C2PCMSReimbursementUse DECIMAL(8,6),  
 C2PCMSReimbursementCost DECIMAL(8,6),  
 C2PContractualUse DECIMAL(8,6),  
 C2PContractualCost DECIMAL(8,6),  
 C2PBendersUse DECIMAL(8,6),    
 C2PBendersCost DECIMAL(8,6),  
 C2PWorkdayUse DECIMAL(8,6),  
 C2PWorkdayCost DECIMAL(8,6),  
 C2PFluUse DECIMAL(8,6),  
 C2PFluCost DECIMAL(8,6),   
 C2PInducedUtilizationUse DECIMAL(8,6),  
 C2PInducedUtilizationCost DECIMAL(8,6),  
 C2PActAdjUse DECIMAL(8,6),   
 C2PActAdjCost DECIMAL(8,6),  
 C2PNonActAdjUse DECIMAL(8,6),  
 C2PNonActAdjCost DECIMAL(8,6),
 C2PPoolingUse DECIMAL(8,6),
 C2PPoolingCost DECIMAL(8,6),
 C2PHTPUse DECIMAL(8,6),
 C2PHTPCost DECIMAL(8,6),
 C2PCompoundingAdjUse DECIMAL(8,6),
 C2PCompoundingAdjCost DECIMAL(8,6)  
    ) AS  
  
BEGIN  
   
 IF @WhereIn IS NULL  
  INSERT @Results  
   SELECT   
    std.ContractNumber,
    std.PlanID,
    std.SegmentId,
    Component,   
    BenefitCategoryNumber,   
    B2CNormalizedUse,   
    B2CNormalizedCost,  
    B2CMorbidityUse,  
    B2CMorbidityCost,  
    B2CDemographicUse,  
    B2CDemographicCost,  
    B2CBustersUse,  
    B2CBustersCost,  
    B2CProductUse,   
    B2CProductCost,  
    B2CGeographicUse,  
    B2CGeographicCost,  
    B2CCMSReimbursementUse,  
    B2CCMSReimbursementCost,  
    B2CContractualUse,  
    B2CContractualCost,  
    B2CBendersUse,    
    B2CBendersCost,  
    B2CWorkdayUse,  
    B2CWorkdayCost,  
    B2CFluUse,  
    B2CFluCost,   
    B2CInducedUtilizationUse,  
    B2CInducedUtilizationCost,  
    B2CActAdjUse,   
    B2CActAdjCost,  
    B2CNonActAdjUse,  
    B2CNonActAdjCost, 
	B2CPoolingUse,
	B2CPoolingCost,
	B2CHTPUse,
	B2CHTPCost,
	B2CCompoundingAdjUse,
	B2CCompoundingAdjCost,  
    C2PNormalizedUse,   
    C2PNormalizedCost,  
    C2PMorbidityUse,  
    C2PMorbidityCost,  
    C2PDemographicUse,  
    C2PDemographicCost,  
    C2PBustersUse,  
    C2PBustersCost,  
    C2PProductUse,   
    C2PProductCost,  
    C2PGeographicUse,  
    C2PGeographicCost,  
    C2PCMSReimbursementUse,  
    C2PCMSReimbursementCost,  
    C2PContractualUse,  
    C2PContractualCost,  
    C2PBendersUse,    
    C2PBendersCost,  
    C2PWorkdayUse,  
    C2PWorkdayCost,  
    C2PFluUse,  
    C2PFluCost,   
    C2PInducedUtilizationUse,  
    C2PInducedUtilizationCost,  
    C2PActAdjUse,   
    C2PActAdjCost,  
    C2PNonActAdjUse,  
    C2PNonActAdjCost,
	C2PPoolingUse,
	C2PPoolingCost,
	C2PHTPUse,
	C2PHTPCost,
	C2PCompoundingAdjUse,
	C2PCompoundingAdjCost  
      
   FROM SavedMATrendData std  
   INNER JOIN SavedPlanHeader sph   
    ON std.ContractNumber = sph.ContractNumber 
    AND std.PlanID = sph.PlanID 
    AND std.SegmentId = sph.SegmentId   --Included Join on SegmentId
    AND sph.isliveindex = 1  
    AND sph.Ishidden = 0  
   INNER JOIN SavedPlanDetail spd  
    ON spd.ForecastID = sph.ForecastID  
    AND std.Component = spd.MARatingOptionID  
      
   ORDER BY ContractNumber,PlanID, SegmentId,Component, BenefitCategoryNumber  
  
 ELSE  
  INSERT @Results  
   SELECT     
    std.ContractNumber,
    std.PlanID,
    std.SegmentId,  
    Component,   
    BenefitCategoryNumber,   
    B2CNormalizedUse,   
    B2CNormalizedCost,  
    B2CMorbidityUse,  
    B2CMorbidityCost,  
    B2CDemographicUse,  
    B2CDemographicCost,  
    B2CBustersUse,  
    B2CBustersCost,  
    B2CProductUse,   
    B2CProductCost,  
    B2CGeographicUse,  
    B2CGeographicCost,  
    B2CCMSReimbursementUse,  
    B2CCMSReimbursementCost,  
    B2CContractualUse,  
    B2CContractualCost,  
    B2CBendersUse,    
    B2CBendersCost,  
    B2CWorkdayUse,  
    B2CWorkdayCost,  
    B2CFluUse,  
    B2CFluCost,   
    B2CInducedUtilizationUse,  
    B2CInducedUtilizationCost,  
    B2CActAdjUse,   
    B2CActAdjCost,  
    B2CNonActAdjUse,  
    B2CNonActAdjCost,
	B2CPoolingUse,
	B2CPoolingCost,
	B2CHTPUse,
	B2CHTPCost,
	B2CCompoundingAdjUse,
	B2CCompoundingAdjCost,   
    C2PNormalizedUse,   
    C2PNormalizedCost,  
    C2PMorbidityUse,  
    C2PMorbidityCost,  
    C2PDemographicUse,  
    C2PDemographicCost,  
    C2PBustersUse,  
    C2PBustersCost,  
    C2PProductUse,   
    C2PProductCost,  
    C2PGeographicUse,  
    C2PGeographicCost,  
    C2PCMSReimbursementUse,  
    C2PCMSReimbursementCost,  
    C2PContractualUse,  
    C2PContractualCost,  
    C2PBendersUse,    
    C2PBendersCost,  
    C2PWorkdayUse,  
    C2PWorkdayCost,  
    C2PFluUse,  
    C2PFluCost,   
    C2PInducedUtilizationUse,  
    C2PInducedUtilizationCost,  
    C2PActAdjUse,   
    C2PActAdjCost,  
    C2PNonActAdjUse,  
    C2PNonActAdjCost,
	C2PPoolingUse,
	C2PPoolingCost,
	C2PHTPUse,
	C2PHTPCost,
	C2PCompoundingAdjUse,
	C2PCompoundingAdjCost  
      
   FROM SavedMATrendData std  
   INNER JOIN SavedPlanHeader sph   
    ON std.ContractNumber = sph.ContractNumber 
    AND std.PlanID = sph.PlanID 
    AND std.SegmentId = sph.SegmentId   --Included Join on SegmentId
   INNER JOIN SavedPlanDetail spd  
    ON spd.ForecastID = sph.ForecastID  
    AND std.Component = spd.MARatingOptionID  
    AND sph.isliveindex = 1  
    AND sph.Ishidden = 0    
   WHERE sph.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))  
    AND spd.ClaimForecastID IN   
     ((Select ClaimForecastID from SavedPlanDetail where ForecastID = sph.ForecastID AND MARatingOptionID = 1), --Experience  
     (Select ClaimForecastID from SavedPlanDetail where ForecastID = sph.ForecastID AND MARatingOptionID = 2)) --Manual  
   ORDER BY ContractNumber,PlanID, SegmentId, Component, BenefitCategoryNumber  
     
RETURN  
END
GO
