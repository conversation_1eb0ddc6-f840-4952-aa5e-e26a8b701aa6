SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2024-Oct-08  2		  Added UserId logic when we do reprice						<PERSON>ya <PERSON>y
-- ---------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[spWritePlanRepriceLog]
(
    @ForecastID INT,
    @ProcNumber SMALLINT,
    @Message VARCHAR(600),
	@UserId VARCHAR(7)
)
AS
BEGIN
    INSERT INTO dbo.PlanRepriceLog
    VALUES (
            @ForecastID,
            @ProcNumber,
            @Message,
            GETDATE(),
            NEWID(),
			@UserId
            )
END
GO

