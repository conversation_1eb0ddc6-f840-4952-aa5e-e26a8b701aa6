SET QUOTED_IDENTIFIER ON
GO
SET AN<PERSON>_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetBenefitName] ******/

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: fnGet<PERSON><PERSON><PERSON>Name
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE:OCT-11-2007
--
-- DESCRIPTION: This function returns Benefit Categiry Name
-- PARAMETERS:
--	Inputs:
--		@BenefitCategoryID                    
--
-- TABLES:
--	Read:
--		LkpIntBenefitCategory
--	Write: NONE
--
-- VIEWS: Read: NONE
--
-- RETURNS: BenefitName
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		CHANGES MADE																		DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------------------
-- 2007-Oct-11		1		Initial Version																		Christian Cofie
-- 2010-Jul-27		2		Moved to 2012																		Joe Casey
-- ----------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBenefitName]
(
	@BenefitCategoryID smallint
)
RETURNS Varchar(50) AS  
BEGIN 

DECLARE @BenefitName varchar(50)

Set  @BenefitName = ISNULL((Select BenefitCategoryName From LkpIntBenefitCategory Where BenefitCategoryID=@BenefitCategoryID),'NA')

RETURN  @BenefitName

END
GO
