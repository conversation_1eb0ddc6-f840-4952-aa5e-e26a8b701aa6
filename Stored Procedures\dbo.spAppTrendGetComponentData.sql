SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================              
-- Author: Florence Sesham             
-- Create date: 2020-03-25         
-- Description: Get Component Data.     
--              
--              
-- PARAMETERS:              
-- Input:                
            
-- TABLES:              
-- Read:              
-- Write:              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:               
             
        
-- $HISTORY                 
        
-- ----------------------------------------------------------------------------------------------------------------------                
-- DATE   VERSION   CHANGES MADE                                              DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------                
-- 2020-03-25  1    Initial version.                                 Florence Sesham   
-- 2020-05-11  2    Added selection for delete revamp                Chhavi Sinha
-- ----------------------------------------------------------------------------------------------------------------------                
 -- =============================================     
    
CREATE PROCEDURE [dbo].[spAppTrendGetComponentData]       
@LastUpdateByID CHAR(7)      
AS      

BEGIN  
 BEGIN TRANSACTION;
        BEGIN TRY   
SELECT  DISTINCT
        UPPER(component) AS component,
       ComponentVersionID , ImportFileName
FROM
        Trend_SavedComponentImportAnnual
WHERE
        ComponentVersionID NOT IN
        (
                SELECT DISTINCT
                        ComponentVersionID
                FROM
                        Trend_SavedPackageOption
                WHERE
                        componentversionid IS NOT NULL)

UNION

SELECT  DISTINCT
         UPPER(component) AS component,
       ComponentVersionID , 
	   ImportFileName
FROM
        dbo.Trend_SavedComponentImportQuarterly
WHERE
        ComponentVersionID NOT IN
        (
                SELECT DISTINCT
                        ComponentVersionID
                FROM
                        Trend_SavedPackageOption
                WHERE
                        componentversionid IS NOT NULL)
ORDER BY
        UPPER(component),
        ImportFileName
COMMIT TRANSACTION;     
       
    END TRY     
      
    BEGIN CATCH
                       
            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @ErrorException NVARCHAR(4000);
            DECLARE @errSrc      VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL')
                   ,@currentdate DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :'
                                      + CAST(@ErrorState AS VARCHAR);

            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);



            ---Insert into app log for logging error------------------             
            EXEC spAppAddLogEntry @currentdate
                                 ,''
                                 ,'ERROR'
                                 ,@errSrc
                                 ,@ErrorMessage
                                 ,@ErrorException
                                 ,@LastUpdateByID;
        END CATCH;
END   
 
GO
