SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- Stored Procedure

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcIsPlanLevel_Trends
--
-- CREATOR: Andy Blink 
--
-- CREATED DATE: MAR-26-2020
--
-- DESCRIPTION:   This stored procedure takes the relativities and projects base to bid trends for experience and manual. It will run for a component based on the ComponentID parameter passed
--              
--              
-- PARAMETERS:
--  Input  :	@ComponentID
--              @PlanInfoID
--              @ActuarialRegion
--              @ProductType
--              @LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  LkpProjectionVersion
--					LkpIntPlanYear
--                  SavedForecastSetup
--                  SavedRollupForecastMap
--                  SavedRollupInfo
--                  Trend_SavedManualCutPlanAssignment
--                  Trend_SavedManualCutPlanComposition
--                  Trend_SavedRelativityOutlierClaims
--                  Trend_SavedRelativityPopulation
--                  Trend_SavedRelativityInducedUtilization
--                  Trend_SavedRelativityContractual
--                  Trend_SavedRelativityCMSReimb
--                  Trend_CalcHistoricMembership
--                  Trend_CalcHistoricCostAndUse
--                  LkpIntBenefitCategory
--					Trend_SavedPopulationBarcBidYearMembership
--					Trend_PerPopulationCredibleMemberMonths
--                  
--					
--          Write:  Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims
--                  Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization
--                  Trend_ProjProcess_CalcIsPlanLevel_Contractual
--                  Trend_ProjProcess_CalcIsPlanLevel_CMSReimb
--                  Trend_ProjProcess_CalcIsPlanLevel_Population
--                  
--
-- VIEWS: Read: vwSAMCrosswalks
--              vwPlanInfo
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																											DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MAR-26-2020      1           Initial Version																											Andy Blink
-- Jun-08-2020      2           Replace vwCrosswalksPlanLevel with vwSamCrosswalks																		Andy Blink
-- Jun-16-2020      3           Replace delete where statments with PlanInfoID instead of CPS															Andy Blink
-- Jul-14-2020      4		    Optimized BARC to MAAUI logic 																							Ramandeep Saini
-- Sep-14-2020		5			Replace email code to use MAAModels access table instead of TPF  														Craig Nielsen
-- Sep-30-2020		6			Revert email to TPF database and update weight logic for bids to														Craig Nielsen
--								IF @LastCurrentYearQuarter < 2. Will revert email logic for 20.12	
-- Oct-1-2020		7			Reverted email logic to use W_FEM_Access control, not TPF for 20.12	Craig Nielen
-- Dec-5-2020		8			Adding local temp table to improve db performance					Surya Murthy 
-- Mar-15-2021		9			Set IU Trend Adjustments to 0 if the bid model is calculating IU 	Craig Nielsen
-- Mar-31-2021		10			Fix IU Trend Adjustments for new in Current Year plans				Craig Nielsen
-- Jul-27-2021		11			Updated email logic to exclude fully credible plans					Franklin Fu
-- Aug-30-2021		12			Update IU Bid Year Trends after Q3									Craig Nielsen 
-- Apr-19-2023		13			Runtime improvement: added temp table #BasePlanList to limit the 
--								pull from dbo.Trend_CalcHistoricCostAndUse & 
--								dbo.Trend_CalcHistoricMembership, additionally limiting the data
--								to base & current year pull											Aleksandar Dimitrijevic
-- Aug-03-2023		14			Added Internal Parameter											Sheetal Patil
-- Sep-25-2023	    15		    Rowlocks added for deadlock issues									Surya Murthy
-- Oct-20-2024		16	        Updated error contact messaging.									Hannah Harmon
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcIsPlanLevel_Trends]

--DECLARE 
@ComponentID      INT = NULL    --see dbo.Trend_ComponentInfo for ComponentID mapping (Pop = 3, IU = 4, Contract = 8, CMS Reimb = 9, Outlier = 10)
,@PlanInfoID      VARCHAR(MAX) = NULL
,@ActuarialRegion VARCHAR(MAX) = NULL
,@ProductType     VARCHAR(MAX) = NULL
,@LastUpdateByID  CHAR(7) = 'Initial'

AS

SET NOCOUNT ON;

DECLARE @LastCurrentYearQuarter TINYINT;
DECLARE @ProjectionName VARCHAR(50);
DECLARE @ProjectionID INT;
DECLARE @BidYear INT;
DECLARE @LastUpdateDateTime DATETIME;
DECLARE @XComponentID      INT = @ComponentID      
DECLARE @XPlanInfoID      VARCHAR(MAX) = @PlanInfoID 
DECLARE @XActuarialRegion      VARCHAR(MAX) = @ActuarialRegion
DECLARE @XProductType      VARCHAR(MAX) = @ProductType


--Pull in the last quarter of actuals in the current year to define the projection type (Bids, Q3, Q4)
SET @LastCurrentYearQuarter = (SELECT   DISTINCT
                                        LastCurrentYearQuarter
                               FROM     dbo.LkpProjectionVersion WITH (NOLOCK)
                               WHERE    IsLiveProjection = 1);

--Pull in the ProjectionName for the live projection
SET @ProjectionName = (SELECT   DISTINCT
                                ProjectionName
                       FROM     dbo.LkpProjectionVersion WITH (NOLOCK)
                       WHERE    IsLiveProjection = 1);

--Pull in the ProjectionID for the live projection
SET @ProjectionID = (SELECT DISTINCT
                            ProjectionID
                     FROM   dbo.LkpProjectionVersion WITH (NOLOCK)
                     WHERE  IsLiveProjection = 1);

--Define the bid year
SET @BidYear = (SELECT  DISTINCT
                        PlanYearID
                FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                WHERE   IsBidYear = 1);

--Timestamp
SET @LastUpdateDateTime = GETDATE ();


--Setup the bid year plan list based on the parameters passed to the procedure
IF (SELECT  OBJECT_ID ('tempdb..#CPS')) IS NOT NULL DROP TABLE #CPS;
SELECT      DISTINCT
            PlanInfoID
           ,CPS
INTO        #CPS
FROM        dbo.vwPlanInfo WITH (NOLOCK)
WHERE       PlanYear = @BidYear
            AND IsHidden = 0
            AND IsOffMAModel = 'No'
            AND Region NOT IN ('Unmapped')
            AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@XPlanInfoID, ',', 1) )
                 OR @XPlanInfoID IS NULL)
            AND (Region IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@XActuarialRegion, ',', 1) )
                 OR @XActuarialRegion IS NULL)
            AND (Product IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@XProductType, ',', 1) )
                 OR @XProductType IS NULL)
ORDER BY    PlanInfoID;


--Create a temp three year crosswalk table from vwSAMCrosswalks
IF (SELECT  OBJECT_ID ('tempdb..#vwSAMCrosswalks')) IS NOT NULL
    DROP TABLE #vwSAMCrosswalks;

--Sub query removed to temp table
IF (SELECT  OBJECT_ID ('tempdb..#tempSavedForecastSetup')) IS NOT NULL
    DROP TABLE #tempSavedForecastSetup;

SELECT  *
INTO    #tempSavedForecastSetup
FROM    dbo.SavedForecastSetup WITH (NOLOCK)
WHERE   ForecastID IN (SELECT   DISTINCT
                                ForecastID
                       FROM     dbo.SavedRollupForecastMap WITH (NOLOCK)
                       WHERE    RollupID IN (SELECT DISTINCT
                                                    RollupID
                                             FROM   dbo.SavedRollupInfo WITH (NOLOCK)
                                             WHERE  RollupName = 'Live'));
--End

SELECT      DISTINCT
            sam.BaseYearCPS AS 'BaseCPS'
           ,sam.CurrentYearCPS AS 'CurrentCPS'
           ,sam.BidYearCPS AS 'BidCPS'
INTO        #vwSamCrosswalks
FROM        dbo.vwSAMCrosswalks sam WITH (NOLOCK)

--Only bring in service area scenarios that are live
INNER JOIN  #tempSavedForecastSetup sfs
        ON sfs.ServiceAreaOptionID = sam.ServiceAreaOptionID
           AND  sfs.PlanInfoID = sam.BidYearPlanInfoID
WHERE       sam.BidYearCPS IN (SELECT   DISTINCT CPS FROM   #CPS);


--Pull in plan level crosswalks from base year to current year
IF (SELECT  OBJECT_ID ('tempdb..#Base2CurrentCrosswalk')) IS NOT NULL
    DROP TABLE #Base2CurrentCrosswalk;
SELECT  DISTINCT
        BaseCPS
       ,CurrentCPS
INTO    #Base2CurrentCrosswalk
FROM    #vwSamCrosswalks;
--WHERE   BidYear IN (SELECT  DISTINCT CPS FROM   #CPS);


--Pull in plan level crosswalks from base year to bid year
IF (SELECT  OBJECT_ID ('tempdb..#Base2BidCrosswalk')) IS NOT NULL
    DROP TABLE #Base2BidCrosswalk;
SELECT  DISTINCT
        BaseCPS
       ,BidCPS
INTO    #Base2BidCrosswalk
FROM    #vwSamCrosswalks;
--WHERE   BidYear IN (SELECT  DISTINCT CPS FROM   #CPS);


--Pull in plan level crosswalks from current year to bid year
IF (SELECT  OBJECT_ID ('tempdb..#Current2BidCrosswalk')) IS NOT NULL
    DROP TABLE #Current2BidCrosswalk;
SELECT  DISTINCT
        CurrentCPS
       ,BidCPS
INTO    #Current2BidCrosswalk
FROM    #vwSamCrosswalks;
--WHERE   BidYear IN (SELECT  DISTINCT CPS FROM   #CPS);


--Determine the plan type of each bid year plan passed to the procedure (concurrent, new in current year, new in bid year)

--Count number of plans in the base year and current year service areas
IF (SELECT  OBJECT_ID ('tempdb..#PriorYearPlanCounts')) IS NOT NULL
    DROP TABLE #PriorYearPlanCounts;
SELECT      BidCPS
           ,SUM (CASE WHEN BaseCPS IS NOT NULL THEN 1 ELSE 0 END) 'BasePlanCount'
           ,SUM (CASE WHEN CurrentCPS IS NOT NULL THEN 1 ELSE 0 END) 'CurrentPlanCount'
INTO        #PriorYearPlanCounts
FROM        #vwSamCrosswalks
--WHERE       BidCPS IS NOT NULL
--            AND BidCPS IN (SELECT  DISTINCT CPS FROM   #CPS)
GROUP BY    BidCPS;


--Determine plan type based on plan count in each base and current years
IF (SELECT  OBJECT_ID ('tempdb..#PlanTypes')) IS NOT NULL
    DROP TABLE #PlanTypes;
SELECT  BidCPS
       ,CASE WHEN BasePlanCount > 0
                  AND   CurrentPlanCount > 0 THEN 'Concurrent'
             WHEN BasePlanCount = 0
                  AND   CurrentPlanCount > 0 THEN 'NewCurrent'
             ELSE 'NewBid' END AS 'PlanType'
INTO    #PlanTypes
FROM    #PriorYearPlanCounts;


--Pull in the manual cut assignments
IF (SELECT  OBJECT_ID ('tempdb..#ManualAssignments')) IS NOT NULL
    DROP TABLE #ManualAssignments;
SELECT      DISTINCT
            a.ProjectionID
           ,a.PlanInfoID
           ,b.CPS
           ,b.PlanYear AS 'PlanYearID'
           ,a.AssignedManualCutID
INTO        #ManualAssignments
FROM        dbo.Trend_SavedManualCutPlanAssignment a WITH (NOLOCK)
INNER JOIN  dbo.SavedPlanInfo b WITH (NOLOCK)
        ON b.PlanInfoID = a.PlanInfoID
WHERE       a.ProjectionID = @ProjectionID
            AND a.PlanInfoID IN (SELECT DISTINCT PlanInfoID FROM #CPS);


--Pull in the manual cuts assigned to plans passed to the procedure, and the plans that make up each of those manual cuts
IF (SELECT  OBJECT_ID ('tempdb..#PlansInManual')) IS NOT NULL
    DROP TABLE #PlansInManual;
SELECT      DISTINCT
            a.ManualCutID
           ,a.PlanInfoID
           ,b.CPS
           ,b.PlanYear AS 'PlanYearID'
INTO        #PlansInManual
FROM        dbo.Trend_SavedManualCutPlanComposition a WITH (NOLOCK)
INNER JOIN  dbo.SavedPlanInfo b WITH (NOLOCK)
        ON b.PlanInfoID = a.PlanInfoID
WHERE       a.ManualCutID IN (SELECT    DISTINCT   AssignedManualCutID FROM #ManualAssignments);

--Creating a list of BaseCPSs to pull base data for (Contains experience + manual plans needed for the cut)
IF (SELECT  OBJECT_ID ('tempdb..#BasePlanList')) IS NOT NULL
    DROP TABLE #BasePlanList;
SELECT BaseCPS AS CPS
  INTO #BasePlanList
  FROM #Base2BidCrosswalk
UNION ALL
SELECT CPS 
  FROM #PlansInManual;


--Pull in base cost & use and membership into one table to calculate weights
IF (SELECT  OBJECT_ID ('tempdb..#CalcBaseHistoric')) IS NOT NULL
    DROP TABLE #CalcBaseHistoric;

IF (SELECT  OBJECT_ID ('tempdb..#TempCalcBaseHistoric')) IS NOT NULL
    DROP TABLE #TempCalcBaseHistoric;

SELECT PlanInfoID,
       cu.CPS,
	   PlanYearID,
	   IncurredMonth,
	   QuarterID,
	   ReportingCategory,
	   Allowed,
	   Utilization
  INTO #TempCalcBaseHistoric
  FROM dbo.Trend_CalcHistoricCostAndUse cu WITH (NOLOCK)
  INNER JOIN (SELECT DISTINCT CPS FROM #BasePlanList) pl
    ON pl.CPS = cu.CPS
 WHERE PlanYearID >= @BidYear - 2;

IF (SELECT  OBJECT_ID ('tempdb..#TempCalcBaseHistoricMembership')) IS NOT NULL
    DROP TABLE #TempCalcBaseHistoricMembership;

SELECT PlanInfoID,
	   hm.CPS,
	   PlanYearID,
	   QuarterID,
	   MemberMonths
  INTO #TempCalcBaseHistoricMembership
  FROM dbo.Trend_CalcHistoricMembership hm WITH (NOLOCK)
  INNER JOIN (SELECT DISTINCT CPS FROM #BasePlanList) pl
    ON pl.CPS = hm.CPS
 WHERE PlanYearID >= @BidYear - 2;

--Cross join temp table for LkpIntBenefitCategory
IF (SELECT  OBJECT_ID ('tempdb..#TempCalcBaseHistoricRepCat')) IS NOT NULL
    DROP TABLE #TempCalcBaseHistoricRepCat;
SELECT  DISTINCT
        ReportingCategory
INTO    #TempCalcBaseHistoricRepCat
FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
WHERE   ReportingCategory IS NOT NULL;
--End

SELECT      PlanInfoID
           ,CPS
           ,PlanYearID
           ,QuarterID
           ,ReportingCategory
           ,'Allowed' AS 'WeightType'
           ,SUM (Allowed) AS 'Weight'
INTO    #CalcBaseHistoric
FROM        #TempCalcBaseHistoric WITH (NOLOCK)
GROUP BY    PlanInfoID
           ,CPS
           ,PlanYearID
           ,QuarterID
           ,ReportingCategory

UNION ALL

SELECT      PlanInfoID
           ,CPS
           ,PlanYearID
           ,QuarterID
           ,ReportingCategory
           ,'Utilization' AS 'WeightType'
           ,SUM (Utilization) AS 'Weight'
FROM        #TempCalcBaseHistoric WITH (NOLOCK)
GROUP BY    PlanInfoID
           ,CPS
           ,PlanYearID
           ,QuarterID
           ,ReportingCategory

UNION ALL

SELECT      a.PlanInfoID
           ,a.CPS
           ,a.PlanYearID
           ,a.QuarterID
           ,b.ReportingCategory
           ,'Membership' AS 'WeightType'
           ,SUM (a.MemberMonths) AS 'Weight'
FROM        #TempCalcBaseHistoricMembership a WITH (NOLOCK)
CROSS JOIN  #TempCalcBaseHistoricRepCat b
GROUP BY    a.PlanInfoID
           ,a.CPS
           ,a.PlanYearID
           ,a.QuarterID
           ,b.ReportingCategory;


--Pull in relativities from each component table
IF (SELECT  OBJECT_ID ('tempdb..#Relativities')) IS NOT NULL
    DROP TABLE #Relativities;

SELECT  CPS
       ,PlanYearID
       ,QuarterID
       ,ReportingCategory
       ,10 AS 'ComponentID'
       ,CostRelativity
       ,UseRelativity
INTO    #Relativities
FROM    dbo.Trend_SavedRelativityOutlierClaims WITH (NOLOCK)
WHERE   PlanYearID >= @BidYear - 2

UNION ALL

SELECT  CPS
       ,PlanYearID
       ,QuarterID
       ,ReportingCategory
       ,3 AS 'ComponentID'
       ,CostRelativity
       ,UseRelativity
FROM    dbo.Trend_SavedRelativityPopulation WITH (NOLOCK)
WHERE   PlanYearID >= @BidYear - 2

UNION ALL

SELECT  CPS
       ,PlanYearID
       ,QuarterID
       ,ReportingCategory
       ,4 AS 'ComponentID'
       ,CostRelativity
       ,UseRelativity
FROM    dbo.Trend_SavedRelativityInducedUtilization WITH (NOLOCK)
WHERE   PlanYearID >= @BidYear - 2

UNION ALL

SELECT  CPS
       ,PlanYearID
       ,QuarterID
       ,ReportingCategory
       ,8 AS 'ComponentID'
       ,CostRelativity
       ,UseRelativity
FROM    dbo.Trend_SavedRelativityContractual WITH (NOLOCK)
WHERE   PlanYearID >= @BidYear - 2

UNION ALL

SELECT  CPS
       ,PlanYearID
       ,QuarterID
       ,ReportingCategory
       ,9 AS 'ComponentID'
       ,CostRelativity
       ,UseRelativity
FROM    dbo.Trend_SavedRelativityCMSReimb WITH (NOLOCK)
WHERE   PlanYearID >= @BidYear - 2;


--Create table of Reporting Categories
IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
SELECT  DISTINCT
        ReportingCategory
INTO    #RepCat
FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
WHERE   ReportingCategory IS NOT NULL;


--Create table of quarters
IF (SELECT  OBJECT_ID ('tempdb..#Quarter')) IS NOT NULL DROP TABLE #Quarter;
SELECT  DISTINCT
        QuarterID
INTO    #Quarter
FROM    #Relativities
WHERE   QuarterID IS NOT NULL;


--Pull in the weighting amounts that will be used to roll up the relativity factors
IF (SELECT  OBJECT_ID ('tempdb..#WeightsNoBid')) IS NOT NULL
    DROP TABLE #WeightsNoBid;
CREATE TABLE #WeightsNoBid
    (CPS               CHAR(13)
    ,PlanYearID        INT
    ,QuarterID         TINYINT
    ,ReportingCategory VARCHAR(50)
    ,Weights           DECIMAL(18, 8));

IF (SELECT  OBJECT_ID ('tempdb..#Weights')) IS NOT NULL DROP TABLE #Weights;
CREATE TABLE #Weights
    (CPS               CHAR(13)
    ,PlanYearID        INT
    ,QuarterID         TINYINT
    ,ReportingCategory VARCHAR(50)
    ,Weights           DECIMAL(18, 8));


--Bid Projection
IF @LastCurrentYearQuarter < 2

    BEGIN

        INSERT INTO #Weights
        --Base year
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        WHERE       a.PlanYearID = @BidYear - 2
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND a.CPS IN (SELECT    DISTINCT   BaseCPS FROM #Base2BidCrosswalk)
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ReportingCategory

        UNION ALL
        --Current year weights do not exist for the bid projection, so we need to take the base year weights, crosswalk it to the current year, and then copy into the current year
        SELECT      cw.CurrentCPS
                   ,@BidYear - 1 AS 'PlanYearID'
                   ,a.QuarterID
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        LEFT JOIN   #Base2CurrentCrosswalk cw
               ON cw.BaseCPS = a.CPS
        WHERE       a.PlanYearID = @BidYear - 2
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND cw.CurrentCPS IN (SELECT    DISTINCT   CurrentCPS FROM #Current2BidCrosswalk)
        GROUP BY    cw.CurrentCPS
                   ,a.QuarterID
                   ,a.ReportingCategory

        UNION ALL
        --Need to take the base year weights, crosswalk it to the bid year, and then copy into the bid year
        SELECT      cw.BidCPS
                   ,@BidYear AS 'PlanYearID'
                   ,a.QuarterID
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        LEFT JOIN   #Base2BidCrosswalk cw
               ON cw.BaseCPS = a.CPS
        WHERE       a.PlanYearID = @BidYear - 2
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND cw.BidCPS IN (SELECT    DISTINCT   CPS FROM #CPS)
        GROUP BY    cw.BidCPS
                   ,a.QuarterID
                   ,a.ReportingCategory;

    END;


--Q3 Projection
IF @LastCurrentYearQuarter = 2

    BEGIN

        INSERT INTO #WeightsNoBid
        --Base year and historic quarters of the current year
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        WHERE       a.PlanYearID >= @BidYear - 2
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND (a.CPS IN (SELECT   DISTINCT BaseCPS FROM   #Base2BidCrosswalk)
        --     OR a.CPS IN (SELECT    DISTINCT   CurrentCPS FROM #Current2BidCrosswalk))
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ReportingCategory

        UNION ALL
        --Copy the last year of historical in the current year (Q2) into Q3
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,@LastCurrentYearQuarter + 1 AS 'QuarterID'
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        WHERE       a.PlanYearID = @BidYear - 1
                    AND QuarterID = @LastCurrentYearQuarter
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND a.CPS IN (SELECT    DISTINCT   CurrentCPS FROM #Current2BidCrosswalk)
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.ReportingCategory

        UNION ALL
        --Copy the last year of historical in the current year (Q2) into Q4
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,@LastCurrentYearQuarter + 2 AS 'QuarterID'
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        WHERE       a.PlanYearID = @BidYear - 1
                    AND QuarterID = @LastCurrentYearQuarter
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND a.CPS IN (SELECT    DISTINCT   CurrentCPS FROM #Current2BidCrosswalk)
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.ReportingCategory;


        INSERT INTO #Weights
        --Insert base and current year data from previous table
        SELECT  CPS
               ,PlanYearID
               ,QuarterID
               ,ReportingCategory
               ,Weights
        FROM    #WeightsNoBid

        UNION ALL
        --Need to take the base year weights (current year if base year does not exist), crosswalk it to the bid year, and then copy into the bid year
        SELECT      a.BidCPS
                   ,@BidYear
                   ,a.QuarterID
                   ,a.ReportingCategory
                   ,a.Weights
        FROM        (SELECT     cw.BidCPS   --Pull in base year data and crosswalk to the bid year
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory
                               ,SUM (weights.Weights) AS 'Weights'
                     FROM       #WeightsNoBid weights
                    INNER JOIN  #Base2BidCrosswalk cw
                            ON cw.BaseCPS = weights.CPS
                     WHERE      weights.PlanYearID = @BidYear - 2
                     GROUP BY   cw.BidCPS
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory

                     UNION

                     SELECT     cw.BidCPS   --Pull in current year data and crosswalk to the bid year
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory
                               ,SUM (weights.Weights) AS 'Weights'
                     FROM       #WeightsNoBid weights
                    INNER JOIN  #Current2BidCrosswalk cw
                            ON cw.CurrentCPS = weights.CPS
                     WHERE      weights.PlanYearID = @BidYear - 1
                     GROUP BY   cw.BidCPS
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory) a
        LEFT JOIN   #PlanTypes pt
               ON pt.BidCPS = a.BidCPS
        WHERE       a.PlanYearID = (CASE WHEN pt.PlanType = 'Concurrent' THEN @BidYear - 2 ELSE @BidYear - 1 END);

    END;


--Q4 Projection
IF @LastCurrentYearQuarter = 3

    BEGIN

        INSERT INTO #WeightsNoBid
        --Base year and historic quarters of the current year
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        WHERE       a.PlanYearID >= @BidYear - 2
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND (a.CPS IN (SELECT   DISTINCT BaseCPS FROM   #Base2BidCrosswalk)
        --     OR a.CPS IN (SELECT    DISTINCT   CurrentCPS FROM #Current2BidCrosswalk))
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,a.ReportingCategory

        UNION ALL
        --Copy the last year of actuals in the current year (Q3) into Q4
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,@LastCurrentYearQuarter + 1 AS 'QuarterID'
                   ,a.ReportingCategory
                   ,COALESCE (SUM (a.Weight), 0)
        FROM        #CalcBaseHistoric a
        WHERE       a.PlanYearID = @BidYear - 1
                    AND QuarterID = @LastCurrentYearQuarter
                    AND a.WeightType = (CASE WHEN @XComponentID IN (8, 9, 10) THEN 'Allowed' --Contractual, CMS Reimbursement, Outlier Claims
                                             WHEN @XComponentID IN (3) THEN 'Membership'     --Population
                                             WHEN @XComponentID IN (4) THEN 'Utilization'    --Induced Utilization
                                        END)
        --AND a.CPS IN (SELECT    DISTINCT   CurrentCPS FROM #Current2BidCrosswalk)
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.ReportingCategory;


        INSERT INTO #Weights
        --Insert base and current year data from previous table
        SELECT  CPS
               ,PlanYearID
               ,QuarterID
               ,ReportingCategory
               ,Weights
        FROM    #WeightsNoBid

        UNION ALL
        --Need to take the base year weights (current year if base year does not exist), crosswalk it, and then copy into the bid year
        SELECT      a.BidCPS
                   ,@BidYear
                   ,a.QuarterID
                   ,a.ReportingCategory
                   ,a.Weights
        FROM        (SELECT     cw.BidCPS   --Pull in base year data and crosswalk to the bid year
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory
                               ,SUM (weights.Weights) AS 'Weights'
                     FROM       #WeightsNoBid weights
                    INNER JOIN  #Base2BidCrosswalk cw
                            ON cw.BaseCPS = weights.CPS
                     WHERE      weights.PlanYearID = @BidYear - 2
                     GROUP BY   cw.BidCPS
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory

                     UNION

                     SELECT     cw.BidCPS   --Pull in current year data and crosswalk to the bid year
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory
                               ,SUM (weights.Weights) AS 'Weights'
                     FROM       #WeightsNoBid weights
                    INNER JOIN  #Current2BidCrosswalk cw
                            ON cw.CurrentCPS = weights.CPS
                     WHERE      weights.PlanYearID = @BidYear - 1
                     GROUP BY   cw.BidCPS
                               ,weights.PlanYearID
                               ,weights.QuarterID
                               ,weights.ReportingCategory) a
        LEFT JOIN   #PlanTypes pt
               ON pt.BidCPS = a.BidCPS
        WHERE       a.PlanYearID = (CASE WHEN pt.PlanType = 'Concurrent' THEN @BidYear - 2 ELSE @BidYear - 1 END);

    END;


--Join weights and relativity factors together
IF (SELECT  OBJECT_ID ('tempdb..#RelativityWeights')) IS NOT NULL
    DROP TABLE #RelativityWeights;
SELECT      a.CPS
           ,a.PlanYearID
           ,a.QuarterID
           ,a.ReportingCategory
           ,COALESCE (b.Weights, 0) AS 'Weights'
           --,CASE WHEN COALESCE (b.Weights, 0) = 0 THEN COALESCE(a.CostRelativity, 1) ELSE COALESCE (b.Weights * a.CostRelativity, 0) END AS 'CostRelativity'
           --,CASE WHEN COALESCE (b.Weights, 0) = 0 THEN COALESCE(a.UseRelativity, 1) ELSE COALESCE (b.Weights * a.UseRelativity, 0) END AS 'UseRelativity'
           ,CASE WHEN COALESCE (b.Weights, 0) = 0 THEN 0 ELSE COALESCE (b.Weights * a.CostRelativity, 0) END AS 'CostRelativity'
           ,CASE WHEN COALESCE (b.Weights, 0) = 0 THEN 0 ELSE COALESCE (b.Weights * a.UseRelativity, 0) END AS 'UseRelativity'
           ,a.CostRelativity AS 'CostRelativityFactor'
           ,a.UseRelativity AS 'UseRelativityFactor'
INTO        #RelativityWeights
FROM        #Relativities a
LEFT JOIN   #Weights b
       ON b.CPS = a.CPS
          AND   b.PlanYearID = a.PlanYearID
          AND   b.QuarterID = a.QuarterID
          AND   b.ReportingCategory = a.ReportingCategory
WHERE       a.ComponentID = @XComponentID;


--Build base year experience relativity table
IF (SELECT  OBJECT_ID ('tempdb..#ExperienceBaseRelativities')) IS NOT NULL
    DROP TABLE #ExperienceBaseRelativities;
SELECT      cw.BidCPS AS 'CPS'
           ,rel.PlanYearID
           ,rel.QuarterID
           ,rel.ReportingCategory
           ,SUM (COALESCE (rel.Weights, 0)) AS 'Weights'
           ,CASE WHEN SUM (COALESCE (rel.Weights, 0)) = 0 THEN COALESCE (AVG (rel.CostRelativityFactor), 1)
                 ELSE dbo.Trend_fnSafeDivide (SUM (COALESCE (rel.CostRelativity, 0)), SUM (COALESCE (rel.Weights, 0)), 1) END AS 'CostRelativity'
           ,CASE WHEN SUM (COALESCE (rel.Weights, 0)) = 0 THEN COALESCE (AVG (rel.UseRelativityFactor), 1)
                 ELSE dbo.Trend_fnSafeDivide (SUM (COALESCE (rel.UseRelativity, 0)), SUM (COALESCE (rel.Weights, 0)), 1) END AS 'UseRelativity'
INTO        #ExperienceBaseRelativities
FROM        #RelativityWeights rel
LEFT JOIN   #Base2BidCrosswalk cw
       ON cw.BaseCPS = rel.CPS
WHERE       rel.PlanYearID = @BidYear - 2
GROUP BY    cw.BidCPS
           ,rel.PlanYearID
           ,rel.QuarterID
           ,rel.ReportingCategory;


--Build current year experience relativity table
IF (SELECT  OBJECT_ID ('tempdb..#ExperienceCurrentRelativities')) IS NOT NULL
    DROP TABLE #ExperienceCurrentRelativities;
SELECT      cw.BidCPS AS 'CPS'
           ,rel.PlanYearID
           ,rel.QuarterID
           ,rel.ReportingCategory
           ,SUM (COALESCE (rel.Weights, 0)) AS 'Weights'
           ,CASE WHEN SUM (COALESCE (rel.Weights, 0)) = 0 THEN COALESCE (AVG (rel.CostRelativityFactor), 1)
                 ELSE dbo.Trend_fnSafeDivide (SUM (COALESCE (rel.CostRelativity, 0)), SUM (COALESCE (rel.Weights, 0)), 1) END AS 'CostRelativity'
           ,CASE WHEN SUM (COALESCE (rel.Weights, 0)) = 0 THEN COALESCE (AVG (rel.UseRelativityFactor), 1)
                 ELSE dbo.Trend_fnSafeDivide (SUM (COALESCE (rel.UseRelativity, 0)), SUM (COALESCE (rel.Weights, 0)), 1) END AS 'UseRelativity'
INTO        #ExperienceCurrentRelativities
FROM        #RelativityWeights rel
LEFT JOIN   #Current2BidCrosswalk cw
       ON cw.CurrentCPS = rel.CPS
WHERE       rel.PlanYearID = @BidYear - 1
GROUP BY    cw.BidCPS
           ,rel.PlanYearID
           ,rel.QuarterID
           ,rel.ReportingCategory;


--Build bid year experience relativity table
IF (SELECT  OBJECT_ID ('tempdb..#ExperienceBidRelativities')) IS NOT NULL
    DROP TABLE #ExperienceBidRelativities;
SELECT      rel.CPS
           ,rel.PlanYearID
           ,rel.QuarterID
           ,rel.ReportingCategory
           ,SUM (COALESCE (rel.CostRelativity, 1)) AS 'CostRelativity'
           ,SUM (COALESCE (rel.UseRelativity, 1)) AS 'UseRelativity'
INTO        #ExperienceBidRelativities
FROM        #Relativities rel
WHERE       rel.PlanYearID = @BidYear
            AND rel.ComponentID = @XComponentID
GROUP BY    rel.CPS
           ,rel.PlanYearID
           ,rel.QuarterID
           ,rel.ReportingCategory;


--Build manual rate relativity table
IF (SELECT  OBJECT_ID ('tempdb..#ManualRateRelativities')) IS NOT NULL
    DROP TABLE #ManualRateRelativities;
SELECT      man.ManualCutID
           ,q.QuarterID
           ,rc.ReportingCategory
           ,SUM (COALESCE (rel.Weights, 0)) AS 'Weights'
           ,dbo.Trend_fnSafeDivide (SUM (COALESCE (rel.CostRelativity, 0)), SUM (COALESCE (rel.Weights, 0)), 1) AS 'CostRelativity'
           ,dbo.Trend_fnSafeDivide (SUM (COALESCE (rel.UseRelativity, 0)), SUM (COALESCE (rel.Weights, 0)), 1) AS 'UseRelativity'
INTO        #ManualRateRelativities
FROM        #PlansInManual man
CROSS JOIN  #RepCat rc
CROSS JOIN  #Quarter q
LEFT JOIN   #RelativityWeights rel
       ON rel.CPS = man.CPS
          AND   rel.PlanYearID = man.PlanYearID
          AND   rc.ReportingCategory = rel.ReportingCategory
          AND   q.QuarterID = rel.QuarterID
GROUP BY    man.ManualCutID
           ,q.QuarterID
           ,rc.ReportingCategory;


--Transpose the rolled up relativities for trend calculation (experience)
IF (SELECT  OBJECT_ID ('tempdb..#YearOverYearRelativitiesExp')) IS NOT NULL
    DROP TABLE #YearOverYearRelativitiesExp;
CREATE TABLE #YearOverYearRelativitiesExp
    (PlanInfoID                  SMALLINT
    ,CPS                         CHAR(13)
    ,PlanType                    VARCHAR(50)
    ,ReportingCategory           VARCHAR(50)
    ,IsSkippedInducedUtilization BIT
    ,BaseCostRelativity          DECIMAL(18, 8)
    ,BaseUseRelativity           DECIMAL(18, 8)
    ,CurrentCostRelativity       DECIMAL(18, 8)
    ,CurrentUseRelativity        DECIMAL(18, 8)
    ,BidCostRelativity           DECIMAL(18, 8)
    ,BidUseRelativity            DECIMAL(18, 8));

--Transpose the rolled up relativities for trend calculation (manual)
IF (SELECT  OBJECT_ID ('tempdb..#YearOverYearRelativitiesMan')) IS NOT NULL
    DROP TABLE #YearOverYearRelativitiesMan;
CREATE TABLE #YearOverYearRelativitiesMan
    (PlanInfoID                  SMALLINT
    ,CPS                         CHAR(13)
    ,PlanType                    VARCHAR(50)
    ,AssignedManualCutID         INT
    ,ReportingCategory           VARCHAR(50)
    ,IsSkippedInducedUtilization BIT
    ,BaseCostRelativity          DECIMAL(18, 8)
    ,BaseUseRelativity           DECIMAL(18, 8)
    ,CurrentCostRelativity       DECIMAL(18, 8)
    ,CurrentUseRelativity        DECIMAL(18, 8)
    ,BidCostRelativity           DECIMAL(18, 8)
    ,BidUseRelativity            DECIMAL(18, 8));



--Pull in base through bid factors and calculate trends

--Experience (RateType = 1)
INSERT INTO #YearOverYearRelativitiesExp
--Concurrent plans
SELECT      cps.PlanInfoID
           ,cps.CPS
           ,pt.PlanType
           ,rc.ReportingCategory
           ,sfs.IsSkippedInducedUtilization
           ,COALESCE (AVG (baserel.CostRelativity), 1) AS 'BaseCostRelativity'
           ,COALESCE (AVG (baserel.UseRelativity), 1) AS 'BaseUseRelativity'
           ,COALESCE (AVG (currrel.CostRelativity), 1) AS 'CurrentCostRelativity'
           ,COALESCE (AVG (currrel.UseRelativity), 1) AS 'CurrentUseRelativity'
           ,COALESCE (AVG (bidrel.CostRelativity), 1) AS 'BidCostRelativity'
           ,COALESCE (AVG (bidrel.UseRelativity), 1) AS 'BidUseRelativity'
FROM        #CPS cps
CROSS JOIN  #RepCat rc
CROSS JOIN  #Quarter q
LEFT JOIN   #PlanTypes pt
       ON pt.BidCPS = cps.CPS
LEFT JOIN   #ExperienceBaseRelativities baserel
       ON baserel.CPS = cps.CPS
          AND   baserel.QuarterID = q.QuarterID
          AND   baserel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   #ExperienceCurrentRelativities currrel
       ON currrel.CPS = cps.CPS
          AND   currrel.QuarterID = q.QuarterID
          AND   currrel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   #ExperienceBidRelativities bidrel
       ON bidrel.CPS = cps.CPS
          AND   bidrel.QuarterID = q.QuarterID
          AND   bidrel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   dbo.SavedForecastSetup sfs
       ON cps.PlanInfoID = sfs.PlanInfoID
WHERE       pt.PlanType = 'Concurrent'  -- AND cps.PlanInfoID IN (3896, 3925) AND rc.ReportingCategory = 'SNF'
GROUP BY    cps.PlanInfoID
           ,cps.CPS
           ,pt.PlanType
           ,rc.ReportingCategory
           ,sfs.IsSkippedInducedUtilization;


INSERT INTO #YearOverYearRelativitiesExp
--New in current year plans
SELECT      cps.PlanInfoID
           ,cps.CPS
           ,pt.PlanType
           ,rc.ReportingCategory
           ,sfs.IsSkippedInducedUtilization
           ,COALESCE (AVG (baserel.CostRelativity), 1) AS 'BaseCostRelativity'
           ,COALESCE (AVG (baserel.UseRelativity), 1) AS 'BaseUseRelativity'
           ,COALESCE (AVG (currrel.CostRelativity), 1) AS 'CurrentCostRelativity'
           ,COALESCE (AVG (currrel.UseRelativity), 1) AS 'CurrentUseRelativity'
           ,COALESCE (AVG (bidrel.CostRelativity), 1) AS 'BidCostRelativity'
           ,COALESCE (AVG (bidrel.UseRelativity), 1) AS 'BidUseRelativity'
FROM        #CPS cps
CROSS JOIN  #RepCat rc
CROSS JOIN  #Quarter q
LEFT JOIN   #PlanTypes pt
       ON pt.BidCPS = cps.CPS
LEFT JOIN   #ExperienceBaseRelativities baserel
       ON baserel.CPS = cps.CPS
          AND   baserel.QuarterID = q.QuarterID
          AND   baserel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   #ExperienceCurrentRelativities currrel
       ON currrel.CPS = cps.CPS
          AND   currrel.QuarterID = q.QuarterID
          AND   currrel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   #ExperienceBidRelativities bidrel
       ON bidrel.CPS = cps.CPS
          AND   bidrel.QuarterID = q.QuarterID
          AND   bidrel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   dbo.SavedForecastSetup sfs
       ON cps.PlanInfoID = sfs.PlanInfoID
WHERE       pt.PlanType = 'NewCurrent'
GROUP BY    cps.PlanInfoID
           ,cps.CPS
           ,pt.PlanType
           ,rc.ReportingCategory
           ,sfs.IsSkippedInducedUtilization;


IF @XComponentID IN (4) --Induced Utilization
    BEGIN

        --Delete plans that are passed to the parameter for experience
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH(ROWLOCK)
        WHERE   RateType = 1
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #YearOverYearRelativitiesExp);
        --Insert concurrent plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Induced Utilization'
               ,'Plan'
               ,NULL
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 END AS 'CostAdjustment'
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 END AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Induced Utilization'
               ,'Plan'
               ,NULL
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 END AS 'CostAdjustment'
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 END AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent';
        --Insert new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH(ROWLOCK)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Induced Utilization'
               ,'Plan'
               ,NULL
               ,0 AS 'CostAdjustment'   --New in current year, so no base to current experience trends
               ,0 AS 'UseAdjustment'    --New in current year, so no base to current experience trends
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent'
        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Induced Utilization'
               ,'Plan'
               ,NULL
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 END AS 'CostAdjustment'
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 END AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent';
    END;



IF @XComponentID IN (8) --Contractual
    BEGIN

        --Delete plans that are passed to the parameter for experience
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH(ROWLOCK)
        WHERE   RateType = 1
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #YearOverYearRelativitiesExp);

        --Insert concurrent plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Contractual'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Contractual'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent';

        --Insert new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH(ROWLOCK)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Contractual'
               ,'Plan'
               ,NULL
               ,0 AS 'CostAdjustment'   --New in current year, so no base to current experience trends
               ,0 AS 'UseAdjustment'    --New in current year, so no base to current experience trends
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent'
        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Contractual'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent';
    END;



IF @XComponentID IN (9) --CMS Reimbursement
    BEGIN

        --Delete plans that are passed to the parameter for experience
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH(ROWLOCK)
        WHERE   RateType = 1
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #YearOverYearRelativitiesExp);

        --Insert concurrent plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'CMS Reimbursement'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'CMS Reimbursement'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent';

        --Insert new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH(ROWLOCK)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'CMS Reimbursement'
               ,'Plan'
               ,NULL
               ,0 AS 'CostAdjustment'   --New in current year, so no base to current experience trends
               ,0 AS 'UseAdjustment'    --New in current year, so no base to current experience trends
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent'
        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'CMS Reimbursement'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent';
    END;


IF @XComponentID IN (10) --Outlier Claims
    BEGIN

        --Delete plans that are passed to the parameter for experience
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH(ROWLOCK)
        WHERE   RateType = 1
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #YearOverYearRelativitiesExp);

        --Insert concurrent plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Outlier Claims'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Outlier Claims'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent';

        --Insert new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH(ROWLOCK)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Outlier Claims'
               ,'Plan'
               ,NULL
               ,0 AS 'CostAdjustment'   --New in current year, so no base to current experience trends
               ,0 AS 'UseAdjustment'    --New in current year, so no base to current experience trends
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent'
        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Outlier Claims'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent';
    END;


IF @XComponentID IN (3) --Population
    BEGIN

        --Delete plans that are passed to the parameter for experience
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH(ROWLOCK)
        WHERE   RateType = 1
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #YearOverYearRelativitiesExp);

        --Insert concurrent plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Population'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Population'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'Concurrent';

        --Insert new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH(ROWLOCK)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Population'
               ,'Plan'
               ,NULL
               ,0 AS 'CostAdjustment'   --New in current year, so no base to current experience trends
               ,0 AS 'UseAdjustment'    --New in current year, so no base to current experience trends
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent'
        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,1 AS 'RateType'
               ,ReportingCategory
               ,'Population'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesExp
        WHERE   PlanType = 'NewCurrent';
    END;



--Manual (RateType = 2)
INSERT INTO #YearOverYearRelativitiesMan
SELECT      cps.PlanInfoID
           ,cps.CPS
           ,pt.PlanType
           ,ManAssign.AssignedManualCutID
           ,rc.ReportingCategory
           ,sfs.IsSkippedInducedUtilization
           ,COALESCE (AVG (baserel.CostRelativity), 1) AS 'BaseCostRelativity'
           ,COALESCE (AVG (baserel.UseRelativity), 1) AS 'BaseUseRelativity'
           ,COALESCE (AVG (currrel.CostRelativity), 1) AS 'CurrentCostRelativity'
           ,COALESCE (AVG (currrel.UseRelativity), 1) AS 'CurrentUseRelativity'
           ,COALESCE (AVG (bidrel.CostRelativity), 1) AS 'BidCostRelativity'
           ,COALESCE (AVG (bidrel.UseRelativity), 1) AS 'BidUseRelativity'
FROM        #CPS cps
CROSS JOIN  #RepCat rc
CROSS JOIN  #Quarter q
LEFT JOIN   #PlanTypes pt
       ON pt.BidCPS = cps.CPS
LEFT JOIN   #ManualAssignments ManAssign
       ON ManAssign.PlanInfoID = cps.PlanInfoID
INNER JOIN  #ManualRateRelativities baserel
        ON baserel.ManualCutID = ManAssign.AssignedManualCutID
           AND  baserel.QuarterID = q.QuarterID
           AND  baserel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   #ExperienceCurrentRelativities currrel
       ON currrel.CPS = cps.CPS
          AND   currrel.QuarterID = q.QuarterID
          AND   currrel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   #ExperienceBidRelativities bidrel
       ON bidrel.CPS = cps.CPS
          AND   bidrel.QuarterID = q.QuarterID
          AND   bidrel.ReportingCategory = rc.ReportingCategory
LEFT JOIN   dbo.SavedForecastSetup sfs
       ON cps.PlanInfoID = sfs.PlanInfoID
GROUP BY    cps.PlanInfoID
           ,cps.CPS
           ,pt.PlanType
           ,ManAssign.AssignedManualCutID
           ,rc.ReportingCategory
           ,sfs.IsSkippedInducedUtilization;


IF @XComponentID IN (4) --Induced utilization
    BEGIN
        --Delete plans that are passed to the parameter for manual
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH(ROWLOCK)
        WHERE   RateType = 2
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #CPS);

        --Concurrent and new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Induced Utilization'
               ,'Plan'
               ,NULL
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 END AS 'CostAdjustment'
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 END AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Induced Utilization'
               ,'Plan'
               ,NULL
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 END AS 'CostAdjustment'
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 END AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid';

        --New in bid year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Induced Utilization'
               ,'Plan'
               ,NULL
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 END AS 'CostAdjustment' --two year trend from base to bid since nothing for the current year exists
               ,CASE WHEN IsSkippedInducedUtilization = 0 THEN 0
                     ELSE dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 END AS 'UseAdjustment'    --two year trend from base to bid since nothing for the current year exists

               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType = 'NewBid';
    END;


IF @XComponentID IN (8) --Contractual
    BEGIN
        --Delete plans that are passed to the parameter for manual
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH(ROWLOCK)
        WHERE   RateType = 2
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #CPS);


        --Concurrent and new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Contractual'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Contractual'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid';


        --New in bid year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Contractual'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'   --two year trend from base to bid since nothing for the current year exists
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'      --two year trend from base to bid since nothing for the current year exists
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType = 'NewBid';
    END;


IF @XComponentID IN (9) --CMS Reimbursement
    BEGIN
        --Delete plans that are passed to the parameter for manual
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH(ROWLOCK)
        WHERE   RateType = 2
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #CPS);


        --Concurrent and new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'CMS Reimbursement'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'CMS Reimbursement'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid';


        --New in bid year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'CMS Reimbursement'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'   --two year trend from base to bid since nothing for the current year exists
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'      --two year trend from base to bid since nothing for the current year exists
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType = 'NewBid';
    END;


IF @XComponentID IN (10) --Outlier Claims
    BEGIN
        --Delete plans that are passed to the parameter for manual
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH(ROWLOCK)
        WHERE   RateType = 2
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #CPS);


        --Concurrent and new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Outlier Claims'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Outlier Claims'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid';


        --New in bid year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Outlier Claims'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'   --two year trend from base to bid since nothing for the current year exists
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'      --two year trend from base to bid since nothing for the current year exists
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType = 'NewBid';
    END;


IF @XComponentID IN (3) --Population
    BEGIN
        --Delete plans that are passed to the parameter for manual
        DELETE  FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH(ROWLOCK)
        WHERE   RateType = 2
                AND PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #CPS);


        --Concurrent and new in current year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear - 1
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Population'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (CurrentCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (CurrentUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid'

        UNION

        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Population'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, CurrentCostRelativity, 1) - 1 AS 'CostAdjustment'
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, CurrentUseRelativity, 1) - 1 AS 'UseAdjustment'
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType <> 'NewBid';


        --New in bid year plans
        INSERT INTO dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,TrendYearID
            ,RateType
            ,ReportingCategory
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  PlanInfoID
               ,CPS
               ,@BidYear
               ,@BidYear - 2
               ,@BidYear
               ,2 AS 'RateType'
               ,ReportingCategory
               ,'Population'
               ,'Plan'
               ,NULL
               ,dbo.Trend_fnSafeDivide (BidCostRelativity, BaseCostRelativity, 1) - 1 AS 'CostAdjustment'   --two year trend from base to bid since nothing for the current year exists
               ,dbo.Trend_fnSafeDivide (BidUseRelativity, BaseUseRelativity, 1) - 1 AS 'UseAdjustment'      --two year trend from base to bid since nothing for the current year exists
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #YearOverYearRelativitiesMan
        WHERE   PlanType = 'NewBid';
    END;




--Email for missing manual cut assignments
DECLARE @recipients NVARCHAR(MAX);
DECLARE @BodyOutput NVARCHAR(MAX);
DECLARE @XML NVARCHAR(MAX);
DECLARE @Subj VARCHAR(MAX);
DECLARE @LastBodyOutput NVARCHAR(MAX);


--Find the plans passed to the procedure with missing cut assignments
-- Update 7-27 to exclude fully credible plans from email
IF (SELECT  OBJECT_ID ('tempdb..#MissingCutAssign')) IS NOT NULL
    DROP TABLE #MissingCutAssign;

SELECT      DISTINCT
            a.PlanInfoID
           ,a.CPS
           ,b.AssignedManualCutID
           ,ISNULL (CASE WHEN SUM (c.MemberMonths) >= (SELECT CredibleMemberMonths
                                                 FROM dbo.PerExtCMSValues) THEN 'Y'
                         ELSE 'N' END
                   ,'N') AS Fully_Credible
INTO        #MissingCutAssign
FROM        #CPS a
LEFT JOIN   #ManualAssignments b
       ON a.PlanInfoID = b.PlanInfoID
LEFT JOIN   dbo.Trend_Data_CalcBaseMembership c
       ON a.PlanInfoID = c.PlanInfoID
WHERE       b.AssignedManualCutID IS NULL AND c.RateType = '1'
GROUP BY    a.PlanInfoID
           ,a.CPS
           ,b.AssignedManualCutID
HAVING      ISNULL (CASE WHEN SUM (c.MemberMonths) >= (SELECT CredibleMemberMonths FROM dbo.PerExtCMSValues) THEN 'Y'
                         ELSE 'N' END
                   ,'N') = 'N';


--Build the email
IF (SELECT  COUNT (*) FROM  #MissingCutAssign) > 0
    BEGIN



        SET @XML = CAST((SELECT     CPS AS 'td'
                         FROM       #MissingCutAssign
                         ORDER BY   CPS
                        FOR XML PATH ('tr'), ELEMENTS) AS NVARCHAR(MAX));


        SET @BodyOutput = N'<html><body><H3>
                                These plans do not have a manual cut assigned.  
                                Assign manual cuts to these plans to project manual plan level trends.  
                                If you have any questions please contact the iRS Team.
                            </H3>
                            <table border = 1 style=font-size:12px;>
                            <tr>
                                <th> PlanWithMissingAssignment </th>
                            </tr>';

        SET @BodyOutput = @BodyOutput + @XML + N'</table></body></html>';

        SET @LastBodyOutput = @BodyOutput;

        --Pulls e-mail addresses from table
        SELECT  @recipients = COALESCE (@recipients + '; ', '') + [e-mail]
        FROM    (SELECT Email AS [e-mail]
                 FROM   dbo.W_FEM_AccessControl WITH (NOLOCK)
                 WHERE  TrendTeam = 1

                 UNION

                 SELECT Email AS [e-mail]
                 FROM   dbo.W_FEM_AccessControl WITH (NOLOCK)
                 WHERE  UserID = @LastUpdateByID) a;


        --Send the e-mail
        SET @Subj = (SELECT DB_NAME () + ': Plans not projecting plan level trends');
        EXEC msdb.dbo.sp_send_dbmail @recipients = @recipients
                                    ,@body = @BodyOutput
                                    ,@subject = @Subj
                                    ,@body_format = 'HTML'
                                    ,@importance = 'High';

    END;










/*Queries for checks*/

--SELECT * FROM #ManualAssignments WHERE PlanInfoID = 3776
--SELECT * FROM #ManualRateRelativities WHERE ManualCutID = 15 AND ReportingCategory = 'OP' ORDER BY QuarterID
--SELECT * FROM #YearOverYearRelativitiesMan WHERE PlanInfoID = 3776 AND ReportingCategory = 'OP'
--SELECT * FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WHERE PlanInfoID = 3776 AND ReportingCategory = 'OP'

--SELECT * FROM #Base2BidCrosswalk WHERE BidCPS = 'H0028-011-000'
--SELECT * FROM #Current2BidCrosswalk
--SELECT * FROM #WeightsNoBid WHERE ReportingCategory = 'OP' AND CPS IN ('H0028-004-000') ORDER BY CPS,PlanYearID,QuarterID
--SELECT * FROM #Weights WHERE ReportingCategory = 'OP' AND CPS IN ('H0028-004-000') ORDER BY CPS,PlanYearID,QuarterID
--SELECT * FROM #Relativities WHERE ReportingCategory = 'IP' AND CPS IN ('H0028-004-000') AND ComponentID = 4 ORDER BY CPS,PlanYearID,QuarterID
--SELECT * FROM #RelativityWeights WHERE ReportingCategory = 'IP' AND CPS IN ('H0028-004-000') ORDER BY CPS,PlanYearID,QuarterID
--SELECT * FROM #ExperienceBaseRelativities WHERE ReportingCategory = 'IP' AND CPS IN ('H0028-004-000') ORDER BY CPS,PlanYearID,QuarterID
--SELECT * FROM #ExperienceCurrentRelativities WHERE ReportingCategory = 'IP' AND CPS IN ('H0028-004-000') ORDER BY CPS,PlanYearID,QuarterID
--SELECT * FROM #ExperienceBidRelativities WHERE ReportingCategory = 'IP' AND CPS IN ('H0028-004-000') ORDER BY CPS,PlanYearID,QuarterID
--SELECT * FROM #YearOverYearRelativitiesExp WHERE PlanInfoID = 5447 ReportingCategory = 'IP' AND CPS IN ('H0028-004-000')
--SELECT * FROM dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WHERE PlanInfoID = 3758 AND ReportingCategory = 'IP'
--SELECT * FROM dbo.Trend_SavedRelativityInducedUtilization WHERE CostRelativity <> 1
GO
