-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	TrendAdj_spClearStageData
--
-- CREATOR:			<PERSON><PERSON><PERSON><PERSON>
--
-- CREATED DATE:	2024-Sep-06
--
-- DESCRIPTION:		This sp is executed by the Unified Actuarial Adjustment Compiler tool; it deletes all key info staging data and initial Adjustments for the matching Region and SessionID.
--		
-- PARAMETERS:
--  Input  :		@SessionID
--					@Region
--					@LastUpdateByID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			vwPlanInfo
--
--  Write:			TrendAdj_SAR_CalcKeyInfo_stage
--					TrendAdj_OSNP_CalcKeyInfo_stage
--					TrendAdj_CalcPlanAdjmt
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS:	NONE
--
-- $HISTORY
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-Sep-06      1           Initial Version                                                     Aleksandar Dimitrijevic
-- 2024-Oct-10      2           Added NOLOCK & optimazation	                                        Kumar Jalendran
-- 2024-Oct-17      3           Dropped all temp tables at end of procedure                         Kumar Jalendran
-- 2024-Oct-25      5           Updated the error loging to dbo.TrendAdjLog                         Kumar Jalendran
-- 2025-May-06      6           Updated to remove TrendAdj_HoaVBid_CalcKeyInfo_stage reference      Kumar Jalendran
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[TrendAdj_spClearStageData]
    @SessionID VARCHAR(19),
    @Region VARCHAR(50),
	@LastUpdateByID CHAR(7)

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY


		-- Declare Variables
		DECLARE @XLastUpdateByID CHAR(7) = @LastUpdateByID,
				@XRegion VARCHAR(50) = @Region,
				@xSessionID VARCHAR(19) = @SessionID;

		-- Declare variables
		DECLARE @BidYear INT = (SELECT dbo.fngetbidyear());

            BEGIN TRANSACTION transaction_TrendAdjspClear; 
				
				-- Clearing data from TrendAdj_SAR_CalcKeyInfo_stage for the selected Region and matching SessionID
				DELETE sar
					FROM dbo.TrendAdj_SAR_CalcKeyInfo_stage sar WITH (NOLOCK)
					INNER JOIN dbo.vwPlanInfo vw WITH (NOLOCK)
					ON vw.CPS = sar.CPS
					WHERE vw.Region = @XRegion
					AND vw.PlanYear =  @BidYear
					AND LEFT(sar.SessionID,LEN(@xSessionID)) = @xSessionID;
					
				-- Clearing data from TrendAdj_OSNP_CalcKeyInfo_stage for the selected Region and matching SessionID
				DELETE osnp
					FROM dbo.TrendAdj_OSNP_CalcKeyInfo_stage osnp WITH (NOLOCK)
					INNER JOIN dbo.vwPlanInfo vw WITH (NOLOCK)
					ON vw.CPS = osnp.CPS
					WHERE vw.Region = @XRegion
					AND vw.PlanYear =  @BidYear
					AND LEFT(osnp.SessionID,LEN(@xSessionID)) = @xSessionID;
			
				-- Clearing data from TrendAdj_CalcPlanAdjmt for the selected Region and matching SessionID
				DELETE cpa
				  FROM dbo.TrendAdj_CalcPlanAdjmt cpa WITH (NOLOCK)
				 INNER JOIN dbo.vwPlanInfo vw WITH (NOLOCK)
				    ON vw.CPS = cpa.CPS
				 WHERE vw.Region = @XRegion
				   AND vw.PlanYear =  @BidYear
			       AND LEFT(cpa.SessionID,LEN(@xSessionID)) = @xSessionID;

				-- Clearing data from TrendAdj_CalcPlanAdjmt_SCT for the selected Region and matching SessionID
				DELETE sct
				  FROM dbo.TrendAdj_CalcPlanAdjmt_SCT sct WITH (NOLOCK)
				 INNER JOIN dbo.vwPlanInfo vw WITH (NOLOCK)
				    ON vw.CPS = sct.ContractPBPSegment
				 WHERE vw.Region = @XRegion
				   AND vw.PlanYear =  @BidYear
			       AND LEFT(sct.SessionID,LEN(@xSessionID)) = @xSessionID;

            COMMIT TRANSACTION transaction_TrendAdjspClear;

        END TRY

        BEGIN CATCH

			DECLARE @ErrorMessage NVARCHAR(4000);
		    DECLARE @ErrorSeverity INT;
		    DECLARE @ErrorState INT;
		    DECLARE @ErrorException NVARCHAR(4000);
		    DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
				    @currentdate DATETIME = GETDATE();

			SELECT @ErrorMessage = ERROR_MESSAGE();
			SELECT @ErrorSeverity = ERROR_SEVERITY();
			SELECT @ErrorState = ERROR_STATE();

			RAISERROR(   
				@ErrorMessage,  -- Message text.  
				@ErrorSeverity, -- Severity.  
				@ErrorState     -- State.  
			);

			ROLLBACK TRANSACTION;

			--- Insert into app log for logging error------------------
			INSERT INTO dbo.TrendAdjLog
                (AdjGroupID
                ,ProcName
                ,Region
                ,UserID
                ,AuditTime
                ,AuditMessage)
            VALUES (1
                   ,'Clear:Stage'
                   ,LEFT(@xRegion, 40)
                   ,@XLastUpdateByID
                   ,GETDATE ()
                   ,@ErrorMessage);

		END CATCH;
		
	END;
GO