SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportColumnProperty
--
-- AUTHOR: <PERSON><PERSON><PERSON>sky    
--
-- CREATED DATE: 2009-Mar-25
-- HEADER UPDATED: 2010-Nov-05
--
-- DESCRIPTION:   Get list of the column names for MAMBA MAReport feature which 
--                  should be displayed with the defined style
--        TEST: select * from fnGetMAReportColumnProperty()
--
-- PARAMETERS:
--	Input:
--  Output:
--
-- TABLES: 
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-25		1		    Initial Version										                Aleksey Titievsky  
-- 2016-Nov-17		2			Removed ReportLevelID 6												Chris Fleming
-- ----------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnGetMAReportColumnProperty] ()
RETURNS TABLE
AS
RETURN

SELECT ReportLevelID, ColumnName, ColumnStyle
    FROM 
   
-- Plan Level

(SELECT  1 ReportLevelID, ColumnName, ColumnStyle
            FROM
            (

SELECT 'Plan Year' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Division' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Region' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'BPT Product Type' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'ContractNumber' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Contract-PBP' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Plan Index' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Market' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Plan Name' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'SNP Type' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Member Premium' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'MA Risk Score' ColumnName, 'number4decimal' ColumnStyle 
UNION		
SELECT 'MSP Adj' ColumnName, 'percent2decimal' ColumnStyle
UNION		
SELECT 'MOOP - Combined' ColumnName, 'currencynodecimal' ColumnStyle
UNION		
SELECT 'MOOP - IN' ColumnName, 'currencynodecimal' ColumnStyle
UNION		
SELECT 'MOOP - OON' ColumnName, 'currencynodecimal' ColumnStyle
UNION		
SELECT 'Plan Level Deductible - IN' ColumnName, 'currencynodecimal' ColumnStyle
UNION		
SELECT 'Plan Level Deductible - OON' ColumnName, 'currencynodecimal' ColumnStyle
UNION		
SELECT 'MMs - Projected Total' ColumnName, 'number1decimal' ColumnStyle
UNION		
SELECT 'MMs - Projected ESRD' ColumnName, 'number1decimal' ColumnStyle
UNION		
SELECT 'MMs - Experience' ColumnName, 'number1decimal' ColumnStyle
UNION		
SELECT 'Total Projected Allowed Cost' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Total Projected Net Cost' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Proj Year Ben Factor' ColumnName, 'number4decimal' ColumnStyle
UNION		
SELECT 'Curr Year Ben Factor' ColumnName, 'number4decimal' ColumnStyle
UNION		
SELECT 'Admin % - Total' ColumnName, 'percent2decimal' ColumnStyle
UNION		
SELECT 'Admin PMPM - Total' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Admin PMPM - Marketing and Sales' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Admin PMPM - Direct' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Admin PMPM - Indirect' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Profit % - Total' ColumnName, 'percent2decimal' ColumnStyle
UNION		
SELECT 'Profit PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Required Revenue' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Plan A/B Bid' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Plan Benchmark' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'RPPO PBC' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Basic Member Premium' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Rebate' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Unspent Rebate' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Max ABCS Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'ABCS Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'ABCS Net' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Max MSB Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'MSB Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'MSB Net' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Max Rx Basic Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Rx Basic Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Rx Basic Net' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Max Rx Supp Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Rx Supp Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Rx Supp Net' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Max Part B Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Part B Buydown' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Member Premium Unrounded' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Audit Time' ColumnName, 'currency2decimal' ColumnStyle
) pl 
            
UNION

-- Benefit Level

SELECT  2 ReportLevelID, ColumnName, ColumnStyle
	FROM
    (

SELECT 'Plan Year' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Division' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Region' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'BPT Product Type' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'ContractNumber' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Contract-PBP' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Plan Index' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Benefit Year' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Benefit Category ID' ColumnName, 'numbernodecimal' ColumnStyle
UNION		
SELECT 'Benefit Category Name' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Benefit Level' ColumnName, 'numbernodecimal' ColumnStyle
UNION		
SELECT 'IN Benefit Type' ColumnName, 'DataValues' ColumnStyle 
UNION		
SELECT 'IN Benefit Value' ColumnName, 'number2decimal' ColumnStyle
UNION		
SELECT 'IN End of Day Range' ColumnName, 'numbernodecimal' ColumnStyle
UNION		
SELECT 'OON Benefit Type' ColumnName, 'DataValues' ColumnStyle               
UNION		
SELECT 'OON Benefit Value' ColumnName, 'number2decimal' ColumnStyle              
UNION		
SELECT 'OON End of Day Range' ColumnName, 'numbernodecimal' ColumnStyle 
) bl

UNION

-- Service Category

SELECT  3 ReportLevelID, ColumnName, ColumnStyle
	FROM
    (

SELECT 'Plan Year' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Division' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Region' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'BPT Product Type' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'ContractNumber' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Contract-PBP' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Plan Index' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'State ID' ColumnName, 'number2digits' ColumnStyle
UNION		
SELECT 'State Name' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'County ID' ColumnName, 'number3digits' ColumnStyle
UNION		
SELECT 'Country Name' ColumnName, 'DataValues' ColumnStyle
) sc

UNION

-- MSB

SELECT  4 ReportLevelID, ColumnName, ColumnStyle
	FROM
    (

SELECT 'Plan Year' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Division' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Region' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'BPT Product Type' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'ContractNumber' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Contract-PBP' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Plan Index' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Benefit Name' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'IN Added Benefit Description' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'IN Added Benefit Allowed' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'IN Added Benefit Utilization' ColumnName, 'number6decimal' ColumnStyle
UNION	
SELECT 'IN Added Benefit Cost Share' ColumnName, 'currency2decimal' ColumnStyle
UNION
SELECT 'OON Added Benefit Description' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'OON Added Benefit Allowed' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'OON Added Benefit Utilization' ColumnName, 'number6decimal' ColumnStyle
UNION	
SELECT 'OON Added Benefit Cost Share' ColumnName, 'currency2decimal' ColumnStyle
) msb

UNION

-- OSB

SELECT  5 ReportLevelID, ColumnName, ColumnStyle
	FROM
    (

SELECT 'Plan Year' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Division' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Region' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'BPT Product Type' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'ContractNumber' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Contract-PBP' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Plan Index' ColumnName, 'numbernodecimalwithoutcomma' ColumnStyle
UNION		
SELECT 'Package Name' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Package Allowed PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Package Cost Share PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION		
SELECT 'Item Description in Package' ColumnName, 'DataValues' ColumnStyle
UNION
SELECT 'Allowed Utilization Type ID' ColumnName, 'numbernodecimal' ColumnStyle
UNION		
SELECT 'Allowed Utilization Per 1000' ColumnName, 'number2decimal' ColumnStyle
UNION		
SELECT 'Allowed Average Cost' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Measurement UnitCode' ColumnName, 'DataValues' ColumnStyle
UNION		
SELECT 'Enrollee Cost Share Utilization' ColumnName, 'number2decimal' ColumnStyle
UNION	
SELECT 'Enrollee Average Cost Share' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Allowed PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Cost Share PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Net PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Admin PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Profit PMPM' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Member Premium' ColumnName, 'currency2decimal' ColumnStyle
UNION	
SELECT 'Projected MMs' ColumnName, 'numbernodecimal' ColumnStyle
 ) osb
 )A
GO
