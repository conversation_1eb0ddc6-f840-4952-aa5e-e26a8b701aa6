SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_spRefreshPopulationCurrentYear
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: Jan-23-2020
--
-- DESCRIPTION: This stored procedure takes current year membership and risk points from MRA and calculates population factor relativities by plan
--        
-- PARAMETERS:
--  Input  : @CPS
--	         @RegionString
--	         @ProductString	
--	         @LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_PerPopulationMRACurrentYear

--          Write:  Trend_CalcPopulationCurrentYear
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS: Trend_fnCalcStringToTable
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 8/1/18    		0		    Initial Version																	Andy Blink
-- 10/5/18			1			Accept report cat and risk ind fields from MRA table							Andy Blink
-- 2/1/19			2			Insert 1.0 factors into the geographic input table								Andy Blink
-- 8/1/19			3			Adding prior year barc class													Andy Blink
--									Moving the pop factor into use instead of allowed
-- 1/23/20          4           Adding to MAAModels for Trend Simplifications                                   Andy Blink
--                                  Updating column header, tables, and sp names
-- 6/24/20          5           Create temporary planinfo view to add SNPtype to product field                  Michael Manes
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_spRefreshPopulationCurrentYearPlanInfoID]
--Declare
@CPS AS          VARCHAR(MAX) = NULL    --list of plans separated by commas, EX: 'H1036-130-000,H1019-079-000,...' 
,@RegionString   VARCHAR(MAX) = NULL
,@ProductString  VARCHAR(MAX) = NULL
,@LastUpdateByID CHAR(7)                --= 'mxm2779'
AS

--DateTime variable so that timestamp on each table is identical 
DECLARE @LastUpdateDateTime AS DATETIME
       ,@CurrentYear        SMALLINT = NULL;

SET @LastUpdateDateTime = (SELECT   GETDATE ());
SET @CurrentYear = (SELECT  PlanYearID - 1 FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);

--Create temp planinfo table to adjust product granularity
IF (SELECT  OBJECT_ID ('tempdb..#vwPlanInfo')) IS NOT NULL
    DROP TABLE #vwPlanInfo;
SELECT  DISTINCT
        [CPS]
       ,[PlanYear]
       ,[Division]
       ,[Region]
	   ,[PlanInfoID]
       ,CASE WHEN [SNPType] = 'NA' THEN Product
             WHEN [SNPType] = 'Dual Eligible' THEN 'D-SNP'
             ELSE 'Other SNP' END AS Product
INTO    #vwPlanInfo
FROM    dbo.vwPlanInfo;

----Setup plan list for sp 
------Based on parameters: Plan,region,product
IF (SELECT  OBJECT_ID ('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans;
CREATE TABLE #Plans
    (CPS VARCHAR(13));
INSERT INTO #Plans
SELECT      DISTINCT
            CPS
FROM        #vwPlanInfo
WHERE       (CPS IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@CPS, ',', 1) )
             OR @CPS IS NULL)
            AND (Region IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@RegionString, ',', 1) )
                 OR @RegionString IS NULL)
            AND (Product IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@ProductString, ',', 1) )
                 OR @ProductString IS NULL)
ORDER BY    CPS;

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;



--Create temp table with RiskIND (used to add cuts to final bid year factors table to fit the MACTAPT input format)
IF (SELECT  OBJECT_ID ('tempdb..#AddFields')) IS NOT NULL
    DROP TABLE #AddFields;
CREATE TABLE #AddFields
    (RiskIND VARCHAR(1) NOT NULL);
INSERT INTO #AddFields (RiskIND) VALUES ('N'), ('Y');


----Create temp current year MRA table
IF (SELECT  OBJECT_ID ('tempdb..#CY_MRA')) IS NOT NULL DROP TABLE #CY_MRA;
CREATE TABLE #CY_MRA
    (CPS                CHAR(13)       NOT NULL
    ,SSStateCountyCD    VARCHAR(5)     NULL
    ,PlanYearID         SMALLINT       NOT NULL
    ,QuarterID          TINYINT        NOT NULL
    ,PriorYearBarcClass VARCHAR(1)     NOT NULL
    ,BarcClass          VARCHAR(1)     NULL
    ,IsDEPound          BIT            NULL
    ,ReportingCategory  VARCHAR(50)    NULL
    ,RiskInd            VARCHAR(1)     NOT NULL
    ,CY_MM              DECIMAL(18, 8) NULL
    ,CY_AllowedFactor   DECIMAL(18, 8) NULL
    ,CY_UseFactor       DECIMAL(18, 8) NULL
    ,LastUpdateByID     CHAR(13)       NOT NULL
    ,LastUpdateDateTime DATETIME       NOT NULL);
INSERT INTO #CY_MRA
SELECT      mra.CPS
           ,mra.SSStateCountyCD
           ,mra.PlanYearID
           ,mra.QuarterID
           ,mra.PriorYearBarcClass
           ,mra.BarcClass
           ,mra.IsDEPound
           ,mra.ReportingCategory
           ,AF.RiskIND
           ,SUM (mra.CY_MM)
           ,SUM (mra.CY_AllowedFactor)  --the input should be in total, i.e. (factor x MMs)
           ,SUM (mra.CY_UseFactor)      --the input should be in total, i.e. (factor x MMs)
           ,@LastUpdateByID
           ,@LastUpdateDateTime
FROM        dbo.Trend_PerPopulationMRACurrentYear mra
CROSS JOIN  #AddFields AF
WHERE       CPS IN (SELECT  * FROM  #Plans)
GROUP BY    mra.CPS
           ,mra.SSStateCountyCD
           ,mra.PlanYearID
           ,mra.QuarterID
           ,mra.PriorYearBarcClass
           ,mra.BarcClass
           ,mra.IsDEPound
           ,mra.ReportingCategory
           ,AF.RiskIND;



--Delete and repopulate current year factors (this table will feed the credibility fill sp and the bid year sp)
DELETE  FROM dbo.Trend_CalcPopulationCurrentYear
WHERE   CPS IN (SELECT  * FROM  #Plans);

INSERT INTO Trend_CalcPopulationCurrentYear
SELECT      mra.CPS
           ,mra.SSStateCountyCD
           ,vpi.Division
           ,vpi.Region
           ,vpi.Product
           ,mra.PlanYearID
           ,mra.QuarterID
           ,mra.ReportingCategory
           ,mra.RiskInd
           ,mra.PriorYearBarcClass
           ,mra.BarcClass
           ,mra.IsDEPound
           ,mra.CY_MM
           ,mra.CY_AllowedFactor    --still (factor x MMs)
           ,mra.CY_UseFactor        --still (factor x MMs)
           ,mra.LastUpdateByID
           ,mra.LastUpdateDateTime
FROM        #CY_MRA mra
LEFT JOIN   #vwPlanInfo vpi
       ON mra.CPS = vpi.CPS
          AND   @CurrentYear = vpi.PlanYear;



--Delete and populate the population relativity table
DELETE  FROM dbo.Trend_SavedRelativityPlanLevel
WHERE   PlanInfoID IN (SELECT       DISTINCT
                                    b.PlanInfoID
                       FROM         #Plans a
                      INNER JOIN    dbo.SavedPlanInfo b
                              ON b.PlanYear = @CurrentYear
                                 AND b.CPS = a.CPS);

INSERT INTO dbo.Trend_SavedRelativityPlanLevel
    (PlanInfoID
    ,Component
    ,PlanYearID
    ,QuarterID
    ,ReportingCategory
    ,CostRelativity
    ,UseRelativity
    ,LastUpdateByID
    ,LastUpdateDateTime)
SELECT      b.PlanInfoID
           ,'Population' AS 'Component'
           ,a.PlanYearID
           ,a.QuarterID
           ,a.ReportingCategory
           ,1 AS 'CostRelativity'
           ,dbo.Trend_fnSafeDivide (SUM (a.CY_UseFactor), SUM (a.CY_MM), 1) AS 'UseRelativity'
           ,@LastUpdateByID
           ,@LastUpdateDateTime
FROM        #CY_MRA a
INNER JOIN  dbo.SavedPlanInfo b
        ON b.PlanYear = @CurrentYear
           AND  b.CPS = a.CPS
GROUP BY    b.PlanInfoID
           ,a.PlanYearID
           ,a.QuarterID
           ,a.ReportingCategory;
GO
