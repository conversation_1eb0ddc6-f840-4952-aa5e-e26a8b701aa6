SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetOSBDropDownInfo]
--    
-- AUTHOR: Adam Gilbert
--    
-- CREATED DATE: 2025-Mar-05    
-- Type: 
-- DESCRIPTION: To get unique OSB codes to use in Benefit Interface App 
--    
-- PARAMETERS:    
-- Input: 
--  

-- TABLES:   
--

-- Read:    
--  dbo.LkpIntAddedBenefitType
--  

-- Write:    
--  
-- 
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-Mar-13		1		Initial Version                 Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetOSBDropDownInfo]  

AS
BEGIN
SET NOCOUNT ON

SELECT DISTINCT LEFT([Description],6) AS OSBCode,[Description] AS OSBDescription
FROM PerIntOptionalPackageheader h WITH (NOLOCK)
ORDER BY OSBDescription

END;
GO
