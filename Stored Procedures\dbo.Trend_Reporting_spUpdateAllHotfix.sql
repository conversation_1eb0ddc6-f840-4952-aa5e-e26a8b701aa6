SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: dbo.Trend_Reporting_spUpdateAllHotfix
--
-- CREATOR: <PERSON> Lewis
--
-- CREATED DATE: 2020-12-23
--
-- DESCRIPTION: Execute trend reporting procedures that are used by the Trend Impact Report and Trend Assumption Report. 
--				Apply locks to the group of procedures to ensure that only one user can execute at one time.  
--				This will help avoid data errors and deadlock issues when Market Support users are refreshing the reports. 
--				Hotfix option: allows queries (in place of the SPs) to be run from the reports in the event that a defect is discovered in the SP code. 
--				The @altCode input parameters below correspond to the executed Stored Procedures listed in the header, in order. 
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--				@PlanInfoID
--				@altCode1
--				@altCode2
--				@altCode3
--				@altCode4
--				@altCode5
--				@altCode6
--				@altCode7
--				@altCode8
--
--  Output : NONE
--
-- TABLES : Read :	Trend_Reporting_Log
--					W_FEM_AccessControl
--					
--          Write:	Trend_Reporting_Log
--                  
--
-- VIEWS: Read: NONE
--
-- FUNCTIONS: NONE
--
-- STORED PROCS: Executed:	Trend_Reporting_spCalcProjectedMembership
--							Trend_HistProcess_spCalcNotPlanLevel_PlanMappingFinal
--							Trend_HistProcess_spCalcIsPlanLevel
--							Trend_HistProcess_spCalcPlanTrends_RepCat
--							Trend_Reporting_spCalcCountyXWalkMembership
--							Trend_Reporting_spCalcImpactProjected
--							Trend_Reporting_spCalcImpactHistoric
--							Trend_Reporting_spCalcCompiledImpacts
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE															DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-12-23		1			Initial Version															Jake Lewis
-- 2021-03-24		2			Add PlanInfoID input parameter											Jake Lewis
--								Send email notification if an error is found
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_Reporting_spUpdateAllHotfix]

@LastUpdateByID CHAR(7)
,@PlanInfoID    VARCHAR(MAX) = NULL
,@altCode1      VARCHAR(MAX) = NULL
,@altCode2      VARCHAR(MAX) = NULL
,@altCode3      VARCHAR(MAX) = NULL
,@altCode4      VARCHAR(MAX) = NULL
,@altCode5      VARCHAR(MAX) = NULL
,@altCode6      VARCHAR(MAX) = NULL
,@altCode7      VARCHAR(MAX) = NULL
,@altCode8      VARCHAR(MAX) = NULL

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

		DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            -- Declare variables
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);
            DECLARE @lockIndicator INT;

            -- Trim unused space
            IF @altCode1 IS NOT NULL SET @altCode1 = RTRIM (@altCode1);
            IF @altCode2 IS NOT NULL SET @altCode2 = RTRIM (@altCode2);
            IF @altCode3 IS NOT NULL SET @altCode3 = RTRIM (@altCode3);
            IF @altCode4 IS NOT NULL SET @altCode4 = RTRIM (@altCode4);
            IF @altCode5 IS NOT NULL SET @altCode5 = RTRIM (@altCode5);
            IF @altCode6 IS NOT NULL SET @altCode6 = RTRIM (@altCode6);
            IF @altCode7 IS NOT NULL SET @altCode7 = RTRIM (@altCode7);
            IF @altCode8 IS NOT NULL SET @altCode8 = RTRIM (@altCode8);

            -- Set lock and prevent SPs within transaction from running simultaneously
            EXEC @lockIndicator = sys.sp_getapplock @Resource = 'Trend_Reporting_spUpdateAllHotfix'
                                                   ,@LockMode = 'Exclusive'
                                                   ,@LockOwner = 'Transaction'
                                                   ,@LockTimeout = 900000   -- 900,000 milliseconds = 15 minutes
                                                   ,@DbPrincipal = 'public';
            IF @lockIndicator IN (0)
                BEGIN
                    WAITFOR DELAY '00:00:02';

                    -- SP1
                    IF @altCode1 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode1); -- Alternate code from report text box; Replaces SP1.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_Reporting_spCalcProjectedMembership @LastUpdateByID;
                        END;

                    -- SP2
                    IF @altCode2 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode2); -- Alternate code from report text box; Replaces SP2.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_HistProcess_spCalcNotPlanLevel_PlanMappingFinal @LastUpdateByID;
                        END;

                    -- SP3
                    IF @altCode3 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode3); -- Alternate code from report text box; Replaces SP3.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_HistProcess_spCalcIsPlanLevel @LastUpdateByID
                                                                           ,@PlanInfoID;
                        END;

                    -- SP4
                    IF @altCode4 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode4); -- Alternate code from report text box; Replaces SP4.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_HistProcess_spCalcPlanTrends_RepCat @LastUpdateByID;
                        END;

                    -- SP5
                    IF @altCode5 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode5); -- Alternate code from report text box; Replaces SP5.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_Reporting_spCalcCountyXWalkMembership @LastUpdateByID;
                        END;

                    -- SP6
                    IF @altCode6 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode6); -- Alternate code from report text box; Replaces SP6.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_Reporting_spCalcImpactProjected @LastUpdateByID
                                                                             ,@PlanInfoID;
                        END;

                    -- SP7
                    IF @altCode7 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode7); -- Alternate code from report text box; Replaces SP7.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_Reporting_spCalcImpactHistoric @LastUpdateByID
                                                                            ,@PlanInfoID;
                        END;

                    -- SP8
                    IF @altCode8 IS NOT NULL
                        BEGIN
                            EXECUTE (@altCode8); -- Alternate code from report text box; Replaces SP8.
                        END;
                    ELSE
                        BEGIN
                            EXECUTE dbo.Trend_Reporting_spCalcCompiledImpacts @LastUpdateByID
                                                                             ,@PlanInfoID;
                        END;
                END;
            ELSE
                BEGIN
                    SET @errorMsg = 'Unable to acquire lock on Trend_Reporting_spUpdateAllHotfix.';
                    RAISERROR (@errorMsg, 16, 1);
                END;

            COMMIT TRANSACTION transactionMain;

        END TRY

        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        DECLARE @LogDate DATETIME = GETDATE ();
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,@lockIndicator
               ,@errorMsg
               ,@LogDate - @startTime
               ,@LastUpdateByID
               ,@LogDate;

        -- Send email if errors exist
        DECLARE @recipients NVARCHAR(MAX);
        DECLARE @BodyOutput NVARCHAR(MAX);
        DECLARE @XML NVARCHAR(MAX);
        DECLARE @Subj VARCHAR(MAX);

        IF (SELECT  COUNT (*)
            FROM    dbo.Trend_Reporting_Log
            WHERE   ErrorMessage IS NOT NULL
                    AND (LockStatus <> 1 OR LockStatus IS NULL) -- LockStatus=1 is expected when the user can't obtain a lock. Don't want to send an email for this. 
                    AND CAST(FLOOR (CAST(LastUpdateDateTime AS FLOAT)) AS DATETIME) = CAST(FLOOR (
                                                                                           CAST(@LogDate AS FLOAT)) AS DATETIME) -- Don't sent emails for previous errors. 
                    AND LastUpdateByID = @LastUpdateByID) > 0
            BEGIN
                SET @XML = CAST((SELECT     (SELECT t.Source AS 'td' FOR XML PATH (''), TYPE)
                                           ,(SELECT ISNULL (CAST(t.LockStatus AS VARCHAR(5)), '') AS 'td'
                                            FOR XML PATH (''), TYPE)
                                           ,(SELECT ISNULL (t.ErrorMessage, '') AS 'td' FOR XML PATH (''), TYPE)
                                           ,(SELECT t.LastUpdateByID AS 'td' FOR XML PATH (''), TYPE)
                                           ,(SELECT CONCAT (w.LastName, ', ', w.FirstName) AS 'td'
                                            FOR XML PATH (''), TYPE)
                                           ,(SELECT t.LastUpdateDateTime AS 'td' FOR XML PATH (''), TYPE)
                                 FROM       dbo.Trend_Reporting_Log t WITH (NOLOCK)
                                 LEFT JOIN  dbo.W_FEM_AccessControl w WITH (NOLOCK)
                                        ON t.LastUpdateByID = w.UserID
                                FOR XML PATH ('tr'), ELEMENTS) AS NVARCHAR(MAX));
                SET @BodyOutput = N'<html><body><H3>
								Errors have been detected in the Trend Reporting process, which was initiated by '
                                  + OBJECT_NAME (@@ProcId)
                                  + N'.<br><br>
                                All errors have been logged in Trend_Reporting_Log.<br><br>
                            </H3>
                            <table border = 1 style=font-size:12px;>
                            <tr>
                                <th>Source</th><th>LockStatus</th><th>ErrorMessage</th><th>LastUpdateByID</th><th>LastUpdateByName</th><th>LastUpdateDateTime</th>
                            </tr>';
                SET @BodyOutput = @BodyOutput + @XML + N'</table></body></html>';

                --Pull email addresses from table
                SELECT  @recipients = COALESCE (@recipients + '; ', '') + [e-mail]
                FROM    (SELECT [Email] AS [e-mail]
                         FROM   dbo.W_FEM_AccessControl WITH (NOLOCK)
                         WHERE  NARTeam = 1
                         UNION
                         SELECT Email AS [e-mail]
                         FROM   dbo.W_FEM_AccessControl WITH (NOLOCK)
                         WHERE  UserID = @LastUpdateByID) a;

                --Send the email
                SET @Subj = (SELECT DB_NAME () + ': Errors in the Trend Reporting process.');
                EXEC msdb.dbo.sp_send_dbmail @recipients = @recipients
                                            ,@body = @BodyOutput
                                            ,@subject = @Subj
                                            ,@body_format = 'HTML'
                                            ,@importance = 'High';
            END;
    END;
GO
