SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================        
-- Author: <PERSON>     
-- Create date: 2023-Oct-25  
-- Description:  Loads PD Benefit String and RX Premium data to dbo.SavedPlanAssumptions. 
--				 Data is sourced from the dbo.SavedPDAuditExhibit.
--				 Reprice required for Rx Premium loads.
--				 
--        
-- PARAMETERS:    
--Input
--@ValidateOnly		-- 0 will run update; 1 will return rows to be updated
--@DivisionList		-- Filtered list of divisions
--@RegionList		-- Filtered list of Regions	 
--@ForecastIDList	-- Filtered list of ForecastID	  
--@LastUpdateByID	
--Output
--@ValidationMessage 
--
--TABLES:     
--Read: SavedPDAuditExhibit
--		 SavedForecastSetup
--		 
--            
-- Write: SavedPlanAssumptions   
--          
-- VIEWS:        
--		vwplaninfo    
--
-- FUNCTIONS:        
--          
-- STORED PROCS: 
--  
-- $HISTORY           
--  
-- ----------------------------------------------------------------------------------------------------------------------          
-- DATE           VERSION   CHANGES MADE                     DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------          
-- 2023-Oct-25    1         Initial version.                 Adam Gilbert  
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [dbo].[spAppSyncPtDSavedPlanAssumptions]  
	@ValidateOnly		bit, 
	@DivisionList		Varchar(MAX) = NULL,
	@RegionList			Varchar(MAX) = NULL,
	@ForecastIDList		VARCHAR(MAX) = NULL ,   
	@LastUpdateByID		VARCHAR(7),  
	@ValidationMessage  VARCHAR(MAX) OUTPUT  
AS  
SET NOCOUNT ON;
BEGIN
	BEGIN TRY

		DECLARE @BidYear int = dbo.fnGetBidYear();

		/*Build Benefit String Suffix
		- 1 exclusion  =  Exclude Tn
		- 2 exclusions =  Exclude T1 and Tn
		- 3+ exclusion =  Exclude T1,T2,...,and Tn
		*/
		Drop table if exists #StringHelper;
		WITH convertString as (
			SELECT ContractPBP, SegmentID,
			case when deductiblet1 = 'Exclude' THEN 1 else 0 end as T1,
			case when deductiblet2 = 'Exclude' then 1 else 0 end as T2,
			case when deductiblet3 = 'Exclude' then 1 else 0 end as T3,
			case when deductiblet4 = 'Exclude' then 1 else 0 end as T4,
			case when deductiblet5 = 'Exclude' then 1 else 0 end as T5,
			case when deductiblet6 = 'Exclude' then 1 else 0 END as T6,

			case when deductiblet1 = 'Exclude' THEN 'T1' else null end as T1S,
			case when deductiblet2 = 'Exclude' then 'T2' else null end as T2S,
			case when deductiblet3 = 'Exclude' then 'T3' else null end as T3S,
			case when deductiblet4 = 'Exclude' then 'T4' else null end as T4S,
			case when deductiblet5 = 'Exclude' then 'T5' else null end as T5S,
			case when deductiblet6 = 'Exclude' then 'T6' else null END as T6S
			FROM dbo.SavedPDAuditExhibit spda

		),
		combineString 
		AS ( 
			SELECT ContractPBP, SegmentID,
			T1+T2+T3+T4+T5+T6 as exclusions,
			CONCAT(T1S,T2S,T3S,T4S,T5S,T6S) baseString,
			concat_ws(', ',T1S,T2S,T3S,T4S,T5S,T6S) baseStringDelimited
			FROM convertString
		)
		SELECT
			ContractPBP, SegmentID,
			CASE 
				WHEN exclusions  = 1 THEN 'Exclude ' + baseString
				WHEN exclusions  = 2 THEN 'Exclude ' + stuff(baseString,3,0,' and ')
				WHEN exclusions >  = 3 THEN 'Exclude ' 
				+ stuff(baseStringDelimited
					,len(baseStringDelimited) - charindex(',', reverse(baseStringDelimited) )+2
					,0
					,' and')
			END AS BenefitStringSuffix
		INTO #StringHelper
		FROM combineString;

		/*****TRANSFORM Data - BUILD UPDATE Dataset****/
		DROP TABLE IF EXISTS #UpdateDataSet;
		SELECT sfs.planyear, 
			   forecastid, 
			   HPASID, 
			   pd.ContractPBP, 
			   pd.segmentid, 
			   BasicPremiumRounded, 
			   SupplementalPremiumRounded,
			   /*build benefit string*/
			   CASE
				   WHEN pd.contractpbp IS NOT NULL
				   THEN CONCAT_WS(' ', BenefitString1MonthStandardRetail
									 ,'Deductible'
									 , CAST(deductible AS INT)
									 , BenefitStringSuffix
									 )
			   END AS BenefitString
		INTO #UpdateDataSet
		FROM dbo.savedforecastsetup sfs
		JOIN dbo.vwplaninfo spi ON sfs.planinfoid = spi.planinfoid
		JOIN dbo.SavedPDAuditExhibit pd ON replace(hpasid, '-', '') = concat(contractpbp, segmentid)
		JOIN #StringHelper sh ON pd.contractpbp = sh.contractpbp
							  AND pd.segmentid = sh.segmentid
		WHERE sfs.planyear = @BidYear
		AND Sfs.isHidden = 0
		AND spi.PlanType = 'MAPD'
		AND spi.planyear =  @BidYear
		AND (Division IN (SELECT value FROM string_split(@DivisionList,',')) 
			 OR @DivisionList IS NULL OR @DivisionList ='')
		AND (Region IN (SELECT value FROM string_split(@RegionList,',')) 
			 OR @RegionList IS NULL OR @RegionList ='')
		AND (ForecastID IN (SELECT value FROM string_split(@ForecastIDList,',')) 
			 OR @ForecastIDList IS null OR @ForecastIDList ='')
		ORDER BY forecastid;

		/*****Find Discrepancies****/
		DROP TABLE IF EXISTS #CheckPlans;
		SELECT ds.PlanYear, ds.ForecastID, HPASID AS CPS, 
		CASE WHEN BasicPremiumRounded = RxBasicPremium THEN 'Y' ELSE 'N' END AS BasicPremiumMatch,
		CASE WHEN SupplementalPremiumRounded = RxSuppPremium THEN 'Y' ELSE 'N' END AS SupplementalPremiumMatch,
		CASE WHEN BenefitString = BidBenefitString THEN 'Y' ELSE 'N' END AS BenefitStringMatch
		INTO #CheckPlans
		from #UpdateDataSet ds
		JOIN dbo.SavedPlanAssumptions spa 
		ON ds.forecastid = spa.forecastid
		AND ds.planyear = spa.planyearid



/*Return Results for Validation Screen*/
IF  @ValidateOnly = 1
BEGIN
	SELECT PlanYear,ForecastID,CPS,BasicPremiumMatch,SupplementalPremiumMatch,BenefitStringMatch 
	FROM #CheckPlans
	WHERE 
	   BasicPremiumMatch        ='N'
	or SupplementalPremiumMatch	='N'
	or BenefitStringMatch 		='N'
END

/*Process Updates*/
IF @ValidateOnly = 0
BEGIN
		BEGIN TRANSACTION
		/*Update Plans*/
		UPDATE SPA
		SET 
		SPA.RxBasicPremium =   UDS.BasicPremiumRounded,
		SPA.RxSuppPremium =    UDS.SupplementalPremiumRounded,
		SPA.BidBenefitString = UDS.BenefitString
		FROM SavedPlanAssumptions SPA
		JOIN #updateDataSet uds
		ON spa.forecastid = uds.forecastid
		AND spa.planyearid = uds.planyear
		WHERE uds.contractPBP IS NOT NULL --ignore plans not in pd audit exhibit
		;

		SET @ValidationMessage = '1:Plans synced successfully ' + trim(str(@@rowcount));

		/*Reprice Updated Plans*/
		UPDATE SFS
		SET isToReprice=1
		FROM SavedForecastSetup SFS
		JOIN #updateDataSet uds
		ON  sfs.forecastid = uds.forecastid
		AND sfs.PlanYear = uds.PlanYear
		JOIN #CheckPlans cp
		ON  cp.forecastid = uds.forecastid
		AND cp.PlanYear = uds.PlanYear
		WHERE uds.contractPBP IS NOT NULL --ignore plans not in pd audit exhibit
		AND (BasicPremiumMatch ='n' OR SuppleMentalPremiumMatch = 'n')

		COMMIT TRANSACTION;

		SELECT @Validationmessage
END


	END TRY
	BEGIN CATCH
	        IF @@TRANCOUNT >   0
            BEGIN
                ROLLBACK TRANSACTION;
			END;

		SET @ValidationMessage ='0: ' + error_message()

	END CATCH
END

GO
