SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnSalesMembershipAdjustmentFactor]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: fnSalesMembershipAdjustmentFactor
--
-- AUTHOR: <PERSON> Wright
--
-- CREATED DATE: 2011-Mar-30
-- HEADER UPDATED: 2011-Mar-30
--
-- DESCRIPTION: Determines factor to scale projected membership to equal sales membership
--		        
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		
--	Output: 
--		@SalesMembershipAdjustment
--
-- TABLES: 
--	Read:
--	LkpExtCMSBidServiceCategory
--	LkpIntTrendType	
--
--	Write:
--	    
-- VIEWS:
--
-- FUNCTIONS:
--	
-- STORED PROCS:
--	
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Mar-30		1			Initial Version														Craig Wright
-- 2011-Mar-31      2           Changed safe division to return 1                                   Michael Siekerka
-- 2011-Aug-25		3			Changed to return a table											Craig Wright
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnSalesMembershipAdjustmentFactor]
(
	@ForecastID INT
)
RETURNS TABLE
AS  

	--@SalesMembershipFactor AS DECIMAL(16,12)
	
RETURN	(
	--IF EXISTS (SELECT 1 FROM SavedPlanAssumptions WHERE ForecastID = @ForecastID AND SalesMembership IS NULL)
	--	SELECT SalesMembershipFactor = 1
	--ELSE
		SELECT a.SalesMembershipFactor FROM
		(SELECT SalesMembershipFactor = 
				dbo.fnGetSafeDivisionResultReturnOne(spa.SalesMembership * 12,
				(
					SELECT SUM(MemberMonths) 
					FROM SavedPlanMemberMonthDetail MM
					INNER JOIN SavedPlanStateCountyDetail SC
						ON SC.ForecastID = MM.ForecastID
						AND SC.StateTerritoryID = MM.StateTerritoryID
						AND SC.CountyCode = MM.CountyCode
					INNER JOIN LkpIntDemogIndicators dem
						ON MM.DemogIndicator = dem.DemogIndicator
					WHERE MM.ForecastID = @ForecastID
						AND dem.DualEligibleTypeID <> 2
						AND SC.IsCountyExcludedFromBPTOutput = 0)
				)
		FROM SavedPlanAssumptions spa
		WHERE spa.ForecastID = @ForecastID 
		)a
		)
GO
