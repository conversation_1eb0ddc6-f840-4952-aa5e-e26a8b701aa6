SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnPadInteger
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE: 2007-Jan-12
--
-- DESCRIPTION: Returns the specified integer as a varchar, padded on the left with zeros to make the total number of digits equal to the specified digit count.  If the number has more than the
--		  desired number of digits, the leftmost digits will be truncated.  If the number is negative, the minus sign is included in addition to the full number of digits.  The maximum digits
--		  returned is 10.
--
-- PARAMETERS:
--	Input:
--		@Number - The integer to be formatted.
--		@DigitCount - The number of digits in the output.
--
-- RETURNS: @Number formatted as a varchar with @DigitCount digits.
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		CHANGES MADE						DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Jan-12		1			Initial Version							Tonya Cockrell
-- 2007-Oct-30		2			Revised code to bring it into coding standards.			Shannon Boykin
--
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE  FUNCTION [dbo].[fnPadInteger]
    (	
    @Number INT,
    @DigitCount TINYINT
    )
RETURNS VARCHAR(11) AS   -- Maximum number of digits in an int is 10, plus an extra for the minus sign.
    BEGIN
        IF @Number IS NULL
            RETURN ''

            DECLARE @Str VARCHAR(11)
            SET @Str =
                RIGHT('0000000000'
                + CONVERT(VARCHAR,ABS(@Number)),@DigitCount)

        IF @Number < 0
            SET @Str = '-' + @Str

        RETURN @Str
    END
GO
