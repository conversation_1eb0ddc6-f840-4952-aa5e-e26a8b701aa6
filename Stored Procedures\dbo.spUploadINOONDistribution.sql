SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- PROCEDURE NAME: spUploadINOONDistribution
--
-- CREATOR: Joe Casey
--
-- CREATED DATE: 2010-Sep-30
-- HEADER UPDATED: 2017-Sept-13
--
-- DESCRIPTION: Stored Procedure responsible for uploading IN and OON distribution info.
--
-- PARAMETERS:
--	Input:
--
--  Output:
--
-- TABLES:
--	Read:
--      SavedPlanINOONDistribution_Stage
--
--	Write:
--		SavedPlanINOONDistributionHeader
--		SavedPlanINOONDistributionDetail
--
-- VIEWS:
--
-- FUNCTIONS:
--		dbo.fnGetBidYear
-- STORED PROCS:
--      
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Sep-30      1           Initial Version                                                     Joe Casey
-- 2010-Oct-07      2           Added IsNull to Validation. Added IsToReprice update.               Joe Casey
-- 2010-Oct-12      3           Added Validation 8                                                  Joe Casey
-- 2010-Nov-10      4           Removed Validation...dern                                           Joe Casey           
-- 2011-Jun-02		5			Replaced LkpIntPlanYear with dbo.fnGetBidYear()						Bobby Jaegers
-- 2011-Jun-14		6			Changed @PlanYearID to return SMALLINT instead of INT				Bobby Jaegers
-- 2014-Mar-05		7			We need to recalculate base benefits after this upload				Mike Deren
-- 2014-Mar-10		8			Added spAppGetBiddableCostAndUse									Sharath Chandra 
-- 2014-Mar-31      9           Remove spAppGetBiddableCostAndUse                                   Manisha Tyagi
-- 2017-Sept-13		10			Removed commented code related to spCalc*PlanExperienceByBenefitCat	Chris Fleming
--									cleaned up logic and updated header								
-- 2018-July-9		11			Edited logic to correctly upload Distribution Description			Craig Nielsen
-- 2019-Jun-28		12			Replace SavedPlanHeader with SavedForecastSetup			            Pooja Dahiya
-- 2019-Oct-30	    13	        Replace @UserID from char(13) to char(7)				            Chhavi Sinha
-- 2023-Jun-23	    14	        Batch upload from staging, Force claims run for updated plan        Adam Gilbert
-- 2023-Aug-14	    15	        Add Validation rule for primary key duplicate error					Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUploadINOONDistribution]

(
	@UserId varchar(7),
	@ImportMessage varchar(MAX) OUT,
	@ClaimsRunMessage varchar(MAX) OUT

)
AS
    SET NOCOUNT ON

    -- Declare Variables
    DECLARE @PlanYearID SMALLINT,
			@LastUpdate DATETIME,
			@UpdatedPlans INT

	-- Set Variables
    SELECT @PlanYearID = dbo.fnGetBidYear()
	SET @LastUpdate = GetDate()


    -- Processing
    BEGIN TRY
    BEGIN TRANSACTION;

		/*Validation*/	
        WITH DuplicateCheck
             AS (SELECT  ForecastID, BenefitCategoryID
					FROM SavedPlanINOONDistribution_Stage
					WHERE LastUpdateByID = @UserId
					GROUP BY ForecastID, BenefitCategoryID
					HAVING count(1) > 1)
             SELECT @ImportMessage = 'Multiple rows found for the following combinations (ForecastID : BenefitCategoryID). ' 
									 + STRING_AGG(Concat('(',ForecastID,':',BenefitCategoryID,')'), ', ') 
									 + '; Review and reimport dataset.'
             FROM DuplicateCheck;
        IF @ImportMessage IS NOT NULL
            BEGIN
                RAISERROR(@ImportMessage, 16, 1);
        END;

		----Remove existing detail table records for imported forecasts
		BEGIN
			DELETE Detail
			FROM	dbo.SavedPlanINOONDistributionDetail Detail	
			JOIN	dbo.SavedPlanINOONDistribution_Stage Stage
			ON  Detail.ForecastID = Stage.ForecastID
			AND Detail.BenefitCategoryID = Stage.BenefitCategoryID
			WHERE Stage.LastUpdateByID = @UserID
		END 

		--Remove existing header table records for imported plans
		BEGIN
				DELETE HEADER
				FROM dbo.SavedPlanINOONDistributionHeader Header
				JOIN dbo.SavedPlanINOONDistribution_Stage Stage
				ON Header.ForecastID = Stage.ForecastID
				WHERE Stage.LastUpdateByID = @UserID
		END
		--Load Header table
		BEGIN  
			INSERT INTO dbo.SavedPlanINOONDistributionHeader
			(
				PlanYearID,
				ForecastID,
				DistrDescription,
				LastUpdateByID,
				LastUpdateDateTime
			)
			SELECT DISTINCT
				@PlanYearID,
				ForecastID,
				DistrDescription,
				LastUpdateByID,
				LastUpdateDateTime
			FROM dbo.SavedPlanINOONDistribution_Stage Stage
			WHERE Stage.LastUpdateByID = @UserID
			AND DistrDescription IS NOT NULL

			SET @UpdatedPlans = @@ROWCOUNT; -- Track plan count. 1 Header row per plan loaded.

		END;

		--Load detail table
		BEGIN

			INSERT INTO SavedPlanINOONDistributionDetail
			(
					PlanYearID,
					ForecastID,
					BenefitCategoryID,
					INDistributionPercent,
					OONDistributionPercent,
					INCostFactor,
					OONCostFactor,
					LastUpdateByID,
					LastUpdateDateTime
			)
			SELECT	@PlanYearID,
					ForecastID,
					BenefitCategoryID,
					INDistributionPercent,
					OONDistributionPercent,
					INCostFactor,
					OONCostFactor,
					LastUpdateByID,
					LastUpdateDateTime

			FROM dbo.SavedPlanINOONDistribution_Stage Stage
			WHERE Stage.LastUpdateByID = @UserID

			SET @ImportMessage = 'Import successful for ( ' +trim(str(@UpdatedPlans)) + ' ) plans' -- set import success message
		END



	/*Reprice Flag Set for uploaded plans*/
    UPDATE	Forecast

    SET		IsToReprice = 1,
            LastUpdateByID = @UserID,
            LastUpdateDateTime = @LastUpdate
	FROM dbo.SavedForecastSetup Forecast

	JOIN dbo.SavedPlanINOONDistribution_Stage Stage
	ON Forecast.ForecastID = Stage.ForecastID
	WHERE Stage.LastUpdateByID = @UserID


	COMMIT;

/******************************
  Claims run for uploaded plans
*******************************/
	/*Remove existing temp table*/
	DROP TABLE IF EXISTS #ClaimsRunList;

	/*Build List of Plans. Stage into #ClaimsRunList*/
	WITH PlanList AS (
		SELECT Distinct DfVersionid, Forecast.ForecastID
		FROM dbo.SavedForecastSetup Forecast
		JOIN dbo.SavedPlanINOONDistribution_Stage Stage
		ON Forecast.ForecastID = Stage.ForecastID
		WHERE Stage.LastUpdateByID = @UserID
	)
	SELECT pl.DFVersionID, String_agg(cast(ForecastID AS varchar(max)),',') ForecastList, concat(DFRunName,': ', format(DFVersionDateTime,'MM/dd/yy' )) as ClaimsVersion
	INTO #ClaimsRunList
	FROM PlanList pl
	LEFT JOIN dbo.SavedDFVersionHeader dfh
	ON pl.DFVersionID = dfh.DFVersionID
	GROUP BY pl.DFVersionID, DFRunName, DFVersionDateTime;

	/*Variables declarations for loop iterations*/
	DECLARE @DFVersionID int = 0
	DECLARE @ClaimsVersion varchar(max)
	DECLARE @ForecastIDList varchar(max)
	DECLARE @SPO varchar(max)

	DROP TABLE IF EXISTS #ClaimRunFullResults;
	/*Hold results of claims runs*/
	CREATE TABLE #ClaimRunFullResults (
	DFVersionID int,
	ForecastList varchar(max),
	Results varchar(max)
	)

	/*Insert records for plans that do not have an existing claims run*/

	INSERT INTO #ClaimRunFullResults
	SELECT DFVersionID, ForecastList, 'Claims Version 1: Not Found;'+'IN/OON Distribution Assumptions successfully loaded. However, existing claims version not found. Manual claims run required for these plan(s):  (' + ForecastList + ')'
	FROM #ClaimsRunList
	WHERE DFVersionID=1;

	/*Iterate through DF Versions and Plan Lists. Ignore DFVersionID 1 - These plans have never had a claims run.*/
	WHILE (1=1 )
	BEGIN
		--Get Next ID
	  SELECT TOP 1 @DFVersionID = DFVersionID, @ForecastIDList = ForecastList, @ClaimsVersion = ClaimsVersion
	  FROM #ClaimsRunList
	  WHERE DFVersionID > @DFVersionID 
	  AND DFVersionID <> 1
	  ORDER BY DFVersionID

	  --Exit Loop if no more ID
		IF @@ROWCOUNT = 0 BREAK;

	  --PRINT @DFVersionID --debug print statement

	  -- Call Claims run sproc
	  EXEC [spAppBatchUpdateDFVersion]  @ForecastIDList, @DFVersionID, @UserID, @MessageFromBackend = @SPO OUTPUT

	  --Record result
	  INSERT Into #ClaimRunFullResults SELECT @DFVersionID, @ForecastIDList, 'Claims Version '+trim(str(@DfVersionID))+': '+ trim(@ClaimsVersion) +';'+  @SPO 


	END;
	--display results table
	-- .Net will convert ; characters into new lines
	SELECT @ClaimsRunMessage = String_agg(cast(Results AS varchar(max)),';;') 
	FROM #ClaimRunFullResults 
/*****
END CLAIMS RUN
*****/


    END TRY

    BEGIN CATCH
        ROLLBACK
		SET @ImportMessage ='Failed'
		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()		 
		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+
		CAST(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
		RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
    END CATCH
-- ------------------------------------------------------------------------------------------------------------------
GO
