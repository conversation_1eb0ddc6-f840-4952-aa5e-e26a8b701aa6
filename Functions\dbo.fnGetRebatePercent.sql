SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetRebatePercent]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: 
--
-- CREATOR: <PERSON> 
--
-- CREATED DATE: Sep-28-2010
-- HEADER UPDATED: Sep-28-2010
--
-- DESCRIPTION: Returns CMS RebatePercent for a specific Contract or the default value if it is a new contract.
--
-- PARAMETERS:
--	Input:
--	Output:
--
-- TABLES:
--	Read:
--        PerExtCMSRebateByContract 
--        PerEctCMSValues
--        Saved<PERSON>lan<PERSON>eader
--        SavedPlanDetail

--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- Sep-28-2010      1           Initial Version.  Made to output correct Rebate Percentage          Jake Gaecke
-- 2011-Feb-07      2           Added more precision to the returned value                          Michael Siekerka
-- 2011-Mar-17		3			Returns NULL if plan is unavailable form ratebook					Jiao Chen
-- 2023-Aug-03		4			@Xvariables, WITH (NOLOCK), Table Schema							Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetRebatePercent]
    (
    @ForecastID INT = NULL
    ) 
RETURNS DECIMAL (25,24)

AS
BEGIN

DECLARE @RebatePercent DECIMAL (25,24),
@XForecastID INT = @ForecastID;
           
    IF EXISTS ( SELECT 1
                FROM dbo.SavedPlanHeader SPH WITH(NOLOCK)
                INNER JOIN dbo.SavedPlanDetail SPD WITH(NOLOCK)
                    ON SPH.ForecastID = SPD.ForecastID
                INNER JOIN dbo.PerExtCMSRebateByContract RB WITH(NOLOCK)
                    ON RB.ContractNumber = SPH.ContractNumber
                WHERE SPH.ForecastID = @XForecastID)
        BEGIN
            SELECT @RebatePercent = RB.RebatePercent
            FROM dbo.SavedPlanHeader SPH WITH(NOLOCK)
            INNER JOIN dbo.SavedPlanDetail SPD WITH(NOLOCK)
                ON SPH.ForecastID = SPD.ForecastID
            INNER JOIN dbo.PerExtCMSRebateByContract RB WITH(NOLOCK)
                ON RB.ContractNumber = SPH.ContractNumber
            WHERE SPH.ForecastID = @XForecastID
        END
    RETURN @RebatePercent
END
GO
