SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-------------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnGetGeneralCharacteristicsExtract  
--  
-- AUTHOR: Pragya Mishra  
--  
-- CREATED DATE: 2016-Dec-07  
--  
-- DESCRIPTION: Designed to extract fields for General Characteristics - will match the upload  
--  
-- PARAMETERS:  
--  Input:  
--      @WhereIN  
--        
--  Output:  
--  
-- TABLES:  
--  Read:   
--  SavedPlanHeader  
--  Write:  
--  
-- VIEWS:  
--        
--  
-- FUNCTIONS:  
--  
-- STORED PROCS:  
--  
-- HISTORY:  
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2016-Dec-07      1           Initial Version                                                     Pragya Mishra 
-- 2017-Sep-05      2           Added Skip Induced Utilization										Ramkumar
-- 2018-Aug-16      3           Added CertifyingActuaryUserID column to SavedPlanHeader             Apoorva Nasa
-- 2019-May-29		4			Added column PlanName to GeneralCharacteriticsExport				Calice Robins 
-- 2019-Oct-25      5           Alter UserID size                                                   Manisha Tyagi
-- 2024-JUNE-14     6           Change Columns with data type as BIT to INT                         Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------  
CREATE FUNCTION [dbo].[fnGetGeneralCharacteristicsExtract]  
    (  
    @WhereIN Varchar(MAX) = NULL  
    )  
RETURNS @Results TABLE  
(    
    ForecastID [int] NOT NULL, 
    ContactID [CHAR](7) NOT NULL, 
    SecondaryContactID [CHAR](7) NULL,
    CertifyingActuaryUserID [CHAR](7) NULL,
    IsHidden [INT] NOT NULL,
    IsLiveIndex [INT] NULL,
    IsSCTPlan [INT] NOT NULL,
    IsFiledPlan [INT] NOT NULL,
    IsLocked [INT] NOT NULL ,
	IsSkipInducedUtilization [INT] NOT NULL,
	PlanName [VARCHAR](100) NOT NULL
	
	
) AS  

BEGIN  

    IF @WhereIn IS NULL  
        INSERT @Results  
            SELECT  ForecastID,  
                    ContactID,
                    SecondaryContactID,
                    CertifyingActuaryUserID,
                    IsHidden,
                    IsLiveIndex,
                    IsSCTPlan,
                    IsFiledPlan,
                    IsLocked,
					IsSkipInducedUtilizationMapping,  
					PlanName
            FROM SavedPlanHeader sph  
            WHERE sph.IsHidden = 0  


    ELSE  
        INSERT @Results  
            SELECT  ForecastID,  
					ContactID,
                    SecondaryContactID,
                    CertifyingActuaryUserID,
                    IsHidden,
                    IsLiveIndex,
                    IsSCTPlan,
                    IsFiledPlan,
                    IsLocked,
					IsSkipInducedUtilizationMapping,
					PlanName  
            FROM SavedPlanHeader sph               
            WHERE ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))  
            AND sph.IsHidden = 0  

    RETURN  
END
GO
