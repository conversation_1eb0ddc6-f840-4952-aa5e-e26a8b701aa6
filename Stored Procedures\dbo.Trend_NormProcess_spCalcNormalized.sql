SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_NormProcess_spCalcNormalized
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: FEB-03-2020
--
-- DESCRIPTION: This procedure normalizes the trends in the historic cost and use data 
--              and includes only plans flagged as IsIncludeInTrend=1
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_NormProcess_CalcCombined
--					Trend_CalcHistoricCostAndUse
--					Trend_CalcHistoricMembership
--					LkpIntBenefitCategory
--					LkpProjectionVersion
--					LkpIntPlanYear
--					Trend_NormProcess_IsIncludeInTrendSnapshot
--					
--          Write:  Trend_NormProcess_CalcNormalized                 
--
-- VIEWS: Read: vwPlanInfo
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FEB-03-2020      1           Initial Version                                                     Jake Lewis
-- 2020-09-18		2			Change Truncate statement to Delete	so sp can be exec in QA/PROD	Craig Nielsen
-- 2020-09-22		3			Fixed Sonar Qube Fixes												Deepali
-- 2021-05-25		4			Referece IsIncludeInTrendSnapshot for normalized audit fix			Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_NormProcess_spCalcNormalized]
@LastUpdateByID CHAR(13)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        -- Declare variables
        DECLARE @CurrentYear INT = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);
        DECLARE @LastCurrentYearQuarter INT = (SELECT   LastCurrentYearQuarter
                                               FROM     dbo.LkpProjectionVersion
                                               WHERE    IsLiveProjection = 1);
        DECLARE @MinPlanYearID INT = (SELECT    MIN (PlanYearID) FROM  dbo.Trend_CalcHistoricCostAndUse);
        DECLARE @NormalizedComponent VARCHAR(20) = (SELECT  Component FROM  dbo.Trend_SavedComponentInfo WHERE  ComponentID = 11);
        DECLARE @NationwideRegion VARCHAR(20) = 'Nationwide';

        -- Create list of plan years. 
        IF (SELECT  OBJECT_ID ('tempdb..#PlanYearID')) IS NOT NULL
            DROP TABLE #PlanYearID;
        SELECT  DISTINCT
                number AS PlanYearID
        INTO    #PlanYearID
        FROM    master..spt_values
        WHERE   number BETWEEN @MinPlanYearID + 1 AND @CurrentYear;

        -- Create list of quarters.
        IF (SELECT  OBJECT_ID ('tempdb..#QuarterID')) IS NOT NULL
            DROP TABLE #QuarterID;
        SELECT  DISTINCT
                number AS QuarterID
        INTO    #QuarterID
        FROM    master..spt_values
        WHERE   number BETWEEN 1 AND 4;

        -- Create list of regions, including Nationwide.
        IF (SELECT  OBJECT_ID ('tempdb..#ActuarialRegionWithNW')) IS NOT NULL
            DROP TABLE #ActuarialRegionWithNW;
        SELECT  DISTINCT
                Region AS ActuarialRegion
        INTO    #ActuarialRegionWithNW
        FROM    dbo.vwPlanInfo
        WHERE   IsHidden = 0
                AND IsOffMAModel = 'No'
                AND Region NOT IN ('Unmapped')
        UNION
        SELECT  @NationwideRegion AS ActuarialRegion;

        -- Create list of reporting categories, excluding Part B Rx Pharmacy
        IF (SELECT  OBJECT_ID ('tempdb..#ReportingCategory')) IS NOT NULL
            DROP TABLE #ReportingCategory;
        SELECT  DISTINCT
                ReportingCategory
        INTO    #ReportingCategory
        FROM    dbo.LkpIntBenefitCategory
        WHERE   ReportingCategory IS NOT NULL
                AND ReportingCategory NOT LIKE '%Pharmacy%';

        -- Load a copy of Trend_CalcHistoricCostAndUse into a temp table
        -- Append Nationwide region
        -- Used for current year data when calculating trend metrics for numerator
        IF (SELECT  OBJECT_ID ('tempdb..#TempCopyCostAndUse')) IS NOT NULL
            DROP TABLE #TempCopyCostAndUse;
        SELECT      b.Region AS ActuarialRegion
                   ,a.ReportingCategory
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,SUM (ISNULL (a.Allowed, 0)) AS Allowed
                   ,SUM (ISNULL (a.Utilization, 0)) AS Utilization
        INTO    #TempCopyCostAndUse
        FROM        dbo.Trend_CalcHistoricCostAndUse a
        LEFT JOIN   dbo.vwPlanInfo b
               ON b.PlanInfoID = a.PlanInfoID
        LEFT JOIN   dbo.Trend_NormProcess_IsIncludeInTrendSnapshot t
               ON t.PlanInfoID = a.PlanInfoID
        WHERE       b.Region NOT IN ('Unmapped')
                    AND a.ReportingCategory IS NOT NULL
                    AND t.IsIncludeInTrend = 1
        GROUP BY    b.Region
                   ,a.ReportingCategory
                   ,a.PlanYearID
                   ,a.QuarterID
        UNION
        SELECT      @NationwideRegion AS ActuarialRegion
                   ,c.ReportingCategory
                   ,c.PlanYearID
                   ,c.QuarterID
                   ,SUM (ISNULL (c.Allowed, 0)) AS Allowed
                   ,SUM (ISNULL (c.Utilization, 0)) AS Utilization
        FROM        dbo.Trend_CalcHistoricCostAndUse c
        LEFT JOIN   dbo.vwPlanInfo d
               ON d.PlanInfoID = c.PlanInfoID
        LEFT JOIN   dbo.Trend_NormProcess_IsIncludeInTrendSnapshot t
               ON t.PlanInfoID = c.PlanInfoID
        WHERE       d.Region NOT IN ('Unmapped')
                    AND c.ReportingCategory IS NOT NULL
                    AND t.IsIncludeInTrend = 1
        GROUP BY    c.ReportingCategory
                   ,c.PlanYearID
                   ,c.QuarterID;

        -- Load a copy of Trend_CalcHistoricMembership into a temp table
        -- Append Nationwide region
        -- Used for current year data when calculating trend metrics for numerator
        IF (SELECT  OBJECT_ID ('tempdb..#TempCopyMembership')) IS NOT NULL
            DROP TABLE #TempCopyMembership;
        SELECT      b.Region AS ActuarialRegion
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,SUM (ISNULL (a.MemberMonths, 0)) AS MemberMonths
        INTO    #TempCopyMembership
        FROM        dbo.Trend_CalcHistoricMembership a
        LEFT JOIN   dbo.vwPlanInfo b
               ON b.PlanInfoID = a.PlanInfoID
        LEFT JOIN   dbo.Trend_NormProcess_IsIncludeInTrendSnapshot t
               ON t.PlanInfoID = a.PlanInfoID
        WHERE       b.Region NOT IN ('Unmapped')
                    AND t.IsIncludeInTrend = 1
        GROUP BY    b.Region
                   ,a.PlanYearID
                   ,a.QuarterID
        UNION
        SELECT      @NationwideRegion AS ActuarialRegion
                   ,c.PlanYearID
                   ,c.QuarterID
                   ,SUM (ISNULL (c.MemberMonths, 0)) AS MemberMonths
        FROM        dbo.Trend_CalcHistoricMembership c
        LEFT JOIN   dbo.vwPlanInfo d
               ON d.PlanInfoID = c.PlanInfoID
        LEFT JOIN   dbo.Trend_NormProcess_IsIncludeInTrendSnapshot t
               ON t.PlanInfoID = c.PlanInfoID
        WHERE       d.Region NOT IN ('Unmapped')
                    AND t.IsIncludeInTrend = 1
        GROUP BY    c.PlanYearID
                   ,c.QuarterID;

        -- Determine the denominator for normalized trend
        IF (SELECT  OBJECT_ID ('tempdb..#NormTrendDenominator')) IS NOT NULL
            DROP TABLE #NormTrendDenominator;
        SELECT      PlanYearID
                   ,QuarterID
                   ,ActuarialRegion
                   ,ReportingCategory
                   ,EXP (SUM (LOG (1 + ISNULL (CostAdjustment, 0)))) AS CostTrendForComponents
                   ,EXP (SUM (LOG (1 + ISNULL (UseAdjustment, 0)))) AS UseTrendForComponents
        INTO        #NormTrendDenominator
        FROM        dbo.Trend_NormProcess_CalcCombined
        GROUP BY    PlanYearID
                   ,QuarterID
                   ,ActuarialRegion
                   ,ReportingCategory;

        -- Determine metrics to be used in the numerator for normalized trend
        -- Numerator is the ratio of current year metric to prior year metric
        IF (SELECT  OBJECT_ID ('tempdb..#NormTrendNumerator')) IS NOT NULL
            DROP TABLE #NormTrendNumerator;
        SELECT      DISTINCT
                    a.PlanYearID
                   ,a.QuarterID
                   ,a.ActuarialRegion
                   ,a.ReportingCategory
                   ,dbo.Trend_fnSafeDivide (b.Allowed, b.Utilization, 1) AS CostMetric
                   ,dbo.Trend_fnSafeDivide (d.Allowed, d.Utilization, 1) AS CostMetricPY
                   ,dbo.Trend_fnSafeDivide (b.Utilization * 12000, c.MemberMonths, 1) AS UseMetric
                   ,dbo.Trend_fnSafeDivide (d.Utilization * 12000, e.MemberMonths, 1) AS UseMetricPY
        INTO        #NormTrendNumerator
        FROM        dbo.Trend_NormProcess_CalcCombined a
        LEFT JOIN   #TempCopyCostAndUse b
               ON b.PlanYearID = a.PlanYearID
                  AND   b.QuarterID = a.QuarterID
                  AND   b.ActuarialRegion = a.ActuarialRegion
                  AND   b.ReportingCategory = a.ReportingCategory
        LEFT JOIN   #TempCopyMembership c
               ON c.PlanYearID = a.PlanYearID
                  AND   c.QuarterID = a.QuarterID
                  AND   c.ActuarialRegion = a.ActuarialRegion
        LEFT JOIN   #TempCopyCostAndUse d
               ON (d.PlanYearID + 1) = a.PlanYearID
                  AND   d.QuarterID = a.QuarterID
                  AND   d.ActuarialRegion = a.ActuarialRegion
                  AND   d.ReportingCategory = a.ReportingCategory
        LEFT JOIN   #TempCopyMembership e
               ON (e.PlanYearID + 1) = a.PlanYearID
                  AND   e.QuarterID = a.QuarterID
                  AND   e.ActuarialRegion = a.ActuarialRegion;

        --Truncate table and insert trends into the final output table
        DELETE  FROM dbo.Trend_NormProcess_CalcNormalized WHERE 1 = 1;

        INSERT INTO dbo.Trend_NormProcess_CalcNormalized
            (Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,PlanYearID
            ,QuarterID
            ,ActuarialRegion
            ,ReportingCategory
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT      @NormalizedComponent
                   ,NULL
                   ,NULL
                   ,a.PlanYearID
                   ,b.QuarterID
                   ,c.ActuarialRegion
                   ,d.ReportingCategory
                   ,dbo.Trend_fnSafeDivide (
                    dbo.Trend_fnSafeDivide (e.CostMetric, e.CostMetricPY, 1), f.CostTrendForComponents, 1) - 1
                   ,dbo.Trend_fnSafeDivide (
                    dbo.Trend_fnSafeDivide (e.UseMetric, e.UseMetricPY, 1), f.UseTrendForComponents, 1) - 1
                   ,@LastUpdateByID
                   ,GETDATE ()
        FROM        #PlanYearID a
       INNER JOIN   #QuarterID b
               ON 1 = 1
       INNER JOIN   #ActuarialRegionWithNW c
               ON 1 = 1
       INNER JOIN   #ReportingCategory d
               ON 1 = 1
        LEFT JOIN   #NormTrendNumerator e
               ON e.PlanYearID = a.PlanYearID
                  AND   e.QuarterID = b.QuarterID
                  AND   e.ActuarialRegion = c.ActuarialRegion
                  AND   e.ReportingCategory = d.ReportingCategory
        LEFT JOIN   #NormTrendDenominator f
               ON f.PlanYearID = a.PlanYearID
                  AND   f.QuarterID = b.QuarterID
                  AND   f.ActuarialRegion = c.ActuarialRegion
                  AND   f.ReportingCategory = d.ReportingCategory
        WHERE       a.PlanYearID < @CurrentYear
                    OR  (a.PlanYearID = @CurrentYear
                         AND b.QuarterID <= @LastCurrentYearQuarter);

    END;
GO

