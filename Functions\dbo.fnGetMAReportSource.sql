SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportSource
--
-- AUTHOR: <PERSON><PERSON><PERSON>
--
-- CREATED DATE: 2009-Mar-20
-- HEADER UPDATED: 2010-Nov-05
--
-- DESCRIPTION:   Get list of available Report Types (SourceID) for MAMBA MAReport feature
--              Test: select * from fnGetMAReportSource(2010)
-- PARAMETERS:
--	Input:
--  Output:
--
-- TABLES: 
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-20		1		    Initial Version										                Aleksey Titievsky
-- 2010-Nov-05      2           Removed @PlanYearID as a parameter                                  Joe Casey
-- 2010-Nov-09      3           Added 'All Divisions'                                               Joe Casey
-- 2016-Nov-17		4			MA Reports Cleanup, refer to spgetMAReport for changes				Chris Fleming
--2022-July-25		5			Updated Data for forecastid											Deepali						
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportSource]()
RETURNS TABLE
RETURN
	SELECT
	    SourceID,
	    SourceName,
	    SourceOptionName,
	    GeoFilterType =
            CASE 
                WHEN SourceID = 3 THEN
                    1
                WHEN SourceID IN (4, 5) THEN
                    2
                ELSE
                    0
            END,
        GeoFilterTypeName =
            CASE 
                WHEN SourceID = 3 THEN
                    'Divisional'
                WHEN SourceID IN (4, 5) THEN
                    'Regional'
                ELSE
                    ''
            END,
        IsGeoFilterVisible =
            CASE 
                WHEN SourceID IN (4, 5) THEN
                    1
                ELSE
                    0
            END,
        IsPlanTypeVisible = 0,
        IsPlanForecastIDVisible = 0,
        IsPlanPlanVersionVisible = 0
    FROM
    (
-- For Product Development only, not needed for MAMBA
-- Leaving in here in case this needs to be selectable in the future
--	    SELECT
--          1 SourceID, 
--          'Product Development' SourceName, 
--          'all biddable plans' SourceOptionName
--		UNION
		SELECT
			2 SourceID, 
			'Milliman' SourceName, 
			'all biddable plans' SourceOptionName
		UNION
		SELECT
			3 SourceID,
			'All Divisions' SourceName,
			'biddable, active plans' SourceOptionName
		UNION        
		SELECT
			4 SourceID,
			'Regional' SourceName, 
			'biddable, active plans' SourceOptionName
		UNION        
		SELECT
			5 SourceID,
			'Regional' SourceName, 
            'all active plan forecastIDs' SourceOptionName
	) S
GO