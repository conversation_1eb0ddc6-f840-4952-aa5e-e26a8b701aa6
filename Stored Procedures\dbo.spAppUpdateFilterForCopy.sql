SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author: Deepali Mittal   
-- Create date: 06-06-2018
-- Description:  Create/Edit plan and Scenario Info
--      
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Jun-06  1		  Initial version.											Deepali Mittal 
-- 2018-Dec-19    3       Updated Error message                                     Kritika Singh
--                        and included logging exception	    
-- ----------------------------------------------------------------------------------------------------------------------                
CREATE PROCEDURE [dbo].[spAppUpdateFilterForCopy]
    @NewForecastID Varchar(20),
    @OldForecastID Varchar(20) ,    
     @LastUpdateByID CHAR(7),
	 @Result BIT OUT
     
	 
AS

 
    BEGIN  
     
        BEGIN TRANSACTION;
       
        BEGIN TRY        
            IF EXISTS ( SELECT TOP 1
                                *
                        FROM    dbo.AppSavedFilterUserPreference
                        WHERE   [UserID]=@LastUpdateByID)
                BEGIN
				declare @SelectedForecastIdList varchar(max)=''
				declare @NewSelectedForecastIdList varchar(max)=''
				select @SelectedForecastIdList= SelectedForecastIdList 
				from dbo.AppSavedFilterUserPreference
                WHERE   [UserID]=@LastUpdateByID

						if CHARINDEX(@OldForecastID,@SelectedForecastIdList) > 0
				    SELECT @NewSelectedForecastIdList=  
				    REPLACE(@SelectedForecastIdList,@OldForecastID,@OldForecastID+' ,'+@NewForecastID)
                    UPDATE  [dbo].AppSavedFilterUserPreference
                    SET     SelectedForecastIdList =@NewSelectedForecastIdList
					WHERE   [UserID]=@LastUpdateByID
                END;
       
            SET @Result = 1; 
            COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
            SET @Result = 0;
			
             DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
			ROLLBACK TRANSACTION; 

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
        END CATCH;  
    END;
GO
