SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO



-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportVBIDUploadStagedData
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-Sep-13      1			Initial Version						                                Archana Sahu
-- 2024-Mar-04		2			Remove delete statement due to duplication of code					Archana Sahu
-- ----------------------------------------------------------------------------------------------------------------------


CREATE  PROCEDURE [dbo].[spAppImportVBIDUploadStagedData]
(@StageId VARCHAR(100))
AS
BEGIN
    --DECLARE @StageId VARCHAR(100)='ec5b9a90-07bc-4e48-aac9-df189dd20949'
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

    DECLARE @tbl__importData TABLE
    (
        ForecastID INT,
        VBID CHAR(2),
		ISDELETE CHAR(1),
        LastUpdateByID CHAR(7),
        LastUpdateDateTime DATETIME
    );

    INSERT INTO @tbl__importData
    SELECT ForecastID,
			VBID,
			ISNULL(ISDELETE,'N'),
           @UserId,
           GETDATE()
    FROM
        OPENJSON(@jsonData, '$.VBID')
        WITH
        (
            ForecastID INT,
            VBID CHAR(2),
            ISDELETE CHAR(1) '$.Delete'
        );

         DELETE ssv FROM dbo.SavedScenarioVBID ssv JOIN @tbl__importData vbid
		ON ssv.ForecastID = vbid.ForecastID;

		INSERT INTO SavedScenarioVBID
        (
            ForecastID,
			VBIDID,
            LastUpdateByID,
            LastUpdateDateTime
        )
		SELECT vbid.ForecastID,lkp.VBIDID, vbid.LastUpdateByID, vbid.LastUpdateDateTime from @tbl__importData vbid JOIN lkpVBID lkp
		ON replace(lkp.vbid,'VBID-','') = vbid.VBID WHERE vbid.ISDELETE !='Y' ;

	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
GO
