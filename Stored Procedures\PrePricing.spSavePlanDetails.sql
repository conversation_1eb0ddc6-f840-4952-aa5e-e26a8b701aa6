SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSavePlanDetails]   
--    
-- AUTHOR: Sur<PERSON>rthy 
--    
-- CREATED DATE: 2024-Oct-29   
-- Type: 
-- DESCRIPTION: Procedure responsible for saving plan details 
--    
-- PARAMETERS:    
-- Input: 
-- @PlanInfoID
-- @MarketID
-- @PlanStatus
-- @PlanCps
-- @PlanDescription
-- @PlanGroups
-- @LastUpdateUserId
-- @OutPutResult

-- TABLES:   
-- 

-- Read:    
--  

-- Write:    
--  PrePricing.PlanGroupMapping
--  PrePricing.PlanInfo 

-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-29		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSavePlanDetails]
(
	@PlanInfoID INT,
	@MarketID INT,
	@PlanStatus BIT,
	@PlanCps VARCHAR(13),
	@PlanDescription VARCHAR(1000),
	@PlanGroups VARCHAR(200),
	@LastUpdateUserId VARCHAR(100)
)
AS    

BEGIN    
	SET NOCOUNT ON
	BEGIN TRY
	BEGIN TRANSACTION plansave	 
	DECLARE	@OutPutResultCode VARCHAR(20)
	DECLARE @OutPutResult VARCHAR(200)
	SET @OutPutResultCode='Success'
	SET @OutPutResult='Plan Saved Successfully.'
	IF EXISTS (SELECT 1 FROM PrePricing.PlanInfo WITH(NOLOCK) WHERE PlanInfoID=@PlanInfoID)
	BEGIN
		--First updaed plan details
		UPDATE  PrePricing.PlanInfo SET
		CPS=@PlanCps,
		MarketID=@MarketID,
		IsEnabled=@PlanStatus,
		PlanDescription=@PlanDescription,
		LastUpdateByID=@LastUpdateUserId,
		LastUpdateDateTime=GETDATE()
		WHERE PlanInfoID=@PlanInfoID;

		--next update group mapping
		DROP TABLE IF EXISTS #PlanGroupIDs
		SELECT VALUE AS PlanGroupID INTO #PlanGroupIDs FROM STRING_SPLIT(@PlanGroups,',')
		DELETE FROM #PlanGroupIDs WHERE PlanGroupID NOT IN (SELECT PlanGroupID FROM prepricing.PlanGroup) --remove invalid selections

		DELETE FROM PrePricing.PlanGroupMapping WHERE PlanInfoID=@PlanInfoID;

		INSERT INTO PrePricing.PlanGroupMapping(PlanInfoID,LastUpdateByID,LastUpdateDateTime,PlanGroupID)
	    SELECT 		
		@PlanInfoId
		,@LastUpdateUserID
		,GETDATE(),
		PlanGroupID
		FROM #PlanGroupIDs

	END
	ELSE
	BEGIN
		SET @OutPutResultCode='Error'
	    SET @OutPutResult='Error Plan not found.'		
	END
	SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT TRANSACTION plansave;	
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION plansave;
		SET @OutPutResultCode='Error'
		SET @OutPutResult='Error while saving the plan.'	
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH
END
GO
