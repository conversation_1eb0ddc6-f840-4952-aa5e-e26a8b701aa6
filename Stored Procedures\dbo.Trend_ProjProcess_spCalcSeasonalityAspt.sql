SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcSeasonalityAspt
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: MAR-13-2020
--
-- DESCRIPTION:   This stored procedure calculates monthly seasonality factors for allowed and utilization per 1000 based on the base year experience
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--              
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntPlanYear
--                  Trend_CalcHistoricCostAndUse
--                  Trend_CalcHistoricMembership
--					
--          Write:  Trend_ProjProcess_CalcSeasonalityAspt_Use
--					Trend_ProjProcess_CalcSeasonalityAspt_Allowed
--                  
--
-- VIEWS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MAR-18-2020      1           Initial Version                                                     Andy Blink
-- NOV-09-2020		2			Added allowed seasonality calculation								Jake Lewis
-- DEC-09-2020		3			Stop writing to Trend_ProjProcess_CalcSeasonalityAspt_Cost			Jake Lewis
-- DEC-10-2020		4			For Q12021, comment out the entire SP.								Jake Lewis
--								We will temporarily need to manually set seasonality
--								assumptions, and therefore we don't want this SP to overwrite
--								the seasonality tables.  The code will be uncommented at a 
--								later date.  
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcSeasonalityAspt]

@LastUpdateByID CHAR(7)

AS

SET NOCOUNT ON;

--DECLARE @BaseYear INT;
--DECLARE @LastUpdateDateTime DATETIME;

----Define the base year
--SET @BaseYear = (SELECT DISTINCT
--                        PlanYearID
--                 FROM   dbo.LkpIntPlanYear
--                 WHERE  IsExperienceYear = 1);

----Timestamp
--SET @LastUpdateDateTime = GETDATE ();

----Bring in the historic cost and use data
--IF (SELECT  OBJECT_ID ('tempdb..#Trend_CalcHistoricCostAndUse')) IS NOT NULL
--    DROP TABLE #Trend_CalcHistoricCostAndUse;
--SELECT      PlanYearID
--           ,MonthID
--           ,SUM (COALESCE (Allowed, 0)) AS Allowed
--           ,SUM (COALESCE (Utilization, 0)) AS Utilization
--INTO        #Trend_CalcHistoricCostAndUse
--FROM        dbo.Trend_CalcHistoricCostAndUse
--WHERE       PlanYearID = @BaseYear
--GROUP BY    PlanYearID
--           ,MonthID;

----Bring in the historic membership data
--IF (SELECT  OBJECT_ID ('tempdb..#Trend_CalcHistoricMembership')) IS NOT NULL
--    DROP TABLE #Trend_CalcHistoricMembership;
--SELECT      PlanYearID
--           ,MonthID
--           ,SUM (COALESCE (MemberMonths, 0)) AS MemberMonths
--INTO        #Trend_CalcHistoricMembership
--FROM        dbo.Trend_CalcHistoricMembership
--WHERE       PlanYearID = @BaseYear
--GROUP BY    PlanYearID
--           ,MonthID;

----Combine membership with claims, and calculate allowed PMPM, unit cost and utilization per 1000 by quarter
--IF (SELECT  OBJECT_ID ('tempdb..#Trend_CalcHistoricCombined')) IS NOT NULL
--    DROP TABLE #Trend_CalcHistoricCombined;
--SELECT      mem.PlanYearID
--           ,mem.MonthID
--           ,dbo.Trend_fnSafeDivide (cu.Allowed, mem.MemberMonths, 0) AS AllowedPMPM
--           ,dbo.Trend_fnSafeDivide (cu.Allowed, cu.Utilization, 0) AS UnitCost
--           ,dbo.Trend_fnSafeDivide (cu.Utilization, mem.MemberMonths, 0) * 12000 AS UtilPer1000
--INTO        #Trend_CalcHistoricCombined
--FROM        #Trend_CalcHistoricMembership mem
--LEFT JOIN   #Trend_CalcHistoricCostAndUse cu
--       ON cu.PlanYearID = mem.PlanYearID
--          AND   cu.MonthID = mem.MonthID;

----Combine membership with claims, and calculate allowed PMPM, unit cost and utilization per 1000 for the full year (needed to calculate the seasonality factors)
--IF (SELECT  OBJECT_ID ('tempdb..#Trend_CalcHistoricCombinedAvg')) IS NOT NULL
--    DROP TABLE #Trend_CalcHistoricCombinedAvg;
--SELECT      PlanYearID
--           ,AVG (AllowedPMPM) AS AllowedPMPM
--           ,AVG (UnitCost) AS UnitCost
--           ,AVG (UtilPer1000) AS UtilPer1000
--INTO        #Trend_CalcHistoricCombinedAvg
--FROM        #Trend_CalcHistoricCombined
--GROUP BY    PlanYearID;

----Delete and insert into the allowed seasonality table
--DELETE  FROM dbo.Trend_ProjProcess_CalcSeasonalityAspt_Allowed WHERE    1 = 1;
--INSERT INTO dbo.Trend_ProjProcess_CalcSeasonalityAspt_Allowed
--    (MonthID
--    ,SeasonalityFactor
--    ,LastUpdateByID
--    ,LastUpdateDateTime)
--SELECT      a.MonthID
--           ,dbo.Trend_fnSafeDivide (a.AllowedPMPM, b.AllowedPMPM, 1) AS SeasonalityFactor
--           ,@LastUpdateByID
--           ,@LastUpdateDateTime
--FROM        #Trend_CalcHistoricCombined a
--LEFT JOIN   #Trend_CalcHistoricCombinedAvg b
--       ON b.PlanYearID = a.PlanYearID;

----Delete and insert into the use seasonality table
--DELETE  FROM dbo.Trend_ProjProcess_CalcSeasonalityAspt_Use WHERE    1 = 1;
--INSERT INTO dbo.Trend_ProjProcess_CalcSeasonalityAspt_Use
--SELECT      a.MonthID
--           ,dbo.Trend_fnSafeDivide (a.UtilPer1000, b.UtilPer1000, 1) AS SeasonalityFactor
--           ,@LastUpdateByID
--           ,@LastUpdateDateTime
--FROM        #Trend_CalcHistoricCombined a
--LEFT JOIN   #Trend_CalcHistoricCombinedAvg b
--       ON b.PlanYearID = a.PlanYearID;
GO
