SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------      
-- PROCEDURE NAME: [PrePricing].[spAppGetBenefitsInterfaceProjectedBenefits]      
--      
-- TYPE: Stored Procedure      
--      
-- AUTHOR: Sur<PERSON>      
--      
-- CREATED DATE: 2025-JAN-13      
--      
-- DESCRIPTION: Procedure responsible for extracting benefits projections from prericeing schema    
--    by ForecastID.      
--      
-- PARAMETERS:      
-- Input:      
--     @ForecastID      
-- TABLES:       
-- Read:      
--          
--         
-- Write:      
--      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:      
--        
-- $HISTORY       
-- ----------------------------------------------------------------------------------------------------------------------      
-- DATE			 VERSION  CHANGES MADE																	DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------      
-- 2025-Jan-14		1    Initial Version																Surya Murthy  
-- 2026-Feb-28		2    Sorting order implemented														Surya Murthy  
-- 2025-Mar-05		3	 CPS Logic Added																Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [PrePricing].[spAppGetBenefitsInterfaceProjectedBenefits]      
	@WhereIn VARCHAR(MAX) = NULL
AS   
SET NOCOUNT ON;
BEGIN   
	DECLARE @ErrorMessage NVARCHAR(4000);                    
	DECLARE @ErrorSeverity INT;                    
	DECLARE @ErrorState INT;                    
	DECLARE @ErrorException NVARCHAR(4000);                         	
	DECLARE @PlanLevelProjectedDataError INT = 0;
	DECLARE @BenefitLevelProjectedDataError INT = 0;
	DECLARE @PlanLevelProjectedData TABLE  
	(    
		[BenefitYearID] [int] NOT NULL,
		[ForecastID] [int] NOT NULL,   
		[CPS] VARCHAR(13) NULL, 
		[PlanName] VARCHAR(200) NULL,
		[INDeductible] VARCHAR(200) NULL,
		[INMOOP] VARCHAR(200) NULL,
		[OONDeductible] VARCHAR(200) NULL,
		[OONMOOP] VARCHAR(200) NULL,
		[CombinedDeductible] VARCHAR(200) NULL,
		[CombinedMOOP] VARCHAR(200) NULL,
		[PartBDeductible] VARCHAR(200) NULL,
		[DeductTypeDesc] VARCHAR(200) NULL
	) 
	DECLARE @BenefitLevelProjectedData TABLE  
	(    
		[BenefitYearID] INT NOT NULL, 
		[ForecastID] INT NOT NULL, 
		[CPS] VARCHAR(13) NULL, 
		[BenefitCategoryID] INT NOT NULL, 
		[BenefitCategoryName] VARCHAR(200) NULL, 
		[BenefitOrdinalID] INT NULL, 
		[INBundleID] INT NULL, 
		[INBenefitDayRange] int NULL, 
		[INDedApplies]  INT NULL, 
		[INCoinsurance] DECIMAL(12,6) NULL, 
		[INPerAdmitCopay] DECIMAL(12,6) NULL, 
		[INCopay] DECIMAL(12,6) NULL, 
		[OONBundleID] INT NULL, 
		[OONBenefitDayRange] int NULL, 
		[OONDedApplies] INT NULL, 
		[OONCoinsurance] DECIMAL(12,6) NULL, 
		[OONPerAdmitCopay] DECIMAL(12,6) NULL, 
		[OONCopay] DECIMAL(12,6) NULL  
	) 
	DECLARE @maxCount INT
	DECLARE @InternalForecastID INT
	IF @WhereIn IS NULL-- No Plans selected, extract all
	BEGIN
			BEGIN TRY
			INSERT INTO @PlanLevelProjectedData SELECT * FROM PrePricing.fnGetPlanLevelProjected()			
			IF EXISTS(SELECT 1 FROM @PlanLevelProjectedData)
				BEGIN
					SELECT * FROM @PlanLevelProjectedData
				END
			END TRY
			BEGIN CATCH				                  
				SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),
				@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
				' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)	    		 
				SELECT @ErrorMessage AS PlanLevelProjectedError;	
				SET @PlanLevelProjectedDataError=1
			END CATCH	
			BEGIN TRY								
				 If(OBJECT_ID('tempdb..#BenefitLevelProjectedDataAll') Is Not Null)
			Begin
				Drop Table #BenefitLevelProjectedDataAll
			End
			SELECT BenefitYearID,
                   ForecastID,
				   CPS,
                   BenefitCategoryID,
                   BenefitCategoryName,
                   BenefitOrdinalID,
                   INBundleID,
                   INBenefitDayRange,
                   INDedApplies,
                   INCoinsurance,
                   INPerAdmitCopay,
                   INCopay,
                   OONBundleID,
                   OONBenefitDayRange,
                   OONDedApplies,
                   OONCoinsurance,
                   OONPerAdmitCopay,
                   OONCopay INTO #BenefitLevelProjectedDataAll  
				   FROM PrePricing.fnGetBenefitLevelProjected();

				    If(OBJECT_ID('tempdb..#InputIdsAll') Is Not Null)
					Begin
						Drop Table #InputIdsAll
					End
					create table #InputIdsAll
					(
						SeqId INT IDENTITY(1,1), 						
						ForecastID int
					)
					INSERT INTO #InputIdsAll(ForecastID)
					SELECT Value AS ForecastID   FROM STRING_SPLIT(@WhereIN, ',')

					SELECT @maxCount = COUNT(1) FROM #InputIdsAll;
					IF(@maxCount>0)
					BEGIN					
						WHILE(@maxCount>0)
						BEGIN									
                            SELECT @InternalForecastID = ForecastID FROM #InputIdsAll WHERE SeqId=@maxCount
							BEGIN TRY
							INSERT INTO @BenefitLevelProjectedData
							SELECT BenefitYearID,
								ForecastID,
								CPS,
								BenefitCategoryID,
								BenefitCategoryName,
								BenefitOrdinalID,
								INBundleID,
								INBenefitDayRange,
								INDedApplies,
								CASE WHEN INCoinsurance =''
								THEN
								CAST(REPLACE(INCoinsurance,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(INCoinsurance AS DECIMAL(12,6))
								END AS INCoinsurance,	
								CASE WHEN INPerAdmitCopay =''
								THEN
								CAST(REPLACE(INPerAdmitCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(INPerAdmitCopay AS DECIMAL(12,6))
								END AS INPerAdmitCopay,	
								CASE WHEN INCopay =''
								THEN
								CAST(REPLACE(INCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(INCopay AS DECIMAL(12,6))
								END AS INCopay,															                                                                                                                        
								OONBundleID,
								OONBenefitDayRange,
								OONDedApplies,
								CASE WHEN OONCoinsurance =''
								THEN
								CAST(REPLACE(OONCoinsurance,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(OONCoinsurance AS DECIMAL(12,6))
								END AS OONCoinsurance,		
								CASE WHEN OONPerAdmitCopay =''
								THEN
								CAST(REPLACE(OONPerAdmitCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(OONPerAdmitCopay AS DECIMAL(12,6))
								END AS OONPerAdmitCopay,															  
								CASE WHEN OONCopay =''
								THEN
								CAST(REPLACE(OONCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(OONCopay AS DECIMAL(12,6))
								END AS OONCopay															                                                               
								FROM 
								 #BenefitLevelProjectedDataAll WHERE ForecastID IN(@InternalForecastID)
							  SET @maxCount=@maxCount-1;
							  END TRY
							  BEGIN CATCH				                  
								SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
								' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)	 
								SET @BenefitLevelProjectedDataError =@InternalForecastID
								RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)  									
							END CATCH	
						  END						  
					END

				IF (EXISTS(SELECT 1 FROM @BenefitLevelProjectedData) AND @BenefitLevelProjectedDataError<>1)
					BEGIN
						SELECT * FROM @BenefitLevelProjectedData ORDER BY ForecastID,BenefitCategoryID,BenefitOrdinalID
					END
			END TRY
			BEGIN CATCH				                  
				 SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
				' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)	 
				SET @BenefitLevelProjectedDataError =@InternalForecastID
				RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)  
			END CATCH						 
		END	

	ELSE --Export selected plans
		BEGIN
			BEGIN TRY
			INSERT INTO @PlanLevelProjectedData SELECT * FROM PrePricing.fnGetPlanLevelProjected()
			WHERE forecastid  IN (SELECT Value FROM STRING_SPLIT(@WhereIN, ','))
			IF EXISTS(SELECT 1 FROM @PlanLevelProjectedData)
				BEGIN
					SELECT * FROM @PlanLevelProjectedData
				END
			END TRY
			BEGIN CATCH				                  
				SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
				' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)	    		 
				SELECT @ErrorMessage AS PlanLevelProjectedError;		
				SET @PlanLevelProjectedDataError=1
			END CATCH	
			BEGIN TRY		
			If(OBJECT_ID('tempdb..#BenefitLevelProjectedData') Is Not Null)
			Begin
				Drop Table #BenefitLevelProjectedData
			End
			SELECT BenefitYearID,
                   ForecastID,
				   CPS,
                   BenefitCategoryID,
                   BenefitCategoryName,
                   BenefitOrdinalID,
                   INBundleID,
                   INBenefitDayRange,
                   INDedApplies,
                   INCoinsurance,
                   INPerAdmitCopay,
                   INCopay,
                   OONBundleID,
                   OONBenefitDayRange,
                   OONDedApplies,
                   OONCoinsurance,
                   OONPerAdmitCopay,
                   OONCopay INTO #BenefitLevelProjectedData  
				   FROM PrePricing.fnGetBenefitLevelProjected()
				   WHERE forecastid  IN (SELECT Value FROM STRING_SPLIT(@WhereIN, ','));

				    If(OBJECT_ID('tempdb..#InputIds') Is Not Null)
					Begin
						Drop Table #InputIds
					End
					create table #InputIds
					(
						SeqId INT IDENTITY(1,1), 						
						ForecastID int
					)
					INSERT INTO #InputIds(ForecastID)
					SELECT Value AS ForecastID   FROM STRING_SPLIT(@WhereIN, ',')



					SELECT @maxCount = COUNT(1) FROM #InputIds;
					IF(@maxCount>0)
					BEGIN					
						WHILE(@maxCount>0)
						BEGIN									
                            SELECT @InternalForecastID = ForecastID FROM #InputIds WHERE SeqId=@maxCount
							BEGIN TRY
							INSERT INTO @BenefitLevelProjectedData
							SELECT BenefitYearID,
								ForecastID,
								CPS,
								BenefitCategoryID,
								BenefitCategoryName,
								BenefitOrdinalID,
								INBundleID,
								INBenefitDayRange,
								INDedApplies,
								CASE WHEN INCoinsurance =''
								THEN
								CAST(REPLACE(INCoinsurance,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(INCoinsurance AS DECIMAL(12,6))
								END AS INCoinsurance,	
								CASE WHEN INPerAdmitCopay =''
								THEN
								CAST(REPLACE(INPerAdmitCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(INPerAdmitCopay AS DECIMAL(12,6))
								END AS INPerAdmitCopay,	
								CASE WHEN INCopay =''
								THEN
								CAST(REPLACE(INCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(INCopay AS DECIMAL(12,6))
								END AS INCopay,															                                                                                                                        
								OONBundleID,
								OONBenefitDayRange,
								OONDedApplies,
								CASE WHEN OONCoinsurance =''
								THEN
								CAST(REPLACE(OONCoinsurance,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(OONCoinsurance AS DECIMAL(12,6))
								END AS OONCoinsurance,		
								CASE WHEN OONPerAdmitCopay =''
								THEN
								CAST(REPLACE(OONPerAdmitCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(OONPerAdmitCopay AS DECIMAL(12,6))
								END AS OONPerAdmitCopay,															  
								CASE WHEN OONCopay =''
								THEN
								CAST(REPLACE(OONCopay,'',NULL)AS DECIMAL(12,6))
								ELSE
								CAST(OONCopay AS DECIMAL(12,6))
								END AS OONCopay															                                                               
								FROM 
								 #BenefitLevelProjectedData WHERE ForecastID IN(@InternalForecastID)
							  SET @maxCount=@maxCount-1;
							  END TRY
							  BEGIN CATCH				                  
								SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
								' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)	 
								SET @BenefitLevelProjectedDataError =@InternalForecastID
								RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)  									
							END CATCH	
						  END						  
					END

				IF (EXISTS(SELECT 1 FROM @BenefitLevelProjectedData) AND @BenefitLevelProjectedDataError<>1)
					BEGIN
						SELECT * FROM @BenefitLevelProjectedData ORDER BY ForecastID,BenefitCategoryID,BenefitOrdinalID
					END
			END TRY
			BEGIN CATCH				                  
				SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
				' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)	    		 
					SELECT 'Error for ForecastID:'+CAST(@BenefitLevelProjectedDataError AS VARCHAR)+', error is ' + @ErrorMessage AS BenefitLevelProjectedError;			
				SET @BenefitLevelProjectedDataError =1
			END CATCH						 
		END	
	END
	IF NOT EXISTS(SELECT 1 FROM @PlanLevelProjectedData) AND @PlanLevelProjectedDataError=0 
	BEGIN
		SELECT 'No Data Available for this ForecastId' AS NoData
	END
	IF NOT EXISTS(SELECT 1 FROM @BenefitLevelProjectedData) AND @BenefitLevelProjectedDataError=0 
	BEGIN
		SELECT 'No Data Available for this ForecastId' AS NoData
	END	
GO
