SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[it_fnGetMABidNotes]    ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: it_fnGetMABidNotes
--
-- AUTHOR: <PERSON><PERSON><PERSON>
--
-- CREATED DATE: 2010-Dec-30
--
-- DESCRIPTION: Function responsible for getting MA Bid Notes. The function returns current value of the
--				bid note if default is not used and it returns also the default value of the bid note
--				so that calling objects can make use of both.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		LkpIntMABidNoteDetail
--		LkpIntMABidNoteHeader
--		SavedPlanBidNoteDetail
--		SavedPlanHeader      
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetMARatingOption
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Dec-30		1			Initial Version														Catalin Tomescu
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[it_fnGetMABidNotes]
(
    @ForecastID INT
)
RETURNS TABLE AS  
RETURN
    (
SELECT 
    SPH.ForecastID,
    SPH.PlanTypeID,
    BidWorksheet,
    CellLocation,
    IsDefaultBidNote = ISNULL(IsDefaultBidNote, 1),
    BH.MABidNoteID AS BidNoteID,
    NoteText =
        CASE WHEN ISNULL(IsDefaultBidNote, 1) = 1
            THEN NULL
            ELSE SPB.BidNoteOverride
        END,
	BD.NoteText AS DefaultNoteText
FROM LkpIntMABidNoteHeader BH WITH(NOLOCK)
INNER JOIN LkpIntMABidNoteDetail BD WITH(NOLOCK)
    ON BH.MABidNoteID = BD.MABidNoteID
INNER JOIN SavedPlanHeader SPH WITH(NOLOCK)
    ON SPH.PlanTypeID = BD.PlanTypeID
LEFT JOIN SavedPlanBidNoteDetail SPB WITH(NOLOCK)
    ON SPB.BidNoteID = BH.MABidNoteID
    AND SPB.ForecastID = SPH.ForecastID
    AND SPB.IsHidden = 0
WHERE SPH.ForecastID = @ForecastID
   AND BD.MARatingOptionID = isnull(dbo.fnGetMARatingOption(@ForecastID), 1)
    )
GO
