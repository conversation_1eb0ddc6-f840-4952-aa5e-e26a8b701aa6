SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnIsPartBOnly]    ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: fnIsPartBOnly
--
-- AUTHOR: Tim Gao
--
-- CREATED DATE: 01-19-2012
-- HEADER UPDATED: 
--
-- DESCRIPTION: Get the indicator for PartB Only plans
--		        
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		
--	Output: 
--		
--
-- TABLES: 
--	Read: SavedPlanHeader
--	
--
--	Write:
--	    
-- VIEWS:
--
-- FUNCTIONS:
--	
-- STORED PROCS:
--	
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2012-Jan-19		Initial																			Tim Gao	
-- 2022-Jul-05		2			Returning 0 as IsPartBOnly                                          Aleksandar Dimitrijevic
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnIsPartBOnly]
    (
    @ForecastID INT
    )
RETURNS TABLE 
AS  
RETURN  (
	SELECT 0 As IsPartBOnly

    )

GO

