/****** Object:  UserDefinedFunction [dbo].[fnAppGetMABPTWS3CSDesc]   ******/

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetMABPTWS3CSDesc
--
-- AUTHOR: Sule <PERSON>
--
-- CREATED DATE: 2008-Aug-20
-- HEADER UPDATED: 2011-Mar-30
--
-- DESCRIPTION: This function returns IN/OON cost share description for benefits.  
--
-- PARAMETERS:
--	Input:
--		@ForecastID 
--		@BidServiceCategoryID
--		@BenefitTypeID
--		@IsIN
--	Output:
--
-- TABLES: 
--	Read:
--		CalcBenefitProjection
--		LkpExtCMSBidServiceCategory
--		LkpProductType
--		LkpIntMOOPCategoryDetail
--		SavedPlanBenefitDetail
--		SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnAppGetMABPTWS3BenefitDetail
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2010-Mar-03      1           Initial Version                                                     Sule Dauda
-- 2010-Oct-05      2           Revised for 2012 database                                           Jake Gaecke
-- 2011-Jan-06      3           Revised for coding standards and naming convention                  Michael Siekerka
-- 2011-Mar-22		4			Created as separate function to clean up fnAppGetMABPTWS3.			Joe Casey
--									Both IN and OON desc are based on INBenefitTypeID.
-- 2011-Mar-30		5			Removed the use of @IsShortLine in detail function					Joe Casey
-- 2011-Apr-18		6			Updated OON Query to remove unnecessary ';'							Nate Jacoby
-- 2011-Apr-29		7			Removed IsPhysician Addon restrictions								Joe Casey
-- 2013-Feb-13		8			0369 - Removed Day Ranges for coins									Mason Roberts
-- 2013-Feb-25		9			Revised desc for OON (not soley based on InBenefitTypeID)	        Nick Koesters
-- 2013-Mar-18		10			0369 - Included IPTier data											Mason Roberts
-- 2013-May-09		11			Increased to 150 days, Corrected Formatting							Mike Deren
-- 2014-Apr-28		12			Added IP Rehab to the list, matching IP Acute						Mike Deren
-- 2016-Feb-26		13			Added two additional parameters @IsINEnabled and @IsOONEnabled		Manisha Tyagi
-- 2018-Apr-24		14			Modified LkpExtCMSPlanType for new UI table modifications			Jordan Purdue
-- 2022-May-01		15			MAAUI migration; replaced intput variable @PlanIndex with
--								@ForecastID; replaced reference frem PlanIndex to ForecastID;
--								added logic for IN/OON BenefitTypeID = 5; added further
--								specification when calling SavedPlanBenefitDetail 
--								"SPBD.IsLiveIndex = 1; removed "Project 0369" and calling table
--								SavedPlanIPTierDetail												Aleksandar Dimitrijevic
-- 2022-Sep-13		16			Updated function for standerds 										Aleksandar Dimitrijevic
-- 2022-Dec-16		17			4119454 Deployment objects from Dev-Grow							Aleksandar Dimitrijevic
-- 2023-Aug-02		18			Added Nolock, Internal Parameter									Sheetal Patil 
-- 2024-AUG-30		19			Replaced CalcPreMOOPBenefitPricing with SavedPlanBenefitDetail;
--								Removed CalcDeductibleMOOPFactors									Franklin Fu
-- -------------------------------------------------------------------------------------------------------------------------	
CREATE FUNCTION [dbo].[fnAppGetMABPTWS3CSDesc]
(
	@ForecastID INT,
	@BidServiceCategoryID SMALLINT,
	@BenefitTypeID TINYINT,
	@IsIN BIT
)
RETURNS VARCHAR(MAX)
AS
BEGIN
	DECLARE @XForecastID INT = @ForecastID;
	DECLARE @XBidServiceCategoryID SMALLINT =@BidServiceCategoryID 
	DECLARE @XBenefitTypeID TINYINT = @BenefitTypeID 
	DECLARE @XIsIN BIT = @IsIN
	DECLARE @Days1To AS VARCHAR(8) = '(Days 1-'

	DECLARE @ResultVar VARCHAR(MAX)

--------------------------------------------------------------------------------------------------------------------
	IF @XIsIN = 1
--- IN Description -------------------------------------------------------------------------------------------------
		BEGIN
			SELECT @ResultVar =
				ISNULL(  -- First Null Check
					CASE WHEN SPBD.INBenefitTypeID = 1 THEN --Coins 
						CASE WHEN  MIN(SPBD.INBenefitValue) = MAX(SPBD.INBenefitValue) THEN
								CAST(CAST(MIN(SPBD.INBenefitValue) * 100 AS INT) AS VARCHAR) + '%'                    
							 WHEN BSC.ServiceCategoryCode = 'b.' THEN 
								(SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
								 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
								 WHERE SPBD.ForecastID = @XForecastID
								   AND SPBD.BenefitOrdinalID = 1
								   AND SPBD.BenefitCategoryID = 83
								   AND SPBD.IsBenefitYearCurrentYear = 0
								   AND SPBD.IsLiveIndex = 1) + '%' 
							 ELSE CAST(CAST(MIN(SPBD.INBenefitValue) * 100 AS INT)AS VARCHAR)
								+ '%-' + CAST(CAST(MAX(SPBD.INBenefitValue) * 100 AS INT) AS VARCHAR) + '%' 
						END
						 ELSE  --Copay
							CASE WHEN MIN(SPBD.INBenefitValue) = MAX(SPBD.INBenefitValue) AND SPBD.INBenefitTypeID <> 5 THEN
								   '$' + CAST(CAST(MIN(SPBD.INBenefitValue) AS INT) AS VARCHAR)
								 WHEN BSC.ServiceCategoryCode = 'b.' THEN '$' +
									(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
									 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									 WHERE SPBD.ForecastID = @XForecastID
									   AND SPBD.BenefitOrdinalID = 1
									   AND SPBD.BenefitCategoryID = 83
									   AND SPBD.IsBenefitYearCurrentYear = 0
									   AND SPBD.IsLiveIndex = 1)
------------------------Start IPTiers
								 WHEN BSC.ServiceCategoryCode = 'a.' AND SPBD.INBenefitTypeID = 5 THEN
									CASE WHEN BSC.CostShareServiceCategoryCode = 'a.1.' THEN '$' +
										(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
										 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										 WHERE SPBD.ForecastID = @XForecastID
										   AND SPBD.BenefitOrdinalID = 1
										   AND SPBD.BenefitCategoryID = 65
										   AND SPBD.IsBenefitYearCurrentYear = 0
										   AND SPBD.IsLiveIndex = 1)
										 ELSE '$' +
										(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
										 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										 WHERE SPBD.ForecastID = @XForecastID
										   AND SPBD.BenefitOrdinalID = 1
										   AND SPBD.BenefitCategoryID = 66
										   AND SPBD.IsBenefitYearCurrentYear = 0
										   AND SPBD.IsLiveIndex = 1)
									END 
------------------------End IPTiers	
								 ELSE   '$' + CAST(CAST(MIN(SPBD.INBenefitValue) AS INT) AS VARCHAR)
									+ '-$' + CAST(CAST(MAX(SPBD.INBenefitValue) AS INT) AS VARCHAR)
							END
					END -- of 1a
					+ CASE WHEN SPBD.INBenefitTypeID <> 5 THEN --1b
						CASE ( --Get the proper value to compare against
							CASE SPBD.INBenefitTypeID
								WHEN 1 THEN BSC.MeasurementCoin
								WHEN 2 THEN BSC.MeasurementCopay
								WHEN 3 THEN BSC.MeasurementAdmit
								WHEN 5 THEN BSC.MeasurementCopay
								ELSE ''
							END)	
						   WHEN 'D'    THEN '/Day'
						   WHEN 'A'    THEN '/Admit'
						   WHEN 'V'    THEN '/Visit'
						   WHEN 'T'    THEN '/Trip'
						   WHEN 'Coin' THEN ' Coins'
						   WHEN 'P'    THEN '/Proc'
						   WHEN 'S'    THEN '/Script' 
						   WHEN 'O'    THEN '/Service'
						   ELSE ''
						END --of 1b
						   ELSE '/Day'
					  END
					, '') --end of Null check 1
				+ ISNULL( -- Second Null check
					CASE
						WHEN BSC.ServiceCategoryCode = 'a.' AND SPBD.INBenefitTypeID = 2
						THEN @Days1To
								+ CAST(MAX(CASE WHEN SPBD.INDayRangeEnd = 0 
												THEN 1
												ELSE SPBD.INDayRangeEnd
											END) AS VARCHAR)
								+ ')'                             
------------------------Start IPTiers
						WHEN BSC.CostShareServiceCategoryCode = 'a.1.' AND SPBD.INBenefitTypeID = 5 THEN
							CASE WHEN (SELECT SPBD.INDayRangeEnd
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
									     AND SPBD.BenefitOrdinalID = 2
										 AND SPBD.BenefitCategoryID = 65
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1) IS NULL THEN --IP With 1 tier populated
									'(Days 1-150) '
								 WHEN (SELECT SPBD.INDayRangeEnd
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
									     AND SPBD.BenefitOrdinalID = 2
									     AND SPBD.BenefitCategoryID = 65
									     AND SPBD.IsLiveIndex = 1) IS NOT NULL 
							      AND (SELECT SPBD.INDayRangeEnd
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
									     AND SPBD.BenefitOrdinalID = 3
									     AND SPBD.BenefitCategoryID = 65
									     AND SPBD.IsBenefitYearCurrentYear = 0
									     AND SPBD.IsLiveIndex = 1) IS NULL 
									THEN --IP With 2 tiers populated	
									@Days1To --1rst Tier Day Range. eg: "(Days 1-20)"
									+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
									     AND SPBD.BenefitOrdinalID = 1
										 AND SPBD.BenefitCategoryID = 65
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1)  
									+ '), '
									+ CASE SPBD.INBenefitTypeID --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's already listed)
										WHEN 1  --Coins
											THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
												  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												  WHERE SPBD.ForecastID = @XForecastID
													AND SPBD.BenefitOrdinalID = 2 --2nd Tier
													AND SPBD.BenefitCategoryID = 65
													AND SPBD.IsBenefitYearCurrentYear = 0
													AND SPBD.IsLiveIndex = 1)				
											+ '%'
											ELSE --Copay
											'$' + 
												(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
												 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												 WHERE SPBD.ForecastID = @XForecastID
												   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
												   AND SPBD.BenefitCategoryID = 65
												   AND SPBD.IsBenefitYearCurrentYear = 0
												   AND SPBD.IsLiveIndex = 1)
									  END
									+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-100)"
									+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 1
										 AND SPBD.BenefitCategoryID = 65
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1) 
									+ '-150)' 
								 WHEN (SELECT SPBD.INDayRangeEnd
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 3
										 AND SPBD.BenefitCategoryID = 65
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1) IS NOT NULL 
									THEN --IP With all three tiers populated
									@Days1To	--1rst Tier Day Range. eg: "(Days 1-20)"
										+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 1
											 AND SPBD.BenefitCategoryID = 65
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1)  
										+ '), '
										+ CASE SPBD.INBenefitTypeID  --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's is listed already)
											WHEN 1  --Coins
												THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
													  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													  WHERE SPBD.ForecastID = @XForecastID
														AND SPBD.BenefitOrdinalID = 2 --2nd Tier
														AND SPBD.BenefitCategoryID = 65
														AND SPBD.IsBenefitYearCurrentYear = 0
														AND SPBD.IsLiveIndex = 1)				
												+ '%' 
												ELSE --Copay
												'$' + 
													(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
													 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													 WHERE SPBD.ForecastID = @XForecastID
													   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
													   AND SPBD.BenefitCategoryID = 65
													   AND SPBD.IsBenefitYearCurrentYear = 0
													   AND SPBD.IsLiveIndex = 1)
										  END
										+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-50)"
										+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 1
											 AND SPBD.BenefitCategoryID = 65
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1) 
										+ '-'
										+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 2
											 AND SPBD.BenefitCategoryID = 65
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1)  
										+ '), ' 
										+ CASE SPBD.INBenefitTypeID  --Putting in Cost Share Amount for 3rd Tier (since 2nd Tier's is listed already)
											WHEN 1  --Coins
												THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
													  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													  WHERE SPBD.ForecastID = @XForecastID
														AND SPBD.BenefitOrdinalID = 3 --3rd Tier
														AND SPBD.BenefitCategoryID = 65
														AND SPBD.IsBenefitYearCurrentYear = 0
														AND SPBD.IsLiveIndex = 1)				
													+ '%' 
											ELSE --Copay
												' $' + 
													(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
													 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													 WHERE SPBD.ForecastID = @XForecastID
													   AND SPBD.BenefitOrdinalID = 3 --3rd Tier
													   AND SPBD.BenefitCategoryID = 65
													   AND SPBD.IsBenefitYearCurrentYear = 0
													   AND SPBD.IsLiveIndex = 1)
										  END
										+ ' (Days ' --3rd Tier Day Range. eg: "(Days 51-100)"
										+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 2
											 AND SPBD.BenefitCategoryID = 65
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1) 
										+ '-150) ' 
								ELSE ''
							END
						WHEN BSC.CostShareServiceCategoryCode = 'a.2.' AND SPBD.INBenefitTypeID = 5 THEN
							CASE WHEN (SELECT SPBD.INDayRangeEnd
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 2
										 AND SPBD.BenefitCategoryID = 66
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1) IS NULL THEN --IP With 1 tier populated
									'(Days 1-150) ' 
								 WHEN (SELECT SPBD.INDayRangeEnd
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 2
										 AND SPBD.BenefitCategoryID = 66
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1) IS NOT NULL 
										 AND (SELECT SPBD.INDayRangeEnd
											  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											  WHERE SPBD.ForecastID = @XForecastID
												AND SPBD.BenefitOrdinalID = 3
												AND SPBD.BenefitCategoryID = 66
												AND SPBD.IsBenefitYearCurrentYear = 0
												AND SPBD.IsLiveIndex = 1) IS NULL THEN --IP With 2 tiers populated
									@Days1To --1rst Tier Day Range. eg: "(Days 1-20)"
									+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 1
										 AND SPBD.BenefitCategoryID = 66
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1)  
									+ '), '
									+ CASE SPBD.INBenefitTypeID --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's already listed)
										WHEN 1  --Coins
											THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
												  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												  WHERE SPBD.ForecastID = @XForecastID
													AND SPBD.BenefitOrdinalID = 2 --2nd Tier
													AND SPBD.BenefitCategoryID = 66
													AND SPBD.IsBenefitYearCurrentYear = 0
													AND SPBD.IsLiveIndex = 1)				
											+ '%' 
										ELSE --Copay
											'$' + 
												(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
												 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												 WHERE SPBD.ForecastID = @XForecastID
												   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
												   AND SPBD.BenefitCategoryID = 66
												   AND SPBD.IsBenefitYearCurrentYear = 0
												   AND SPBD.IsLiveIndex = 1)
									  END
									+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-100)"
									+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 1
										 AND SPBD.BenefitCategoryID = 66
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1) 
									+ '-150)' 	

								 WHEN MAX(SPBD.BenefitOrdinalID) = 3 --SNF With all three tiers populated
									THEN
										@Days1To	--1rst Tier Day Range. eg: "(Days 1-20)"
										+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 1
											 AND SPBD.BenefitCategoryID = 66
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1)  
										+ '), '
										+ CASE SPBD.INBenefitTypeID  --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's is listed already)
											WHEN 1  --Coins
												THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
													  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													  WHERE SPBD.ForecastID = @XForecastID
														AND SPBD.BenefitOrdinalID = 2 --2nd Tier
														AND SPBD.BenefitCategoryID = 66
														AND SPBD.IsBenefitYearCurrentYear = 0
														AND SPBD.IsLiveIndex = 1)				
												+ '%' 
												ELSE --Copay
												'$' + 
													(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
													 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													 WHERE SPBD.ForecastID = @XForecastID
													   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
													   AND SPBD.BenefitCategoryID = 66
													   AND SPBD.IsBenefitYearCurrentYear = 0
													   AND SPBD.IsLiveIndex = 1)
										  END
										+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-50)"
										+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 1
											 AND SPBD.BenefitCategoryID = 66
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1) 
										+ '-'
										+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 2
											 AND SPBD.BenefitCategoryID = 66
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1)  
										+ '), ' 
										+ CASE SPBD.INBenefitTypeID  --Putting in Cost Share Amount for 3rd Tier (since 2nd Tier's is listed already)
											WHEN 1  --Coins
												THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
													  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													  WHERE SPBD.ForecastID = @XForecastID
														AND SPBD.BenefitOrdinalID = 3 --3rd Tier
														AND SPBD.BenefitCategoryID = 66
														AND SPBD.IsBenefitYearCurrentYear = 0
														AND SPBD.IsLiveIndex = 1)				
												+ '%' 
												ELSE --Copay
												' $' + 
													(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
													 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													 WHERE SPBD.ForecastID = @XForecastID
													   AND SPBD.BenefitOrdinalID = 3 --3rd Tier
													   AND SPBD.BenefitCategoryID = 66
													   AND SPBD.IsBenefitYearCurrentYear = 0
													   AND SPBD.IsLiveIndex = 1)
										  END
										+ ' (Days ' --3rd Tier Day Range. eg: "(Days 51-100)"
										+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 2
											 AND SPBD.BenefitCategoryID = 66
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1) 
										+ '-150) ' 
								ELSE ''
							END 
------------------------End IPTiers
						WHEN BSC.ServiceCategoryCode = 'b.' AND SPBD.INBenefitTypeID <> 1 THEN --SNF
							CASE
								WHEN MAX(SPBD.BenefitOrdinalID) = 1 --SNF With 1 tier populated
									THEN '(Days 1-100) '

								WHEN MAX(SPBD.BenefitOrdinalID) = 2 --SNF With 2 tiers populated
									THEN
									@Days1To --1rst Tier Day Range. eg: "(Days 1-20)"
									+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 1
										 AND SPBD.BenefitCategoryID = 83
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1)  
									+ '), '
									+ CASE SPBD.INBenefitTypeID --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's already listed)
										WHEN 1  --Coins
											THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
												  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												  WHERE SPBD.ForecastID = @XForecastID
													AND SPBD.BenefitOrdinalID = 2 --2nd Tier
													AND SPBD.BenefitCategoryID = 83
													AND SPBD.IsBenefitYearCurrentYear = 0
													AND SPBD.IsLiveIndex = 1)				
											+ '%' 
										ELSE --Copay
											'$' + 
												(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
												 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												 WHERE SPBD.ForecastID = @XForecastID
												   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
												   AND SPBD.BenefitCategoryID = 83
												   AND SPBD.IsBenefitYearCurrentYear = 0
												   AND SPBD.IsLiveIndex = 1)
									  END
									+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-100)"
									+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
									   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
									   WHERE SPBD.ForecastID = @XForecastID
										 AND SPBD.BenefitOrdinalID = 1
										 AND SPBD.BenefitCategoryID = 83
										 AND SPBD.IsBenefitYearCurrentYear = 0
										 AND SPBD.IsLiveIndex = 1) 
									+ '-100)' 		

								WHEN MAX(SPBD.BenefitOrdinalID) = 3 --SNF With all three tiers populated
									THEN
										@Days1To	--1rst Tier Day Range. eg: "(Days 1-20)"
										+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 1
											 AND SPBD.BenefitCategoryID = 83
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1)  
										+ '), '
										+ CASE SPBD.INBenefitTypeID  --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's is listed already)
											WHEN 1  --Coins
												THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
													  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													  WHERE SPBD.ForecastID = @XForecastID
														AND SPBD.BenefitOrdinalID = 2 --2nd Tier
														AND SPBD.BenefitCategoryID = 83
														AND SPBD.IsBenefitYearCurrentYear = 0
														AND SPBD.IsLiveIndex = 1)				
												+ '%' 
											ELSE --Copay
												'$' + 
													(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
													 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													 WHERE SPBD.ForecastID = @XForecastID
													   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
													   AND SPBD.BenefitCategoryID = 83
													   AND SPBD.IsBenefitYearCurrentYear = 0
													   AND SPBD.IsLiveIndex = 1)
										  END
										+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-50)"
										+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 1
											 AND SPBD.BenefitCategoryID = 83
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1) 
										+ '-'
										+ (SELECT CAST(SPBD.INDayRangeEnd  AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 2
											 AND SPBD.BenefitCategoryID = 83
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1)  
										+ '), ' 
										+ CASE SPBD.INBenefitTypeID  --Putting in Cost Share Amount for 3rd Tier (since 2nd Tier's is listed already)
											WHEN 1  --Coins
												THEN (SELECT CAST(CAST(SPBD.INBenefitValue * 100 AS INT) AS VARCHAR)
													  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													  WHERE SPBD.ForecastID = @XForecastID
														AND SPBD.BenefitOrdinalID = 3 --3rd Tier
														AND SPBD.BenefitCategoryID = 83
														AND SPBD.IsBenefitYearCurrentYear = 0
														AND SPBD.IsLiveIndex = 1)				
												+ '%' 
											ELSE --Copay
												' $' + 
													(SELECT CAST(CAST(SPBD.INBenefitValue AS INT) AS VARCHAR)
													 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
													 WHERE SPBD.ForecastID = @XForecastID
													   AND SPBD.BenefitOrdinalID = 3 --3rd Tier
													   AND SPBD.BenefitCategoryID = 83
													   AND SPBD.IsBenefitYearCurrentYear = 0
													   AND SPBD.IsLiveIndex = 1)
										  END
										+ ' (Days ' --3rd Tier Day Range. eg: "(Days 51-100)"
										+ (SELECT CAST(SPBD.INDayRangeEnd + 1 AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 2
											 AND SPBD.BenefitCategoryID = 83
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1) 
										+ '-100) '
								ELSE ''										
							END
						ELSE ''
					END    

				, '')--end of Null 2
				+ 
				ISNULL( --Added to prevent a null value from showing up on the BPT. 
					CASE
						WHEN SPBD.INBenefitTypeID IS NULL THEN ''
						ELSE 
							': ' + CHAR(10)  --carriage return/line feed
							+ dbo.fnAppGetMABPTWS3BenefitDetail(@XForecastID,BSC.CostShareServiceCategoryCode,1,SPBD.INBenefitTypeID)
							+ CASE WHEN BSC.CostShareServiceCategoryCode = 'a.1.' and SPBD.INBenefitTypeID = 5 
								   THEN ', ' + dbo.fnAppGetMABPTWS3BenefitDetail(@XForecastID,BSC.CostShareServiceCategoryCode,1,2) -- adding description for BenefitCategoryID = 102
								   ELSE ''
							  END
					END
				, '')
			FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
			INNER JOIN dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
				ON SPBD.BenefitCategoryID = BC.BenefitCategoryID
			INNER JOIN dbo.LkpExtCMSBidServiceCategory BSC WITH (NOLOCK)
				ON BSC.BidServiceCategoryID = BC.BidServiceCatID
			INNER JOIN dbo.CalcBenefitProjection BBP WITH (NOLOCK)
				ON BBP.ForecastID = SPBD.ForecastID
			   AND BBP.BenefitCategoryID = SPBD.BenefitCategoryID
			   AND MARatingOptionID = 3 --Blended
			INNER JOIN dbo.SavedPlanHeader SPH WITH (NOLOCK)-- to get PlanTypeID
				ON SPH.ForecastID = SPBD.ForecastID
			INNER JOIN dbo.LkpProductType  CPT WITH (NOLOCK) -- to get ProductID
				ON SPH.PlanTypeID = CPT.ProductTypeID
			INNER JOIN dbo.LkpIntMOOPCategoryDetail MCD WITH (NOLOCK)
				ON MCD.ProductID = CPT.ProductMOOPID
			   AND MCD.BenefitCategoryID = SPBD.BenefitCategoryID
			WHERE SPBD.ForecastID = @XForecastID
			  AND MCD.MOOPCategoryID <> 5
			  AND SPBD.IsBenefitYearCurrentYear = 0
			  AND BBP.DualEligibleTypeID = 0 --NonDual
			  AND BSC.BidServiceCategoryID = @XBidServiceCategoryID
			  AND SPBD.INBenefitTypeID = @XBenefitTypeID
			  AND SPBD.IsLiveIndex = 1

			GROUP BY 
				SPBD.ForecastID,
				SPBD.INBenefitTypeID,
				BSC.CostShareServiceCategoryCode,
				BSC.ServiceCategory,
				BSC.SubServiceCategory,
				BSC.ServiceCategoryCode,
				MeasurementCoin,
				MeasurementAdmit,
				MeasurementCopay,
				SPBD.InDayRangeEnd,
				SPBD.InBenefitTypeID,
				SPBD.BenefitOrdinalID,
				SPBD.IsBenefitYearCurrentYear
		END
	ELSE
--------------------------------------------------------------------------------------------------------------------
--- OON Description ------------------------------------------------------------------------------------------------
	BEGIN
				DECLARE @OONDesc TABLE
					(
					 BenDesc VARCHAR(MAX)
					)

				INSERT INTO @OONDesc
				SELECT BenDesc =
					CASE WHEN ISNULL(SPBD.OONBenefitTypeID,0) = 0 AND SPBD.OONBenefitTypeID <> 5 THEN ''
						 ELSE --Show Description
						ISNULL( --First Null check
							CASE WHEN SPBD.OONBenefitTypeID = 1 AND SPBD.OONBenefitTypeID <> 5 THEN --Coinsurance
								CASE WHEN MIN(SPBD.OONBenefitValue) = MAX(SPBD.OONBenefitValue)
										THEN CAST(CAST(MIN(SPBD.OONBenefitValue) * 100 AS INT) AS VARCHAR) + '%'
									 WHEN BSC.ServiceCategoryCode = 'b.'
										THEN (SELECT CAST(CAST(spbd.OONBenefitValue * 100 AS INT) AS VARCHAR)
											  FROM dbo.SavedPlanBenefitDetail SPBD
											  WHERE SPBD.ForecastID = @XForecastID
												AND SPBD.BenefitOrdinalID = 1
												AND SPBD.BenefitCategoryID = 83
												AND SPBD.IsBenefitYearCurrentYear = 0
												AND spbd.IsLiveIndex = 1) + '%'
									ELSE CAST(CAST(MIN(SPBD.OONBenefitValue) * 100 AS INT) AS VARCHAR) + '%-'
										+ CAST(CAST(MAX(SPBD.OONBenefitValue) * 100 AS INT) AS VARCHAR) + '%'
								END
								 ELSE 	--Copay, or Admit, or Other dollar amount
									CASE WHEN MIN(SPBD.OONBenefitValue) = MAX(SPBD.OONBenefitValue) AND SPBD.OONBenefitTypeID <> 5 THEN
										'$' + CAST(CAST(MIN(SPBD.OONBenefitValue) AS INT) AS VARCHAR)
										 WHEN BSC.ServiceCategoryCode = 'b.'
											THEN '$'
										+ (SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
										   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
										   WHERE SPBD.ForecastID = @XForecastID
											 AND SPBD.BenefitOrdinalID = 1
											 AND SPBD.BenefitCategoryID = 83
											 AND SPBD.IsBenefitYearCurrentYear = 0
											 AND SPBD.IsLiveIndex = 1)
------------------------Start IPTiers
										 WHEN BSC.ServiceCategoryCode = 'a.' AND SPBD.OONBenefitTypeID = 5 THEN
												CASE WHEN BSC.CostShareServiceCategoryCode = 'a.1.' THEN
												'$' +
												(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
												 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												 WHERE SPBD.ForecastID = @XForecastID
												   AND SPBD.BenefitOrdinalID = 1
												   AND SPBD.BenefitCategoryID = 65
												   AND SPBD.IsBenefitYearCurrentYear = 0
												   AND spbd.IsLiveIndex = 1)
										 ELSE '$' +
											(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
											 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											 WHERE SPBD.ForecastID = @XForecastID
											   AND SPBD.BenefitOrdinalID = 1
											   AND SPBD.BenefitCategoryID = 66
											   AND SPBD.IsBenefitYearCurrentYear = 0
											   AND SPBD.IsLiveIndex = 1)
									END 
------------------------End IPTiers																
								 ELSE '$' + CAST(CAST(MIN(SPBD.OONBenefitValue) AS INT) AS VARCHAR) + '-$'
										+ CAST(CAST(MAX(SPBD.OONBenefitValue) AS INT) AS VARCHAR)
							END
					END
								+ 
							CASE WHEN SPBD.OONBenefitTypeID <> 5 THEN
								CASE --Get the item to compare to
									CASE SPBD.OONBenefitTypeID
										WHEN 1 THEN BSC.MeasurementCoin
										WHEN 2 THEN BSC.MeasurementCopay
										WHEN 3 THEN BSC.MeasurementAdmit
									END
									WHEN 'D'    THEN '/Day'
									WHEN 'A'    THEN '/Admit'
									WHEN 'V'    THEN '/Visit'
									WHEN 'T'    THEN '/Trip'
									WHEN 'Coin' THEN ' Coins'
									WHEN 'P'    THEN '/Proc'
									WHEN 'S'    THEN '/Script'
									WHEN 'O'    THEN '/Service'
									ELSE ''
								END
								 ELSE '/Day'
							END, ''   --of 1b
							) --end of first Null check
						+ ISNULL ( --Second Null check
							CASE WHEN BSC.ServiceCategoryCode = 'a.' AND SPBD.OONBenefitTypeID = 2
									THEN @Days1To + CAST(MAX(SPBD.OONDayRangeEnd) AS VARCHAR) + ')'                             
------------------------Start IPTiers
								 WHEN BSC.CostShareServiceCategoryCode = 'a.1.' AND SPBD.OONBenefitTypeID = 5 THEN
									CASE WHEN (SELECT SPBD.OONDayRangeEnd
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 2
												 AND SPBD.BenefitCategoryID = 65
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) IS NULL THEN --IP With 1 tier populated
											'(Days 1-150) '
										 WHEN (SELECT SPBD.OONDayRangeEnd
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 2
												 AND SPBD.BenefitCategoryID = 65
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) IS NOT NULL 
										  AND (SELECT SPBD.OONDayRangeEnd
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 3
												 AND SPBD.BenefitCategoryID = 65
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) IS NULL 	
													THEN @Days1To --1rst Tier Day Range. eg: "(Days 1-20)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 65
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
											+ '), '
											+ CASE SPBD.OONBenefitTypeID --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's already listed)
												WHEN 1  --Coins
													THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 150 AS INT) AS VARCHAR)
														  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														  WHERE SPBD.ForecastID = @XForecastID
															AND SPBD.BenefitOrdinalID = 2 --2nd Tier
															AND SPBD.BenefitCategoryID = 65
															AND SPBD.IsBenefitYearCurrentYear = 0
															AND SPBD.IsLiveIndex = 1)				
													+ '%' 
													ELSE --Copay
													'$' + 
														(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
														 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														 WHERE SPBD.ForecastID = @XForecastID
														   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
														   AND SPBD.BenefitCategoryID = 65
														   AND SPBD.IsBenefitYearCurrentYear = 0
														   AND SPBD.IsLiveIndex = 1)
											  END
											+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-150)"
											+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 1
												 AND SPBD.BenefitCategoryID = 65
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) 
											+ '-150)'
										 WHEN MAX(SPBD.BenefitOrdinalID) = 3 --SNF With all three tiers populated
											THEN @Days1To	--1rst Tier Day Range. eg: "(Days 1-20)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 65
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '), '
												+ CASE SPBD.OONBenefitTypeID  --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's is listed already)
													WHEN 1  --Coins
														THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 150 AS INT) AS VARCHAR)
															  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															  WHERE SPBD.ForecastID = @XForecastID
																AND SPBD.BenefitOrdinalID = 2 --2nd Tier
																AND SPBD.BenefitCategoryID = 65
																AND SPBD.IsBenefitYearCurrentYear = 0
																AND SPBD.IsLiveIndex = 1)
														+ '%'
													ELSE --Copay
														'$' + (SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
															   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															   WHERE SPBD.ForecastID = @XForecastID
																 AND SPBD.BenefitOrdinalID = 2 --2nd Tier
																 AND SPBD.BenefitCategoryID = 65
																 AND SPBD.IsBenefitYearCurrentYear = 0
																 AND SPBD.IsLiveIndex = 1)
												  END
												+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-50)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 65
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '-' + (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
														 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														 WHERE SPBD.ForecastID = @XForecastID
														   AND SPBD.BenefitOrdinalID = 2
														   AND SPBD.BenefitCategoryID = 65
														   AND SPBD.IsBenefitYearCurrentYear = 0
														   AND SPBD.IsLiveIndex = 1)
												+ '), ' 
												+ CASE SPBD.OONBenefitTypeID  --Putting in Cost Share Amount for 3rd Tier (since 2nd Tier's is listed already)
													WHEN 1  --Coins
														THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 150 AS INT) AS VARCHAR)
															  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															  WHERE SPBD.ForecastID = @XForecastID
																AND SPBD.BenefitOrdinalID = 3 --3rd Tier
																AND SPBD.BenefitCategoryID = 65
																AND SPBD.IsBenefitYearCurrentYear = 0
																AND SPBD.IsLiveIndex = 1)
														+ '%'
													ELSE --Copay
														' $' +
															(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
															 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															 WHERE SPBD.ForecastID = @XForecastID
															   AND SPBD.BenefitOrdinalID = 3 --3rd Tier
															   AND SPBD.BenefitCategoryID = 65
															   AND SPBD.IsBenefitYearCurrentYear = 0
															   AND SPBD.IsLiveIndex = 1)
												  END
												+ ' (Days ' --3rd Tier Day Range. eg: "(Days 51-150)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 2
													 AND SPBD.BenefitCategoryID = 65
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '-150)'
										ELSE ''
									END
								 WHEN BSC.CostShareServiceCategoryCode = 'a.2.' AND SPBD.OONBenefitTypeID = 5 THEN
									CASE WHEN (SELECT SPBD.OONDayRangeEnd
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 2
												 AND SPBD.BenefitCategoryID = 66
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) IS NULL THEN --IP With 1 tier populated
											'(Days 1-150) '
										 WHEN (SELECT SPBD.OONDayRangeEnd
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 2
												 AND SPBD.BenefitCategoryID = 66
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) IS NOT NULL 
										  AND (SELECT SPBD.OONDayRangeEnd
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 3
												 AND SPBD.BenefitCategoryID = 66
												 AND SPBD.IsBenefitYearCurrentYear = 0
											     AND SPBD.IsLiveIndex = 1) IS NULL 	
													THEN @Days1To --1rst Tier Day Range. eg: "(Days 1-20)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 66
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
											+ '), '
											+ CASE SPBD.OONBenefitTypeID --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's already listed)
												WHEN 1  --Coins
													THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 150 AS INT) AS VARCHAR)
														  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														  WHERE SPBD.ForecastID = @XForecastID
														    AND SPBD.BenefitOrdinalID = 2 --2nd Tier
															AND SPBD.BenefitCategoryID = 66
															AND SPBD.IsBenefitYearCurrentYear = 0
															AND SPBD.IsLiveIndex = 1)				
													+ '%' 
												ELSE --Copay
													'$' + 
														(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
														 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														 WHERE SPBD.ForecastID = @XForecastID
														   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
														   AND SPBD.BenefitCategoryID = 66
														   AND SPBD.IsBenefitYearCurrentYear = 0
														   AND SPBD.IsLiveIndex = 1)
											  END
											+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-150)"
											+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 1
												 AND SPBD.BenefitCategoryID = 66
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) 
											+ '-150)'
										 WHEN MAX(SPBD.BenefitOrdinalID) = 3 --SNF With all three tiers populated
											THEN @Days1To	--1rst Tier Day Range. eg: "(Days 1-20)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 66
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '), '
												+ CASE SPBD.OONBenefitTypeID  --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's is listed already)
													WHEN 1  --Coins
														THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 150 AS INT) AS VARCHAR)
															  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															  WHERE SPBD.ForecastID = @XForecastID
																AND SPBD.BenefitOrdinalID = 2 --2nd Tier
																AND SPBD.BenefitCategoryID = 66
																AND SPBD.IsBenefitYearCurrentYear = 0
																AND SPBD.IsLiveIndex = 1)
														+ '%'
													ELSE --Copay
														'$' + (SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
															   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															   WHERE SPBD.ForecastID = @XForecastID
																 AND SPBD.BenefitOrdinalID = 2 --2nd Tier
																 AND SPBD.BenefitCategoryID = 66
																 AND SPBD.IsBenefitYearCurrentYear = 0
																 AND SPBD.IsLiveIndex = 1)
												  END
												+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-50)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 66
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '-' + (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
														 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														 WHERE SPBD.ForecastID = @XForecastID
														   AND SPBD.BenefitOrdinalID = 2
														   AND SPBD.BenefitCategoryID = 66
														   AND SPBD.IsBenefitYearCurrentYear = 0
														   AND SPBD.IsLiveIndex = 1)
												+ '), ' 
												+ CASE SPBD.OONBenefitTypeID  --Putting in Cost Share Amount for 3rd Tier (since 2nd Tier's is listed already)
													WHEN 1  --Coins
														THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 150 AS INT) AS VARCHAR)
															  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															  WHERE SPBD.ForecastID = @XForecastID
																AND SPBD.BenefitOrdinalID = 3 --3rd Tier
																AND SPBD.BenefitCategoryID = 66
																AND SPBD.IsBenefitYearCurrentYear = 0
															    AND SPBD.IsLiveIndex = 1)
														+ '%'
													ELSE --Copay
														' $' +
															(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
															 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															 WHERE SPBD.ForecastID = @XForecastID
															   AND SPBD.BenefitOrdinalID = 3 --3rd Tier
															   AND SPBD.BenefitCategoryID = 66
															   AND SPBD.IsBenefitYearCurrentYear = 0
															   AND SPBD.IsLiveIndex = 1)
												  END
												+ ' (Days ' --3rd Tier Day Range. eg: "(Days 51-150)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 2
													 AND SPBD.BenefitCategoryID = 66
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '-150)'
										ELSE ''
									END
------------------------End IPTiers
								 WHEN BSC.ServiceCategoryCode = 'b.' AND SPBD.OONBenefitTypeID <> 1 THEN --SNF
									CASE WHEN MAX(SPBD.BenefitOrdinalID) = 1 --SNF With 1 tier populated
											THEN '(Days 1-100) '
										 WHEN MAX(SPBD.BenefitOrdinalID) = 2 --SNF With 2 tiers populated
											THEN @Days1To --1rst Tier Day Range. eg: "(Days 1-20)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 83
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
											+ '), '
											+ CASE SPBD.OONBenefitTypeID --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's already listed)
												WHEN 1  --Coins
													THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 100 AS INT) AS VARCHAR)
														  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														  WHERE SPBD.ForecastID = @XForecastID
															AND SPBD.BenefitOrdinalID = 2 --2nd Tier
															AND SPBD.BenefitCategoryID = 83
															AND SPBD.IsBenefitYearCurrentYear = 0
															AND SPBD.IsLiveIndex = 1)				
													+ '%' 
												ELSE --Copay
													'$' + 
														(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
														 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														 WHERE SPBD.ForecastID = @XForecastID
														   AND SPBD.BenefitOrdinalID = 2 --2nd Tier
														   AND SPBD.BenefitCategoryID = 83
														   AND SPBD.IsBenefitYearCurrentYear = 0
														   AND SPBD.IsLiveIndex = 1)
											  END
											+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-100)"
											+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
											   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
											   WHERE SPBD.ForecastID = @XForecastID
												 AND SPBD.BenefitOrdinalID = 1
												 AND SPBD.BenefitCategoryID = 83
												 AND SPBD.IsBenefitYearCurrentYear = 0
												 AND SPBD.IsLiveIndex = 1) 
											+ '-100)'
										 WHEN MAX(SPBD.BenefitOrdinalID) = 3 --SNF With all three tiers populated
											THEN @Days1To	--1rst Tier Day Range. eg: "(Days 1-20)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail spbd WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 83
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '), '
												+ CASE SPBD.OONBenefitTypeID  --Putting in Cost Share Amount for 2nd Tier (since 1rst Tier's is listed already)
													WHEN 1  --Coins
														THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 100 AS INT) AS VARCHAR)
															  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															  WHERE SPBD.ForecastID = @XForecastID
																AND SPBD.BenefitOrdinalID = 2 --2nd Tier
																AND SPBD.BenefitCategoryID = 83
																AND SPBD.IsBenefitYearCurrentYear = 0
																AND SPBD.IsLiveIndex = 1)
														+ '%'
													ELSE --Copay
														'$' + (SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
															   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															   WHERE SPBD.ForecastID = @XForecastID
																 AND SPBD.BenefitOrdinalID = 2 --2nd Tier
																 AND SPBD.BenefitCategoryID = 83
																 AND SPBD.IsBenefitYearCurrentYear = 0
																 AND SPBD.IsLiveIndex = 1)
													END
												+ ' (Days ' --2nd Tier Day Range. eg: "(Days 21-50)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 1
													 AND SPBD.BenefitCategoryID = 83
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '-' + (SELECT CAST(SPBD.OONDayRangeEnd  AS VARCHAR)
														 FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
														 WHERE SPBD.ForecastID = @XForecastID
														   AND SPBD.BenefitOrdinalID = 2
														   AND SPBD.BenefitCategoryID = 83
														   AND SPBD.IsBenefitYearCurrentYear = 0
														   AND SPBD.IsLiveIndex = 1)
												+ '), ' 
												+ CASE SPBD.OONBenefitTypeID  --Putting in Cost Share Amount for 3rd Tier (since 2nd Tier's is listed already)
													WHEN 1  --Coins
														THEN (SELECT CAST(CAST(SPBD.OONBenefitValue * 100 AS INT) AS VARCHAR)
															  FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
															  WHERE SPBD.ForecastID = @XForecastID
																AND SPBD.BenefitOrdinalID = 3 --3rd Tier
																AND SPBD.BenefitCategoryID = 83
																AND SPBD.IsBenefitYearCurrentYear = 0
																AND SPBD.IsLiveIndex = 1)
														+ '%'
													ELSE --Copay
														' $' +
															(SELECT CAST(CAST(SPBD.OONBenefitValue AS INT) AS VARCHAR)
															 FROM dbo.SavedPlanBenefitDetail spbd WITH (NOLOCK)
															 WHERE SPBD.ForecastID = @XForecastID
															   AND SPBD.BenefitOrdinalID = 3 --3rd Tier
															   AND SPBD.BenefitCategoryID = 83
															   AND SPBD.IsBenefitYearCurrentYear = 0
															   AND SPBD.IsLiveIndex = 1)
												  END
												+ ' (Days ' --3rd Tier Day Range. eg: "(Days 51-100)"
												+ (SELECT CAST(SPBD.OONDayRangeEnd + 1 AS VARCHAR)
												   FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
												   WHERE SPBD.ForecastID = @XForecastID
													 AND SPBD.BenefitOrdinalID = 2
													 AND SPBD.BenefitCategoryID = 83
													 AND SPBD.IsBenefitYearCurrentYear = 0
													 AND SPBD.IsLiveIndex = 1)
												+ '-100)'
										ELSE ''
									END
								ELSE ''
							END
							, '')--end of second Null check
							+
				            ISNULL( --Added to prevent a null value from showing up on the BPT. 
					            CASE
						            WHEN SPBD.OONBenefitTypeID IS NULL THEN ''
						            ELSE ': ' + CHAR(10)  --carriage return/line feed
							            + dbo.fnAppGetMABPTWS3BenefitDetail(@XForecastID,BSC.CostShareServiceCategoryCode,0,SPBD.OONBenefitTypeID)
										+ CASE WHEN BSC.CostShareServiceCategoryCode = 'a.1.' AND SPBD.OONBenefitTypeID = 5 
											   THEN ', ' + dbo.fnAppGetMABPTWS3BenefitDetail(@XForecastID,BSC.CostShareServiceCategoryCode,0,2) -- adding description for BenefitCategoryID = 102
											   ELSE ''
										  END
					            END
				            , '')
	END
					FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
					INNER JOIN dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
						ON SPBD.BenefitCategoryID = BC.BenefitCategoryID
					INNER JOIN dbo.LkpExtCMSBidServiceCategory BSC WITH (NOLOCK)
						ON BSC.BidServiceCategoryID = BC.BidServiceCatID
					INNER JOIN dbo.CalcBenefitProjection BBP WITH (NOLOCK)
						ON BBP.ForecastID = SPBD.ForecastID
						AND BBP.BenefitCategoryID = SPBD.BenefitCategoryID
						AND MARatingOptionID = 3 --Blended
					INNER JOIN dbo.SavedPlanHeader SPH WITH (NOLOCK) -- to get PlanTypeID
						ON SPH.ForecastID = SPBD.ForecastID
					INNER JOIN dbo.LkpProductType  CPT WITH (NOLOCK) -- to get ProductID
						ON SPH.PlanTypeID = CPT.ProductTypeID
					INNER JOIN dbo.LkpIntMOOPCategoryDetail MCD WITH (NOLOCK)
						ON MCD.ProductID = CPT.ProductMOOPID
						AND MCD.BenefitCategoryID = SPBD.BenefitCategoryID
					WHERE SPBD.ForecastID = @XForecastID
						AND MCD.MOOPCategoryID <> 5
						AND SPBD.IsBenefitYearCurrentYear = 0
						AND BBP.DualEligibleTypeID = 0 --NonDual
						AND BSC.BidServiceCategoryID = @XBidServiceCategoryID
						AND SPBD.INBenefitTypeID = @XBenefitTypeID
						AND SPBD.IsLiveIndex = 1
					GROUP BY 
						SPBD.ForecastID,
						SPBD.OONBenefitTypeID,
						BSC.CostShareServiceCategoryCode,
						BSC.ServiceCategory,
						BSC.SubServiceCategory,
						BSC.ServiceCategoryCode,
						MeasurementCoin,
						MeasurementAdmit,
						MeasurementCopay,
						SPBD.OOnDayRangeEnd,
						SPBD.OOnBenefitTypeID,
						SPBD.BenefitOrdinalID,
						SPBD.IsBenefitYearCurrentYear

			DECLARE @FINAL TABLE
				(

				 BenDesc VARCHAR(MAX)

				)

			INSERT INTO @FINAL
			SELECT BenDesc

				FROM @OONDesc
				WHERE BenDesc <> ''

		SELECT @ResultVar = BenDesc
			FROM @FINAL

		SET @ResultVar = LEFT(@ResultVar,LEN(@ResultVar)) --commenting out the minus one, nick -1)
	END

    RETURN @ResultVar
END
GO
