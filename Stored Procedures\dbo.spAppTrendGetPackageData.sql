SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
 
-- =============================================          
-- Author:  <PERSON>       
-- Create date: 02-26-2020    
-- Description: Get Data For Package Option Main Grid  
--          
--          
-- PARAMETERS:          
-- Input:            
        
-- TABLES:          
-- Read:          
-- Write:          
-- VIEWS: dbo.Trend_vwPackageOptionUI         
--          
-- FUNCTIONS:          
--            
-- STORED PROCS:           
         
    
-- $HISTORY             
    
-- ----------------------------------------------------------------------------------------------------------------------            
-- DATE   VERSION   CHANGES MADE                                                            DEVELOPER            
-- ----------------------------------------------------------------------------------------------------------------------            
-- 2020-Feb-26  1    Initial version.                                                       Kiran Kolachalama    
-- 2020-May-12  2    Added order by clause to show results in descending order              Florence Sesham      
-- ----------------------------------------------------------------------------------------------------------------------            
    
    
create PROCEDURE [dbo].[spAppTrendGetPackageData]
@LastUpdateByID  CHAR(13)
AS    
BEGIN    
 SELECT * FROM dbo.Trend_vwPackageOptionUI
 order by PackageOptionID DESC
END 


GO
