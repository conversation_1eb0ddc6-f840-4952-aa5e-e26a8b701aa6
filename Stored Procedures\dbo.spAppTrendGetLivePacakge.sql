SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


 -- =============================================          
-- Author:  Chhavi Sinha       
-- Create date: 26-03-2020    
-- Description:  Get Live Trend Pacakge    
--          
--          
-- PARAMETERS:          
-- Input:            
        
-- TABLES:          
-- Read:          
-- Write:          
-- VIEWS:          
--          
-- FUNCTIONS:          
--            
-- STORED PROCS:           
         
    
-- $HISTORY             
    
-- ----------------------------------------------------------------------------------------------------------------------            
-- DATE				VERSION		 CHANGES MADE                  DEVELOPER            
-- ----------------------------------------------------------------------------------------------------------------------            
-- 2020-Mar-26			1		Initial version.				Chhavi Sinha     
-- ----------------------------------------------------------------------------------------------------------------------            
           
CREATE PROCEDURE [dbo].[spAppTrendGetLivePacakge] @LastUpdateByID CHAR(7)     
AS     
  BEGIN 

      BEGIN TRY 
	  
	  SELECT PackageOptionName 
	  FROM trend_SavedPackageOption 
	  WHERE IsLivePackage = 1 GROUP BY PackageOptionName
	  
      END TRY     
    
      BEGIN CATCH     
          --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'      
          DECLARE @ErrorMessage NVARCHAR(4000);     
          DECLARE @ErrorSeverity INT;     
          DECLARE @ErrorState INT;     
          DECLARE @ErrorException NVARCHAR(4000);     
          DECLARE @errSrc      VARCHAR(max) =Isnull(Error_procedure(), 'SQL'),     
                  @currentdate DATETIME=Getdate()     
    
          SELECT @ErrorMessage = Error_message(),     
                 @ErrorSeverity = Error_severity(),     
                 @ErrorState = Error_state(),     
                 @ErrorException = 'Line Number :'     
                                   + Cast(Error_line() AS VARCHAR)     
                                   + ' .Error Severity :'     
                                   + Cast(@ErrorSeverity AS VARCHAR)     
                                   + ' .Error State :'     
                                   + Cast(@ErrorState AS VARCHAR)     
    
          RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)     
    
             
    
          ---Insert into app log for logging error------------------       
          EXEC Spappaddlogentry     
            @currentdate,     
            '',     
            'ERROR',     
            @errSrc,     
            @ErrorMessage,     
            @ErrorException,     
            @LastUpdateByID     
      END CATCH;     
  END 
GO
