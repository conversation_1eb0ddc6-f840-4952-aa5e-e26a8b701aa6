SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcPlanBase
--
-- CREATOR:			<PERSON> Galloway
--
-- CREATED DATE:	2020-JUL-15
--
-- DESCRIPTION:		This is a rewrite of the old spCalcPlanExperienceByBenefitCat.  
--					Responsible for aggregating the base cost and use data for all the plans assigned to the manual  
--					and experience cut of a forecast.  Experience cuts are aggregated to a risk provider level to aid  
--					in capitation projections.  Manual cuts are aggregated to the plan level.  
--		
-- PARAMETERS:
--  Input  :		@ForecastID			INT           
--					@MARatingOptionID	TINYINT          
--					@UserID				CHAR(7) 
--	
--  Output :		NONE
--
-- TABLES : 
--	Read :			SavedForecastSetup
--					SavedPlanInfo
--					SavedPlanINOONDistributionDetail
--					CalcSQSFactors  
--					SavedCUOverrideAdj 
--	
--  Write:			CalcPlanBaseBeforeAdj  
--					CalcPlanBase  
--					CalcPlanExperiencebyBenefitCat  
--					CalcPlanExperiencebyBenefitCatNonSQS  
--					CalcPlanExperiencebyBenefitCatAfterAdj  
--					CalcPlanExperiencebyBenefitCatBeforeAdj  
--
-- VIEWS: 
--	Read:			SavedPlanHeader
--
-- FUNCTIONS:		fnGetSafeDivisionResult
--
-- STORED PROCS:	spGetBaseData
--					spCalcSQSFactors
--					spAppAddLogEntry
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER   
-- 2007-JAN-29      1			Initial spCalcPlanExperienceByBenefitCat Version					Christian Cofie  
-- 2001-2020        2-49        See archive database for details									Various IT and Actuarial Developers  
-- 2020-OCT-27      50          Rewrite for Back End Alignment and Restructuring					Keith Galloway  
-- 2021-JUL-06		51			Code Change for MARatingOption issue								Alex Beruscha
-- 2021-OCT-12		52			Reprice error for 0 MM GrouperIDs impacting legacy tables			Alex Beruscha
-- 2024-OCT-03		53			Cost Share Basis:
--									Updated header
--									Pull in new fields from SavedDFClaims / spGetBaseData
--									Removed DE Pound override logic
--									Handle IsIncludeInCostShareBasis logic on 4x legacy tables
--									IN/OON distribution and cost factors apply only to 
--										IsIncludeInCostShare=1										Jake Lewis
-- 2025-JAN-29		54			Include new SurplusDeficit field for BPT requirements				Michael Manes
-- 2025-MAR-25		55			SurplusDeficit field order corrected in SELECT & INSERT				Michael Manes
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------       
CREATE PROCEDURE [dbo].[spCalcPlanBase]
    (
    @ForecastID       INT
   ,@MARatingOptionID TINYINT = 3   --1=Experience, 2=Manual, 3=Both            
   ,@UserID           CHAR(7))

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages           


        -- Declare Variables            
        DECLARE @PlanYearID                SMALLINT
               ,@LastDateTime              DATETIME
               ,@LocalError                INT
               ,@ReturnStatus              INT
               ,@ExperienceRating          TINYINT
               ,@ManualRating              TINYINT
               ,@BothRating                TINYINT
               ,@NonDualEligible           TINYINT
               ,@DualEligible              TINYINT
               ,@DualAndNonDualEligible    TINYINT
               ,@IncludeInCostShareBasis   BIT
               ,@ExcludeFromCostShareBasis BIT
               ,@XForecastID               INT
               ,@XMARatingOptionID         TINYINT
               ,@XUserID                   CHAR(7);


        -- Set Variables           
        SET @ExperienceRating = 1;
        SET @ManualRating = 2;
        SET @BothRating = 3;
        SET @LastDateTime = GETDATE ();
        SET @ReturnStatus = 0;
        SET @NonDualEligible = 0;
        SET @DualEligible = 1;
        SET @DualAndNonDualEligible = 2;
        SET @IncludeInCostShareBasis = 1;
        SET @ExcludeFromCostShareBasis = 0;
        SET @XForecastID = @ForecastID;
        SET @XMARatingOptionID = @MARatingOptionID;
        SET @XUserID = @UserID;
        SELECT      @PlanYearID = SPI.PlanYear
        FROM        dbo.SavedForecastSetup SFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanInfo SPI WITH (NOLOCK)
               ON SFS.PlanInfoID = SPI.PlanInfoID
        WHERE       SFS.ForecastID = @XForecastID;


        -- Get data from spGetBaseData
        DROP TABLE IF EXISTS #spGetBaseData;
        DROP TABLE IF EXISTS #BaseData;

        CREATE TABLE #spGetBaseData
            (MARatingOptionID                             TINYINT        NOT NULL
            ,DualEligibleTypeID                           TINYINT        NOT NULL
            ,[Provider]                                   VARCHAR(255)   NOT NULL
            ,BenefitCategoryID                            SMALLINT       NOT NULL
            ,BidServiceCatID                              SMALLINT       NOT NULL
            ,Paid                                         DECIMAL(19, 9) NULL
            ,MbrCS                                        DECIMAL(19, 9) NULL
            ,PymtReductionAmt                             DECIMAL(19, 9) NULL
            ,AdmitCnt                                     DECIMAL(19, 9) NULL
            ,UnitCnt                                      DECIMAL(19, 9) NULL
            ,EncounterMbrCS                               DECIMAL(19, 9) NULL
            ,EncounterAdmitCnt                            DECIMAL(19, 9) NULL
            ,EncounterUnitCnt                             DECIMAL(19, 9) NULL
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9) NULL
            ,DelegatedEncounterAdmitCnt                   DECIMAL(19, 9) NULL
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9) NULL
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9) NULL
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9) NULL
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9) NULL
            ,CapMSBs                                      DECIMAL(19, 9) NULL
            ,CapProviderRewards                           DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9) NULL
            ,PartDCapAdj                                  DECIMAL(19, 9) NULL
            ,PartDCapAdjDelegated                         DECIMAL(19, 9) NULL
            ,MSCapAdj                                     DECIMAL(19, 9) NULL
            ,MSCapAdjDelegated                            DECIMAL(19, 9) NULL
            ,SubCapAdj                                    DECIMAL(19, 9) NULL
            ,SubCapAdjExclude                             DECIMAL(19, 9) NULL
            ,MedicaidAdjPaid                              DECIMAL(19, 9) NULL
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9) NULL
            ,ImplicitMarginPaid                           DECIMAL(19, 9) NULL
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9) NULL
            ,AdditiveAdjPaid                              DECIMAL(19, 9) NULL
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9) NULL
            ,AdditiveAdjAdmits                            DECIMAL(19, 9) NULL
            ,AdditiveAdjUnits                             DECIMAL(19, 9) NULL
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9) NULL
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9) NULL
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9) NULL
            ,UCAdmitsAdj                                  DECIMAL(19, 9) NULL
            ,UCUnitsAdj                                   DECIMAL(19, 9) NULL
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9) NULL
            ,PartBRxRebatesQN                             DECIMAL(19, 9) NULL
            ,RelatedPartiesAdj                            DECIMAL(19, 9) NULL
            ,ProfitAdj                                    DECIMAL(19, 9) NULL
            ,MSBPaid                                      DECIMAL(19, 9) NULL
            ,MSBMbrCS                                     DECIMAL(19, 9) NULL
            ,MSBUnits                                     DECIMAL(19, 9) NULL
            ,MSBReductionCap                              DECIMAL(19, 9) NULL
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9) NULL
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9) NULL
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9) NULL
            ,MSBReductionQuality                          DECIMAL(19, 9) NULL
            ,MemberMonths                                 INT            NULL PRIMARY KEY (
                                                                              BenefitCategoryID
                                                                             ,DualEligibleTypeID
                                                                             ,[Provider]
                                                                             ,MARatingOptionID));

        CREATE TABLE #BaseData
            (MARatingOptionID                             TINYINT        NOT NULL
            ,DualEligibleTypeID                           TINYINT        NOT NULL
            ,[Provider]                                   VARCHAR(255)   NOT NULL
            ,BenefitCategoryID                            SMALLINT       NOT NULL
            ,BidServiceCatID                              SMALLINT       NOT NULL
            ,Paid                                         DECIMAL(19, 9) NULL
            ,MbrCS                                        DECIMAL(19, 9) NULL
            ,PymtReductionAmt                             DECIMAL(19, 9) NULL
            ,AdmitCnt                                     DECIMAL(19, 9) NULL
            ,UnitCnt                                      DECIMAL(19, 9) NULL
            ,EncounterMbrCS                               DECIMAL(19, 9) NULL
            ,EncounterAdmitCnt                            DECIMAL(19, 9) NULL
            ,EncounterUnitCnt                             DECIMAL(19, 9) NULL
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9) NULL
            ,DelegatedEncounterAdmitCnt                   DECIMAL(19, 9) NULL
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9) NULL
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9) NULL
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9) NULL
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9) NULL
            ,CapMSBs                                      DECIMAL(19, 9) NULL
            ,CapProviderRewards                           DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9) NULL
            ,PartDCapAdj                                  DECIMAL(19, 9) NULL
            ,PartDCapAdjDelegated                         DECIMAL(19, 9) NULL
            ,MSCapAdj                                     DECIMAL(19, 9) NULL
            ,MSCapAdjDelegated                            DECIMAL(19, 9) NULL
            ,SubCapAdj                                    DECIMAL(19, 9) NULL
            ,SubCapAdjExclude                             DECIMAL(19, 9) NULL
            ,MedicaidAdjPaid                              DECIMAL(19, 9) NULL
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9) NULL
            ,ImplicitMarginPaid                           DECIMAL(19, 9) NULL
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9) NULL
            ,AdditiveAdjPaid                              DECIMAL(19, 9) NULL
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9) NULL
            ,AdditiveAdjAdmits                            DECIMAL(19, 9) NULL
            ,AdditiveAdjUnits                             DECIMAL(19, 9) NULL
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9) NULL
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9) NULL
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9) NULL
            ,UCAdmitsAdj                                  DECIMAL(19, 9) NULL
            ,UCUnitsAdj                                   DECIMAL(19, 9) NULL
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9) NULL
            ,PartBRxRebatesQN                             DECIMAL(19, 9) NULL
            ,RelatedPartiesAdj                            DECIMAL(19, 9) NULL
            ,ProfitAdj                                    DECIMAL(19, 9) NULL
            ,MSBPaid                                      DECIMAL(19, 9) NULL
            ,MSBMbrCS                                     DECIMAL(19, 9) NULL
            ,MSBUnits                                     DECIMAL(19, 9) NULL
            ,MSBReductionCap                              DECIMAL(19, 9) NULL
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9) NULL
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9) NULL
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9) NULL
            ,MSBReductionQuality                          DECIMAL(19, 9) NULL
            ,MemberMonths                                 INT            NULL PRIMARY KEY (
                                                                              MARatingOptionID
                                                                             ,BenefitCategoryID
                                                                             ,DualEligibleTypeID
                                                                             ,[Provider]));
        -- Call spGetBaseData
        INSERT INTO #spGetBaseData
            (MARatingOptionID
            ,DualEligibleTypeID
            ,Provider
            ,BenefitCategoryID
            ,BidServiceCatID
            ,Paid
            ,MbrCS
            ,PymtReductionAmt
            ,AdmitCnt
            ,UnitCnt
            ,EncounterMbrCS
            ,EncounterAdmitCnt
            ,EncounterUnitCnt
            ,DelegatedEncounterMbrCS
            ,DelegatedEncounterAdmitCnt
            ,DelegatedEncounterUnitCnt
            ,CapDirectPayEPaidClaims
            ,CapDirectPayDEPaidClaims
            ,CapDirectPayBCAllocated
            ,CapDirectPayScenarioAllocated
            ,CapDirectPayBCAllocatedDelegated
            ,CapDirectPayScenarioAllocatedDelegated
            ,CapSurplusDeficitEPaidClaims
            ,CapSurplusDeficitDEPaidClaims
            ,CapSurplusDeficitBCAllocated
            ,CapSurplusDeficitScenarioAllocated
            ,CapSurplusDeficitBCAllocatedDelegated
            ,CapSurplusDeficitScenarioAllocatedDelegated
            ,CapMSBs
            ,CapProviderRewards
            ,OPBOtherNonHospitalBCAllocated
            ,OPBOtherNonHospitalScenarioAllocated
            ,OPBClaimsBuyDownBCAllocated
            ,OPBClaimsBuyDownScenarioAllocated
            ,OPBProviderClaimSettlementsBCAllocated
            ,OPBProviderClaimSettlementsScenarioAllocated
            ,OPBAccessFeesAndOtherBCAllocated
            ,OPBAccessFeesAndOtherScenarioAllocated
            ,PartDCapAdj
            ,PartDCapAdjDelegated
            ,MSCapAdj
            ,MSCapAdjDelegated
            ,SubCapAdj
            ,SubCapAdjExclude
            ,MedicaidAdjPaid
            ,MedicaidAdjMbrCS
            ,ImplicitMarginPaid
            ,ImplicitMarginMbrCS
            ,AdditiveAdjPaid
            ,AdditiveAdjMbrCS
            ,AdditiveAdjAdmits
            ,AdditiveAdjUnits
            ,ModelOfCareAdjPaid
            ,ModelOfCareAdjMbrCS
            ,ModelOfCareAdjUnits
            ,UCAdmitsAdj
            ,UCUnitsAdj
            ,PartBRxRebatesPharmacy
            ,PartBRxRebatesQN
            ,RelatedPartiesAdj
            ,ProfitAdj
            ,MSBPaid
            ,MSBMbrCS
            ,MSBUnits
            ,MSBReductionCap
            ,MSBReductionClaimsPaid
            ,MSBReductionClaimsMbrCS
            ,MSBReductionClaimsUnits
            ,MSBReductionQuality
            ,MemberMonths)
        EXEC dbo.spGetBaseData @XForecastID, @XMARatingOptionID;


        -- Set MA Rating Option values
        DROP TABLE IF EXISTS #MARatingOption;

        CREATE TABLE #MARatingOption
            (MARatingOptionID TINYINT);

        INSERT INTO #MARatingOption
            (MARatingOptionID)
        SELECT  CASE WHEN @XMARatingOptionID IN (1, 3) THEN 1 ELSE 0 END
        UNION
        SELECT  CASE WHEN @XMARatingOptionID IN (2, 3) THEN 2 ELSE 0 END;

        DELETE  FROM #MARatingOption WHERE  MARatingOptionID = 0;


        ------------------------------------------------------------------------------------------------------------            
        --                                              EXPERIENCE                                                --            
        ------------------------------------------------------------------------------------------------------------            
        IF (@XMARatingOptionID = 3) --Both
           OR   (@XMARatingOptionID = 1) --Experience

            BEGIN;

                WITH BaseData_CTE (MARatingOptionID, DualEligibleTypeID, [Provider], BenefitCategoryID, BidServiceCatID
                                  ,Paid, MbrCS, PymtReductionAmt, AdmitCnt, UnitCnt, EncounterMbrCS, EncounterAdmitCnt
                                  ,EncounterUnitCnt, DelegatedEncounterMbrCS, DelegatedEncounterAdmitCnt
                                  ,DelegatedEncounterUnitCnt, CapDirectPayEPaidClaims, CapDirectPayDEPaidClaims
                                  ,CapDirectPayBCAllocated, CapDirectPayScenarioAllocated
                                  ,CapDirectPayBCAllocatedDelegated, CapDirectPayScenarioAllocatedDelegated
                                  ,CapSurplusDeficitEPaidClaims, CapSurplusDeficitDEPaidClaims
                                  ,CapSurplusDeficitBCAllocated, CapSurplusDeficitScenarioAllocated
                                  ,CapSurplusDeficitBCAllocatedDelegated, CapSurplusDeficitScenarioAllocatedDelegated
                                  ,CapMSBs, CapProviderRewards, OPBOtherNonHospitalBCAllocated
                                  ,OPBOtherNonHospitalScenarioAllocated, OPBClaimsBuyDownBCAllocated
                                  ,OPBClaimsBuyDownScenarioAllocated, OPBProviderClaimSettlementsBCAllocated
                                  ,OPBProviderClaimSettlementsScenarioAllocated, OPBAccessFeesAndOtherBCAllocated
                                  ,OPBAccessFeesAndOtherScenarioAllocated, PartDCapAdj, PartDCapAdjDelegated, MSCapAdj
                                  ,MSCapAdjDelegated, SubCapAdj, SubCapAdjExclude, MedicaidAdjPaid, MedicaidAdjMbrCS
                                  ,ImplicitMarginPaid, ImplicitMarginMbrCS, AdditiveAdjPaid, AdditiveAdjMbrCS
                                  ,AdditiveAdjAdmits, AdditiveAdjUnits, ModelOfCareAdjPaid, ModelOfCareAdjMbrCS
                                  ,ModelOfCareAdjUnits, UCAdmitsAdj, UCUnitsAdj, PartBRxRebatesPharmacy
                                  ,PartBRxRebatesQN, RelatedPartiesAdj, ProfitAdj, MSBPaid, MSBMbrCS, MSBUnits
                                  ,MSBReductionCap, MSBReductionClaimsPaid, MSBReductionClaimsMbrCS
                                  ,MSBReductionClaimsUnits, MSBReductionQuality, MemberMonths)
                AS (SELECT  bd.MARatingOptionID
                           ,bd.DualEligibleTypeID
                           ,bd.[Provider]
                           ,bd.BenefitCategoryID
                           ,bd.BidServiceCatID
                           ,bd.Paid
                           ,bd.MbrCS
                           ,bd.PymtReductionAmt
                           ,bd.AdmitCnt
                           ,bd.UnitCnt
                           ,bd.EncounterMbrCS
                           ,bd.EncounterAdmitCnt
                           ,bd.EncounterUnitCnt
                           ,bd.DelegatedEncounterMbrCS
                           ,bd.DelegatedEncounterAdmitCnt
                           ,bd.DelegatedEncounterUnitCnt
                           ,bd.CapDirectPayEPaidClaims
                           ,bd.CapDirectPayDEPaidClaims
                           ,bd.CapDirectPayBCAllocated
                           ,bd.CapDirectPayScenarioAllocated
                           ,bd.CapDirectPayBCAllocatedDelegated
                           ,bd.CapDirectPayScenarioAllocatedDelegated
                           ,bd.CapSurplusDeficitEPaidClaims
                           ,bd.CapSurplusDeficitDEPaidClaims
                           ,bd.CapSurplusDeficitBCAllocated
                           ,bd.CapSurplusDeficitScenarioAllocated
                           ,bd.CapSurplusDeficitBCAllocatedDelegated
                           ,bd.CapSurplusDeficitScenarioAllocatedDelegated
                           ,bd.CapMSBs
                           ,bd.CapProviderRewards
                           ,bd.OPBOtherNonHospitalBCAllocated
                           ,bd.OPBOtherNonHospitalScenarioAllocated
                           ,bd.OPBClaimsBuyDownBCAllocated
                           ,bd.OPBClaimsBuyDownScenarioAllocated
                           ,bd.OPBProviderClaimSettlementsBCAllocated
                           ,bd.OPBProviderClaimSettlementsScenarioAllocated
                           ,bd.OPBAccessFeesAndOtherBCAllocated
                           ,bd.OPBAccessFeesAndOtherScenarioAllocated
                           ,bd.PartDCapAdj
                           ,bd.PartDCapAdjDelegated
                           ,bd.MSCapAdj
                           ,bd.MSCapAdjDelegated
                           ,bd.SubCapAdj
                           ,bd.SubCapAdjExclude
                           ,bd.MedicaidAdjPaid
                           ,bd.MedicaidAdjMbrCS
                           ,bd.ImplicitMarginPaid
                           ,bd.ImplicitMarginMbrCS
                           ,bd.AdditiveAdjPaid
                           ,bd.AdditiveAdjMbrCS
                           ,bd.AdditiveAdjAdmits
                           ,bd.AdditiveAdjUnits
                           ,bd.ModelOfCareAdjPaid
                           ,bd.ModelOfCareAdjMbrCS
                           ,bd.ModelOfCareAdjUnits
                           ,bd.UCAdmitsAdj
                           ,bd.UCUnitsAdj
                           ,bd.PartBRxRebatesPharmacy
                           ,bd.PartBRxRebatesQN
                           ,bd.RelatedPartiesAdj
                           ,bd.ProfitAdj
                           ,bd.MSBPaid
                           ,bd.MSBMbrCS
                           ,bd.MSBUnits
                           ,bd.MSBReductionCap
                           ,bd.MSBReductionClaimsPaid
                           ,bd.MSBReductionClaimsMbrCS
                           ,bd.MSBReductionClaimsUnits
                           ,bd.MSBReductionQuality
                           ,bd.MemberMonths
                    FROM    #spGetBaseData bd
                    WHERE   MARatingOptionID = @ExperienceRating)
                INSERT INTO #BaseData
                    (MARatingOptionID
                    ,DualEligibleTypeID
                    ,Provider
                    ,BenefitCategoryID
                    ,BidServiceCatID
                    ,Paid
                    ,MbrCS
                    ,PymtReductionAmt
                    ,AdmitCnt
                    ,UnitCnt
                    ,EncounterMbrCS
                    ,EncounterAdmitCnt
                    ,EncounterUnitCnt
                    ,DelegatedEncounterMbrCS
                    ,DelegatedEncounterAdmitCnt
                    ,DelegatedEncounterUnitCnt
                    ,CapDirectPayEPaidClaims
                    ,CapDirectPayDEPaidClaims
                    ,CapDirectPayBCAllocated
                    ,CapDirectPayScenarioAllocated
                    ,CapDirectPayBCAllocatedDelegated
                    ,CapDirectPayScenarioAllocatedDelegated
                    ,CapSurplusDeficitEPaidClaims
                    ,CapSurplusDeficitDEPaidClaims
                    ,CapSurplusDeficitBCAllocated
                    ,CapSurplusDeficitScenarioAllocated
                    ,CapSurplusDeficitBCAllocatedDelegated
                    ,CapSurplusDeficitScenarioAllocatedDelegated
                    ,CapMSBs
                    ,CapProviderRewards
                    ,OPBOtherNonHospitalBCAllocated
                    ,OPBOtherNonHospitalScenarioAllocated
                    ,OPBClaimsBuyDownBCAllocated
                    ,OPBClaimsBuyDownScenarioAllocated
                    ,OPBProviderClaimSettlementsBCAllocated
                    ,OPBProviderClaimSettlementsScenarioAllocated
                    ,OPBAccessFeesAndOtherBCAllocated
                    ,OPBAccessFeesAndOtherScenarioAllocated
                    ,PartDCapAdj
                    ,PartDCapAdjDelegated
                    ,MSCapAdj
                    ,MSCapAdjDelegated
                    ,SubCapAdj
                    ,SubCapAdjExclude
                    ,MedicaidAdjPaid
                    ,MedicaidAdjMbrCS
                    ,ImplicitMarginPaid
                    ,ImplicitMarginMbrCS
                    ,AdditiveAdjPaid
                    ,AdditiveAdjMbrCS
                    ,AdditiveAdjAdmits
                    ,AdditiveAdjUnits
                    ,ModelOfCareAdjPaid
                    ,ModelOfCareAdjMbrCS
                    ,ModelOfCareAdjUnits
                    ,UCAdmitsAdj
                    ,UCUnitsAdj
                    ,PartBRxRebatesPharmacy
                    ,PartBRxRebatesQN
                    ,RelatedPartiesAdj
                    ,ProfitAdj
                    ,MSBPaid
                    ,MSBMbrCS
                    ,MSBUnits
                    ,MSBReductionCap
                    ,MSBReductionClaimsPaid
                    ,MSBReductionClaimsMbrCS
                    ,MSBReductionClaimsUnits
                    ,MSBReductionQuality
                    ,MemberMonths)
                SELECT  cte.MARatingOptionID
                       ,cte.DualEligibleTypeID
                       ,cte.[Provider]
                       ,cte.BenefitCategoryID
                       ,cte.BidServiceCatID
                       ,cte.Paid
                       ,cte.MbrCS
                       ,cte.PymtReductionAmt
                       ,cte.AdmitCnt
                       ,cte.UnitCnt
                       ,cte.EncounterMbrCS
                       ,cte.EncounterAdmitCnt
                       ,cte.EncounterUnitCnt
                       ,cte.DelegatedEncounterMbrCS
                       ,cte.DelegatedEncounterAdmitCnt
                       ,cte.DelegatedEncounterUnitCnt
                       ,cte.CapDirectPayEPaidClaims
                       ,cte.CapDirectPayDEPaidClaims
                       ,cte.CapDirectPayBCAllocated
                       ,cte.CapDirectPayScenarioAllocated
                       ,cte.CapDirectPayBCAllocatedDelegated
                       ,cte.CapDirectPayScenarioAllocatedDelegated
                       ,cte.CapSurplusDeficitEPaidClaims
                       ,cte.CapSurplusDeficitDEPaidClaims
                       ,cte.CapSurplusDeficitBCAllocated
                       ,cte.CapSurplusDeficitScenarioAllocated
                       ,cte.CapSurplusDeficitBCAllocatedDelegated
                       ,cte.CapSurplusDeficitScenarioAllocatedDelegated
                       ,cte.CapMSBs
                       ,cte.CapProviderRewards
                       ,cte.OPBOtherNonHospitalBCAllocated
                       ,cte.OPBOtherNonHospitalScenarioAllocated
                       ,cte.OPBClaimsBuyDownBCAllocated
                       ,cte.OPBClaimsBuyDownScenarioAllocated
                       ,cte.OPBProviderClaimSettlementsBCAllocated
                       ,cte.OPBProviderClaimSettlementsScenarioAllocated
                       ,cte.OPBAccessFeesAndOtherBCAllocated
                       ,cte.OPBAccessFeesAndOtherScenarioAllocated
                       ,cte.PartDCapAdj
                       ,cte.PartDCapAdjDelegated
                       ,cte.MSCapAdj
                       ,cte.MSCapAdjDelegated
                       ,cte.SubCapAdj
                       ,cte.SubCapAdjExclude
                       ,cte.MedicaidAdjPaid
                       ,cte.MedicaidAdjMbrCS
                       ,cte.ImplicitMarginPaid
                       ,cte.ImplicitMarginMbrCS
                       ,cte.AdditiveAdjPaid
                       ,cte.AdditiveAdjMbrCS
                       ,cte.AdditiveAdjAdmits
                       ,cte.AdditiveAdjUnits
                       ,cte.ModelOfCareAdjPaid
                       ,cte.ModelOfCareAdjMbrCS
                       ,cte.ModelOfCareAdjUnits
                       ,cte.UCAdmitsAdj
                       ,cte.UCUnitsAdj
                       ,cte.PartBRxRebatesPharmacy
                       ,cte.PartBRxRebatesQN
                       ,cte.RelatedPartiesAdj
                       ,cte.ProfitAdj
                       ,cte.MSBPaid
                       ,cte.MSBMbrCS
                       ,cte.MSBUnits
                       ,cte.MSBReductionCap
                       ,cte.MSBReductionClaimsPaid
                       ,cte.MSBReductionClaimsMbrCS
                       ,cte.MSBReductionClaimsUnits
                       ,cte.MSBReductionQuality
                       ,cte.MemberMonths
                FROM    BaseData_CTE cte;

            END;


        ------------------------------------------------------------------------------------------------------------            
        --                                              MANUAL                                                    --            
        ------------------------------------------------------------------------------------------------------------          
        IF (@XMARatingOptionID = 3) --Both
           OR   (@XMARatingOptionID = 2) --Manual

            BEGIN;

                WITH BaseData_CTE (MARatingOptionID, DualEligibleTypeID, [Provider], BenefitCategoryID, BidServiceCatID
                                  ,Paid, MbrCS, PymtReductionAmt, AdmitCnt, UnitCnt, EncounterMbrCS, EncounterAdmitCnt
                                  ,EncounterUnitCnt, DelegatedEncounterMbrCS, DelegatedEncounterAdmitCnt
                                  ,DelegatedEncounterUnitCnt, CapDirectPayEPaidClaims, CapDirectPayDEPaidClaims
                                  ,CapDirectPayBCAllocated, CapDirectPayScenarioAllocated
                                  ,CapDirectPayBCAllocatedDelegated, CapDirectPayScenarioAllocatedDelegated
                                  ,CapSurplusDeficitEPaidClaims, CapSurplusDeficitDEPaidClaims
                                  ,CapSurplusDeficitBCAllocated, CapSurplusDeficitScenarioAllocated
                                  ,CapSurplusDeficitBCAllocatedDelegated, CapSurplusDeficitScenarioAllocatedDelegated
                                  ,CapMSBs, CapProviderRewards, OPBOtherNonHospitalBCAllocated
                                  ,OPBOtherNonHospitalScenarioAllocated, OPBClaimsBuyDownBCAllocated
                                  ,OPBClaimsBuyDownScenarioAllocated, OPBProviderClaimSettlementsBCAllocated
                                  ,OPBProviderClaimSettlementsScenarioAllocated, OPBAccessFeesAndOtherBCAllocated
                                  ,OPBAccessFeesAndOtherScenarioAllocated, PartDCapAdj, PartDCapAdjDelegated, MSCapAdj
                                  ,MSCapAdjDelegated, SubCapAdj, SubCapAdjExclude, MedicaidAdjPaid, MedicaidAdjMbrCS
                                  ,ImplicitMarginPaid, ImplicitMarginMbrCS, AdditiveAdjPaid, AdditiveAdjMbrCS
                                  ,AdditiveAdjAdmits, AdditiveAdjUnits, ModelOfCareAdjPaid, ModelOfCareAdjMbrCS
                                  ,ModelOfCareAdjUnits, UCAdmitsAdj, UCUnitsAdj, PartBRxRebatesPharmacy
                                  ,PartBRxRebatesQN, RelatedPartiesAdj, ProfitAdj, MSBPaid, MSBMbrCS, MSBUnits
                                  ,MSBReductionCap, MSBReductionClaimsPaid, MSBReductionClaimsMbrCS
                                  ,MSBReductionClaimsUnits, MSBReductionQuality, MemberMonths)
                AS (SELECT  bd.MARatingOptionID
                           ,bd.DualEligibleTypeID
                           ,bd.[Provider]
                           ,bd.BenefitCategoryID
                           ,bd.BidServiceCatID
                           ,bd.Paid
                           ,bd.MbrCS
                           ,bd.PymtReductionAmt
                           ,bd.AdmitCnt
                           ,bd.UnitCnt
                           ,bd.EncounterMbrCS
                           ,bd.EncounterAdmitCnt
                           ,bd.EncounterUnitCnt
                           ,bd.DelegatedEncounterMbrCS
                           ,bd.DelegatedEncounterAdmitCnt
                           ,bd.DelegatedEncounterUnitCnt
                           ,bd.CapDirectPayEPaidClaims
                           ,bd.CapDirectPayDEPaidClaims
                           ,bd.CapDirectPayBCAllocated
                           ,bd.CapDirectPayScenarioAllocated
                           ,bd.CapDirectPayBCAllocatedDelegated
                           ,bd.CapDirectPayScenarioAllocatedDelegated
                           ,bd.CapSurplusDeficitEPaidClaims
                           ,bd.CapSurplusDeficitDEPaidClaims
                           ,bd.CapSurplusDeficitBCAllocated
                           ,bd.CapSurplusDeficitScenarioAllocated
                           ,bd.CapSurplusDeficitBCAllocatedDelegated
                           ,bd.CapSurplusDeficitScenarioAllocatedDelegated
                           ,bd.CapMSBs
                           ,bd.CapProviderRewards
                           ,bd.OPBOtherNonHospitalBCAllocated
                           ,bd.OPBOtherNonHospitalScenarioAllocated
                           ,bd.OPBClaimsBuyDownBCAllocated
                           ,bd.OPBClaimsBuyDownScenarioAllocated
                           ,bd.OPBProviderClaimSettlementsBCAllocated
                           ,bd.OPBProviderClaimSettlementsScenarioAllocated
                           ,bd.OPBAccessFeesAndOtherBCAllocated
                           ,bd.OPBAccessFeesAndOtherScenarioAllocated
                           ,bd.PartDCapAdj
                           ,bd.PartDCapAdjDelegated
                           ,bd.MSCapAdj
                           ,bd.MSCapAdjDelegated
                           ,bd.SubCapAdj
                           ,bd.SubCapAdjExclude
                           ,bd.MedicaidAdjPaid
                           ,bd.MedicaidAdjMbrCS
                           ,bd.ImplicitMarginPaid
                           ,bd.ImplicitMarginMbrCS
                           ,bd.AdditiveAdjPaid
                           ,bd.AdditiveAdjMbrCS
                           ,bd.AdditiveAdjAdmits
                           ,bd.AdditiveAdjUnits
                           ,bd.ModelOfCareAdjPaid
                           ,bd.ModelOfCareAdjMbrCS
                           ,bd.ModelOfCareAdjUnits
                           ,bd.UCAdmitsAdj
                           ,bd.UCUnitsAdj
                           ,bd.PartBRxRebatesPharmacy
                           ,bd.PartBRxRebatesQN
                           ,bd.RelatedPartiesAdj
                           ,bd.ProfitAdj
                           ,bd.MSBPaid
                           ,bd.MSBMbrCS
                           ,bd.MSBUnits
                           ,bd.MSBReductionCap
                           ,bd.MSBReductionClaimsPaid
                           ,bd.MSBReductionClaimsMbrCS
                           ,bd.MSBReductionClaimsUnits
                           ,bd.MSBReductionQuality
                           ,bd.MemberMonths
                    FROM    #spGetBaseData bd
                    WHERE   MARatingOptionID = @ManualRating)
                INSERT INTO #BaseData
                    (MARatingOptionID
                    ,DualEligibleTypeID
                    ,Provider
                    ,BenefitCategoryID
                    ,BidServiceCatID
                    ,Paid
                    ,MbrCS
                    ,PymtReductionAmt
                    ,AdmitCnt
                    ,UnitCnt
                    ,EncounterMbrCS
                    ,EncounterAdmitCnt
                    ,EncounterUnitCnt
                    ,DelegatedEncounterMbrCS
                    ,DelegatedEncounterAdmitCnt
                    ,DelegatedEncounterUnitCnt
                    ,CapDirectPayEPaidClaims
                    ,CapDirectPayDEPaidClaims
                    ,CapDirectPayBCAllocated
                    ,CapDirectPayScenarioAllocated
                    ,CapDirectPayBCAllocatedDelegated
                    ,CapDirectPayScenarioAllocatedDelegated
                    ,CapSurplusDeficitEPaidClaims
                    ,CapSurplusDeficitDEPaidClaims
                    ,CapSurplusDeficitBCAllocated
                    ,CapSurplusDeficitScenarioAllocated
                    ,CapSurplusDeficitBCAllocatedDelegated
                    ,CapSurplusDeficitScenarioAllocatedDelegated
                    ,CapMSBs
                    ,CapProviderRewards
                    ,OPBOtherNonHospitalBCAllocated
                    ,OPBOtherNonHospitalScenarioAllocated
                    ,OPBClaimsBuyDownBCAllocated
                    ,OPBClaimsBuyDownScenarioAllocated
                    ,OPBProviderClaimSettlementsBCAllocated
                    ,OPBProviderClaimSettlementsScenarioAllocated
                    ,OPBAccessFeesAndOtherBCAllocated
                    ,OPBAccessFeesAndOtherScenarioAllocated
                    ,PartDCapAdj
                    ,PartDCapAdjDelegated
                    ,MSCapAdj
                    ,MSCapAdjDelegated
                    ,SubCapAdj
                    ,SubCapAdjExclude
                    ,MedicaidAdjPaid
                    ,MedicaidAdjMbrCS
                    ,ImplicitMarginPaid
                    ,ImplicitMarginMbrCS
                    ,AdditiveAdjPaid
                    ,AdditiveAdjMbrCS
                    ,AdditiveAdjAdmits
                    ,AdditiveAdjUnits
                    ,ModelOfCareAdjPaid
                    ,ModelOfCareAdjMbrCS
                    ,ModelOfCareAdjUnits
                    ,UCAdmitsAdj
                    ,UCUnitsAdj
                    ,PartBRxRebatesPharmacy
                    ,PartBRxRebatesQN
                    ,RelatedPartiesAdj
                    ,ProfitAdj
                    ,MSBPaid
                    ,MSBMbrCS
                    ,MSBUnits
                    ,MSBReductionCap
                    ,MSBReductionClaimsPaid
                    ,MSBReductionClaimsMbrCS
                    ,MSBReductionClaimsUnits
                    ,MSBReductionQuality
                    ,MemberMonths)
                SELECT  cte.MARatingOptionID
                       ,cte.DualEligibleTypeID
                       ,cte.[Provider]
                       ,cte.BenefitCategoryID
                       ,cte.BidServiceCatID
                       ,cte.Paid
                       ,cte.MbrCS
                       ,cte.PymtReductionAmt
                       ,cte.AdmitCnt
                       ,cte.UnitCnt
                       ,cte.EncounterMbrCS
                       ,cte.EncounterAdmitCnt
                       ,cte.EncounterUnitCnt
                       ,cte.DelegatedEncounterMbrCS
                       ,cte.DelegatedEncounterAdmitCnt
                       ,cte.DelegatedEncounterUnitCnt
                       ,cte.CapDirectPayEPaidClaims
                       ,cte.CapDirectPayDEPaidClaims
                       ,cte.CapDirectPayBCAllocated
                       ,cte.CapDirectPayScenarioAllocated
                       ,cte.CapDirectPayBCAllocatedDelegated
                       ,cte.CapDirectPayScenarioAllocatedDelegated
                       ,cte.CapSurplusDeficitEPaidClaims
                       ,cte.CapSurplusDeficitDEPaidClaims
                       ,cte.CapSurplusDeficitBCAllocated
                       ,cte.CapSurplusDeficitScenarioAllocated
                       ,cte.CapSurplusDeficitBCAllocatedDelegated
                       ,cte.CapSurplusDeficitScenarioAllocatedDelegated
                       ,cte.CapMSBs
                       ,cte.CapProviderRewards
                       ,cte.OPBOtherNonHospitalBCAllocated
                       ,cte.OPBOtherNonHospitalScenarioAllocated
                       ,cte.OPBClaimsBuyDownBCAllocated
                       ,cte.OPBClaimsBuyDownScenarioAllocated
                       ,cte.OPBProviderClaimSettlementsBCAllocated
                       ,cte.OPBProviderClaimSettlementsScenarioAllocated
                       ,cte.OPBAccessFeesAndOtherBCAllocated
                       ,cte.OPBAccessFeesAndOtherScenarioAllocated
                       ,cte.PartDCapAdj
                       ,cte.PartDCapAdjDelegated
                       ,cte.MSCapAdj
                       ,cte.MSCapAdjDelegated
                       ,cte.SubCapAdj
                       ,cte.SubCapAdjExclude
                       ,cte.MedicaidAdjPaid
                       ,cte.MedicaidAdjMbrCS
                       ,cte.ImplicitMarginPaid
                       ,cte.ImplicitMarginMbrCS
                       ,cte.AdditiveAdjPaid
                       ,cte.AdditiveAdjMbrCS
                       ,cte.AdditiveAdjAdmits
                       ,cte.AdditiveAdjUnits
                       ,cte.ModelOfCareAdjPaid
                       ,cte.ModelOfCareAdjMbrCS
                       ,cte.ModelOfCareAdjUnits
                       ,cte.UCAdmitsAdj
                       ,cte.UCUnitsAdj
                       ,cte.PartBRxRebatesPharmacy
                       ,cte.PartBRxRebatesQN
                       ,cte.RelatedPartiesAdj
                       ,cte.ProfitAdj
                       ,cte.MSBPaid
                       ,cte.MSBMbrCS
                       ,cte.MSBUnits
                       ,cte.MSBReductionCap
                       ,cte.MSBReductionClaimsPaid
                       ,cte.MSBReductionClaimsMbrCS
                       ,cte.MSBReductionClaimsUnits
                       ,cte.MSBReductionQuality
                       ,cte.MemberMonths
                FROM    BaseData_CTE cte;
            END;


        ------------------------------------------------------------------------------------------------------------            
        --                                         TABLE POPULATION                                               --            
        ------------------------------------------------------------------------------------------------------------     

        BEGIN TRY

            -------------------------------------------------------------------------------------------------------------        
            ---  VBC TABLE POPULATION.                                                                                 --              
            ---  STEP 1. Start with unadjusted values in dbo.CalcPlanBaseBeforeAdj                                     --              
            -------------------------------------------------------------------------------------------------------------              
            --  IsInNetwork = 1 ; IsNetworkOnly = 0              
            --  Base Allowed x INCostFactor x INDistribution.                
            --  We assume 100% of Related Party Adj, Profit Adj, and Rx Rebates are in-network. 
            --  The following fields are excluded from the cost share basis, and are treated as in-network: 
            --		CapDirectPayBCAllocated
            --		CapDirectPayScenarioAllocated
            --		CapSurplusDeficitBCAllocated
            --		CapSurplusDeficitScenarioAllocated
            --		CapProviderRewards
            --		PartDCapAdj
            --		MSCapAdj
            --		SubCapAdjExclude

            BEGIN TRANSACTION spCalcPlanBase;

            BEGIN

                IF @XMARatingOptionID = @BothRating
                    BEGIN
                        DELETE  FROM dbo.CalcPlanBaseBeforeAdj WHERE    ForecastID = @XForecastID;
                    END;

                IF (@XMARatingOptionID = @ExperienceRating
                    OR  @XMARatingOptionID = @ManualRating)
                    BEGIN
                        DELETE  FROM dbo.CalcPlanBaseBeforeAdj
                        WHERE   ForecastID = @XForecastID
                                AND MARatingOptionID = @XMARatingOptionID;
                    END;

                INSERT INTO dbo.CalcPlanBaseBeforeAdj
                    (ForecastID
                    ,MARatingOptionID
                    ,[Provider]
                    ,IsInNetwork
                    ,IsNetworkOnly
                    ,BenefitCategoryID
                    ,DualEligibleTypeID
                    ,Paid
                    ,MbrCS
                    ,PymtReductionAmt
                    ,AdmitCnt
                    ,UnitCnt
                    ,EncounterMbrCS
                    ,EncounterAdmitCnt
                    ,EncounterUnitCnt
                    ,DelegatedEncounterMbrCS
                    ,DelegatedEncounterAdmitCnt
                    ,DelegatedEncounterUnitCnt
                    ,CapDirectPayEPaidClaims
                    ,CapDirectPayDEPaidClaims
                    ,CapDirectPayBCAllocated
                    ,CapDirectPayScenarioAllocated
                    ,CapDirectPayBCAllocatedDelegated
                    ,CapDirectPayScenarioAllocatedDelegated
                    ,CapSurplusDeficitEPaidClaims
                    ,CapSurplusDeficitDEPaidClaims
                    ,CapSurplusDeficitBCAllocated
                    ,CapSurplusDeficitScenarioAllocated
                    ,CapSurplusDeficitBCAllocatedDelegated
                    ,CapSurplusDeficitScenarioAllocatedDelegated
                    ,CapMSBs
                    ,CapProviderRewards
                    ,OPBOtherNonHospitalBCAllocated
                    ,OPBOtherNonHospitalScenarioAllocated
                    ,OPBClaimsBuyDownBCAllocated
                    ,OPBClaimsBuyDownScenarioAllocated
                    ,OPBProviderClaimSettlementsBCAllocated
                    ,OPBProviderClaimSettlementsScenarioAllocated
                    ,OPBAccessFeesAndOtherBCAllocated
                    ,OPBAccessFeesAndOtherScenarioAllocated
                    ,PartDCapAdj
                    ,PartDCapAdjDelegated
                    ,MSCapAdj
                    ,MSCapAdjDelegated
                    ,SubCapAdj
                    ,SubCapAdjExclude
                    ,MedicaidAdjPaid
                    ,MedicaidAdjMbrCS
                    ,ImplicitMarginPaid
                    ,ImplicitMarginMbrCS
                    ,AdditiveAdjPaid
                    ,AdditiveAdjMbrCS
                    ,AdditiveAdjAdmits
                    ,AdditiveAdjUnits
                    ,ModelOfCareAdjPaid
                    ,ModelOfCareAdjMbrCS
                    ,ModelOfCareAdjUnits
                    ,UCAdmitsAdj
                    ,UCUnitsAdj
                    ,PartBRxRebatesPharmacy
                    ,PartBRxRebatesQN
                    ,RelatedPartiesAdj
                    ,ProfitAdj
                    ,MSBPaid
                    ,MSBMbrCS
                    ,MSBUnits
                    ,MSBReductionCap
                    ,MSBReductionClaimsPaid
                    ,MSBReductionClaimsMbrCS
                    ,MSBReductionClaimsUnits
                    ,MSBReductionQuality
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      DISTINCT
                            @XForecastID ForecastID
                           ,DFD.MARatingOptionID
                           ,DFD.[Provider]
                           ,1 IsInNetwork
                           ,0 IsNetworkOnly
                           ,DFD.BenefitCategoryID
                           ,DFD.DualEligibleTypeID
                           ,ISNULL ((DFD.Paid * PD.INDistributionPercent * PD.INCostFactor), 0) Paid
                           ,ISNULL ((DFD.MbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) MbrCS
                           ,ISNULL ((DFD.PymtReductionAmt * PD.INDistributionPercent * PD.INCostFactor), 0) PymtReductionAmt
                           ,ISNULL ((DFD.AdmitCnt * PD.INDistributionPercent), 0) AdmitCnt
                           ,ISNULL ((DFD.UnitCnt * PD.INDistributionPercent), 0) UnitCnt
                           ,ISNULL ((DFD.EncounterMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) EncounterMbrCS
                           ,ISNULL ((DFD.EncounterAdmitCnt * PD.INDistributionPercent), 0) EncounterAdmitCnt
                           ,ISNULL ((DFD.EncounterUnitCnt * PD.INDistributionPercent), 0) EncounterUnitCnt
                           ,ISNULL ((DFD.DelegatedEncounterMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) DelegatedEncounterMbrCS
                           ,ISNULL ((DFD.DelegatedEncounterAdmitCnt * PD.INDistributionPercent), 0) DelegatedEncounterAdmitCnt
                           ,ISNULL ((DFD.DelegatedEncounterUnitCnt * PD.INDistributionPercent), 0) DelegatedEncounterUnitCnt
                           ,ISNULL ((DFD.CapDirectPayEPaidClaims * PD.INDistributionPercent * PD.INCostFactor), 0) CapDirectPayEPaidClaims
                           ,ISNULL ((DFD.CapDirectPayDEPaidClaims * PD.INDistributionPercent * PD.INCostFactor), 0) CapDirectPayDEPaidClaims
                           ,ISNULL ((DFD.CapDirectPayBCAllocated), 0) CapDirectPayBCAllocated
                           ,ISNULL ((DFD.CapDirectPayScenarioAllocated), 0) CapDirectPayScenarioAllocated
                           ,ISNULL ((DFD.CapDirectPayBCAllocatedDelegated * PD.INDistributionPercent * PD.INCostFactor), 0) CapDirectPayBCAllocatedDelegated
                           ,ISNULL ((DFD.CapDirectPayScenarioAllocatedDelegated * PD.INDistributionPercent * PD.INCostFactor), 0) CapDirectPayScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapSurplusDeficitEPaidClaims * PD.INDistributionPercent * PD.INCostFactor), 0) CapSurplusDeficitEPaidClaims
                           ,ISNULL ((DFD.CapSurplusDeficitDEPaidClaims * PD.INDistributionPercent * PD.INCostFactor), 0) CapSurplusDeficitDEPaidClaims
                           ,ISNULL ((DFD.CapSurplusDeficitBCAllocated), 0) CapSurplusDeficitBCAllocated
                           ,ISNULL ((DFD.CapSurplusDeficitScenarioAllocated), 0) CapSurplusDeficitScenarioAllocated
                           ,ISNULL ((DFD.CapSurplusDeficitBCAllocatedDelegated * PD.INDistributionPercent * PD.INCostFactor), 0) CapSurplusDeficitBCAllocatedDelegated
                           ,ISNULL (
                            (DFD.CapSurplusDeficitScenarioAllocatedDelegated * PD.INDistributionPercent * PD.INCostFactor), 0) CapSurplusDeficitScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapMSBs * PD.INDistributionPercent * PD.INCostFactor), 0) CapMSBs
                           ,ISNULL ((DFD.CapProviderRewards), 0) CapProviderRewards
                           ,ISNULL ((DFD.OPBOtherNonHospitalBCAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBOtherNonHospitalBCAllocated
                           ,ISNULL ((DFD.OPBOtherNonHospitalScenarioAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBOtherNonHospitalScenarioAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownBCAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBClaimsBuyDownBCAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownScenarioAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBClaimsBuyDownScenarioAllocated
                           ,ISNULL ((DFD.OPBProviderClaimSettlementsBCAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBProviderClaimSettlementsBCAllocated
                           ,ISNULL (
                            (DFD.OPBProviderClaimSettlementsScenarioAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBProviderClaimSettlementsScenarioAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherBCAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBAccessFeesAndOtherBCAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherScenarioAllocated * PD.INDistributionPercent * PD.INCostFactor), 0) OPBAccessFeesAndOtherScenarioAllocated
                           ,ISNULL ((DFD.PartDCapAdj), 0) PartDCapAdj
                           ,ISNULL ((DFD.PartDCapAdjDelegated * PD.INDistributionPercent * PD.INCostFactor), 0) PartDCapAdjDelegated
                           ,ISNULL ((DFD.MSCapAdj), 0) MSCapAdj
                           ,ISNULL ((DFD.MSCapAdjDelegated * PD.INDistributionPercent * PD.INCostFactor), 0) MSCapAdjDelegated
                           ,ISNULL ((DFD.SubCapAdj * PD.INDistributionPercent * PD.INCostFactor), 0) SubCapAdj
                           ,ISNULL ((DFD.SubCapAdjExclude), 0) SubCapAdjExclude
                           ,ISNULL ((DFD.MedicaidAdjPaid * PD.INDistributionPercent * PD.INCostFactor), 0) MedicaidAdjPaid
                           ,ISNULL ((DFD.MedicaidAdjMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) MedicaidAdjMbrCS
                           ,ISNULL ((DFD.ImplicitMarginPaid * PD.INDistributionPercent * PD.INCostFactor), 0) ImplicitMarginPaid
                           ,ISNULL ((DFD.ImplicitMarginMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) ImplicitMarginMbrCS
                           ,ISNULL ((DFD.AdditiveAdjPaid * PD.INDistributionPercent * PD.INCostFactor), 0) AdditiveAdjPaid
                           ,ISNULL ((DFD.AdditiveAdjMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) AdditiveAdjMbrCS
                           ,ISNULL ((DFD.AdditiveAdjAdmits * PD.INDistributionPercent), 0) AdditiveAdjAdmits
                           ,ISNULL ((DFD.AdditiveAdjUnits * PD.INDistributionPercent), 0) AdditiveAdjUnits
                           ,ISNULL ((DFD.ModelOfCareAdjPaid * PD.INDistributionPercent * PD.INCostFactor), 0) ModelOfCareAdjPaid
                           ,ISNULL ((DFD.ModelOfCareAdjMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) ModelOfCareAdjMbrCS
                           ,ISNULL ((DFD.ModelOfCareAdjUnits * PD.INDistributionPercent), 0) ModelOfCareAdjUnits
                           ,ISNULL ((DFD.UCAdmitsAdj * PD.INDistributionPercent), 0) UCAdmitsAdj
                           ,ISNULL ((DFD.UCUnitsAdj * PD.INDistributionPercent), 0) UCUnitsAdj
                           ,ISNULL ((DFD.PartBRxRebatesPharmacy), 0) PartBRxRebatesPharmacy
                           ,ISNULL ((DFD.PartBRxRebatesQN), 0) PartBRxRebatesQN
                           ,ISNULL ((DFD.RelatedPartiesAdj), 0) RelatedPartiesAdj
                           ,ISNULL ((DFD.ProfitAdj), 0) ProfitAdj
                           ,ISNULL ((DFD.MSBPaid * PD.INDistributionPercent * PD.INCostFactor), 0) MSBPaid
                           ,ISNULL ((DFD.MSBMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) MSBMbrCS
                           ,ISNULL ((DFD.MSBUnits * PD.INDistributionPercent), 0) MSBUnits
                           ,ISNULL ((DFD.MSBReductionCap * PD.INDistributionPercent * PD.INCostFactor), 0) MSBReductionCap
                           ,ISNULL ((DFD.MSBReductionClaimsPaid * PD.INDistributionPercent * PD.INCostFactor), 0) MSBReductionClaimsPaid
                           ,ISNULL ((DFD.MSBReductionClaimsMbrCS * PD.INDistributionPercent * PD.INCostFactor), 0) MSBReductionClaimsMbrCS
                           ,ISNULL ((DFD.MSBReductionClaimsUnits * PD.INDistributionPercent), 0) MSBReductionClaimsUnits
                           ,ISNULL ((DFD.MSBReductionQuality * PD.INDistributionPercent * PD.INCostFactor), 0) MSBReductionQuality
                           ,@XUserID LastUpdateByID
                           ,@LastDateTime LastUpdateDateTime
                FROM        #BaseData DFD
               INNER JOIN   dbo.SavedPlanINOONDistributionDetail PD WITH (NOLOCK)
                       ON PD.BenefitCategoryID = DFD.BenefitCategoryID
                          AND   PD.ForecastID = @XForecastID

                UNION ALL

                --  IsInNetwork = 1 ; IsNetworkOnly = 1              
                --  Base Allowed x INCostFactor                
                --    NOTE: Distribution is not applied.  Assumes all services occur in-network.                
                --          Used in conjunction with CPDs to value in-network deductible and MOOP cost sharing.              
                --          Previously called INNetworkAllowed.              
                --  We assume 100% of Related Party Adj, Profit Adj, and Rx Rebates are in-network.    
                --  The following fields are excluded from the cost share basis, and are treated as in-network: 
                --		CapDirectPayBCAllocated
                --		CapDirectPayScenarioAllocated
                --		CapSurplusDeficitBCAllocated
                --		CapSurplusDeficitScenarioAllocated
                --		CapProviderRewards
                --		PartDCapAdj
                --		MSCapAdj
                --		SubCapAdjExclude
                SELECT      DISTINCT
                            @XForecastID ForecastID
                           ,DFD.MARatingOptionID
                           ,DFD.[Provider]
                           ,1 IsInNetwork
                           ,1 IsNetworkOnly
                           ,DFD.BenefitCategoryID
                           ,DFD.DualEligibleTypeID
                           ,ISNULL ((DFD.Paid * PD.INCostFactor), 0) Paid
                           ,ISNULL ((DFD.MbrCS * PD.INCostFactor), 0) MbrCS
                           ,ISNULL ((DFD.PymtReductionAmt * PD.INCostFactor), 0) PymtReductionAmt
                           ,ISNULL ((DFD.AdmitCnt), 0) AdmitCnt
                           ,ISNULL ((DFD.UnitCnt), 0) UnitCnt
                           ,ISNULL ((DFD.EncounterMbrCS * PD.INCostFactor), 0) EncounterMbrCS
                           ,ISNULL ((DFD.EncounterAdmitCnt), 0) EncounterAdmitCnt
                           ,ISNULL ((DFD.EncounterUnitCnt), 0) EncounterUnitCnt
                           ,ISNULL ((DFD.DelegatedEncounterMbrCS * PD.INCostFactor), 0) DelegatedEncounterMbrCS
                           ,ISNULL ((DFD.DelegatedEncounterAdmitCnt), 0) DelegatedEncounterAdmitCnt
                           ,ISNULL ((DFD.DelegatedEncounterUnitCnt), 0) DelegatedEncounterUnitCnt
                           ,ISNULL ((DFD.CapDirectPayEPaidClaims * PD.INCostFactor), 0) CapDirectPayEPaidClaims
                           ,ISNULL ((DFD.CapDirectPayDEPaidClaims * PD.INCostFactor), 0) CapDirectPayDEPaidClaims
                           ,ISNULL ((DFD.CapDirectPayBCAllocated), 0) CapDirectPayBCAllocated
                           ,ISNULL ((DFD.CapDirectPayScenarioAllocated), 0) CapDirectPayScenarioAllocated
                           ,ISNULL ((DFD.CapDirectPayBCAllocatedDelegated * PD.INCostFactor), 0) CapDirectPayBCAllocatedDelegated
                           ,ISNULL ((DFD.CapDirectPayScenarioAllocatedDelegated * PD.INCostFactor), 0) CapDirectPayScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapSurplusDeficitEPaidClaims * PD.INCostFactor), 0) CapSurplusDeficitEPaidClaims
                           ,ISNULL ((DFD.CapSurplusDeficitDEPaidClaims * PD.INCostFactor), 0) CapSurplusDeficitDEPaidClaims
                           ,ISNULL ((DFD.CapSurplusDeficitBCAllocated), 0) CapSurplusDeficitBCAllocated
                           ,ISNULL ((DFD.CapSurplusDeficitScenarioAllocated), 0) CapSurplusDeficitScenarioAllocated
                           ,ISNULL ((DFD.CapSurplusDeficitBCAllocatedDelegated * PD.INCostFactor), 0) CapSurplusDeficitBCAllocatedDelegated
                           ,ISNULL ((DFD.CapSurplusDeficitScenarioAllocatedDelegated * PD.INCostFactor), 0) CapSurplusDeficitScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapMSBs * PD.INCostFactor), 0) CapMSBs
                           ,ISNULL ((DFD.CapProviderRewards), 0) CapProviderRewards
                           ,ISNULL ((DFD.OPBOtherNonHospitalBCAllocated * PD.INCostFactor), 0) OPBOtherNonHospitalBCAllocated
                           ,ISNULL ((DFD.OPBOtherNonHospitalScenarioAllocated * PD.INCostFactor), 0) OPBOtherNonHospitalScenarioAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownBCAllocated * PD.INCostFactor), 0) OPBClaimsBuyDownBCAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownScenarioAllocated * PD.INCostFactor), 0) OPBClaimsBuyDownScenarioAllocated
                           ,ISNULL ((DFD.OPBProviderClaimSettlementsBCAllocated * PD.INCostFactor), 0) OPBProviderClaimSettlementsBCAllocated
                           ,ISNULL ((DFD.OPBProviderClaimSettlementsScenarioAllocated * PD.INCostFactor), 0) OPBProviderClaimSettlementsScenarioAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherBCAllocated * PD.INCostFactor), 0) OPBAccessFeesAndOtherBCAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherScenarioAllocated * PD.INCostFactor), 0) OPBAccessFeesAndOtherScenarioAllocated
                           ,ISNULL ((DFD.PartDCapAdj), 0) PartDCapAdj
                           ,ISNULL ((DFD.PartDCapAdjDelegated * PD.INCostFactor), 0) PartDCapAdjDelegated
                           ,ISNULL ((DFD.MSCapAdj), 0) MSCapAdj
                           ,ISNULL ((DFD.MSCapAdjDelegated * PD.INCostFactor), 0) MSCapAdjDelegated
                           ,ISNULL ((DFD.SubCapAdj * PD.INCostFactor), 0) SubCapAdj
                           ,ISNULL ((DFD.SubCapAdjExclude), 0) SubCapAdjExclude
                           ,ISNULL ((DFD.MedicaidAdjPaid * PD.INCostFactor), 0) MedicaidAdjPaid
                           ,ISNULL ((DFD.MedicaidAdjMbrCS * PD.INCostFactor), 0) MedicaidAdjMbrCS
                           ,ISNULL ((DFD.ImplicitMarginPaid * PD.INCostFactor), 0) ImplicitMarginPaid
                           ,ISNULL ((DFD.ImplicitMarginMbrCS * PD.INCostFactor), 0) ImplicitMarginMbrCS
                           ,ISNULL ((DFD.AdditiveAdjPaid * PD.INCostFactor), 0) AdditiveAdjPaid
                           ,ISNULL ((DFD.AdditiveAdjMbrCS * PD.INCostFactor), 0) AdditiveAdjMbrCS
                           ,ISNULL ((DFD.AdditiveAdjAdmits), 0) AdditiveAdjAdmits
                           ,ISNULL ((DFD.AdditiveAdjUnits), 0) AdditiveAdjUnits
                           ,ISNULL ((DFD.ModelOfCareAdjPaid * PD.INCostFactor), 0) ModelOfCareAdjPaid
                           ,ISNULL ((DFD.ModelOfCareAdjMbrCS * PD.INCostFactor), 0) ModelOfCareAdjMbrCS
                           ,ISNULL ((DFD.ModelOfCareAdjUnits), 0) ModelOfCareAdjUnits
                           ,ISNULL ((DFD.UCAdmitsAdj), 0) UCAdmitsAdj
                           ,ISNULL ((DFD.UCUnitsAdj), 0) UCUnitsAdj
                           ,ISNULL ((DFD.PartBRxRebatesPharmacy), 0) PartBRxRebatesPharmacy
                           ,ISNULL ((DFD.PartBRxRebatesQN), 0) PartBRxRebatesQN
                           ,ISNULL ((DFD.RelatedPartiesAdj), 0) RelatedPartiesAdj
                           ,ISNULL ((DFD.ProfitAdj), 0) ProfitAdj
                           ,ISNULL ((DFD.MSBPaid * PD.INCostFactor), 0) MSBPaid
                           ,ISNULL ((DFD.MSBMbrCS * PD.INCostFactor), 0) MSBMbrCS
                           ,ISNULL ((DFD.MSBUnits), 0) MSBUnits
                           ,ISNULL ((DFD.MSBReductionCap * PD.INCostFactor), 0) MSBReductionCap
                           ,ISNULL ((DFD.MSBReductionClaimsPaid * PD.INCostFactor), 0) MSBReductionClaimsPaid
                           ,ISNULL ((DFD.MSBReductionClaimsMbrCS * PD.INCostFactor), 0) MSBReductionClaimsMbrCS
                           ,ISNULL ((DFD.MSBReductionClaimsUnits), 0) MSBReductionClaimsUnits
                           ,ISNULL ((DFD.MSBReductionQuality * PD.INCostFactor), 0) MSBReductionQuality
                           ,@XUserID LastUpdateByID
                           ,@LastDateTime LastUpdateDateTime
                FROM        #BaseData DFD
               INNER JOIN   dbo.SavedPlanINOONDistributionDetail PD WITH (NOLOCK)
                       ON PD.BenefitCategoryID = DFD.BenefitCategoryID
                          AND   PD.ForecastID = @XForecastID

                UNION ALL

                --  IsInNetwork = 0 ; IsNetworkOnly = 0              
                --  Base Allowed x OONCostFactor x OONDistribution.                
                --  We assume 0% of Related Party Adj, Profit Adj, and Rx Rebates are out-of-network.   
                --  The following fields are excluded from the cost share basis, and are treated as in-network: 
                --		CapDirectPayBCAllocated
                --		CapDirectPayScenarioAllocated
                --		CapSurplusDeficitBCAllocated
                --		CapSurplusDeficitScenarioAllocated
                --		CapProviderRewards
                --		PartDCapAdj
                --		MSCapAdj
                --		SubCapAdjExclude
                SELECT      DISTINCT
                            @XForecastID ForecastID
                           ,DFD.MARatingOptionID
                           ,DFD.[Provider]
                           ,0 IsInNetwork
                           ,0 IsNetworkOnly
                           ,DFD.BenefitCategoryID
                           ,DFD.DualEligibleTypeID
                           ,ISNULL ((DFD.Paid * PD.OONDistributionPercent * PD.OONCostFactor), 0) Paid
                           ,ISNULL ((DFD.MbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) MbrCS
                           ,ISNULL ((DFD.PymtReductionAmt * PD.OONDistributionPercent * PD.OONCostFactor), 0) PymtReductionAmt
                           ,ISNULL ((DFD.AdmitCnt * PD.OONDistributionPercent), 0) AdmitCnt
                           ,ISNULL ((DFD.UnitCnt * PD.OONDistributionPercent), 0) UnitCnt
                           ,ISNULL ((DFD.EncounterMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) EncounterMbrCS
                           ,ISNULL ((DFD.EncounterAdmitCnt * PD.OONDistributionPercent), 0) EncounterAdmitCnt
                           ,ISNULL ((DFD.EncounterUnitCnt * PD.OONDistributionPercent), 0) EncounterUnitCnt
                           ,ISNULL ((DFD.DelegatedEncounterMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) DelegatedEncounterMbrCS
                           ,ISNULL ((DFD.DelegatedEncounterAdmitCnt * PD.OONDistributionPercent), 0) DelegatedEncounterAdmitCnt
                           ,ISNULL ((DFD.DelegatedEncounterUnitCnt * PD.OONDistributionPercent), 0) DelegatedEncounterUnitCnt
                           ,ISNULL ((DFD.CapDirectPayEPaidClaims * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapDirectPayEPaidClaims
                           ,ISNULL ((DFD.CapDirectPayDEPaidClaims * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapDirectPayDEPaidClaims
                           ,0 CapDirectPayBCAllocated               --Excluded from CS basis and assumed in network only
                           ,0 CapDirectPayScenarioAllocated         --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.CapDirectPayBCAllocatedDelegated * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapDirectPayBCAllocatedDelegated
                           ,ISNULL ((DFD.CapDirectPayScenarioAllocatedDelegated * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapDirectPayScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapSurplusDeficitEPaidClaims * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapSurplusDeficitEPaidClaims
                           ,ISNULL ((DFD.CapSurplusDeficitDEPaidClaims * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapSurplusDeficitDEPaidClaims
                           ,0 CapSurplusDeficitBCAllocated          --Excluded from CS basis and assumed in network only
                           ,0 CapSurplusDeficitScenarioAllocated    --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.CapSurplusDeficitBCAllocatedDelegated * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapSurplusDeficitBCAllocatedDelegated
                           ,ISNULL (
                            (DFD.CapSurplusDeficitScenarioAllocatedDelegated * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapSurplusDeficitScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapMSBs * PD.OONDistributionPercent * PD.OONCostFactor), 0) CapMSBs
                           ,0 CapProviderRewards                    --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.OPBOtherNonHospitalBCAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBOtherNonHospitalBCAllocated
                           ,ISNULL ((DFD.OPBOtherNonHospitalScenarioAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBOtherNonHospitalScenarioAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownBCAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBClaimsBuyDownBCAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownScenarioAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBClaimsBuyDownScenarioAllocated
                           ,ISNULL ((DFD.OPBProviderClaimSettlementsBCAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBProviderClaimSettlementsBCAllocated
                           ,ISNULL (
                            (DFD.OPBProviderClaimSettlementsScenarioAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBProviderClaimSettlementsScenarioAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherBCAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBAccessFeesAndOtherBCAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherScenarioAllocated * PD.OONDistributionPercent * PD.OONCostFactor), 0) OPBAccessFeesAndOtherScenarioAllocated
                           ,0 PartDCapAdj                           --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.PartDCapAdjDelegated * PD.OONDistributionPercent * PD.OONCostFactor), 0) PartDCapAdjDelegated
                           ,0 MSCapAdj                              --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.MSCapAdjDelegated * PD.OONDistributionPercent * PD.OONCostFactor), 0) MSCapAdjDelegated
                           ,ISNULL ((DFD.SubCapAdj * PD.OONDistributionPercent * PD.OONCostFactor), 0) SubCapAdj
                           ,0 SubCapAdjExclude                      --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.MedicaidAdjPaid * PD.OONDistributionPercent * PD.OONCostFactor), 0) MedicaidAdjPaid
                           ,ISNULL ((DFD.MedicaidAdjMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) MedicaidAdjMbrCS
                           ,ISNULL ((DFD.ImplicitMarginPaid * PD.OONDistributionPercent * PD.OONCostFactor), 0) ImplicitMarginPaid
                           ,ISNULL ((DFD.ImplicitMarginMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) ImplicitMarginMbrCS
                           ,ISNULL ((DFD.AdditiveAdjPaid * PD.OONDistributionPercent * PD.OONCostFactor), 0) AdditiveAdjPaid
                           ,ISNULL ((DFD.AdditiveAdjMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) AdditiveAdjMbrCS
                           ,ISNULL ((DFD.AdditiveAdjAdmits * PD.OONDistributionPercent), 0) AdditiveAdjAdmits
                           ,ISNULL ((DFD.AdditiveAdjUnits * PD.OONDistributionPercent), 0) AdditiveAdjUnits
                           ,ISNULL ((DFD.ModelOfCareAdjPaid * PD.OONDistributionPercent * PD.OONCostFactor), 0) ModelOfCareAdjPaid
                           ,ISNULL ((DFD.ModelOfCareAdjMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) ModelOfCareAdjMbrCS
                           ,ISNULL ((DFD.ModelOfCareAdjUnits * PD.OONDistributionPercent), 0) ModelOfCareAdjUnits
                           ,ISNULL ((DFD.UCAdmitsAdj * PD.OONDistributionPercent), 0) UCAdmitsAdj
                           ,ISNULL ((DFD.UCUnitsAdj * PD.OONDistributionPercent), 0) UCUnitsAdj
                           ,0 PartBRxRebatesPharmacy                -- Assumption is that all pharmacy rebates are given for in-network only              
                           ,0 PartBRxRebatesQN                      -- Assumption is that all pharmacy rebates are given for in-network only              
                           ,0 RelatedPartiesAdj                     -- Assumption is that all parties with partial or full ownership (related parties) are also only part of the In-Network              
                           ,0 ProfitAdj                             -- Adjustments for Dental / Vision.  Like related parties these adjustments should only occur in-Network              
                           ,ISNULL ((DFD.MSBPaid * PD.OONDistributionPercent * PD.OONCostFactor), 0) MSBPaid
                           ,ISNULL ((DFD.MSBMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) MSBMbrCS
                           ,ISNULL ((DFD.MSBUnits * PD.OONDistributionPercent), 0) MSBUnits
                           ,ISNULL ((DFD.MSBReductionCap * PD.OONDistributionPercent * PD.OONCostFactor), 0) MSBReductionCap
                           ,ISNULL ((DFD.MSBReductionClaimsPaid * PD.OONDistributionPercent * PD.OONCostFactor), 0) MSBReductionClaimsPaid
                           ,ISNULL ((DFD.MSBReductionClaimsMbrCS * PD.OONDistributionPercent * PD.OONCostFactor), 0) MSBReductionClaimsMbrCS
                           ,ISNULL ((DFD.MSBReductionClaimsUnits * PD.OONDistributionPercent), 0) MSBReductionClaimsUnits
                           ,ISNULL ((DFD.MSBReductionQuality * PD.OONDistributionPercent * PD.OONCostFactor), 0) MSBReductionQuality
                           ,@XUserID LastUpdateByID
                           ,@LastDateTime LastUpdateDateTime
                FROM        #BaseData DFD
               INNER JOIN   dbo.SavedPlanINOONDistributionDetail PD WITH (NOLOCK)
                       ON PD.BenefitCategoryID = DFD.BenefitCategoryID
                          AND   PD.ForecastID = @XForecastID

                UNION ALL

                --  IsInNetwork = 0 ; IsNetworkOnly = 1              
                --  Base Allowed x OONCostFactor                
                --    NOTE: Distribution is not applied.  Assumes all services occur out-of-network.                
                --          Used in conjunction with CPDs to value out-of-network deductible and MOOP cost sharing.         
                --          Previously called OONNetworkAllowed.               
                --  We assume 0% of Related Party Adj, Profit Adj, and Rx Rebates are out-of-network.
                --  The following fields are excluded from the cost share basis, and are treated as in-network: 
                --		CapDirectPayBCAllocated
                --		CapDirectPayScenarioAllocated
                --		CapSurplusDeficitBCAllocated
                --		CapSurplusDeficitScenarioAllocated
                --		CapProviderRewards
                --		PartDCapAdj
                --		MSCapAdj
                --		SubCapAdjExclude
                SELECT      DISTINCT
                            @XForecastID ForecastID
                           ,DFD.MARatingOptionID
                           ,DFD.[Provider]
                           ,0 IsInNetwork
                           ,1 IsNetworkOnly
                           ,DFD.BenefitCategoryID
                           ,DFD.DualEligibleTypeID
                           ,ISNULL ((DFD.Paid * PD.OONCostFactor), 0) Paid
                           ,ISNULL ((DFD.MbrCS * PD.OONCostFactor), 0) MbrCS
                           ,ISNULL ((DFD.PymtReductionAmt * PD.OONCostFactor), 0) PymtReductionAmt
                           ,ISNULL ((DFD.AdmitCnt), 0) AdmitCnt
                           ,ISNULL ((DFD.UnitCnt), 0) UnitCnt
                           ,ISNULL ((DFD.EncounterMbrCS * PD.OONCostFactor), 0) EncounterMbrCS
                           ,ISNULL ((DFD.EncounterAdmitCnt), 0) EncounterAdmitCnt
                           ,ISNULL ((DFD.EncounterUnitCnt), 0) EncounterUnitCnt
                           ,ISNULL ((DFD.DelegatedEncounterMbrCS * PD.OONCostFactor), 0) DelegatedEncounterMbrCS
                           ,ISNULL ((DFD.DelegatedEncounterAdmitCnt), 0) DelegatedEncounterAdmitCnt
                           ,ISNULL ((DFD.DelegatedEncounterUnitCnt), 0) DelegatedEncounterUnitCnt
                           ,ISNULL ((DFD.CapDirectPayEPaidClaims * PD.OONCostFactor), 0) CapDirectPayEPaidClaims
                           ,ISNULL ((DFD.CapDirectPayDEPaidClaims * PD.OONCostFactor), 0) CapDirectPayDEPaidClaims
                           ,0 CapDirectPayBCAllocated               --Excluded from CS basis and assumed in network only
                           ,0 CapDirectPayScenarioAllocated         --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.CapDirectPayBCAllocatedDelegated * PD.OONCostFactor), 0) CapDirectPayBCAllocatedDelegated
                           ,ISNULL ((DFD.CapDirectPayScenarioAllocatedDelegated * PD.OONCostFactor), 0) CapDirectPayScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapSurplusDeficitEPaidClaims * PD.OONCostFactor), 0) CapSurplusDeficitEPaidClaims
                           ,ISNULL ((DFD.CapSurplusDeficitDEPaidClaims * PD.OONCostFactor), 0) CapSurplusDeficitDEPaidClaims
                           ,0 CapSurplusDeficitBCAllocated          --Excluded from CS basis and assumed in network only
                           ,0 CapSurplusDeficitScenarioAllocated    --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.CapSurplusDeficitBCAllocatedDelegated * PD.OONCostFactor), 0) CapSurplusDeficitBCAllocatedDelegated
                           ,ISNULL ((DFD.CapSurplusDeficitScenarioAllocatedDelegated * PD.OONCostFactor), 0) CapSurplusDeficitScenarioAllocatedDelegated
                           ,ISNULL ((DFD.CapMSBs * PD.OONCostFactor), 0) CapMSBs
                           ,0 CapProviderRewards                    --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.OPBOtherNonHospitalBCAllocated * PD.OONCostFactor), 0) OPBOtherNonHospitalBCAllocated
                           ,ISNULL ((DFD.OPBOtherNonHospitalScenarioAllocated * PD.OONCostFactor), 0) OPBOtherNonHospitalScenarioAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownBCAllocated * PD.OONCostFactor), 0) OPBClaimsBuyDownBCAllocated
                           ,ISNULL ((DFD.OPBClaimsBuyDownScenarioAllocated * PD.OONCostFactor), 0) OPBClaimsBuyDownScenarioAllocated
                           ,ISNULL ((DFD.OPBProviderClaimSettlementsBCAllocated * PD.OONCostFactor), 0) OPBProviderClaimSettlementsBCAllocated
                           ,ISNULL ((DFD.OPBProviderClaimSettlementsScenarioAllocated * PD.OONCostFactor), 0) OPBProviderClaimSettlementsScenarioAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherBCAllocated * PD.OONCostFactor), 0) OPBAccessFeesAndOtherBCAllocated
                           ,ISNULL ((DFD.OPBAccessFeesAndOtherScenarioAllocated * PD.OONCostFactor), 0) OPBAccessFeesAndOtherScenarioAllocated
                           ,0 PartDCapAdj                           --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.PartDCapAdjDelegated * PD.OONCostFactor), 0) PartDCapAdjDelegated
                           ,0 MSCapAdj                              --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.MSCapAdjDelegated * PD.OONCostFactor), 0) MSCapAdjDelegated
                           ,ISNULL ((DFD.SubCapAdj * PD.OONCostFactor), 0) SubCapAdj
                           ,0 SubCapAdjExclude                      --Excluded from CS basis and assumed in network only
                           ,ISNULL ((DFD.MedicaidAdjPaid * PD.OONCostFactor), 0) MedicaidAdjPaid
                           ,ISNULL ((DFD.MedicaidAdjMbrCS * PD.OONCostFactor), 0) MedicaidAdjMbrCS
                           ,ISNULL ((DFD.ImplicitMarginPaid * PD.OONCostFactor), 0) ImplicitMarginPaid
                           ,ISNULL ((DFD.ImplicitMarginMbrCS * PD.OONCostFactor), 0) ImplicitMarginMbrCS
                           ,ISNULL ((DFD.AdditiveAdjPaid * PD.OONCostFactor), 0) AdditiveAdjPaid
                           ,ISNULL ((DFD.AdditiveAdjMbrCS * PD.OONCostFactor), 0) AdditiveAdjMbrCS
                           ,ISNULL ((DFD.AdditiveAdjAdmits), 0) AdditiveAdjAdmits
                           ,ISNULL ((DFD.AdditiveAdjUnits), 0) AdditiveAdjUnits
                           ,ISNULL ((DFD.ModelOfCareAdjPaid * PD.OONCostFactor), 0) ModelOfCareAdjPaid
                           ,ISNULL ((DFD.ModelOfCareAdjMbrCS * PD.OONCostFactor), 0) ModelOfCareAdjMbrCS
                           ,ISNULL ((DFD.ModelOfCareAdjUnits), 0) ModelOfCareAdjUnits
                           ,ISNULL ((DFD.UCAdmitsAdj), 0) UCAdmitsAdj
                           ,ISNULL ((DFD.UCUnitsAdj), 0) UCUnitsAdj
                           ,0 PartBRxRebatesPharmacy                -- Assumption is that all pharmacy rebates are given for in-network only              
                           ,0 PartBRxRebatesQN                      -- Assumption is that all pharmacy rebates are given for in-network only              
                           ,0 RelatedPartiesAdj                     -- Assumption is that all parties with partial or full ownership (related parties) are also only part of the In-Network              
                           ,0 ProfitAdj                             -- Adjustments for Dental / Vision.  Like related parties these adjustments should only occur in-Network              
                           ,ISNULL ((DFD.MSBPaid * PD.OONCostFactor), 0) MSBPaid
                           ,ISNULL ((DFD.MSBMbrCS * PD.OONCostFactor), 0) MSBMbrCS
                           ,ISNULL ((DFD.MSBUnits), 0) MSBUnits
                           ,ISNULL ((DFD.MSBReductionCap * PD.OONCostFactor), 0) MSBReductionCap
                           ,ISNULL ((DFD.MSBReductionClaimsPaid * PD.OONCostFactor), 0) MSBReductionClaimsPaid
                           ,ISNULL ((DFD.MSBReductionClaimsMbrCS * PD.OONCostFactor), 0) MSBReductionClaimsMbrCS
                           ,ISNULL ((DFD.MSBReductionClaimsUnits), 0) MSBReductionClaimsUnits
                           ,ISNULL ((DFD.MSBReductionQuality * PD.OONCostFactor), 0) MSBReductionQuality
                           ,@XUserID LastUpdateByID
                           ,@LastDateTime LastUpdateDateTime
                FROM        #BaseData DFD
               INNER JOIN   dbo.SavedPlanINOONDistributionDetail PD WITH (NOLOCK)
                       ON PD.BenefitCategoryID = DFD.BenefitCategoryID
                          AND   PD.ForecastID = @XForecastID;

            END;

            BEGIN

                IF @XMARatingOptionID = 3
                    DELETE  FROM dbo.CalcPlanBase WHERE ForecastID = @XForecastID;
                IF (@XMARatingOptionID = 1 OR   @XMARatingOptionID = 2)
                    DELETE  FROM dbo.CalcPlanBase
                    WHERE   ForecastID = @XForecastID
                            AND MARatingOptionID = @XMARatingOptionID;

                ---  STEP 2. Populate table with adjusted values into dbo.CalcPlanBase                                   --            
                IF EXISTS (SELECT       1
                           FROM         dbo.SavedCUOverrideAdj cuo
                          INNER JOIN    #MARatingOption mao
                                  ON mao.MARatingOptionID = cuo.MARatingOptionID
                           WHERE        ForecastID = @XForecastID)

                    BEGIN

                        INSERT INTO dbo.CalcPlanBase
                            (ForecastID
                            ,MARatingOptionID
                            ,[Provider]
                            ,IsInNetwork
                            ,IsNetworkOnly
                            ,BenefitCategoryID
                            ,DualEligibleTypeID
                            ,Paid
                            ,MbrCS
                            ,PymtReductionAmt
                            ,AdmitCnt
                            ,UnitCnt
                            ,EncounterMbrCS
                            ,EncounterAdmitCnt
                            ,EncounterUnitCnt
                            ,DelegatedEncounterMbrCS
                            ,DelegatedEncounterAdmitCnt
                            ,DelegatedEncounterUnitCnt
                            ,CapDirectPayEPaidClaims
                            ,CapDirectPayDEPaidClaims
                            ,CapDirectPayBCAllocated
                            ,CapDirectPayScenarioAllocated
                            ,CapDirectPayBCAllocatedDelegated
                            ,CapDirectPayScenarioAllocatedDelegated
                            ,CapSurplusDeficitEPaidClaims
                            ,CapSurplusDeficitDEPaidClaims
                            ,CapSurplusDeficitBCAllocated
                            ,CapSurplusDeficitScenarioAllocated
                            ,CapSurplusDeficitBCAllocatedDelegated
                            ,CapSurplusDeficitScenarioAllocatedDelegated
                            ,CapMSBs
                            ,CapProviderRewards
                            ,OPBOtherNonHospitalBCAllocated
                            ,OPBOtherNonHospitalScenarioAllocated
                            ,OPBClaimsBuyDownBCAllocated
                            ,OPBClaimsBuyDownScenarioAllocated
                            ,OPBProviderClaimSettlementsBCAllocated
                            ,OPBProviderClaimSettlementsScenarioAllocated
                            ,OPBAccessFeesAndOtherBCAllocated
                            ,OPBAccessFeesAndOtherScenarioAllocated
                            ,PartDCapAdj
                            ,PartDCapAdjDelegated
                            ,MSCapAdj
                            ,MSCapAdjDelegated
                            ,SubCapAdj
                            ,SubCapAdjExclude
                            ,MedicaidAdjPaid
                            ,MedicaidAdjMbrCS
                            ,ImplicitMarginPaid
                            ,ImplicitMarginMbrCS
                            ,AdditiveAdjPaid
                            ,AdditiveAdjMbrCS
                            ,AdditiveAdjAdmits
                            ,AdditiveAdjUnits
                            ,ModelOfCareAdjPaid
                            ,ModelOfCareAdjMbrCS
                            ,ModelOfCareAdjUnits
                            ,UCAdmitsAdj
                            ,UCUnitsAdj
                            ,PartBRxRebatesPharmacy
                            ,PartBRxRebatesQN
                            ,RelatedPartiesAdj
                            ,ProfitAdj
                            ,MSBPaid
                            ,MSBMbrCS
                            ,MSBUnits
                            ,MSBReductionCap
                            ,MSBReductionClaimsPaid
                            ,MSBReductionClaimsMbrCS
                            ,MSBReductionClaimsUnits
                            ,MSBReductionQuality
                            ,LastUpdateByID
                            ,LastUpdateDateTime)
                        SELECT      @XForecastID ForecastID
                                   ,BEF.MARatingOptionID
                                   ,BEF.[Provider]
                                   ,BEF.IsInNetwork
                                   ,BEF.IsNetworkOnly
                                   ,BEF.BenefitCategoryID
                                   ,BEF.DualEligibleTypeID
                                   ,Paid = (BEF.Paid * ISNULL (ADJ.AllowedAdjFactor, 1)) + ISNULL (ADJ.AllowedAddAdj, 0)
                                           * CASE BEF.IsInNetwork WHEN 1 THEN
                                                                      SD.INCostFactor
                                                                      * CASE BEF.IsNetworkOnly WHEN 1 THEN 1 ELSE
                                                                                                         SD.INDistributionPercent END
                                                                  WHEN 0 THEN
                                                                      SD.OONCostFactor
                                                                      * CASE BEF.IsNetworkOnly WHEN 1 THEN 1 ELSE
                                                                                                         SD.OONDistributionPercent END
                                                                  ELSE 0 END
                                   ,(BEF.MbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) MbrCS
                                   ,PymtReductionAmt = (BEF.PymtReductionAmt * ISNULL (ADJ.AllowedAdjFactor, 1))
                                                       + (ISNULL (ADJ.AllowedAddAdj, 0) * (1 - CSF.ProjectedSQS))
                                                       * CASE BEF.IsInNetwork WHEN 1 THEN
                                                                                  SD.INCostFactor
                                                                                  * CASE BEF.IsNetworkOnly WHEN 1 THEN 1 ELSE
                                                                                                                     SD.INDistributionPercent END
                                                                              WHEN 0 THEN
                                                                                  SD.OONCostFactor
                                                                                  * CASE BEF.IsNetworkOnly WHEN 1 THEN 1 ELSE
                                                                                                                     SD.OONDistributionPercent END
                                                                              ELSE 0 END
                                   ,(BEF.AdmitCnt * ISNULL (ADJ.UnitsAdjFactor, 1)) AdmitCnt
                                   ,UnitCnt = (BEF.UnitCnt * ISNULL (ADJ.UnitsAdjFactor, 1)) + ISNULL (ADJ.UnitsAddAdj, 0)
                                              * CASE BEF.IsInNetwork WHEN 1 THEN
                                                                         CASE BEF.IsNetworkOnly WHEN 1 THEN 1 ELSE
                                                                                                          SD.INDistributionPercent END
                                                                     WHEN 0 THEN
                                                                         CASE BEF.IsNetworkOnly WHEN 1 THEN 1 ELSE
                                                                                                          SD.OONDistributionPercent END
                                                                     ELSE 0 END
                                   ,(BEF.EncounterMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) EncounterMbrCS
                                   ,(BEF.EncounterAdmitCnt * ISNULL (ADJ.UnitsAdjFactor, 1)) EncounterAdmitCnt
                                   ,(BEF.EncounterUnitCnt * ISNULL (ADJ.UnitsAdjFactor, 1)) EncounterUnitCnt
                                   ,(BEF.DelegatedEncounterMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) DelegatedEncounterMbrCS
                                   ,(BEF.DelegatedEncounterAdmitCnt * ISNULL (ADJ.UnitsAdjFactor, 1)) DelegatedEncounterAdmitCnt
                                   ,(BEF.DelegatedEncounterUnitCnt * ISNULL (ADJ.UnitsAdjFactor, 1)) DelegatedEncounterUnitCnt
                                   ,(BEF.CapDirectPayEPaidClaims * ISNULL (ADJ.AllowedAdjFactor, 1)) CapDirectPayEPaidClaims
                                   ,(BEF.CapDirectPayDEPaidClaims * ISNULL (ADJ.AllowedAdjFactor, 1)) CapDirectPayDEPaidClaims
                                   ,(BEF.CapDirectPayBCAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapDirectPayBCAllocated
                                   ,(BEF.CapDirectPayScenarioAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapDirectPayScenarioAllocated
                                   ,(BEF.CapDirectPayBCAllocatedDelegated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapDirectPayBCAllocatedDelegated
                                   ,(BEF.CapDirectPayScenarioAllocatedDelegated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapDirectPayScenarioAllocatedDelegated
                                   ,(BEF.CapSurplusDeficitEPaidClaims * ISNULL (ADJ.AllowedAdjFactor, 1)) CapSurplusDeficitEPaidClaims
                                   ,(BEF.CapSurplusDeficitDEPaidClaims * ISNULL (ADJ.AllowedAdjFactor, 1)) CapSurplusDeficitDEPaidClaims
                                   ,(BEF.CapSurplusDeficitBCAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapSurplusDeficitBCAllocated
                                   ,(BEF.CapSurplusDeficitScenarioAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapSurplusDeficitScenarioAllocated
                                   ,(BEF.CapSurplusDeficitBCAllocatedDelegated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapSurplusDeficitBCAllocatedDelegated
                                   ,(BEF.CapSurplusDeficitScenarioAllocatedDelegated * ISNULL (ADJ.AllowedAdjFactor, 1)) CapSurplusDeficitScenarioAllocatedDelegated
                                   ,(BEF.CapMSBs * ISNULL (ADJ.AllowedAdjFactor, 1)) CapMSBs
                                   ,(BEF.CapProviderRewards * ISNULL (ADJ.AllowedAdjFactor, 1)) CapProviderRewards
                                   ,(BEF.OPBOtherNonHospitalBCAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBOtherNonHospitalBCAllocated
                                   ,(BEF.OPBOtherNonHospitalScenarioAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBOtherNonHospitalScenarioAllocated
                                   ,(BEF.OPBClaimsBuyDownBCAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBClaimsBuyDownBCAllocated
                                   ,(BEF.OPBClaimsBuyDownScenarioAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBClaimsBuyDownScenarioAllocated
                                   ,(BEF.OPBProviderClaimSettlementsBCAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBProviderClaimSettlementsBCAllocated
                                   ,(BEF.OPBProviderClaimSettlementsScenarioAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBProviderClaimSettlementsScenarioAllocated
                                   ,(BEF.OPBAccessFeesAndOtherBCAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBAccessFeesAndOtherBCAllocated
                                   ,(BEF.OPBAccessFeesAndOtherScenarioAllocated * ISNULL (ADJ.AllowedAdjFactor, 1)) OPBAccessFeesAndOtherScenarioAllocated
                                   ,(BEF.PartDCapAdj * ISNULL (ADJ.AllowedAdjFactor, 1)) PartDCapAdj
                                   ,(BEF.PartDCapAdjDelegated * ISNULL (ADJ.AllowedAdjFactor, 1)) PartDCapAdjDelegated
                                   ,(BEF.MSCapAdj * ISNULL (ADJ.AllowedAdjFactor, 1)) MSCapAdj
                                   ,(BEF.MSCapAdjDelegated * ISNULL (ADJ.AllowedAdjFactor, 1)) MSCapAdjDelegated
                                   ,(BEF.SubCapAdj * ISNULL (ADJ.AllowedAdjFactor, 1)) SubCapAdj
                                   ,(BEF.SubCapAdjExclude * ISNULL (ADJ.AllowedAdjFactor, 1)) SubCapAdjExclude
                                   ,(BEF.MedicaidAdjPaid * ISNULL (ADJ.AllowedAdjFactor, 1)) MedicaidAdjPaid
                                   ,(BEF.MedicaidAdjMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) MedicaidAdjMbrCS
                                   ,(BEF.ImplicitMarginPaid * ISNULL (ADJ.AllowedAdjFactor, 1)) ImplicitMarginPaid
                                   ,(BEF.ImplicitMarginMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) ImplicitMarginMbrCS
                                   ,(BEF.AdditiveAdjPaid * ISNULL (ADJ.AllowedAdjFactor, 1)) AdditiveAdjPaid
                                   ,(BEF.AdditiveAdjMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) AdditiveAdjMbrCS
                                   ,(BEF.AdditiveAdjAdmits * ISNULL (ADJ.UnitsAdjFactor, 1)) AdditiveAdjAdmits
                                   ,(BEF.AdditiveAdjUnits * ISNULL (ADJ.UnitsAdjFactor, 1)) AdditiveAdjUnits
                                   ,(BEF.ModelOfCareAdjPaid * ISNULL (ADJ.AllowedAdjFactor, 1)) ModelOfCareAdjPaid
                                   ,(BEF.ModelOfCareAdjMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) ModelOfCareAdjMbrCS
                                   ,(BEF.ModelOfCareAdjUnits * ISNULL (ADJ.UnitsAdjFactor, 1)) ModelOfCareAdjUnits
                                   ,(BEF.UCAdmitsAdj * ISNULL (ADJ.UnitsAdjFactor, 1)) UCAdmitsAdj
                                   ,(BEF.UCUnitsAdj * ISNULL (ADJ.UnitsAdjFactor, 1)) UCUnitsAdj
                                   ,(BEF.PartBRxRebatesPharmacy * ISNULL (ADJ.AllowedAdjFactor, 1)) PartBRxRebatesPharmacy
                                   ,(BEF.PartBRxRebatesQN * ISNULL (ADJ.AllowedAdjFactor, 1)) PartBRxRebatesQN
                                   ,(BEF.RelatedPartiesAdj * ISNULL (ADJ.AllowedAdjFactor, 1)) RelatedPartiesAdj
                                   ,(BEF.ProfitAdj * ISNULL (ADJ.AllowedAdjFactor, 1)) ProfitAdj
                                   ,(BEF.MSBPaid * ISNULL (ADJ.AllowedAdjFactor, 1)) MSBPaid
                                   ,(BEF.MSBMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) MSBMbrCS
                                   ,(BEF.MSBUnits * ISNULL (ADJ.UnitsAdjFactor, 1)) MSBUnits
                                   ,(BEF.MSBReductionCap * ISNULL (ADJ.AllowedAdjFactor, 1)) MSBReductionCap
                                   ,(BEF.MSBReductionClaimsPaid * ISNULL (ADJ.AllowedAdjFactor, 1)) MSBReductionClaimsPaid
                                   ,(BEF.MSBReductionClaimsMbrCS * ISNULL (ADJ.AllowedAdjFactor, 1)) MSBReductionClaimsMbrCS
                                   ,(BEF.MSBReductionClaimsUnits * ISNULL (ADJ.UnitsAdjFactor, 1)) MSBReductionClaimsUnits
                                   ,(BEF.MSBReductionQuality * ISNULL (ADJ.AllowedAdjFactor, 1)) MSBReductionQuality
                                   ,@XUserID
                                   ,@LastDateTime
                        FROM        dbo.CalcPlanBaseBeforeAdj BEF WITH (NOLOCK)
                       INNER JOIN   #MARatingOption MAO
                               ON MAO.MARatingOptionID = BEF.MARatingOptionID
                       INNER JOIN   dbo.SavedPlanINOONDistributionDetail SD WITH (NOLOCK)
                               ON SD.BenefitCategoryID = BEF.BenefitCategoryID
                                  AND   SD.ForecastID = @XForecastID
                        LEFT JOIN   dbo.SavedCUOverrideAdj ADJ WITH (NOLOCK)
                               ON ADJ.MARatingOptionID = BEF.MARatingOptionID
                                  AND   ADJ.BenefitCategoryID = BEF.BenefitCategoryID
                                  AND   ADJ.ForecastID = @XForecastID
                       INNER JOIN   dbo.CalcSQSFactors CSF WITH (NOLOCK)
                               ON CSF.MARatingOptionID = BEF.MARatingOptionID
                                  AND   CSF.BenefitCategoryID = BEF.BenefitCategoryID
                                  AND   CSF.ForecastID = @XForecastID
                        WHERE       BEF.ForecastID = @XForecastID;

                    END;

                ELSE

                    BEGIN

                        IF @XMARatingOptionID = 3
                            INSERT INTO dbo.CalcPlanBase
                                (ForecastID
                                ,MARatingOptionID
                                ,[Provider]
                                ,IsInNetwork
                                ,IsNetworkOnly
                                ,BenefitCategoryID
                                ,DualEligibleTypeID
                                ,Paid
                                ,MbrCS
                                ,PymtReductionAmt
                                ,AdmitCnt
                                ,UnitCnt
                                ,EncounterMbrCS
                                ,EncounterAdmitCnt
                                ,EncounterUnitCnt
                                ,DelegatedEncounterMbrCS
                                ,DelegatedEncounterAdmitCnt
                                ,DelegatedEncounterUnitCnt
                                ,CapDirectPayEPaidClaims
                                ,CapDirectPayDEPaidClaims
                                ,CapDirectPayBCAllocated
                                ,CapDirectPayScenarioAllocated
                                ,CapDirectPayBCAllocatedDelegated
                                ,CapDirectPayScenarioAllocatedDelegated
                                ,CapSurplusDeficitEPaidClaims
                                ,CapSurplusDeficitDEPaidClaims
                                ,CapSurplusDeficitBCAllocated
                                ,CapSurplusDeficitScenarioAllocated
                                ,CapSurplusDeficitBCAllocatedDelegated
                                ,CapSurplusDeficitScenarioAllocatedDelegated
                                ,CapMSBs
                                ,CapProviderRewards
                                ,OPBOtherNonHospitalBCAllocated
                                ,OPBOtherNonHospitalScenarioAllocated
                                ,OPBClaimsBuyDownBCAllocated
                                ,OPBClaimsBuyDownScenarioAllocated
                                ,OPBProviderClaimSettlementsBCAllocated
                                ,OPBProviderClaimSettlementsScenarioAllocated
                                ,OPBAccessFeesAndOtherBCAllocated
                                ,OPBAccessFeesAndOtherScenarioAllocated
                                ,PartDCapAdj
                                ,PartDCapAdjDelegated
                                ,MSCapAdj
                                ,MSCapAdjDelegated
                                ,SubCapAdj
                                ,SubCapAdjExclude
                                ,MedicaidAdjPaid
                                ,MedicaidAdjMbrCS
                                ,ImplicitMarginPaid
                                ,ImplicitMarginMbrCS
                                ,AdditiveAdjPaid
                                ,AdditiveAdjMbrCS
                                ,AdditiveAdjAdmits
                                ,AdditiveAdjUnits
                                ,ModelOfCareAdjPaid
                                ,ModelOfCareAdjMbrCS
                                ,ModelOfCareAdjUnits
                                ,UCAdmitsAdj
                                ,UCUnitsAdj
                                ,PartBRxRebatesPharmacy
                                ,PartBRxRebatesQN
                                ,RelatedPartiesAdj
                                ,ProfitAdj
                                ,MSBPaid
                                ,MSBMbrCS
                                ,MSBUnits
                                ,MSBReductionCap
                                ,MSBReductionClaimsPaid
                                ,MSBReductionClaimsMbrCS
                                ,MSBReductionClaimsUnits
                                ,MSBReductionQuality
                                ,LastUpdateByID
                                ,LastUpdateDateTime)
                            SELECT  ForecastID
                                   ,MARatingOptionID
                                   ,[Provider]
                                   ,IsInNetwork
                                   ,IsNetworkOnly
                                   ,BenefitCategoryID
                                   ,DualEligibleTypeID
                                   ,Paid
                                   ,MbrCS
                                   ,PymtReductionAmt
                                   ,AdmitCnt
                                   ,UnitCnt
                                   ,EncounterMbrCS
                                   ,EncounterAdmitCnt
                                   ,EncounterUnitCnt
                                   ,DelegatedEncounterMbrCS
                                   ,DelegatedEncounterAdmitCnt
                                   ,DelegatedEncounterUnitCnt
                                   ,CapDirectPayEPaidClaims
                                   ,CapDirectPayDEPaidClaims
                                   ,CapDirectPayBCAllocated
                                   ,CapDirectPayScenarioAllocated
                                   ,CapDirectPayBCAllocatedDelegated
                                   ,CapDirectPayScenarioAllocatedDelegated
                                   ,CapSurplusDeficitEPaidClaims
                                   ,CapSurplusDeficitDEPaidClaims
                                   ,CapSurplusDeficitBCAllocated
                                   ,CapSurplusDeficitScenarioAllocated
                                   ,CapSurplusDeficitBCAllocatedDelegated
                                   ,CapSurplusDeficitScenarioAllocatedDelegated
                                   ,CapMSBs
                                   ,CapProviderRewards
                                   ,OPBOtherNonHospitalBCAllocated
                                   ,OPBOtherNonHospitalScenarioAllocated
                                   ,OPBClaimsBuyDownBCAllocated
                                   ,OPBClaimsBuyDownScenarioAllocated
                                   ,OPBProviderClaimSettlementsBCAllocated
                                   ,OPBProviderClaimSettlementsScenarioAllocated
                                   ,OPBAccessFeesAndOtherBCAllocated
                                   ,OPBAccessFeesAndOtherScenarioAllocated
                                   ,PartDCapAdj
                                   ,PartDCapAdjDelegated
                                   ,MSCapAdj
                                   ,MSCapAdjDelegated
                                   ,SubCapAdj
                                   ,SubCapAdjExclude
                                   ,MedicaidAdjPaid
                                   ,MedicaidAdjMbrCS
                                   ,ImplicitMarginPaid
                                   ,ImplicitMarginMbrCS
                                   ,AdditiveAdjPaid
                                   ,AdditiveAdjMbrCS
                                   ,AdditiveAdjAdmits
                                   ,AdditiveAdjUnits
                                   ,ModelOfCareAdjPaid
                                   ,ModelOfCareAdjMbrCS
                                   ,ModelOfCareAdjUnits
                                   ,UCAdmitsAdj
                                   ,UCUnitsAdj
                                   ,PartBRxRebatesPharmacy
                                   ,PartBRxRebatesQN
                                   ,RelatedPartiesAdj
                                   ,ProfitAdj
                                   ,MSBPaid
                                   ,MSBMbrCS
                                   ,MSBUnits
                                   ,MSBReductionCap
                                   ,MSBReductionClaimsPaid
                                   ,MSBReductionClaimsMbrCS
                                   ,MSBReductionClaimsUnits
                                   ,MSBReductionQuality
                                   ,LastUpdateByID
                                   ,LastUpdateDateTime
                            FROM    dbo.CalcPlanBaseBeforeAdj WITH (NOLOCK)
                            WHERE   ForecastID = @XForecastID;

                        IF (@XMARatingOptionID = 1 OR   @XMARatingOptionID = 2)
                            INSERT INTO dbo.CalcPlanBase
                                (ForecastID
                                ,MARatingOptionID
                                ,[Provider]
                                ,IsInNetwork
                                ,IsNetworkOnly
                                ,BenefitCategoryID
                                ,DualEligibleTypeID
                                ,Paid
                                ,MbrCS
                                ,PymtReductionAmt
                                ,AdmitCnt
                                ,UnitCnt
                                ,EncounterMbrCS
                                ,EncounterAdmitCnt
                                ,EncounterUnitCnt
                                ,DelegatedEncounterMbrCS
                                ,DelegatedEncounterAdmitCnt
                                ,DelegatedEncounterUnitCnt
                                ,CapDirectPayEPaidClaims
                                ,CapDirectPayDEPaidClaims
                                ,CapDirectPayBCAllocated
                                ,CapDirectPayScenarioAllocated
                                ,CapDirectPayBCAllocatedDelegated
                                ,CapDirectPayScenarioAllocatedDelegated
                                ,CapSurplusDeficitEPaidClaims
                                ,CapSurplusDeficitDEPaidClaims
                                ,CapSurplusDeficitBCAllocated
                                ,CapSurplusDeficitScenarioAllocated
                                ,CapSurplusDeficitBCAllocatedDelegated
                                ,CapSurplusDeficitScenarioAllocatedDelegated
                                ,CapMSBs
                                ,CapProviderRewards
                                ,OPBOtherNonHospitalBCAllocated
                                ,OPBOtherNonHospitalScenarioAllocated
                                ,OPBClaimsBuyDownBCAllocated
                                ,OPBClaimsBuyDownScenarioAllocated
                                ,OPBProviderClaimSettlementsBCAllocated
                                ,OPBProviderClaimSettlementsScenarioAllocated
                                ,OPBAccessFeesAndOtherBCAllocated
                                ,OPBAccessFeesAndOtherScenarioAllocated
                                ,PartDCapAdj
                                ,PartDCapAdjDelegated
                                ,MSCapAdj
                                ,MSCapAdjDelegated
                                ,SubCapAdj
                                ,SubCapAdjExclude
                                ,MedicaidAdjPaid
                                ,MedicaidAdjMbrCS
                                ,ImplicitMarginPaid
                                ,ImplicitMarginMbrCS
                                ,AdditiveAdjPaid
                                ,AdditiveAdjMbrCS
                                ,AdditiveAdjAdmits
                                ,AdditiveAdjUnits
                                ,ModelOfCareAdjPaid
                                ,ModelOfCareAdjMbrCS
                                ,ModelOfCareAdjUnits
                                ,UCAdmitsAdj
                                ,UCUnitsAdj
                                ,PartBRxRebatesPharmacy
                                ,PartBRxRebatesQN
                                ,RelatedPartiesAdj
                                ,ProfitAdj
                                ,MSBPaid
                                ,MSBMbrCS
                                ,MSBUnits
                                ,MSBReductionCap
                                ,MSBReductionClaimsPaid
                                ,MSBReductionClaimsMbrCS
                                ,MSBReductionClaimsUnits
                                ,MSBReductionQuality
                                ,LastUpdateByID
                                ,LastUpdateDateTime)
                            SELECT  ForecastID
                                   ,MARatingOptionID
                                   ,[Provider]
                                   ,IsInNetwork
                                   ,IsNetworkOnly
                                   ,BenefitCategoryID
                                   ,DualEligibleTypeID
                                   ,Paid
                                   ,MbrCS
                                   ,PymtReductionAmt
                                   ,AdmitCnt
                                   ,UnitCnt
                                   ,EncounterMbrCS
                                   ,EncounterAdmitCnt
                                   ,EncounterUnitCnt
                                   ,DelegatedEncounterMbrCS
                                   ,DelegatedEncounterAdmitCnt
                                   ,DelegatedEncounterUnitCnt
                                   ,CapDirectPayEPaidClaims
                                   ,CapDirectPayDEPaidClaims
                                   ,CapDirectPayBCAllocated
                                   ,CapDirectPayScenarioAllocated
                                   ,CapDirectPayBCAllocatedDelegated
                                   ,CapDirectPayScenarioAllocatedDelegated
                                   ,CapSurplusDeficitEPaidClaims
                                   ,CapSurplusDeficitDEPaidClaims
                                   ,CapSurplusDeficitBCAllocated
                                   ,CapSurplusDeficitScenarioAllocated
                                   ,CapSurplusDeficitBCAllocatedDelegated
                                   ,CapSurplusDeficitScenarioAllocatedDelegated
                                   ,CapMSBs
                                   ,CapProviderRewards
                                   ,OPBOtherNonHospitalBCAllocated
                                   ,OPBOtherNonHospitalScenarioAllocated
                                   ,OPBClaimsBuyDownBCAllocated
                                   ,OPBClaimsBuyDownScenarioAllocated
                                   ,OPBProviderClaimSettlementsBCAllocated
                                   ,OPBProviderClaimSettlementsScenarioAllocated
                                   ,OPBAccessFeesAndOtherBCAllocated
                                   ,OPBAccessFeesAndOtherScenarioAllocated
                                   ,PartDCapAdj
                                   ,PartDCapAdjDelegated
                                   ,MSCapAdj
                                   ,MSCapAdjDelegated
                                   ,SubCapAdj
                                   ,SubCapAdjExclude
                                   ,MedicaidAdjPaid
                                   ,MedicaidAdjMbrCS
                                   ,ImplicitMarginPaid
                                   ,ImplicitMarginMbrCS
                                   ,AdditiveAdjPaid
                                   ,AdditiveAdjMbrCS
                                   ,AdditiveAdjAdmits
                                   ,AdditiveAdjUnits
                                   ,ModelOfCareAdjPaid
                                   ,ModelOfCareAdjMbrCS
                                   ,ModelOfCareAdjUnits
                                   ,UCAdmitsAdj
                                   ,UCUnitsAdj
                                   ,PartBRxRebatesPharmacy
                                   ,PartBRxRebatesQN
                                   ,RelatedPartiesAdj
                                   ,ProfitAdj
                                   ,MSBPaid
                                   ,MSBMbrCS
                                   ,MSBUnits
                                   ,MSBReductionCap
                                   ,MSBReductionClaimsPaid
                                   ,MSBReductionClaimsMbrCS
                                   ,MSBReductionClaimsUnits
                                   ,MSBReductionQuality
                                   ,LastUpdateByID
                                   ,LastUpdateDateTime
                            FROM    dbo.CalcPlanBaseBeforeAdj WITH (NOLOCK)
                            WHERE   ForecastID = @XForecastID
                                    AND MARatingOptionID = @XMARatingOptionID;

                    END;

            END;

            -----------------------------------------------------------------------------------------------------------------            
            --  LEGACY TABLE POPULATION (REPLACING CODE PREVIOUSLY INCLUDED IN spCalcPlanExperienceByBenefitCat )          --            
            -----------------------------------------------------------------------------------------------------------------            

            BEGIN

                -- Populate table for unadjusted values            
                IF @XMARatingOptionID = @BothRating
                    BEGIN
                        DELETE  FROM dbo.CalcPlanExperienceByBenefitCatBeforeAdj
                        WHERE   ForecastID = @XForecastID;
                    END;
                IF (@XMARatingOptionID = @ExperienceRating
                    OR  @XMARatingOptionID = @ManualRating)
                    BEGIN
                        DELETE  FROM dbo.CalcPlanExperienceByBenefitCatBeforeAdj
                        WHERE   ForecastID = @XForecastID
                                AND MARatingOptionID = @XMARatingOptionID;

                    END;


                --Roll up base data
                DROP TABLE IF EXISTS #tempBaseDataForCalcPlanExperienceByBenefitCatBeforeAdj;
                CREATE TABLE #tempBaseDataForCalcPlanExperienceByBenefitCatBeforeAdj
                    (ForecastID                INT          NOT NULL
                    ,MARatingOptionID          TINYINT      NOT NULL
                    ,BenefitCategoryID         SMALLINT     NOT NULL
                    ,BidServiceCatID           SMALLINT     NOT NULL
                    ,DualEligibleTypeID        TINYINT      NOT NULL
                    ,IsIncludeInCostShareBasis BIT          NOT NULL
                    ,[Provider]                VARCHAR(255) NOT NULL
                    ,INPaidWS1                 DECIMAL(29, 9)
                    ,INAllowedWS1              DECIMAL(29, 9)
                    ,INNetworkAllowedWS1       DECIMAL(29, 9)
                    ,INPaidNonSQS              DECIMAL(29, 9)
                    ,INAllowedNonSQS           DECIMAL(29, 9)
                    ,INNetworkAllowedNonSQS    DECIMAL(29, 9)
                    ,INUnits                   DECIMAL(29, 9)
                    ,INAdmits                  DECIMAL(29, 9)
                    ,INCostSharePMPM           DECIMAL(29, 9)
                    ,OONPaidWS1                DECIMAL(29, 9)
                    ,OONAllowedWS1             DECIMAL(29, 9)
                    ,OONNetworkAllowedWS1      DECIMAL(29, 9)
                    ,OONPaidNonSQS             DECIMAL(29, 9)
                    ,OONAllowedNonSQS          DECIMAL(29, 9)
                    ,OONNetworkAllowedNonSQS   DECIMAL(29, 9)
                    ,OONUnits                  DECIMAL(29, 9)
                    ,OONAdmits                 DECIMAL(29, 9)
                    ,OONCostSharePMPM          DECIMAL(29, 9)
                    ,AllowedWS1WRPP            DECIMAL(29, 9)
                    ,SurplusDeficit            DECIMAL(29, 9)
                    ,MemberMonths              INT);
                INSERT INTO #tempBaseDataForCalcPlanExperienceByBenefitCatBeforeAdj
                    (ForecastID
                    ,MARatingOptionID
                    ,BenefitCategoryID
                    ,BidServiceCatID
                    ,DualEligibleTypeID
                    ,IsIncludeInCostShareBasis
                    ,[Provider]
                    ,INPaidWS1
                    ,INAllowedWS1
                    ,INNetworkAllowedWS1
                    ,INPaidNonSQS
                    ,INAllowedNonSQS
                    ,INNetworkAllowedNonSQS
                    ,INUnits
                    ,INAdmits
                    ,INCostSharePMPM
                    ,OONPaidWS1
                    ,OONAllowedWS1
                    ,OONNetworkAllowedWS1
                    ,OONPaidNonSQS
                    ,OONAllowedNonSQS
                    ,OONNetworkAllowedNonSQS
                    ,OONUnits
                    ,OONAdmits
                    ,OONCostSharePMPM
                    ,AllowedWS1WRPP
					,SurplusDeficit
                    ,MemberMonths)

                --Include in Cost Share Basis
                SELECT      @XForecastID ForecastID
                           ,DFD.MARatingOptionID
                           ,DFD.BenefitCategoryID
                           ,DFD.BidServiceCatID
                           ,DFD.DualEligibleTypeID
                           ,@IncludeInCostShareBasis AS IsIncludeInCostShareBasis
                           ,DFD.[Provider]
                            -- In-Network Fields ----            
                           ,ISNULL (
                            (DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid
                             + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality)
                            * DFD.MemberMonths * pd.INDistributionPercent * pd.INCostFactor
                           ,0) AS INPaidWS1
                           ,ISNULL (
                            (DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid
                             + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality + DFD.MbrCS
                             + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS + DFD.MedicaidAdjMbrCS
                             + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS + DFD.MSBReductionClaimsMbrCS)
                            * DFD.MemberMonths * pd.INDistributionPercent * pd.INCostFactor
                           ,0) AS INAllowedWS1
                           ,ISNULL (
                            (DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid
                             + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality + DFD.MbrCS
                             + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS + DFD.MedicaidAdjMbrCS
                             + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS + DFD.MSBReductionClaimsMbrCS)
                            * DFD.MemberMonths * pd.INCostFactor
                           ,0) AS INNetworkAllowedWS1
                           ,ISNULL (
                            (DFD.Paid + DFD.PymtReductionAmt + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.MSBPaid + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid
                             + DFD.MSBReductionQuality) * DFD.MemberMonths * pd.INDistributionPercent * pd.INCostFactor
                           ,0) AS INPaidNonSQS
                           ,ISNULL (
                            (DFD.Paid + DFD.PymtReductionAmt + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.MSBPaid + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid
                             + DFD.MSBReductionQuality + DFD.MbrCS + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS
                             + DFD.MedicaidAdjMbrCS + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS
                             + DFD.MSBReductionClaimsMbrCS) * DFD.MemberMonths * pd.INDistributionPercent * pd.INCostFactor
                           ,0) AS INAllowedNonSQS
                           ,ISNULL (
                            (DFD.Paid + DFD.PymtReductionAmt + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.MSBPaid + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid
                             + DFD.MSBReductionQuality + DFD.MbrCS + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS
                             + DFD.MedicaidAdjMbrCS + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS
                             + DFD.MSBReductionClaimsMbrCS) * DFD.MemberMonths * pd.INCostFactor
                           ,0) AS INNetworkAllowedNonSQS
                           ,ISNULL (
                            (DFD.UnitCnt + DFD.EncounterUnitCnt + DFD.DelegatedEncounterUnitCnt + DFD.AdditiveAdjUnits
                             + DFD.ModelOfCareAdjUnits + DFD.UCUnitsAdj + DFD.MSBUnits + DFD.MSBReductionClaimsUnits)
                            * DFD.MemberMonths * pd.INDistributionPercent
                           ,0) AS INUnits
                           ,ISNULL (
                            (DFD.AdmitCnt + DFD.EncounterAdmitCnt + DFD.DelegatedEncounterAdmitCnt + DFD.AdditiveAdjAdmits
                             + DFD.UCAdmitsAdj) * DFD.MemberMonths * pd.INDistributionPercent
                           ,0) AS INAdmits
                           ,ISNULL (
                            (DFD.MbrCS + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS + DFD.MedicaidAdjMbrCS
                             + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS + DFD.MSBReductionClaimsMbrCS)
                            * DFD.MemberMonths * pd.INDistributionPercent * pd.INCostFactor
                           ,0) AS INCostSharePMPM
                            -- Out-of-Network Fields ----            
                           ,ISNULL (
                            (DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid
                             + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality)
                            * DFD.MemberMonths * pd.OONDistributionPercent * pd.OONCostFactor
                           ,0) AS OONPaidWS1
                           ,ISNULL (
                            (DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid
                             + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality + DFD.MbrCS
                             + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS + DFD.MedicaidAdjMbrCS
                             + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS + DFD.MSBReductionClaimsMbrCS)
                            * DFD.MemberMonths * pd.OONDistributionPercent * pd.OONCostFactor
                           ,0) AS OONAllowedWS1
                           ,ISNULL (
                            (DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid
                             + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality + DFD.MbrCS
                             + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS + DFD.MedicaidAdjMbrCS
                             + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS + DFD.MSBReductionClaimsMbrCS)
                            * DFD.MemberMonths * pd.OONCostFactor
                           ,0) AS OONNetworkAllowedWS1
                           ,ISNULL (
                            (DFD.Paid + DFD.PymtReductionAmt + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.MSBPaid + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid
                             + DFD.MSBReductionQuality) * DFD.MemberMonths * pd.OONDistributionPercent * pd.OONCostFactor
                           ,0) AS OONPaidNonSQS
                           ,ISNULL (
                            (DFD.Paid + DFD.PymtReductionAmt + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.MSBPaid + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid
                             + DFD.MSBReductionQuality + DFD.MbrCS + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS
                             + DFD.MedicaidAdjMbrCS + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS
                             + DFD.MSBReductionClaimsMbrCS) * DFD.MemberMonths * pd.OONDistributionPercent
                            * pd.OONCostFactor
                           ,0) AS OONAllowedNonSQS
                           ,ISNULL (
                            (DFD.Paid + DFD.PymtReductionAmt + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.MSBPaid + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid
                             + DFD.MSBReductionQuality + DFD.MbrCS + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS
                             + DFD.MedicaidAdjMbrCS + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS
                             + DFD.MSBReductionClaimsMbrCS) * DFD.MemberMonths * pd.OONCostFactor
                           ,0) AS OONNetworkAllowedNonSQS
                           ,ISNULL (
                            (DFD.UnitCnt + DFD.EncounterUnitCnt + DFD.DelegatedEncounterUnitCnt + DFD.AdditiveAdjUnits
                             + DFD.ModelOfCareAdjUnits + DFD.UCUnitsAdj + DFD.MSBUnits + DFD.MSBReductionClaimsUnits)
                            * DFD.MemberMonths * pd.OONDistributionPercent
                           ,0) AS OONUnits
                           ,ISNULL (
                            (DFD.AdmitCnt + DFD.EncounterAdmitCnt + DFD.DelegatedEncounterAdmitCnt + DFD.AdditiveAdjAdmits
                             + DFD.UCAdmitsAdj) * DFD.MemberMonths * pd.OONDistributionPercent
                           ,0) AS OONAdmits
                           ,ISNULL (
                            (DFD.MbrCS + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS + DFD.MedicaidAdjMbrCS
                             + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS + DFD.MSBReductionClaimsMbrCS)
                            * DFD.MemberMonths * pd.OONDistributionPercent * pd.OONCostFactor
                           ,0) AS OONCostSharePMPM
                            -- AllowedWS1WRPP Field --
                           ,ISNULL (
                            (DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                             + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                             + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                             + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
                             + DFD.CapMSBs + DFD.OPBOtherNonHospitalBCAllocated + DFD.OPBOtherNonHospitalScenarioAllocated
                             + DFD.OPBClaimsBuyDownBCAllocated + DFD.OPBClaimsBuyDownScenarioAllocated
                             + DFD.OPBProviderClaimSettlementsBCAllocated
                             + DFD.OPBProviderClaimSettlementsScenarioAllocated + DFD.OPBAccessFeesAndOtherBCAllocated
                             + DFD.OPBAccessFeesAndOtherScenarioAllocated + DFD.PartDCapAdjDelegated
                             + DFD.MSCapAdjDelegated + DFD.SubCapAdj + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                             + DFD.ModelOfCareAdjPaid + DFD.MSBPaid + DFD.MSBReductionCap + DFD.MSBReductionClaimsPaid
                             + DFD.MSBReductionQuality + DFD.MbrCS + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS
                             + DFD.MedicaidAdjMbrCS + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS
                             + DFD.MSBReductionClaimsMbrCS) * DFD.MemberMonths
                           ,0) AS AllowedWS1WRPP
						   -- WS1 SurplusDeficit Field --
						   ,ISNULL(
						     (DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
							 + DFD.CapSurplusDeficitBCAllocatedDelegated + DFD.CapSurplusDeficitScenarioAllocatedDelegated
							 + DFD.PartDCapAdjDelegated + DFD.MSCapAdjDelegated) * DFD.MemberMonths
						   ,0) AS SurplusDeficit
                           ,DFD.MemberMonths
                FROM        #BaseData DFD
               INNER JOIN   #MARatingOption MAO
                       ON MAO.MARatingOptionID = DFD.MARatingOptionID
               INNER JOIN   dbo.SavedPlanINOONDistributionDetail pd WITH (NOLOCK)
                       ON pd.BenefitCategoryID = DFD.BenefitCategoryID
                          AND   pd.ForecastID = @XForecastID
                WHERE       DFD.DualEligibleTypeID IN (@DualEligible, @NonDualEligible)

                UNION ALL

                --Exclude from Cost Share Basis
                --We treat all records excluded from the Cost Share Basis as In Network
                --Therefore, we do not apply the IN/OON distribution percents and IN/OON cost factors to this section
                SELECT      @XForecastID ForecastID
                           ,DFD.MARatingOptionID
                           ,DFD.BenefitCategoryID
                           ,DFD.BidServiceCatID
                           ,DFD.DualEligibleTypeID
                           ,@ExcludeFromCostShareBasis AS IsIncludeInCostShareBasis
                           ,DFD.[Provider]
                            -- In-Network Fields ----            
                           ,ISNULL (
                            (DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                             + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                             + DFD.CapProviderRewards + DFD.PartDCapAdj + DFD.MSCapAdj + DFD.SubCapAdjExclude
                             + DFD.PartBRxRebatesPharmacy + DFD.PartBRxRebatesQN) * DFD.MemberMonths
                           ,0) AS INPaidWS1
                           ,ISNULL (
                            (DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                             + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                             + DFD.CapProviderRewards + DFD.PartDCapAdj + DFD.MSCapAdj + DFD.SubCapAdjExclude
                             + DFD.PartBRxRebatesPharmacy + DFD.PartBRxRebatesQN) * DFD.MemberMonths
                           ,0) AS INAllowedWS1
                           ,ISNULL (
                            (DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                             + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                             + DFD.CapProviderRewards + DFD.PartDCapAdj + DFD.MSCapAdj + DFD.SubCapAdjExclude
                             + DFD.PartBRxRebatesPharmacy + DFD.PartBRxRebatesQN) * DFD.MemberMonths
                           ,0) AS INNetworkAllowedWS1
                           ,ISNULL (
                            (DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                             + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                             + DFD.CapProviderRewards + DFD.PartDCapAdj + DFD.MSCapAdj + DFD.SubCapAdjExclude)
                            * DFD.MemberMonths
                           ,0) AS INPaidNonSQS
                           ,ISNULL (
                            (DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                             + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                             + DFD.CapProviderRewards + DFD.PartDCapAdj + DFD.MSCapAdj + DFD.SubCapAdjExclude)
                            * DFD.MemberMonths
                           ,0) AS INAllowedNonSQS
                           ,ISNULL (
                            (DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                             + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                             + DFD.CapProviderRewards + DFD.PartDCapAdj + DFD.MSCapAdj + DFD.SubCapAdjExclude)
                            * DFD.MemberMonths
                           ,0) AS INNetworkAllowedNonSQS
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,0 AS INCostSharePMPM
                            -- Out-of-Network Fields ----            
                           ,0 AS OONPaidWS1
                           ,0 AS OONAllowedWS1
                           ,0 AS OONNetworkAllowedWS1
                           ,0 AS OONPaidNonSQS
                           ,0 AS OONAllowedNonSQS
                           ,0 AS OONNetworkAllowedNonSQS
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                           ,0 AS OONCostSharePMPM
                            -- AllowedWS1WRPP Field --
                           ,ISNULL (
                            (DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                             + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                             + DFD.CapProviderRewards + DFD.PartDCapAdj + DFD.MSCapAdj + DFD.SubCapAdjExclude)
                            * DFD.MemberMonths
                           ,0) AS AllowedWS1WRPP
						   -- WS1 SurplusDeficit Field --
						   ,ISNULL(
						     (DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
							 + DFD.PartDCapAdj + DFD.MSCapAdj) * DFD.MemberMonths
						   ,0) AS SurplusDeficit
                           ,DFD.MemberMonths
                FROM        #BaseData DFD
               INNER JOIN   #MARatingOption MAO
                       ON MAO.MARatingOptionID = DFD.MARatingOptionID
                WHERE       DFD.DualEligibleTypeID IN (@DualEligible, @NonDualEligible);


                --Populate Dual Eligible and Non-Dual Eligible separately
                INSERT INTO dbo.CalcPlanExperienceByBenefitCatBeforeAdj
                    (PlanYearID
                    ,ForecastID
                    ,MARatingOptionID
                    ,BenefitCategoryID
                    ,DualEligibleTypeID
                    ,IsIncludeInCostShareBasis
                    ,INPaidWS1
                    ,INAllowedWS1
                    ,INNetworkAllowedWS1
                    ,INPaidNonSQS
                    ,INAllowedNonSQS
                    ,INNetworkAllowedNonSQS
                    ,INUnits
                    ,INAdmits
                    ,INCostSharePMPM
                    ,OONPaidWS1
                    ,OONAllowedWS1
                    ,OONNetworkAllowedWS1
                    ,OONPaidNonSQS
                    ,OONAllowedNonSQS
                    ,OONNetworkAllowedNonSQS
                    ,OONUnits
                    ,OONAdmits
                    ,OONCostSharePMPM
                    ,AllowedWS1WRPP
					,SurplusDeficit
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      PlanYearID = @PlanYearID
                           ,PrvLvl.ForecastID ForecastID
                           ,PrvLvl.MARatingOptionID
                           ,PrvLvl.BenefitCategoryID
                           ,PrvLvl.DualEligibleTypeID
                           ,PrvLvl.IsIncludeInCostShareBasis
                           ,INPaidWS1 = dbo.fnGetSafeDivisionResult (
                                        SUM (ISNULL (PrvLvl.INPaidWS1, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                           SUM (ISNULL (PrvLvl.INAllowedWS1, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INNetworkAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                                  SUM (ISNULL (PrvLvl.INNetworkAllowedWS1, 0))
                                                 ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INPaidNonSQS = dbo.fnGetSafeDivisionResult (
                                           SUM (ISNULL (PrvLvl.INPaidNonSQS, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                              SUM (ISNULL (PrvLvl.INAllowedNonSQS, 0))
                                             ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INNetworkAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                                     SUM (ISNULL (PrvLvl.INNetworkAllowedNonSQS, 0))
                                                    ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INUnits = dbo.fnGetSafeDivisionResult (
                                      SUM (ISNULL (PrvLvl.INUnits, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INAdmits = dbo.fnGetSafeDivisionResult (
                                       SUM (ISNULL (PrvLvl.INAdmits, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,INCostSharePMPM = dbo.fnGetSafeDivisionResult (
                                              SUM (ISNULL (PrvLvl.INCostSharePMPM, 0))
                                             ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONPaidWS1 = dbo.fnGetSafeDivisionResult (
                                         SUM (ISNULL (PrvLvl.OONPaidWS1, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                            SUM (ISNULL (PrvLvl.OONAllowedWS1, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONNetworkAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                                   SUM (ISNULL (PrvLvl.OONNetworkAllowedWS1, 0))
                                                  ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONPaidNonSQS = dbo.fnGetSafeDivisionResult (
                                            SUM (ISNULL (PrvLvl.OONPaidNonSQS, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                               SUM (ISNULL (PrvLvl.OONAllowedNonSQS, 0))
                                              ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONNetworkAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                                      SUM (ISNULL (PrvLvl.OONNetworkAllowedNonSQS, 0))
                                                     ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONUnits = dbo.fnGetSafeDivisionResult (
                                       SUM (ISNULL (PrvLvl.OONUnits, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONAdmits = dbo.fnGetSafeDivisionResult (
                                        SUM (ISNULL (PrvLvl.OONAdmits, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,OONCostSharePMPM = dbo.fnGetSafeDivisionResult (
                                               SUM (ISNULL (PrvLvl.OONCostSharePMPM, 0))
                                              ,SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,AllowedWS1WRPP = dbo.fnGetSafeDivisionResult (
                                             SUM (ISNULL (PrvLvl.AllowedWS1WRPP, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,SurplusDeficit = dbo.fnGetSafeDivisionResult (
                                             SUM (ISNULL (PrvLvl.SurplusDeficit, 0)), SUM (ISNULL (PrvLvl.MemberMonths, 0)))
                           ,@XUserID
                           ,@LastDateTime
                FROM        #tempBaseDataForCalcPlanExperienceByBenefitCatBeforeAdj PrvLvl
                GROUP BY    PrvLvl.ForecastID
                           ,PrvLvl.MARatingOptionID
                           ,PrvLvl.BenefitCategoryID
                           ,PrvLvl.DualEligibleTypeID
                           ,PrvLvl.IsIncludeInCostShareBasis;


                --Insert combined Plan Level Values for Combined Dual and Non-Dual Eligibles
                INSERT INTO dbo.CalcPlanExperienceByBenefitCatBeforeAdj
                    (PlanYearID
                    ,ForecastID
                    ,MARatingOptionID
                    ,BenefitCategoryID
                    ,DualEligibleTypeID
                    ,IsIncludeInCostShareBasis
                    ,INPaidWS1
                    ,INAllowedWS1
                    ,INNetworkAllowedWS1
                    ,INPaidNonSQS
                    ,INAllowedNonSQS
                    ,INNetworkAllowedNonSQS
                    ,INUnits
                    ,INAdmits
                    ,INCostSharePMPM
                    ,OONPaidWS1
                    ,OONAllowedWS1
                    ,OONNetworkAllowedWS1
                    ,OONPaidNonSQS
                    ,OONAllowedNonSQS
                    ,OONNetworkAllowedNonSQS
                    ,OONUnits
                    ,OONAdmits
                    ,OONCostSharePMPM
                    ,AllowedWS1WRPP
                    ,SurplusDeficit
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      PlanYearID = @PlanYearID
                           ,CBA.ForecastID
                           ,CBA.MARatingOptionID
                           ,CBA.BenefitCategoryID
                           ,@DualAndNonDualEligible AS DualEligibleTypeID
                           ,CBA.IsIncludeInCostShareBasis
                           ,INPaidWS1 = dbo.fnGetSafeDivisionResult (
                                        SUM (ISNULL (CBA.INPaidWS1 * BC.MemberMonths, 0))
                                       ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                           SUM (ISNULL (CBA.INAllowedWS1 * BC.MemberMonths, 0))
                                          ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INNetworkAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                                  SUM (ISNULL (CBA.INNetworkAllowedWS1 * BC.MemberMonths, 0))
                                                 ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INPaidNonSQS = dbo.fnGetSafeDivisionResult (
                                           SUM (ISNULL (CBA.INPaidNonSQS * BC.MemberMonths, 0))
                                          ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                              SUM (ISNULL (CBA.INAllowedNonSQS * BC.MemberMonths, 0))
                                             ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INNetworkAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                                     SUM (ISNULL (CBA.INNetworkAllowedNonSQS * BC.MemberMonths, 0))
                                                    ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INUnits = dbo.fnGetSafeDivisionResult (
                                      SUM (ISNULL (CBA.INUnits * BC.MemberMonths, 0)), SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INAdmits = dbo.fnGetSafeDivisionResult (
                                       SUM (ISNULL (CBA.INAdmits * BC.MemberMonths, 0)), SUM (ISNULL (BC.MemberMonths, 0)))
                           ,INCostSharePMPM = dbo.fnGetSafeDivisionResult (
                                              SUM (ISNULL (CBA.INCostSharePMPM * BC.MemberMonths, 0))
                                             ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONPaidWS1 = dbo.fnGetSafeDivisionResult (
                                         SUM (ISNULL (CBA.OONPaidWS1 * BC.MemberMonths, 0))
                                        ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                            SUM (ISNULL (CBA.OONAllowedWS1 * BC.MemberMonths, 0))
                                           ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONNetworkAllowedWS1 = dbo.fnGetSafeDivisionResult (
                                                   SUM (ISNULL (CBA.OONNetworkAllowedWS1 * BC.MemberMonths, 0))
                                                  ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONPaidNonSQS = dbo.fnGetSafeDivisionResult (
                                            SUM (ISNULL (CBA.OONPaidNonSQS * BC.MemberMonths, 0))
                                           ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                               SUM (ISNULL (CBA.OONAllowedNonSQS * BC.MemberMonths, 0))
                                              ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONNetworkAllowedNonSQS = dbo.fnGetSafeDivisionResult (
                                                      SUM (ISNULL (CBA.OONNetworkAllowedNonSQS * BC.MemberMonths, 0))
                                                     ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONUnits = dbo.fnGetSafeDivisionResult (
                                       SUM (ISNULL (CBA.OONUnits * BC.MemberMonths, 0)), SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONAdmits = dbo.fnGetSafeDivisionResult (
                                        SUM (ISNULL (CBA.OONAdmits * BC.MemberMonths, 0))
                                       ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,OONCostSharePMPM = dbo.fnGetSafeDivisionResult (
                                               SUM (ISNULL (CBA.OONCostSharePMPM * BC.MemberMonths, 0))
                                              ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,AllowedWS1WRPP = dbo.fnGetSafeDivisionResult (
                                             SUM (ISNULL (CBA.AllowedWS1WRPP * BC.MemberMonths, 0))
                                            ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,SurplusDeficit = dbo.fnGetSafeDivisionResult (
                                             SUM (ISNULL (CBA.SurplusDeficit * BC.MemberMonths, 0))
                                            ,SUM (ISNULL (BC.MemberMonths, 0)))
                           ,@XUserID
                           ,@LastDateTime
                FROM        dbo.CalcPlanExperienceByBenefitCatBeforeAdj CBA WITH (NOLOCK)
               INNER JOIN   #MARatingOption MAR
                       ON MAR.MARatingOptionID = CBA.MARatingOptionID
                LEFT JOIN   #spGetBaseData BC
                       ON BC.BenefitCategoryID = CBA.BenefitCategoryID
                          AND   BC.DualEligibleTypeID = CBA.DualEligibleTypeID
                          AND   BC.MARatingOptionID = CBA.MARatingOptionID
                WHERE       CBA.ForecastID = @XForecastID
                            AND CBA.DualEligibleTypeID IN (@DualEligible, @NonDualEligible)
                GROUP BY    CBA.ForecastID
                           ,CBA.MARatingOptionID
                           ,CBA.BenefitCategoryID
                           ,CBA.IsIncludeInCostShareBasis;

            END;

            BEGIN

                --Populate table for adjusted values. 
                --Multiplicative adjustments from SavedCUOverrideAdj apply to 
                --	IsIncludeInCostShareBasis = 0 and IsIncludeInCostShareBasis = 1
                IF EXISTS (SELECT       1
                           FROM         dbo.SavedCUOverrideAdj CUO WITH (NOLOCK)
                          INNER JOIN    #MARatingOption MAO
                                  ON MAO.MARatingOptionID = CUO.MARatingOptionID
                           WHERE        ForecastID = @XForecastID)
                    BEGIN
                        DELETE  FROM dbo.CalcPlanExperienceByBenefitCatAfterAdj
                        WHERE   ForecastID = @XForecastID
                                AND MARatingOptionID IN (SELECT MARatingOptionID FROM   #MARatingOption);

                        INSERT INTO dbo.CalcPlanExperienceByBenefitCatAfterAdj
                            (PlanYearID
                            ,ForecastID
                            ,MARatingOptionID
                            ,BenefitCategoryID
                            ,DualEligibleTypeID
                            ,IsIncludeInCostShareBasis
                            ,INPaidWS1
                            ,INAllowedWS1
                            ,INNetworkAllowedWS1
                            ,INPaidNonSQS
                            ,INAllowedNonSQS
                            ,INNetworkAllowedNonSQS
                            ,INUnits
                            ,INAdmits
                            ,INCostSharePMPM
                            ,OONPaidWS1
                            ,OONAllowedWS1
                            ,OONNetworkAllowedWS1
                            ,OONPaidNonSQS
                            ,OONAllowedNonSQS
                            ,OONNetworkAllowedNonSQS
                            ,OONUnits
                            ,OONAdmits
                            ,OONCostSharePMPM
                            ,AllowedWS1WRPP
							,SurplusDeficit
                            ,LastUpdateByID
                            ,LastUpdateDateTime)
                        SELECT      BEF.PlanYearID
                                   ,BEF.ForecastID
                                   ,BEF.MARatingOptionID
                                   ,BEF.BenefitCategoryID
                                   ,BEF.DualEligibleTypeID
                                   ,BEF.IsIncludeInCostShareBasis
                                   ,INPaidWS1 = (BEF.INPaidWS1 * adj.AllowedAdjFactor)
                                   ,INAllowedWS1 = (BEF.INAllowedWS1 * adj.AllowedAdjFactor)
                                   ,INNetworkAllowedWS1 = (BEF.INNetworkAllowedWS1 * adj.AllowedAdjFactor)
                                   ,INPaidNonSQS = (BEF.INPaidNonSQS * adj.AllowedAdjFactor)
                                   ,INAllowedNonSQS = (BEF.INAllowedNonSQS * adj.AllowedAdjFactor)
                                   ,INNetworkAllowedNonSQS = (BEF.INNetworkAllowedNonSQS * adj.AllowedAdjFactor)
                                   ,INUnits = (BEF.INUnits * adj.UnitsAdjFactor)
                                   ,INAdmits = (BEF.INAdmits * adj.UnitsAdjFactor)
                                   ,INCostSharePMPM = (BEF.INCostSharePMPM * adj.AllowedAdjFactor)
                                   ,OONPaidWS1 = (BEF.OONPaidWS1 * adj.AllowedAdjFactor)
                                   ,OONAllowedWS1 = (BEF.OONAllowedWS1 * adj.AllowedAdjFactor)
                                   ,OONNetworkAllowedWS1 = (BEF.OONNetworkAllowedWS1 * adj.AllowedAdjFactor)
                                   ,OONPaidNonSQS = (BEF.OONPaidNonSQS * adj.AllowedAdjFactor)
                                   ,OONAllowedNonSQS = (BEF.OONAllowedNonSQS * adj.AllowedAdjFactor)
                                   ,OONNetworkAllowedNonSQS = (BEF.OONNetworkAllowedNonSQS * adj.AllowedAdjFactor)
                                   ,OONUnits = (BEF.OONUnits * adj.UnitsAdjFactor)
                                   ,OONAdmits = (BEF.OONAdmits * adj.UnitsAdjFactor)
                                   ,OONCostSharePMPM = (BEF.OONCostSharePMPM * adj.AllowedAdjFactor)
                                   ,AllowedWS1WRPP = (BEF.AllowedWS1WRPP * adj.AllowedAdjFactor)
                                   ,SurplusDeficit = (BEF.SurplusDeficit)
                                   ,@XUserID
                                   ,@LastDateTime
                        FROM        dbo.CalcPlanExperienceByBenefitCatBeforeAdj BEF WITH (NOLOCK)
                       INNER JOIN   #MARatingOption MAO
                               ON MAO.MARatingOptionID = BEF.MARatingOptionID
                       INNER JOIN   dbo.SavedCUOverrideAdj adj WITH (NOLOCK)
                               ON adj.ForecastID = BEF.ForecastID
                                  AND   adj.MARatingOptionID = BEF.MARatingOptionID
                                  AND   adj.BenefitCategoryID = BEF.BenefitCategoryID
                        WHERE       BEF.ForecastID = @XForecastID;

                    END;

                ELSE
                    --This plan doesn't have adjusted values in dbo.SavedCUOverrideAdj, but it might have previously.  So this is making            
                    --sure that the plan doesn't have any old adjusted calculations in dbo.CalcPlanExperienceByBenefitCatAfterAdj.            
                    BEGIN
                        DELETE  FROM dbo.CalcPlanExperienceByBenefitCatAfterAdj
                        WHERE   ForecastID = @XForecastID
                                AND MARatingOptionID NOT IN (SELECT MARatingOptionID
                                                             FROM   dbo.SavedCUOverrideAdj
                                                             WHERE  ForecastID = @XForecastID);
                    END;

            END;

            ------------------------------------------------------------------------------------------------------------------------------------------            
            -- SQS            
            ------------------------------------------------------------------------------------------------------------------------------------------                   
            BEGIN

                DELETE  FROM dbo.CalcPlanExperienceByBenefitCat
                WHERE   ForecastID = @XForecastID
                        AND MARatingOptionID IN (SELECT MARatingOptionID FROM   #MARatingOption);

                --Insert Adjusted values         
                --Additive adjustments from SavedCUOverrideAdj apply only to IsIncludeInCostShareBasis = 1
                INSERT INTO dbo.CalcPlanExperienceByBenefitCat
                    (PlanYearID
                    ,ForecastID
                    ,MARatingOptionID
                    ,BenefitCategoryID
                    ,DualEligibleTypeID
                    ,IsIncludeInCostShareBasis
                    ,INPaid
                    ,INAllowed
                    ,INNetworkAllowed
                    ,INUnits
                    ,INAdmits
                    ,INCostSharePMPM
                    ,OONPaid
                    ,OONAllowed
                    ,OONNetworkAllowed
                    ,OONUnits
                    ,OONAdmits
                    ,OONCostSharePMPM
                    ,AllowedWS1WRPP
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      cpbaa.PlanYearID
                           ,cpbaa.ForecastID
                           ,cpbaa.MARatingOptionID
                           ,cpbaa.BenefitCategoryID
                           ,cpbaa.DualEligibleTypeID
                           ,cpbaa.IsIncludeInCostShareBasis
                           ,INPaid = cpbaa.INPaidWS1
                                     + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.INDistributionPercent
                                        * spio.INCostFactor)
                           ,INAllowed = cpbaa.INAllowedWS1
                                        + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.INDistributionPercent
                                           * spio.INCostFactor)
                           ,INNetworkAllowed = cpbaa.INNetworkAllowedWS1
                                               + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.INCostFactor)
                           ,INUnits = cpbaa.INUnits
                                      + (soa.UnitsAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.INDistributionPercent)
                           ,INAdmits = cpbaa.INAdmits
                           ,INCostSharePMPM = (cpbaa.INCostSharePMPM
                                               + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis
                                                  * spio.INDistributionPercent * spio.INCostFactor))
                           ,OONPaid = cpbaa.OONPaidWS1
                                      + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.OONDistributionPercent
                                         * spio.OONCostFactor)
                           ,OONAllowed = cpbaa.OONAllowedWS1
                                         + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis
                                            * spio.OONDistributionPercent * spio.OONCostFactor)
                           ,OONNetworkAllowed = cpbaa.OONNetworkAllowedWS1
                                                + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.OONCostFactor)
                           ,OONUnits = cpbaa.OONUnits
                                       + (soa.UnitsAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.OONDistributionPercent)
                           ,OONAdmits = cpbaa.OONAdmits
                           ,OONCostSharePMPM = (cpbaa.OONCostSharePMPM
                                                + (soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis
                                                   * spio.OONDistributionPercent * spio.OONCostFactor))
                           ,AllowedWS1WRPP = (cpbaa.AllowedWS1WRPP + soa.AllowedAddAdj * cpbaa.IsIncludeInCostShareBasis)
                           ,@XUserID
                           ,@LastDateTime
                FROM        dbo.CalcPlanExperienceByBenefitCatAfterAdj cpbaa WITH (NOLOCK)
               INNER JOIN   #MARatingOption MAO
                       ON MAO.MARatingOptionID = cpbaa.MARatingOptionID
               INNER JOIN   dbo.SavedPlanINOONDistributionDetail spio WITH (NOLOCK)
                       ON cpbaa.ForecastID = spio.ForecastID
                          AND   cpbaa.BenefitCategoryID = spio.BenefitCategoryID
               INNER JOIN   dbo.SavedCUOverrideAdj soa WITH (NOLOCK)
                       ON cpbaa.ForecastID = soa.ForecastID
                          AND   cpbaa.BenefitCategoryID = soa.BenefitCategoryID
                          AND   cpbaa.MARatingOptionID = soa.MARatingOptionID
                WHERE       cpbaa.ForecastID = @XForecastID;

                --Insert Unadjusted values. May only have override adjustments for the manual cut but not the experience or vice versa            
                INSERT INTO dbo.CalcPlanExperienceByBenefitCat
                    (PlanYearID
                    ,ForecastID
                    ,MARatingOptionID
                    ,BenefitCategoryID
                    ,DualEligibleTypeID
                    ,IsIncludeInCostShareBasis
                    ,INPaid
                    ,INAllowed
                    ,INNetworkAllowed
                    ,INUnits
                    ,INAdmits
                    ,INCostSharePMPM
                    ,OONPaid
                    ,OONAllowed
                    ,OONNetworkAllowed
                    ,OONUnits
                    ,OONAdmits
                    ,OONCostSharePMPM
                    ,AllowedWS1WRPP
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      PlanYearID
                           ,ForecastID
                           ,CPE.MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,IsIncludeInCostShareBasis
                           ,INPaid = INPaidWS1
                           ,INAllowed = INAllowedWS1
                           ,INNetworkAllowed = INNetworkAllowedWS1
                           ,INUnits
                           ,INAdmits
                           ,INCostSharePMPM
                           ,OONPaid = OONPaidWS1
                           ,OONAllowed = OONAllowedWS1
                           ,OONNetworkAllowed = OONNetworkAllowedWS1
                           ,OONUnits
                           ,OONAdmits
                           ,OONCostSharePMPM
                           ,AllowedWS1WRPP
                           ,LastUpdateByID
                           ,LastUpdateDateTime
                FROM        dbo.CalcPlanExperienceByBenefitCatBeforeAdj CPE WITH (NOLOCK)
               INNER JOIN   #MARatingOption MAO
                       ON MAO.MARatingOptionID = CPE.MARatingOptionID
                WHERE       ForecastID = @XForecastID
                            AND CPE.MARatingOptionID NOT IN (SELECT MARatingOptionID
                                                             FROM   dbo.CalcPlanExperienceByBenefitCatAfterAdj WITH (NOLOCK)
                                                             WHERE  ForecastID = @XForecastID);
            END;

            ------------------------------------------------------------------------------------------------------------------------------------------            
            -- NonSQS            
            ------------------------------------------------------------------------------------------------------------------------------------------                 
            BEGIN

                DELETE  FROM dbo.CalcPlanExperienceByBenefitCatNonSQS
                WHERE   ForecastID = @XForecastID
                        AND MARatingOptionID IN (SELECT MARatingOptionID FROM   #MARatingOption);

                --Insert Adjusted values        
                --Additive adjustments from SavedCUOverrideAdj apply only to IsIncludeInCostShareBasis = 1
                INSERT INTO dbo.CalcPlanExperienceByBenefitCatNonSQS
                    (PlanYearID
                    ,ForecastID
                    ,MARatingOptionID
                    ,BenefitCategoryID
                    ,DualEligibleTypeID
                    ,IsIncludeInCostShareBasis
                    ,INPaid
                    ,INAllowed
                    ,INNetworkAllowed
                    ,INUnits
                    ,INAdmits
                    ,INCostSharePMPM
                    ,OONPaid
                    ,OONAllowed
                    ,OONNetworkAllowed
                    ,OONUnits
                    ,OONAdmits
                    ,OONCostSharePMPM
					,SurplusDeficit
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      cpbaa.PlanYearID
                           ,cpbaa.ForecastID
                           ,cpbaa.MARatingOptionID
                           ,cpbaa.BenefitCategoryID
                           ,cpbaa.DualEligibleTypeID
                           ,cpbaa.IsIncludeInCostShareBasis
                           ,INPaid = cpbaa.INPaidNonSQS
                                     + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                        * spio.INDistributionPercent * spio.INCostFactor)
                           ,INAllowed = cpbaa.INAllowedNonSQS
                                        + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                           * spio.INDistributionPercent * spio.INCostFactor)
                           ,INNetworkAllowed = cpbaa.INNetworkAllowedNonSQS
                                               + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                                  * spio.INCostFactor)
                           ,INUnits = cpbaa.INUnits
                                      + (soa.UnitsAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.INDistributionPercent)
                           ,INAdmits = cpbaa.INAdmits
                           ,INCostSharePMPM = (cpbaa.INCostSharePMPM
                                               + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                                  * spio.INDistributionPercent * spio.INCostFactor))
                           ,OONPaid = cpbaa.OONPaidNonSQS
                                      + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                         * spio.OONDistributionPercent * spio.OONCostFactor)
                           ,OONAllowed = cpbaa.OONAllowedNonSQS
                                         + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                            * spio.OONDistributionPercent * spio.OONCostFactor)
                           ,OONNetworkAllowed = cpbaa.OONNetworkAllowedNonSQS
                                                + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                                   * spio.OONCostFactor)
                           ,OONUnits = cpbaa.OONUnits
                                       + (soa.UnitsAddAdj * cpbaa.IsIncludeInCostShareBasis * spio.OONDistributionPercent)
                           ,OONAdmits = cpbaa.OONAdmits
                           ,OONCostSharePMPM = (cpbaa.OONCostSharePMPM
                                                + (soa.AllowedAddAdjNonSeq * cpbaa.IsIncludeInCostShareBasis
                                                   * spio.OONDistributionPercent * spio.OONCostFactor))
						   ,cpbaa.SurplusDeficit
                           ,@XUserID
                           ,@LastDateTime
                FROM        dbo.CalcPlanExperienceByBenefitCatAfterAdj cpbaa WITH (NOLOCK)
               INNER JOIN   #MARatingOption MAO
                       ON MAO.MARatingOptionID = cpbaa.MARatingOptionID
               INNER JOIN   dbo.SavedPlanINOONDistributionDetail spio WITH (NOLOCK)
                       ON cpbaa.ForecastID = spio.ForecastID
                          AND   cpbaa.BenefitCategoryID = spio.BenefitCategoryID
               INNER JOIN   dbo.SavedCUOverrideAdj soa WITH (NOLOCK)
                       ON cpbaa.ForecastID = soa.ForecastID
                          AND   cpbaa.BenefitCategoryID = soa.BenefitCategoryID
                          AND   cpbaa.MARatingOptionID = soa.MARatingOptionID
                WHERE       cpbaa.ForecastID = @XForecastID;

                --Insert Unadjusted values. May only have override adjustments for the manual cut but not the experience or vice versa            
                INSERT INTO dbo.CalcPlanExperienceByBenefitCatNonSQS
                    (PlanYearID
                    ,ForecastID
                    ,MARatingOptionID
                    ,BenefitCategoryID
                    ,DualEligibleTypeID
                    ,IsIncludeInCostShareBasis
                    ,INPaid
                    ,INAllowed
                    ,INNetworkAllowed
                    ,INUnits
                    ,INAdmits
                    ,INCostSharePMPM
                    ,OONPaid
                    ,OONAllowed
                    ,OONNetworkAllowed
                    ,OONUnits
                    ,OONAdmits
                    ,OONCostSharePMPM
					,SurplusDeficit
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      PlanYearID
                           ,ForecastID
                           ,CPE.MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,IsIncludeInCostShareBasis
                           ,INPaid = INPaidNonSQS
                           ,INAllowed = INAllowedNonSQS
                           ,INNetworkAllowed = INNetworkAllowedNonSQS
                           ,INUnits
                           ,INAdmits
                           ,INCostSharePMPM
                           ,OONPaid = OONPaidNonSQS
                           ,OONAllowed = OONAllowedNonSQS
                           ,OONNetworkAllowed = OONNetworkAllowedNonSQS
                           ,OONUnits
                           ,OONAdmits
                           ,OONCostSharePMPM
						   ,SurplusDeficit
                           ,LastUpdateByID
                           ,LastUpdateDateTime
                FROM        dbo.CalcPlanExperienceByBenefitCatBeforeAdj CPE WITH (NOLOCK)
               INNER JOIN   #MARatingOption MAO
                       ON MAO.MARatingOptionID = CPE.MARatingOptionID
                WHERE       ForecastID = @XForecastID
                            AND CPE.MARatingOptionID NOT IN (SELECT MARatingOptionID
                                                             FROM   dbo.CalcPlanExperienceByBenefitCatAfterAdj WITH (NOLOCK)
                                                             WHERE  ForecastID = @XForecastID);
                SET @LocalError = @@Error;
                IF @ReturnStatus = 0 SET @ReturnStatus = @LocalError;

            END;

            -- Update SQS factors via spCalcSQSFactors call 
            EXEC dbo.spCalcSQSFactors @XForecastID, @XUserID;
            UPDATE  dbo.SavedForecastSetup
            SET     IsToReprice = 1
                   ,LastUpdateByID = @XUserID
                   ,LastUpdateDateTime = @LastDateTime
            WHERE   ForecastID = @XForecastID;


            COMMIT TRANSACTION spCalcPlanBase;

        END TRY

        BEGIN CATCH

            ROLLBACK TRANSACTION spCalcPlanBase;

            DECLARE @ErrorMessage   NVARCHAR(4000)
                   ,@ErrorSeverity  INT
                   ,@ErrorState     INT
                   ,@ErrorException NVARCHAR(4000)
                   ,@errSrc         VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL')
                   ,@currentdate    DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :'
                                      + CAST(@ErrorState AS VARCHAR);

            ---Insert into app log for logging error       
            EXEC dbo.spAppAddLogEntry @currentdate
                                     ,''
                                     ,'ERROR'
                                     ,@errSrc
                                     ,@ErrorMessage
                                     ,@ErrorException
                                     ,@XUserID;
            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);

        END CATCH;

    END;
GO