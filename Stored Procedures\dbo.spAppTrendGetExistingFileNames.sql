SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------        
-- PROCEDURE NAME: [dbo].[spAppTrendGetExistingFileNames]       
--        
-- AUTHOR: Kiran Kola      
--        
-- CREATED DATE: 2020-Mar-22      
-- Type:     
-- DESCRIPTION: Procedure responsible for retrieving exisiting File Names 
--        
-- PARAMETERS:        
-- Input:     
       
-- TABLES:       
--Trend_SavedComponentImportAnnual        
--Trend_SavedComponentImportQuarterly      
-- Read:        
--      
    
-- Write:        
--        
-- VIEWS:        
--        
-- FUNCTIONS:        
-- STORED PROCS:        
--          
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE   VERSION  CHANGES MADE												DEVELOPER           
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2011-Mar-22 1  Initial Version											Kiran Kola    
-- 2020-Jun-10 2 Renamed the sp name in										Kiran Pant 
--            the comment section with prefix spAppTrend	  
-- ----------------------------------------------------------------------------------------------------------------------        
CREATE PROCEDURE [dbo].[spAppTrendGetExistingFileNames]  
AS        
BEGIN        
select distinct upper(ImportFileName) as ImportFileName from dbo.Trend_SavedComponentImportAnnual    
Union
select distinct upper(ImportFileName) as ImportFileName from  dbo.Trend_SavedComponentImportQuarterly    
    
END    

GO
