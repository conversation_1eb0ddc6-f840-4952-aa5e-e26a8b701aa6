SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------      
-- PROCEDURE NAME: [PrePricing].[spAppGetBenefitsInterfaceOSBSelections]      
--      
-- TYPE: Stored Procedure      
--      
-- AUTHOR: <PERSON><PERSON>      
--      
-- CREATED DATE: 2025-Feb-19      
--      
-- DESCRIPTION: Procedure responsible for extracting OSB projections from prericeing schema    
--    by ForecastID.      
--      
-- PARAMETERS:      
-- Input:      
--     @ForecastID      
-- TABLES:       
-- Read:     
-- PrePricing.MarketInputValue
-- prepricing.MarketInputSubCategory
-- PrePricing.PlanInfo         
-- dbo.SavedPlanInfo
-- dbo.SavedForecastSetup
--         
-- Write:      
--      
-- VIEWS:      
--      
-- FUNCTIONS:      
-- dbo.fnGetBidYear()
--        
-- STORED PROCS:      
--        
-- $HISTORY       
-- ----------------------------------------------------------------------------------------------------------------------      
-- DATE			 VERSION  CHANGES MADE																	DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------      
-- 2025-Feb-19		1    Initial Version																Surya Murthy         
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [PrePricing].[spAppGetBenefitsInterfaceOSBSelections]      
	@WhereIn VARCHAR(MAX)    
AS    
BEGIN    
	SET NOCOUNT ON;
	IF @WhereIn IS NULL-- No Plans selected, extract all
	BEGIN
			SELECT d.ForecastID ,LEFT(z.SubCategoryName,6) AS OSBCode,a.INValue AS OSBName 		
			FROM PrePricing.MarketInputValue a WITH (NOLOCK)
			JOIN prepricing.MarketInputSubCategory z ON a.SubcategoryID=z.SubCategoryID
			JOIN PrePricing.PlanInfo b WITH (NOLOCK) ON b.PlanInfoID=a.PlanInfoID
			JOIN dbo.SavedPlanInfo c WITH (NOLOCK) ON c.CPS=b.CPS
			JOIN dbo.SavedForecastSetup d WITH (NOLOCK) ON d.PlanInfoID=c.PlanInfoID AND d.PlanYear=dbo.fnGetBidYear()		
			WHERE z.Categoryid=9 AND a.INValue IS NOT NULL AND a.INValue <>'' 			 
			ORDER BY d.ForecastID,OSBCode	
	END 

	ELSE --Export selected plans
		BEGIN
			SELECT d.ForecastID ,LEFT(z.SubCategoryName,6) AS OSBCode,a.INValue AS OSBName 		
			FROM PrePricing.MarketInputValue a WITH (NOLOCK)
			JOIN prepricing.MarketInputSubCategory z ON a.SubcategoryID=z.SubCategoryID
			JOIN PrePricing.PlanInfo b WITH (NOLOCK) ON b.PlanInfoID=a.PlanInfoID
			JOIN dbo.SavedPlanInfo c WITH (NOLOCK) ON c.CPS=b.CPS
			JOIN dbo.SavedForecastSetup d WITH (NOLOCK) ON d.PlanInfoID=c.PlanInfoID AND d.PlanYear=dbo.fnGetBidYear()		
			WHERE z.Categoryid=9 AND a.INValue IS NOT NULL AND a.INValue <>'' 			 			
			AND d.ForecastID	IN (SELECT Value FROM STRING_SPLIT(@WhereIN, ','))
			ORDER BY d.ForecastID,OSBCode	 
		END
END
GO
