SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------                      
-- PROCEDURE NAME: [dbo].[spAppImportRxPremium]            
--                      
-- TYPE: SAME                      
--                      
-- AUTHOR: <PERSON><PERSON>                     
--                      
-- CREATED DATE:2024-12-13                     
--                      
-- DESCRIPTION: Procedure responsible for insert and update from Medicare Secondary Payer Import                  
--                      
--                      
-- PARAMETERS:                      
-- Input:                      
--     @StageId
-- TABLES:                       
-- Read:                      
--                          
--                         
-- Write:                      
--                      
-- VIEWS:                      
--                      
-- FUNCTIONS:                      
--                        
-- STORED PROCS:                      
--                        
-- $HISTORY                       
-- ----------------------------------------------------------------------------------------------------------------------                      
-- DATE					 VERSION   CHANGES MADE                 DEVELOPER                        
-- ----------------------------------------------------------------------------------------------------------------------                      
-- 2025-MAR-07		      1			Initial Version              Ramaraj Kumar
-- ---------------------------------------------------------------------------------------------------------------------- 


CREATE PROCEDURE [dbo].[spAppImportRxPremium]
(
	@StageId VARCHAR(100)
)
AS
BEGIN
      SET NOCOUNT ON;
    BEGIN TRY
	BEGIN TRANSACTION

    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;
	DECLARE @LastUpdate DateTime;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

	SET @LastUpdate = GETDATE();

	DECLARE @RxPremium TABLE
	(
		[ForecastID] [int] NOT NULL,
		[RxSuppPremium] [decimal] (9,2),
		[RxBasicPremium] [decimal] (9,2),
		[LastUpdateByID] [char] (7) NOT NULL,
		[LastUpdateDateTime] [DateTime] NOT NULL
	);

	INSERT INTO @RxPremium SELECT ForecastID, RxSuppPremium, RxBasicPremium, @UserId, @LastUpdate 
	FROM OPENJSON(@jsonData, '$.RxPremium') 
	WITH (ForecastID INT '$."ForecastID"', RxSuppPremium DECIMAL(9,2) '$."Rx Supp Premium"', RxBasicPremium DECIMAL(9,2) '$."Rx Basic Premium"')

	--Update SavedPlanAssumptions
	MERGE INTO [dbo].[SavedPlanAssumptions] AS Target
	USING @RxPremium as Source
	ON Target.ForecastID = Source.ForecastID

	WHEN MATCHED THEN
	UPDATE SET Target.RxSuppPremium = Source.RxSuppPremium, Target.RxBasicPremium = Source.RxBasicPremium;

	--Update SavedForecastSetup to set Reprice flag
	MERGE INTO [dbo].[SavedForecastSetup] AS Target
	USING @RxPremium as Source
	ON Target.ForecastID = Source.ForeCastID

	WHEN MATCHED THEN
	UPDATE SET Target.IsToReprice = 1,
        Target.LastUpdateByID = @UserID,
        Target.LastUpdateDateTime = @LastUpdate;

    DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;

    COMMIT TRANSACTION 
    END TRY
    BEGIN CATCH

        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;
        DECLARE @ErrorException NVARCHAR(4000);
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
                @currentdate DATETIME = GETDATE();

        SELECT @ErrorMessage = ERROR_MESSAGE();
        SELECT @ErrorSeverity = ERROR_SEVERITY();
        SELECT @ErrorState = ERROR_STATE();

        RAISERROR(   
			   @ErrorMessage,  -- Message text.  
               @ErrorSeverity, -- Severity.  
               @ErrorState     -- State.  
        );

		ROLLBACK TRANSACTION;
		       ---Insert into app log for logging error------------------
        EXEC dbo.spAppAddLogEntry @log_date = @currentdate,
                                  @log_thread = '',
                                  @log_level = 'ERROR',
                                  @log_source = @errSrc,
                                  @log_message = @ErrorMessage,
                                  @exception = @ErrorException,
                                  @USER = @UserID

       END CATCH;

END;
