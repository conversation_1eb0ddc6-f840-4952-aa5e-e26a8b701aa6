SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  Deepali Mittal    
-- Create date: 18-04-2019
-- Description:  Get Scenario Setup Other BPT Information data
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2019-Apr-18  1		  Initial version.											Deepali Mittal
--2019-June-06  2         Added PMPM                                                Kiran Pant
--2019-July-01	3		  Renamed PMPM to PartBPremiumBuydown in forecast table 	Pooja Dahiya
--2019-Aug-13	3		  Added AltRebateOrder 	                                    Kiran Pant
--2019-Sep-27   4         toggle IsToReprice                                        Brent Osantowski
--2019-Oct-15   5         Removed unnessary call to spAppSetRebateOrder             Pooja Dahiya
--2020-Jun-25   6         Removed 2 fields PartBPremiumBuydown and AltRebateOrder   Anand Debadwar
--2023-Jan-30   7         Adding a where Clause to only pull BY data from SavedForecastSetup table        Abraham Ndabian
-- ----------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spAppSaveBPTInformationData]
  @ForecastID INT ,
  @ContactPerson varchar(100),
  @SecondaryContactPerson  varchar(100),
  @CertifyingActuary  varchar(100),
  @LastUpdateByID varchar(7),
  @MessageFromBackend NVARCHAR(500) OUTPUT,
  @Result BIT OUT,
  @CheckValidation BIT
AS
    BEGIN   
        BEGIN TRANSACTION;
        BEGIN TRY    

	  IF(@CheckValidation = 0)
	  BEGIN

	  UPDATE SavedForecastSetup 
	  set LastUpdateByID=@LastUpdateByID,
	  LastUpdateDateTime=getdate(),
	  ContactID = (select UserID from AdminUserHeader 
	   where UserLastName=(SELECT Value FROM fnStringSplit(@ContactPerson, ',') where Id=1)
	    and UserFirstName= (SELECT Value FROM fnStringSplit(@ContactPerson, ',') where Id=2)),
	  SecondaryContactID =(select UserID from AdminUserHeader 
	   where UserLastName=(SELECT Value FROM fnStringSplit(@SecondaryContactPerson, ',') where Id=1)
	    and UserFirstName= (SELECT Value FROM fnStringSplit(@SecondaryContactPerson, ',') where Id=2)),
	  CertifyingActuaryUserID = (select UserID from AdminUserHeader 
	   where UserLastName=(SELECT Value FROM fnStringSplit(@CertifyingActuary, ',') where Id=1)
	    and UserFirstName= (SELECT Value FROM fnStringSplit(@CertifyingActuary, ',') where Id=2))
	  where ForecastID= @ForecastID 
	  -- added this filter to restrict data to new ForecastIDs values for new BY, by Abe 1/30/23
	  AND PlanYear= dbo.fnGetBidYear() 


set @Result=1
  SET @MessageFromBackend='';
END
       COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
				 SET @Result = 0;
			SET @MessageFromBackend='Scenario save failed. Please try again <NAME_EMAIL>.' ;
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),
			@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'
			+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

			ROLLBACK TRANSACTION; 

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID

        END CATCH; 
		END;
GO
