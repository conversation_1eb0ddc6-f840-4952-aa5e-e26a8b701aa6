SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetMABPTWS3BenefitDetail]  ******/

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetMABPTWS3BenefitDetail
--
-- AUTHOR: <PERSON><PERSON> 
--
-- CREATED DATE: 2008-May-02
-- HEADER UPDATED: 2011-Jan-10
--
-- DESCRIPTION: Returns text that lists the copay or coins values for each benefit category that corresponds to the
--				specified bid service category.  Only data where benefit year = plan year is used.  IN or OON can be
--				specified.
--
-- PARAMETERS:
--  Input:
--		@ForecastID
--		@CostShareServiceCategoryCode
--		@IsIN 
--		@BenefitTypeID
--	Output:
--
-- TABLES: 
--  Read:
--		LkpExtCMSBidServiceCategory
--		LkpIntBenefitCategory
--		SavedPlanBenefitDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2008-May-02      1			Initial version.													Tonya Cockrell
-- 2008-May-21      2			Put each item on a separate line.  Added OON option.				Tonya Cockrell
-- 2009-Nov-04		3			Clear distinction btn coin/copay Added INBenefitTypeID,				Sule Dauda
--									and also added a parameter for coinsurance check.			
-- 2010-Mar-09		4			Remove the parameter CoinValue and added the parameters				Sule Dauda
--									INBenefitTypeID and OONBenefitTypeID. The added parameters 	
--									are used in the filter clause to ensure that the appropariate
--									costshare description are obtained for all plan types.   
-- 2011-Jan-07      5			Updated for 2012 Database											Michael Siekerka
-- 2011-Feb-14		6			Added Where statement to prevent a Copay/Peradmit with				Craig Wright
--									IsPhysAddOn = 1 from appearing on the BPT
-- 2011-Feb-14		7			Changed IsBenefitYearCurrentYear from 1 to 0						Craig Wright
-- 2011-Feb-28		8			Removed PlanYearID from Inner Join									Craig Wright
-- 2011-Mar-25		9			Removed	OONBenefitTypeID since this is only used for IN.			Joe Casey
-- 2011-Mar-30		10			Removed @IsShortFormat concept so the value is shown for each 		Joe Casey
--									benefit.
-- 2011-Apr-29		11			Changed bc.PBPLineCode = NULL to display with "N/A"					Joe Casey
-- 2011-May-02		12			Updated to display PBP line when it's an Admit						Joe Casey
-- 2013-May-09		13			Updated for IP Tiers												Mike Deren
-- 2013-Apr-28		14			Updated for IP Rehab												Mike Deren
-- 2016-Feb-29		15			Added two additional parameters @IsINEnabled and @IsOONEnabled		Pragya Mishra
-- 2022-May-02		16			MAAUI migration; replaced intput variable @PlanIndex with
--								@ForecastID; for table SavedPlanBenefitDetail, replaced reference
--								from PlanIndex to ForecastID; distinguishing @Copay1 and @Copay2
--								for DayRange benefits; enhanced logic for cases where 
--								OONBenefitTypeID in table SavedPlanBenefitDetail IS NULL; added
--								ORDER BY BenefitCategoryID, BenefitOrdinalID to line up 
--								descriptions by order; added "AND IsLiveIndex = 1" to pull only  
--								one option per ID; in line 337, removed code reference to table
--								SavedPlanIPTierDetail												Aleksandar Dimitrijevic
-- 2023-Aug-02		17			Added (NOLOCK), Internal Parameters									Sheetal Patil 
-- ---------------------------------------------------------------------------------------------------------------------
CREATE  FUNCTION [dbo].[fnAppGetMABPTWS3BenefitDetail]
	(
    @ForecastID INT,
    @CostShareServiceCategoryCode VARCHAR(4),
    @IsIN BIT,
	@BenefitTypeID SMALLINT
	)
RETURNS VARCHAR(MAX) AS
BEGIN

    --These values come from LkpIntBenefitType.  They will be used as constants.
    DECLARE @Coins TINYINT,
            @Copay1 TINYINT,
            @Copay2 TINYINT,
            @Admit TINYINT,
            @OutputText VARCHAR(MAX)

    SET @Coins = 1
    SET @Copay1 = 2
    SET @Admit = 3
	SET @Copay2 = 5
    SET @OutputText = NULL

	DECLARE @XForecastID INT = @ForecastID
    DECLARE @XCostShareServiceCategoryCode VARCHAR(4) = @CostShareServiceCategoryCode
	DECLARE @XIsIN BIT = @IsIN 
   	DECLARE @XBenefitTypeID SMALLINT = @BenefitTypeID
    
	BEGIN

		SELECT @OutputText =
		CASE WHEN (@XIsIN = 0 AND p.OONBenefitTypeID IS NULL) THEN 
			 CASE WHEN p.BenefitOrdinalID = 2 
				  THEN LEFT(@OutputText,LEN(@OutputText) -2)
				  ELSE @OutputText
			 END
			 ELSE COALESCE(@OutputText + ',' + CHAR(10), '') +
				  ISNULL(bc.PBPLineCode,'N/A') + '. '
			 + 
			 CASE WHEN @XIsIN = 1 THEN
				  CASE WHEN p.INBenefitTypeID IN (@Copay1, @Copay2, @Admit) 
						THEN '$' + CONVERT(VARCHAR, FLOOR(p.INBenefitValue))
					   WHEN p.INBenefitTypeID = @Coins 
						THEN CONVERT(VARCHAR, FLOOR(p.INBenefitValue * 100)) + '%'
					   ELSE ''
				  END
				  ELSE  --OON
				  CASE WHEN p.OONBenefitTypeID IN (@Copay1, @Copay2, @Admit) 
						THEN '$' + CONVERT(VARCHAR, FLOOR(p.OONBenefitValue))
					   WHEN p.OONBenefitTypeID = @Coins 
						THEN CONVERT(VARCHAR, FLOOR(p.OONBenefitValue * 100)) + '%'
					   ELSE ''
				  END
			 END  --@IsIN
			 + ' ' + bc.BenefitCategoryName
			 + 
			 CASE WHEN bc.IsDayRangeEnabled = 1 
				  THEN ' ' + CONVERT(VARCHAR, p.BenefitOrdinalID)
			      ELSE ''
			 END
		END
		FROM dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
		INNER JOIN dbo.LkpExtCMSBidServiceCategory bs WITH (NOLOCK)
			ON bc.BidServiceCatID = bs.BidServiceCategoryID
		INNER JOIN dbo.SavedPlanBenefitDetail p WITH (NOLOCK)
			ON bc.BenefitCategoryID = p.BenefitCategoryID
		WHERE bs.CostShareServiceCategoryCode = @XCostShareServiceCategoryCode
		  AND p.ForecastID = @XForecastID
		  AND p.IsBenefitYearCurrentYear = 0
		  AND p.INBenefitTypeID = @XBenefitTypeID
		  AND p.IsLiveIndex = 1
		ORDER BY p.BenefitCategoryID,
				 p.BenefitOrdinalID 
	END 
    RETURN @OutputText 
END
GO
