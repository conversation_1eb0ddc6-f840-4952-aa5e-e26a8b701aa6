SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------              
-- PROCEDURE NAME: [dbo].[spAppTrendExecuteProjProcessspCalcPlanFinal]    
--              
-- TYPE: SAME              
--              
-- AUTHOR: <PERSON> Pant            
--              
-- CREATED DATE: 2020-Apr-16	             
--              
-- DESCRIPTION: Procedure responsible for executing Trend_ProjProcess_spCalcPlanTrendsFinal
-- PARAMETERS:              
-- Input:               
--@UserID             
-- TABLES:               
-- Read:              
--                  
--                 
-- Write:              
--              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:              
--                
-- $HISTORY               
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE			VERSION		CHANGES MADE                 DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Apr-16		1		Initial Version				Kiran Pant  
-- 2020-Jun-10	    2	Renamed the sp name in the            Kiran Pant
--                comment section with prefix spAppTrend
-- 2020-Jun-12      3 Pass NULL as parameter in Trend_ProjProcess_spCalcPlanTrendsFinal   Manisha Tyagi
-- ----------------------------------------------------------------------------------------------------------------------              
CREATE PROCEDURE [dbo].[spAppTrendExecuteProjProcessspCalcPlanFinal]
(@UserID VARCHAR(7))
AS
SET NOCOUNT ON;
BEGIN

EXECUTE [dbo].[Trend_ProjProcess_spCalcPlanTrendsFinal] NULL,@UserID;

END;
GO
