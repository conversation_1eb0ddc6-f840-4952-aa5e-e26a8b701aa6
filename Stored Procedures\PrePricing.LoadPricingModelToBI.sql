SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[LoadPricingModelToBI]   
--    
-- AUTHOR: <PERSON>
--    
-- CREATED DATE: 2025-Jan-14   
-- Type: 
-- DESCRIPTION: Procedure responsible for intilaize prepricing data 
--    
-- PARAMETERS:    
-- Input: 
-- 

-- TABLES:   
--  

-- Read:    
--  

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE								DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-Jan-14		1		Initial Version								Adam Gilbert
-- 2025-Feb-06      2		Defect fix- Rx Benefit name change			Priyadarshini Deshmukh
-- 2025-Feb-06      3		Add Record for AppBenefitInterfaceControl   Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------   

CREATE PROCEDURE [PrePricing].[LoadPricingModelToBI] AS 
BEGIN

SET NOCOUNT ON;

TRUNCATE TABLE [PrePricing].PlanCrossWalkType
SET IDENTITY_INSERT [PrePricing].PlanCrossWalkType ON

INSERT INTO [PrePricing].PlanCrossWalkType ([CrosswalkTypeID], [CrossWalkTypeName], [LastUpdateDateTime], [LastUpdateByID])
VALUES
( 1, 'New Plan', NULL, NULL ), 
( 2, 'Consolidation', NULL, NULL ), 
( 3, 'Segmentation', NULL, NULL ), 
( 4, 'Termination', NULL, NULL ), 
( 5, 'Plan Flip', NULL, NULL )
SET IDENTITY_INSERT [PrePricing].PlanCrossWalkType OFF
UPDATE [PrePricing].PlanCrossWalkType SET [LastUpdateDateTime]=GETDATE(),[LastUpdateByID]='lag5251' WHERE 1=1

TRUNCATE TABLE PrePricing.MarketInputCategory
SET IDENTITY_INSERT [PrePricing].MarketInputCategory ON
INSERT INTO PrePricing.MarketInputCategory ([CategoryID], [CategoryName], [SortOrder], [LastUpdateDateTime], [LastUpdateByID])
VALUES
( 1, 'RX', 5, NULL, NULL ), 
( 2, 'MSBs', 6, NULL, NULL ), 
( 4, 'Part B Benefits', 4, NULL, NULL ), 
( 5, 'Deductible & MOOP', 2, NULL, NULL ), 
( 6, 'Not Priced in MAAUI / Free Form', 7, NULL, NULL ), 
( 7, 'Premium', 1, NULL, NULL ), 
( 8, 'Part A Benefits', 3, NULL, NULL ), 
( 9, 'OSBs', 9, NULL, NULL ), 
( 10, 'SCT Metrics', 0, N'2024-12-18T15:28:24.553', 'lag5251' )
SET IDENTITY_INSERT [PrePricing].MarketInputCategory OFF
UPDATE [PrePricing].MarketInputCategory SET [LastUpdateDateTime]=GETDATE(),[LastUpdateByID]='lag5251' WHERE 1=1

TRUNCATE TABLE PrePricing.MarketInputSubCategory
SET IDENTITY_INSERT [PrePricing].MarketInputSubCategory ON
INSERT INTO PrePricing.MarketInputSubCategory  ([SubCategoryID], [CategoryID], [SubCategoryName], [IsCostShareType], [SortOrder], [LastUpdateDateTime], [LastUpdateByID], [ABBenefitCategoryID], [IsReadOnly], [IsINValueOnly], [IsNumber], [IsPositiveNumber])
VALUES
( 1, 5, 'Combined Deductible', 0, 4, GETDATE(), 'LAG5251', NULL, 0, 1, 1, 1 ), 
( 2, 5, 'Combined MOOP', 0, 8, GETDATE(), 'LAG5251', NULL, 0, 1, 1, 1 ), 
( 3, 5, 'Deductible', 0, 3, GETDATE(), 'LAG5251', NULL, 0, 0, 1, 1 ), 
( 4, 5, 'Deductible Type', 0, 6, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 5, 5, 'MOOP', 0, 7, GETDATE(), 'LAG5251', NULL, 0, 0, 1, 1 ), 
( 6, 5, 'Part B Deductible', 0, 5, GETDATE(), 'LAG5251', NULL, 0, 1, 1, 1 ), 
( 7, 2, 'Acupuncture (ACU)', 0, 135, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 8, 2, 'Acupuncture (CLB)', 0, 136, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 9, 2, 'Bath Chairs (BCS)', 0, 119, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 11, 2, 'Blood Pressure Monitor (BPM)', 0, 120, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 13, 2, 'Dental (DEN)', 0, 108, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 14, 2, 'Diapers (DPR)', 0, 117, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 15, 2, 'Extended Over-the-Counter (EXT)', 0, 132, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 16, 2, 'Fitness Benefit (FTP)', 0, 121, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 19, 2, 'Hearing (HER)', 0, 110, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 20, 2, 'HFC&OTC Card SSBCI (SHF)', 0, 112, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 21, 2, 'HFC&OTC Card VBID (HFC)', 0, 111, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 27, 2, 'Incentive Rewards Program (INC)', 0, 130, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 29, 2, 'Naturopathy (NTR)', 0, 116, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 30, 2, 'Nursing Hotline (HST)', 0, 123, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 31, 2, 'Nutritional Benefit-1 (WDE)', 0, 124, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 32, 2, 'Nutritional Benefit-2 (WDE)', 0, 125, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 33, 2, 'Nutritional Benefit-3 (WDE)', 0, 126, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 34, 2, 'Nutritional Benefit-4 (WDE)', 0, 127, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 35, 2, 'Nutritional Benefit-5 (WDE)', 0, 128, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 41, 2, 'Over-the-counter Items (OTC)', 0, 113, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 44, 2, 'Personal Home Care (PHC)', 0, 122, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 45, 2, 'Projected Chiro Not MSB (CHR)', 0, 137, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 46, 2, 'Projected Podiatry Not MSB (POD)', 0, 138, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 47, 2, 'Smoking Cessation (SMC)', 0, 131, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 48, 2, 'Special Supplemental Benefit (SSB)', 0, 115, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 49, 2, 'Special Supplemental Benefit - Belle (BFC)', 0, 129, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 57, 2, 'Transportation (TRN)', 0, 114, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 58, 2, 'Vision (VIS)', 0, 109, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 59, 2, 'Wigs (WIG)', 0, 118, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 60, 6, 'Ambulance Emergency-Air Ambulance', 0, 141, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 61, 6, 'Ambulance Non-Emergency-Air Ambulance', 0, 142, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 62, 6, 'Ambulance Non-Emergency-Ground Ambulance', 0, 143, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 63, 6, 'Chiropractic Services (Medicare Covered)-Specialist''s Office', 0, 139, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 64, 6, 'Diagnostic Colonoscopy-Outpatient Hospital', 0, 144, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 65, 6, 'Diagnostic Mammography-Freestanding Radiological Facility', 0, 145, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 66, 6, 'Diagnostic Mammography-Specialist''s Office', 0, 146, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 67, 6, 'HMO Travel Benefit-', 0, 147, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 68, 6, 'Medicare Part B Covered Drugs-Insulin', 0, 148, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 69, 6, 'Observation Services-Outpatient Hospital', 0, 149, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 70, 6, 'Podiatry Services (Medicare Covered)-Specialist''s Office', 0, 140, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 71, 6, 'Urgently Needed Services-Urgent Care Center', 0, 150, GETDATE(), 'LAG5251', NULL, 0, 0, 0, 0 ), 
( 72, 8, 'IP Acute - Facility: Level 1', 1, 9, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 73, 8, 'IP Acute - Facility: Level 2', 1, 12, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 74, 8, 'IP Acute - Facility: Level 3', 1, 15, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 76, 8, 'IP Acute - Physician', 1, 36, GETDATE(), 'LAG5251', 125, 0, 0, 1, 1 ), 
( 77, 8, 'IP Acute L1: Day Range - Begin', 1, 10, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 78, 8, 'IP Acute L1: Day Range - End', 1, 11, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 79, 8, 'IP Acute L2: Day Range - Begin', 1, 13, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 80, 8, 'IP Acute L2: Day Range - End', 1, 14, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 81, 8, 'IP Acute L3: Day Range - Begin', 1, 16, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 82, 8, 'IP Acute L3: Day Range - End', 1, 17, GETDATE(), 'LAG5251', 65, 0, 0, 1, 1 ), 
( 85, 8, 'IP Psych - Facility: Level 1', 1, 27, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 86, 8, 'IP Psych - Facility: Level 2', 1, 30, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 87, 8, 'IP Psych - Facility: Level 3', 1, 33, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 89, 8, 'IP Psych - Physician', 1, 38, GETDATE(), 'LAG5251', 126, 0, 0, 1, 1 ), 
( 90, 8, 'IP Psych L1: Day Range - Begin', 1, 28, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 91, 8, 'IP Psych L1: Day Range - End', 1, 29, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 92, 8, 'IP Psych L2: Day Range - Begin', 1, 31, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 93, 8, 'IP Psych L2: Day Range - End', 1, 32, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 94, 8, 'IP Psych L3: Day Range - Begin', 1, 34, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 95, 8, 'IP Psych L3: Day Range - End', 1, 35, GETDATE(), 'LAG5251', 66, 0, 0, 1, 1 ), 
( 98, 8, 'IP Rehab - Facility: Level 1', 1, 18, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 99, 8, 'IP Rehab - Facility: Level 2', 1, 21, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 100, 8, 'IP Rehab - Facility: Level 3', 1, 24, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 102, 8, 'IP Rehab - Physician', 1, 37, GETDATE(), 'LAG5251', 132, 0, 0, 1, 1 ), 
( 103, 8, 'IP Rehab L1: Day Range - Begin', 1, 19, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 104, 8, 'IP Rehab L1: Day Range - End', 1, 20, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 105, 8, 'IP Rehab L2: Day Range - Begin', 1, 22, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 106, 8, 'IP Rehab L2: Day Range - End', 1, 23, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 107, 8, 'IP Rehab L3: Day Range - Begin', 1, 25, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 108, 8, 'IP Rehab L3: Day Range - End', 1, 26, GETDATE(), 'LAG5251', 131, 0, 0, 1, 1 ), 
( 111, 8, 'SNF - Facility: Level 1', 1, 39, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 112, 8, 'SNF - Facility: Level 2', 1, 42, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 113, 8, 'SNF - Facility: Level 3', 1, 45, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 115, 8, 'SNF - Physician', 1, 48, GETDATE(), 'LAG5251', 127, 0, 0, 1, 1 ), 
( 116, 8, 'SNF L1: Day Range - Begin', 1, 40, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 117, 8, 'SNF L1: Day Range - End', 1, 41, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 118, 8, 'SNF L2: Day Range - Begin', 1, 43, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 119, 8, 'SNF L2: Day Range - End', 1, 44, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 120, 8, 'SNF L3: Day Range - Begin', 1, 46, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 121, 8, 'SNF L3: Day Range - End', 1, 47, GETDATE(), 'LAG5251', 83, 0, 0, 1, 1 ), 
( 124, 4, 'Advanced Imaging - FSR', 1, 66, GETDATE(), 'LAG5251', 109, 0, 0, 1, 1 ), 
( 125, 4, 'Advanced Imaging - Office', 1, 67, GETDATE(), 'LAG5251', 110, 0, 0, 1, 1 ), 
( 126, 4, 'Advanced Imaging - OP', 1, 68, GETDATE(), 'LAG5251', 111, 0, 0, 1, 1 ), 
( 127, 4, 'Ambulance Emergency', 1, 71, GETDATE(), 'LAG5251', 50, 0, 0, 1, 1 ), 
( 128, 4, 'Cardiac Rehab', 1, 90, GETDATE(), 'LAG5251', 52, 0, 0, 1, 1 ), 
( 129, 4, 'Chiropractic', 1, 56, GETDATE(), 'LAG5251', 53, 0, 0, 1, 1 ), 
( 130, 4, 'Diabetic Monitoring Supplies', 1, 88, GETDATE(), 'LAG5251', 124, 0, 0, 1, 1 ), 
( 131, 4, 'Diabetic Shoes', 1, 89, GETDATE(), 'LAG5251', 128, 0, 0, 1, 1 ), 
( 132, 4, 'Dialysis - Dialysis Center', 1, 75, GETDATE(), 'LAG5251', 55, 0, 0, 1, 1 ), 
( 133, 4, 'Dialysis - OP Hosp', 1, 76, GETDATE(), 'LAG5251', 56, 0, 0, 1, 1 ), 
( 134, 4, 'DMAM - OP', 1, 84, GETDATE(), 'LAG5251', 58, 0, 0, 1, 1 ), 
( 135, 4, 'DME - High', 1, 77, GETDATE(), 'LAG5251', 129, 0, 0, 1, 1 ), 
( 136, 4, 'DME - Low', 1, 78, GETDATE(), 'LAG5251', 130, 0, 0, 1, 1 ), 
( 137, 4, 'Emergency Room', 1, 70, GETDATE(), 'LAG5251', 61, 0, 0, 1, 1 ), 
( 138, 4, 'Home Health', 1, 94, GETDATE(), 'LAG5251', 63, 0, 0, 1, 1 ), 
( 139, 4, 'Lab - FSL', 1, 65, GETDATE(), 'LAG5251', 91, 0, 0, 1, 1 ), 
( 140, 4, 'Lab - OP', 1, 64, GETDATE(), 'LAG5251', 92, 0, 0, 1, 1 ), 
( 141, 4, 'Nuclear Medicine', 1, 95, GETDATE(), 'LAG5251', 68, 0, 0, 1, 1 ), 
( 142, 4, 'OP Other', 1, 96, GETDATE(), 'LAG5251', 70, 0, 0, 1, 1 ), 
( 143, 4, 'Other Part B', 1, 97, GETDATE(), 'LAG5251', 122, 0, 0, 1, 1 ), 
( 144, 4, 'Part B Rx', 1, 79, GETDATE(), 'LAG5251', 71, 0, 0, 1, 1 ), 
( 145, 4, 'Part B Rx - Chemo', 1, 81, GETDATE(), 'LAG5251', 118, 0, 0, 1, 1 ), 
( 146, 4, 'Part B Rx at Pharmacy', 1, 80, GETDATE(), 'LAG5251', 108, 0, 0, 1, 1 ), 
( 147, 4, 'Partial Hospitalization', 1, 59, GETDATE(), 'LAG5251', 72, 0, 0, 1, 1 ), 
( 148, 4, 'PCP Cap', 1, 50, GETDATE(), 'LAG5251', 102, 0, 0, 1, 1 ), 
( 149, 4, 'PCP Non-Preferred Tier', 1, 58, GETDATE(), 'LAG5251', NULL, 0, 0, 1, 1 ), 
( 150, 4, 'PCP Preferred Tier', 1, 57, GETDATE(), 'LAG5251', NULL, 0, 0, 1, 1 ), 
( 151, 4, 'Physician Services PCP', 1, 49, GETDATE(), 'LAG5251', 73, 0, 0, 1, 1 ), 
( 152, 4, 'Physician Services Spec', 1, 52, GETDATE(), 'LAG5251', 97, 0, 0, 1, 1 ), 
( 153, 4, 'Podiatry', 1, 53, GETDATE(), 'LAG5251', 94, 0, 0, 1, 1 ), 
( 154, 4, 'Preventive Services', 1, 69, GETDATE(), 'LAG5251', 123, 0, 0, 1, 1 ), 
( 155, 4, 'Prosthetics', 1, 92, GETDATE(), 'LAG5251', 105, 0, 0, 1, 1 ), 
( 156, 4, 'Psych - OP', 1, 55, GETDATE(), 'LAG5251', 74, 0, 0, 1, 1 ), 
( 157, 4, 'Psych - Other Provider', 1, 54, GETDATE(), 'LAG5251', 93, 0, 0, 1, 1 ), 
( 158, 4, 'Rad Therapy', 1, 93, GETDATE(), 'LAG5251', 76, 0, 0, 1, 1 ), 
( 159, 4, 'Radiology - FSR', 1, 87, GETDATE(), 'LAG5251', 77, 0, 0, 1, 1 ), 
( 160, 4, 'Radiology OP - Facility', 1, 85, GETDATE(), 'LAG5251', 113, 0, 0, 1, 1 ), 
( 161, 4, 'Radiology OP - Physician', 1, 86, GETDATE(), 'LAG5251', 112, 0, 0, 1, 1 ), 
( 162, 4, 'SMAM - FSR', 1, 82, GETDATE(), 'LAG5251', 80, 0, 0, 1, 1 ), 
( 163, 4, 'SMAM - OP', 1, 83, GETDATE(), 'LAG5251', 81, 0, 0, 1, 1 ), 
( 164, 4, 'Spec Cap', 1, 51, GETDATE(), 'LAG5251', 103, 0, 0, 1, 1 ), 
( 165, 4, 'Supplies', 1, 91, GETDATE(), 'LAG5251', 104, 0, 0, 1, 1 ), 
( 166, 4, 'Surgery ASC - Facility', 1, 63, GETDATE(), 'LAG5251', 115, 0, 0, 1, 1 ), 
( 167, 4, 'Surgery ASC - Physician', 1, 60, GETDATE(), 'LAG5251', 114, 0, 0, 1, 1 ), 
( 168, 4, 'Surgery OP - Facility', 1, 62, GETDATE(), 'LAG5251', 117, 0, 0, 1, 1 ), 
( 169, 4, 'Surgery OP - Physician', 1, 61, GETDATE(), 'LAG5251', 116, 0, 0, 1, 1 ), 
( 170, 4, 'Therapy (AT,PT,ST,OT) - CORF', 1, 72, GETDATE(), 'LAG5251', 87, 0, 0, 1, 1 ), 
( 171, 4, 'Therapy (AT,PT,ST,OT) - OP', 1, 73, GETDATE(), 'LAG5251', 88, 0, 0, 1, 1 ), 
( 172, 4, 'Therapy (AT,PT,ST,OT) - Specialist', 1, 74, GETDATE(), 'LAG5251', 119, 0, 0, 1, 1 ), 
( 173, 7, 'Member Premium', 0, 1, GETDATE(), 'LAG5251', NULL, 0, 1, 1, 1 ), 
( 174, 7, 'Part B Giveback', 0, 2, GETDATE(), 'LAG5251', NULL, 0, 1, 1, 1 ), 
( 177, 1, 'Zero Dollar DSNP', 0, 102, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 179, 1, 'Anti Obesity', 0, 106, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 180, 1, 'COPD VBID', 0, 103, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 181, 1, 'DOAC VBID', 0, 104, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 182, 1, 'Drug Plan TYPE', 0, 98, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 183, 1, 'Erectile Dysfuntion', 0, 105, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 184, 1, 'Formulary', 0, 99, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 188, 1, 'Prescription Vitamins', 0, 107, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 189, 1, 'RX Deductible', 0, 100, GETDATE(), 'LAG5251', NULL, 1, 0, 1, 0 ), 
( 191, 1, 'Standard Rx String (Retail)', 0, 101, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 193, 9, 'OSB001~MyOption Dental High PPO', 0, 151, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 194, 9, 'OSB004~MyOption Vision', 0, 152, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 195, 9, 'OSB005~MyOption Plus', 0, 153, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 196, 9, 'OSB016~MyOption Platinum Dental', 0, 154, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 197, 9, 'OSB047~MyOption DEN204', 0, 155, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 198, 9, 'OSB048~MyOption DEN205', 0, 156, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 199, 9, 'OSB049~MyOption DEN206', 0, 157, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 200, 9, 'OSB050~MyOption DEN207', 0, 158, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 201, 9, 'OSB051~MyOption DEN432', 0, 159, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 202, 9, 'OSB052~MyOption DEN478', 0, 160, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 203, 1, 'Tiers excluded from deductible', 0, 161, GETDATE(), 'LAG5251', NULL, 1, 0, 0, 0 ), 
( 204, 10, 'UM (in $000s)', 0, NULL, GETDATE(), 'LAG5251', NULL, 1, 0, 1, 0 ), 
( 205, 10, 'Net Growth', 0, NULL, GETDATE(), 'LAG5251', NULL, 1, 0, 1, 0 ), 
( 206, 10, 'Average Members', 0, NULL, GETDATE(), 'LAG5251', NULL, 1, 0, 1, 0 ), 
( 207, 10, 'MER', 0, NULL, GETDATE(), 'LAG5251', NULL, 1, 0, 1, 0 ), 
( 208, 10, 'Bid Profit %', 0, NULL, GETDATE(), 'LAG5251', NULL, 1, 0, 1, 0 ), 
( 209, 2, 'Solutran Admin Offset (ADM)', 0, 133, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 ), 
( 210, 2, 'Music Therapy (MUS)', 0, 134, GETDATE(), 'LAG5251', NULL, 0, 1, 0, 0 )
SET IDENTITY_INSERT [PrePricing].MarketInputSubCategory OFF
UPDATE [PrePricing].MarketInputSubCategory SET [LastUpdateDateTime]=GETDATE(),[LastUpdateByID]='lag5251' WHERE 1=1

--Plan -- pull live,filed,nonhidden plans from pricing model.
TRUNCATE TABLE prepricing.planinfo;
INSERT INTO PrePricing.PlanInfo
( PlanYear, CPS, MarketID, PlanDescription, DataSource, IsEnabled,
ProductTypeID,SNPTypeID,LastUpdateDateTime,LastUpdateByID,plantypeid )
SELECT spi.planyear,CPS AS CPS, Actuarialmarketid,
NULL AS planDescription,'MaaModels' AS DataSource, 1 AS IsEnabled,spi.ProductTypeID ,
spi.SNPTypeID ,GETDATE(), 'INITIAL LOAD',spi.plantypeid
FROM SavedForecastSetup sfs
JOIN SavedPlanInfo spi 
ON sfs.PlanInfoID = spi.PlanInfoID 
WHERE sfs.planyear=dbo.fngetbidyear() 
	AND sfs.isHidden = 0 
	AND sfs.IsFIledPlan = 1 
	AND sfs.IsLiveIndex = 1

--Service Area Mapping Load
TRUNCATE TABLE PrePricing.ServiceAreaMapping
INSERT INTO PrePricing.ServiceAreaMapping(PlanInfoID, SSStateCountyCD, lastupdatedatetime,lastupdatebyid)
	SELECT 
	ppi.PlanInfoID,
	mme.CountyCode,
	GETDATE(),
	'INITIAL LOAD'
	FROM dbo.SavedPlanHeader ph
	JOIN PrePricing.PlanInfo ppi
	ON ph.ContractNumber + '-' + ph.PlanID + '-' + ph.SegmentID  = ppi.CPS
	CROSS APPLY dbo.fnGetServiceAreaExtract(ph.ForecastID) mme -- formerly member month extract
	WHERE ph.IsFiledPlan = 1 AND ishidden = 0 AND ph.PlanyearID = dbo.fngetbidyear()--	SELECT * FROM prepricing.serviceareamapping



--Fill Market Input Value using Bid Year data
--TRUNCATE TABLE prepricing.MarketInputValue; --truncate wont work on temporal
DELETE FROM prepricing.MarketInputValue WHERE 1=1;
MERGE INTO prepricing.MarketInputValue t
USING (SELECT DISTINCT * FROM prepricing.[fnGetBidYearMarketInputValues]()) s
ON t.planinfoid = s.planinfoid AND t.subcategoryid = s.subcategoryid
WHEN MATCHED THEN
   UPDATE SET t.invalue = s.BidYearINValue, 
   t.oonvalue = s.BidYearOONValue,
   t.InCostShareType = s.InCostShareType,
   t.oonCostShareType = s.ooncostsharetype,
   t.lastupdatedatetime = GETDATE(),
   t.lastupdatebyid = 'INITIAL LOAD'
WHEN NOT MATCHED THEN
   INSERT (PlanInfoId,SubcategoryID,Invalue,OONValue,benefitchangevalue,note,lastupdatebyid,lastupdatedatetime,InCostShareType,OONCostShareType,LastUpdateSource)
   VALUES (s.planinfoid,s.subcategoryid,s.BidYearINValue,s.BidYearOONValue,NULL,'','INIT',GETDATE(),s.incostsharetype,s.ooncostsharetype,'INIT Script');

   DELETE FROM prepricing.AppBenefitInterfaceControl WHERE 1=1;
   INSERT INTO Prepricing.AppBenefitInterfaceControl VALUES ( GETDATE(), 60)
 END
GO
