SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnGetMAReportBLInfo]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportBLInfo
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2009-Mar-12
-- HEADER UPDATED: 2010-Oct-05
--
-- DESCRIPTION:  To pull benefits for bidable active plans (used for Auditing Benefits)
--
-- PARAMETERS:
--	Input:
--      @PlanYearID
--      @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--      ArcLkpExtCMSBidServiceCategory
--      ArcLkpIntBenefitCategory
--      ArcSavedPlanBenefitDetail
--      ArcSavedPlanHeader
--      LkpExtCMSBidServiceCategory
--      LkpIntBenefitCategory
--      LkpIntBenefitType
--		MAReportPlanLevel
--      SavedPlanBenefitDetail
--      SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnUseAlternativeEmRoomDesc
--		fnGetBidYear
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-12		1		    Initial Version										                Keith Galloway
-- 2009-Apr-01		2		    Added Ordinal ID, Now in scope for auditing			                Keith Galloway
-- 2009-Apr-21		3		    Trim Cost Share Types								                Keith Galloway
-- 2009-Apr-30		4		    Force Level 2 SNF Day Range = 100					                Keith Galloway
-- 2010-Jan-28		5		    Added fields for Common Language Specification		                Jake Gaecke
-- 2010-Mar-16		6		    No longer returns IN/OON Benefit Values when IN/OON BenefitType     Jake Gaecke
--                                  is NULL
-- 2010-Oct-05      7           Added Arc tables for past year reference. Removed @PlanVersion      Joe Casey
--                                  as a parameter. Default PlanVersion = 1. Removed
--                                  PerIntBenefitCollectionDetail, PerExtContractNumberPlanIDDetail.
--                                  Default PBPServiceId, PlaceOfTreatmentId = 0. BenefitYearID
--                                  now IsBenefitYearCurrentYear.  DayRanges replaced by RangeEnds.
-- 2010-Nov-10		8			Added PlanTypeName													Jiao Chen
-- 2011-Jan-21      9           Altered IN and OON benefit description for day ranges (to account   Casey Sanders
--                              for multiple SNF tiers).  
-- 2011-Feb-10		10			Added logic that displays the benefit value for the benefit cat		Craig Wright
--									as $0 if IsPhysAddOn = 1 and BenefitTypeID <> 1.  Otherwise, 
--									we display the actual benefit value.
-- 2011-May-23		11			Coins no longer display '/admit' for 65, 66, and 83					Joe Casey
-- 2011-Sep-14		12			Commented out Eric King description within INBenefitDescription		Alex Rezmerski
-- 2013-Mar-21		13			0369 - Including multi-tiered benefits								Mason Roberts
-- 2013-Mar-27		14			0369 - Changed Benefit Description									Mason Roberts
-- 2013-Apr-19		15			Removed Day Ranges for OON Coin										Mason Roberts
-- 2013-Oct-07		16			Modified to Include SegmentId										Anubhav Mishra
-- 2015-Oct-09      17          Modified logic to exclude bidYear from Arc Tables                   Kritika Singh
-- 2016-Feb-03		18			Modified logic to resolve MLR defects								Pooja Dahiya 
-- 2016-Feb-29		19			Added columns IsINEnabled and IsOONEnabled							Pooja Dahiya
-- 2016-Sep-10		20			Added explicit column names while selecting data 
--								from LkpExtCMSBidServiceCategory									Pooja Dahiya
-- 2022-May-01		21			MAAUI MIgration; replaced the input variable from @PlanIndex to
--								@ForecastID; for table SavedPlanBenefitDetail, replaced the 
--								reference from PlanIndex to ForecastID; removed table 
--								SavedPlanIPTierDetail as now reading from SavedPlanBenefitDetail;
--								removed GROUP BY statement; changed the logic for 
--								INBenefitDayRangeEnd to get value 1 when values are 0 in the 
--								source table; changed the logic for IN/OONBenefitType to be based
--								on value from LkpIntBenefitType, except for DayRange where value 
--								assigned is Copay; changed the logic for BenefitOrdinalID as this 
--								value pulled from spdb only; changed the logic for 
--								IN/OONBenefitValue; removed nested queries; function dropped and 
--								recreated; function needs to be recoded if
--								dbo.ArcSavedPlanBenefitDetail changes to fit the new structure;
--								pulling from archive tables will need to change once ForecastID
--								field moves to the new logic; limiting the pull to only
--								specified @PlanYearID												Aleksandar Dimitrijevic	
-- 2022-Dec-16		22			4119454 Deployment objects from Dev-Grow							Aleksandar Dimitrijevic
-- 2023-Aug-22		23			Ignore archive records when planyear=bidyear.
--								Filter benefits to live option only									Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportBLInfo]
    (
    @PlanYearID SMALLINT,
    @ForecastID INT
    )
RETURNS @Results TABLE
	(
	 PlanYearID SMALLINT,
	 ContractNumber CHAR(5),
	 PlanID CHAR(3),
	 SegmentId CHAR(3),
	 PBPServiceId BIT,
	 PlaceOfTreatmentId BIT,
	 ForecastID INT,
	 PlanVersion BIT,
	 BenefitCategoryID SMALLINT,
	 BenefitOrdinalID TINYINT,
	 BenefitCategoryName VARCHAR(80),
	 PlanTypeName VARCHAR(100),
	 INBenefitType CHAR(11),
	 INBenefitValue DECIMAL(9,4),
	 INBenefitDayRange TINYINT,
	 OONBenefitType CHAR(11),
	 OONBenefitValue DECIMAL(9,4),
	 OONBenefitDayRange TINYINT,
	 INBenefitDescription VARCHAR(MAX),
	 OONBenefitDescription VARCHAR(MAX)
	) AS
BEGIN
	
	DECLARE @BidYear SMALLINT = dbo.fnGetBidYear()
	DECLARE @Copay CHAR(5) = 'Copay'

				--SavedPlanBenefitDetail
				DECLARE @SPBD TABLE
					(
					 PlanYearID SMALLINT,
					 ForecastID INT,
					 IsBenefitYearCurrentYear BIT,
					 BenefitCategoryID SMALLINT,
					 BenefitOrdinalID TINYINT,
					 INBenefitTypeID TINYINT,
					 INDayRangeBegin TINYINT,
					 INDayRangeEnd TINYINT,
					 INBenefitValue DECIMAL(9,4),
					 OONBenefitTypeID TINYINT,
					 OONDayRangeBegin TINYINT,
					 OONDayRangeEnd TINYINT,
					 OONBenefitValue DECIMAL(9,4)
					)

				INSERT INTO @SPBD
				SELECT PlanYearID,
					   ForecastID,
					   IsBenefitYearCurrentYear,
					   BenefitCategoryID,
					   BenefitOrdinalID,
					   INBenefitTypeID,
					   INDayRangeBegin,
					   INDayRangeEnd,
					   INBenefitValue,
					   OONBenefitTypeID,
					   OONDayRangeBegin,
					   OONDayRangeEnd,
					   OONBenefitValue
				FROM dbo.SavedPlanBenefitDetail	  WITH(NOLOCK)
				WHERE IsBenefitYearCurrentYear = 0
			      AND IsLiveIndex = 1
				  AND ForecastID = @ForecastID
				  AND PlanYearID = @PlanYearID
				--------------------------------------
				UNION ALL
				--------------------------------------
				SELECT PlanYearID,
					   ForecastID,
					   IsBenefitYearCurrentYear,
					   BenefitCategoryID,
					   BenefitOrdinalID,
					   INBenefitTypeID,
					   INDayRangeBegin,
					   INDayRangeEnd,
					   INBenefitValue,
					   OONBenefitTypeID,
					   OONDayRangeBegin,
					   OONDayRangeEnd,
					   OONBenefitValue
				FROM dbo.ArcSavedPlanBenefitDetail WITH(NOLOCK)
				WHERE PlanYearID = @PlanYearID
				  AND ForecastID = @ForecastID
				  AND IsBenefitYearCurrentYear = 0
				  AND IsLiveIndex = 1
				  AND PlanYearID <> @BidYear
				--------------------------------------

				-- Saved Plan Header
				DECLARE @SPH TABLE 
					(
					 PlanyearID SMALLINT,
					 ForecastID INT,
					 ContractNumber CHAR(5),
					 PlanID CHAR(3),
					 SegmentId CHAR(3)
					)

				INSERT INTO @SPH
				SELECT PlanyearID,
					   ForecastID,
					   ContractNumber,
					   PlanID,
					   SegmentId
				FROM dbo.SavedPlanHeader WITH(NOLOCK)
				WHERE PlanyearID = @PlanYearID
				  AND ForecastID = @ForecastID
				--------------------------------------
				UNION ALL
				--------------------------------------
				SELECT PlanyearID,
					   ForecastID,
					   ContractNumber,
					   PlanID,
					   SegmentId
				FROM dbo.ArcSavedPlanHeader	 WITH(NOLOCK)
				WHERE PlanyearID = @PlanYearID
					  AND PlanYearID <> @BidYear
				---------------------------------------

				--Benefit Category
				DECLARE @BC TABLE
					(
					 PlanYearID SMALLINT,
					 BenefitCategoryID SMALLINT,
					 BenefitCategoryName VARCHAR(100),
					 BidServiceCatID SMALLINT,
					 IsDayRangeEnabled BIT,
					 IsPhysAddOn BIT
					)

				INSERT INTO @BC
				SELECT PlanYearID,
					   BenefitCategoryID,
					   BenefitCategoryName,
					   BidServiceCatID,
					   BidServiceCatID,
					   IsPhysAddOn
				FROM dbo.LkpIntBenefitCategory WITH(NOLOCK)
				---------------------------------------
				UNION ALL
				---------------------------------------
				SELECT PlanYearID,
					   BenefitCategoryID,
					   BenefitCategoryName,
					   BidServiceCatID,
					   BidServiceCatID,
					   IsPhysAddOn
			    FROM  dbo.ArcLkpIntBenefitCategory WITH(NOLOCK)
				WHERE PlanYearID = @PlanYearID
					  AND PlanYearID <> @BidYear
				--------------------------------------

				--Bid Service Category
				DECLARE @BSC TABLE
					(
					 PlanYearID SMALLINT,
					 BidServiceCategoryID SMALLINT,
					 ServiceCategoryCode VARCHAR(2)
					)

				INSERT INTO @BSC	
				SELECT PlanYearID
					  ,BidServiceCategoryID
					  ,ServiceCategoryCode
				FROM dbo.LkpExtCMSBidServiceCategory WITH(NOLOCK)
				--------------------------------
				UNION ALL
				--------------------------------
				SELECT PlanYearID
					   ,BidServiceCategoryID
					   ,ServiceCategoryCode 
			  FROM dbo.ArcLkpExtCMSBidServiceCategory WITH(NOLOCK) 
			  WHERE PlanYearID = @PlanYearID
			  		AND PlanYearID <> @BidYear
			----------------------------------------

			--FINAL table
			DECLARE @Final TABLE
				(
				 PlanYearID SMALLINT,
				 ContractNumber CHAR(5),
				 PlanID CHAR(3),
				 SegmentId CHAR(3),
				 PBPServiceId BIT,
				 PlaceOfTreatmentId BIT,
				 ForecastID INT,
				 PlanVersion BIT,
				 IsDayRangeEnabled BIT,
				 BenefitCategoryID SMALLINT,
				 ServiceCategoryCode VARCHAR(2),
				 BenefitOrdinalID TINYINT,
				 BenefitCategoryName VARCHAR(80),
				 PlanTypeName VARCHAR(100),
				 INBenefitTypeID TINYINT,
				 INBenefitType CHAR(11),
				 INBenefitValue DECIMAL(9,4),
				 INBenefitDayRangeBegin TINYINT,
				 INBenefitDayRangeEnd TINYINT,
				 OONBenefitTypeID TINYINT,
				 OONBenefitType CHAR(11),
				 OONBenefitValue DECIMAL(9,4),
				 OONBenefitDayRangeBegin TINYINT,
				 OONBenefitDayRangeEnd TINYINT
				)

			INSERT INTO @Final
			SELECT spbd.PlanYearID,
				   sph.ContractNumber,
				   sph.PlanID,
				   sph.SegmentId,  --Added SegmentId
				   PBPServiceId = 0,
				   PlaceOfTreatmentId = 0,
				   spbd.ForecastID,
				   PlanVersion = 1,
				   bc.IsDayRangeEnabled,
				   spbd.BenefitCategoryID,
				   bsc.ServiceCategoryCode,
				   BenefitOrdinalID = spbd.BenefitOrdinalID,
				   bc.BenefitCategoryName,
				   pd.PlanTypeName,
				   spbd.INBenefitTypeID,
				   INBenefitType = CASE WHEN spbd.INBenefitTypeID IS NULL
										THEN NULL
									    WHEN spbd.INBenefitTypeID = 5
										THEN @Copay
										ELSE RTRIM(ibt.Name)
								   END,
				   INBenefitValue = CASE WHEN spbd.INBenefitTypeID <> 1 AND bc.IsPhysAddOn = 1
										 THEN 0
										 ELSE spbd.INBenefitValue
									END,
				   INBenefitDayRangeBegin = spbd.INDayRangeBegin,
				   INBenefitDayRangeEnd = CASE WHEN spbd.INDayRangeEnd = 0
											   THEN 1
											   ELSE spbd.INDayRangeEnd
										  END,
				   spbd.OONBenefitTypeID,
				   OONBenefitType = CASE WHEN spbd.OONBenefitTypeID IS NULL
										 THEN NULL
										 WHEN spbd.OONBenefitTypeID = 5
										 THEN @Copay
										 ELSE RTRIM(obt.Name)
								    END,
				   OONBenefitValue = CASE WHEN spbd.OONBenefitTypeID <> 1 AND bc.IsPhysAddOn = 1
										  THEN 0
										  ELSE spbd.OONBenefitValue
									 END,
				   OONBenefitDayRangeBegin = spbd.OONDayRangeBegin,
				   OONBenefitDayRangeEnd = CASE WHEN spbd.OONDayRangeEnd = 0
											    THEN 1
											    ELSE spbd.OONDayRangeEnd
										   END
			FROM @SPBD spbd

			INNER JOIN @SPH sph
				ON spbd.PlanYearID = sph.PlanYearID
				AND	spbd.ForecastID = sph.ForecastID
			INNER JOIN @BC bc
				ON spbd.PlanYearID = bc.PlanYearID
				AND spbd.BenefitCategoryID = bc.BenefitCategoryID
			LEFT JOIN dbo.LkpIntBenefitType ibt	WITH(NOLOCK)
				ON spbd.INBenefitTypeID = ibt.BenefitTypeID
			LEFT JOIN dbo.LkpIntBenefitType obt	WITH(NOLOCK)
				ON spbd.OONBenefitTypeID = obt.BenefitTypeID
			INNER JOIN @BSC bsc
				ON bsc.BidServiceCategoryID = bc.BidServiceCatID
				AND bsc.PlanYearID = bc.PlanYearID
			INNER JOIN dbo.MAReportPlanLevel pd	WITH(NOLOCK)
				ON pd.ForecastID =  spbd.ForecastID 
				AND pd.PlanYearID = spbd.PlanYearID
	----------------------------------------------------------------------------------------------
	-- INSERT RESULTS
	----------------------------------------------------------------------------------------------
    INSERT @Results
	SELECT
        PlanYearID,
        ContractNumber,
        PlanID,
        SegmentId,  --Added SegmentId
		PBPServiceId,
		PlaceOfTreatmentId,
        ForecastID,
        PlanVersion,
        BenefitCategoryID,
		BenefitOrdinalID,
		BenefitCategoryName,
		PlanTypeName,
		INBenefitType,
		INBenefitValue,
		INBenefitDayRange = INBenefitDayRangeEnd,
		OONBenefitType,
		OONBenefitValue,
		OONBenefitDayRange = OONBenefitDayRangeEnd,
		INBenefitDescription =
			CASE WHEN INBenefitType = 'Coinsurance'
					THEN CAST(CAST(INBenefitValue * 100 AS DECIMAL(10,2)) AS VARCHAR) + '%'
				 WHEN INBenefitType = @Copay THEN
						CASE WHEN INBenefitTypeID != 5
								THEN '$' + CAST(CAST(INBenefitValue AS DECIMAL(10,2)) AS VARCHAR)
								ELSE '$' + CAST(CAST(INBenefitValue AS DECIMAL(10,2)) AS VARCHAR)
							+ '/day, Days (' + CAST(INBenefitDayRangeBegin AS VARCHAR)
							+ '-' + CAST(INBenefitDayRangeEnd AS VARCHAR) + ')'
						END
				 WHEN INBenefitType = 'Admit'
					THEN '$' + CAST(CAST(INBenefitValue AS DECIMAL(10,2)) AS VARCHAR) + '/admit'
				ELSE NULL
			END,
		OONBenefitDescription =
			CASE WHEN OONBenefitType = 'Coinsurance'
					THEN CAST(CAST(OONBenefitValue * 100 AS DECIMAL(10,2)) AS VARCHAR) + '%'
				 WHEN OONBenefitType = @Copay THEN
						CASE WHEN OONBenefitTypeID != 5
								THEN '$' + CAST(CAST(OONBenefitValue AS DECIMAL(10,2)) AS VARCHAR)
							 ELSE '$' + CAST(CAST(OONBenefitValue AS DECIMAL(10,2)) AS VARCHAR)
							+ '/day, Days (' + CAST(OONBenefitDayRangeBegin AS VARCHAR)
							+ '-' + CAST(OONBenefitDayRangeEnd AS VARCHAR) + ')'
						END
				 WHEN OONBenefitType = 'Admit'
					THEN '$' + CAST(CAST(OONBenefitValue AS DECIMAL(10,2)) AS VARCHAR) + '/admit'
				ELSE NULL
			END
	FROM @Final
	 RETURN

END
GO