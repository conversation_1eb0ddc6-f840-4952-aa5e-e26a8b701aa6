SET AN<PERSON>_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- FUNCTION NAME: fnAppGetMABPTWS4Percent
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2009-Apr-08
-- HEADER UPDATED: 2011-Jan-12
--
-- DESCRIPTION: Function responsible for listing percent values on the MA-BPT Worksheet 4 in the MAPD Model.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		CalcActEquivByBenefitCategory
--		LkpExtCMSBidServiceCategory
--		LkpIntAddedBenefitType
--		LkpIntBenefitCategory
--		SavedPlanAddedBenefits
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetSafeDivisionResult
--		fnGetSafeDivisionResultReturnOne
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE														DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2009-Apr-08		1           Initial Version														Keith Galloway
-- 2009-Apr-29      2           Added PlanYearID join on spab.  Added Case statement for COB		Brian Lake
--                                  Added extra UNION to include Dual Type 2 for COB
-- 2009-May-14		3			Removed the where clause so Added benefits would appear				Keith Galloway
-- 2009-Dec-16		4			Where clause was not removed in last version, but is in this		Joe Casey
--									version
-- 2011-Jan-10      5           Revised for 2012 database											Michael Siekerka	
-- 2011-Jan-19      6           Changed safe division in PercentCoveredAllowed to return 1          Michael Siekerka						
-- 2011-Jun-14		7			Changed PlanYearId to return SMALLINT instead of INT				Bobby Jaegers
-- 2014-Mar-11		8			Modified for SQS													Mike Deren
-- 2014-Mar-13      9           Fixed Allowed rounding and auxillary calculations for DE#           Aaron Schaffer
-- 2014-Mar-13      10          Added WHERE statement for ServiceCategoryCode IS NOT NULL           Aaron Schaffer
-- 2014-May-15		11			Modified for Related parties and SQS								Mike Deren
-- 2014-July-11		12			Removed Sum of Product Difference on %CoveredAllowed,Net			Mike Deren
-- 2015-Nov-23		13			Updated Percent Covered Calculations to make sure values are not    Jordan Purdue
--									greater than 1
-- 2018-Jan-16		14			Changed the Union to a Union All to ensure all values are present   Jordan Purdue
-- 2023-Jan-20      15          Fixed integer overflow                                              Alex Brandt
-- 2023-Sep-13      16			Added no lock for the tables										Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS4Percent]
	(
    @ForecastID INT
	)
RETURNS @Results TABLE
    (
    PlanYearID SMALLINT,
    ForecastID INT,
	DualEligibleTypeID TINYINT,
    ServiceCategoryCode char(2),
    Allowed decimal (12, 6),--was 10,6
    CostShare decimal (10, 6),
    Net decimal (12, 6),
    PercentCoveredAllowed decimal (7, 6),
    PercentCoveredCostShare decimal (7, 6),
    FFSActEquivCostShare decimal (7, 6),
    PlanCostShareMedicareCovered decimal (24, 18),
    MedicareCoveredAllowed decimal (24, 18),
    MedicareCoveredCostShare decimal (12, 6),
    MedicareCoveredNet decimal (12, 6),
    AddedBenefitNet decimal (10, 6),
    CostShareReduction decimal (12, 6),
    SupplementalBenefitTotal decimal (12, 6),
    IsPercentCoveredAllowedShown bit,
    IsPercentCoveredCostShareShown bit
    ) AS

    BEGIN
        -- These were chosen as representative of the FFS Cost Share values on WS 4, 
        --    and while BenefitCategoryIDs are currently hardcoded, in the future
        --    they need to be chosen possibly from a not yet existing Lkp table.
        DECLARE @IPFacilityFFSCostShare DECIMAL (14, 6),
                @SNFFFSCostShare DECIMAL (14, 6),
                @AmbERFFSCostShare DECIMAL (14, 6),
                @PlanYearID SMALLINT

        SELECT @PlanYearID = dbo.fnGetBidYear()

        SELECT @IPFacilityFFSCostShare = MAX(FFSActEquivCostShare)
        FROM CalcActEquivByBenefitCategory WITH(NOLOCK)        
        WHERE ForecastID = @ForecastID
            AND BenefitCategoryID = 65 -- This is IP Acute 

        SELECT @SNFFFSCostShare = Max(FFSActEquivCostShare)
        FROM CalcActEquivByBenefitCategory WITH(NOLOCK)
        WHERE ForecastID = @ForecastID
            AND BenefitCategoryID = 83 -- This is SNF

        SELECT @AmbERFFSCostShare = Max(FFSActEquivCostShare)
        FROM CalcActEquivByBenefitCategory WITH(NOLOCK)
        WHERE ForecastID = @ForecastID
            AND BenefitCategoryID = 50  -- This is Amb ER


        INSERT @Results
         SELECT
            @PlanYearID,
            ae.ForecastID,
			ae.DualEligibleTypeID,
            bs.ServiceCategoryCode,
            Allowed = (CASE ae.DualEligibleTypeID -- Projected Allowed should include a full year of sequestration
						WHEN 0
							THEN SUM(cbpnon.INAllowed + cbpnon.OONAllowed)
						WHEN 1
							THEN SUM(cbpdual.INAllowed + cbpdual.OONAllowed)
						ELSE NULL
					  END)
					-
					  (CASE ae.DualEligibleTypeID -- Conditional statement needed to match BPT's WS4 calculations where DE# formulas differs from NDE# due to cost share requirements for DE#.
						WHEN 0
							THEN 0
						WHEN 1
							THEN SUM(ae.CostShare)
						ELSE 0
						END), 
            CostShare = SUM(ae.CostShare),
            Net = (CASE ae.DualEligibleTypeID WHEN 0 THEN SUM(cbpnon.INAllowed + cbpnon.OONAllowed) WHEN 1 THEN SUM(cbpdual.INAllowed + cbpdual.OONAllowed) ELSE NULL END) - SUM(ae.CostShare), --Net should equal Project Allowed with full year of sequestration minus Cost Share that is based on non-sequestered values.
            PercentCoveredAllowed = 
                CASE WHEN bs.IsPercentCoveredAllowedShown = 1
                    THEN CASE ae.DualEligibleTypeID WHEN 0 THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(round(ae.PercentCoveredAllowed,6) * round((cbpnon.INAllowed + cbpnon.OONAllowed),6)),SUM(round(cbpnon.INAllowed + cbpnon.OONAllowed,6)))
                    WHEN 1 THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(round(ae.PercentCoveredAllowed,6) * round((cbpdual.INAllowed + cbpdual.OONAllowed),6)),SUM(round(cbpdual.INAllowed + cbpdual.OONAllowed,6))) END
                    ELSE NULL
                END,
            PercentCoveredCostShare =
                CASE WHEN bs.IsPercentCoveredCostShareShown = 1
                    THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(round(ae.PercentCoveredCostShare,6) * round(ae.CostShare,6)), SUM(round(ae.CostShare,6)))
                    ELSE NULL
                END,
            FFSActEquivCostShare = MAX(
                CASE 
                    WHEN bs.BidServiceCategoryID in (1, 2) -- Category A: IP Facility
                        THEN @IPFacilityFFSCostShare 
                    WHEN bs.BidServiceCategoryID = 3 -- Category B: SNF
                        THEN @SNFFFSCostShare
                    WHEN bs.BidServiceCategoryID BETWEEN 5 AND 22 -- Category D-K: 
                        THEN @AmbERFFSCostShare
                    ELSE -- Everything else, including Home Health
                        0
                END),    
            PlanCostShareMedicareCovered = SUM(ae.PercentCoveredCostShare * ae.CostShare),
            MedicareCoveredAllowed = 
            						CASE WHEN bs.IsPercentCoveredAllowedShown = 1
										THEN CASE ae.DualEligibleTypeID WHEN 0 THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpnon.INAllowed + cbpnon.OONAllowed)),SUM(cbpnon.INAllowed + cbpnon.OONAllowed))
										WHEN 1 THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpdual.INAllowed + cbpdual.OONAllowed)),SUM(cbpdual.INAllowed + cbpdual.OONAllowed)) END
										ELSE NULL
									END *
								   SUM(CASE ae.DualEligibleTypeID -- Conditional statement needed to match BPT's DE# calculation that differs from NDE#
										WHEN 0
											THEN (cbpnon.INAllowed + cbpnon.OONAllowed) - 0
										WHEN 1
											THEN (cbpdual.INAllowed + cbpdual.OONAllowed) - (ae.CostShare)
										ELSE NULL
									  END),
            MedicareCoveredCostShare = (CASE ae.DualEligibleTypeID -- Conditional statement needed to match BPT's DE# calculation that differs from NDE#
											WHEN 0
												THEN SUM(cbpnon.INAllowed + cbpnon.OONAllowed)
											WHEN 1
												THEN 0
											ELSE NULL
									    END)
									 * (CASE -- PercentCoveredAllowed
											WHEN bs.IsPercentCoveredAllowedShown = 1
												THEN
													CASE ae.DualEligibleTypeID
														WHEN 0
															THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpnon.INAllowed + cbpnon.OONAllowed)),SUM(cbpnon.INAllowed + cbpnon.OONAllowed))
														WHEN 1
															THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpdual.INAllowed + cbpdual.OONAllowed)),SUM(cbpdual.INAllowed + cbpdual.OONAllowed))
													END
											ELSE NULL
										END)
									 * (MAX --FFSActEquivCostShare
											(CASE 
												WHEN bs.BidServiceCategoryID in (1, 2)
													THEN @IPFacilityFFSCostShare 
												WHEN bs.BidServiceCategoryID = 3
													THEN @SNFFFSCostShare
												WHEN bs.BidServiceCategoryID BETWEEN 5 AND 22
													THEN @AmbERFFSCostShare
												ELSE
													0
											END)),
            MedicareCoveredNet = 
									-- Medicare Covered Allowed
									(CASE WHEN bs.IsPercentCoveredAllowedShown = 1
										THEN CASE ae.DualEligibleTypeID WHEN 0 THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpnon.INAllowed + cbpnon.OONAllowed)),SUM(cbpnon.INAllowed + cbpnon.OONAllowed))
										WHEN 1 THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpdual.INAllowed + cbpdual.OONAllowed)),SUM(cbpdual.INAllowed + cbpdual.OONAllowed)) END
										ELSE NULL
									END *
								   SUM(CASE ae.DualEligibleTypeID -- Conditional statement needed to match BPT's DE# calculation that differs from NDE#
										WHEN 0
											THEN (cbpnon.INAllowed + cbpnon.OONAllowed) - 0
										WHEN 1
											THEN (cbpdual.INAllowed + cbpdual.OONAllowed) - (ae.CostShare)
										ELSE NULL
									  END))
									  -
									  ---- Medicare Covered Cost Share
									  (CASE ae.DualEligibleTypeID -- Conditional statement needed to match BPT's DE# calculation that differs from NDE#
											WHEN 0
												THEN SUM(cbpnon.INAllowed + cbpnon.OONAllowed)
											WHEN 1
												THEN 0
											ELSE NULL
									    END)
									 * (CASE -- PercentCoveredAllowed
											WHEN bs.IsPercentCoveredAllowedShown = 1
												THEN
													CASE ae.DualEligibleTypeID
														WHEN 0
															THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpnon.INAllowed + cbpnon.OONAllowed)),SUM(cbpnon.INAllowed + cbpnon.OONAllowed))
														WHEN 1
															THEN dbo.fnGetSafeDivisionResultReturnOne(SUM(ae.PercentCoveredAllowed * (cbpdual.INAllowed + cbpdual.OONAllowed)),SUM(cbpdual.INAllowed + cbpdual.OONAllowed))
													END
											ELSE NULL
										END)
									 * (MAX --FFSActEquivCostShare
											(CASE 
												WHEN bs.BidServiceCategoryID in (1, 2)
													THEN @IPFacilityFFSCostShare 
												WHEN bs.BidServiceCategoryID = 3
													THEN @SNFFFSCostShare
												WHEN bs.BidServiceCategoryID BETWEEN 5 AND 22
													THEN @AmbERFFSCostShare
												ELSE
													0
											END)),
            AddedBenefitNet = SUM(ae.AddedBenefitNet),
            CostShareReduction = SUM(ae.CostShareReduction),
            SupplementalBenefitTotal = SUM(ae.SupplementalBenefitTotal),
            bs.IsPercentCoveredAllowedShown,
            bs.IsPercentCoveredCostShareShown
        FROM fnAppGetMABPTWS4CostShares(@ForecastID) ae
        INNER JOIN LkpIntBenefitCategory bc WITH(NOLOCK)
            ON ae.BenefitCategoryID = bc.BenefitCategoryID
        INNER JOIN LkpExtCMSBidServiceCategory bs WITH(NOLOCK)
            ON bs.BidServiceCategoryID = bc.BidServiceCatID
        INNER JOIN CalcBenefitProjection cbpnon WITH(NOLOCK) -- Projected Allowed values coming from CalcBenefitProjection will include a full year of sequestration
			ON cbpnon.ForecastID = ae.ForecastID
			AND cbpnon.BenefitCategoryID = ae.BenefitCategoryID
		INNER JOIN CalcBenefitProjection cbpdual WITH(NOLOCK) -- Projected Allowed values coming from CalcBenefitProjection will include a full year of sequestration
			ON cbpdual.ForecastID = ae.ForecastID
			AND cbpdual.BenefitCategoryID = ae.BenefitCategoryID
        WHERE ae.DualEligibleTypeID IN (0, 1) -- Include everything but 3 which is weighted
			AND cbpnon.DualEligibleTypeID = 0
			AND cbpnon.MARatingOptionID = 3 -- We only need the projected blended amount
			AND cbpdual.DualEligibleTypeID = 1
			AND cbpdual.MARatingOptionID = 3
            AND ae.ForecastID = @ForecastID
            AND bs.ServiceCategoryCode IS NOT NULL
        GROUP BY
            ae.ForecastID,
			ae.DualEligibleTypeID,
            bs.ServiceCategoryCode,
            bs.IsPercentCoveredAllowedShown,
            bs.IsPercentCoveredCostShareShown,
            ae.FFSActEquivCostShare

        UNION ALL -------------------------------------------Dual Elig Type 0 Non DE# Added Benefits--------------------------------------

        SELECT
            @PlanYearID,
            spab.ForecastID,
			DualEligibleTypeID = 0,
            bs.ServiceCategoryCode,
            AddedBenefitAllowed = CAST(SUM(ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0)) as DECIMAL(10,6)), --CAST as 6 Decimal places so Allowed on WS4 will not have rounding errors
            AddedBenefitCostShare = SUM(ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0)),
            AddedBenefitNet = SUM((ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0))
                    - (ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0))),
            PercentCoveredAllowed = IsPercentCoveredAllowedShown, --When 1 then 1 else 0
            PercentCoveredCostShare = 0,
            FFSActEquivCostShare = 0,
            PlanCostShareMedicareCovered = 0,
            MedicareCoveredAllowed = 0,
            MedicareCoveredCostShare = 0,
            MedicareCoveredNet = 0,
            NetAddedServices = SUM((ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0))
                    - (ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0))),
            CostShareReduction = 0,
            SupplementalBenefitTotal =  SUM((ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0))
                    - (ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0))),
            bs.IsPercentCoveredAllowedShown,
            bs.IsPercentCoveredCostShareShown
        FROM SavedPlanAddedBenefits spab WITH(NOLOCK)
        INNER JOIN LkpIntAddedBenefitType ab WITH(NOLOCK)
            ON spab.AddedBenefitTypeID = ab.AddedBenefitTypeID
        INNER JOIN LkpExtCMSBidServiceCategory bs WITH(NOLOCK)
            ON ab.BidServiceCatID = bs.BidServiceCategoryID
        WHERE ForecastID = @ForecastID
            AND IsHidden = 0
            AND bs.ServiceCategoryCode IS NOT NULL
        GROUP BY
            spab.PlanYearID,
            spab.ForecastID,
            bs.ServiceCategoryCode,
            bs.UtilType,
            spab.INAddedBenefitUtilization,
            spab.INAddedBenefitCostShare,
            spab.OONAddedBenefitCostShare,
            bs.IsPercentCoveredAllowedShown,
            bs.IsPercentCoveredCostShareShown

        UNION ALL -------------------------------------------Dual Elig Type 1 DE# Added Benefits-------------------------------------------

        SELECT
            @PlanYearID,
            spab.ForecastID,
			DualEligibleTypeID = 1,
            bs.ServiceCategoryCode,
            AddedBenefitAllowed = CAST(SUM(ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0)) as DECIMAL(10,6)), --CAST as 6 Decimal places so Allowed on WS4 will not have rounding errors
            AddedBenefitCostShare = SUM(ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0)),
            AddedBenefitNet = SUM((ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0))
                    - (ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0))),
            PercentCoveredAllowed = IsPercentCoveredAllowedShown,
            PercentCoveredCostShare = 0,
            FFSActEquivCostShare = 0,
            PlanCostShareMedicareCovered = 0,
            MedicareCoveredAllowed = 0,
            MedicareCoveredCostShare = 0,
            MedicareCoveredNet = 0,
            NetAddedServices = SUM((ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0))
                    - (ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0))),
            CostShareReduction = 0,
            SupplementalBenefitTotal = SUM((ISNULL(spab.INAddedBenefitAllowed,0) + ISNULL(spab.OONAddedBenefitAllowed,0))
                    - (ISNULL(spab.INAddedBenefitCostShare,0) + ISNULL(spab.OONAddedBenefitCostShare,0))),
            bs.IsPercentCoveredAllowedShown,
            bs.IsPercentCoveredCostShareShown
        FROM SavedPlanAddedBenefits spab WITH(NOLOCK)
        INNER JOIN LkpIntAddedBenefitType ab WITH(NOLOCK)
            ON spab.AddedBenefitTypeID = ab.AddedBenefitTypeID
        INNER JOIN LkpExtCMSBidServiceCategory bs WITH(NOLOCK)
            ON ab.BidServiceCatID = bs.BidServiceCategoryID
        WHERE ForecastID = @ForecastID
            AND IsHidden=0
            AND bs.ServiceCategoryCode IS NOT NULL
        GROUP BY
            spab.ForecastID,
            bs.ServiceCategoryCode,
            bs.UtilType,
            spab.INAddedBenefitUtilization,
            spab.INAddedBenefitCostShare,
            spab.OONAddedBenefitCostShare,
            bs.IsPercentCoveredAllowedShown,
            bs.IsPercentCoveredCostShareShown

        RETURN 
    END
GO
