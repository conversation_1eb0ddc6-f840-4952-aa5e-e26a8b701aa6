SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
    
    
-- PROCEDURE NAME: [dbo].[spBenefitPricingImport]        
--                  
-- TYPE:                   
--                  
-- AUTHOR: <PERSON><PERSON><PERSON>yi    
--                  
-- CREATED DATE: 2022-Aug-02                  
--                  
-- DESCRIPTION:                   
--                  
-- PARAMETERS:                  
-- Input:                  
--     @ImportData                  
-- TABLES:                   
-- Read:                  
--                      
--                     
-- Write:                  
--     dbo.SavedForecastSetup    
--     dbo.SavedPlanBenefitDetail    
-- VIEWS:                  
--                  
-- FUNCTIONS:                  
--                    
-- STORED PROCS:                  
--                    
-- $HISTORY                   
-- ----------------------------------------------------------------------------------------------------------------------                  
-- DATE   VERSION  CHANGES MADE                                            DEVELOPER                    
-- ----------------------------------------------------------------------------------------------------------------------                  
-- 08-02-2022     1    Intial Version               Bhavana Tayi    
-- 10-21-2022     2    Select Forecastid instead of Planinfoid               Shivgopal   
-- ----------------------------------------------------------------------------------------------------------------------     
    
CREATE PROCEDURE [dbo].[spBenefitPricingImport]    
(     @ImportData BenefitOptionPricing READONLY,    
   @IsExecute BIT =0,    
      @UserID VARCHAR(7),    
   @MessageFromBackend VARCHAR(MAX) OUTPUT,    
   @Result BIT OUT    
)    
AS    
BEGIN    
 SET NOCOUNT ON;    
 SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED     
 SET XACT_ABORT ON    
        
           
        
DECLARE @BenefitOptionId INT;    
DECLARE @forecastid SMALLINT;    
DECLARE @ForecastidMsg VARCHAR(MAX);    
DECLARE @InvalidForecastid VARCHAR(MAX);    
    
BEGIN TRY    
  BEGIN TRANSACTION    
  SELECT i.ForecastID INTO #temp FROM  @ImportData i     
         EXCEPT    
         SELECT DISTINCT ForecastID FROM dbo.SavedForecastSetup    
 
  SELECT @InvalidForecastid= stuff( (select ',' + cast(ForecastID as varchar(max))
               from #temp
               for xml path ('')
              ), 1, 1, ''
            );

  DECLARE MyCursor  CURSOR    
   FOR    
   SELECT i.ForecastID, i.BenefitOptionID  FROM  @ImportData i    
   INNER JOIN  dbo.SavedForecastSetup sfs ON i.ForecastID=sfs.ForecastID    
      
    OPEN MyCursor    
    FETCH NEXT FROM MyCursor    
    INTO @forecastid, @BenefitOptionId    
  SET @ForecastidMsg=''    
    WHILE @@FETCH_STATUS=0    
     BEGIN    
     IF(@IsExecute=1)    
     Begin    
    UPDATE dbo.SavedForecastSetup  SET BenefitOptionID=@BenefitOptionId,    
    LastUpdateByID=@UserID,    
    LastUpdateDateTime=GETDATE(),    
    IsToReprice=1    
    FROM dbo.SavedForecastSetup sfs    
    INNER JOIN @ImportData t ON      
    sfs.ForecastID=t.ForecastID    
    WHERE sfs.ForecastID= @forecastid    
    AND t.IsUsedInPricing=1    
    
    UPDATE dbo.SavedPlanBenefitDetail SET IsLiveIndex=0,    
    LastUpdateByID=@UserID,    
    LastUpdateDateTime=GETDATE()    
    FROM dbo.SavedPlanBenefitDetail spbd INNER JOIN @ImportData t ON    
    t.ForecastID=spbd.ForecastID    
    WHERE spbd.IsBenefitYearCurrentYear=0    
    AND spbd.benefitoptionid<>@BenefitOptionId    
    AND spbd.ForecastID=@forecastid    
    AND t.IsUsedInPricing=1    
       
    
    UPDATE dbo.SavedPlanBenefitDetail SET IsLiveIndex=1,    
    LastUpdateByID=@UserID,    
    LastUpdateDateTime=GETDATE()    
    FROM dbo.SavedPlanBenefitDetail spbd INNER JOIN @ImportData t ON    
    t.ForecastID=spbd.ForecastID    
    WHERE spbd.IsBenefitYearCurrentYear=0     
    AND spbd.benefitoptionid=@BenefitOptionId    
    AND spbd.ForecastID=@forecastid    
    AND t.IsUsedInPricing=1    
           END    
    SELECT @ForecastidMsg =CONCAT(@ForecastidMsg, @forecastid,',')    
        
    FETCH NEXT FROM MyCursor    
    INTO @forecastid, @BenefitOptionId    
     END    
    CLOSE MyCursor;    
  DEALLOCATE MyCursor;    
      
       
  SET @Result=1    
  IF(@IsExecute=0)    
  BEGIN    
    
  Set @MessageFromBackend ='You are about to update Benefit option id for Forecastid(s)-' +@ForecastidMsg    
      
  END    
  ELSE    
  BEGIN    
  IF(@InvalidForecastid <> ''AND @ForecastidMsg <> '')    
  BEGIN    
  SET @MessageFromBackend='Import is sucessfully completed for the Forecastid(s)- '+ SUBSTRING(@ForecastidMsg,0,LEN(@ForecastidMsg)) +'. Forecastid(s)- '+ @InvalidForecastid +' does not exist.'    
  END    
  ELSE     
  BEGIN    
  IF(@InvalidForecastid <> '')    
  BEGIN    
  SET @MessageFromBackend='Forecastid(s)- '+ @InvalidForecastid +' does not exist'    
  END    
  ELSE    
     BEGIN    
   BEGIN    
  SET @MessageFromBackend='Import is sucessfully completed for the Forecastid(s)- '+ @ForecastidMsg     
  END    
     END    
  END    
         
  END    
   COMMIT TRANSACTION    
 END TRY    
 BEGIN CATCH    
  DECLARE @ErrorMessage NVARCHAR(4000);      
  DECLARE @ErrorSeverity INT;      
  DECLARE @ErrorState INT;
  DECLARE @ErrorException NVARCHAR(4000);     
  DECLARE @errSrc VARCHAR(MAX) =ISNULL( ERROR_PROCEDURE(),'SQL');
  DECLARE @currentdate DATETIME=GETDATE() 
  SET @Result=0;    
 SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+     
 CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)    
       
    
 RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)    
       
 ROLLBACK TRANSACTION;    
 SET @Result=0    
    SET  @MessageFromBackend=@ErrorMessage;    
 ---Insert into app log for logging error------------------    
 EXEC spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@UserID;    
 END CATCH;    
 END    
    
    
    
    
    
    
GO
