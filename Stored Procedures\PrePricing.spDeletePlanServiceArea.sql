SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spDeletePlanServiceArea]   
--    
-- AUTHOR: Surya <PERSON>rthy 
--    
-- CREATED DATE: 2024-Dec-02    
-- Type: 
-- DESCRIPTION: Procedure responsible for deleting service area  
--    
-- PARAMETERS:    
-- Input: 
-- @ServiceAreaID

-- TABLES:   
--  

-- Read:    
-- PrePricing.ServiceAreaMapping

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Dec-02		1		Initial Version                 Surya Murthy
-- 2025-Apr-16		2		Allow Multi-Delete              Adam Gilbert
-- 2025-Apr-18		3		Modified success message        Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spDeletePlanServiceArea]
(
	@ServiceAreaID VARCHAR(MAX)
)
AS    
BEGIN  
	SET NOCOUNT ON;
	DECLARE @OutPutResult VARCHAR(MAX);
	DECLARE	@OutPutResultCode VARCHAR(20)
	SET @OutPutResultCode='Success'
	BEGIN TRY
		BEGIN TRANSACTION serviceareadelete	
			DROP TABLE IF EXISTS #SplitID
			SELECT value IDList INTO #SplitID FROM STRING_SPLIT(@ServiceAreaID,',')
			DELETE FROM PrePricing.ServiceAreaMapping WHERE ServiceAreaID IN (SELECT IDList FROM #SplitID);
		COMMIT TRANSACTION serviceareadelete
		SET @OutPutResult='Service Area Deleted successfully.';	
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END TRY
	BEGIN CATCH		
		DECLARE @ErrorMessage NVARCHAR(4000);                    
        DECLARE @ErrorSeverity INT;                    
        DECLARE @ErrorState INT;                    
        DECLARE @ErrorException NVARCHAR(4000);                    
		DECLARE @errSrc VARCHAR(MAX) =ISNULL( ERROR_PROCEDURE(),'SQL')                    
		DECLARE @currentdate DATETIME=GETDATE()                    
		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
		' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)                
		ROLLBACK TRANSACTION serviceareadelete    
		SET @OutPutResultCode='Error'
		SET @OutPutResult=@ErrorMessage;	
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH	
END
GO
