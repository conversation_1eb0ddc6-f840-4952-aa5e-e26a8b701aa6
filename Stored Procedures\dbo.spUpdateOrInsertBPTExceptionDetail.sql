SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Stored Procedure

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateOrInsertBPTExceptionDetail
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE: 2008-May-05
-- HEADER UPDATED: 2010-Oct-13  
--
-- DESCRIPTION: Updates or inserts a BPT exception detail record for the specified plan index.  The record with the
--              lowest contract number/plan ID that is hidden will be overwritten, if there is one.  Otherwise, a new
--              record will be created.  The contract number/plan ID will be stored in the detail table as CCCCC-PPP,
--              or as "All Other" if NULL.
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--      @ContractNumber
--        Contract number.  If NULL, then this is assumed to be the "all
--        other" record, and the ContractNumberPlanID field in the table
--        will reflect this.
--      @SegmentId
--      @PlanID
--      @MemberMonths
--      @NonDualMemberMonths
--  Output:
--
-- TABLES:
--	Read:
--      SavedPlanBPTExceptionDetail
--	Write:
--      SavedPlanBPTExceptionDetail
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                             	        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-May-05      1           Initial version.                                                    Tonya Cockrell
-- 2008-Sep-19      2           Added @UserID to the list of parameters and replaced all            Shannon Boykin
--                                  occurrences of SUSER_SNAME with @UserID.
-- 2009-May-08      3           Added @NonDualMemberMonths                                          Lawrence Choi
-- 2010-Oct-13      4           Removed @PlanYearID                                                 Joe Casey
-- 2011-Jan-11      5           Updated @planYearID to fnGetBidYear                                 Nate Jacoby
-- 2013-Oct-04      6           Included Join on Segment ID                                         Manisha Tyagi
-- 2019-Oct-30	    9           Replace @UserID from char(13) to char(7)							Chhavi Sinha
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROC [dbo].[spUpdateOrInsertBPTExceptionDetail]
    @ForecastID INT,
    @ContractNumber CHAR(5),
    @PlanID CHAR(3),
    @SegmentId CHAR(3),  --Declare @SegmentId
    @MemberMonths INT,
    @NonDualMemberMonths INT,
    @UserID CHAR(7)
AS

    DECLARE @PlanYearID SMALLINT
    SET @PlanYearID = dbo.fnGetBidYear()

    --Put the contract number and plan ID in this format: CCCCC-PPP.
    DECLARE @NewContractNumberPlanID CHAR(9)

    IF @ContractNumber IS NULL OR @PlanID IS NULL OR @SegmentId IS NULL --Including SegmentId
        SET @NewContractNumberPlanID = 'All Other'
    ELSE
        SET @NewContractNumberPlanID
            = LEFT(@ContractNumber + 'XXXXX', 5)
            + '-'
            + LEFT(@PlanID + 'XXX', 3)


    DECLARE @ExistingContractNumberPlanID CHAR(9)
    SET @ExistingContractNumberPlanID = NULL

    --Figure out which record we'll replace, if any.  The first candidate is the record with the same
    --ContractNumberPlanID.  We can't have two records with the same value because this would cause a primary key
    --violation.
    IF EXISTS (SELECT 1 FROM SavedPlanBPTExceptionDetail WHERE ForecastID = @ForecastID AND @ContractNumber = ContractNumber AND PlanID = @PlanID AND SegmentId = @SegmentId  --Including Segmentid
    AND [ContractNumber-PBP] = @NewContractNumberPlanID)
        SET @ExistingContractNumberPlanID = @NewContractNumberPlanID

    --The other option is to replace a record that is hidden.  We'll pick the minimum hidden ContractNumberPlanID
    --(no particular reason it has to be the minimum).
    ELSE
        SELECT @ExistingContractNumberPlanID
            =
            (
            SELECT MIN([ContractNumber-PBP])
            FROM SavedPlanBPTExceptionDetail
            WHERE ForecastID = @ForecastID
                AND IsHidden = 1
            )

    IF @ExistingContractNumberPlanID IS NOT NULL

        --We have found a record to overwrite - do it now.
        UPDATE SavedPlanBPTExceptionDetail
        SET [ContractNumber-PBP] = @NewContractNumberPlanID,
        ContractNumber =@ContractNumber,
        PlanID = @PlanID,
        SegmentId = @SegmentId, --Including SegmentId
            MemberMonths = @MemberMonths,
            NonDualMemberMonths = @NonDualMemberMonths,
            IsHidden = 0,
            LastUpdateByID = @UserID,
            LastUpdateDateTime = GETDATE()
        WHERE ForecastID = @ForecastID
            AND [ContractNumber-PBP] = @ExistingContractNumberPlanID

    ELSE
        --A hidden record doesn't exist, so insert a new one.
        INSERT SavedPlanBPTExceptionDetail
            (
            PlanYearID,
            ForecastID,
            ContractNumber,
            PlanID,
            SegmentId,--Including SegmentId
            [ContractNumber-PBP],
            MemberMonths,
            NonDualMemberMonths,
            IsHidden,
            LastUpdateByID,
            LastUpdateDateTime
            )
        VALUES
            (
            @PlanYearID,
            @ForecastID,
            @ContractNumber,
            @PlanID,
            @SegmentId,--Including SegmentId
            @NewContractNumberPlanID,
            @MemberMonths,
            @NonDualMemberMonths,            
            0,
            @UserID,
            GETDATE()
            )
GO
