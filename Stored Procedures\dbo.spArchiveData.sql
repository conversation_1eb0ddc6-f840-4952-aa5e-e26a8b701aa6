SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO




-- ----------------------------------------------------------------------------------------------------------------------        
-- FUNCTION NAME: spArchiveData        
--        
-- AUTHOR: Sumit Kumar        
--        
-- CREATED DATE: 2015-06-29        
--        
-- DESCRIPTION: Procedure responsible for running the Second step in ModelTurnOver(Run by package)       
--        
-- PARAMETERS: NONE             
--        
-- TABLES:         
-- Read:        

-- Write:        
--        
-- VIEWS:        
--        
-- FUNCTIONS:       
--        
-- STORED PROCS:        
--        
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION			CHANGES MADE									DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2015-06-29		1			Initial Version									Sumit Kumar   
-- 2015-11-10		2			Commented the incrementing						Kritika Singh
--								part of PlanYearID and changed table names
-- 2017-Jun-29		3			Removed ArcSavedBidMembership					Crystal Quirino
-- 2017-Jul-07		4			Removed ArcPerExtContractNumberPlanIDDetail		Chris Fleming
-- 2018-Feb-11      5           Remove ArcSavedCMSOOPCData and ArcSavedCMSOOPCData_backup  Manisha Tyagi
-- 2020-Jan-15      6           Modify for new market, region, division tables  Brent Osantowski
-- 2020-Jul-07      7           Modify to include market, region, division tables archive process(run this only starting next year)    Abraham Ndabian
-- 2022-Aug-12      8           Added delete if data exists                                 Manisha Tyagi
-- 2024-Oct-23      9           Added ArcVwPlanSummaryReport logic                          Surya Murthy
-- 2025-Jan-17      10          Excluding PD AUdit Archive Table                            Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[spArchiveData]
WITH EXECUTE AS OWNER 
AS
BEGIN

DECLARE @PlanYearID Int

SELECT @PlanYearID = PlanYearID  FROM dbo.lkpIntPlanyear where IsBidYear = 1

-- Archive process for market, region and division tables

DELETE dbo.ArcSavedDivisionDetail WHERE PlanYearID = @PlanYearID

INSERT INTO dbo.ArcSavedDivisionDetail(PlanYearID,DivisionID,HumanaRegionID) SELECT	@PlanYearID,ActuarialDivisionID,ActuarialRegionID FROM dbo.SavedRegionInfo

DELETE dbo.ArcSavedDivisionHeader WHERE PlanYearID = @PlanYearID

INSERT INTO dbo.ArcSavedDivisionHeader(PlanYearID,DivisionID,DivisionName,LastUpdateByID,LastUpdateDateTime) SELECT @PlanYearID,ActuarialDivisionID,ActuarialDivision,LastUpdateByID,LastUpdateDateTime FROM dbo.SavedDivisionInfo

DELETE dbo.ArcSavedMarketHeader WHERE PlanYearID = @PlanYearID

INSERT INTO dbo.ArcSavedMarketHeader(PlanYearID,MarketID,MarketName,MarketDescription,LastUpdateByID,LastUpdateDateTime) SELECT @PlanYearID,ActuarialMarketID,ActuarialMarket,ActuarialMarket,LastUpdateByID,LastUpdateDateTime FROM dbo.SavedMarketInfo

DELETE dbo.ArcSavedRegionDetail WHERE PlanYearID = @PlanYearID

INSERT INTO dbo.ArcSavedRegionDetail(PlanYearID,HumanaRegionID,MarketID,LastUpdateByID,LastUpdateDateTime) SELECT @PlanYearID,ActuarialRegionID,ActuarialMarketID,LastUpdateByID,LastUpdateDateTime FROM dbo.SavedMarketInfo

DELETE dbo.ArcSavedRegionHeader WHERE PlanYearID = @PlanYearID

INSERT INTO dbo.ArcSavedRegionHeader(PlanYearID,HumanaRegionID,HumanaRegionName,LastUpdateByID,LastUpdateDateTime) SELECT @PlanYearID,ActuarialRegionID,ActuarialRegion,LastUpdateByID,LastUpdateDateTime FROM dbo.SavedRegionInfo

DELETE dbo.ArcVwPlanSummaryReport WHERE [Plan Year] = @PlanYearID
INSERT INTO dbo.ArcVwPlanSummaryReport SELECT * FROM dbo.vwPlanSummaryReport


DECLARE UpdateCursor
CURSOR	FOR
SELECT  SUBSTRING(table_name, 4, len(table_name))
FROM	INFORMATION_SCHEMA.TABLES
WHERE	table_type = 'BASE TABLE'
AND     table_name NOT LIKE '%_sigma_backup'  
AND     table_name NOT LIKE '%_bkp%' 
AND		table_name LIKE 'Arc%'
AND     table_name NOT IN (
'arcSavedDivisionDetail'
,'arcSavedDivisionHeader'
,'arcSavedMarketHeader'
,'arcSavedRegionDetail'
,'arcSavedRegionHeader'
,'arcSavedPlanBenefitDetail2010_2022'
,'arcSavedPlanHeader2010_2022'
,'ArchiveSavedPlanListFull'
,'ArcVwPlanSummaryReport'
,'ArcSavedPDAuditExhibit'
)



DECLARE	@TableName VARCHAR(100);
OPEN UpdateCursor;

BEGIN TRANSACTION;


BEGIN TRY

	-- Update Arc tables 

	FETCH NEXT FROM UpdateCursor INTO @TableName   
	WHILE @@FETCH_STATUS = 0   
	BEGIN  

		DECLARE @InsertSql VARCHAR(MAX), @UpdateSql VARCHAR(MAX);

		SET @InsertSql = 'IF NOT EXISTS(SELECT TOP(1) PlanYearID FROM dbo.Arc'+@TableName+' WHERE PlanYearID = '+CAST(@PlanYearID AS VARCHAR(4))+') BEGIN INSERT INTO dbo.Arc' + @TableName + ' SELECT * FROM ' + @TableName +' END';
		PRINT @InsertSql;
		EXEC(@InsertSql);



		FETCH NEXT FROM UpdateCursor INTO @TableName;   
	END 

END TRY
BEGIN CATCH

--To raise caught Exception
DECLARE  @ERR_MSG AS NVARCHAR(4000) ,@ERR_SEV AS SMALLINT ,@ERR_STA AS SMALLINT
SELECT @ERR_MSG = ERROR_MESSAGE(), @ERR_SEV =ERROR_SEVERITY(), @ERR_STA = ERROR_STATE()




    IF @@TRANCOUNT > 0
    BEGIN
		PRINT 'Rolling back';
        ROLLBACK TRANSACTION;
    END

	RAISERROR (@ERR_MSG, @ERR_SEV, @ERR_STA) WITH NOWAIT

END CATCH

IF @@TRANCOUNT > 0
BEGIN
    -- Comment above and uncomment below to have script commit changes
    PRINT 'Changes saved'
     COMMIT TRANSACTION;
END 

CLOSE UpdateCursor;
DEALLOCATE UpdateCursor; 

END
GO
