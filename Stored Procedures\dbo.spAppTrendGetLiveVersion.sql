SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
     
 -- =============================================            
-- Author:  Satyam <PERSON>al         
-- Create date: 07-02-2020      
-- Description:  Get Live Version      
--            
--            
-- PARAMETERS:            
-- Input:              
          
-- TABLES:            
-- Read:            
-- Write:            
-- VIEWS:            
--            
-- FUNCTIONS:            
--              
-- STORED PROCS:             
           
      
-- $HISTORY               
      
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE   VERSION   CHANGES MADE                                              DEVELOPER              
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Feb-07  1    Initial version.              Satyam Singhal      
-- ----------------------------------------------------------------------------------------------------------------------              
 -- =============================================              
-- Author:  Satyam Singhal           
-- Create date: 07-02-2020        
-- Description:  Get Live Version        
--              
--              
-- PARAMETERS:              
-- Input:                
            
-- TABLES:              
-- Read:              
-- Write:              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:               
             
        
-- $HISTORY                 
        
-- ----------------------------------------------------------------------------------------------------------------------                
-- DATE   VERSION   CHANGES MADE                                              DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------                
-- 2020-Feb-07  1    Initial version.              Satyam Singhal        
-- ----------------------------------------------------------------------------------------------------------------------                
CREATE PROCEDURE [dbo].[spAppTrendGetLiveVersion] @LastUpdateByID CHAR(7)       
AS       
  BEGIN       
          
      
      BEGIN TRY       
          SELECT DISTINCT projectionname       
          FROM   dbo.lkpprojectionversion       
          WHERE  isliveprojection = 1       
      
              
      END TRY       
      
      BEGIN CATCH       
          --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'        
          DECLARE @ErrorMessage NVARCHAR(4000);       
          DECLARE @ErrorSeverity INT;       
          DECLARE @ErrorState INT;       
          DECLARE @ErrorException NVARCHAR(4000);       
          DECLARE @errSrc      VARCHAR(max) =Isnull(Error_procedure(), 'SQL'),       
                  @currentdate DATETIME=Getdate()       
      
          SELECT @ErrorMessage = Error_message(),       
                 @ErrorSeverity = Error_severity(),       
                 @ErrorState = Error_state(),       
                 @ErrorException = 'Line Number :'       
                                   + Cast(Error_line() AS VARCHAR)       
                                   + ' .Error Severity :'       
                                   + Cast(@ErrorSeverity AS VARCHAR)       
                                   + ' .Error State :'       
                                   + Cast(@ErrorState AS VARCHAR)       
      
          RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)       
      
               
      
          ---Insert into app log for logging error------------------         
          EXEC Spappaddlogentry       
            @currentdate,       
            '',       
            'ERROR',       
            @errSrc,       
            @ErrorMessage,       
            @ErrorException,       
            @LastUpdateByID       
      END CATCH;       
  END 
GO
