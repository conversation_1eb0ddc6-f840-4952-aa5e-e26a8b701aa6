SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- PROCEDURE NAME: spDashboardReportRPOUpdateInSCTPBI2

-- DESCRIPTION: This SP returns extract for "RPO Update in SCT" user action from dm_exec_procedure_stats_snapshot table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      [dbo].[dm_exec_procedure_stats_snapshot]
--		
-- Example 
-- Exec [dbo].[spDashboardReportRPOUpdateInSCTPBI2] 19170061, '2023-09-25 13:13:16.073'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-24			1			Initial Version                                                                         Sheetal Patil
---------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROC [dbo].[spDashboardReportRPOUpdateInSCTPBI2] 
(@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  

IF (SELECT  OBJECT_ID ('tempdb..#AppLogRpoUpdateTemp')) IS NOT NULL DROP TABLE #AppLogRpoUpdateTemp;


SELECT [snapshot_datatime] AS [Date],[procedure_name] AS ProcName, COUNT([procedure_name]) AS Counts,
SUM([execution_count]) AS ExecutionCount, 
CONVERT(FLOAT, SUM([total_elapsed_time]))/1000000/60 AS ExecutionTImeinMinutes  ,
CONVERT(FLOAT, SUM([total_elapsed_time]))/1000000 AS ExecutionTimeinSeconds    
INTO #AppLogRpoUpdateTemp  
FROM dbo.dm_exec_procedure_stats_snapshot WITH (NOLOCK) 
WHERE (([procedure_name] LIKE '%SCT_spUpdAddAdjsRevClaims%' OR [procedure_name] LIKE '%SCT_spMAClaimTrend%' 
OR [procedure_name] LIKE '%SCT_spMAForecast%')
 AND [snapshot_datatime] BETWEEN  GETDATE() -1 AND GETDATE()
 )      
GROUP BY [procedure_name],[snapshot_datatime]


IF (SELECT  OBJECT_ID ('tempdb..#AppLogRpoUpdateTemp1')) IS NOT NULL DROP TABLE #AppLogRpoUpdateTemp1;

SELECT CONVERT(DATE,[Date],23) AS [Date],MIN([Date]) AS StartDate, COUNT(ProcName) AS Counts,
SUM(ExecutionCount) ExecutionCount, 
CONVERT(FLOAT, SUM(ExecutionTImeinMinutes)) ExecutionTImeinMinutes ,
CONVERT(FLOAT, SUM(ExecutionTimeinSeconds))ExecutionTimeinSeconds  
INTO #AppLogRpoUpdateTemp1  
FROM #AppLogRpoUpdateTemp      
GROUP BY CONVERT(DATE,[Date],23)

SELECT 
'RPO Update in SCT' AS [UserActions]
,'Multiple users' AS [UserID]
,StartDate AS [Run Date]
, 'RPO Update in SCT' AS [Type]
, 'NULL' AS [Plans]
, 0 AS [Plan Count]
, ExecutionTimeinSeconds
, 0 AS characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
FROM #AppLogRpoUpdateTemp1 WITH (NOLOCK)

END
GO
