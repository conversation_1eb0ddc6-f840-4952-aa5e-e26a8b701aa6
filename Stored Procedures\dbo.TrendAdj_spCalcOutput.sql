------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--
-- PROCEDURE NAME:	dbo.TrendAdj_spCalcOutput
--
-- CREATOR:			Michael Manes
--
-- CREATED DATE:	2024-Sep-09
--
-- DESCRIPTION:		This sp is executed by the Unified Actuarial Adjustment Compiler tool; it transforms the recently calculated and compiled data sitting in TrendAdj_CalcPlanAdjmt
--					and matches against the MAAUI repcat and bencat table to determine plans that have had changes, and adjustments that should be cleared out
--					Returns two tables directly to the Excel file, 'Check' first comparing latest adjustments to permanent, and to archive adjustments. 
--					The second table 'Output' has only the plan and adjustments that need to be uploaded to MAAUI
--		
-- PARAMETERS:
--  Input  :		@AdjGroup
--					@SessionID
--					@Region
--					@PlanInfoIDList  --still need to add this
--					@CalcOrClear
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			TrendAdj_Controls_LkpAdjTypeID
--					TrendAdj_CalcPlanAdjmt
--					Trend_ProjProcess_CalcPlanAdjmt_RepCat
--					Trend_ProjProcess_CalcPlanAdjmt_BenCat
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			vwPlanInfo
--
-- FUNCTIONS:		fngetbidyear
--					fnGetLiveBidYearPlanList
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--
-- 2024-Sep-09      1           Initial Version                                                     Michael Manes
-- 2024-Oct-10      2           Added NOLOCK & optimazation	                                        Kumar Jalendran
-- 2024-Oct-18      3           Dropped all temp tables at end of procedure                         Kumar Jalendran
--                              IsEnabled, IsHidden & IsOffMAModel filter added
-- 2024-Oct-24      4           Added join on fnGetLiveBidYearPlanList for BidYear filter.          Kumar Jalendran
-- 2024-Oct-25      5           Updated the error loging to dbo.TrendAdjLog                         Kumar Jalendran
-- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[TrendAdj_spCalcOutput]
@AdjGroup     TINYINT
,@SessionID   VARCHAR(19)
,@CalcOrClear NVARCHAR(5)
,@Region      VARCHAR(50)
AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 


        --internalparam
        DECLARE @xregion      VARCHAR(50) = @Region
               ,@xadjgroup    TINYINT     = @AdjGroup
               ,@xcalcorclear NVARCHAR(5) = @CalcOrClear
               ,@xsessionid   VARCHAR(19) = @SessionID
               ,@bidyear      INT         = dbo.fnGetBidYear ();

			DECLARE @stringBenCat VARCHAR(20) ='BenCat';
			DECLARE @stringRepCat VARCHAR(20) ='RepCat';
			DECLARE @stringClear VARCHAR(20) ='Clear';

        BEGIN TRY


            BEGIN TRANSACTION transaction_TrendAdj;


            /*SQL Adj Groups*/
            DROP TABLE IF EXISTS #LkpAdjTypeID;
            SELECT  AdjGroupID
                   ,AdjTypeID
                   ,AdjustmentName
                   ,AdjustmentDescription
                   ,IsEnabled
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            INTO    #LkpAdjTypeID
            FROM    dbo.TrendAdj_Controls_LkpAdjTypeID WITH(NOLOCK)
            WHERE   IsEnabled = 1;

            /*Region Plan List*/
            DROP TABLE IF EXISTS #RegionPlanList;
            SELECT  vpi.CPS, vpi.PlanInfoID
            INTO    #RegionPlanList
            FROM    dbo.vwPlanInfo vpi WITH(NOLOCK)
			INNER JOIN dbo.fnGetLiveBidYearPlanList() lp ON lp.PlanInfoID = vpi.PlanInfoID
            WHERE   PlanYear = @bidyear
                    AND Region = @xregion;


            /*Staged Calculations*/ 
            DROP TABLE IF EXISTS #stgCalcPlanAdjmt;
            SELECT  Granularity
                   ,@xadjgroup AS AdjGroupID
                   ,AdjustmentDescription
                   ,CPS
                   ,PlanInfoID
                   ,TrendYearID
                   ,RateType
                   ,ReportingCategory
                   ,BenefitCategoryID
                   ,CostAdjustment
                   ,UseAdjustment
                   ,SessionID
            INTO    #stgCalcPlanAdjmt
            FROM    dbo.TrendAdj_CalcPlanAdjmt WITH(NOLOCK)
            WHERE   SessionID = @xsessionid;

            /*Permanent Adj All*/
            DROP TABLE IF EXISTS #CalcPlanAdjmt;
            SELECT  a.*
            INTO    #CalcPlanAdjmt
            FROM    (SELECT     AdjustmentID
                               ,RIGHT(AdjustmentType, 6) AS Granularity
                               ,AdjustmentDescription
                               ,rpl.CPS
                               ,TrendYearID
                               ,RateType
                               ,ReportingCategory
                               ,CAST(NULL AS INT) AS BenefitCategoryID
                               ,CostAdjustment
                               ,UseAdjustment
                     FROM       dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat rep WITH(NOLOCK)
                    INNER JOIN  #RegionPlanList rpl WITH(NOLOCK)
                            ON rpl.PlanInfoID = rep.PlanInfoID
                     UNION ALL
                     SELECT     AdjustmentID
                               ,RIGHT(AdjustmentType, 6) AS Granularity
                               ,AdjustmentDescription
                               ,rpl.CPS
                               ,TrendYearID
                               ,RateType
                               ,NULL AS ReportingCategory
                               ,BenefitCategoryID
                               ,CostAdjustment
                               ,UseAdjustment
                     FROM       dbo.Trend_ProjProcess_CalcPlanAdjmt_BenCat ben WITH(NOLOCK)
                    INNER JOIN  #RegionPlanList rpl WITH(NOLOCK)
                            ON rpl.PlanInfoID = ben.PlanInfoID) a;

            /*ClearAdj Templates*/
            DROP TABLE IF EXISTS #cleartemplaterep;
            SELECT  @stringRepCat AS Granularity
                   ,@stringClear AS AdjustmentDescription
                   ,@bidyear AS TrendYearID
                   ,1 AS RateType
                   ,'Ancil' AS ReportingCategory
                   ,CAST(NULL AS INT) AS BenefitCategoryID
                   ,0 AS CostAdjustment
                   ,0 AS UseAdjustment
            INTO    #cleartemplaterep;

            DROP TABLE IF EXISTS #cleartemplateben;
            SELECT  @stringBenCat AS Granularity
                   ,@stringClear AS AdjustmentDescription
                   ,@bidyear AS TrendYearID
                   ,1 AS RateType
                   ,CAST(NULL AS VARCHAR(50)) AS ReportingCategory
                   ,'50' AS BenefitCategoryID
                   ,0 AS CostAdjustment
                   ,0 AS UseAdjustment
            INTO    #cleartemplateben;


            /*Prior Saved Adjustments*/
            DROP TABLE IF EXISTS #CalcPlanAdjmtPrior;
            SELECT  Granularity
                   ,AdjustmentDescription
                   ,CPS
                   ,TrendYearID
                   ,RateType
                   ,ReportingCategory
                   ,BenefitCategoryID
                   ,CostAdjustment
                   ,UseAdjustment
                   ,LastUpdateDateTime
            INTO    #CalcPlanAdjmtPrior
            FROM    dbo.TrendAdj_CalcPlanAdjmt_Prior cpa WITH(NOLOCK)
            WHERE   EXISTS (SELECT rpl.CPS, rpl.PlanInfoID FROM  #RegionPlanList rpl WHERE   cpa.PlanInfoID = rpl.PlanInfoID);

            DROP TABLE IF EXISTS #newadjustments;
            CREATE TABLE #newadjustments
                ([AdjustmentID]          [INT]            NULL
                ,[Granularity]           [VARCHAR](6)     NULL
                ,[AdjustmentDescription] [VARCHAR](100)   NULL
                ,[CPS]                   [CHAR](13)       NOT NULL
                ,[TrendYearID]           [SMALLINT]       NOT NULL
                ,[RateType]              [SMALLINT]       NOT NULL
                ,[ReportingCategory]     [VARCHAR](50)    NULL
                ,[BenefitCategoryID]     [INT]            NULL
                ,[CostAdjustment]        [DECIMAL](18, 8) NULL
                ,[UseAdjustment]         [DECIMAL](18, 8) NULL);

            IF UPPER (@xcalcorclear) = 'CALC'
                BEGIN
                    --take perm table (except if G1, append latest calc and latest compile)

                    IF @xadjgroup = 1 --some MS users don't fully clear adjustments before calculating G1 since there's no dependency issues, so this ignores any adjustments that are in
                        BEGIN
                            INSERT INTO #newadjustments
                                (AdjustmentID
                                ,Granularity
                                ,AdjustmentDescription
                                ,CPS
                                ,TrendYearID
                                ,RateType
                                ,ReportingCategory
                                ,BenefitCategoryID
                                ,CostAdjustment
                                ,UseAdjustment)
                            SELECT  NULL AS AdjustmentID
                                   ,Granularity
                                   ,AdjustmentDescription
                                   ,CPS
                                   ,TrendYearID
                                   ,RateType
                                   ,ReportingCategory
                                   ,BenefitCategoryID
                                   ,CostAdjustment
                                   ,UseAdjustment
                            FROM    #stgCalcPlanAdjmt WITH(NOLOCK);

                        END;
                    ELSE
                        BEGIN
                            INSERT INTO #newadjustments
                                (AdjustmentID
                                ,Granularity
                                ,AdjustmentDescription
                                ,CPS
                                ,TrendYearID
                                ,RateType
                                ,ReportingCategory
                                ,BenefitCategoryID
                                ,CostAdjustment
                                ,UseAdjustment)
                            SELECT  NULL AS AdjustmentID
                                   ,Granularity
                                   ,AdjustmentDescription
                                   ,CPS
                                   ,TrendYearID
                                   ,RateType
                                   ,ReportingCategory
                                   ,BenefitCategoryID
                                   ,CostAdjustment
                                   ,UseAdjustment
                            FROM    #stgCalcPlanAdjmt WITH(NOLOCK)
                            UNION ALL
                            SELECT  AdjustmentID
                                   ,Granularity
                                   ,AdjustmentDescription
                                   ,CPS
                                   ,TrendYearID
                                   ,RateType
                                   ,ReportingCategory
                                   ,BenefitCategoryID
                                   ,CostAdjustment
                                   ,UseAdjustment
                            FROM    #CalcPlanAdjmt WITH(NOLOCK)
							WHERE adjustmentDescription <> @stringClear;
                        END;
                END;
            ELSE IF UPPER (@xcalcorclear) = UPPER(@stringClear)
                     BEGIN
                         IF @xadjgroup = 1
                             BEGIN
                                 INSERT INTO    #newadjustments
                                     (AdjustmentID
                                     ,Granularity
                                     ,AdjustmentDescription
                                     ,CPS
                                     ,TrendYearID
                                     ,RateType
                                     ,ReportingCategory
                                     ,BenefitCategoryID
                                     ,CostAdjustment
                                     ,UseAdjustment)
                                 SELECT     NULL AS AdjustmentID
                                           ,b.Granularity
                                           ,b.AdjustmentDescription
                                           ,a.CPS
                                           ,b.TrendYearID
                                           ,b.RateType
                                           ,b.ReportingCategory
                                           ,b.BenefitCategoryID
                                           ,b.CostAdjustment
                                           ,b.UseAdjustment
                                 FROM       (SELECT DISTINCT CPS FROM   #CalcPlanAdjmt WITH(NOLOCK)) a
                                OUTER APPLY (SELECT *
                                             FROM   #cleartemplaterep WITH(NOLOCK)
                                             UNION ALL
                                             SELECT *
                                             FROM   #cleartemplateben WITH(NOLOCK)) b;
                             END;
                         IF @xadjgroup > 1
                             BEGIN
                                 INSERT INTO    #newadjustments
                                     (AdjustmentID
                                     ,Granularity
                                     ,AdjustmentDescription
                                     ,CPS
                                     ,TrendYearID
                                     ,RateType
                                     ,ReportingCategory
                                     ,BenefitCategoryID
                                     ,CostAdjustment
                                     ,UseAdjustment)
                                 SELECT NULL AS AdjustmentID
                                       ,Granularity
                                       ,AdjustmentDescription
                                       ,CPS
                                       ,TrendYearID
                                       ,RateType
                                       ,ReportingCategory
                                       ,BenefitCategoryID
                                       ,CostAdjustment
                                       ,UseAdjustment
                                 FROM   #stgCalcPlanAdjmt WITH(NOLOCK)
                                 UNION ALL
                                 (SELECT    AdjustmentID
                                           ,Granularity
                                           ,cpa.AdjustmentDescription
                                           ,CPS
                                           ,TrendYearID
                                           ,RateType
                                           ,ReportingCategory
                                           ,BenefitCategoryID
                                           ,CostAdjustment
                                           ,UseAdjustment
                                  FROM      #CalcPlanAdjmt cpa WITH(NOLOCK)
                                 INNER JOIN #LkpAdjTypeID tid WITH(NOLOCK)
                                         ON cpa.AdjustmentDescription = tid.AdjustmentDescription
                                  WHERE     tid.AdjTypeID < @xadjgroup);
                             END;
                     END;
			ELSE
				BEGIN
					PRINT 'Invalid CalcOrClear Parameter.';
					RETURN;
				END
			--add clear rows where appropriate (no current adjustments but there are some present in MAAUI table that need to be cleared)
            DROP TABLE IF EXISTS #clearrowsben;
            SELECT      cpa.CPS
                       ,ben.*
            INTO        #clearrowsben
            FROM        #cleartemplateben ben WITH(NOLOCK)
           OUTER APPLY  (SELECT DISTINCT
                                c.CPS
                         FROM   #CalcPlanAdjmt c WITH(NOLOCK)
                         WHERE  c.Granularity = @stringBenCat
								AND c.AdjustmentDescription <> @stringClear
								AND NOT EXISTS (SELECT  DISTINCT a.CPS FROM   #newadjustments a WITH(NOLOCK) WHERE c.CPS=a.CPS AND a.Granularity = @stringBenCat)) cpa
            WHERE       CPS IS NOT NULL;

            DROP TABLE IF EXISTS #clearrowsrep;
            SELECT      cpa.CPS
                       ,rep.*
            INTO        #clearrowsrep
            FROM        #cleartemplaterep rep WITH(NOLOCK)
           OUTER APPLY  (SELECT DISTINCT
                                c.CPS
                         FROM   #CalcPlanAdjmt c WITH(NOLOCK)
                         WHERE  c.Granularity = @stringRepCat
								AND c.AdjustmentDescription <> @stringClear
								AND NOT EXISTS (SELECT  DISTINCT a.CPS FROM   #newadjustments a WITH(NOLOCK) WHERE c.CPS=a.CPS AND a.Granularity = @stringRepCat)) cpa

            WHERE       CPS IS NOT NULL;

            DROP TABLE IF EXISTS #newadjustmentsfull;
            SELECT  a.*
            INTO    #newadjustmentsfull
            FROM    (SELECT AdjustmentID
                           ,Granularity
                           ,AdjustmentDescription
                           ,CPS
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,BenefitCategoryID
                           ,CostAdjustment
                           ,UseAdjustment
                     FROM   #newadjustments WITH(NOLOCK)
                     UNION ALL
                     SELECT NULL AS AdjustmentID
                           ,Granularity
                           ,AdjustmentDescription
                           ,CPS
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,BenefitCategoryID
                           ,CostAdjustment
                           ,UseAdjustment
                     FROM   #clearrowsrep WITH(NOLOCK)
                     UNION ALL
                     SELECT NULL AS AdjustmentID
                           ,Granularity
                           ,AdjustmentDescription
                           ,CPS
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,BenefitCategoryID
                           ,CostAdjustment
                           ,UseAdjustment
                     FROM   #clearrowsben WITH(NOLOCK)) a;


            DROP TABLE IF EXISTS #checkoutput;
			--Prepare 'Compare' data that puts three sources of adjustments together
            WITH cteAdjJoin
            AS (SELECT      cpa.AdjustmentID AS AdjustmentID
                           ,na1.Granularity
                           ,na1.AdjustmentDescription
                           ,na1.CPS
                           ,na1.TrendYearID
                           ,na1.RateType
                           ,na1.ReportingCategory
                           ,na1.BenefitCategoryID
                           ,na.CostAdjustment
                           ,na.UseAdjustment
                           ,cpap.CostAdjustment AS PriorCost
                           ,cpap.UseAdjustment AS PriorUse
                           ,CASE WHEN (COALESCE (na.CostAdjustment, 0) = COALESCE (cpap.CostAdjustment, 0)
                                       AND  COALESCE (na.UseAdjustment, 0) = COALESCE (cpap.UseAdjustment, 0)) THEN 0
                                 ELSE 1 END AS PriorAdjDiff
                           ,cpap.LastUpdateDateTime AS PriorDateTime
                           ,cpa.CostAdjustment AS SQLCost
                           ,cpa.UseAdjustment AS SQLUse
                           ,CASE WHEN (COALESCE (na.CostAdjustment, 0) = COALESCE (cpa.CostAdjustment, 0)
                                       AND  COALESCE (na.UseAdjustment, 0) = COALESCE (cpa.UseAdjustment, 0)) THEN 0
                                 ELSE 1 END AS SQLAdjDiff
                FROM        (SELECT DISTINCT
                                    Granularity AS Granularity
                                   ,AdjustmentDescription AS AdjustmentDescription
                                   ,CPS AS CPS
                                   ,TrendYearID AS TrendYearID
                                   ,RateType AS RateType
                                   ,(CASE WHEN Granularity = @stringRepCat THEN ReportingCategory ELSE NULL END) AS ReportingCategory
                                   ,(CASE WHEN Granularity = @stringBenCat THEN BenefitCategoryID ELSE NULL END) AS BenefitCategoryID
                             FROM   #newadjustmentsfull WITH(NOLOCK)
                             UNION
                             SELECT DISTINCT
                                    Granularity AS Granularity
                                   ,AdjustmentDescription AS AdjustmentDescription
                                   ,CPS AS CPS
                                   ,TrendYearID AS TrendYearID
                                   ,RateType AS RateType
                                   ,(CASE WHEN Granularity = @stringRepCat THEN ReportingCategory ELSE NULL END) AS ReportingCategory
                                   ,(CASE WHEN Granularity = @stringBenCat THEN BenefitCategoryID ELSE NULL END) AS BenefitCategoryID
                             FROM   #CalcPlanAdjmt WITH(NOLOCK)
                             UNION
                             SELECT DISTINCT
                                    Granularity AS Granularity
                                   ,AdjustmentDescription AS AdjustmentDescription
                                   ,CPS AS CPS
                                   ,TrendYearID AS TrendYearID
                                   ,RateType AS RateType
                                   ,(CASE WHEN Granularity = @stringRepCat THEN ReportingCategory ELSE NULL END) AS ReportingCategory
                                   ,(CASE WHEN Granularity = @stringBenCat THEN BenefitCategoryID ELSE NULL END) AS BenefitCategoryID
                             FROM   #CalcPlanAdjmtPrior WITH(NOLOCK)) AS na1
                LEFT JOIN   #newadjustmentsfull na WITH(NOLOCK)
                       ON na1.Granularity = na.Granularity 
                          AND   na1.AdjustmentDescription = na.AdjustmentDescription
                          AND   na1.CPS = na.CPS
                          AND   na1.TrendYearID = na.TrendYearID
                          AND   na1.RateType = na.RateType
                          AND   COALESCE (na1.ReportingCategory, 'ABC') = COALESCE (na.ReportingCategory, 'ABC')
                          AND   COALESCE (na1.BenefitCategoryID, -1) = COALESCE (na.BenefitCategoryID, -1)
                LEFT JOIN   #CalcPlanAdjmt cpa WITH(NOLOCK)
                       ON na1.Granularity = cpa.Granularity
                          AND   na1.AdjustmentDescription = cpa.AdjustmentDescription
                          AND   na1.CPS = cpa.CPS
                          AND   na1.TrendYearID = cpa.TrendYearID
                          AND   na1.RateType = cpa.RateType
                          AND   COALESCE (na1.ReportingCategory, 'ABC') = COALESCE (cpa.ReportingCategory, 'ABC')
                          AND   COALESCE (na1.BenefitCategoryID, -1) = COALESCE (cpa.BenefitCategoryID, -1)
                LEFT JOIN   #CalcPlanAdjmtPrior cpap WITH(NOLOCK)
                       ON na1.Granularity = cpap.Granularity
                          AND   na1.AdjustmentDescription = cpap.AdjustmentDescription
                          AND   na1.CPS = cpap.CPS
                          AND   na1.TrendYearID = cpap.TrendYearID
                          AND   na1.RateType = cpap.RateType
                          AND   COALESCE (na1.ReportingCategory, 'ABC') = COALESCE (cpap.ReportingCategory, 'ABC')
                          AND   COALESCE (na1.BenefitCategoryID, -1) = COALESCE (cpap.BenefitCategoryID, -1))

            SELECT      base.AdjustmentID
                       ,base.Granularity
                       ,base.AdjustmentDescription
                       ,base.CPS
                       ,base.TrendYearID
                       ,base.RateType
                       ,base.ReportingCategory
                       ,base.BenefitCategoryID
                       ,base.CostAdjustment
                       ,base.UseAdjustment
                       ,base.PriorCost
                       ,base.PriorUse
                       ,base.PriorAdjDiff
                       ,CASE WHEN plancnt.PriorAdjCnt = 0 THEN 0 ELSE 1 END AS PriorPlanDiff
                       ,base.PriorDateTime
                       ,base.SQLCost
                       ,base.SQLUse
                       ,base.SQLAdjDiff
                       ,CASE WHEN plancnt.SQLAdjCnt = 0 THEN 0 ELSE 1 END AS SQLPlanDiff
            INTO        #checkoutput
            FROM        cteAdjJoin base WITH(NOLOCK)
            LEFT JOIN   (SELECT     CPS
								   ,Granularity
                                   ,SUM (SQLAdjDiff) AS SQLAdjCnt
                                   ,SUM (PriorAdjDiff) AS PriorAdjCnt
                         FROM       cteAdjJoin WITH(NOLOCK)
                         GROUP BY   CPS, Granularity) plancnt
                   ON plancnt.CPS = base.CPS AND plancnt.Granularity = base.Granularity;
			
			--filter on plans that have at least one change, which is what will need to be uploaded to MAAUI
			--uploading plans without changes would flip the reprice flag unnecessarily 
            DROP TABLE IF EXISTS #output;
            SELECT      AdjustmentID
                       ,Granularity
                       ,AdjustmentDescription
                       ,CPS
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,BenefitCategoryID
                       ,CostAdjustment
                       ,UseAdjustment
            INTO        #output
            FROM        #checkoutput WITH(NOLOCK)
            WHERE       SQLPlanDiff = 1
                        AND NOT (CostAdjustment IS NULL
                                 AND UseAdjustment IS NULL)
            ORDER BY    CPS
                       ,AdjustmentDescription
                       ,RateType
                       ,ReportingCategory;


            --return compare to tool
            SELECT  Granularity
                   ,AdjustmentDescription
                   ,CPS
                   ,TrendYearID
                   ,RateType
                   ,ReportingCategory
                   ,BenefitCategoryID
                   ,CostAdjustment
                   ,UseAdjustment
                   ,PriorCost
                   ,PriorUse
                   ,CASE WHEN PriorAdjDiff = 1 THEN 'Y' ELSE 'N' END AS PriorAdjDiff
                   ,CASE WHEN PriorPlanDiff = 1 THEN 'Y' ELSE 'N' END AS PriorPlanDiff
                   ,PriorDateTime
                   ,SQLCost
                   ,SQLUse
                   ,CASE WHEN SQLAdjDiff = 1 THEN 'Y' ELSE 'N' END AS SQLAdjDiff
                   ,CASE WHEN SQLPlanDiff = 1 THEN 'Y' ELSE 'N' END AS SQLPlanDiff
            FROM    #checkoutput WITH(NOLOCK);

            --return output to tool
            SELECT  AdjustmentID
                   ,Granularity
                   ,AdjustmentDescription
                   ,CPS
                   ,TrendYearID
                   ,RateType
                   ,ReportingCategory
                   ,BenefitCategoryID
                   ,CostAdjustment
                   ,UseAdjustment
            FROM    #output WITH(NOLOCK);

            --return SCT output to tool
            SELECT  sct.PlanYearID
                   ,sct.ContractPBPSegment
                   ,sct.NonESRDHospiceRevenueAdj
                   ,sct.NonESRDHospiceClaimAdjRiskOffset
                   ,sct.NonESRDHospiceClaimAdjOther
                   ,sct.ESRDHospiceRevenueAdj
                   ,sct.ESRDHospiceClaimAdj
                   ,sct.QualityAdj
                   ,TRIM(sct.AdjustmentType) AS AdjustmentType
                   ,TRIM(sct.Comment) AS Comment
            FROM    dbo.TrendAdj_CalcPlanAdjmt_SCT sct WITH(NOLOCK)
            WHERE   sct.SessionID = @xsessionid;

            INSERT INTO dbo.TrendAdjLog
                (AdjGroupID
                ,ProcName
                ,Region
                ,UserID
                ,AuditTime
                ,AuditMessage)
            VALUES (@xadjgroup              -- AdjGroupID - tinyint
                   ,@xcalcorclear           -- ProcName - varchar(20)
                   ,@xregion                -- Region - varchar(40)
                   ,LEFT(@xsessionid, 7)    -- UserID - char(7)
                   ,GETDATE ()              -- AuditTime - datetime
                   ,NULL);

            COMMIT TRANSACTION transaction_TrendAdj;

            -- Clean all temp tables.
            DROP TABLE IF EXISTS #LkpAdjTypeID;
            DROP TABLE IF EXISTS #RegionPlanList;
            DROP TABLE IF EXISTS #stgCalcPlanAdjmt;
            DROP TABLE IF EXISTS #CalcPlanAdjmt;
            DROP TABLE IF EXISTS #cleartemplaterep;
            DROP TABLE IF EXISTS #cleartemplateben;
            DROP TABLE IF EXISTS #CalcPlanAdjmtPrior;
            DROP TABLE IF EXISTS #newadjustments;
            DROP TABLE IF EXISTS #clearrowsben;
            DROP TABLE IF EXISTS #clearrowsrep;
            DROP TABLE IF EXISTS #newadjustmentsfull;
            DROP TABLE IF EXISTS #checkoutput;
            DROP TABLE IF EXISTS #output;

        END TRY

        BEGIN CATCH

            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @currentdate DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ();
            SELECT  @ErrorSeverity = ERROR_SEVERITY ();
            SELECT  @ErrorState = ERROR_STATE ();

            RAISERROR (@ErrorMessage    -- Message text.  
                      ,@ErrorSeverity   -- Severity.  
                      ,@ErrorState      -- State.  
            );

            ROLLBACK TRANSACTION;

			INSERT INTO dbo.TrendAdjLog
                (AdjGroupID
                ,ProcName
                ,Region
                ,UserID
                ,AuditTime
                ,AuditMessage)
            VALUES (@xadjgroup
                   ,'Calc:OutPut'
                   ,LEFT(@xregion, 40)
                   ,LEFT(@xsessionid, 7)
                   ,GETDATE ()
                   ,@ErrorMessage);

        END CATCH;

    END;
GO