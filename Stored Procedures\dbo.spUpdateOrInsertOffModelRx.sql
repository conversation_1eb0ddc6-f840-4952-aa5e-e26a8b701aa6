SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateOrInsertOffModelRx
--
-- AUTHOR: <PERSON><PERSON>rell
--
-- CREATED DATE: 2008-Apr-16
-- HEADER UPDATED: 2010-Oct-18
--
-- DESCRIPTION: Updates the existing off-model Rx details for the specified MA plan index.  If there is not already a
--              record for the plan, a new record is inserted.
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--      @RxBasicPremium
--      @RxSuppPremium
--	Output:
--
-- TABLES:
--	Read:
--      SavedPlanAssumptions
--	Write:
--      SavedPlanAssumptions
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                             	        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-Apr-17      1           Initial version.                                                    Tonya Cockrell
-- 2009-Mar-27      2           Accept negative Basic premiums                                      Sandy Ellis
-- 2010-Oct-15      3           Updated for Table Changes, and added IsOffModelRx update		    Craig Wright
-- 2010-Oct-18      4           Removed the IsOffModelRx Indicator							        Craig Wright
-- 2011-Feb-07		5			Enabled Negative RxBasic premiums									Craig Wright
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROC [dbo].[spUpdateOrInsertOffModelRx]
(
    @ForecastID INT,
    @RxBasicPremium DECIMAL(9,2),
    @RxSuppPremium DECIMAL(9,2)
)
AS
    --Make Sure we don't have any negative premiums
    --IF ISNULL(@RxBasicPremium, 0) <= 0
    --    SET @RxBasicPremium = 0
    IF ISNULL(@RxSuppPremium, 0) <= 0
        SET @RxSuppPremium = 0
    IF EXISTS
        (
        SELECT 1  
        FROM SavedPlanAssumptions
        WHERE ForecastID = @ForecastID
        )
        BEGIN
            --Record already exists - update it.
            UPDATE SavedPlanAssumptions
            SET RxBasicPremium = @RxBasicPremium,
                RxSuppPremium = @RxSuppPremium
            WHERE ForecastID = @ForecastID
        END
    ELSE
        INSERT SavedPlanAssumptions
        (
            ForecastID,
            RxBasicPremium,
            RxSuppPremium
        )
        VALUES
        (
            @ForecastID,
            @RxBasicPremium,
            @RxSuppPremium
        )
GO
