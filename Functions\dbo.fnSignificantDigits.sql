SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnSignificantDigits]  ******/

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnSignificantDigits
--
-- AUTHOR: Sandy Ellis
--
-- CREATED DATE: 2007-Oct-11
--
-- DESCRIPTION: This function is used for limiting values to a specified number of significant digits.
--
-- PARAMETERS:
--	Input:
--		@number - The value limited to a certain number of significant digits, specified by @digits.
--		@digits - The number of significant digits to be applied to @number.
--
-- RETURNS: 
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE																	DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Oct-11		1			Initial Version																	Sandy Ellis
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnSignificantDigits]
    (
    @number FLOAT,
    @digits INT
    )
RETURNS FLOAT AS
    BEGIN
    DECLARE @result FLOAT
    SELECT @result =
        CASE
            WHEN @number = 0
                THEN 0
            ELSE ROUND(@number,@digits - 1 - FLOOR(LOG10(ABS(@number))))
        END
    RETURN (@result)
    END
GO
