SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_spCalcUCDBBidYear
--
-- CREATOR: <PERSON><PERSON><PERSON><PERSON> 
--
-- CREATED DATE: Nov-05-2020 
--
-- DESCRIPTION: This stored procedure takes membership from MRA deliverables and adds on the UCDB relativites from the Trends team
--        
-- PARAMETERS:
--  Input  : @CPBPList
--			 @RegionString
--			 @ProductString
--			 @LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  dbo.LkpIntPlanYear
--					dbo.UCDB_County_FIPS
--                  dbo.UCDB_Final_Relativities
--					dbo.Trend_SavedPopulationBarcBidYearMembership
--          Write:  dbo.Trend_SavedRelativityCMSReimb
--
-- VIEWS: Read: dbo.vwPlanInfo
--
-- FUNCTIONS: dbo.Trend_fnSafeDivide
--		      dbo.Trend_fnCalcStringToTable
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 11/05/20    		0		    Initial Version																Aleksandar Dimitrijevic
-- 11/30/20    		1		    Removed temp table #RepCat													
--								Added line "WHERE SSStateCountyCD <> 99999"									Aleksandar Dimitrijevic
--								for the creation of table #mbr_tbl
--								Removed section of the code that replicates relativities for
--								Part B Rx categories
-- 12/02/20    		2		    Split into two sp's (current year and bid year)								Aleksandar Dimitrijevic
-- 12/22/20    		3		    Added ROWLOCK and NOLOCK for required tables								Surya Murthy
-- 1/21/21    		4		    Added PlanInfo/Region/Product Strings										Aleksandar Dimitrijevic
-- 2/20/21          5           sp renamed from [dbo].[Trend_BidYearUCBD]
--								replaced the output table:                                                  
--								dbo.Trend_UCDB --> [dbo].[Trend_SavedRelativityCMSReimb]                    Aleksandar Dimitrijevic    
--								changed the calculation of the CostRelativity in table #final_predup
--                              to force the CostRelativity = 1 when sum(total_MM) = 0
-- 9/16/21	        6			Cleaned up code to use best practices										Tanvi Khanna
-- 10/15/21         7           Fixed Sonar Qube Issues                                                     Ramandeep Saini
-- 08/04/23			8			Change LEFT JOIN #vwPlanInfo to INNER JOIN			                        Aleksandar Dimitrijevic
-- 08/04/23			9			Added Internal Parameter, NOLOCK, Object Schema								Sheetal Patil
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_spCalcUCDBBidYear]
--Declare
@CPBPList        VARCHAR(MAX) = NULL  --list of plans separated by commas, EX: 'H1036-130-000,H1019-079-000,...' 
,@RegionString   VARCHAR(MAX) = NULL
,@ProductString  VARCHAR(MAX) = NULL
,@LastUpdateByID CHAR(13)  --= 'axd8231'

AS

   BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        ----------------------------------------------------
        -- 0. Declare / set variables and pull parameters --
        ----------------------------------------------------
DECLARE @XCPBPList        VARCHAR(MAX) = @CPBPList;
DECLARE @XRegionString   VARCHAR(MAX) = @RegionString;
DECLARE @XProductString  VARCHAR(MAX) = @ProductString  

        -- Bid Year
        DECLARE @BidYear SMALLINT;
        SET @BidYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WITH (NOLOCK) WHERE IsBidYear = 1);

        -- Plan Info 
        IF (SELECT  OBJECT_ID ('tempdb..#vwPlanInfo')) IS NOT NULL
            DROP TABLE #vwPlanInfo;
        SELECT  DISTINCT
                CPS
               ,PlanYear
               ,Division
               ,Region
               ,PlanInfoID
               ,Product
        INTO    #vwPlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear
                AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@XCPBPList, ',', 1) )
                     OR @XCPBPList IS NULL)
                AND (Region IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@XRegionString, ',', 1) )
                     OR @XRegionString IS NULL)
                AND (Product IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@XProductString, ',', 1) )
                     OR @XProductString IS NULL);

        ----------------------------------------------------
        -- 1. Pull county level source data ----------------
        ----------------------------------------------------

        -- Relativities and Membership
        IF (SELECT  OBJECT_ID ('tempdb..#CountyData')) IS NOT NULL
            DROP TABLE #CountyData;
        SELECT      m.CPS
                   ,CASE WHEN LEN (m.SSStateCountyCD) = 4 THEN '0' + CAST(m.SSStateCountyCD AS VARCHAR)
                         ELSE m.SSStateCountyCD END AS SSStateCountyCD
                   ,m.PlanYearID
                   ,m.QuarterID
                   ,ufr.CBSAStrip
                   ,ufr.ReportingCategory
                   ,vpi.Product
                   ,COALESCE (ufr.relativity, 1) AS Relativity
                   ,COALESCE (SUM (m.Bid_MM), 0) AS MemberMonths
        INTO        #CountyData
        FROM        dbo.Trend_SavedPopulationBarcBidYearMembership m WITH (NOLOCK)
        INNER JOIN  #vwPlanInfo vpi
               ON   vpi.CPS = m.CPS
              AND   vpi.PlanYear = m.PlanYearID
        LEFT JOIN   dbo.UCDB_County_FIPS ucf WITH (NOLOCK)
               ON m.SSStateCountyCD = ucf.MedicareCntyNbr
        LEFT JOIN   dbo.UCDB_Final_Relativities ufr WITH (NOLOCK)
               ON ufr.PlanYearID = m.PlanYearID
                  AND   ufr.Quarter = m.QuarterID
                  AND   ufr.CBSAStrip = ucf.CBSAStrip
                  AND   ufr.product = vpi.Product
        WHERE       m.SSStateCountyCD <> 99999
        GROUP BY    m.CPS
                   ,m.SSStateCountyCD
                   ,m.PlanYearID
                   ,m.QuarterID
                   ,ufr.CBSAStrip
                   ,ufr.ReportingCategory
                   ,vpi.product
                   ,ufr.relativity;

        -------------------------------------------------------
        -- 2. Roll up to plan level and insert final results --
        -------------------------------------------------------

        DELETE  FROM dbo.Trend_SavedRelativityCMSReimb WITH (ROWLOCK)
        WHERE   PlanYearID = @BidYear
                AND CPS IN (SELECT  CPS FROM #vwPlanInfo);

        INSERT INTO dbo.Trend_SavedRelativityCMSReimb WITH (ROWLOCK)
        (CPS
		,PlanYearID
		,QuarterID
		,ReportingCategory
		,CostRelativity
		,UseRelativity
		,LastUpdateByID
		,LastUpdateDateTime)
        SELECT      c.CPS
                   ,c.PlanYearID
                   ,c.QuarterID
                   ,c.ReportingCategory
                   ,dbo.Trend_fnSafeDivide (SUM (c.MemberMonths * c.Relativity), SUM (c.MemberMonths), 1) AS CostRelativity
                   ,1 AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #CountyData c
        WHERE       c.CPS IN (SELECT    CPS FROM   #vwPlanInfo)
        GROUP BY    c.CPS
                   ,c.PlanYearID
                   ,c.QuarterID
                   ,c.reportingcategory;
    END;

GO
