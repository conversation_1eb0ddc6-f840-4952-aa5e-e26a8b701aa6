SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSavePlanGroups]   
--    
-- AUTHOR: Sur<PERSON>rthy 
--    
-- CREATED DATE: 2024-Oct-30   
-- Type: 
-- DESCRIPTION: Procedure responsible for saving plan groups 
--    
-- PARAMETERS:    
-- Input: 
-- @GroupID
-- @GroupName
-- @PlanInfoIDs
-- @RegionID
-- @LastUpdatedByUserId

-- TABLES:   
--
-- Read:    
--  
-- Write:    
--  PrePricing.PlanGroupMapping
--  PrePricing.PlanGroup 

-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE										DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-30		1		Initial Version										  Surya Murthy
-- 2025-Feb-03		2		Error message logic added							  Surya Murthy
-- 2025-Apr-01		3		Altered name constraint to be unique per region       Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSavePlanGroups]
(
	@GroupID INT,
	@GroupName VARCHAR(200),
	@PlanInfoIDs VARCHAR(MAX), 
	@RegionID INT,
	@LastUpdatedByUserId VARCHAR(100)	
)
AS    
BEGIN    
	SET NOCOUNT ON
	BEGIN TRY
	BEGIN TRANSACTION groupsave	 
	DECLARE	@OutPutResultCode VARCHAR(20)
	SET @OutPutResultCode='Success'
	DECLARE @OutPutResult VARCHAR(200);
	SET @OutPutResult='Group:'+@GroupName+' Saved Successfully.'
	IF EXISTS (SELECT 1 FROM PrePricing.PlanGroup WITH(NOLOCK) WHERE PlanGroupID=@GroupID AND RegionID = @regionid)
	BEGIN
		--First updaed group details
		UPDATE  PrePricing.PlanGroup SET
		PlanGroupName=@GroupName,		
		LastUpdateByID=@LastUpdatedByUserId,
		LastUpdateDateTime=GETDATE()
		WHERE PlanGroupID=@GroupID;
		DELETE FROM PrePricing.PlanGroupMapping WHERE PlanGroupID=@GroupID;
		--Next update group mapping			 
		INSERT INTO PrePricing.PlanGroupMapping(PlanGroupID,LastUpdateByID,LastUpdateDateTime,PlanInfoID)
		SELECT 		
		@GroupID
		,@LastUpdatedByUserId
		,GETDATE()
		,VALUE AS PlanInfoID FROM STRING_SPLIT(@PlanInfoIDs,',');			
	END
	ELSE
	BEGIN	
		IF NOT EXISTS (SELECT 1 FROM PrePricing.PlanGroup WITH(NOLOCK) WHERE PlanGroupName=@GroupName AND RegionID = @regionid)
		BEGIN
			INSERT INTO PrePricing.PlanGroup
			(			
				PlanGroupName,
				PlanGroupDescription,
				RegionID,
				LastUpdateByID,
				LastUpdateDateTime
			)
			VALUES
			(   			
				@GroupName,
				NULL,
				@RegionID,
				@LastUpdatedByUserId,
				GETDATE()
			 );		
			 SELECT @GroupID = SCOPE_IDENTITY();
			 --Next update group mapping			 
			INSERT INTO PrePricing.PlanGroupMapping(PlanGroupID,LastUpdateByID,LastUpdateDateTime,PlanInfoID)
			SELECT 		
			@GroupID
			,@LastUpdatedByUserId
			,GETDATE()
			,VALUE AS PlanInfoID FROM STRING_SPLIT(@PlanInfoIDs,',');				
		 END
		 ELSE
		 BEGIN
			SET @OutPutResultCode='Error'
			SET @OutPutResult='Error Group Name '+@GroupName+' Already Exists.';				
		 END
	END			
	SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT TRANSACTION groupsave;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION groupsave;
		SET @OutPutResultCode='Error'
		SET @OutPutResult='Error while saving group:'+@GroupName;		
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH
END
GO
