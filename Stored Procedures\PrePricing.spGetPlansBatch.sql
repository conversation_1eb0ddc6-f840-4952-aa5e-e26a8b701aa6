SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetPlansBatch]   
--    
-- AUTHOR: <PERSON><PERSON>y 
--    
-- CREATED DATE: 2024-Oct-28    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Regions  
--    
-- PARAMETERS:   
	--@RegionID
	--@PlanInfoID
-- Input: 

-- TABLES:   
-- PrePricing.PlanInfo
-- dbo.SavedMarketInfo
-- dbo.LkpProductType
-- PrePricing.PlanGroupMapping
-- PrePricing.PlanGroup

-- Read:    
--  

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE							DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-28		1		Initial Version							Surya Murthy
-- 2025-Feb-21		2		Sorting order changes by CPS            Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetPlansBatch]
(
	@RegionID VARCHAR(MAX)
)
AS    
BEGIN    
		SELECT pinfo.PlanInfoID , smi.ActuarialMarketID AS MarketID,smi.ActuarialMarket AS MarketName,
		smi.ActuarialRegionID AS RegionID,
		lkptype.ProductType AS Product,pinfo.IsEnabled,
		CASE 
WHEN snptype = 'NA' THEN 'GEIC'
WHEN snptype = 'CHRONIC' THEN 'C-SNP'
WHEN snptype = 'ESRD' THEN 'C-SNP ESRD'
WHEN snptype = 'Dual Eligible' THEN 'D-SNP'
WHEN snptype = 'Institutional' THEN 'I-SNP'
WHEN snptype = 'Institutional Equivalent' THEN 'IE-SNP'
ELSE snpType
END AS SNPTypeName,
		pinfo.CPS,pinfo.PlanDescription,
		PlanGroups=STUFF((SELECT ',' +  CAST(b.PlanGroupID AS VARCHAR(7))
				   FROM PrePricing.PlanGroup b WITH(NOLOCK)
				   JOIN PrePricing.PlanGroupMapping c WITH(NOLOCK) ON c.PlanInfoID=pinfo.PlanInfoID AND b.PlanGroupID=c.PlanGroupID           
				  FOR XML PATH('')),1, 1, '')
		FROM PrePricing.PlanInfo AS pinfo WITH(NOLOCK)
		JOIN dbo.SavedMarketInfo AS smi ON smi.ActuarialMarketID = pinfo.MarketID AND smi.ActuarialRegionID IN(SELECT VALUE  FROM STRING_SPLIT(@RegionID,','))
		JOIN dbo.LkpProductType AS lkptype ON lkptype.ProductTypeID = pinfo.ProductTypeID
		LEFT JOIN PrePricing.PlanGroupMapping AS pgpm ON pgpm.PlanInfoID=pinfo.PlanInfoID
		JOIN dbo.LkpSNPType AS lkpsnptype ON lkpsnptype.SNPTypeID=pinfo.SNPTypeID
		LEFT JOIN PrePricing.PlanGroup AS pgrp ON pgrp.PlanGroupID=pgpm.PlanGroupID AND pgrp.RegionID=smi.ActuarialRegionID	
		GROUP BY pinfo.PlanInfoID ,smi.ActuarialMarketID,smi.ActuarialMarket,smi.ActuarialRegionID,lkptype.ProductType,
		pinfo.IsEnabled,snptype,lkpsnptype.SNPTypeName,pinfo.CPS,pinfo.PlanDescription, pinfo.LastUpdateDateTime 
		ORDER BY  pinfo.CPS	ASC
END
GO
