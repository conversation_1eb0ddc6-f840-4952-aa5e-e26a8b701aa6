SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnAppGetMABidNotes]   ******/

-- FUNCTION NAME: fnAppGetMABidNotes
--
-- AUTHOR: Christian Cofie 
--
-- CREATED DATE: 2007-Apr-24
-- HEADER UPDATED: 2010-Aug-23
--
-- DESCRIPTION: Function responsible for getting MA Bid Notes.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		LkpIntMABidNoteDetail
--		LkpIntMABidNoteHeader
--		SavedPlanBidNoteDetail
--		SavedPlanHeader      
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetMARatingOption
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007-Apr-24		1			Initial Version														Christian Cofie
-- 2007-Oct-04		2			Revised code to bring it into coding standards.						Shannon Boykin
-- 2008-Feb-04		3			Updated BeginVersion -> PlanVersionBegin and EndVersion ->			Brian Lake
--									PlanVersionEnd in SavedPlanBidNoteDetail
-- 2008-May-02		4			Applied function fnGetMARatingOption as part of the join			Christian Cofie
--									to use the computed MA Rating Option ID
-- 2008-May-03		5			Added  AND n.BidNoteID = l.MABidNoteID to Left join					Sandy Ellis
-- 2008-May-05		6			Revised Bid Note function, joins to SavedPlanHeader					Brian Lake
-- 2010-Aug-23		7			Removed PlanYearID and PlanVersion									Joe Casey
-- 2012-Jan-03		8			Added code to get NoteText for EGWP (Group plans)					Tim Gao
-- 2014-Feb-27		9			SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals		Mike Deren 
-- 2017-Sep-13		10			Removed parameter in fnGetMemberMonthsAndAllowedByDuals				Chris Fleming
-- 2022-June-10		11			MAAUI migration; replaced @PlanIndex with @ForecastID and other
--								references from PlanIndex to ForecastID; removed IsEGWP; removed
--								nested queries and recoded based on coding standards (function
--								dropped and recreated)												Aleksandar Dimitrijevic 
-- 2023-Aug-03		12			Added NOLOCK and Internal parameter								    Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABidNotes]
    (
    @ForecastID INT
    )
	
RETURNS @Results TABLE    
    (    
      ForecastID INT,  
	  PlanTypeID TINYINT,
	  BidWorksheet TINYINT,
	  CellLocation VARCHAR(7),
	  IsDefaultBidNote BIT,
	  BidNoteID SMALLINT,
	  NoteText VARCHAR(MAX)
	)
AS
BEGIN

DECLARE @XForecastID INT = @ForecastID

	--Output
	INSERT INTO @Results
    SELECT SPH.ForecastID,
		   SPH.PlanTypeID,
		   BH.BidWorksheet,
		   BH.CellLocation,
		   IsDefaultBidNote = ISNULL(IsDefaultBidNote, 1),
		   BH.MABidNoteID as BidNoteID,
		   NoteText = CASE WHEN ISNULL(IsDefaultBidNote, 1) = 1 
						   THEN BD.NoteText
						   ELSE SPB.BidNoteOverride
					  END
    FROM dbo.LkpIntMABidNoteHeader BH WITH (NOLOCK)
	INNER JOIN dbo.LkpIntMABidNoteDetail BD WITH (NOLOCK)
		ON BH.MABidNoteID = BD.MABidNoteID
	INNER JOIN dbo.SavedPlanHeader SPH WITH (NOLOCK)
		ON SPH.PlanTypeID = BD.PlanTypeID
	LEFT JOIN dbo.SavedPlanBidNoteDetail SPB WITH (NOLOCK)
		ON SPB.BidNoteID = BH.MABidNoteID
	   AND SPB.ForecastID = SPH.ForecastID
	   AND SPB.IsHidden = 0

    WHERE SPH.ForecastID = @XForecastID
	  AND BD.MARatingOptionID = ISNULL(dbo.fnGetMARatingOption(@XForecastID), 1)

RETURN    
END

GO
