SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSaveMarketInputValues]   
--    
-- AUTHOR: Surya Murthy 
--    
-- CREATED DATE: 2024-Nov-04   
-- Type: 
-- DESCRIPTION: Procedure responsible for saving market input report info.
--    
-- PARAMETERS:    
-- Input: 
-- @PlanInfoID
-- @SubcategoryID
-- @CostShareType
-- @FlagType
-- @Inputvalue
-- @UserID
-- @OutPutResult

-- TABLES:   
--
-- Read:    
--  
-- Write:    
--  PrePricing.MarketInputValue

-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					    DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Nov-04		1		Initial Version                    Adam Gilbert
-- 2025-Feb-03		2		Error message logic added          Surya Murthy
-- 2025-Feb-12		3		MSB code upper case logic added    Adam Gilbert
-- 2025-Feb-18		4		Formatting issue				   Adam Gilbert
-- 2025-Feb-20		5		Make NUll to emptry string		   Adam Gilbert
-- 2025-Feb-28		6		Adding trim to input value		   Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSaveMarketInputValues]
(
	 @PlanInfoID VARCHAR(MAX)
	,@SubcategoryID VARCHAR(MAX)
	,@CostShareType INT
	,@FlagType VARCHAR(10)
	,@Inputvalue VARCHAR(500)
	,@Notes VARCHAR(MAX)
	,@LastUpdateSource VARCHAR(20)
	,@UserID	VARCHAR(200)
)
AS
BEGIN
	DECLARE @OutPutCode VARCHAR(20)     = '';--Error, Success
	DECLARE @OutputMessage VARCHAR(MAX) = '';--Freeform

	SET NOCOUNT ON;
	BEGIN TRY
		BEGIN TRANSACTION marketsave	
		DECLARE @LogDate DATETIME = DATEADD(SECOND,-1,GETDATE()) --Create small gap between log date and update date

		SET @Inputvalue= TRIM(ISNULL(@Inputvalue,''));

		/*Validation - Begin*/
		DROP TABLE IF EXISTS #Validations

		SELECT 
		DISTINCT OutPutCode,OutputMessage 
		INTO #Validations
		FROM STRING_SPLIT(@SubCategoryID,',')
		CROSS APPLY PrePricing.fnValidateMarketInputSave (value,@CostShareType,@FlagType,@Inputvalue) AS val

		SELECT @OutPutCode = Outputcode, @OutputMessage = STRING_AGG(OutputMessage,'; ') 
		FROM #Validations WHERE OutPutCode = 'Error'
		GROUP BY Outputcode

		IF @OutPutCode = 'Error'
		BEGIN 
			RAISERROR(@OutputMessage,16,1);
		END
		/*Validation - End*/


			INSERT INTO [PrePricing].[LogMarketInputSave] 
			(LogDate,UserID,UpdateSource,PlanList,BenefitList,InputValue,InputType,CostShareType, Notes)
			SELECT @LogDate AS LogDate,@UserID, @LastUpdateSource, @PlanInfoID,@SubcategoryID,@Inputvalue,@FlagType,@CostShareType,@Notes;

			DROP TABLE IF EXISTS #InputData;

			--Parse input and build format dataset to be merged.
			SELECT *,@FlagType FlagType,
			CASE WHEN @FlagType IN ( 'IN', 'BOTH') THEN @InputValue ELSE NULL END AS INValue, 
			CASE WHEN @FlagType IN ( 'OON', 'BOTH') THEN @InputValue ELSE NULL END AS OONValue, 
			CASE WHEN @FlagType IN ('$ Change') THEN @InputValue ELSE NULL END AS BenefitChangeValue,
			CASE WHEN @FlagType IN ( 'IN', 'BOTH') THEN @CostShareType ELSE NULL END AS INCostShareType, 
			CASE WHEN @FlagType IN ( 'OON', 'BOTH') THEN @CostShareType ELSE NULL END AS OONCostShareType,
			@Notes AS Notes
			INTO #InputData 
			FROM 
				(SELECT value PlanInfoID FROM STRING_SPLIT(@PlanInfoID,',')	) a
				CROSS JOIN
				(SELECT value SubCategoryID FROM STRING_SPLIT(@SubCategoryID,','))b	


			/*Format Input - Begin*/

			--IF input is an MSB or OSB, capitalize all characters.
			UPDATE t
			SET INValue = UPPER(invalue), OONValue = UPPER(oonvalue)
			FROM #InputData t
			JOIN PrePricing.MarketInputSubCategory s
			ON t.SubCategoryID=s.subcategoryid
			WHERE s.CategoryID IN ( 2 ,9)

			--Prevent cost share selections from being applied to non-costshare fields.
			UPDATE t
			SET INCostShareType = NULL, OONCostShareType = NULL
			FROM #InputData t
			JOIN Prepricing.MarketInputSubCategory sc ON t.SubCategoryID = sc.SubCategoryID
			WHERE sc.IsCostShareType = 0

			/*Format Input - End*/


			--SELECT * FROM #InputData
			--Merge Statement
			MERGE prepricing.MarketInputValue t
			USING #InputData s
			ON t.PlanInfoID = s.PlanInfoID
			AND t.SubcategoryID = s.subcategoryid
			WHEN MATCHED THEN
			UPDATE 
				SET 
				t.INValue = ISNULL (s.INValue,t.INValue),
				t.OONValue = ISNULL(s.OONValue,t.OONValue),
				t.INCostShareType = ISNULL (s.INCostShareType,t.INCostShareType),
				t.OONCostShareType = ISNULL(s.OONCostShareType,t.OONCostShareType),
				t.BenefitChangeValue = CASE WHEN s.BenefitChangeValue = '' THEN NULL ELSE ISNULL(s.BenefitChangeValue,t.BenefitChangeValue) END ,
				t.Note = CASE WHEN ((t.Note <> @Notes AND @Notes<>'') OR t.Note IS NULL) THEN @Notes ELSE ISNULL(t.Note,'') END,
				t.LastUpdateByID = @UserID,
				t.LastUpdateSource = @LastUpdateSource,
				t.LastUpdateDateTime = GETDATE()
			WHEN NOT MATCHED THEN
				INSERT (PlanInfoID, Subcategoryid, INvalue, OONValue, BenefitChangeValue,Note, LastUpdateByID , LastUpdateDateTime,INCostShareType,OONCostShareType,LastUpdateSource)
				VALUES (s.PlanInfoID, s.Subcategoryid,ISNULL(s.invalue,''), ISNULL(s.oonvalue,''), s.BenefitChangeValue,ISNULL(@Notes,''), @UserID,GETDATE(),s.INCostShareType,s.OONCostShareType,@LastUpdateSource);
		COMMIT TRANSACTION marketsave;
		SELECT 'Success' AS OutputCode, 'Save Successful' AS OutputMessage;		
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION marketsave;
		SET @OutPutCode='Error'
		SET @OutputMessage='Failed: ' + ERROR_MESSAGE()
		SELECT @OutPutCode AS OutputCode, @OutputMessage AS OutputMessage
	END CATCH
END
GO
