SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------              
-- PROCEDURE NAME: [dbo].[spAppTrendDeleteImportTablesData]    
--              
-- TYPE: SAME              
--              
-- AUTHOR: Chhavi            
--              
-- CREATED DATE: 2020-Mar-12	             
--              
-- DESCRIPTION: Procedure responsible for retrieving Member month extract infomation based on extract type              
--    by ForecastID.              
--              
-- PARAMETERS:              
-- Input:              
--     @ForecastID              
-- TABLES:               
-- Read:              
--                  
--                 
-- Write:              
--              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:              
--                
-- $HISTORY               
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE			VERSION		CHANGES MADE                 DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Mar-12		1		Initial Version				Chhavi 
--2020-Apr-16       2     Updated for Humana at Home    Kiran   
-- 2020-Jun-10	    3	Renamed the sp name in the       Kiran Pant
--                comment section with prefix spAppTrend
-- ----------------------------------------------------------------------------------------------------------------------              
CREATE PROCEDURE [dbo].[spAppTrendDeleteImportTablesData]--'','Humana at Home Trends','KXP3871',OUT,1
(    
	  @UploadType VARCHAR(50),
      @UserID varchar(7),
	  @MessageFromBackend VARCHAR(MAX) OUTPUT,
	  @Result BIT OUT
)
AS
 SET NOCOUNT ON
    
       BEGIN
	
	 
BEGIN TRY
    BEGIN TRANSACTION

	IF(@UploadType='Humana at Home Trends')
    DELETE FROM dbo.Trend_SavedHumanaAtHomeTrends

	 SET @MessageFromBackend='Successfully Deleted data';	 
	 	SET @Result=1;
	    COMMIT TRANSACTION
    END TRY
   
BEGIN CATCH

		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()
		SET @Result=0;
	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			

	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
	ROLLBACK TRANSACTION;
	set @Result=0
     
	---Insert into app log for logging error------------------
	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@UserID;
 END CATCH;
    
	--ELSE 
	--	SET @ValidationMessage = 'You Do Not Have The Authority To Modify OSB Data' 
	END
GO
