SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetFinalPremiumSavings
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2007-Apr-30
-- HEADER UPDATED: 2010-Nov-19
--
-- DESCRIPTION: Returns Savings calculation amount used in spFinalPremium.
--
-- PARAMETERS:
--  Input:
--      @PlanTypeID 
--      @PlanBenchmark
--      @PlanBid
--      @StandardizedBenchmark
--      @StandardizedBid
--      @ConversionFactor
--  Output:
--
-- RETURNS: 
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007-Apr-30      1           Initial version                                                     Brad Ennis
-- 2008-Sep-25      2           Added @UserID to the list of parameters.	                        Shannon Boykin
-- 2009-Apr-01      3           Changed reference from PlanTypeName to PlanTypeID                   Sandy Ellis
-- 2010-Sep-28      4           Updated for 2012 database                                           Michael Siekerka
-- 2016-Oct-19		5			Added rounding functions around Plan Bid to Match BPT				Jordan Purdue
--2019-oct-30		6			 Removed 'HUMAD\' to UserID											Chhavi Sinha
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetFinalPremiumSavings] 
(
    @PlanTypeID TINYINT, 
    @PlanBenchmark FLOAT,
    @PlanBid FLOAT,
    @StandardizedBenchmark FLOAT,
    @StandardizedBid DECIMAL(7,2),
    @ConversionFactor DECIMAL(38,15),
    @UserID CHAR(7)
)
    RETURNS FLOAT AS
    BEGIN
        IF @PlanTypeID = 3 -- RPPO
            BEGIN
    		IF @PlanBenchmark < @PlanBid
    			RETURN 0
			ELSE
				IF @StandardizedBenchmark < @StandardizedBid
					RETURN 
                        ROUND(
                            dbo.fnIsNegative(
                                @PlanBenchmark - ROUND(@PlanBid,2) + (@StandardizedBenchmark - @StandardizedBid) * @ConversionFactor, 
                                0.0
                            ), 
                            2
                        )
				ELSE 
                    RETURN ROUND(@PlanBenchmark - ROUND(@PlanBid,2), 2)
            END
        ELSE
            IF @PlanBenchmark <= @PlanBid
                RETURN 0
            ELSE 
                RETURN ROUND((@PlanBenchmark - ROUND(@PlanBid,2)),2)
        RETURN 0.0
    END

GO
