SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		<PERSON><PERSON><PERSON>vey
-- Create date: 2024-AUG-12 

-- Description:	Stored procedure responsible for inserting the data for the OSBDetails for the 
-- PerIntOptionalPackageHeader  table.

-- PARAMETERS:  
-- Input:  
--
-- Output:  
--  
-- TABLES:  
-- Read:
--	   ImportDataStaging
--     
-- Write:
--		PerIntOptionalPackageHeader
--      PerIntOptionalPackageDetail
-- VIEWS:  
--  
-- FUNCTIONS:
--
-- STORED PROCS:  
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-AUG-12         1          Initial Version                                                     Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------  
-- =======================================================================================================================

CREATE PROCEDURE [dbo].[spAppImportOSBDetailed]
(
	 @StageId VARCHAR(100)
)
AS
BEGIN
SET NOCOUNT ON;

		DECLARE @jsonData VARCHAR(MAX);
		DECLARE @UserId VARCHAR(7) ;

		SELECT @jsonData = JsonData, @UserId = UserId
		FROM dbo.ImportDataStaging WITH (NOLOCK)
		WHERE StageId = @StageId;
		
	   DELETE FROM dbo.PerIntOptionalPackageHeader;
	   DELETE FROM dbo.PerIntOptionalPackageDetail;

		DECLARE @tbl__importData TABLE
		(
			PlanYearID SMALLINT NOT NULL,
			PackageIndex INT NOT NULL,
			[Name] VARCHAR (200) NOT NULL,
			[Description] VARCHAR (200) NOT NULL,
			TotalExpense [DECIMAL](14, 2) NOT NULL,
			TotalGainLoss [DECIMAL](14, 2) NOT NULL,
			TotalProjectedMemberMonths [DECIMAL](28, 6) NOT NULL,
			IsEnabled [BIT] NOT NULL,
			LastUpdateByID [CHAR](7) NOT NULL,
			LastUpdateDateTime [DATETIME] NOT NULL
		);

		INSERT INTO @tbl__importData 
		SELECT
			PlanYearID,
			PackageIndex,
			[Name],
			[Description],
			[TotalExpense],
			[TotalGainLoss],
			[TotalProjectedMemberMonths],
			[IsEnabled],
			@UserId,
			GETDATE()			
		FROM 
		OPENJSON(@jsonData, '$.PerIntOptionalPackageHeader')
		WITH
	    (
			PlanYearID SMALLINT,
			PackageIndex INT,
			[Name] VARCHAR (200),
			[Description] VARCHAR (200),
			TotalExpense [DECIMAL](14, 2),
			TotalGainLoss [DECIMAL](14, 2),
			TotalProjectedMemberMonths [DECIMAL](28, 6),
			IsEnabled [BIT]
		);
		MERGE INTO [dbo].[PerIntOptionalPackageHeader] AS target
		USING @tbl__importData AS source
		ON (
		    target.PackageIndex = source.PackageIndex 
			AND target.PlanYearID = source.PlanYearID
		)
		WHEN NOT MATCHED THEN 
		INSERT 
		(
			PlanYearID,
			PackageIndex,
			[Name],
			[Description],
			[TotalExpense],
			[TotalGainLoss],
			[TotalProjectedMemberMonths],
			[IsEnabled],
			LastUpdateByID,
			LastUpdateDateTime
		)
		VALUES
		(source.PlanYearID, source.PackageIndex, source.[Name], 
		source.[Description], source.TotalExpense, source.TotalGainLoss, 
		source.TotalProjectedMemberMonths, source.IsEnabled, 
		source.LastUpdateByID, source.LastUpdateDateTime); 
		
		DECLARE @tbl__importDetailData TABLE
		(
			[PlanYearID] [SMALLINT] NOT NULL,
			[PackageIndex] [INT] NOT NULL,
			[PackageDetailID] [TINYINT] NOT NULL,
			[BidServiceCategoryID] [SMALLINT] NOT NULL,
			[PricingComponentDescription] [VARCHAR](500) NOT NULL,
			[AllowedUtilizationTypeID] [TINYINT] NOT NULL,
			[AllowedUtilzationPer1000] [DECIMAL](14, 2) NOT NULL,
			[AllowedAverageCost] [DECIMAL](14, 2) NOT NULL,
			[MeasurementUnitCode] [VARCHAR](4) NOT NULL,
			[EnrolleeCostShareUtilization] [DECIMAL](14, 2) NOT NULL,
			[EnrolleeAverageCostShare] [DECIMAL](14, 2) NOT NULL,
			[AverageAdminExpense] [DECIMAL](14, 2) NOT NULL,
			[AnnualizedAverageProfit] [DECIMAL](14, 2) NOT NULL,
			[IsHidden] [BIT] NOT NULL,
			[LastUpdateByID] [CHAR](7) NOT NULL,
			[LastUpdateDateTime] [DATETIME] NOT NULL
		);

		INSERT INTO @tbl__importDetailData 
		SELECT
			PlanYearID,
			PackageIndex,
			ROW_NUMBER() OVER(PARTITION BY PackageIndex ORDER BY PackageIndex),
			BidServiceCategoryID,
			PricingComponentDescription,
			AllowedUtilizationTypeID,
			AllowedUtilzationPer1000,
			AllowedAverageCost,
			MeasurementUnitCode,
			EnrolleeCostShareUtilization,
			EnrolleeAverageCostShare,
			AverageAdminExpense,
			AnnualizedAverageProfit,
			IsHidden,
			@UserId,
			GETDATE()
		FROM 
		OPENJSON(@jsonData, '$.PerIntOptionalPackageDetail')
		WITH
	    (
			PlanYearID SMALLINT,
			PackageIndex INT,
			BidServiceCategoryID SMALLINT,
			PricingComponentDescription VARCHAR (500),
			AllowedUtilizationTypeID TINYINT,
			AllowedUtilzationPer1000 DECIMAL(14, 2),
		    AllowedAverageCost DECIMAL(14, 2),
			MeasurementUnitCode VARCHAR(4),
			EnrolleeCostShareUtilization DECIMAL(14, 2),
			EnrolleeAverageCostShare DECIMAL(14, 2),
			AverageAdminExpense DECIMAL(14, 2),
			AnnualizedAverageProfit DECIMAL(14, 2),
			IsHidden BIT
		);

		MERGE INTO [dbo].[PerIntOptionalPackageDetail] AS target
		USING @tbl__importDetailData AS source
		ON (
		    target.PackageIndex = source.PackageIndex 
			AND target.PlanYearID = source.PlanYearID
			AND target.BidServiceCategoryID = source.BidServiceCategoryID
			AND target.PricingComponentDescription = source.PricingComponentDescription
		)
		WHEN NOT MATCHED THEN 
		INSERT 
		(
			PlanYearID,
			PackageIndex,
			PackageDetailID,
			BidServiceCategoryID,
			PricingComponentDescription,
			AllowedUtilizationTypeID,
			AllowedUtilzationPer1000,
			AllowedAverageCost,
			MeasurementUnitCode,
			EnrolleeCostShareUtilization,
			EnrolleeAverageCostShare,
			AverageAdminExpense,
			AnnualizedAverageProfit,
			IsHidden,
			LastUpdateByID,
			LastUpdateDateTime
		)
	   VALUES 
	   (source.PlanYearID, source.PackageIndex, source.PackageDetailID, source.BidServiceCategoryID, 
	    source.PricingComponentDescription, source.AllowedUtilizationTypeID, source.AllowedUtilzationPer1000, 
		source.AllowedAverageCost, source.MeasurementUnitCode, source.EnrolleeCostShareUtilization, 
		source.EnrolleeAverageCostShare, source.AverageAdminExpense, source.AnnualizedAverageProfit, source.IsHidden,
		source.LastUpdateByID, source.LastUpdateDateTime); 

		DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;

END
GO
