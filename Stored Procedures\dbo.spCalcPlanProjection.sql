SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcPlanProjection
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Jan-30
--
-- DESCRIPTION: Responsible for updating the CalcPlanProjection table.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@UserID
--
--	Output: NONE
--
-- RETURNS: 
--
-- TABLES:
--	Read:
--		Calc<PERSON>enefitProjection
--		LkpProductType
--		LkpIntBenefitCategory
--		SavedMOOPInclusion
--		Saved<PERSON>lan<PERSON>eader    
--  
--	Write:
--		CalcPlanProjection
--
-- VIEWS:
--	Read: NONE
--
-- STORED PROCS:
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                                    DEVELOPER
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Jan-30      1           Initial Version	                                                                Christian Cofie
-- 2007-Sep-04      2           Added PlanYearID link to SavedMOOPInclusion.                                    Christian Cofie
-- 2008-Feb-04      3           Revised code to bring it into coding standards.                                 Shannon Boykin
-- 2008-Mar-11      4           Added LkpIntBenefitCategory.IsEnabled = 1 to Where Clause                       Brian Lake
-- 2008-May-01      5           Switched CalcBenefitProj to CalcBlendedBenefitProjection                        Brian Lake
-- 2008-May-16      6           Updated comments to reflect CalcBlendedBenefitProj                              Sandy Ellis
-- 2008-Sep-19      7           Added @UserID to the list of parameters and replaced all                        Shannon Boykin
--                                 occurrences of SUSER_SNAME with @UserID.
-- 2009-Mar-11      8           Revised error handling                                                          Brian Lake
-- 2009-Mar-17      9           Data types                                                                      Sandy Ellis
-- 2009-Mar-18      10          Added DualEligibleTypeID                                                        Sandy Ellis
-- 2009-Mar-19      11          Removed error handling and transaction                                          Brian Lake
-- 2009-Apr-16      12          Added Delete statement                                                          Brian Lake
-- 2010-Mar-12		13			Implemented new PlanYear methodology and independence for 2012					Joe Casey
-- 2010-Sep-22      14          Removed PlanVersion                                                             Michael Siekerka
-- 2010-Sep-28      15          Changed reference from CalcBlendedBenefitProjection to CalcBenefitProjection    Michael Siekerka
-- 2010-Oct-01      16          Added MARatingOptionID = 3 to where clause to focus on blended portion only.    Casey Sanders
-- 2011-Jun-06		17			Replaced LkpIntPlanYear with dbo.fnGetBidYear()									Bobby Jaegers
-- 2011-Jun-14		18			Changed @PlanYearID to return SMALLINT instead of INT							Bobby Jaegers
-- 2012-Jan-19		19			Added code to handle PartB only allowed											Tim Gao
-- 2014-Mar-10		20			Modified for SQS																Mike Deren
-- 2014-May-05		21			Modified for 2015 Part B Only Logic, now automated								Nick Koesters		
-- 2016-Mar-10		22			Added PlanReprice Run, when one should populate									Mark Freel
--                                  CalcPlanProjectionPreMbrCS, when 2 CalcPlanProjection		
-- 2017-Sept-12		23			Removed SQSData ID and Reprice # parameters, removed SQS related logic			Chris Fleming
--									cleaned up code, revamped logic to write to CPP
--									removed CalcPlanProjectionPreMbrCS, no longer needed
-- 2018-Apr-24		24			Removed LkpIntProduct Inner Join reference										Jordan Purdue
-- 2019-Jun-28		25			Replace SavedPlanHeader with SavedForecastSetup, removed
--								IsPartBOnly and added 1 by default as this is not present in forecast table 	Pooja Dahiya
--2019-oct-24		26			Removed HUMAD from LastUpdateByID												Chhavi Sinha
--2019-Nov-06       27			Replaced  SavedForecastSetup with SavedPlanHeader								Chhavi Sinha
--2019-Dec-16       28          Correct isPartBOnly hard-coded reference                                        Brent Osantowski
--2022-Jun-22		29			Removed @IsPartBOnly; removed CASE statement from INAllowed, INNetworkAllowed
--								OONAllowed & OONNetworkAllowed													Aleksandar Dimitrijevic
--2022-Oct-18	    30    		Added internal variables for input parameters                           		Khurram Minhas
--						    	Added  WITH (NOLOCK)                        
--2022-Nov-21       31          Added dbo schema for objects, Added temp table #CalcPlanProjection,				Phani Adduri
--								Release temp table memory					
-- --------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcPlanProjection]
(
    @ForecastID INT,
    @UserID CHAR(7)
)
AS

SET NOCOUNT ON

BEGIN
	-- Declare Variables
	DECLARE @LastDateTime DATETIME,
            @PlanYearID SMALLINT
	
    DECLARE @XForecastID INT = @ForecastID,
            @XUserID CHAR(7) = @UserID
			
	-- Set Variables
    SET @PlanYearID = dbo.fnGetBidYear()
    SET @LastDateTime = GETDATE()

IF OBJECT_ID('tempdb.dbo.#CalcPlanProjection') IS NOT NULL   
DROP TABLE #CalcPlanProjection

CREATE TABLE #CalcPlanProjection
(
	PlanYearID SMALLINT,
	ForecastID INT,
	DualEligibleTypeID TINYINT,
	INAllowed DECIMAL(14,6),
	INNetworkAllowed DECIMAL(14,6),
	INMOOPAllowed DECIMAL(14,6),
	OONAllowed DECIMAL(14,6),
	OONNetworkAllowed DECIMAL(14,6),
	OONMOOPAllowed DECIMAL(14,6),
	LastUpdateByID CHAR(7),
	LastUpdateDateTime DATETIME
);
    
	INSERT INTO #CalcPlanProjection(
		PlanYearID, ForecastID, DualEligibleTypeID, INAllowed, INNetworkAllowed,
		INMOOPAllowed, OONAllowed, OONNetworkAllowed, OONMOOPAllowed, LastUpdateByID,
		LastUpdateDateTime)
	SELECT	@PlanYearID,
			BP.ForecastID,
			BP.DualEligibleTypeID, 
			INAllowed = SUM(ISNULL(BP.INAllowed, 0)),
			INNetworkAllowed = SUM(ISNULL(BP.INNetworkAllowed, 0)), 
			INMOOPAllowed = SUM	(	CASE
										WHEN MOOP.IsINMOOPIncluded = 1 THEN BP.INAllowed
										ELSE 0
									END
								),
			OONAllowed = SUM(ISNULL(BP.OONAllowed, 0)),
			OONNetworkAllowed = SUM(ISNULL(BP.OONNetworkAllowed, 0)),
			OONMOOPAllowed = SUM (	CASE
										WHEN MOOP.IsOONMOOPIncluded = 1 THEN BP.OONAllowed
										ELSE 0
									END
								 ),
			@XUserID,
			@LastDateTime
    FROM	dbo.SavedPlanHeader SPH WITH (NOLOCK)
	INNER JOIN 
	(
		SELECT  pro.PlanYearID,
				pro.ForecastID,
				pro.BenefitCategoryID,
				pro.DualEligibleTypeID,
				pro.MARatingOptionID,
				INAllowed = ISNULL(pro.INAllowed, 0),
				INNetworkAllowed = ISNULL(pro.INNetworkAllowed, 0),
				OONAllowed = ISNULL(pro.OONAllowed, 0),
				OONNetworkAllowed = ISNULL(pro.OONNetworkAllowed, 0)
		FROM	dbo.CalcBenefitProjection pro WITH (NOLOCK)
		INNER JOIN dbo.vwLkpPartBOnlyCoveredPercent vw WITH (NOLOCK) --To handle automated Part B Only Covered Percents for Allowed values
		ON vw.BenefitCategoryID = pro.BenefitCategoryID
	) BP
	ON BP.ForecastID = SPH.ForecastID
	INNER JOIN dbo.LkpIntBenefitCategory BC  WITH (NOLOCK)
	ON BC.BenefitCategoryID = BP.BenefitCategoryID
	INNER JOIN dbo.LkpProductType PT  WITH (NOLOCK)
	ON PT.ProductTypeID = SPH.PlanTypeID
	INNER JOIN dbo.SavedMOOPInclusion MOOP   WITH (NOLOCK)
	ON MOOP.BenefitCategoryID = BC.BenefitCategoryID
		AND MOOP.ProductID = PT.ProductMOOPID
	WHERE	BP.ForecastID = @XForecastID
			AND BC.IsEnabled = 1
			AND BP.MARatingOptionID = 3
    GROUP BY 
			BP.ForecastID,
			BP.DualEligibleTypeID;
			
    DELETE FROM dbo.CalcPlanProjection
    WHERE ForecastID = @XForecastID;      

    -- Append new stuff
    INSERT INTO dbo.CalcPlanProjection(
		PlanYearID, ForecastID, DualEligibleTypeID, INAllowed, INNetworkAllowed,
		INMOOPAllowed, OONAllowed, OONNetworkAllowed, OONMOOPAllowed, LastUpdateByID,
		LastUpdateDateTime)
	SELECT PlanYearID, ForecastID, DualEligibleTypeID, INAllowed, INNetworkAllowed,
		INMOOPAllowed, OONAllowed, OONNetworkAllowed, OONMOOPAllowed, LastUpdateByID,
		LastUpdateDateTime
	FROM #CalcPlanProjection;

	IF OBJECT_ID('tempdb.dbo.#CalcPlanProjection') IS NOT NULL   
	DROP TABLE #CalcPlanProjection;
END
