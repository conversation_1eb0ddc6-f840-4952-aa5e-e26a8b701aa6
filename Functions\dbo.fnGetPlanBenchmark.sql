SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- =============================================
-- FUNCTION NAME: fnGetPlanBenchmark
--
-- AUTHOR:  Tim Gao 
--
-- CREATED DATE: 2012-August-13
--
-- DESCRIPTION: Returns the Plan's benchmark
--             
-- PARAMETERS:
--  Input:
--      @ForecastID  
--       
--  Output: 
--
-- RETURNS: 
--  
--  Planbenchmark FLOAT 
--
-- TABLES:
--  Read:
--      SavedPlanHeaders
--		CalcBenchmarkSummary
--      PerExtCMSRebateByContract
--      SavedPalnDetail
--  Write:
--    None
--
--	Functions to call:
--		
--		
--		
--		
--
-- VIEWS: Read: None
--
-- STORED PROCS: Executed: None
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE         VERSION     CHANGES MADE                                                DEVELOPER                        
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2012-Apr-13		1		Initial Version													Tim Gao
-- 2018-Apr-26		2		Modified LkpExtCMSPlanType for new UI table modifications		Jordan Purdue
-- 2018-Oct-30		3		Removed LkpIntMARegionMarketDetail table						Jordan Purdue
-- 2023-Aug-03		4		Added Nolock and Internal Parameter								Sheetal Patil
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetPlanBenchmark]
(
	@ForecastID INT
)
RETURNS @Results TABLE 
(
	ForecastID INT,
	PlanBenchmark FLOAT
)
AS
BEGIN
	DECLARE @PlanBenchmark FLOAT,
			@MSP DECIMAL(8,7),
			@XForecastID INT = @ForecastID 
	SET @MSP = (SELECT SecondaryPayerAdjustment FROM dbo.SavedPlanAssumptions WITH (NOLOCK) WHERE ForecastID = @XForecastID)
	SET @PlanBenchmark = (SELECT PlanBenchmark =
							(
								(
								CASE
									WHEN cpt.ProductTypeID = 3 -- RPPO
										THEN 
										dbo.fnSignificantDigits(
											(ratebook.MARegionRate
											   * cval.StatCompPercent)
												 +   (
													CASE
														WHEN (EstimatedPlanBidComponent IS NULL)
															THEN 0
																--ROUND(				
																--	(prem.PreReqRev + 0
																--	/ (1 - prem.ProfitFactor)) 
																--	* dbo.fnGetSafeDivisionResult(prem.MedicareCoveredPct,prem.ConversionFactor)
																--,2)   
																--* (1 - cval.StatCompPercent)
														ELSE
															EstimatedPlanBidComponent 
															* (1 - cval.StatCompPercent)
													END
													)
											,
											15)
									ELSE bs.PlanAverageRiskRate
								END
								)
							* (1 - @MSP)* bs.PlanRiskFactor
							) 
			           
					FROM dbo.SavedPlanHeader  sph WITH (NOLOCK)
					INNER JOIN dbo.CalcBenchmarkSummary bs WITH (NOLOCK)
						ON bs.ForecastID = sph.ForecastID
					
					INNER JOIN dbo.LkpProductType cpt WITH (NOLOCK)
						ON sph.PlanTypeID = cpt.ProductTypeID
					CROSS JOIN dbo.PerExtCMSValues cval WITH (NOLOCK)
					LEFT JOIN  --ratebook
					(
						SELECT --Start of temp table ratebook
							mh.MarketID,
							rr.MARegionRate
						FROM dbo.SavedPlanHeader mh WITH (NOLOCK)
						INNER JOIN dbo.LkpExtCMSMARegionHeader rh WITH (NOLOCK)
							ON mh.MARegionID=rh.MARegionID
						INNER JOIN dbo.fnGetCMSMARegionRate(@XForecastID) rr
							ON mh.MARegionID = rr.MARegionID
						WHERE mh.ForecastID = @XForecastID
					) ratebook -- End of temp table ratebook
						ON sph.MarketID = ratebook.MarketID
					LEFT JOIN dbo.SavedPlanDetail spd WITH (NOLOCK)
						ON spd.ForecastID = sph.ForecastID
						AND spd.MARatingOptionID = 1
					WHERE sph.ForecastID = @XForecastID
					)
					
					
				

	BEGIN
        INSERT @Results
        VALUES(@XForecastID, ISNULL(@PlanBenchmark,0))
    END
	RETURN 
END
GO
