SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author:  <PERSON>cha     
-- Create date: 24 Dec 2024
-- Description:  spAppUpdateTargetProfitPMPM

-- STORED PROCS:	spTargetProfitPMPMReprice
--					spAppUpdateSavedTargetInputs

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION		CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 24 Dec 2024  1			Initial version.											Alex Beruscha

-- ----------------------------------------------------------------------------------------------------------------------        

-- =============================================   
CREATE PROCEDURE [dbo].[spAppUpdateTargetProfitPMPM]
    @ForecastID INT,
	@TargetProfitPMPM FLOAT,
    @UserID VARCHAR(7),
	@MessageFromBackend VARCHAR(MAX) OUT,
	@Result BIT OUT
AS
BEGIN
--	BEGIN TRANSACTION;
        BEGIN TRY 
			BEGIN					
				EXEC	[dbo].[spTargetProfitPMPMReprice] @ForecastID, @UserID, @MessageFromBackend OUTPUT, @TargetProfitPMPM
			END
--	COMMIT TRANSACTION;

			IF (@MessageFromBackend = '0')
			  BEGIN
				SET @MessageFromBackend =  'The Target Profit PMPM calculation was successful and the scenario has been repriced.'
				SET @Result=1 
-- Bob K 9/18/21 commented out below temporarily, until 21.12 ready for environments above DEV_Grow
			EXECUTE [dbo].[spAppUpdateSavedTargetInputs] @ForecastID, @TargetProfitPMPM, @UserID, 4     --4 implies update Target Profit PMPM in table
			  END
			ELSE
			  BEGIN
				SET @MessageFromBackend =  @MessageFromBackend
				SET @Result=0 
			  END
  		END TRY
        BEGIN CATCH
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;
			 DECLARE @ErrorException NVARCHAR(4000); 

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

--			ROLLBACK TRANSACTION; 
			SET @MessageFromBackend =  @ErrorMessage
			SET @Result=0 
        END CATCH;  
END;
GO

