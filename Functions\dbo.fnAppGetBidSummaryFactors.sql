SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:	fnAppGetBidSummaryFactors
--
-- CREATOR:			Nate Jacoby
--
-- CREATED DATE:	2010-DEC-03
--
-- DESCRIPTION:		Function responsible for listing Trend/Claim Factors on the 
--						Actuarial Review Bid Summary tab in the MAPD Model
--
-- PARAMETERS:
--  Input  :		@ForecastID       INT
--					,@MARatingOptionID INT
--
--  Output :		PlanYearID					 SMALLINT
--					,ForecastID                  INT
--					,MARatingOptionID            TINYINT
--					,ExperienceAllowed           DECIMAL(30, 20)
--					,ProjectedAllowed            DECIMAL(30, 20)
--					,CurrentAllowedBeforeDEAdj   DECIMAL(30, 20)
--					,ProjectedAllowedBeforeDEAdj DECIMAL(30, 20)
--					,TotalE2CClaimFactorUnits    DECIMAL(28, 22)
--					,TotalC2BClaimFactorUnits    DECIMAL(28, 22)
--					,TotalE2CClaimFactorAllowed  DECIMAL(28, 22)
--					,TotalC2BClaimFactorAllowed  DECIMAL(28, 22)
--
-- TABLES : 
--	Read :			CalcBenefitProjection
--					CalcPlanExperienceByBenefitCat
--					CalcProjectionFactors
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			vwLkpPartBOnlyCoveredPercent
--					SavedPlanHeader
--
-- FUNCTIONS:		fnGetBidYear
--					fnGetCredibilityFactor
--					fnGetINOONProjectionFactors
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2010-DEC-02		1			Initial Version														Nate Jacoby
-- 2011-FEB-07		2			Included Current & Projected Allowed amounts before DE Adj			Nate Jacoby
-- 2011-MAR-03		3			Updated Decimal Places for Factors, ensure fn wont error out		Nate Jacoby
-- 2011-JUN-14		4			Changed PlanYearId to return SMALLINT instead of INT				Bobby Jaegers
-- 2012-JAN-18		5			Added code to get allowed for partB only plans						Tim Gao
-- 2012-MAY-17		6			Updated Alloweds for Part B Only (multiplies SNF by .05)			Craig Wright
-- 2013-MAR-13		7			Switched order of proj factors to back into DualPopFactor			Trevor Mahoney
-- 2013-MAR-26		8			Added code to get trend and claim factor from different source		Tim Gao
-- 2013-MAY-07		9			Removed Trevor's changes											Mike Deren
-- 2014-MAR-10		10			Modified for SQS (Projected Allowed as displayed on WS2)			Mike Deren
-- 2014-MAY-05		11			Modified for 2015 Part B Only Logic, now automated					Nick Koesters
-- 2014-MAY-16		12			Modified for Projected Allowed PMPM for Part B Only Logic
--									to correctly bring through the 5% Retained SNF. 
--									Previous year logic wrongfully brought in 0% retained			Nick Koesters 
-- 2022-MAY-14		13			MAAUI migration; replaced input factor @PlanIndex with @ForecastID;
--								; for table CalcBenefitProjection, replaced reference from
--								PlanIndex to ForecastID; removed nested queries; removed 
--								@IsPartBOnly														Aleksandar Dimitrijevic
-- 2022-DEC-16		14			4119454 Deployment objects from Dev-Grow							Aleksandar Dimitrijevic
-- 2023-FEB-10		15			Remove Dual Population adjustment and legacy trend fields			Jake Lewis
-- 2023-AUG-04		16			Added Nolock, internal parameter, avoid execution of same 
--								function multiple times												Sheetal Patil
-- 2024-OCT-07		17			Cost Share Basis: add handling for IsIncludeInCostShareBasis field
--									in the base data tables											Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnAppGetBidSummaryFactors]
    (@ForecastID       INT
    ,@MARatingOptionID INT)

RETURNS @RESULTS TABLE
    (PlanYearID                  SMALLINT
    ,ForecastID                  INT
    ,MARatingOptionID            TINYINT
    ,ExperienceAllowed           DECIMAL(30, 20)
    ,ProjectedAllowed            DECIMAL(30, 20)
    ,CurrentAllowedBeforeDEAdj   DECIMAL(30, 20)
    ,ProjectedAllowedBeforeDEAdj DECIMAL(30, 20)
    ,TotalE2CClaimFactorUnits    DECIMAL(28, 22)
    ,TotalC2BClaimFactorUnits    DECIMAL(28, 22)
    ,TotalE2CClaimFactorAllowed  DECIMAL(28, 22)
    ,TotalC2BClaimFactorAllowed  DECIMAL(28, 22))

AS

    BEGIN

        DECLARE @XForecastID       INT = @ForecastID
               ,@XMARatingOptionID INT = @MARatingOptionID
               ,@PlanYearID        SMALLINT;
        SET @PlanYearID = dbo.fnGetBidYear ();
        DECLARE @Credibility FLOAT = dbo.fnGetCredibilityFactor (@XForecastID);

        ------------------------------------------------------------------------------------------------------------------------------
        -- Create Temp Tables                                                                                                        --
        ------------------------------------------------------------------------------------------------------------------------------
        DECLARE @TrendAndClaimFactorRollUp TABLE
            (PlanYearID                 SMALLINT
            ,ForecastID                 INT
            ,MARatingOptionID           TINYINT
            ,TotalE2CClaimFactorUnits   DECIMAL(28, 22)
            ,TotalC2BClaimFactorUnits   DECIMAL(28, 22)
            ,TotalE2CClaimFactorAllowed DECIMAL(28, 22)
            ,TotalC2BClaimFactorAllowed DECIMAL(28, 22)
            ,Credibility                DECIMAL(28, 22));

        DECLARE @ExperienceAllowed TABLE
            (PlanYearID        SMALLINT
            ,ForecastID        INT
            ,MARatingOptionID  TINYINT
            ,ExperienceAllowed DECIMAL(30, 20));

        DECLARE @ProjectedAllowed TABLE
            (PlanYearID       SMALLINT
            ,ForecastID       INT
            ,MARatingOptionID INT
            ,ProjectedAllowed DECIMAL(30, 20));

        ------------------------------------------------------------------------------------------------------------------------------
        -- Gets Final Trend/Claim Factor and inserts into temp table @TrendAndClaimFactorRollUp                                     --
        ------------------------------------------------------------------------------------------------------------------------------

        --Gets Trend/Claim Factor * Allowed for all Benefit Categories
        DECLARE @BB TABLE
            (ForecastID               INT
            ,ClaimForecastID          INT
            ,MARatingOptionID         INT
            ,BenefitCategoryID        INT
            ,E2CINClaimFactorUnits    DECIMAL(24, 15)
            ,C2BINClaimFactorUnits    DECIMAL(24, 15)
            ,E2CINClaimFactorAllowed  DECIMAL(24, 15)
            ,C2BINClaimFactorAllowed  DECIMAL(24, 15)
            ,E2COONClaimFactorUnits   DECIMAL(24, 15)
            ,C2BOONClaimFactorUnits   DECIMAL(24, 15)
            ,E2COONClaimFactorAllowed DECIMAL(24, 15)
            ,C2BOONClaimFactorAllowed DECIMAL(24, 15));

        INSERT INTO @BB
        SELECT  ForecastID
               ,ClaimForecastID
               ,MARatingOptionID
               ,BenefitCategoryID
               ,E2CINClaimFactorUnits
               ,C2BINClaimFactorUnits
               ,E2CINClaimFactorAllowed
               ,C2BINClaimFactorAllowed
               ,E2COONClaimFactorUnits
               ,C2BOONClaimFactorUnits
               ,E2COONClaimFactorAllowed
               ,C2BOONClaimFactorAllowed
        FROM    dbo.fnGetINOONProjectionFactors (@ForecastID);

        DECLARE @TCF TABLE
            (PlanYearID                    SMALLINT
            ,ForecastID                    INT
            ,MARatingOptionID              TINYINT
            ,INAllowed                     FLOAT
            ,OONAllowed                    FLOAT
            ,TotalE2CINClaimFactorUnits    FLOAT
            ,TotalC2BINClaimFactorUnits    FLOAT
            ,TotalE2CINClaimFactorAllowed  FLOAT
            ,TotalC2BINClaimFactorAllowed  FLOAT
            ,TotalE2COONClaimFactorUnits   FLOAT
            ,TotalC2BOONClaimFactorUnits   FLOAT
            ,TotalE2COONClaimFactorAllowed FLOAT
            ,TotalC2BOONClaimFactorAllowed FLOAT);

        INSERT INTO @TCF
        SELECT      A.PlanYearID
                   ,A.ForecastID
                   ,A.MARatingOptionID
                   ,SUM (A.INAllowed) AS INAllowed
                   ,SUM (A.OONAllowed) AS OONAllowed
                   ,TotalE2CINClaimFactorUnits = SUM ((BB.E2CINClaimFactorUnits * A.INAllowed))
                   ,TotalC2BINClaimFactorUnits = SUM ((BB.C2BINClaimFactorUnits * A.INAllowed))
                   ,TotalE2CINClaimFactorAllowed = SUM ((BB.E2CINClaimFactorAllowed * A.INAllowed))
                   ,TotalC2BINClaimFactorAllowed = SUM ((BB.C2BINClaimFactorAllowed * A.INAllowed))
                   ,TotalE2COONClaimFactorUnits = SUM ((BB.E2COONClaimFactorUnits * A.OONAllowed))
                   ,TotalC2BOONClaimFactorUnits = SUM ((BB.C2BOONClaimFactorUnits * A.OONAllowed))
                   ,TotalE2COONClaimFactorAllowed = SUM ((BB.E2COONClaimFactorAllowed * A.OONAllowed))
                   ,TotalC2BOONClaimFactorAllowed = SUM ((BB.C2BOONClaimFactorAllowed * A.OONAllowed))
        FROM        dbo.CalcPlanExperienceByBenefitCat A (NOLOCK)
       INNER JOIN   dbo.CalcProjectionFactors B (NOLOCK)
               ON A.PlanYearID = B.PlanYearID
                  AND   A.ForecastID = B.ForecastID
                  AND   A.MARatingOptionID = B.MARatingOptionID
                  AND   A.BenefitCategoryID = B.BenefitCategoryID
       INNER JOIN   @BB BB -- new data source ---Tim
               ON B.MARatingOptionID = BB.MARatingOptionID
                  AND   B.BenefitCategoryID = BB.BenefitCategoryID
        WHERE       A.ForecastID = @XForecastID
                    AND A.DualEligibleTypeID = 2
        GROUP BY    A.PlanYearID
                   ,A.ForecastID
                   ,A.MARatingOptionID;


        --Gets Trend/Claim Factor (Rolls up OON and INN)
        DECLARE @TCFR TABLE
            (PlanYearID                 SMALLINT
            ,ForecastID                 INT
            ,MARatingOptionID           TINYINT
            ,TotalE2CClaimFactorUnits   FLOAT
            ,TotalC2BClaimFactorUnits   FLOAT
            ,TotalE2CClaimFactorAllowed FLOAT
            ,TotalC2BClaimFactorAllowed FLOAT);

        INSERT INTO @TCFR
        SELECT      PlanYearID
                   ,ForecastID
                   ,MARatingOptionID
                   ,TotalE2CClaimFactorUnits = dbo.fnGetSafeDivisionResultReturnOne (
                                               SUM (TotalE2CINClaimFactorUnits) + SUM (TotalE2COONClaimFactorUnits)
                                              ,SUM (INAllowed) + SUM (OONAllowed))
                   ,TotalC2BClaimFactorUnits = dbo.fnGetSafeDivisionResultReturnOne (
                                               SUM (TotalC2BINClaimFactorUnits) + SUM (TotalC2BOONClaimFactorUnits)
                                              ,SUM (INAllowed) + SUM (OONAllowed))
                   ,TotalE2CClaimFactorAllowed = dbo.fnGetSafeDivisionResultReturnOne (
                                                 SUM (TotalE2CINClaimFactorAllowed) + SUM (TotalE2COONClaimFactorAllowed)
                                                ,SUM (INAllowed) + SUM (OONAllowed))
                   ,TotalC2BClaimFactorAllowed = dbo.fnGetSafeDivisionResultReturnOne (
                                                 SUM (TotalC2BINClaimFactorAllowed) + SUM (TotalC2BOONClaimFactorAllowed)
                                                ,SUM (INAllowed) + SUM (OONAllowed))
        FROM        @TCF C
        GROUP BY    PlanYearID
                   ,ForecastID
                   ,MARatingOptionID;

        -------------------------------------------------------------------------------------------------------------------------
        INSERT INTO @TrendAndClaimFactorRollUp
        SELECT      D.PlanYearID
                   ,D.ForecastID
                   ,MARatingOptionID
                   ,TotalE2CClaimFactorUnits = MAX (TotalE2CClaimFactorUnits)
                   ,TotalC2BClaimFactorUnits = MAX (TotalC2BClaimFactorUnits)
                   ,TotalE2CClaimFactorAllowed = MAX (TotalE2CClaimFactorAllowed)
                   ,TotalC2BClaimFactorAllowed = MAX (TotalC2BClaimFactorAllowed)
                   ,Credibility = CASE MARatingOptionID WHEN 1 THEN @Credibility ELSE (1 - @Credibility) END
        FROM        @TCFR D
        WHERE       D.PlanYearID = @PlanYearID
                    AND D.ForecastID = @ForecastID
        GROUP BY    D.PlanYearID
                   ,D.ForecastID
                   ,MARatingOptionID;

        ------------------------------------------------------------------------------------------------------------------------------
        -- Inserts the ExperienceAllowed amounts into the temptable @ExperienceAllowed                                              --
        ------------------------------------------------------------------------------------------------------------------------------        

        -- cpbc experience
        DECLARE @CPBCe TABLE
            (PlanYearID         SMALLINT
            ,ForecastID         INT
            ,MARatingOptionID   TINYINT
            ,DualEligibleTypeID TINYINT
            ,INAllowed          FLOAT
            ,OONAllowed         FLOAT);

        INSERT INTO @CPBCe
        SELECT      cat.PlanYearID
                   ,cat.ForecastID
                   ,cat.MARatingOptionID
                   ,cat.DualEligibleTypeID
                   ,SUM (cat.INAllowed) AS INAllowed
                   ,SUM (cat.OONAllowed) AS OONAllowed
        FROM        dbo.CalcPlanExperienceByBenefitCat cat (NOLOCK)
       INNER JOIN   dbo.vwLkpPartBOnlyCoveredPercent vw --To handle automated Part B Only Covered Percents for Allowed values
               ON vw.BenefitCategoryID = cat.BenefitCategoryID
        WHERE       cat.ForecastID = @XForecastID
                    AND cat.DualEligibleTypeID = 2
        GROUP BY    cat.PlanYearID
                   ,cat.ForecastID
                   ,cat.MARatingOptionID
                   ,cat.DualEligibleTypeID;

        --
        DECLARE @CPBCF TABLE
            (PlanYearID        SMALLINT
            ,ForecastID        INT
            ,MARatingOptionID  TINYINT
            ,ExperienceAllowed FLOAT);

        INSERT INTO @CPBCF
        SELECT      cpbc.PlanYearID
                   ,ForecastID
                   ,MARatingOptionID = 3
                   ,ExperienceAllowed = SUM (INAllowed + OONAllowed)
                                        * CASE MARatingOptionID WHEN 1 THEN @Credibility ELSE (1 - @Credibility) END
        FROM        @CPBCe cpbc
        GROUP BY    cpbc.PlanYearID
                   ,cpbc.ForecastID
                   ,cpbc.MARatingOptionID;
        ---------------------------------------------------------------------------------------------------------------
        INSERT INTO @ExperienceAllowed
        SELECT      cpbc.PlanYearID
                   ,ForecastID
                   ,MARatingOptionID
                   ,ExperienceAllowed = SUM (INAllowed + OONAllowed)
        FROM        @CPBCe cpbc
        GROUP BY    cpbc.PlanYearID
                   ,cpbc.ForecastID
                   ,cpbc.MARatingOptionID

        UNION ALL

        SELECT  PLanYearID = MAX (PlanYearID)
               ,ForecastID = MAX (ForecastID)
               ,MARatingOptionID = MAX (MARatingOptionID)
               ,ExperienceAllowed = SUM (ExperienceAllowed)
        FROM    @CPBCF;

        ------------------------------------------------------------------------------------------------------------------------------
        -- Inserts the ProjectedAllowed amounts into the temptable @ProjectedAllowed                                              --
        ------------------------------------------------------------------------------------------------------------------------------         

        -- cpbc projected
        DECLARE @CPBCp TABLE
            (PlanYearID         SMALLINT
            ,ForecastID         INT
            ,MARatingOptionID   TINYINT
            ,DualEligibleTypeID TINYINT
            ,INAllowed          FLOAT
            ,OONAllowed         FLOAT);

        INSERT INTO @CPBCp
        SELECT      bp.PlanYearID
                   ,bp.ForecastID
                   ,bp.MARatingOptionID
                   ,bp.DualEligibleTypeID
                   ,INAllowed = ISNULL (bp.INAllowed, 0)
                   ,OONAllowed = ISNULL (bp.OONAllowed, 0)
        FROM        dbo.CalcBenefitProjection bp (NOLOCK)
       INNER JOIN   dbo.vwLkpPartBOnlyCoveredPercent vw --To handle automated Part B Only Covered Percents for Allowed values
               ON vw.BenefitCategoryID = bp.BenefitCategoryID
        WHERE       bp.ForecastID = @XForecastID
                    AND bp.DualEligibleTypeID = 2;

        --------------------------------------------------------------------------------------------------------------------
        INSERT INTO @ProjectedAllowed
        SELECT      PlanYearID
                   ,ForecastID
                   ,MARatingOptionID
                   ,ProjectedAllowed = SUM (INAllowed + OONAllowed)
        FROM        @CPBCp cpbc
        GROUP BY    cpbc.PlanYearID
                   ,cpbc.ForecastID
                   ,cpbc.MARatingOptionID;


        ------------------------------------------------------------------------------------------------------------------------------
        -- Selects Factors/Trend based on an MARatingOptionID                                                                       --
        ------------------------------------------------------------------------------------------------------------------------------
        IF @XMARatingOptionID IN (1, 2)
            BEGIN

                --MANEXP
                DECLARE @MANEXP TABLE
                    (PlanYearID                 SMALLINT
                    ,ForecastID                 INT
                    ,MARatingOptionID           TINYINT
                    ,ExperienceAllowed          FLOAT
                    ,ProjectedAllowed           FLOAT
                    ,TotalE2CClaimFactorUnits   FLOAT
                    ,TotalC2BClaimFactorUnits   FLOAT
                    ,TotalE2CClaimFactorAllowed FLOAT
                    ,TotalC2BClaimFactorAllowed FLOAT);

                INSERT INTO @MANEXP
                SELECT      A.PlanYearID
                           ,A.ForecastID
                           ,MARatingOptionID = @XMARatingOptionID
                           ,B.ExperienceAllowed
                           ,C.ProjectedAllowed
                           ,TotalE2CClaimFactorUnits
                           ,TotalC2BClaimFactorUnits
                           ,TotalE2CClaimFactorAllowed
                           ,TotalC2BClaimFactorAllowed
                FROM        @TrendAndClaimFactorRollUp A
               INNER JOIN   @ExperienceAllowed B
                       ON A.PlanYearID = B.PlanYearID
                          AND   A.ForecastID = B.ForecastID
                          AND   A.MARatingOptionID = B.MARatingOptionID
               INNER JOIN   @ProjectedAllowed C
                       ON A.PlanYearID = C.PlanYearID
                          AND   A.ForecastID = C.ForecastID
                          AND   A.MARatingOptionID = C.MARatingOptionID
                WHERE       A.MARatingOptionID = @XMARatingOptionID;

                ---------------------------------------------------------------------------------------------------
                INSERT INTO @RESULTS
                SELECT  PlanYearID
                       ,ForecastID
                       ,MARatingOptionID
                       ,ExperienceAllowed
                       ,ProjectedAllowed
                       ,CurrentAllowedBeforeDEAdj = ExperienceAllowed * TotalE2CClaimFactorAllowed
                       ,ProjectedAllowedBeforeDEAdj = ExperienceAllowed * TotalE2CClaimFactorAllowed
                                                      * TotalC2BClaimFactorAllowed
                       ,TotalE2CClaimFactorUnits
                       ,TotalC2BClaimFactorUnits
                       ,TotalE2CClaimFactorAllowed
                       ,TotalC2BClaimFactorAllowed
                FROM    @MANEXP MANEXP;
            END;

        IF @XMARatingOptionID = 3
            BEGIN
                --CredFactors
                DECLARE @CredFactors TABLE
                    (PlanYearID                 SMALLINT
                    ,ForecastID                 INT
                    ,MARatingOptionID           TINYINT
                    ,TotalE2CClaimFactorUnits   FLOAT
                    ,TotalC2BClaimFactorUnits   FLOAT
                    ,TotalE2CClaimFactorAllowed FLOAT
                    ,TotalC2BClaimFactorAllowed FLOAT);

                INSERT INTO @CredFactors
                SELECT      PlanYearID
                           ,ForecastID
                           ,MARatingOptionID = 3
                           ,TotalE2CClaimFactorUnits = SUM (TotalE2CClaimFactorUnits * Credibility)
                           ,TotalC2BClaimFactorUnits = SUM (TotalC2BClaimFactorUnits * Credibility)
                           ,TotalE2CClaimFactorAllowed = SUM (TotalE2CClaimFactorAllowed * Credibility)
                           ,TotalC2BClaimFactorAllowed = SUM (TotalC2BClaimFactorAllowed * Credibility)
                FROM        @TrendAndClaimFactorRollUp
                GROUP BY    PlanYearID
                           ,ForecastID;

                --Combined
                DECLARE @Combined TABLE
                    (PlanYearID                 SMALLINT
                    ,ForecastID                 INT
                    ,MARatingOptionID           TINYINT
                    ,ExperienceAllowed          FLOAT
                    ,ProjectedAllowed           FLOAT
                    ,TotalE2CClaimFactorUnits   FLOAT
                    ,TotalC2BClaimFactorUnits   FLOAT
                    ,TotalE2CClaimFactorAllowed FLOAT
                    ,TotalC2BClaimFactorAllowed FLOAT);

                INSERT INTO @Combined
                SELECT      A.PlanYearID
                           ,A.ForecastID
                           ,MARatingOptionID = 3
                           ,B.ExperienceAllowed
                           ,C.ProjectedAllowed
                           ,TotalE2CClaimFactorUnits
                           ,TotalC2BClaimFactorUnits
                           ,TotalE2CClaimFactorAllowed
                           ,TotalC2BClaimFactorAllowed
                FROM        @CredFactors A
               INNER JOIN   @ExperienceAllowed B
                       ON A.PlanYearID = B.PlanYearID
                          AND   A.ForecastID = B.ForecastID
                          AND   A.MARatingOptionID = B.MARatingOptionID
               INNER JOIN   @ProjectedAllowed C
                       ON A.PlanYearID = C.PlanYearID
                          AND   A.ForecastID = C.ForecastID
                          AND   A.MARatingOptionID = C.MARatingOptionID;

                --------------------------------------------------------------------------------------------------
                INSERT INTO @RESULTS
                SELECT  PlanYearID
                       ,ForecastID
                       ,MARatingOptionID
                       ,ExperienceAllowed
                       ,ProjectedAllowed
                       ,CurrentAllowedBeforeDEAdj = ExperienceAllowed * TotalE2CClaimFactorAllowed
                       ,ProjectedAllowedBeforeDEAdj = ExperienceAllowed * TotalE2CClaimFactorAllowed
                                                      * TotalC2BClaimFactorAllowed
                       ,TotalE2CClaimFactorUnits
                       ,TotalC2BClaimFactorUnits
                       ,TotalE2CClaimFactorAllowed
                       ,TotalC2BClaimFactorAllowed
                FROM    @Combined;

            END;

        RETURN;

    END;
GO
