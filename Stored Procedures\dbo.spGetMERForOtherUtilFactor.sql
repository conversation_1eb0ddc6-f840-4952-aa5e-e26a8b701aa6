SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spGetMERForOtherUtilFactor
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Mar-08
-- HEADER UPDATED: 2011-Mar-09
--
-- DESCRIPTION: Procedure used to iteratively goal-seek an Other Util Factor value that will result in a desired MER
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@UserID
--		@NewClaimFactor-->This procedure will be run multiple times with this variable varying until the desired MER is hit
--	Output:
--		@NewMER
--
-- TABLES:
--	Read:
--		SavedPlanAssumptions
--      SavedClaimFactorBenefitLevel
--      SavedPlanDetail
--      SavedClaimFactorDetail
--	Write:  
--      SavedClaimFactorDetail
--      SavedPlanAssumptions
--      SavedClaimFactorBenefitLevel
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnAppGetBidSummary
--		fnAppGetClaimForecastFactors
--
-- STORED PROCS:
--		spTargetPremiumReprice
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Mar-08      1			Initial Version                                                     Michael Siekerka
-- 2011-Mar-11		2			Moved the original value storing and repricing to					Joe Casey
--									spTargetOtherUtilForMER
--2019-oct-30		3			Removed 'HUMAD\' to UserID											Chhavi Sinha  
-- ---------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spGetMERForOtherUtilFactor]
(
    @ForecastID INT,
    @UserID CHAR(7),
    @OldProfitPercent DECIMAL(10,8),
	@NewClaimFactor DECIMAL(8,6),
	@NewMER FLOAT OUT
)
AS
BEGIN
    DECLARE @ValidationMessage VARCHAR(MAX)
            
    -----------------------------------------------------------------------------------------------          
    -- ---Update desired claim factor at plan or benefit level-------------------------------------
        UPDATE SavedClaimFactorDetail
        SET ClaimFactor = @NewClaimFactor
        WHERE ClaimForecastID IN
			(
				SELECT DISTINCT fd.ClaimForecastID
				FROM SavedClaimFactorDetail fd
				INNER JOIN SavedPlanDetail spd
					ON fd.ClaimForecastID = spd.ClaimForecastID
				WHERE spd.ForecastID = @ForecastID
			)
			AND ClaimFactorTypeID = 7
			AND ProjectionYearID = 2
        
        UPDATE SavedClaimFactorBenefitLevel
        SET ClaimFactor = @NewClaimFactor
        WHERE ClaimForecastID IN
			(
				SELECT DISTINCT fd.ClaimForecastID
				FROM SavedClaimFactorBenefitLevel fd
				INNER JOIN SavedPlanDetail spd
					ON fd.ClaimForecastID = spd.ClaimForecastID
				WHERE spd.ForecastID = @ForecastID
			)
			AND ClaimFactorTypeID = 7
			AND ProjectionYearID = 2

	-- --------------------------------------------------------------------------------------------
	-- ---Reprice again so we get the new MER without changing the premium-------------------------
        EXECUTE spTargetPremiumReprice @ForecastID, @UserID, @ValidationMessage OUTPUT

        --Make sure there was no problem with repricing then grab new MER
        IF @ValidationMessage = '0'
			BEGIN
				SELECT @NewMER = 1 - (ExpensePMPM + UncollectedPremium + UserFee + Profit) / TotalReqRev FROM fnAppGetBidSummary(@ForecastID)
				UPDATE SavedPlanAssumptions SET ProfitPercent = @OldProfitPercent WHERE ForecastID = @ForecastID
            END
		ELSE
			SELECT @ValidationMessage
END
GO
