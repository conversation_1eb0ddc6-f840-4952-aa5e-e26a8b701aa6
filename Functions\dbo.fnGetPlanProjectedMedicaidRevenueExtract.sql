SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnGetPlanProjectedMedicaidRevenueExtract]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetPlanProjectedMedicaidRevenueExtract
--
-- AUTHOR: Apoor<PERSON> Nasa
--
-- CREATED DATE: 2018-Feb-09
--
-- DESCRIPTION: Designed to extract fields for Medicaid Revenue
--
-- PARAMETERS:
--	Input: @WhereIN
--     
--  Output:
--
-- TABLES: 
--	Read:
--		Saved<PERSON>lanHeader
--      SavedPlanProjectedMedicaidRevenue
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnStringSplit
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION	    CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2018-Feb-09		1			Initial Version							                           Apoorva Nasa
-- 2024-Dec-10		2			Added LastUpdateByID & LastUpdatedDateTime column for Export	   Archana Sahu

----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetPlanProjectedMedicaidRevenueExtract]
    (
    @WhereIN VARCHAR(MAX)=NULL
    ) 
RETURNS @Results TABLE
	(  
    [ForecastID] INT NOT NULL,
	[CPS] CHAR(13) NOT NULL,
	[MedicaidProjectedRevenue] DECIMAL(14,6)NULL,
	[MedicaidProjectedCostBenefitExpense]  DECIMAL(14,6)NULL,
	[MedicaidProjectedCostNonBenefitExpense]  DECIMAL(14,6)NULL,
	[LastUpdateByID] CHAR(7) NOT NULL,
	[LastUpdateDateTime] DATETIME NOT NULL
	
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT  DISTINCT
			S.ForecastID, spi.CPS,
			S.MedicaidProjectedRevenue,
			S.MedicaidProjectedCostBenefitExpense,
			S.MedicaidProjectedCostNonBenefitExpense,
			S.LastUpdateByID, S.LastUpdateDateTime
			FROM SavedPlanProjectedMedicaidRevenue S
			INNER JOIN SavedPlanHeader p ON p.ForecastID=s.ForecastID  
			INNER JOIN dbo.SavedForecastSetup sfs ON S.ForecastID = sfs.ForecastID
			INNER JOIN dbo.SavedPlanInfo spi ON sfs.PlanInfoID = spi.PlanInfoID
	ELSE
		INSERT @Results
			SELECT  DISTINCT
			S.ForecastID, spi.CPS,
			S.MedicaidProjectedRevenue,
			S.MedicaidProjectedCostBenefitExpense,
			S.MedicaidProjectedCostNonBenefitExpense,
			S.LastUpdateByID, S.LastUpdateDateTime
			FROM SavedPlanProjectedMedicaidRevenue S
			INNER JOIN SavedPlanHeader P
			ON p.ForecastID=s.ForecastID
			INNER JOIN dbo.SavedForecastSetup sfs ON S.ForecastID = sfs.ForecastID
			INNER JOIN dbo.SavedPlanInfo spi ON sfs.PlanInfoID = spi.PlanInfoID
			WHERE P.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
RETURN
END
GO
