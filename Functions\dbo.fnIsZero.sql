SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnIsZero
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2007-05-05
--
-- DESCRIPTION: Returns replacement value if primary value is zero.
--
-- PARAMETERS:
--  Input:  @input        value to be checked
--          @replacement  value to be returned if @input is zero
--  Output: None
--
-- RETURNS: 
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION     CHANGES MADE																	DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-May-05		1           Initial version																	Brad Ennis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnIsZero] 
(
    @input FLOAT, 
    @replacement FLOAT
)
    RETURNS FLOAT AS
    BEGIN
		IF (@input = 0)
            RETURN @replacement
        RETURN @input
    END
GO
