SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- $HISTORY    
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-Oct-08      1   Initial Version                                                     Harini Thangadurai 
-- 2024-DEC-06	    2	Adding ForecastID to the SavedMERActAdj Import assumptions table  		    <PERSON>
-- 2025-Feb-28	    3	Clearing SavedMERActAdj table before Importing new MER data                 Abraham <PERSON>bian
-- 2025-Mar-06	    4	Delete logic changed														<PERSON> Ndabian
-- ----------------------------------------------------------------------------------------------------------------------  


CREATE PROCEDURE [dbo].[spAppImportMERFactors]  
(@StageId VARCHAR(100))  
AS  
BEGIN  
    --DECLARE @StageId VARCHAR(100)='ec5b9a90-07bc-4e48-aac9-df189dd20949'  
    DECLARE @jsonData VARCHAR(MAX);  
    DECLARE @UserId VARCHAR(7) ;  

    SELECT @jsonData = JsonData, @UserId = UserId  
    FROM dbo.ImportDataStaging WITH (NOLOCK)  
    WHERE StageId = @StageId;  


    DECLARE @tbl__importData TABLE  
    ( ForecastID INT,      
      ContractPBP VARCHAR(13),     
      Component INT,
      BenefitCategoryNumber INT,
      C2PMERUseMultAdj DECIMAL(24,15),
      C2PMERCostMultAdj DECIMAL(24,15),
      C2PMERUseAddAdj DECIMAL(24,15),
      C2PMERCostAddAdj DECIMAL(24,15),
      UserID CHAR(7),
      LastUpdateDateTime DATETIME
    );  


    INSERT INTO @tbl__importData  
    SELECT  
	        ForecastID,     
            ContractPBP,          
            Component,
            BenefitCategoryNumber,
            C2PMERUseMultAdj,
            C2PMERCostMultAdj,
            C2PMERUseAddAdj,
            C2PMERCostAddAdj,
           @UserId,
            GETDATE()
    FROM  
        OPENJSON(@jsonData, '$.MERFactors')  
        WITH  
        (    ForecastID INT,              
             ContractPBP VARCHAR(13),           
             Component INT,
             BenefitCategoryNumber INT,
             C2PMERUseMultAdj DECIMAL(24,15),
             C2PMERCostMultAdj DECIMAL(24,15),
             C2PMERUseAddAdj DECIMAL(24,15),
             C2PMERCostAddAdj DECIMAL(24,15),
             UserID CHAR(7),
             LastUpdateDateTime DATETIME              
        );    

	 --Delete and insert MER data by Abe 0n 2/28/2025
	   DELETE sma FROM dbo.SavedMERActAdj sma INNER JOIN @tbl__importData imd ON imd.ForecastID=sma.ForecastID  

  MERGE INTO dbo.SavedMERActAdj AS target  
    USING @tbl__importData AS source  
    ON (    
	       target.ForecastID= source.ForecastID AND
	       target.ContractPBP=source.ContractPBP AND
           target.Component  = source.Component AND  
           target.BenefitCategoryNumber = source.BenefitCategoryNumber
       )  
    WHEN MATCHED THEN   
        UPDATE SET 
		           target.ForecastID = source.ForecastID,  
		           target.C2PMERUseMultAdj = source.C2PMERUseMultAdj,  
                   target.C2PMERCostMultAdj = source.C2PMERCostMultAdj,  
                   target.C2PMERUseAddAdj = source.C2PMERUseAddAdj,  
                   target.C2PMERCostAddAdj = source.C2PMERCostAddAdj,
				   target.UserID = source.UserID,
                   target.LastUpdateDateTime = GETDATE() 
    WHEN NOT MATCHED BY TARGET THEN
     INSERT 
        (  ForecastID,
           ContractPBP,
           ContractNumber,
           PlanID,
           SegmentID,
           Component,
           BenefitCategoryNumber,
           C2PMERUseMultAdj,
           C2PMERCostMultAdj,
           C2PMERUseAddAdj,
           C2PMERCostAddAdj,
           UserID,
           LastUpdateDateTime  
        )  VALUES 
  (Source.ForecastID, Source.ContractPBP,LEFT(Source.ContractPBP,5),Substring(Source.ContractPBP,7,3),Right(Source.ContractPBP,3),Source.Component,Source.BenefitCategoryNumber,Source.C2PMERUseMultAdj,Source.C2PMERCostMultAdj,
  Source.C2PMERUseAddAdj,Source.C2PMERCostAddAdj,Source.UserID,GETDATE());

 DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;  
END
GO
