SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetProng1Test]    ******/

-------------------------------------------------------------------------------------------------------------------------


-- FUNCTION NAME: [fnAppGetProng1Test]
--
-- AUTHOR: Nate Jacoby
--
-- CREATED DATE: 2011-Jan-20
-- HEADER UPDATED: 2011-Jan-20
--
-- DESCRIPTION: Displays info used in the Prong 1 test
--  
-- PARAMETERS:
--	Input: 
--		@ForecastID
--
--	Output:
--      @RESULTS
--
-- TABLES: 
--	Read:
--      CalcActEquivByB<PERSON>fitCategory
--      LkpIntBenefitCategory
--      LkpExtCMSBidServiceCategory
--
-- WRITE:
--
-- VIEWS:
--
-- FUNCTIONS:
--		
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
---
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-20		1			Initial Version														Nate Jacoby
-- 2011-Apr-01		2			Updated the formulas for the CMS column to correctly calculate		Craig Wright
-- 2011-Apr-01		3			Removed the Column "ServiceCategory" from the results				Craig Wright
-- 2011-Apr-04		4			Added the 5 CMS Parameters so we can just update them in 1 spot		Craig Wright
-- 2011-May-09		5			Changed DualEligibleTypeID from 2 to 0								Joe Casey
-- 2011-Apr-27		6			Changed DECIMAL to (27,22) from (25,22)							    Craig Wright
-- 2013-Mar-22		7			Updated for 2013 Values												Lindsay Allen
-- 2014-Mar-06		8			Updated for 2015 Bid Season factors (IP,SNF)						Nick Koesters
-- 2015-Apr-08      9			Home Health is no longer being tested, so 'c.' was removed from     Chris McAuley
--								ServiceCategoryCode line, @HHfactor lines are commented out. The 
--								@IPfactor and @SNFfactor variables were also updated for 2016 Bid Season.
--								CMS lines were also commented out pertaining to 'c.'
-- 2016-May-08		10			Updated for 2017 Bid Season factors (IP, SNF)						Mark Freel
-- 2016-Aug-12		11			Updated hardcoded factor values to pull from PerExtProng1Factor		Chris Fleming
-- 2017-Feb-28      12			SNF is no longer being tested, so 'b.' was removed from				Chris Fleming
--								ServiceCategoryCode line, @SNFfactor lines are commented out. 
--								CMS lines were also commented out pertaining to 'b.'
-- 2017-Apr-17		13			Revert prior SNF changes											Chris Fleming
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetProng1Test]
(
    @ForecastID INT
)

    RETURNS @RESULTS TABLE
    (
        ServiceCategory VARCHAR(MAX),
        Humana VARCHAR(10),
        CMS VARCHAR(10),
        PassFail VARCHAR(4)
    ) AS 
BEGIN

    -- ------------------------------------------------------------------------------------------------------------------
    -- Declare and Set CMS factor values
    -- ------------------------------------------------------------------------------------------------------------------
		DECLARE @IPfactor DECIMAL(6,4)
		DECLARE @SNFfactor DECIMAL(6,4)
		--DECLARE @HHfactor DECIMAL(6,4)
		DECLARE @DMEfactor DECIMAL(6,4)
		DECLARE @PartBRxfactor DECIMAL(6,4)

		SET @IPfactor = (SELECT Prong1FactorValue FROM PerExtProng1Factor WHERE Prong1FactorName = 'IP')
		SET @SNFfactor = (SELECT Prong1FactorValue FROM PerExtProng1Factor WHERE Prong1FactorName = 'SNF')
		--SET @HHfactor = 0.15
		SET @DMEfactor = (SELECT Prong1FactorValue FROM PerExtProng1Factor WHERE Prong1FactorName = 'DME')
		SET @PartBRxfactor = (SELECT Prong1FactorValue FROM PerExtProng1Factor WHERE Prong1FactorName = 'PartBRx')

    -- ------------------------------------------------------------------------------------------------------------------
    -- Declare @TEMPTABLE to hold info for Prong 1 test
    -- ------------------------------------------------------------------------------------------------------------------
    DECLARE @TEMPTABLE TABLE
    (
        ForecastID INT, 
        ServiceCategoryCode VARCHAR(2), 
        ServiceCategory VARCHAR(MAX), 
        PlanCSMedCoveredSVCS DECIMAL(27,22),
        AllowedPMPM DECIMAL(27,22),
        FFSAECostShare DECIMAL(27,22)
    )
    -- ------------------------------------------------------------------------------------------------------------------
    -- Get info to use in Prong 1 test
    -- ------------------------------------------------------------------------------------------------------------------
    INSERT INTO @TEMPTABLE
    SELECT 
        act.ForecastID,
        bsc.ServiceCategoryCode,
        bsc.ServiceCategory,
        PlanCSMedCoveredSVCS = SUM(act.CostShare * act.PercentCoveredCostShare),
        AllowedPMPM = SUM(act.Allowed * act.PercentCoveredAllowed),
        FFSAECostShare = Sum(act.Allowed * act.PercentCoveredAllowed * act.FFSActEquivCostShare)
    FROM CalcActEquivByBenefitCategory act
    INNER JOIN LkpIntBenefitCategory bc
       ON act.BenefitCategoryID = bc.BenefitCategoryID
    INNER JOIN LkpExtCMSBidServiceCategory bsc
       ON bc.BidServiceCatID = bsc.BidServiceCategoryID
    WHERE act.ForecastID = @ForecastID
        AND act.DualEligibleTypeID = 0 --Non-DE#
        AND bsc.ServiceCategoryCode IN ('a.','b.','e.','j.')  --'c.' removed because Home Health is no longer being tested
    GROUP BY act.ForecastID, 
        bsc.ServiceCategoryCode, 
        bsc.ServiceCategory


    -- ------------------------------------------------------------------------------------------------------------------
    -- Returns table with Pass or Fail, based on CMS guidelines
    -- ------------------------------------------------------------------------------------------------------------------
    INSERT INTO @RESULTS
    SELECT 
        ServiceCategory,
        Humana = '$' + CAST(CAST(ROUND(PlanCSMedCoveredSVCS,2) AS DECIMAL(8,2)) AS VARCHAR),
        CMS = CASE ServiceCategoryCode 
                    WHEN 'a.' -- InPatient Facility
						THEN '$' + CAST(CAST(ROUND(FFSAECostShare * @IPfactor ,2) AS DECIMAL(8,2)) AS VARCHAR)
					WHEN 'b.' -- SNF
                        THEN '$' + CAST(CAST(ROUND(FFSAECostShare * @SNFfactor ,2) AS DECIMAL(8,2)) AS VARCHAR)
                    --WHEN 'c.' -- Home Health
                    --    THEN '$' + CAST(CAST(ROUND(AllowedPMPM * @HHfactor ,2) AS DECIMAL(8,2)) AS VARCHAR)
					WHEN 'e.' -- DME/Prosthetics/Supplies
                        THEN '$' + CAST(CAST(ROUND(FFSAECostShare * @DMEfactor ,2) AS DECIMAL(8,2)) AS VARCHAR)
					WHEN 'j.' -- Part B Rx
                        THEN '$' + CAST(CAST(ROUND(FFSAECostShare * @PartBRxfactor ,2) AS DECIMAL(8,2)) AS VARCHAR)
				END,	                                    
        PassFail = CASE ServiceCategoryCode
                        WHEN 'a.'
                            THEN
                                CASE
                                WHEN PlanCSMedCoveredSVCS > FFSAECostShare * @IPfactor
                                    THEN 'F'
                                ELSE 'P' END

                        WHEN 'b.'
                            THEN 
                                CASE
                                WHEN PlanCSMedCoveredSVCS > FFSAECostShare * @SNFfactor
                                    THEN 'F'
                                ELSE 'P' END
                        --WHEN 'c.'
                        --    THEN 
                        --        CASE
                        --        WHEN PlanCSMedCoveredSVCS > AllowedPMPM * @HHfactor
                        --            THEN 'F'
                        --        ELSE 'P' END
                        WHEN 'e.'
                            THEN 
                                CASE
                                WHEN PlanCSMedCoveredSVCS > FFSAECostShare * @DMEfactor
                                    THEN 'F'
                                ELSE 'P' END
                        ELSE
                                CASE
                                WHEN PlanCSMedCoveredSVCS > FFSAECostShare * @PartBRxfactor
                                    THEN 'F'
                                ELSE 'P' END                    
                        END                    
    FROM @TEMPTABLE
    ORDER BY ServiceCategoryCode
RETURN
END
GO
