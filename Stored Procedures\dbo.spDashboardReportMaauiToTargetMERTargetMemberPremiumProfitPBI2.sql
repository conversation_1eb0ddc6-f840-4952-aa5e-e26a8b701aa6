SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- PROCEDURE NAME: spDashboardReportMaauiToTargetMERTargetMemberPremiumProfitPBI2

-- DESCRIPTION: This SP returns extract for "'Target Profit %', 'Target MER' and 'Target Member Premium'" user action from AppLogs table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      [dbo].[DashboardReportAppLogs]
--		
-- Example 
-- Exec [dbo].[spDashboardReportMaauiToTargetMERTargetMemberPremiumProfitPBI2] 21434289, '2023-09-25 11:51:14.820'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-24			1			Initial Version                                                                         Sheetal Patil
-- 2024-Feb-20			2			Changed "Message" column logic                                                          Sheetal Patil
-- 2024-Jun-24          3           Replacing /Scenario with /BatchPlanLevelMain based on the new path of batch Page        Chaitanya Durga
---------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROC [dbo].[spDashboardReportMaauiToTargetMERTargetMemberPremiumProfitPBI2]
 (@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


 DECLARE @XLastSuccessfullRunLogid  INT = @LastSuccessfullRunLogid

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTemp')) IS NOT NULL DROP TABLE #AppLogTemp;

SELECT 
 [LogID]
 ,[Date]
 ,[User]
 ,[Message]
 ,characterEvenCount
 ,btwType
 ,dbo.fnRemoveBetweenCharacters(plans,'|',':') AS plans
 INTO #AppLogTemp
  FROM(
 SELECT
	   [LogID]
      ,[Date]
      ,[User]
	  ,CASE	
	  	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
	    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	   ELSE [Message] END AS [Message]
	  --,LTRIM(LEFT([Message], CHARINDEX('[',[t9].[Message])-1) + RIGHT([t9].[Message],LEN(t9.[Message]) - CHARINDEX(']',[t9].[Message]))) [Message]
	  ,characterEvenCount
	  ,FormattedMesssage
	  ,btwType
	  ,plans
 FROM
(SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
	  ,characterEvenCount,FormattedMesssage
	  ,btwType
	  ,plans+':' AS plans
	  
FROM (
SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
      WHEN CHARINDEX('{',FormattedMesssage) = 0 THEN '{"emptyData":""}'
       ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (
SELECT REPLACE(REPLACE(RIGHT(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END, 
DATALENGTH(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END) -
CHARINDEX('{', CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END),0) < 5 
THEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END),0) 
ELSE 0 END AS  DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (
SELECT [LogID]
	  ,[Date]
      ,[User]
      ,[Message]
FROM [dbo].[DashboardReportAppLogs] WITH (NOLOCK) 
WHERE 
 [LogID] > @XLastSuccessfullRunLogid
AND ([Message] LIKE '%Finished executing POST: BatchPlanLevelMain/getBTWDataforPOC%' OR [Message] LIKE '%Finished executing POST: BatchPlanLevelMain/BTWUpdateTargets%'
OR [Message] LIKE '%Executing POST: BatchPlanLevelMain/getBTWDataforPOC%' OR [Message] LIKE '%Executing POST: BatchPlanLevelMain/BTWUpdateTargets%'
)) AS t1 ) AS t2 ) AS t3) AS t4
CROSS APPLY 
OPENJSON(FormattedMesssage)
WITH
(
btwType INT '$.btwType'
,plans NVARCHAR(MAX) '$.premiumData') t5
) AS t9) AS t10


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction1')) IS NOT NULL DROP TABLE #AppLogTempAction1;


SELECT 
CASE WHEN btwType=1 THEN 'Target Profit %'
	 WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END AS [UserActions],
UserId AS [UserID]
,RunDate AS [Run Date]
,CASE WHEN btwType=1 THEN 'Target Profit %'
	   WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END  AS [Type]
,plans AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0  
       ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
,ExecutionTime 
,characterEvenCount
,0 AS ErrorCount
, ' ' AS [ErrorMessage]
INTO #AppLogTempAction1
FROM (SELECT a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
,a.btwType
,a.plans
,a.characterEvenCount
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) 
				FROM #AppLogTemp app WITH (NOLOCK) 
				WHERE a.logid < app.LogID 
				AND app.[Message] LIKE '%Finished executing POST: BatchPlanLevelMain/getBTWDataforPOC%' AND a.[User] = app.[User])
				AND a.[User] = ap.[User]
				AND ap.[Message] LIKE '%Finished executing POST: BatchPlanLevelMain/getBTWDataforPOC%'
				WHERE (a.[Message] LIKE 'Executing POST: BatchPlanLevelMain/getBTWDataforPOC%' AND a.[Message] LIKE '%btwajaxdata%' )
)t01

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction2')) IS NOT NULL DROP TABLE #AppLogTempAction2;


SELECT 
CASE WHEN btwType=1 THEN 'Target Profit %'
	 WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END AS [UserActions],
UserId AS [UserID]
, RunDate AS [Run Date]
, CASE WHEN btwType=1 THEN 'Target Profit %'
	   WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END  AS [Type]
, plans AS [Plans]
, CASE WHEN DATALENGTH(plans)=0 THEN 0  
       ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction2
FROM (
SELECT
a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
,a.btwType
,a.plans
,a.characterEvenCount
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON ap.LogID < a.logid
AND ap.LogID = (SELECT MAX(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE app.LogID < a.logid 
				AND app.[Message] LIKE '%Finished executing POST: BatchPlanLevelMain/BTWUpdateTargets%' AND a.[User] = app.[User])
				AND a.[User] = ap.[User]
				AND ap.[Message] LIKE '%Finished executing POST: BatchPlanLevelMain/BTWUpdateTargets%'
				WHERE (a.[Message] LIKE 'Executing POST: BatchPlanLevelMain/getBTWDataforPOC%' AND a.[Message] LIKE '%btwajaxdata%')
) AS t01

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction3')) IS NOT NULL DROP TABLE #AppLogTempAction3;

SELECT 
CASE WHEN btwType=1 THEN 'Target Profit %'
	 WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END AS [UserActions],
UserId AS [UserID]
, RunDate AS [Run Date]
, CASE WHEN btwType=1 THEN 'Target Profit %'
	   WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END  AS [Type]
, plans AS [Plans]
, CASE WHEN DATALENGTH(plans)=0 THEN 0  
       ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction3
FROM (
SELECT 
a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
,a.btwType
,a.plans
,a.characterEvenCount
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON ap.LogID < a.logid
AND ap.LogID = (SELECT MAX(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE app.LogID < a.logid AND 
app.[Message] LIKE '%Finished executing POST: BatchPlanLevelMain/getBTWDataforPOC%' AND a.[User] = app.[User])
				AND a.[User] = ap.[User]
				AND ap.[Message] LIKE '%Finished executing POST: BatchPlanLevelMain/getBTWDataforPOC%'
				WHERE (a.[Message] LIKE 'Executing POST: BatchPlanLevelMain/getBTWDataforPOC%' AND a.[Message] LIKE '%btwajaxdata%')
) AS t01


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction4')) IS NOT NULL DROP TABLE #AppLogTempAction4;

SELECT 
CASE WHEN btwType=1 THEN 'Target Profit %'
	 WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END AS [UserActions],
UserId AS [UserID]
, RunDate AS [Run Date]
, CASE WHEN btwType=1 THEN 'Target Profit %'
	   WHEN btwType=2 THEN 'Target MER'
	   ELSE 'Target Member Premium' END  AS [Type]
, plans AS [Plans]
, CASE WHEN DATALENGTH(plans)=0 THEN 0  
       ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction4
FROM (
SELECT
a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,0 AS ExecutionTime
,a.btwType,
SUBSTRING(ap.plans,1,(LEN(ap.plans)-1)) AS plans
,a.characterEvenCount
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON ap.LogID < a.logid
AND ap.LogID = (SELECT MAX(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE app.LogID < a.logid 
				AND app.[Message] LIKE 'Executing POST: BatchPlanLevelMain/BTWUpdateTargets%' AND a.[User] = app.[User])
				AND a.[User] = ap.[User]
				AND ap.[Message] LIKE 'Executing POST: BatchPlanLevelMain/BTWUpdateTargets%'
				WHERE (a.[Message] LIKE 'Executing POST: BatchPlanLevelMain/getBTWDataforPOC%' AND a.[Message] LIKE '%btwajaxdata%')
) AS t01


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction5')) IS NOT NULL DROP TABLE #AppLogTempAction5;


SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],SUM([ExecutionTime]) AS [ExecutionTime] ,[characterEvenCount],[ErrorCount],[ErrorMessage] 
INTO #AppLogTempAction5
FROM 
(
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[ErrorMessage] FROM #AppLogTempAction1 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans] ,[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[ErrorMessage] FROM #AppLogTempAction2 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans] ,[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[ErrorMessage] FROM #AppLogTempAction3 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans] ,[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[ErrorMessage] FROM #AppLogTempAction4 WITH (NOLOCK)
) t
GROUP BY [UserActions],[UserID],[Run Date],[Type],[Plans] ,[Plan Count],[characterEvenCount],[ErrorCount],[ErrorMessage]


SELECT DISTINCT A.UserActions
,[A].[UserID]
,A.[Run Date]
,A.[Type]
,A.[Plans]
,SUM(ISNULL([Plan Count],0)) [Plan Count]
,SUM([ExecutionTime]) [ExecutionTime]
,SUM([characterEvenCount]) [characterEvenCount]
,SUM([ErrorCount]) [ErrorCount]
,[RunEndDate]
,[ErrorMessage]
FROM (
SELECT 
[UserActions]
,[UserID]
,[Run Date]
,[Type]
--,[Plans] 
,STUFF(
(SELECT ', ' + CONVERT(VARCHAR(4000), t1.Plans, 120)
FROM #AppLogTempAction5 t1
WHERE t1.UserID = t2.UserID AND t1.UserActions = t2.UserActions AND t1.[Run Date] = t2.[Run Date]
FOR XML PATH (''))
, 1, 1, '')  AS Plans
,[Plan Count]
,[ExecutionTime]
,[characterEvenCount]
,[ErrorCount]
,[Run Date] AS [RunEndDate]
,[ErrorMessage]
FROM #AppLogTempAction5 t2) A
GROUP BY [UserActions],[UserID],[Run Date],[Type],[Plans],[RunEndDate],[ErrorMessage]

 
 END
GO
