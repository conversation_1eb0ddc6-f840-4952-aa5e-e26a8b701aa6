SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetBPTPlanWS7Sec2
--
-- AUTHOR: <PERSON>, Brian <PERSON>iss
--
-- CREATED DATE: 2008-May-09
-- UPDATED HEADER: 2010-Oct-01
--
-- DESCRIPTION: Function returns values for WKSH7
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--
-- RETURNS: Table
--
-- TABLES: 
--	Read:
--        		SavedPlanOptionalPackageDetail
--        		PerIntOptionalPackageHeader 
--        		PerIntOptionalPackageDetail 
--        		LkpExtCMSBidServiceCategory
--              LkpIntMnRUtilizationType
--
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		CHANGES MADE						                            DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-May-09		1			Initial Version							                    Amy Rice, Brian Theiss
-- 2009-Mar-17      1           Data types                                                  Sandy Ellis
-- 2009-Apr-16      1           Data types of AllowedUtilizationPer100,                     Lawrence Choi                   
--                                  EnrolleeCostShareUtilization             
--                                  and EnrolleeAverageCostShare to Decimal (14,2)
-- 2009-Apr-16      1           AllowedUtilizationTypeID (TINYINT) is changed               Lawrence Choi
--                                  to AllowedUtilizationType VARCHAR(2)  
-- 2009-Apr-16      1           Join to LkpIntMnRUtilizationType to                         Lawrence Choi
--                                  obtain AllowedUtilizationType                    
-- 2010-Oct-01      2           Altered to fit 2012 format                                  Nate Jacoby
-- 2011-Jan-18      3           Renamed from fnAppGetBPTPlanWS7Sec2 and revised code        Michael Siekerka
-- 2011-Feb-04      4           Added PackageIndex to input to return only details of       Michael Siekerka
--                                  specified package
-- 2011-Feb-09      5           Changed ProjMM type from INT to DECIMAL and increased       Michael Siekerka
--                                  some VARCHAR's to MAX 
-- 2019-Feb-04		6			PackageIndex and PlanPackageID data type change				Keith Galloway
-------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS7Sec2] 
(
    @ForecastID INT,
    @PackageIndex INT
)
RETURNS @Results TABLE 
(   
    PlanYearID SMALLINT,
    ForecastID INT,       
    PlanPackageID TINYINT,
    PackageIndex INT,
    [Name] VARCHAR(MAX),
    [Description] VARCHAR(MAX),
    TotalExpense DECIMAL(14,2),
    TotalGainLoss  DECIMAL(14,2),
    TotalProjectedMemberMonths DECIMAL(20, 6),
    ServiceCategory VARCHAR(50),    
    PricingComponentDescription VARCHAR(MAX),
    AllowedUtilizationType VARCHAR(2),
    AllowedUtilzationPer1000 DECIMAL(14,2),    
    AllowedAverageCost DECIMAL(14,2),
    MeasurementUnitCode VARCHAR(4),
    EnrolleeCostShareUtilization DECIMAL(14,2),
    EnrolleeAverageCostShare DECIMAL(14,2),
    AllowedPMPM DECIMAL(14,6),
    EnrolleePMPM DECIMAL(14,6),
    Expense  VARCHAR(10),
    GainLossMargin VARCHAR(10),
    Premium VARCHAR(10),
    ProjectedMemberMonths VARCHAR(10)
)
AS
BEGIN  
    DECLARE @PlanYearID SMALLINT
    SET @PlanYearID = dbo.fnGetBidYear()
                 
    INSERT @Results
        SELECT 
            @PlanYearID,
            @ForecastID,        
            s.PlanPackageID,
            s.PackageIndex,
            ph.Name,
            ph.Description,
            TotalExpense,
            TotalGainLoss,
            TotalProjectedMemberMonths,
            ServiceCategory,    
            pd.PricingComponentDescription,
            pd.MeasurementUnitCode AS AllowedUtilizationType,
            pd.AllowedUtilzationPer1000,    
            AllowedAverageCost,
            MeasurementUnitCode,
            EnrolleeCostShareUtilization,
            EnrolleeAverageCostShare,
            AllowedPMPM = pd.AllowedUtilzationPer1000 * pd.AllowedAverageCost / 12000,
            EnrolleePMPM = 
                CASE WHEN pd.MeasurementUnitCode = 'Coin' THEN pd.EnrolleeCostShareUtilization * pd.EnrolleeAverageCostShare
                    ELSE pd.EnrolleeCostShareUtilization * pd.EnrolleeAverageCostShare / 12000 
                END,
            Expense = 'n/a', 
            GainLossMargin = 'n/a', 
            Premium = 'n/a', 
            ProjectedMemberMonths = 'n/a'
        FROM SavedPlanOptionalPackageDetail s
        INNER JOIN PerIntOptionalPackageHeader ph 
            ON ph.PackageIndex = s.PackageIndex
            AND ph.IsEnabled = 1
        INNER JOIN PerIntOptionalPackageDetail pd
            ON ph.PackageIndex = pd.PackageIndex
        INNER JOIN LkpExtCMSBidServiceCategory lkp
            ON lkp.BidServiceCategoryID = pd.BidServiceCategoryID
            AND lkp.PlanYearID = pd.PlanYearID
        WHERE
            s.IsHidden = 0
            AND pd.IsHidden = 0
            AND s.ForecastID = @ForecastID
            AND s.PackageIndex = @PackageIndex
        ORDER BY s.PlanPackageID
    RETURN
END

GO
