SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO





-- ----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: spGetMAReport    
--    
-- AUTHOR: <PERSON><PERSON><PERSON>      
--    
-- CREATED DATE: 2009-Sep-12    
-- HEADER UPDATED: 2016-Nov-18    
--    
-- DESCRIPTION: Procedure returns completed MA Plan and Benefit summary reports to miscellanious callers    
--    
-- PARAMETERS:    
-- Input:    
--      @PlanYearID     - Required    
--      @SourceID       - Required    
--      @ReportLevelID  - Required 
--      @GeoFilterID    - Optional (used only when SourceID IN (3, 4))  
--      @PlanTypeID     - Obsolete, leaving param so proc calls have placeholders   
--      @ForecastID	    - Obsolete, leaving param so proc calls have placeholders   
--      @PlanVersion    - Obsolete, leaving param so proc calls have placeholders 
--		@Plans          - Optional CSV string 'H0623-801,H1019-002' (used for SourceID = 1 and ReportLevel IN (1, 2, 3, 4, 5)) 
--      @IsGroup        - Optional (0 = Individual, 1 = Group, 2 = All)     
--    
--      ======================================================================================    
--      @SourceID         
--      1 = All biddable plans (Product Development fields) 
--      2 = All biddable plans (Milliman fields)  
--      3 = All divisions - biddable, active plans               
--      4 = Regional - biddable, active plans
--		5 = Regional - all active plan indexes
--      ======================================================================================     
--      @ReportLevelID    
--      1 = Plan Level  (dbo.MAReportPlanLevel)    
--      2 = Benefit Level (dbo.fnGetMAReportBLInfo)    
--      3 = Service Area (dbo.fnGetMAReportSAInfo)    
--      4 = Supplemental Benefits (dbo.fnGetMAReportSBInfo)    
--      5 = Optional Supplemental Benefits (dbo.fnGetMAReportOSBInfo or fnGetMAReportOSBTotals)
--      ======================================================================================   
--		Sample Product Development Queries @SourceID = 1 (Updated 2017-Jun-20):
--		--------------------------------------------------------------------------------------
--			EXEC spAppGetAllMAPDAuditExhibit NULL, NULL, NULL, NULL;
--			EXEC spGetMAReport 2018, 1, 1, 0, 0, 0, 0, NULL, 1;
--			EXEC spGetMAReport 2018, 1, 2, 0, 0, 0, 0, NULL, 1;
--			EXEC spGetMAReport 2018, 1, 3, 0, 0, 0, 0, NULL, 1;
--			EXEC spGetMAReport 2018, 1, 4, 0, 0, 0, 0, NULL, 1;
--			EXEC spGetMAReport 2018, 1, 5, 0, 0, 0, 0, NULL, 1;
--		======================================================================================
--    
-- TABLES:     
-- Read:    
--      ArcSavedDivisionDetail    
--      ArcSavedDivisionHeader    
--      ArcSavedMarketHeader    
--      ArcSavedPlanHeader    
--      ArcSavedRegionDetail    
--      ArcSavedRegionHeader    
--      MAReportPlanLevel    
--      SavedDivisionDetail    
--      SavedDivisionHeader    
--      SavedMarketHeader    
--      SavedPlanHeader    
--      SavedRegionDetail    
--      SavedRegionHeader    
-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
--      fnGetMAReportBLInfo    
--      fnGetMAReportCPFInfo    
--      fnGetMAReportOSBInfo    
--      fnGetMAReportOSBTotals    
--      fnGetMAReportSAInfo    
--      fnGetMAReportSBInfo    
--    
-- STORED PROCS:    
--    
-- $HISTORY     
-- ------------------------------------------------------------------------------------------------------------------------------------    
-- DATE           VERSION		CHANGES MADE																		DEVELOPER    
-- ------------------------------------------------------------------------------------------------------------------------------------     
-- 2009-Apr-02      1			Initial Version																		Aleksey Titievsky    
-- 2009-Apr-06      2			Rid of filtering by PerExtContractNumberPlanIDDetail plans							Aleksey Titievsky    
--									for SorceID 5,8,11,12        
-- 2009-Apr-08      3			Add  @ReportLevel 6   claim projection factors										Aleksey Titievsky    
-- 2009-Apr-10      4			Use snapshot table for PlanLevel Reports to improve performance,					Aleksey Titievsky    
--									add ReportLevel 0 to populate snapshot table    
-- 2009-Apr-17      5			Added new fields to Milliman, plan level report                         			Aleksey Titievsky    
-- 2009-Apr-21      6			Added fields to SourceID=12, ReportID=1                                 			Keith Galloway    
-- 2009-May-01      7			Added membership fields to ReportID=0 and for Milliman                  			Keith Galloway    
-- 2009-May-08      8			Added ExpensePMPM fields to many of ReportID's = 0,1                    			Keith Galloway    
-- 2009-May-15      9			Added Fields to all Supp Benefit Reports                                			Keith Galloway    
-- 2009-May-18      10			Added PlanYearID filter to avoid duplicate ForecastIDes                  			Aleksey Titievsky    
-- 2009-May-22      11			Use left join for Division tables at plan level to enable               			Aleksey Titievsky    
--									extracts when Division is not essigned. Exclude unnecessary     
--									joins to Division tables for Report level > 1 SourceID 6-11    
-- 2010-Jan-12      12			Adding MAMemberPremium, PartDBasic, and PartDSupp fields to             			Casey Sanders    
--									output for SourceID=1, ReportLevel=1    
-- 2010-Jan-28      13			Added INBenefitDescription and OONBenefitDescription for                			Jake Gaecke    
--									SourceID=1 and ReportLevel=2    
-- 2010-Feb-17      14			Use fnGetOptionalSuppBenefitsTotal for SourceID=1 and                   			Lawrence Choi    
--									ReportLevel=5     
-- 2010-Mar-01      15			Added use of Contract/Plan for SourceID 1 ReportLevel 2,3 for           			Aleksey Titievsky    
--									product development    
-- 2010-Mar-10      16			Added INBenefitDescription and OONBenefitDescription for                			Lawrence Choi    
--									SourceID=12 and ReportLevel=2            
-- 2010-Apr-01      17			Added use of Contract/Plan for SourceID 1 ReportLevel 4,5 for           			Lawrence Choi    
--									product development    
-- 2010-Apr-10      18			Redefined ExpensePercent                                                			Casey Sanders    
-- 2010-Oct-05      19			Renamed the functions that deal with archived tables. Revamped          			Joe Casey    
--									structure for SourceID = 1, output remains the same.    
-- 2010-Oct-06      20			Revamped structure for SourceID = 2,3. Output remains the same.         			Joe Casey    
--									Removed SourceID=1, ReportLevelID=0. That information is done    
--									by spUpdateOrInsertMAReportPlanLevel now.    
-- 2010-Oct-08      21			Revamped structure for remaining SourceIDs. Output remains the same     			Joe Casey     
-- 2010-Nov-15      22			Add Fields to Plan Level Report.                                        			Jiao Chen    
--									Make first few fields identical across all reports (except Milliman)    
--									Add more filtering options: All Divisions, Group/Individual/Both    
--									Correct duplicate rows in OSB report     
--									Indicate various benefit levels for certain benefit categories    
--									Add fields to OSB report.    
-- 2010-Nov-15      23			Changed Field Names to a more desired output for ReportIDs 3-13         			Joe Casey    
-- 2010-Nov-16      24			Added ISNULL clause for @IsGroup for ReportIDs 3-13                     			Joe Casey    
-- 2010-Nov-29      25			Changed Division joins to Left to account for Group plans that          			Joe Casey    
--									don't have a Division.    
-- 2011-Feb-04      26			Added MAPartBDeductible to output for SourceID 1, ReportLevel 1         			Michael Siekerka    
-- 2011-Feb-07      27			Switched MemberPremium and MemberPremiumRounded for SourceIDs >=3,      			Joe Casey    
--									ReportLevel 1    
-- 2011-Mar-01      28			Removed -1 value for Part B Deductible and added actual value						Joe Casey    
-- 2011-Dec-08      29			Added OOPC/TBC statuses to output for all SourceID's, ReportLevel 1					Trevor Mahoney    
-- 2012-Jan-18      30			Added WHERE statement for ForecastID, SourceID = 13									Trevor Mahoney    
-- 2012-Feb-24      31			Added new admin buckets "Quality Initiatives" and "Taxes and Fees"					Alex Rezmerski    
-- 2012-Dec-12      32			Added IsCombinedDeductible to the report for Product Development					Mason Roberts    
-- 2013-Oct-04      33			Included Join on Segment ID															Manisha Tyagi    
-- 2013-Oct-21      34				Rename Contact - PBP to Contract-PBP-Segment and 
--								Adding segmentId in Contract-PBP-Segment											Manisha Tyagi
-- 2015-Oct-15		35			Updated logic to avoid duplicate bid year data										Deepthi Thiyagu
-- 2016-Nov-18		36			MA Reports Cleanup Summary:															Chris Fleming
--									Removed code related to @ReportLevelID = 6
--								    Removed code related to @SourceID = 4-6, 8, 10-13
--									Updated comments to reflect mapping & numbering changes
--									Cleaned up logic handling reports
-- 2017-Jun-20		37			Removed PerExtContractNumberPlanIDDetail and ArcPerExtContractNumberPlanIDDetail	Chris Fleming
--									and replaced with SavedPlanHeader and ArcSavedPlanHeader
--									Added sample Product Development queries for testing @SourceID = 1 in comments
-- 2019-Feb-12      38           Removed OOPCStatus and TBC Status columns from MAReportPlanLevel                    Kiran Pant
-- 2019-Aug-05      39           Removed reference of Arch tables                                                    Kiran Pant
-- 2019-Sep-10      40           Renamed PackageIndex to PackageID and PlanYearID to PlanYear                        Pooja Dahiya
-- 2019-Sep-19      41           Revert changes in version 40 as benefits changes are not yet moved					 Pooja Dahiya
-- 2019-Oct-16      42           Revert Removed reference of Arch tables                                             Manisha Tyagi
-- 2019-Oct-23      43           Remove multiple rows issue because of change in version 42                          Pooja Dahiya
-- 2022-May-01		44			MAAUI migration; for MAReportPlanLevel, changed reference from PlanIndex to 
--								ForecastID; removed IsEGWP; SavedPlanHeader structure change recoding;
--								replaced variable [Plan Index] with ForecastID for consistency						Aleksandar Dimitrijevic
-- 2023-June-16      45   To view MA report for Benefit Level, created the table variable to improve the performance of the query
-- 2024-Mar-29		46		Added WITH(NOLOCK)																		Archana Sahu
-----------------------------------------------------------------------------------------------------------------------------------   
CREATE PROCEDURE [dbo].[spGetMAReport] 
    (    
     @PlanYearID SMALLINT,    
     @SourceID TINYINT,       
     @ReportLevel TINYINT,   
     @GeoFilterID INT     = 0,    -- Optional (used only when SourceID IN (3, 4, 5))
     @Plans VARCHAR(2000) = NULL, -- Optional (used for SourceID = 1 and ReportLevel IN (1, 2, 3, 4, 5)) 
     @IsGroup TINYINT     = 2     -- Optional (0 = Individual, 1 = Group, 2 = All)    
	)     
AS 
BEGIN
IF OBJECT_ID('tempdb.dbo.#tmpSourceTable') IS NOT NULL 
	DROP TABLE #tmpSourceTable
	CREATE TABLE #tmpSourceTable 
		(
		 ForecastID INT, 
		 DivisionName VARCHAR(30), 
		 HumanaRegionName VARCHAR(30), 
		 MarketName VARCHAR(35)
		)

IF OBJECT_ID('tempdb.dbo.#tmpSource1Table') IS NOT NULL
	DROP TABLE #tmpSource1Table
	CREATE TABLE #tmpSource1Table 
		(
		 ForecastID INT
		)

DECLARE @BidYear INT 
SET @BidYear =  dbo.fnGetBidYear()

BEGIN
IF @SourceID = 1 
BEGIN
	INSERT INTO #tmpSource1Table    
    SELECT  sph.ForecastID
	FROM    (SELECT * FROM SavedPlanHeader WITH (NOLOCK)
			 UNION ALL
			 SELECT PlanyearID,
					ForecastID,
					0,
					0,
					MarketID,
					PlanTypeID,
					ContractNumber,
					PlanID,
					SegmentID,
					CPDID,
					IsSNP,
					ContactID,
					SecondaryContactID,
					PartBPremiumBuyDown,
					MARegionID,
					Notes,
					isMAPD,
					IsOffModelRx,
					IsHidden,
					LastUpdateByID,
					LastUpdateDateTime,
					IsToReprice,
					SNPTypeID,
					AltRebateOrder,
					IsLiveIndex,
					PlanDescription,
					IsMLA,
					QualityInitDescrip,
					IsCombinedDeductible,
					IsSkipInducedUtilizationMapping,
					IsRiskPlan,
					IsSctPlan,
					IsFiledPlan,
					IsLocked,
					PlanName,
					CertifyingActuaryUserID 
			 FROM ArcSavedPlanHeader WITH (NOLOCK) WHERE PlanYearID != @BidYear) sph    
	WHERE   sph.PlanYearID = @PlanYearID     
			AND sph.IsHidden = 0  
			AND sph.IsFiledPlan = 1
			AND (@ReportLevel NOT IN (1,2,3,4,5) OR (PATINDEX('%' + sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentId + '%', @Plans) > 0 OR @Plans IS NULL))
END

ELSE IF @SourceID = 2 --All biddable plans (Milliman fields)   
BEGIN
	INSERT INTO #tmpSource1Table  	  
    SELECT  sph.ForecastID
	FROM    (SELECT * FROM SavedPlanHeader WITH (NOLOCK)
			 UNION ALL
			 SELECT  PlanyearID,
					ForecastID,
					0,
					0,
					MarketID,
					PlanTypeID,
					ContractNumber,
					PlanID,
					SegmentID,
					CPDID,
					IsSNP,
					ContactID,
					SecondaryContactID,
					PartBPremiumBuyDown,
					MARegionID,
					Notes,
					isMAPD,
					IsOffModelRx,
					IsHidden,
					LastUpdateByID,
					LastUpdateDateTime,
					IsToReprice,
					SNPTypeID,
					AltRebateOrder,
					IsLiveIndex,
					PlanDescription,
					IsMLA,
					QualityInitDescrip,
					IsCombinedDeductible,
					IsSkipInducedUtilizationMapping,
					IsRiskPlan,
					IsSctPlan,
					IsFiledPlan,
					IsLocked,
					PlanName,
					CertifyingActuaryUserID
			FROM ArcSavedPlanHeader WITH (NOLOCK) WHERE PlanYearID != @BidYear) sph    
	WHERE   sph.PlanYearID = @PlanYearID   
			AND sph.IsHidden = 0 
			AND sph.IsFiledPlan = 1
			AND sph.IsMAPD = 1
END

ELSE IF @SourceID = 3 --All divisions - biddable, active plans 
BEGIN
	INSERT INTO #tmpSourceTable    
    SELECT	sph.ForecastID,     
			smh.ActuarialDivision DivisionName,    
			sdd.ActuarialRegion HumanaRegionName,    
			srd.ActuarialMarket MarketName    
	FROM    (SELECT * FROM SavedPlanHeader WITH (NOLOCK)
			 UNION ALL
			 SELECT  PlanyearID,
					ForecastID,
					0,
					0,
					MarketID,
					PlanTypeID,
					ContractNumber,
					PlanID,
					SegmentID,
					CPDID,
					IsSNP,
					ContactID,
					SecondaryContactID,
					PartBPremiumBuyDown,
					MARegionID,
					Notes,
					isMAPD,
					IsOffModelRx,
					IsHidden,
					LastUpdateByID,
					LastUpdateDateTime,
					IsToReprice,
					SNPTypeID,
					AltRebateOrder,
					IsLiveIndex,
					PlanDescription,
					IsMLA,
					QualityInitDescrip,
					IsCombinedDeductible,
					IsSkipInducedUtilizationMapping,
					IsRiskPlan,
					IsSctPlan,
					IsFiledPlan,
					IsLocked,
					PlanName,
					CertifyingActuaryUserID
			FROM ArcSavedPlanHeader WITH (NOLOCK) WHERE PlanYearID != @BidYear) sph

			INNER JOIN (SELECT  ActuarialMarketID,ActuarialMarket,ActuarialRegionID,@BidYear  as PlanYearID FROM dbo.SavedMarketInfo WITH (NOLOCK)
			UNION SELECT asm.MarketID AS ActuarialMarketID,asm.MarketName AS ActuarialMarket,asd.HumanaRegionID,asm.PlanYearID AS ActuarialRegionID FROM dbo.ArcSavedMarketHeader asm WITH (NOLOCK)
			LEFT JOIN ArcSavedRegionDetail asd WITH (NOLOCK) ON asm.PlanYearID=asd.PlanYearID AND asm.MarketID=asd.MarketID 
			WHERE asm.PlanYearID != @BidYear) srd    
			ON srd.ActuarialMarketID = sph.MarketID and  srd.PlanYearID = sph.PlanYearID

			LEFT JOIN (SELECT ActuarialRegionID,ActuarialRegion,ActuarialDivisionID,@BidYear  as PlanYearID FROM dbo.SavedRegionInfo WITH (NOLOCK)
			UNION SELECT asr.HumanaRegionID AS ActuarialRegionID,ash.HumanaRegionName AS ActuarialRegion,DivisionID AS ActuarialDivisionID,asr.PlanYearID FROM dbo.ArcSavedRegionDetail asr WITH (NOLOCK)
			LEFT JOIN dbo.ArcSavedRegionHeader ash WITH (NOLOCK) ON asr.PlanYearID=ash.PlanYearID AND asr.HumanaRegionID=ash.HumanaRegionID 
			LEFT JOIN dbo.ArcSavedDivisionDetail asd WITH (NOLOCK) ON asr.PlanYearID=asd.PlanYearID AND asr.HumanaRegionID=asd.HumanaRegionID WHERE asr.PlanYearID != @BidYear) sdd    
			ON   srd.ActuarialRegionID = sdd.ActuarialRegionID   AND sph.PlanYearID = sdd.PlanYearID 

			LEFT JOIN (SELECT ActuarialDivisionID,ActuarialDivision,@BidYear  as PlanYearID FROM dbo.SavedDivisionInfo WITH (NOLOCK)
			UNION SELECT asd.DivisionID AS ActuarialDivisionID,DivisionName AS ActuarialDivision,asd.PlanYearID FROM dbo.ArcSavedDivisionDetail asd WITH (NOLOCK)
			LEFT JOIN dbo.ArcSavedDivisionHeader ash WITH (NOLOCK) ON asd.PlanYearID=ash.PlanYearID AND asd.DivisionID=ash.DivisionID WHERE asd.PlanYearID != @BidYear) smh    
			ON  smh.ActuarialDivisionID = sdd.ActuarialDivisionID AND sph.PlanYearID = smh.PlanYearID

	WHERE   sph.PlanYearID = @PlanYearID     
			AND sph.IsHidden = 0  
			AND sph.IsFiledPlan = 1
END

ELSE IF @SourceID = 4 --Regional - biddable, active plans   
BEGIN
	INSERT INTO #tmpSourceTable    
    SELECT	sph.ForecastID,     
			smh.ActuarialDivision DivisionName,    
			sdd.ActuarialRegion HumanaRegionName,    
			srd.ActuarialMarket MarketName    
	FROM    (SELECT * FROM SavedPlanHeader WITH (NOLOCK)
			 UNION ALL
			 SELECT  PlanyearID,
					ForecastID,
					0,
					0,
					MarketID,
					PlanTypeID,
					ContractNumber,
					PlanID,
					SegmentID,
					CPDID,
					IsSNP,
					ContactID,
					SecondaryContactID,
					PartBPremiumBuyDown,
					MARegionID,
					Notes,
					isMAPD,
					IsOffModelRx,
					IsHidden,
					LastUpdateByID,
					LastUpdateDateTime,
					IsToReprice,
					SNPTypeID,
					AltRebateOrder,
					IsLiveIndex,
					PlanDescription,
					IsMLA,
					QualityInitDescrip,
					IsCombinedDeductible,
					IsSkipInducedUtilizationMapping,
					IsRiskPlan,
					IsSctPlan,
					IsFiledPlan,
					IsLocked,
					PlanName,
					CertifyingActuaryUserID
			FROM ArcSavedPlanHeader WITH (NOLOCK) WHERE PlanYearID != @BidYear) sph 

			INNER JOIN (SELECT  ActuarialMarketID,ActuarialMarket,ActuarialRegionID,@BidYear  as PlanYearID FROM dbo.SavedMarketInfo WITH (NOLOCK)
			UNION SELECT asm.MarketID AS ActuarialMarketID,asm.MarketName AS ActuarialMarket,asd.HumanaRegionID,asm.PlanYearID AS ActuarialRegionID FROM dbo.ArcSavedMarketHeader asm WITH (NOLOCK)
			LEFT JOIN ArcSavedRegionDetail asd WITH (NOLOCK) ON asm.PlanYearID=asd.PlanYearID AND asm.MarketID=asd.MarketID 
			WHERE asm.PlanYearID != @BidYear) srd    
			ON srd.ActuarialMarketID = sph.MarketID and  srd.PlanYearID = sph.PlanYearID

			LEFT JOIN (SELECT ActuarialRegionID,ActuarialRegion,ActuarialDivisionID,@BidYear  as PlanYearID FROM dbo.SavedRegionInfo WITH (NOLOCK)
			UNION SELECT asr.HumanaRegionID AS ActuarialRegionID,ash.HumanaRegionName AS ActuarialRegion,DivisionID AS ActuarialDivisionID,asr.PlanYearID FROM dbo.ArcSavedRegionDetail asr WITH (NOLOCK)
			LEFT JOIN dbo.ArcSavedRegionHeader ash WITH (NOLOCK) ON asr.PlanYearID=ash.PlanYearID AND asr.HumanaRegionID=ash.HumanaRegionID 
			LEFT JOIN dbo.ArcSavedDivisionDetail asd WITH (NOLOCK) ON asr.PlanYearID=asd.PlanYearID AND asr.HumanaRegionID=asd.HumanaRegionID WHERE asr.PlanYearID != @BidYear) sdd    
			ON   srd.ActuarialRegionID = sdd.ActuarialRegionID   AND sph.PlanYearID = sdd.PlanYearID 

			LEFT JOIN (SELECT ActuarialDivisionID,ActuarialDivision,@BidYear  as PlanYearID FROM dbo.SavedDivisionInfo WITH (NOLOCK)
			UNION SELECT asd.DivisionID AS ActuarialDivisionID,DivisionName AS ActuarialDivision,asd.PlanYearID FROM dbo.ArcSavedDivisionDetail asd WITH (NOLOCK)
			LEFT JOIN dbo.ArcSavedDivisionHeader ash WITH (NOLOCK) ON asd.PlanYearID=ash.PlanYearID AND asd.DivisionID=ash.DivisionID WHERE asd.PlanYearID != @BidYear) smh    
			ON  smh.ActuarialDivisionID = sdd.ActuarialDivisionID AND sph.PlanYearID = smh.PlanYearID 

	WHERE   sph.PlanYearID = @PlanYearID       
			AND sph.IsHidden = 0 
			AND sph.IsFiledPlan = 1
			AND srd.ActuarialRegionID = @GeoFilterID --@RegionID will be passed 
END

ELSE IF @SourceID = 5 -- Regional - all active plan indexes   
BEGIN
	INSERT INTO #tmpSourceTable 
   SELECT	sph.ForecastID,     
			smh.ActuarialDivision DivisionName,    
			sdd.ActuarialRegion HumanaRegionName,    
			srd.ActuarialMarket MarketName    
	FROM    (SELECT * FROM SavedPlanHeader WITH (NOLOCK)
			 UNION ALL
			 SELECT  PlanyearID,
					ForecastID,
					0,
					0,
					MarketID,
					PlanTypeID,
					ContractNumber,
					PlanID,
					SegmentID,
					CPDID,
					IsSNP,
					ContactID,
					SecondaryContactID,
					PartBPremiumBuyDown,
					MARegionID,
					Notes,
					isMAPD,
					IsOffModelRx,
					IsHidden,
					LastUpdateByID,
					LastUpdateDateTime,
					IsToReprice,
					SNPTypeID,
					AltRebateOrder,
					IsLiveIndex,
					PlanDescription,
					IsMLA,
					QualityInitDescrip,
					IsCombinedDeductible,
					IsSkipInducedUtilizationMapping,
					IsRiskPlan,
					IsSctPlan,
					IsFiledPlan,
					IsLocked,
					PlanName,
					CertifyingActuaryUserID
			FROM ArcSavedPlanHeader WITH (NOLOCK) WHERE PlanYearID != @BidYear) sph 

			INNER JOIN (SELECT  ActuarialMarketID,ActuarialMarket,ActuarialRegionID,@BidYear  as PlanYearID FROM dbo.SavedMarketInfo WITH (NOLOCK)
			UNION SELECT asm.MarketID AS ActuarialMarketID,asm.MarketName AS ActuarialMarket,asd.HumanaRegionID,asm.PlanYearID AS ActuarialRegionID FROM dbo.ArcSavedMarketHeader asm WITH (NOLOCK)
			LEFT JOIN ArcSavedRegionDetail asd WITH (NOLOCK) ON asm.PlanYearID=asd.PlanYearID AND asm.MarketID=asd.MarketID 
			WHERE asm.PlanYearID != @BidYear) srd
			ON srd.ActuarialMarketID = sph.MarketID and  srd.PlanYearID = sph.PlanYearID

			LEFT JOIN (SELECT ActuarialRegionID,ActuarialRegion,ActuarialDivisionID,@BidYear  as PlanYearID FROM dbo.SavedRegionInfo WITH (NOLOCK)
			UNION SELECT asr.HumanaRegionID AS ActuarialRegionID,ash.HumanaRegionName AS ActuarialRegion,DivisionID AS ActuarialDivisionID,asr.PlanYearID FROM dbo.ArcSavedRegionDetail asr WITH (NOLOCK)
			LEFT JOIN dbo.ArcSavedRegionHeader ash WITH (NOLOCK) ON asr.PlanYearID=ash.PlanYearID AND asr.HumanaRegionID=ash.HumanaRegionID 
			LEFT JOIN dbo.ArcSavedDivisionDetail asd WITH (NOLOCK) ON asr.PlanYearID=asd.PlanYearID AND asr.HumanaRegionID=asd.HumanaRegionID WHERE asr.PlanYearID != @BidYear) sdd    
			ON   srd.ActuarialRegionID = sdd.ActuarialRegionID   AND sph.PlanYearID = sdd.PlanYearID 

			LEFT JOIN (SELECT ActuarialDivisionID,ActuarialDivision,@BidYear  as PlanYearID FROM dbo.SavedDivisionInfo WITH (NOLOCK)
			UNION SELECT asd.DivisionID AS ActuarialDivisionID,DivisionName AS ActuarialDivision,asd.PlanYearID FROM dbo.ArcSavedDivisionDetail asd WITH (NOLOCK) 
			LEFT JOIN dbo.ArcSavedDivisionHeader ash WITH (NOLOCK) ON asd.PlanYearID=ash.PlanYearID AND asd.DivisionID=ash.DivisionID WHERE asd.PlanYearID != @BidYear) smh    
			ON  smh.ActuarialDivisionID = sdd.ActuarialDivisionID AND sph.PlanYearID = smh.PlanYearID

	WHERE   sph.PlanYearID = @PlanYearID       
			AND sph.IsHidden = 0 
			AND srd.ActuarialRegionID = @GeoFilterID 
END
ELSE
BEGIN
   SELECT 'Invalid SourceID' Message 
END
END

 IF @ReportLevel = 1 AND @SourceID = 1 --Plan level
BEGIN
	SELECT   
		[PlanYearID]                        = pd.PlanYearID,    
        [ContractNumber]                    = pd.ContractNumber,
		[PBPID]								= pd.PlanID,                      
        [SegmentId]							= pd.SegmentId,                    
        [ForecastID]						= pd.ForecastID,                  
        [PlanVersion]						= pd.PlanVersion,               
        [PlanName]							= pd.PlanName,                  
        [OrganizationName]					= pd.OrganizationName,       
        [PlanTypeName]						= pd.PlanTypeName,          
        [MAPD]								= pd.MAPD,    
  --      [OOPCStatus]						= pd.OOPCStatus, 
		--[TBCStatus]							= pd.TBCStatus, 
		[SNP]								= pd.SNP, 
		[BasicMemberPremium]				= pd.BasicMbrPrem,    
		[MAMemberPremium]					= ROUND(pd.BasicMbrPrem + pd.NetABSuppBen + pd.NetSuppBen, 1),         
		[INMOOP]							= pd.INMOOP,                    
		[INDeductible]						= pd.INDeductible,              
		[OONMOOP]							= pd.OONMOOP,
		[OONDeductible]						= pd.OONDeductible,
		[CombinedMOOP]						= pd.CombinedMOOP,
		[PartBPremiumBuyDown]				= pd.PartBPremiumBuyDown,
		[RxBasicPremium]					= pd.RxBasicPremium,
		[RxSuppPremium]						= pd.RxSuppPremium,
		[NetPartDBasic]						= pd.NetPartDBasic,
		[NetPartDSupp]						= pd.NetPartDSupp,
		[MAPartBDeductible]					= pd.PartBDeductible,    
        [IsCombinedDeductible]				= pd.IsCombinedDeductible
	FROM #tmpSource1Table plans    
    INNER JOIN dbo.MAReportPlanLevel pd  WITH (NOLOCK)  
    ON pd.ForecastID = plans.ForecastID  
		AND pd.PlanYearID = @PlanYearID
END

ELSE IF @ReportLevel = 1 AND @SourceID = 2
BEGIN
	SELECT   
		[PlanYearID]                        = pd.PlanYearID,    
        [ContractNumber]                    = pd.ContractNumber,
		[PBPID]								= pd.PlanID,                      
        [SegmentId]							= pd.SegmentId,                    
        [ForecastID]						= pd.ForecastID,                  
        [PlanVersion]						= pd.PlanVersion,               
        [PlanName]							= pd.PlanName,                  
        [OrganizationName]					= pd.OrganizationName,       
        [PlanTypeName]						= pd.PlanTypeName,          
        [MAPD]								= pd.MAPD,
        [SNP]								= pd.SNP,    
  --      [OOPCStatus]						= pd.OOPCStatus, 
		--[TBCStatus]							= pd.TBCStatus, 
		[BasicMemberPremium]				= pd.BasicMbrPrem,              
		[INMOOP]							= pd.INMOOP,                    
		[INDeductible]						= pd.INDeductible,              
		[OONMOOP]							= pd.OONMOOP,
		[OONDeductible]						= pd.OONDeductible,
		[CombinedMOOP]						= pd.CombinedMOOP,
		[PartBPremiumBuyDown]				= pd.PartBPremiumBuyDown,
		[RxBasicPremium]					= pd.RxBasicPremium,
		[RxSuppPremium]						= pd.RxSuppPremium,
		[MARiskScore]						= pd.MARiskScore,
		[ExpensePercent]					= pd.ExpensePercent,
		[ExpensePMPM]						= pd.ExpensePMPM,
		[ProfitPercent]						= pd.ProfitPercent,
		[ProjectedTotalMemberMonths]		= pd.ProjectedMemberMonths,
		[ProjectedDualEligibleMemberMonths] = pd.ProjectedDualMemberMonths,
		[Rebate]							= pd.Rebate,
		[RebateSpent]						= pd.RebateSpent,
		[RebateUnspent]						= pd.RebateUnspent,
		[AuditTime]							= pd.AuditTime
	FROM #tmpSource1Table plans    
    INNER JOIN dbo.MAReportPlanLevel pd WITH (NOLOCK)  
    ON pd.ForecastID = plans.ForecastID   
		AND pd.PlanYearID = @PlanYearID
END

ELSE IF @ReportLevel = 1 AND @SourceID IN (3, 4, 5)
BEGIN
	SELECT  
		[Plan Year]                         = pd.PlanyearID,    
        [Division]                          = plans.DivisionName,    
        [Region]                            = plans.HumanaRegionName,    
		[BPT Product Type]                  = pd.PlanTypeName,    
        [Contract Number]                   = pd.ContractNumber,    
        [PlanId]                            = pd.PlanID,    
        [SegmentId]                         = pd.SegmentId,
		[Contract-PBP-Segment]              = pd.ContractNumber + '-' + LTRIM(pd.PlanID)+ '-' + LTRIM(pd.SegmentId),    
        [ForecastID]                        = pd.ForecastID,    
		[Market]                            = plans.MarketName,    
		[Plan Name]                         = pd.PlanName,    
		[SNP Type]                          = pd.SNPTypeName,    
		--[OOPC Status]						= pd.OOPCStatus,    
		--[TBC Status]						= pd.TBCStatus,    
        [Member Premium]                    = pd.MemberPremiumRounded,    
        [MA Risk Score]                     = pd.MARiskScore,    
        [MSP Adj]                           = pd.SecondaryPayerAdjustment,    
        [MOOP - Combined]                   = pd.CombinedMOOP,    
        [MOOP - IN]                         = pd.INMOOP,    
        [MOOP - OON]                        = pd.OONMOOP,    
        [Plan Level Deductible - IN]        = pd.INdeductible,    
        [Plan Level Deductible - OON]       = pd.OONdeductible,    
        [MMs - Projected Total]             = pd.ProjectedMemberMonths,    
        [MMs - Projected ESRD]              = pd.ProjESRDMemberMonths,    
        [MMs - Experience]                  = pd.ExperienceMemberMonths,    
        [Total Projected Allowed Cost]      = pd.TotalMedicalExp,    
        [Total Projected Net Cost]          = pd.NetNonESRD,    
        [Proj Year Ben Factor]              = pd.ProjYrBenFactor,    
        [Curr Year Ben Factor]              = pd.CurYrBenFactor,    
        [Admin % - Total]                   = pd.ExpensePercent,    
		[Admin PMPM - Total]                = pd.ExpensePMPM,    
		[Admin PMPM - Marketing and Sales]  = pd.MarketingSales,    
		[Admin PMPM - Direct]               = pd.DirectAdmin,    
		[Admin PMPM - Indirect]             = pd.InDirectAdmin,    
		[Admin PMPM - Quality Initiatives]  = pd.QualityInitiatives,    
		[Admin PMPM - Taxes and Fees]		= pd.TaxesAndFees,    
        [Profit % - Total]                  = pd.ProfitPercent,    
		[Profit PMPM]                       = pd.Profit,    
		[Required Revenue]                  = pd.TotalReqRev,    
        [Plan A/B Bid]                      = pd.PlanBid,    
		[Plan Benchmark]					= pd.PlanBenchmark,    
		[RPPO PBC]                          = pd.RPPOPBC,    
        [Basic Member Premium]              = pd.BasicMbrPrem,    
        [Rebate]                            = pd.Rebate,    
        [Unspent Rebate]                    = pd.RebateUnspent,    
        [Max ABCS Buydown]                  = pd.CostShare,    
        [ABCS Buydown]                      = pd.CostShareReduction,    
        [ABCS Net]                          = pd.NetABSuppBen,    
        [Max MSB Buydown]                   = pd.SuppPrem,    
        [MSB Buydown]                       = pd.TotalOtherSuppBen,    
        [MSB Net]                           = pd.NetSuppBen,    
        [Max Rx Basic Buydown]              = pd.RxBasicPremium,    
        [Rx Basic Buydown]                  = pd.RxBasicBuyDown,    
        [Rx Basic Net]                      = pd.NetPartDBasic,    
        [Max Rx Supp Buydown]               = pd.RxSuppPremium,    
        [Rx Supp Buydown]                   = pd.RxSuppBuyDown,    
        [Rx Supp Net]                       = pd.NetPartDSupp,    
        [Max Part B Buydown]                = pd.PtBMaxRebateAlloc,    
        [Part B Buydown]                    = pd.PartBPremiumBuyDown,    
        [Member Premium Unrounded]          = pd.MemberPremium,    
        [Audit Time]                        = pd.AuditTime    
	FROM #tmpSourceTable plans    
    INNER JOIN dbo.MAReportPlanLevel pd WITH (NOLOCK)    
    ON pd.ForecastID =  plans.ForecastID   
		AND pd.PlanYearID = @PlanYearID
END

ELSE IF @ReportLevel = 2 AND @SourceID = 1 --Benefit Level
BEGIN
	SELECT DISTINCT    
		[PlanYearID]						= pd.PlanYearID,
		[BenefitYearID]						= pd.PlanYearID,
		[ContractNumber]					= pd.ContractNumber,
		[PBPID]								= pd.PlanID,
		[SegmentId]							= pd.SegmentId,
		[ForecastID]						= pd.ForecastID,
		[PlanVersion]						= pd.PlanVersion,
		[BenefitCategoryID]					= pd.BenefitCategoryID,
		[BenefitOrdinalID]					= pd.BenefitOrdinalID,
		[BenefitCategoryName]				= pd.BenefitCategoryName,
		[INBenefitType]						= pd.INBenefitType,
		[INBenefitValue]					= pd.INBenefitValue,
		[INEndOfDayRange]					= pd.INBenefitDayRange,
		[OONBenefitType]					= pd.OONBenefitType,
		[OONBenefitValue]					= pd.OONBenefitValue,
		[OONEndOfDayRange]					= pd.OONBenefitDayRange,
		[ServiceID]							= pd.PBPServiceID,   
        [PlaceOfTreatmentID]				= pd.PlaceOfTreatmentID,    
		[INBenefitDescription]				= pd.INBenefitDescription,   
		[OONBenefitDescription]				= pd.OONBenefitDescription  
	FROM #tmpSource1Table plans    
    CROSS APPLY fnGetMAReportBLInfo(@PlanYearID, plans.ForecastID) pd   
END

ELSE IF @ReportLevel = 2 AND @SourceID = 2
BEGIN
	DECLARE @BenefitLevelDataTable AS TABLE
	([PlanYearID] INTEGER NOT NULL,
		[BenefitYearID]	INTEGER NOT NULL,
		[ContractNumber] VARCHAR(20) NOT NULL,
		[PBPID] VARCHAR(20),
		[SegmentId] VARCHAR(20),
		[ForecastID] INTEGER,
		[PlanVersion] VARCHAR(10),
		[BenefitCategoryID] INTEGER,
		[BenefitCategoryName] VARCHAR(200),
		[INBenefitType]	VARCHAR(100),
		[INBenefitValue] NUMERIC(10,4),
		[INEndOfDayRange]	integer,
		[OONBenefitType]	VARCHAR(100),
		[OONBenefitValue]	VARCHAR(100),
		[OONEndOfDayRange]	VARCHAR(100)
		)
	
	IF (@GeoFilterID <> 1)--View MAReport
	BEGIN
		INSERT INTO @BenefitLevelDataTable
		([PlanYearID] ,
		[BenefitYearID]	,
		[ContractNumber] ,
		[PBPID],
		[SegmentId],
		[ForecastID],
		[PlanVersion],
		[BenefitCategoryID],
		[BenefitCategoryName],
		[INBenefitType],
		[INBenefitValue],
		[INEndOfDayRange],
		[OONBenefitType],
		[OONBenefitValue],
		[OONEndOfDayRange]
		)
	SELECT TOP 100
		[PlanYearID]						= pd.PlanYearID,
		[BenefitYearID]						= pd.PlanYearID,
		[ContractNumber]					= pd.ContractNumber,
		[PBPID]								= pd.PlanID,
		[SegmentId]							= pd.SegmentId,
		[ForecastID]						= pd.ForecastID,
		[PlanVersion]						= pd.PlanVersion,
		[BenefitCategoryID]					= pd.BenefitCategoryID,
		[BenefitCategoryName]				= pd.BenefitCategoryName,
		[INBenefitType]						= pd.INBenefitType,
		[INBenefitValue]					= pd.INBenefitValue,
		[INEndOfDayRange]					= pd.INBenefitDayRange,
		[OONBenefitType]					= pd.OONBenefitType,
		[OONBenefitValue]					= pd.OONBenefitValue,
		[OONEndOfDayRange]					= pd.OONBenefitDayRange   
	FROM #tmpSource1Table plans    
    CROSS APPLY fnGetMAReportBLInfo(@PlanYearID, plans.ForecastID) pd 
	ORDER BY    
		pd.PlanYearID,    
        pd.ContractNumber,    
        pd.PlanID,    
        pd.SegmentId,    
        pd.ForecastID,    
        pd.PlanVersion,    
        pd.BenefitCategoryID
	END 
	ELSE --Exporting MA report 
    BEGIN
		INSERT INTO 	@BenefitLevelDataTable
		([PlanYearID] ,
		[BenefitYearID]	,
		[ContractNumber] ,
		[PBPID],
		[SegmentId],
		[ForecastID],
		[PlanVersion],
		[BenefitCategoryID],
		[BenefitCategoryName],
		[INBenefitType],
		[INBenefitValue],
		[INEndOfDayRange],
		[OONBenefitType],
		[OONBenefitValue],
		[OONEndOfDayRange]
		)
	SELECT 
		[PlanYearID]						= pd.PlanYearID,
		[BenefitYearID]						= pd.PlanYearID,
		[ContractNumber]					= pd.ContractNumber,
		[PBPID]								= pd.PlanID,
		[SegmentId]							= pd.SegmentId,
		[ForecastID]						= pd.ForecastID,
		[PlanVersion]						= pd.PlanVersion,
		[BenefitCategoryID]					= pd.BenefitCategoryID,
		[BenefitCategoryName]				= pd.BenefitCategoryName,
		[INBenefitType]						= pd.INBenefitType,
		[INBenefitValue]					= pd.INBenefitValue,
		[INEndOfDayRange]					= pd.INBenefitDayRange,
		[OONBenefitType]					= pd.OONBenefitType,
		[OONBenefitValue]					= pd.OONBenefitValue,
		[OONEndOfDayRange]					= pd.OONBenefitDayRange   
	FROM #tmpSource1Table plans    
    CROSS APPLY fnGetMAReportBLInfo(@PlanYearID, plans.ForecastID) pd  
	
    END
	SELECT DISTINCT * FROM @BenefitLevelDataTable
END

ELSE IF @ReportLevel = 2 AND @SourceID IN (3, 4, 5)
BEGIN
	SELECT DISTINCT     
		[Plan Year]                         = pd.PlanYearID,    
        [Division]                          = plans.DivisionName,    
        [Region]                            = plans.HumanaRegionName,    
		[BPT Product Type]                  = pd.PlanTypeName,    
		[Contract Number]                   = pd.ContractNumber,    
		[PlanId]                            = pd.PlanId,    
		[SegmentId]                         = pd.SegmentId,  
		[Contract-PBP-Segment]              = pd.ContractNumber + '-' + LTRIM(pd.PlanId)+ '-' + LTRIM(pd.SegmentId),   
        [ForecastID]                        = pd.ForecastID,    
		[Benefit Year]                      = pd.PlanYearID,    
		[Benefit Category ID]               = pd.BenefitCategoryID,    
		[Benefit Level]                     = 'L' + LTRIM(Str(pd.BenefitOrdinalID)),    
        [Benefit Category Name]             = pd.BenefitCategoryName,    
        [IN Benefit Type]                   = pd.INBenefitType,    
        [IN Benefit Value]                  = pd.INBenefitValue,    
        [IN End Of Day Range]               = pd.INBenefitDayRange,    
        [OON Benefit Type]                  = pd.OONBenefitType,    
        [OON Benefit Value]                 = pd.OONBenefitValue,    
        [OON End Of Day Range]              = pd.OONBenefitDayRange    
	FROM #tmpSourceTable plans    
    CROSS APPLY fnGetMAReportBLInfo(@PlanYearID, plans.ForecastID) pd
END

ELSE IF @ReportLevel = 3 AND @SourceID IN (1, 2) --Service Area
BEGIN
	SELECT        
		[PlanYearID]						= pd.PlanYearID,
		[ContractNumber]					= pd.ContractNumber,
		[PBPID]								= pd.PlanID,
		[SegmentId]							= pd.SegmentId,
		[ForecastID]						= pd.ForecastID,
		[StateID]							= pd.StateTerritoryCode,
		[StateName]							= pd.StateTerritoryName,
		[CountyID]							= pd.CountyCode,
		[CountyName]						= pd.CountyName   
	FROM #tmpSource1Table plans    
    CROSS APPLY dbo.fnGetMAReportSAInfo(@PlanYearID, plans.ForecastID) pd 
END

ELSE IF @ReportLevel = 3 AND @SourceID IN (3, 4, 5) 
BEGIN
	SELECT 
		[Plan Year]							= pd.PlanYearID,    
		[Division]							= plans.DivisionName,    
        [Region]							= plans.HumanaRegionName,    
		[BPT Product Type]					= pd.PlanTypeName,    
		[Contract Number]					= pd.ContractNumber,    
		[PlanId]							= pd.PlanID,    
        [SegmentId]							= pd.SegmentId,   
		[Contract-PBP-Segment]				= pd.ContractNumber + '-' + LTRIM(pd.PlanID) + '-' + LTRIM(pd.SegmentId),   
        [ForecastID]						= pd.ForecastID,    
        [State ID]							= pd.StateTerritoryCode,    
        [State Name]						= pd.StateTerritoryName,    
        [County ID]							= pd.CountyCode,    
        [County Name]						= pd.CountyName    
	FROM #tmpSourceTable plans    
    CROSS APPLY dbo.fnGetMAReportSAInfo(@PlanYearID, plans.ForecastID) pd            
END

ELSE IF @ReportLevel = 4 AND @SourceID = 1 --MSBs
BEGIN
	SELECT    
		[PlanYearID]						= pd.PlanYearID,    
		[ContractNumber]					= pd.ContractNumber,    
		[PBPID]								= pd.PlanID,    
        [SegmentId]							= pd.SegmentId,   
        [ForecastID]						= pd.ForecastID,      
        [BenefitName]		                = pd.AddedBenefitName,   
        [BenefitCode]						= pd.BenefitCode,       
		[INAddedBenefitDescription]			= pd.INAddedBenefitDescription,    
		[INAddedBenefitAllowed]				= pd.INAddedBenefitAllowed,        
		[INAddedBenefitCostShare]			= pd.INAddedBenefitCostShare,    
		[OONAddedBenefitDescription]		= pd.OONAddedBenefitDescription,    
		[OONAddedBenefitAllowed]			= pd.OONAddedBenefitAllowed,    
		[OONAddedBenefitUtilization]		= pd.OONAddedBenefitUtilization,    
		[OONAddedBenefitCostShare]			= pd.OONAddedBenefitCostShare               
	FROM #tmpSource1Table plans    
    CROSS APPLY fnGetMAReportSBInfo (@PlanYearID, plans.ForecastID) pd  
END

ELSE IF @ReportLevel = 4 AND @SourceID = 2
BEGIN
	SELECT    
		[PlanYearID]						= pd.PlanYearID,    
		[ContractNumber]					= pd.ContractNumber,    
		[PBPID]								= pd.PlanID,    
        [SegmentId]							= pd.SegmentId,   
        [ForecastID]						= pd.ForecastID,  
		[PlanVersion]						= pd.PlanVersion,  
        [BenefitName]		                = pd.AddedBenefitName,    
		[INAddedBenefitDescription]			= pd.INAddedBenefitDescription,    
		[INAddedBenefitAllowed]				= pd.INAddedBenefitAllowed,    
		[INAddedBenefitUtilization]			= pd.INAddedBenefitUtilization,    
		[INAddedBenefitCostShare]			= pd.INAddedBenefitCostShare,    
		[OONAddedBenefitDescription]		= pd.OONAddedBenefitDescription,    
		[OONAddedBenefitAllowed]			= pd.OONAddedBenefitAllowed,    
		[OONAddedBenefitUtilization]		= pd.OONAddedBenefitUtilization,    
		[OONAddedBenefitCostShare]			= pd.OONAddedBenefitCostShare               
	FROM #tmpSource1Table plans    
    CROSS APPLY fnGetMAReportSBInfo (@PlanYearID, plans.ForecastID) pd  
END

ELSE IF @ReportLevel = 4 AND @SourceID IN (3, 4, 5)  
BEGIN
	SELECT    
		[Plan Year]                         = pd.PlanYearID,    
        [Division]                          = plans.DivisionName,    
        [Region]                            = plans.HumanaRegionName,    
		[BPT Product Type]                  = pd.PlanTypeName,    
		[Contract Number]                   = pd.ContractNumber,    
		[PlanId]                            = pd.PlanID,    
        [SegmentId]                         = pd.SegmentId,   
		[Contract-PBP-Segment]              = pd.ContractNumber + '-' + LTRIM(pd.PlanID)+ '-' + LTRIM(pd.SegmentId),   
        [ForecastID]                        = pd.ForecastID,    
        [Added Benefit Name]                = pd.AddedBenefitName,    
		[IN Added Benefit Description]      = pd.INAddedBenefitDescription,    
		[IN Added Benefit Allowed]          = pd.INAddedBenefitAllowed,    
		[IN Added Benefit Utilization]      = pd.INAddedBenefitUtilization,    
		[IN Added Benefit Cost Share]       = pd.INAddedBenefitCostShare,    
		[OON Added Benefit Description]     = pd.OONAddedBenefitDescription,    
		[OON Added Benefit Allowed]         = pd.OONAddedBenefitAllowed,    
		[OON Added Benefit Utilization]     = pd.OONAddedBenefitUtilization,    
		[OON Added Benefit Cost Share]      = pd.OONAddedBenefitCostShare    
	FROM #tmpSourceTable plans    
    CROSS APPLY fnGetMAReportSBInfo (@PlanYearID, plans.ForecastID) pd  
END

ELSE IF @ReportLevel = 5 AND @SourceID = 1 --OSBs
BEGIN
	SELECT   
		[PlanYearID]						= pd.PlanYearID,    
		[ContractNumber]					= pd.ContractNumber, 
		[PBPID]								= pd.PBPID,    
        [SegmentId]							= pd.SegmentId,   
        [ForecastID]							= pd.ForecastID,  
		[PlanVersion]						= pd.PlanVersion,   
        [PlanPackageID]						= pd.PlanPackageID,
		[PackageIndex]						= pd.PackageIndex,     
        [Package Name]						= pd.Name,          
        [OSBDescription]					= pd.OSBDescription,    
        [TotalExpense]						= pd.TotalExpense,    
        [TotalGainLoss]						= pd.TotalGainLoss,    
        [PackageAllowedPMPM]				= pd.PackageAllowedPMPM,
		[PackageCostSharePMPM]				= pd.PackageCostSharePMPM,  
        [PackageNetPMPM]					= pd.PackageNetPMPM,    
        [OSBPremium]						= pd.OSBPremium    
    FROM #tmpSource1Table plans    
    CROSS APPLY dbo.fnGetMAReportOSBTotals (@PlanYearID, plans.ForecastID) pd   
    ORDER BY    
		pd.PlanYearID,    
        pd.ContractNumber,    
        pd.PBPID,    
        pd.SegmentId,    
        pd.ForecastID,    
        pd.PlanVersion,    
        pd.PlanPackageID,    
        pd.PackageIndex
END

ELSE IF @ReportLevel = 5 AND @SourceID = 2
BEGIN
	SELECT   
		[PlanYearID]						= pd.PlanYearID,    
		[ContractNumber]					= pd.ContractNumber, 
		[PBPID]								= pd.PlanID,    
        [SegmentId]							= pd.SegmentId,   
        [ForecastID]						= pd.ForecastID,  
		[PlanVersion]						= pd.PlanVersion,   
		[Package Name]						= pd.Name,    
		[PackageAllowedPMPM]				= pd.PackageAllowedPMPM,
		[PackageCostSharePMPM]				= pd.PackageCostSharePMPM,
		[Item Description in Package]		= pd.PricingComponentDescription,
		[AllowedUtilizationTypeID]			= pd.AllowedUtilizationTypeID,
		[AllowedUtilzationPer1000]			= pd.AllowedUtilzationPer1000,
		[AllowedAverageCost]				= pd.AllowedAverageCost,
		[MeasurementUnitCode]				= pd.MeasurementUnitCode,
		[EnrolleeCostShareUtilization]		= pd.EnrolleeCostShareUtilization,
		[EnrolleeAverageCostShare]			= pd.EnrolleeAverageCostShare,
		[AllowedPMPM]						= pd.AllowedPMPM,
		[CostSharePMPM]						= pd.CostSharePMPM,
		[NetPMPM]							= pd.NetPMPM    
	FROM #tmpSource1Table plans    
    CROSS APPLY dbo.fnGetMAReportOSBInfo (@PlanYearID, plans.ForecastID) pd
END

ELSE IF  @ReportLevel = 5 AND @SourceID IN (3, 4, 5) 
BEGIN
	SELECT    
		[Plan Year]                         = pd.PlanYearID,    
        [Division]                          = plans.DivisionName,    
        [Region]                            = plans.HumanaRegionName,    
		[BPT Product Type]                  = pd.PlanTypeName,    
		[Contract Number]                   = pd.ContractNumber,    
		[PlanId]                            = pd.PlanID,    
        [SegmentId]                         = pd.SegmentId,   
		[Contract-PBP-Segment]              = pd.ContractNumber + '-' + LTRIM(pd.PlanID)+ '-' + LTRIM(pd.SegmentId),
        [ForecastID]                        = pd.ForecastID,    
		[Package Name]                      = pd.Name,    
		[Package Allowed PMPM]              = pd.PackageAllowedPMPM,    
		[Package Cost Share PMPM]           = pd.PackageCostSharePMPM,    
		[Item Description in Package]       = pd.PricingComponentDescription,    
		[Allowed Utilization Type ID]       = pd.AllowedUtilizationTypeID,    
		[Allowed Utilization Per 1000]      = pd.AllowedUtilzationPer1000,    
		[Allowed Average Cost]              = pd.AllowedAverageCost,    
		[Measurement Unit Code]             = pd.MeasurementUnitCode,    
		[Enrollee Cost Share Utilization]   = pd.EnrolleeCostShareUtilization,    
		[Enrollee Average Cost Share]       = pd.EnrolleeAverageCostShare,    
		[Allowed PMPM]                      = pd.AllowedPMPM,    
		[Cost Share PMPM]                   = pd.CostSharePMPM,    
		[Net PMPM]                          = pd.NetPMPM,    
		[Admin PMPM]                        = pd.ExpensePMPM,    
		[Profit PMPM]                       = pd.Profit,    
		[Member Premium]                    = pd.MemberPremium,    
		[Projected MMs]                     = pd.ProjectedMemberMonths    
	FROM #tmpSourceTable plans    
    CROSS APPLY dbo.fnGetMAReportOSBInfo (@PlanYearID, plans.ForecastID) pd
END
ELSE
BEGIN
	SELECT 'Invalid ReportLevelID' Message
END
END
GO
