SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: spCopyPlanDetailTables  
--  
-- AUTHOR: Christian Cofie  
--  
-- CREATED DATE: 2007-Mar-20  
-- HEADER UPDATED: 2010-Sep-24  
--  
-- DESCRIPTION: Responsible for copying plan detail tables. This is used by spCopyPlan.  
--  
-- PARAMETERS:  
--  Input:  
--      @ForecastIDFrom  
--      @ForecastIDTo  
--      @MarketIDTo  
--      @IsSameMarket  
--      @IsIncludeRiskMembership  
--      @UserID  
-- Output:  
--      @ErrorMessage  
--  
-- TABLES:  
--  Read:  
--      LkpProductType  
--      LkpIntDemogIndicators  
--      LkpIntMARatingOption  
--      LkpIntPlanYear  
--      SavedMATrendRegionPlanTypeDetail  
--      SavedPlanBenefitDetail  
--      SavedPlanBidNoteDetail  
--      SavedPlanDFSummary  
--      SavedPlanDeductibleMOOPDetail  
--      SavedPlanDetail  
--      SavedPlanHeader  
--      SavedPlanINOONDistributionDetail  
--      SavedPlanMemberMonthDetail  
--      SavedPlanRiskFactorDetail  
--      SavedMarketInfo  
--		SavedPlanProjectedMedicaidRevenue
--		SAVEDclaimfactorheader
--		SavedPlanAddedBenefits
--		SavedIsCombinedDeductiblePlan
--		SavedPlanOOAMemberMonthDetail

--  Write:  
--      SavedPlanBenefitDetail  
--      SavedPlanBidNoteDetail  
--      SavedPlanDFSummary  
--      SavedPlanDeductibleMOOPDetail  
--      SavedPlanDetail  
--      SavedPlanINOONDistributionDetail  
--      SavedPlanMemberMonthDetail  
--      SavedPlanRiskFactorDetail 
--		SavedPlanProjectedMedicaidRevenue 
--		SavedPlanAddedBenefits
--		SavedIsCombinedDeductiblePlan
--		SavedPlanOOAMemberMonthDetail
--          
-- VIEWS:  
--  
-- FUNCTIONS:  
--      fnGetStateTerritoryCountyForMarket  
--  
-- STORED PROCS:  
--      spUpdateOrInsertStateCountyDetail  
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2007-Mar-20      1           Initial Version.                                                    Christian Cofie  
-- 2008-Feb-04      2           Updated BeginVersion --> PlanVersionBegin and PlanVersionEnd        Brian Lake  
--                                  in SavedPlanBidNoteDetail.  
-- 2008-Feb-05      3           Revised code to bring it into coding standards.                     Shannon Boykin  
-- 2008-Feb-18      4           Added IsDeSynchronized field to updates on SavedPlanBenefitDetail.  Debbie Cooper  
-- 2008-Feb-25      5           Improved the logic for copying each table.                          Tonya Cockrell  
-- 2008-Feb-29      6           Added BenefitOrdinalID to NOT EXISTS clause of the                  Tonya Cockrell  
--                                  INSERT that adds missing benefit categories.  
-- 2008-Apr-28      7           Added ClaimForecastID, IsCounty1, and EstimatedPlanBidComponent     Tonya Cockrell  
--                                  to SavedPlanDetail copy. Added PercentCoveredAllowed and  
--                                  PercentCoveredCostShare to SavedPlanBenefitDetail copy.  
-- 2008-Apr-29      8           Added MARatingOptionID to SavedPlanDetail copy.                     Tonya Cockrell  
-- 2008-May-01      9           NULL records for L2 benefits are only created when                  Tonya Cockrell  
--                                  IsLevel2Consecutive=1.  Added IsUseISARFactor to  
--                                  SavedPlanDetail copy.  
-- 2008-May-05      10          Bid notes are not copied at all unless the from and to years are    Tonya Cockrell  
--                                  the same.   
-- 2008-May-07      11          Only BenefitCategoryIDs that have IsUsed = 1 are copied.            Tonya Cockrell  
-- 2008-Sep-11      12          Added line for SavedPlanStateCountyDetail                           Brian Lake  
-- 2008-Sep-19      13          Added @UserID to the list of parameters and replaced all            Shannon Boykin  
--                                  occurrences of SUSER_SNAME with @UserID.  
-- 2008-Nov-18  14          Modified to exclude discontinued benefits from being                Aleksey Titievsky  
--                                  copied over.  Also added copying over Combined amounts   
--                                  for SavedPlanDeductibleMOOPDetail table.  
-- 2009-Mar-17      15          Data types.                                                         Sandy Ellis  
-- 2009-Apr-06      16          Updated CostAndUseID and TrendID copying to use new 2010            Tonya Cockrell  
--                                  tables and views.  
-- 2010-Jan-29  17         Added IN/OON Distribution Detail table copy             Nick Skeen  
-- 2010-Apr-05  18         Changed to prevent copying ClaimForecastID             Casey Sanders  
-- 2010-Sep-24      19          Removed @PlanYearIDTo, @PlanVersionTo from input. Changed           Joe Casey  
--                                  several tables calls to work until new design.  
-- 2010-Dec-30  20   Added @FilePath Parameter to account for added columns in   Craig Wright  
--         SavedPlanRiskFactorDetail and SavedPlanMemberMonthDetail tables    
-- 2011-Jan-25  21   Removed @FilePath as parameter          Joe Casey  
-- 2011-Mar-07      22          Added Part B Deductible                                             Michael Siekerka  
-- 2011-Apr-12  22   Ensured that included/excluded counties are based on original plan  Sule Dauda  
--        when is the same market   
-- 2011-Jun-02  23   Replaced LkpIntPlanYear with dbo.fnGetBidYear()      Bobby Jaegers  
-- 2011-Jun-14  24   Changed @PlanYearID to return SMALLINT instead of INT    Bobby Jaegers  
-- 2013-Jan-25      25          Set @TrendIDTo to 1 when IS NULL                                    Lindsay Allen 
-- 2013-Oct-07      26          Included Join on Segment ID                                         Manisha Tyagi 
-- 2015-Jul-03		3			Added columns UpdatedBy,UpdatedDateTime for table SavedPlanBenefitDetail			Deepali Mittal
-- 2018-May-10		28			Changed LkpExtCMSPlanType to LkpProductType									Jordan Purdue
-- 2018-Aug-27      29          Removed MarketId filter from fnGetStateTerritoryCountyForMarket     Apoorva Nasa
-- 2018-Aug-28      30          Commented code related spUpdateOrInsertStateCountyDetail            Manisha Tyagi
-- 2019-Feb-18		31			Insert into SavedPlanStateCountyDetail a default county to allow	Alex Beruscha
--								MAA-UI SAM sync
-- 2019-Jul-08      32          Made changes for market, region, division table.					Kritika Singh
-- 2020-Jan-02		33			[20.02] Added SavedPlanAddedBenefits, 								AOB
--								SavedPlanOptionalPackageDetail, SavedPlanOOAMemberMonthDetail,  
--								ClaimForecastID, SavedIsCombinedDeductiblePlan, CalcPlanAdminBlend,
--								SavedPlanProjectedMedicaidRevenue		
-- 2020-Jan-13      34          [20.02] Added DedApplies fields                                     Keith Galloway
--2020-Nov-02		35			Added Logs															Deepali
--2020-Dec-04		36			Added Nolock															Ramandeep Saini
--2020-Dec-05		37			Changes logic to insert into temp first								Deepali
--2020-Dec-07       38          Remove Cartesian join and SavedPlanHeader reference                 Brent Osantowski
-- 2021-May-27		21.08		Update for Value Based Care Phase 1 changes							Alex Beruscha
-- 2022-Jun-24		40			Updated fields to fit new structure of table SavedPlanBenefitDetail,
--								removed updating SavedPlanDeductibleMOOPDetail and 
--								SavedIsCombinedDeductiblePlan; for SavedPlanBenefitDetail copying 
--								BenefitOptionID = 1 to match the spAppCopyPlanAndScenario process &
--								pulling the new PlanInfoID from SavedForecastSetup					Aleksandar Dimitrijevic
--2023-Jan-26       41         Adding a where Clause to only pull BY data from SavedForecastSetup table        Abraham Ndabian
-- 2023-Sep-22		42			Added IsOverride field	to savedplanaddedbenefit					Adam Gilbert
--2024-FEB-07		47			Copy benefits for live option instead of only option 1. BPR#108		Adam Gilbert
--2024-SEP-24		48			Add columns to calcplanadminblend									Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------  
CREATE PROCEDURE [dbo].[spCopyPlanDetailTables]
 @ForecastIDFrom INT,  
    @ForecastIDTo INT,  
    @MarketIDTo SMALLINT,  
    @IsSameMarket BIT,  
    @IsIncludeRiskMembership BIT,  
    @UserID CHAR(13),  
    @ErrorMessage VARCHAR(250) OUTPUT  
AS  
    SET NOCOUNT ON  
	 Declare @ErrorMessageFromBackend varchar(max) ;
	  DECLARE @ErrorSeverity INT;      
   DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000);     
   DECLARE @errSrc varchar(max),  @currentdate datetime=getdate() ;
    --These will be used as constants.  Values come from LkpIntMARatingOption.  
    DECLARE @Experience TINYINT  
    DECLARE @Manual TINYINT  
    SET @Experience = 1  
    SET @Manual = 2  

    DECLARE @LocalError INT  

    DECLARE @PlanYearID SMALLINT  
    SET @PlanYearID = dbo.fnGetBidYear()  

    DECLARE @LastUpdate DATETIME  
    SET @LastUpdate = GETDATE()  

    --Get the plan type, contract number, and plan ID, which are needed below.  
    DECLARE @PlanTypeID TINYINT  
    DECLARE @IsRPPO BIT  
    DECLARE @ContractNumber CHAR(5)  
    DECLARE @PlanID CHAR(3)
    DECLARE @SegmentId CHAR(3)  --declare SegmentId  
    SELECT  
        @PlanTypeID = spi.ProductTypeID,  
        @IsRPPO  
            = CASE pt.ProductType  
                WHEN 'RPPO'  
                    THEN 1  
                ELSE 0  
            END,  
        @ContractNumber = LEFT(spi.CPS,5),  
        @PlanID = SUBSTRING(spi.CPS,7,3),
        @SegmentId = RIGHT(spi.CPS,3) --Including SegmentId

    FROM dbo.SavedForecastSetup sfs  with (nolock) 
	INNER JOIN dbo.SavedPlanInfo spi WITH (NOLOCK) ON spi.PlanInfoID = sfs.PlanInfoID
    LEFT JOIN LkpProductType  pt with (nolock)  
        ON spi.ProductTypeID = pt.ProductTypeID 
    WHERE sfs.ForecastID = @ForecastIDTo  AND sfs.PlanYear= dbo.fnGetBidYear() -- added this filter to restrict data to new ForecastIDs values for new BY, by Abe 1/26/23

    --Get the region being copied into.  
    DECLARE @HumanaRegionIDTo TINYINT  
    SELECT @HumanaRegionIDTo = ActuarialRegionID  
    FROM SavedMarketInfo  with (nolock) 
    WHERE ActuarialMarketID = @MarketIDTo  

    --Loop through all rating options for the plan.  There should only be 2.  
    DECLARE @MARatingOptionID TINYINT  
    SET @MARatingOptionID = 0  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanDetail--------------------------------------------------------------------------------------------------------  
    IF OBJECT_ID('tempdb.dbo.#SCFH') IS NOT NULL   DROP TABLE  #SCFH
	--get claimforecastIDs created in spAppCopyPlanAndScenario
	select DISTINCT 
	[description], 
	MAX(ClaimForecastID) OVER (PARTITION BY description) ClaimForecastID
	INTO #SCFH
	FROM SAVEDclaimfactorheader with (nolock) 
	WHERE ForecastID = @ForecastIDFrom

	WHILE EXISTS (SELECT 1 FROM SavedPlanDetail WHERE ForecastID = @ForecastIDFrom AND MARatingOptionID > @MARatingOptionID)  
        BEGIN  
            --Get the existing plan's cost & use, trend, contract projection IDs.  
            DECLARE @TrendIDFrom INT  
            DECLARE @RatebookIDFrom TINYINT  
            DECLARE @ClaimForecastIDFrom INT  

            SELECT TOP 1  
                @MARatingOptionID = MARatingOptionID,  
                @TrendIDFrom = TrendID,  
                @RatebookIDFrom = RatebookID,  
                @ClaimForecastIDFrom = ClaimForecastID  
            FROM SavedPlanDetail  with (nolock) 
            WHERE ForecastID = @ForecastIDFrom  
                AND MARatingOptionID > @MARatingOptionID  
            ORDER BY MARatingOptionID ASC  

            --The trend ID can only be copied forward if the cost & use is being copied.  
            --If trend ID is null will be set to 1.  
            DECLARE @TrendIDTo INT  
            SELECT @TrendIDTo = TrendID  
            FROM SavedMATrendRegionPlanTypeDetail   with (nolock) 
            WHERE HumanaRegionID = @HumanaRegionIDTo  
                AND PlanTypeID = @PlanTypeID  
                AND TrendID = @TrendIDFrom  

            --Get the ratebook ID based on the copy-to year and the plan type. If there is more than one option,  
            --get the minimum ID that meets the criteria.  
            DECLARE @RatebookIDTo TINYINT  
            SET @RatebookIDTo = @RatebookIDFrom  

            BEGIN TRY   
                INSERT INTO SavedPlanDetail  
                SELECT  
                    @PlanYearID,  
                    @ForecastIDTo,  
                    MARatingOptionID,  
                    @RatebookIDTo,  
                    CASE  
                        WHEN @TrendIDTo IS NULL  
                            THEN 1  
                        ELSE @TrendIDTo  
                    END,  
                    NULL, --ContractProjectionID is not used for 2010.  
                    cfh.ClaimForecastID, --already copied to cfh
                    NULL, --IsCounty1  
                    CASE @IsRPPO  
                        WHEN 1  
                            THEN p.EstimatedPlanBidComponent  
                        ELSE NULL  
                    END,  
                    CASE @IsRPPO  
                        WHEN 1  
                            THEN p.IsUseISARFactor  
                        ELSE 0  
                    END,  
                    @UserID,  
                    @LastUpdate  
                FROM SavedPlanDetail p with (nolock)  
				LEFT JOIN #SCFH cfh
					ON p.MARatingOptionID = CASE [description] WHEN 'Experience' THEN 1 WHEN 'Manual' THEN 2 ELSE 0 end
                WHERE p.ForecastID = @ForecastIDFrom  
                    AND MARatingOptionID = @MARatingOptionID  
            END TRY  

            BEGIN CATCH  
                --Don't continue if an error happened.  
                SELECT @ErrorMessage = 'Could not copy ' + LOWER(MARatingOption) + ' detail data.'  
                FROM LkpIntMARatingOption  with (nolock) 
                WHERE MARatingOptionID = @MARatingOptionID  ;

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID    


                RETURN  
            END CATCH  
        END  
IF OBJECT_ID('tempdb.dbo.#SCFH') IS NOT NULL   DROP TABLE  #SCFH
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanDFSummary-----------------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO SavedPlanDFSummary 
        SELECT    
            @ForecastIDTo,  
            MARatingOptionID,  
            PlanInfoID,
			@UserID,  
            @LastUpdate 
        FROM SavedPlanDFSummary  with (nolock) 
        WHERE ForecastID = @ForecastIDFrom  
    END TRY  

    BEGIN CATCH  
        SET @ErrorMessage = 'Could not copy Experience and/or Manual Cuts.' ;

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID  


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanMemberMonthDetail---------------------------------------------------------------------------------------------  
    BEGIN TRY  
        --Member Months  
        --Copy the member months, but only for counties that are valid for both the existing plan and the copy-to plan.  
        --Use 0 for an counties that weren't listed in the existing plan.  Also use 0 everywhere if the user has  
        --specified not to copy the member months.  
       -- INSERT INTO SavedPlanMemberMonthDetail  
        SELECT  
            @PlanYearID PlanYearID,  
            @ForecastIDTo ForecastID,  
            mmd.StateTerritoryID StateTerritoryID,  
            mmd.CountyCode CountyCode,  
            mmd.DemogIndicator DemogIndicator,  
            CASE  
                WHEN mmd.MemberMonths IS NULL  
                  OR @IsIncludeRiskMembership = 0  
                    THEN 0  
                ELSE mmd.MemberMonths  
            END MemberMonths,  
			mmd.FilePath FilePath,  
            @UserID LastUpdateByID,  
            @LastUpdate LastUpdateDateTime
			 into #tempSavedPlanMemberMonthDetail 
        FROM SavedPlanMemberMonthDetail mmd WITH (nolock) 
            where  mmd.ForecastID = @ForecastIDFrom  

			Insert into SavedPlanMemberMonthDetail
			Select * 			
			from #tempSavedPlanMemberMonthDetail

    END TRY  

    BEGIN CATCH  
        SET @ErrorMessage = 'Could not copy member month data.'  ;

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID    


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanOOAMemberMonthDetail-------------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO dbo.SavedPlanOOAMemberMonthDetail
        SELECT  
		@PlanYearID,  
		@ForecastIDTo,  
		MemberMonths,
		FilePath,
		@UserID,
		@LastUpdate			  
        FROM dbo.SavedPlanOOAMemberMonthDetail  with (nolock) 
        WHERE ForecastID = @ForecastIDFrom  
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy OOA membership data.'  ;


   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID     


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanRiskFactorDetail----------------------------------------------------------------------------------------------  
    BEGIN TRY  
        --Copy the risk factors, but only for counties that are valid for both the existing plan and the copy-to plan.  
        --Use 0 for an counties that weren't listed in the existing plan.  Also use 0 everywhere if the user has  
        --specified not to copy the risk factors.  Separate inserts for IsExperience = 0 and 1.  
       -- INSERT INTO SavedPlanRiskFactorDetail  
        SELECT  
            @PlanYearID PlanYearID,  
            @ForecastIDTo ForecastID,  
            rfd.StateTerritoryID StateTerritoryID,  
            rfd.CountyCode CountyCode,  
            rfd.DemogIndicator DemogIndicator,  
            rfd.IsExperience,  
            CASE  
                WHEN rfd.RiskFactor IS NULL  
                  OR @IsIncludeRiskMembership = 0  
                    THEN 0  
                ELSE rfd.RiskFactor  
            END RiskFactor,  
			rfd.FilePath FilePath,  
            @UserID LastUpdateByID,  
            @LastUpdate LastUpdateDateTime  
			into #tempSavedPlanRiskFactorDetail1
        FROM SavedPlanRiskFactorDetail rfd  with(nolock) 
            where rfd.ForecastID = @ForecastIDFrom  


			Insert into  SavedPlanRiskFactorDetail
			select *  from #tempSavedPlanRiskFactorDetail1
			OPTION (MAXDOP 1)

    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy risk factor data.' ;


   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID    


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanBidNoteDetail-------------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO SavedPlanBidNoteDetail  
        SELECT  
            @PlanYearID,  
            @ForecastIDTo,  
            BidNoteID,  
            IsDefaultBidNote,  
            BidNoteOverride,  
            0, --IsHidden  
            @UserID,  
            @LastUpdate  
        FROM SavedPlanBidNoteDetail  with(nolock)
        WHERE ForecastID = @ForecastIDFrom 
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy bid note data.' ;


   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID     


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanBenefitDetail-------------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO dbo.SavedPlanBenefitDetail  
		SELECT  
            @PlanYearID,  
            sfs.ForecastID,
			sfs.PlanInfoID,
			1 AS BenefitOptionID,
            IsBenefitYearCurrentYear, 
			IsLiveIndex = CASE IsBenefitYearCurrentYear
							   WHEN 1 THEN 0
							   ELSE 1
						  END,
            BenefitCategoryID,  
            BenefitOrdinalID,
			INBundleID,
            INBenefitTypeID, 
            INDayRangeBegin,  
            INDayRangeEnd,    
            INBenefitValue, 
            INDedApplies,
			OONBundleID,
            OONBenefitTypeID, 
            OONDayRangeBegin,  
            OONDayRangeEnd,    
            OONBenefitValue, 
            OONDedApplies, 
            PercentCoveredAllowed,  
            PercentCoveredCostShare,
            @UserID,  
            @LastUpdate  
        FROM dbo.SavedPlanBenefitDetail  spd with(nolock)
		CROSS JOIN dbo.SavedForecastSetup sfs
        WHERE spd.ForecastID = @ForecastIDFrom
		  AND sfs.ForecastID = @ForecastIDTo
		  AND ( (spd.BenefitOptionID = 1 AND IsBenefitYearCurrentYear = 1) --current year benefit records
				OR 
				(spd.IsLiveIndex=1)    --active benefit option records
			) 
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy benefit data.' ;

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID    


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanINOONDistributionDetail---------------------------------------------------------------------------------------  
    BEGIN TRY  
     INSERT INTO SavedPlanINOONDistributionDetail  
     SELECT  
            @PlanYearID,  
            @ForecastIDTo,  
      BenefitCategoryID,  
      INDistributionPercent,  
      OONDistributionPercent,  
      INCostFactor,  
      OONCostFactor,  
      @UserID,  
      @LastUpdate  
  FROM SavedPlanINOONDistributionDetail with(nolock) 
  WHERE ForecastID = @ForecastIDFrom  
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not add new distribution detail data.'  ;


   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID      


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  

-------------------------------------------------------------------------------------------------------------------------  
--spUpdateOrInsertStateCountyDetail--------------------------------------------------------------------------------------  
    BEGIN TRY  
  ----Insert counties into SavedPlanStateCountyDetail for the new plan.  
   BEGIN  
    INSERT INTO SavedPlanStateCountyDetail  
    SELECT  
     @PlanYearID,  
     @ForecastIDTo,  
     StateTerritoryID,  
     CountyCode,  
     IsCountyExcludedFromBPTOutput,  
     @UserID,  
     @LastUpdate  
    FROM SavedPlanStateCountyDetail  with(nolock)
    WHERE ForecastID = @ForecastIDFrom  
   END    

    END TRY  

    BEGIN CATCH  
        SET @ErrorMessage = 'Could not insert state/county data.' ;

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID    


        RETURN  
   END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanAddedBenefits------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO dbo.SavedPlanAddedBenefits
        (PlanYearID,  
		ForecastID,  
		AddedBenefitTypeID,
		AddedBenefitName,
		INAddedBenefitDescription,
		INAddedBenefitAllowed,
		INAddedBenefitUtilization,
		INAddedBenefitCostShare,
		OONAddedBenefitDescription,
		OONAddedBenefitAllowed,
		OONAddedBenefitUtilization,
		OONAddedBenefitCostShare,
		BidServiceCatID,
		IsValueAdded,
		IsNetwork,
		IsHidden,
		LastUpdateByID,  
		LastUpdateDateTime,
        IsOverride)
        SELECT  
		@PlanYearID,  
		@ForecastIDTo,  
		AddedBenefitTypeID,
		AddedBenefitName,
		INAddedBenefitDescription,
		INAddedBenefitAllowed,
		INAddedBenefitUtilization,
		INAddedBenefitCostShare,
		OONAddedBenefitDescription,
		OONAddedBenefitAllowed,
		OONAddedBenefitUtilization,
		OONAddedBenefitCostShare,
		BidServiceCatID,
		IsValueAdded,
		IsNetwork,
		IsHidden,
		@UserID,  
		@LastUpdate,
        IsOverride
        FROM dbo.SavedPlanAddedBenefits with(nolock)
        WHERE ForecastID = @ForecastIDFrom  
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy MSB/RP data.';

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID     


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanOptionalPackageDetail------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO dbo.SavedPlanOptionalPackageDetail
        SELECT 
		@PlanYearID,  
		@ForecastIDTo,  
		PlanPackageID,
        PackageIndex,
        IsHidden,
		@UserID,  
		@LastUpdate
	    FROM dbo.SavedPlanOptionalPackageDetail with(nolock)
        WHERE ForecastID = @ForecastIDFrom  
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy OSB data.' ;

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID     


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanOptionalPackageDetail------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO dbo.CalcPlanAdminBlend
		(
		PlanYearID,
        ForecastID,
        MAMarketingAdminPMPM,
        MADirectAdminPMPM,
        MAIndirectAdminPMPM,
        MAQualityAdminPMPM,
        MATaxesAndFeesAdminPMPM,
		MARelatedPartyCoreNBEPMPM,
        PDMarketingAdminPMPM,
        PDDirectAdminPMPM,
        PDIndirectAdminPMPM,
        PDQualityAdminPMPM,
        PDTaxesAndFeesAdminPMPM,
		PDRelatedPartyCoreNBEPMPM,
        LastUpdateByID,
        LastUpdateDateTime
		)
		SELECT 
			@PlanYearID,  
			@ForecastIDTo,  
			MAMarketingAdminPMPM,
            MADirectAdminPMPM,
            MAIndirectAdminPMPM,
            MAQualityAdminPMPM,
            MATaxesAndFeesAdminPMPM,
			MARelatedPartyCoreNBEPMPM,
            PDMarketingAdminPMPM,
            PDDirectAdminPMPM,
            PDIndirectAdminPMPM,
            PDQualityAdminPMPM,
            PDTaxesAndFeesAdminPMPM,
			PDRelatedPartyCoreNBEPMPM,
			@UserID,  
			@LastUpdate
		FROM dbo.CalcPlanAdminBlend with(nolock)
        WHERE ForecastID = @ForecastIDFrom  
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy OSB data.'  ;

   select @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =getdate()    
    Select @ErrorMessageFromBackend = ERROR_Message()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)   



   ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID    


        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
--SavedPlanProjectedMedicaidRevenue------------------------------------------------------------------------------------------  
    BEGIN TRY  
        INSERT INTO dbo.SavedPlanProjectedMedicaidRevenue
		SELECT 
			@ForecastIDTo,  
            MedicaidProjectedRevenue,
            MedicaidProjectedCostBenefitExpense,
            MedicaidProjectedCostNonBenefitExpense,
			@UserID,  
			@LastUpdate
		FROM dbo.SavedPlanProjectedMedicaidRevenue WITH(NOLOCK)
        WHERE ForecastID = @ForecastIDFrom  
    END TRY  

    BEGIN CATCH  
        --Don't continue if an error happened.  
        SET @ErrorMessage = 'Could not copy WS4 projected Medicaid data.'  ;


   SELECT @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =GETDATE()    
    SELECT @ErrorMessageFromBackend = ERROR_MESSAGE()
   SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState

 AS VARCHAR)   



   ---Insert into app log for logging error------------------    
   EXEC spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID  

        RETURN  
    END CATCH  
-------------------------------------------------------------------------------------------------------------------------  
    --If we get this far, everything succeeded, so there is no error message.  
    SET @ErrorMessage = NULL
GO


