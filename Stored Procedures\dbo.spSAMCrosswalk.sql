SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  Satyam <PERSON>al      
-- Create date: 12-10-2019
-- Description:  Get SAM Crosswalks data
--      
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES:    
-- Read:      
-- Write:      
-- VIEWS:  vwSAMCrosswalks    
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE         VERSION   CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2019-Dec-10   1        Initial version.											 Satyam Singhal  
-- ----------------------------------------------------------------------------------------------------------------------  

CREATE PROCEDURE [dbo].[spSAMCrosswalk] 
	@RollupId VARCHAR(MAX)=NULL,
    @RegionID VARCHAR(MAX)=NULL
AS
    BEGIN
        SET NOCOUNT ON					
		
		SELECT	BidYear,
				SSStateCountyCD,
				CountyName ,
				StateCode,
				PrYearCPS,
				BaseYearCPS,
				CurrentYearCPS,
				BidYearCPS ,
				s.ServiceAreaOptionID,
				ServiceAreaOptionName,
				ServiceAreaOptionDescription,
				CurrentYearRenewalType,
				BidYearRenewalType,
				BidYearMapID
			
		FROM dbo.vwSAMCrosswalks s
		INNER JOIN	dbo.SavedPlanInfo p ON p.PlanInfoID = s.BidYearPlanInfoID
		INNER JOIN dbo.SavedPlanInfo b ON b.PlanInfoID = s.BaseYearPlanInfoID
		INNER JOIN dbo.SavedMarketInfo m ON m.ActuarialMarketID = p.ActuarialMarketID
		INNER JOIN dbo.SavedForecastSetup f ON f.PlanInfoID = s.BidYearPlanInfoID AND f.ServiceAreaOptionID = s.ServiceAreaOptionID 
		INNER JOIN dbo.SavedRollupForecastMap rf ON rf.ForecastID = f.ForecastID
		INNER JOIN dbo.SavedRollupInfo r ON r.RollupID = rf.RollupID

		WHERE ((@RegionID IS NULL) OR m.ActuarialRegionID  IN (SELECT CAST(value AS SMALLINT) FROM dbo.fnStringSplit(@RegionID,',')))
		--AND
		--  s.BaseYearPlanInfoID <> 0 
		AND ((@RollupId IS NULL) OR r.RollupID IN (SELECT CAST(value AS SMALLINT) FROM dbo.fnStringSplit(@RollupId,',')))


		
		
		
        AND s.IsActive = 1
 
 END
GO
