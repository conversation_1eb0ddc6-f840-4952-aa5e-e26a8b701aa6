SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------        
-- Stored Procedure NAME: [spAppInsertSavedPlanProjectedMedicaidRevenue]        
--        
-- AUTHOR: Apoorva Nasa
-- 
-- CREATED DATE: 2018-Feb-07        
--        
-- DESCRIPTION: Stored Procedure is responsible for saving data in SavedPlanProjectedMedicaidRevenue     
--        
-- PARAMETERS:        
-- Input: 
--   @ForecastID       
--   @MedicaidProjectedRevenue 
--   @MedicaidProjectedCostBenefitExpense
--   @MedicaidProjectedCostNonBenefitExpense
--   @LastUpdateByID
--   @LastUpdateDateTime

        
-- Output:        
--        
-- TABLES:         
-- Read:        
-- SavedPlanProjectedMedicaidRevenue      
-- Write:        
-- SavedPlanProjectedMedicaidRevenue       
-- VIEWS:        
--        
-- FUNCTIONS:        
--         
-- STORED PROCS:        
--        
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE    VERSION  CHANGES MADE                   DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Feb-07  1   Initial Version               Apoorva Nasa      

-- ----------------------------------------------------------------------------------------------------------------------        
CREATE PROCEDURE [dbo].[spAppInsertSavedPlanProjectedMedicaidRevenue]        
    @ForecastID INT,
	@MedicaidProjectedRevenue DECIMAL(14,6),
	@MedicaidProjectedCostBenefitExpense DECIMAL(14,6) ,
	@MedicaidProjectedCostNonBenefitExpense  DECIMAL(14,6),  
	@LastUpdateByID  char(13),
	@LastUpdateDateTime datetime
      
AS        
        
BEGIN 

    
IF ( EXISTS ( SELECT    1  
                      FROM      dbo.SavedPlanProjectedMedicaidRevenue  
                      WHERE     ForecastID = @ForecastID  
                                ) )                 
            BEGIN                        
					Update dbo.SavedPlanProjectedMedicaidRevenue
					set 
					MedicaidProjectedRevenue= @MedicaidProjectedRevenue,
					MedicaidProjectedCostBenefitExpense = @MedicaidProjectedCostBenefitExpense,
					MedicaidProjectedCostNonBenefitExpense=@MedicaidProjectedCostNonBenefitExpense,
					
					LastUpdateByID=@LastUpdateByID,
					LastUpdateDateTime=@LastUpdateDateTime
					where ForecastID=  @ForecastID 
                              
            END 
            ELSE    
        BEGIN                      
  
            INSERT  INTO [dbo].[SavedPlanProjectedMedicaidRevenue] 
                        (
                        
						ForecastID,
						MedicaidProjectedRevenue,
						MedicaidProjectedCostBenefitExpense,
						MedicaidProjectedCostNonBenefitExpense,
						
						LastUpdateByID,
						LastUpdateDateTime)  
            VALUES      (
						
						@ForecastID,
						@MedicaidProjectedRevenue,
						@MedicaidProjectedCostBenefitExpense,
						@MedicaidProjectedCostNonBenefitExpense,
						
						@LastUpdateByID,
						@LastUpdateDateTime)                      
        END                      

END
GO
