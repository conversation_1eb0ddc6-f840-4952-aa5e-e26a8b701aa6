SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-----------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: spPullSCTDataForPREP
-- 
-- CREATOR: <PERSON> Nielsen
--  
-- CREATED DATE: 2019-Oct-09
--  
-- DESCRIPTION: Stored Procedure to upload SCT Datamart Values for use in the PREP
--  
-- PARAMETERS: 
--  
-- INPUT: 
--
-- OUTPUT:
--
-- TABLES: 
--
-- READ: [dbo].[SCT_MAForecast], [dbo].[SCT_CalcMAMEMandRS], [dbo].[SCT_vwPartD], [dbo].[SCT_CurrentToolVersion]
--       In 2021-Sep above replaced [dbo].[SCT].[vwMAActual], [dbo].[SCT].[vwMAProj], [dbo].[SCT].[vwPDActual], [dbo].[SCT].[vwPDProj]
--
-- WRITE:   W_FEM_MAProjUpload, W_FEM_PDProjUpload
--  
-- VIEWS: 
--  
-- FUNCTIONS: 
--  
-- STORED PROCS:
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE			VERSION		CHANGES MADE														DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2019-Oct-09		1		Initial Version														Craig Nielsen
-- 2019-Dec-15		2		Bring in PlanList to reduce run-time								Craig Nielsen
-- 2019-Jan-30		3		Bring in ESRD Subsidy												Craig Nielsen
-- 2020-Feb-12		4		Pull Correct Field for [..Other DIR..] from vwPDProj & vwPDActual	Bob Knadler
-- 2020-May-06		5		Change calc pulled from vwMAActual to revise forced match			Bob Knadler
--	             				  of [SCT Total Claims] in PREP Fin View to [TotalMAMetLiability]
-- 2020-May-06		6		Added section to Delete old records from W_FEM_MAProjUpload and 	Bob Knadler
--							  W_FEM_PDProjUpload corresponding to anything being inserted
-- 2020-Jul-30      7       Separate out Part B Rx Rebate Adjustments from [PBRx DIR] column    Abraham Ndabian
-- 2020-Nov-02      8       Removed commented code --		SELECT * FROM [sct].[dbo].[vwMAProj] Satyam Singhal
--							as part of sonarcube fix
-- 2021-Sep-16		9		Revised to pull from MAForecast instead of legacy views				Isaac Ortiz
--  2021-Nov-05		10			Added SonarQube Fixes											Deepali		Mittal	
--	2021-Dec-14		11		Limited pull from vwPartD to the current iteration					Jordan Lake						
-- 2021-Dec-16		12		Needed two additional PD fields for PD in PREP work                 Bob Knadler	
--                          Added EXEC of sp [CW_SavePDPlanDetail]
-- 2022-Jan-13		13		Added FCF And Cost Share adjustments to MAProj pull					Jordan Lake
-- 2022-Feb-09		14		Change ref from SCT2 to SCT         								Shivgopal
-- 2022-Oct-13		15      Add @XVariables, (NOLOCK), @Quarter variable, 						Phani Adduri
--							Release Temp table memory
-- 2023-Aug-25		16		Add PDAdjustments 3-5 to get correct Tot PD Net Claims;              Bob Knadler	
--                            Since PREP isn't using the Dispensing Rate fields, capture NULLS 
--                            to avoid overflows
-- 2024-May-01		17		Added Bid Risk Score and MSB Adj to MAProj pull                      Bob Knadler	
--                            after restructuring table W_FEM_MAProjUpload 
--                             Note: Bid RS for bid year, but =Fin RS for base and current
--                           No longer having separate base year pulls from SCT_MAForecaset, 
--                             but including base year in the exisitng pull for current and bid years
--                           Will use temp table to check pull for records first and then
--                              perform deletion and insertion or message out
-- 2024-Jun-05		18		Discovered still need to back into Other Rev Adjmts                  Bob Knadler	
--                            and Other Claims Adjmts for base year
-- 2024-Aug-24      19      Added Plan Benchmark for Benchmark Rebalancing                       Devin Goodman
-- 2024-Aug-19		20		Added SCT MLR Rebate Adjustment Specific Field						 Charlie Brockhaus
-- ---------------------------------------------------------------------------------------------------------------------- 


CREATE PROCEDURE [dbo].[spPullSCTDataForPREP]
(
    @CPBPList VARCHAR(MAX),
    @UserID CHAR(7),
    @BidYear INT
)

AS

--DECLARE @CPBPList VARCHAR(MAX) = 'H5216-092-000,H5216-140-000'
--DECLARE @UserID CHAR(7) = 'RJK2133'
--DECLARE @BidYear INT = 2025
DECLARE @BaseYear INT, 
@XBidYear INT;
SET @XBidYear = @BidYear;
SET @BaseYear = @XBidYear - 2;

BEGIN

    SET NOCOUNT ON;


      --Put Plan List in temp table                                 							
       IF ( SELECT OBJECT_ID('tempdb..#PlanList') ) IS NOT NULL     							
                  DROP TABLE #PlanList                              							

       CREATE TABLE #PlanList                                       							
                  (                                                 							
                    [CPS] [varchar](13) NULL                        							
                  )                                                 							

      INSERT  INTO #PlanList
      SELECT  Value                                                 							
        FROM    dbo.fnSTringSPlit(@CPBPList, ',')                   							


    -- BK revision #6


    DECLARE @Quarter VARCHAR(6) = CONCAT(

                (





                    SELECT Year FROM dbo.SCT_CurrentToolVersion (NOLOCK)
                ),
                'Q',
                (
                    SELECT Quarter FROM dbo.SCT_CurrentToolVersion (NOLOCK)
                ));


--Create temp tables to temporarily house SCT data pull
IF ( SELECT OBJECT_ID('tempdb..#SCTMAForecast') ) IS NOT NULL
  BEGIN
    DROP TABLE #SCTMAForecast
  END

IF ( SELECT OBJECT_ID('tempdb..#SCTvwPD') ) IS NOT NULL
  BEGIN
    DROP TABLE #SCTvwPD
  END


--mirorring [W_FEM_MAProjUpload]
CREATE TABLE #SCTMAForecast (   
	[Year] [int] NOT NULL,
	[Bid Year Contract-PBP] [char](13) NOT NULL,
	[ContractPBPSegmentID] [char](13) NOT NULL,
	[Total Member Months] [decimal](18, 8) NULL,
	[ESRD Hospice Member Months] [decimal](18, 8) NULL,
	[Non E/H + OOA Member Months] [decimal](18, 8) NULL,
	[Weighted Bid Risk Score] [decimal](18, 8) NULL,
	[Weighted Avg County Rate @ 1.0] [decimal](18, 8) NULL,
	[Weighted Projected Risk Score (Mbr Wtd)] [decimal](18, 8) NULL,
	[Weighted ESRD/Hosp Revenue] [decimal](18, 8) NULL,
	[Weighted Risk-Adjusted Revenue] [decimal](18, 8) NULL,
--#19 DG 8/9/24
	[Weighted Plan Benchmark] [decimal](18, 8) NULL,
	
	[Weighted Risk-adj A/B Revenue] [decimal](18, 8) NULL,
	[Weighted Reduced CS Rebate] [decimal](18, 8) NULL,
	[Weighted Reduced CS Prem] [decimal](18, 8) NULL,
	[Weighted Mand Supp Rebate] [decimal](18, 8) NULL,
	[Weighted Mand Supp Prem] [decimal](18, 8) NULL,
	[Weighted MA Uncollected Member Premium] [decimal](18, 8) NULL,
	[Weighted MA Sequestration - Revenue] [decimal](18, 8) NULL,
	[Weighted MSP] [decimal](18, 8) NULL,
	[Weighted Other Revenue Adjs] [decimal](18, 8) NULL,

--#20 CB 8/19/2024 [Add New SCT MLR Field]
    [Weighted MLR Rebate Adjs] [decimal](18, 8) NULL,

	[Weighted Basic Member Premium] [decimal](18, 8) NULL,
	[Weighted MA Admin: Insurer Fees] [decimal](18, 8) NULL,
	[Weighted Risk Provider Offsets] [decimal](18, 8) NULL,
	[Wtd. Medicare Secondary Payer] [decimal](18, 8) NULL,
	[Weighted ESRD/Hosp Liability] [decimal](18, 8) NULL,
	[Weighted Total MA Net (Covered)] [decimal](18, 8) NULL,
	[Weighted Total MA Net (Non Covered)] [decimal](18, 8) NULL,
	[Weighted Financial Quality] [decimal](18, 8) NULL,
	[Weighted Sequestration - Claims] [decimal](18, 8) NULL,
	[Weighted Bender/PPD Release] [decimal](18, 8) NULL,
	[Weighted Other Claims Adjs] [decimal](18, 8) NULL,
	[Weighted Related Party Admin + Profit to Claims] [decimal](18, 8) NULL,
	[BPT_Covered RP Profits] [decimal](18, 8) NULL,
	[BPT_HAH No Profit] [decimal](18, 8) NULL,
	[BPT_MSB No Profit] [decimal](18, 8) NULL,
	[SD_HAH No Profit] [decimal](18, 8) NULL,
	[SD_HAH Profit] [decimal](18, 8) NULL,
	[SD_MSB No Profit] [decimal](18, 8) NULL,
	[SD_MSB Profit] [decimal](18, 8) NULL,
	[SD_Quality Profit] [decimal](18, 8) NULL,
	[SD_Quality No Profit] [decimal](18, 8) NULL,
	[SD_Non Core Quality No Profit] [decimal](18, 8) NULL,
	[SD_Core Quality] [decimal](18, 8) NULL,
	[MSB Adj] [decimal](18, 8) NULL,
	[Quality (Non-HaH) Adj] [decimal](18, 8) NULL,
	[Humana at Home Adj] [decimal](18, 8) NULL,
	[Weighted MSBs(Finance)] [decimal](18, 8) NULL,
	[Weighted Total Humana at Home] [decimal](18, 8) NULL,
	[PBRx DIR] [decimal](18, 8) NULL,
	[PBRx DIR Adj] [decimal](18, 8) NULL,
	[ESRD Subsidy] [decimal](18, 8) NULL,
	[Cost Share Adj] [decimal](18, 8) NULL,
	[FCF Adj] [decimal](18, 8) NULL,
	[USERID] [varchar](7) NOT NULL,
	[TimeStamp] [datetime] NOT NULL
);


INSERT INTO #SCTMAForecast
    ( 
        [Year],
        [Bid Year Contract-PBP],
        [ContractPBPSegmentID],
        [Total Member Months],
        [ESRD Hospice Member Months],
        [Non E/H + OOA Member Months],

--#17 BK 5/1/24
		[Weighted Bid Risk Score],

        [Weighted Avg County Rate @ 1.0],
        [Weighted Projected Risk Score (Mbr Wtd)],
        [Weighted ESRD/Hosp Revenue],
        [Weighted Risk-Adjusted Revenue],
--#19 DG 8/9/24
		[Weighted Plan Benchmark],
        [Weighted Risk-adj A/B Revenue],
        [Weighted Reduced CS Rebate],
        [Weighted Reduced CS Prem],
        [Weighted Mand Supp Rebate],
        [Weighted Mand Supp Prem],
        [Weighted MA Uncollected Member Premium],
        [Weighted MA Sequestration - Revenue],
        [Weighted MSP],
        [Weighted Other Revenue Adjs],

--#20 CB 8/19/2024 [Add new MLR Adj Field]
	    [Weighted MLR Rebate Adjs],

        [Weighted Basic Member Premium],
        [Weighted MA Admin: Insurer Fees],
        [Weighted Risk Provider Offsets],
        [Wtd. Medicare Secondary Payer],
        [Weighted ESRD/Hosp Liability],
        [Weighted Total MA Net (Covered)],
        [Weighted Total MA Net (Non Covered)],
        [Weighted Financial Quality],
        [Weighted Sequestration - Claims],
        [Weighted Bender/PPD Release],
        [Weighted Other Claims Adjs],
        [Weighted Related Party Admin + Profit to Claims],
        [BPT_Covered RP Profits],
        [BPT_HAH No Profit],
        [BPT_MSB No Profit],
        [SD_HAH No Profit],
        [SD_HAH Profit],
        [SD_MSB No Profit],
        [SD_MSB Profit],
        [SD_Quality Profit],
        [SD_Quality No Profit],
        [SD_Non Core Quality No Profit],
        [SD_Core Quality],

--#17 BK 5/1/24
		[MSB Adj],

        [Quality (Non-HaH) Adj],
        [Humana at Home Adj],
        [Weighted MSBs(Finance)],
        [Weighted Total Humana at Home],
        [PBRx DIR],
        [PBRx DIR Adj],
        [ESRD Subsidy],
		[Cost Share Adj],
		[FCF Adj],
        [USERID],
        [TimeStamp]
    )
    SELECT a.PlanYearID AS [Year],
           a.ContractPBPSegment AS [Bid Year Contract-PBP],
           a.planyearcontractpbpsegment AS [ContractPBPSegmentID],
           a.AdjTotalMM AS [Total Member Months],
           a.ESRDHospMemberMonths AS [ESRD Hospice Member Months],
           a.NonESRDHospiceOOAMM AS [Non E/H + OOA Member Months],

--#17 BK 5/1/24
		   a.BidriskScore * b.NonESRDHospiceOOAMM AS [Weighted Bid Risk Score],

           a.AverageCountyRate * b.NonESRDHospiceOOAMM AS [Weighted Avg County Rate @ 1.0],
           a.ProjectedRiskScoreMbrWtd * b.NonESRDHospiceOOAMM AS [Weighted Projected Risk Score (Mbr Wtd)],
           a.ESRDHRevenueWtd * b.ESRDHospMemberMonths AS [Weighted ESRD/Hosp Revenue],
           a.MARevenue * b.NonESRDHospiceOOAMM AS [Weighted Risk-Adjusted Revenue],
--#19 DG 8/9/24
		   a.CMSBenchmarkwQualityStarsImpact * b.NonESRDHospiceOOAMM AS [Weighted Plan Benchmark],
           a.RiskAdjABRevenue * b.NonESRDHospiceOOAMM AS [Weighted Risk-adj A/B Revenue],
           a.RedCSRebate * b.NonESRDHospiceOOAMM AS [Weighted Reduced CS Rebate],
           a.RedCSPrem * b.NonESRDHospiceOOAMM AS [Weighted Reduced CS Prem],
           a.RedMandRebate * b.NonESRDHospiceOOAMM AS [Weighted Mand Supp Rebate],
           a.RedMandPrem * b.NonESRDHospiceOOAMM AS [Weighted Mand Supp Prem],
           a.UncollectedMbrPrem * b.NonESRDHospiceOOAMM AS [Weighted MA Uncollected Member Premium],
           a.SeqRevenue * b.NonESRDHospiceOOAMM AS [Weighted MA Sequestration - Revenue],
           a.MSP * b.NonESRDHospiceOOAMM AS [Weighted MSP],
           a.OtherRevTotalAdj * b.NonESRDHospiceOOAMM AS [Weighted Other Revenue Adjs],

--#20 CB 8/19/2024
		   a.MLRRebateAdj * b.NonESRDHospiceOOAMM AS [Weighted MLR Rebate Adjs],

           a.BasicMemberPremium * b.NonESRDHospiceOOAMM AS [Weighted Basic Member Premium],
           a.InsurerFee * b.AdjTotalMM AS [Weighted Risk Provider Offsets],
           a.RiskProviderOffsets * b.NonESRDHospiceOOAMM AS [Weighted Risk Provider Offsets],
           a.WtdMedSecPay * b.NonESRDHospiceOOAMM AS [Wtd. Medicare Secondary Payer],
           a.ESRDHLiab * b.ESRDHospMemberMonths AS [Weighted ESRD/Hosp Liability],
           a.TotalMANetCovered * b.NonESRDHospiceOOAMM AS [Weighted Total MA Net (Covered)],
           a.TotalMANetNonCovered * b.NonESRDHospiceOOAMM AS [Weighted Total MA Net (Non Covered)],
           a.FinancialQuality * b.NonESRDHospiceOOAMM AS [Weighted Financial Quality],
           0 AS [Weighted Sequestration - Claims],
           0 AS [Weighted Bender/PPD Release],
           a.OtherClaimsAdjs * b.NonESRDHospiceOOAMM AS [Weighted Other Claims Adjs],
           a.RPAdminandProfit2Claims * b.NonESRDHospiceOOAMM AS [Weighted Related Party Admin + Profit to Claims],
           a.BPTCoveredRPProfits * b.NonESRDHospiceOOAMM AS [BPT_Covered RP Profits],
           a.BPTHAHNoProfit * b.NonESRDHospiceOOAMM AS [BPT_HAH No Profit],
           a.BPTMSBNoProfit * b.NonESRDHospiceOOAMM AS [BPT_MSB No Profit],
           a.SDHAHNoProfit * b.NonESRDHospiceOOAMM AS [SD_HAH No Profit],
           a.SDHAHProfit * b.NonESRDHospiceOOAMM AS [SD_HAH Profit],
           a.SDMSBNoProfit * b.NonESRDHospiceOOAMM AS [SD_MSB No Profit],
           a.SDMSBProfit * b.NonESRDHospiceOOAMM AS [SD_MSB Profit],
           a.SDQualityProfit * b.NonESRDHospiceOOAMM AS [SD_Quality Profit],
           a.SDQualityNoProfit * b.NonESRDHospiceOOAMM AS [SD_Quality No Profit],
           a.SDNonCoreQualityNoProfit * b.NonESRDHospiceOOAMM AS [SD_Non Core Quality No Profit],
           a.SDCoreQuality * b.NonESRDHospiceOOAMM AS [SD_Core Quality],

--#17 BK 5/1/24
		   a.MSBAdj * b.NonESRDHospiceOOAMM AS [MSB Adj],

           a.QualityNonHAHAdj * b.NonESRDHospiceOOAMM AS [Quality (Non-HaH) Adj],
           a.HumanaatHomeAdj * b.NonESRDHospiceOOAMM AS [Humana at Home Adj],
           a.MSBFinance * b.NonESRDHospiceOOAMM AS [Weighted MSBs(Finance)],
           a.HumanaAtHomeTotal * b.NonESRDHospiceOOAMM AS [Weighted Total Humana at Home],
           a.RxRebates * b.NonESRDHospiceOOAMM AS [PBRx DIR],
           a.RxRebateAdj * b.NonESRDHospiceOOAMM AS [PBRx DIR Adj],
           a.ESRDSubsidy * b.NonESRDHospiceOOAMM AS [ESRD Subsidy],
		  a.CostShareAdj*b.NonESRDHospiceOOAMM AS [Cost Share Adj],
		  a.FCFAdj*b.NonESRDHospiceOOAMM AS [FCF Adj],
           @UserID,
           GETDATE()

    FROM
    (
        SELECT [PlanYearID],
               [ContractPBPSegment],
               [planyearcontractpbpsegment],
               [AdjTotalMM],
               [ESRDHospMemberMonths],
               [NonESRDHospiceOOAMM],

--#17 BK 5/1/24
               CASE
			     WHEN [PlanYearID] = @XBidYear THEN [BidriskScore]
				 ELSE [ProjectedRiskScoreMbrWtd]
				END AS [BidriskScore],       --have Bid RS match Fin RS unless bid year

               [AverageCountyRate],
               [ProjectedRiskScoreMbrWtd],
               [ESRDHRevenueWtd],
               [MARevenue],
--#19 DG 8/9/24
			   [CMSBenchmarkwQualityStarsImpact],
               [RiskAdjABRevenue],
               [RedCSRebate],
               [RedCSPrem],
               [RedMandRebate],
               [RedMandPrem],
               [UncollectedMbrPrem],
               [SeqRevenue],
               [MSP],

--#18 BK 6/5/24 for base year need to back into Other Rev Adjmt
               CASE
			     WHEN [PlanYearID] = (@XBidYear - 2) 
				   THEN [TotalMABidRevenue] - [UncollectedMbrPrem] - [SeqRevenue] - [MSP] - [RedMandPrem] - [RedMandRebate]
				           - [RedCSPrem] - [RedCSRebate] - [BasicMemberPremium] - [RiskAdjABRevenue]
				 ELSE [OtherRevTotalAdj]
				END AS [OtherRevTotalAdj],

--#20 CB 8/19/2024
			   [MLRRebateAdj],

               [BasicMemberPremium],
               [InsurerFee],
               [RiskProviderOffsets],
               [WtdMedSecPay],
               [ESRDHLiab],
               [TotalMANetCovered],
               [TotalMANetNonCovered],
               [FinancialQuality],
               0 AS [SeqClaims],
               0 AS [TrendCompPPDAdj],

--#18 BK 6/5/24 for base year need to back into Other Claims Adjmt
               CASE
			     WHEN [PlanYearID] = (@XBidYear - 2) 
				   THEN [TotalMANetLiablility]
                          - ( [RxRebates] + [SelectableQuality] + [CoreQuality] + [HumanaAtHomeExpPlusSNP] 
						       + [MSBFinance] + [TotalMANetCovered] )
				 ELSE [OtherClaimsAdjs]
				END AS [OtherClaimsAdjs],

               [RPAdminandProfit2Claims],
               [BPTCoveredRPProfits],
               [BPTHAHNoProfit],
               [BPTMSBNoProfit],
               [SDHAHNoProfit],
               [SDHAHProfit],
               [SDMSBNoProfit],
               [SDMSBProfit],
               [SDQualityProfit],
               [SDQualityNoProfit],
               [SDNonCoreQualityNoProfit],
               [SDCoreQuality],

--#17 BK 5/1/24
               [MSBAdj],

               [QualityNonHAHAdj],
               [HumanaatHomeAdj],
               [MSBFinance],
               [HumanaAtHomeTotal],
               [RxRebates],
               [RxRebateAdj],
               [ESRDSubsidy],
			   [CostShareAdj],
			   [FCFAdj],
               Iteration
        FROM dbo.SCT_MAForecast (NOLOCK)
    ) AS a
        INNER JOIN dbo.SCT_CalcMAMEMandRS AS b (NOLOCK)
            ON b.PlanYearID = a.PlanYearID
               AND b.ContractPBPSegment = a.planyearcontractpbpsegment
               AND b.Iteration = a.Iteration
    WHERE a.Iteration = @Quarter
          AND a.[PlanYearID] IN ( @XBidYear - 2, @XBidYear - 1, @XBidYear )
          AND
          (
              (a.[ContractPBPSegment] IN
               (
                   SELECT [CPS] FROM #PlanList
               )
              ) --match on BidYearPlan							
              OR (a.[planyearcontractpbpsegment] IN
                  (
                      SELECT [CPS] FROM #PlanList
                  )
                 )
          ); -- match on plan 



--mirror [W_FEM_PDProjUpload
CREATE TABLE #SCTvwPD (
	[Inc Year] [int] NOT NULL,
	[Bid Year Contract-PBP] [varchar](13) NOT NULL,
	[ContractPBPSegmentID] [varchar](13) NOT NULL,
	[Total Member Months] [decimal](18, 8) NULL,
	[Weighted Direct Subsidy] [decimal](18, 8) NULL,
	[Weighted Basic Rebate] [decimal](18, 8) NULL,
	[Weighted Basic Prem] [decimal](18, 8) NULL,
	[Weighted Supp Rebate] [decimal](18, 8) NULL,
	[Weighted Supp Prem] [decimal](18, 8) NULL,
	[Weighted Sequestration (Direct Subsidy)] [decimal](18, 8) NULL,
	[Weighted Sequestration (MA Rebates for Part D)] [decimal](18, 8) NULL,
	[Weighted Revenue: Platino Wrap] [decimal](18, 8) NULL,
	[Weighted Risk Share] [decimal](18, 8) NULL,
	[Weighted Other Revenue Adjs] [decimal](18, 8) NULL,
	[Weighted Allowed] [decimal](18, 8) NULL,
	[Weighted Reinsurance] [decimal](18, 8) NULL,
	[Weighted Member OOP] [decimal](18, 8) NULL,
	[Weighted Rebates] [decimal](18, 8) NULL,
	[Weighted Non-TrOOP] [decimal](18, 8) NULL,
	[Weighted Financial Quality] [decimal](18, 8) NULL,
	[Weighted Liability: Platino Wrap] [decimal](18, 8) NULL,
	[Weighted Network Fee] [decimal](18, 8) NULL,
	[Weighted Incremental Hep C] [decimal](18, 8) NULL,
	[Weighted Other Claims Adjs] [decimal](18, 8) NULL,
	[Weighted Other DIR (Quality Network and Provider)] [decimal](18, 8) NULL,
	[Weighted Risk Deal Adj.] [decimal](18, 8) NULL,
	[WeightedRiskScore] [decimal](18, 8) NULL,
	[WeightedPDRevenue] [decimal](18, 8) NULL,
	[WeightedPDClaims] [decimal](18, 8) NULL,
	[WeightedBasicPaid] [decimal](18, 8) NULL,
	[WeightedSuppPaid] [decimal](18, 8) NULL,
	[StretchRebate] [decimal](18, 8) NULL,
	[HPGERDIR] [decimal](18, 8) NULL,
	[RiskDealDIR] [decimal](18, 8) NULL,
	[PDMSP] [decimal](18, 8) NULL,
	[PDAdjustment1] [decimal](18, 8) NULL,
	[PDAdjustment2] [decimal](18, 8) NULL,
	[PDAdjustment3] [decimal](18, 8) NULL,
	[PDAdjustment4] [decimal](18, 8) NULL,
	[PDAdjustment5] [decimal](18, 8) NULL,
	[RiskDealReins] [decimal](18, 8) NULL,
	[RiskDealRiskShare] [decimal](18, 8) NULL,
	[NetRiskDealDIR] [decimal](18, 8) NULL,
	[OtherPDReportingPlaceholder1] [decimal](18, 8) NULL,
	[MailDispensingRateMDR] [decimal](18, 8) NULL,
	[GenericDispensingRateGDR] [decimal](18, 8) NULL,
	[EQScriptsPMPM] [decimal](18, 8) NULL,
	[UnitCostbyEQ] [decimal](18, 8) NULL,
	[OtherPDReportingPlaceholder2] [decimal](18, 8) NULL,
	[ImpacttoHPSPlaceholder1] [decimal](18, 8) NULL,
	[ImpacttoHPSPlaceholder2] [decimal](18, 8) NULL,
	[ImpacttoHPSPlaceholder3] [decimal](18, 8) NULL,
	[USERID] [varchar](7) NOT NULL,
	[TimeStamp] [datetime] NOT NULL




 );



 INSERT INTO #SCTvwPD
    ( 
        [Inc Year],
        [Bid Year Contract-PBP],
        [ContractPBPSegmentID],
        [Total Member Months],
        [Weighted Direct Subsidy],
        [Weighted Basic Rebate],
        [Weighted Basic Prem],
        [Weighted Supp Rebate],
        [Weighted Supp Prem],
        [Weighted Sequestration (Direct Subsidy)],
        [Weighted Sequestration (MA Rebates for Part D)],
        [Weighted Revenue: Platino Wrap],
        [Weighted Risk Share],
        [Weighted Other Revenue Adjs],
        [Weighted Allowed],
        [Weighted Reinsurance],
        [Weighted Member OOP],
        [Weighted Rebates],
        [Weighted Non-TrOOP],
        [Weighted Financial Quality],
        [Weighted Liability: Platino Wrap],
        [Weighted Network Fee],
        [Weighted Incremental Hep C],
        [Weighted Other DIR (Quality Network and Provider)],
        [Weighted Risk Deal Adj.],
        [WeightedRiskScore],
        [WeightedPDRevenue],
        [WeightedPDClaims],

--BK #12 12/16/21 - Two additional PD fields needed
        [WeightedBasicPaid],
        [WeightedSuppPaid],

        [StretchRebate],
        [HPGERDIR],
        [RiskDealDIR],
        [PDMSP],
		[PDAdjustment1],
        [PDAdjustment2],
        [PDAdjustment3],
        [PDAdjustment4],
        [PDAdjustment5],
        [RiskDealReins],
        [RiskDealRiskShare],
        [OtherPDReportingPlaceholder1],
        [MailDispensingRateMDR],
        [GenericDispensingRateGDR],
        [EQScriptsPMPM],
        [UnitCostbyEQ],
        [OtherPDReportingPlaceholder2],
        [ImpacttoHPSPlaceholder1],
        [ImpacttoHPSPlaceholder2],
        [ImpacttoHPSPlaceholder3],
        [USERID],
        [TimeStamp] 
    )
    SELECT DISTINCT
           [PlanYearID],
           [ContractPBPSegment],
           [PlanYearContractPBPSegment],
           [AdjTotalMM],
           [WeightedDirectSubsidy] * [AdjTotalMM],
           [WeightedBasicRebate] * [AdjTotalMM],
           [WeightedBasicPrem] * [AdjTotalMM],
           [WeightedSuppRebate] * [AdjTotalMM],
           [WeightedSuppPrem] * [AdjTotalMM],
           [WeightedSequestrationDirectSubsidy] * [AdjTotalMM],
           [WeightedSequestrationMARebates] * [AdjTotalMM],
           [WeightedRevenuePlatinoWrap] * [AdjTotalMM],
           [WeightedRiskShare] * [AdjTotalMM],
           [WeightedOtherRevenueAdj] * [AdjTotalMM],
           [WeightedAllowed] * [AdjTotalMM],
           [WeightedReinsurance] * [AdjTotalMM],
           [WeightedMemberOOP] * [AdjTotalMM],
           [WeightedRebates] * [AdjTotalMM],
           [WeightedNonTrOOP] * [AdjTotalMM],
           [WeightedFinancialQuality] * [AdjTotalMM],
           [WeightedLiabilityPlatinoWrap] * [AdjTotalMM],
           [WeightedNetworkFee] * [AdjTotalMM],
           NULL AS [Weighted Incremental Hep C],
           [WeightedOtherDIR] * [AdjTotalMM],
           [WeightedRiskDealAdj] * [AdjTotalMM],
           [WeightedRiskScore] * [AdjTotalMM],
           [WeightedPDRevenue] * [AdjTotalMM],
           ([WeightedPDClaims] + [PDAdjustment3] + [PDAdjustment4] + [PDAdjustment5]) * [AdjTotalMM], 
--BK #16 - 8/25/23 above adding in PDAdjmts 3-5 to get correct Total Net PD Claims for downstream use 

--BK #12 12/16/21 - Two additional PD fields needed
           [WeightedBasicPaid] * [AdjTotalMM],
           [WeightedSuppPaid] * [AdjTotalMM],

           [StretchRebate] * [AdjTotalMM],
           [HPGERDIR] * [AdjTotalMM],
           [RiskDealDIR] * [AdjTotalMM],
           [PDMSP] * [AdjTotalMM],
		   [PDAdjustment1] * [AdjTotalMM],
           [PDAdjustment2] * [AdjTotalMM],
           [PDAdjustment3] * [AdjTotalMM],
           [PDAdjustment4] * [AdjTotalMM],
           [PDAdjustment5] * [AdjTotalMM],
           [RiskDealReins] * [AdjTotalMM],
           [RiskDealRiskShare] * [AdjTotalMM],

           [OtherPDReportingPlaceholder1] * [AdjTotalMM],
           --BK #16 - 8/25/23  Capture NULLs for Dispensing Rate fields to prevent overflow; PREP isn't using this informaiton
           NULL,   --[MailDispensingRateMDR] * [AdjTotalMM],
           NULL,   --[GenericDispensingRateGDR] * [AdjTotalMM],
           [EQScriptsPMPM] * [AdjTotalMM],
           [UnitCostbyEQ] * [AdjTotalMM],
           [OtherPDReportingPlaceholder2] * [AdjTotalMM],
           [ImpacttoHPSPlaceholder1] * [AdjTotalMM],
           [ImpacttoHPSPlaceholder2] * [AdjTotalMM],
           [ImpacttoHPSPlaceholder3] * [AdjTotalMM],

           @UserID,
           GETDATE() 
    FROM [dbo].[SCT_vwPartD] (NOLOCK)
	 WHERE Iteration = @Quarter
    AND [PlanYearID] IN ( @XBidYear - 2, @XBidYear - 1, @XBidYear )
          AND
          (
              ([ContractPBPSegment] IN
               (
                   SELECT [CPS] FROM #PlanList
               )
              ) --match on BidYearPlan            							
              OR ([PlanYearContractPBPSegment] IN
                  (
                      SELECT [CPS] FROM #PlanList
                  )
                 )
          ); -- match on plan in current year							




DECLARE @MAPullRecCnt BIGINT

SELECT @MAPullRecCnt = COUNT(*) FROM #SCTMAForecast

--/*** START: Delete old records corresponding to anything being inserted and Insert update ********/
IF @MAPullRecCnt > 0
  BEGIN --MA Pull has records; assume PD does too

	DELETE FROM dbo.[W_FEM_MAProjUpload]
    WHERE [Year] IN ( @XBidYear - 2, @XBidYear - 1, @XBidYear )
          AND
          (
              ([Bid Year Contract-PBP] IN
               (
                   SELECT [CPS] FROM #PlanList


               )
              ) --match on BidYearPlan                                                                   							
              OR ([ContractPBPSegmentID] IN
                  (
                      SELECT [CPS] FROM #PlanList
                  )
                 )
          ); -- match on plan    

   INSERT INTO dbo.W_FEM_MAProjUpload
    (
        Year,
        [Bid Year Contract-PBP],
        ContractPBPSegmentID,
        [Total Member Months],
        [ESRD Hospice Member Months],
        [Non E/H + OOA Member Months],

		[Weighted Bid Risk Score],

        [Weighted Avg County Rate @ 1.0],
        [Weighted Projected Risk Score (Mbr Wtd)],
        [Weighted ESRD/Hosp Revenue],
        [Weighted Risk-Adjusted Revenue],
--#19 DG 8/9/24
		[Weighted Plan Benchmark],
        [Weighted Risk-adj A/B Revenue],
        [Weighted Reduced CS Rebate],
        [Weighted Reduced CS Prem],
        [Weighted Mand Supp Rebate],
        [Weighted Mand Supp Prem],
        [Weighted MA Uncollected Member Premium],
        [Weighted MA Sequestration - Revenue],
        [Weighted MSP],
        [Weighted Other Revenue Adjs],

--#20 CB 8/27/24
		[Weighted MLR Rebate Adjs],

        [Weighted Basic Member Premium],
        [Weighted MA Admin: Insurer Fees],

        [Weighted Risk Provider Offsets],
        [Wtd. Medicare Secondary Payer],
        [Weighted ESRD/Hosp Liability],

        [Weighted Total MA Net (Covered)],
        [Weighted Total MA Net (Non Covered)],
        [Weighted Financial Quality],
        [Weighted Sequestration - Claims],
        [Weighted Bender/PPD Release],
        [Weighted Other Claims Adjs],
        [Weighted Related Party Admin + Profit to Claims],
        [BPT_Covered RP Profits],
        [BPT_HAH No Profit],
        [BPT_MSB No Profit],
        [SD_HAH No Profit],
        [SD_HAH Profit],
        [SD_MSB No Profit],
        [SD_MSB Profit],
        [SD_Quality Profit],
        [SD_Quality No Profit],
        [SD_Non Core Quality No Profit],
        [SD_Core Quality],

		[MSB Adj],

        [Quality (Non-HaH) Adj],
        [Humana at Home Adj],
        [Weighted MSBs(Finance)],
        [Weighted Total Humana at Home],
        [PBRx DIR],
        [PBRx DIR Adj],
        [ESRD Subsidy],
        [Cost Share Adj],
        [FCF Adj],
        USERID,
        TimeStamp
    )
	SELECT Year,
           [Bid Year Contract-PBP],
           ContractPBPSegmentID,
           [Total Member Months],
           [ESRD Hospice Member Months],
           [Non E/H + OOA Member Months],

		   [Weighted Bid Risk Score],

           [Weighted Avg County Rate @ 1.0],
           [Weighted Projected Risk Score (Mbr Wtd)],
           [Weighted ESRD/Hosp Revenue],
           [Weighted Risk-Adjusted Revenue],
--#19 DG 8/9/24
		   [Weighted Plan Benchmark],
           [Weighted Risk-adj A/B Revenue],
           [Weighted Reduced CS Rebate],
           [Weighted Reduced CS Prem],
           [Weighted Mand Supp Rebate],
           [Weighted Mand Supp Prem],
           [Weighted MA Uncollected Member Premium],
           [Weighted MA Sequestration - Revenue],
           [Weighted MSP],
           [Weighted Other Revenue Adjs],

--#20 CB 8/27/24
		   [Weighted MLR Rebate Adjs],

           [Weighted Basic Member Premium],
           [Weighted MA Admin: Insurer Fees],
           [Weighted Risk Provider Offsets],
           [Wtd. Medicare Secondary Payer],
           [Weighted ESRD/Hosp Liability],
           [Weighted Total MA Net (Covered)],
           [Weighted Total MA Net (Non Covered)],
           [Weighted Financial Quality],
           [Weighted Sequestration - Claims],
           [Weighted Bender/PPD Release],
           [Weighted Other Claims Adjs],
           [Weighted Related Party Admin + Profit to Claims],
           [BPT_Covered RP Profits],
           [BPT_HAH No Profit],
           [BPT_MSB No Profit],
           [SD_HAH No Profit],
           [SD_HAH Profit],
           [SD_MSB No Profit],
           [SD_MSB Profit],
           [SD_Quality Profit],
           [SD_Quality No Profit],
           [SD_Non Core Quality No Profit],
           [SD_Core Quality],

		   [MSB Adj],

           [Quality (Non-HaH) Adj],
           [Humana at Home Adj],
           [Weighted MSBs(Finance)],
           [Weighted Total Humana at Home],
           [PBRx DIR],
           [PBRx DIR Adj],
           [ESRD Subsidy],
           [Cost Share Adj],
           [FCF Adj],
           USERID,
           TimeStamp
	  FROM #SCTMAForecast
	  ;


    DELETE FROM dbo.[W_FEM_PDProjUpload]
    WHERE [Inc Year] IN ( @XBidYear - 2, @XBidYear - 1, @XBidYear )
          AND
          (
              ([Bid Year Contract-PBP] IN
               (
                   SELECT [CPS] FROM #PlanList
               )
              ) --match on BidYearPlan           							
              OR ([ContractPBPSegmentID] IN
                  (
                      SELECT [CPS] FROM #PlanList
                  )
                 )
          ); -- match on plan 


    INSERT INTO dbo.W_FEM_PDProjUpload
	 (
	     [Inc Year],
	     [Bid Year Contract-PBP],
	     ContractPBPSegmentID,
	     [Total Member Months],
	     [Weighted Direct Subsidy],
	     [Weighted Basic Rebate],
	     [Weighted Basic Prem],
	     [Weighted Supp Rebate],
	     [Weighted Supp Prem],
	     [Weighted Sequestration (Direct Subsidy)],
	     [Weighted Sequestration (MA Rebates for Part D)],
	     [Weighted Revenue: Platino Wrap],
	     [Weighted Risk Share],
	     [Weighted Other Revenue Adjs],
	     [Weighted Allowed],
	     [Weighted Reinsurance],
	     [Weighted Member OOP],
	     [Weighted Rebates],
	     [Weighted Non-TrOOP],
	     [Weighted Financial Quality],
	     [Weighted Liability: Platino Wrap],
	     [Weighted Network Fee],
	     [Weighted Incremental Hep C],
	     [Weighted Other Claims Adjs],
	     [Weighted Other DIR (Quality Network and Provider)],
	     [Weighted Risk Deal Adj.],
	     WeightedRiskScore,
	     WeightedPDRevenue,
	     WeightedPDClaims,
	     WeightedBasicPaid,
	     WeightedSuppPaid,
	     StretchRebate,
	     HPGERDIR,
	     RiskDealDIR,
	     PDMSP,
	     PDAdjustment1,
	     PDAdjustment2,
	     PDAdjustment3,
	     PDAdjustment4,
	     PDAdjustment5,
	     RiskDealReins,
	     RiskDealRiskShare,
	     NetRiskDealDIR,
	     OtherPDReportingPlaceholder1,
	     MailDispensingRateMDR,
	     GenericDispensingRateGDR,
	     EQScriptsPMPM,
	     UnitCostbyEQ,
	     OtherPDReportingPlaceholder2,
	     ImpacttoHPSPlaceholder1,
	     ImpacttoHPSPlaceholder2,
	     ImpacttoHPSPlaceholder3,
	     USERID,
	     TimeStamp
	 )
	 SELECT [Inc Year],
            [Bid Year Contract-PBP],
            ContractPBPSegmentID,
            [Total Member Months],
            [Weighted Direct Subsidy],
            [Weighted Basic Rebate],
            [Weighted Basic Prem],
            [Weighted Supp Rebate],
            [Weighted Supp Prem],
            [Weighted Sequestration (Direct Subsidy)],
            [Weighted Sequestration (MA Rebates for Part D)],
            [Weighted Revenue: Platino Wrap],
            [Weighted Risk Share],
            [Weighted Other Revenue Adjs],
            [Weighted Allowed],
            [Weighted Reinsurance],
            [Weighted Member OOP],
            [Weighted Rebates],
            [Weighted Non-TrOOP],
            [Weighted Financial Quality],
            [Weighted Liability: Platino Wrap],
            [Weighted Network Fee],
            [Weighted Incremental Hep C],
            [Weighted Other Claims Adjs],
            [Weighted Other DIR (Quality Network and Provider)],
            [Weighted Risk Deal Adj.],
            WeightedRiskScore,
            WeightedPDRevenue,
            WeightedPDClaims,
            WeightedBasicPaid,
            WeightedSuppPaid,
            StretchRebate,
            HPGERDIR,
            RiskDealDIR,
            PDMSP,
            PDAdjustment1,
            PDAdjustment2,
            PDAdjustment3,
            PDAdjustment4,
            PDAdjustment5,
            RiskDealReins,
            RiskDealRiskShare,
            NetRiskDealDIR,
            OtherPDReportingPlaceholder1,
            MailDispensingRateMDR,
            GenericDispensingRateGDR,
            EQScriptsPMPM,
            UnitCostbyEQ,
            OtherPDReportingPlaceholder2,
            ImpacttoHPSPlaceholder1,
            ImpacttoHPSPlaceholder2,
            ImpacttoHPSPlaceholder3,
            USERID,
            TimeStamp
	   FROM #SCTvwPD
	   ;


  EXECUTE [dbo].[CW_SavePDPlanDetail] 
     @CPBPLIst = @CPBPList
    ,@BaseYear = @BaseYear
    ,@UserID = @UserID
  ;


   END  --MA Pull has records; Assume PD does too
ELSE
   BEGIN --Message that not updating MA Proj; Make severity > 10 for VBA to pick up
     RAISERROR (N'No records returned from SCT MAForecast.  PREP [W_FEM_MAProjUpload] and [W_FEM_PDProjUpload] could not be updated.', 11, 1)
   END  --Message that not updating MA Proj
;

--/*** END: Delete old records corresponding to anything being inserted and Insert update ********/


IF ( SELECT OBJECT_ID('tempdb..#PlanList') ) IS NOT NULL
    DROP TABLE #PlanList

IF ( SELECT OBJECT_ID('tempdb..#SCTMAForecast') ) IS NOT NULL
  BEGIN
    DROP TABLE #SCTMAForecast
  END

IF ( SELECT OBJECT_ID('tempdb..#SCTvwPD') ) IS NOT NULL
  BEGIN
    DROP TABLE #SCTvwPD
  END


END;
GO
