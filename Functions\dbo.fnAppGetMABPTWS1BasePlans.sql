SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetMABPTWS1BasePlans]  ******/

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetMABPTWS1BasePlans
--
-- AUTHOR: <PERSON> Lake
--
-- CREATED DATE: 2008-May-08
-- HEADER UPDATED: 2016-Aug-22
--
-- DESCRIPTION: Function responsible for listing Plans in Base on MA WS1
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--      SavedPlanBPTExceptionDetail
--      SavedPlanDFSummary
--      SavedForecastSetup
--      SavedPlanInfo
--
--	Write:
--
-- VIEWS:
--      vwSavedDFMembership
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2008-May-08      1           Initial Version                                                     Brian Lake
-- 2008-May-16      2           Added Base period Member Months to output                           Brian Lake
-- 2009-Mar-17      3           Data types                                                          Sandy Ellis
-- 2009-May-08      4           Added Base period Non Dual Member Months                            Lawrence Choi
--                                and Non Dual Member Months % to output
-- 2010-May-11      5           Added subquery to non-all other plan to                             James Wu 
--                                sort by Member Months
-- 2010-Oct-05      6           Revised for 2012 database                                           Jake Gaecke
-- 2011-Jan-17      7           Renamed from fnAppGetBPTPlanWS1BasePlans. Added AND                 Michael Siekerka
--                                D.MemberMonths IS NOT NULL  
-- 2011-Jan-18      8           Copied functionality from spGetBPTExceptionDetail, this function    Michael Siekerka
--                                will replace that procedure
-- 2011-Feb-07      9           Added joins to SavedCUHeader                                        Joe Casey
-- 2011-Mar-29      10          Ordered so higher membership displays first                         Joe Casey
-- 2013-Oct-08      11          Modified to Include SegmentId                                       Anubhav Mishra
-- 2014-Apr-23      12          Truncating Segment ID into 2 Char to meet BPT restrictions          Mason Roberts
-- 2016-Mar-08      13          Removed truncation from Segment ID to meet BPT requirements         Chris McAuley
-- 2016-Aug-22      14          Removed SavedCUPlanMap from the function and replaced with          Jordan Purdue
--                                SavedCUMembership
-- 2020-Oct-13      15          Back End Alignment and Restructuring                                Keith Galloway	
-- 2023-Sep-13      16			Added no lock for the tables										Surya Murthy
-- 2023-Oct-04      17          Added Internal Parameter			                                Sheetal Patil	
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS1BasePlans]
(
    @ForecastID INT
)
RETURNS @Results TABLE 
(
    ContractNumber CHAR(5),
    PlanID CHAR(3),  
    SegmentId CHAR(3),  --Added SegmentId 
    MemberMonths INT,
    NonDualMemberMonths INT
) AS
BEGIN
    DECLARE @MaxPlans AS INT
    SET @MaxPlans = 7
	DECLARE  @XForecastID INT =  @ForecastID 

    IF EXISTS (SELECT 1 FROM dbo.SavedPlanBPTExceptionDetail WITH (NOLOCK) WHERE ForecastID = @XForecastID AND IsHidden = 0)
    -- If there has been an update to the membership then pull from the Override table
    BEGIN
		INSERT INTO @Results
        SELECT
            ContractNumber,
            PlanID,
            SegmentID,
            MemberMonths,
            NonDualMemberMonths
        FROM dbo.SavedPlanBPTExceptionDetail WITH(NOLOCK)
        WHERE ForecastID = @XForecastID
            AND IsHidden = 0
        ORDER BY MemberMonths DESC
    END
    ELSE
    --If no update or override has taken place pull from Final C&U
    BEGIN
        DECLARE @ExtraPlans AS INT
        SET @ExtraPlans =	(         
							SELECT COUNT(DFS.PlanInfoID)
							FROM dbo.SavedPlanDFSummary DFS WITH(NOLOCK)
							WHERE DFS.MARatingOptionID = 1
							  AND DFS.ForecastID = @XForecastID
							)
        IF @ExtraPlans < 8
            BEGIN
				INSERT INTO @Results
                SELECT
                    A.ContractNumber,
                    A.PlanID,
                    SegmentId,  --Added SegmentId
                    MemberMonths = A.TotalMemberMonths,
                    NonDualMemberMonths = A.NonDEPoundMemberMonths
                FROM 
				 (
				SELECT 
					ROW = ROW_NUMBER() OVER (ORDER BY Mbrs.TotalMemberMonths DESC),
					Mbrs.ForecastID,
					Mbrs.ContractNumber,
					Mbrs.PlanID,
					Mbrs.SegmentID,
					Mbrs.TotalMemberMonths,
					Mbrs.NonDEPoundMemberMonths
				FROM
					(
					SELECT
						SFS.ForecastID,
						ContractNumber = LEFT(SPI.CPS,5),
						Substring(SPI.CPS,7,3) PlanID,
						SegmentID = Right(SPI.CPS,3),
						TotalMemberMonths = SUM(COALESCE(Total.MemberMonths,0)),
						NonDEPoundMemberMonths = SUM(COALESCE(NonDE.MemberMonths,0))
					FROM dbo.SavedForecastSetup SFS WITH(NOLOCK)
					INNER JOIN dbo.SavedPlanDFSummary DFS (NOLOCK)    
						ON SFS.ForecastID = DFS.ForecastID
					INNER JOIN dbo.SavedPlanInfo SPI WITH(NOLOCK)     
						ON DFS.PlanInfoID = SPI.PlanInfoID
					LEFT JOIN dbo.vwSavedDFMembership Total WITH(NOLOCK)   
						ON DFS.PlanInfoID = Total.PlanInfoID    
						AND SFS.DFVersionID = Total.DFVersionID
						AND Total.DemogIndicator = 3
					LEFT JOIN dbo.vwSavedDFMembership NonDE WITH(NOLOCK)   
						ON DFS.PlanInfoID = NonDE.PlanInfoID    
						AND SFS.DFVersionID = NonDE.DFVersionID
						AND NonDE.DemogIndicator = 1
						AND total.SSStateCountyCD = nonde.SSStateCountyCD
						AND total.GrouperID = nonde.GrouperID
					WHERE 
						DFS.MARatingOptionID = 1 
						AND DFS.ForecastID = @XForecastID
						AND total.GrouperID = 'Plan Lvl' --Prevent double-counting
					GROUP BY SFS.ForecastID, SPI.CPS
					) Mbrs
				) A
                WHERE A.Row < @MaxPlans + 1
                ORDER BY A.TotalMemberMonths DESC
            END
        ELSE
            BEGIN
				INSERT INTO @Results
				SELECT
					ContractNumber,
					PlanID,
					SegmentId,  --Added SegmentId
					MemberMonths,
					NonDualMemberMonths
				FROM
					(SELECT
						ID = 1,
						A.ContractNumber,
						A.PlanID,
						A.SegmentId,  --Added SegmentId
						MemberMonths = A.TotalMemberMonths,
						NonDualMemberMonths =A.NonDEPoundMemberMonths
					From
				 (
				SELECT 
					ROW = ROW_NUMBER() OVER (ORDER BY Mbrs.TotalMemberMonths DESC),
					Mbrs.ForecastID,
					Mbrs.ContractNumber,
					Mbrs.PlanID,
					Mbrs.SegmentID,
					Mbrs.TotalMemberMonths,
					Mbrs.NonDEPoundMemberMonths
				FROM
					(
					SELECT
						SFS.ForecastID,
						ContractNumber = LEFT(SPI.CPS,5),
						Substring(SPI.CPS,7,3) PlanID,
						SegmentID = Right(SPI.CPS,3),
						TotalMemberMonths = SUM(COALESCE(Total.MemberMonths,0)),
						NonDEPoundMemberMonths = SUM(COALESCE(NonDE.MemberMonths,0))
					FROM dbo.SavedForecastSetup SFS WITH(NOLOCK)
					INNER JOIN dbo.SavedPlanDFSummary DFS WITH(NOLOCK)    
						ON SFS.ForecastID = DFS.ForecastID
					INNER JOIN dbo.SavedPlanInfo SPI  WITH(NOLOCK)   
						ON DFS.PlanInfoID = SPI.PlanInfoID
					LEFT JOIN dbo.vwSavedDFMembership Total  WITH(NOLOCK)  
						ON DFS.PlanInfoID = Total.PlanInfoID    
						AND SFS.DFVersionID = Total.DFVersionID
						AND Total.DemogIndicator = 3
					LEFT JOIN dbo.vwSavedDFMembership NonDE WITH(NOLOCK)   
						ON DFS.PlanInfoID = NonDE.PlanInfoID    
						AND SFS.DFVersionID = NonDE.DFVersionID
						AND NonDE.DemogIndicator = 1
						AND total.SSStateCountyCD = nonde.SSStateCountyCD
						AND total.GrouperID = nonde.GrouperID
					WHERE 
						DFS.MARatingOptionID = 1 
						AND DFS.ForecastID = @XForecastID
						AND total.GrouperID = 'Plan Lvl' --Prevent double-counting
					GROUP BY SFS.ForecastID, SPI.CPS
					) Mbrs
				) A
					WHERE A.Row < @MaxPlans + 1
	                
					UNION
	                
					SELECT
						ID = 2,
						ContractNumber = NULL,
						PlanID = NULL,
						SegmentId = NULL,  --Added SegmentId
						MemberMonths = SUM(A.TotalMemberMonths),
						NonDualMemberMonths = SUM(A.NonDEPoundMemberMonths)
					FROM
 				 (
				SELECT 
					ROW = ROW_NUMBER() OVER (ORDER BY Mbrs.TotalMemberMonths DESC),
					Mbrs.ForecastID,
					Mbrs.ContractNumber,
					Mbrs.PlanID,
					Mbrs.SegmentID,
					Mbrs.TotalMemberMonths,
					Mbrs.NonDEPoundMemberMonths
				FROM
					(
					SELECT
						SFS.ForecastID,
						ContractNumber = LEFT(SPI.CPS,5),
						Substring(SPI.CPS,7,3) AS PlanID,
						SegmentID = Right(SPI.CPS,3),
						TotalMemberMonths = SUM(COALESCE(Total.MemberMonths,0)),
						NonDEPoundMemberMonths = SUM(COALESCE(NonDE.MemberMonths,0))
					FROM dbo.SavedForecastSetup SFS WITH(NOLOCK)
					INNER JOIN dbo.SavedPlanDFSummary DFS WITH(NOLOCK)    
						ON SFS.ForecastID = DFS.ForecastID
					INNER JOIN dbo.SavedPlanInfo SPI  WITH(NOLOCK)   
						ON DFS.PlanInfoID = SPI.PlanInfoID
					LEFT JOIN dbo.vwSavedDFMembership Total  WITH(NOLOCK)  
						ON DFS.PlanInfoID = Total.PlanInfoID    
						AND SFS.DFVersionID = Total.DFVersionID
						AND Total.DemogIndicator = 3
					LEFT JOIN dbo.vwSavedDFMembership NonDE  WITH(NOLOCK)  
						ON DFS.PlanInfoID = NonDE.PlanInfoID    
						AND SFS.DFVersionID = NonDE.DFVersionID
						AND NonDE.DemogIndicator = 1
						AND total.SSStateCountyCD = nonde.SSStateCountyCD
						AND total.GrouperID = nonde.GrouperID
					WHERE 
						DFS.MARatingOptionID = 1 
						AND DFS.ForecastID = @XForecastID
						AND total.GrouperID = 'Plan Lvl' --Prevent double-counting
					GROUP BY SFS.ForecastID, SPI.CPS
					) Mbrs
				) A
					WHERE A.Row > @MaxPlans
					) total
				ORDER BY ID, MemberMonths DESC
            END
    END
    RETURN            
END
GO
