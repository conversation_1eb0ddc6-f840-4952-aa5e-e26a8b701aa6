SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Author:  <PERSON>             
-- Create date: 2020-Sep-24        
-- Description:  Trend_spSyncCostandUse         
--              
--              
-- PARAMETERS:              
-- Input:                

-- TABLES:            
-- Read:              

-- Write:              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:               


-- $HISTORY                 

-- ----------------------------------------------------------------------------------------------------------------------                
-- DATE    VERSION    CHANGES MADE                                                DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------                
-- 2020-Sep-24         1    Initial version												Kiran Pant        
-- 2020-Nov-05         2    Replaced truncate with delete								Kiran Pant   
-- 2020-Nov-24         3    Modifed query & ADDED temp table							Anurodh Pandey
-- 2021-Mar-07         4    Correct DFRunID filtering to header table                   Michael Manes  
-- 2022-Aug-29		   5    Set IsRisk to 0; Removed joins on SavedDFHeader,            Franklin Fu
--							LkpIntRiskPlanMapping, LkpIntDemog, LkpIntRiskDefnDetail	
-- 2023-Feb-20		   6	Changed max incurred month logic. Remove order by 			Adam Gilbert
--							from insert. Remove @IncurredMonth parameter.
-- 2025-Mar-24		   7    Optimization for Trend DF sync issue						Surya Murthy
-- 2025-Apr-10		   8    Replacing Truncate to Delete								Surya Murthy
---------------------------------------------------------------------------------------------------------------------                

CREATE PROCEDURE [dbo].[Trend_spSyncCostandUse]    
(      
    @DFRunID VARCHAR(MAX),        
    @LastUpdateByID VARCHAR(7)  ,      
	@ValidationMessage VARCHAR(MAX) OUT      
  )         

AS        
BEGIN        
    SET NOCOUNT ON;      	
    BEGIN TRY        
        BEGIN     
			DECLARE @DFRunID1 SMALLINT,      
					@maxIncurredMonth INT,
					@DeleteRowcount INT = 1;

			-- Delete logic 	
			BEGIN TRY 
				BEGIN TRANSACTION dfloopdelete;
					WHILE @DeleteRowcount > 0
					BEGIN
						DELETE FROM dbo.Trend_CalcHistoricCostAndUse        
						WHERE PlanInfoID IN        
							  (        
								  SELECT TOP (100000)        
										 PlanInfoID        
								  FROM dbo.Trend_CalcHistoricCostAndUse        
								  ORDER BY PlanInfoID DESC        
							  );   
						SET @DeleteRowcount = @@ROWCOUNT
					END
				COMMIT TRANSACTION dfloopdelete
			END TRY
			BEGIN CATCH
				ROLLBACK TRANSACTION dfloopdelete
			END CATCH

		   --view temptable
			DROP TABLE IF EXISTS #viewtable;
			SELECT PlanInfoID,CPS INTO #viewtable FROM [dbo].[vwPlanInfo]

			--loop table for df run ids
			DROP TABLE IF EXISTS #dfrunidtemp;
			SELECT CAST(Value AS SMALLINT) DFRunID INTO #dfrunidtemp FROM dbo.fnStringSplit(@DFRunID,',');  

			--loop the DF runids
			 WHILE (SELECT COUNT(*) FROM #dfrunidtemp WHERE 1=1) > 0
				BEGIN			
					SELECT TOP 1 @DFRunID1=   DFRunID FROM #dfrunidtemp ORDER BY DFRunID;
					BEGIN TRY                    								
							BEGIN TRANSACTION dfloopinsert;
 							WITH maxDate AS(
							SELECT DISTINCT DFRunID, IncurredStartDate, IncurredEndDate, PaidThroughDate ,
								CASE
									WHEN ( YEAR(PaidThroughDate) = YEAR(IncurredEndDate) 
										AND NOT (  (MONTH(PaidThroughDate) = 12 AND DAY(PaidThroughDate) = 31)  
												   OR (MONTH(PaidThroughDate) = 1 AND DAY(PaidThroughDate) = 31)
												) 
										  )
									THEN EOMONTH(DATEADD(m,-1,IncurredEndDate)) 
									ELSE IncurredEndDate
								END AS maxIncurredDt
							FROM df.dbo.SavedDfHeader
							WHERE DFRunid = @DFRunID1
							)
							SELECT  @maxIncurredMonth = (YEAR(maxIncurredDt) * 100) + MONTH(maxIncurredDt) --format to an integer value Calendarmonth YYYYMM 
							FROM maxDate

							 INSERT INTO dbo.Trend_CalcHistoricCostAndUse     
							(        
								PlanInfoID,        
								CPS,        
								PlanYearID,        
								IncurredMonth,        
								MonthID,        
								QuarterID,        
								BenefitCategoryID,        
								ReportingCategory,        
								IsRisk,        
								Allowed,        
								Net,        
								Units,      
								Admits,        
								Utilization,        
								LastUpdateByID,        
								LastUpdateDateTime        
							)        
							SELECT c.PlanInfoID,  					 
								   vpi.CPS,        
								   c.IncurredMonth / 100 AS PlanYearID,        
								   c.IncurredMonth,        
								   c.IncurredMonth % 100 AS MonthID,        
								   FLOOR((c.IncurredMonth % 100 - 1) / 3) + 1 AS QuarterID,        
								   c.BenefitCategoryID,        
								   bc.ReportingCategory,        
								   0 AS IsRisk,        
								   SUM(c.Paid + c.CapDirectPayEPaidClaims + c.CapDirectPayDEPaidClaims + c.CapSurplusDeficitEPaidClaims        
									   + c.CapSurplusDeficitDEPaidClaims + c.MbrCS + c.EncounterMbrCS + c.DelegatedEncounterMbrCS        
									   + c.PymtReductionAmt + c.ImplicitMarginPaid + c.ImplicitMarginMbrCS        
									  ) AS Allowed,        
								   SUM(c.Paid + c.CapDirectPayEPaidClaims + c.CapDirectPayDEPaidClaims + c.CapSurplusDeficitEPaidClaims        
									   + c.CapSurplusDeficitDEPaidClaims + c.ImplicitMarginPaid        
									  ) AS Net,        
								   SUM(c.UnitCnt + c.EncounterUnitCnt + c.DelegatedEncounterUnitCnt) AS UnitCnt,        
								   SUM(c.AdmitCnt + c.EncounterAdmitCnt + c.DelegatedEncounterAdmitCnt) AS AdmitCnt,        
								   CASE        
									   WHEN bc.ReportingCategory = 'IP' THEN        
										   SUM(c.AdmitCnt + c.EncounterAdmitCnt + c.DelegatedEncounterAdmitCnt)        
									   ELSE        
										   SUM(c.UnitCnt + c.EncounterUnitCnt + c.DelegatedEncounterUnitCnt)        
								   END AS Utilization,        
								   @LastUpdateByID AS LastUpdateByID,        
								   GETDATE() AS LastUpdateDateTime   						   
							FROM DF.dbo.SavedDFClaims c WITH (NOLOCK)        
							INNER JOIN #viewtable vpi WITH (NOLOCK)        
								ON c.PlanInfoID = vpi.PlanInfoID  					         
								INNER JOIN DF.dbo.LkpIntDemog d WITH (NOLOCK)        
									ON c.Demog = d.Demog             
								LEFT JOIN dbo.LkpIntBenefitCategory bc WITH (NOLOCK)        
									ON bc.BenefitCategoryID = c.BenefitCategoryID        
							WHERE d.IsHospice = 0        
								  AND d.IsESRD = 0        
								  AND c.BenefitCategoryID < 1000        
								  AND c.DFRunID= @DFRunID1              
								  AND c.IncurredMonth <= @maxIncurredMonth 

							GROUP BY c.IncurredMonth / 100,        
									 c.IncurredMonth % 100,        
									 FLOOR((c.IncurredMonth % 100 - 1) / 3) + 1,              
									 c.PlanInfoID,        
									 vpi.CPS,        
									 c.IncurredMonth,        
									 c.BenefitCategoryID,        
									 bc.ReportingCategory     											
							COMMIT TRANSACTION dfloopinsert
					END TRY
					BEGIN CATCH
						ROLLBACK TRANSACTION dfloopinsert
					END CATCH
					DELETE FROM #dfrunidtemp WHERE DFRunID=@DFRunID1;
				END -- while loop end           
        END;        
    END TRY        
    BEGIN CATCH        	
     SET @ValidationMessage = ERROR_MESSAGE()          
        DECLARE @ErrorMessage NVARCHAR(4000);        
        DECLARE @ErrorSeverity INT;        
        DECLARE @ErrorState INT;        
        DECLARE @ErrorException NVARCHAR(4000);        
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),        
                @currentdate DATETIME = GETDATE();        

        SELECT @ErrorMessage = ERROR_MESSAGE(),        
               @ErrorSeverity = ERROR_SEVERITY(),        
               @ErrorState = ERROR_STATE(),        
               @ErrorException        
                   = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'        
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);        
        ---Insert into app log for logging error------------------        
        EXEC [dbo].[spAppAddLogEntry] @currentdate,        
                                      '',        
                                      'ERROR',        
                                      @errSrc,        
                                    @ErrorMessage,        
                                      @ErrorException,        
                                      @LastUpdateByID;        

    END CATCH;        
END;
GO
