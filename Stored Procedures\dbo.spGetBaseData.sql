SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spGetBaseData
--
-- CREATOR:			<PERSON> Galloway
--
-- CREATED DATE:	2020-AUG-06
--
-- DESCRIPTION:		Procedure that returns per member per month dollars and annual per member per thousand units
--						for either the experience build, the manual build, or both.
--					Results are at the plan level for the DE# and NonDE# cuts as well as for the Manual Combined cut.
--					However, results are at the provider level for the Experience Combined cut.
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT
--					@MARatingOptionID TINYINT
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			SavedDFClaims
--					SavedDFFinance
--					SavedPlanDFSummary
--					SavedForecastSetup
--					SavedPlanInfo
--					SavedMarketInfo
--					SavedRegionGrouperMapping
--					LkpIntBenefitCategory
--					LkpIntDemogIndicators
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetBidYear
--					fnGetSafeDivisionResult
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-SEP-21		1		Initial Version															Keith Galloway / Alex Beruscha
-- 2021-JUN-11		2		Fix to Insert to #FullSet for MARatingOptionID							Bob Knadler
-- 2021-JEL-07	    3		Created temptables														Deepali
-- 2021-AUG-12		4		Code to give #FullSet full complement of biddable demogs so that		Bob Knadler
--								spCalcProjectionFactors won't return empty sets
-- 2021-AUG-15		5		Rewrite of revision #4 to handle sp being passed @MARatingOptionID=3	Bob Knadler
--								indicating do both 1 & 2
-- 2021-DEC-22		6		Minor update to version 5 fix to reset variables used when looping		Alex Beruscha
--								for MARatingOptionID = 3
-- 2023-FEB-01		7		Added AND sfs.PlanYear = dbo.fnGetBidYear()								Phillip Leigh
-- 2024-OCT-22		8		Cost Share Basis: Add new fields from table SavedDFClaims				Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spGetBaseData]
    (
    @ForecastID       INT
   ,@MARatingOptionID TINYINT)

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages

        --Variables
        DECLARE @XForecastID INT = @ForecastID;
        DECLARE @XMARatingOptionID TINYINT = @MARatingOptionID;
        DECLARE @MARtgOptnValue TINYINT; -- Will loop through MARatingOptionIDs 1 and 2 if sp is passed @MARatingOptionID = 3 to do both
        DECLARE @NoDEType0 BIT = 0; --DEType0 is Non-DE#
        DECLARE @NoDEType1 BIT = 0; --DEType1 is DE#
        DECLARE @PlanLvl VARCHAR(10) = 'Plan Lvl';
        DECLARE @DeemedNonRisk VARCHAR(20) = 'Deemed NonRisk';


        ---------------------------------------------------------------------------------------------------------------
        --                                       Data Cuts				                                             --
        ---------------------------------------------------------------------------------------------------------------
        -----  Populate temp table with relevant Grouper to Provider Mapping info eventually defined through the PREP uploaded
        IF (SELECT  OBJECT_ID ('tempdb..#RegionGrouperMap')) IS NOT NULL
            DROP TABLE #RegionGrouperMap;
        CREATE TABLE #RegionGrouperMap
            (GrouperID  VARCHAR(8)   NOT NULL
            ,[Provider] VARCHAR(255) NOT NULL PRIMARY KEY (GrouperID));
        INSERT INTO #RegionGrouperMap
            (GrouperID
            ,[Provider])
        SELECT      SRG.GrouperID
                   ,SRG.[Provider]
        FROM        dbo.SavedForecastSetup SFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanInfo SPI WITH (NOLOCK)
               ON SFS.PlanInfoID = SPI.PlanInfoID
       INNER JOIN   dbo.SavedMarketInfo SMI WITH (NOLOCK)
               ON SMI.ActuarialMarketID = SPI.ActuarialMarketID
       INNER JOIN   dbo.SavedRegionGrouperMapping SRG WITH (NOLOCK)
               ON SRG.PlanYear = SPI.PlanYear
                  AND   SRG.ActuarialRegionID = SMI.ActuarialRegionID
        WHERE       SFS.ForecastID = @XForecastID
                    AND SFS.PlanYear = dbo.fnGetBidYear ();

        -----  Populate temp table with relevant base plans to pull
        IF (SELECT  OBJECT_ID ('tempdb..#BasePlans')) IS NOT NULL
            DROP TABLE #BasePlans;
        CREATE TABLE #BasePlans
            (MARatingOptionID TINYINT NOT NULL
            ,PlanInfoID       INT     NOT NULL
            ,DFVersionID      INT     NOT NULL PRIMARY KEY CLUSTERED (
                                               PlanInfoID ASC
                                              ,MARatingOptionID ASC
                                              ,DFVersionID ASC));
        INSERT INTO #BasePlans
            (MARatingOptionID
            ,PlanInfoID
            ,DFVersionID)
        SELECT      DFS.MARatingOptionID
                   ,DFS.PlanInfoID
                   ,SFS.DFVersionID
        FROM        dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedForecastSetup SFS WITH (NOLOCK)
               ON SFS.ForecastID = DFS.ForecastID
        WHERE       DFS.ForecastID = @XForecastID
        GROUP BY    DFS.MARatingOptionID
                   ,DFS.PlanInfoID
                   ,SFS.DFVersionID;

        IF @XMARatingOptionID < 3
            -- Delete any unnecessary group by records
            BEGIN
                DELETE  FROM #BasePlans WHERE   MARatingOptionID = 3 - @XMARatingOptionID;
            END;

        ---------------------------------------------------------------------------------------------------------------
        --                                       Claims Table				                                         --
        ---------------------------------------------------------------------------------------------------------------
        -----  Populate temp table with subset of relevant base claims to improve runtime
        IF (SELECT  OBJECT_ID ('tempdb..#SavedDFClaims')) IS NOT NULL
            DROP TABLE #SavedDFClaims;
        IF (SELECT  OBJECT_ID ('tempdb..#tempSavedDFClaims')) IS NOT NULL
            DROP TABLE #tempSavedDFClaims;
        CREATE TABLE #tempSavedDFClaims
            (MARatingOptionID                             TINYINT        NOT NULL
            ,IncurredYear                                 INT            NOT NULL
            ,DemogIndicator                               TINYINT        NOT NULL
            ,[Provider]                                   VARCHAR(255)   NOT NULL
            ,BenefitCategoryID                            SMALLINT       NOT NULL
            ,Paid                                         DECIMAL(19, 9) NULL
            ,MbrCS                                        DECIMAL(19, 9) NULL
            ,PymtReductionAmt                             DECIMAL(19, 9) NULL
            ,AdmitCnt                                     DECIMAL(19, 9) NULL
            ,UnitCnt                                      DECIMAL(19, 9) NULL
            ,EncounterMbrCS                               DECIMAL(19, 9) NULL
            ,EncounterAdmitCnt                            DECIMAL(19, 9) NULL
            ,EncounterUnitCnt                             DECIMAL(19, 9) NULL
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9) NULL
            ,DelegatedEncounterAdmitCnt                   DECIMAL(19, 9) NULL
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9) NULL
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9) NULL
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9) NULL
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9) NULL
            ,CapMSBs                                      DECIMAL(19, 9) NULL
            ,CapProviderRewards                           DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9) NULL
            ,PartDCapAdj                                  DECIMAL(19, 9) NULL
            ,PartDCapAdjDelegated                         DECIMAL(19, 9) NULL
            ,MSCapAdj                                     DECIMAL(19, 9) NULL
            ,MSCapAdjDelegated                            DECIMAL(19, 9) NULL
            ,SubCapAdj                                    DECIMAL(19, 9) NULL
            ,SubCapAdjExclude                             DECIMAL(19, 9) NULL
            ,MedicaidAdjPaid                              DECIMAL(19, 9) NULL
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9) NULL
            ,ImplicitMarginPaid                           DECIMAL(19, 9) NULL
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9) NULL
            ,AdditiveAdjPaid                              DECIMAL(19, 9) NULL
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9) NULL
            ,AdditiveAdjAdmits                            DECIMAL(19, 9) NULL
            ,AdditiveAdjUnits                             DECIMAL(19, 9) NULL
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9) NULL
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9) NULL
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9) NULL
            ,UCAdmitsAdj                                  DECIMAL(19, 9) NULL
            ,UCUnitsAdj                                   DECIMAL(19, 9) NULL
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9) NULL
            ,PartBRxRebatesQN                             DECIMAL(19, 9) NULL
            ,RelatedPartiesAdj                            DECIMAL(19, 9) NULL
            ,ProfitAdj                                    DECIMAL(19, 9) NULL
            ,MSBPaid                                      DECIMAL(19, 9) NULL
            ,MSBMbrCS                                     DECIMAL(19, 9) NULL
            ,MSBUnits                                     DECIMAL(19, 9) NULL
            ,MSBReductionCap                              DECIMAL(19, 9) NULL
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9) NULL
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9) NULL
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9) NULL
            ,MSBReductionQuality                          DECIMAL(19, 9) NULL);
        CREATE TABLE #SavedDFClaims
            (MARatingOptionID                             TINYINT        NOT NULL
            ,IncurredYear                                 INT            NOT NULL
            ,DemogIndicator                               TINYINT        NOT NULL
            ,[Provider]                                   VARCHAR(255)   NOT NULL
            ,BenefitCategoryID                            SMALLINT       NOT NULL
            ,Paid                                         DECIMAL(19, 9) NULL
            ,MbrCS                                        DECIMAL(19, 9) NULL
            ,PymtReductionAmt                             DECIMAL(19, 9) NULL
            ,AdmitCnt                                     DECIMAL(19, 9) NULL
            ,UnitCnt                                      DECIMAL(19, 9) NULL
            ,EncounterMbrCS                               DECIMAL(19, 9) NULL
            ,EncounterAdmitCnt                            DECIMAL(19, 9) NULL
            ,EncounterUnitCnt                             DECIMAL(19, 9) NULL
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9) NULL
            ,DelegatedEncounterAdmitCnt                   DECIMAL(19, 9) NULL
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9) NULL
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9) NULL
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9) NULL
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9) NULL
            ,CapMSBs                                      DECIMAL(19, 9) NULL
            ,CapProviderRewards                           DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9) NULL
            ,PartDCapAdj                                  DECIMAL(19, 9) NULL
            ,PartDCapAdjDelegated                         DECIMAL(19, 9) NULL
            ,MSCapAdj                                     DECIMAL(19, 9) NULL
            ,MSCapAdjDelegated                            DECIMAL(19, 9) NULL
            ,SubCapAdj                                    DECIMAL(19, 9) NULL
            ,SubCapAdjExclude                             DECIMAL(19, 9) NULL
            ,MedicaidAdjPaid                              DECIMAL(19, 9) NULL
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9) NULL
            ,ImplicitMarginPaid                           DECIMAL(19, 9) NULL
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9) NULL
            ,AdditiveAdjPaid                              DECIMAL(19, 9) NULL
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9) NULL
            ,AdditiveAdjAdmits                            DECIMAL(19, 9) NULL
            ,AdditiveAdjUnits                             DECIMAL(19, 9) NULL
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9) NULL
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9) NULL
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9) NULL
            ,UCAdmitsAdj                                  DECIMAL(19, 9) NULL
            ,UCUnitsAdj                                   DECIMAL(19, 9) NULL
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9) NULL
            ,PartBRxRebatesQN                             DECIMAL(19, 9) NULL
            ,RelatedPartiesAdj                            DECIMAL(19, 9) NULL
            ,ProfitAdj                                    DECIMAL(19, 9) NULL
            ,MSBPaid                                      DECIMAL(19, 9) NULL
            ,MSBMbrCS                                     DECIMAL(19, 9) NULL
            ,MSBUnits                                     DECIMAL(19, 9) NULL
            ,MSBReductionCap                              DECIMAL(19, 9) NULL
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9) NULL
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9) NULL
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9) NULL
            ,MSBReductionQuality                          DECIMAL(19, 9) NULL PRIMARY KEY CLUSTERED (
                                                                              BenefitCategoryID ASC
                                                                             ,DemogIndicator ASC
                                                                             ,MARatingOptionID ASC
                                                                             ,[Provider] ASC));
        INSERT INTO #tempSavedDFClaims
        SELECT      BP.MARatingOptionID
                   ,DFC.IncurredYear
                   ,DFC.DemogIndicator
                   ,DFC.GrouperID
                   ,DFC.BenefitCategoryID
                   ,(DFC.Paid) Paid
                   ,(DFC.MbrCS) MbrCS
                   ,(DFC.PymtReductionAmt) PymtReductionAmt
                   ,(DFC.AdmitCnt) AdmitCnt
                   ,(DFC.UnitCnt) UnitCnt
                   ,(DFC.EncounterMbrCS) EncounterMbrCS
                   ,(DFC.EncounterAdmitCnt) EncounterAdmitCnt
                   ,(DFC.EncounterUnitCnt) EncounterUnitCnt
                   ,(DFC.DelegatedEncounterMbrCS) DelegatedEncounterMbrCS
                   ,(DFC.DelegatedEncounterAdmitCnt) DelegatedEncounterAdmitCnt
                   ,(DFC.DelegatedEncounterUnitCnt) DelegatedEncounterUnitCnt
                   ,(DFC.CapDirectPayEPaidClaims) CapDirectPayEPaidClaims
                   ,(DFC.CapDirectPayDEPaidClaims) CapDirectPayDEPaidClaims
                   ,(DFC.CapDirectPayBCAllocated) CapDirectPayBCAllocated
                   ,(DFC.CapDirectPayScenarioAllocated) CapDirectPayScenarioAllocated
                   ,(DFC.CapDirectPayBCAllocatedDelegated) CapDirectPayBCAllocatedDelegated
                   ,(DFC.CapDirectPayScenarioAllocatedDelegated) CapDirectPayScenarioAllocatedDelegated
                   ,(DFC.CapSurplusDeficitEPaidClaims) CapSurplusDeficitEPaidClaims
                   ,(DFC.CapSurplusDeficitDEPaidClaims) CapSurplusDeficitDEPaidClaims
                   ,(DFC.CapSurplusDeficitBCAllocated) CapSurplusDeficitBCAllocated
                   ,(DFC.CapSurplusDeficitScenarioAllocated) CapSurplusDeficitScenarioAllocated
                   ,(DFC.CapSurplusDeficitBCAllocatedDelegated) CapSurplusDeficitBCAllocatedDelegated
                   ,(DFC.CapSurplusDeficitScenarioAllocatedDelegated) CapSurplusDeficitScenarioAllocatedDelegated
                   ,(DFC.CapMSBs) CapMSBs
                   ,(DFC.CapProviderRewards) CapProviderRewards
                   ,(DFC.OPBOtherNonHospitalBCAllocated) OPBOtherNonHospitalBCAllocated
                   ,(DFC.OPBOtherNonHospitalScenarioAllocated) OPBOtherNonHospitalScenarioAllocated
                   ,(DFC.OPBClaimsBuyDownBCAllocated) OPBClaimsBuyDownBCAllocated
                   ,(DFC.OPBClaimsBuyDownScenarioAllocated) OPBClaimsBuyDownScenarioAllocated
                   ,(DFC.OPBProviderClaimSettlementsBCAllocated) OPBProviderClaimSettlementsBCAllocated
                   ,(DFC.OPBProviderClaimSettlementsScenarioAllocated) OPBProviderClaimSettlementsScenarioAllocated
                   ,(DFC.OPBAccessFeesAndOtherBCAllocated) OPBAccessFeesAndOtherBCAllocated
                   ,(DFC.OPBAccessFeesAndOtherScenarioAllocated) OPBAccessFeesAndOtherScenarioAllocated
                   ,(DFC.PartDCapAdj) PartDCapAdj
                   ,(DFC.PartDCapAdjDelegated) PartDCapAdjDelegated
                   ,(DFC.MSCapAdj) MSCapAdj
                   ,(DFC.MSCapAdjDelegated) MSCapAdjDelegated
                   ,(DFC.SubCapAdj) SubCapAdj
                   ,(DFC.SubCapAdjExclude) SubCapAdjExclude
                   ,(DFC.MedicaidAdjPaid) MedicaidAdjPaid
                   ,(DFC.MedicaidAdjMbrCS) MedicaidAdjMbrCS
                   ,(DFC.ImplicitMarginPaid) ImplicitMarginPaid
                   ,(DFC.ImplicitMarginMbrCS) ImplicitMarginMbrCS
                   ,(DFC.AdditiveAdjPaid) AdditiveAdjPaid
                   ,(DFC.AdditiveAdjMbrCS) AdditiveAdjMbrCS
                   ,(DFC.AdditiveAdjAdmits) AdditiveAdjAdmits
                   ,(DFC.AdditiveAdjUnits) AdditiveAdjUnits
                   ,(DFC.ModelOfCareAdjPaid) ModelOfCareAdjPaid
                   ,(DFC.ModelOfCareAdjMbrCS) ModelOfCareAdjMbrCS
                   ,(DFC.ModelOfCareAdjUnits) ModelOfCareAdjUnits
                   ,(DFC.UCAdmitsAdj) UCAdmitsAdj
                   ,(DFC.UCUnitsAdj) UCUnitsAdj
                   ,(DFC.PartBRxRebatesPharmacy) PartBRxRebatesPharmacy
                   ,(DFC.PartBRxRebatesQN) PartBRxRebatesQN
                   ,(DFC.RelatedPartiesAdj) RelatedPartiesAdj
                   ,(DFC.ProfitAdj) ProfitAdj
                   ,(DFC.MSBPaid) MSBPaid
                   ,(DFC.MSBMbrCS) MSBMbrCS
                   ,(DFC.MSBUnits) MSBUnits
                   ,(DFC.MSBReductionCap) MSBReductionCap
                   ,(DFC.MSBReductionClaimsPaid) MSBReductionClaimsPaid
                   ,(DFC.MSBReductionClaimsMbrCS) MSBReductionClaimsMbrCS
                   ,(DFC.MSBReductionClaimsUnits) MSBReductionClaimsUnits
                   ,(DFC.MSBReductionQuality) MSBReductionQuality
        FROM        #BasePlans BP
       INNER JOIN   dbo.SavedDFClaims DFC WITH (NOLOCK)
               ON DFC.PlanInfoID = BP.PlanInfoID
                  AND   DFC.DFVersionID = BP.DFVersionID
                  AND   DFC.BenefitCategoryID < 1000 --Exclude MSBs
                  AND   DFC.BenefitCategoryID > 0
                  AND   DFC.DemogIndicator IN (1, 2, 3);


        -- INSERT DE# and NonDE# at the plan level for both experience and manual plans
        INSERT INTO #SavedDFClaims
        SELECT      MARatingOptionID
                   ,DFC.IncurredYear
                   ,DFC.DemogIndicator
                   ,DFC.[Provider] [Provider]
                   ,DFC.BenefitCategoryID
                   ,SUM (DFC.Paid) Paid
                   ,SUM (DFC.MbrCS) MbrCS
                   ,SUM (DFC.PymtReductionAmt) PymtReductionAmt
                   ,SUM (DFC.AdmitCnt) AdmitCnt
                   ,SUM (DFC.UnitCnt) UnitCnt
                   ,SUM (DFC.EncounterMbrCS) EncounterMbrCS
                   ,SUM (DFC.EncounterAdmitCnt) EncounterAdmitCnt
                   ,SUM (DFC.EncounterUnitCnt) EncounterUnitCnt
                   ,SUM (DFC.DelegatedEncounterMbrCS) DelegatedEncounterMbrCS
                   ,SUM (DFC.DelegatedEncounterAdmitCnt) DelegatedEncounterAdmitCnt
                   ,SUM (DFC.DelegatedEncounterUnitCnt) DelegatedEncounterUnitCnt
                   ,SUM (DFC.CapDirectPayEPaidClaims) CapDirectPayEPaidClaims
                   ,SUM (DFC.CapDirectPayDEPaidClaims) CapDirectPayDEPaidClaims
                   ,SUM (DFC.CapDirectPayBCAllocated) CapDirectPayBCAllocated
                   ,SUM (DFC.CapDirectPayScenarioAllocated) CapDirectPayScenarioAllocated
                   ,SUM (DFC.CapDirectPayBCAllocatedDelegated) CapDirectPayBCAllocatedDelegated
                   ,SUM (DFC.CapDirectPayScenarioAllocatedDelegated) CapDirectPayScenarioAllocatedDelegated
                   ,SUM (DFC.CapSurplusDeficitEPaidClaims) CapSurplusDeficitEPaidClaims
                   ,SUM (DFC.CapSurplusDeficitDEPaidClaims) CapSurplusDeficitDEPaidClaims
                   ,SUM (DFC.CapSurplusDeficitBCAllocated) CapSurplusDeficitBCAllocated
                   ,SUM (DFC.CapSurplusDeficitScenarioAllocated) CapSurplusDeficitScenarioAllocated
                   ,SUM (DFC.CapSurplusDeficitBCAllocatedDelegated) CapSurplusDeficitBCAllocatedDelegated
                   ,SUM (DFC.CapSurplusDeficitScenarioAllocatedDelegated) CapSurplusDeficitScenarioAllocatedDelegated
                   ,SUM (DFC.CapMSBs) CapMSBs
                   ,SUM (DFC.CapProviderRewards) CapProviderRewards
                   ,SUM (DFC.OPBOtherNonHospitalBCAllocated) OPBOtherNonHospitalBCAllocated
                   ,SUM (DFC.OPBOtherNonHospitalScenarioAllocated) OPBOtherNonHospitalScenarioAllocated
                   ,SUM (DFC.OPBClaimsBuyDownBCAllocated) OPBClaimsBuyDownBCAllocated
                   ,SUM (DFC.OPBClaimsBuyDownScenarioAllocated) OPBClaimsBuyDownScenarioAllocated
                   ,SUM (DFC.OPBProviderClaimSettlementsBCAllocated) OPBProviderClaimSettlementsBCAllocated
                   ,SUM (DFC.OPBProviderClaimSettlementsScenarioAllocated) OPBProviderClaimSettlementsScenarioAllocated
                   ,SUM (DFC.OPBAccessFeesAndOtherBCAllocated) OPBAccessFeesAndOtherBCAllocated
                   ,SUM (DFC.OPBAccessFeesAndOtherScenarioAllocated) OPBAccessFeesAndOtherScenarioAllocated
                   ,SUM (DFC.PartDCapAdj) PartDCapAdj
                   ,SUM (DFC.PartDCapAdjDelegated) PartDCapAdjDelegated
                   ,SUM (DFC.MSCapAdj) MSCapAdj
                   ,SUM (DFC.MSCapAdjDelegated) MSCapAdjDelegated
                   ,SUM (DFC.SubCapAdj) SubCapAdj
                   ,SUM (DFC.SubCapAdjExclude) SubCapAdjExclude
                   ,SUM (DFC.MedicaidAdjPaid) MedicaidAdjPaid
                   ,SUM (DFC.MedicaidAdjMbrCS) MedicaidAdjMbrCS
                   ,SUM (DFC.ImplicitMarginPaid) ImplicitMarginPaid
                   ,SUM (DFC.ImplicitMarginMbrCS) ImplicitMarginMbrCS
                   ,SUM (DFC.AdditiveAdjPaid) AdditiveAdjPaid
                   ,SUM (DFC.AdditiveAdjMbrCS) AdditiveAdjMbrCS
                   ,SUM (DFC.AdditiveAdjAdmits) AdditiveAdjAdmits
                   ,SUM (DFC.AdditiveAdjUnits) AdditiveAdjUnits
                   ,SUM (DFC.ModelOfCareAdjPaid) ModelOfCareAdjPaid
                   ,SUM (DFC.ModelOfCareAdjMbrCS) ModelOfCareAdjMbrCS
                   ,SUM (DFC.ModelOfCareAdjUnits) ModelOfCareAdjUnits
                   ,SUM (DFC.UCAdmitsAdj) UCAdmitsAdj
                   ,SUM (DFC.UCUnitsAdj) UCUnitsAdj
                   ,SUM (DFC.PartBRxRebatesPharmacy) PartBRxRebatesPharmacy
                   ,SUM (DFC.PartBRxRebatesQN) PartBRxRebatesQN
                   ,SUM (DFC.RelatedPartiesAdj) RelatedPartiesAdj
                   ,SUM (DFC.ProfitAdj) ProfitAdj
                   ,SUM (DFC.MSBPaid) MSBPaid
                   ,SUM (DFC.MSBMbrCS) MSBMbrCS
                   ,SUM (DFC.MSBUnits) MSBUnits
                   ,SUM (DFC.MSBReductionCap) MSBReductionCap
                   ,SUM (DFC.MSBReductionClaimsPaid) MSBReductionClaimsPaid
                   ,SUM (DFC.MSBReductionClaimsMbrCS) MSBReductionClaimsMbrCS
                   ,SUM (DFC.MSBReductionClaimsUnits) MSBReductionClaimsUnits
                   ,SUM (DFC.MSBReductionQuality) MSBReductionQuality
        FROM        #tempSavedDFClaims DFC
        WHERE       DFC.DemogIndicator IN (1, 2)
        GROUP BY    MARatingOptionID
                   ,DFC.IncurredYear
                   ,DFC.DemogIndicator
                   ,[Provider]
                   ,DFC.BenefitCategoryID;

        --  If Manual Rate, then Combined DE#&NonDE# (DemogIndicator = 3) is summed from GrouperID to 'Plan Lvl'
        IF (@XMARatingOptionID = 2 OR   @XMARatingOptionID = 3)
            BEGIN
                INSERT INTO #SavedDFClaims
                SELECT      MARatingOptionID
                           ,DFC.IncurredYear
                           ,DFC.DemogIndicator
                           ,@PlanLvl [Provider]
                           ,DFC.BenefitCategoryID
                           ,SUM (DFC.Paid) Paid
                           ,SUM (DFC.MbrCS) MbrCS
                           ,SUM (DFC.PymtReductionAmt) PymtReductionAmt
                           ,SUM (DFC.AdmitCnt) AdmitCnt
                           ,SUM (DFC.UnitCnt) UnitCnt
                           ,SUM (DFC.EncounterMbrCS) EncounterMbrCS
                           ,SUM (DFC.EncounterAdmitCnt) EncounterAdmitCnt
                           ,SUM (DFC.EncounterUnitCnt) EncounterUnitCnt
                           ,SUM (DFC.DelegatedEncounterMbrCS) DelegatedEncounterMbrCS
                           ,SUM (DFC.DelegatedEncounterAdmitCnt) DelegatedEncounterAdmitCnt
                           ,SUM (DFC.DelegatedEncounterUnitCnt) DelegatedEncounterUnitCnt
                           ,SUM (DFC.CapDirectPayEPaidClaims) CapDirectPayEPaidClaims
                           ,SUM (DFC.CapDirectPayDEPaidClaims) CapDirectPayDEPaidClaims
                           ,SUM (DFC.CapDirectPayBCAllocated) CapDirectPayBCAllocated
                           ,SUM (DFC.CapDirectPayScenarioAllocated) CapDirectPayScenarioAllocated
                           ,SUM (DFC.CapDirectPayBCAllocatedDelegated) CapDirectPayBCAllocatedDelegated
                           ,SUM (DFC.CapDirectPayScenarioAllocatedDelegated) CapDirectPayScenarioAllocatedDelegated
                           ,SUM (DFC.CapSurplusDeficitEPaidClaims) CapSurplusDeficitEPaidClaims
                           ,SUM (DFC.CapSurplusDeficitDEPaidClaims) CapSurplusDeficitDEPaidClaims
                           ,SUM (DFC.CapSurplusDeficitBCAllocated) CapSurplusDeficitBCAllocated
                           ,SUM (DFC.CapSurplusDeficitScenarioAllocated) CapSurplusDeficitScenarioAllocated
                           ,SUM (DFC.CapSurplusDeficitBCAllocatedDelegated) CapSurplusDeficitBCAllocatedDelegated
                           ,SUM (DFC.CapSurplusDeficitScenarioAllocatedDelegated) CapSurplusDeficitScenarioAllocatedDelegated
                           ,SUM (DFC.CapMSBs) CapMSBs
                           ,SUM (DFC.CapProviderRewards) CapProviderRewards
                           ,SUM (DFC.OPBOtherNonHospitalBCAllocated) OPBOtherNonHospitalBCAllocated
                           ,SUM (DFC.OPBOtherNonHospitalScenarioAllocated) OPBOtherNonHospitalScenarioAllocated
                           ,SUM (DFC.OPBClaimsBuyDownBCAllocated) OPBClaimsBuyDownBCAllocated
                           ,SUM (DFC.OPBClaimsBuyDownScenarioAllocated) OPBClaimsBuyDownScenarioAllocated
                           ,SUM (DFC.OPBProviderClaimSettlementsBCAllocated) OPBProviderClaimSettlementsBCAllocated
                           ,SUM (DFC.OPBProviderClaimSettlementsScenarioAllocated) OPBProviderClaimSettlementsScenarioAllocated
                           ,SUM (DFC.OPBAccessFeesAndOtherBCAllocated) OPBAccessFeesAndOtherBCAllocated
                           ,SUM (DFC.OPBAccessFeesAndOtherScenarioAllocated) OPBAccessFeesAndOtherScenarioAllocated
                           ,SUM (DFC.PartDCapAdj) PartDCapAdj
                           ,SUM (DFC.PartDCapAdjDelegated) PartDCapAdjDelegated
                           ,SUM (DFC.MSCapAdj) MSCapAdj
                           ,SUM (DFC.MSCapAdjDelegated) MSCapAdjDelegated
                           ,SUM (DFC.SubCapAdj) SubCapAdj
                           ,SUM (DFC.SubCapAdjExclude) SubCapAdjExclude
                           ,SUM (DFC.MedicaidAdjPaid) MedicaidAdjPaid
                           ,SUM (DFC.MedicaidAdjMbrCS) MedicaidAdjMbrCS
                           ,SUM (DFC.ImplicitMarginPaid) ImplicitMarginPaid
                           ,SUM (DFC.ImplicitMarginMbrCS) ImplicitMarginMbrCS
                           ,SUM (DFC.AdditiveAdjPaid) AdditiveAdjPaid
                           ,SUM (DFC.AdditiveAdjMbrCS) AdditiveAdjMbrCS
                           ,SUM (DFC.AdditiveAdjAdmits) AdditiveAdjAdmits
                           ,SUM (DFC.AdditiveAdjUnits) AdditiveAdjUnits
                           ,SUM (DFC.ModelOfCareAdjPaid) ModelOfCareAdjPaid
                           ,SUM (DFC.ModelOfCareAdjMbrCS) ModelOfCareAdjMbrCS
                           ,SUM (DFC.ModelOfCareAdjUnits) ModelOfCareAdjUnits
                           ,SUM (DFC.UCAdmitsAdj) UCAdmitsAdj
                           ,SUM (DFC.UCUnitsAdj) UCUnitsAdj
                           ,SUM (DFC.PartBRxRebatesPharmacy) PartBRxRebatesPharmacy
                           ,SUM (DFC.PartBRxRebatesQN) PartBRxRebatesQN
                           ,SUM (DFC.RelatedPartiesAdj) RelatedPartiesAdj
                           ,SUM (DFC.ProfitAdj) ProfitAdj
                           ,SUM (DFC.MSBPaid) MSBPaid
                           ,SUM (DFC.MSBMbrCS) MSBMbrCS
                           ,SUM (DFC.MSBUnits) MSBUnits
                           ,SUM (DFC.MSBReductionCap) MSBReductionCap
                           ,SUM (DFC.MSBReductionClaimsPaid) MSBReductionClaimsPaid
                           ,SUM (DFC.MSBReductionClaimsMbrCS) MSBReductionClaimsMbrCS
                           ,SUM (DFC.MSBReductionClaimsUnits) MSBReductionClaimsUnits
                           ,SUM (DFC.MSBReductionQuality) MSBReductionQuality
                FROM        #tempSavedDFClaims DFC
                WHERE       MARatingOptionID = 2 --Manual
                            AND DFC.DemogIndicator = 3
                GROUP BY    MARatingOptionID
                           ,DFC.IncurredYear
                           ,DFC.DemogIndicator
                           ,DFC.BenefitCategoryID;
            END;

        --  If Experience Rate, then Combined DE#&NonDE# (DemogIndicator = 3) is summed from GrouperID to Provider
        IF (@XMARatingOptionID = 1 OR   @XMARatingOptionID = 3)
            BEGIN
                INSERT INTO #SavedDFClaims
                SELECT      MARatingOptionID
                           ,DFC.IncurredYear
                           ,DFC.DemogIndicator
                           ,ISNULL (RGM.[Provider], @DeemedNonRisk) [Provider]
                           ,DFC.BenefitCategoryID
                           ,SUM (DFC.Paid) Paid
                           ,SUM (DFC.MbrCS) MbrCS
                           ,SUM (DFC.PymtReductionAmt) PymtReductionAmt
                           ,SUM (DFC.AdmitCnt) AdmitCnt
                           ,SUM (DFC.UnitCnt) UnitCnt
                           ,SUM (DFC.EncounterMbrCS) EncounterMbrCS
                           ,SUM (DFC.EncounterAdmitCnt) EncounterAdmitCnt
                           ,SUM (DFC.EncounterUnitCnt) EncounterUnitCnt
                           ,SUM (DFC.DelegatedEncounterMbrCS) DelegatedEncounterMbrCS
                           ,SUM (DFC.DelegatedEncounterAdmitCnt) DelegatedEncounterAdmitCnt
                           ,SUM (DFC.DelegatedEncounterUnitCnt) DelegatedEncounterUnitCnt
                           ,SUM (DFC.CapDirectPayEPaidClaims) CapDirectPayEPaidClaims
                           ,SUM (DFC.CapDirectPayDEPaidClaims) CapDirectPayDEPaidClaims
                           ,SUM (DFC.CapDirectPayBCAllocated) CapDirectPayBCAllocated
                           ,SUM (DFC.CapDirectPayScenarioAllocated) CapDirectPayScenarioAllocated
                           ,SUM (DFC.CapDirectPayBCAllocatedDelegated) CapDirectPayBCAllocatedDelegated
                           ,SUM (DFC.CapDirectPayScenarioAllocatedDelegated) CapDirectPayScenarioAllocatedDelegated
                           ,SUM (DFC.CapSurplusDeficitEPaidClaims) CapSurplusDeficitEPaidClaims
                           ,SUM (DFC.CapSurplusDeficitDEPaidClaims) CapSurplusDeficitDEPaidClaims
                           ,SUM (DFC.CapSurplusDeficitBCAllocated) CapSurplusDeficitBCAllocated
                           ,SUM (DFC.CapSurplusDeficitScenarioAllocated) CapSurplusDeficitScenarioAllocated
                           ,SUM (DFC.CapSurplusDeficitBCAllocatedDelegated) CapSurplusDeficitBCAllocatedDelegated
                           ,SUM (DFC.CapSurplusDeficitScenarioAllocatedDelegated) CapSurplusDeficitScenarioAllocatedDelegated
                           ,SUM (DFC.CapMSBs) CapMSBs
                           ,SUM (DFC.CapProviderRewards) CapProviderRewards
                           ,SUM (DFC.OPBOtherNonHospitalBCAllocated) OPBOtherNonHospitalBCAllocated
                           ,SUM (DFC.OPBOtherNonHospitalScenarioAllocated) OPBOtherNonHospitalScenarioAllocated
                           ,SUM (DFC.OPBClaimsBuyDownBCAllocated) OPBClaimsBuyDownBCAllocated
                           ,SUM (DFC.OPBClaimsBuyDownScenarioAllocated) OPBClaimsBuyDownScenarioAllocated
                           ,SUM (DFC.OPBProviderClaimSettlementsBCAllocated) OPBProviderClaimSettlementsBCAllocated
                           ,SUM (DFC.OPBProviderClaimSettlementsScenarioAllocated) OPBProviderClaimSettlementsScenarioAllocated
                           ,SUM (DFC.OPBAccessFeesAndOtherBCAllocated) OPBAccessFeesAndOtherBCAllocated
                           ,SUM (DFC.OPBAccessFeesAndOtherScenarioAllocated) OPBAccessFeesAndOtherScenarioAllocated
                           ,SUM (DFC.PartDCapAdj) PartDCapAdj
                           ,SUM (DFC.PartDCapAdjDelegated) PartDCapAdjDelegated
                           ,SUM (DFC.MSCapAdj) MSCapAdj
                           ,SUM (DFC.MSCapAdjDelegated) MSCapAdjDelegated
                           ,SUM (DFC.SubCapAdj) SubCapAdj
                           ,SUM (DFC.SubCapAdjExclude) SubCapAdjExclude
                           ,SUM (DFC.MedicaidAdjPaid) MedicaidAdjPaid
                           ,SUM (DFC.MedicaidAdjMbrCS) MedicaidAdjMbrCS
                           ,SUM (DFC.ImplicitMarginPaid) ImplicitMarginPaid
                           ,SUM (DFC.ImplicitMarginMbrCS) ImplicitMarginMbrCS
                           ,SUM (DFC.AdditiveAdjPaid) AdditiveAdjPaid
                           ,SUM (DFC.AdditiveAdjMbrCS) AdditiveAdjMbrCS
                           ,SUM (DFC.AdditiveAdjAdmits) AdditiveAdjAdmits
                           ,SUM (DFC.AdditiveAdjUnits) AdditiveAdjUnits
                           ,SUM (DFC.ModelOfCareAdjPaid) ModelOfCareAdjPaid
                           ,SUM (DFC.ModelOfCareAdjMbrCS) ModelOfCareAdjMbrCS
                           ,SUM (DFC.ModelOfCareAdjUnits) ModelOfCareAdjUnits
                           ,SUM (DFC.UCAdmitsAdj) UCAdmitsAdj
                           ,SUM (DFC.UCUnitsAdj) UCUnitsAdj
                           ,SUM (DFC.PartBRxRebatesPharmacy) PartBRxRebatesPharmacy
                           ,SUM (DFC.PartBRxRebatesQN) PartBRxRebatesQN
                           ,SUM (DFC.RelatedPartiesAdj) RelatedPartiesAdj
                           ,SUM (DFC.ProfitAdj) ProfitAdj
                           ,SUM (DFC.MSBPaid) MSBPaid
                           ,SUM (DFC.MSBMbrCS) MSBMbrCS
                           ,SUM (DFC.MSBUnits) MSBUnits
                           ,SUM (DFC.MSBReductionCap) MSBReductionCap
                           ,SUM (DFC.MSBReductionClaimsPaid) MSBReductionClaimsPaid
                           ,SUM (DFC.MSBReductionClaimsMbrCS) MSBReductionClaimsMbrCS
                           ,SUM (DFC.MSBReductionClaimsUnits) MSBReductionClaimsUnits
                           ,SUM (DFC.MSBReductionQuality) MSBReductionQuality
                FROM        #tempSavedDFClaims DFC
                LEFT JOIN   #RegionGrouperMap RGM
                       ON DFC.[Provider] = RGM.GrouperID
                WHERE       DFC.DemogIndicator = 3
                            AND MARatingOptionID = 1    --Experience
                GROUP BY    MARatingOptionID
                           ,DFC.IncurredYear
                           ,DFC.DemogIndicator
                           ,ISNULL (RGM.[Provider], @DeemedNonRisk)
                           ,DFC.BenefitCategoryID;
            END;

        ---------------------------------------------------------------------------------------------------------------
        --                                       Membership Table				                                     --
        ---------------------------------------------------------------------------------------------------------------
        -----  Populate temp table with subset of relevant member months to improve runtime
        IF (SELECT  OBJECT_ID ('tempdb..#SavedDFFinance')) IS NOT NULL
            DROP TABLE #SavedDFFinance;
        IF (SELECT  OBJECT_ID ('tempdb..#tempSavedDFFinance')) IS NOT NULL
            DROP TABLE #tempSavedDFFinance;
        CREATE TABLE #tempSavedDFFinance
            (MARatingOptionID TINYINT      NOT NULL
            ,IncurredYear     INT          NOT NULL
            ,DemogIndicator   TINYINT      NOT NULL
            ,[Provider]       VARCHAR(255) NOT NULL
            ,MemberMonths     INT          NOT NULL);
        CREATE TABLE #SavedDFFinance
            (MARatingOptionID TINYINT      NOT NULL
            ,IncurredYear     INT          NOT NULL
            ,DemogIndicator   TINYINT      NOT NULL
            ,[Provider]       VARCHAR(255) NOT NULL
            ,MemberMonths     INT          NOT NULL PRIMARY KEY CLUSTERED (
                                                    MARatingOptionID ASC
                                                   ,DemogIndicator ASC
                                                   ,[Provider] ASC));

        INSERT INTO #tempSavedDFFinance
            (MARatingOptionID
            ,IncurredYear
            ,DemogIndicator
            ,[Provider]
            ,MemberMonths)
        SELECT      BP.MARatingOptionID
                   ,DFF.IncurredYear
                   ,DFF.DemogIndicator
                   ,DFF.GrouperID [Provider]
                   ,(DFF.MemberMonths)
        FROM        #BasePlans BP
       INNER JOIN   dbo.SavedDFFinance DFF WITH (NOLOCK)
               ON DFF.PlanInfoID = BP.PlanInfoID
                  AND   DFF.DFVersionID = BP.DFVersionID
                  AND   DFF.DemogIndicator IN (1, 2, 3);

        INSERT INTO #SavedDFFinance
            (MARatingOptionID
            ,IncurredYear
            ,DemogIndicator
            ,[Provider]
            ,MemberMonths)
        SELECT      MARatingOptionID
                   ,DFF.IncurredYear
                   ,DFF.DemogIndicator
                   ,[Provider]
                   ,SUM (DFF.MemberMonths) AS MemberMonths
        FROM        #tempSavedDFFinance DFF
        WHERE       DFF.DemogIndicator IN (1, 2)
        GROUP BY    MARatingOptionID
                   ,DFF.IncurredYear
                   ,DFF.DemogIndicator
                   ,DFF.[Provider];

        --  If Manual Rate, then Combined DE#&NonDE# (DemogIndicator = 3) is summed from GrouperID to 'Plan Lvl'
        IF (@XMARatingOptionID = 2 OR   @XMARatingOptionID = 3)
            BEGIN
                INSERT INTO #SavedDFFinance
                    (MARatingOptionID
                    ,IncurredYear
                    ,DemogIndicator
                    ,[Provider]
                    ,MemberMonths)
                SELECT      MARatingOptionID
                           ,DFF.IncurredYear
                           ,DFF.DemogIndicator
                           ,@PlanLvl [Provider]
                           ,SUM (DFF.MemberMonths) AS MemberMonths
                FROM        #tempSavedDFFinance DFF
                WHERE       DFF.DemogIndicator = 3
                            AND MARatingOptionID = 2
                GROUP BY    MARatingOptionID
                           ,DFF.IncurredYear
                           ,DFF.DemogIndicator;
            END;

        --  If Experience Rate, then Combined DE#&NonDE# (DemogIndicator = 3) is summed from GrouperID to Provider
        IF (@XMARatingOptionID = 1 OR   @XMARatingOptionID = 3)
            BEGIN
                INSERT INTO #SavedDFFinance
                    (MARatingOptionID
                    ,IncurredYear
                    ,DemogIndicator
                    ,[Provider]
                    ,MemberMonths)
                SELECT      MARatingOptionID
                           ,DFF.IncurredYear
                           ,DFF.DemogIndicator
                           ,ISNULL (RGM.[Provider], @DeemedNonRisk) [Provider]
                           ,SUM (DFF.MemberMonths) AS MemberMonths
                FROM        #tempSavedDFFinance DFF
                LEFT JOIN   #RegionGrouperMap RGM
                       ON DFF.[Provider] = RGM.GrouperID
                WHERE       DFF.DemogIndicator = 3
                            AND MARatingOptionID = 1
                GROUP BY    MARatingOptionID
                           ,DFF.IncurredYear
                           ,DFF.DemogIndicator
                           ,ISNULL (RGM.[Provider], @DeemedNonRisk);
            END;

        ---------------------------------------------------------------------------------------------------------------
        --                    Define the full data set so non-existent records are populated		                 --
        ---------------------------------------------------------------------------------------------------------------
        --#2 BK 6/11/21 Include below for proper temp table handling
        IF (SELECT  OBJECT_ID ('tempdb..#FullSet')) IS NOT NULL DROP TABLE #FullSet;
        CREATE TABLE #FullSet
            (MARatingOptionID  TINYINT      NOT NULL
            ,DemogIndicator    TINYINT      NOT NULL
            ,[Provider]        VARCHAR(255) NOT NULL
            ,BenefitCategoryID SMALLINT     NOT NULL
            ,BidServiceCatID   SMALLINT     NOT NULL);
        INSERT INTO #FullSet
            (MARatingOptionID
            ,DemogIndicator
            ,[Provider]
            ,BenefitCategoryID
            ,BidServiceCatID)
        SELECT      RatingProviders.MARatingOptionID
                   ,RatingProviders.DemogIndicator
                   ,RatingProviders.[Provider]
                   ,BenCats.BenefitCategoryID
                   ,BenCats.BidServiceCatID
        FROM        (SELECT             ISNULL (DFC.MARatingOptionID, DFF.MARatingOptionID) MARatingOptionID
                                       ,ISNULL (DFC.DemogIndicator, DFF.DemogIndicator) DemogIndicator
                                       ,ISNULL (DFC.[Provider], DFF.[Provider]) [Provider]
                     FROM               #SavedDFClaims DFC
                     FULL OUTER JOIN    #SavedDFFinance DFF
                                  ON DFC.MARatingOptionID = DFF.MARatingOptionID
                                     AND   DFC.DemogIndicator = DFF.DemogIndicator
                                     AND   DFC.[Provider] = DFF.[Provider]
                     GROUP BY           ISNULL (DFC.MARatingOptionID, DFF.MARatingOptionID)
                                       ,ISNULL (DFC.DemogIndicator, DFF.DemogIndicator)
                                       ,ISNULL (DFC.[Provider], DFF.[Provider])) RatingProviders
       CROSS JOIN   (SELECT LBC.BenefitCategoryID
                           ,LBC.BidServiceCatID
                     FROM   dbo.LkpIntBenefitCategory LBC WITH (NOLOCK)
                     WHERE  LBC.IsUsed = 1
                            AND LBC.IsEnabled = 1
                            AND LBC.BenefitCategoryID < 1000) BenCats;


        -- Start #5 BK 8/15/21 - Ensure all Biddable Demog Indicators present in #FullSet for 'Plan Lvl' Provider
        IF @XMARatingOptionID = 3
            BEGIN
                SET @MARtgOptnValue = 1;
                WHILE @MARtgOptnValue < 3
                    BEGIN
                        IF NOT EXISTS (SELECT   1
                                       FROM     #FullSet t1
                                       WHERE    t1.MARatingOptionID = @MARtgOptnValue
                                                AND t1.DemogIndicator = 1   --Biddable Non-DE#
                        )
                            SET @NoDEType0 = 1;

                        IF NOT EXISTS (SELECT   1
                                       FROM     #FullSet t1
                                       WHERE    t1.MARatingOptionID = @MARtgOptnValue
                                                AND t1.DemogIndicator = 2   --Biddable DE#
                        )
                            SET @NoDEType1 = 1;


                        IF @NoDEType1 = 1
                           OR   @NoDEType0 = 1 -- Assumes it can't be both or there'd be no biddable membership
                            BEGIN
                                INSERT INTO #FullSet
                                    (MARatingOptionID
                                    ,DemogIndicator
                                    ,[Provider]
                                    ,BenefitCategoryID
                                    ,BidServiceCatID)
                                SELECT  t2.MARatingOptionID
                                       ,CASE WHEN @NoDEType0 = 1 THEN 1 --Need Non-DE# records for completeness
                                             WHEN @NoDEType1 = 1 THEN 2 --Need DE# records for completeness
                                             ELSE 99                    -- should cause cause join failure and prevent unintended insert
                                        END AS DemogIndicator
                                       ,@PlanLvl AS [Provider]
                                       ,t2.BenefitCategoryID
                                       ,t2.BidServiceCatID
                                FROM    #FullSet t2
                                WHERE   t2.MARatingOptionID = @MARtgOptnValue
                                        AND t2.DemogIndicator = CASE WHEN @NoDEType0 = 1 THEN 2 -- Use DE# records for common columns on Non-DE# records being added
                                                                     WHEN @NoDEType1 = 1 THEN 1 -- Use Non-DE# records for common columns on DE# records being added
                                                                     ELSE 99                    -- should cause no records to return
                                                                END;
                            END;
                        SET @NoDEType0 = 0;
                        SET @NoDEType1 = 0;
                        SET @MARtgOptnValue = @MARtgOptnValue + 1;
                    END;
            END;
        ELSE -- passed @XMARatingOptionID is 1 or 2, not 3 for both
            BEGIN
                SET @MARtgOptnValue = @XMARatingOptionID;
                IF NOT EXISTS (SELECT   1
                               FROM     #FullSet t1
                               WHERE    t1.MARatingOptionID = @MARtgOptnValue
                                        AND t1.DemogIndicator = 1   --Biddable Non-DE#
                )
                    SET @NoDEType0 = 1;

                IF NOT EXISTS (SELECT   1
                               FROM     #FullSet t1
                               WHERE    t1.MARatingOptionID = @MARtgOptnValue
                                        AND t1.DemogIndicator = 2   --Biddable DE#
                )
                    SET @NoDEType1 = 1;


                IF @NoDEType1 = 1
                   OR   @NoDEType0 = 1 -- Assumes it can't be both or there'd be no biddable membership
                    BEGIN
                        INSERT INTO #FullSet
                            (MARatingOptionID
                            ,DemogIndicator
                            ,[Provider]
                            ,BenefitCategoryID
                            ,BidServiceCatID)
                        SELECT  t2.MARatingOptionID
                               ,CASE WHEN @NoDEType0 = 1 THEN 1 --Need Non-DE# records for completeness
                                     WHEN @NoDEType1 = 1 THEN 2 --Need DE# records for completeness
                                     ELSE 99                    -- should cause cause join failure and prevent unintended insert
                                END AS DemogIndicator
                               ,@PlanLvl AS [Provider]
                               ,t2.BenefitCategoryID
                               ,t2.BidServiceCatID
                        FROM    #FullSet t2
                        WHERE   t2.MARatingOptionID = @MARtgOptnValue
                                AND t2.DemogIndicator = CASE WHEN @NoDEType0 = 1 THEN 2 -- Use DE# records for common columns on Non-DE# records being added
                                                             WHEN @NoDEType1 = 1 THEN 1 -- Use Non-DE# records for common columns on DE# records being added
                                                             ELSE 99                    -- should cause no records to return
                                                        END;
                    END;
            END;
        -- End #5 BK 8/15/21

        --  Insert the Base Plan Totals for Experience and Manual by DE# and NonDE# Demographic Indicators
        SELECT      FS.MARatingOptionID
                   ,DI.DualEligibleTypeID
                   ,FS.[Provider]
                   ,FS.BenefitCategoryID
                   ,FS.BidServiceCatID
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.Paid, 0)), SUM (ISNULL (m.MemberMonths, 0))) Paid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) MbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PymtReductionAmt, 0)), SUM (ISNULL (m.MemberMonths, 0))) PymtReductionAmt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdmitCnt, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) AdmitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UnitCnt, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) UnitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.EncounterMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) EncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) EncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterUnitCnt, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) EncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) DelegatedEncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) DelegatedEncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterUnitCnt, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) DelegatedEncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayEPaidClaims, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapDirectPayEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayDEPaidClaims, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapDirectPayDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapDirectPayBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapDirectPayScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocatedDelegated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapDirectPayBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocatedDelegated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapDirectPayScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitEPaidClaims, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapSurplusDeficitEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitDEPaidClaims, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapSurplusDeficitDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapSurplusDeficitBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapSurplusDeficitScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocatedDelegated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapSurplusDeficitBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocatedDelegated, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapSurplusDeficitScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapMSBs, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapMSBs
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapProviderRewards, 0)), SUM (ISNULL (m.MemberMonths, 0))) CapProviderRewards
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalBCAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBOtherNonHospitalBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalScenarioAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBOtherNonHospitalScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownBCAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBClaimsBuyDownBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownScenarioAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBClaimsBuyDownScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsBCAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBProviderClaimSettlementsBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsScenarioAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBProviderClaimSettlementsScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherBCAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBAccessFeesAndOtherBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherScenarioAllocated, 0)), SUM (ISNULL (m.MemberMonths, 0))) OPBAccessFeesAndOtherScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartDCapAdj, 0)), SUM (ISNULL (m.MemberMonths, 0))) PartDCapAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartDCapAdjDelegated, 0)), SUM (ISNULL (m.MemberMonths, 0))) PartDCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdj, 0)), SUM (ISNULL (m.MemberMonths, 0))) MSCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdjDelegated, 0)), SUM (ISNULL (m.MemberMonths, 0))) MSCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdj, 0)), SUM (ISNULL (m.MemberMonths, 0))) SubCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdjExclude, 0)), SUM (ISNULL (m.MemberMonths, 0))) SubCapAdjExclude
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjPaid, 0)), SUM (ISNULL (m.MemberMonths, 0))) MedicaidAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) MedicaidAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ImplicitMarginPaid, 0)), SUM (ISNULL (m.MemberMonths, 0))) ImplicitMarginPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ImplicitMarginMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) ImplicitMarginMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjPaid, 0)), SUM (ISNULL (m.MemberMonths, 0))) AdditiveAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) AdditiveAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjAdmits, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) AdditiveAdjAdmits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjUnits, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) AdditiveAdjUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ModelOfCareAdjPaid, 0)), SUM (ISNULL (m.MemberMonths, 0))) ModelOfCareAdjPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) ModelOfCareAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjUnits, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) ModelOfCareAdjUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.UCAdmitsAdj, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) UCAdmitsAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UCUnitsAdj, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) UCUnitsAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartBRxRebatesPharmacy, 0)), SUM (ISNULL (m.MemberMonths, 0))) PartBRxRebatesPharmacy
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartBRxRebatesQN, 0)), SUM (ISNULL (m.MemberMonths, 0))) PartBRxRebatesQN
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.RelatedPartiesAdj, 0)), SUM (ISNULL (m.MemberMonths, 0))) RelatedPartiesAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ProfitAdj, 0)), SUM (ISNULL (m.MemberMonths, 0))) ProfitAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBPaid, 0)), SUM (ISNULL (m.MemberMonths, 0))) MSBPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) MSBMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBUnits, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) MSBUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBReductionCap, 0)), SUM (ISNULL (m.MemberMonths, 0))) MSBReductionCap
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsPaid, 0)), SUM (ISNULL (m.MemberMonths, 0))) MSBReductionClaimsPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsMbrCS, 0)), SUM (ISNULL (m.MemberMonths, 0))) MSBReductionClaimsMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsUnits, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) MSBReductionClaimsUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionQuality, 0)) * 12000, SUM (ISNULL (m.MemberMonths, 0))) MSBReductionQuality
                   ,SUM (ISNULL (m.MemberMonths, 0)) MemberMonths
        FROM        #FullSet FS
        LEFT JOIN   #SavedDFClaims DFC
               ON FS.MARatingOptionID = DFC.MARatingOptionID
                  AND   FS.DemogIndicator = DFC.DemogIndicator
                  AND   FS.[Provider] = DFC.[Provider]
                  AND   FS.BenefitCategoryID = DFC.BenefitCategoryID
        LEFT JOIN   #SavedDFFinance m
               ON FS.MARatingOptionID = m.MARatingOptionID
                  AND   FS.DemogIndicator = m.DemogIndicator
                  AND   FS.[Provider] = m.[Provider]
       INNER JOIN   dbo.LkpIntDemogIndicators DI
               ON FS.DemogIndicator = DI.DemogIndicator
        GROUP BY    FS.MARatingOptionID
                   ,DI.DualEligibleTypeID
                   ,FS.[Provider]
                   ,FS.BenefitCategoryID
                   ,FS.BidServiceCatID;

    END;
GO
