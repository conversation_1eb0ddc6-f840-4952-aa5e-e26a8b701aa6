SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spDeletePlanGroups]   
--    
-- AUTHOR: Sur<PERSON>rthy 
--    
-- CREATED DATE: 2024-Oct-30   
-- Type: 
-- DESCRIPTION: Procedure responsible for deleting plan groups 
--    
-- PARAMETERS:    
-- Input: 
-- @GroupID
-- @OutPutResult

-- TABLES:   
--
-- Read:    
--  
-- Write:    
--  PrePricing.PlanGroupMapping
--  PrePricing.PlanGroup 

-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-30		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spDeletePlanGroups]
(
	@GroupID INT
)
AS    
BEGIN    
	SET NOCOUNT ON
	DECLARE	@OutPutResultCode VARCHAR(20)
    DECLARE @OutPutResult VARCHAR(200)     

	BEGIN TRY
	BEGIN TRANSACTION groupdelete	 
		SET @OutPutResultCode='Success' 
		SET @OutPutResult='Group Deleted Successfully.'	 
		DELETE FROM PrePricing.PlanGroupMapping WHERE PlanGroupID=@GroupID;	 
		DELETE FROM PrePricing.PlanGroup WHERE PlanGroupID=@GroupID;
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
		COMMIT TRANSACTION groupdelete;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION groupdelete;
		SET @OutPutResultCode='Error'
		SET @OutPutResult='Error while deleting group.';	
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH
END
GO
