SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO


-- ----------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: spAppUpdatePlanOSBPackageInfo  
--  
-- AUTHOR: James Vijay   
--  
-- CREATED DATE: 2011-Mar-01  
--  
-- DESCRIPTION: Procedure responsible for Updating Package details based on Plan Index.  
--            

-- TYPE: New(moved inline queries to SP)  
--  
-- PARAMETERS:  
-- Input:  
-- @ForecastID  
-- @LastUpdateByID  
-- @PackageIndex  
-- @PlanPackageID  
-- @PlanYearID  


-- TABLES:   
-- Read:  
-- SavedPlanOptionalPackageDetail  
--    

-- Write:  
-- Saved<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ageDetail      

-- VIEWS:  
--  
-- FUNCTIONS:  
--    
-- STORED PROCS:  
--   

-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE    VERSION  CHANGES MADE									DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2011-Mar-01  1   Initial Version									James Vijay  
-- 2012-Jun-29  2   Modified to (insert) new rows instead of
--					update/insert									Senthilkumar
-- 2019-Feb-04	3	PackageIndex & PlanPackageID data type change	Keith Galloway
-- 2023-May-17	4	PlanYearId logic changed						Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------  
CREATE PROCEDURE [dbo].[spAppUpdatePlanOSBPackageInfo]  
 @PlanYearID   SMALLINT,  
 @ForecastID   INT,  
 @PlanPackageID  TINYINT,   
 @PackageIndex  INT,  
 @LastUpdateByID  CHAR(13)
AS  
BEGIN

	SET @PlanYearID = dbo.fnGetBidYear();
   INSERT INTO dbo.SavedPlanOptionalPackageDetail(
   PlanYearID,
   ForecastID,
   PlanPackageID,
   PackageIndex,
   IsHidden,
   LastUpdateByID,
   LastUpdateDatetime
   )
   VALUES 
   (
	   @PlanYearID,
	   @ForecastID,
	   @PlanPackageID,
	   @PackageIndex,
	   0, 
	   @LastUpdateByID, 
	   GetDate()
	)            

END
GO
