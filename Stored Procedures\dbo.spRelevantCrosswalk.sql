SET <PERSON><PERSON>_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spRelevantCrosswalk
--
-- AUTHOR: <PERSON>ame
--
-- CREATED DATE: 2018-Oct-2
-- HEADER UPDATED: 2018-Oct-2
--
-- DESCRIPTION: Generate Relevant crosswalk field for substantiation report
--
-- PARAMETERS:
--	Input:	PlanInfoID and ServiceAreaOptionID
--		
--	Output: None
--
-- RETURNS: 
--		RelevantCrosswalk(
--			ServiceAreaOptionID
--			BidYearPlanInfo
--			BaseYearPlanInfo
--			IsSubstantiationNeeded
--			RelevantCrosswalk)

-- TABLES:
--	Read: 
--		MAAModels -> LkpModelSettings
--		MAAModels -> Lkp<PERSON><PERSON>walSubstantiationMap
--		MAAModels -> SavedPlanInfo
--
--	Write:
--		None
--
-- VIEWS: 
--	Read: 
--		MAAModels -> vwSAMCrosswalk
--
-- FUNCTIONS:
--      None
--
-- STORED PROCS: 
--		NONE
--
-- HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			 VERSION			CHANGES MADE								DEVELOPER								REVIEWER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2018-10-1		1				Initial Version								Daniel Nyatuame                    
-- 2019-02-6		2				Clean up documentation for header section   Daniel Nyatuame       
-- 2024-01-19       3               Added line of code to properly label 
--                                  Special Crosswalk plans                     Zoey Glenn   
--2023-02-16		4				Market Support requested that CY 
--									crosswalks also be included in the methodology	Zoey Glenn
--2024-MAY-05	    5				Added Description For CurrentYearRenewalTypeID = 16	Zoey Glenn
-- ----------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spRelevantCrosswalk] (
	@PlanInfoID SMALLINT = NULL,
    @SAOptionID TINYINT = NULL
)
AS
    BEGIN
        SET NOCOUNT ON
		-- Declare and initialize variables
		DECLARE @saeDescription VARCHAR(20) = '(w SAE)'
		DECLARE @PlanYear INT; --Year for which you want to calculate significance
		SET @PlanYear = (SELECT    [SAMYear] FROM      dbo.LkpModelSettings )
		
		/* Select Current Year contracts(s)*/
		IF ( SELECT OBJECT_ID('tempdb..#EYPlanInfo')
			) IS NOT NULL
			DROP TABLE #EYPlanInfo;
		SELECT PlanInfoID, CPS
		INTO #EYPlanInfo
		FROM    dbo.SavedPlanInfo
		WHERE   PlanYear = @PlanYear - 2

			/* Save county level crosswalks for the chosen plan(s)*/
		IF ( SELECT OBJECT_ID('tempdb..#SAM_Crosswalks')
			) IS NOT NULL
			DROP TABLE #SAM_Crosswalks;
		SELECT DISTINCT
				x.[ServiceAreaOptionID] ,
				x.[SSStateCountyCD] ,
				x.[BaseYearCPS] ,
				BasePlanInfoID = spi.PlanInfoID ,
				x.[CurrentYearCPS] ,
				x.[BidYearCPS] ,
				x.[BidYearPlanInfoID] ,
				x.BidYearRenewalTypeID,
				x.CurrentYearRenewalTypeID
				--x.[IsTermed]
		INTO    #SAM_Crosswalks
		FROM    dbo.vwSAMCrosswalks x
				LEFT JOIN #EYPlanInfo spi ON x.BaseYearCPS = spi.CPS
		WHERE   x.BidYearRenewalTypeID <> 2 --exclude termed plans
				AND (x.BidYearPlanInfoID = @PlanInfoID OR @PlanInfoID IS NULL)
                AND (x.ServiceAreaOptionID = @SAOptionID OR @SAOptionID IS NULL)

		/* Select Base Year, Current Year contract pair(s)*/
		IF ( SELECT OBJECT_ID('tempdb..#CYContracts_Temp')
			) IS NOT NULL
			DROP TABLE #CYContracts_Temp
		SELECT DISTINCT
				EYContract = LEFT(BaseYearCPS, 5),
				CYContract = LEFT(CurrentYearCPS, 5)
		INTO #CYContracts_Temp
		FROM dbo.vwSAMCrosswalks
		WHERE BaseYearCPS IS NOT NULL AND CurrentYearCPS IS NOT NULL

		/* Count EY Contract(s)*/
		IF ( SELECT OBJECT_ID('tempdb..#Cnt_EY')
			) IS NOT NULL
			DROP TABLE #Cnt_EY
		SELECT EYContract,
				Cnts = COUNT(EYContract)
		INTO #Cnt_EY
		FROM #CYContracts_Temp
		GROUP BY EYContract

		/* CY Contract Separation(s) */
		IF ( SELECT OBJECT_ID('tempdb..#CYContracts')
			) IS NOT NULL
			DROP TABLE #CYContracts
		SELECT t1.EYContract,
				t1.CYContract,
				ey.Cnts,
				[IsSeperation] = CASE WHEN t1.EYContract = t1.CYContract THEN 0 ELSE 
						CASE WHEN ey.Cnts = 1 THEN 0 ELSE CASE 
						WHEN t2.CYContract IS NULL THEN 1 ELSE 0 END END END
		INTO #CYContracts
		FROM #CYContracts_Temp t1 LEFT JOIN (SELECT DISTINCT CYContract
		FROM #CYContracts_Temp WHERE CYContract IN (SELECT EYContract FROM #CYContracts_Temp)	) t2 ON t2.CYContract = t1.CYContract
		LEFT JOIN #Cnt_EY ey ON ey.EYContract = t1.EYContract

		/*Drop tables no longer needed*/
		IF ( SELECT OBJECT_ID('tempdb..#CYContracts_Temp')
			) IS NOT NULL
			DROP TABLE #CYContracts_Temp

		IF ( SELECT OBJECT_ID('tempdb..#Cnt_EY')
			) IS NOT NULL
			DROP TABLE #Cnt_EY	
		
		/* Select Bid Year, Current Year contract pair(s)*/
		IF ( SELECT OBJECT_ID('tempdb..#BYContracts_Temp')
			) IS NOT NULL
			DROP TABLE #BYContracts_Temp
		SELECT DISTINCT
			CYContract = LEFT(CurrentYearCPS, 5),
			BYContract = LEFT(BidYearCPS, 5)
		INTO #BYContracts_Temp
		FROM dbo.vwSAMCrosswalks
		WHERE BidYearCPS IS NOT NULL AND CurrentYearCPS IS NOT NULL

		/* Count CY Contract(s)*/
		IF ( SELECT OBJECT_ID('tempdb..#Cnt_CY')
			) IS NOT NULL
			DROP TABLE #Cnt_CY
		SELECT CYContract,
				Cnts = COUNT(CYContract)
		INTO #Cnt_CY
		FROM #BYContracts_Temp
		GROUP BY CYContract

		/* BY Contract Separation(s) */
		IF ( SELECT OBJECT_ID('tempdb..#BYContracts')
			) IS NOT NULL
			DROP TABLE #BYContracts
		SELECT t1.CYContract,
				t1.BYContract,
				cy.Cnts,
				C2 = t2.BYContract,
				[IsSeperation] = CASE WHEN t1.CYContract = t1.BYContract THEN 0 ELSE 
						CASE WHEN cy.Cnts = 1 THEN 0 ELSE CASE 
						WHEN t2.BYContract IS NULL THEN 1 ELSE 0 END END END
		INTO #BYContracts
		FROM #BYContracts_Temp t1 LEFT JOIN (SELECT DISTINCT BYContract
		FROM #BYContracts_Temp WHERE BYContract IN (SELECT CYContract FROM #BYContracts_Temp)	) t2 ON t2.BYContract = t1.BYContract
		LEFT JOIN #Cnt_CY cy ON cy.CYContract = t1.CYContract

		/*Drop tables no longer needed*/
		IF ( SELECT OBJECT_ID('tempdb..#BYContracts_Temp')
			) IS NOT NULL
			DROP TABLE #BYContracts_Temp

		IF ( SELECT OBJECT_ID('tempdb..#Cnt_CY')
			) IS NOT NULL
			DROP TABLE #Cnt_CY

/*************************************************************************************************************************************/
		-- Select Plan Level Crosswalks and add contract information
		IF ( SELECT OBJECT_ID('tempdb..#PLXw_Temp1')
			) IS NOT NULL
			DROP TABLE #PLXw_Temp1;
		SELECT DISTINCT
				v.[ServiceAreaOptionID] ,
				v.BidYearPlanInfoID,
				v.BidYearCPS,
				BidYearContract = LEFT(v.BidYearCPS, 5),
				v.BidYearRenewalTypeID,
				[IsBid=CYContract] = CASE WHEN LEFT(v.BidYearCPS, 5) = LEFT(v.CurrentYearCPS, 5) THEN 1 ELSE 0 END,
				[IsBid=CYCPS] = CASE WHEN v.BidYearCPS = v.CurrentYearCPS THEN 1 ELSE 0 END,
				[IsBidContractSep] = bc.IsSeperation,
				[IsPFFS] = CASE	WHEN p.ProductTypeID IN (4,6) THEN 1 ELSE 0 END,
				v.CurrentYearCPS,
				CurrentYearContract = LEFT(v.CurrentYearCPS, 5),
				v.CurrentYearRenewalTypeID,
				[IsBase=CYContract] = CASE WHEN LEFT(v.CurrentYearCPS, 5) = LEFT(v.BaseYearCPS, 5) THEN 1 ELSE 0 END,
				[IsBase=CYCPS] = CASE WHEN v.CurrentYearCPS = v.BaseYearCPS THEN 1 ELSE 0 END,
				[IsCurContractSep] = cc.IsSeperation,
				v.BasePlanInfoID,
				v.BaseYearCPS,
				BaseYearContract = LEFT(v.BaseYearCPS, 5)
		INTO    #PLXw_Temp1
		FROM    #SAM_Crosswalks v
				LEFT JOIN #CYContracts cc ON cc.EYContract = LEFT(v.BaseYearCPS, 5) AND cc.CYContract = LEFT(v.CurrentYearCPS, 5)
				LEFT JOIN #BYContracts bc ON bc.CYContract = LEFT(v.CurrentYearCPS, 5) AND bc.BYContract = LEFT(v.BidYearCPS, 5)
				LEFT JOIN dbo.SavedPlanInfo p ON v.BidYearPlanInfoID = p.PlanInfoID

		/*-- Add relevant crosswalk information */
		IF ( SELECT OBJECT_ID('tempdb..#PLXw_Temp2')
			) IS NOT NULL
			DROP TABLE #PLXw_Temp2;
		SELECT DISTINCT
				t.[ServiceAreaOptionID] ,
				t.BidYearPlanInfoID,
				t.BidYearCPS,
				t.BidYearContract,
				t.BidYearRenewalTypeID,
				t.[IsBid=CYContract],
				t.[IsBid=CYCPS],
				t.[IsBidContractSep],
				t.[IsPFFS],
				BidRelevantCrosswalkText = bmap.RelevantCrosswalkText,
				--next field could be removed
				[BY SAE/SAR Description] = CASE WHEN t.BidYearRenewalTypeID IN (5,9,13) THEN @saeDescription
					WHEN t.BidYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
					WHEN t.BidYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
					ELSE '' END,
				[BY SAE/SAR] = CASE WHEN t.[IsBid=CYCPS] = 1 THEN 'Plan to Plan Renewal' + CASE WHEN t.BidYearRenewalTypeID IN (5,9,13) THEN @saeDescription
					WHEN t.BidYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
					WHEN t.BidYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
					ELSE '' END ELSE '' END,
				[Include BY Plan] = bmap.IsInPlanDescription,
				[BY Crosswalk] = CASE WHEN --[BY SAE/SAR] 
					t.BidYearRenewalTypeID = 16 THEN 'Special Crosswalk' WHEN 
					CASE WHEN t.[IsBid=CYCPS] = 1 THEN 'Plan to Plan Renewal' + CASE WHEN t.BidYearRenewalTypeID IN (5,9,13) THEN @saeDescription
						WHEN t.BidYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
						WHEN t.BidYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
						ELSE '' END ELSE '' END = '' THEN 
					CASE WHEN t.BidYearRenewalTypeID = 3 AND t.IsPFFS = 1 THEN 'PFFS Exception'
						ELSE bmap.RelevantCrosswalkText END + 
					--[BY SAE/SAR Description] 
					CASE WHEN t.BidYearRenewalTypeID IN (5,9,13) THEN @saeDescription
						WHEN t.BidYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
						WHEN t.BidYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
					ELSE '' END
					+
					CASE WHEN bmap.IsInPlanDescription = 0 THEN ''
						ELSE ' into ' + t.BidYearCPS END 
					ELSE --[BY SAE/SAR] 
					CASE WHEN t.[IsBid=CYCPS] = 1 THEN 'Plan to Plan Renewal' + CASE WHEN t.BidYearRenewalTypeID IN (5,9,13) THEN @saeDescription
						WHEN t.BidYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
						WHEN t.BidYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
					ELSE '' END ELSE '' END
					
					END,
				t.CurrentYearCPS,
				t.CurrentYearContract ,
				t.CurrentYearRenewalTypeID,
				t.[IsBase=CYContract],
				t.[IsBase=CYCPS],
				t.[IsCurContractSep] ,
				CurRelevantCrosswalkText = cmap.RelevantCrosswalkText,
				--next field could be removed
				[CY SAE/SAR Description] = CASE WHEN 
				--Added for CY Special Crosswalks
				t.CurrentYearRenewalTypeID = 16 THEN 'Special Crosswalk' WHEN 
				t.CurrentYearRenewalTypeID IN (5,9,13) THEN @saeDescription
					WHEN t.CurrentYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
					WHEN t.CurrentYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
					ELSE '' END,
				[CY SAE/SAR] = CASE WHEN t.[IsBase=CYCPS] = 1 THEN 'Plan to Plan Renewal' + CASE WHEN t.CurrentYearRenewalTypeID IN (5,9,13) THEN @saeDescription
					WHEN t.CurrentYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
					WHEN t.CurrentYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
					ELSE '' END ELSE '' END,
				[Include CY Plan] = cmap.IsInPlanDescription,
				[CY Crosswalk] = CASE WHEN --[CY SAE/SAR] 
					--Added for CY Special Crosswalks
				     t.CurrentYearRenewalTypeID = 16 THEN 'Special Crosswalk' WHEN 
					CASE WHEN t.[IsBase=CYCPS] = 1 THEN 'Plan to Plan Renewal' + CASE WHEN t.CurrentYearRenewalTypeID IN (5,9,13) THEN @saeDescription
						WHEN t.CurrentYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
						WHEN t.CurrentYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
						ELSE '' END ELSE '' END = '' THEN 
					CASE WHEN t.CurrentYearRenewalTypeID = 3 AND t.IsPFFS = 1 THEN 'PFFS Exception'
						ELSE cmap.RelevantCrosswalkText END + --[CY SAE/SAR Description] 
					CASE WHEN t.CurrentYearRenewalTypeID IN (5,9,13) THEN @saeDescription
						WHEN t.CurrentYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
						WHEN t.CurrentYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
					ELSE '' END
					+
					CASE WHEN cmap.IsInPlanDescription = 0 THEN ''
						ELSE ' into ' + t.CurrentYearCPS END 
					ELSE --[CY SAE/SAR] 
						CASE WHEN t.[IsBase=CYCPS] = 1 THEN 'Plan to Plan Renewal' + CASE WHEN t.CurrentYearRenewalTypeID IN (5,9,13) THEN @saeDescription
						WHEN t.CurrentYearRenewalTypeID IN (6,10,14) THEN '(w SAR)'
						WHEN t.CurrentYearRenewalTypeID IN (7,11,15) THEN '(w SAE and SAR)'
						ELSE '' END ELSE '' END
					END,
				t.BasePlanInfoID,
				t.BaseYearCPS,
				t.BaseYearContract 
		INTO    #PLXw_Temp2
		FROM    #PLXw_Temp1 t
				LEFT JOIN dbo.LkpRenewalSubstantiationMap cmap ON t.CurrentYearRenewalTypeID = cmap.RenewalTypeID
					AND t.[IsBase=CYContract] = cmap.IsContractChange
					AND t.[IsCurContractSep] = cmap.IsContractSeparation
				LEFT JOIN dbo.LkpRenewalSubstantiationMap bmap ON t.BidYearRenewalTypeID = bmap.RenewalTypeID
					AND t.[IsBid=CYContract] = bmap.IsContractChange
					AND t.[IsBidContractSep] = bmap.IsContractSeparation

		--Drop temporal crosswalk table 1
		IF ( SELECT OBJECT_ID('tempdb..#PLXw_Temp1')
			) IS NOT NULL
			DROP TABLE #PLXw_Temp1;

		-- Add relevant crosswalk text
		IF ( SELECT OBJECT_ID('tempdb..#IsSubstn')
			) IS NOT NULL
			DROP TABLE #IsSubstn;
		SELECT DISTINCT
				[ServiceAreaOptionID] ,
				[BidYearPlanInfoID],
				[IsSubstNeeded] = SUM(CASE WHEN [IsBase=CYCPS] = 1 AND [IsBid=CYCPS] = 1 
					AND LEFT([CY Crosswalk], 12) = 'Plan to Plan' AND LEFT([BY Crosswalk], 12) = 'Plan to Plan'
							THEN 0 ELSE 1 END)
		INTO #IsSubstn
		FROM #PLXw_Temp2 WHERE BaseYearCPS IS NOT NULL AND BidYearCPS IS NOT NULL
		GROUP BY ServiceAreaOptionID, BidYearPlanInfoID


		/*Add unique relevant crosswalk types: unique by SAOption, BYPlanInfo, BasePlanInfo.
		 Remove duplicates
		*/
		IF ( SELECT OBJECT_ID('tempdb..#AddRelXw')
			) IS NOT NULL
			DROP TABLE #AddRelXw;
		SELECT DISTINCT
				t.[ServiceAreaOptionID],
				t.[BidYearPlanInfoID],
				t.[BasePlanInfoID],
				[IsSubstantiationNeeded] = CASE WHEN sb.IsSubstNeeded > 0 AND t.[BasePlanInfoID] IS NOT NULL THEN 1 ELSE 0 END,
				[RelXw] = CASE WHEN sb.IsSubstNeeded = 0 THEN 'NA' ELSE
					CASE WHEN t.BidYearCPS = t.BaseYearCPS THEN 'N/A -- Ongoing Bid' ELSE
					'CY' + CAST(@PlanYear - 1 AS CHAR(4)) + ' ' + t.[CY Crosswalk] +
					' CY' + CAST(@PlanYear  AS CHAR(4)) + ' ' + t.[BY Crosswalk]	END END,
				
				[R] = RANK() OVER (PARTITION BY t.BidYearPlanInfoID, t.ServiceAreaOptionID, t.BasePlanInfoID 
				ORDER BY 
					--RelXw 
					CASE WHEN sb.IsSubstNeeded = 0 THEN 'NA' ELSE
						CASE WHEN t.BidYearCPS = t.BaseYearCPS THEN 'N/A -- Ongoing Bid' ELSE
						'CY' + CAST(@PlanYear - 1 AS CHAR(4)) + ' ' + t.[CY Crosswalk] +
						' CY' + CAST(@PlanYear  AS CHAR(4)) + ' ' + t.[BY Crosswalk]	END END
				
				DESC)

		INTO #AddRelXw
		FROM #PLXw_Temp2 t
		INNER JOIN #IsSubstn sb ON sb.ServiceAreaOptionID = t.ServiceAreaOptionID 
		AND sb.BidYearPlanInfoID = t.BidYearPlanInfoID
		
		--Return Relevant Crosswalk
		SELECT 
			[ServiceAreaOptionID],
			[BidYearPlanInfoID],
			[BasePlanInfoID],
			[IsSubstantiationNeeded],
			[RelXw]
		FROM #AddRelXw
	END
GO