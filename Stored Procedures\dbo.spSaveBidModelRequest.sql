SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spSaveBidModelRequest
--
-- Author:		T Y Bhavana
--
-- Create date: 26-Oct-2021
-- HEADER UPDATED: 26-Oct-2021
--
-- DESCRIPTION: Insert Bid Model Request details into AdminUserHeader
--
-- PARAMETERS:
--	Input:
--		
--
-- RETURNS: 
--		
--
-- TABLES:
--  Read:
--
--Write:AdminUserHeader

--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--    sp_helprolemember (system)
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
--2021-oct-26			1			Intial version													 Bhavana                       
-- ----------------------------------------------------------------------------------------------------------------------




-- Description:	Insert into  AdminUserHeader
-- =============================================
CREATE PROCEDURE [dbo].[spSaveBidModelRequest]
@UserID VARCHAR(7),
@LastName VARCHAR(25),
@FirstName VARCHAR(25),
@HumanaTitle VARCHAR(35),
@CertifyingTitle VARCHAR(35),
@Email VARCHAR(50),
@PhoneNumber VARCHAR(12),
@Role TINYINT,
@ServerId TINYINT,
@CreatedByServerId TINYINT
AS
BEGIN TRY
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	BEGIN
	INSERT INTO dbo.AdminUserHeader
	(
	    UserID,
	    UserLastName,
	    UserFirstName,
	    UserMiddleInitial,
	    UserContactTitle,
	    UserCertifyingTitle,
	    UserHumanaTitle,
	    UserEmail,
	    UserPhone,
	    UserFax,
	    IsSelectable,
	    IsContact,
	    IsCertifying,
	    IsEnabled,
	    UserHomePage,
	    RoleID,
	    ServerID,
	    CreatedByServerID
	)
	VALUES
	(   @UserID,   
	   @LastName,  
	    @FirstName,
	    '', 
	    @HumanaTitle,   
	    @CertifyingTitle,
	    @HumanaTitle,   
	    @Email,   
	    @PhoneNumber,   
	    '',
	    1, 
	    1, 
	    1, 
	    1, 
	    0, 
	    @Role,    
	    @ServerId,
	    @CreatedByServerId
	    )
	END
	
END TRY

BEGIN CATCH
   

    SELECT
        ERROR_NUMBER() AS ErrorNumber,
        ERROR_SEVERITY() AS ErrorSeverity,
        ERROR_STATE() AS ErrorState,
        ERROR_PROCEDURE() AS ErrorProcedure,
        ERROR_LINE() AS ErrorLine,
        ERROR_MESSAGE() AS ErrorMessage;
END CATCH







GO
