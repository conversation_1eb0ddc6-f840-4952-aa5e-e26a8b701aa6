SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetOSBInfo
--
-- AUTHOR: Joe Casey
--
-- CREATED DATE: 2010-Dec-13
-- HEADER UPDATED: 2010-Dec-13
--
-- DESCRIPTION:  This function will populate the OSB listviews in the model.
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--      @IsInPlan
--  Output:
--
-- TABLES: 
--	Read:
--      PerIntOptionalPackageDetail
--      PerIntOptionalPackageHeader
--      SavedPlanOptionalPackageDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnPadInteger
--      fnGetSafeDivisionResult
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Dec-13		1		    Initial Version										                Joe Casey
-- 2011-Jan-06      2           Altered Gain/Loss% to look like a percent now                       Joe Casey
-- 2016-Feb-24      3           Changed Varchar(50) with Varchar(200) for Name in @Results table    Manisha Tyagi       
-- 2019-Feb-04		4			PackageIndex and PlanPackageID data type change						Keith Galloway
-- 2020-Sep-08		5			Added condition of null null for sonar Qube for package index		Deepali	
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetOSBInfo]
    (
    @ForecastID INT,
    @IsInPlan BIT
    )
RETURNS @Results TABLE 
(
    PlanPackageID TINYINT,
    PackageIndex INT,
    [Name] VARCHAR(200),
    [Description] VARCHAR (200),
    TotalExpense DECIMAL (14,2),
    GainLossPercent DECIMAL (4,1),
    PackagePremium DECIMAL (7,2)
) AS
BEGIN
    IF @IsInPlan = 0
        BEGIN
            INSERT @Results
	        SELECT
	            0,
                packagetotals.PackageIndex,
                packagetotals.Name,
                packagetotals.Description,
                packagetotals.TotalExpense,
                GainLossPercent = CAST(dbo.fnGetSafeDivisionResult(packagetotals.TotalGainLoss,ROUND(packagetotals.PackageAllowedPMPM - packagetotals.PackageCostSharePMPM + packagetotals.TotalExpense + packagetotals.TotalGainLoss, 1))*100 AS DECIMAL(4,1)),
                PackagePremium = CAST(ROUND(packagetotals.PackageAllowedPMPM - packagetotals.PackageCostSharePMPM + packagetotals.TotalExpense + packagetotals.TotalGainLoss, 1) AS DECIMAL(7,2))
             FROM	 
                (SELECT
                    perHeader.PackageIndex,
                    perHeader.Name,
                    perHeader.Description,							
                    perHeader.TotalExpense,
                    perHeader.TotalGainLoss,
                    PackageAllowedPMPM = ROUND(SUM((perDetail.AllowedUtilzationPer1000/1000)*(perDetail.AllowedAverageCost/12)), 2),
                    PackageCostSharePMPM =
                        ROUND(SUM(
                            CASE WHEN perDetail.MeasurementUnitCode = 'Coin' THEN
	                            perDetail.EnrolleeCostShareUtilization * 
	                            perDetail.EnrolleeAverageCostShare
                            ELSE 
	                            perDetail.EnrolleeCostShareUtilization * 
	                            perDetail.EnrolleeAverageCostShare / 12000
                            END
                        ), 2)
                FROM PerIntOptionalPackageDetail perDetail
                INNER JOIN PerIntOptionalPackageHeader perHeader
                    ON perDetail.PackageIndex = perHeader.PackageIndex
                WHERE perDetail.IsHidden = 0
                    AND perHeader.IsEnabled = 1
                    AND perDetail.PackageIndex NOT IN
                        (SELECT PackageIndex from SavedPlanOptionalPackageDetail WHERE IsHidden = 0 AND ForecastID = @ForecastID and PackageIndex is not null)
                GROUP BY
                    perHeader.PackageIndex,
                    perHeader.Description,
                    perHeader.Name,
                    perHeader.TotalExpense,
                    perHeader.TotalGainLoss
                ) packagetotals
        END
    ELSE
        BEGIN
            INSERT @Results
            SELECT
                saved.PlanPackageID,
                packagetotals.PackageIndex,
                packagetotals.Name,
                packagetotals.Description,
                packagetotals.TotalExpense,
                GainLossPercent = CAST(dbo.fnGetSafeDivisionResult(packagetotals.TotalGainLoss,ROUND(packagetotals.PackageAllowedPMPM - packagetotals.PackageCostSharePMPM + packagetotals.TotalExpense + packagetotals.TotalGainLoss, 1))*100 AS DECIMAL(4,1)),
                PackagePremium = CAST(ROUND(packagetotals.PackageAllowedPMPM - packagetotals.PackageCostSharePMPM + packagetotals.TotalExpense + packagetotals.TotalGainLoss, 1) AS DECIMAL(7,2))
             FROM	 
                (SELECT
                    perHeader.PackageIndex,
                    perHeader.Name,
                    perHeader.Description,							
                    perHeader.TotalExpense,
                    perHeader.TotalGainLoss,
                    PackageAllowedPMPM = ROUND(SUM((perDetail.AllowedUtilzationPer1000/1000)*(perDetail.AllowedAverageCost/12)), 2),
                    PackageCostSharePMPM =
                        ROUND(SUM(
                            CASE WHEN perDetail.MeasurementUnitCode = 'Coin' THEN
	                            perDetail.EnrolleeCostShareUtilization * 
	                            perDetail.EnrolleeAverageCostShare
                            ELSE 
	                            perDetail.EnrolleeCostShareUtilization * 
	                            perDetail.EnrolleeAverageCostShare / 12000
                            END
                        ), 2)
                FROM PerIntOptionalPackageDetail perDetail
                INNER JOIN PerIntOptionalPackageHeader perHeader
                    ON perDetail.PackageIndex = perHeader.PackageIndex
                WHERE perDetail.IsHidden = 0
                    AND perHeader.IsEnabled = 1
                    AND perDetail.PackageIndex IN
                        (SELECT PackageIndex from SavedPlanOptionalPackageDetail WHERE IsHidden = 0 AND ForecastID = @ForecastID)
                GROUP BY
                    perHeader.PackageIndex,
                    perHeader.Description,
                    perHeader.Name,
                    perHeader.TotalExpense,
                    perHeader.TotalGainLoss
                ) packagetotals
            INNER JOIN SavedPlanOptionalPackageDetail saved
                ON packagetotals.PackageIndex = saved.PackageIndex
                AND saved.ForecastID = @ForecastID
        END
RETURN
END

GO
