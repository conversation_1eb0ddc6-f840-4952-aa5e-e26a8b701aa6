﻿/****** Object:  StoredProcedure [dbo].[spAppImportPBPMapStagedData]    Script Date: 8/15/2024 9:43:13 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportPBPMapStagedData
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-july-22      1			Initial Version						                                Dheeraj Singh
-- ----------------------------------------------------------------------------------------------------------------------

CREATE   PROCEDURE [dbo].[spAppImportPBPMapStagedData]
(@StageId VARCHAR(100))
AS
BEGIN
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

    DECLARE @tbl__importData TABLE
    (
        BenefitCategoryID INT,
		PBPLineCode VARCHAR(100),
        UserID CHAR(7)
    );

    INSERT INTO @tbl__importData
    SELECT BenefitCategoryID,
	      ISNULL ( PBPLineCode,''),
           @UserId
    FROM
        OPENJSON(@jsonData, '$.BPT_To_PBP_Mapping')
        WITH
        (
            BenefitCategoryID INT,
		PBPLineCode VARCHAR(100)
        );

	MERGE INTO dbo.LKPIntBenefitCategory AS target
    USING @tbl__importData AS source
    ON (
           target.BenefitCategoryID = source.BenefitCategoryID
       )
    WHEN MATCHED THEN
	      
        UPDATE SET target.PBPLineCode = source.PBPLineCode  ;
		

                  
    
	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
