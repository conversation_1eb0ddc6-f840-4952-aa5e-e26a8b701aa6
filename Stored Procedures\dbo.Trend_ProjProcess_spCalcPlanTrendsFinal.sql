SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcPlanTrendsFinal
--
-- CREATOR: <PERSON> Cannon
--
-- CREATED DATE: APR-6-2020
--
-- DESCRIPTION:
--  Compile final projected trends at plan-benefit category level and produce result set
--		
-- PARAMETERS:
--  Input  : LastUpdateByID
--  Output : NONE
--
-- TABLES : Read :
--					Trend_ProjProcess_CalcPlanTrends_RepCat
--				    Trend_ProjProcess_CalcPlanTrends_BenCat
--					Trend_SavedHumanaAtHomeTrends
--					LkpIntBenefitCategory
--					Trend_SavedComponentInfo
--					SavedRegionInfo
--					SavedMarketInfo
--					LkpSNPType
--					SavedPlanInfo
--          Write:  Trend_ProjProcess_CalcPlanTrendsFinal                                                                                             
--
-- VIEWS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- Apr-06-2020      1           Initial Version                                                     Clay Cannon
-- May-06-2020		2			Update BenefitCategory to BenefitCategoryID							Craig Nielsen
-- Jun-10-2020      3           Adding a PlanInfoIDList parameter                                   Andy Blink
-- Jun-12-2020      4           Remove CPS and PlanYearID from #PlanInfoIDList                      Manisha Tyagi
-- NOV-19-2020      5           Include NOLOCK & ROWLOCK                                            Manisha Tyagi
-- DEC-06-2020      6           Batch delete and Temp insert implemented                            Surya Murthy
-- OCT-17-2022		7			Added Column names and NewID() column in insert statement			Sheetal Patil

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------                                                         
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcPlanTrendsFinal] 
 @PlanInfoIDList VARCHAR(MAX) = NULL
,@LastUpdateByID CHAR(7)

AS

BEGIN

    DECLARE @LastUpdateDateTime DATETIME = GETDATE();
    DECLARE @BidYear INT;
    
    SET @BidYear = (SELECT PlanYearID FROM dbo.LkpIntPlanYear WITH(NOLOCK) WHERE IsBidYear = 1);

    --Create PlanInfoID list
    IF (SELECT  OBJECT_ID ('tempdb..#PlanInfoIDList')) IS NOT NULL
        DROP TABLE #PlanInfoIDList;
    SELECT  DISTINCT
            PlanInfoID
    INTO    #PlanInfoIDList
    FROM    dbo.vwPlanInfo WITH(NOLOCK)
    WHERE   PlanYear = @BidYear
            AND IsHidden = 0
            AND IsOffMAModel = 'No'
            AND Region NOT IN ('Unmapped')
            AND (PlanInfoID IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@PlanInfoIDList, ',', 1) )
                 OR @PlanInfoIDList IS NULL);


   -- DELETE  FROM dbo.Trend_ProjProcess_CalcPlanTrendsFinal WITH(ROWLOCK)
   --WHERE   PlanInfoID IN (SELECT DISTINCT PlanInfoID FROM #PlanInfoIDList);

	-- Deleting with BATCH process
	DECLARE @RepCatRowcount INT = 1
	WHILE @RepCatRowcount > 0
	BEGIN
		DELETE TOP (10000)
		FROM dbo.Trend_ProjProcess_CalcPlanTrendsFinal WITH(ROWLOCK)
		WHERE   PlanInfoID IN (SELECT DISTINCT PlanInfoID FROM #PlanInfoIDList)
		SET @RepCatRowcount = @@ROWCOUNT
	END

	--Creating temp table for RepCat table
	IF (SELECT  OBJECT_ID ('tempdb..#TempCalcPlanTrendsFinal')) IS NOT NULL
		DROP TABLE #TempCalcPlanTrendsFinal;

	CREATE TABLE #TempCalcPlanTrendsFinal
							([PlanInfoID] [int] ,
							[CPS] [char](13) ,
							[PlanYearID] [int] ,							 
							[TrendYearID] [int] ,
							[RateType] [smallint] ,
							[BenefitCategoryID] [smallint] ,
							[ComponentReporting] [varchar](50) ,							 
							[CostAdjustment] [decimal](18, 8) ,
							[UseAdjustment] [decimal](18, 8) ,							 
							[LastUpdateByID] [char](13) ,
							[LastUpdateDateTime] [datetime],
                            ID UniqueIdentifier)

    INSERT INTO #TempCalcPlanTrendsFinal
    --INSERT INTO dbo.Trend_ProjProcess_CalcPlanTrendsFinal WITH (ROWLOCK)
    SELECT t.PlanInfoID
          ,t.CPS
          ,t.PlanYearID
          ,t.TrendYearID
          ,t.RateType
          ,t.BenefitCategoryID
          ,t.ComponentReporting
          ,COALESCE(hah.CostAdjustment, t.CostAdjustment, 0) AS CostAdjustment
          ,COALESCE(hah.UseAdjustment, t.UseAdjustment, 0) AS UseAdjustment
          ,@LastUpdateByID
          ,@LastUpdateDateTime
          ,NEWID()
    FROM
    (
        --Reporting Category level expanded to Benefit Categories 
        SELECT trc.PlanInfoID
              ,trc.CPS
              ,trc.PlanYearID
              ,trc.RateType
              ,trc.TrendYearID
              ,bc.BenefitCategoryID
              ,trc.ComponentReporting
              ,trc.CostAdjustment
              ,trc.UseAdjustment
        FROM dbo.Trend_ProjProcess_CalcPlanTrends_RepCat trc WITH(NOLOCK)
            INNER JOIN dbo.LkpIntBenefitCategory bc WITH(NOLOCK)
                ON bc.ReportingCategory = trc.ReportingCategory
        WHERE trc.PlanInfoID IN (SELECT DISTINCT PlanInfoID FROM #PlanInfoIDList)

        UNION ALL

        --Benefit Category level
        SELECT tbc.PlanInfoID
              ,tbc.CPS
              ,tbc.PlanYearID
              ,tbc.RateType
              ,tbc.TrendYearID
              ,tbc.BenefitCategoryID
              ,tbc.ComponentReporting AS ComponentReporting
              ,tbc.CostAdjustment
              ,tbc.UseAdjustment
        FROM dbo.Trend_ProjProcess_CalcPlanTrends_BenCat tbc WITH(NOLOCK)
        WHERE tbc.PlanInfoID IN (SELECT DISTINCT PlanInfoID FROM #PlanInfoIDList)
    ) t
        --HAH Overrides
        LEFT JOIN
        (
            SELECT p.PlanInfoID
                  ,h.RateType
                  ,'Normalized' AS ComponentReporting
                  ,h.BenefitCategoryID
                  ,h.TrendYearID
                  ,h.CostAdjustment
                  ,h.UseAdjustment
            FROM dbo.Trend_SavedHumanaAtHomeTrends h WITH(NOLOCK)
                INNER JOIN dbo.SavedRegionInfo ri WITH(NOLOCK)
                    ON ri.ActuarialRegion = h.ActuarialRegion
                INNER JOIN dbo.SavedMarketInfo mi WITH(NOLOCK)
                    ON mi.ActuarialRegionID = ri.ActuarialRegionID
                JOIN dbo.LkpSNPType s WITH(NOLOCK)
                    ON s.SNPType = h.SNPType
                INNER JOIN dbo.SavedPlanInfo p WITH(NOLOCK)
                    ON p.ActuarialMarketID = mi.ActuarialMarketID
                       AND p.SNPTypeID = s.SNPTypeID
            WHERE p.PlanInfoID IN (SELECT DISTINCT PlanInfoID FROM #PlanInfoIDList)
        ) hah
            ON hah.PlanInfoID = t.PlanInfoID
               AND hah.RateType = t.RateType
               AND hah.ComponentReporting = t.ComponentReporting
               AND hah.BenefitCategoryID = t.BenefitCategoryID
               AND hah.TrendYearID = t.TrendYearID;
		--Finally inserting into main table from temp table
		insert into dbo.Trend_ProjProcess_CalcPlanTrendsFinal 
		(PlanInfoID
		,CPS
		,PlanYearID
		,TrendYearID
		,RateType
		,BenefitCategoryID
		,ComponentReporting
		,CostAdjustment
		,UseAdjustment
		,LastUpdateByID
		,LastUpdateDateTime
		,ID) 
        select [PlanInfoID] ,
				[CPS]  ,
				[PlanYearID]  ,							 
				[TrendYearID]  ,
				[RateType] ,
				[BenefitCategoryID]  ,
				[ComponentReporting]  ,							 
				[CostAdjustment]  ,
				[UseAdjustment]  ,							 
				[LastUpdateByID] ,
				[LastUpdateDateTime] ,
				[ID]   from #TempCalcPlanTrendsFinal;
		--End

END;
GO
