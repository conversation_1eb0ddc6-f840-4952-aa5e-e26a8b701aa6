SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_spCalcInducedUtilization
--
-- CREATOR: Andy Blink 
--
-- CREATED DATE: Mar-15-2020
--
-- DESCRIPTION: The purpose of this stored procedure is to calculate Induced Utilization factors based region/product factors and plan level adjustments.  
--              These factors are provided to the MACTAPT.													                    
--                                                                                      
--              Update IU BidYear in Tools_BidYear in Q3 
--
--       
-- PARAMETERS:
--  Input  : @LastUpdateByID
--           @CPBPList
--           @RegionString
--           @ProductString
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntPlanYear
--                  LkpProjectionVersion
--                  LkpIntBenefitCategory
--                  SavedForecastSetup
--                  SavedRollupForecastMap
--                  SavedRollupInfo                  
--
--
--          Write:  Trend_SavedRelativityInducedUtilization
--
-- VIEWS: Read: vwPlanInfo
--              vwSamCrosswalks
--
-- FUNCTIONS: Trend_fnSafeDivide
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                                    DEVELOPER		
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2013-Dec-12		1		    Initial Version										                                    Jacob O'Bryant
-- 2014-Mar-28		2			Simplified the crosswalk section					                                    Jacob O'Bryant
-- 2014-Apr-14		3			Added logic to limit insert statement to parameters	                                    Jacob O'Bryant
-- 2014-Jul-25		4			Modified for differences between IU and SAM Years	                                    Jacob O'Bryant
-- 2014-Jul-16		5			Added Capitation field for SI 4						                                    Jordan Lake
-- 2014-Nov-03		6			Fixed Join issue for Base Data						                                    Jordan Lake
-- 2014-Dec-02		7			Updated the Email Notification						                                    Brian Gartner
-- 2015-Jun-12		8			Replaced fn_Assumptions_SegmentVersionAllocation	                                    Sumit Kumar	
--								with SP_fn_Assumptions_SegmentVersionAllocation
-- 2017-Jun-06		9			Changed to Bid Taxonomy								                                    Christine Zheng
-- 2020-Mar-15      10          Migrate procedure from TPF to MAAModels and update column/table references              Andy Blink
-- 2020-Jun-08      11          Update from vwCrosswalksPlanLevel to vwSamCrosswalks                                    Andy Blink
-- 2020-Jul-14      12			Optimized BARC to MAAUI logic 															Ramandeep Saini
-- 2020-Sep-17		12			Update @IUBidYear = BidYear + 1 for Q3 / Q4 projection									Jake Lewis
-- 2020-Feb-14		13			Update logic on Crosswalk Logic section to improve join performance						Aleksander Dimitrijevic
-- 2023-Aug-03		14			Added Nolock, Internal  variable and schema												Sheetal Patil
-- 2023-Sep-25	    15 		    Rowlocks added for deadlock issues														Surya Murthy
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------

/*To Do:
        Does IUBidYear need to be BidYear + 1 for Q3/Q4?
*/


CREATE PROCEDURE [dbo].[Trend_spCalcInducedUtilization]
--DECLARE 
@LastUpdateByID [CHAR](7) 
,@CPBPList      [VARCHAR](MAX) = NULL
,@RegionString  [VARCHAR](MAX) = NULL
,@ProductString [VARCHAR](MAX) = NULL

AS

--SET NOCOUNT ON
DECLARE @LastUpdateDateTime AS [DATETIME];
DECLARE @BaseYear SMALLINT;
DECLARE @IUBidYear SMALLINT;
DECLARE @XCPBPList        VARCHAR(MAX) = @CPBPList;
DECLARE @XRegionString   VARCHAR(MAX) = @RegionString;
DECLARE @XProductString  VARCHAR(MAX) = @ProductString  


SET @LastUpdateDateTime = (SELECT   GETDATE ());

--Determines max year with historical data
SET @BaseYear = (CASE WHEN (SELECT  DISTINCT
                                    LastCurrentYearQuarter
                            FROM    dbo.LkpProjectionVersion WITH(NOLOCK)
                            WHERE   IsLiveProjection = 1) = 0 THEN (SELECT  DISTINCT
                                                                            PlanYearID
                                                                    FROM    dbo.LkpIntPlanYear WITH (NOLOCK) 
                                                                    WHERE   IsExperienceYear = 1) --When it's the bid projection, IUBaseYear = BidYear - 2
                      ELSE (SELECT  DISTINCT
                                    PlanYearID
                            FROM    dbo.LkpIntPlanYear  WITH(NOLOCK)
                            WHERE   IsExperienceYear = 1) + 1 END); --When it's the Q3/Q4 projection, IUBaseYear = BidYear - 1

--Set IU bid year based on what QuarterIDly projection is taking place
--Set IU bid year based on what QuarterIDly projection is taking place
SET @IUBidYear = (CASE WHEN (SELECT DISTINCT
                                    LastCurrentYearQuarter
                             FROM   dbo.LkpProjectionVersion WITH (NOLOCK)
                             WHERE  IsLiveProjection = 1) = 0 THEN
                       (SELECT  DISTINCT PlanYearID FROM dbo.LkpIntPlanYear WITH (NOLOCK) WHERE   IsBidYear = 1) --When it's the bid projection, IUBidYear = BidYear
                       ELSE (SELECT DISTINCT PlanYearID FROM dbo.LkpIntPlanYear WITH (NOLOCK) WHERE   IsBidYear = 1) + 1 
                       END); --When it's the Q3/Q4 projection, IUBidYear = BidYear + 1




---------------------------------
-- Create Temp table for plans --
---------------------------------

--Drop table if it already exists
IF (SELECT  OBJECT_ID ('tempdb..#TempTableIUPlanList')) IS NOT NULL
    DROP TABLE #TempTableIUPlanList;

CREATE TABLE #TempTableIUPlanList
    (CPS [CHAR](13) NULL, );
INSERT INTO #TempTableIUPlanList
    (CPS)
SELECT  DISTINCT
        CPS
FROM    dbo.vwPlanInfo WITH(NOLOCK)
WHERE   PlanYear >= @IUBidYear
        --CPS in parameter list (or NULL)
        AND (CPS IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@CPBPList, ',', 1) )
             OR @CPBPList IS NULL)
        --Region in parameter list (or NULL)
        AND ([Region] IN (SELECT    Val FROM   dbo.Trend_fnCalcStringToTable (@RegionString, ',', 1) )
             OR @RegionString IS NULL)
        --Product in parameter list (or NULL)
        AND ([Product] IN (SELECT   Val FROM dbo.Trend_fnCalcStringToTable (@ProductString, ',', 1) )
             OR @ProductString IS NULL);



--------------------------------------------------------
-- Create Temp table for non-crosswalked, plan level, --
-- factors.                                           --
--------------------------------------------------------

--Drop table if it already exists
IF (SELECT  OBJECT_ID ('tempdb..#TempTableIUPlan')) IS NOT NULL
    DROP TABLE #TempTableIUPlan;

--Create table
CREATE TABLE #TempTableIUPlan
    ([PlanYearID]        [SMALLINT]       NOT NULL
    ,[QuarterID]         [TINYINT]        NOT NULL
    ,[ReportingCategory] [VARCHAR](50)    NULL
    ,[CPS]               [CHAR](13)       NULL
    ,[CostRelativity]    [DECIMAL](18, 8) NOT NULL
    ,[UseRelativity]     [DECIMAL](18, 8) NOT NULL);

INSERT INTO #TempTableIUPlan
    ([PlanYearID]
    ,[QuarterID]
    ,[ReportingCategory]
    ,[CPS]
    ,[CostRelativity]
    ,[UseRelativity])

--Retrieve factors for IU's current year (IU BidYear - 1) and label as bid year
SELECT      map.[PlanYear] + 1 AS [PlanYearID]
           ,[QuarterID]
           ,[ReportingCategory]
           ,map.[CPS]
           ,COALESCE ([CostRelativity], 1)
           ,COALESCE ([UseRelativity], 1)
FROM        dbo.Trend_SavedRelativityInducedUtilization iu WITH(NOLOCK)
INNER JOIN  dbo.vwPlanInfo map  WITH(NOLOCK)
        ON map.[CPS] = iu.[CPS]
           AND  map.PlanYear = iu.[PlanYearID]
		   INNER JOIN  #TempTableIUPlanList temp ON temp.CPS = iu.CPS
WHERE       iu.[PlanYearID] = (@IUBidYear - 1);


------------------------------------------------------------------------------------------------------
-- Create table for custom view                                                                     
-- Replaces the vw_sam_crosswalks by shifting BaseYear, CurrentYear, BidYear and FutureYear CPBPS's
-- depending on the difference between the IU Baseyear and Sam BidYear
------------------------------------------------------------------------------------------------------

--Drop table if it already exists
IF (SELECT  OBJECT_ID ('tempdb..#TempTableIUView')) IS NOT NULL
    DROP TABLE #TempTableIUView;

--Create table
CREATE TABLE #TempTableIUView
    ([BaseCPS]    [CHAR](13) NULL
    ,[CurrentCPS] [CHAR](13) NULL
    ,[BidCPS]     [CHAR](13) NULL
    ,[FutureCPS]  [CHAR](13) NULL, );
INSERT INTO #TempTableIUView
--Shifts years if the base year equals the SAM's previous year

--=======================================
--Commented out 2015 Q3 
--May Delete 2016 Q3 if this doesn't break 
--=========================================

--SELECT DISTINCT [Version]
--	,[BaseCPS] = [PrYearCPBPS]
--	,[CurrentCPS] = [BaseCPS]
--	,[BidCPS] = [CurrentCPS]
--	,[FutureCPS] = [BidCPS]
--FROM vw_SAM_Crosswalks
--WHERE @BaseYear = (
--	SELECT [PlanYearID] - 3 AS [PlanyearID]
--	FROM   dbo.Tools_BidYear
--	WHERE  ToolName = 'SAM'
--	)
--	AND (
--		[PrYearCPBPS] IN (
--			SELECT [CPS] 
--			FROM #TempTableIUPlanList
--			)
--		OR [BaseCPS] IN (
--			SELECT [CPS] 
--			FROM #TempTableIUPlanList
--			)
--		OR [CurrentCPS] IN (
--			SELECT [CPS] 
--			FROM #TempTableIUPlanList
--			)
--		OR [BidCPS] IN (
--			SELECT [CPS] 
--			FROM #TempTableIUPlanList
--			)
--		)
--UNION
--Shifts years if the base year equals the SAM's base year

--This will be used in Q1, Q2
SELECT      DISTINCT
            sam.BaseYearCPS AS 'BaseCPS'
           ,sam.CurrentYearCPS AS 'CurrentCPS'
           ,sam.BidYearCPS AS 'BidCPS'
           ,sam.BidYearCPS AS 'FutureCPS'
FROM        dbo.vwSAMCrosswalks sam WITH(NOLOCK)

--Only bring in service area scenario that are live
INNER JOIN  (SELECT     *
             FROM       dbo.SavedForecastSetup WITH(NOLOCK)
             WHERE ForecastID IN (
                                     SELECT DISTINCT ForecastID
                                     FROM   dbo.SavedRollupForecastMap WITH (NOLOCK)
                                     WHERE  RollupID IN (SELECT DISTINCT
                                                                RollupID
                                                         FROM   dbo.SavedRollupInfo WITH (NOLOCK)
                                                         WHERE  RollupName = 'Live'
                                                         )
                                  )
             ) sfs
ON sfs.ServiceAreaOptionID = sam.ServiceAreaOptionID
    AND  sfs.PlanInfoID = sam.BidYearPlanInfoID

WHERE       (   sam.BaseYearCPS    IN (SELECT   [CPS] FROM  #TempTableIUPlanList)
             OR sam.CurrentYearCPS IN (SELECT   [CPS] FROM  #TempTableIUPlanList)
             OR sam.BidYearCPS     IN (SELECT   [CPS] FROM  #TempTableIUPlanList))

--This part of the WHERE statement is used in TPF, but I don't think it's needed so commenting it out
--@BaseYear = (
--SELECT [PlanYearID] - 2 AS [PlanyearID]
--FROM   dbo.Tools_BidYear
--WHERE  ToolName = 'SAM'
--)
--AND 

UNION

--Shifts years if the base year equals the SAM's current year
--This will be used in Q3, Q4
SELECT  DISTINCT
        sam.CurrentYearCPS AS 'BaseCPS'
       ,sam.BidYearCPS AS 'CurrentCPS'
       ,sam.BidYearCPS AS 'BidCPS'
       ,sam.BidYearCPS AS 'FutureCPS'
FROM    dbo.vwSAMCrosswalks sam WITH(NOLOCK)

--Only bring in service area scenario that are live
INNER JOIN  (SELECT     *
             FROM       dbo.SavedForecastSetup WITH(NOLOCK)
             WHERE ForecastID IN (
                                     SELECT DISTINCT ForecastID
                                     FROM   dbo.SavedRollupForecastMap WITH (NOLOCK) 
                                     WHERE  RollupID IN (SELECT DISTINCT
                                                                RollupID
                                                         FROM   dbo.SavedRollupInfo WITH (NOLOCK)
                                                         WHERE  RollupName = 'Live'
                                                         )
                                  )
             ) sfs
ON sfs.ServiceAreaOptionID = sam.ServiceAreaOptionID
    AND  sfs.PlanInfoID = sam.BidYearPlanInfoID

WHERE   (       sam.BaseYearCPS    IN (SELECT  [CPS] FROM  #TempTableIUPlanList)
         OR     sam.CurrentYearCPS IN (SELECT  [CPS] FROM  #TempTableIUPlanList)
         OR     sam.BidYearCPS     IN (SELECT  [CPS] FROM  #TempTableIUPlanList));

--This part of the WHERE statement is used in TPF, but I don't think it's needed so commenting it out
--@BaseYear = (
--SELECT [PlanYearID] - 1 AS [PlanyearID]
--FROM   dbo.Tools_BidYear
--WHERE  ToolName = 'SAM'
--)
--AND 


---------------------------------------------------
--New Temp for replacement of function
--------------------------------------------------
DECLARE @CPS [varchar](max)
SELECT @CPS= STUFF((SELECT distinct  ',' +  [CPS]
FROM #TempTableIUPlanList EE
FOR XML PATH('')), 1, 1, '') 


IF (SELECT  OBJECT_ID ('tempdb..#TempAssumptionsSegment')) IS NOT NULL
    DROP TABLE #TempAssumptionsSegment;


CREATE TABLE #TempAssumptionsSegment
    (BaseCPS                CHAR(13)       NOT NULL
    ,CurrentCPS             CHAR(13)       NOT NULL
    ,BidCPS                 CHAR(13)       NOT NULL
    ,FutureCPS              CHAR(13)       NOT NULL
    ,SegVerAllocationFactor DECIMAL(18, 8) NOT NULL);

INSERT INTO #TempAssumptionsSegment
EXEC dbo.Trend_spCalcSegmentVersionAllocation @CPS ;


-----------------------------------------------------
-- Create table for custom segmentation allocation --
-- Apply segment version allocation by applying    --
-- crosswalks                                      --
-----------------------------------------------------


--Drop table if it already exists
IF (SELECT  OBJECT_ID ('tempdb..#TempTableIUSegAll')) IS NOT NULL
    DROP TABLE #TempTableIUSegAll;

--Create table
CREATE TABLE #TempTableIUSegAll
    ([BaseCPS]                [CHAR](13)       NULL
    ,[CurrentCPS]             [CHAR](13)       NULL
    ,[BidCPS]                 [CHAR](13)       NULL
    ,[FutureCPS]              [CHAR](13)       NULL
    ,[SegVerAllocationFactor] [DECIMAL](18, 8) NOT NULL);
INSERT INTO #TempTableIUSegAll
--Uses function results as is if base year matches the MACTAPT's base year
--This sections used during Q2
SELECT  [BaseCPS]
       ,[CurrentCPS]
       ,[BidCPS]
       ,[FutureCPS]
       ,[SegVerAllocationFactor]
FROM    #TempAssumptionsSegment
WHERE
--   @BaseYear = (
--SELECT [PlanYearID] - 2 AS [PlanyearID]
--FROM   dbo.Tools_BidYear
--WHERE  ToolName = 'MACTAPT'
--)
--AND 
        ([BaseCPS] IN (SELECT   [CPS] FROM  #TempTableIUPlanList)
         OR     [CurrentCPS] IN (SELECT [CPS] FROM  #TempTableIUPlanList)
         OR     [BidCPS] IN (SELECT [CPS] FROM  #TempTableIUPlanList)
         OR     [FutureCPS] IN (SELECT  [CPS] FROM  #TempTableIUPlanList))
UNION
--Shifts years if the base year equals the MACTAPT's current year
--This section used in Q1, Q3, Q4
SELECT      [BaseCPS] = [CurrentCPS]
           ,[CurrentCPS] = [BidCPS]
           ,[BidCPS] = [FutureCPS]
           ,[FutureCPS]
           ,SUM ([SegVerAllocationFactor]) AS [SegVerAllocationFactor]
FROM        #TempAssumptionsSegment
WHERE
--   @BaseYear = (
--SELECT [PlanYearID] - 1 AS [PlanyearID]
--FROM   dbo.Tools_BidYear
--WHERE  ToolName = 'MACTAPT'
--)
--AND 
            ([BaseCPS] IN (SELECT   [CPS] FROM  #TempTableIUPlanList)
             OR         [CurrentCPS] IN (SELECT [CPS] FROM  #TempTableIUPlanList)
             OR         [BidCPS] IN (SELECT [CPS] FROM  #TempTableIUPlanList)
             OR         [FutureCPS] IN (SELECT  [CPS] FROM  #TempTableIUPlanList))
GROUP BY    [BaseCPS]
           ,[CurrentCPS]
           ,[BidCPS]
           ,[FutureCPS];



---------------------
-- Crosswalk Logic --
---------------------

IF (SELECT  OBJECT_ID ('tempdb..#TempFinal')) IS NOT NULL
    DROP TABLE #TempFinal;

SELECT      vw.[BidCPS] AS [CPS]
           ,map.[PlanYearID] as  PlanYearID
           ,pl.[QuarterID] as QuarterID
           ,pl.[ReportingCategory] as ReportingCategory
           ,[CostRelativity] = 1 
            --The following logic resulted in values different than 1 due to rounding
            --Note that for IU factors, cost should always be equal to 1.  If that changes,
            --then the following logic may be useful
            ----Allowed Factor
            --(SUM([UseMultipAdj]*[CostMultipAdj]*ISNULL([NormalizedAllowed],0.0000000001)*ISNULL([SegVerAllocationFactor],1))/NULLIF(SUM(ISNULL([NormalizedAllowed],0.0000000001)*ISNULL([SegVerAllocationFactor],1)),0))
            ----Divided by Use Factor
            --/(SUM([UseMultipAdj]*ISNULL([NormalizedUtilization],0.0000000001)*ISNULL([SegVerAllocationFactor],1))/NULLIF(SUM(ISNULL([NormalizedUtilization],0.0000000001)*ISNULL([SegVerAllocationFactor],1)),0))
            --
            --This segment calculates the weighted average of Use Factors when combining plans
           ,[UseRelativity] = SUM (
                              [UseRelativity] * ISNULL ([Utilization], 0.0000000001) * ISNULL ([SegVerAllocationFactor], 1))
                              / NULLIF(SUM (ISNULL ([Utilization], 0.0000000001) * ISNULL ([SegVerAllocationFactor], 1)), 0)
           ,@LastUpdateByID as LastUpdateByID
           ,@LastUpdateDateTime as LastUpdateDateTime 
		    into #TempFinal
--Pull factors from table with bid year factors set to current year factors
FROM        #TempTableIUPlan pl

--Map to all future years
INNER JOIN  (SELECT DISTINCT
                    PlanYear AS 'PlanYearID'
             FROM   dbo.vwPlanInfo WITH(NOLOCK)
             WHERE  PlanYear >= @IUBidYear) map --This >= on the join may be redundant since the #TempTableIUPlan should only contain one distinct PlanYearID
        ON map.PlanYearID >= pl.[PlanYearID]
--Maps to crosswalks 
-- 2/14 Update to improve join efficiency
INNER JOIN  (
            --Need base year for normalized allowed and us
            --Need current year to map to current year factors 
            --Need bid year to assign factors
            SELECT  DISTINCT
                    TTV.[BaseCPS]
                   ,[CurrentCPS]
                   ,TTV.[BidCPS]
                                ,[SegVerAllocationFactor]
            FROM    #TempTableIUView TTV
                      --Maps to segment allocation percentages
                      LEFT JOIN   (SELECT [BaseCPS]
                                                     ,[BidCPS]
                                                     ,[SegVerAllocationFactor]
                                           FROM   #TempTableIUSegAll) sa
                             ON TTV.[BaseCPS] = sa.[BaseCPS]
                         AND   TTV.[BidCPS] = sa.[BidCPS]
            WHERE   TTV.[BidCPS] IS NOT NULL) vw
       ON pl.[CPS] = vw.[CurrentCPS]



--Maps to base year claims (base year is the most recent year with completed data
LEFT JOIN   (SELECT     [CPS]
                       ,[QuarterID]
                       ,[ReportingCategory]
                        --		,SUM([Normalized Allowed]) AS [NormalizedAllowed]  NormalizedAllowed is not needed because we currently do not project an Allowed or Cost factor 
                       ,SUM ([Utilization]) AS [Utilization]
             FROM       dbo.Trend_CalcHistoricCostAndUse WITH(NOLOCK)
             WHERE      [PlanYearID] = @BaseYear
             GROUP BY   [CPS]
                       ,[QuarterID]
                       ,[ReportingCategory]) planbase
       ON planbase.[QuarterID] = pl.[QuarterID]
          AND   planbase.[ReportingCategory] = pl.[ReportingCategory]
          AND   planbase.[CPS] = vw.[BaseCPS]

WHERE       vw.[BidCPS] IN (SELECT  [CPS] FROM  #TempTableIUPlanList)
GROUP BY    map.[PlanYearID]
           ,pl.[QuarterID]
           ,pl.[ReportingCategory]
           ,vw.[BidCPS];

DELETE --Delete factors for all QuarterIDs in which the BidYear is >= the IU's BidYear
FROM dbo.Trend_SavedRelativityInducedUtilization WITH(ROWLOCK)
WHERE   [PlanYearID] >= @IUBidYear
        AND [CPS] IN (SELECT    [CPS] FROM #TempTableIUPlanList);

INSERT INTO dbo.Trend_SavedRelativityInducedUtilization WITH(ROWLOCK)
    ([CPS]
    ,[PlanYearID]
    ,[QuarterID]
    ,[ReportingCategory]
    ,[CostRelativity]
    ,[UseRelativity]
    ,[LastUpdateByID]
    ,[LastUpdateDateTime])

SELECT 
[CPS]
    ,[PlanYearID]
    ,[QuarterID]
    ,[ReportingCategory]
    ,[CostRelativity]
    ,[UseRelativity]
    ,[LastUpdateByID]
    ,[LastUpdateDateTime]
from #TempFinal




-- Here begins the notification email the SP sends out
-- identifies how many factors should be calculated
-- identifies how many are missing
--IF @CPBPList IS NULL
--	BEGIN 

--		DECLARE @CountMissing int
--		SET @CountMissing = (
--			SELECT COUNT(*)
--			FROM (
--				SELECT [CPS],[PlanyearID]
--				FROM dbo.New_AnnualPlanMapping 
--				WHERE [Type] = 'Individual'
--				) map
--			LEFT JOIN MACTAPT_Inputs_Normalization_InducedUtilization iu
--				ON map.[CPS] = iu.[CPS] 
--					AND map.[PlanYearID] = iu.[PlanYearID] 
--					AND iu.[Date] >= @LastUpdateDateTime 
--			WHERE map.[PlanYearID] >= @IUBidYear
--				AND iu.[CPS] IS NULL
--			)

--		DECLARE @CountPresent int
--		SET @CountPresent = (
--			SELECT COUNT(*)
--			FROM (
--				SELECT [CPS],[PlanyearID]
--				FROM dbo.New_AnnualPlanMapping 
--				WHERE [Type] = 'Individual'
--				) map
--			INNER JOIN (
--				SELECT DISTINCT [CPS]
--					,[PlanyearID]
--					,[Date]
--				FROM MACTAPT_Inputs_Normalization_InducedUtilization 
--				) iu
--				ON map.[CPS] = iu.[CPS] 
--					AND map.[PlanYearID] = iu.[PlanYearID] 
--					ANd iu.[Date] >= @LastUpdateDateTime
--			WHERE map.[PlanYearID] >= @IUBidYear
--			)

--		DECLARE @recipients NVARCHAR(MAX)
--			--Pulls e-mail addresses from table
--			SELECT  @recipients = COALESCE(@recipients + '; ','') + [E-mail]
--			FROM    (
--				SELECT [e-mail]
--				FROM ToolsTeam_Email
--				) a

--		DECLARE @database varchar(15)
--		SET @database = ( SELECT DB_NAME() )

--		DECLARE @subject varchar(max) 
--		SET @subject = @database + ': The Induced Utilization stored procedure has been executed.'

--		DECLARE @body varchar(max) 

--		SET @body = '<html><body><p> The Induced Utilization stored procedure for base and prior years has been executed for all plans. This stored procedure calculates current, bid and future year factors based on historical data (provided by the assumptions team). </p>' 
--		     	  + '<p> Currently there are ' + CAST(@CountPresent AS varchar(10)) + ' Contract-PBP-Segment ID/Plan Year ID combinations with Induced Utilization Factors, and there are currently ' + CAST(@CountMissing AS varchar(10)) + ' Contract-PBP-Segment ID/Plan Year ID combinations WITHOUT Induced Utilization Factors. </p>'
--				  + '<p> All Contract-PBP-Segment ID/Plan Year ID combinations have a date/time stamp greater than or equal to ' + Cast(@LastUpdateDateTime AS varchar(11)) + ', when this store procedure was executed.  </p></body></html>'



--		--Sends e-mail
--		EXEC msdb.dbo.sp_send_dbmail @recipients = @recipients,
--			@body = @body,
--			@body_Format = 'HTML',
--			@subject = @subject ;

--	END
GO
