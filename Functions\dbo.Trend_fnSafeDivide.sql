SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnSafeDivide
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: Jan-27-2020
--
-- DESCRIPTION: Safely divides two quantities to account for divide by zero errors
--        
-- PARAMETERS:
--  Input  : @Numerator
--	         @Denominator
--           @IfError - The value to return if a divide by zero error will occur
--
--  Output : @Result
--
-- TABLES : Read :  

--          Write:  
--
-- VIEWS: Read: 
--
-- FUNCTIONS: 
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 1/27/20    		1		    Initial Version																Andy Blink
-- 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[Trend_fnSafeDivide]
    (@Numerator   FLOAT
    ,@Denominator FLOAT
    ,@IfError     FLOAT)
RETURNS FLOAT
AS
    BEGIN
        DECLARE @Result FLOAT;

        IF ISNULL (@Denominator, 0) = 0 SET @Result = @IfError;
        ELSE SET @Result = ISNULL (@Numerator, 0) / @Denominator;

        RETURN @Result;
    END;
GO
