SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
--- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCopyPlan
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Mar-20
-- HEADER UPDATED: 2019-Apr-29
--
-- DESCRIPTION: Copies the specified plan into a new plan.  The new plan is
--              always for the current bid year.
--
-- PARAMETERS:
--  Input:
--      @ForecastIDFrom
--      @MarketIDTo
--      @IsIncludeRiskMembership
--      @UserID
--  Output:
--      @ForecastIDTo
--      @ErrorMessage
--
-- TABLES:
--  Read:
--      LkpProductType
--      LkpIntMarketTypeDetail
--      LkpIntPlanYear
--      PerIntMAAssumptions
--      SavedMarketHeader
--      SavedPlanAssumptions
--      SavedPlanHeader
--      SavedPlanINOONDistributionHeader
--      SavedOOPCHeader
--      SavedOOPCDetail
--      SavedTBCDetail
--  Write:
--      SavedPlanAssumptions
--      SavedPlanHeader
--      SavedPlanINOONDistributionHeader
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--      spCopyPlanDetailTables
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007-Mar-20      1           Initial Version.                                                    Christian Cofie
-- 2008-Feb-05      2           Revised code to bring it into coding standards.                     Shannon Boykin
-- 2008-Feb-25      3           Added comments.  General cleanup.  Eliminated unneeded              Tonya Cockrell
--                                parameters.  Returned new plan index as the return value.
-- 2008-Mar-03      4           Added copying of SavedPlanBidExpenseProfit.                         Tonya Cockrell
-- 2008-Apr-21      5           Added off-model Rx functionality and PD phantom plan.               Tonya Cockrell
-- 2008-May-01      6           Added SecondaryContactID to list of SavedPlanHeader fields          Tonya Cockrell
--                                that are copied.  Follows the same rules as ContactID.
-- 2008-Sep-18      7           Added @UserID to the list of parameters and replaced                Shannon Boykin
--                                all occurrences of SUSER_SNAME with @UserID.
-- 2008-Sep-26      8           Added @UserID to the spCopyPlanDetailTables call.                   Shannon Boykin
-- 2009-Mar-12      9           Commented out the code that sets the contacts to blank              Tonya Cockrell
--                                when copying into a different region.
-- 2009-Apr-02      10          Removed return value and replaced it with @ForecastIDTo              Tonya Cockrell
--                                and @ErrorMessage output parameters.  If the plan type
--                                is not valid in @MarketIDTo, then an error message will
--                                be returned.
-- 2009-Apr-10      11          PerIntAdminAllocation is now copied.  If there isn't a              Tonya Cockrell
--                                  record for the source plan, default values will be used.
-- 2010-Jan-29      12          Added IN/OON Header table copy                                      Nick Skeen
-- 2010-Feb-09      13          Added AltRebateOrder and IsLiveIndex                                Jake Gaecke
-- 2010-Apr-08      14          Added logic to execute spExperienceByBenefitCat                     Casey Sanders
-- 2010-May-05      15          Added SNPTypeID to copy                                             Casey Sanders
-- 2010-Sep-23      16          Removed @PlanYearIDFrom, @PlanVersionFrom from input. Changed       Joe Casey
--                                several tables calls to work until new design.
-- 2010-Sep-29      17          Removed PerIntAdminAllowed and moved values to SavedPlanAssumptions Joe Casey
-- 2010-Oct-13      18          Removed PlanName from SavedPlanHeader copy                          Joe Casey
-- 2011-Feb-08      19          Made adjustments for AdminExpID in SavedPlanHeader and accounted    Joe Casey
--                                SalesMembership in SavedPlanAssumptions
-- 2011-Feb-11      20          Revised so IsMAPD is copied from prior index instead of set to 0    Michael Siekerka
-- 2011-Feb-22      21          Added PlanDescription and IsMLA column to SavedPlanHeader           Joe Casey
-- 2011-Apr-20      22          Updated Sales membership to be NULL. 0 was erroring out             Nate Jacoby
-- 2011-Jun-02      23          Replaced LkpIntPlanYear	with dbo.fnGetBidYear()                     Bobby Jaegers
-- 2011-Jun-14      24          Changed @PlanYearID to return SMALLINT instead of INT               Bobby Jaegers
-- 2011-Sep-12      25          Added SalesAdjustmentFactor to be inserted into                     Bobby Jaegers 
--                                SavedPlanAssumptions	
-- 2012-Feb-13      26          Added IsToRefreshOOPC to INSERT INTO SavedPlanHeader                Alex Rezmerski
-- 2012-Feb-24      27          Added new admin buckets to select list from SavedPlanAssumptions    Alex Rezmerski
-- 2012-Mar-08      28          Added QualityInitDescrip to INSERT INTO SavedPlanHeader             Trevor Mahoney
-- 2012-Mar-12      29          Added Inserts into SavedOOPCHeader, SavedOOPCDetail and             Trevor Mahoney
--                                SavedTBCDetail				
-- 2012-Aug-10      30          Added RxBasicPremium, RxSuppPremium to insert into SavedPlanHeader  Alex Rezmerski
-- 2012-Aug-20      31          Added new Benefit Strings to insert into savedplanassumptions       Mike Deren	
-- 2013-Jan-03      32          Added IsCombinedDeductible to SavedplanHeader                       Mike Deren	
-- 2013-Jan-09      33          Changed the order of SalesMembership and SalesAdjFactors            Tim Gao
-- 2013-Jan-10      34          Removed Rxbasic and rxsupp                                          Mike Deren	
-- 2013-Oct-07      35          Included Join on Segment ID                                         Manisha Tyagi
-- 2014-Mar-07      36          Added in calculation of calcplanexperiencebybenefitcat              Mike Deren 		
-- 2014-Mar-10      37          Added spAppGetBiddableCostAndUse                                    Sharath Chandra 
-- 2016-Jan-28      38          Added IsSCTPlan and IsFiledPlan columns of SavedPlanHeader          Manisha Tyagi
-- 2016-Dec-20      39          Added IsLocked column of SavedPlanHeader                            Deepali Mittal
-- 2017-Feb-07      40          Include IUBasePlanManual                                            Manisha Tyagi
-- 2017-Jun-29      41          Removed MemberMonths column; adding PlanName to SavedPlanHeader     Crystal Quirino
-- 2017-Sep-05      42          Removed column IUBasePlan,IUBasePlanManual                          Ramkumar 
-- 2018-May-10      43          Changed LkpExtCMSPlanType to LkpProductType                         Jordan Purdue
-- 2018-Aug-16      44          Removed the check if plan type is valid in the copy-to market       Apoorva Nasa
-- 2018-Sep-07      45          Added CertifyingUserId column                                       Apoorva Nasa
-- 2018-Oct-04      46          Remove AdminExpID field                                             Manisha Tyagi
-- 2018-Oct-30      47          Removed LkpIntMARegionMarketDetail                                  Jordan Purdue
-- 2019-Feb-11      48          Removed code for SavedOOPCHeader,SavedOOPCDetail and SavedTBCDetail Kiran Pant
-- 2019-May-20      49          Copy ForecastID instead of ForecastID                                Kritika
-- 2019-May-22      50          added commit tran before return                                     Kritika
-- 2019-May-22      51          removed unused variable @NextForecastIDTo                            Pooja Dahiya 
-- 2019-May-23      52          modified code to get data from MAAUI tables whereever possible,     Pooja Dahiya
--                                updated @ForecastIDTo as non output parameter
-- 2019-Jun-07      53          Updated for PMPM                                                    Kiran Pant
-- 2019-Jul-01	    54          Renamed PMPM to PartBPremiumBuydown in forecast table               Pooja Dahiya      					                 					        
-- 2019-Sep-20      55          Removed SavedPlanHeader insert (table is now a view)                Brent Osantowski 
-- 2019-Nov-07      56          Changed UserId length and replaced forecast table                   Chhavi Sinha
--                                with savedplanheader		 
-- 2019-Dec-18      57          Corrected Split for SavedPlanHeader (ContractNbr,SegmentID,PlanID)  Deepali Mittal
-- 2019-Nov-14      58          [20.02] Added all SavedPlanAssumptions fields                       Andy OBrien
-- 2020-Sep-29      59          Backend Alignment and Restructuring                                 Keith Galloway
-- 2023-Aug-01      60          Updated SavedPlanINOONDistributionHeader insert on line#469         Adam Gilbert
-- 2024-Jul-26      61          Removed "CurrentBenefitString"                                      Dheeraj Singh
-- 2024-Sep-11		62		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCopyPlan]
    @ForecastIDFrom INT ,
    @IsIncludeRiskMembership BIT ,
    @UserID CHAR(7) ,
    @ForecastIDTo INT ,
    @ErrorMessage VARCHAR(250) OUTPUT
AS
    SET NOCOUNT ON;

    SET @ErrorMessage = NULL;

    DECLARE @IsFinal BIT;

    DECLARE @IsActivePlan BIT;

    DECLARE @IsToReprice BIT;

    DECLARE @IsMAPD BIT;

    DECLARE @IsSameMarket BIT;

    DECLARE @MARegionID TINYINT;

	DECLARE @MarketIDTo SMALLINT;
	DECLARE @PMPM decimal(9,1);

    DECLARE @ContactID CHAR(13);

    DECLARE @SecondaryContactID CHAR(13);
    
    DECLARE @CertifyingActuaryUserID CHAR(13);


    --Leave until IsOffModelRx is removed from SavedPlanHeader------

    DECLARE @IsOffModelRx BIT;

    SET @IsOffModelRx = 1;
	SET @issamemarket = 1
    DECLARE @ErrorMessageFromBackend varchar(max) ;
    DECLARE @ErrorSeverity INT;      
    DECLARE @ErrorState INT;
    DECLARE @ErrorException NVARCHAR(4000);     
    DECLARE @errSrc varchar(max),  @currentdate datetime=getdate() ;
    ----------------------------------------------------------------

    DECLARE @PlanYearID SMALLINT;
	

SELECT @PlanYearID=f.PlanyearID  FROM dbo.SavedPlanHeader f --New Update instead of using function [dbo].[fnGetBidYear]() [Discussed with business
	
	WHERE f.ForecastID=@ForecastIDFrom
	

    DECLARE @LastUpdate DATETIME;

    SET @LastUpdate = GETDATE();

    

    SET @IsFinal = 0;

    SELECT  @IsMAPD = IsMAPD
    FROM    SavedPlanHeader
    WHERE   ForecastID = @ForecastIDFrom;

    SET @IsActivePlan = 1;

    SET @IsToReprice = 1;

    --Determine whether the base plan is an MAPD plan and whether we are

    --copying to a new market.  Also get the existing region.

    SELECT 
            @MARegionID = MARegionID
    FROM    SavedPlanHeader
    WHERE   ForecastID = @ForecastIDFrom;


	-- added market copy instead of user input to align with how MAAUI is currently doing

	SELECT 
            @MarketIDTo = MarketID
    FROM    SavedPlanHeader
    WHERE   ForecastID = @ForecastIDFrom;
 
    DECLARE @Contract char(5),@Plan char(3), @Segment char(3)
	SELECT  @Contract=f.ContractNumber,@Plan=f.PlanID,@Segment=f.SegmentID ,@PMPM=PartBPremiumBuydown 
	FROM dbo.SavedPlanHeader f 
	
	WHERE f.ForecastID=@ForecastIDTo


  
    --We only want to commit the changes if ALL plan table inserts were
    --successful.

   BEGIN TRAN ;
	
-------------------------------------------------------------------------------------------------------------------------
--SavedPlanHeader--------------------------------------------------------------------------------------------------------

   -- BEGIN TRY
	
   --     INSERT  INTO SavedPlanHeader
   --             ( PlanYearID ,
   --               ForecastID ,
   --               MarketID ,
   --               PlanTypeID ,
   --               ContractNumber ,
   --               PlanID ,
   --               SegmentID ,
   --               CPDID ,
   --               ForecastIDParent ,
   --               IsActuarialSwap ,
   --               IsSNP ,
   --               IsEGWP ,
   --               ContactID ,
   --               SecondaryContactID ,
   --               CertifyingActuaryUserID ,
   --               PartBPremiumBuyDown ,
   --               DualEligiblePercent ,
   --               MARegionID ,
   --               Notes ,
   --               IsNonCalendarYear ,
   --               IsMAPD ,
   --               IsOffModelRx ,
   --               IsFinal ,
   --               IsHidden ,
   --               IsPartBOnly ,
   --               LastUpdateByID ,
   --               LastUpdateDateTime ,
   --               MARatingOptionID ,
   --               ManualForecastID ,
   --               ExperienceCredibility ,
   --               IsClaimBucket2Output ,
   --               ExpBaseForecastID ,
   --               IsToReprice ,
   --               SNPTypeID ,
   --               AltRebateOrder ,
   --               IsLiveIndex ,
   --               RebateOrderID ,
   --               PlanDescription ,
   --               IsMLA ,
   --               IsToRefreshOOPC ,
   --               QualityInitDescrip ,
   --               IsCombinedDeductible ,
   --               IsSkipInducedUtilizationMapping ,
   --               IsRiskPlan ,
   --               IsSCTPlan ,
   --               IsFiledPlan ,
   --               IsLocked ,
   --               PlanName
   --             )
   --             SELECT  @PlanYearID ,
   --                     @ForecastIDTo ,
   --                     @MarketIDTo ,
   --                     PlanTypeID ,
   --                     @Contract ,--New updated code to include the changes in CPS value in MAAUI to reflect in SavedPlanHeader
   --                     @Plan ,
   --                     @Segment ,   
   --                     CPDID ,
   --                     ISNULL(ForecastIDParent, @ForecastIDFrom) ,
   --                     IsActuarialSwap ,
   --                     IsSNP ,
   --                     IsEGWP ,
   --                     ContactID ,
   --                     SecondaryContactID ,
   --                     CertifyingActuaryUserID ,
   --                     @PMPM ,
   --                     DualEligiblePercent ,
   --                     @MARegionID ,
   --                     Notes ,
   --                     IsNonCalendarYear ,
   --                     @IsMAPD ,
   --                     @IsOffModelRx ,
   --                     @IsFinal ,
   --                     IsHidden ,
   --                     IsPartBOnly ,
   --                     @UserID ,
   --                     @LastUpdate ,
   --                     MARatingOptionID ,
   --                     ManualForecastID ,
   --                     ExperienceCredibility ,
   --                     IsClaimBucket2Output ,
   --                     ExpBaseForecastID ,
   --                     @IsToReprice ,
   --                     SNPTypeID ,
   --                     AltRebateOrder ,
   --                     0 AS IsLiveIndex , --default to not live for a newly copied plan
   --                     RebateOrderID ,
   --                     PlanDescription ,
   --                     IsMLA ,
   --                     IsToRefreshOOPC ,
   --                     QualityInitDescrip ,
			----RxBasicPremium,
			----RxSuppPremium,
   --                     IsCombinedDeductible ,
			----IUBasePlan,			
   --                     IsSkipInducedUtilizationMapping ,
   --                     IsRiskPlan ,
   --                     IsSCTPlan ,
   --                     IsFiledPlan ,
   --                     IsLocked ,
			----IUBasePlanManual,
   --                     PlanName
   --             FROM    SavedPlanHeader
   --             WHERE   ForecastID = @ForecastIDFrom;

   -- END TRY

   -- BEGIN CATCH
	
        --If the insert failed, return an error message.
        --ROLLBACK TRAN ;

   --     IF EXISTS ( SELECT  1
   --                 FROM    SavedPlanHeader
   --                 WHERE   ForecastID = @ForecastIDTo )
   --         SELECT  @ErrorMessage = 'Attempted to copy to index '
   --                 + CONVERT(VARCHAR, @ForecastIDTo)
   --                 + ', but this plan already exists.  '
   --                 + 'Please try again, or contact the model '
   --                 + +'development team if the problem persists.' ,
   --                 @ForecastIDTo = NULL;

   --     ELSE
   --         SELECT  @ForecastIDTo = NULL ,
   --                 @ErrorMessage = 'Could not copy plan header data.';
  
   --commit Tran;
   --     RETURN;

   -- END CATCH;

-------------------------------------------------------------------------------------------------------------------------
--SavedPlanAssumptions---------------------------------------------------------------------------------------------------
 
    BEGIN TRY

         INSERT INTO SavedPlanAssumptions
                SELECT  
                  @PlanYearID ,
                  @ForecastIDTo ,
                  @IsMAPD ,
                  ExpensePercent,
                  ProfitPercent,
                  RxBasicPremium,
                  RxSuppPremium,
                  ExpensePMPM,
                  SecondaryPayerAdjustment,
                  SalesAndMarketingPercent,
                  DirectAdminPercent,
                  IndirectAdminPercent,                  
                  SalesMembership,
                  QualityInitiatives,
                  TaxesAndFees,
                  BidBenefitString
                FROM SavedPlanAssumptions
                WHERE ForecastID = @ForecastIDFrom;

    END TRY

    BEGIN CATCH

        --If the insert failed, return an error message.
        
        SELECT  @ForecastIDTo = NULL ,
                @ErrorMessage = 'Could not copy plan assumptions.',
 				@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR);
--logger logic
        SELECT @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =GETDATE()    
        SELECT @ErrorMessageFromBackend = ERROR_Message()
        SELECT @ErrorSeverity = ERROR_SEVERITY(), 
               @ErrorState = ERROR_STATE(),
               @ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS varchar) 

   ---Insert into app log for logging error------------------    
        EXEC spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID  

         COMMIT TRAN;
        RETURN;

    END CATCH;

-------------------------------------------------------------------------------------------------------------------------
--spCopyPlanDetailTables-------------------------------------------------------------------------------------------------

    BEGIN TRY
      --Copy Detail Tables
		EXEC spCopyPlanDetailTables @ForecastIDFrom, @ForecastIDTo, @MarketIDTo,
            @IsSameMarket, @IsIncludeRiskMembership, @UserID,
            @ErrorMessage OUTPUT;

    END TRY

    BEGIN CATCH

     

        SELECT  @ForecastIDTo = NULL ,
                @ErrorMessage = 'Could not copy plan detail data.',
				@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR);
				--logger logic

        SELECT @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'), @currentdate =GETDATE()    
        SELECT @ErrorMessageFromBackend = ERROR_Message()
        SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR) 
    ---Insert into app log for logging error------------------    
        EXEC spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID  

        COMMIT TRAN;
        RETURN;

    END CATCH;



    IF @ErrorMessage IS NOT NULL
        BEGIN

        

            SET @ForecastIDTo = NULL;
			commit Tran;
            RETURN;

        END;

-------------------------------------------------------------------------------------------------------------------------
--SavedPlanINOONDistributionHeader---------------------------------------------------------------------------------------

    BEGIN TRY

        INSERT  INTO SavedPlanINOONDistributionHeader
					(PlanYearID,
					ForecastID,
					DistrDescription,
					LastUpdateByID,
					LastUpdateDateTime)
                SELECT  @PlanYearID ,
                        @ForecastIDTo ,
                        DistrDescription,
						@UserID,
						GETDATE()
                FROM    SavedPlanINOONDistributionHeader
                WHERE   ForecastID = @ForecastIDFrom;

				EXEC dbo.spCalcPlanBase @ForecastIDTo, 3, @UserID;

    END TRY

    BEGIN CATCH

     

        SELECT  @ForecastIDTo = NULL ,
                @ErrorMessage = 'Could not copy plan detail data.',
                @ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR);
				--logger logic
 
        SELECT @errSrc=ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate =GETDATE()    
        SELECT @ErrorMessageFromBackend = ERROR_Message()
        SELECT @ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)   
         
   ---Insert into app log for logging error------------------    
        EXEC spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessageFromBackend,@ErrorException,@UserID  

		COMMIT TRAN;
        RETURN;

    END CATCH;



    IF @ErrorMessage IS NOT NULL
        BEGIN

          

            SET @ForecastIDTo = NULL;
			commit Tran;
            RETURN;

        END;


    --Everything completed successfully.  Commit the changes.  The new plan
    --index and a null error message will be returned.
	
    COMMIT TRAN ;
    GO
GRANT EXECUTE ON  [dbo].[spCopyPlan] TO [MAPDModel]
GO