SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: fnAppGetMABPTWS7
--
-- CREATOR: <PERSON>ka
--
-- CREATED DATE: 2011-Jan-12
-- HEADER UPDATED: 2011-Jan-14
--
-- DESCRIPTION: Optional Supplemental Benefit (OSB) info for WS 7
--
-- PARAMETERS:
--	Input: 
--      @ForecastID     
--	Output:
--
-- TABLES:
--	Read:
--      SavedPlanOptionalPackageDetail
--      PerIntOptionalPackageHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-12      1           Initial Version.													Michael Siekerka
-- 2011-Jan-14      2           Removed NumberOfPackages as parameter, hard coded it                Michael Siekerka
-- 2011-May-26		3			Added correct total for TotalProjectedMemberMonths					Joe Casey
-- 2014-Mar-19		4			Changed the description to be the full description					Mason Roberts
-- 2014-Apr-10		5			Reverted the description changes									Mason Roberts
-- 2019-Feb-04		6			PackageIndex and PlanPackageID data type change						Keith Galloway
-- 2023-Sep-13      7			Added no lock for the tables										Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS7]
	(
    @ForecastID INT
	) 
RETURNS @Result TABLE
	(
	PlanPackageID TINYINT NOT NULL,
	[Description] VARCHAR(MAX) NOT NULL,
	PackageIndex INT NOT NULL,
	TotalExpense DECIMAL(5,2),
	TotalGainLoss DECIMAL(5,2),
	TotalProjectedMemberMonths DECIMAL(20,6)
	)
	AS
BEGIN
    INSERT INTO @Result
    SELECT TOP 5 --Number of packages to select
        s.PlanPackageID, 
        [Description] = RIGHT(RTRIM(p.[Description]),LEN(RTRIM(p.[Description]))-CHARINDEX('~',RTRIM(p.[Description]))), 
        s.PackageIndex, 
        p.TotalExpense, 
        p.TotalGainLoss, 
        TotalProjectedMemberMonths = SUM(mm.ProjectedMemberMonths * .01) --OSB projected membership is 1% of total projected membership
    FROM SavedPlanOptionalPackageDetail s WITH(NOLOCK)   
    INNER JOIN PerIntOptionalPackageHeader p WITH(NOLOCK)
        ON p.PackageIndex = s.PackageIndex
        AND p.IsEnabled = 1
    INNER JOIN fnPlanCountyProjectedMemberMonths(@ForecastID,2) mm
		ON s.ForecastID = mm.ForecastID
    WHERE s.IsHidden = 0
		AND s.ForecastID = @ForecastID
	GROUP BY s.PlanPackageID, 
        p.[Description], 
        s.PackageIndex, 
        p.TotalExpense, 
        p.TotalGainLoss
    ORDER BY s.PlanPackageID
    RETURN
END
GO
