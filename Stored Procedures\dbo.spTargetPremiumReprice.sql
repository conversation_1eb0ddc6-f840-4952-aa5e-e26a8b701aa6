SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spTargetPremiumReprice
--
-- CREATOR:			<PERSON>
--
-- CREATED DATE:	2010-DEC-27
--
-- DESCRIPTION:		Allows user to reprice by targeting premium or keeping the current premium if no target is specified.
--		
-- PARAMETERS:
--  Input  :		@ForecastID        INT
--					@UserID            VARCHAR(7)
--					@TargetPremium     DECIMAL(5, 2)
--
--  Output :		@ValidationMessage VARCHAR(MAX)
--
-- TABLES : 
--	Read :			SavedPlanAssumptions
--
--  Write:			CalcFinalPremium
--					SavedPlanAssumptions
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnAppGetBidSummary
--					fnGetMARebateAllocation
--					fnCalcProfitFromTargetMemberPremium
--					fnAppGetRevenueAndPremium
--
-- STORED PROCS:	spPlanRefresh
--					spUpdateOrInsertExpenseProfit
--					spUpdateOrInsertMAReportPlanLevel
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-OCT-18      1       Initial Version                                                         Michael Siekerka
-- 2011-FEB-08		2		Removed @Expenses														Joe Casey
-- 2011-FEB-14      3       Removed second, unnecessary select for @IsMAPD                          Michael Siekerka
-- 2011-APR-25		4		Changed spPlanRefresh proc from 19 to 18								Joe Casey
-- 2011-SEP-29		5		Changed spPlanRefresh proc from 18 to 19								Trevor Mahoney
-- 2012-MAR-20		6		Changed spPlanRefresh proc from 19 to 21								Mike Deren 
-- 2019-OCT-30		7		Removed 'HUMAD\' to UserID												Chhavi Sinha  
-- 2020-OCT-29		8		Removed Grant Execute													Rodney Smith
-- 2020-DEC-28		9		Added NOLOCK														    Mahendran Chinnaiah
-- 2022-NOV-23      10      Remove redundant function call and added changes from Jake Lewis        Khurram Minhas
-- 2023-MAY-30      11      Remove old profit percent function calls and replaced them with         Alex Brandt
--                          1 call to fnCalcProfitPercent											
-- 2023-AUG-09		12		Remove one call to fnCalcProfitPercent.									Jake Lewis
--							Replace with one call to fnCalcProfitFromTargetMemberPremium
--							Use FN results to directly update CalcFinalPremium (i.e. no longer 
--								call dbo.spCalcFinalPremium.
-- 2023-AUG-29		13		Pull Expenses and InsurerFee from fnCalcProfitFromTargetMemberPremium,	Jake Lewis
--								and write to CalcFinalPremium.	
-- 2023-SEP-11		14		Write to new fields TotalMemberPremium and RoundedMemberPrem in 
--								table CalcFinalPremium.												Jake Lewis
-- 2023-Oct-03		15		Cleaned up code as part of User Story #5165029							Alex Brandt
-- 2024-JAN-30		16		Change rounding on ProfitPMPM from fnCalcProfitFromTargetMemberPremium
--								to resolve some pesky penny problems in the BPTs					Jake Lewis
-- 2024-Oct-17      17      Adding user id for spWritePlanRepriceLog SP                             Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spTargetPremiumReprice]
    (
    @ForecastID        INT
   ,@UserID            VARCHAR(7)
   ,@ValidationMessage VARCHAR(MAX) OUT
   ,@TargetPremium     DECIMAL(5, 2) = -1)
AS
BEGIN

	SET NOCOUNT ON;
	SET ANSI_WARNINGS OFF;

    --Internal parameters
    DECLARE @XForecastID    INT           = @ForecastID
           ,@XUserID        VARCHAR(7)    = @UserID
           ,@XTargetPremium DECIMAL(5, 2) = @TargetPremium;

    DECLARE @ProfitPct DEC(10, 8);
    DECLARE @IsMAPD BIT;

    BEGIN TRY
        IF @XTargetPremium < 0 --No premium or negative premium was provided, use the current premium if one exists
            BEGIN
                DECLARE @result_fnAppGetBidSummary TABLE
                    (BasicMemberPremium FLOAT
                    ,RxBasicPremium     DECIMAL(9, 2)
                    ,RxSuppPremium      DECIMAL(9, 2)
                    ,CS                 DECIMAL(10, 2)
                    ,Sup_Prem           DECIMAL(10, 2));
                INSERT  @result_fnAppGetBidSummary
                SELECT  BasicMemberPremium
                       ,RxBasicPremium
                       ,RxSuppPremium
                       ,CS
                       ,Sup_Prem
                FROM    dbo.fnAppGetBidSummary (@XForecastID);

                DECLARE @result_fnGetMARebateAllocation TABLE
                    (RedABCostShare  DECIMAL(9, 2)
                    ,SuppPremBuydown DECIMAL(9, 2)
                    ,RxBasicBuyDown  DECIMAL(8, 1)
                    ,RxSuppBuyDown   DECIMAL(8, 1));
                INSERT  @result_fnGetMARebateAllocation
                SELECT  RedABCostShare
                       ,SuppPremBuydown 
                       ,RxBasicBuyDown
                       ,RxSuppBuyDown
                FROM    dbo.fnGetMARebateAllocation (@XForecastID);

                --Make sure the plan exists in the two functions first, if not then error out
                IF EXISTS (SELECT   1 FROM  @result_fnAppGetBidSummary)
                   AND EXISTS (SELECT  1 FROM  @result_fnGetMARebateAllocation)
                    BEGIN
                        SELECT  @XTargetPremium = bs.BasicMemberPremium + bs.CS - ra.RedABCostShare + bs.Sup_Prem
                                                 - ra.SuppPremBuydown + bs.RxBasicPremium - ra.RxBasicBuyDown
                                                  + bs.RxSuppPremium - ra.RxSuppBuyDown
                        FROM    @result_fnAppGetBidSummary bs
                               ,@result_fnGetMARebateAllocation ra;
                    END;
                ELSE 
                    BEGIN
                        RAISERROR ('Cannot determine target premium.', 16, 1);
                    END;
            END;

        --Now that we have a target premium, reprice the plan normally
        EXEC dbo.spPlanRefresh @XForecastID
                              ,@XUserID
                              ,@ValidationMessage = @ValidationMessage OUTPUT;

        IF @ValidationMessage = '' --Continue if there are no problems repricing the plan
            BEGIN

                -- Calculate and pull values needed to update CalcFinalPremium and SavedPlanAssumptions  
                DECLARE @result_fnCalcProfitFromTargetMemberPremium TABLE
                    (Profit             DECIMAL(14, 8)
                    ,ProfitPercentage   DECIMAL(10, 8)
                    ,Expenses           DECIMAL(14, 8)
                    ,TotalReqRev        DECIMAL(14, 8)
                    ,PlanBid            DECIMAL(14, 8)
                    ,StandardizedBid    DECIMAL(14, 8)
                    ,Savings            DECIMAL(14, 8)
                    ,Rebate             DECIMAL(14, 8)
                    ,BasicMemberPremium DECIMAL(14, 8)
                    ,GovtPremiumAdj     DECIMAL(14, 8)
                    ,UncollectedPremium DECIMAL(14, 8)
                    ,InsurerFee         DECIMAL(14, 8));
                INSERT INTO @result_fnCalcProfitFromTargetMemberPremium
                SELECT  Profit
                       ,ProfitPercentage
                       ,Expenses
                       ,TotalReqRev
                       ,PlanBid
                       ,StandardizedBid
                       ,Savings
                       ,Rebate
                       ,BasicMemberPremium
                       ,GovtPremiumAdj
                       ,UncollectedPremium
                       ,InsurerFee
                FROM    dbo.fnCalcProfitFromTargetMemberPremium (@XForecastID, @XTargetPremium, @XUserID);

                --Get other values needed to update profit percentage in SavedPlanAssumptions
                SELECT  @IsMAPD = IsMAPD
                FROM    dbo.SavedPlanAssumptions WITH (NOLOCK)
                WHERE   ForecastID = @XForecastID;

                --Load ProfitPct into SavedPlanAssumptions
                SET @ProfitPct = (SELECT    ProfitPercentage FROM  @result_fnCalcProfitFromTargetMemberPremium);
                EXEC dbo.spUpdateOrInsertExpenseProfit @XForecastID, @IsMAPD, @ProfitPct;

                --Write final values to CalcFinalPremium
                UPDATE  dbo.CalcFinalPremium
                SET     Profit = fcp.Profit
                       ,Expenses = fcp.Expenses
                       ,TotalReqRev = fcp.TotalReqRev
                       ,PlanBid = fcp.PlanBid
                       ,StandardizedBid = fcp.StandardizedBid
                       ,Savings = fcp.Savings
                       ,Rebate = fcp.Rebate
                       ,BasicMemberPremium = fcp.BasicMemberPremium
                       ,GovtPremiumAdj = fcp.GovtPremiumAdj
                       ,UncollectedPremium = fcp.UncollectedPremium
                       ,InsurerFee = fcp.InsurerFee
                       ,LastUpdateByID = @XUserID
                       ,LastUpdateDateTime = GETDATE ()
                FROM    @result_fnCalcProfitFromTargetMemberPremium fcp
                WHERE   ForecastID = @XForecastID;

                --Final step in plan reprice
                EXEC dbo.spUpdateOrInsertMAReportPlanLevel @XForecastID, @XUserID;

                --Write TotalMemberPremium and RoundedMemberPrem to CalcFinalPremium table
                UPDATE  dbo.CalcFinalPremium
                SET     TotalMemberPremium = (SELECT    TotalMemberPremium FROM fnAppGetRevenueAndPremium (@XForecastID) )
                       ,RoundedMemberPrem = (SELECT RoundedMemberPrem FROM  fnAppGetRevenueAndPremium (@XForecastID) )
                WHERE   CalcFinalPremium.ForecastID = @XForecastID;

            END;

        ELSE --Error Trapping
            BEGIN
                RAISERROR (@ValidationMessage, 16, 1);
            END;

        --All code executed properly, return 0 to indicate success
        SET @ValidationMessage = '0';
    END TRY

    -- In the event of an error, we return the error details to the client
    BEGIN CATCH
        SELECT  @ValidationMessage = ERROR_MESSAGE ();
    END CATCH;
END;
GO
