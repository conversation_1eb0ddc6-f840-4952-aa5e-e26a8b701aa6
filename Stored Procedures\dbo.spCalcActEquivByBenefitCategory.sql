SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcActEquivByBenefitCategory
--
-- CREATOR:			Christian Cofie
--
-- CREATED DATE:	2007-FEB-03 
--
-- DESCRIPTION:		This creates all of the calculations done on WS4 for use in the revenue portion of the pricing model.
--		
-- PARAMETERS:
--  Input  :		@ForecastID  INT
--					@UserID     CHAR(7)
--					@IsWeighted BIT
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			CalcTotalCostShare 
--					CalcBenchmarkSummary  
--					LkpIntBenefitCategory  
--					CalcBenefitProjection
--					CalcBenefitProjectionPreMbrCS  
--					LkpExtCMSBidServiceCategory  
--					LkpIntFFSCategory  
--					SavedPlanBenefitDetail
--					CalcActEquivByBenefitCategory (for weighted calcs)
--
--  Write:			CalcActEquivByBenefitCat
--
-- VIEWS: 
--	Read:			SavedPlanHeader
--
-- FUNCTIONS:		fnGetBidYear
--					fnPlanCountyProjectedMemberMonths
--					fnGetSafeDivisionResult
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																		DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-FEB-03		1		Initial Version                                                                         Christian Cofie   
-- 2007-APR-08      2		Removed double counting of ESRD.                                                        Sandy Ellis  
-- 2007-SEP-04      3		Added PlanYearID link to LkpIntBenefitCategory                                          Christian Cofie  
-- 2008-FEB-04      4		Revised code to bring it into coding standards.                                         Shannon Boykin  
-- 2008-FEB-23      5           Removed hardcoding by using IsLevel2Consecutive and FFSID                           Sandy Ellis  
--                                  and added PlanYearID link to LkpExtCMSBidServiceCategory  
-- 2008-MAR-11      6		Added LkpIntBenefitCategory.IsEnabled = 1 to Where Clause                               Brian Lake  
-- 2008-APR-28      7		Included PctCoveredAllowed, PctCoveredCostShare in select                               Brian Lake  
-- 2008-APR-29      8		Medicare covered % is now multiplied in the Allowed, CS,                                Brian Lake  
--								& CoveredActEquivNet  
-- 2008-MAY-1       9		Updated CalcBenefitProj to CalcBlendedBenefitProjection                                 Brian Lake  
-- 2008-MAY-4       10		Forced conversion to decimal(x.y) and used fnSignificantDigits                          Sandy Ellis  
-- 2008-MAY-5       11          Updated to use CalcTotalBenefitsByBenefitCategory and to                            Sandy Ellis  
--								handle Level 2 changes in Medicare Covered %s  
-- 2008-MAY-16      12		Removed references to CalcBenefitProj in comments                                       Sandy Ellis  
-- 2008-SEP-19      13		Added @UserID to the list of parameters and replaced all                                Shannon Boykin  
--							occurrences of SUSER_SNAME with @UserID.  
-- 2009-FEB-20       14      Eliminated the reference to the CalcPlanCountySummary                                   Mallika Eyunni  
--                              table and replaced it with two tables CalcBenchmarkFactors  
--                              CalcBenchmarkSummary  
-- 2009-FEB-23      15      Revised to use new vwFinalBenefitPricing for 2010 MOOP methodology                      Brian Lake  
-- 2009-MAR-11      16      Revised error handling                                                                  Brian Lake  
-- 2009-MAR-17      17      Data tyeps																				Sandy Ellis  
-- 2009-MAR-19      18      Added Dual Elig Type 3 temporarily                                                      Brian Lake  
-- 2009-MAR-20      19      Removed delete statement                                                                Brian Lake  
-- 2009-APR-21      20      Changed to Dual Elig Type <> 3, CASE WHEN to Cost Share                                 Brian Lake  
-- 2009-APR-22      21      Added case statements around AddedBenefitNet   
-- 2009-MAY-14      22      Changed calculation of Allowed for Duals for >2009 onl                                  Sandy Ellis  
-- 2009-MAY-15      23      Changed from CalcTotalBenefit... to CalcPlanCostShareRatio for Dual Type 1              Brian Lake  
-- 2010-JUN-14		24		Updated for 2012 DB and included functionality for "weighted" procedure					Michael Siekerka  
-- 2010-JUL-20      25      Adjusted for new DE methodology                                                         Jake Gaecke  
-- 2010-AUG-16		26		Removed PlanVersion, added PlanYearID                                                   Michael Siekerka  
-- 2010-OCT-05      27          Fixed reference to DualEligibleType = 3 and removed IsWeighted from INSERT          Michael Siekerka  
-- 2010-DEC-09      28          Fixed MAX statements where DualEligibleType <> 2                                    Trevor Mahoney  
-- 2011-JAN-14		29		Dual, NonDual, and TotalMembership changed from INT to DECIMAL(28,6)					Jiao Chen  
-- 2011-JAN-14      30          Corrected join to CalcPlanCostShareRatio                                            Casey Sanders  
-- 2011-MAR-11      31          Changed reference from vwFinalBenefitPricing to fnGetFinalBenefitPricing            Michael Siekerka  
-- 2011-JUN-02		32		Replaced LkpIntPlanYear with dbo.fnGetBidYear()											Bobby Jaegers  
-- 2011-JUN-14		33		Changed @PlanYearID to return SMALLINT instead of INT									Bobby Jaegers  
-- 2012-JAN-17		34		Added code to handle PartB Only															Tim Gao  
-- 2012-AUG-24		35		Added code to add 5%(Allowed) to PartB only SNF benefit									Tim Gao  
-- 2014-MAR-10		36		Modified for SQS																		Mike Deren
-- 2014-MAY-05		37		Modified for 2015 Part B Only Logic, now automated										Nick Koesters																		
-- 2015-MAY-11      38		Included Safe Division in case statement to avoid divide by zero error					Deepthi Thiyagu
-- 2017-OCT-18		39		Updated Allowed, CS, and Net calculations for DE#										Chris Fleming & Jordan Purdue
-- 2019-JUN-28      40      Replaced SavedPlanHeader with SavedForecastSetup in join condition                      Kritika Singh
-- 2019-JUN-28      41      Removed code for IsPartBOnly as this field is not present  in forecast table            Pooja Dahiya
-- 2019-OCT-07      42      Add 'HUMAD\' to UserID when length is not 13 to fix sp crash                            Manisha Tyagi
-- 2019-OCT-24		43		Removed 'HUMAD\' to UserID																Chhavi Sinha
-- 2019-NOV-06      44      Replaced SavedForecastSetup with SavedPlanHeader										Chhavi Sinha
-- 2019-DEC-17      45      Correcting isPartB assignment                                                           Brent Osantowski
-- 2021-JUL-28		46		Dual, NonDual, and TotalMembership changed from DECIMAL (28, 6) to DECIMAL (28, 15)	_	Jake Lewis
--								to align with fnPlanCountyProjectedMemberMonths output.  This helps to avoid _
--								calculation discrepancies between SQL output and the Excel audit file.
--							Added CAST-ing in CostShareReduction and SupplementalBenefitTotal calculations _
--								to avoid truncation during intermediate calculations.
--							Added CAST-ing in weighted calculation section to avoid truncation during _
--								intermediate calculations. 
-- 2022-APR-07		47		Replaced input @PlanIndex with @ForecastID; replaced reference from 
--							PlanIndex to ForecastID for tables CalcTotalBenefitsByBenefitCategory,
--							CalcPlanCostShareRatio, CalcBenefitProjectionPreMbrCS & CalcActEquivByBenefitCategory;
--							removed the BenefitOrdinalPercent field; added further specification for 
--							"CASE WHEN ffs.FFSID = 1" to remove duplication of DayRange benefits, similar to how
--							SNF facitility is being handled; @IsPartBOnly was removed as the value is always 0		Aleksandar Dimitrijevic
-- 2022-NOV-30      48	   	Added internal variables for input parameters, Added  WITH (NOLOCK), use temp table 	Khurram Minhas
--						   	for physical table  
-- 2024-AUG-28		49		Removed CalcPlanCostShareRatio from Read tables in comments; Renamed 
--							CalcTotalCostShareByBenCat to CalcTotalCostShare										Franklin Fu
-- 2024-OCT-08		50		Cost Share Basis: add handling on IsIncludeInCostShareBasis flag (select =1) from 
--								table CalcBenefitProjectionPreMbrCS, so that we're only bringing in the allowed
--								that forms our cost share basis														Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spCalcActEquivByBenefitCategory]
    (
    @ForecastID INT
   ,@UserID     CHAR(7)
   ,@IsWeighted BIT)

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY

            DECLARE @tranCount INT = @@TranCount; --Current transaction count
            DECLARE @errorMsg VARCHAR(500); --Variable for error messages 

            BEGIN TRANSACTION transaction_spCAE;

            DECLARE @XForecastID  INT     = @ForecastID
                   ,@XUserID      CHAR(7) = @UserID
                   ,@XIsWeighted  BIT     = @IsWeighted
                   ,@LastDateTime DATETIME
                   ,@PlanYearID   SMALLINT;

            SELECT  @PlanYearID = dbo.fnGetBidYear ();
            SET @LastDateTime = GETDATE ();

            BEGIN

                IF (SELECT  OBJECT_ID ('tempdb..#CalcActEquivByBenefitCategory')) IS NOT NULL
                    BEGIN
                        DROP TABLE #CalcActEquivByBenefitCategory;
                    END;

                CREATE TABLE #CalcActEquivByBenefitCategory
                    (PlanYearID               INT             NOT NULL
                    ,ForecastID               INT             NOT NULL
                    ,DualEligibleTypeID       TINYINT         NOT NULL
                    ,BenefitCategoryID        SMALLINT        NOT NULL
                    ,Allowed                  DECIMAL(20, 12) NOT NULL
                    ,CostShare                DECIMAL(20, 12) NOT NULL
                    ,Net                      DECIMAL(20, 12) NOT NULL
                    ,PercentCoveredAllowed    DECIMAL(7, 6)   NOT NULL
                    ,PercentCoveredCostShare  DECIMAL(7, 6)   NOT NULL
                    ,FFSActEquivCostShare     DECIMAL(14, 6)  NOT NULL
                    ,CoveredActEquivNet       DECIMAL(14, 6)  NOT NULL
                    ,AddedBenefitNet          DECIMAL(14, 6)  NOT NULL
                    ,CostShareReduction       DECIMAL(14, 6)  NOT NULL
                    ,SupplementalBenefitTotal DECIMAL(14, 6)  NOT NULL
                    ,LastUpdateByID           CHAR(7)         NOT NULL
                    ,LastUpdateDateTime       DATETIME        NOT NULL);

                --Main portion of calculation done here with Non-DE# and DE# having separate calcs
                IF @XIsWeighted = 0
                    BEGIN

                        DELETE  FROM dbo.CalcActEquivByBenefitCategory
                        WHERE   ForecastID = @XForecastID
                                AND DualEligibleTypeID <> 2;

                        INSERT INTO #CalcActEquivByBenefitCategory
                            (PlanYearID
                            ,ForecastID
                            ,DualEligibleTypeID
                            ,BenefitCategoryID
                            ,Allowed
                            ,CostShare
                            ,Net
                            ,PercentCoveredAllowed
                            ,PercentCoveredCostShare
                            ,FFSActEquivCostShare
                            ,CoveredActEquivNet
                            ,AddedBenefitNet
                            ,CostShareReduction
                            ,SupplementalBenefitTotal
                            ,LastUpdateByID
                            ,LastUpdateDateTime)
                        SELECT      @PlanYearID
                                   ,cbp.ForecastID
                                   ,cbp.DualEligibleTypeID
                                   ,cbp.BenefitCategoryID
                                    --==============================================================================  
                                    -- These first 5 fields are Total Benefits (Medicare Covered + Non Covered)  
                                    -- and correspond to the first 5 columns on WS4.  They have been pre-calculated  
                                    -- in CalcBenefitProjection and CalcTotalCostShare, so all we have to do is pick them up.  
                                    --==============================================================================  
                                   ,Allowed = ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                   ,CostShare = CASE WHEN cbp.DualEligibleTypeID = 1 THEN
                                                         ISNULL (bp.INAllowed + bp.OONAllowed, 0)
                                                         * ISNULL (tcs.BenefitCostShareRatio, 0)
                                                     ELSE ISNULL (tcs.INTotalCostShare + tcs.OONTotalCostShare, 0) END
                                   ,Net = ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                          - CASE WHEN cbp.DualEligibleTypeID = 1 THEN
                                                     ISNULL (bp.INAllowed + bp.OONAllowed, 0)
                                                     * ISNULL (tcs.BenefitCostShareRatio, 0)
                                                 ELSE ISNULL (tcs.INTotalCostShare + tcs.OONTotalCostShare, 0) END
                                   ,PctCoveredAllowed = ISNULL (SPBD.PercentCoveredAllowed, 0)
                                   ,PctCoveredCostShare = ISNULL (SPBD.PercentCoveredCostShare, 0)
                                    --==============================================================================  
                                    -- This just a lookup of the FFS equivalences provided by CMS.  
                                    --==============================================================================  
                                   ,FFSActEquivCostShare = CASE WHEN ffs.FFSID = 1 --IP
                                   THEN                             bs.PlanIPCostShare
                                                                WHEN ffs.FFSID = 2 --OP  
                                   THEN                             bs.PlanOPCostShare
                                                                WHEN ffs.FFSID = 3 --SNF
                                   THEN                             bs.PlanSNFCostShare
                                                                WHEN ffs.FFSID = 5 -- OTHER  
                                   THEN                             bs.PlanOtherCostShare
                                                                ELSE 0 END
                                    --==============================================================================  
                                    -- This corresponds to the Medicare Covered Net pmpm  
                                    -- = Total Allowed * Medicare Covered Allowed % * (1-FFSActEquivCostShare)  
                                    --==============================================================================  
                                   ,CoveredActEquivNet = CASE WHEN bp.DualEligibleTypeID = 1 THEN
                                   (1 - ISNULL (tcs.BenefitCostShareRatio, 0)) * ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                   * ISNULL (SPBD.PercentCoveredAllowed, 1)
                                                              ELSE
                                                                  ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                                                  * ISNULL (SPBD.PercentCoveredAllowed, 1)
                                                                  * (1
                                                                     - CASE WHEN ffs.FFSID = 1 --IP
                                   THEN
                                                                                bs.PlanIPCostShare
                                                                            WHEN ffs.FFSID = 2 --OP  
                                   THEN
                                                                                bs.PlanOPCostShare
                                                                            WHEN ffs.FFSID = 3 --SNF
                                   THEN
                                                                                bs.PlanSNFCostShare
                                                                            WHEN ffs.FFSID = 5 --OTHER  
                                   THEN
                                                                                bs.PlanOtherCostShare
                                                                            ELSE 0 END) END
                                    --==============================================================================  
                                    -- This corresponds to the Non-Covered Net pmpm (Net pmpm for add'l svcs)  
                                    -- = Total Allowed * (1 - Medicare Covered Allowed %)   
                                    --    - Total Cost Share * (1 - Medicare Covered Cost Share %)  
                                    --==============================================================================  
                                   ,AddedBenefitNet = CASE WHEN bp.DualEligibleTypeID = 1 THEN
                                   (1 - ISNULL (tcs.BenefitCostShareRatio, 0)) * ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                   * (1 - ISNULL (SPBD.PercentCoveredAllowed, 0))
                                                           ELSE
                                                               ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                                               * (1 - ISNULL (SPBD.PercentCoveredAllowed, 0))
                                                               - ISNULL (tcs.INTotalCostShare + tcs.OONTotalCostShare, 0)
                                                               * (1 - ISNULL (SPBD.PercentCoveredCostShare, 0)) END
                                    --==============================================================================  
                                    -- This corresponds to the Reduction of A/B Cost Share  
                                    -- = FFS AE Cost Sharing - Plan Cost share for Medicare Covered Svcs  
                                    -- = [FFSActEquivCostShare] * [Total Allowed] * [PctCoveredAllowed]  
                                    --    - Total Cost Share * (1 - Medicare Covered Cost Share %)  
                                    --==============================================================================  
                                   ,CostShareReduction = CASE WHEN bp.DualEligibleTypeID = 1 THEN 0
                                                              ELSE
                                                                  CAST(CAST(CAST(CASE WHEN ffs.FFSID = 1 --IP
                                   THEN                                                    bs.PlanIPCostShare
                                                                                      WHEN ffs.FFSID = 2 -- OP  
                                   THEN                                                    bs.PlanOPCostShare
                                                                                      WHEN ffs.FFSID = 3 --SNF
                                   THEN                                                    bs.PlanSNFCostShare
                                                                                      WHEN ffs.FFSID = 5 --OTHER  
                                   THEN                                                    bs.PlanOtherCostShare
                                                                                      ELSE 0 END AS FLOAT)
                                                                            * ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                                                            * ISNULL (SPBD.PercentCoveredAllowed, 0) AS FLOAT)
                                                                       - CAST(ISNULL (
                                                                              tcs.INTotalCostShare + tcs.OONTotalCostShare
                                                                             ,0) * ISNULL (SPBD.PercentCoveredCostShare, 0) AS FLOAT) AS DECIMAL(14, 6))END
                                    --==============================================================================  
                                    -- This corresponds to the Suplemental Benefit Total  
                                    -- = Added Benefit Net + Cost Share Reduction  
                                    --==============================================================================  
                                   ,SupplementalBenefitTotal = CASE WHEN bp.DualEligibleTypeID = 1 THEN
                                   (1 - ISNULL (tcs.BenefitCostShareRatio, 0)) * ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                   * (1 - ISNULL (SPBD.PercentCoveredAllowed, 0))
                                                                    ELSE
                                                                        CAST(ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                                                             * (1 - ISNULL (SPBD.PercentCoveredAllowed, 0)) AS FLOAT)
                                                                        - CAST(ISNULL (
                                                                               tcs.INTotalCostShare + tcs.OONTotalCostShare
                                                                              ,0)
                                                                               * (1
                                                                                  - ISNULL (SPBD.PercentCoveredCostShare, 0)) AS FLOAT)
                                                                        + CAST(CAST(CASE WHEN ffs.FFSID = 1 --IP
                                   THEN
                                                                                             bs.PlanIPCostShare
                                                                                         WHEN ffs.FFSID = 2 -- OP  
                                   THEN
                                                                                             bs.PlanOPCostShare
                                                                                         WHEN ffs.FFSID = 3 --SNF
                                   THEN
                                                                                             bs.PlanSNFCostShare
                                                                                         WHEN ffs.FFSID = 5 --OTHER  
                                   THEN
                                                                                             bs.PlanOtherCostShare
                                                                                         ELSE 0 END AS FLOAT)
                                                                               * ISNULL (cbp.INAllowed + cbp.OONAllowed, 0)
                                                                               * ISNULL (SPBD.PercentCoveredAllowed, 0) AS FLOAT)
                                                                        - CAST(ISNULL (
                                                                               tcs.INTotalCostShare + tcs.OONTotalCostShare
                                                                              ,0)
                                                                               * ISNULL (SPBD.PercentCoveredCostShare, 0) AS FLOAT)END
                                   ,@XUserID
                                   ,@LastDateTime
                        FROM        dbo.CalcTotalCostShare tcs WITH (NOLOCK)
                       INNER JOIN   dbo.CalcBenefitProjection cbp
                               ON cbp.ForecastID = tcs.ForecastID
                                  AND   cbp.BenefitCategoryID = tcs.BenefitCategoryID
                       INNER JOIN   dbo.CalcBenchmarkSummary bs WITH (NOLOCK)
                               ON bs.ForecastID = tcs.ForecastID
                       INNER JOIN   dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
                               ON bc.BenefitCategoryID = tcs.BenefitCategoryID
                       INNER JOIN   dbo.CalcBenefitProjectionPreMbrCS bp WITH (NOLOCK)
                               ON bp.BenefitCategoryID = bc.BenefitCategoryID
                                  AND   bp.ForecastID = bs.ForecastID
                                  AND   bp.DualEligibleTypeID = cbp.DualEligibleTypeID
                                  AND   bp.MARatingOptionID = cbp.MARatingOptionID
                       INNER JOIN   dbo.SavedPlanHeader sph WITH (NOLOCK)
                               ON sph.ForecastID = bs.ForecastID
                       INNER JOIN   dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
                               ON SPBD.ForecastID = tcs.ForecastID
                                  AND   SPBD.BenefitCategoryID = tcs.BenefitCategoryID
                                  AND   SPBD.IsLiveIndex = 1
                                  AND   SPBD.IsBenefitYearCurrentYear = 0
                                  AND   SPBD.BenefitOrdinalID = 1
                        LEFT JOIN   dbo.LkpExtCMSBidServiceCategory c WITH (NOLOCK)
                               ON c.BidServiceCategoryID = bc.BidServiceCatID
                        LEFT JOIN   dbo.LkpIntFFSCategory ffs WITH (NOLOCK)
                               ON ffs.FFSID = c.FFSID
                        WHERE       bp.ForecastID = @XForecastID
                                    AND bc.IsEnabled = 1
                                    AND bp.DualEligibleTypeID <> 2
                                    AND bp.MARatingOptionID = 3 --Blended
                                    AND bp.IsIncludeInCostShareBasis = 1 --Only include Allowed from the cost share basis
                                    AND tcs.IsBenefitYearCurrentYear = 0;

                        INSERT INTO dbo.CalcActEquivByBenefitCategory

                            (PlanYearID
                            ,ForecastID
                            ,DualEligibleTypeID
                            ,BenefitCategoryID
                            ,Allowed
                            ,CostShare
                            ,Net
                            ,PercentCoveredAllowed
                            ,PercentCoveredCostShare
                            ,FFSActEquivCostShare
                            ,CoveredActEquivNet
                            ,AddedBenefitNet
                            ,CostShareReduction
                            ,SupplementalBenefitTotal
                            ,LastUpdateByID
                            ,LastUpdateDateTime)
                        SELECT  cb.PlanYearID
                               ,cb.ForecastID
                               ,cb.DualEligibleTypeID
                               ,cb.BenefitCategoryID
                               ,cb.Allowed
                               ,cb.CostShare
                               ,cb.Net
                               ,cb.PercentCoveredAllowed
                               ,cb.PercentCoveredCostShare
                               ,cb.FFSActEquivCostShare
                               ,cb.CoveredActEquivNet
                               ,cb.AddedBenefitNet
                               ,cb.CostShareReduction
                               ,cb.SupplementalBenefitTotal
                               ,cb.LastUpdateByID
                               ,cb.LastUpdateDateTime
                        FROM    #CalcActEquivByBenefitCategory cb;
                    END;

                --Update the weighted portion using the separate Non-DE# and DE# values from the same table


                ELSE IF @XIsWeighted = 1
                         BEGIN

                             DELETE FROM dbo.CalcActEquivByBenefitCategory
                             WHERE  ForecastID = @XForecastID
                                    AND DualEligibleTypeID = 2;

                             DECLARE @NonDualMembership DECIMAL(28, 15)
                                    ,@DualMembership    DECIMAL(28, 15)
                                    ,@TotalMembership   DECIMAL(28, 15);

                             SELECT @NonDualMembership = SUM (ProjectedMemberMonths)
                             FROM   dbo.fnPlanCountyProjectedMemberMonths (@XForecastID, 0);

                             SELECT @DualMembership = SUM (ProjectedMemberMonths)
                             FROM   dbo.fnPlanCountyProjectedMemberMonths (@XForecastID, 1);

                             SELECT @TotalMembership = @NonDualMembership + @DualMembership;

                             IF @TotalMembership > 0 --Make sure we have membership  
                                 BEGIN
                                     INSERT INTO    #CalcActEquivByBenefitCategory
                                         (PlanYearID
                                         ,ForecastID
                                         ,DualEligibleTypeID
                                         ,BenefitCategoryID
                                         ,Allowed
                                         ,CostShare
                                         ,Net
                                         ,PercentCoveredAllowed
                                         ,PercentCoveredCostShare
                                         ,FFSActEquivCostShare
                                         ,CoveredActEquivNet
                                         ,AddedBenefitNet
                                         ,CostShareReduction
                                         ,SupplementalBenefitTotal
                                         ,LastUpdateByID
                                         ,LastUpdateDateTime)
                                     SELECT     @PlanYearID
                                               ,Dual.ForecastID
                                               ,DualEligibleTypeID = 2
                                               ,Dual.BenefitCategoryID
                                               ,Allowed = CAST(dbo.fnGetSafeDivisionResult (
                                                               CAST(@DualMembership * Dual.Allowed + @NonDualMembership
                                                                    * NonDual.Allowed AS FLOAT)
                                                              ,@TotalMembership) AS DECIMAL(20, 12))
                                               ,CostShare = CAST(dbo.fnGetSafeDivisionResult (
                                                                 CAST(@DualMembership * Dual.CostShare + @NonDualMembership
                                                                      * NonDual.CostShare AS FLOAT)
                                                                ,@TotalMembership) AS DECIMAL(20, 12))
                                               ,Net = CAST(dbo.fnGetSafeDivisionResult (
                                                           CAST(@DualMembership * Dual.Net + @NonDualMembership
                                                                * NonDual.Net AS FLOAT)
                                                          ,@TotalMembership) AS DECIMAL(20, 12))
                                               ,PctCoveredAllowed = CAST(dbo.fnGetSafeDivisionResult (
                                                                         CAST(@DualMembership * Dual.PercentCoveredAllowed
                                                                              + @NonDualMembership
                                                                              * NonDual.PercentCoveredAllowed AS FLOAT)
                                                                        ,@TotalMembership) AS DECIMAL(7, 6))
                                               ,PctCoveredCostShare = CAST(dbo.fnGetSafeDivisionResult (
                                                                           CAST(@DualMembership
                                                                                * Dual.PercentCoveredCostShare
                                                                                + @NonDualMembership
                                                                                * NonDual.PercentCoveredCostShare AS FLOAT)
                                                                          ,@TotalMembership) AS DECIMAL(7, 6))
                                               ,FFSActEquivCostShare = CAST(dbo.fnGetSafeDivisionResult (
                                                                            CAST(@DualMembership
                                                                                 * Dual.FFSActEquivCostShare
                                                                                 + @NonDualMembership
                                                                                 * NonDual.FFSActEquivCostShare AS FLOAT)
                                                                           ,@TotalMembership) AS DECIMAL(14, 6))
                                               ,CoveredActEquivNet = CAST(dbo.fnGetSafeDivisionResult (
                                                                          CAST(@DualMembership * Dual.CoveredActEquivNet
                                                                               + @NonDualMembership
                                                                               * NonDual.CoveredActEquivNet AS FLOAT)
                                                                         ,@TotalMembership) AS DECIMAL(20, 10))
                                               ,AddedBenefitNet = CAST(dbo.fnGetSafeDivisionResult (
                                                                       CAST(@DualMembership * Dual.AddedBenefitNet
                                                                            + @NonDualMembership * NonDual.AddedBenefitNet AS FLOAT)
                                                                      ,@TotalMembership) AS DECIMAL(20, 10))
                                               ,CostShareReduction = CAST(dbo.fnGetSafeDivisionResult (
                                                                          CAST(@DualMembership * Dual.CostShareReduction
                                                                               + @NonDualMembership
                                                                               * NonDual.CostShareReduction AS FLOAT)
                                                                         ,@TotalMembership) AS DECIMAL(20, 10))
                                               ,SupplementalBenefitTotal = CAST(dbo.fnGetSafeDivisionResult (
                                                                                CAST(@DualMembership
                                                                                     * Dual.SupplementalBenefitTotal
                                                                                     + @NonDualMembership
                                                                                     * NonDual.SupplementalBenefitTotal AS FLOAT)
                                                                               ,@TotalMembership) AS DECIMAL(20, 10))
                                               ,Dual.LastUpdateByID
                                               ,Dual.LastUpdateDateTime
                                     FROM       dbo.CalcActEquivByBenefitCategory Dual WITH (NOLOCK)
                                    INNER JOIN  dbo.CalcActEquivByBenefitCategory NonDual WITH (NOLOCK)
                                            ON Dual.ForecastID = NonDual.ForecastID
                                               AND  Dual.BenefitCategoryID = NonDual.BenefitCategoryID
                                     WHERE      Dual.ForecastID = @XForecastID
                                                AND Dual.DualEligibleTypeID = 1
                                                AND NonDual.DualEligibleTypeID = 0;

                                     INSERT INTO    dbo.CalcActEquivByBenefitCategory
                                         (PlanYearID
                                         ,ForecastID
                                         ,DualEligibleTypeID
                                         ,BenefitCategoryID
                                         ,Allowed
                                         ,CostShare
                                         ,Net
                                         ,PercentCoveredAllowed
                                         ,PercentCoveredCostShare
                                         ,FFSActEquivCostShare
                                         ,CoveredActEquivNet
                                         ,AddedBenefitNet
                                         ,CostShareReduction
                                         ,SupplementalBenefitTotal
                                         ,LastUpdateByID
                                         ,LastUpdateDateTime)
                                     SELECT cb.PlanYearID
                                           ,cb.ForecastID
                                           ,cb.DualEligibleTypeID
                                           ,cb.BenefitCategoryID
                                           ,cb.Allowed
                                           ,cb.CostShare
                                           ,cb.Net
                                           ,cb.PercentCoveredAllowed
                                           ,cb.PercentCoveredCostShare
                                           ,cb.FFSActEquivCostShare
                                           ,cb.CoveredActEquivNet
                                           ,cb.AddedBenefitNet
                                           ,cb.CostShareReduction
                                           ,cb.SupplementalBenefitTotal
                                           ,cb.LastUpdateByID
                                           ,cb.LastUpdateDateTime
                                     FROM   #CalcActEquivByBenefitCategory cb;
                                 END;
                         END;

                IF (SELECT  OBJECT_ID ('tempdb..#CalcActEquivByBenefitCategory')) IS NOT NULL
                    BEGIN
                        DROP TABLE #CalcActEquivByBenefitCategory;
                    END;

            END;

            COMMIT TRANSACTION transaction_spCAE;

        END TRY

        BEGIN CATCH

            IF (@@TranCount > @tranCount) --Check if transaction in TRY block was not closed

                BEGIN

                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transaction_spCAE;

                END;

        END CATCH;

    END;
GO
