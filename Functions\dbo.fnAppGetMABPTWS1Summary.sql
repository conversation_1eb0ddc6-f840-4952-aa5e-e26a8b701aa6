SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- User Defined Function

-- FUNCTION NAME: fnAppGetMABPTWS1Summary
--
-- AUTHOR: <PERSON> Lake
--
-- CREATED DATE: 2008-Aug-13
-- HEADER UPDATED: 2011-Jan-07
--
-- DESCRIPTION: Function responsible for grouping data that is displayed on WS 1. This summarizes the underlying
--				function fnAppGetMABPTWS1.  
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--	Output:
--
-- TABLES:
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnGetBaseMSBs
--      fnAppGetMABPTWS1
--      fnGetSafeDivisionResultReturnOne
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2008-Aug-13      1           Initial Version                                                     Brian Lake
-- 2009-Feb-26      2           Removed user id parameter                                           Brian Lake
-- 2009-Mar-17      3           Data types                                                          Sandy Ellis
-- 2009-Mar-20      4           Safe Division by zero                                               Keith Galloway
-- 2009-Apr-05      5           Alter Cost product                                                  Sandy Ellis
-- 2009-Apr-07      6           Add benefit change product                                          Sandy Ellis
-- 2009-May-13      7           Population Change from dec(16,15) to dec(17,15)                     Keith Galloway
-- 2009-Dec-28      8           Added NetPMPM column                                                Nick Skeen
-- 2010-Jan-11      9           Added InflationTrend Column                                         Nick Skeen
-- 2010-Jan-28      10          Switched InflationTrend and CostTrend columns to match              Joe Casey
--                                calculations
-- 2010-Feb-25      11          Added Mandatory Supplemental Benefits                               Jake Gaecke
-- 2010-Mar-13      12          Redefined UtilAdditiveAdjustment and PMPMAdditiveAdjustment         Casey Sanders
-- 2010-Apr-11      13          Rounding added benefit trend factors to 6 decimals                  Casey Sanders
-- 2010-Apr-21      14          Case for AB inflation trend to set to 1.0 if rounds to 0.           Casey Sanders
-- 2010-Apr-29      15          Replaced V.14,changed the precision on the variables that           Sule Dauda
--                                compute AB Inflation Trend to ensure that values close to zero
--                                do not result in inlationTrend of zero ( rather 1)	
-- 2010-May-11      16          Change casting added in ver 15 to DECIMAL(5,2)                      Casey Sanders		
-- 2011-Jan-06      17          Revised for 2012 Database and reduced 2 queries to 1                Michael Siekerka
-- 2011-Feb-04      18          Changed rounding from 6 decimals to 15 on factors                   Michael Siekerka
-- 2011-Feb-04      19          Increased precision on inflation trend to eliminate rounding err    Michael Siekerka
-- 2011-Feb-27      20          Updated 'pre' query as MSB's need to be handled seperately          Nate Jacoby
-- 2011-Mar-18      21          Changed UtilAdditiveAdjustment and PMPMAdditiveAdjustment for       Joe Casey
--                                for MSBs to prevent new MSBs from having an additive
--                                component, creating a projected experience value.
-- 2011-Mar-24      22          Changed fnGetSafeDivisionResult to fnGetSafeDivisionResultReturnOne Joe Casey
-- 2011-Mar-30      23          Changed 'InflationTrend' to 'ProviderPaymentChange'.                Joe Casey
--                                CostTrend is now backed into, instead of ProviderPaymentChange.
-- 2012-Mar-26      24          Added Utilizers to allow the data to flow into fnAppMABPTCreation   Alex Rezmerski
-- 2012-Oct-10      25          Added IF Statement to account for Group - *More changes to Come     Mike Deren
-- 2013-Apr-14      26          Changed the casting of "UtilAdditiveAdjustment" and 
--                                "PMPMAdditiveAdjustment" from decimal (10,6) to (38,6)            Fayaz Syed
-- 2015-Feb-??      27          Added logic to rollup utilization using Uilt/1000                   Chris McAuley
-- 2015-Mar-21      28          Corrected safe division logic in Cost Trend calculation             Matthew Evans
--                                to handle categories with zero utilization
-- 2015-Jul-31      29          Case for UtilAdditiveAdjustment and PMPmAdditiveAdjustment          Mark Freel
--                                updated to check if both base util and allowed are 0.		
-- 2020-Oct-26      30          Backend Alignment and Restructuring                                 Keith Galloway	
-- 2021-Jan-11		31			OtherFactor & ProviderPaymentChange from Decimal (22,21) to (30,22) Franklin Fu
-- 2022-Oct-21      23          Modifications to handle multiplicative trends for existing added 
--								  benefits (MSBs)													Michael Manes
-- 2023-Apr-10		24			Implementing  flag to enable/disable multiplicative methodology		Aleksandar Dimitrijevic
-- 2023-Jul-07		25			Removed field CostUtilizationProduct from temp table @MSBInfo		Aleksandar Dimitrijevic
-- 2025-Jan-29      26          Edits to OtherFactor for bid audit remediation                      Zoey Glenn
-- 2025-JAN-30		27			Include new SurplusDeficit field for BPT requirements				Michael Manes
-- 2025-Apr-23		28			Added missing calculations logic of CostTrend (25.03) - observerd by Zoey				Kumar Jalendran
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS1Summary]
	(
     @ForecastID INT
	)

RETURNS @Results TABLE 
	(
     ForecastID INT,
     ServiceCategoryCode VARCHAR(4),
     UtilType CHAR(1),
     BaseUnits DECIMAL(30, 22),
     AvgCost DECIMAL(30, 22),
     BaseAllowed DECIMAL(30, 22),
	 BaseNetPMPM DECIMAL(30, 22),
     UtilizationTrend DECIMAL(30, 22),
     BenefitChange DECIMAL(30, 22),
     PopulationChange DECIMAL(30, 22),
     OtherFactor DECIMAL(30, 22),
	 ProviderPaymentChange DECIMAL(30, 22),
	 CostTrend DECIMAL(30, 22),
     UtilAdditiveAdjustment DECIMAL(38, 6),
     PMPMAdditiveAdjustment DECIMAL(38, 6),
     SurplusDeficit DECIMAL(14, 6)
	) 
AS
BEGIN

	-- declare variables
	DECLARE @XForecastID INT = @ForecastID;
	DECLARE @MSBMethod BIT;

	-- set values
	SET @MSBMethod = (SELECT IsMSBWS1MultFactorsEnabled FROM dbo.LkpModelSettings);

			-- MSB Information
			DECLARE @MSBInfo TABLE
				(
				 ForecastID INT,
				 ServiceCategoryCode VARCHAR(4),
				 UtilType CHAR(1),
				 BaseUnits DECIMAL(30, 22),
				 AvgCost DECIMAL(30, 22),
				 BaseAllowed DECIMAL(30, 22),
				 BaseNetPMPM DECIMAL(30, 22),
				 UtilizationTrendProduct DECIMAL(30, 22),
				 BenefitChangeProduct DECIMAL(30, 22),
				 PopulationChangeProduct DECIMAL(30, 22),
				 OtherFactorProduct DECIMAL(30, 22),
				 TotalProduct DECIMAL(30, 22),
				 ProviderPaymentProduct DECIMAL(30, 22),
				 UtilAdditiveAdjustment DECIMAL(38, 6),
				 PMPMAdditiveAdjustment DECIMAL(38, 6),
				 BidUtilization DECIMAL(23, 15),
				 BidAllowed DECIMAL(23, 15)
				);

			INSERT INTO @MSBInfo
			SELECT ForecastID,
				   ServiceCategoryCode,
				   UtilType,
				   SUM(BaseUnits) BaseUnits,   
				   SUM(AvgCost) AvgCost,
				   SUM(BaseAllowed) BaseAllowed,
				   SUM(BaseNetPMPM) BaseNetPMPM,
				   SUM(UtilizationTrendProduct) UtilizationTrendProduct,
				   SUM(BenefitChangeProduct) BenefitChangeProduct,
				   SUM(PopulationChangeProduct) PopulationChangeProduct,
				   SUM(OtherFactorProduct) OtherFactorProduct,
				   SUM(TotalProduct) TotalProduct,
				   SUM(ProviderPaymentProduct) ProviderPaymentProduct,
				   SUM(UtilAdditiveAdjustment) UtilAdditiveAdjustment,
				   SUM(PMPMAdditiveAdjustment) PMPMAdditiveAdjustment,
				   SUM(BidUtilization) BidUtilization,
				   SUM(BidAllowed) BidAllowed
			  FROM dbo.fnGetBaseMSBs(@XForecastID, 1)
			 GROUP BY ForecastID,
					  ServiceCategoryCode,
					  UtilType;

		DECLARE @pre TABLE
			(
			 ForecastID INT,
			 ServiceCategoryCode VARCHAR(4),
			 UtilType CHAR(1),
			 IsAddedBenCat BIT,
			 BaseUnits DECIMAL(30, 22),
			 AvgCost DECIMAL(30, 22),
			 BaseAllowed DECIMAL(30, 22),
			 BaseNetPMPM DECIMAL(30, 22),
			 UtilizationTrend DECIMAL(30, 22),
			 BenefitChange DECIMAL(30, 22),
			 PopulationChange DECIMAL(30, 22),
			 OtherFactor DECIMAL(30, 22),
			 ProviderPaymentChange DECIMAL(30, 22),
			 CostTrend DECIMAL(30, 22),
			 UtilAdditiveAdjustment DECIMAL(38, 6),
			 PMPMAdditiveAdjustment DECIMAL(38, 6),
			 BidAllowed DECIMAL(23, 15),
			 SurplusDeficit DECIMAL(14, 6)
			);

		-- prepping the output
		INSERT INTO  @pre
		SELECT ForecastID,
			   ServiceCategoryCode,
			   UtilType,
			   IsAddedBenCat = CASE WHEN UtilAdditiveAdjustment IS NULL THEN 0
									ELSE 1
							   END,
			   BaseUnits = ROUND(SUM(BaseUnits),12),
			   AvgCost = ROUND(SUM(AvgCost),12),
			   BaseAllowed = ROUND(SUM(BaseAllowed),12),
			   BaseNetPMPM = ROUND(SUM(BaseNetPMPM),12),
			   UtilizationTrend = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(UtilizationTrendProduct), SUM(BaseUnits)),12),/*Base Units?*/
			   BenefitChange = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(BenefitChangeProduct), SUM(BaseUnits)),12), /*Base Units?*/
			   PopulationChange = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(PopulationChangeProduct), SUM(BaseUnits)),12), /*Base Units?*/
			   OtherFactor = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(OtherFactorProduct), SUM(BaseUnits)*
																			ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(UtilizationTrendProduct), SUM(BaseUnits)),12)*
																			ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(BenefitChangeProduct), SUM(BaseUnits)),12)*
																			ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(PopulationChangeProduct), SUM(BaseUnits)),12)),12), /*Base Units?*/
			   ProviderPaymentChange = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(ProviderPaymentProduct), SUM(BaseAllowed)),12),
			   CostTrend = ROUND(dbo.fnGetSafeDivisionResultReturnOne(CAST (SUM(TotalProduct) AS DECIMAL (30, 22)) ,/**Where is Total Product coming from**/
																		   (ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(UtilizationTrendProduct), SUM(BaseUnits)),12) *
																													   ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(BenefitChangeProduct), SUM(BaseUnits)),12)*
																													   ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(PopulationChangeProduct), SUM(BaseUnits)),12)*
																													   ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(OtherFactorProduct), SUM(BaseUnits)*
																													   ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(UtilizationTrendProduct), SUM(BaseUnits)),12)*
																													   ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(BenefitChangeProduct), SUM(BaseUnits)),12)*
																													   ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(PopulationChangeProduct), SUM(BaseUnits)),12)),12)*
																													   CAST (SUM(ProviderPaymentProduct) AS DECIMAL (30, 22))
																		   )),15),
			   UtilAdditiveAdjustment = ROUND(SUM(UtilAdditiveAdjustment),12),
			   PMPMAdditiveAdjustment = ROUND(SUM(PMPMAdditiveAdjustment),12),
			   BidAllowed = 0, --used for added benefit calculation, need matching column counts for union
			   SurplusDeficit = ROUND(SUM(SurplusDeficit),12)
		  FROM dbo.fnAppGetMABPTWS1(@XForecastID)
		 GROUP BY ForecastID,
				  ServiceCategoryCode,
				  UtilType,
			      UtilAdditiveAdjustment

		 UNION ALL

		SELECT MSBINFO.ForecastID,
			   MSBINFO.ServiceCategoryCode,
			   MSBINFO.UtilType,
			   IsAddedBenCat = CASE WHEN MSBINFO.ServiceCategoryCode IN ('l.','m.','n.','o.','p.','q.') THEN 1
								    ELSE 0
							   END,
			   BaseUnits = ROUND(SUM(MSBINFO.BaseUnits),12),
			   AvgCost = ROUND(SUM(MSBINFO.AvgCost),12),
			   BaseAllowed = ROUND(SUM(MSBINFO.BaseAllowed),12),
			   BaseNetPMPM = ROUND(SUM(MSBINFO.BaseNetPMPM),12),
			   UtilizationTrend = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.UtilizationTrendProduct), SUM(MSBINFO.BaseUnits)),12),
			   BenefitChange = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.BenefitChangeProduct), SUM(MSBINFO.BaseUnits)),12),
			   PopulationChange = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.PopulationChangeProduct), SUM(MSBINFO.BaseUnits)),12),
			   OtherFactor = CASE WHEN @MSBMethod = 1 THEN
														   ROUND(dbo.fnGetSafeDivisionResultReturnOne((CAST (SUM(MSBINFO.BidUtilization) AS DECIMAL (30, 22)) - CAST (SUM(MSBINFO.UtilAdditiveAdjustment) AS DECIMAL (30, 22))) ,
																									  (ROUND(SUM(MSBINFO.BaseUnits),12)*
																										     ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.UtilizationTrendProduct), SUM(MSBINFO.BaseUnits)),12)*
																										     ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.BenefitChangeProduct), SUM(MSBINFO.BaseUnits)),12)*
																										     ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.PopulationChangeProduct), SUM(MSBINFO.BaseUnits)),12)
																									  )),15)
								  ELSE ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.OtherFactorProduct), SUM(MSBINFO.BaseUnits)),12)
							 END,
			   ProviderPaymentChange = ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.ProviderPaymentProduct), SUM(MSBINFO.BaseAllowed)),12),
			   CostTrend = CASE WHEN @MSBMethod = 1 THEN 1 --calculated after this sum, need to use calculated "OtherFactor" above
								ELSE ROUND(dbo.fnGetSafeDivisionResultReturnOne(CAST (SUM(MSBINFO.TotalProduct) AS DECIMAL (30, 22)),
			   																		 (ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.UtilizationTrendProduct), SUM(MSBINFO.BaseUnits)),12)*
			   																		  ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.BenefitChangeProduct), SUM(MSBINFO.BaseUnits)),12)*
			   																		  ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.PopulationChangeProduct), SUM(MSBINFO.BaseUnits)),12)*
			   																		  ROUND(dbo.fnGetSafeDivisionResultReturnOne(SUM(MSBINFO.OtherFactorProduct), SUM(MSBINFO.BaseUnits)),12)*
			   																		  CAST (SUM(MSBINFO.ProviderPaymentProduct) AS DECIMAL (30, 22))
																					 )),15)
						   END,
			   UtilAdditiveAdjustment = CASE WHEN SUM(MSBINFO.BaseUnits) = 0  AND SUM(MSBINFO.BaseAllowed) =0 THEN 0 
											 ELSE SUM(MSBINFO.UtilAdditiveAdjustment) 
									    END,
			   PMPMAdditiveAdjustment = CASE WHEN SUM(MSBINFO.BaseUnits) = 0  AND SUM(MSBINFO.BaseAllowed) =0 THEN 0 
											 ELSE SUM(MSBINFO.PMPMAdditiveAdjustment) 
									    END,
			   BidAllowed = SUM(BidAllowed),
			   SurplusDeficit = 0 --not allocating surplus deficit to added benefits
		  FROM @MSBInfo MSBINFO
		 GROUP BY MSBINFO.ForecastID,
			      MSBINFO.ServiceCategoryCode,
			      MSBINFO.UtilType,
			      MSBINFO.UtilAdditiveAdjustment;

	-- final output
	INSERT @Results
    SELECT CAST(pre.ForecastID AS INT),
		   CAST(pre.ServiceCategoryCode AS VARCHAR(4)),
		   CAST(pre.UtilType AS CHAR(1)),
		   BaseUnits = CAST(SUM(pre.BaseUnits) AS DECIMAL(30, 22)),
		   AvgCost = CAST(dbo.fnGetSafeDivisionResult(SUM(pre.BaseAllowed)*12000,SUM(pre.BaseUnits)) AS DECIMAL(30, 22)),
		   BaseAllowed = CAST(SUM(pre.BaseAllowed) AS DECIMAL(30, 22)),
		   BaseNetPMPM = CAST(SUM(pre.BaseNetPMPM) AS DECIMAL(30, 22)),
		   CAST(pre.UtilizationTrend AS DECIMAL(30, 22)),
		   CAST(pre.BenefitChange AS DECIMAL(30, 22)),
		   CAST(pre.PopulationChange AS DECIMAL(30, 22)),
		   CAST(pre.OtherFactor AS DECIMAL(30, 22)),
		   CAST(pre.ProviderPaymentChange AS DECIMAL(30, 22)),		
		   CostTrend /*OtherCostFactor*/ = CASE WHEN @MSBMethod = 1 THEN
																	     CAST(CASE WHEN pre.IsAddedBenCat = 0 OR (pre.IsAddedBenCat=1 AND (SUM(pre.BaseAllowed)=0 OR SUM(pre.BidAllowed)=0)) THEN pre.CostTrend
																			       ELSE dbo.fnGetSafeDivisionResultReturnOne((SUM(pre.BidAllowed)-SUM(pre.PMPMAdditiveAdjustment)), --need to verify if cost trend goes in the other cost column of BPT
																															 (pre.BaseAllowed *																			
																															  pre.UtilizationTrend *
																														      pre.BenefitChange *
																														      pre.PopulationChange *
																														      pre.OtherFactor*
																														      pre.ProviderPaymentChange)) 
																			  END AS DECIMAL(30,22))
											    ELSE CAST(pre.CostTrend AS DECIMAL(30, 22))
										   END,
		   UtilAdditiveAdjustment = CASE WHEN @MSBMethod = 1 THEN CAST(SUM(pre.UtilAdditiveAdjustment) AS DECIMAL(38, 6))
										 ELSE CAST(CASE WHEN IsAddedBenCat = 0 THEN pre.UtilAdditiveAdjustment
														ELSE pre.UtilAdditiveAdjustment - 
															(pre.BaseUnits * pre.UtilizationTrend * pre.BenefitChange * pre.PopulationChange * pre.OtherFactor)
												   END AS DECIMAL(38, 6))
									END,	
		PMPMAdditiveAdjustment = CASE WHEN @MSBMethod = 1 THEN CAST(SUM(pre.PMPMAdditiveAdjustment) AS DECIMAL(38, 6))	
									  ELSE CAST(CASE WHEN IsAddedBenCat = 0 THEN pre.PMPMAdditiveAdjustment
													 ELSE pre.PMPMAdditiveAdjustment - 
														 (pre.BaseAllowed * pre.UtilizationTrend * pre.BenefitChange * pre.PopulationChange * pre.OtherFactor *
														  pre.CostTrend * pre.ProviderPaymentChange)
												END AS DECIMAL(38, 6))
								END,
			SurplusDeficit = CAST(SUM(pre.SurplusDeficit) AS DECIMAL(30, 22))
   FROM @pre pre
  GROUP BY ForecastID,
	       ServiceCategoryCode,
	       UtilType,
	       UtilizationTrend,
	       BenefitChange,
	       PopulationChange,
	       OtherFactor,
	       ProviderPaymentChange,
	       CostTrend,
	       UtilAdditiveAdjustment,
	       PMPMAdditiveAdjustment,
	       IsAddedBenCat,
	       BaseUnits,
	       BaseAllowed
  ORDER BY ServiceCategoryCode;

RETURN
END
GO