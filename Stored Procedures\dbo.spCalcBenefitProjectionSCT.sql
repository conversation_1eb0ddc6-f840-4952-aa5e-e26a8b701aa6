SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcBenefitProjectionSCT
--
-- CREATOR:			Christian Cofie
--
-- CREATED DATE:	2007-JAN-29
-- HEADER UPDATED:	2023-JAN-24 Jake Lewis
--
-- DESCRIPTION:		Responsible for updating the CalcBenefitProjectionSCTCurrent and CalcBenefitProjectionBid.
--				
--		
-- PARAMETERS:
--  Input  :		@ForecastID
--					@UserID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			NONE
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnAppGetBidSummarySCT
--
-- STORED PROCS:	spUpdateCalcBenefitProjectionSCTCurrent
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2018-SEP-18      1           Initial Version                                                     Jordan Purdue
-- 2018-DEC-12      2			Add spUpdateCalcBenefitProjectionSCTCurrent                         Manisha Tyagi    
-- 2019-OCT-30		3			Removed 'HUMAD\' from UserID										Chhavi Sinha
-- 2023-JAN-24		4			Changes for dual pop adj factor removal								Jake Lewis
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spCalcBenefitProjectionSCT]
    (
    @ForecastID INT
   ,@UserID     CHAR(7))

AS
    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        BEGIN

            EXEC dbo.spUpdateCalcBenefitProjectionSCTCurrent @ForecastID, @UserID;

        END;

        SELECT  ForecastID
               ,SCTBaseExp
               ,SCTBaseMan
               ,SCTBaseBlend
               ,SCTCurrentExp
               ,SCTCurrentMan
               ,SCTCurrentBlend
               ,SCTProjExp
               ,SCTProjMan
               ,SCTProjBlend
        FROM    fnAppGetBidSummarySCT (@ForecastID);

    END;