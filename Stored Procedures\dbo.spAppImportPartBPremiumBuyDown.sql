
/****** Object:  StoredProcedure [dbo].[spAppImportPartBPremiumBuyDown]    Script Date: 8/27/2024 10:01:28 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================      
-- Author:  <PERSON><PERSON><PERSON> 
-- Create date: 2024-July-25
-- Description: PartBPremiumBuyDown
--      
--      
-- PARAMETERS:      
-- Input:   
    
-- TABLES:    
-- Read:      
--              
--            
-- Write:	SavedForecastSetup    
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION   CHANGES MADE											DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2024-July-25		1	     Initial version.										Latoya Garvey
----------------------------------------------------------------------------------------------------------------------
-- ======================================================================================================================

CREATE PROCEDURE [dbo].[spAppImportPartBPremiumBuyDown]
	-- Add the parameters for the stored procedure here
		@StageId VARCHAR(100)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
SET NOCOUNT ON;

		DECLARE @jsonData VARCHAR(MAX);
		DECLARE @UserId VARCHAR(7);
		DECLARE @PlanYearID SMALLINT;
        SELECT @PlanYearID = dbo.fnGetBidYear();

		SELECT @jsonData = JsonData, @UserId = UserId
		FROM dbo.ImportDataStaging WITH (NOLOCK)
		WHERE StageId = @StageId;
		

		DECLARE @tbl__importData TABLE
		(
			ForecastID INT NOT NULL,
			PartBPremiumBuydown decimal (9, 1) NULL,
			LastUpdateByID CHAR(7) NOT NULL,
			LastUpdateDateTime DATETIME NOT NULL
		);

		INSERT INTO @tbl__importData 
		SELECT
				ForecastID,
				CAST (PTBPremiumBuydown AS DECIMAL (9, 1)) AS PartBPremiumBuydown,
				@UserId,
				GETDATE()			
		FROM 
		OPENJSON(@jsonData, '$.PartBPremiumBuyDown') 
		WITH
	    (   
		    ForecastID int,
			PTBPremiumBuydown decimal (9, 1)
		);

		MERGE INTO [dbo].[SavedForecastSetup] AS target
		USING @tbl__importData AS source
		ON   
		(
			target.ForecastID = source.ForecastID
			AND target.PlanYear = @PlanYearID
		)
		WHEN MATCHED THEN 
			UPDATE	
			SET 
			    target.ForecastID = source.ForecastID,
				target.PartBPremiumBuydown = source.PartBPremiumBuydown,
				target.IsToReprice = 1, 		/*Reprice Flag Set for uploaded plans*/
				target.LastUpdateByID = source.LastUpdateByID, 
			    target.LastUpdateDateTime =   source.LastUpdateDateTime;
			




	   DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;

END
