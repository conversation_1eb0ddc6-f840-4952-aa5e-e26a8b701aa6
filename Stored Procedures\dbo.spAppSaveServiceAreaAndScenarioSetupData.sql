SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author: <PERSON><PERSON><PERSON>   
-- Create date: 05-01-2018
-- Description:  Update  Service Area
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:
-- Read:      
--				SavedForecastSetup	
--				SavedServiceAreaOption
--				SavedMarketInfo
--				LkpPlanType
--				SavedPlanInfo
--				LkpPFFSNetwork
--				LkpSNPType
--				LkpMAPlanDesign
-- Write:      
--				SavedPlanInfo
--				SavedForecastSetup
--				MAReportPlanLevel
--				Trend_ProjProcess_CalcPlanAdjmt_RepCat
--				Trend_ProjProcess_CalcPlanAdjmt_BenCat
--              SavedMATrendData
--              SavedMERActAdj
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Jan-05  1		  Initial version.											Manisha Tyagi
-- 2018-Jan-10  2         Added parameter @MessageFromBackend                       Pooja Dahiya
-- 2018-Mar-20  3         Added parameter @IsForAllScenarios						Pooja Dahiya
--2018-June-15  4         Made changes for new screen                               Kritika Singh
-- 2018-Sep-18  5		  Added TPF Sp  while renaming CPS							Deepali
-- 2018-Oct-17  6         Added  @LastUpdateByID CHAR(7)							Pooja Dahiya
-- 2018-Oct-18  7         Fixed issue related to getting correct PlanInfoID			Pooja Dahiya
-- 2018-Dec-19	8		  Updated Error message and included logging exception	    Kritika Singh
-- 2019-Jan-16	9		  Messaging Changes											Deepali
--2019-May-21  10         Added PlanName column                                      Kiran Pant 
-- 2019-Dec-16 11         Updating isToReprice on SavedForecastSetup                Brent Osantowski
--2019-Dec-20  12		 Added logic to update planName in  MAReportPlanLevel		Deepali Mittal
--2020-Oct-15  13		 Comment out calling spRenameCPS to avoid calling TPF		Craig Nielsen
--2020-Nov-19  14		 Removed IsTPFUpdatesEnabled								Sachin Yadav
--2020-Dec-28  15		 Added NOLOCK												Mahendran Chinnaiah
--2021-Jul-23  16        Update CPS for Act Adj tables to reflect changes in MAAUI  Franklin Fu	
--2024-DEC-19  17        Updating CPS from dummy to final PBP in SavedMERActAdj     Abraham Ndabian
--2025-FEB-20  18        Adding Contract Number to ensure whole CPS is picked up 
--                       correctly during Plan Copy process for SavedMERActAdj data Abraham Ndabian
-- ----------------------------------------------------------------------------------------------------------------------                
CREATE PROCEDURE [dbo].[spAppSaveServiceAreaAndScenarioSetupData]
@ForecastID           INT
,@ServiceAreaOption   VARCHAR(MAX)
,@MarketSelection     VARCHAR(30)
,@Plan                CHAR(13)
,@ScenarioNumber      TINYINT
,@ScenarioName        VARCHAR(40)
,@ScenarioDescription VARCHAR(100)
,@HPASID              CHAR(13)
,@PlanType            VARCHAR(30)
,@ProductType         VARCHAR(20)
,@PFFSNetwork         VARCHAR(30)
,@SNPType             VARCHAR(30)
,@MAPlanDesign        VARCHAR(30)
,@MessageFromBackend  NVARCHAR(500) OUTPUT
,@Result              BIT           OUT
,@IsForAllScenarios   BIT
,@LastUpdateByID      CHAR(7)
,@PlanName            VARCHAR(100)
AS
    BEGIN
        BEGIN TRY
            BEGIN TRANSACTION;
            SAVE TRANSACTION MySavePoint;

            BEGIN
                DECLARE @PlanInfoID INT;
                DECLARE @OldCPS VARCHAR(13);

                ------------------Update CPS and PlanName------------------------------------------------------------			

                IF NOT EXISTS (SELECT       1
                               FROM         dbo.SavedPlanInfo SP WITH (NOLOCK)
                              INNER JOIN    dbo.SavedForecastSetup SS WITH (NOLOCK)
                                      ON SP.PlanInfoID = SS.PlanInfoID
                               WHERE        SP.CPS = @Plan)
                    BEGIN

                        SET @PlanInfoID = (SELECT       SP.PlanInfoID
                                           FROM         dbo.SavedPlanInfo SP WITH (NOLOCK)
                                          INNER JOIN    dbo.SavedForecastSetup SS WITH (NOLOCK)
                                                  ON SP.PlanInfoID = SS.PlanInfoID
                                           WHERE        SS.ForecastID = @ForecastID);
                        SET @OldCPS = (SELECT       SP.CPS
                                           FROM         dbo.SavedPlanInfo SP WITH (NOLOCK)
                                          INNER JOIN    dbo.SavedForecastSetup SS WITH (NOLOCK)
                                                  ON SP.PlanInfoID = SS.PlanInfoID
                                           WHERE        SS.ForecastID = @ForecastID);

                        UPDATE      SP
                        SET         SP.LastCPS = SP.CPS
                                   ,SP.CPS = @Plan
                                   ,SP.LastUpdateByID = @LastUpdateByID
                                   ,SP.LastUpdateDateTime = GETDATE ()
                        FROM        dbo.SavedPlanInfo SP
                       INNER JOIN   dbo.SavedForecastSetup SS
                               ON SP.PlanInfoID = SS.PlanInfoID
                        WHERE       SS.ForecastID = @ForecastID;
                        SET @Result = 1;
                        SET @MessageFromBackend = '';


                        -- Update actuarial adjustment tables to reflect the CPS change for new in bid year plans
                        UPDATE      BC
                        SET         BC.CPS = SP.CPS
                        FROM        dbo.Trend_ProjProcess_CalcPlanAdjmt_BenCat BC
                       INNER JOIN   dbo.SavedPlanInfo SP
                               ON BC.PlanInfoID = SP.PlanInfoID;
                        SET @Result = 1;
                        SET @MessageFromBackend = '';

                        UPDATE      RC
                        SET         RC.CPS = SP.CPS
                        FROM        dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat RC
                       INNER JOIN   dbo.SavedPlanInfo SP
                               ON RC.PlanInfoID = SP.PlanInfoID;
                        SET @Result = 1;
                        SET @MessageFromBackend = '';

						-----updating SavedMERActAdj MER info for flipping dummy to PBP by Abe 12/19/2024

						UPDATE     MER
						SET  MER.ContractPBP = @Plan,
						     MER.ContractNumber = LEFT(spi.CPS,5), -- ( SPI.CPS,5)  -- added this contract number to fix issue # 171 from BPR Phase 2 by Abe
						     MER.PlanID =  RIGHT(LEFT(spi.CPS,9),3), -- ( SPI.CPS,5) 
							 MER.SegmentID = RIGHT(spi.CPS,3) -- ( SPI.CPS,5) 
						FROM dbo.SavedMERActAdj MER 
						INNER JOIN dbo.SavedForecastSetup sf
						ON sf.ForecastID = mer.ForecastID
						INNER JOIN	dbo.SavedPlanInfo spi
						ON spi.PlanInfoID= sf.PlanInfoID
						WHERE MER.ForeCastID = @ForecastID

                        UPDATE     TRD
						SET  TRD.ContractPBP = @Plan,
						     TRD.ContractNumber = LEFT(@Plan,5), -- ( SPI.CPS,5) -- added this contract number for potential issue by Abe
						     TRD.PlanID =  RIGHT(LEFT(@Plan,9),3), -- ( SPI.CPS,5) 
							 TRD.SegmentID = RIGHT(@Plan,3) -- ( SPI.CPS,5) 
						FROM dbo.SavedMATrendData TRD 
						WHERE TRD.ContractPBP = @OldCPS


                    ----------------------------Added TPF SP-----------------------------------
                    --if(@IsBidModelUpdatesEnabled=1)
                    --BEGIN
                    --BEGIN TRY
                    --EXEC spRenameCPS @LastUpdateByID,@PlanInfoID
                    --SET @Result = 1; 
                    --SET @MessageFromBackend='. And changes published to legacy Model'
                    --END TRY
                    --BEGIN CATCH
                    -- SET @Result = 0;
                    --  SET @MessageFromBackend=' Save failed during publish to legacy Model. Please try again <NAME_EMAIL>.'
                    --  ROLLBACK TRANSACTION MySavePoint; 
                    --END CATCH
                    --END	
                    --ELSE
                    --BEGIN
                    --SET @MessageFromBackend=' but not published. Reason: Publishing disabled.';
                    --END	

                    END;
                BEGIN
                    UPDATE      SS
                    SET         SS.ScenarioNbr = @ScenarioNumber
                               ,SS.ScenarioName = @ScenarioName
                               ,SS.ScenarioDescription = @ScenarioDescription
                               ,SS.HPASID = CASE WHEN @HPASID = '' THEN SS.HPASID ELSE @HPASID END
                               ,SS.IsToReprice = 1
                    FROM        dbo.SavedPlanInfo SP
                   INNER JOIN   dbo.SavedForecastSetup SS
                           ON SP.PlanInfoID = SS.PlanInfoID
                    WHERE       SS.ForecastID = @ForecastID;
                    SET @Result = 1;

                    UPDATE      SP
                    SET         SP.PlanName = @PlanName
                               ,SP.LastUpdateByID = @LastUpdateByID
                               ,SP.LastUpdateDateTime = GETDATE ()
                    FROM        dbo.SavedPlanInfo SP
                   INNER JOIN   dbo.SavedForecastSetup SS
                           ON SP.PlanInfoID = SS.PlanInfoID
                    WHERE       SS.ForecastID = @ForecastID;

                    -----Added logic from Bid Model to update MAReportPlanLevel-----------

                    DECLARE @PlanYearID INT;
                    SELECT  @PlanYearID = PlanyearID
                    FROM    SavedPlanHeader WITH (NOLOCK)
                    WHERE   ForecastID = @ForecastID;

                    UPDATE  MAReportPlanLevel
                    SET     PlanName = @PlanName
                    WHERE   ForecastID = @ForecastID
                            AND PlanYearID = @PlanYearID;

                    SET @Result = 1;
                END;

            END;


            COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
            SET @Result = 0;
            SET @MessageFromBackend = 'An error occurred while processing your request. <NAME_EMAIL>.';

            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @ErrorException NVARCHAR(4000);
            DECLARE @errSrc      VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL')
                   ,@currentdate DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :'
                                      + CAST(@ErrorState AS VARCHAR);
            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);

            ROLLBACK TRANSACTION MySavePoint;

            ---Insert into app log for logging error------------------
            EXEC spAppAddLogEntry @currentdate
                                 ,''
                                 ,'ERROR'
                                 ,@errSrc
                                 ,@ErrorMessage
                                 ,@ErrorException
                                 ,@LastUpdateByID;
        END CATCH;

    END;
GO
