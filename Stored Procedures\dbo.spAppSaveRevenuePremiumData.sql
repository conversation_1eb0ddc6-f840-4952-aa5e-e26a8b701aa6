SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author:  Anand <PERSON>      
-- Create date: 07/07/2020      
-- Description:       
--            
--            
-- PARAMETERS:            
-- Input:              
          
-- TABLES:            
-- Read:            
-- Write:            
-- VIEWS:            
--            
-- FUNCTIONS:            
--              
-- STORED PROCS:             
           
      
-- $HISTORY      
      
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE   VERSION   CHANGES MADE                                              DEVELOPER              
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Jul-07  1    Initial version.											Anand Debadwar      
-- 2020-Oct-28 2 Added @IsToRepriceinTable										Deepali  
-- 2020-Nov-2   3   Added @RxBasicPremium										Florence  
-- 2020-Nov-11  4   Added @RxSuppPremium										Florence  
-- 2020-Nov-13  5   Changed @AltRebateOrder	to TinyINT							Anand A
-- 2020-Nov-13  5   Reading AltRebateOrder from table lkpRebateOrderID			Anand A
-- 2020-Dec-28  6   Merging IT optimization code with business lkp code         Brent Osantowski
-- 2021-Jan-05	7	Added WITH Nolock											Satyam Singhal
-- ----------------------------------------------------------------------------------------------------------------------      
               
CREATE PROCEDURE [dbo].[spAppSaveRevenuePremiumData]
  @ForecastID INT,      
  @AltRebateOrder nvarchar(50),      
  @PartBPremiumBuydown decimal(9,1),      
  @SecondaryPayerAdjustment decimal(7,6),      
  @PlanIntentionValue nvarchar(50),   
  @RxBasicPremium  decimal(9,2),   
  @RxSuppPremium  decimal(9,2),   
  @LastUpdateByID varchar(7),      
  @MessageFromBackend nvarchar(500) OUTPUT,      
  @Result BIT OUT    
AS      
    BEGIN      
              
    BEGIN TRY      
    BEGIN TRANSACTION;  
  DECLARE @ForecastIDList int = @ForecastID;  
  DECLARE @IsToReprice bit, @AltRebateOrderBit TINYINT, @PlanIntentionId NVARCHAR(MAX),   
  @OldRxBasicPremium DECIMAL(9,2), @OldRxSuppPremium DECIMAL(9,2),@OldPartBPremiumBuydown decimal(9,1)  
  SET @OldPartBPremiumBuydown= (SELECT PartBPremiumBuydown FROM dbo.SavedForecastSetup WITH(NOLOCK) WHERE ForecastID=@ForecastID)  
  SET @PlanIntentionId= (SELECT lpi.PlanIntentionMessage FROM dbo.SavedForecastSetup sfs WITH(NOLOCK)  INNER JOIN dbo.LkpPlanIntention lpi WITH(NOLOCK) ON sfs.PlanIntentionID=lpi.PlanIntentionID WHERE ForecastID=@ForecastID)  
  SET @OldRxBasicPremium=(SELECT isnull(RxBasicPremium,0) FROM dbo.SavedPlanAssumptions WITH(NOLOCK) WHERE ForecastID=@ForecastID)  
  SET @OldRxSuppPremium=(SELECT RxSuppPremium FROM dbo.SavedPlanAssumptions WITH(NOLOCK) WHERE ForecastID=@ForecastID)  
  SELECT @IsToReprice = CASE WHEN SecondaryPayerAdjustment<>@SecondaryPayerAdjustment THEN 1 ELSE 0 END FROM SavedPlanAssumptions WITH(NOLOCK) WHERE ForecastID = @ForecastID      
    DECLARE @IsToRepriceinTable bit  
 SELECT @IsToRepriceinTable = (select IsToReprice from SavedForecastSetup WITH(NOLOCK) where ForecastID=@ForecastID)  
 SET @AltRebateOrderBit = (SELECT RebateOrderID FROM dbo.lkpRebateOrderID WITH(NOLOCK)  WHERE RebateOrderDescription=@AltRebateOrder)  
  
   UPDATE SavedForecastSetup   
   SET LastUpdateByID=@LastUpdateByID,      
    LastUpdateDateTime=getdate(),      
    AltRebateOrder= @AltRebateOrderBit  ,    
    IsToReprice= CASE WHEN (AltRebateOrder<>@AltRebateOrderBit OR PartBPremiumBuydown<>@PartBPremiumBuydown OR @IsToReprice=1 OR @IsToRepriceinTable=1) THEN 1 ELSE 0 END,      
    PartBPremiumBuydown=@PartBPremiumBuydown,      
    PlanIntentionID=  (SELECT PlanIntentionID FROM dbo.LkpPlanIntention WITH(NOLOCK) WHERE PlanIntentionMessage = @PlanIntentionValue)
  
   WHERE ForecastID=@ForecastID      
   
    UPDATE dbo.SavedPlanAssumptions       
    SET SecondaryPayerAdjustment=@SecondaryPayerAdjustment      
    WHERE ForecastID = @ForecastID   
   
    IF(@OldRxBasicPremium<> @RxBasicPremium)  
    BEGIN  
    UPDATE dbo.SavedPlanAssumptions  
    SET RxBasicPremium=@RxBasicPremium  
    WHERE ForecastID=@ForecastID  
    END  
  
    IF(@OldRxBasicPremium <> @RxBasicPremium OR @IsToReprice<>1 OR @PlanIntentionId<>@PlanIntentionValue OR @PartBPremiumBuydown<>@OldPartBPremiumBuydown)  
    BEGIN  
    SELECT 1  
   EXEC dbo.spFlagForReprice @ForecastIDList, @LastUpdateByID    
    END    
     
  
   IF(@OldRxSuppPremium<> @RxSuppPremium)  
    BEGIN  
    UPDATE dbo.SavedPlanAssumptions  
    SET RxSuppPremium=@RxSuppPremium  
    WHERE ForecastID=@ForecastID  
    END  
  
    IF(@OldRxSuppPremium <> @RxSuppPremium OR @IsToReprice<>1 OR @PlanIntentionId<>@PlanIntentionValue OR @PartBPremiumBuydown<>@OldPartBPremiumBuydown)  
    BEGIN  
    SELECT 1  
   EXEC dbo.spFlagForReprice @ForecastIDList, @LastUpdateByID     
    END    
  
  SET @Result=1      
  SET @MessageFromBackend='';      
      
       COMMIT TRANSACTION;      
        END TRY      
        BEGIN CATCH     
   ROLLBACK TRANSACTION;    
   SET @Result = 0;      
   SET @MessageFromBackend='Scenario save failed during Revenue/Premium save. Please try again <NAME_EMAIL>.' ;      
   DECLARE @ErrorMessage NVARCHAR(4000);        
   DECLARE @ErrorSeverity INT;        
   DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000);       
   DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()      
      
   SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState
  
    
 as varchar)      
   RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)      
         
       
      
   ---Insert into app log for logging error------------------      
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID      
                  
        END CATCH;       
END; 
GO
