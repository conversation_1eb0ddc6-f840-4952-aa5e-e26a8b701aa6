SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault
--
-- CREATOR: <PERSON> Lewis
--
-- CREATED DATE: FEB-27-2020
--
-- DESCRIPTION:   Trends from Trend_ProjProcess_CalcNotPlanLevel_Trends are mapped to plans for the projected process.  
--              
--              
-- PARAMETERS:
--  Input  :	@PlanList
--				@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_ProjProcess_CalcNotPlanLevel_Trends
--					Trend_SavedComponentInfo
--					LkpIntPlanYear
--					
--          Write:  Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS: Read: Trend_fnCalcStringToTable
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FEB-27-2020      1           Initial Version                                                     Jake Lewis
-- NOV-19-2020      2          Include NOLOCK & ROWLOCK                                             Manisha Tyagi
-- DEC-06-2020      3          Batch delete and Temp insert implemented                             Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault]
@PlanList        VARCHAR(2000) = NULL
,@LastUpdateByID CHAR(13)

AS
    BEGIN
        SET NOCOUNT ON;		 
        -- Declare variables
        DECLARE @CurrentYear INT;
        SET @CurrentYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);

        DECLARE @BidYear INT;
        SET @BidYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);

        DECLARE @ExperienceYear INT;
        SET @ExperienceYear = (SELECT   PlanYearID FROM dbo.LkpIntPlanYear WHERE IsExperienceYear = 1);

        DECLARE @PackageOptionID INT;
        SET @PackageOptionID = (SELECT  DISTINCT TOP(1)
                                        PackageOptionID
                                FROM    dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends WITH (NOLOCK) ORDER BY PackageOptionID);


        -- Set up PlanTypeGranularity hierarchy
        IF (SELECT  OBJECT_ID ('tempdb..#GranularityOrder')) IS NOT NULL
            DROP TABLE #GranularityOrder;
        CREATE TABLE #GranularityOrder
            (GranOrder           INT
            ,PlanTypeGranularity VARCHAR(500));
        INSERT INTO #GranularityOrder
            (GranOrder
            ,PlanTypeGranularity)
        VALUES (1, 'ActuarialRegion,ProductType')
              ,(2, 'IsNationwide,SNPType,ProductType')
              ,(3, 'ActuarialRegion')
              ,(4, 'IsNationwide,SNPType')
              ,(5, 'IsNationwide,ProductType')
              ,(6, 'IsNationwide');


        -- Get PlanInfoIDs, CPS codes, PlanYearIDs, BaseYearIDs from vwPlanInfo
        -- Only specified plans, or all plans if @PlanList is null
        IF (SELECT  OBJECT_ID ('tempdb..#PlanInfo')) IS NOT NULL
            DROP TABLE #PlanInfo;
        SELECT  DISTINCT
                PlanInfoID
               ,CPS
               ,PlanYear AS 'PlanYearID'
               ,@ExperienceYear AS 'BaseYearID'
        INTO    #PlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear
                AND IsOffMAModel = 'No'
                AND IsHidden = 0
                AND Region NOT IN ('Unmapped')
                AND (PlanInfoID IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@PlanList, ',', 1) )
                     OR @PlanList IS NULL);


        -- Get Components 
        IF (SELECT  OBJECT_ID ('tempdb..#Component')) IS NOT NULL
            DROP TABLE #Component;
        SELECT  Component
        INTO    #Component
        FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
        WHERE   IsPartOfPackage = 1


        -- Create shell
        IF (SELECT  OBJECT_ID ('tempdb..#TempShell')) IS NOT NULL
            DROP TABLE #TempShell;
        SELECT      DISTINCT
                    #PlanInfo.PlanInfoID
                   ,#PlanInfo.CPS
                   ,#PlanInfo.PlanYearID
                   ,#PlanInfo.BaseYearID
                   ,@PackageOptionID AS 'PackageOptionID'
                   ,tpc.ComponentVersionID
                   ,#Component.Component
        INTO        #TempShell
        FROM        #PlanInfo 
       INNER JOIN   #Component
               ON 1 = 1
        LEFT JOIN   dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends tpc WITH (NOLOCK)
               ON tpc.Component = #Component.Component;


        -- Create PlanTypeGranularity lookups for each PlanInfoID
        IF (SELECT  OBJECT_ID ('tempdb..#PlanTypeGranularityLookups')) IS NOT NULL
            DROP TABLE #PlanTypeGranularityLookups;
        SELECT      DISTINCT
                    dbo.vwPlanInfo.PlanInfoID
                   ,CONCAT (#TempShell.ComponentVersionID, ',', Region, ',', Product) AS 'ActuarialRegion,ProductType'
                   ,CONCAT (#TempShell.ComponentVersionID, ',', 1, ',', SNPType, ',', Product) AS 'IsNationwide,SNPType,ProductType'
                   ,CONCAT (#TempShell.ComponentVersionID, ',', Region) AS 'ActuarialRegion'
                   ,CONCAT (#TempShell.ComponentVersionID, ',', 1, ',', SNPType) AS 'IsNationwide,SNPType'
                   ,CONCAT (#TempShell.ComponentVersionID, ',', 1, ',', Product) AS 'IsNationwide,ProductType'
                   ,CONCAT (#TempShell.ComponentVersionID, ',', 1) AS 'IsNationwide'
        INTO        #PlanTypeGranularityLookups
        FROM        dbo.vwPlanInfo WITH (NOLOCK)
       INNER JOIN   #TempShell
               ON #TempShell.PlanInfoID = dbo.vwPlanInfo.PlanInfoID;


        -- Source data from Trend_ProjProcess_CalcNotPlanLevel_Trends
        IF (SELECT  OBJECT_ID ('tempdb..#sourceData')) IS NOT NULL
            DROP TABLE #sourceData;
        SELECT  DISTINCT
                CONCAT (ComponentVersionID, ',', PlanTypeGranularityValue) AS 'Lookup'
               ,ComponentVersionID
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
        INTO    #sourceData
        FROM    dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends WITH (NOLOCK);


        -- For each PlanInfoID / ComponentVersionID set, select only the PlanTypeGranularities and PlanTypeGranularityValues that exist in the source data. 
        -- Multiple granularities and values may exist for each PlanInfoID / ComponentVersionID set.  
        IF (SELECT  OBJECT_ID ('tempdb..#GranularityAndValueOptions')) IS NOT NULL
            DROP TABLE #GranularityAndValueOptions;
        SELECT      PlanInfoID
                   ,ComponentVersionID
                   ,PlanTypeGranularity
                   ,PlanTypeGranularityValue
        INTO    #GranularityAndValueOptions
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [ActuarialRegion,ProductType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,ComponentVersionID
                   ,PlanTypeGranularity
                   ,PlanTypeGranularityValue
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [IsNationwide,SNPType,ProductType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,ComponentVersionID
                   ,PlanTypeGranularity
                   ,PlanTypeGranularityValue
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON ActuarialRegion = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,ComponentVersionID
                   ,PlanTypeGranularity
                   ,PlanTypeGranularityValue
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [IsNationwide,SNPType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,ComponentVersionID
                   ,PlanTypeGranularity
                   ,PlanTypeGranularityValue
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [IsNationwide,ProductType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,ComponentVersionID
                   ,PlanTypeGranularity
                   ,PlanTypeGranularityValue
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON IsNationwide = [Lookup]
        WHERE       [Lookup] IS NOT NULL;


        -- Add all selectable PlanTypeGranularites / PlanTypeGranularityValues to the shell. 
        -- Include Granularity Order, which allows the selection of the proper PlanTypeGranularity option.
        IF (SELECT  OBJECT_ID ('tempdb..#ShellWithAllOptions')) IS NOT NULL
            DROP TABLE #ShellWithAllOptions;
        SELECT      #TempShell.PlanInfoID
                   ,CPS
                   ,PlanYearID
                   ,BaseYearID
                   ,PackageOptionID
                   ,#TempShell.ComponentVersionID
                   ,Component
                   ,#GranularityAndValueOptions.PlanTypeGranularity
                   ,PlanTypeGranularityValue
                   ,GranOrder
        INTO        #ShellWithAllOptions
        FROM        #TempShell
        JOIN        #GranularityAndValueOptions
          ON CONCAT (#TempShell.PlanInfoID, ',', #TempShell.ComponentVersionID) = CONCAT (
                                                                                  #GranularityAndValueOptions.PlanInfoID
                                                                                 ,','
                                                                                 ,#GranularityAndValueOptions.ComponentVersionID)
        LEFT JOIN   #GranularityOrder
               ON #GranularityOrder.PlanTypeGranularity = #GranularityAndValueOptions.PlanTypeGranularity;


        -- For each PlanInfoID / ComponentVersionID set, select the appropriate Granularity Order, which allows for the later selection of the appropriate PlanTypeGranularity / PlanTypeGranularityValue. 
        IF (SELECT  OBJECT_ID ('tempdb..#GranularitySelection')) IS NOT NULL
            DROP TABLE #GranularitySelection;
        SELECT      #TempShell.PlanInfoID
                   ,#TempShell.CPS
                   ,#TempShell.PlanYearID
                   ,#TempShell.BaseYearID
                   ,#TempShell.PackageOptionID
                   ,#TempShell.ComponentVersionID
                   ,#TempShell.Component
                   ,MIN (COALESCE(#ShellWithAllOptions.GranOrder,999)) AS 'GranOrder'
        INTO        #GranularitySelection
        FROM        #TempShell
        LEFT JOIN   #ShellWithAllOptions
               ON #ShellWithAllOptions.PlanInfoID = #TempShell.PlanInfoID
                  AND   #ShellWithAllOptions.CPS = #TempShell.CPS
                  AND   #ShellWithAllOptions.PlanYearID = #TempShell.PlanYearID
                  AND   #ShellWithAllOptions.BaseYearID = #TempShell.BaseYearID
                  AND   #ShellWithAllOptions.PackageOptionID = #TempShell.PackageOptionID
                  AND   #ShellWithAllOptions.ComponentVersionID = #TempShell.ComponentVersionID
                  AND   #ShellWithAllOptions.Component = #TempShell.Component
        LEFT JOIN   #GranularityOrder
               ON #GranularityOrder.GranOrder = #ShellWithAllOptions.GranOrder
        GROUP BY    #TempShell.PlanInfoID
                   ,#TempShell.CPS
                   ,#TempShell.PlanYearID
                   ,#TempShell.BaseYearID
                   ,#TempShell.PackageOptionID
                   ,#TempShell.ComponentVersionID
                   ,#TempShell.Component;


        -- Delete from and insert into final output table
        -- Only for specified plans, or all plans if @PlanList is null
        DELETE  FROM dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault
        WHERE   (PlanInfoID IN (SELECT PlanInfoID FROM #PlanInfo));

		IF (SELECT  OBJECT_ID ('tempdb..#TempPlanMappingDefault')) IS NOT NULL
			DROP TABLE #TempPlanMappingDefault;
		
		CREATE TABLE #TempPlanMappingDefault
		([PlanInfoID] [int] ,
		[CPS] [char](13) ,
		[PlanYearID] [int] ,
		[BaseYearID] [int] ,
		[PackageOptionID] [int] ,
		[ComponentVersionID] [int] ,
		[Component] [varchar](50) ,
		[PlanTypeGranularity] [varchar](250) ,
		[PlanTypeGranularityValue] [varchar](500) ,
		[LastUpdateByID] [char](13) ,
		[LastUpdateDateTime] [datetime] )

        --INSERT INTO dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault WITH(ROWLOCK)
		INSERT INTO #TempPlanMappingDefault
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,PackageOptionID
            ,ComponentVersionID
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT      #GranularitySelection.PlanInfoID
                   ,#GranularitySelection.CPS
                   ,#GranularitySelection.PlanYearID
                   ,#GranularitySelection.BaseYearID
                   ,#GranularitySelection.PackageOptionID
                   ,#GranularitySelection.ComponentVersionID
                   ,#GranularitySelection.Component
                   ,#GranularityOrder.PlanTypeGranularity
                   ,#GranularityAndValueOptions.PlanTypeGranularityValue
                   ,@LastUpdateByID
                   ,GETDATE ()
        FROM        #GranularitySelection
        LEFT JOIN   #GranularityOrder
               ON #GranularityOrder.GranOrder = #GranularitySelection.GranOrder
        LEFT JOIN   #GranularityAndValueOptions
               ON #GranularityAndValueOptions.PlanInfoID = #GranularitySelection.PlanInfoID
                  AND   #GranularityAndValueOptions.ComponentVersionID = #GranularitySelection.ComponentVersionID
                  AND   #GranularityAndValueOptions.PlanTypeGranularity = #GranularityOrder.PlanTypeGranularity;

		--Finally inserting into main table from temp table
		insert into dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault select * from #TempPlanMappingDefault;
		--End
    END;
GO
