SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------        
-- FUNCTION NAME: spClearOverrides       
--        
-- AUTHOR: <PERSON><PERSON><PERSON> Singh       
--        
-- CREATED DATE: 2015-11-10       
--        
-- DESCRIPTION: Procedure responsible for running the first step in ModelTurnOver(Run by package)       
--        
-- PARAMETERS: NONE             
--        
-- TABLES:         
-- Read:        

-- Write:        
--		SavedCUOverrideAdj
--		SavedPlanBidNoteDetail
--		SavedDFFinance
--		SavedDFClaims
--		SavedPlanBPTExceptionHeader
--		SavedPlanRiskScoreOverride
--		SQSFactorsOverride
--		SavedPlanProductPairing
--		Saved<PERSON><PERSON>fitLevelClaimFactors
--		Trend_ProjProcess_CalcPlanAdjmt_BenCat
--		Trend_ProjProcess_CalcPlanAdjmt_RepCat
--
-- VIEWS:        
--        
-- FUNCTIONS: 
--		fnGetBidYear
--        
-- STORED PROCS:        
--        
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION      CHANGES MADE														DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2015-11-10		1           Initial Version														Kritika Singh  
-- 2016-Sep-09		2			Remove Saved*CUOverrideMSBForecast and Saved*CUOverrideMSBTrend		Pragya Mishra 
-- 2021-Jan-15		3			Added line for SavedPlanProductPairing								Jennifer Chapman
-- 2022-Dec-19      4           Replacing Delete Script with Truncate for optimization              Abraham Ndabian
-- 2023-Jan-26      5           Truncate SavedBenefitLevelClaimFactors table during Model Turn      Abraham Ndabian
-- 2023-Sep-18      6           Adding the following trend tables:dbo.Trend_ProjProcess_CalcPlanAdjmt_BenCat  
--                              and  dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat                      Abraham Ndabian
-- 2024-OCT-24		7		    Cost Share Basis: removed references to SavedCUOverrideAdminRev, 
--								SavedCUOverrideDEPound, and SavedCUOverrideMSB						Franklin Fu
-------------------------------------------------------------------------------------------------------------------------    

CREATE PROCEDURE [dbo].[spClearOverrides]
AS
BEGIN
SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;
TRUNCATE TABLE dbo.SavedCUOverrideAdj
TRUNCATE TABLE dbo.SavedPlanBidNoteDetail  
DELETE FROM dbo.SavedDFFinance where IncurredYear =  (SELECT dbo.fnGetBidYear () - 4)
DELETE FROM dbo.SavedDFClaims where IncurredYear =  (SELECT dbo.fnGetBidYear () - 4)
TRUNCATE TABLE dbo.SavedPlanBPTExceptionHeader  
TRUNCATE TABLE dbo.SavedPlanRiskScoreOverride
TRUNCATE TABLE dbo.SQSFactorsOverride
TRUNCATE TABLE dbo.SavedPlanProductPairing
TRUNCATE TABLE dbo.SavedBenefitLevelClaimFactors
TRUNCATE TABLE dbo.Trend_ProjProcess_CalcPlanAdjmt_BenCat -- two trend tables below
TRUNCATE TABLE dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat 


END
GO
