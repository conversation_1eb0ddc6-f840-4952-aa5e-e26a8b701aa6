SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================            
-- Author:  Satyam <PERSON>al           
-- Create date: 03 March 2020      
-- Description: save changes for Trend Package       
--            
--            
-- PARAMETERS:            
-- Input:              
      
-- TABLES:          
-- Read:           
-- Write:            
-- VIEWS:            
--            
-- FUNCTIONS:            
--              
-- STORED PROCS:             
      
      
-- $HISTORY               
      
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE        VERSION   CHANGES MADE                                                DEVELOPER              
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Mar-03  1        Initial version.         Satyam Singhal      
--2020- May-06	2 	Changed length for PackageOptionName						Deepali Mittal
--2020-June-05	3 	Removed Humad from LastUpdatedID      						Kiran Pant
--2022-Dec-21	3 	Added NEWID() column      									Sheetal Patil     
------------------------------------------------------------------------------------------------------------------------      
      
CREATE PROCEDURE [dbo].[spAppTrendSaveNewPackageData]      
    @PackageOptionName VARCHAR(100),      
    @LastUpdateByID CHAR(13),      
    @MessageFromBackend NVARCHAR(MAX) OUTPUT,      
    @Result BIT OUT      
AS      
BEGIN      
    BEGIN TRANSACTION;      
    BEGIN TRY      
        DECLARE @Component VARCHAR(50),      
                @ProjectionName VARCHAR(50),      
                @PackageOptionID INT,            
    @NewPackageOptionName VARCHAR(100);      
        SET @ProjectionName =      
        (      
      
            SELECT DISTINCT      
            ProjectionName      
   FROM dbo.LkpProjectionVersion      
            WHERE IsLiveProjection = 1      
        );      
        IF EXISTS (SELECT TOP 1 PackageOptionID FROM dbo.Trend_SavedPackageOption)      
        BEGIN      
            SET @PackageOptionID =      
            (      
                SELECT MAX(PackageOptionID) + 1 FROM dbo.Trend_SavedPackageOption      
            );      
        END;      
        ELSE      
        BEGIN      
            SET @PackageOptionID = 1;      
      
        END;           
  SET @NewPackageOptionName = LTRIM(RTRIM(@PackageOptionName));      
        CREATE TABLE #tmp      
        (      
            Component VARCHAR(50)      
        );      
      
        INSERT INTO #tmp      
        SELECT DISTINCT      
               Component      
        FROM dbo.Trend_SavedComponentInfo      
        WHERE IsPartOfPackage = 1;      
      
        DECLARE @isrun BIT;      
        SET @isrun =      
        (      
            SELECT COUNT(*)      
            FROM dbo.Trend_SavedPackageOption      
            WHERE PackageOptionName = @NewPackageOptionName and ProjectionName = @ProjectionName   
        );      
      
      
        
        BEGIN      
            IF (@isrun < 1)      
            BEGIN      
                INSERT INTO dbo.Trend_SavedPackageOption 
				(
				PackageOptionID
				,PackageOptionName
				,ProjectionName
				,Component
				,ComponentVersionID
				,IsLivePackage
				,LastUpdateByID
				,LastUpdateDateTime
				,ID)
                SELECT @PackageOptionID,      
                       @NewPackageOptionName,      
                       @ProjectionName,      
                       info.Component,      
                       NULL,      
                       0,      
                       @LastUpdateByID,      
                       GETDATE(),
					   NEWID()
                FROM #tmp info;      
                SET @MessageFromBackend = 'Your Package Option was successfully created';      
                SET @Result = 1;      
            END;      
            ELSE      
            BEGIN      
                SET @MessageFromBackend = 'Package Option Name already exists for selected projection';      
                SET @Result = 0;      
            END;      
        END;      
        COMMIT TRANSACTION;      
    END TRY      
    BEGIN CATCH      
      
        --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'      
        DECLARE @ErrorMessage NVARCHAR(4000);      
        DECLARE @ErrorSeverity INT;      
        DECLARE @ErrorState INT;      
        DECLARE @ErrorException NVARCHAR(4000);      
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),      
                @currentdate DATETIME = GETDATE();      
      
        SELECT @ErrorMessage = ERROR_MESSAGE(),      
               @ErrorSeverity = ERROR_SEVERITY(),      
               @ErrorState = ERROR_STATE(),      
               @ErrorException      
             = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'      
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);      
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);      
      
        ROLLBACK TRANSACTION;      
      
        ---Insert into app log for logging error------------------      
        EXEC spAppAddLogEntry @currentdate,      
                              '',      
                              'ERROR',      
                              @errSrc,      
                              @ErrorMessage,      
                              @ErrorException,      
                              @LastUpdateByID;      
      
    END CATCH;      
END;      
GO
