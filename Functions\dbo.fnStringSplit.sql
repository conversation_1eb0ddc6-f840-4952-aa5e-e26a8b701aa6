SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO





-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnStringSplit
--
-- CREATOR: ?
--
-- CREATED DATE: 2008-Jan-4
--HEADER UPDATED: 2010-Sep-22
--
-- DESCRIPTION: This function is used for splitting an string array of values into individual values
--
-- PARAMETERS:
--	Input:
--		@List varchar(MAX) 
--			Array, must be of type string for example '1, 2, 3, 4'
--		@SplitOn varchar(5)
--			The delimiter.  Comma (,)  in the above example
--  Output:
--      @RtnValue table
--          Table of VarChar(5) instead of a string        
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
--
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2013-Aug-30		1			Initial Version							                            Ramkumar
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnStringSplit]
(
    @List varchar(MAX),
    @SplitOn varchar(5)
)  
RETURNS @RtnValue table 
( 		 
    Id int identity(1,1),
    Value varchar(100)
) 
AS  
BEGIN 
    While (Charindex(@SplitOn,@List)>0)
    Begin
         Insert Into @RtnValue (value)
         Select 
            Value = Ltrim(Rtrim(Substring(@List,1, Charindex(@SplitOn, @List)-1)))

         Set @List = Substring(@List, Charindex(@SplitOn, @List) + len(@SplitOn), len(@List))
    End
         Insert Into @RtnValue (Value)
         Select Value = Ltrim(Rtrim(@List))
    Return
END

GO
