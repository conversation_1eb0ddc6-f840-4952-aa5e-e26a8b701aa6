-- =============================================      
-- Author: Vikrant <PERSON>    
-- Create date: 2024-Mar-04
-- Description:  Add 
--      
--      
-- PARAMETERS: @UserId, @ImportTypeId, @ImportRecordCount, @SuccessfulCount, @FailedCount, @ImportStatus     
-- Input:        

-- TABLES:      
-- Read:    
--           
-- Write: ImportSummaryLogs 
--             
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2024-Mar-04    1		  Initial version.											Vikrant Bagal
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE dbo.spAppLogImportSummary
    @UserId VARCHAR(100),
    @ImportName AS varchar(200),
    @ImportRecordCount AS int,
    @SuccessfulCount AS int,
    @FailedCount AS int,
    @ImportStatus AS bit
AS
BEGIN
    INSERT INTO dbo.ImportSummaryLogs
    (
        UserId,
        ImportName,
        ImportRecordCount,
        SuccessfulCount,
        FailedCount,
        ImportStatus
    )
    VALUES
    (@UserId, @ImportName, @ImportRecordCount, @SuccessfulCount, @FailedCount, @ImportStatus);
END;
GO