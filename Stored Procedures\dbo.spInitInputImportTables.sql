SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
-- STORED PROCEDURE NAME: spInitInputImportTables
--
-- AUTHOR: Vikrant <PERSON>
--
-- CREATED DATE: 2024-Nov-04
--
-- DESCRIPTION: Procedure is responsible prepopulating data in Import import tables
--
-- PARAMETERS:
-- Input:
--
-- Output:
-- TABLES:
-- Read:
-- Write:
--   ImportTypes
--   ImportTypeFields
--   ImportValidationTypes
--   ImportFieldValidationRules
--
-- VIEWS:
--
-- FUNCTIONS:
--
--
-- STORED PROCS:
--
-- $HISTORY
-- -------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE																				DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-Nov-04      1           Initial Version																				Vikrant Bagal
-- 2024-Nov-05      2           Script added for Other SNP Adjustment import												Kumar Jalendran
-- 2024-NOV-07		3		    Updated script for Admin upload changes                             						Vikrant Bagal
-- 2024-NOV-11      4           Updated script for Benefits Import Day Range validations                                    Alex Brandt
-- 2024-NOV-13      5           Added scripts for HOA VBid Adjustment in Data Owner import                                  Aniketh Badakala
-- 2024-NOV-20		6		    Added script for Special Crosswalk Membership import                   						Vikrant Bagal
-- 2024-NOV-25      7           Added rule for OONBenefitDayRange limits and missing CS inputs                              Alex Brandt
-- 2024-NOV-26      8           Added warning rule setup for WtdRelativity field                                            Kumar Jalendran
-- 2024-NOV-26      9           Updated rules scripts for HOA VBid Adjustment in Data Owner import                          Aniketh Badakala
-- 2024-NOV-28      10          Updated script for SCTDampenBaseThreshold                                                   Aniketh Badakala
-- 2024-Dec-06		11			Medicare Secondary Payer addition	                                                        Ramaraj Kumar
-- 2024-DEC-12      12          Added script for DentalOOPC in Data Owner import                                            Aniketh Badakala
-- 2024-DEC-19      13          Updated field name from CPS to PlanId for DentalOOPC in Data Owner import                   Aniketh Badakala
-- 2024-DEC-20      14          Added script for Projected Medicaid Revenue Import functionality                            Archana Sahu
-- 2025-JAN-07      15          Ratebook import/export new sheet and validations                                            Ramaraj Kumar
-- 2025-JAN-08		16			Added script for MSB Selection Import functionality											Archana Sahu
-- 2025-FEB-20		17			Add PD Benefit String upload validations                                                    Alex Brandt
-- 2025-FEB-28      18          Added OSB upload Validations                                                                Kiran Kola
-- 2025-Mar-03      19          Added MACTAPT Validations                                                                   Chaitanya Durga K
-- 2025-MAR-07      20          RxPremium addition                                                                          Ramaraj Kumar
-- 2025-MAR-24	    21		    Added BARC MM & RS validation																Archana Sahu
-- 2025-Mar-31      22          Ratebook validation [CountyCode] format as 000								                Vikrant Bagal
-- 2025-APR-03      23          Updated Benefits and MOOP/Deductible import to expect decimal values for INCopay and        Alex Brandt
--                                  OONCopay rather than int values
-- 2025-APR-07      24          Updated Rule #530 to set the message to be a warning for MSBs in the Dental OOPC Import     Alex Brandt
-- 2025-APR-29      25          Added validation to MAAUI Benefits import to verify that each plan has all A/B benefits     Alex Brandt
-- 2025-May-07      26          Updated to remove HOAVBidAdjustment import reference			                            Kumar Jalendran

----------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spInitInputImportTables]
AS
BEGIN

    BEGIN TRANSACTION t1;

    BEGIN TRY


        DELETE FROM dbo.ImportFieldValidationRules
        WHERE 1 = 1;
        DELETE FROM dbo.ImportTypeFields
        WHERE 1 = 1;
        DELETE FROM dbo.ImportTypes
        WHERE 1 = 1;
        DELETE FROM dbo.ImportValidationTypes
        WHERE 1 = 1;


        INSERT INTO dbo.ImportValidationTypes ([ImportValidationTypeId], [ValidationType])
        VALUES
        ( 1, 'Range' ),
        ( 2, 'RowCount' ),
        ( 3, 'CountDistinct' ),
        ( 4, 'TableLookup' ),
        ( 5, 'LessThan' ),
        ( 6, 'GreaterThan' ),
        ( 7, 'ValueLookUp' ),
        ( 8, 'RegEx' ),
        ( 9, 'Custom' ),
        (10, 'CostShareTypePerProductType' ),
        (11, 'Expression' ),
        (12, 'UniqueValue'),
        (13, 'BenefitCategoryNumComponentPlan'),
        (14, 'DeductTypeLimit'),
        (15, 'MOOPLimit'),
        (16, 'DeductTypeDescPlan'),
        (17, 'HMOBlanks'),
        (18, 'CostShareBlank'),
        (19, 'CostShareLimits'),
        (20, 'BenefitCategoryMatch'),
        (21, 'ForecastIdComparison'),
        (22, 'PhysicianFacilityBenefitsCheck'),
        (23, 'SctFindUniqueColumns'),
        (24, 'BenefitOptionsIsLiveIndex'),
        (25, 'BenefitOptionsPlanInfo'),
        (26, 'ValidForecastId'),
        (27, 'DayRangeBenefits'),
        (28, 'CostFactor'),
        (29, 'DistributionPercent'),
        (30, 'NoBlankCostShareInput'),
        (31, 'SheetDataMatch');


        INSERT INTO dbo.ImportTypes ([ImportTypeId], [ImportTypeName], [ImportSheetName])
        VALUES
        ( 1, 'LkpIntCostShareDampening', 'LkpIntCostShareDampening' ),
        ( 2, 'PerIntBenefitCategoryUtilization', 'PerIntBenefitCategoryUtilizatio' ),
        ( 3, 'LkpExtCMSPlanTypeValues', 'LkpExtCMSPlanTypeValues' ),
        ( 4, 'ProngTestsandMOOPLimits', 'LkpExtCMSMOOPLimits' ),
        ( 5, 'ProngTestsandMOOPLimits', 'PerExtProng1Factor' ),
        ( 6, 'ProngTestsandMOOPLimits', 'LkpIntCostSharingStandards' ),
        ( 7, 'PlanSummaryDetails', 'PlanSummaryDetails' ),
        ( 8, 'RatebookUploadAll', 'Ratebook Name' ),
        ( 9, 'RatebookUploadAll', 'Ratebook' ),
        ( 10, 'RatebookUploadAll', 'RPPO Ratebook' ),
        ( 11, 'RatebookUploadAll', 'Quality Stars' ),
        ( 12, 'RatebookUploadAll', 'Rebate by Contract' ),
        ( 13, 'OSBBaseExperience', 'OSB Experience' ),
        ( 14, 'AdUpload', 'AdminUpload' ),
        ( 15, 'MOOPandCPD', 'CalcCPDAllowedPMPM'),
        ( 16, 'MOOPandCPD', 'CalcMOOPInitialContinuance'),
        (17, 'GenChar', 'SavedPlanHeader'),
        (18, 'PDAuditExhibitUpload', 'Audit Exhibit'),
        (19, 'CostAndUseOverridesUpload', 'Cost And Use Override'),
        (20, 'OSBDetailedUploadImport', 'PerIntOptionalPackageDetail'),
        (21, 'OSBDetailedUploadImport', 'PerIntOptionalPackageHeader'),
        (22, 'RelatedPartyImport','RelatedPartyExtract'),
        (23, 'PBPMap', 'BPT_To_PBP_Mapping'),
        (24, 'BasetoBidMSBMapping', 'SavedBaseToBidBenefitSelection'),
        (25, 'BaseMSBDetails', 'Base MSB Details'),
        (26, 'LowIncomeBenchmarks', 'LowIncomeBenchmarks'),
        (27, 'MSBProjected', 'MSBData'),
        (28, 'MSBProjected', 'MSBDataDetail'),
        (29, 'RelatedPartyAndRXRebates', 'RelatedPartyAndRXRebates'),
        (30, 'PartBPremiumBuyDown', 'PartBPremiumBuyDown'),
        (31, 'EstimatedPlanBidComponent', 'EstimatedPlanBidComponent'),
        (32, 'ActAdjBenCat', 'ActAdjBenCat'),
        (33, 'VBID', 'VBID'),
        (34, 'ActAdjRepCat', 'ActAdjRepCat'),
        (36, 'BenefitOptionsIDs', 'BenefitOptionsIDs'),
        (37, 'MERFactors', 'MERFactors'),
        (38, 'BenMOOPDedImp', 'Plan Level Projected'),
        (39, 'BenMOOPDedImp', 'Benefit Level Projected'),
        (40, 'SCTPlanListOverride', 'CPSRemove' ),
        (41, 'SCTPlanListOverride', 'CPSPlanList' ),
        (42, 'SCTPlanListOverride', 'CPSData' ),
        (43, 'INOONDistribution', 'INOONDistribution'),
        (44, 'OtherSNPAdjustment', 'OtherSNPAdjustment'),
        (45, 'TrendAdjServiceAreaReduction', 'TrendAdjSARSavedOutlier'),
        (46, 'TrendAdjServiceAreaReduction', 'TrendAdjSARSavedSuperBIDE'),
        (47, 'TrendAdjServiceAreaReduction', 'TrendAdjSavedUCDB'),
        (51, 'RelatedPartySubstantiation' , 'RelatedPartySubstantiation' ),
        (53,'SpecialCrosswalkMembership','SpecialCrosswalkMembership'),
	    (54, 'MedicareSecondaryPayerTemplate', 'MedicareSecPayerAdj'),
        (55, 'RxPremium', 'RxPremium'),
        (57, 'DeductibleTypesAndExclusions', 'DeductibleTypes'),
        (58, 'DeductibleTypesAndExclusions', 'DeductibleExclusions'),
        (60,'ProfitPercent','ProfitPercent'),
        (61, 'DentalOOPC', 'SavedPlanLevelDENOOPC'),
        (62, 'DentalOOPC', 'SavedDENOOPCByBenCode'),
        (63, 'DentalOOPC', 'SavedOOPCDedAdj'),
        (64, 'RatebookUploadAll' , 'CMS Regional Statutory Rates'),
        (65, 'PDBenefitString', 'PD Benefit String'),
	    (66, 'MACTAPT','MACTAPT'),
	    (67, 'ProjectedMedicaidRevenue', 'ProjectedMedicaidRevenue'),
	    (68, 'ActAdjMSB', 'MSBSelections'),
        (69, 'OsbImport','OSBExtract'),
        (70,'PercentCovered','PercentCovered'),
        (71, 'ActAdjRepRS', 'MemberMonths'),
	    (72, 'ActAdjRepRS', 'RiskScores');


        INSERT INTO dbo.ImportTypeFields ([ImportTypeFieldId], [ImportTypeId], [FieldName], [FieldDataType], [IsAllowNull], [FieldOrder])
        VALUES
        ( 1, 1, 'HumanaRegionID', 'int', 0, NULL ),
        ( 2, 1, 'HumanaRegionName', 'string', 1, NULL ),
        ( 3, 1, 'PlanTypeID', 'int', 0, NULL ),
        ( 4, 1, 'PlanTypeName', 'string', 0, NULL ),
        ( 5, 1, 'BenefitCategoryID', 'int', 0, NULL ),
        ( 6, 1, 'BenefitCategoryName', 'string', 0, NULL ),
        ( 7, 1, 'Factor', 'decimal', 0, NULL ),
        ( 8, 2, 'PlanYearID', 'int', 0, NULL ),
        ( 9, 2, 'BenefitCategoryID', 'int', 0, NULL ),
        ( 10, 2, 'LengthOfStay', 'int', 0, NULL ),
        ( 11, 2, 'AdmitCount', 'int', 0, NULL ),
        ( 12, 3, 'PlanYearID', 'int', 0, NULL ),
        ( 13, 3, 'ContractNumber', 'string', 0, NULL ),
        ( 14, 3, 'PlanID', 'string', 0, NULL ),
        ( 15, 3, 'SegmentID', 'string', 0, NULL ),
        ( 16, 3, 'CompletionFactor', 'decimal', 0, NULL ),
        ( 17, 3, 'PaidClaims', 'decimal', 0, NULL ),
        ( 18, 3, 'CompletedPaidClaims', 'decimal', 0, NULL ),
        ( 19, 4, 'ProductTypeID', 'int', 0, NULL ),
        ( 20, 4, 'MinINMOOP', 'int', 0, NULL ),
        ( 21, 4, 'MaxINMOOP', 'int', 0, NULL ),
        ( 22, 4, 'MinOONMOOP', 'int', 0, NULL ),
        ( 23, 4, 'MaxOONMOOP', 'int', 0, NULL ),
        ( 24, 4, 'MinCOMBMOOP', 'int', 0, NULL ),
        ( 25, 4, 'MaxCOMBMOOP', 'int', 0, NULL ),
        ( 26, 4, 'CostShareType', 'string', 0, NULL ),
        ( 27, 5, 'PlanYearID', 'int', 0, NULL ),
        ( 28, 5, 'Prong1FactorName', 'string', 0, NULL ),
        ( 29, 5, 'Prong1FactorValue', 'decimal', 0, NULL ),
        ( 30, 6, 'MOOPCategory', 'string', 0, NULL ),
        ( 31, 6, 'BenefitCategoryID', 'int', 0, NULL ),
        ( 32, 6, 'INBenefitTypeID', 'int', 0, NULL ),
        ( 33, 6, 'INEndDayRange', 'int', 0, NULL ),
        ( 34, 6, 'INMaxBenefit', 'decimal', 0, NULL ),
        ( 35, 6, 'BenefitProngTesting', 'int', 0, NULL ),
        ( 36, 7, 'ForecastID', 'int', 0, 1 ),
        ( 37, 7, 'Nickname', 'string', 1, 2 ),
        ( 38, 7, 'Notes', 'string', 1, 3 ),
        ( 40, 7, 'Honor', 'string', 0, 5 ),
        ( 41, 7, 'VeteranBrandUSAA', 'string', 0, 6 ),
        ( 42, 7, 'HMOPOSDental', 'string', 0, 7 ),
        ( 43, 8, 'Ratebook Name', 'string', 0, NULL ),
        ( 44, 9, 'PlanYearID', 'int', 0, NULL ),
        ( 45, 9, 'StateTerritoryID', 'int', 0, NULL ),
        ( 46, 9, 'CountyCode', 'string', 0, NULL ),
        ( 47, 9, 'County_Name', 'string', 0, NULL ),
        ( 48, 9, 'OrigMedicareCostSharing_Inpatient', 'decimal', 0, NULL ),
        ( 49, 9, 'OrigMedicareCostSharing_SNF', 'decimal', 0, NULL ),
        ( 50, 9, 'OrigMedicareCostSharing_Part B', 'decimal', 0, NULL ),
        ( 51, 9, 'FFS county level costs: Inpatient', 'decimal', 0, NULL ),
        ( 52, 9, 'FFS county level costs: SNF', 'decimal', 0, NULL ),
        ( 53, 9, 'FFS county level costs:Part B', 'decimal', 0, NULL ),
        ( 54, 9, 'Risk-stand FFS cost shr: Part A', 'decimal', 0, NULL ),
        ( 55, 9, 'Risk-stand FFS cost shr:Part B', 'decimal', 0, NULL ),
        ( 56, 9, '5% Bonus Rates', 'decimal', 0, NULL ),
        ( 57, 9, '0% Bonus Rates', 'decimal', 0, NULL ),
        ( 58, 9, 'PartBRate', 'decimal', 0, NULL ),
        ( 59, 9, '3.5% Bonus Rates', 'decimal', 0, NULL ),
        ( 60, 10, 'MARegion', 'string', 0, NULL ),
        ( 61, 10, 'ContractNumber', 'string', 0, NULL ),
        ( 62, 10, 'MARegionID', 'int', 0, NULL ),
        ( 63, 10, 'MARegionRate', 'decimal', 0, NULL ),
        ( 64, 11, 'PlanYear', 'int', 0, NULL ),
        ( 65, 11, 'ContractNumber', 'string', 0, NULL ),
        ( 66, 11, 'CombinedStars', 'decimal', 0, NULL ),
        ( 67, 11, 'IsLowEnrollment', 'int', 0, NULL ),
        ( 68, 11, 'IsNew', 'int', 0, NULL ),
        ( 69, 12, 'PlanYearID', 'int', 0, NULL ),
        ( 70, 12, 'ContractNumber', 'string', 0, NULL ),
        ( 71, 12, 'RebatePercent', 'decimal', 0, NULL ),
        ( 72, 13, 'PlanYearID', 'int', 0, NULL ),
        ( 73, 13, 'ContractNumber', 'string', 0, NULL ),
        ( 74, 13, 'NetMedicalExpenses', 'decimal', 0, NULL ),
        ( 75, 13, 'NonBenefitExpenses', 'decimal', 0, NULL ),
        ( 76, 13, 'Premium', 'decimal', 0, NULL ),
        ( 77, 13, 'MemberMonths', 'int', 0, NULL ),
        ( 78, 14, 'PlanYearID', 'int', 0, NULL ),
        ( 79, 14, 'ActuarialRegionID', 'Short', 0, NULL ),
        ( 80, 14, 'StateID', 'Short', 0, NULL ),
        ( 81, 14, 'ActuarialMarketID', 'Short', 0, NULL ),
        ( 82, 14, 'CountyCode', 'string', 0, NULL ),
        ( 83, 14, 'CPS', 'string', 0, NULL ),
        ( 84, 14, 'Product', 'string', 0, NULL ),
        ( 85, 14, 'IsSNP', 'bool', 0, NULL ),
        ( 86, 14, 'MA Marketing Admin PMPM', 'decimal', 0, NULL ),
        ( 87, 14, 'MA Direct Admin PMPM', 'decimal', 0, NULL ),
        ( 88, 14, 'MA Indirect Admin PMPM', 'decimal', 0, NULL ),
        ( 89, 14, 'PD Marketing Admin PMPM', 'decimal', 0, NULL ),
        ( 90, 14, 'PD Direct Admin PMPM', 'decimal', 0, NULL ),
        ( 91, 14, 'PD Indirect Admin PMPM', 'decimal', 0, NULL ),
        ( 92, 14, 'MAQualityAdmin PMPM', 'decimal', 0, NULL ),
        ( 93, 14, 'MATaxesAndFeesAdmin PMPM', 'decimal', 0, NULL ),
        ( 94, 14, 'PDQualityAdmin PMPM', 'decimal', 0, NULL ),
        ( 95, 14, 'PDTaxesAndFeesAdmin PMPM', 'decimal', 0, NULL ),
        ( 96, 15, 'PlanYearID', 'int', 0, NULL ),
        ( 97, 15, 'CPDID', 'int', 0, NULL ),
        ( 98, 15, 'DualEligibleTypeID', 'int', 0, NULL ),
        ( 99, 15, 'IPAllowedPMPM', 'decimal', 0, NULL ),
        ( 100, 15, 'SNFAllowedPMPM', 'decimal', 0, NULL ),
        ( 101, 15, 'OtherAllowedPMPM', 'decimal', 0, NULL ),
        ( 102, 16, 'PlanYearID', 'int', 0, NULL ),
        ( 103, 16, 'BalancingResultID', 'int', 0, NULL ),
        ( 104, 16, 'ClaimBenefitMapID', 'int', 0, NULL ),
        ( 105, 16, 'MOOPBucketID', 'int', 0, NULL ),
        ( 106, 16, 'CPDID', 'int', 0, NULL ),
        ( 107, 16, 'DualEligibleTypeID', 'int', 0, NULL ),
        ( 108, 16, 'Members', 'int', 0, NULL ),
        ( 109, 16, 'Distribution', 'double', 0, NULL ),
        ( 110, 16, 'TotalAllowedAmt', 'decimal', 0, NULL ),
        ( 111, 16, 'IPAllowedAmt', 'decimal', 0, NULL ),
        ( 112, 16, 'SNFAllowedAmt', 'decimal', 0, NULL ),
        ( 113, 16, 'OtherAllowedAmt', 'decimal', 0, NULL ),
        (114, 17, 'ForecastID', 'int', 0, 1),
        (115, 17, 'ContactID', 'string', 0, 2),
        (116, 17, 'SecondaryContactID', 'string', 0, 3),
        (117, 17, 'CertifyingActuaryUserID', 'string', 0, 4),
        (118, 17, 'IsHidden', 'int', 0, 5),
        (119, 17, 'IsLiveIndex', 'int', 0, 6),
        (120, 17, 'IsSCTPlan', 'int', 0, 7),
        (121, 17, 'IsFiledPlan', 'int', 0, 8),
        (122, 17, 'IsLocked', 'int', 0, 9),
        (123, 17, 'IsSkipInducedUtilization', 'int', 0, 10),
        (124, 17, 'PlanName', 'string', 0, 11),
         (125, 18,'ContractPBP','string', 0, 1),
         (126, 18,'SegmentID','string', 0, 2),
         (127, 18,'OrganizationName','string', 0, 3),
         (128, 18,'PlanName','string', 0, 4),
         (129, 18,'DivisionName','string', 0, 5),
         (130, 18,'HumanaRegionName','string', 0, 6),
         (131, 18,'Market','string', 0, 7),
         (132, 18,'ProductType','string', 0, 8),
         (133, 18,'SnpTypeName','string', 0, 9),
         (134, 18,'PlanType','string', 0, 10),
         (135, 18,'ActuarialProduct','string', 0, 11),
         (136, 18,'BasicPremiumRounded','decimal', 0, 12),
         (137, 18,'SupplementalPremiumRounded','decimal', 0, 13),
         (138, 18,'TotalPremiumRounded','decimal', 0, 14),
         (139, 18,'BidDirectSubsidy','decimal', 0, 15),
         (140, 18,'Formulary','string', 1, 16),
         (142, 18,'ErectileDysfunction','string', 1, 17),
         (143, 18,'ErectileDysfunctionTier','string', 1, 18),
         (144, 18,'AntiObesityDrugCoverage','string', 1, 19),
         (145, 18,'AntiObesityTier','string', 1, 20),
         (146, 18,'PrescriptionVitamins','string', 1, 21),
         (147, 18,'PrescriptionVitaminsTier','string', 1, 22),
         (148, 18,'ZeroDollarDSNP','string', 1, 23),
         (149, 18,'COPDVBID','string', 1, 24),
         (150, 18,'DoacVbid','string', 0, 25),
         (389, 18,'Placeholder1','string', 1, 26),
         (390, 18,'Placeholder2','string', 1, 27),
         (388, 18,'HundredDayFill','string', 0, 28),
         (391, 18,'Placeholder3','string', 1, 29),
         (151, 18,'Placeholder4','string', 1, 30),
         (152, 18,'Placeholder5','string', 1, 31),
         (153, 18,'Deductible','Short', 0, 32),
         (154, 18,'DeductibleExcludeAnyTiers','string', 1, 33),
         (155, 18,'DeductibleT1','string', 1, 34),
         (156, 18,'DeductibleT2','string', 1, 35),
         (157, 18,'DeductibleT3','string', 1, 36),
         (158, 18,'DeductibleT4','string', 1, 37),
         (159, 18,'DeductibleT5','string', 1, 38),
         (160, 18,'DeductibleT6','string', 1, 39),
         (161, 18,'Part_D_MOOP','Short', 0, 40),
         (162, 18,'BenefitString1MonthStandardRetail','string', 1, 41),
         (163, 18,'BenefitString3MonthStandardRetail','string', 1, 42),
         (164, 18,'BenefitString1MonthPreferredRetail','string', 1, 43),
         (165, 18,'BenefitString3MonthPreferredRetail','string', 1, 44),
         (166, 18,'BenefitString1MonthStandardMail','string', 1, 45),
         (167, 18,'BenefitString3MonthStandardMail','string', 1, 46),
         (168, 18,'BenefitString1MonthPreferredMail','string', 1, 47),
         (169, 18,'BenefitString3MonthPreferredMail','string', 1, 48),
         (170, 18,'ICCostShare1MonthStandardRetailT1','decimal', 1, 49),
         (171, 18,'ICCostShare1MonthStandardRetailT2','decimal', 1, 50),
         (172, 18,'ICCostShare1MonthStandardRetailT3','decimal', 1, 51),
         (173, 18,'ICCostShare1MonthStandardRetailT4','decimal', 1, 52),
         (174, 18,'ICCostShare1MonthStandardRetailT5','decimal', 1, 53),
         (175, 18,'ICCostShare1MonthStandardRetailT6','decimal', 1, 54),
         (176, 18,'ICCostShare3MonthStandardRetailT1','decimal', 1, 55),
         (177, 18,'ICCostShare3MonthStandardRetailT2','decimal', 1, 56),
         (178, 18,'ICCostShare3MonthStandardRetailT3','decimal', 1, 57),
         (179, 18,'ICCostShare3MonthStandardRetailT4','decimal', 1, 58),
         (180, 18,'ICCostShare3MonthStandardRetailT5','decimal', 1, 59),
         (181, 18,'ICCostShare3MonthStandardRetailT6','decimal', 1, 60),
         (182, 18,'ICCostShare1MonthPreferredRetailT1','decimal', 1, 61),
         (183, 18,'ICCostShare1MonthPreferredRetailT2','decimal', 1, 62),
         (184, 18,'ICCostShare1MonthPreferredRetailT3','decimal', 1, 63),
         (185, 18,'ICCostShare1MonthPreferredRetailT4','decimal', 1, 64),
         (186, 18,'ICCostShare1MonthPreferredRetailT5','decimal', 1, 65),
         (187, 18,'ICCostShare1MonthPreferredRetailT6','decimal', 1, 66),
         (188, 18,'ICCostShare3MonthPreferredRetailT1','decimal', 1, 67),
         (189, 18,'ICCostShare3MonthPreferredRetailT2','decimal', 1, 68),
         (190, 18,'ICCostShare3MonthPreferredRetailT3','decimal', 1, 69),
         (191, 18,'ICCostShare3MonthPreferredRetailT4','decimal', 1, 70),
         (192, 18,'ICCostShare3MonthPreferredRetailT5','decimal', 1, 71),
         (193, 18,'ICCostShare3MonthPreferredRetailT6','decimal', 1, 72),
         (194, 18,'ICCostShare1MonthStandardMailT1','decimal', 1, 73),
         (195, 18,'ICCostShare1MonthStandardMailT2','decimal', 1, 74),
         (196, 18,'ICCostShare1MonthStandardMailT3','decimal', 1, 75),
         (197, 18,'ICCostShare1MonthStandardMailT4','decimal', 1, 76),
         (198, 18,'ICCostShare1MonthStandardMailT5','decimal', 1, 77),
         (199, 18,'ICCostShare1MonthStandardMailT6','decimal', 1, 78),
         (200, 18,'ICCostShare3MonthStandardMailT1','decimal', 1, 79),
         (201, 18,'ICCostShare3MonthStandardMailT2','decimal', 1, 80),
         (202, 18,'ICCostShare3MonthStandardMailT3','decimal', 1, 81),
         (203, 18,'ICCostShare3MonthStandardMailT4','decimal', 1, 82),
         (204, 18,'ICCostShare3MonthStandardMailT5','decimal', 1, 83),
         (205, 18,'ICCostShare3MonthStandardMailT6','decimal', 1, 84),
         (206, 18,'ICCostShare1MonthPreferredMailT1','decimal', 1, 85),
         (207, 18,'ICCostShare1MonthPreferredMailT2','decimal', 1, 86),
         (208, 18,'ICCostShare1MonthPreferredMailT3','decimal', 1, 87),
         (209, 18,'ICCostShare1MonthPreferredMailT4','decimal', 1, 88),
         (210, 18,'ICCostShare1MonthPreferredMailT5','decimal', 1, 89),
         (211, 18,'ICCostShare1MonthPreferredMailT6','decimal', 1, 90),
         (212, 18,'ICCostShare3MonthPreferredMailT1','decimal', 1, 91),
         (213, 18,'ICCostShare3MonthPreferredMailT2','decimal', 1, 92),
         (214, 18,'ICCostShare3MonthPreferredMailT3','decimal', 1, 93),
         (215, 18,'ICCostShare3MonthPreferredMailT4','decimal', 1, 94),
         (216, 18,'ICCostShare3MonthPreferredMailT5','decimal', 1, 95),
         (217, 18,'ICCostShare3MonthPreferredMailT6','decimal', 1, 96),
         (218, 18,'BelowICLCoins1MonthStandardRetailT1','decimal', 1, 97),
         (219, 18,'BelowICLCoins1MonthStandardRetailT2','decimal', 1, 98),
         (220, 18,'BelowICLCoins1MonthStandardRetailT3','decimal', 1, 99),
         (221, 18,'BelowICLCoins1MonthStandardRetailT4','decimal', 1, 100),
         (222, 18,'BelowICLCoins1MonthStandardRetailT5','decimal', 1, 101),
         (223, 18,'BelowICLCoins1MonthStandardRetailT6','decimal', 1, 102),
         (230, 18,'BelowICLCoins1MonthPreferredRetailT1','decimal', 1, 103),
         (231, 18,'BelowICLCoins1MonthPreferredRetailT2','decimal', 1, 104),
         (232, 18,'BelowICLCoins1MonthPreferredRetailT3','decimal', 1, 105),
         (233, 18,'BelowICLCoins1MonthPreferredRetailT4','decimal', 1, 106),
         (234, 18,'BelowICLCoins1MonthPreferredRetailT5','decimal', 1, 107),
         (235, 18,'BelowICLCoins1MonthPreferredRetailT6','decimal', 1, 108),
         (296, 18,'InsulinCostShare1MonthStandardRetailT1','decimal', 1, 109),
         (297, 18,'InsulinCostShare1MonthStandardRetailT2','decimal', 1, 110),
         (298, 18,'InsulinCostShare1MonthStandardRetailT3','decimal', 1, 111),
         (299, 18,'InsulinCostShare1MonthStandardRetailT4','decimal', 1, 112),
         (300, 18,'InsulinCostShare1MonthStandardRetailT5','decimal', 1, 113),
         (301, 18,'InsulinCostShare1MonthStandardRetailT6','decimal', 1, 114),
         (302, 18,'InsulinCostShare3MonthStandardRetailT1','decimal', 1, 115),
         (303, 18,'InsulinCostShare3MonthStandardRetailT2','decimal', 1, 116),
         (304, 18,'InsulinCostShare3MonthStandardRetailT3','decimal', 1, 117),
         (305, 18,'InsulinCostShare3MonthStandardRetailT4','decimal', 1, 118),
         (306, 18,'InsulinCostShare3MonthStandardRetailT5','decimal', 1, 119),
         (307, 18,'InsulinCostShare3MonthStandardRetailT6','decimal', 1, 120),
         (308, 18,'InsulinCostShare1MonthPreferredRetailT1','decimal', 1, 121),
         (309, 18,'InsulinCostShare1MonthPreferredRetailT2','decimal', 1, 122),
         (310, 18,'InsulinCostShare1MonthPreferredRetailT3','decimal', 1, 123),
         (311, 18,'InsulinCostShare1MonthPreferredRetailT4','decimal', 1, 124),
         (312, 18,'InsulinCostShare1MonthPreferredRetailT5','decimal', 1, 125),
         (313, 18,'InsulinCostShare1MonthPreferredRetailT6','decimal', 1, 126),
         (314, 18,'InsulinCostShare3MonthPreferredRetailT1','decimal', 1, 127),
         (315, 18,'InsulinCostShare3MonthPreferredRetailT2','decimal', 1, 128),
         (316, 18,'InsulinCostShare3MonthPreferredRetailT3','decimal', 1, 129),
         (317, 18,'InsulinCostShare3MonthPreferredRetailT4','decimal', 1, 130),
         (318, 18,'InsulinCostShare3MonthPreferredRetailT5','decimal', 1, 131),
         (319, 18,'InsulinCostShare3MonthPreferredRetailT6','decimal', 1, 132),
         (320, 18,'InsulinCostShare1MonthStandardMailT1','decimal', 1, 133),
         (321, 18,'InsulinCostShare1MonthStandardMailT2','decimal', 1, 134),
         (322, 18,'InsulinCostShare1MonthStandardMailT3','decimal', 1, 134),
         (323, 18,'InsulinCostShare1MonthStandardMailT4','decimal', 1, 135),
         (324, 18,'InsulinCostShare1MonthStandardMailT5','decimal', 1, 136),
         (325, 18,'InsulinCostShare1MonthStandardMailT6','decimal', 1, 137),
         (326, 18,'InsulinCostShare3MonthStandardMailT1','decimal', 1, 138),
         (327, 18,'InsulinCostShare3MonthStandardMailT2','decimal', 1, 139),
         (328, 18,'InsulinCostShare3MonthStandardMailT3','decimal', 1, 140),
         (329, 18,'InsulinCostShare3MonthStandardMailT4','decimal', 1, 141),
         (330, 18,'InsulinCostShare3MonthStandardMailT5','decimal', 1, 142),
         (331, 18,'InsulinCostShare3MonthStandardMailT6','decimal', 1, 143),
         (332, 18,'InsulinCostShare1MonthPreferredMailT1','decimal', 1, 144),
         (333, 18,'InsulinCostShare1MonthPreferredMailT2','decimal', 1, 145),
         (334, 18,'InsulinCostShare1MonthPreferredMailT3','decimal', 1, 146),
         (335, 18,'InsulinCostShare1MonthPreferredMailT4','decimal', 1, 147),
         (336, 18,'InsulinCostShare1MonthPreferredMailT5','decimal', 1, 148),
         (337, 18,'InsulinCostShare1MonthPreferredMailT6','decimal', 1, 149),
         (338, 18,'InsulinCostShare3MonthPreferredMailT1','decimal', 1, 150),
         (339, 18,'InsulinCostShare3MonthPreferredMailT2','decimal', 1, 151),
         (340, 18,'InsulinCostShare3MonthPreferredMailT3','decimal', 1, 152),
         (341, 18,'InsulinCostShare3MonthPreferredMailT4','decimal', 1, 153),
         (342, 18,'InsulinCostShare3MonthPreferredMailT5','decimal', 1, 154),
         (343, 18,'InsulinCostShare3MonthPreferredMailT6','decimal', 1, 155),
         (344, 18,'DaySupply1MonthRetailT1','decimal', 1, 156),
         (345, 18,'DaySupply1MonthRetailT2','decimal', 1, 157),
         (346, 18,'DaySupply1MonthRetailT3','decimal', 1, 158),
         (347, 18,'DaySupply1MonthRetailT4','decimal', 1, 159),
         (348, 18,'DaySupply1MonthRetailT5','decimal', 1, 160),
         (349, 18,'DaySupply1MonthRetailT6','decimal', 1, 161),
         (350, 18,'DaySupply1MonthMailT1','decimal', 1, 162),
         (351, 18,'DaySupply1MonthMailT2','decimal', 1, 163),
         (352, 18,'DaySupply1MonthMailT3','decimal', 1, 164),
         (353, 18,'DaySupply1MonthMailT4','decimal', 1, 165),
         (354, 18,'DaySupply1MonthMailT5','decimal', 1, 166),
         (355, 18,'DaySupply1MonthMailT6','decimal', 1, 167),
         (356, 18,'DaySupply3MonthRetailT1','decimal', 1, 168),
         (357, 18,'DaySupply3MonthRetailT2','decimal', 1, 169),
         (358, 18,'DaySupply3MonthRetailT3','decimal', 1, 170),
         (359, 18,'DaySupply3MonthRetailT4','decimal', 1, 171),
         (360, 18,'DaySupply3MonthRetailT5','decimal', 1, 172),
         (361, 18,'DaySupply3MonthRetailT6','decimal', 1, 173),
         (362, 18,'DaySupply3MonthMailT1','decimal', 1, 174),
         (363, 18,'DaySupply3MonthMailT2','decimal', 1, 175),
         (364, 18,'DaySupply3MonthMailT3','decimal', 1, 176),
         (365, 18,'DaySupply3MonthMailT4','decimal', 1, 177),
         (366, 18,'DaySupply3MonthMailT5','decimal', 1, 178),
         (367, 18,'DaySupply3MonthMailT6','decimal', 1, 179),
         (368, 18,'BaseMemberMonths','Int', 0, 180),
         (369, 18,'ProjectedMM','decimal', 0, 181),
         (370, 18,'PlanYearLIPercent','decimal', 1, 182),
         (371, 18,'PlanYearDrugNetClaims','decimal', 1, 183),
         (372, 18,'PlanYearAllowedClaims','decimal', 1, 184),
         (373, 18,'AdminPMPM','decimal', 1, 185),
         (374, 18,'PlanYearDrugProfitPercent','decimal', 1, 186),
         (375, 18,'ProjectedRiskScore','decimal', 1, 187),
         (376, 18,'TargetAmountAdjustment','decimal', 1, 188),
         (377, 18,'OOPC','decimal', 1, 189),
         (378, 18,'NonTroop','decimal', 1, 190),
         (379, 18,'QualityInitiatives','decimal', 1, 191),
         (380, 18,'TaxesFees','decimal', 1, 192),
         (381, 18,'DIRCapbeforeReins','decimal', 1, 193),
         (382, 18,'DIRCapafterReins','decimal', 1, 194),
         (383, 18,'BasicPremiumUnrounded','decimal', 0, 195),
         (384, 18,'SupplementalPremiumUnrounded','decimal', 0, 196),
         (385, 18,'TotalPremiumUnrounded','decimal', 0, 197),
         (386, 18,'FormularyID','Int', 1, 198),
        ( 392, 19, 'ForecastID', 'int', 0, NULL ),
        ( 393, 19, 'BenefitCategoryName', 'string', 0, NULL ),
        ( 394, 19, 'MARatingOptionID', 'int', 0, NULL ),
        ( 395, 19, 'UnitsAdjustmentFactor', 'decimal', 0, NULL ),
        ( 396, 19, 'PaidAdjustmentFactor', 'decimal', 0, NULL ),
        ( 397, 19, 'Reason For Adjustment', 'string', 1, NULL ),
        ( 398, 19, 'UnitsAdditiveAdjustment', 'decimal', 0, NULL ),
        ( 399, 19, 'PaidAdditiveAdjustment', 'decimal', 0, NULL ),
        ( 400, 19, 'PaidAdditiveAdjustmentNonSeq', 'decimal', 0, NULL ),
         (403, 20,'PlanYearID', 'int', 0, 1),
         (404, 20,'PackageIndex', 'int', 0, 2),
         (405, 20,'PackageDetailID', 'int', 0, 3),
         (406, 20,'BidServiceCategoryID', 'int',0, 4 ),
         (407, 20,'PricingComponentDescription', 'string',0, 5 ),
         (408, 20,'AllowedUtilizationTypeID', 'int', 0, 6),
         (409, 20,'AllowedUtilzationPer1000', 'decimal', 0, 7),
         (410, 20,'AllowedAverageCost', 'decimal', 0, 8),
         (411, 20,'MeasurementUnitCode', 'string', 0, 9),
         (412, 20,'EnrolleeCostShareUtilization', 'decimal', 0, 10),
         (413, 20,'EnrolleeAverageCostShare', 'decimal', 0, 11),
         (414, 20,'AverageAdminExpense', 'decimal', 0, 12),
         (415, 20,'AnnualizedAverageProfit', 'decimal', 0, 13),
         (416, 20,'IsHidden', 'int', 0, 14),
         (417, 21,'PlanYearID', 'int', 0, 1),
         (418, 21,'PackageIndex', 'int', 0, 2),
         (419, 21,'Name', 'string', 0, 3),
         (420, 21,'Description', 'string', 0, 4),
         (421, 21,'TotalExpense', 'decimal', 0, 5),
         (422, 21,'TotalGainLoss','decimal', 0, 6),
         (423, 21,'TotalProjectedMemberMonths', 'decimal', 0, 7),
         (424, 21,'IsEnabled', 'int', 0, 8),
         (425, 22,'ForecastID','int',0,1),
         (426, 22,'AddedBenefitName','string',0,1),
         (427, 23, 'BenefitCategoryID', 'int', 0, NULL),
         (428, 23, 'PBPLineCode', 'string', 1, NULL),
         (429, 24, 'PlanYear', 'int', 0, 1),
         (430, 24, 'CPS', 'string', 0, 2),
         (431, 24, 'BaseMSBCode', 'string', 0, 3),
         (432, 24, 'BidAddedBenefitName', 'string', 0, 4),
         (433, 25, 'PlanYearID', 'int', 0, 1),
         (434, 25, 'PlanInfoID', 'int', 0, 2),
         (435, 25, 'CPS', 'string', 0, 3),
         (436, 25, 'MSBCode', 'string', 0, 4),
         (437, 25, 'BenefitCategoryID', 'int', 0, 5),
         (438, 26, 'CMSRegion', 'int', 0, 1),
         (439, 26, 'States', 'string', 0, 2),
         (440, 26, 'LIBenchmark', 'decimal', 0, 3),
         (441, 27, 'PlanYearID', 'int', 0, 1),
         (442, 27, 'AddedBenefitTypeID', 'int', 0,2),
         (443, 27, 'AddedBenefitCatID', 'int', 0, 3),
         (444, 27, 'IsStandard', 'int', 0, 4),
         (445, 27, 'AddedBenefitName', 'string', 0, 5),
         (446, 27, 'INAddedBenefitDescription', 'string', 0, 6),
         (447, 27, 'INAddedBenefitAllowed', 'decimal', 0, 7),
         (448, 27, 'INAddedBenefitUtilization', 'decimal', 0, 8),
         (449, 27, 'INAddedBenefitCostShare', 'decimal', 0, 9),
         (450, 27, 'OONAddedBenefitDescription', 'string', 0, 10),
         (451, 27, 'OONAddedBenefitAllowed', 'decimal', 0, 11),
         (452, 27, 'OONAddedBenefitUtilization', 'decimal', 0, 12),
         (453, 27, 'OONAddedBenefitCostShare', 'decimal', 0, 13),
         (454, 27, 'BidServiceCatID', 'int', 0, 14),
         (455, 27, 'IsValueAdded', 'int', 0, 15),
         (456, 27, 'IsNetwork', 'int', 0, 16),
         (457, 27, 'IsEnabled', 'int', 0, 17),
         (458, 27, 'OONPercent', 'decimal', 0, 18),
         (459, 27, 'SubstantiationDetail', 'string', 0, 19),
         (460, 28, 'PlanYearID', 'int', 0, 1),
         (461, 28, 'AddedBenefitTypeID', 'int', 0, 2),
         (462, 28, 'AddedBenefitName', 'string', 0, 3),
         (463, 28, 'AddedBenefitClaims', 'decimal', 0, 4),
         (464, 28, 'AddedBenefitAdmin', 'decimal', 0, 5),
         (465, 28, 'AddedBenefitSales', 'decimal', 0, 6),
         (466, 28, 'AddedBenefitProfit', 'decimal', 0, 7),
         (467, 28, 'HumanaOwnership', 'decimal', 0, 8),
         (468, 28, 'SalesMktExpensePMPM', 'decimal', 0, 9),
         (469, 28, 'DirectExpensePMPM', 'decimal', 0, 10),
         (470, 28, 'IndirectExpensePMPM', 'decimal', 0, 11),
         (471, 28, 'IsRelatedParty', 'int', 0, 12),
         (472, 28, 'AddedBenefitQuality', 'decimal', 1, 13),
         (473, 28, 'ClaimsClaims', 'decimal', 0, 14),
         (474, 28, 'ClaimsQuality', 'decimal', 0, 15),
         (475, 28, 'ClaimsAdmin', 'decimal', 0, 16),
         (476, 28, 'QualityClaims', 'decimal', 0, 17),
         (477, 28, 'QualityQuality', 'decimal', 0, 18),
         (478, 28, 'QualityAdmin', 'decimal', 0, 19),
         (479, 28, 'AdminClaims', 'decimal', 0, 20),
         (480, 28, 'AdminQuality', 'decimal', 0, 21),
         (481, 28, 'AdminAdmin', 'decimal', 0, 22),
         (482, 28, 'ProfitClaims', 'decimal', 0, 23),
         (483, 28, 'ProfitQuality', 'decimal', 0, 24),
         (484, 28, 'ProfitAdmin', 'decimal', 0, 25),
         (485, 28, 'RelatedPartyMedicalBenefit', 'decimal', 0, 26),
         (486, 28, 'RelatedPartyNonMedicalBenefit', 'decimal', 0, 27),
         (487, 28, 'BidServiceCatID', 'int', 0, 28),
         (488, 28, 'TrueRelatedParty', 'int', 0, 29),
         (489, 28, 'MSBEqualsOTC', 'int', 0, 30),
         (490, 28, 'NonOTCMSB', 'int', 0, 31),
         (491, 28, 'RelatedPartyMedicalClaims', 'decimal', 0, 32),
         (492, 28, 'RelatedPartyMedicalAdmin', 'decimal', 0, 33),
         (493, 28, 'RelatedPartyMedicalProfit', 'decimal', 0, 34),
         (494, 28, 'MbrCS', 'decimal', 0, 35),
         (495, 28, 'Totalz1', 'decimal', 0, 36),
         (496, 28, 'RelatedPartyNonMedicalAdmin', 'decimal', 0, 37),
         (497, 28, 'RelatedPartyNonMedicalSales', 'decimal', 0, 38),
         (498, 28, 'RelatedPartyNonMedicalProfit', 'decimal', 0, 39),
         (499, 28, 'Totalz2', 'decimal', 0, 40),
         (500, 28, 'MSBsWith2LinesinPBP', 'int', 0, 41),
         (501, 28, 'Capitated', 'int', 0, 42),
         (502, 28, 'MbrCS1', 'decimal', 0, 43),
         (503, 28, 'TotalAllowed', 'decimal', 0, 44),
         (504, 28, 'AllowedCovered', 'decimal',0, 45),
         (505, 28, 'AllowedNotCovered', 'decimal', 0, 46),
         (506, 28, 'MSBModelID', 'string', 0, 47),
         (507, 28, 'MSBParentID', 'string', 0, 48),
         (508, 28, 'AddedBenefitCode', 'string', 0, 49),
         (509, 28, 'IsCap', 'int', 0, 50),
         (510, 28, 'UtilUnitP1000Trend', 'decimal', 0, 51),
         (511, 28, 'UtilBenefitPlanChange', 'decimal', 0, 52),
         (512, 28, 'UtilPopulationChange', 'decimal', 0, 53),
         (513, 28, 'UtilOtherFactor', 'decimal',0, 54),
         (514, 28, 'UCAProviderPaymentChange', 'decimal', 0, 55),
         (515, 28, 'UCAOtherFactor', 'decimal', 0, 56),
         (516, 28, 'NationwideMSBGranularity', 'string', 0, 57),
         (517, 28, 'DivisionMSBGranularity', 'string', 0, 58),
         (518, 28, 'RegionMSBGranularity', 'string', 0, 59),
         (519, 28, 'MarketMSBGranularity', 'string', 0, 60),
         (520, 28, 'StateMSBGranularity', 'string', 0, 61),
         (521, 28, 'PlanMSBGranularity', 'string', 0, 62),
         (522, 28, 'AddedBenefitProductType', 'string', 0, 63),
         (523, 28, 'AddedBenefitSNPType', 'string', 0, 64),
         (524, 28, 'BidServiceCategory', 'string', 0, 65),
         (525, 28, 'AddedBenefitVendor', 'string', 0, 66),
         (526, 28, 'RenewalType', 'int', 0, 67),
         (527, 28, 'FinancialCost', 'decimal', 0, 68),
         (528, 28, 'PriorYearFinancialCost', 'decimal', 0, 69),
         (529, 28, 'FinDollarIncrease', 'decimal', 0, 70),
         (530, 28, 'FinPercentIncrease', 'decimal', 0, 71),
         (531, 28, 'BidCost', 'decimal', 0, 72),
         (532, 28, 'PriorYearBidCost', 'decimal', 0, 73),
         (533, 28, 'BidDollarIncrease', 'decimal', 0, 74),
         (534, 28, 'BidPercentIncrease', 'decimal', 0, 75),
         (535, 28, 'BidAllowedClaims', 'decimal', 0, 76),
         (536, 28, 'Quality', 'decimal', 0, 77),
         (537, 28, 'NonBenefitExpensePMPM', 'decimal', 0, 78),
         (538, 28, 'SalesAndMarketingAdminPMPM', 'decimal', 0, 79),
         (539, 28, 'RelatedPartyProfitPMPM', 'decimal', 0, 80),
         (540, 28, 'FinancialUnitCost', 'decimal', 0, 81),
         (541, 28, 'BidAllowedUnitCost', 'decimal', 0, 82),
         (542, 28, 'AddedBenefitUtilization', 'decimal', 0, 83),
         (543, 28, 'FinPriceIncreasefromPriorDistribution', 'decimal', 0, 84),
         (544, 28, 'BidPriceIncreasefromPriorDistribution', 'decimal', 0, 85),
         (545, 28, 'ExclusionsDivision', 'string', 0, 86),
         (546, 28, 'ExclusionsRegion', 'string', 0, 87),
         (547, 28, 'ExclusionsState', 'string', 0, 88),
         (548, 28, 'ExclusionsMarket', 'string', 0, 89),
         (549, 28, 'ExclusionsPlan', 'string', 0, 90),
         (550, 28, 'ExclusionSNP', 'string', 0, 91),
         (551, 29,'PlanYearID', 'int', 0, 1),
         (552, 29,'AddedBenefitTypeID', 'int', 0, 2),
         (553, 29,'AddedBenefitName', 'string', 0, 3),
         (554, 29,'INAddedBenefitDescription', 'string', 0, 4),
         (555, 29,'IsEnabled', 'int', 0, 5),
         (556, 29,'AddedBenefitClaims', 'decimal', 0, 6),
         (557, 29,'AddedBenefitAdmin', 'decimal', 0, 7),
         (558, 29,'AddedBenefitSales', 'decimal', 0, 8),
         (559, 29,'SalesMktExpensePMPM', 'decimal', 0, 9),
         (560, 29,'AddedBenefitQuality', 'decimal', 0, 10),
         (561, 29,'ClaimsClaims', 'decimal', 0, 11),
         (562, 29,'QualityClaims', 'decimal', 0, 12),
         (563, 29,'QualityQuality', 'decimal', 0, 13),
         (564, 29,'AdminAdmin', 'decimal', 0, 14),
         (565, 29,'Totalz1', 'decimal', 0, 15),
         (566, 29,'Totalz2', 'decimal', 0, 16),
         (567, 30,'ForecastID', 'int', 0, 1),
         (568, 30,'PTBPremiumBuydown', 'decimal', 0, 2),
         (569, 31,'ForecastID', 'int', 0, 1),
         (570, 31,'EstimatedPlanBidComponent', 'decimal', 0, 2),
         (580, 33, 'ForecastID', 'string', 0, 1),
         (581, 33, 'VBID', 'string', 0, 2),
         (582, 33, 'Delete', 'string', 1, 3),
         (571, 32, 'AdjustmentID', 'int', 1, 1),
         (572, 32, 'AdjustmentDescription', 'string', 0, 2),
         (573, 32, 'CPS', 'string', 0, 3),
         (574, 32, 'PlanYearID', 'int', 0, 4),
         (575, 32, 'TrendYearID', 'int', 0, 5),
         (576, 32, 'RateType', 'int', 0, 6),
         (577, 32, 'BenefitCategoryID', 'int', 0, 7),
         (578, 32, 'CostAdjustment', 'decimal', 0, 8),
         (579, 32, 'UseAdjustment', 'decimal', 0, 9),
        (583, 34, 'AdjustmentID', 'int', 1, 1),
        (584, 34, 'AdjustmentDescription', 'string', 0, 2),
        (585, 34, 'CPS', N'string', 0, 3),
        (586, 34, 'PlanYearID', 'int', 0, 4),
        (587, 34, 'TrendYearID', 'int', 0, 5),
        (588, 34, 'RateType', 'int', 0, 6),
        (589, 34, 'ReportingCategory', 'string', 0, 7),
        (590, 34, 'CostAdjustment', 'decimal', 0, 8),
        (591, 34, 'UseAdjustment', 'decimal', 0, 9),
        (600, 36, 'ForecastID', 'int', 0,1),
        (601, 36, 'BenefitOptionID', 'int', 0,2),
        (602, 36, 'BenefitOptionName', 'string', 0, 3),
        (603, 36, 'BenefitOptionDescription', 'string', 0, 4),
        (604, 36, 'IsLiveIndex', 'int', 0, 5),
        (606, 37, 'ContractPBP', 'string', 0, 2),
        (610, 37, 'Component', 'int', 0, 3),
        (611, 37, 'BenefitCategoryNumber', 'int', 0, 4),
        (612, 37, 'C2PMERUseMultAdj', 'decimal', 1, 5),
        (613, 37, 'C2PMERCostMultAdj', 'decimal', 1, 6),
        (614, 37, 'C2PMERUseAddAdj', 'decimal', 1, 7),
        (615, 37, 'C2PMERCostAddAdj', 'decimal', 1, 8),
        (616, 38, 'BenefitYearID', 'int', 0, 1 ),
        (617, 38, 'ForecastID', 'int', 0, 2 ),
        (618, 38, 'PlanName', 'string', 1, 3 ),
        (619, 38, 'INDeductible', 'int', 1, 4 ),
        (620, 38, 'INMOOP', 'int', 1, 5 ),
        (621, 38, 'OONDeductible', 'int', 1, 6 ),
        (622, 38, 'OONMOOP', 'int', 1, 7 ),
        (623, 38, 'CombinedDeductible', 'int', 1, 8 ),
        (624, 38, 'CombinedMOOP', 'int', 1, 9 ),
        (625, 38, 'PartBDeductible', 'int', 1, 10 ),
        (626, 38, 'DeductTypeDesc', 'string', 0, 11 ),
        (627, 39, 'BenefitYearID', 'int', 0, 1 ),
        (628, 39, 'ForecastID', 'int', 0, 2 ),
        (629, 39, 'BenefitCategoryID', 'int', 0, 3 ),
        (630, 39, 'BenefitCategoryName', 'string', 0, 4 ),
        (631, 39, 'BenefitOrdinalID', 'int', 0, 5 ),
        (632, 39, 'INBundleID', 'int', 0, 6 ),
        (633, 39, 'INBenefitDayRange', 'int', 1, 7 ),
        (634, 39, 'INCoinsurance', 'decimal', 1, 9 ),
        (635, 39, 'INPerAdmitCopay', 'int', 1, 10 ),
        (636, 39, 'INCopay', 'decimal', 1, 11 ),
        (637, 39, 'OONBundleID', 'int', 0, 12 ),
        (638, 39, 'OONBenefitDayRange', 'int', 1, 13 ),
        (639, 39, 'OONCoinsurance', 'decimal', 1, 15 ),
        (640, 39, 'OONPerAdmitCopay', 'int', 1, 16 ),
        (641, 39, 'OONCopay', 'decimal', 1, 17 ),
        (642, 14, 'OrganizationCode', 'string', 0, NULL),
        (643, 40, 'CPS', 'string', 0, 1),
        (644, 41, 'BidYear', 'int', 0, 2),
        (645, 41, 'ContractPBPSegment', 'string', 0, 3),
        (646, 41, 'PlanYearID', 'int', 0, 4),
        (647, 41, 'PlanYearContractPBPSegment', 'string', 0, 5),
        (648, 42, 'ContractPBPSegment', 'string', 0, 6),
        (649, 42, 'Division', 'string', 0, 7),
        (650, 42, 'Region', 'string', 0, 8),
        (651, 42, 'SubRegion', 'string', 0, 9),
        (652, 42, 'PrimaryState', 'string', 0, 10),
        (653, 42, 'Market', 'string', 0, 11),
        (654, 42, 'PlanType', 'string', 0, 12),
        (655, 42, 'IsSNP', 'bool', 0, 13),
        (656, 42, 'SNPType', 'string', 0, 14),
        (657, 42, 'DSNPSubType', 'string', 0, 15),
        (658, 42, 'Product', 'string', 0, 16),
        (659, 42, 'PlanCategory', 'string', 0, 17),
        (660, 42, 'TargetedSegment', 'string', 0, 18),
        (661, 42, 'IsPassive', 'bool', 0, 19),
        (662, 42, 'GivebackRange', 'string', 0, 20),
        (663, 42, 'INNMedicalDeductibleRange', 'string', 0, 21),
        (664, 42, 'IsRiskPlan', 'bool', 0, 22),
        (665, 42, 'IsDelegated', 'string', 1, 23),
        (666, 42, 'NewPlanFlag', 'string', 0, 24),
        (667, 42, 'ConcurrentPlan', 'string', 0, 25),
        (668, 7, 'SubRegion', 'string', 0, 8 ),
        (669, 7, 'DSNPSubType', 'string', 0, 9 ),
        (670, 7, 'TargetedSegment', 'string', 0, 10 ),
        (671, 7, 'IsPassive', 'string', 0, 11),
        (672, 7, 'RRGiveback', 'string', 0, 12),
        (673, 7, 'IsRiskPlan', 'string', 0, 13),
        (674, 7, 'IsDelegated', 'string', 1, 14),
        (675, 43, 'ForecastID', 'int', 0, 1),
        (676, 43, 'BenefitCategoryID', 'int', 0, 2),
        (677, 43, 'INDistributionPercent', 'decimal', 0, 3),
        (678, 43, 'OONDistributionPercent', 'decimal', 0, 4),
        (679, 43, 'INCostFactor', 'decimal', 0, 5),
        (680, 43, 'OONCostFactor', 'decimal', 0, 6),
        (681, 43, 'DistributionDescription', 'string', 0, 7),
        (682, 44, 'PlanYear', 'int', 0, 1 ),
        (683, 44, 'CPS', 'string', 0, 2 ),
        (684, 44, 'SNPTypeDetail', 'string', 0, 3 ),
        (685, 44, 'MemberMonths', 'decimal', 0, 4 ),
        (686, 44, 'WtdRelativity', 'decimal', 0, 5 ),
        (687, 45, 'PlanYear', 'int', 0, 1),
        (688, 45, 'CPS', 'string', 0,2),
        (689, 45, 'SSStateCountyCD', 'string', 0, 3),
        (690, 45, 'Allowed', 'decimal', 0, 4),
        (691, 45, 'Units', 'decimal', 0, 5),
        (692, 46, 'PlanYear', 'int', 0, 1),
        (693, 46, 'CPS', 'string', 0, 2),
        (694, 46, 'SSStateCountyCD', 'string', 0, 3),
        (695, 46, 'CMSMemberMonths', 'int', 0, 4),
        (696, 46, 'AllowedNonSQS', 'decimal', 0, 5),
        (697, 46, 'AllowedOtherPartB', 'decimal', 0, 6),
        (698, 47, 'PlanYear', 'int', 0, 1),
        (699, 47, 'CPS', 'string', 0, 2),
        (700, 47, 'SSStateCountyCD', 'string', 0, 3),
        (701, 47, 'BaseYearRelativity', 'decimal', 0, 4),
        (712, 51, 'RelatedParty', 'string', 0, 1),
        (713, 51, 'PlanYearID', 'int', 0, 2),
        (714, 51, 'AddedBenefitName', 'string', 0, 3),
        (716, 51, 'Totalz1', 'decimal', 0, 4),
        (717, 51, 'Totalz2', 'decimal', 0, 5),
        (718, 53, 'PlanType', 'string', 0, NULL ),
        (719, 53, 'GrpIndvInD', 'string', 0, NULL ),
        (720, 53, 'CPS', 'string', 0, NULL ),
        (721, 53, 'ResidenceCounty', 'string', 0, NULL ),
        (722, 53, 'ResidenceState', 'string', 0, NULL ),
        (723, 53, 'SSStateCountyCd', 'int', 0, NULL ),
        (724, 53, 'Product', 'string', 0, NULL ),
        (725, 53, 'SNPType', 'string', 0, NULL ),
        (726, 53, 'VPP', 'string', 0, NULL ),
        (727, 53, 'MedcaidEligible', 'string', 0, NULL ),
        (728, 53, 'MedicaidStatus', 'string', 0, NULL ),
        (729, 53, 'MedicaidStatusCd', 'int', 0, NULL ),
        (730, 53, 'MedicaidCategory', 'string', 0, NULL ),
        (731, 53, 'AgeIndicator', 'string', 0, NULL ),
        (732, 53, 'MBR_CNT', 'int', 0, NULL ),
		(733, 54, 'ForecastID', 'int', 0, 1),
		(734, 54, 'SecondaryPayerAdjustment', 'decimal', 1, 2),
        (735, 55, 'ForecastID', 'int', 0, 1),
        (736, 55, 'Rx Basic Premium', 'decimal', 0, 2),
        (737, 55, 'Rx Supp Premium', 'decimal', 0, 3),
        (743, 57, 'DeductTypeDesc', 'string', 0, null),
        (744, 57, 'PermitPPO', 'int', 0, null),
        (745, 57, 'PermitHMO', 'int', 0, null),
        (746, 57, 'PartBDeduct', 'int', 0, null),
        (747, 57, 'INDeductLimit', 'int', 1, null),
        (748, 57, 'OONDeductLimit', 'int', 1, null),
        (749, 57, 'COMBDeductLimit', 'int', 1, null),
        (750, 57, 'MOOPLimit', 'int', 1, null),
        (751, 57, 'Ded_IN', 'int', 0, null),
        (752, 57, 'Ded_OON', 'int', 0, null),
        (753, 57, 'Ded_Comb', 'int', 0, null),
        (754, 57, 'MOOP_IN', 'int', 0, null),
        (755, 57, 'MOOP_OON', 'int', 0, null),
        (756, 57, 'MOOP_Comb', 'int', 0, null),
        (757, 58, 'PlanYearID', 'int', 0, null),
        (758, 58, 'BenefitCategoryID', 'int', 0, null),
        (759, 58, 'BenefitCategoryName', 'string', 0, null),
        (760, 58, 'ReportingCategory', 'string', 1, null),
        (761, 58, 'IsOON', 'int', 1, null),
        (762, 58, 'DeductTypeDesc', 'string', 0, null),
        (763, 58, 'IsDeductibleApplies', 'int', 0, null),
        (771, 37, 'ForecastID', 'int', 0,1),
        (769, 60, 'ForecastID', 'int', 0, 1),
        (770, 60, 'ProfitPercent', 'decimal',0, 2),
        (772, 61, 'PlanID', 'string', 0,1),
        (773, 61, 'TBCRoom', 'decimal', 0, 2),
		(774, 61, 'BaselineDENOOPC', 'decimal', 0, 3),
        (775, 61, 'BaselinePDOOPC', 'decimal', 0, 4),
        (776, 61, 'MAOOPCTechAdj', 'decimal', 0, 5),
        (777, 61, 'PDOOPCTechAdj', 'decimal', 0, 6),
        (778, 62, 'BenefitCode', 'string', 0, 1),
        (779, 62, 'OOPC', 'decimal', 0, 2),
        (780, 63, 'DeductibleLevel', 'int', 0, 1),
        (781, 63, 'OOPCAdj', 'decimal', 0, 2),
        (786, 67, 'ForecastID', 'int', 0, 1),
        (787, 67, 'MedicaidProjectedRevenue', 'decimal', 1, 2),
        (788, 67, 'MedicaidProjectedCostBenefitExpense', 'decimal', 1, 3),
        (789, 67, 'MedicaidProjectedCostNonBenefitExpense', 'decimal', 1, 4),
        (790, 64, 'Region', 'string', 0, 1),
        (791, 64, 'FivePercentBonus', 'decimal', 0, 1),
        (792, 64, 'LowEnrollment', 'decimal', 0, 1),
        (793, 64, 'NoBonus', 'decimal', 0, 1),
		(794, 65, 'ForecastID', 'int', 0, NULL),
		(795, 65, 'Bid Year Benefit String', 'string', 1, NULL),
	    (796, 66,'ContractNumber','string',0,1),
        (797, 66,'PlanID','string',0,2),
        (798, 66,'SegmentId','string',0,3),
        (799, 66,'Component','int',0,4),
        (800, 66,'BenefitCategoryNumber','int',0,5),
        (801, 66,'B2CNormalizedUse','decimal',0,6),
        (802, 66,'B2CNormalizedCost','decimal',0,7),
        (803, 66,'B2CMorbidityUse','decimal',0,8),
        (804, 66,'B2CMorbidityCost','decimal',0,9),
        (805, 66,'B2CDemographicUse','decimal',0,10),
        (806, 66,'B2CDemographicCost','decimal',0,11),
        (807, 66,'B2CBustersUse','decimal',0,12),
        (808, 66,'B2CBustersCost','decimal',0,13),
        (809, 66,'B2CProductUse','decimal',0,14),
        (810, 66,'B2CProductCost','decimal',0,15),
        (811, 66,'B2CGeographicUse','decimal',0,16),
        (812, 66,'B2CGeographicCost','decimal',0,17),
        (813, 66,'B2CCMSReimbursementUse','decimal',0,18),
        (814, 66,'B2CCMSReimbursementCost','decimal',0,19),
        (815, 66,'B2CContractualUse','decimal',0,20),
        (816, 66,'B2CContractualCost','decimal',0,21),
        (817, 66,'B2CBendersUse','decimal',0,22),
        (818, 66,'B2CBendersCost','decimal',0,23),
        (819, 66,'B2CWorkdayUse','decimal',0,24),
        (820, 66,'B2CWorkdayCost','decimal',0,25),
        (821, 66,'B2CFluUse','decimal',0,26),
        (822, 66,'B2CFluCost','decimal',0,27),
        (823, 66,'B2CInducedUtilizationUse','decimal',0,28),
        (824, 66,'B2CInducedUtilizationCost','decimal',0,29),
        (825, 66,'B2CActAdjUse','decimal',0,30),
        (826, 66,'B2CActAdjCost','decimal',0,31),
        (827, 66,'B2CNonActAdjUse','decimal',0,32),
        (828, 66,'B2CNonActAdjCost','decimal',0,33),
        (829, 66,'B2CPoolingUse','decimal',0,34),
        (830, 66,'B2CPoolingCost','decimal',0,35),
        (831, 66,'B2CHTPUse','decimal',0,36),
        (832, 66,'B2CHTPCost','decimal',0,37),
        (833, 66,'B2CCompoundingAdjUse','decimal',0,38),
        (834, 66,'B2CCompoundingAdjCost','decimal',0,39),
        (835, 66,'C2PNormalizedUse','decimal',0,40),
        (836, 66,'C2PNormalizedCost','decimal',0,41),
        (837, 66,'C2PMorbidityUse','decimal',0,42),
        (838, 66,'C2PMorbidityCost','decimal',0,43),
        (839, 66,'C2PDemographicUse','decimal',0,44),
        (840, 66,'C2PDemographicCost','decimal',0,45),
        (841, 66,'C2PBustersUse','decimal',0,46),
        (842, 66,'C2PBustersCost','decimal',0,47),
        (843, 66,'C2PProductUse','decimal',0,48),
        (844, 66,'C2PProductCost','decimal',0,49),
        (845, 66,'C2PGeographicUse','decimal',0,50),
        (846, 66,'C2PGeographicCost','decimal',0,51),
        (847, 66,'C2PCMSReimbursementUse','decimal',0,52),
        (848, 66,'C2PCMSReimbursementCost','decimal',0,53),
        (849, 66,'C2PContractualUse','decimal',0,54),
        (850, 66,'C2PContractualCost','decimal',0,55),
        (851, 66,'C2PBendersUse','decimal',0,56),
        (852, 66,'C2PBendersCost','decimal',0,57),
        (853, 66,'C2PWorkdayUse','decimal',0,58),
        (854, 66,'C2PWorkdayCost','decimal',0,59),
        (855, 66,'C2PFluUse','decimal',0,60),
        (856, 66,'C2PFluCost','decimal',0,61),
        (857, 66,'C2PInducedUtilizationUse','decimal',0,62),
        (858, 66,'C2PInducedUtilizationCost','decimal',0,63),
        (859, 66,'C2PActAdjUse','decimal',0,64),
        (860, 66,'C2PActAdjCost','decimal',0,65),
        (861, 66,'C2PNonActAdjUse','decimal',0,66),
        (862, 66,'C2PNonActAdjCost','decimal',0,67),
        (863, 66,'C2PPoolingUse','decimal',0,68),
        (864, 66,'C2PPoolingCost','decimal',0,69),
        (865, 66,'C2PHTPUse','decimal',0,70),
        (866, 66,'C2PHTPCost','decimal',0,71),
        (867, 66,'C2PCompoundingAdjUse','decimal',0,72),
        (868, 66,'C2PCompoundingAdjCost','decimal',0,73),
	    (869, 68, 'ForecastID', 'int', 0, 1 ),
        (870, 68, 'MSBCode', 'string', 1, 2 ),
        (871, 68, 'AddedBenefitName', 'string', 1, 3),
        (872, 68, 'Override', 'string', 1, 4),
        (873, 68, 'Instructions', 'string', 1, 5),
        (874, 69,'ForecastID', 'int', 0, 1),
	    (875, 69,'OSBCode', 'string', 0, 2),
	    (876, 69,'OSBName', 'string', 0, 3),
        (879, 70, 'PlanYearID', 'int', 0, 1),
        (880, 70, 'Contract', 'string', 0, 2),
        (881, 70, 'PlanID', 'string', 0, 3),
        (882, 70, 'SegmentID', 'string', 0, 4),  
        (883, 70, 'IsBenefitYearCurrentYear', 'int', 0, 5),
        (884, 70, 'BenefitCategoryID', 'int', 0, 6),
        (885, 70, 'PercentCoveredAllowed', 'int', 0, 7),
        (886, 70, 'PercentCoveredCostShare', 'int', 0, 8),
        (887, 71, 'FilePath', 'string', 0, 1),
	    (888, 71, 'ForecastID', 'int', 0, 2),
	    (889, 71, 'CountyCode', 'int', 0, 3),
	    (890, 71, 'NonDualAged', 'decimal', 0, 4),
	    (891, 71, 'NonDualESRD', 'decimal', 0, 5),
	    (892, 71, 'NonDualHospice', 'decimal', 0, 6),
	    (893, 71, 'DualAged', 'decimal', 0, 7),
	    (894, 71, 'DualESRD', 'decimal', 0, 8),
	    (895, 71, 'DualHospice', 'decimal', 0, 9),
	    (896, 71, 'OOAMemberMonths', 'decimal', 0 , 10),
	    (897, 71, 'LastUpdateDateTime', 'DateTime', 0, 11),
	    (898, 72, 'FilePath', 'string', 0, 1),
	    (899, 72, 'ForecastID', 'int', 0, 2),
	    (900, 72, 'CountyCode', 'int', 0, 3),
	    (901, 72, 'NonDualExperienceRiskFactor', 'decimal', 0, 4),
	    (902, 72, 'NonDualProjectionRiskFactor', 'decimal', 0, 5),
	    (903, 72, 'DualExperienceRiskFactor', 'decimal', 0, 6),
	    (904, 72, 'DualProjectionRiskFactor', 'decimal', 0, 7);



        INSERT INTO  dbo.ImportFieldValidationRules  ([RuleId], [ImportTypeFieldId], [ValidationParam1], [ValidationParam2], [ValidationParam3], [ValidationMessage], [IsActive], [IsWarnOnly], [ValidationTypeId])
        VALUES
        ( 1, 1, '0', '11', '', '', 1, 0, 1 ),
        ( 2, 1, '3960', '11', '', '', 1, 0, 2 ),
        ( 3, 1, '12', '', '', '', 1, 0, 3 ),
        ( 4, 3, '1', '6', '', '', 1, 0, 1 ),
        ( 5, 3, '6', '', '', '', 1, 0, 3 ),
        ( 6, 4, 'LkpProductType', 'Description', '', '', 1, 0, 4 ),
        ( 7, 5, 'LkpIntBenefitCategory', 'BenefitCategoryID', '', '', 1, 0, 4 ),
        ( 8, 6, 'LkpIntBenefitCategory', 'BenefitCategoryName', '', '', 1, 0, 4 ),
        ( 11, 8, 'function', 'fnGetBidYear', '', '', 1, 0, 4 ),
        ( 12, 9, '65|66|83|131', '', '', '', 1, 0, 7 ),
        ( 13, 10, '1', '366', '', '', 1, 0, 1 ),
        ( 14, 11, '1', '10000000', '', '', 1, 0, 1 ),
        ( 15, 12, 'function', 'fnGetBidYear', '', '', 1, 0, 4 ),
        ( 16, 13, '^.{5}$', '', '', 'Value in the field "ContractNumber" must be 5 characters long.', 1, 0, 8 ),
        ( 17, 13, '^(H|R).*', '', '', 'Value in the field "ContractNumber" must begin with "H" or "R".', 1, 0, 8 ),
        ( 18, 13, '\d{4}$', '', '', 'Value in the field "ContractNumber" must end with 4 digits.', 1, 0, 8 ),
        ( 19, 14, '^\d{3}$', '', '', 'Value in the field "PlanID" must be 3 digits.', 1, 0, 8 ),
        ( 20, 14, '^(?:[0-7]?[0-9]{1,2}|799)$', '', '', 'Value in the field "PlanID" must be less than 800.', 1, 0, 8 ),
        ( 21, 15, '^\d{3}$', '', '', 'Value in the field "SegmentID" must be 3 digits.', 1, 0, 8 ),
        ( 22, 16, '1', '2', '', '', 1, 0, 1 ),
        ( 23, 17, '1', '9999999999', '', '', 1, 0, 1 ),
        ( 24, 18, '1', '9999999999', '', '', 1, 0, 1 ),
        ( 25, 18, '', 'PaidClaims', '', '', 1, 0, 6 ),
        ( 26, 19, 'LkpProductType', 'ProductTypeID', '', '', 1, 0, 4 ),
        ( 27, 19, '6', '', '', '', 1, 0, 3 ),
        ( 28, 20, '-1', '', '', '', 1, 0, 6 ),
        ( 29, 20, 'LIMFOrder', '', '', '', 1, 1, 9 ),
        ( 30, 21, '-1', '', '', '', 1, 0, 6 ),
        ( 31, 21, 'LIMFOrder', '', '', '', 1, 1, 9 ),
        ( 32, 22, '-1', '', '', '', 1, 0, 6 ),
        ( 33, 22, 'LIMFOrder', '', '', '', 1, 1, 9 ),
        ( 34, 23, '-1', '', '', '', 1, 0, 6 ),
        ( 35, 23, 'LIMFOrder', '', '', '', 1, 1, 9 ),
        ( 36, 24, '-1', '', '', '', 1, 0, 6 ),
        ( 37, 24, 'LIMFOrder', '', '', '', 1, 1, 9 ),
        ( 38, 25, '-1', '', '', '', 1, 0, 6 ),
        ( 39, 25, 'LIMFOrder', '', '', '', 1, 1, 9 ),
        ( 40, 26, 'Lower|Intermediate|Mandatory|Fail', '', '', '', 1, 0, 7 ),
        ( 41, 26, '', '', '', 'Each ProductTypeID (1-6) must contain 1 of each of the 4 allowed text - "Lower", "Intermediate", "Mandatory" and "Fail"', 1, 0, 10 ),
        ( 42, 27, 'function', 'fnGetBidYear', '', '', 1, 0, 4 ),
        ( 43, 28, 'DME|IP|PartBRx|SNF', '', '', '', 1, 0, 7 ),
        ( 44, 29, '0', '', '', '', 1, 0, 6 ),
        ( 45, 30, 'Lower|Intermediate|Mandatory', '', '', '', 1, 0, 7 ),
        ( 46, 31, 'LkpIntBenefitCategory', 'BenefitCategoryID', 'AND IsEnabled = 1', '', 1, 0, 4 ),
        ( 47, 31, 'BenefitCategoryID < 1000', '', '', 'Value in the field "BenefitCategoryID" must be < 1000.', 1, 0, 11 ),
        ( 48, 32, 'LkpIntBenefitType', 'BenefitTypeID', '', '', 1, 0, 4 ),
        ( 49, 33, ' INEndDayRange== 1 || INBenefitTypeID == 5', '', '', 'Value in the field "INEndDayRange" Must be 1 unless INBenefitTypeID = 5.', 1, 0, 11 ),
        ( 50, 34, ' INBenefitTypeID != 1 || ( INBenefitTypeID == 1 &&  0 <= INMaxBenefit && INMaxBenefit <=1 )', '', '', 'Value in the field "INMaxBenefit" should be 0 <= INMaxBenefit <=1 , if INBenefitTypeID = 1.', 1, 0, 11 ),
        ( 51, 35, ' BenefitProngTesting == 0', '', '', 'Value in the field "BenefitProngTesting" should be = 0.', 1, 0, 11 ),
        ( 52, 36, 'SavedForecastSetup', 'ForecastID', '', 'Value in the field "ForecastID" is not valid', 1, 0, 4 ),
        ( 54, 40, 'Yes|No', '', '', '', 1, 0, 7 ),
        ( 55, 41, 'Yes|No', '', '', '', 1, 0, 7 ),
        ( 56, 42, 'Yes|No', '', '', '', 1, 0, 7 ),
        ( 57, 73, '^.{5}$', '', '', 'ContractNumber must be 5 characters.', 1, 0, 8 ),
        ( 58, 73, '^(H|R).*', '', '', 'Value in the field "ContractNumber" must begin with "H" or "R".', 1, 0, 8 ),
        ( 59, 73, '\d{4}$', '', '', 'Value in the field "ContractNumber" must end with 4 digits.', 1, 0, 8 ),
        ( 60, 74, 'NetMedicalExpenses>= 0', '', '', 'NetMedicalExpenses must be greater than or equal to 0', 1, 0, 11 ),
        ( 61, 75, 'NonBenefitExpenses>= 0', '', '', 'NonBenefitExpenses must be greater than or equal to 0', 1, 0, 11 ),
        ( 62, 76, 'Premium>= 0', '', '', 'Premium must be greater than or equal to 0', 1, 0, 11 ),
        ( 63, 48, '0', '1', '', '', 1, 0, 1 ),
        ( 64, 49, '0', '1', '', '', 1, 0, 1 ),
        ( 65, 50, '0', '1', '', '', 1, 0, 1 ),
        ( 66, 58, '0', '1', '', '', 1, 0, 1 ),
        ( 67, 62, '0', '26', '', '', 1, 0, 1 ),
        ( 68, 66, '0', '5', '', '', 1, 0, 1 ),
        ( 69, 67, '0 == IsLowEnrollment || 1 == IsLowEnrollment', '', '', 'Value must be a 0 or 1. Please review the data and update accordingly.', 1, 0, 11 ),
        ( 70, 68, '0 == IsNew || 1 == IsNew', '', '', 'Value must be a 0 or 1. Please review the data and update accordingly.', 1, 0, 11 ),
        ( 71, 71, '0.5|0.65|0.7', '', '', 'Valid values are (.5 , .65, .7). Please review the data and update accordingly.', 1, 0, 7 ),
        ( 72, 7, ' Factor >0 && Factor <=1 ', '', '', 'Value in the field "Factor" should be 0 < Factor <=1.', 1, 0, 11 ),
        ( 73, 78, 'function', 'fnGetBidYear', '', '', 1, 0, 4 ),-- dbo.fnGetBidYear() or fromLkpIntPlanYear where bid year=1
        ( 74, 79, 'SavedRegionInfo', 'ActuarialRegionID', '', '', 1, 0, 4 ),-- value should exist in ActurialRegionid SavedRegionInfo
        ( 75, 80, '(SELECT DISTINCT StateTerritoryID FROM LkpStateCounty UNION    SELECT 0 StateTerritoryID)  t', 'StateTerritoryID', '', '', 1, 0, 4 ),   -- it should match from LkpStateCounty
        ( 76, 81, 'SavedMarketInfo', 'ActuarialMarketID', '', '', 1, 0, 4 ),	 -- it should match from SavedMarketInfo
        ( 77, 82, '(SELECT DISTINCT RIGHT(SSStateCountyCD,3) CountyID FROM LkpStateCounty UNION    SELECT ''0'' CountyID)  t', 'CountyID', '', '', 1, 0, 4 ),	 -- it should SSStateCountyCD match from LkpStateCounty
        ( 78, 83, '^([a-zA-Z]{1}[0-9]{4}-[0-9]{3}-[0-9]{3}|0)$', '', '', 'Value in the field "CPS" must "0" or begin with "H" ,"R".', 1, 0, 8 ),
        ( 79, 84, 'LkpProductType', 'ProductType', 'AND IsEnabled = 1', '', 1, 0, 4 ),
        ( 80, 85, '0|1', '', '', '', 1, 0, 7 ),
        ( 81, 86, 'MAMarketingAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 82, 87, 'MADirectAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 83, 88, 'MAIndirectAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 84, 89, 'PDMarketingAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 85, 90, 'PDDirectAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 86, 91, 'PDIndirectAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 87, 92, 'MAQualityAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 88, 93, 'MATaxesAndFeesAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 89, 94, 'PDQualityAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 90, 95, 'PDTaxesAndFeesAdminPMPM >= 0 ', '', '', 'Value is < 0. Please review the data.', 1, 1, 11 ),
        ( 91, 96, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        ( 92, 97, 'SavedContractCPDIDHeader', 'CPDID', '', 'Value must be valid CPDID. Please review the data and update accordingly.', 1, 0, 4 ),   -- it should match from SavedContractCPDIDHeader
        ( 93, 98, '0|1|2', '', '', 'Value must be a 0, 1, or 2. Please review the data and update accordingly.', 1, 0, 7 ),
        ( 94, 98, 'LkpIntDualEligibleType ', 'DualEligibleTypeID', '', 'Value must be valid DualEligibleTypeID. Please review the data and update accordingly.', 1, 0, 4 ),   -- it should match from LkpIntDualEligibleType
        ( 95, 99, 'IPAllowedPMPM >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 96, 100, 'SNFAllowedPMPM >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 97, 101, 'OtherAllowedPMPM >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 98, 102, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        ( 99, 103, '999', '', '', '', 1, 0, 7 ),
        ( 100, 104, '999', '', '', '', 1, 0, 7 ),
        ( 101, 105, 'MOOPBucketID > 0 ', '', '', 'Value must be > 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 102, 106, 'SavedContractCPDIDHeader', 'CPDID', '', 'Value must be valid CPDID. Please review the data and update accordingly.', 1, 0, 4 ),   -- it should match from SavedContractCPDIDHeader
        ( 103, 107, '2', '', '', 'Value for DualEligibleTypeID must be 2 (both Dual and Non Dual). Please review the data and update accordingly.', 1, 0, 7 ),   -- it should match from LkpIntDualEligibleType
        ( 104, 108, 'Members >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 105, 109, 'Distribution >= 0 && Distribution <=1', '', '', ' Value in the field "Distribution" should be 0 < Distribution <=1. Please review the data and update accordingly.', 1, 0, 11 ),
        ( 106, 110, 'TotalAllowedAmt >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 107, 111, 'IPAllowedAmt >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 108, 112, 'SNFAllowedAmt >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        ( 109, 113, 'OtherAllowedAmt >= 0 ', '', '', 'Value must be >= 0 . Please review the data and update accordingly.', 1, 0, 11 ),
        (110, 114, 'SavedForecastSetup', 'ForecastID', '', 'Value in the field "ForecastID" is not valid', 1, 0, 4),
        (111, 115, 'AdminUserHeader', 'UserID', 'AND IsContact = 1', '', 1, 0, 4),
        (112, 116, 'AdminUserHeader', 'UserID', 'AND IsContact = 1', '', 1, 0, 4),
        (113, 117, 'AdminUserHeader', 'UserID', 'AND IsCertifying = 1', '', 1, 0, 4),
        (114, 118, '0|1', '', '', '', 1, 0, 7),
        (115, 119, '0|1', '', '', '', 1, 0, 7),
        (116, 120, '0|1', '', '', '', 1, 0, 7),
        (117, 121, '0|1', '', '', '', 1, 0, 7),
        (118, 122, '0|1', '', '', '', 1, 0, 7),
        (119, 123, '0|1', '', '', '', 1, 0, 7),
        (120, 125,'^[a-zA-Z]{1}[0-9]{7}$','','','Value in the field "ContractPBP" begin with "H" ,"R" or must be end with 7 digits number.', 1, 0, 8),
        (121, 125,'^.{8}$','','','ContractPBP must be 8 characters.', 1, 0, 8),
        (122, 126,'[0-9]{1,3}','','','SegID should contain only 3 numerical characters.', 1, 0, 8),
        (123, 130,'SavedRegionInfo','ActuarialRegion','','', 1, 0, 4),
        (124, 132,'LkpProductType','ProductType','','', 1, 0, 4),
        (125, 392, 'SavedForecastSetup', 'ForecastID', '', 'Value in the field "ForecastID" is not valid', 1, 0, 4),
        (126, 393, 'LkpIntBenefitCategory', 'BenefitCategoryName', 'AND IsUsed = 1  AND IsEnabled = 1', '', 1, 0, 4 ),
        (127, 394, '1|2', '', '', 'Value in the field "MARatingOptionID" Must be either 1 for Experience or 2 for Manual', 1, 0, 7 ),
        (128, 397,'^.{0,200}$','','','Reason For Adjustment must be <= 200 characters.', 1, 0, 8),
        (129, 403,'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (130, 404,'PackageIndex > 0','', '', 'Value must be > 0. Please review the data and update accordingly.', 1, 0, 11 ),
        (131, 404,'PackageIndex|BidServiceCategoryID|PricingComponentDescription', '', '','Violation of PRIMARY KEY constraint. Cannot insert duplicate key into PerIntOptionalPackageHeader. Please review the data and update accordingly.', 1, 0, 12),
        (132, 405,'PackageDetailID > 0', '', '', 'Value must be > 0. Please review the data and update accordingly.', 1, 0, 11),
        (133, 406,'LkpExtCMSBidServiceCategory', 'BidServiceCategoryID','', 'Value must be valid BidServiceCategoryID. Please review the data and update accordingly.', 1, 0, 4 ),
        (134, 407,'(!PricingComponentDescription.ToLower().Contains("dental") && BidServiceCategoryID != 24 && !PricingComponentDescription.ToLower().Contains("vision") && BidServiceCategoryID != 25)  || ((PricingComponentDescription.ToLower().Contains("dental") && BidServiceCategoryID == 24) ||(PricingComponentDescription.ToLower().Contains("vision") && BidServiceCategoryID == 25))', '','', 'BidServiceCategoryID is 24 Pricing Component Description must be Dental,or BidServiceCategoryID is 25 Pricing Component Description must be Vision"', 1, 0, 11 ),
        (135, 408,'AllowedUtilizationTypeID >= 0','','', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11 ),
        (136, 409,'AllowedUtilzationPer1000 >= 0','', '','Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11 ),
        (137, 410,'AllowedAverageCost >= 0','', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11 ),
        (138, 411,'V','', '','Value must be V. Please review the data and update accordingly.', 1, 0, 7 ),
        (139, 412,'EnrolleeCostShareUtilization >= 0','','', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11 ),
        (140, 413,'EnrolleeAverageCostShare >= 0','', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11 ),
        (141, 414,'AverageAdminExpense >= 0','', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11 ),
        (142, 416,'0|1', '', '', 'Value must be 0, or 1. Please review the data and update accordingly.', 1, 0, 7),
        (143, 417,'function','fnGetBidYear', '', '', 1, 0, 4),
        (144, 418,'PackageIndex', '', '', 'Violation of PRIMARY KEY constraint. Cannot insert duplicate key into PerIntOptionalPackageHeader. Please review the data and update accordingly.', 1, 0, 12),
        (145, 422,'TotalGainLoss > 0 || TotalGainLoss < 0', '', '', 'Value can be negative. Please review the data and update accordingly.', 1, 0, 11),
        (146, 423,'0','','', 'Value must be 0. Please review the data and update accordingly.', 1, 0, 7 ),
        (147, 424,'0|1','', '', 'Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (148, 425,'SavedForecastSetup','ForecastID','','Value in the field "ForecastID" is not valid',1,0,4),
        (149, 426,'^.{1,50}$','','','AddedBenefitName must be <= 50 characters.', 1, 0, 8),
        (150, 426,'LkpIntAddedBenefitType','AddedBenefitName','AND isEnabled = 1 AND BidServiceCatID = 35','Value in the field "AddedBenefitName" is not valid',1,0,4),
        (151, 392,'ForecastID|BenefitCategoryName|MARatingOptionID','','','',1,0,12),
        (152, 425,'ForecastID|AddedBenefitName','','','',1,0,12),
        (158, 427,'LKPIntBenefitCategory', 'BenefitCategoryID', ' ', 'Value in the field "BenefitCategoryID" is not valid', 1, 0, 4),
        (159, 429,'PlanYear|CPS|BaseMSBCode|BidAddedBenefitName', '', '', '', 1, 0, 12),
        (160, 432,'LkpIntAddedBenefitType', 'AddedBenefitName', '', 'Value in the field "BidAddedBenefitName" is not valid', 1, 0, 4),
        (164, 438, 'CMSRegion > 0 && CMSRegion < 255', '','', 'Value in the field "CMSRegion" should be 0 < CMSRegion < 255. Please review the data and update accordingly.', 1, 0, 11),
        (165, 439, 'LkpStateTerritory', 'StateTerritoryCD', '', 'Value in the field "States" is not valid.', 1, 0, 4),
        (166, 440, 'LIBenchmark >= 0 && LIBenchmark < 1000', '', '', 'Value in the field "LIBenchmark" should be 0 <= LIBenchmark < 1000. Please review the data and update accordingly.', 1, 0, 11),
        (187, 440, 'LIBenchmark < 200', '', '', 'Value in the field "LIBenchmark" is >= 200.', 1, 1, 11),
        (169, 441,'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (170, 442,'AddedBenefitTypeID|AddedBenefitCatID', '','','', 1, 0, 12),
        (171, 443,'LkpIntAddedBenefitCategory', 'AddedBenefitCatID', '', 'Value in the field AddedBenefitCatID is not valid Please review the data and update accordingly.', 1,0, 4),
        (172, 444,'0|1','', '', 'Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (173, 455,'0|1','', '', 'Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (174, 456,'0|1','', '', 'Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (175, 457,'0|1','', '', 'Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (176, 460,'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (177, 461,'AddedBenefitTypeID', '','','', 1, 0, 12),
        (178, 471,'0|1','', '', 'Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (179, 509,'0|1','', '', 'Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (180, 510,'UtilUnitP1000Trend >= 0', '', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11),
        (181, 511,'UtilBenefitPlanChange >= 0', '', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11),
        (182, 512,'UtilPopulationChange >= 0', '', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11),
        (183, 513,'UtilOtherFactor >= 0', '', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11),
        (184, 514,'UCAProviderPaymentChange >= 0', '', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11),
        (185, 515,'UCAOtherFactor >= 0', '', '', 'Value must be > or Equal to 0. Please review the data and update accordingly.', 1, 0, 11),
        (186, 526,'0|1','', '', 'Value must be 0. Please review the data and update accordingly.', 1, 0, 7 ),
        (188, 551,'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (189, 552,'AddedBenefitTypeID','','','',1,0,12),
        (190, 555,'0|1', '','','Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (191, 567,'ForecastID','','','',1,0,12),
        (192, 567,'ForecastID > 1','','','Value in the field ForecastID cannot be Null or less than 1. Please fix in the template and import again.', 1,0, 11),
        (193, 567,'SavedForecastSetup', 'ForecastID','', 'Value in the field ForecastID invalid not in SavedForecastSetup. Please fix in the template and import again.', 1,0, 4),
        (194, 568,'Convert.ToDecimal(PTBPremiumBuydown) >= 0M', '', '', 'Value in the field PTBPremiumBuydown should be greater than or equal to 0. Please fix in the template and import again.', 1,0, 11),
        (195, 568,'Convert.ToDecimal(PTBPremiumBuydown) <= Convert.ToDecimal(#ValidationParam2#)', 'SELECT TOP 1 PartBMaxRebateAllocation FROM PerExtCMSValues WHERE planyearid = dbo.fngetbidyear()', '', 'Value in the field PTBPremiumBuydown is >= #ValidationParam2# PartBMaxRebateAllocation .', 1,1, 11),
        (198, 430,'SavedPlanInfo', 'CPS', '', 'Value in the field "CPS" is not valid', 1, 0, 4 ),
        (200, 569, 'ForecastID','','','',1,0,12),
        (201, 569, 'ForecastID > 1','','','Value in the field ForecastID cannot be Null or less than 1. Please fix in the template and import again.', 1,0, 11),
        (202, 569, 'SavedPlanHeader', 'ForecastID','AND PlanTypeID = 3', 'Value in the field ForecastID invalid not RPPO. Please fix in the template and import again.', 1,0, 4),
        (203, 570, 'EstimatedPlanBidComponent >= 0', '','', 'Value in the field EstimatedPlanBidComponent must be >= 0. Please fix in the template and import again.', 1,0, 11),
        (204, 439,'States', '', '', '', 1, 0, 12),
        (208, 510,'UtilUnitP1000Trend != 0', '', '', 'Value in the field UtilUnitP1000Trend = 0.', 1, 1, 11),
        (209, 511,'UtilBenefitPlanChange != 0', '', '', 'Value in the field UtilBenefitPlanChange = 0.', 1, 1, 11),
        (210, 512,'UtilPopulationChange != 0', '', '', 'Value in the field UtilPopulationChange = 0.', 1, 1, 11),
        (211, 513,'UtilOtherFactor != 0', '', '', 'Value in the field UtilOtherFactor = 0.', 1, 1, 11),
        (212, 514,'UCAProviderPaymentChange != 0', '', '', 'Value in the field UCAProviderPaymentChange = 0.', 1, 1, 11),
        (213, 515,'UCAOtherFactor != 0', '', '', 'Value in the field UCAOtherFactor = 0.', 1, 1, 11),
        (214, 580, 'SavedForecastSetup', 'ForecastID', 'AND PlanYear = dbo.fnGetBidYear()',  'Value in the field "ForecastID" invalid not in SavedForecastSetup. Please fix in the template and import again.', 1, 0, 4),
        (215, 581, 'LkpVBID', 'RTRIM(replace(vbid,''VBID-'',''''))', '',  'Value in the field "VBID" is not valid.', 1, 0, 4),
        (216, 571, 'Trend_ProjProcess_CalcPlanAdjmt_BenCat', 'AdjustmentID', '', 'Value in the field "AdjustmentID" is not valid.', 1, 0, 4),
        (217, 573, 'SavedplanInfo', 'CPS', 'AND PlanYear = dbo.fnGetBidYear()', 'Value in the field "CPS" is not valid.', 1, 0, 4),
        (218, 574, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (219, 575, 'TrendYearID == #ValidationParam2# || TrendYearID == (#ValidationParam2# - 1 )', 'SELECT dbo.fnGetBidYear()', '', 'Value in the field "TrendYearID" is not valid.', 1, 0, 11 ),
        (220, 576, '1|2', '', '', 'Value must be 1 or 2. Please review the data and update accordingly.', 1, 0, 7),
        (221, 577, 'LkpIntBenefitCategory', 'BenefitCategoryID', '', 'Value in the field "BenefitCategoryID" is not valid.', 1, 0, 4),
        (222, 583, 'Trend_ProjProcess_CalcPlanAdjmt_RepCat', 'AdjustmentID', '', 'Value in the field "AdjustmentID" is not valid.', 1, 0, 4),
        (224, 585, 'SavedplanInfo', 'CPS', 'AND PlanYear = dbo.fnGetBidYear()', 'Value in the field "CPS" is not valid.', 1, 0, 4),
        (225, 586, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (226, 587, 'TrendYearID == #ValidationParam2# || TrendYearID == (#ValidationParam2# - 1 )', 'SELECT dbo.fnGetBidYear()', '', 'Value in the field "TrendYearID" is not valid.', 1, 0, 11 ),
        (227, 588, '1|2', '', '', 'Value must be 1 or 2. Please review the data and update accordingly.', 1, 0, 7),
        (228, 589, 'LkpIntBenefitCategory', 'ReportingCategory', 'AND ReportingCategory  is not null', 'Value in the field "ReportingCategory" is not valid.', 1, 0, 4),
        (229, 582, 'Delete =="Y" || Delete == "" || Delete == "y"', '', '', 'Value other than ''Y'' found in delete column. Records will only be removed if the value = ''Y''.', 1, 1, 11),
        (230, 571, 'AdjustmentID', '', '', '', 1, 0, 12 ),
        (231, 583, 'AdjustmentID', '', '', '', 1, 0, 12 ),
        (232, 584, 'AdjustmentDescription|CPS|PlanYearID|TrendYearID|RateType|ReportingCategory', '', '', '', 1, 0, 12 ),
        (233, 572, 'AdjustmentDescription|CPS|PlanYearID|TrendYearID|RateType|BenefitCategoryID', '', '', '', 1, 0, 12 ),
        (234, 600, 'SavedForecastSetup', 'ForecastID','','Value in the field invalid Forecast ID should exist in SavedForcastSetup. Please fix in the template and import again.', 1, 0, 4),
        (236, 601, '0 < BenefitOptionID  && BenefitOptionID < 255', '', '', 'Value in the field "BenefitOptionID" should be 0 < BenefitOptionID and < 255. Please review the data and update accordingly.', 1, 0, 11),
        (237, 604, '0|1','','','Value must be 0 or 1. Please review the data and update accordingly.', 1, 0, 7 ),
        (345, 606, 'SavedplanInfo', 'CPS', 'AND PlanYear = dbo.fnGetBidYear()', 'Value in the field "ContractPBP" is not valid.', 1, 0, 4),
        (347, 606, 'ContractPBP|Component|BenefitCategoryNumber', '', '', '', 1, 0, 12),
        (353, 610, '1|2', '', '', 'Value must be a  1 or 2. Please review the data and update accordingly.', 1, 0, 7),
        (354, 610, '', '', '', 'Each ContractPBP and Component must contain all BenefitCategoryNumbers.', 1, 0, 13),
        (352, 611, 'LkpIntBenefitCategory', 'BenefitCategoryID', 'AND ReportingCategory IS NOT NULL AND PlanYearID = dbo.fnGetBidYear()', 'Value in the field "BenefitCategoryNumber" is not valid.', 1, 0, 4),
        (348, 612, 'C2PMERUseMultAdj > 0 ', '', '', 'Value must be > 0 . Please review the data and update accordingly.', 1, 0, 11),
        (349, 613, 'C2PMERCostMultAdj > 0 ', '', '', 'Value must be > 0 . Please review the data and update accordingly.', 1, 0, 11),
        (350, 614, 'C2PMERUseAddAdj > 0 ', '', '', 'Value must be > 0 . Please review the data and update accordingly.', 1, 0, 11),
        (351, 615, 'C2PMERCostAddAdj > 0 ', '', '', 'Value must be > 0 . Please review the data and update accordingly.', 1, 0, 11),
        (355, 616, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (356, 617, 'SavedPlanHeader', 'ForecastID', 'AND BenefitOptionID > 1 AND IsLiveIndex = 1', 'Value in the field "ForecastID" is not valid and import not allowed for BenefitOptionID = 1 or IsLiveIndex <> 1. Please select a non-default option as IsLive and try again.', 1, 0, 4),
        (357, 618, '^.{1,40}$', '', '', 'PlanName must be <= 40 characters.', 0, 0, 8),
        (358, 626, 'LkpDeductTypeDesc', 'DeductTypeDesc', 'AND DeductTypeDesc IS NOT NULL', 'Value in the field "DeductTypeDesc" is not valid.', 1, 0, 4),
        (359, 627, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (360, 628, 'SavedPlanHeader', 'ForecastID', 'AND BenefitOptionID > 1 AND IsLiveIndex = 1', 'Value in the field "ForecastID" is not valid and import not allowed for BenefitOptionID = 1 or IsLiveIndex <> 1. Please select a non-default option as IsLive and try again.', 1, 0, 4),
        (361, 629, 'LkpIntBenefitCategory', 'BenefitCategoryID', 'AND ReportingCategory IS NOT NULL AND PlanYearID = dbo.fnGetBidYear() AND IsUsed = 1', 'Value in field "BenefitCategoryID" is not valid.', 1, 0, 4),
        (362, 630, 'LkpIntBenefitCategory', 'BenefitCategoryName', 'AND ReportingCategory IS NOT NULL AND PlanYearID = dbo.fnGetBidYear() AND IsUsed = 1', 'Value in field "BenefitCategoryID" is not valid.', 1, 0, 4),
        (363, 631, 'LkpIntBenefitOrdinal', 'BenefitOrdinalID', '', 'BenefitOrdinalID should be a positive numerical value in the range of 1 to 4.', 1, 0, 4),
        (364, 632, '0', '255', '', 'INBundleID should be a positive numerical value between 0 and 255 (inclusive).', 1, 0, 1),
        (365, 633, '((BenefitCategoryID != 65 && BenefitCategoryID != 66 && BenefitCategoryID != 83 && BenefitCategoryID != 131) && INBenefitDayRange.ToString() == "") ^ ((BenefitCategoryID == 65 || BenefitCategoryID == 66 || BenefitCategoryID == 131) && (INBenefitDayRange.ToString() == "" || (INBenefitDayRange >= 0 && INBenefitDayRange <= 150))) ^ (BenefitCategoryID == 83 && (INBenefitDayRange.ToString() == "" || (INBenefitDayRange >= 0 && INBenefitDayRange <= 100)))', '', '', 'Value in field "INBenefitDayRange" is not valid.', 1, 0, 11),
        (366, 619, 'INDeductible', '', '', '', 1, 0, 14),
        (367, 621, 'OONDeductible', '', '', '', 1, 0, 14),
        (368, 623, 'CombinedDeductible', '', '', '', 1, 0, 14),
        (369, 625, 'PartBDeductible', '', '', '', 1, 0, 14),
        (370, 620, 'INMOOP', '', '', '', 1, 0, 15),
        (371, 622, 'OONMOOP', '', '', '', 1, 0, 15),
        (372, 624, 'CombinedMOOP', '', '', '', 1, 0, 15),
        (373, 626, 'DeductTypeDesc', '', '', '', 1, 0, 16),
        (374, 621, 'OONDeductible', '', '', 'This is an HMO plan, so OON fields are expected to be empty.', 1, 0, 17),
        (375, 622, 'OONMOOP', '', '', 'This is an HMO plan, so OON fields are expected to be empty.', 1, 0, 17),
        (376, 623, 'CombinedDeductible', '', '', 'This is an HMO plan, so Combined fields are expected to be empty.', 1, 0, 17),
        (378, 624, 'CombinedMOOP', '', '', 'This is an HMO plan, so Combined fields are expected to be empty.', 1, 0, 17),
        (379, 637, 'OONBundleID', '', '', 'This is an HMO plan, so "OONBundleID" is expected to be 0.', 1, 0, 17),
        (380, 638, 'OONBenefitDayRange', '', '', 'This is an HMO plan, so OON fields are expected to be empty.', 1, 0, 17),
        (381, 639, 'OONCoinsurance', '', '', 'This is an HMO plan, so OON fields are expected to be empty.', 1, 0, 17),
        (382, 640, 'OONPerAdmitCopay', '', '', 'This is an HMO plan, so OON fields are expected to be empty.', 1, 0, 17),
        (383, 641, 'OONCopay', '', '', 'This is an HMO plan, so OON fields are expected to be empty.', 1, 0, 17),
        (384, 637, '0', '255', '', 'OONBundleID should be a positive numerical value between 0 and 255 (inclusive).', 1, 0, 1),
        (385, 639, '0', '1', '', 'Cost Share not valid. You may enter a value ranging from 0 to 1 in decimal format.', 1, 0, 1),
        (386, 640, 'OONPerAdmitCopay >= 0', '', '', 'Cost Share not valid. You may only enter a value >= 0.', 1, 0, 11),
        (387, 641, 'OONCopay >= 0', '', '', 'Cost Share not valid. You may only enter a value >= 0.', 1, 0, 11),
        (388, 639, 'OONCoinsurance', '', '', 'Cost Share not valid. You may only enter a value in one CS type cell.', 1, 0, 18),
        (389, 640, 'OONPerAdmitCopay', '', '', 'Cost Share not valid. You may only enter a value in one CS type cell.', 1, 0, 18),
        (390, 641, 'OONCopay', '', '', 'Cost Share not valid. You may only enter a value in one CS type cell.', 1, 0, 18),
        (391, 634, 'INCoinsurance', '', '', 'Cost Share not valid. You may only enter a value in one CS type cell.', 1, 0, 18),
        (392, 635, 'INPerAdmitCopay', '', '', 'Cost Share not valid. You may only enter a value in one CS type cell.', 1, 0, 18),
        (393, 636, 'INCopay', '', '', '', 1, 0, 19),
        (394, 634, 'INCoinsurance', '', '', '', 1, 0, 19),
        (395, 635, 'INPerAdmitCopay', '', '', '', 1, 0, 19),
        (396, 629, '', '', '', 'BenefitCategoryID and BenefitCategoryName must correspond to a matching ID and Name pairing.', 1, NULL, 20),
        (397, 617, 'Benefit Level Projected', '', '', 'There is not a corresponding Forecast ID in worksheet "Benefit Level Projected".', 1, NULL, 21),
        (398, 628, 'Plan Level Projected', '', '', 'There is not a corresponding Forecast ID in worksheet "Plan Level Projected".', 1, NULL, 21),
        (399, 634, 'INCoinsurance', '', '', '', 1, 0, 22),
        (400, 635, 'INPerAdmitCopay', '', '', '', 1, 0, 22),
        (401, 636, 'INCopay', '', '', '', 1, NULL, 22),
        (402, 639, 'OONCoinsurance', '', '', '', 1, 0, 22),
        (403, 640, 'OONPerAdmitCopay', '', '', '', 1, 0, 22),
        (404, 641, 'OONCopay', '', '', '', 1, 0, 22 ),
        (406, 642, 'LkpOrganization', 'OrganizationCode', '', 'Value in the field �Organization Code� was not found on the organization lookup table. Review the file for errors or contact support if the code needs to be added.', 1, 0, 4),
        (268, 645, '', '', '', '', 1, 0, 23 ),
        (271, 648, 'ContractPBPSegment', '', '', '', 1, 0, 12 ),
        (255, 655, 'Yes|No', '', '', '', 1, 0, 7 ),
        (256, 656, '^.{0,30}$', '', '', 'Value in the field "SNPType" must be less than 30 characters.', 1, 0, 8 ),
        (257, 657, '^.{0,50}$', '', '', 'Value in the field "DSNPSubType" must be less than 50 characters.', 1, 0, 8 ),
        (258, 658, '^.{0,30}$', '', '', 'Value in the field "Product" must be less than 30 characters.', 1, 0, 8 ),
        (259, 659, '^.{0,50}$', '', '', 'Value in the field "PlanCategory" must be less than 50 characters.', 1, 0, 8 ),
        (260, 660, '^.{0,100}$', '', '', 'Value in the field "TargetedSegment" must be less than 100 characters.', 1, 0, 8 ),
        (261, 661, 'Yes|No', '', '', '', 1, 0, 7 ),
        (262, 662, '^.{0,50}$', '', '', 'Value in the field "GivebackRange" must be less than 50 characters.', 1, 0, 8 ),
        (263, 663, '^.{0,100}$', '', '', 'Value in the field "INNMedicalDeductibleRange" must be less than 100 characters.', 1, 0, 8 ),
        (264, 664, 'Yes|No', '', '', '', 1, 0, 7 ),
        (265, 665, '^.{0,25}$', '', '', 'Value in the field "IsDelegated" must be less than 25 characters.', 1, 0, 8  ),
        (266, 666, '^.{0,11}$', '', '', 'Value in the field "NewPlanFlag" must be less than 11 characters.', 1, 0, 8  ),
        (267, 667, '^.{0,8}$', '', '', 'Value in the field "ConcurrentPlan" must be less than 8 characters.', 1, 0, 8 ),
        (249, 649, '^.{0,50}$', '', '', 'Value in the field "Division" must be less than 50 characters.', 1, 0, 8 ),
        (250, 650, '^.{0,50}$', '', '', 'Value in the field "Region" must be less than 50 characters.', 1, 0, 8 ),
        (251, 651, '^.{0,50}$', '', '', 'Value in the field "SubRegion" must be less than 50 characters.', 1, 0, 8 ),
        (252, 652, '^.{0,50}$', '', '', 'Value in the field "PrimaryState" must be less than 50 characters.', 1, 0, 8 ),
        (253, 653, '^.{0,50}$', '', '', 'Value in the field "Market" must be less than 50 characters.', 1, 0, 8 ),
        (254, 654, '^.{0,30}$', '', '', 'Value in the field "PlanType" must be less than 30 characters.', 1, 0, 8 ),
        (248, 647, 'BidYear|ContractPBPSegment|PlanYearID|PlanYearContractPBPSegment', '', '', '', 1, 0, 12 ),
        (247, 647, 'SavedplanInfo', 'CPS', 'AND PlanYear in(dbo.fnGetBidYear(),dbo.fnGetBidYear()-1,dbo.fnGetBidYear()-2)', 'PlanYearContractPBPSegment is not valid.', 1, 0, 4),
        (246, 646, 'PlanYearID == #ValidationParam2# || PlanYearID == (#ValidationParam2# - 1 ) || PlanYearID == (#ValidationParam2# - 2 )', 'SELECT dbo.fnGetBidYear()', '', 'Value in the field "PlanYearID" is not valid.', 1, 0, 11 ),
        (245, 645, 'SavedplanInfo', 'CPS', '', 'ContractPBPSegment is not valid.', 1, 0, 4),
        (244, 644, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (416, 671, 'Yes|No', '', '', '', 1, 0, 7 ),
        (407, 672, 'Yes|No', '', '', '', 1, 0, 7 ),
        (408, 673, 'Yes|No', '', '', '', 1, 0, 7 ),
        (409, 668, '^.{0,50}$', '', '', 'Value in the field "SubRegion" must be less than 50 characters.', 1, 0, 8 ),
        (410, 668, '^.{0,50}$', '', '', 'Value in the field "DSNPSubType" must be less than 50 characters.', 1, 0, 8 ),
        (411, 670, '^.{0,50}$', '', '', 'Value in the field "TargetedSegment" must be less than 50 characters.', 1, 0, 8 ),
        (412, 674, '^.{0,25}$', '', '', 'Value in the field "IsDelegated" must be less than 25 characters.', 1, 0, 8 ),
        (413, 37, '^.{0,100}$', '', '', 'Value in the field "Nickname" must be less than 100 characters.', 1, 0, 8 ),
        (414, 36, 'ForecastID', '', '', '', 1, 0, 12 ),
        (415, 36, 'SavedForecastSetup', 'ForecastID', 'AND PlanYear = dbo.fnGetBidYear() AND IsLiveIndex = 1', 'Invalid "ForecastID".', 1, 0, 4 ),
        (420, 600, 'ForecastID', 'BenefitOptionsID', 'IsLiveIndex', '', 1, 0, 24 ),
        (421, 601, 'BenefitOptionsID', '', '', '', 1, 0, 25),
        (422, 675, 'ForecastID|BenefitCategoryID','','','',1,0,12),
        (423, 675, 'ForecastID','', '', '', 1, 0, 26),
        (424, 676, 'LkpIntBenefitCategory', 'BenefitCategoryID','AND IsUsed = 1 AND IsEnabled = 1  AND BenefitCategoryID < 1000','Value in BenefitCategoryID is invalid. Please fix in the template and import again.', 1, 0, 4),
        (425, 677, 'INDistributionPercent >= 0 && INDistributionPercent <= 1','', '', 'Value invalid should be 0>= INDistributionPercent <= 1. Please review the data and update accordingly.', 1, 0, 11),
        (426, 677, 'INDistributionPercent', '','', '', 1, 0, 28),
        (427, 678, 'OONDistributionPercent >= 0 && OONDistributionPercent <= 1','', '', 'Value invalid should be 0>= OONDistributionPercent <= 1. Please review the data and update accordingly.', 1, 0, 11),
        (428, 678, 'OONDistributionPercent',  '','', 'Value in the field OONDistributionPercent is invalid, Please review the data and update accordingly.', 1, 0, 28),
        (429, 679, 'INCostFactor >= 0', '', '', 'Value in the field INCostFactor >= 0. Please review the data and update accordingly.', 1, 0, 11),
        (430, 679, 'INCostFactor', '', '', 'Value in the field INCostFactor is invalid. Please review the data and update accordingly.', 1, 0, 29),
        (431, 680, 'OONCostFactor >= 0','', '', 'Value in the field OONCostFactor >= 0. Please review the data and update accordingly.', 1, 0, 11),
        (432, 680, 'OONCostFactor','', '', 'Value in the field OONCostFactor is invalid. Please review the data and update accordingly.', 1, 0, 29),
        (433, 683, '"#ValidationParam2#".Split('','').Contains(PlanYear+"|"+CPS)', 'SELECT STRING_AGG(CAST(CONCAT(PlanYear,''|'',RTRIM(CPS)) AS NVARCHAR(MAX)), '','') FROM dbo.SavedPlanInfo WITH(NOLOCK);', '', 'Combination of PlanYear and CPS must exist in dbo.SavedPlanInfo.', 1, 0, 11 ),
        (434, 683, '^[a-zA-Z]{1}[0-9]{4}-[0-9]{3}-[0-9]{3}$', '', '', 'CPS must be contain 13 characters and valid format.', 1, 0, 8 ),
        (435, 684, '^[^\n]{1,50}$', '', '', 'SNPTypeDetail must be <= 50 characters.', 1, 0, 8 ),
        (436, 685, '0', '1000000000', '', 'MemberMonths not valid. You may enter a value ranging from 0 to 1000000000 in decimal format.', 1, 0, 1 ),
        (437, 686, '0', '1000000000', '', 'WtdRelativity not valid. You may enter a value ranging from 0 to 1000000000 in decimal format.', 1, 1, 1 ),
        (438, 683, 'PlanYear|CPS|SNPTypeDetail', '', '', 'Value(s) in column(s) PlanYear, CPS, SNPTypeDetail are not unique.', 1, 0, 12 ),
        (439, 633, 'INBenefitDayRange', '', '', '', 1, 0, 27),
        (440, 687, 'PlanYear|CPS|SSStateCountyCD', '', '', 'Value(s) in column(s) PlanYear, CPS, SSStateCountyCD are not unique.', 1, 0, 12 ),
        (441, 687, '"#ValidationParam2#".Split('','').Contains(PlanYear+"|"+CPS)', 'SELECT STRING_AGG(CAST(CONCAT(PlanYear,''|'',RTRIM(CPS)) AS NVARCHAR(MAX)), '','') FROM dbo.SavedPlanInfo WITH(NOLOCK);', '','PlanYear, CPS, field is invalid should exist in SavedPlanInfo. Please fix in the template and import again.', 1, 0, 11),
        (442, 688, '^[a-zA-Z]{1}[0-9]{4}-[0-9]{3}-[0-9]{3}$', '', '', 'CPS must be contain 13 characters and valid format.', 1, 0, 8 ),
        (443, 689, '^[^\n]{1,5}$', '', '', 'SSStateCountyCD must be <= 5 characters.', 1, 0, 8 ),
        (444, 690, '0','100000000', '','Value Allowed field is invalid should be greater than zero and less than 100000000. Please fix in the template and import again.', 1, 0, 1),
        (445, 691, '0','100000000', '','Value Units field is invalid should be greater than zero and less than 100000000. Please fix in the template and import again.', 1, 0, 1),
        (446, 692, 'PlanYear|CPS|SSStateCountyCD', '', '', 'Value(s) in column(s) PlanYear, CPS, SSStateCountyCD are not unique.', 1, 0, 12 ),
        (447, 692, '"#ValidationParam2#".Split('','').Contains(PlanYear+"|"+CPS)', 'SELECT STRING_AGG(CAST(CONCAT(PlanYear,''|'',RTRIM(CPS)) AS NVARCHAR(MAX)), '','') FROM dbo.SavedPlanInfo WITH(NOLOCK);', '','PlanYear, CPS, field is invalid should exist in SavedPlanInfo. Please fix in the template and import again.', 1, 0, 11),
        (448, 693, '^[a-zA-Z]{1}[0-9]{4}-[0-9]{3}-[0-9]{3}$', '', '', 'CPS must be contain 13 characters and valid format.', 1, 0, 8 ),
        (449, 694, '^[^\n]{1,5}$', '', '', 'SSStateCountyCD must be <= 5 characters.', 1, 0, 8 ),
        (450, 695, 'CMSMemberMonths >= 0 && CMSMemberMonths <= 1000000000','', '','Value CMSMemberMonths field is invalid should be greater than zero and less than 100000000. Please fix in the template and import again.', 1, 0, 11),
        (451, 696,  'AllowedNonSQS >= 0 && AllowedNonSQS <= 1000000000','','','Value Allowed AllowedNonSQS field is invalid should be greater than zero and less than 1000000000. Please fix in the template and import again.', 1, 1, 11),
        (452, 697, 'AllowedOtherPartB >= -1000000000 && AllowedOtherPartB <= 1000000000','', '','Value AllowedOtherPartB is invalid should beAllowedOtherPartB >= -1000000000 && AllowedOtherPartB <= 1000000000. Please fix in the template and import again.', 1, 0, 11),
        (453, 698, 'PlanYear|CPS|SSStateCountyCD', '', '', 'Value(s) in column(s) PlanYear, CPS, SSStateCountyCD are not unique.', 1, 0, 12 ),
        (454, 698, '"#ValidationParam2#".Split('','').Contains(PlanYear+"|"+CPS)', 'SELECT STRING_AGG(CAST(CONCAT(PlanYear,''|'',RTRIM(CPS)) AS NVARCHAR(MAX)), '','') FROM dbo.SavedPlanInfo WITH(NOLOCK);', '','PlanYear, CPS field is invalid should exist in SavedPlanInfo. Please fix in the template and import again.', 1, 0, 11),
        (455, 699, '^[a-zA-Z]{1}[0-9]{4}-[0-9]{3}-[0-9]{3}$', '', '', 'CPS must be contain 13 characters and valid format.', 1, 0, 8 ),
        (456, 700, '^[^\n]{1,5}$', '', '', 'SSStateCountyCD must be <= 5 characters.', 1, 0, 8 ),
        (457, 701, '0','1000000000', '','Value BaseYearRelativity field is invalid should be greater than zero and less than 100000000. Please fix in the template and import again.', 1, 0, 1),
        (466, 718, 'Convert.ToString(PlanType) == "MAPD" || Convert.ToString(PlanType) == "MA"', '', '', 'Value in the field PlanType must be  equal to MA Or MAPD. Please review the data and update accordingly.', 1, 0, 11),
        (467, 719, 'Convert.ToString(GrpIndvInD) == "INDIVIDUAL"', '', '', 'Value in the field GrpIndvInD must be  equal to INDIVIDUAL. Please review the data and update accordingly.', 1, 0, 11),
        (468, 720, 'SavedplanInfo', 'CPS', 'AND PlanYear in(dbo.fnGetBidYear()-2)', 'CPS is not valid.', 1, 0, 4),
        (469, 723, 'ArcLkpExtCMSStateCounty', ' CASE WHEN LEN(StateTerritoryID) = 1 THEN CONCAT(''0'', StateTerritoryID, CountyCode) ELSE CONCAT(StateTerritoryID, CountyCode)  END', 'AND PlanYearID = dbo.fnGetBidYear() - 2', 'SSStateCountyCd is not valid.', 1, 0, 4),
        (470, 729, '0|1|2|3|4|5|6|8|10|99', '', '', '', 1, 0, 7 ),
        (471, 731, '<60|60+', '', '', '', 1, 0,7 ),
        (472, 732, '0 <= MBR_CNT && MBR_CNT < 10000000', '', '', 'Value in the field MBR_CNT must be  >= 0 or < 10,000,000. Please review the data and update accordingly.', 1, 0, 11 ),
        (473, 712, '^.{0,50}$', '', '', 'Value in the field "RelatedParty" must be less than 50 characters.', 1, 0, 8 ),
        (474, 713, 'function', 'fnGetBidYear', '', '', 1, 0, 4 ),
        (475, 714, 'LkpIntAddedBenefitType', 'AddedBenefitName', '', 'Value in the field "AddedBenefitName" is not valid', 1, 0, 4 ),
        (477, 716, 'Totalz1 < 10000', '', '', 'Value must be < 10000 . Please review the data and update accordingly.', 1, 0, 11 ),
        (478, 717, 'Totalz2 < 10000', '', '', 'Value must be < 10000 . Please review the data and update accordingly.', 1, 0, 11 ),
        (479, 638, '((BenefitCategoryID != 65 && BenefitCategoryID != 66 && BenefitCategoryID != 83 && BenefitCategoryID != 131) && OONBenefitDayRange.ToString() == "") ^ ((BenefitCategoryID == 65 || BenefitCategoryID == 66 || BenefitCategoryID == 131) && (OONBenefitDayRange.ToString() == "" || (OONBenefitDayRange >= 0 && OONBenefitDayRange <= 150))) ^ (BenefitCategoryID == 83 && (OONBenefitDayRange.ToString() == "" || (OONBenefitDayRange >= 0 && OONBenefitDayRange <= 100)))', '', '', 'Value in field "OONBenefitDayRange" is not valid.', 1, 0, 11),
        (480, 629, 'NoBlankCostShareInputValidator', '', '', '', 1, 0, 30),
        (481, 733, 'SavedPlanHeader', 'ForecastID', 'AND PlanYearID = dbo.fnGetBidYear() AND isHidden = 0', 'Value in the field ForecastID is invalid, Please review the data and update accordingly.', 1, 0, 4),
	    (482, 734, '^0(\.([0-9]{1,6}))?$|^1(\.0{1,6})?$', '', '', 'Value in the field SecondaryPayerAdjustment should be SecondaryPayerAdjustment >=0 && SecondaryPayerAdjustment <= 1, Please review the data and update accordingly.', 1, 0, 8),
        (483, 735, 'SavedPlanHeader', 'ForecastID', 'AND PlanYearID = dbo.fnGetBidYear() AND isHidden = 0', 'Value in the field ForecastID is invalid, Please review the data and update accordingly.', 1, 0, 4),
        (484, 736, '^(?:-1000000(?:\.00?)?|1000000(?:\.00?)?|-?[0-9]{1,6}(?:\.[0-9]{1,2})?)$', '', '', 'Value in the field RxBasicPremium should be RxBasicPremium >=-1000000 && RxBasicPremium <= 1000000, Please review the data and update accordingly.', 1, 0, 8),
        (485, 737, '^(?:1000000(?:\.00?)?|[0-9]{1,6}(?:\.[0-9]{1,2})?)$', '', '', 'Value in the field RxSuppPremium should be RxSuppPremium >=0 && RxSuppPremium <= 1000000, Please review the data and update accordingly.', 1, 0, 8),
        (494, 743, '^.{0,50}$', '', '', 'Length of the value in the field "DeductTypeDesc" must be <= 50 characters.',1,  0, 8),
        (495, 744, '0|1', '', '', '',1,  0, 7),
        (496, 745, '0|1', '', '', '',1,  0, 7),
        (497, 746, '0|1', '', '', '',1,  0, 7),
        (498, 747, '0 <= INDeductLimit && INDeductLimit < 100000', '', '', 'Value in the field "INDeductLimit" should be >=0 and <100000.', 1, 0, 11),
        (499, 748, '0 <= OONDeductLimit && OONDeductLimit < 100000', '', '', 'Value in the field "INDeductLimit" should be >=0 and <100000.', 1, 0, 11),
        (500, 749, '0 <= COMBDeductLimit && COMBDeductLimit < 100000', '', '', 'Value in the field "COMBDeductLimit" should be >=0 and <100000.',1,  0, 11),
        (501, 750, '0 <= MOOPLimit && MOOPLimit < 100000', '', '', 'Value in the field "MOOPLimit" should be >=0 and <100000.', 1, 0, 11),
        (502, 751, '0|1', '', '', '',1, 0, 7),
        (503, 752, '0|1', '', '', '',1, 0, 7),
        (504, 753, '0|1', '', '', '',1, 0, 7),
        (505, 754, '0|1', '', '', '',1, 0, 7),
        (506, 755, '0|1', '', '', '',1, 0, 7),
        (507, 756, '0|1', '', '', '',1, 0, 7),
        (508, 757, 'function', 'fnGetBidYear', '', '', 1, 0, 4),
        (509, 758, 'LkpIntBenefitCategory', 'BenefitCategoryID', '', '', 1, 0, 4),
        (510, 759, 'LkpIntBenefitCategory', 'BenefitCategoryName', '', '', 1, 0, 4),
        (511, 760, 'LkpIntBenefitCategory', 'ReportingCategory', '', '', 1, 0, 4),
        (512, 761, '0|1', '', '', '',1,  0, 7),
        (513, 762, 'DeductTypeDescInDeductibleExclusion', '', '', '',1,  0, 9),
        (514, 763, '0|1', '', '', '', 1, 0, 7),
        (521,769, 'ForecastID', '', '', '', 1, 0, 12 ),
        (522,769, 'SavedForecastSetup', 'ForecastID', 'AND PlanYear = dbo.fnGetBidYear() AND isHidden = 0', 'Value invalid ForecastID must be in the SavedForecastSetup table. Please review the data and update accordingly.', 1, 0, 4),
        (523, 772, '^[hHrR]\d{4}-\d{3}-\d{3}$','','','CPS must contain 13 characters and should be in valid format.',1,0,8),
        (524, 772, 'SavedplanInfo', 'CPS', 'AND PlanYear = dbo.fnGetBidYear()', 'Value in the field CPS is invalid, Please review the data and update accordingly.', 1, 0, 4),
        (525, 773, 'TBCRoom > -10000 && TBCRoom < 10000','','','Value in the field TBCRoom should be TBCRoom > -10000 && TBCRoom < 10000, Please review the data and update accordingly.',1,0,11),
        (526, 774, 'BaselineDENOOPC >= 0 && BaselineDENOOPC < 10000','','','Value in the field BaselineDENOOPC should be BaselineDENOOPC >= 0 && BaselineDENOOPC < 10000, Please review the data and update accordingly.',1,0,11),
        (527, 775, 'BaselinePDOOPC >= 0 && BaselinePDOOPC < 10000','','','Value in the field BaselinePDOOPC should be BaselinePDOOPC >= 0 && BaselinePDOOPC < 10000, Please review the data and update accordingly.',1,0,11),
        (528, 776, 'MAOOPCTechAdj > -10000 && MAOOPCTechAdj < 10000','','','Value in the field MAOOPCTechAdj should be MAOOPCTechAdj > -10000 && MAOOPCTechAdj < 10000, Please review the data and update accordingly.',1,0,11),
        (529, 777, 'PDOOPCTechAdj > -10000 && PDOOPCTechAdj < 10000','','','Value in the field PDOOPCTechAdj should be PDOOPCTechAdj > -10000 && PDOOPCTechAdj < 10000, Please review the data and update accordingly.',1,0,11),
        (530, 778, 'dbo.LkpIntAddedBenefitType','Left(AddedBenefitName,6)','','Value in the field BenefitCode is invalid, Please review the data and update accordingly.',1,1,4),
        (531, 779, 'OOPC >= 0 && OOPC < 10000','','','Value in the field OOPC should be OOPC >= 0 && OOPC < 10000, Please review the data and update accordingly.',1,0,11),
        (532, 780, 'DeductibleLevel >= 0 && DeductibleLevel < 10000','','','Value in the field DeductibleLevel should be DeductibleLevel >= 0 && DeductibleLevel < 10000, Please review the data and update accordingly.',1,0,11),
        (533, 781, 'OOPCAdj > -10000 && OOPCAdj < 10000','','','Value in the field OOPCAdj should be OOPCAdj > -10000 && OOPCAdj < 10000, Please review the data and update accordingly.',1,0,11),
        (534, 794, 'SavedForecastSetup', 'ForecastID', 'AND PlanYear = dbo.fnGetBidYear() AND IsHidden = 0', 'Value in the field "ForecastID" invalid not in SavedForecastSetup. Please fix in the template and import again.', 1, 0, 4 ),
	    (535, 795,'^.{0,100}$','','','Bid Year Benefit String must be <= 100 characters.', 1, 0, 8),
	    (551, 786, 'SavedForecastSetup', 'ForecastID', 'AND ForecastID = ForecastID AND PlanYear = dbo.fnGetBidYear() AND isHidden = 0', 'Value invalid ForecastID must be in the SavedForecastSetup table. Please review the data and update accordingly.', 1 , 0, 4),
        (552, 787, '0 <= MedicaidProjectedRevenue && MedicaidProjectedRevenue <= 1000000', '', '', 'Value in the field "Medicaid Projected Revenue" should be >= 0 and <= 1000000.', 1,0,11),
        (553, 788, '0 <= MedicaidProjectedCostBenefitExpense && MedicaidProjectedCostBenefitExpense <= 1000000', '', '', 'Value in the field "Medicaid Projected Cost Benefit Expense" should be >= 0 and <= 1000000.', 1,0,11),
        (554, 789, '0 <= MedicaidProjectedCostNonBenefitExpense && MedicaidProjectedCostNonBenefitExpense <= 1000000', '', '', 'Value in the field "Medicaid Projected Cost Non Benefit Expense" should be >= 0 and <= 1000000.', 1,0,11),
		(555, 869, 'SavedForecastSetup', 'ForecastID', 'AND PlanYear = dbo.fnGetBidYear() AND isHidden = 0', 'Value invalid ForecastID must be in the SavedForecastSetup table. Please review the data and update accordingly.', 1, 0, 4),
        (556, 870, 'ForecastID|MSBCode', '', '', '', 1, 0, 12),
        (557, 870, 'LkpIntAddedBenefitType', 'LEFT(AddedBenefitName,6)', 'AND IsEnabled=1 AND BidServiceCatID <> 35', 'Value in the field MSBCode is invalid, Please review the data and update accordingly.', 1, 0, 4),
        (558, 871, 'LkpIntAddedBenefitType', 'AddedBenefitName', 'AND IsEnabled=1 AND BidServiceCatID <> 35', 'Value in the field AddedBenefitName is invalid, Please review the data and update accordingly.', 1, 0, 4),
        (559, 869, 'Convert.ToString(Override).ToLower() =="y" || Convert.ToString(Override) == "" ', '', '', 'Value other than ''Y'' found in Override column. Please review.', 1, 1, 11),
        (560, 869, 'Convert.ToString(Override) != "" || ( Convert.ToString(Override) == "" && Convert.ToString(MSBCode) != "")', '', '', 'Value in the field MSBCode should not be NULL, if Override != ''Y'', Please review the data and update accordingly.', 1, 0, 11),
        (561, 869, 'Convert.ToString(Override) == "" || ( Convert.ToString(Override).ToLower() =="y"  && Convert.ToString(AddedBenefitName) != "")', '', '','Value in the field AddedBenefitName should not be NULL, if Override = ''Y'', Please review the data and update accordingly.', 1, 0, 11),
	    (562,874,'ForecastID > 1','','','Value in the field ForecastID cannot be Null or less than 1.', 1,0, 11),
        (563,874,'ForecastID|OSBCode','','','ForecastID,OSBCode Combination must be unique',1,0,12),
        (564,874, 'SavedPlanHeader', 'ForecastID', 'AND PlanYearID = dbo.fnGetBidYear() AND isHidden = 0', 'Value in the field ForecastID is invalid,Please review the data and update accordingly.', 1, 0, 4),
        (565, 875, 'PerIntOptionalPackageHeader', 'SUBSTRING(Description, 1, 6)', '', 'Value in the field "OSBCode" is not valid', 1, 0, 4 ),
        (566, 876, 'PerIntOptionalPackageHeader', 'Name', '', 'Value in the field "OSBName" is not valid', 1, 0, 4 ),
	    (567, 796, '^.{5}$', '', '', 'Value in the field "ContractNumber" must be 5 characters long.', 1, 0, 8),
        (568, 796, '^(H|R).*', '', '', 'Value in the field "ContractNumber" must begin with "H" or "R".', 1, 0, 8),
        (569, 796, '\d{4}$', '', '', 'Value in the field "ContractNumber" must end with 4 digits.', 1, 0, 8),
	    (570, 797, '^\d{3}$', '', '', 'Value in the field "PlanID" must be 3 digits.', 1, 0, 8 ),
        (571, 797, '^(?:[0-7]?[0-9]{1,2}|799)$', '', '', 'Value in the field "PlanID" must be less than 800.', 1, 0, 8 ),
	(572, 798, '^\d{3}$', '', '', 'Value in the field "SegmentID" must be 3 digits.', 1, 0, 8 ),
	(573, 799, '1|2', '', '', 'Value must be a  1 or 2. Please review the data and update accordingly.', 1, 0, 7),
	(574, 800, 'LkpIntBenefitCategory', 'BenefitCategoryID', 'AND ReportingCategory IS NOT NULL AND PlanYearID = dbo.fnGetBidYear() AND IsUsed = 1', 'Value in the field "BenefitCategoryNumber" is not valid.', 1, 0, 4),
	(575, 796, '"#ValidationParam2#".Split('','').Contains(ContractNumber+"-"+PlanID+"-"+SegmentId)', 'SELECT STRING_AGG(CAST(CPS AS NVARCHAR(MAX)),'','') FROM dbo.SavedPlanInfo WITH(NOLOCK);', '', 'Combination of ContractNumber or PlanID or SegmentId must exist in dbo.SavedPlanInfo.', 1, 0, 11 ),
	(576, 796, 'ContractNumber|PlanID|SegmentId|Component|BenefitCategoryNumber', '', '', '', 1, 0, 12),
    (577, 888, 'SavedPlanHeader', 'ForecastID', 'AND PlanYearID = dbo.fnGetBidYear() AND isHidden = 0', 'Value in the field ForecastID is invalid, Please review the data and update accordingly.', 1, 0, 4),
		(578, 889, '(SELECT DISTINCT RIGHT(SSStateCountyCD,5) CountyID FROM LkpStateCounty UNION    SELECT ''0'' CountyID)  t', 'CountyID', '', '', 1, 0, 4),
		(579, 899, 'SavedPlanHeader', 'ForecastID', 'AND PlanYearID = dbo.fnGetBidYear() AND isHidden = 0', 'Value in the field ForecastID is invalid, Please review the data and update accordingly.', 1, 0, 4),
		(580, 900, '(SELECT DISTINCT RIGHT(SSStateCountyCD,5) CountyID FROM LkpStateCounty UNION    SELECT ''0'' CountyID)  t', 'CountyID', '', '', 1, 0, 4),
		(581, 901, 'NonDualExperienceRiskFactor > 0 && NonDualExperienceRiskFactor < 999', '', '', 'Value in the field "NonDualExperienceRiskFactor" should be > 0 and < 999.', 1, 0, 11),
		(582, 902, 'NonDualProjectionRiskFactor > 0 && NonDualProjectionRiskFactor < 999', '', '', 'Value in the field "NonDualProjectionRiskFactor" should be > 0 and < 999.', 1, 0, 11),
		(583, 903, 'DualExperienceRiskFactor > 0 && DualExperienceRiskFactor < 999', '', '', 'Value in the field "DualExperienceRiskFactor" should be > 0 and < 999.', 1, 0, 11),
		(584, 904, 'DualProjectionRiskFactor > 0 && DualProjectionRiskFactor < 999', '', '', 'Value in the field "DualProjectionRiskFactor" should be > 0 and < 999.', 1, 0, 11),
        (585, 46, '^\d{3}$', '', '', 'Value in the field "CountyCode" must be 3 digits.', 1, 0, 8  ),
        (586, 628, 'BenefitsMoopDeductAllBenefits', '', '', 'Please review the data and update accordingly.', 1, 0, 9);





        COMMIT TRANSACTION t1;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION t1;
        DECLARE @ErrorMessage NVARCHAR(2048),
                @ErrorSeverity INT,
                @ErrorState INT;
        SELECT @ErrorMessage = ERROR_MESSAGE(),
               @ErrorSeverity = ERROR_SEVERITY(),
               @ErrorState = ERROR_STATE();

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH;
END;
GO
