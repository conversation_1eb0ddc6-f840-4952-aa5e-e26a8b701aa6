SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetBidSummarySCT]   ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetBidSummarySCT
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Mar-19
-- HEADER UPDATED: 2010-Sep-21
--
-- DESCRIPTION: Function responsible for listing values on the Bid Summary tab in the MAPD Model for the SCT Allowed section.
--
-- PARAMETERS:
--	Input: 
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		CalcPlanExperienceByBenefitCat
--		Cal<PERSON>BenefitProjectionSCTCurrent
--      Cal<PERSON><PERSON><PERSON>fitProjectionSCTBid
--		SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetCredibilityFactor
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
---
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2018-Sep-18		1			Initial Version														Jordan Purdue		
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetBidSummarySCT]
    (
    @ForecastID INT
    )
RETURNS @Results TABLE 
    (
    ForecastID INT,
	SCTBaseExp DECIMAL(23,15),
	SCTBaseMan DECIMAL(23,15),
	SCTBaseBlend DECIMAL(23,15),
	SCTCurrentExp DECIMAL(23,15),
	SCTCurrentMan DECIMAL(23,15),
	SCTCurrentBlend DECIMAL(23,15),
	SCTProjExp DECIMAL(23,15),
	SCTProjMan DECIMAL(23,15),
	SCTProjBlend DECIMAL(23,15)
    ) AS

BEGIN  
    INSERT @Results    
        SELECT
            sph.ForecastID,
			SCTBaseExp = ISNULL(SCTBaseExp.SCTBaseExp,0),
			SCTBaseMan = ISNULL(SCTBaseMan.SCTBaseMan,0),
			SCTBaseBlend = dbo.fnGetCredibilityFactor(@ForecastID)*ISNULL(SCTBaseExp.SCTBaseExp,0) + (1-dbo.fnGetCredibilityFactor(@ForecastID))*ISNULL(SCTBaseMan.SCTBaseMan,0),
			SCTCurrentExp = ISNULL(SCTCurrentExp.SCTCurrentExp,0),
			SCTCurrentMan = ISNULL(SCTCurrentMan.SCTCurrentMan,0),
			SCTCurrentBlend = dbo.fnGetCredibilityFactor(@ForecastID)*ISNULL(SCTCurrentExp.SCTCurrentExp,0) + (1-dbo.fnGetCredibilityFactor(@ForecastID))*ISNULL(SCTCurrentMan.SCTCurrentMan,0),
			SCTProjExp = ISNULL(SCTProjExp.SCTCProjExp,0),
			SCTProjMan = ISNULL(SCTProjMan.SCTProjMan,0),
			SCTProjBlend = dbo.fnGetCredibilityFactor(@ForecastID)*ISNULL(SCTProjExp.SCTCProjExp,0) + (1-dbo.fnGetCredibilityFactor(@ForecastID))*ISNULL(SCTProjMan.SCTProjMan,0)
        FROM SavedPlanHeader sph WITH (NOLOCK)
		LEFT JOIN 
			(
			SELECT 
				ForecastID, 
				'SCTBaseExp' = SUM(AllowedWS1WRPP)  
			FROM CalcPlanExperienceByBenefitCat
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 1
			GROUP BY ForecastID
			) SCTBaseExp
			ON sph.ForecastID = SCTBaseExp.ForecastID		
		LEFT JOIN 
			(
			SELECT 
				ForecastID, 
				'SCTBaseMan' = SUM(AllowedWS1WRPP)  
			FROM CalcPlanExperienceByBenefitCat
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 2
			GROUP BY ForecastID
			) SCTBaseMan
			ON sph.ForecastID = SCTBaseMan.ForecastID
		LEFT JOIN
			(
			SELECT 
				ForecastID, 
				'SCTCurrentExp' = SUM(INAllowed) + SUM(OONAllowed)
			FROM CalcBenefitProjectionSCTCurrent
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 1
			GROUP BY ForecastID) SCTCurrentExp
			ON sph.ForecastID = SCTCurrentExp.ForecastID
		LEFT JOIN
			(
			SELECT 
				ForecastID, 
				'SCTCurrentMan' = SUM(INAllowed) + SUM(OONAllowed)
			FROM CalcBenefitProjectionSCTCurrent
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 2
			GROUP BY ForecastID) SCTCurrentMan
			ON sph.ForecastID = SCTCurrentMan.ForecastID
		LEFT JOIN
			(
			SELECT 
				ForecastID, 
				'SCTCurrentBlend' = SUM(INAllowed) + SUM(OONAllowed)
			FROM CalcBenefitProjectionSCTCurrent
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 3
			GROUP BY ForecastID) SCTCurrentBlend
			ON sph.ForecastID = SCTCurrentBlend.ForecastID
		LEFT JOIN
			(
			SELECT 
				ForecastID, 
				'SCTCProjExp' = SUM(INAllowed) + SUM(OONAllowed)
			FROM CalcBenefitProjectionSCTBid
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 1
			GROUP BY ForecastID) SCTProjExp
			ON sph.ForecastID = SCTProjExp.ForecastID
		LEFT JOIN
			(
			SELECT 
				ForecastID, 
				'SCTProjMan' = SUM(INAllowed) + SUM(OONAllowed)
			FROM CalcBenefitProjectionSCTBid
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 2
			GROUP BY ForecastID) SCTProjMan
			ON sph.ForecastID = SCTProjMan.ForecastID
		LEFT JOIN
			(
			SELECT 
				ForecastID, 
				'SCTProjBlend' = SUM(INAllowed) + SUM(OONAllowed)
			FROM CalcBenefitProjectionSCTBid
			WHERE ForecastID = @ForecastID AND DualEligibleTypeID = 2 AND MARatingOptionID = 3
			GROUP BY ForecastID) SCTProjBlend
			ON sph.ForecastID = SCTProjBlend.ForecastID
        WHERE
			sph.ForecastID = @ForecastID
    RETURN
END
GO
