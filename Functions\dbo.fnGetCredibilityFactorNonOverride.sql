SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetCredibilityFactorNonOverride]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetCredibilityFactorNonOverride
--
-- AUTHOR: <PERSON> Jacoby
--
-- CREATED DATE: 2011-Jan-18
-- HEADER UPDATED: 2011-Jan-18
--
-- DESCRIPTION: Computes the credibility factor for the specified plan.
--
-- PARAMETERS:
--  Input:
--		@ForecastID
--
-- RETURNS:
--    The computed credibility factor for the plan.
--
-- TABLES: 
--  Read:
--		Calc<PERSON>enchmarkSummary
--		PerExtCMSValues
--		SavedPlanBPTExceptionDetail
--		SavedPlanBPTExceptionHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnSignificantDigits
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-18		1			Initial version.													Nate Jacoby
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetCredibilityFactorNonOverride]
    (
    @ForecastID INT
    )
RETURNS FLOAT
AS
BEGIN
    DECLARE @MemberMonthsExperience INT
    DECLARE @MinimumCredibleMemberMonths INT
    DECLARE @CredibleMemberMonths INT
    DECLARE @CredibilityFactor FLOAT
    DECLARE @CredibilityFactorOverride FLOAT
	
    --Grab the base plan indicator from the exception table.
    SELECT
		@CredibilityFactorOverride = Credibility
    FROM SavedPlanBPTExceptionHeader 
    WHERE ForecastID = @ForecastID
        AND IsHidden = 0

    --Get the experience member months come from the appropriate source,
    --depending on the base plan status.
    SELECT @MemberMonthsExperience 
        = 
          (
          SELECT ISNULL(PlanExperienceMembership, 0) --actual member months 
          FROM CalcBenchmarkSummary
             WHERE ForecastID = @ForecastID
          )
    --The credible member months values are constants.
    SELECT
        @MinimumCredibleMemberMonths = MinimumCredibleMemberMonths,
        @CredibleMemberMonths = CredibleMemberMonths
    FROM PerExtCMSValues
    
    --Finally, compute the credibililty factor.
    SET @CredibilityFactor =
			CASE
				WHEN @MemberMonthsExperience > @CredibleMemberMonths
					THEN 1
				 WHEN @MemberMonthsExperience < @MinimumCredibleMemberMonths
					THEN 0
				ELSE
					ROUND((POWER((dbo.fnSignificantDigits(@MemberMonthsExperience, 15)
					/ dbo.fnSignificantDigits(@CredibleMemberMonths, 15)), 0.5)), 6)
			END

    SET @CredibilityFactor = 
			CASE ISNULL(@CredibilityFactor,0)
			    WHEN 0 THEN
                    @CredibilityFactorOverride
				ELSE @CredibilityFactor
			END    
    RETURN @CredibilityFactor
END
GO
