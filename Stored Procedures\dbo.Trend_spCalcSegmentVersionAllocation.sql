SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


--Function to allocate segment base data for plan level assumptions  
-- -------------------------------------------------------------------------------------------------------------------------  
-- DATE				VERSION		CHANGES MADE															DEVELOPER    
-- -------------------------------------------------------------------------------------------------------------------------  
-- 2013-Dec-11			1		Initial Build (modified from fn_MACTAPT_SegmentVersionAllocation		<PERSON>  
-- 2014-Feb-04			2		Speed Enhancement														<PERSON>  
-- 2014-Aug-19			3		Speed Enhancement														<PERSON>  
-- 2015-Jan-29			4		Added DISTINCT to account for base to bid combinations					Tony AMEG  
-- 2015-Jun-29			5		Changed to Stored Proc from function for speed							Deepali Mittal  
-- 2018-Nov-13			6		Removed references to MaRS and removed reference to the SAM table		Eric Zimmerman
-- 2020-Apr-07          7       Migrate from TPF to MAAModels and update references                     Andy Blink
-- 2020-Jul-14			8		Optimized BARC to MAAUI logic 											Ramandeep Saini
-- 2023-Aug-04			9		Added object schema, Internal Parameter	 								Sheetal Patil
-- -------------------------------------------------------------------------------------------------------------------------  

-------------------------------------------------------------------  
-- The purpose of this funciton is to allocate segment base data --  
-- for plan level normalization assumptions.  This is used for  --  
-- projecting normalization factors.        --  
-------------------------------------------------------------------  

CREATE PROCEDURE [dbo].[Trend_spCalcSegmentVersionAllocation]
--declare
@CPS VARCHAR(MAX)--='H5216-040-000,H5216-252-000'
AS

BEGIN
DECLARE @XCPS        VARCHAR(MAX) = @CPS

    --Drop table if it already exists  
    IF (SELECT  OBJECT_ID ('tempdb..#RESULTS')) IS NOT NULL DROP TABLE #RESULTS;


    CREATE TABLE #RESULTS
        (BaseCPS                CHAR(13)       NOT NULL
        ,CurrentCPS             CHAR(13)       NOT NULL
        ,BidCPS                 CHAR(13)       NOT NULL
        ,FutureCPS              CHAR(13)       NOT NULL
        ,SegVerAllocationFactor DECIMAL(18, 8) NOT NULL);


    BEGIN

        --Determine the number of years between the SAM's bid year and the  
        --data on the base year membership table  

        DECLARE @YearDiff SMALLINT;
        SET @YearDiff = 2;

        IF (SELECT  OBJECT_ID ('tempdb..#TempBaseCurrent')) IS NOT NULL
        DROP TABLE #TempBaseCurrent;
        --Create table   
        CREATE TABLE  #TempBaseCurrent(BaseYearCPBPS VARCHAR(13),CurrentYearCPBPS VARCHAR(13))
		INSERT INTO  #TempBaseCurrent
		 (
		     BaseYearCPBPS,
		     CurrentYearCPBPS
		 )
		 SELECT DISTINCT  BaseYearCPBPS
                         ,CurrentYearCPBPS
         FROM   dbo.vw_SAM_Crosswalks WITH(NOLOCK)
         WHERE  BaseYearCPBPS IS NOT NULL
         AND CurrentYearCPBPS IS NOT NULL
		 AND CurrentYearCPBPS IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@XCPS, ',', 1) )

	   SELECT BaseYearCPBPS AS BaseYearCPBPS 
	   INTO #tempFinal  
	   FROM  #TempBaseCurrent GROUP BY BaseYearCPBPS HAVING COUNT (CurrentYearCPBPS) > 1


 IF (SELECT  OBJECT_ID ('tempdb..#TempVersionCurrentBid')) IS NOT NULL
            DROP TABLE #TempVersionCurrentBid;
         CREATE TABLE  #TempVersionCurrentBid([Version] BIGINT,CurrentYearCPBPS VARCHAR(13),BidYearCPBPS  VARCHAR(13))
		 INSERT INTO  #TempVersionCurrentBid
		 (
		     Version,
		     CurrentYearCPBPS,
		     BidYearCPBPS
		 )
		SELECT DISTINCT
            [Version],
            CurrentYearCPBPS,
            BidYearCPBPS
            FROM   dbo.vw_SAM_Crosswalks  WITH (NOLOCK)
            WHERE  CurrentYearCPBPS IS NOT NULL
            AND BidYearCPBPS IS NOT NULL
	        AND CurrentYearCPBPS IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@XCPS, ',', 1) )

			SELECT [Version] AS [Version],CurrentYearCPBPS AS CurrentYearCPBPS  INTO #tempFinal1  
			FROM  #TempVersionCurrentBid GROUP BY CurrentYearCPBPS,[Version] HAVING COUNT (BidYearCPBPS) > 1
        --Create table   
        --Drop table if it already exists  
        IF (SELECT  OBJECT_ID ('tempdb..#SegmentedPlans')) IS NOT NULL
            DROP TABLE #SegmentedPlans;

        CREATE TABLE #SegmentedPlans
            (CPS VARCHAR(13));

        INSERT INTO #SegmentedPlans
        --Determine plans that segment in the current year  
        SELECT      DISTINCT
                    cc.CurrentYearCPBPS
        FROM        dbo.vw_SAM_Crosswalks cc WITH (NOLOCK)
       INNER JOIN   #tempFinal temp1 ON temp1.BaseYearCPBPS = cc.BaseYearCPBPS
	   WHERE cc.CurrentYearCPBPS IS NOT NULL 
        
                   
        UNION
        --Determine plans that segment in the bid year  
        SELECT      DISTINCT
                    cc.BidYearCPBPS
        FROM        dbo.vw_SAM_Crosswalks cc WITH (NOLOCK)
      INNER JOIN #tempFinal1 temp2 ON temp2.[Version] = cc.[Version] AND temp2.CurrentYearCPBPS = cc.CurrentYearCPBPS
	   WHERE       cc.BidYearCPBPS IS NOT NULL


        --This table is used to store results from the vw_SAM_Crosswalks  
        --Drop table if it already exists  
        IF (SELECT  OBJECT_ID ('tempdb..#CountyCrosswalk')) IS NOT NULL
            DROP TABLE #CountyCrosswalk;

        CREATE TABLE #CountyCrosswalk
            (BaseYearCPBPS    VARCHAR(13)
            ,CurrentYearCPBPS VARCHAR(13)
            ,BidYearCPBPS     VARCHAR(13)
            ,[CountyCode]     INT);

        INSERT INTO #CountyCrosswalk
        SELECT  DISTINCT
                BaseYearCPBPS
               ,CurrentYearCPBPS
               ,BidYearCPBPS
               ,[CountyCode]
        FROM    dbo.vw_SAM_Crosswalks  WITH (NOLOCK)
        WHERE   CurrentYearCPBPS IN (SELECT   CPS FROM #SegmentedPlans)
                OR  BidYearCPBPS IN (SELECT   CPS FROM #SegmentedPlans);
        --Calculate segment allocation percentages  


        INSERT INTO #RESULTS
        SELECT  Seg.BaseYearCPBPS AS 'BaseCPS'
               ,Seg.CurrentYearCPBPS AS 'CurrentCPS'
               ,Seg.BidYearCPBPS AS 'BidCPS'
               ,Seg.BidYearCPBPS AS 'FutureCPS'
               ,ISNULL (Seg.MembershipAllocationPct, 1) AS SegVerAllocationFactor
        FROM    (SELECT     se.BaseYearCPBPS
                           ,se.CurrentYearCPBPS
                           ,se.BidYearCPBPS
                           ,[SegmentMembership] / [PlanMembership] AS [MembershipAllocationPct]
                 FROM       (SELECT     BaseYearCPBPS
                                       ,CurrentYearCPBPS
                                       ,BidYearCPBPS
                                       ,SUM (CE.MemberMonths) AS [SegmentMembership]
                             FROM       dbo.Trend_CalcHistoricMembership CE WITH (NOLOCK)
                            INNER JOIN  (SELECT BaseYearCPBPS
                                               ,CurrentYearCPBPS
                                               ,BidYearCPBPS
                                               ,[CountyCode]
                                         FROM   #CountyCrosswalk
                                         WHERE  (BidYearCPBPS IS NOT NULL
                                                 AND BaseYearCPBPS IS NOT NULL)
                                                AND (BidYearCPBPS IN (SELECT    DISTINCT   CPS FROM   #SegmentedPlans)
                                                     OR CurrentYearCPBPS IN (SELECT DISTINCT CPS FROM  #SegmentedPlans))) vw
                                    ON vw.BaseYearCPBPS = CE.CPS
                                       AND  vw.[CountyCode] = CE.SSStateCountyCD
                             WHERE      CE.PlanYearID = (SELECT PlanYearID - @YearDiff FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1)
                             GROUP BY   BaseYearCPBPS
                                       ,CurrentYearCPBPS
                                       ,BidYearCPBPS) se --Bring in base plan's membership for the current year  
                INNER JOIN  (SELECT     ce.CPS
                                       ,SUM (ce.MemberMonths) AS [PlanMembership]
                             FROM       dbo.Trend_CalcHistoricMembership ce WITH (NOLOCK)--Limit plan membership to in-service area counties  
                            INNER JOIN  (SELECT BaseYearCPBPS
                                               ,[CountyCode]
                                         FROM   #CountyCrosswalk
                                         WHERE  (BidYearCPBPS IS NOT NULL
                                                 AND BaseYearCPBPS IS NOT NULL)
                                                AND (BidYearCPBPS IN (SELECT    DISTINCT   CPS FROM   #SegmentedPlans)
                                                     OR CurrentYearCPBPS IN (SELECT DISTINCT CPS FROM  #SegmentedPlans))) vw
                                    ON vw.BaseYearCPBPS = ce.CPS
                                       AND  vw.[CountyCode] = ce.SSStateCountyCD
                             WHERE      ce.PlanYearID = (SELECT PlanYearID - @YearDiff FROM dbo.LkpIntPlanYear WITH (NOLOCK) WHERE IsBidYear = 1)
                             GROUP BY   ce.CPS) pe
                        ON pe.CPS = se.BaseYearCPBPS) Seg
        --Exclude results without segmentations  
        WHERE   ISNULL (Seg.MembershipAllocationPct, 1) <> 1;


        SELECT  *
        FROM    #RESULTS
        WHERE   CAST(SegVerAllocationFactor AS VARCHAR) != '1';

    END;



END;
GO
