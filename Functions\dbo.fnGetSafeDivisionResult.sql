SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetSafeDivisionResult
--
-- AUTHOR: <PERSON> Lake 
--
-- CREATED DATE: 2008-Apr-18
--
-- DESCRIPTION: Function returns 0 if a divide by 0 error would normally have been returned.
--
-- PARAMETERS:
--	Input:
--		@Numerator float
--      @Denominator float
-- RETURNS: Table
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read:  NONE
--	Called:  NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE																	DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--  2008-Apr-18      1      	Initial version                     											Brian Lake
--  2008-Aug-08      2          Changed return from Float to Decimal                                            Brian Lake
--  2011-Jun-07		 3			Changed decimal return from (25,15) to (30,15)									Trevor Mahoney
--  2023-Aug-03      4          Added internal variable for parameters                                          Khurram Minhas/Sheetal 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE  FUNCTION [dbo].[fnGetSafeDivisionResult]
    (
    @Numerator float,
    @Denominator float
    )
RETURNS DECIMAL(30,15) AS
--RETURNS FLOAT AS
    BEGIN
	    DECLARE @XNumerator FLOAT = @Numerator;
		DECLARE @XDenominator FLOAT = @Denominator; 
        DECLARE @Result DECIMAL(30,15)

        IF ISNULL(@XDenominator,0)=0
		BEGIN 
            SET @Result=0
	    END 
        ELSE	
		BEGIN
            SET @Result=ISNULL(@XNumerator,0)/@XDenominator 
        END 
        RETURN @Result    
    END
GO
