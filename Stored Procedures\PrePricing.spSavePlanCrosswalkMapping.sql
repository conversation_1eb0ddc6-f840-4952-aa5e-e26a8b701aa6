SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSavePlanCrosswalkMapping]   
--    
-- AUTHOR: Sur<PERSON>y 
--    
-- CREATED DATE: 2024-Nov-08    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving cross walk types  
--    
-- PARAMETERS:    
-- Input: 
--

-- TABLES:   
-- PrePricing.PlanCrossWalkType 
-- PrePricing.PlanCrossWalkHeader 
-- PrePricing.PlanCrossWalkDetail 

-- Read:    
--  

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Nov-08		1		Initial Version                 Surya Murthy
-- 2025-Feb-29		2		Costshare field logic added     Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSavePlanCrosswalkMapping]
(
	@CrosswalkTypeID INT,
	@sourcePlaninfoID INT=0,
	@SourcePlans VARCHAR(MAX)=NULL,
	@TargetPlans VARCHAR(MAX)=NULL,
	@TerminationComments VARCHAR(MAX)=NULL,
	@TargetPlaninfoID INT=0,
	@CopyBenefitsFrom INT=0,
	@CopyServiceArea INT=0,
	@LastUpdateByID VARCHAR(200)
)
AS    
BEGIN    
	SET NOCOUNT ON
	BEGIN TRY
	BEGIN TRANSACTION crosswaksave		
	DECLARE @NewPlaninfoId INT;
	DECLARE @NewCrossWalkId INT;	
	DECLARE @maxCrosswalkId INT;	
	DECLARE	@OutPutResult VARCHAR(MAX);
	DECLARE	@OutPutResultCode VARCHAR(20);
	/*******COPY PLAN LOGIC********/
		IF @CrosswalkTypeID =1 
			BEGIN
				IF NOT EXISTS (SELECT 1 FROM PrePricing.PlanInfo WITH(NOLOCK) WHERE CPS=@TargetPlans)
				BEGIN
				--Step1: insert into Planinfo Table
					INSERT INTO PrePricing.PlanInfo
					(
					    MarketID,
					    DataSource,
					    PlanYear,
					    CPS,
					    PlanDescription,
					    PlanTypeID,
					    SNPTypeID,
					    ProductTypeID,
					    IsEnabled,
					    IsLocked,
					    LockedDate,
					    LockedBy,
					    LastUpdateByID,
					    LastUpdateDateTime
					)
					SELECT 
						 MarketID
						,DataSource
						,PlanYear
						,@TargetPlans
						,PlanDescription
						,PlanTypeID
						,SNPTypeID						
						,ProductTypeID						
						,1
						,NULL
						,NULL
						,NULL
						,@LastUpdateByID
						,GETDATE()
					FROM PrePricing.PlanInfo WHERE PlanInfoID = @sourcePlaninfoID;
					SELECT @NewPlaninfoId = SCOPE_IDENTITY();										
					--Step2: insert into PlanCrossWalkHeader Table							
					INSERT INTO PrePricing.PlanCrossWalkHeader
					(						
					    CrosswalkTypeID,
					    IsDeleted,
					    CrosswalkNotes,						
					    LastUpdateDateTime,
					    LastUpdateByID
					)
					VALUES
					(   						
						@CrosswalkTypeID,
						0,
					    NULL,								
					    GETDATE(),
					    @LastUpdateByID
					);
					SELECT @NewCrossWalkId = SCOPE_IDENTITY();	
					--Step3: insert into PlanCrossWalkDetail Table
					INSERT INTO PrePricing.PlanCrossWalkDetail
					(
						CrossWalkID,
						TargetPlanInfoID,
						ContributingPlanInfoID,
						IsPrimaryContributor,
						IsCurrentYearPlan
					)
					VALUES
					(   						
						@NewCrossWalkId,
						@NewPlaninfoId,
					    @sourcePlaninfoID,								
					    1,
					    1
					);
					--Step4: insert into MarketInputValue Table
					INSERT INTO PrePricing.MarketInputValue
					(
					    PlanInfoID,
					    SubcategoryID,
					    INValue,
					    OONValue,
					    BenefitChangeValue,
					    Note,
					    LastUpdateDateTime,
					    LastUpdateByID,
						INCostShareType,
						OONCostShareType
					)
					SELECT 
						@NewPlaninfoId,
						SubcategoryID,
						INValue,
						OONValue,
						BenefitChangeValue,
						Note,
						GETDATE(),
						@LastUpdateByID,
						INCostShareType,
						OONCostShareType
					FROM PrePricing.MarketInputValue WITH(NOLOCK)  WHERE PlanInfoID=@sourcePlaninfoID;

					--Step5: Copy Service area logic
					IF @CopyServiceArea = 1
					BEGIN
						INSERT INTO PrePricing.ServiceAreaMapping
						(
						    PlanInfoID,
						    SSStateCountyCD,
						    Notes,
						    LastUpdateDateTime,
						    LastUpdateByID
						)
						SELECT
							@NewPlaninfoId,
							a.SSStateCountyCD,
							NULL,
							GETDATE(),
							@LastUpdateByID
						FROM PrePricing.ServiceAreaMapping a WITH(NOLOCK) WHERE PlanInfoID=@sourcePlaninfoID;
					END

					--Send success message
					SET @OutPutResultCode='Success'
					SET @OutPutResult='Copied Plan Successfully.'
				END
				ELSE
				BEGIN
					SET @OutPutResultCode='Error'
					SET @OutPutResult='Cps:'+@TargetPlans+' Already Exists.'
				END
			END
		/*******Consolidation LOGIC********/
		ELSE IF @CrosswalkTypeID = 2
		BEGIN
			IF NOT EXISTS (
				SELECT 1 
				FROM Prepricing.PlanCrossWalkHeader h
				JOIN PrePricing.PlanCrossWalkDetail d
				ON h.CrossWalkID = d.crosswalkid
				WHERE IsDeleted = 0
				AND CrosswalkTypeID = 2
				AND TargetPlanInfoID = @TargetPlanInfoID
				)
				BEGIN


				--Step1: Disable contibuted plans except consolidated one
				UPDATE PrePricing.PlanInfo SET IsEnabled = 0, LastUpdateByID=@LastUpdateByID, LastUpdateDateTime=GETDATE()
				WHERE PlanInfoID IN(SELECT VALUE AS PlanInfoID FROM STRING_SPLIT(@SourcePlans,',') WHERE PlanInfoID<>@TargetPlaninfoID);
				UPDATE PrePricing.PlanInfo SET IsEnabled = 1, LastUpdateByID=@LastUpdateByID, LastUpdateDateTime=GETDATE() WHERE PlanInfoID  = @TargetPlaninfoID;

				--Step2: insert into PlanCrossWalkHeader Table							
				INSERT INTO PrePricing.PlanCrossWalkHeader
				(						
					CrosswalkTypeID,
					IsDeleted,
					CrosswalkNotes,						
					LastUpdateDateTime,
					LastUpdateByID
				)
				VALUES
				(   						
					@CrosswalkTypeID,
					0,
					NULL,								
					GETDATE(),
					@LastUpdateByID
				);
				SELECT @NewCrossWalkId = SCOPE_IDENTITY();	
				--Step3: insert into PlanCrossWalkDetail Table
				INSERT INTO PrePricing.PlanCrossWalkDetail
				(
					CrossWalkID,
					TargetPlanInfoID,				
					IsPrimaryContributor,
					IsCurrentYearPlan,
					ContributingPlanInfoID
				)
				SELECT 
					@NewCrossWalkId,
					@TargetPlaninfoID,														
					0,
					0,
					VALUE AS PlanInfoID FROM STRING_SPLIT(@SourcePlans,',');
				--set IsPrimaryContributor for primary plan			
				 UPDATE PrePricing.PlanCrossWalkDetail SET IsPrimaryContributor=1, IsCurrentYearPlan=1
				 WHERE CrossWalkID=@NewCrossWalkId AND ContributingPlanInfoID=@CopyBenefitsFrom;			 

				--Step4: Benefits logic
				IF @TargetPlaninfoID<>@CopyBenefitsFrom
				BEGIN
					--Delete existings benefits
					DELETE FROM PrePricing.MarketInputValue  WHERE PlanInfoID=@TargetPlaninfoID;
					--Now insert data back from selected benefit plan info id
					INSERT INTO PrePricing.MarketInputValue
						(
							PlanInfoID,
							SubcategoryID,
							INValue,
							OONValue,
							BenefitChangeValue,
							Note,
							LastUpdateDateTime,
							LastUpdateByID,
							INCostShareType,
							OONCostShareType
						)
						SELECT 
							@TargetPlaninfoID,
							SubcategoryID,
							INValue,
							OONValue,
							BenefitChangeValue,
							Note,
							GETDATE(),
							@LastUpdateByID,
							INCostShareType,
							OONCostShareType
						FROM PrePricing.MarketInputValue WITH(NOLOCK)  WHERE PlanInfoID=@CopyBenefitsFrom;
						END
				SET @OutPutResultCode='Success'
				SET @OutPutResult='Consolidation is done successfully.'
				END
			ELSE 
			BEGIN
				SET @OutPutResultCode='Error'
				SET @OutPutResult='Active consolidation already exist for target plan.'
			END
		END
		/*******Segmentation LOGIC********/
		ELSE IF @CrosswalkTypeID = 3
		BEGIN
			--Step1: insert into Planinfo table
			INSERT INTO PrePricing.PlanInfo
					(
					    MarketID,
					    DataSource,
					    PlanYear,					    
					    PlanDescription,
					    PlanTypeID,
					    SNPTypeID,
					    ProductTypeID,
					    IsEnabled,
					    IsLocked,
					    LockedDate,
					    LockedBy,
					    LastUpdateByID,
					    LastUpdateDateTime,
						CPS
					)
					SELECT 
						 MarketID
						,DataSource
						,PlanYear						
						,PlanDescription
						,PlanTypeID
						,SNPTypeID						
						,ProductTypeID						
						,1
						,NULL
						,NULL
						,NULL
						,@LastUpdateByID
						,GETDATE()
						,VALUE AS CPS FROM STRING_SPLIT(@TargetPlans,',') JOIN
					  PrePricing.PlanInfo ON PlanInfoID = @sourcePlaninfoID;

			UPDATE PrePricing.PlanInfo SET IsEnabled=0, LastUpdateByID=@LastUpdateByID, LastUpdateDateTime=GETDATE() WHERE PlanInfoID = @sourcePlaninfoID;			
			--Step2: insert into PlanCrossWalkHeader Table							
			INSERT INTO PrePricing.PlanCrossWalkHeader
			(						
				CrosswalkTypeID,
				IsDeleted,
				CrosswalkNotes,						
				LastUpdateDateTime,
				LastUpdateByID
			)
			VALUES
			(   						
				@CrosswalkTypeID,
				0,
				NULL,								
				GETDATE(),
				@LastUpdateByID
			);
			SELECT @NewCrossWalkId = SCOPE_IDENTITY();	
			--Step3: insert into PlanCrossWalkDetail Table
			INSERT INTO PrePricing.PlanCrossWalkDetail
			(
				CrossWalkID,
				TargetPlanInfoID,	
				ContributingPlanInfoID,
				IsPrimaryContributor,
				IsCurrentYearPlan				
			)
			SELECT 
				@NewCrossWalkId,					
				a.PlanInfoID,
				@sourcePlaninfoID,
				1,
				1
				FROM PrePricing.PlanInfo a WHERE cps IN (SELECT VALUE AS CPS FROM STRING_SPLIT(@TargetPlans,','))


			--Step4: insert into MarketInputValue table
			INSERT INTO PrePricing.MarketInputValue
					(
					    PlanInfoID,
					    SubcategoryID,
					    INValue,
					    OONValue,
					    BenefitChangeValue,
					    Note,
					    LastUpdateDateTime,
					    LastUpdateByID,
						INCostShareType,
						OONCostShareType
					)
					SELECT 
						b.PlanInfoID,
						a.SubcategoryID,
						a.INValue,
						a.OONValue,
						a.BenefitChangeValue,
						a.Note,
						GETDATE(),
						@LastUpdateByID,
						INCostShareType,
						OONCostShareType
					FROM PrePricing.MarketInputValue a WITH(NOLOCK)  
					CROSS JOIN (SELECT PlanInfoID FROM PrePricing.PlanInfo WHERE cps IN (SELECT VALUE AS CPS FROM STRING_SPLIT(@TargetPlans,','))) b
					WHERE a.PlanInfoID=@sourcePlaninfoID
			--Step5: Copy Service area logic
				IF @CopyServiceArea = 1
				BEGIN
					INSERT INTO PrePricing.ServiceAreaMapping
					(
						PlanInfoID,
						SSStateCountyCD,
						Notes,
						LastUpdateDateTime,
						LastUpdateByID
					)
					SELECT
						b.PlanInfoID,
						a.SSStateCountyCD,
						NULL,
						GETDATE(),
						@LastUpdateByID
					FROM PrePricing.ServiceAreaMapping a WITH(NOLOCK)  
					CROSS JOIN (SELECT PlanInfoID FROM PrePricing.PlanInfo WHERE cps IN (SELECT VALUE AS CPS FROM STRING_SPLIT(@TargetPlans,','))) b
					WHERE a.PlanInfoID=@sourcePlaninfoID				
				END
			--Send success message
			SET @OutPutResultCode='Success'
			SET @OutPutResult='Segmentation is Done Successfully.'
		END
		/*******Termination LOGIC********/
		ELSE IF @CrosswalkTypeID = 4
		BEGIN
		IF NOT EXISTS (SELECT 1 FROM PrePricing.PlanInfo WHERE IsEnabled=0 AND PlanInfoID = @sourcePlaninfoID)
		BEGIN
			--Step1: Disable plan
			UPDATE PrePricing.PlanInfo SET IsEnabled=0, LastUpdateByID=@LastUpdateByID, LastUpdateDateTime=GETDATE() WHERE PlanInfoID = @sourcePlaninfoID;

			--Step2: insert into PlanCrossWalkHeader Table							
			INSERT INTO PrePricing.PlanCrossWalkHeader
			(						
				CrosswalkTypeID,
				IsDeleted,
				CrosswalkNotes,						
				LastUpdateDateTime,
				LastUpdateByID
			)
			VALUES
			(   						
				@CrosswalkTypeID,
				0,
				@TerminationComments,								
				GETDATE(),
				@LastUpdateByID
			);
			SELECT @NewCrossWalkId = SCOPE_IDENTITY();	
			--Step3: insert into PlanCrossWalkDetail Table
			INSERT INTO PrePricing.PlanCrossWalkDetail
			(
				CrossWalkID,
				TargetPlanInfoID,
				ContributingPlanInfoID,
				IsPrimaryContributor,
				IsCurrentYearPlan
			)
			VALUES
			(   						
				@NewCrossWalkId,
				@sourcePlaninfoID,
				@sourcePlaninfoID,								
				1,
				1
			);
			--Send success message
			SET @OutPutResultCode='Success'
			SET @OutPutResult='Termination is Done Successfully.'
		END
		ELSE
		BEGIN
			SET @OutPutResultCode='Error'
			SET @OutPutResult='Plan Terminated already.'
		END
		END
		IF @CrosswalkTypeID =5 
		BEGIN
			/*******Plan Flip LOGIC********/
			DECLARE @previousCps VARCHAR(13);
			SELECT @previousCps = CPS FROM PrePricing.PlanInfo WHERE PlanInfoID=@sourcePlaninfoID;
			IF @previousCps<>@TargetPlans 
			BEGIN
				UPDATE PrePricing.PlanInfo SET CPS=@TargetPlans,LastUpdateByID=@LastUpdateByID,LastUpdateDateTime=GETDATE()
				WHERE PlanInfoID = @sourcePlaninfoID;		
				INSERT INTO PrePricing.PlanFlip
				(
					PlanInfoID,				
					PreviousCPS,
					NewCPS,
					LastUpdateByID,
					LastUpdateDateTime					    
				)
				VALUES
				(
					@sourcePlaninfoID,
					@previousCps,
					@TargetPlans,
					@LastUpdateByID,
					GETDATE()
				);
				--Send success message
				SET @OutPutResultCode='Success'
				SET @OutPutResult='Plan Flip is Done Successfully.'
			END
			ELSE
			BEGIN
				--Send success message
				SET @OutPutResultCode='Error'
				SET @OutPutResult='CPS Name should be diffrent.'
			END
		END
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT TRANSACTION crosswaksave;
	END TRY
	BEGIN CATCH		
		DECLARE @ErrorMessage NVARCHAR(4000);                    
        DECLARE @ErrorSeverity INT;                    
        DECLARE @ErrorState INT;                    
        DECLARE @ErrorException NVARCHAR(4000);                    
		DECLARE @errSrc VARCHAR(MAX) =ISNULL( ERROR_PROCEDURE(),'SQL')                    
		DECLARE @currentdate DATETIME=GETDATE()                    
		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
		' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)                
		ROLLBACK TRANSACTION crosswaksave;       
		SET @OutPutResult=@ErrorMessage;	
		SET @OutPutResultCode='Error';
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH

END
GO
