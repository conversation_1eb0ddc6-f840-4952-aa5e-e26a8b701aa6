SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSaveCrossWalkCurrentYearPlan]
--    
-- AUTHOR: Surya <PERSON>rthy 
--    
-- CREATED DATE: 2024-Dec-18    
-- Type: 
-- DESCRIPTION: Procedure responsible to setup cross wak current year plan
--    
-- PARAMETERS:    
-- Input: 
-- @CrossWalkID
-- @InputCPS
-- @LastUpdatedByUserId


-- TABLES:   
--  

-- Read:    
--  

-- Write:    
--    PrePricing.PlanNote
--
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Dec-18		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSaveCrossWalkCurrentYearPlan]
(
	@CrossWalkID INT,
	@InputCPS VARCHAR(13),
	@LastUpdatedByUserId VARCHAR(200)	
)
AS    
BEGIN    
	BEGIN TRY
	BEGIN TRANSACTION saveCurrentYearCPS	
		DECLARE	@OutPutResultCode VARCHAR(20)
	    DECLARE @OutPutResult VARCHAR(200) 
		SET @OutPutResultCode='Success'
		SET @OutPutResult = 'Current Year Plan Saved Successfully.'	
		UPDATE PrePricing.PlanCrossWalkHeader SET LastUpdateByID=@LastUpdatedByUserId, LastUpdateDateTime=GETDATE() WHERE CrossWalkID=@CrossWalkID;
		-- First revert exisitng currnet plan year
		UPDATE PrePricing.PlanCrossWalkDetail SET IsCurrentYearPlan=0 WHERE CrossWalkID=@CrossWalkID;
		-- Update with latest entry for currnet plan year from cross walk info screen 
		UPDATE pcd
		SET pcd.IsCurrentYearPlan=1 
		FROM PrePricing.PlanCrossWalkDetail pcd
		JOIN PrePricing.PlanInfo spi ON spi.PlanInfoID=pcd.ContributingPlanInfoID AND pcd.CrossWalkID=@CrossWalkID
		WHERE spi.CPS=@InputCPS;
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT TRANSACTION saveCurrentYearCPS
	END TRY
	BEGIN CATCH	
		ROLLBACK TRANSACTION saveCurrentYearCPS;
		SET @OutPutResultCode='Error'
		SET @OutPutResult = 'Error While Saving Current Year Plan.'	
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage		
	END CATCH
END
GO
