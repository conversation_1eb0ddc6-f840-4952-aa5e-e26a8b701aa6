SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


/****** Object:  UserDefinedFunction [dbo].[fnGetMAReportGeoFilter]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportGeoFilter
--
-- AUTHOR: <PERSON><PERSON><PERSON>    
--
-- CREATED DATE: 2009-Mar-20
-- HEADER UPDATED: 2016-Nov-17
--
-- DESCRIPTION:   Get list of Geo Indicators for MAMBA MAReport feature
--              Test: select * from fnGetMAReportGeoFilter(2010,3)
--
-- PARAMETERS:
--	Input:
--      @PlanYearID
--      @GeoFilterTypeId (1-Divisional, 2-Regional)
--  Output:
--
-- TABLES: 
--	Read:
--      ArcSavedDivisionHeader
--      ArcSavedRegionHeader
--      SavedDivisionHeader
--      SavedRegionHeader  
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-20		1		    Initial Version										                Aleksey Titievsky
-- 2010-Nov-05      2           Added Arc tables                                                    Joe Casey
-- 2016-Nov-17		3			Removed GeoFilterType 3 (Market)									Chris Fleming
-- 2019-Aug-05      4           Removed reference of Arch tables                                    Kiran Pant
-- 2019-Sep-10      5           Removed @PlanYearID parameter                                       Pooja Dahiya
-- 2019-Oct-16      6           Revert Removed reference of Arch tables                             Manisha Tyagi
-- 2019-Oct-22      7           Added parameter @PlanYearID to avoid duplicate data					Pooja Dahiya                             
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportGeoFilter]
    (
    @PlanYearID SMALLINT,
    @GeoFilterTypeID TINYINT
    )
RETURNS TABLE
AS    
RETURN
	SELECT
	    GeoFilterID,
	    GeoFilterName
	FROM
        ( 
        SELECT
            GeoFilterID = dh.DivisionID  ,
            GeoFilterName = dh.DivisionName
        FROM (SELECT DISTINCT ActuarialDivisionID AS DivisionID,ActuarialDivision AS DivisionName,   dbo.fnGetBidYear() as PlanYearID  FROM dbo.SavedDivisionInfo 
		UNION SELECT DISTINCT DivisionID,DivisionName,PlanYearID FROM dbo.ArcSavedDivisionHeader) dh
        WHERE  dh.PlanYearID  = @PlanYearID AND @GeoFilterTypeID = 1 
        UNION
        SELECT
            GeoFilterID = rh.HumanaRegionID,
            GeoFilterName = rh.HumanaRegionName
        FROM (SELECT DISTINCT ActuarialRegionID AS HumanaRegionID,ActuarialRegion AS HumanaRegionName,   dbo.fnGetBidYear() as PlanYearID  FROM dbo.SavedRegionInfo 
		UNION SELECT DISTINCT HumanaRegionID,HumanaRegionName,PlanYearID FROM dbo.ArcSavedRegionHeader) rh
        WHERE  rh.PlanYearID  = @PlanYearID AND @GeoFilterTypeID = 2
        ) G
GO
