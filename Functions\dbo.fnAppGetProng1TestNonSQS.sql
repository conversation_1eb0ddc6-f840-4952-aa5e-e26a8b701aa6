SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:	fnAppGetProng1TestNonSQS
--
-- CREATOR:			Nate Jacoby
--
-- CREATED DATE:	2014-MAR-06
--
-- DESCRIPTION:		Displays info used in the Non Sequestered Prong 1 Test on the 
--					Actuarial Summary tab.
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT
--
--  Output :		ServiceCategory VARCHAR(MAX),
--					Humana VARCHAR(10),
--					CMS VARCHAR(10),
--					PassFail VARCHAR(4)
--
-- TABLES : 
--	Read :			PerExtProng1Factor
--					LkpExtCMSBidServiceCategory
--					CalcBenefitProjectionPreMbrCS
--					LkpIntBenefitCategory
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnAppGetMABPTWS3
--					fnAppGetMABPTWS4Percent
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2014-MAR-06		1			Initial Version														Nick Koesters
-- 2014-MAR-10		2			Modified table Name													Mike Deren
-- 2015-APR-08      3			Home Health is no longer being tested, so 'c.' was removed from     Chris McAuley
--								ServiceCategoryCode lines, @HHfactor lines are commented out. The 
--								@IPfactor and @SNFfactor variables were also updated for 2016 Bid Season.
--								CMS lines were also commented out pertaining to 'c.'
-- 2016-MAY-08		4			Updated for 2017 Bid Season factors (IP, SNF)						Mark Freel
-- 2016-AUG-12		5			Updated hardcoded factor values to pull from PerExtProng1Factor		Chris Fleming
-- 2017-FEB-28      6			SNF is no longer being tested, so 'b.' was removed from				Chris Fleming
--								ServiceCategoryCode lines, @SNFfactor lines are commented out. 
--								CMS lines were also commented out pertaining to 'b.'
-- 2017-APR-17		7			Revert prior SNF changes											Chris Fleming
-- 2024-OCT-07		8			Cost Share Basis: add handling on IsIncludeInCostShareBasis flag
--									in the base data table, so that we're only pulling in Allowed
--									used in the Cost Share basis									Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnAppGetProng1TestNonSQS]
    (@ForecastID INT)

RETURNS @RESULTS TABLE
    (ServiceCategory VARCHAR(MAX)
    ,Humana          VARCHAR(10)
    ,CMS             VARCHAR(10)
    ,PassFail        VARCHAR(4))
AS
    BEGIN

        -- ------------------------------------------------------------------------------------------------------------------
        -- Variables
        -- ------------------------------------------------------------------------------------------------------------------
        DECLARE @XForecastID INT = @ForecastID;

        -- CMS Values
        DECLARE @IPfactor DECIMAL(6, 4);
        DECLARE @SNFfactor DECIMAL(6, 4);
        DECLARE @DMEfactor DECIMAL(6, 4);
        DECLARE @PartBRxfactor DECIMAL(6, 4);

        SET @IPfactor = (SELECT Prong1FactorValue
                         FROM   dbo.PerExtProng1Factor
                         WHERE  Prong1FactorName = 'IP');
        SET @SNFfactor = (SELECT    Prong1FactorValue
                          FROM      dbo.PerExtProng1Factor
                          WHERE     Prong1FactorName = 'SNF');
        SET @DMEfactor = (SELECT    Prong1FactorValue
                          FROM      dbo.PerExtProng1Factor
                          WHERE     Prong1FactorName = 'DME');
        SET @PartBRxfactor = (SELECT    Prong1FactorValue
                              FROM      dbo.PerExtProng1Factor
                              WHERE     Prong1FactorName = 'PartBRx');

        --Hardcodes
        DECLARE @DollarSign CHAR(1) = '$';
        DECLARE @Fail CHAR(1) = 'F';
        DECLARE @Pass CHAR(1) = 'P';
        DECLARE @A CHAR(2) = 'a.'; --IP
        DECLARE @B CHAR(2) = 'b.'; --SNF
        DECLARE @E CHAR(2) = 'e.'; --DME/Prosthetics/Supplies
        DECLARE @J CHAR(2) = 'j.'; --Part B Rx

        --PercentCovered
        DECLARE @PercentCovered TABLE
            (ForecastID              INT
            ,ServiceCategoryCode     CHAR(2)
            ,PercentCoveredAllowed   DECIMAL(7, 6)
            ,PercentCoveredCostShare DECIMAL(7, 6)
            ,FFSActEquivCostShare    DECIMAL(7, 6));

        INSERT INTO @PercentCovered
            (ForecastID
            ,ServiceCategoryCode
            ,PercentCoveredAllowed
            ,PercentCoveredCostShare
            ,FFSActEquivCostShare)
        SELECT  ForecastID
               ,ServiceCategoryCode
               ,PercentCoveredAllowed
               ,PercentCoveredCostShare
               ,FFSActEquivCostShare
        FROM    dbo.fnAppGetMABPTWS4Percent (@XForecastID)
        WHERE   ServiceCategoryCode IN (@A, @B, @E, @J)
                AND DualEligibleTypeID = 0;

        --Cost Share
        DECLARE @CostShare TABLE
            (ForecastID          INT
            ,ServiceCategoryCode CHAR(2)
            ,ServiceCategory     VARCHAR(50)
            ,TotalCostShare      DECIMAL(20, 12));

        INSERT INTO @CostShare
            (ForecastID
            ,ServiceCategoryCode
            ,ServiceCategory
            ,TotalCostShare)
        SELECT      ws3.ForecastID
                   ,bsc.ServiceCategoryCode
                   ,bsc.ServiceCategory
                   ,SUM (ws3.INCostShare + ws3.OONCostShare) AS TotalCostShare
        FROM        dbo.fnAppGetMABPTWS3 (@XForecastID) ws3 --Grab NonSQS Cost Share values
       INNER JOIN   dbo.LkpExtCMSBidServiceCategory bsc
               ON bsc.CostShareServiceCategoryCode = ws3.CostShareServiceCategoryCode
                  AND   bsc.ServiceCategory = ws3.ServiceCategory
                  AND   bsc.SubServiceCategory = ws3.SubServiceCategory
        WHERE       bsc.ServiceCategoryCode IN (@A, @B, @E, @J) --'c.' removed because Home Health is no longer being tested
        GROUP BY    ws3.ForecastID
                   ,bsc.ServiceCategoryCode
                   ,bsc.ServiceCategory;

        --Allowed
        DECLARE @Allowed TABLE
            (ForecastID          INT
            ,ServiceCategoryCode CHAR(2)
            ,ServiceCategory     VARCHAR(50)
            ,TotalAllowedNonSQS  DECIMAL(20, 12));

        INSERT INTO @Allowed
            (ForecastID
            ,ServiceCategoryCode
            ,ServiceCategory
            ,TotalAllowedNonSQS)
        SELECT      cbb.ForecastID
                   ,bsc.ServiceCategoryCode
                   ,bsc.ServiceCategory
                   ,SUM (cbb.INAllowed + cbb.OONAllowed) AS TotalAllowedNonSQS
        FROM        dbo.CalcBenefitProjectionPreMbrCS cbb
       INNER JOIN   dbo.LkpIntBenefitCategory bc
               ON cbb.BenefitCategoryID = bc.BenefitCategoryID
       INNER JOIN   dbo.LkpExtCMSBidServiceCategory bsc
               ON bc.BidServiceCatID = bsc.BidServiceCategoryID
        WHERE       cbb.ForecastID = @XForecastID
                    AND cbb.DualEligibleTypeID = 0 --Non-DE#
                    AND cbb.MARatingOptionID = 3 --Blended
                    AND bsc.ServiceCategoryCode IN (@A, @B, @E, @J)
                    AND cbb.IsIncludeInCostShareBasis = 1   --Only include allowed from the cost share basis
        GROUP BY    cbb.ForecastID
                   ,bsc.ServiceCategoryCode
                   ,bsc.ServiceCategory


        -- ------------------------------------------------------------------------------------------------------------------
        -- Declare @TEMPTABLE to hold info for Prong 1 test
        -- ------------------------------------------------------------------------------------------------------------------
        DECLARE @TEMPTABLE TABLE
            (ForecastID           INT
            ,ServiceCategoryCode  VARCHAR(2)
            ,ServiceCategory      VARCHAR(MAX)
            ,PlanCSMedCoveredSVCS DECIMAL(27, 22)
            ,AllowedPMPM          DECIMAL(27, 22)
            ,FFSAECostShare       DECIMAL(27, 22));
        -- ------------------------------------------------------------------------------------------------------------------
        -- Get info to use in Prong 1 test
        -- ------------------------------------------------------------------------------------------------------------------
        INSERT INTO @TEMPTABLE
        SELECT      ProngNonSQS.ForecastID
                   ,ProngNonSQS.ServiceCategoryCode
                   ,ProngNonSQS.ServiceCategory
                   ,PlanCSMedCoveredSVCS = SUM (ProngNonSQS.TotalCostShare * ProngNonSQS.PercentCoveredCostShare)
                   ,AllowedPMPM = SUM (ProngNonSQS.TotalAllowedNonSQS * ProngNonSQS.PercentCoveredAllowed)
                   ,FFSAECostShare = SUM (
                                     ProngNonSQS.TotalAllowedNonSQS * ProngNonSQS.PercentCoveredAllowed
                                     * ProngNonSQS.FFSActEquivCostShare)
        FROM        (SELECT     CostShare.ForecastID
                               ,CostShare.ServiceCategoryCode
                               ,CostShare.ServiceCategory
                               ,CostShare.TotalCostShare
                               ,TotalAllowed.TotalAllowedNonSQS
                               ,PercentCovered.PercentCoveredAllowed
                               ,PercentCovered.PercentCoveredCostShare
                               ,PercentCovered.FFSActEquivCostShare
                     FROM       @CostShare CostShare
                    INNER JOIN  @Allowed TotalAllowed
                            ON CostShare.ServiceCategoryCode = TotalAllowed.ServiceCategoryCode
                               AND CostShare.ServiceCategory = TotalAllowed.ServiceCategory
                    INNER JOIN  @PercentCovered PercentCovered
                            ON PercentCovered.ServiceCategoryCode = CostShare.ServiceCategoryCode) ProngNonSQS
        GROUP BY    ProngNonSQS.ForecastID
                   ,ProngNonSQS.ServiceCategoryCode
                   ,ProngNonSQS.ServiceCategory;


        -- ------------------------------------------------------------------------------------------------------------------
        -- Returns table with Pass or Fail, based on CMS guidelines
        -- ------------------------------------------------------------------------------------------------------------------
        INSERT INTO @RESULTS
        SELECT      ServiceCategory
                   ,Humana = @DollarSign + CAST(CAST(ROUND (PlanCSMedCoveredSVCS, 2) AS DECIMAL(8, 2)) AS VARCHAR)
                   ,CMS = CASE ServiceCategoryCode WHEN @A -- InPatient Facility
                   THEN                                @DollarSign
                                                       + CAST(CAST(ROUND (FFSAECostShare * @IPfactor, 2) AS DECIMAL(8, 2)) AS VARCHAR)
                                                   WHEN @B -- SNF
                   THEN                                @DollarSign
                                                       + CAST(CAST(ROUND (FFSAECostShare * @SNFfactor, 2) AS DECIMAL(8, 2)) AS VARCHAR)
                                                   WHEN @E -- DME/Prosthetics/Supplies
                   THEN                                @DollarSign
                                                       + CAST(CAST(ROUND (FFSAECostShare * @DMEfactor, 2) AS DECIMAL(8, 2)) AS VARCHAR)
                                                   WHEN @J -- Part B Rx
                   THEN                                @DollarSign
                                                       + CAST(CAST(ROUND (FFSAECostShare * @PartBRxfactor, 2) AS DECIMAL(8, 2)) AS VARCHAR)END
                   ,PassFail = CASE ServiceCategoryCode WHEN @A THEN
                                                            CASE WHEN PlanCSMedCoveredSVCS > FFSAECostShare * @IPfactor THEN
                                                             @Fail ELSE @Pass END
                                                        WHEN @B THEN
                                                            CASE WHEN PlanCSMedCoveredSVCS > FFSAECostShare * @SNFfactor THEN
                                                             @Fail ELSE @Pass END
                                                        WHEN @E THEN
                                                            CASE WHEN PlanCSMedCoveredSVCS > FFSAECostShare * @DMEfactor THEN
                                                             @Fail ELSE @Pass END
                                                        ELSE
                                                            CASE WHEN PlanCSMedCoveredSVCS > FFSAECostShare
                                                                                         * @PartBRxfactor THEN @Fail ELSE
                                                                                                                     @Pass END END
        FROM        @TEMPTABLE

        ORDER BY    ServiceCategoryCode;

        RETURN;

    END;
GO
