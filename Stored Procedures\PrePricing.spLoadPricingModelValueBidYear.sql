SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: PrePricing.spLoadPricingModelValueBidYear  
--    
-- AUTHOR: Adam Gilbert 
--    
-- CREATED DATE: 2024-Dec-4  
-- Type: 
-- DESCRIPTION: Procedure transforms bid year pricing model data and materializes it for use in the market input view.
--    
-- PARAMETERS:    
-- Input: 
   
-- TABLES:   
--  
 
-- Read:    
--  PrePricing.fnGetBidYearMarketInputValues()

-- Write:    
--    PrePricing.PricingModelValueBidYear
--
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Dec-4		1		Initial Version                 Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------   
CREATE PROCEDURE [PrePricing].[spLoadPricingModelValueBidYear]
AS
	SET NOCOUNT ON;
	BEGIN

		DELETE FROM PrePricing.PricingModelValueBidYear;

		INSERT INTO PrePricing.PricingModelValueBidYear 
			   (PlanInfoID,
			   SubCategoryID,
			   BidYearINValue,
			   BidYearOONValue,
			   INCostShareType,
			   OONCostShareType,
			   LastUpdateDateTime)

		SELECT PlanInfoID,
			   SubCategoryID,
			   BidYearINValue,
			   BidYearOONValue,
			   INCostShareType,
			   OONCostShareType,
			   GETDATE() FROM PrePricing.fnGetBidYearMarketInputValues();
	END
GO
