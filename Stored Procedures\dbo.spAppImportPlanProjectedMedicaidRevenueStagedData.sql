SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO



-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportPlanProjectedMedicaidRevenueStagedData
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-DEC-10      1			Initial Version						                                Archana Sahu
-- ----------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spAppImportPlanProjectedMedicaidRevenueStagedData]
(@StageId VARCHAR(100))
AS
BEGIN
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

    DECLARE @tbl__importData TABLE
    (
        ForecastID INT,
        MedicaidProjectedRevenue DECIMAL(14,6),
        MedicaidProjectedCostBenefitExpense DECIMAL(14,6),
        MedicaidProjectedCostNonBenefitExpense DECIMAL(14,6),
        LastUpdateByID CHAR(7),
        LastUpdateDateTime DATETIME
    );

    INSERT INTO @tbl__importData
    SELECT ForecastID,
           MedicaidProjectedRevenue,
           MedicaidProjectedCostBenefitExpense,
           MedicaidProjectedCostNonBenefitExpense,
           @UserId,
		   GETDATE()
    FROM
        OPENJSON(@jsonData, '$.ProjectedMedicaidRevenue')
        WITH
        (
            ForecastID INT,
			MedicaidProjectedRevenue DECIMAL(14,6),
			MedicaidProjectedCostBenefitExpense DECIMAL(14,6),
			MedicaidProjectedCostNonBenefitExpense DECIMAL(14,6)
        );

	
	MERGE INTO dbo.SavedPlanProjectedMedicaidRevenue AS target
    USING @tbl__importData AS source
    ON (
           target.ForecastID = source.ForecastID
       )
    WHEN MATCHED THEN
				UPDATE SET target.MedicaidProjectedRevenue = source.MedicaidProjectedRevenue,
                   target.MedicaidProjectedCostBenefitExpense = source.MedicaidProjectedCostBenefitExpense,
                   target.MedicaidProjectedCostNonBenefitExpense = source.MedicaidProjectedCostNonBenefitExpense,
                   target.LastUpdateByID = source.LastUpdateByID,
                   target.LastUpdateDateTime = source.LastUpdateDateTime
	WHEN NOT MATCHED BY TARGET THEN
		INSERT
		( ForecastID, MedicaidProjectedRevenue, MedicaidProjectedCostBenefitExpense, MedicaidProjectedCostNonBenefitExpense, LastUpdateByID, LastUpdateDateTime)
		VALUES
		(source.ForecastID, source.MedicaidProjectedRevenue, source.MedicaidProjectedCostBenefitExpense, source.MedicaidProjectedCostNonBenefitExpense, 
		source.LastUpdateByID, source.LastUpdateDateTime);

	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
GO
