SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	fnGetSQSFactors
--
-- CREATOR:			Nick <PERSON>s
--
-- CREATED DATE:	2014-MAR-05
--
-- DESCRIPTION:		Returns the Partial and Projected Sequestration Factors by MARatingOptionID at Plan Level
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			PerExtCMSValues
--					CalcPlanExperienceByBenefitCatNonSQS
--					CalcPlanExperienceByBenefitCat
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetCredibilityFactor
--					dbo.fnGetSafeDivisionResultReturnOne(
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																		DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2014-MAR-05      1			Initial Version.																	Nick Koesters
-- 2014-MAR-20		2			Included override in the calcs														Nick Koesters & Mike Deren
-- 2014-NOV-26      3           Renamed tables CalcPlanExperienceByBenefitCatNonSQS9mth and							Amit Tiwari
--                              CalcPlanExperienceByBenefitCat9mth to CalcPlanExperienceByBenefitCat*NonSQSPrtl  
--									and CalcPlanExperienceByBenefitCatPrtl respectivily and also renamed aliases. 
-- 2017-FEB-10		4			Implementating permananent SQS Factor Fix											Jordan Purdue   
-- 2017-OCT-12		5			Replaced override with dampening factor	and removed prtl tables						Chris Fleming & Jordan Purdue
-- 2018-FEB-01		6			Correct case statement for dampening factor (changed from when 1 to when 0)			Matthew Evans
--									Added projected SQS factors without dampening
-- 2018-FEB-05		7			Modifying the SQS Allowed to contain Related Party Profits							Jordan Purdue
-- 2024-OCT-04		8			Add handling for IsIncludeInCostShareBasis flag in the base data tables				
--								Moved ExperienceFactors, ManualFactors pulls into table variables					Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnGetSQSFactors]
    (@ForecastID INT)

RETURNS @Results TABLE
    (ManualPartialSQSFactor                  DECIMAL(14, 6)
    ,ExperiencePartialSQSFactor              DECIMAL(14, 6)
    ,ManualProjectedSQSFactor                DECIMAL(14, 6)
    ,ExperienceProjectedSQSFactor            DECIMAL(14, 6)
    ,ManualProjectedSQSFactorNoDampening     DECIMAL(14, 6)
    ,ExperienceProjectedSQSFactorNoDampening DECIMAL(14, 6))

AS

    BEGIN

        DECLARE @XForecastID INT = @ForecastID;

        DECLARE @Credibility DECIMAL(14, 6);
        DECLARE @DampeningFactor DECIMAL(14, 6);

        SET @Credibility = (SELECT  dbo.fnGetCredibilityFactor (@XForecastID));
        SET @DampeningFactor = (SELECT  SQSDampeningFactor FROM dbo.PerExtCMSValues);


        --Experience Factors
        DECLARE @experienceFactors TABLE
            (ForecastID        INT
            ,NonSQSAllowed     DECIMAL(14, 6)
            ,SQSAllowed        DECIMAL(14, 6)
            ,SQSPrtlAllowed    DECIMAL(14, 6)
            ,NonSQSPrtlAllowed DECIMAL(14, 6)
            ,MARatingOptionID  TINYINT);
        INSERT INTO @experienceFactors
            (ForecastID
            ,NonSQSAllowed
            ,SQSAllowed
            ,SQSPrtlAllowed
            ,NonSQSPrtlAllowed
            ,MARatingOptionID)
        SELECT      DISTINCT
                    SQS.ForecastID
                   ,NonSQSAllowed = SUM (NonSQS.INAllowed + NonSQS.OONAllowed)
                   ,SQSAllowed = SUM (SQS.AllowedWS1WRPP)
                   ,SQSPrtlAllowed = SUM (SQS.AllowedWS1WRPP)
                   ,NonSQSPrtlAllowed = SUM (NonSQS.INAllowed + NonSQS.OONAllowed)
                   ,SQS.MARatingOptionID
        FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS NonSQS WITH (NOLOCK)
       INNER JOIN   dbo.CalcPlanExperienceByBenefitCat SQS WITH (NOLOCK)
               ON NonSQS.PlanYearID = SQS.PlanYearID
                  AND   NonSQS.ForecastID = SQS.ForecastID
                  AND   NonSQS.MARatingOptionID = SQS.MARatingOptionID
                  AND   NonSQS.BenefitCategoryID = SQS.BenefitCategoryID
                  AND   NonSQS.DualEligibleTypeID = SQS.DualEligibleTypeID
                  AND   NonSQS.IsIncludeInCostShareBasis = SQS.IsIncludeInCostShareBasis
        WHERE       SQS.ForecastID = @XForecastID
                    AND SQS.DualEligibleTypeID = 2 --Both Dual and NonDual 
                    AND SQS.MARatingOptionID = 1    --Experience
        GROUP BY    SQS.MARatingOptionID
                   ,SQS.ForecastID;


        --Manual Factors
        DECLARE @manualFactors TABLE
            (ForecastID        INT
            ,NonSQSAllowed     DECIMAL(14, 6)
            ,SQSAllowed        DECIMAL(14, 6)
            ,SQSPrtlAllowed    DECIMAL(14, 6)
            ,NonSQSPrtlAllowed DECIMAL(14, 6)
            ,MARatingOptionID  TINYINT);
        INSERT INTO @manualFactors
            (ForecastID
            ,NonSQSAllowed
            ,SQSAllowed
            ,SQSPrtlAllowed
            ,NonSQSPrtlAllowed
            ,MARatingOptionID)
        SELECT      DISTINCT
                    SQS.ForecastID
                   ,NonSQSAllowed = SUM (NonSQS.INAllowed + NonSQS.OONAllowed)
                   ,SQSAllowed = SUM (SQS.AllowedWS1WRPP)
                   ,SQSPrtlAllowed = SUM (SQS.AllowedWS1WRPP)
                   ,NonSQSPrtlAllowed = SUM (NonSQS.INAllowed + NonSQS.OONAllowed)
                   ,SQS.MARatingOptionID
        FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS NonSQS WITH (NOLOCK)
       INNER JOIN   dbo.CalcPlanExperienceByBenefitCat SQS WITH (NOLOCK)
               ON NonSQS.PlanYearID = SQS.PlanYearID
                  AND   NonSQS.ForecastID = SQS.ForecastID
                  AND   NonSQS.MARatingOptionID = SQS.MARatingOptionID
                  AND   NonSQS.BenefitCategoryID = SQS.BenefitCategoryID
                  AND   NonSQS.DualEligibleTypeID = SQS.DualEligibleTypeID
                  AND   NonSQS.IsIncludeInCostShareBasis = SQS.IsIncludeInCostShareBasis
        WHERE       SQS.ForecastID = @XForecastID
                    AND SQS.DualEligibleTypeID = 2 --Both Dual and NonDual 
                    AND SQS.MARatingOptionID = 2    --Manual
        GROUP BY    SQS.MARatingOptionID
                   ,SQS.ForecastID;


        --Pull Experience Partial & Projected Plan Level SQS Factors for 100% credible plan
        IF @Credibility = 1

            BEGIN

                INSERT INTO @Results
                SELECT      DISTINCT
                            ManualPartialSQSFactor = NULL
                           ,ExperiencePartialSQSFactor = (SELECT    DISTINCT
                                                                    (dbo.fnGetSafeDivisionResultReturnOne (
                                                                     (SUM (ExperienceFactors.NonSQSAllowed))
                                                                    ,(SUM (ExperienceFactors.SQSAllowed)))))
                           ,ManualProjectedSQSFactor = NULL
                           ,ExperienceProjectedSQSFactor = CASE @DampeningFactor WHEN 0 THEN 1
                                                                                 ELSE
                                                                                     1 - @DampeningFactor
                                                                                     * (1
                                                                                        - (SELECT   DISTINCT
                                                                                                    (dbo.fnGetSafeDivisionResultReturnOne (
                                                                                                     (SUM (ExperienceFactors.SQSPrtlAllowed))
                                                                                                    ,(SUM (
                                                                                                      ExperienceFactors.NonSQSPrtlAllowed)))))) END
                           ,ManualProjectedSQSFactorNoDampening = NULL
                           ,ExperienceProjectedSQSFactorNoDampening = (SELECT   DISTINCT
                                                                                (dbo.fnGetSafeDivisionResultReturnOne (
                                                                                 (SUM (ExperienceFactors.SQSPrtlAllowed))
                                                                                ,(SUM (ExperienceFactors.NonSQSPrtlAllowed)))))
                FROM        @experienceFactors ExperienceFactors
                GROUP BY    ExperienceFactors.MARatingOptionID;

            END;

        --Pull Manual Partial & Projected Plan Level SQS Factors for 0% credible plan
        IF @Credibility = 0

            BEGIN

                INSERT INTO @Results
                SELECT      ManualPartialSQSFactor = (SELECT    DISTINCT
                                                                (dbo.fnGetSafeDivisionResultReturnOne (
                                                                 (SUM (ManualFactors.NonSQSAllowed))
                                                                ,(SUM (ManualFactors.SQSAllowed)))))
                           ,ExperiencePartialSQSFactor = NULL
                           ,ManualProjectedSQSFactor = CASE @DampeningFactor WHEN 0 THEN 1
                                                                             ELSE
                                                                                 1 - @DampeningFactor
                                                                                 * (1
                                                                                    - (SELECT   DISTINCT
                                                                                                (dbo.fnGetSafeDivisionResultReturnOne (
                                                                                                 (SUM (ManualFactors.SQSPrtlAllowed))
                                                                                                ,(SUM (
                                                                                                  ManualFactors.NonSQSPrtlAllowed)))))) END
                           ,ExperienceProjectedSQSFactor = NULL
                           ,ManualProjectedSQSFactorNoDampening = (SELECT   DISTINCT
                                                                            (dbo.fnGetSafeDivisionResultReturnOne (
                                                                             (SUM (ManualFactors.SQSPrtlAllowed))
                                                                            ,(SUM (ManualFactors.NonSQSPrtlAllowed)))))
                           ,ExperienceProjectedSQSFactorNoDampening = NULL
                FROM        @manualFactors ManualFactors
                GROUP BY    ManualFactors.MARatingOptionID;

            END;


        --Pull Experience and Manual Partial & Projected Plan Level SQS Factors for partially credible plan
        IF @Credibility NOT IN (0, 1)

            BEGIN

                INSERT INTO @Results
                SELECT      ManualPartialSQSFactor = (SELECT    DISTINCT
                                                                (dbo.fnGetSafeDivisionResultReturnOne (
                                                                 (SUM (ManualFactors.NonSQSAllowed))
                                                                ,(SUM (ManualFactors.SQSAllowed)))))
                           ,ExperiencePartialSQSFactor = (SELECT    DISTINCT
                                                                    (dbo.fnGetSafeDivisionResultReturnOne (
                                                                     (SUM (ExperienceFactors.NonSQSAllowed))
                                                                    ,(SUM (ExperienceFactors.SQSAllowed)))))
                           ,ManualProjectedSQSFactor = CASE @DampeningFactor WHEN 0 THEN 1
                                                                             ELSE
                                                                                 1 - @DampeningFactor
                                                                                 * (1
                                                                                    - (SELECT   DISTINCT
                                                                                                (dbo.fnGetSafeDivisionResultReturnOne (
                                                                                                 (SUM (ManualFactors.SQSPrtlAllowed))
                                                                                                ,(SUM (
                                                                                                  ManualFactors.NonSQSPrtlAllowed)))))) END
                           ,ExperienceProjectedSQSFactor = CASE @DampeningFactor WHEN 0 THEN 1
                                                                                 ELSE
                                                                                     1 - @DampeningFactor
                                                                                     * (1
                                                                                        - (SELECT   DISTINCT
                                                                                                    (dbo.fnGetSafeDivisionResultReturnOne (
                                                                                                     (SUM (
                                                                                                      ExperienceFactors.SQSPrtlAllowed))
                                                                                                    ,(SUM (
                                                                                                      ExperienceFactors.NonSQSPrtlAllowed)))))) END
                           ,ManualProjectedSQSFactorNoDampening = (SELECT   DISTINCT
                                                                            (dbo.fnGetSafeDivisionResultReturnOne (
                                                                             (SUM (ManualFactors.SQSPrtlAllowed))
                                                                            ,(SUM (ManualFactors.NonSQSPrtlAllowed)))))
                           ,ExperienceProjectedSQSFactorNoDampening = (SELECT   DISTINCT
                                                                                (dbo.fnGetSafeDivisionResultReturnOne (
                                                                                 (SUM (ExperienceFactors.SQSPrtlAllowed))
                                                                                ,(SUM (ExperienceFactors.NonSQSPrtlAllowed)))))
                FROM        @experienceFactors ExperienceFactors
               INNER JOIN   @manualFactors ManualFactors
                       ON ManualFactors.ForecastID = ExperienceFactors.ForecastID;

            END;

        RETURN;

    END;
GO
