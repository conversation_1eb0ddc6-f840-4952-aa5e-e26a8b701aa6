SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetRatebook]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: 
--
-- CREATOR: <PERSON> 
--
-- CREATED DATE: Sep-01-2010
-- HEADER UPDATED: Sep-01-2010
--
-- DESCRIPTION: Returns CMS ratebook for a specific State, County and Contract or the default value if it is a new contract.
--
-- PARAMETERS:
--	Input:
--	Output:
--
-- TABLES:
--	Read: 
--		SavedPlanDetail 
--		Saved<PERSON>lanHeader
--		PerExtCMSRatebookDefault
--		PerExtCMSRatebookByContract
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Sept-01         1           Initial Version.  Made to output default values for new Contracts   Jake Gaecke
--                                  not present in CMS ratebook
-- 2010-Nov-08          2           Was returning duplicate rows for everything.  Corrected.            Casey Sanders   
-- 2011-Mar-17			3			Returns blank if plan is unavailable form ratebook					Jiao Chen    
-- 2012-Jan-19			4			Changed CMSPartBOnlyRiskRate to equal to CMSRiskRate*PartBRate		Tim Gao 
-- 2022-Sep-27			5			@XVariables, WITH (NOLOCK)											Phani Adduri
-- 2023-Aug-03			6			Added table schema and column references							Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetRatebook]
    (
    @ForecastID INT = NULL
    ) 
RETURNS @Results TABLE
(
	RatebookID TINYINT,
	StateTerritoryID TINYINT,
	CountyCode CHAR(3),
	ContractNumber CHAR(5),
	CMSRiskRate DECIMAL(8, 4),
	CMSPartBOnlyRiskRate DECIMAL(16, 12),
	CMSIPCostShare DECIMAL(16, 15),
	CMSSNFCostShare DECIMAL(16, 15),
	CMSOPCostShare DECIMAL(16, 15),
	CMSOtherCostShare DECIMAL(16, 15),
	CMSIPFFSCosts DECIMAL(24, 15),
	CMSSNFCosts DECIMAL(24, 15),
	CMSOPCosts DECIMAL(24, 15),
	CMSOtherCosts DECIMAL(24, 15),
	CMSPartAEquivCostShare DECIMAL(23, 15),
	CMSPartBEquivCostShare DECIMAL(10, 6),
	CMSMedicareEligibles INT,
	CMSEnrollment INT
) AS

BEGIN
    DECLARE @XForecastID INT = @ForecastID;
	
    IF EXISTS ( SELECT 1
                FROM dbo.SavedPlanHeader SPH WITH(NOLOCK)
                INNER JOIN dbo.SavedPlanDetail SPD WITH(NOLOCK)
                    ON SPH.ForecastID = SPD.ForecastID
                INNER JOIN dbo.PerExtCMSRatebookByContract RB WITH(NOLOCK)
                    ON RB.RatebookID = SPD.RatebookID
                    AND RB.ContractNumber = SPH.ContractNumber
                WHERE SPH.ForecastID = @XForecastID)
        BEGIN
            INSERT @Results
                SELECT
                    SPD.RatebookID,
                    RB.StateTerritoryID,
                    RB.CountyCode,
                    SPH.ContractNumber,
                    RB.CMSRiskRate,
                    CMSPartBOnlyRiskRate=RB.CMSRiskRate*RB.PartBRate,
                    RB.CMSIPCostShare,
                    RB.CMSSNFCostShare,
                    RB.CMSOPCostShare,
                    RB.CMSOtherCostShare,
                    RB.CMSIPFFSCosts,
                    RB.CMSSNFCosts,
                    RB.CMSOPCosts,
                    RB.CMSOtherCosts,
                    RB.CMSPartAEquivCostShare,
                    RB.CMSPartBEquivCostShare,
                    RB.CMSMedicareEligibles,
                    RB.CMSEnrollment
                FROM dbo.SavedPlanHeader SPH WITH(NOLOCK)
                INNER JOIN dbo.SavedPlanDetail SPD WITH(NOLOCK)
                    ON SPH.ForecastID = SPD.ForecastID
                INNER JOIN dbo.PerExtCMSRatebookByContract RB WITH(NOLOCK)
                    ON RB.RatebookID = SPD.RatebookID
                    AND RB.ContractNumber = SPH.ContractNumber
                WHERE SPH.ForecastID = @XForecastID
                    AND SPD.MARatingOptionID = 1
        END

    RETURN
END
GO
