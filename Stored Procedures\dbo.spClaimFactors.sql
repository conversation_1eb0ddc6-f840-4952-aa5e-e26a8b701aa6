SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spClaimFactors
--
-- CREATOR: <PERSON>
--
-- CREATED DATE: 2010-Nov-08
-- HEADER UPDATED: 2010-Nov-08
--
-- DESCRIPTION: Stored procedure used to break apart the trend data to fit into savedclaimfactorbenefitlevel
--
-- PARAMETERS:
-- Input:
--      @ContractPBP
--  Output:
--
-- TABLES:
-- Read: SavedMATrendData,
--		 SavedMERActAdj
-- Write: SavedBenefitLevelClaimFactors
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2012-OCT-08      1           Initial Version                                                     Mike Deren
-- 2012-OCT-10      2           Removed 1 from the Calculations                                     Mike Deren
-- 2012-OCT-17		3			Inserted Delete Command to remove previous uploaded INFO			Mike Deren
-- 2012-OCT-18		4			Updated to Allow for all combos of IN/OON, MAN/EXP, CUR/Proj		Mike Deren
-- 2012-Nov-13		5			Changed To Plan Index Entry											Mike Deren
-- 2013-Jan-04		6			Added in Is Live filters and Is Hidden Filters						Mike Deren
-- 2013-Jan-21		7			Cleaned up SavedBenefitLevelClaimFactors (Optimized)				Mike Deren
-- 2013-Mar-26		8			Added UserID as an input parameter									Trevor Mahoney
-- 2013-Jun-25		9			Changed B2CBenefitChangeCost to ...Use in Lines 312 and 513			Trevor Mahoney
-- 2013-Oct-08      10			Included Join on Segment ID                                         Manisha Tyagi
-- 2013-Nov-06      11			Modified the code to improve the performance						Rajendran
-- 2014-Jan-09		12			Changed a table name												Mike Deren
-- 2015-Oct-09      13          Rename LastUpdate to LastUpdateDateTime                             Manisha Tyagi
-- 2015-Nov-17		14			Added join to SavedMERActAdj. Added ID's 35 & 36					Mark Freel
-- 2019-Jun-28		15			Replace SavedPlanHeader with SavedForecastSetup					    Pooja Dahiya
-- 2019-oct-24		16			Removed 'HUMAD\' to UserID											Chhavi Sinha
-- 2019-Nov-06      17			Replaced  SavedForecastSetup with SavedPlanHeader					Chhavi Sinha
-- 2020-Dec-20		18			Adding temp table and row lock to table to improve db performance	Surya Murthy
-- 2022-Oct-14      19          Added internal variables for parameters                             Khurram Minhas
--                              Added WITH (NOLOCK)
--                              Drop temp tables
-- 2023-Jan-10		20			Removing PlanYearId from table SavedBenefitLevelClaimFactors    	Surya Murthy
-- 2024-May-05      21			Removed NOLOCK Table Hint											Kiran Kola
-- 2024-July-17		22			Added transaction to fix deadlock issue.
--								Remove sorting. Drop Temp Tables.									Yuriy Ivanyuk
-- 2024-Sept-10		23			Modify transaction to fix parallel deadlock issue					Yuriy Ivanyuk
-- 2024-DEC-16		24			Add additional condition to WHERE to only retrieve data from		Alex Brandt
--									SavedMERActAdj for @ForecastID and not all plans which share
--									CPSIDs across multiple years.
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spClaimFactors]
    (
      @ForecastID INT ,
      @UserID CHAR(7)
    )
AS
    BEGIN
		SET XACT_ABORT ON;
	    DECLARE @XForecastID INT = @ForecastID,
	    @XUserID CHAR(7) = @UserID

        DECLARE @PlanYearID SMALLINT
        SELECT  @PlanYearID = dbo.fnGetBidYear()

        IF ( SELECT OBJECT_ID('tempdb..#Test')
           ) IS NOT NULL
            DROP TABLE #Test

        SELECT  cf.ClaimFactorTypeID ,
                py.ProjectionYearID ,
                net.IsInNetwork IsInNetwork ,
                bc.BenefitCategoryID
        INTO    #Test
        FROM   dbo.PerIntClaimFactorTypeTest cf WITH (NOLOCK)
                CROSS JOIN dbo.LkpIntProjectionYear py  WITH (NOLOCK)
                CROSS JOIN dbo.LkpIntBenefitCategory bc  WITH (NOLOCK)
                CROSS JOIN ( SELECT 1 AS IsInNetwork
                             UNION
                             SELECT 0
                           ) net
        WHERE   bc.IsEnabled = 1
		CREATE NONCLUSTERED INDEX IDX_Test_ClaimFactorTypeID_ProjectionYearID_BenefitCategoryID
								 ON #Test (ClaimFactorTypeID,ProjectionYearID,BenefitCategoryID)



    --DECLARE  @ContractPBP VARCHAR(MAX)
        DECLARE @ContractNumber CHAR(5)
        DECLARE @PlanID CHAR(3)
        DECLARE @SegmentId CHAR(3)

        SELECT  @ContractNumber = sph.ContractNumber,
                @PlanID = sph.PlanID ,
                @SegmentId =sph.SegmentID
        FROM    dbo.SavedPlanDetail spd WITH (NOLOCK)
                INNER JOIN dbo.SavedPlanHeader sph WITH (NOLOCK) ON spd.ForecastID = sph.ForecastID

        WHERE   @XForecastID = sph.ForecastID
                AND spd.MARatingOptionId = 1
                AND sph.IsLiveindex = 1
                AND sph.IsHidden = 0


        IF ( SELECT OBJECT_ID('tempdb..#TestData1')
           ) IS NOT NULL
            DROP TABLE #TestData1

			SELECT
			   mat.[ContractNumber]
			  ,mat.[PlanID]
			  ,mat.[SegmentID]
			  ,mat.[Component]
			  ,mat.[BenefitCategoryNumber]
			  ,[B2CNormalizedUse]
			  ,[B2CNormalizedCost]
			  ,[B2CMorbidityUse]
			  ,[B2CMorbidityCost]
			  ,[B2CDemographicUse]
			  ,[B2CDemographicCost]
			  ,[B2CBustersUse]
			  ,[B2CBustersCost]
			  ,[B2CProductUse]
			  ,[B2CProductCost]
			  ,[B2CGeographicUse]
			  ,[B2CGeographicCost]
			  ,[B2CCMSReimbursementUse]
			  ,[B2CCMSReimbursementCost]
			  ,[B2CContractualUse]
			  ,[B2CContractualCost]
			  ,[B2CBendersUse]
			  ,[B2CBendersCost]
			  ,[B2CWorkdayUse]
			  ,[B2CWorkdayCost]
			  ,[B2CFluUse]
			  ,[B2CFluCost]
			  ,[B2CInducedUtilizationUse]
			  ,[B2CInducedUtilizationCost]
			  ,[B2CActAdjUse]
			  ,[B2CActAdjCost]
			  ,[B2CNonActAdjUse]
			  ,[B2CNonActAdjCost]
			  ,[B2CPoolingUse]
			  ,[B2CPoolingCost]
			  ,[B2CHTPUse]
			  ,[B2CHTPCost]
			  ,[B2CCompoundingAdjUse]
			  ,[B2CCompoundingAdjCost]
			  ,[C2PNormalizedUse]
			  ,[C2PNormalizedCost]
			  ,[C2PMorbidityUse]
			  ,[C2PMorbidityCost]
			  ,[C2PDemographicUse]
			  ,[C2PDemographicCost]
			  ,[C2PBustersUse]
			  ,[C2PBustersCost]
			  ,[C2PProductUse]
			  ,[C2PProductCost]
			  ,[C2PGeographicUse]
			  ,[C2PGeographicCost]
			  ,[C2PCMSReimbursementUse]
			  ,[C2PCMSReimbursementCost]
			  ,[C2PContractualUse]
			  ,[C2PContractualCost]
			  ,[C2PBendersUse]
			  ,[C2PBendersCost]
			  ,[C2PWorkdayUse]
			  ,[C2PWorkdayCost]
			  ,[C2PFluUse]
			  ,[C2PFluCost]
			  ,[C2PInducedUtilizationUse]
			  ,[C2PInducedUtilizationCost]
			  ,[C2PActAdjUse]
			  ,[C2PActAdjCost]
			  ,[C2PNonActAdjUse]
			  ,[C2PNonActAdjCost]
			  ,[C2PPoolingUse]
			  ,[C2PPoolingCost]
			  ,[C2PHTPUse]
			  ,[C2PHTPCost]
			  ,[C2PCompoundingAdjUse]
			  ,[C2PCompoundingAdjCost]
			  ,[C2PMERCostMultAdj]
			  ,[C2PMERUseMultAdj]
        INTO    #TestData1
        FROM    dbo.SavedMATrendData  mat WITH (NOLOCK)
        LEFT JOIN dbo.SavedMERActAdj mer WITH (NOLOCK)
        ON mer.ContractNumber=mat.ContractNumber
        AND mer.PlanID=mat.PlanID
        AND mer.SegmentID=mat.SegmentID
        AND mer.BenefitCategoryNumber=mat.BenefitCategoryNumber
        AND mer.Component=mat.Component
        WHERE   mat.ContractNumber = @ContractNumber
                AND mat.PlanID = @PlanID
                AND mat.SegmentID = @SegmentID
				AND mer.ForecastID = @ForecastID
		 UPDATE STATISTICS #TestData1

        IF ( SELECT OBJECT_ID('tempdb..#fnGetNewClaimFactors')
           ) IS NOT NULL
            DROP TABLE #fnGetNewClaimFactors

        CREATE TABLE #fnGetNewClaimFactors
            (
              Component INT ,
              BenefitCategoryNumber INT ,
              ProjectionYearID INT ,
              ClaimFactorTypeID INT ,
              VALUE DECIMAL(24, 15)
            )
		CREATE NONCLUSTERED INDEX IDX_fnGetNewClaimFactors_ClaimFactorTypeID_ProjectionYearID_BenefitCategoryNumber
		ON #fnGetNewClaimFactors (ClaimFactorTypeID,ProjectionYearID,BenefitCategoryNumber)


		INSERT INTO #fnGetNewClaimFactors
		SELECT Component, BenefitCategoryNumber, GroupID, FactorID, ISNULL(Cost,1)
		FROM #TestData1
		CROSS APPLY (VALUES
			(1, 1, B2CNormalizedCost),
			(1, 2, B2CMorbidityCost),
			(1, 3, B2CDemographicCost),
			(1, 4, B2CBustersCost),
			(1, 5, B2CProductCost),
			(1, 6, B2CGeographicCost),
			(1, 7, B2CCMSReimbursementCost),
			(1, 8, B2CContractualCost),
			(1, 9, B2CBendersCost),
			(1, 10, B2CWorkdayCost),
			(1, 11, B2CFluCost),
			(1, 12, B2CInducedUtilizationCost),
			(1, 13, B2CActAdjCost),
			(1, 14, B2CNonActAdjCost),
			(1, 15, B2CPoolingCost),
			(1, 16, B2CHTPCost),
			(1, 17, B2CCompoundingAdjCost),
			(1, 18, B2CNormalizedUse),
			(1, 19, B2CMorbidityUse),
			(1, 20, B2CDemographicUse),
			(1, 21, B2CBustersUse),
			(1, 22, B2CProductUse),
			(1, 23, B2CGeographicUse),
			(1, 24, B2CCMSReimbursementUse),
			(1, 25, B2CContractualUse),
			(1, 26, B2CBendersUse),
			(1, 27, B2CWorkdayUse),
			(1, 28, B2CFluUse),
			(1, 29, B2CInducedUtilizationUse),
			(1, 30, B2CActAdjUse),
			(1, 31, B2CNonActAdjUse),
			(1, 32, B2CPoolingUse),
			(1, 33, B2CHTPUse),
			(1, 34, B2CCompoundingAdjUse),
			(2, 1, C2PNormalizedCost),
			(2, 2, C2PMorbidityCost),
			(2, 3, C2PDemographicCost),
			(2, 4, C2PBustersCost),
			(2, 5, C2PProductCost),
			(2, 6, C2PGeographicCost),
			(2, 7, C2PCMSReimbursementCost),
			(2, 8, C2PContractualCost),
			(2, 9, C2PBendersCost),
			(2, 10, C2PWorkdayCost),
			(2, 11, C2PFluCost),
			(2, 12, C2PInducedUtilizationCost),
			(2, 13, C2PActAdjCost),
			(2, 14, C2PNonActAdjCost),
			(2, 15, C2PPoolingCost),
			(2, 16, C2PHTPCost),
			(2, 17, C2PCompoundingAdjCost),
			(2, 18, C2PNormalizedUse),
			(2, 19, C2PMorbidityUse),
			(2, 20, C2PDemographicUse),
			(2, 21, C2PBustersUse),
			(2, 22, C2PProductUse),
			(2, 23, C2PGeographicUse),
			(2, 24, C2PCMSReimbursementUse),
			(2, 25, C2PContractualUse),
			(2, 26, C2PBendersUse),
			(2, 27, C2PWorkdayUse),
			(2, 28, C2PFluUse),
			(2, 29, C2PInducedUtilizationUse),
			(2, 30, C2PActAdjUse),
			(2, 31, C2PNonActAdjUse),
			(2, 32, C2PPoolingUse),
			(2, 33, C2PHTPUse),
			(2, 34, C2PCompoundingAdjUse),
			(2, 35, C2PMERCostMultAdj),
			(2, 36, C2PMERUseMultAdj)
		) AS Factors(GroupID,FactorID, Cost)

		UPDATE STATISTICS #fnGetNewClaimFactors
		IF OBJECT_ID('tempdb.dbo.#TestData1') IS NOT NULL
			DROP TABLE #TestData1

		IF OBJECT_ID(N'tempdb..#SavedBenefitLevelClaimFactorsDeletesTemp') IS NOT NULL
			DROP TABLE #SavedBenefitLevelClaimFactorsDeletesTemp;

		SELECT DISTINCT
	        P.ClaimForecastID ,
            T.IsInNetwork
		INTO #SavedBenefitLevelClaimFactorsDeletesTemp
        FROM #Test T
        INNER JOIN #fnGetNewClaimFactors F ON F.ClaimFactorTypeID = T.ClaimFactorTypeID
                                           AND F.ProjectionYearID = T.ProjectionYearID
                                           AND F.BenefitCategoryNumber = T.BenefitCategoryID
        INNER JOIN dbo.SavedPlanDetail P WITH (NOLOCK) ON P.ForecastID = @XForecastID
		AND F.Component = P.MARatingOptionID
            WHERE  P.ClaimForecastID IS NOT NULL

		CREATE NONCLUSTERED INDEX idx_ClaimForecastID_IsInNetwork ON #SavedBenefitLevelClaimFactorsDeletesTemp (ClaimForecastID, IsInNetwork)


	    SELECT
		    P.ClaimForecastID ,
			T.ClaimFactorTypeID ,
			T.ProjectionYearID ,
			T.IsInNetwork ,
			T.BenefitCategoryID,
			'ClaimFactor' = CASE WHEN T.ClaimFactorTypeID = 29 THEN
				CASE WHEN T.IsInNetwork = 1 THEN CAST(F.VALUE * SQRT(ISNULL(IU.InducedUtilizationFactorIN ,1))  AS DECIMAL(8,6))
				ELSE  CAST(F.VALUE * SQRT(ISNULL(IU.InducedUtilizationFactorOON ,1))  AS DECIMAL(8,6)) END
			ELSE F.VALUE END
		INTO #SavedBenefitLevelClaimFactorsTemp
		FROM    #Test T
        INNER JOIN #fnGetNewClaimFactors F ON F.ClaimFactorTypeID = T.ClaimFactorTypeID
												AND F.ProjectionYearID = T.ProjectionYearID
												AND F.BenefitCategoryNumber = T.BenefitCategoryID
        INNER JOIN dbo.SavedPlanDetail P (NOLOCK) ON  P.ForecastID = @XForecastID
												AND F.Component = P.MARatingOptionID
        LEFT JOIN dbo.CalcInducedUtilizationFactors IU (NOLOCK) ON  IU.ForecastID = P.ForecastID
												AND IU.BenefitCategoryID = T.BenefitCategoryID
												AND IU.MARating = P.MARatingOptionID
		WHERE   P.ClaimForecastID IS NOT NULL -- 11872


		IF OBJECT_ID('tempdb.dbo.#Test') IS NOT NULL
        DROP TABLE #Test

		IF OBJECT_ID('tempdb.dbo.#fnGetNewClaimFactors') IS NOT NULL
        DROP TABLE #fnGetNewClaimFactors

		DELETE  s
        FROM    dbo.SavedBenefitLevelClaimFactors (ROWLOCK) s
		INNER JOIN #SavedBenefitLevelClaimFactorsDeletesTemp t
		ON s.ClaimForecastID = T.ClaimForecastID
			AND s.IsInNetwork = T.IsInNetwork

		INSERT INTO dbo.SavedBenefitLevelClaimFactors SELECT * FROM #SavedBenefitLevelClaimFactorsTemp

		IF OBJECT_ID(N'tempdb..#SavedBenefitLevelClaimFactorsDeletesTemp') IS NOT NULL
		Begin
			DROP TABLE #SavedBenefitLevelClaimFactorsDeletesTemp;
			End

		IF OBJECT_ID(N'tempdb..#SavedBenefitLevelClaimFactorsTemp') IS NOT NULL
		Begin
			DROP TABLE #SavedBenefitLevelClaimFactorsTemp;
			End


        DECLARE @ClaimForecastID INT ,
            @ClaimForecastID2 INT

        SELECT  @ClaimForecastID = ClaimForecastID
        FROM    dbo.SavedPlanDetail spd WITH (NOLOCK)
                INNER JOIN dbo.SavedPlanHeader sph WITH (NOLOCK) ON spd.ForecastID = sph.ForecastID
        WHERE   sph.ForecastID = @XForecastID
                AND spd.MARatingOptionID = 1
                AND IsLiveIndex = 1
                AND IsHidden = 0

        SELECT  @ClaimForecastID2 = ClaimForecastID
        FROM    dbo.SavedPlanDetail spd WITH (NOLOCK)
                INNER JOIN dbo.SavedPlanHeader sph WITH (NOLOCK) ON spd.ForecastID = sph.ForecastID
        WHERE   sph.ForecastID = @XForecastID
                AND spd.MARatingOptionID = 2
                AND IsLiveIndex = 1
                AND IsHidden = 0


        IF NOT EXISTS ( SELECT  1
                        FROM    dbo.SavedClaimFactorHeaderBenefitLevel  WITH (NOLOCK)
                        WHERE   ClaimForecastID = @ClaimForecastID )
            BEGIN
                INSERT  INTO dbo.SavedClaimFactorHeaderBenefitLevel
                        SELECT  @PlanYearID ,
                                @ClaimForecastID , --ClaimForecastID
                                @XForecastID ,
                                @XForecastID ,  --NAME
                                'MANUAL' ,  --Description
                                0 , --IsHidden
                                0 , --IsAtPlanLevel,
                                @XUserID ,
                                GETDATE() ,
                                NEWID()
            END
        ELSE
            BEGIN
                UPDATE  dbo.SavedClaimFactorHeaderBenefitLevel
                SET     LastUpdateDateTime = GETDATE()
                WHERE   ClaimForecastID = @ClaimForecastID
            END

         --If ClaimForecastID does not already exist put it in the header table

        IF NOT EXISTS ( SELECT  1
                        FROM    dbo.SavedClaimFactorHeaderBenefitLevel  WITH (NOLOCK)
                        WHERE   ClaimForecastID = @ClaimForecastID2 )
            BEGIN
                INSERT  INTO dbo.SavedClaimFactorHeaderBenefitLevel
                        SELECT  @PlanYearID ,
                                @ClaimForecastID2 , --ClaimForecastID
                                @XForecastID ,
								@XForecastID ,  --NAME
                                'EXPERIENCE' ,  --Description
                                0 , --IsHidden
                                0 , --IsAtPlanLevel,
                                @XUserID ,
                                GETDATE() ,
                                NEWID()
            END
        BEGIN
            UPDATE  dbo.SavedClaimFactorHeaderBenefitLevel
            SET     LastUpdateDateTime = GETDATE()
            WHERE   ClaimForecastID = @ClaimForecastID2
        END

    END
GO
