SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		<PERSON><PERSON><PERSON>vey
-- Create date: 2024-AUG-16

-- Description:	Stored procedure responsible for Import the data for the Related Party and RX Rebates for the 
-- LkpIntAddedBenefitType and LkpIntAddedBenefitExpenseDetail table.

-- PARAMETERS:  
-- Input:  
--
-- Output:  
--  
-- TABLES:  
-- Read:
--	  
--    
-- Write:
--	 LkpIntAddedBenefitType	
--   LkpIntAddedBenefitExpenseDetail
--
-- VIEWS:  
--  
-- FUNCTIONS:
--
-- STORED PROCS:  
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-AUG-16         1          Initial Version                                                     Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------  
-- =======================================================================================================================

CREATE PROCEDURE [dbo].[spAppImportRelatedPartyAndRxRebates]
(
	-- Add the parameters for the stored procedure here
		@StageId VARCHAR(100)
)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;


	SELECT @jsonData = JsonData, @UserId = UserId
	FROM dbo.ImportDataStaging WITH (NOLOCK)
	WHERE StageId = @StageId;



	DECLARE @tbl__importData TABLE
	(
		[PlanYearID] [INT] NOT NULL,
		[AddedBenefitTypeID] [int] NOT NULL,
		[AddedBenefitName] [varchar] (50) NULL,
		[INAddedBenefitDescription] [varchar] (2000) NULL,
	    [IsEnabled] [bit] NULL,
	    [AddedBenefitClaims] [decimal] (6, 2) NULL,
	 	[AddedBenefitAdmin] [decimal] (6, 2) NULL,
		[AddedBenefitSales] [decimal] (6, 2) NULL,
		[SalesMktExpensePMPM] [decimal] (6, 2) NULL,
		[AddedBenefitQuality] [decimal] (6, 2) NULL,
		[ClaimsClaims] [decimal] (6, 2) NULL,
		[QualityClaims] [decimal] (6, 2) NULL,
		[QualityQuality] [decimal] (6, 2) NULL,
		[AdminAdmin] [decimal] (6, 2) NULL,
		[Totalz1] [decimal] (6, 2) NULL,
		[Totalz2] [decimal] (6, 2) NULL,
		LastUpdateByID [CHAR](7) NOT NULL,
		LastUpdateDateTime [DATETIME] NOT NULL
	);

	INSERT INTO @tbl__importData 
	SELECT
		PlanYearID,
		AddedBenefitTypeID,
		AddedBenefitName,
		INAddedBenefitDescription,
		IsEnabled,
		AddedBenefitClaims,
		AddedBenefitAdmin,
		AddedBenefitSales,
		SalesMktExpensePMPM,
		AddedBenefitQuality,
		ClaimsClaims,
		QualityClaims,
		QualityQuality,
		AdminAdmin,
		Totalz1,
		Totalz2,
		@UserId,
		GETDATE()			
	FROM 
	OPENJSON(@jsonData, '$.RelatedPartyAndRXRebates')
	WITH
    (	
	    [PlanYearID] [INT],
		[AddedBenefitTypeID] [INT],
		[AddedBenefitName] [varchar] (50),
		[INAddedBenefitDescription] [varchar] (2000),
	    [IsEnabled] [bit],
	    [AddedBenefitClaims] [decimal] (6, 2),
	 	[AddedBenefitAdmin] [decimal] (6, 2),
		[AddedBenefitSales] [decimal] (6, 2),
		[SalesMktExpensePMPM] [decimal] (6, 2),
		[AddedBenefitQuality] [decimal] (6, 2),
		[ClaimsClaims] [decimal] (6, 2),
		[QualityClaims] [decimal] (6, 2),
		[QualityQuality] [decimal] (6, 2),
		[AdminAdmin] [decimal] (6, 2),
		[Totalz1] [decimal] (6, 2),
		[Totalz2] [decimal] (6, 2),
		LastUpdateByID [CHAR](7),
		LastUpdateDateTime [DATETIME]
	);

	MERGE INTO [dbo].[LkpIntAddedBenefitType] AS target
	USING @tbl__importData AS source
	ON (
	    target.PlanYearID = source.PlanYearID
		AND target.AddedBenefitTypeID =  source.AddedBenefitTypeID
		AND target.BidServiceCatID = 35
	)
	WHEN MATCHED THEN 
		UPDATE	
		SET 	target.PlanYearID = source.PlanYearID,
				target.AddedBenefitTypeID = source.AddedBenefitTypeID,
			    target.AddedBenefitCatID = 8,
				target.IsStandard = 1,
				target.AddedBenefitName = source.AddedBenefitName,
				target.INAddedBenefitDescription = source.INAddedBenefitDescription,
				target.INAddedBenefitAllowed = 0,
		        target.INAddedBenefitUtilization = 0,
				target.INAddedBenefitCostShare = 0,
				target.OONAddedBenefitDescription = '',
				target.OONAddedBenefitAllowed = 0,
				target.OONAddedBenefitUtilization = 0,
				target.OONAddedBenefitCostShare = 0,
				target.BidServiceCatID = 35,
				target.IsValueAdded = 0,
				target.IsNetwork = 0,
				target.IsEnabled = source.IsEnabled,
				target.OONPercent = 0,
				target.SubstantiationDetail = '',
				target.LastUpdateByID = source.LastUpdateByID,
				target.LastUpdateDateTime =  source.LastUpdateDateTime

	WHEN NOT MATCHED THEN 
	INSERT 
	(
		    PlanYearID,
			AddedBenefitTypeID,
			AddedBenefitCatID,
			IsStandard,
			AddedBenefitName,
			INAddedBenefitDescription,
			INAddedBenefitAllowed,
			INAddedBenefitUtilization,
			INAddedBenefitCostShare,
			OONAddedBenefitDescription,
			OONAddedBenefitAllowed,
			OONAddedBenefitUtilization,
			OONAddedBenefitCostShare,
			BidServiceCatID,
			IsValueAdded,
			IsNetwork,
			IsEnabled,
			OONPercent,
			SubstantiationDetail,
			LastUpdateByID,
			LastUpdateDateTime
	)
	VALUES
	(source.PlanYearID, source.AddedBenefitTypeID, 8, 1, source.AddedBenefitName, source.INAddedBenefitDescription, 
	 0, 0, 0, '', 0, 0, 0, 35, 0, 0, source.IsEnabled, 0, '', source.LastUpdateByID, 
	 source.LastUpdateDateTime); 

	MERGE INTO [dbo].[LkpIntAddedBenefitExpenseDetail] AS target
	USING @tbl__importData AS source
	ON (
			target.PlanYearID = source.PlanYearID
			AND target.AddedBenefitTypeID = source.AddedBenefitTypeID
			AND target.BidServiceCatID = 35
		)
		WHEN MATCHED THEN 
			UPDATE	
			SET 		
				target.PlanYearID = source.PlanYearID,	
				target.AddedBenefitTypeID = source.AddedBenefitTypeID,
				target.AddedBenefitName = source.AddedBenefitName,
				target.AddedBenefitClaims = source.AddedBenefitClaims,
				target.AddedBenefitAdmin = source.AddedBenefitAdmin,
				target.AddedBenefitSales = source.AddedBenefitSales,
				target.AddedBenefitProfit = 0,
				target.HumanaOwnership = 1,
				target.SalesMktExpensePMPM = source.SalesMktExpensePMPM,
				target.DirectExpensePMPM = 	0,
				target.IndirectExpensePMPM = 0,
				target.IsRelatedParty = 1,
				target.AddedBenefitQuality = source.AddedBenefitQuality,
				target.ClaimsClaims = source.ClaimsClaims,
				target.ClaimsQuality = 0,
				target.ClaimsAdmin = 0,
				target.QualityClaims = source.QualityClaims,
			    target.QualityQuality = source.QualityQuality,
				target.QualityAdmin = 0,
				target.AdminClaims = 0,
				target.AdminQuality = 0,
				target.AdminAdmin = source.AdminAdmin,
				target.ProfitClaims = 0,
				target.ProfitQuality = 0,
				target.ProfitAdmin = 0,
				target.RelatedPartyMedicalBenefit = source.Totalz1,
				target.RelatedPartyNonMedicalBenefit = source.Totalz2,
				target.BidServiceCatID = 35,
				target.TrueRelatedParty = 1,
				target.MSBEqualsOTC = 0,
				target.NonOTCMSB = 0,
				target.RelatedPartyMedicalClaims = source.Totalz1,
				target.RelatedPartyMedicalAdmin = 0,
				target.RelatedPartyMedicalProfit = 0,
				target.MbrCS = 0,
				target.Totalz1 = source.Totalz1,
				target.RelatedPartyNonMedicalAdmin = source.Totalz2,
				target.RelatedPartyNonMedicalSales = 0,
				target.RelatedPartyNonMedicalProfit = 0,
				target.Totalz2 = source.Totalz2,
				target.MSBsWith2LinesinPBP = 0,
				target.Capitated = 0,
				target.MbrCS1 = 0,
				target.TotalAllowed = 0,
				target.AllowedCovered = 0,
				target.AllowedNotCovered = 0,
				target.MSBModelID = NULL,
				target.MSBParentID = NULL,
				target.AddedBenefitCode = NULL,
				target.IsCap = 0,
				target.UtilUnitP1000Trend = 0,
				target.UtilBenefitPlanChange = 1,
				target.UtilPopulationChange = 1,
				target.UtilOtherFactor = 1,
				target.UCAProviderPaymentChange = 1,
				target.UCAOtherFactor = 1,
				target.NationwideMSBGranularity = NULL,
				target.DivisionMSBGranularity = NULL,
				target.RegionMSBGranularity = NULL,
				target.MarketMSBGranularity = NULL,
				target.StateMSBGranularity = NULL,
				target.PlanMSBGranularity = NULL,
				target.AddedBenefitProductType = NULL,
				target.AddedBenefitSNPType = NULL,
				target.BidServiceCategory = NULL,
				target.AddedBenefitVendor = NULL,
				target.RenewalType = 0,
				target.FinancialCost = 0,
				target.PriorYearFinancialCost = 0,
				target.FinDollarIncrease = 0,
				target.FinPercentIncrease = 0,
				target.BidCost = 0,
				target.PriorYearBidCost = 0,
				target.BidDollarIncrease = 0,
				target.BidPercentIncrease = 0,
				target.BidAllowedClaims = 0,
				target.Quality = 0,
				target.NonBenefitExpensePMPM = 0,
				target.SalesAndMarketingAdminPMPM = 0,
				target.RelatedPartyProfitPMPM = 0,
				target.FinancialUnitCost = 0,
				target.BidAllowedUnitCost = 0,
				target.AddedBenefitUtilization = 0,
				target.FinPriceIncreasefromPriorDistribution = 0,
				target.BidPriceIncreasefromPriorDistribution = 0,
				target.ExclusionsDivision = NULL,
				target.ExclusionsRegion = NULL,
				target.ExclusionsState = NULL,
				target.ExclusionsMarket = NULL,
				target.ExclusionsPlan = NULL,
				target.ExclusionSNP = NULL,
				target.LastUpdateByID = source.LastUpdateByID,
				target.LastUpdateDateTime =  source.LastUpdateDateTime
		WHEN NOT MATCHED THEN 
		INSERT 
		(
			PlanYearID,
			AddedBenefitTypeID,
			AddedBenefitName,
			AddedBenefitClaims,
			AddedBenefitAdmin,
			AddedBenefitSales,
			AddedBenefitProfit,
			HumanaOwnership,
			SalesMktExpensePMPM,
			DirectExpensePMPM,
			IndirectExpensePMPM,
			IsRelatedParty,
			AddedBenefitQuality,
			ClaimsClaims,
			ClaimsQuality,
			ClaimsAdmin,
			QualityClaims,
			QualityQuality,
			QualityAdmin,
			AdminClaims,
			AdminQuality,
			AdminAdmin,
			ProfitClaims,
			ProfitQuality,
			ProfitAdmin,
			RelatedPartyMedicalBenefit,
			RelatedPartyNonMedicalBenefit,
			BidServiceCatID,
			TrueRelatedParty,
			MSBEqualsOTC,
			NonOTCMSB,
			RelatedPartyMedicalClaims,
			RelatedPartyMedicalAdmin,
			RelatedPartyMedicalProfit,
			MbrCS,
			Totalz1,
			RelatedPartyNonMedicalAdmin,
			RelatedPartyNonMedicalSales,
			RelatedPartyNonMedicalProfit,
			Totalz2,
			MSBsWith2LinesinPBP,
			Capitated,
			MbrCS1,
			TotalAllowed,
			AllowedCovered,
			AllowedNotCovered,
			MSBModelID,
			MSBParentID,
		    AddedBenefitCode,
			IsCap,
			UtilUnitP1000Trend,
			UtilBenefitPlanChange,
			UtilPopulationChange,
			UtilOtherFactor,
			UCAProviderPaymentChange,
			UCAOtherFactor,
			NationwideMSBGranularity,
			DivisionMSBGranularity,
			RegionMSBGranularity,
			MarketMSBGranularity,
			StateMSBGranularity,
			PlanMSBGranularity,
			AddedBenefitProductType,
			AddedBenefitSNPType,
			BidServiceCategory,
			AddedBenefitVendor,
			RenewalType,
			FinancialCost,
			PriorYearFinancialCost,
			FinDollarIncrease,
			FinPercentIncrease,
			BidCost,
			PriorYearBidCost,
			BidDollarIncrease,
			BidPercentIncrease,
			BidAllowedClaims,
			Quality,
			NonBenefitExpensePMPM,
			SalesAndMarketingAdminPMPM,
			RelatedPartyProfitPMPM,
			FinancialUnitCost,
			BidAllowedUnitCost,
			AddedBenefitUtilization,
			FinPriceIncreasefromPriorDistribution,
			BidPriceIncreasefromPriorDistribution,
			ExclusionsDivision,
			ExclusionsRegion,
			ExclusionsState,
			ExclusionsMarket,
			ExclusionsPlan,
			ExclusionSNP,
			LastUpdateByID,
			LastUpdateDateTime
		)
	   VALUES 
			(source.PlanYearID, source.AddedBenefitTypeID, source.AddedBenefitName, source.AddedBenefitClaims, source.AddedBenefitAdmin,
			source.AddedBenefitSales, 0, 1, source.SalesMktExpensePMPM, 0, 0, 1, source.AddedBenefitQuality,
			source.ClaimsClaims, 0, 0, source.QualityClaims, source.QualityQuality, 0, 0, 0, source.AdminAdmin, 0, 0, 0, source.Totalz1,
			source.Totalz2, 35, 1, 0, 0, source.Totalz1, 0, 0, 0, source.Totalz1, source.Totalz2, 0, 0, source.Totalz2, 0, 0, 0, 0, 0,
			0, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0,
			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, source.LastUpdateByID, source.LastUpdateDateTime); 


		DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;


END
GO
