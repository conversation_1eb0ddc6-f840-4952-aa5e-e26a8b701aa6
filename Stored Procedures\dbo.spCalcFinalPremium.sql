SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- Stored Procedure

-- --------------------------------------------------------------------------------------------------------------------------------------------  


-- --------------------------------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: spCalcFinalPremium  
--  
-- AUTHOR: <PERSON> Cofie  
--  
-- CREATED DATE: 2007-Feb-05  
--  
-- DESCRIPTION: Stored procedure responsible for updating the CalcFinalPremium table.  
--  
-- PARAMETERS:  
--    Input:   
--        @ForecastID  
--        @UserID  
--    Output: NONE  
--  
-- RETURNS:   
--  
-- TABLES:  
--    Read:  
--        CalcBenchmarkSummary  
--        LkpProductType  
--        PerExtCMSValues  
--        LkpExtCMSMARegionHeader  
--        SavedMarketHeader  
--        LkpIntPlanYear  
--        SavedPlanDetail  
--        SavedPlanHeader  
--        fnGetRebatePercent  
--    fnGetCMSMARegionRate  

--    Write: CalcFinalPremium  
--  
-- VIEWS:  
--    Read: NONE  
--  
-- STORED PROCS:  
--    Executed: NONE  
--  
-- FUNCTIONS:  
--  Executed: fnPrePremium  
--  
-- $HISTORY   
-- --------------------------------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                                            DEVELOPER  
-- --------------------------------------------------------------------------------------------------------------------------------------------  
-- 2007-Feb-05       1          Initial Version                                                                         Christian Cofie  
-- 2008-Feb-11       2          Revised code to bring it into coding standards.                                         Shannon Boykin  
-- 2008-Apr-30       3          Added RPPO functionality.                                                               Brad Ennis  
--                                    -- Savings  
--                                    -- Rebate  
--                                    -- StandardizedBenchmark  
--                                    -- JOIN to SavedPlanDetail  
--                              Revised StandardizedBenchmark Calc & Savings.                                           Brian Lake  
-- 2008-May-01       4          Moved Savings calc out to fn                                                            Brad Ennis  
-- 2008-May-04       5          Forced rebate to decimal*.2)                                                            Sandy Ellis  
-- 2008-May-06       6          Updated Plan  Bench for RPPO                                                            Sandy Ellis  
-- 2008-Sep-19       7          Added @UserID to the list of parameters and replaced                                    Shannon Boykin  
--                                   all occurrences of SUSER_SNAME with @UserID.  
-- 2008-Sep-26       8          Added @UserID to the call of fnPrePremium and fnGetFinalPremiumSavings                 Shannon Boykin  
-- 2009-Feb-20      9          Replaced CalcPlanCountySummary with CalcBenchmarkSummary                             Mallika Eyunni  
-- 2009-Mar-19      10          Changed PlanYearID to small int.                                                        Brian Lake  
-- 2009-Mar-20      11          Removed delete statement                                                                Brian Lake  
-- 2009-Apr-01      12          Changed reference from PlanTypeName to PlanTypeID          Sandy Ellis  
-- 2009-Apr-16      13          Added DELETE statement back                                                             Brian Lake  
-- 2010-Feb-01  14   Corrected TotalReqRev                 Jake Gaecke  
-- 2010-Sep-28      15          Revised for 2012 database                                                               Michael Siekerka  
-- 2010-Sep-29      16          Revised for fnGetRebate, and renamed LkpExtCMSValues to PerExtCMSValues          Jake Gaecke  
-- 2010-Sep-29      17          Renamed LkpIntMarketHeader to SavedMarketHeader                                         Michael Siekerka  
-- 2010-Oct-04      18          Changed reference from RRegionID to MARegionID                                          Michael Siekerka  
-- 2010-Oct-06      19          Changed reference from LkpExtCMSMARegionRatebook to PerExtCMSRatebookRPPO               Jake Gaecke  
-- 2010-Dec-20  20   Changed reference from PerExtCMSRatebookRPPO to fnGetCMSMARegionRate     Jiao Chen  
-- 2011-Jan-24  21   Updated call for PlanYearID                Trevor Mahoney  
-- 2011-Jan-28  22   fnGetCMSMARegionRate(@ForecastID) now directly join instead of SELECT * FROM    Jiao Chen  
-- 2011-Mar-08  23   Set Uncollected Premium = 0 When plan is EGWP           Nate Jacoby  
-- 2011-Mar-08  24   Eliminated hardcoding ForecastID = 286             Trevor Mahoney  
-- 2011-Jun-14  25   Changed @PlanYearID to return SMALLINT instead of INT         Bobby Jaegers  
-- 2011-Jun-30  26   Introduced scalar function for uncollected premium          Trevor Mahoney  
-- 2011-Aug-22      27   Scalar function for uncollected premium removed.  Replaced with entire code    Trevor Mahoney  
--         from the scalar function itself.  
-- 2011-Sep-21  28   Scalar variable for UncollectedPremium created to account for being locked down   Bobby Jaegers  
--         on PerIntMAAssumptions  
-- 2011-Dec-08  29   Added CASE statement to correct for @UncollectedPremium coming through as NULL   Craig Wright  
-- 2011-Dec-08  30   Removed CASE statement. Instead, changed the minimum Range on LkpIntUncollectedPremium  
--         from 0 to -99999.   
-- 2012-Jan-25  31   Added CASE statement to correct for @UncollectedPremium coming through as NULL   Alex Rezmerski  
-- 2012-Aug-28  32   Added CASE statement to adjust Profit,TotalReqRev, and PlanBid for Group plans   Tim Gao  
--        if there EGWPMSB is not zero  
-- 2012 Dec-06  33   Comment the ProfitAdjustment               Tim Gao  
-- 2013-Feb-24  34   Added else statement for case statement for Plan Bid calculation      Trevor Mahoney  
-- 2013-Feb-28  35   0379 - Added in InsurerFee to UncollectedPremium, TotalReqRev, PlanBid,     Mason Roberts  
--        StandardizedBid, StandardizedBenchmark, and PlanBenchmark calculations  
--        also added InsurerFee to CalFinalPremium  
-- 2013-Mar-15  36   Added Uncollected Premium to InsurerFee and loaded prem.InsurerFeePercent to   Mason Roberts  
--        UncollectedPremium  
-- 2013-Jul-29  37   Added functionality to lock insurer fees after benchmark        Mike Deren  
-- 2013-Jul-30  38   Subtracted insurer fees from calculation of expenses in variable (avoid double count) Trevor Mahoney  
-- 2013-Nov=06  39   fnprepremium & fnGetUnspentRebateForEGWPMSB values are stored into Temp table (to aviod multiple calls)  Rajendran
-- 2016-Nov-29  40	 Add @InsurerFee, change prem.InsurerFee to @InsurerFee, update IsLocked logic						Chris Fleming
-- 2018-May-10	41	 Change LkpExtCMSPlanType to LkpProductType													Jordan Purdue
-- 2018-Oct-30	42	 Removed LkpIntMARegionMarketDetail															Jordan Purdue
-- 2019-Jun-28	43	 Replace SavedPlanHeader with SavedForecastSetup											Pooja Dahiya
-- 2019-Jul-30	44	 Update Uncollected Premium to refer to regression equation rather than lookup table.		Calice Robins
-- 2019-Oct-10  45   Add 'HUMAD\' to UserID when length is not 13 to fix sp crash                               Manisha Tyagi
-- 2019-oct-24	46	 Removed 'HUMAD\' to UserID																	Chhavi Sinha
-- 2019-Nov-06	47	Replaced savedforecastSetup with SavedplanHeader											Deepali Mittal
-- 2019-Dec-10   48	 Correcting MARegionID join so that RPPOs have a benchmark applied                          Brent Osantowski
-- 2019-Dec-19  49	 Correcting isEGWP logic to get data for it from  SavedplanHeader                           Pooja Dahiya
-- 2020-Jan-13	50	 Revising Uncollected Premium Calculation Methodology										Alex Beruscha
-- 2020-Jan-22	51	 Reverting Uncollected Premium Methodology to step function									Alex Beruscha
-- 2022-Feb-15  52   Excluding DSNP Plans from having Uncollected Member Premium                                Abraham Ndabian
-- 2022-Nov-22  53   Insert into table using temp table, added WITH(NOLOCK), added internal variables using @X  Khurram Minhas
-- 2023-Jan-18  54   Added Transaction																			Shivgopal
-- 2023-May-31  55   Added two new columns to CalcFinalPremium Table:TotalMemberPremium &  RoundedMemberPrem	Abraham Ndabian
-- 2023-Nov-01  56   Commented out calculation of the two new columns Table:TotalMemberPremium &  RoundedMemberPrem	
--                   Calculation was moved into spPlanRefresh procedure                                          Abraham Ndabian
-- 2024-Oct-17  57   Adding user id for spWritePlanRepriceLog SP                                                 Surya Murthy
-- --------------------------------------------------------------------------------------------------------------------------------------------  
CREATE PROCEDURE [dbo].[spCalcFinalPremium]  
(  
    @ForecastID INT,  
    @UserID CHAR(7)  
)  
AS  


    SET NOCOUNT ON;  

	--Declarations  
    DECLARE @XForecastID INT = @ForecastID,  
			@XUserID CHAR(7) = @UserID,			 
	        @UncollectedPremium FLOAT,  
	        @LastDateTime DATETIME,  
            @PlanYearID SMALLINT  ,
			@TotalMemberPremium FLOAT,
			@RoundedMemberPrem FLOAT

    SELECT @PlanYearID = dbo.fnGetBidYear()  
    SET  @LastDateTime = GETDATE()  
	----SET  @TotalMemberPremium = (Select TotalMemberPremium from fnAppGetRevenueAndPremium (@ForecastID))   -- new colun added by Abe 
	----SET  @RoundedMemberPrem  = (Select RoundedMemberPrem  from fnAppGetRevenueAndPremium (@ForecastID))   -- new colun added by Abe 


    -- Determine if Admin is Locked  
    DECLARE @locked INT   
    SET @locked = (SELECT IsLocked FROM SavedPlanHeader WITH(NOLOCK) WHERE ForecastID = @XForecastID)   

    SELECT * INTO #fnprepremium FROM fnprepremium(@XForecastID)  
    SELECT * INTO #fnGetUnspentRebateForEGWPMSB FROM fnGetUnspentRebateForEGWPMSB(@XForecastID)  

    DECLARE @Expenses DECIMAL(20,12)  
  SET @Expenses = (SELECT PreReqRev FROM #fnprepremium)  
 DECLARE @InsurerFeePercent DECIMAL (8,6)   
  SET @InsurerFeePercent = CASE WHEN @locked = 1 THEN 0   
         ELSE (SELECT InsurerFeePercent FROM PerExtCMSValues WITH(NOLOCK))  
         END  

	--Declare & Set Insurer Fee
	DECLARE @InsurerFee DECIMAL (8,6)
	SET @InsurerFee = 
	    CASE 
			WHEN @locked = 1 THEN ISNULL((SELECT InsurerFee FROM CalcFinalPremium WITH(NOLOCK) WHERE ForecastID = @XForecastID), 0)  
			ELSE (SELECT InsurerFee FROM #fnPrePremium)
		END 

BEGIN TRY 
	BEGIN TRANSACTION  
  --Uncollected Premium  
 SELECT @UncollectedPremium =  
  CASE @locked  
   WHEN 1  
    THEN ISNULL(cfp.UncollectedPremium,0)  
   ELSE Uncol.UncollectedPremium  
  END  
 FROM  
  (  
        SELECT   
   UncollectedPremium =  
    CASE   
     WHEN NewPlanPremium <= RangeMax   
     THEN InitialUncollectedPremium  
    ELSE  
     (  
     SELECT UncollectedPremium =   
      CASE   
       WHEN 0 = 1   
       THEN 0 
	   WHEN sph.IsSNP = 1 AND (spi.PlanName Like '%D-SNP%' OR lkp.SNPType = 'Dual Eligible')  THEN 0         -- updating case when to incorporate DSNP exclusion in the UM by Abe 2/15/2022
       ELSE ISNULL(up.UncollectedPremium,0)   
      END   
     FROM LkpIntUncollectedPremium up  WITH(NOLOCK)
     INNER JOIN SavedPlanHeader sph  WITH(NOLOCK)
      ON sph.ForecastID = @XForecastID  
	INNER JOIN SavedForecastSetup sfs WITH(NOLOCK)
     ON sfs.ForecastID= sph.ForecastID
    INNER JOIN SavedplanInfo spi WITH(NOLOCK)
     ON spi.PlanInfoID = sfs.PlanInfoID
	 INNER JOIN LkpSNPType lkp WITH(NOLOCK)
     ON spi.SNPTypeID = lkp.SNPTypeID
     WHERE NewPlanPremium >= RangeMin  
      AND  NewPlanPremium < RangeMax  
     )  
    END,  
   a.ForecastID   
  FROM  
   (  
   SELECT   
    InitialUncollectedPremium =   
     CASE   
      WHEN 0 = 1   
      THEN 0  
	  WHEN sph.IsSNP = 1 AND (sp.PlanName Like '%D-SNP%' OR lkp.SNPType = 'Dual Eligible') THEN 0          -- updating case when to incorporate DSNP exclusion in the UM by Abe 2/15/2022
      ELSE ISNULL(up.UncollectedPremium,0)   
     END,   
    RangeMax,  
    NewPlanPremium = PrePlanPremium + UncollectedPremium,  
    prem.ForecastID  
   FROM LkpIntUncollectedPremium up  WITH(NOLOCK)
   INNER JOIN SavedPlanHeader sph  WITH(NOLOCK)
    ON sph.ForecastID = @XForecastID  
   INNER JOIN #fnprepremium prem  WITH(NOLOCK)
     ON sph.ForecastID = prem.ForecastID 
   INNER JOIN SavedForecastSetup sf WITH(NOLOCK)
     ON sf.ForecastID= sph.ForecastID
   INNER JOIN SavedplanInfo sp WITH(NOLOCK)
     ON sp.PlanInfoID = sf.PlanInfoID	
   INNER JOIN LkpSNPType lkp WITH(NOLOCK)
    ON sp.SNPTypeID = lkp.SNPTypeID
   WHERE PrePlanPremium >= RangeMin  
    AND PrePlanPremium < RangeMax  
   ) a  
  ) Uncol  
  INNER JOIN CalcFinalPremium cfp  WITH(NOLOCK)
  ON cfp.ForecastID = Uncol.ForecastID


-------------------------------------------------------------------------------------------------------------  
------If there is Unspent Rebate, uncollected premium comes through as Null.  We need it to come through as 0  
-------------------------------------------------------------------------------------------------------------  
 SET @UncollectedPremium =  
  CASE   
   WHEN (@UncollectedPremium IS NULL)  
    THEN 0  
   ELSE @UncollectedPremium  
  END  
-------------------------------------------------------------------------------------------------------------  

    DELETE FROM dbo.CalcFinalPremium
    WHERE ForecastID = @XForecastID    

    -- Append new stuff  
	IF (SELECT OBJECT_ID('tempdb..#CalcFinalPremium')) IS NOT NULL
	BEGIN
        DROP TABLE #CalcFinalPremium;
    END

	CREATE TABLE #CalcFinalPremium
	(
	[PlanYearID] [int] NOT NULL,
	[ForecastID] [int] NOT NULL,
	[MedicareCoveredPct] [float] NULL,
	[AddedBenefitPct] [float] NULL,
	[CostShareReductionPct] [float] NULL,
	[Expenses] [float] NULL,
	[Profit] [float] NULL,
	[Reins] [float] NULL,
	[UserFee] [decimal](7, 6) NULL,
	[ConversionFactor] [decimal](38, 15) NULL,
	[TotalReqRev] [float] NULL,
	[PlanBid] [float] NULL,
	[StandardizedBid] [decimal](7, 2) NULL,
	[PlanAverageRiskRate] [decimal](38, 6) NULL,
	[StandardizedBenchmark] [float] NULL,
	[PlanBenchmark] [float] NULL,
	[Savings] [decimal](8, 2) NULL,
	[Rebate] [decimal](8, 2) NULL,
	[BasicMemberPremium] [float] NULL,
	[GovtPremiumAdj] [float] NULL,
	[PlanTypeName] [varchar](30) NULL,
	[UncollectedPremium] [float] NULL,
	[NetNonESRD] [float] NULL,
	[Allowed] [float] NULL,
	[RxBasicPremium] [decimal](8, 2) NULL,
	[RxSuppPremium] [decimal](8, 2) NULL,
	[LastUpdateByID] [char](7) NOT NULL,
	[LastUpdateDateTime] [datetime] NOT NULL,
	[InsurerFee] [decimal](8, 6) NULL
	);

     INSERT INTO #CalcFinalPremium  
     SELECT DISTINCT  
        @PlanYearID,   
        ForecastID,  
        MedicareCoveredPct,   
        AddedBenefitPct,   
        CostShareReductionPct,   
        Expenses,   
        Profit,   
        Reins,   
        UserFee,   
        ConversionFactor,   
        TotalReqRev,  
        PlanBid,  
        StandardizedBid,  
        PlanAverageRiskRate,   
        StandardizedBenchmark,  
        PlanBenchmark,  
        Savings = dbo.fnGetFinalPremiumSavings(ProductTypeID, PlanBenchmark, PlanBid, StandardizedBenchmark, StandardizedBid, ConversionFactor, @XUserID),  
        Rebate =   
            ROUND(  
                CONVERT(decimal(8,2),   
                    dbo.fnIsNegative(  
                        dbo.fnGetFinalPremiumSavings(ProductTypeID, PlanBenchmark, PlanBid, StandardizedBenchmark, StandardizedBid, ConversionFactor, @XUserID)  
                    , 0.0) )  
                * RebatePercent,  
            2),   
        BasicMemberPremium =  
            ROUND  
                (  
                    (  
                    CASE  
                        WHEN PlanBenchmark >= PlanBid THEN 0  
                        ELSE (StandardizedBid - StandardizedBenchmark)  
                    END  
                    ),  
                2),   
        GovtPremiumAdj =  
            ROUND  
                (  
                    (  
                    CASE  
                        WHEN PlanBenchmark >= PlanBid THEN 0  
                        ELSE (StandardizedBid - StandardizedBenchmark)  
                    END  
                    )  
                * (1 - ConversionFactor),  
                2),  
        ProductType,   
        @UncollectedPremium,   
        NetNonESRD,   
        Allowed,   
        RxBasicPremium,   
        RxSuppPremium,  
        @XUserID,  
        @LastDateTime,  
        [InsurerFee] = CASE WHEN @locked = 1 THEN @InsurerFee
					   ELSE InsurerFee   
					   END  
    FROM -- T1  temp table  
        (  
        SELECT -- Start of T1  
            prem.ForecastID,  
            prem.MedicareCoveredPct,   
            prem.AddedBenefitPct,   
            prem.CostShareReductionPct,   
            Expenses = prem.Expenses + (@UncollectedPremium * @InsurerFeePercent) / (1 - prem.ProfitFactor - @InsurerFeePercent),       
            Profit = CASE -- added to deal with group plan profit adjustment if EGWPMSB is not zero  
      WHEN 0 = 1 THEN   
        CASE WHEN (SELECT EGWPMSB FROM #fnGetUnspentRebateForEGWPMSB)> 0 THEN   
          prem.Profit + (@UncollectedPremium / (1 - prem.ProfitFactor - @InsurerFeePercent))*prem.ProfitFactor --+   
             --(SELECT ProfitAdjustment FROM fnGetProfitAdjForGroup(@XForecastID))  
        ELSE   
          prem.Profit + (@UncollectedPremium / (1 - prem.ProfitFactor - @InsurerFeePercent))*prem.ProfitFactor  
        END  

      ELSE prem.Profit + (@UncollectedPremium / (1 - prem.ProfitFactor - @InsurerFeePercent))*prem.ProfitFactor  
      END,   
            prem.Reins,  
            prem.UserFee,   
            prem.ConversionFactor,   
            RebatePercent = dbo.fnGetRebatePercent(@XForecastID),  
            TotalReqRev = CASE -- added to deal with group plan profit adjustment if EGWPMSB is not zero  
       WHEN 0 = 1 THEN   
         CASE WHEN (SELECT EGWPMSB FROM #fnGetUnspentRebateForEGWPMSB)> 0 THEN   
           (@Expenses + @UncollectedPremium / (1 - prem.ProfitFactor - @InsurerFeePercent)) ---+   
              --(SELECT ProfitAdjustment FROM fnGetProfitAdjForGroup(@XForecastID)))  
         ELSE  
           (@Expenses + @UncollectedPremium / (1 - prem.ProfitFactor - @InsurerFeePercent))  
         END  

       ELSE (@Expenses + @UncollectedPremium / (1 - prem.ProfitFactor - @InsurerFeePercent))   
        END,        

            PlanBid = CASE -- added to deal with group plan profit adjustment if EGWPMSB is not zero  
       WHEN 0 = 1 THEN   
         CASE WHEN (SELECT EGWPMSB FROM #fnGetUnspentRebateForEGWPMSB)> 0 THEN   
           ROUND(  
             (       
              (@Expenses + @UncollectedPremium  
              / (1 - prem.ProfitFactor - @InsurerFeePercent))* prem.MedicareCoveredPct  --- + (SELECT ProfitAdjustment FROM fnGetProfitAdjForGroup(@XForecastID)))  

             )  
            ,2)    
         ELSE  
           ROUND(  
             (       
              (@Expenses + @UncollectedPremium  
              / (1 - prem.ProfitFactor - @InsurerFeePercent))* prem.MedicareCoveredPct  --- + (SELECT ProfitAdjustment FROM fnGetProfitAdjForGroup(@XForecastID)))  

             )  
            ,2)     -----Added Case Statement (starting from ELSE) for when EGWPMSB = 0    
         END  

       ELSE  ROUND(  
          (       
           (@Expenses + @UncollectedPremium  
           / (1 - prem.ProfitFactor - @InsurerFeePercent))  
           * prem.MedicareCoveredPct  
          )  
         ,2)  
      END,       

            StandardizedBid =  
                ROUND(  
                    dbo.fnGetSafeDivisionResult(ROUND(  
                        (       
                            (@Expenses + @UncollectedPremium  
       / (1 - prem.ProfitFactor - @InsurerFeePercent))  
                            * prem.MedicareCoveredPct  
                        )  
                    ,2)  
                    , prem.ConversionFactor)  
                ,2),  
            bs.PlanAverageRiskRate,   
            StandardizedBenchmark =  
                (  
                CASE  
                    WHEN cpt.ProductTypeID = 3 --RPPO  
                        THEN   
                            dbo.fnSignificantDigits(  
                                (ratebook.MARegionRate  
                                   * cval.StatCompPercent)  
                                     +   (  
                                        CASE  
   WHEN (EstimatedPlanBidComponent IS NULL)  
                                                THEN  
                                                    ROUND(      
                                                        (@Expenses + @UncollectedPremium  
              / (1 - prem.ProfitFactor - @InsurerFeePercent))   
                                                        * dbo.fnGetSafeDivisionResult(prem.MedicareCoveredPct,prem.ConversionFactor)  
                                                    ,2)  
                                                    * (1 - cval.StatCompPercent)  
                                            ELSE  
                                                EstimatedPlanBidComponent * (1 - cval.StatCompPercent)  
                                        END  
                                        )  
                                ,  
                                15)  
                    ELSE prem.PreStandardizedBenchmark  
                END  
                ),  
            PlanBenchmark =  
                (  
                    (  
                    CASE  
                        WHEN cpt.ProductTypeID = 3 -- RPPO  
                            THEN   
                            dbo.fnSignificantDigits(  
                                (ratebook.MARegionRate  
                                   * cval.StatCompPercent)  
                                     +   (  
                                        CASE  
                                            WHEN (EstimatedPlanBidComponent IS NULL)  
                                                THEN  
                                                    ROUND(      
                                                        (@Expenses + @UncollectedPremium  
              / (1 - prem.ProfitFactor - @InsurerFeePercent))  
                                                        * dbo.fnGetSafeDivisionResult(prem.MedicareCoveredPct,prem.ConversionFactor)  
                                                    ,2)     
                                                    * (1 - cval.StatCompPercent)  
                                            ELSE  
                                                EstimatedPlanBidComponent   
                                                * (1 - cval.StatCompPercent)  
                                        END  
                                        )  
                                ,  
                                15)  
                        ELSE prem.PreStandardizedBenchmark  
                    END  
                    )  
                * prem.ConversionFactor  
                ),   
            cpt.ProductType,   
            cpt.ProductTypeID,   
            prem.NetNonESRD,   
            prem.Allowed,   
            prem.RxBasicPremium,   
            prem.RxSuppPremium,  
            InsurerFee = @InsurerFee + (@UncollectedPremium  * @InsurerFeePercent) / (1 - prem.ProfitFactor - @InsurerFeePercent)  
        FROM dbo.SavedPlanHeader sph 
        INNER JOIN CalcBenchmarkSummary bs WITH	(NOLOCK)
            ON bs.ForecastID = sph.ForecastID  
        INNER JOIN #fnprepremium prem  --Calling functon fnPrePremium to use as prem  
            ON prem.ForecastID = sph.ForecastID  
        INNER JOIN LkpProductType cpt WITH (NOLOCK)
            ON sph.PlanTypeID = cpt.ProductTypeID  
        CROSS JOIN PerExtCMSValues cval WITH (NOLOCK)
        LEFT JOIN  --ratebook  
        (  
            SELECT --Start of temp table ratebook  
                mh.MarketID ,  
                rr.MARegionRate  
            FROM dbo.SavedPlanHeader mh WITH (NOLOCK)

   INNER JOIN LkpExtCMSMARegionHeader rh WITH (NOLOCK)
    ON mh.MARegionID=rh.MARegionID  
   INNER JOIN fnGetCMSMARegionRate(@XForecastID) rr  
    ON mh.MARegionID = rr.MARegionID 
	WHERE mh.ForecastID = @XForecastID  
        ) ratebook -- End of temp table ratebook  
            ON sph.MarketID = ratebook.MarketID  
        LEFT JOIN SavedPlanDetail spd WITH (NOLOCK)
            ON spd.ForecastID = sph.ForecastID  
            AND spd.MARatingOptionID = 1  
        ) T1 -- End of temp table T1

	 INSERT INTO [dbo].[CalcFinalPremium] (
	 [PlanYearID],
     [ForecastID],
     [MedicareCoveredPct],
     [AddedBenefitPct],
     [CostShareReductionPct],
     [Expenses],
     [Profit],
     [Reins],
     [UserFee],
     [ConversionFactor],
     [TotalReqRev],
     [PlanBid], 
     [StandardizedBid],
     [PlanAverageRiskRate],
     [StandardizedBenchmark],
     [PlanBenchmark],
     [Savings],
	 [Rebate],
	 [BasicMemberPremium],
	 [GovtPremiumAdj],
	 [PlanTypeName],
	 [UncollectedPremium],
	 [NetNonESRD],
	 [Allowed],
	 [RxBasicPremium],
	 [RxSuppPremium],
	 [LastUpdateByID],
 	 [LastUpdateDateTime],
	 [InsurerFee],
     [TotalMemberPremium],      -- Initiliaze New Column
     [RoundedMemberPrem]        -- Initialize New Column
	 )
	SELECT cp.[PlanYearID] , 
	cp.[ForecastID],
	cp.[MedicareCoveredPct],
	cp.[AddedBenefitPct],
	cp.[CostShareReductionPct],
	cp.[Expenses],
	cp.[Profit],
	cp.[Reins],
	cp.[UserFee],
	cp.[ConversionFactor],
	cp.[TotalReqRev],
	cp.[PlanBid],
	cp.[StandardizedBid],
	cp.[PlanAverageRiskRate],
	cp.[StandardizedBenchmark],
	cp.[PlanBenchmark],
	cp.[Savings],
	cp.[Rebate],
	cp.[BasicMemberPremium],
	cp.[GovtPremiumAdj],
	cp.[PlanTypeName],
	cp.[UncollectedPremium],
	cp.[NetNonESRD],
	cp.[Allowed],
	cp.[RxBasicPremium],
	cp.[RxSuppPremium],
	@XUserID,  
    @LastDateTime,
	cp.[InsurerFee],
	@TotalMemberPremium,
	@RoundedMemberPrem
	FROM #CalcFinalPremium cp

	IF (SELECT OBJECT_ID('tempdb..#CalcFinalPremium')) IS NOT NULL
	BEGIN
        DROP TABLE #CalcFinalPremium;
    END

	COMMIT TRANSACTION; 
		 END TRY
		   BEGIN CATCH
		    ROLLBACK TRANSACTION; 
			  -- In the event of an error, we return the error details to the client  
		DECLARE	@ValidationMessage VARCHAR(MAX)
		DECLARE @ProcNumber INT
        SELECT @ValidationMessage = ERROR_MESSAGE()  
		SET @ProcNumber = 21
		EXEC dbo.spWritePlanRepriceLog @ForecastID,@ProcNumber, @ValidationMessage,@UserID  

	END CATCH;
GO
