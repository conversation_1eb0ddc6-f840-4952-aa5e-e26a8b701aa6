SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

CREATE PROCEDURE [PrePricing].[spGetPricingModelCYBYLastRefresh]
AS


SELECT FORMAT(MIN(LastUpdate),'yyyy-MM-dd HH:mm:ss') LastUpdate FROM (
	SELECT MAX(LastUpdateDateTime) LastUpdate FROM prepricing.PricingModelValueBidYear WITH (NOLOCK)
	UNION ALL
	SELECT MAX(LastUpdateDateTime) LastUpdate FROM prepricing.PricingModelValueCurrentYear WITH (NOLOCK)
) compare



GO
