SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetExportClaimsSelectionHeader]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetExportClaimsSelectionHeader
--
-- AUTHOR: Nate Jacoby
--
-- CREATED DATE: 2010-Oct-05
-- HEADER UPDATED: 2011-Feb-07
--
-- DESCRIPTION: Outputs Header information for the Export Claims Selections in the Model 
--
-- PARAMETERS:
--    Input:
--      @ForecastID
--      @ClaimForecastID
--      @C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Man
--      @TrendID
--
-- TABLES: 
--  Read:
--      SavedPlanInfo
--      LkpProductType
--  Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Oct-05      1           Initial Version                                                     Nate Jacoby
-- 2011-Feb-07      2           Added SavedCUHeader join                                            Joe Casey
-- 2011-Jun-02      3           Replaced LkpIntPlanYear with dbo.fnGetBidYear()                     Bobby Jaegers
-- 2011-Jun-14      4           Changed @PlanYearID to return SMALLINT instead of INT               Bobby Jaegers
-- 2012-Apr-15      5           Changed Data Type to Varchar(max) for Cost and Use cuts             Mike Deren
-- 2018-Apr-25      6           Modified LkpExtCMSPlanType for new UI table modifications           Jordan Purdue
-- 2020-Jun-29      7           Backend Alignement and Restructuring                                Keith Galloway
-- 2024-May-05      8			Added NOLOCK Table Hint												Kiran Kola
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetExportClaimsSelectionHeader]
    (
    @ForecastID INT,
    @ClaimForecastID CHAR(100),
    @ClaimForecastIDMan CHAR(100),
    @TrendID INT
    ) 

RETURNS @Results TABLE
    (
    PlanYearID SMALLINT,
    ForecastID INT,
    PlanTypeName CHAR(10),
    MarketID INT,
    ExperiencePlans VARCHAR(MAX),
    ManualPlans VARCHAR(MAX),
    ExperienceClaimProjectionID CHAR(100),
    ManualClaimProjectionID CHAR(100),
    TrendID INT,
    DownloadDateTime DATETIME
    ) AS

BEGIN

------------------------------------------------------------------------------------------------------------------------
-- PlanYearID -----------------------------------------------------------------------------------------------------------  
DECLARE @PlanYearID SMALLINT
SELECT @PlanYearID = SPI.PlanYear 
FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)
INNER JOIN dbo.SavedPlanInfo SPI  WITH (NOLOCK)
    ON SPI.PlaninfoID = sfs.PlaninfoID
WHERE SFS.forecastID = @ForecastID

-------------------------------------------------------------------------------------------------------------------------
-- Get C&U Plans --------------------------------------------------------------------------------------------------------         
        DECLARE @ExpPlans VARCHAR(MAX)
        SELECT @ExpPlans = COALESCE(@ExpPlans,'') + CAST(SPI.CPS AS VARCHAR(13)) + ', '
        FROM dbo.SavedPlanDFSummary DFS
		INNER JOIN dbo.SavedPlanInfo SPI
			ON DFS.PlanInfoID = SPI.PlanInfoID
        WHERE DFS.ForecastID = @ForecastID
            AND DFS.MARatingOptionID = 1
            
        SET @ExpPlans = (SELECT LEFT(@ExpPlans,LEN(@ExpPlans)-1))
        
        
        DECLARE @ManPlans VARCHAR(MAX)
        SELECT @ManPlans = COALESCE(@ManPlans,'') + CAST(SPI.CPS AS VARCHAR(13)) + ', '
        FROM dbo.SavedPlanDFSummary DFS
		INNER JOIN dbo.SavedPlanInfo SPI
			ON DFS.PlanInfoID = SPI.PlanInfoID
        WHERE DFS.ForecastID = @ForecastID
            AND DFS.MARatingOptionID = 2
                
        SET @ManPlans = (SELECT LEFT(@ManPlans,LEN(@ManPlans)-1))  

-------------------------------------------------------------------------------------------------------------------------
-- @Results Table -------------------------------------------------------------------------------------------------------    

    INSERT @RESULTS
    SELECT
        @PlanYearID AS PlanYearID, 
        @ForecastID AS ForecastID, 
        LPT.ProductType AS PlanTypeName, 
        SPI.ActuarialMarketID AS MarketID,
        @ExpPlans AS ExperiencePlans,
        @ManPlans AS ManualPlans, 
        @ClaimForecastID AS ExperienceClaimProjectionID,
        @ClaimForecastIDMan AS ManualClaimProjectionID,
        @TrendID AS TrendID,
        GETDATE() AS DownloadDateTime
    FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)
	LEFT JOIN dbo.SavedPlanInfo SPI  WITH (NOLOCK)
	  ON SFS.PlanInfoID = SPI.PlanInfoID
    INNER JOIN dbo.LkpProductType LPT
        ON LPT.ProductTypeID = SPI.ProductTypeID
    WHERE SFS.ForecastID =  @ForecastID

    RETURN
END
GO
