SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------              
-- PROCEDURE NAME: [dbo].[spAppTrendGetActuarialAdjReportingCategoryExtract]    
--              
-- TYPE: SAME              
--              
-- AUTHOR: Chhavi            
--              
-- CREATED DATE: 2020-Mar-31	             
--              
-- DESCRIPTION: Procedure responsible for retrieving [TrendActuarialAdj_ReportingCategory infomation based on extract type              
--    by ForecastID.              
--              
-- PARAMETERS:              
-- Input:              
--     @ForecastID              
-- TABLES:               
-- Read:              
--                  
--                 
-- Write:              
--              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:              
--                
-- $HISTORY               
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE			VERSION		CHANGES MADE                 DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Mar-31		1		Initial Version				Deepali  
-- 2020-May-26		2		Removed AdjustmentSybType	Ramandeep   
-- 2020-Jun-10	   3 	Renamed the sp name in the       Kiran Pant
--                    comment section with prefix spAppTrend
-- 2021-Nov-09      4     Included Last Updated column in export file     Ramandeep Saini
-- 2023-Jan-05      5     Excluding zeros while export    Surya Murty
-- 2024-Sep-17      6     Added LastUpdateByID to the Second select for Exports   Chaitanya Dugra
-- ----------------------------------------------------------------------------------------------------------------------              
CREATE PROCEDURE [dbo].[spAppTrendGetActuarialAdjReportingCategoryExtract]          
 @ForecastID VARCHAR(MAX),
 @LastUpdateByID     VARCHAR(7)     
AS   
BEGIN
BEGIN TRY
	 BEGIN TRANSACTION

  SELECT Sp.CPS,Sp.PlanYear,Sp.PlanInfoID INTO #tempPlanInfoId FROM dbo.SavedPlanInfo Sp WITH (NOLOCK)
 INNER JOIN dbo.SavedForecastSetup SS WITH (NOLOCK) ON SS.PlanInfoID=Sp.PlanInfoID WHERE 
		SS.ForecastID IN (SELECT  Value FROM    dbo.fnStringSplit(@ForecastID, ',') )   
		AND sp.PlanYear = dbo.fnGetBidYear()
		ORDER BY CHARINDEX(CAST(SS.ForecastID AS VARCHAR), @ForecastID)
SELECT 
bc.AdjustmentID,bc.AdjustmentDescription, bc.CPS,bc.PlanYearID, bc.TrendYearID,bc.RateType, bc.ReportingCategory, bc.CostAdjustment, bc.UseAdjustment,bc.LastUpdateByID,bc.LastUpdateDateTime
FROM dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat bc WITH (NOLOCK) JOIN #tempPlanInfoId pl ON bc.PlanInfoID=pl.PlanInfoID
WHERE bc.CostAdjustment <> 0 or bc.UseAdjustment <>0  
COMMIT TRANSACTION;
	
END TRY    
BEGIN CATCH

		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			

	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
	ROLLBACK TRANSACTION; 
	---Insert into app log for logging error------------------
	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID;
 END CATCH;  
END
GO
