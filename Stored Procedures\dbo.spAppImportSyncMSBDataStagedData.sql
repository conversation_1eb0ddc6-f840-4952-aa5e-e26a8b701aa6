﻿SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportSyncMSBDataStagedData
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-Aug-05      1			Initial Version						                                Archana Sahu
-- 2024-Sep-05      2           Rename Json data from Sync Base MSB Details to Base MSB Details     Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------

CREATE  PROCEDURE [dbo].[spAppImportSyncMSBDataStagedData]
(@StageId VARCHAR(100))
AS
BEGIN
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

     DECLARE @tbl__importData TABLE
    (
        PlanYearID INT,
        PlanInfoID INT,
        CPS VARCHAR(13),
        MSBCode VARCHAR(6),
        BenefitCategoryID INT,
		LastUpdateDateTime DATETIME,
        LastUpdateByID CHAR(7)
    );

    INSERT INTO @tbl__importData
    SELECT PlanYearID,
           PlanInfoID,
           CPS,
           MSBCode,
           BenefitCategoryID,
		   GETDATE(),
           @UserId
    FROM
        OPENJSON(@jsonData, '$."Base MSB Details"')
        WITH
        (
            PlanYearID INT,
			PlanInfoID INT,
			CPS VARCHAR(13),
			MSBCode VARCHAR(6),
			BenefitCategoryID INT
        );

	DELETE lkp FROM dbo.LkpIntMSBDetail lkp JOIN @tbl__importData msb
		ON lkp.PlanYearID = msb.PlanYearID;
		
		INSERT INTO dbo.LkpIntMSBDetail
		(
		    PlanYearID,
		    PlanInfoID,
		    CPS,
		    MSBCode,
		    BenefitCategoryID,
		    LastUpdateDateTime,
		    LastUpdateByID
		)
		SELECT PlanYearID, PlanInfoID, CPS, MSBCode, BenefitCategoryID,LastUpdateDateTime, LastUpdateByID  FROM @tbl__importData


	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
GO
