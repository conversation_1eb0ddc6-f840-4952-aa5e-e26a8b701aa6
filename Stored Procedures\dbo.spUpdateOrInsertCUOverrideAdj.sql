
/****** Object:  StoredProcedure [dbo].[spUpdateOrInsertCUOverrideAdj]    Script Date: 7/8/2021 2:23:48 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateOrInsertCUOverrideAdj
--
-- CREATOR: Joe Casey
--
-- CREATED DATE: 2010-Aug-25
-- HEADER UPDATED: 2011-Feb-04
--
-- DESCRIPTION: Stored Procedure responsible for updating or inserting the multiplicative adjustment factors for 
--				Cost and Use data.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@MARatingOptionID
--		@BenefitCategoryID
--		@<PERSON><PERSON><PERSON><PERSON><PERSON>actor
--		@AllowedAdjFactor
--		@UserID
--	Output:
--
-- TABLES:
--	Read:
--		LkpIntBenefitCategory
--		SavedCUOverrideAdj
--	Write:
--		SavedCUOverrideAdj
--		SavedPlanHeader
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetBidYear
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Aug-25		1			Initial Version														Joe Casey
-- 2010-Aug-26		2			Checks to see if all factors are 1.0.  If they are, delete that		Joe Casey
--									plan from SavedCUOverrideAdj because it is unnecessary.
-- 2010-Sep-14      3           Added PlanYearID                                                    Joe Casey
-- 2011-Feb-04		4			Added SavedPlanHeader update										Joe Casey
-- 2011-Jun-14		5			Changed PlanYearId to return SMALLINT instead of INT				Bobby Jaegers
-- 2016-Sep-21		6			Changed datatype for UnitsAdjFactor and AllowedAdjFactor			Deepali Mittal
-- 2017-Jan-4		7			Added additive adjustments											Jordan Purdue
-- 2019-Jun-28		8			Replace SavedPlanHeader with SavedForecastSetup			            Pooja Dahiya
-- 2019-Oct-30	    9           Replace @UserID from char(13) to char(7)							Chhavi Sinha
-- 2021-Jul-07		10			Revisions for MSBs now in LkpIntBenefitCategory						Alex Beruscha
-- 2022-Feb-28      11          Updating spCalcPlanBase on SavedCUOverrideAdj Upload in Bid Model   Abraham Ndabian    
-- ----------------------------------------------------------------------------------------------------------------------
CREATE  PROCEDURE [dbo].[spUpdateOrInsertCUOverrideAdj]
    @ForecastID INT,
	@MARatingOptionID TINYINT,
	@BenefitCategoryID INT,
	@UnitsAdjFactor DECIMAL(13,6) = NULL,
	@AllowedAdjFactor DECIMAL(13,6) = NULL,
	@Justification VARCHAR(200),
	@UnitsAddAdj DECIMAL(13,6) = NULL,
	@AllowedAddAdj DECIMAL(13,6) = NULL,
	@AllowedAddAdjNonSeq DECIMAL(13,6) = NULL,
	@UserID CHAR(7)
AS

SET NOCOUNT ON;

DECLARE @PlanYearID SMALLINT
SELECT @PlanYearID = dbo.fnGetBidYear()

DECLARE @LastDateTime DATETIME
SET @LastDateTime = GETDATE()

IF NOT EXISTS (SELECT 1 FROM SavedCUOverrideAdj WHERE ForecastID = @ForecastID AND MARatingOptionID = @MARatingOptionID)
	--This plan hasn't been adjusted before, so create a full record in SavedCUOverrideAdj that will be updated
	BEGIN
		--Insert new record with adj value for the input Benefit Category only
		INSERT INTO SavedCUOverrideAdj
		SELECT
		    @PlanYearID,
			@ForecastID,
			@MARatingOptionID,
			@BenefitCategoryID,
			ISNULL(@UnitsAdjFactor,1),
			ISNULL(@AllowedAdjFactor,1),
			@Justification,
			@UserID,
			@LastDateTime,
			ISNULL(@UnitsAddAdj,0),
			ISNULL(@AllowedAddAdj,0),
			ISNULL(@AllowedAddAdjNonSeq,0)
	
		--Insert factors of 1.0 to complete the rest of the Benefit Categories for this Plan Index
		INSERT INTO SavedCUOverrideAdj
		SELECT
		    @PlanYearID,
			@ForecastID,
			@MARatingOptionID,
			BenefitCategoryID,
			1,
			1,
			NULL,
			@UserID,
			@LastDateTime,
			0,
			0,
			0
		FROM LkpIntBenefitCategory
		WHERE IsEnabled = 1
			AND IsUsed = 1
			AND BenefitCategoryID <> @BenefitCategoryID
			AND BenefitCategoryID > 0
			AND BenefitCategoryID < 1000
	END
ELSE
	--This plan's factors have been updated before, re-update factors
	BEGIN
		IF @UnitsAdjFactor IS NOT NULL
			BEGIN
				UPDATE SavedCUOverrideAdj
				SET UnitsAdjFactor = @UnitsAdjFactor,
				    Justification = @Justification,
					LastUpdateByID = @UserID,
					LastUpdateDateTime = @LastDateTime
				WHERE ForecastID = @ForecastID
					AND MARatingOptionID = @MARatingOptionID
					AND BenefitCategoryID = @BenefitCategoryID
			END
		IF @AllowedAdjFactor IS NOT NULL
			BEGIN
				UPDATE SavedCUOverrideAdj
				SET AllowedAdjFactor = @AllowedAdjFactor,
				    Justification = @Justification,
					LastUpdateByID = @UserID,
					LastUpdateDateTime = @LastDateTime
				WHERE ForecastID = @ForecastID
					AND MARatingOptionID = @MARatingOptionID
					AND BenefitCategoryID = @BenefitCategoryID
			END
		IF @UnitsAddAdj IS NOT NULL
			BEGIN
				UPDATE SavedCUOverrideAdj
				SET UnitsAddAdj = @UnitsAddAdj,
				    Justification = @Justification,
					LastUpdateByID = @UserID,
					LastUpdateDateTime = @LastDateTime
				WHERE ForecastID = @ForecastID
					AND MARatingOptionID = @MARatingOptionID
					AND BenefitCategoryID = @BenefitCategoryID
			END
		IF @AllowedAddAdj IS NOT NULL
			BEGIN
				UPDATE SavedCUOverrideAdj
				SET AllowedAddAdj = @AllowedAddAdj,
				    Justification = @Justification,
					LastUpdateByID = @UserID,
					LastUpdateDateTime = @LastDateTime
				WHERE ForecastID = @ForecastID
					AND MARatingOptionID = @MARatingOptionID
					AND BenefitCategoryID = @BenefitCategoryID
			END
		IF @AllowedAddAdjNonSeq IS NOT NULL
			BEGIN
				UPDATE SavedCUOverrideAdj
				SET AllowedAddAdjNonSeq = @AllowedAddAdjNonSeq,
				    Justification = @Justification,
					LastUpdateByID = @UserID,
					LastUpdateDateTime = @LastDateTime
				WHERE ForecastID = @ForecastID
					AND MARatingOptionID = @MARatingOptionID
					AND BenefitCategoryID = @BenefitCategoryID
			END													
	END

--Checks to see if all factors are 1.0.  If they are, it is unnecessary to have it in SavedCUOverrideAdj, so delete.
IF NOT EXISTS
	(SELECT UnitsAdjFactor, UnitsAddAdj, AllowedAdjFactor, AllowedAddAdj, AllowedAddAdjNonSeq FROM SavedCUOverrideAdj
	WHERE ForecastID = @ForecastID AND MARatingOptionID = @MARatingOptionID AND (UnitsAdjFactor <> 1 OR UnitsAddAdj <> 0 OR AllowedAdjFactor <> 1 OR AllowedAddAdj <> 0 OR AllowedAddAdjNonSeq <> 0))
				BEGIN
					DELETE FROM SavedCUOverrideAdj
					WHERE ForecastID = @ForecastID AND MARatingOptionID = @MARatingOptionID
				END

-- Updating spCalcPlanBase so that SavedCUOverrideAdj data flows through the Bid Model by Abe 2/28/2022
	--DECLARE @ForecastID INT
	--SET @ForecastID = @ForecastID
	EXEC spCalcPlanBase    @ForecastID , @MARatingOptionID , @UserID --1=Experience, 2=Manual, 3=Both            
	
BEGIN
	UPDATE SavedForecastSetup
    SET IsToReprice = 1,
        LastUpdateByID =@UserID,
        LastUpdateDateTime = @LastDateTime
    WHERE ForecastID = @ForecastID
END
GO

