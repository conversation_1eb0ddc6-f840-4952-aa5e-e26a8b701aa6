SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Stored Procedure

-- ----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: spUpdateOrInsertMAReportPlanLevel    
--    
-- CREATOR: <PERSON>    
--    
-- CREATED DATE: 2010-Oct-06    
-- HEADER UPDATED: 2011-Mar-01    
--    
-- DESCRIPTION: Stored Procedure responsible for updating MAReportPlanLevel at the end of reprice.    
--    
-- PARAMETERS:    
-- Input:    
--  @ForecastID  
-- Output:    
--    
-- TABLES:    
-- Read:   
-- SavedForecastSetup  
-- SavedPlanInfo  
-- SavedPlanDetail  
-- SavedPlanMemberMonthDetail  
-- SavedPlanStateCountyDetail  
-- SavedPlanAssumptions  
-- SavedMarketInfo  
-- SavedRegionInfo  
-- SavedPlanAddedBenefits  
-- Benefits_SavedBenefitOption  
-- AdminUserHeader  
-- LkpPlanType  
-- LkpIntDemogIndicators  
-- LkpExtSNPType  
-- LkpProductType  
-- LkpIntAddedBenefitExpenseDetail  
-- PerExtContractNumberHeader  
-- CalcFinalPremium  
--  
-- Write:  MAReportPlanLevel  
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
--      fnAppGetBidSummary  
--      fnGetSafeDivisionResult  
--  fnGetMemberMonthsAndAllowedByDuals  
--  fnGetMARebateAllocation  
--  fnAppGetMABPTWS4Expenses  
--    
-- STORED PROCS:    
--    
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE             VERSION   CHANGES MADE                                                                  DEVELOPER      
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2010-Oct-06      1         Initial Version                                                               Jake Gaecke    
-- 2010-Oct-06      2         Removed fnGetMAReportPLInfo and actually added coding from that               Joe Casey    
--                              function.  Added PlanYearID to deletion on MAReportPlanLevel.    
-- 2010-Nov-03      3         Added SNPType, MarketingSales, DirectAdmin, InDirectmin, Profit               Jiao Chen  
--                              PlanBenchmark, TotalReqRev, RPPOPBC to the return table.          
-- 2011-Feb-14      4         RPPOPBC now reads directly from EstimatedPlanBidComponent                     Jiao Chen    
-- 2011-Mar-01      5         Added Part B Deductible                                                       Joe Casey    
-- 2011-Jun-02      6         Replaced LkpIntPlanYear with dbo.fnGetBidYear()                               Bobby Jaegers    
-- 2011-Jun-14      7         Changed @PlanYearID to return SMALLINT instead of INT                         Bobby Jaegers    
-- 2011-Dec-13      8         Added OOPC and TBC status                                                     Trevor Mahoney    
-- 2012-Feb-24      9         Made changes to account for new admin buckets                                 Alex Rezmerski    
-- 2012-Mar-12      10        Changed CROSS APPLY to OOPC functions to INNER JOIN on tables                 Trevor Mahoney    
-- 2012-Apr-29      11        Changed INNER JOIN on SavedOOPCHeader to a LEFT JOIN                          Trevor Mahoney    
-- 2012-Dec-12      12        Added CombinedDeductibleIndicator to the table                                Mason Roberts    
-- 2013-Apr-02      13        Total Restructure, removing fnappgetgeneralmabptvalues                        Mike Deren    
-- 2013-APr-15      14        Add InsurerFees to admins                                                     Tim Gao    
-- 2013-Apr-15      15        Adjusted calculation for Direct Admin to match model                          Lindsay Allen    
-- 2013-Apr-15      16        Add Insurer Fees to taxes and Fees                                            Mike Deren    
-- 2013-Oct-07      17        Included Join on Segment ID                                                   Manisha Tyagi  
-- 2014-Feb-27      18        SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals      Mike Deren   
-- 2017-Jun-20      19        Remove join for PerExtContractNumberPlanIDDetail and change PlanName          Chris Fleming  
--                              to reference new column added to SavedPlanHeader       
-- 2017-Sep-13      20        Removed parameter in fnGetMemberMonthsAndAllowedByDuals                       Chris Fleming  
-- 2018-May-10      21        Changed LkpExtPlanType to LkpExtProductType                                   Jordan Purdue  
-- 2018-Sep-19      22        Modified join for CertifyingActuaryUserID                                     Apoorva Nasa  
-- 2019-Feb-12      23        Removed OOPCStatus and TBC Status columns from MAReportPlanLevel              Kiran Pant  
-- 2019-Jun-28      24        Replace SavedPlanHeader with SavedForecastSetup,removed code for              Pooja Dahiya  
--                              a) ExpBasePlanIndex b) MARatingOptionID c) IsCombinedDeductible   
--                              d) AltRebateOrder                   
-- 2019-Jul-05      25        Made changes for market, region table.                                        Satyam Singhal     
-- 2019-Nov-06      26        replacedSavedForecastSetup With SavedPlanHeader                               Deepali  
-- 2019-Dec-19      27        Revert version 24                                                             Pooja Dahiya  
-- 2019-Dec-20      28        Corrected Logic to make similar like Prod                                     Deepali  
-- 2020-Sep-01      29        Removed Case statements regarding AltRebateOrder                              Brent Osantowski  
-- 2020-Sep-28      30        Backend Alignment and Restructuring                                           Keith Galloway  
-- 2022-Apr-06      31        SNPTypeName Issue from LkpExtSNPType table fixed by using                     Abraham Ndabian  
--                            an updated LkpSNPType table with new added SNPTypeName column              
-- 2022-May-01      32        MAAUI migration; replaced reference from PlanIndex to ForecastID; replaced   
--                            table SavedPlanDeductibleMOOPDetail with Benefits_SavedBenefitOption, joining   
--                            on PlanInfoID & BenefitOptionID; added logic for pulling PartBDeductible and  
--                            IN/OONDeductible; removed table SavedIsCombinedDeductiblePlan - variable   
--                            IsCombinedDeductible now genarated from Benefits_SavedBenefitOption; removed   
--                                                                            nested queries                Aleksandar Dimitrijevic  
-- 2022-Dec-16      33        4119454 Deployment objects from Dev-Grow          Aleksandar Dimitrijevic  
-- 2022-Oct-26      34        Add internal variables for input parameters                                   Khurram Minhas  
--                            Added With (NOLOCK)  
--                            Remove temp tables  
-- 2022-Nov-21      35        Created temp table #MAReportPlanLevel to hold final data and added data in original table at the   
--                                                                last as a single select statement.           Sheetal Patil  
-- 2023-Jan-18      36		  Added Transaction																   Shivgopal
-- 2023-Feb-23      37        Re-committing changes made in version 31 overwritten by subsequent changes       Abraham Ndabian  
-- 2024-Jul-02		38		  Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table		   Surya Murthy
-- 2024-Oct-17      39        Adding user id for spWritePlanRepriceLog SP                                  Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------------------  

CREATE PROCEDURE [dbo].[spUpdateOrInsertMAReportPlanLevel]    
    @ForecastID INT,
	@UserID VARCHAR(7)
AS    

SET NOCOUNT ON;    

BEGIN    
    DECLARE @PlanYearID SMALLINT ,  
 @XForecastID INT = @ForecastID   
 SELECT @PlanYearID = SPI.PlanYear  
 FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)  
 LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)  
  ON SPI.PlanInfoID = SFS.PlanInfoID  
 WHERE SFS.ForecastID = @XForecastID  

 DECLARE @MAPD int  
 SELECT @MAPD=PlanTypeID --= 2 (1='MA Only';2='MAPD')  
 FROM dbo.LkpPlanType WITH (NOLOCK)  
 WHERE PlanType='MAPD'   

    DECLARE @AuditTime DATETIME    
    SET @AuditTime = GETDATE()    

BEGIN TRY 
	   BEGIN TRANSACTION  

    --Delete old record    
    DELETE FROM dbo.MAReportPlanLevel 
    WHERE ForecastID = @XForecastID    
        AND PlanYearID = @PlanYearID   

-----************************* MM     

    DECLARE     

            @ExperienceMemberMonths INT,    
            @ExperienceNonDualMemberMonths INT,    
            @ExperienceDualMemberMonths INT,    
            @ExperienceESRDMemberMonths INT,    
            @ExperienceBlendedRiskFactor DECIMAL (18,15),    
            @ExperienceNonDualRiskFactor DECIMAL (18,15),    
            @ExperienceDualRiskFactor DECIMAL (18,15),    

            @AltBaseExpMemberMonths INT,    
            @AltBaseExpNonDualMemberMonths INT,    
            @AltBaseExpDualMemberMonths INT,    
            @AltBaseExpProjectedESRDMemberMonths INT,    
            @AltBaseExpESRDMemberMonths INT,    
            @AltBaseExpBlendedRiskFactor DECIMAL (18,15),    
            @AltBaseExpNonDualRiskFactor DECIMAL (18,15),    
            @AltBaseExpDualRiskFactor DECIMAL (18,15),    
            @ProjectedESRDMemberMonths DECIMAL (30,15),    
            @EstimatedPlanBidComponent DECIMAL(7, 2),    
            @AlternateBaseForecastID INT    

        SET @AlternateBaseForecastID = @XForecastID     

        SELECT @EstimatedPlanBidComponent = MAX(ISNULL(EstimatedPlanBidComponent, 0))    
  FROM dbo.SavedPlanDetail  spd WITH (NOLOCK)  
  WHERE spd.ForecastID = @XForecastID    


            BEGIN    
                SELECT     
                    @ExperienceMemberMonths =     
                        NonDualBiddableMemberMonths + DualBiddableMemberMonths,     
                    @ExperienceNonDualMemberMonths = NonDualBiddableMemberMonths,     
                    @ExperienceDualMemberMonths = DualBiddableMemberMonths,    
                    @ExperienceESRDMemberMonths = ESRDNonDualMemberMonths + ESRDDualMemberMonths,    
                    @ExperienceBlendedRiskFactor =     
                        CASE (NonDualBiddableMemberMonths + DualBiddableMemberMonths)    
                            WHEN 0 THEN 0     
                            ELSE     
                                (NonDualBiddableMemberMonths * NonDualRiskFactor    
                                    + DualBiddableMemberMonths * DualRiskFactor)    
                                / (NonDualBiddableMemberMonths + DualBiddableMemberMonths)    
                        END,    
                    @ExperienceNonDualRiskFactor =     
                        CASE NonDualBiddableMemberMonths    
                            WHEN 0 THEN 0    
                            ELSE    
                                NonDualBiddableMemberMonths * NonDualRiskFactor    
                                / NonDualBiddableMemberMonths    
                        END,    
                    @ExperienceDualRiskFactor =     
                        CASE DualBiddableMemberMonths    
                            WHEN 0 THEN 0     
                            ELSE     
                                DualBiddableMemberMonths * DualRiskFactor    
                                / DualBiddableMemberMonths    
                        END    
                FROM dbo.fnGetMemberMonthsAndAllowedByDuals (@XForecastID, 1) --Experience    

                IF @AlternateBaseForecastID IS NOT NULL    
                BEGIN    
                    SELECT     
                        @AltBaseExpMemberMonths =     
                            NonDualBiddableMemberMonths + DualBiddableMemberMonths,     
                        @AltBaseExpNonDualMemberMonths = NonDualBiddableMemberMonths,     
                        @AltBaseExpDualMemberMonths = DualBiddableMemberMonths,    
                        @AltBaseExpESRDMemberMonths = ESRDNonDualMemberMonths + ESRDDualMemberMonths,    
                        @AltBaseExpBlendedRiskFactor =     
                            CASE (NonDualBiddableMemberMonths + DualBiddableMemberMonths)    
                                WHEN 0 THEN 0     
                                ELSE     
                                    (NonDualBiddableMemberMonths * NonDualRiskFactor    
                                        + DualBiddableMemberMonths * DualRiskFactor)    
                                    / (NonDualBiddableMemberMonths + DualBiddableMemberMonths)    
                            END,    
                        @AltBaseExpNonDualRiskFactor =     
                            CASE NonDualBiddableMemberMonths    
                                WHEN 0 THEN 0    
                                ELSE    
                                    NonDualBiddableMemberMonths * NonDualRiskFactor    
                                    / NonDualBiddableMemberMonths    
                            END,    
                        @AltBaseExpDualRiskFactor =     
                            CASE DualBiddableMemberMonths    
                                WHEN 0 THEN 0     
                                ELSE     
                                    DualBiddableMemberMonths * DualRiskFactor    
                                    / DualBiddableMemberMonths    
                            END    
     FROM dbo.fnGetMemberMonthsAndAllowedByDuals (@AlternateBaseForecastID, 1) --Alternate Base Experience    
                END    
            END     


        ------------------------ ******    



 SELECT -- This is the ESRDMembership Total that is put in cell U15 on the MA Bnchmk tab.    
  @ProjectedESRDMemberMonths = SUM(spmm.MemberMonths)   
 FROM dbo.SavedPlanMemberMonthDetail spmm  WITH (NOLOCK)  
 INNER JOIN dbo.SavedPlanStateCountyDetail spscd  WITH (NOLOCK)  
  ON spmm.ForecastID = spscd.ForecastID    
    AND spmm.StateTerritoryID = spscd.StateTerritoryID    
    AND spmm.CountyCode = spscd.CountyCode    
 INNER JOIN dbo.LkpIntDemogIndicators di  WITH (NOLOCK)  
  ON spmm.DemogIndicator = di.DemogIndicator    
 WHERE spmm.ForecastID = @XForecastID    
   AND spscd.IsCountyExcludedFromBPTOutput = 0    
   AND di.DualEligibleTypeID <> 2    
   AND di.IsESRD = 1    
   AND di.IsHospice = 0     


--- ************** INSERT INTO **************************     


   --Added Benefits  

   SELECT spab.ForecastID,    
       SUM(added.AddedBenefitQuality) MSBQualityPMPM,    
       SUM(added.AddedBenefitAdmin) MSBAdminPMPM      
   INTO #AddedBen  
   FROM dbo.LkpIntAddedBenefitExpenseDetail added   WITH (NOLOCK)  
   INNER JOIN dbo.SavedPlanAddedBenefits spab   WITH (NOLOCK)  
    ON spab.AddedBenefitTypeID = added.AddedBenefitTypeID      
   WHERE spab.ForecastID = @XForecastID     
     AND spab.Ishidden = 0      
   GROUP BY spab.ForecastID  
  -----------------------------------------------------------------------  

  --Report  
  SELECT PlanYearID = @PlanYearID,    
      LEFT(SPI.CPS,5) ContractNumber,    
      SUBSTRING(SPI.CPS,7,3) AS PlanID,   
      RIGHT(SPI.CPS,3) AS SegmentId,   
      SPI.PlanName,  
      cnh.OrganizationName, --savedplanheader    
      MAPD = (CASE WHEN spi.PlanTypeID = @MAPD  THEN 'Y' ELSE 'N' END),    
      SNP = (CASE WHEN spi.SNPTypeID = 0 THEN 'N' ELSE 'Y' END),      
      lkp.SNPTypeName,                                         -- switch to new LkpSNPType table by Abe 2/23/2023
      cpt.ProductType,    
      bidsum.ForecastID,    
      PlanVersion = 1,    
      bidsum.BasicMemberPremium BasicMbrPrem,    
      MoopDet.INMOOP,    
      INDeductible = CASE WHEN MOOPDet.IsPartBDeductible = 0  
           THEN MOOPDet.INDeductible  
           ELSE NULL  
         END,    
      MOOPDet.OONMOOP,    
      OONDeductible = CASE WHEN MOOPDet.IsPartBDeductible = 0  
         THEN MOOPDet.OONDeductible  
         ELSE NULL  
          END,    
      MOOPDet.CombinedMOOP,    
      PartBDeductible = CASE WHEN MOOPDet.IsPartBDeductible = 1  
           THEN CASE WHEN MOOPDet.INDeductible IS NULL  
            THEN CASE WHEN MOOPDet.CombinedDeductible IS NULL  
                THEN 0  
                ELSE MOOPDet.CombinedDeductible  
              END  
            ELSE MOOPDet.INDeductible  
             END  
            ELSE NULL  
         END,  

    --A/B    
    bidsum.CS CostShare, --Max    
    CostShareReduction = CAST(Rebate.RedABCostShare AS DECIMAL(7,2)),    
    NetABSuppBen = --Net    
        (     
        ISNULL(bidsum.cs,0)     
        - ISNULL(Rebate.RedABCostShare,0)   
        ),  

    --B Only    
    PtBMaxRebateAlloc = bidsum.PartBMaxRebateAllocation,  --Max    
    sfs.PartBPremiumBuydown,     

       --Supplemental    
    bidsum.Sup_Prem SuppPrem,  --Max    
    TotalOtherSuppBen =  Rebate.SuppPremBuydown,  --Applied   
    NetSuppBen =     --Net    
        (    
        ISNULL(bidsum.Sup_Prem,0)    
        - ISNULL(Rebate.SuppPremBuydown,0)     
        ),    

    --Rx Basic    
    RxBasicPremium =(CASE WHEN spi.PlanTypeID = @MAPD THEN spa.RxBasicPremium    
           WHEN spi.PlanTypeID <> @MAPD THEN NULL    
              ELSE Rebate.RxBasicPremium    
         END), --Max   
    Rebate.RxBasicBuyDown, --Applied    
    NetPartDBasic =   --Net    
        (    
        ISNULL( (CASE WHEN spi.PlanTypeID = @MAPD THEN spa.RxBasicPremium    
                WHEN spi.PlanTypeID <>@MAPD THEN NULL    
                ELSE Rebate.RxBasicPremium  
              END),0)    
        - ISNULL(Rebate.RxBasicBuyDown,0)    
        ),     

    --Rx Supp    
    RxSuppPremium=(    
        CASE WHEN spi.PlanTypeID = @MAPD THEN spa.RxSuppPremium    
          WHEN spi.PlanTypeID <> @MAPD THEN NULL    
             ELSE Rebate.RxSuppPremium    
        END),     
    Rebate.RxSuppBuyDown, --Applied    
    NetPartDSupp =   --Net    
        (    
        ISNULL((    
          CASE WHEN spi.PlanTypeID = @MAPD THEN spa.RxSuppPremium    
            WHEN spi.PlanTypeID <> @MAPD THEN NULL    
               ELSE Rebate.RxSuppPremium    
          END),0)    
        - ISNULL(Rebate.RxSuppBuyDown,0)    
        ),    

    --SegmentID = '000',    
    bidsum.PlanProjectedMembership ProjectedMemberMonths,    
    bidsum.PlanProjectedNonDualMembership ProjectedNonDualMemberMonths,    
    bidsum.PlanProjectedDualMembership ProjectedDualMemberMonths,    
    ProjESRDMemberMonths = @ProjectedESRDMemberMonths,    
    bidsum.PlanRiskFactor MARiskScore,    
    bidsum.Allowed TotalMedicalExp,    
    CMSPremium = -1,    
    MemberPremium =     
        (    
        ISNULL(bidsum.BasicMemberPremium,0)    
        + ISNULL(bidsum.cs,0)    
        - ISNULL(Rebate.RedABCostShare,0)   
        + ISNULL(bidsum.Sup_Prem,0)    
        - ISNULL(Rebate.SuppPremBuydown,0)    
        + ISNULL((CASE WHEN spi.PlanTypeID = @MAPD THEN spa.RxBasicPremium    
              WHEN spi.PlanTypeID <> @MAPD THEN NULL    
              ELSE Rebate.RxBasicPremium    
            END),0)    
        - ISNULL(Rebate.RxBasicBuyDown,0)    
        + ISNULL((CASE WHEN spi.PlanTypeID = @MAPD THEN spa.RxSuppPremium    
              WHEN spi.PlanTypeID <> @MAPD THEN NULL    
              ELSE Rebate.RxSuppPremium    
            END),0)   
        - ISNULL(Rebate.RxSuppBuyDown,0)    
        ),    
    MemberPremiumRounded =    
        ROUND(    
        (ISNULL(bidsum.BasicMemberPremium,0)    
        + ISNULL(bidsum.cs,0)    
        - ISNULL(Rebate.RedABCostShare ,0)   
        + ISNULL(bidsum.Sup_Prem,0)    
        - ISNULL(Rebate.SuppPremBuydown,0)    
        + ISNULL((CASE WHEN SPI.PlanTypeID = @MAPD THEN spa.RxBasicPremium     
              WHEN SPI.PlanTypeID <> @MAPD THEN NULL    
              ELSE Rebate.RxBasicPremium    
            END),0)    
        - ISNULL(Rebate.RxBasicBuyDown,0)    
        + ISNULL((CASE WHEN spi.PlanTypeID = @MAPD THEN spa.RxSuppPremium    
              WHEN spi.PlanTypeID <> @MAPD THEN NULL    
              ELSE Rebate.RxSuppPremium    
            END),0)    
        - ISNULL(Rebate.RxSuppBuyDown,0)    
        ),1),    
    TotalPremium = -1,    
    dbo.fnGetSafeDivisionResult(bidsum.ExpensePMPM + bidsum.UserFee + bidsum.UncollectedPremium,    
    bidsum.TotalReqRev) ExpensePercent,    
    bidsum.ExpensePMPM + Bidsum.InsurerFee + bidsum.UserFee + bidsum.UncollectedPremium ExpensePMPM,    
    WS4And6Totals.MarketingSales,    
    DirectAdmin = WS4And6Totals.DirectAdmin       
         - cfp.UserFee    
         - cfp.UncollectedPremium,    
    WS4And6Totals.InDirectAdmin,    
    WS4And6Totals.QualityInitiatives,    
    [TaxesandFees] = WS4And6Totals.TaxesAndFees + WS4and6Totals.InsurerFee,    
    bidsum.ProfitPercent,    
    bidsum.Profit,    
    bidsum.PlanBenchmark,    
    bidsum.TotalReqRev,    
    -- Total Rebate    
    bidsum.Rebate,  --Max    
    RebateSpent =  --Applied    
     (    
     ISNULL( Rebate.RedABCostShare ,0)    
     + ISNULL(SFS.PartBPremiumBuydown,0)    
     + ISNULL(Rebate.SuppPremBuydown,0)    
     + ISNULL(Rebate.RxBasicBuydown,0)    
     + ISNULL(Rebate.RxSuppBuydown,0)    
     ),    
    RebateUnspent =  --Net    
     (    
     ISNULL(bidsum.Rebate,0)    
     - ISNULL( Rebate.RedABCostShare ,0)    
     - ISNULL(SFS.PartBPremiumBuydown,0)    
     - ISNULL( Rebate.SuppPremBuydown,0)     
     - ISNULL(Rebate.RxBasicBuydown,0)    
     - ISNULL(Rebate.RxSuppBuydown,0)    
     ),    
    bidsum.PlanExperienceMembership ExperienceMemberMonths,    
    NonDEMembers = @ExperienceNonDualMemberMonths,    
    DEMembers = @ExperienceDualMemberMonths,    
    ExperienceESRDMemberMonths = @ExperienceESRDMemberMonths,    
    SecondaryPayerAdjustment = ISNULL(spa.SecondaryPayerAdjustment,0),    
    bidsum.NetNonESRD,    
    bidsum.PlanBid,    
    bidsum.StandardizedBid,    
    bidsum.ProjectedYearBenefitFactor ProjYrBenFactor,    
    bidsum.CurrentYearBenefitFactor CurYrBenFactor,    
    PartCRiskScoreDE = NULL, -- Place Holder    
    PartCRiskScoreNonDE = NULL, -- Place Holder    
    AdminPartC = NULL,   -- Place Holder    
    GainLossPartC = NULL,  -- Place Holder    
    1 MARatingOptionID,   
    EstimatedPlanBidComponent = @EstimatedPlanBidComponent,     
    IsCombinedDeductible = CASE WHEN MOOPDet.CombinedDeductible IS NOT NULL THEN 1  
           ELSE 0  
            END  
  INTO #Report  
  FROM dbo.fnAppGetBidSummary(@XForecastID) bidsum     
  INNER JOIN dbo.SavedForecastSetup SFS WITH (NOLOCK)  
   ON SFS.ForecastID = bidsum.ForecastID  
  LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)  
   ON SFS.PlanInfoID = SPI.PlanInfoID   
  INNER JOIN dbo.SavedPlanAssumptions spa  WITH (NOLOCK)  
   ON spa.ForecastID = sfs.ForecastID    
  --LEFT JOIN dbo.LkpExtSNPType LST  WITH (NOLOCK)                   -- Removed this mapping because short on SNPTypeID versus SavedPlanInfo's SNPTypeID by Abe 2/23/2023            
  -- ON SPI.SNPTypeID = LST.SNPTypeID  
    INNER JOIN dbo.LkpSNPType lkp                                    -- Added new join to pull from new LkpSNPType table SNPTypeName names by Abe 2/23/2023   
	ON lkp.SNPTypeID = spi.SNPTypeID
  INNER JOIN  dbo.SavedMarketInfo lmh  WITH (NOLOCK)  
   ON lmh.ActuarialMarketID = SPI.ActuarialMarketID  
  INNER JOIN  dbo.SavedRegionInfo srh  WITH (NOLOCK)  
   ON srh.ActuarialRegionID = lmh.ActuarialRegionID    
  INNER JOIN dbo.AdminUserHeader Contact  WITH (NOLOCK)  
   ON Contact.UserID=SFS.ContactID    
  LEFT JOIN dbo.AdminUserHeader SecondaryContact  WITH (NOLOCK)  
   ON SecondaryContact.UserID = SFS.SecondaryContactID    
  INNER JOIN dbo.AdminUserHeader Certifying  WITH (NOLOCK)  
   ON SFS.CertifyingActuaryUserID = Certifying.UserID    
  INNER JOIN dbo.SavedPlanDetail spd  WITH (NOLOCK)  
   ON spd.ForecastID = SFS.ForecastID    
   AND spd.MARatingOptionID = 1    
  LEFT JOIN dbo.Benefits_SavedBenefitOption MOOPDet WITH (NOLOCK)  
   ON MOOPDet.PlanInfoID = SFS.PlanInfoID  
   AND MOOPDet.BenefitOptionID = SFS.BenefitOptionID  
  INNER JOIN dbo.LkpProductType cpt  WITH (NOLOCK)  
   ON cpt.ProductTypeID=SPI.ProductTypeID    
  INNER JOIN dbo.PerExtContractNumberHeader cnh  WITH (NOLOCK)  
   ON LEFT(SPI.CPS,5) = cnh.ContractNumber    
  INNER JOIN dbo.CalcFinalPremium cfp  WITH (NOLOCK)  
   ON cfp.ForecastID = SFS.ForecastID    
  LEFT JOIN #AddedBen added      
   ON added.ForecastID = SFS.ForecastID    
  INNER JOIN dbo.fnGetMARebateAllocation(@XForecastID) Rebate    
   ON SFS.ForecastID=Rebate.ForecastID  
  INNER JOIN dbo.fnAppGetMABPTWS4Expenses(@XForecastID) WS4And6Totals    
   ON SFS.ForecastID=WS4And6Totals.ForecastID  
  WHERE SFS.ForecastID = @XForecastID   

  IF  
    (  
        SELECT OBJECT_ID('tempdb..#AddedBen')  
    ) IS NOT NULL  
 BEGIN  
        DROP TABLE #AddedBen;  
  END  
 ----------------------------------------------------------------------------  
 --INSERTINT Value  
 ----------------------------------------------------------------------------  

    SELECT @PlanYearID PlanYearID,    
           ContractNumber,    
           PlanID,    
           SegmentId,  
           PlanName,    
           OrganizationName,    
           MAPD,    
           SNP,    
     SNPTypeName,    
           ProductType PlanTypeName,  
           ForecastID,    
           PlanVersion,    
           BasicMbrPrem,    
           INMOOP,    
           INDeductible,    
           OONMOOP,    
           OONDeductible,    
           CombinedMOOP,    
           PartBDeductible,    
           CostShare,    
           CostShareReduction,    
           NetABSuppBen,    
           PtBMaxRebateAlloc,    
           PartBPremiumBuyDown,    
           SuppPrem,    
           TotalOtherSuppBen,    
           NetSuppBen,    
           RxBasicPremium,    
           RxBasicBuyDown,    
           NetPartDBasic,    
           RxSuppPremium,    
           RxSuppBuyDown,    
           NetPartDSupp,    
           ProjectedMemberMonths,    
           ProjectedNonDualMemberMonths,    
           ProjectedDualMemberMonths,    
           ProjESRDMemberMonths,    
           MARiskScore,    
           TotalMedicalExp,    
           CMSPremium,    
           MemberPremium,    
           MemberPremiumRounded,    
           TotalPremium,    
           dbo.fnGetSafeDivisionResult(ExpensePMPM,(PlanBid + CostShare + SuppPrem)) ExpensePercent,    
           ExpensePMPM,    
           MarketingSales,    
           DirectAdmin,    
           InDirectAdmin,    
           ProfitPercent,    
           Profit,    
           PlanBenchmark,    
           TotalReqRev,    
           Rebate,    
           RebateSpent,    
           RebateUnspent,    
           ExperienceMemberMonths,    
           SecondaryPayerAdjustment,    
           NetNonESRD,    
           PlanBid,    
           StandardizedBid,    
           ProjYrBenFactor,    
           CurYrBenFactor,    
           NonDEMembers,    
           DEMembers,    
           ExperienceESRDMemberMonths,    
           PartCRiskScoreDE,    
           PartCRiskScoreNonDE,    
           AdminPartC,    
           GainLossPartC,     
           RPPOPBC = EstimatedPlanBidComponent,    
           AuditTime = @AuditTime,  
           QualityInitiatives,    
           TaxesAndFees,    
           IsCombinedDeductible    
    INTO #MAReportPlanLevel  
    FROM #Report Rpt    

 INSERT INTO dbo.MAReportPlanLevel 
 SELECT   
     PlanYearID,    
           ContractNumber,    
           PlanID,    
           SegmentId,  
           PlanName,    
           OrganizationName,    
           MAPD,    
           SNP,    
     SNPTypeName,    
           PlanTypeName,  
           ForecastID,    
           PlanVersion,    
           BasicMbrPrem,    
           INMOOP,    
           INDeductible,    
           OONMOOP,    
           OONDeductible,    
           CombinedMOOP,    
           PartBDeductible,    
           CostShare,    
           CostShareReduction,    
           NetABSuppBen,    
           PtBMaxRebateAlloc,    
           PartBPremiumBuyDown,    
           SuppPrem,    
           TotalOtherSuppBen,    
           NetSuppBen,    
           RxBasicPremium,    
           RxBasicBuyDown,    
           NetPartDBasic,    
           RxSuppPremium,    
           RxSuppBuyDown,    
           NetPartDSupp,    
           ProjectedMemberMonths,    
           ProjectedNonDualMemberMonths,    
           ProjectedDualMemberMonths,    
           ProjESRDMemberMonths,    
           MARiskScore,    
           TotalMedicalExp,    
           CMSPremium,    
           MemberPremium,    
           MemberPremiumRounded,    
           TotalPremium,    
           ExpensePercent,    
           ExpensePMPM,    
           MarketingSales,    
           DirectAdmin,    
           InDirectAdmin,    
           ProfitPercent,    
           Profit,    
           PlanBenchmark,    
           TotalReqRev,    
           Rebate,    
           RebateSpent,    
           RebateUnspent,    
   ExperienceMemberMonths,    
           SecondaryPayerAdjustment,    
           NetNonESRD,    
           PlanBid,    
           StandardizedBid,    
           ProjYrBenFactor,    
           CurYrBenFactor,    
           NonDEMembers,    
           DEMembers,    
           ExperienceESRDMemberMonths,    
           PartCRiskScoreDE,    
           PartCRiskScoreNonDE,    
           AdminPartC,    
           GainLossPartC,     
           RPPOPBC,    
           AuditTime,  
           QualityInitiatives,    
           TaxesAndFees,    
           IsCombinedDeductible  
 FROM #MAReportPlanLevel  

DROP TABLE #Report  
DROP TABLE #MAReportPlanLevel  

COMMIT TRANSACTION; 
END TRY
	BEGIN CATCH
	ROLLBACK TRANSACTION; 
	 -- In the event of an error, we return the error details to the client  
		DECLARE	@ValidationMessage VARCHAR(MAX)
		DECLARE @ProcNumber INT
        SELECT @ValidationMessage = ERROR_MESSAGE()  
		SET @ProcNumber = 23
		EXEC dbo.spWritePlanRepriceLog @ForecastID,@ProcNumber, @ValidationMessage,@UserID

	END CATCH;

END
GO
