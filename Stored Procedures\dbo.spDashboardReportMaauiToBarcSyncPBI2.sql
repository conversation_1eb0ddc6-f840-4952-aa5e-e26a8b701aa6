SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- PROCEDURE NAME: spDashboardReportMaauiToBarcSyncPBI2

-- DESCRIPTION: This SP returns extract for "Sync Service Area to BARC" user action from AppLogs table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      [dbo].[DashboardReportAppLogs]
--		
-- Example 
-- Exec [dbo].[spDashboardReportMaauiToBarcSyncPBI2] 0, '2023-08-22 14:42:38.237'


-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-22			1			Initial Version                                                                         Sheetal Patil
-- 2024-Feb-20			2			Changed "Message" column logic                                                          Sheetal Patil
-- 2024-Nov-29          3           Sync COntroller paths changed based on sync move to left nav                            Chaitanya Durga K
---------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROC [dbo].[spDashboardReportMaauiToBarcSyncPBI2]
 (@LastSuccessfullRunLogid INT
  ,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


 DECLARE @XLastSuccessfullRunLogid  INT = @LastSuccessfullRunLogid

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTemp')) IS NOT NULL DROP TABLE #AppLogTemp;

SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
	  ,characterEvenCount
	  ,FormattedMesssage
INTO #AppLogTemp
FROM (SELECT
	   [LogID]
      ,[Date]
      ,[User]
	  ,CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END AS [Message]
,characterEvenCount
,FormattedMesssage
FROM(
SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
       ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (
SELECT REPLACE(REPLACE(RIGHT(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END, 
		DATALENGTH(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END) - 
		CHARINDEX('{', CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END),0) < 5 
	THEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END),0) 
	ELSE 0 END AS  DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (SELECT CAST([LogID] AS INT) LogID
	  ,[Date]
      ,[User]
      ,[Message]
FROM [dbo].[DashboardReportAppLogs] WITH (NOLOCK) 
WHERE 
 [LogID] > @XLastSuccessfullRunLogid
AND ([Message] LIKE '%Finished executing POST: Sync/SyncToBARC%' OR [Message] LIKE '%Finished executing POST: Sync/GetBARCValidationsAuditData%'
OR [Message] LIKE '%Executing POST: Sync/SyncToBARC%'
)) AS t1 ) AS t2 ) AS t3 ) AS t4 )AS t5


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction1')) IS NOT NULL DROP TABLE #AppLogTempAction1;


SELECT 
'Sync Service Area to BARC' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'Sync Service Area to BARC' AS [Type]
, plans AS [Plans]
, CASE WHEN DATALENGTH(plans)=0 THEN 0  
       ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction1
FROM (
SELECT UserId, RunDate, ExecutionTime, btwType , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,UserId
,RunDate
,ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: Sync/SyncToBARC%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: Sync/SyncToBARC%'
WHERE a.[Message] LIKE 'Executing POST: Sync/SyncToBARC%'
) AS t01) AS t02 ) AS t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
btwType NVARCHAR(50) '$.AuditType'
,plans NVARCHAR(4000) '$.PlanIDList'
)) AS t03
WHERE btwType LIKE '%MAAUI To BARC Validations'

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction2')) IS NOT NULL DROP TABLE #AppLogTempAction2;


SELECT 
'Sync Service Area to BARC' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'Sync Service Area to BARC' AS [Type]
, plans AS [Plans]
, CASE WHEN DATALENGTH(plans)=0 THEN 0  
       ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction2
FROM (
SELECT UserId, RunDate, ExecutionTime, btwType , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,UserId
,RunDate
,ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: Sync/GetBARCValidationsAuditData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: Sync/GetBARCValidationsAuditData%'
WHERE a.[Message] LIKE 'Executing POST: Sync/SyncToBARC%'
) AS t01) AS t02 ) AS t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
btwType NVARCHAR(50) '$.AuditType'
,plans NVARCHAR(4000) '$.PlanIDList'
)) AS t03
WHERE btwType LIKE '%MAAUI To BARC Validations'


SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],SUM([ExecutionTime]) AS [ExecutionTime] ,[characterEvenCount],[ErrorCount],[RunEndDate],[ErrorMessage] FROM 
(
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction1 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime] AS [ExecutionTime] ,[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction2 WITH (NOLOCK)
) t
GROUP BY [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[characterEvenCount],[ErrorCount],[RunEndDate],[ErrorMessage]

END
GO
