 SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================        
-- Author: Deepali Mittal      
-- Create date: 2018-Oct-03  
-- Description:  spAppSaveUtilityRequest  
--        
--        
-- PARAMETERS:        
-- Input:          

-- TABLES:        
-- Read:  LkpIntServerList  
--            
-- Write: []   
--              
-- VIEWS:        
--        
-- FUNCTIONS:        
--          
-- STORED PROCS:         

-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------          
-- DATE			VERSION		CHANGES MADE																DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------          
-- 2018-Oct-03		1		Initial version.															Deepali Mittal                                      
-- 2018-Nov-06		2		Added null for [LastUpdateDateTime]  
--					        in [SavedUtilityRequestHeader] to take this as Request Run Time  
--							Null means request is not yet completed										Pooja Dahiya  
-- 2018-Nov-19		3       Updated SQLQuery for to handle different request type						Deepthi Thiyagu   
-- 2018-Nov-30		4       Removed Significance table													Deepthi Thiyagu  
-- 2018-Nov-30		5       Updated message and logging													Kritika Singh  
-- 2019-Jan-16		6		Messaging Changes															Deepali  
-- 2018-Feb-01		7       Removed @MessageFromBackend parameter										Pooja Dahiya  
-- 2019-Apr-03		8       Changed field names for header and details table							Kritika Singh  
-- 2019-Apr-12		9       INCLUDED LOGIC FOR PREP ID AND CU ID										Kritika Singh  
-- 2019-Apr-12		9.1		Changes to accept the table type along with table names						Nagarani Kolanchelmi  
--							for Data Foundation Sync and to seperate them   
--							while inserting into request table    
-- 2019-Apr-15		9.2		Corrected logic to save cuIdList as null when Prep process					 Kritika Singh  
--							also prep run id save  
--2020-Apr-21		10      Added logic for Fusion ID List												Kiran Pant  
--2020-Apr-24		11      Added logic for IsMACTAPTProcess											Kiran Pant  
--2020-Oct-06		12      Updating for new DF sync													Rodney Smith  
--2020-dEC-01		13      Updating for new DF sync													Anurodh Pandey  
--2021-Jan-11		14		Sp change for Population 3of4												Deepali	
--2021-APR-08		15		changes w.r.t. story#1959802												Priyesh  
--2021-Oct-14		16      changed for story 2407084 added accessrequest								Bhavana
--2022-Jul-01		17      Added DFVersion & renamed DFVersionIds to DFRunID							Manisha Tyagi
--2022-Jul-01		17		Added DFVersion & renamed DFVersionIds to DFRunID							Manisha Tyagi
--2023-Aug-01		18		Updated table SavedUtilityRequestDataForTrendSPUpdateAll					Deepali Mittal
--2023-Aug-16		19		Updated Model turn over logic												Surya Murthy
--2024-Feb-06		20		Prep table logic added														Surya Murthy
--2024-Mar-19		21		DFRunID selection fix, DFVersion id only for DF Sync						Adam Gilbert
--2025-Mar-25		22      multiple DFRunID Issue for DF Sync											Chaitanya Durga K
--2025-Apr-08		23		Removed unused fields														Vikrant Bagal
--2025-Apr-29	    24	    Added data for DataFoundationSync & DataFoundationSyncTM in		            Archana Sahu
--					        SavedUtilityRequestDetail table 
-- ----------------------------------------------------------------------------------------------------------------------        
CREATE PROCEDURE [dbo].[spAppSaveUtilityRequest]
    @RequestType VARCHAR(100),
    @ModuleName VARCHAR(100),     
    @Comments VARCHAR(250),
    @SQLQuery VARCHAR(MAX),
    @dfRunIDList VARCHAR(200)=NULL,
    @incurredMonths VARCHAR(MAX),	  
    @txtNumberRange VARCHAR(50) = NULL,
    @sourceTableName VARCHAR(MAX) = NULL,
	@selectedDFVersionID INT = NULL,
    @LastUpdateByID VARCHAR(7),
	@DataIncurredDate VARCHAR(6)=NULL,
	@DataPaidDate VARCHAR(6)=NULL,
	@Quarter VARCHAR(4)=NULL,
    @Result INT OUT
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        SAVE TRANSACTION MySavePoint;
        DECLARE @RequestID INT,
                @DFVersionID INT;
		IF @sourceTableName ='SavedDFClaims,SavedDFFinance,SavedDFBaseAdminAndRevenue' OR @sourceTableName ='SavedDFClaims,SavedDFFinance,SavedDFBaseAdminAndRevenue,SyncPrep' 
		BEGIN
        SELECT @DFVersionID = COALESCE(MAX(DFVersionID), 0) + 1
        FROM dbo.SavedDFVersionHeader;
		END
		ELSE
		BEGIN
		SET @DFVersionID = @selectedDFVersionID
		END
        BEGIN
            INSERT INTO [dbo].[SavedUtilityRequestHeader]
            (
                [ModuleName],
                [RequestType],
                [RequestStatus],
                [RequestedBy],
                [RequestedDateTime],
                [RequestorComments],
                [ReviewedBy],
                [ReviewedDateTime],
                [ReviewerComments],
                [RequestStartDateTime],
                [RequestEndDateTime],
                [LastUpdateByID]
            )
            SELECT @ModuleName,
                   @RequestType,
                   'Pending Approval',
                   @LastUpdateByID,
                   GETDATE(),
                   @Comments,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   @LastUpdateByID;

            SET @RequestID =
            (
                SELECT MAX(RequestID) FROM dbo.[SavedUtilityRequestHeader]
            );

            INSERT INTO [dbo].[SavedUtilityRequestDetail]
            (
                [RequestID],
                [ExecutionStatus],
                [SQLText],		  
                [DFRunIDs],
                [SourceTableName],
                [IncurredMonths],
                [ProcNumberRange], 
                [DFVersionID]
            )
            SELECT 
                @RequestID,
                'Pending Approval',
                CASE
                    WHEN @RequestType = 'Custom' THEN
                        @SQLQuery
                     WHEN @RequestType = 'ArchiveData' OR @RequestType ='ModelTurn_Script5' OR @RequestType = 'DataFoundationSync'	
					OR @RequestType ='ModelTurn_Script1' OR @RequestType ='ModelTurn_Script2' OR @RequestType = 'DataFoundationSyncTM'
					OR @RequestType ='ModelTurn_Script3' OR @RequestType ='ModelTurn_Script4'
					THEN
                        @SQLQuery
                    WHEN @RequestType = 'DataFoundationSyncForecastingcu' THEN
                         ''			      
                    ELSE
                        'CalcSignificance,SavedRollupForecastMap,SavedRollupInfo,SavedForecastSetup,SavedProjectedCrosswalk,SavedHistoricalCrosswalk,SavedServiceAreaOption,SavedPlanInfo,SavedMarketInfo,SavedRegionInfo,SavedDivisionInfo,LkpStateCounty,
                        LkpStateTerritory,LkpSNPType,LkpRenewalSubstantiationMap,LkpRenewalType,LkpProductType,LkpProductMOOP,LkpProduct,LkpPlanYear,LkpPlanType,LkpPFFSNetwork,LkpModelSettings,LkpMARegion,LkpMAPlanDesign,AppSavedFilterUserPreference,AppSavedReportUserPreference,AppSavedRollupUserPreference,SavedPlanHeader,SavedMOOPInclusion,LkpIntRegionElement'
                END,
                
                CASE 
				    WHEN @RequestType = 'DataFoundationSyncForecastingcu' THEN
					    CASE WHEN @sourceTableName ='SavedDFClaims,SavedDFFinance,SavedDFBaseAdminAndRevenue' OR @sourceTableName ='SavedDFClaims,SavedDFFinance,SavedDFBaseAdminAndRevenue,SyncPrep'  THEN 
						    @dfRunIDList
					    ELSE
						       CAST((SELECT TOP 1 DFRunID FROM SavedDFVersionHeader WHERE DFVersionid = @DFVersionID) AS VARCHAR(200))
				
					    END
				    ELSE
					    @dfRunIDList
			    END AS DFRunIDs,
                @sourceTableName,
                @incurredMonths,
                @txtNumberRange,	  
                CASE WHEN @RequestType = 'DataFoundationSyncForecastingcu' OR @RequestType = 'DataFoundationSync'
					THEN @DFVersionID 
					ELSE null 
				END;
				IF (@txtNumberRange=1 OR @txtNumberRange=2)
				INSERT INTO dbo.SavedUtilityRequestDataForTrendSPUpdateAll(
				[RequestID],
				[DataIncurredDate],
				[DataPaidDate],
				[Quarter])
				VALUES(@RequestID,
				@DataIncurredDate,
				@DataPaidDate,
				@Quarter
				)

        END;		
        SET @Result = @RequestID;
    END TRY
    BEGIN CATCH
        SET @Result = 0;
        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;
        DECLARE @ErrorException NVARCHAR(4000);
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
                @currentdate DATETIME = GETDATE();

        SELECT @ErrorMessage = ERROR_MESSAGE(),
               @ErrorSeverity = ERROR_SEVERITY(),
               @ErrorState = ERROR_STATE(),
               @ErrorException
                   = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR(100)) + N' .Error Severity :'
                     + CAST(@ErrorSeverity AS VARCHAR(20)) + N' .Error State :' + CAST(@ErrorState AS VARCHAR(20));
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);


        ROLLBACK TRANSACTION MySavePoint;
        ---Insert into app log for logging error------------------  
        EXEC dbo.spAppAddLogEntry @currentdate,
                                  '',
                                  'ERROR',
                                  @errSrc,
                                  @ErrorMessage,
                                  @ErrorException,
                                  @LastUpdateByID;
    END CATCH;
    COMMIT TRANSACTION;
END;
