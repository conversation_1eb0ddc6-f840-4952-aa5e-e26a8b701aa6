SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetRiskFactorExtract]    ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetRiskFactorExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2012-Apr-05
-- HEADER UPDATED: 2012-Apr-05
--
-- DESCRIPTION: Designed to extract fields for risk factors - will match upload
--
-- PARAMETERS:
--	Input:
--      @WhereIN
--      @IsPlanLevel
--  Output:
--
-- TABLES: 
--	Read:
--      Saved<PERSON>lanHeader
--      SavedPlanDetail
--      SavedPlanMemberMonthDetail
--      SavedPlanRiskFactorDetail
--      SavedMarketInfo
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnStringSplit
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION	    CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2012-Apr-05		1			Initial Version							                            Alex Rezmerski
-- 2013-Oct-04		2			Modified to Include SegmentId										Anubhav Mishra
-- 2017-Jul-06		3			Removed PerExtContractNumberPlanIDDetail, referenced PlanName		Chris Fleming
--									from new column in SavedPlanHeader
-- 2018-05-10		4			Changing LkpExtCMSPlanType to LkpProductType					    Jordan Purdue
-- 2019-Jul-08      5            Made changes in market table.										Kritika Singh 
-- 2020-Jan-06      6           Modified code for statecounycode									Deepali Mittal
-- 2020-Dec-18      7           Increasing scale to align BPT risk scores with MRA                  Brent Osantowski
-- 2022-Apr-22      8           Change @WhereIN from 4000 to MAX                                    Manisha Tyagi
-- 2025-Apr-01		9			Added CPS, LastUpdateDateTime & LastUpdateByID column				Archana Sahu
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetRiskFactorExtract]
    (
    @WhereIN VARCHAR(MAX)=NULL,
    @IsPlanLevel BIT
    ) 
RETURNS @Results TABLE 
(
    FilePath VARCHAR(MAX),
    ForecastID INT,
    CountyCode CHAR(5) ,
    NonDualExperienceRiskFactor DECIMAL (18, 15),
    NonDualProjectionRiskFactor DECIMAL (18, 15),
    DualExperienceRiskFactor DECIMAL (18, 15),
    DualProjectionRiskFactor DECIMAL (18, 15),
	CPS CHAR(13),
	LastUpdateByID CHAR(7),
	LastUpdateDateTime DATETIME
) AS
BEGIN
 
 

-----------------------------------------------------------------------------------------------------------
        IF @WhereIN IS NULL --All Plans 
-----------------------------------------------------------------------------------------------------------
            INSERT @Results -- No filter (Risk Factors)
                SELECT
                    RFD.FilePath,
                    P.ForecastID, 
					StateCountyCode = dbo.fnPadInteger(SCD.StateTerritoryID, 2) + SCD.CountyCode,
                   
                    NonDualExperienceRiskFactor =
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 1 THEN CASE RFD.IsExperience
                                                    WHEN 1 THEN RiskFactor
                                                END
                                END	
                            ),
                        0),
                    NonDualProjectedRiskFactor =							
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 1 THEN CASE RFD.IsExperience
                                                    WHEN 0 THEN RiskFactor
                                                END
                                END	
                            ),
                        0), 							
                    DualExperienceRiskFactor =							
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 2 THEN CASE RFD.IsExperience
                                                    WHEN 1 THEN RiskFactor
                                                END
                                END	
                            ),
                        0),						
                    DualProjectionRiskFactor =							
                        ISNULL(
                            SUM(
                                CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                    WHEN 2 THEN CASE RFD.IsExperience
                                                    WHEN 0 THEN RiskFactor
                                                END
                                END	
                            ),
                        0),
						spi.CPS,
						RFD.LastUpdateByID ,
						RFD.LastUpdateDateTime
                FROM SavedPlanHeader P 							
                INNER JOIN SavedMarketInfo M 							
                    ON M.ActuarialMarketID = P.MarketID	
				INNER JOIN dbo.SavedPlanInfo spi
				ON P.PlanInfoID = spi.PlanInfoID
                INNER JOIN LkpProductType cpt 							
                    ON P.PlanTypeID = cpt.ProductTypeID							
                INNER JOIN SavedPlanStateCountyDetail SCD							
                    ON SCD.ForecastID = P.ForecastID					
                    AND SCD.IsCountyExcludedFromBPTOutput = 0							
                INNER JOIN LkpExtCMSStateCounty SC 							
                    ON SCD.StateTerritoryID = SC.StateTerritoryID 							
                    AND CASE WHEN LEN(SCD.CountyCode)=2 THEN '0'+scd.CountyCode ELSE scd.CountyCode end = SC.CountyCode					
                LEFT JOIN SavedPlanRiskFactorDetail RFD 							
                    ON RFD.ForecastID = P.ForecastID					
                    AND RFD.StateTerritoryID = SC.StateTerritoryID						
                    AND RFD.CountyCode = SC.CountyCode  		
                LEFT JOIN LkpIntDemogIndicators DI
	                ON RFD.DemogIndicator = DI.DemogIndicator
                WHERE
                    P.ForecastID > 0
                    AND P.IsHidden = 0
                    AND DI.DemogIndicator IN (1,2)		
                GROUP BY
                    RFD.FilePath,
                    P.ForecastID,
                    ActuarialMarket, 	
                    P.PlanName, 	
                    P.PlanTypeID, 	
                    ProductType, 	
                    P.ContractNumber, 	
                    P.PlanID,	
                    P.SegmentId,  --Added SegmentId
                    SCD.StateTerritoryID, 	
                    SCD.CountyCode, 	
                    SC.CountyName,
					spi.CPS,
					RFD.LastUpdateByID,
					RFD.LastUpdateDateTime
                ORDER BY
                    ActuarialMarket, 	
                    P.ForecastID,
                    SCD.StateTerritoryID, 	
                    SCD.CountyCode	
        ELSE
-----------------------------------------------------------------------------------------------------------
            IF @IsPlanLevel = 0 --Market Level (Risk Factors)
-----------------------------------------------------------------------------------------------------------
                INSERT @Results 
                    SELECT 
						RFD.FilePath,
						P.ForecastID,									
                        StateCountyCode = dbo.fnPadInteger(SCD.StateTerritoryID, 2) + SCD.CountyCode, 									
                        
                        NonDualExperienceRiskFactor =
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
                        NonDualProjectedRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0), 							
                        DualExperienceRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),						
                        DualProjectionRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
                        spi.CPS,
						RFD.LastUpdateByID,
						RFD.LastUpdateDateTime
                    FROM SavedPlanHeader P 									
			        INNER JOIN SavedPlanStateCountyDetail SCD					
                        ON SCD.ForecastID = P.ForecastID								
                        AND SCD.IsCountyExcludedFromBPTOutput = 0
					INNER JOIN dbo.SavedPlanInfo spi
					ON P.PlanInfoID = spi.PlanInfoID
                    INNER JOIN SavedMarketInfo M 									
                        ON M.ActuarialMarketID = P.MarketID								
                    INNER JOIN LkpProductType cpt 									
                        ON P.PlanTypeID = cpt.ProductTypeID 									
                    INNER JOIN LkpExtCMSStateCounty SC 									
                        ON SCD.StateTerritoryID = SC.StateTerritoryID									
                        AND CASE WHEN LEN(SCD.CountyCode)=2 THEN '0'+scd.CountyCode ELSE scd.CountyCode end = SC.CountyCode									
                    LEFT OUTER JOIN SavedPlanRiskFactorDetail RFD 									
                        ON RFD.ForecastID = P.ForecastID							
					    AND RFD.StateTerritoryID = SC.StateTerritoryID 			
                        AND RFD.CountyCode = SC.CountyCode
                    LEFT JOIN LkpIntDemogIndicators DI
	                    ON RFD.DemogIndicator = DI.DemogIndicator					
                    WHERE
                        P.MarketID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))																
                        AND P.IsHidden = 0
                        AND DI.DemogIndicator IN (1,2)									
                    GROUP BY
                        RFD.FilePath,
                        P.ForecastID,							
                        ActuarialMarket, 									
                        P.PlanName, 									
                        P.PlanTypeID, 									
                        ProductType, 									
                        P.ContractNumber, 									
                        P.PlanID,	
                        P.SegmentId,		--Added SegmentId						
                        SCD.StateTerritoryID, 									
                        SCD.CountyCode, 									
                        SC.CountyName,
						spi.CPS,
						RFD.LastUpdateByID,
						RFD.LastUpdateDateTime
                    ORDER BY
                        ActuarialMarket, 									
                        P.ForecastID,							
                        SCD.StateTerritoryID, 									
                        SCD.CountyCode									
-----------------------------------------------------------------------------------------------------------
            ELSE -- Plan Level (Risk Factors)
-----------------------------------------------------------------------------------------------------------
                INSERT @Results -- Plan Level Risk Factors									
                    SELECT
                        RFD.FilePath,
                        P.ForecastID,							
                        StateCountyCode = dbo.fnPadInteger(SCD.StateTerritoryID, 2) + SCD.CountyCode, 									
                       			 									
                        NonDualExperienceRiskFactor =
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
                        NonDualProjectedRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 1 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0), 							
                        DualExperienceRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 1 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),						
                        DualProjectionRiskFactor =							
                            ISNULL(
                                SUM(
                                    CASE DI.DemogIndicator --RFD.RiskFactorTypeID
                                        WHEN 2 THEN CASE RFD.IsExperience
                                                        WHEN 0 THEN RiskFactor
                                                    END
                                    END	
                                ),
                            0),
						spi.CPS,
						RFD.LastUpdateByID,
						RFD.LastUpdateDateTime
                    FROM SavedPlanHeader P 									
			        INNER JOIN SavedPlanStateCountyDetail SCD					
                        ON SCD.ForecastID = P.ForecastID								
                        AND SCD.IsCountyExcludedFromBPTOutput = 0
					INNER JOIN dbo.SavedPlanInfo spi
					ON P.PlanInfoID = spi.PlanInfoID
                    INNER JOIN SavedMarketInfo M 									
                        ON M.ActuarialMarketID = P.MarketID								
                    INNER JOIN LkpProductType cpt 									
                        ON P.PlanTypeID = cpt.ProductTypeID 									
                    INNER JOIN LkpExtCMSStateCounty SC 									
                        ON SCD.StateTerritoryID = SC.StateTerritoryID									
                        AND CASE WHEN LEN(SCD.CountyCode)=2 THEN '0'+scd.CountyCode ELSE scd.CountyCode END = SC.CountyCode									
                    LEFT OUTER JOIN SavedPlanRiskFactorDetail RFD 									
                        ON RFD.ForecastID = P.ForecastID								
					    AND RFD.StateTerritoryID = SC.StateTerritoryID 			
                        AND RFD.CountyCode = SC.CountyCode
                    LEFT JOIN LkpIntDemogIndicators DI
	                    ON RFD.DemogIndicator = DI.DemogIndicator	
                    WHERE
                        P.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))								
                        AND P.IsHidden = 0
                        AND DI.DemogIndicator IN (1,2)									
                    GROUP BY
                        RFD.FilePath,
                        P.ForecastID,								
                        ActuarialMarket, 									
                        P.PlanName, 									
                        P.PlanTypeID, 									
                        ProductType, 									
                        P.ContractNumber, 									
                        P.PlanID,
                        P.SegmentId,  ---Added SegmentId in Group By
                        SCD.StateTerritoryID,
                        SCD.CountyCode,
                        SC.CountyName,
						spi.CPS,
						RFD.LastUpdateByID,
						RFD.LastUpdateDateTime
                    ORDER BY
                        ActuarialMarket, 									
                        P.ForecastID,								
                        SCD.StateTerritoryID, 									
                        SCD.CountyCode	
	RETURN
END
GO
