SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_HistProcess_spCalcNotPlanLevel_PlanMappingFinal
--
-- CREATOR: <PERSON> Lewis
--
-- CREATED DATE: SEP-08-2020
--
-- DESCRIPTION:   Trends from Trend_CalcFileSync_Annual are mapped to historic plans for reporting purposes.  
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntPlanYear
--					Trend_CalcFileSync_Annual
--					Trend_SavedComponentInfo
--					Trend_SavedPackageOption		
--					
--          Write:  Trend_HistProcess_CalcNotPlanLevel_PlanMappingFinal
--                  Trend_Reporting_Log
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER  
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- SEP-08-2020      1           Initial Version																								Jake Lewis
-- OCT-19-2020		2			Fixed issues with sonar qube																				Deepali Mittal
-- OCT-20-2020      3           Updated PlanInfo to include all historic plans																Tanvi Khanna
-- DEC-10-2020		4			Name changes for the SP and some of the referenced tables:													Jake Lewis
--								NEW SP = Trend_HistProcess_spCalcNotPlanLevel_PlanMappingFinal, OLD SP = Trend_Reporting_spCalcNotPlanLevel_HistoricPlanMapping
--								NEW = Trend_HistProcess_CalcNotPlanLevel_PlanMappingFinal, OLD = Trend_Reporting_CalcNotPlanLevel_HistoricPlanMapping
-- MAR-15-2021		5			Code adjustments to improve efficiency and decrease runtime													Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_HistProcess_spCalcNotPlanLevel_PlanMappingFinal]

@LastUpdateByID CHAR(13)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

		DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            -- Declare variables
            DECLARE @BidYear INT = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);
            DECLARE @ExperienceYear INT = (SELECT   PlanYearID FROM dbo.LkpIntPlanYear WHERE IsExperienceYear = 1);
            DECLARE @PackageOptionID INT = (SELECT  DISTINCT PackageOptionID FROM   dbo.Trend_CalcFileSync_Annual);
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);

            -- Set up PlanTypeGranularity hierarchy
            IF (SELECT  OBJECT_ID ('tempdb..#GranularityOrder')) IS NOT NULL
                DROP TABLE #GranularityOrder;
            CREATE TABLE #GranularityOrder
                (GranOrder           INT
                ,PlanTypeGranularity VARCHAR(500));
            INSERT INTO #GranularityOrder
                (GranOrder
                ,PlanTypeGranularity)
            VALUES (1, 'ActuarialRegion,ProductType')
                  ,(2, 'IsNationwide,SNPType,ProductType')
                  ,(3, 'ActuarialRegion')
                  ,(4, 'IsNationwide,SNPType')
                  ,(5, 'IsNationwide,ProductType')
                  ,(6, 'IsNationwide');

            -- Get Components 
            IF (SELECT  OBJECT_ID ('tempdb..#Component')) IS NOT NULL
                DROP TABLE #Component;
            SELECT      c.Component
                       ,p.ComponentVersionID
            INTO        #Component
            FROM        dbo.Trend_SavedComponentInfo c
            LEFT JOIN   dbo.Trend_SavedPackageOption p
                   ON p.Component = c.Component
            WHERE       c.IsPartOfPackage = 1
                        AND c.Component <> 'Normalized'
                        AND c.Component NOT LIKE 'Rx%'
                        AND p.IsLivePackage = 1;

            -- Create data shell
            IF (SELECT  OBJECT_ID ('tempdb..#DataShell')) IS NOT NULL
                DROP TABLE #DataShell;
            SELECT      v.PlanInfoID
                       ,v.CPS
                       ,v.PlanYear AS PlanYearID
                       ,@ExperienceYear BaseYearID
                       ,v.Region
                       ,v.Product
                       ,v.SNPType
                       ,@PackageOptionID AS PackageOptionID
                       ,c.ComponentVersionID
                       ,c.Component
            INTO        #DataShell
            FROM        dbo.vwPlanInfo v
            LEFT JOIN   #Component c
                   ON 1 = 1
            WHERE       v.IsHidden = 0
                        AND v.CPS IS NOT NULL
                        AND v.IsOffMAModel = 'No'
                        AND v.Region NOT IN ('Unmapped')
                        AND v.PlanYear < @BidYear - 1
                        AND v.PlanYear > @BidYear - 5;

            -- Source data from Trend_CalcFileSync_Annual
            IF (SELECT  OBJECT_ID ('tempdb..#sourceData')) IS NOT NULL
                DROP TABLE #sourceData;
            SELECT DISTINCT
                    CONCAT (ComponentVersionID, ',', PlanTypeGranularityValue) AS Lookup
                   ,ComponentVersionID
                   ,PlanTypeGranularity
                   ,PlanTypeGranularityValue
            INTO    #sourceData
            FROM    dbo.Trend_CalcFileSync_Annual;

            -- Create PlanTypeGranularity lookups for each PlanInfoID
            IF (SELECT  OBJECT_ID ('tempdb..#PlanTypeGranularityLookups')) IS NOT NULL
                DROP TABLE #PlanTypeGranularityLookups;
            SELECT  DISTINCT
                    ds.PlanInfoID
                   ,CONCAT (ds.ComponentVersionID, ',', ds.Region, ',', ds.Product) AS [ActuarialRegion,ProductType]
                   ,CONCAT (ds.ComponentVersionID, ',', 1, ',', ds.SNPType, ',', ds.Product) AS [IsNationwide,SNPType,ProductType]
                   ,CONCAT (ds.ComponentVersionID, ',', ds.Region) AS [ActuarialRegion]
                   ,CONCAT (ds.ComponentVersionID, ',', 1, ',', ds.SNPType) AS [IsNationwide,SNPType]
                   ,CONCAT (ds.ComponentVersionID, ',', 1, ',', ds.Product) AS [IsNationwide,ProductType]
                   ,CONCAT (ds.ComponentVersionID, ',', 1) AS [IsNationwide]
            INTO    #PlanTypeGranularityLookups
            FROM    #DataShell ds;

            -- For each PlanInfoID / ComponentVersionID set, select only the PlanTypeGranularities and PlanTypeGranularityValues that exist in the source data. 
            -- Multiple granularities and values may exist for each PlanInfoID / ComponentVersionID set.  
            IF (SELECT  OBJECT_ID ('tempdb..#GranularityAndValueOptions')) IS NOT NULL
                DROP TABLE #GranularityAndValueOptions;
            SELECT      PlanInfoID
                       ,ComponentVersionID
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
            INTO    #GranularityAndValueOptions
            FROM        #PlanTypeGranularityLookups
            LEFT JOIN   #sourceData
                   ON [ActuarialRegion,ProductType] = [Lookup]
            WHERE       [Lookup] IS NOT NULL
            UNION ALL
            SELECT      PlanInfoID
                       ,ComponentVersionID
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
            FROM        #PlanTypeGranularityLookups
            LEFT JOIN   #sourceData
                   ON [IsNationwide,SNPType,ProductType] = [Lookup]
            WHERE       [Lookup] IS NOT NULL
            UNION ALL
            SELECT      PlanInfoID
                       ,ComponentVersionID
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
            FROM        #PlanTypeGranularityLookups
            LEFT JOIN   #sourceData
                   ON ActuarialRegion = [Lookup]
            WHERE       [Lookup] IS NOT NULL
            UNION ALL
            SELECT      PlanInfoID
                       ,ComponentVersionID
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
            FROM        #PlanTypeGranularityLookups
            LEFT JOIN   #sourceData
                   ON [IsNationwide,SNPType] = [Lookup]
            WHERE       [Lookup] IS NOT NULL
            UNION ALL
            SELECT      PlanInfoID
                       ,ComponentVersionID
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
            FROM        #PlanTypeGranularityLookups
            LEFT JOIN   #sourceData
                   ON [IsNationwide,ProductType] = [Lookup]
            WHERE       [Lookup] IS NOT NULL
            UNION ALL
            SELECT      PlanInfoID
                       ,ComponentVersionID
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
            FROM        #PlanTypeGranularityLookups
            LEFT JOIN   #sourceData
                   ON IsNationwide = [Lookup]
            WHERE       [Lookup] IS NOT NULL;

            -- Add all selectable PlanTypeGranularites / PlanTypeGranularityValues to the shell. 
            -- Include Granularity Order, which allows the selection of the proper PlanTypeGranularity option.
            IF (SELECT  OBJECT_ID ('tempdb..#ShellWithAllOptions')) IS NOT NULL
                DROP TABLE #ShellWithAllOptions;
            SELECT      d.PlanInfoID
                       ,d.CPS
                       ,d.PlanYearID
                       ,d.BaseYearID
                       ,d.PackageOptionID
                       ,d.ComponentVersionID
                       ,d.Component
                       ,gv.PlanTypeGranularity
                       ,gv.PlanTypeGranularityValue
                       ,g.GranOrder
            INTO        #ShellWithAllOptions
            FROM        #DataShell d
            JOIN        #GranularityAndValueOptions gv
              ON CONCAT (d.PlanInfoID, ',', d.ComponentVersionID) = CONCAT (gv.PlanInfoID, ',', gv.ComponentVersionID)
            LEFT JOIN   #GranularityOrder g
                   ON g.PlanTypeGranularity = gv.PlanTypeGranularity;

            -- For each PlanInfoID / ComponentVersionID set, select the appropriate Granularity Order.
            -- This allows for the later selection of the appropriate PlanTypeGranularity / PlanTypeGranularityValue. 
            IF (SELECT  OBJECT_ID ('tempdb..#GranularitySelection')) IS NOT NULL
                DROP TABLE #GranularitySelection;
            SELECT      PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,PackageOptionID
                       ,ComponentVersionID
                       ,Component
                       ,MIN (ISNULL (GranOrder, 999)) AS GranOrder
            INTO        #GranularitySelection
            FROM        #ShellWithAllOptions
            GROUP BY    PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,PackageOptionID
                       ,ComponentVersionID
                       ,Component;

            -- Generate output
            DELETE  FROM dbo.Trend_HistProcess_CalcNotPlanLevel_PlanMappingFinal
            WHERE   1 = 1;

            INSERT INTO dbo.Trend_HistProcess_CalcNotPlanLevel_PlanMappingFinal
                (PlanInfoID
                ,CPS
                ,PlanYearID
                ,BaseYearID
                ,PackageOptionID
                ,ComponentVersionID
                ,Component
                ,PlanTypeGranularity
                ,PlanTypeGranularityValue
                ,LastUpdateByID
                ,LastUpdateDateTime) 
            SELECT      gs.PlanInfoID
                       ,gs.CPS
                       ,gs.PlanYearID
                       ,gs.BaseYearID
                       ,gs.PackageOptionID
                       ,gs.ComponentVersionID
                       ,gs.Component
                       ,g.PlanTypeGranularity
                       ,gv.PlanTypeGranularityValue
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        #GranularitySelection gs
            LEFT JOIN   #GranularityOrder g
                   ON g.GranOrder = gs.GranOrder
            LEFT JOIN   #GranularityAndValueOptions gv
                   ON gv.PlanInfoID = gs.PlanInfoID
                      AND   gv.ComponentVersionID = gs.ComponentVersionID
                      AND   gv.PlanTypeGranularity = g.PlanTypeGranularity;

            COMMIT TRANSACTION transactionMain;

        END TRY

        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
       INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@LastUpdateByID
               ,GETDATE ();

    END;

GO
