-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	dbo.TrendAdj_spRefresh
--
-- CREATOR:			<PERSON><PERSON><PERSON><PERSON>
--
-- CREATED DATE:	2024-Jul-30
--
-- DESCRIPTION:		This sp is executed by the Unified Actuarial Adjustment Compiler tool; it runs the individual actuarial adjustment stored procedures and writes initial adjustments for review in table TrendAdj_CalcPlanAdjmt.
--		
-- PARAMETERS:
--  Input  :		@AdjGroup
--					@SessionID
--					@Region
--					@LastUpdateByID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			TrendAdj_Controls_SavedSelections
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			vwPlanInfo
--
-- FUNCTIONS:		fngetbidyear
--					Trend_fnCalcStringToTable
--
-- STORED PROCS:	TrendAdj_spCalcSAR
--					TrendAdj_spCalcOSNP
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-Jul-30      1           Initial Version                                                     Aleksandar Dimitrijevic
-- 2024-Sep-12      2           Add HOA VBID Adj                                                    Michael Manes
-- 2024-Oct-10      3           Added NOLOCK & optimazation	                                        Kumar Jalendran
-- 2024-Oct-17      4           Dropped all temp tables at end of procedure                         Kumar Jalendran
-- 2024-Oct-25      5           Updated the error loging to dbo.TrendAdjLog                         Kumar Jalendran
-- 2024-Nov-11		6			Replace CPS with PlanInfoID in permanent tables						Michael Manes
-- 2025-May-06      7           Updated to remove TrendAdj_spCalcHOAVBid reference					Kumar Jalendran
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[TrendAdj_spRefresh]
	@AdjGroup TINYINT,
	@SessionID VARCHAR(19),
	@Region VARCHAR(50),
	@LastUpdateByID CHAR(7)
AS
	
	BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY


            BEGIN TRANSACTION transaction_TrendAdj_spRefresh;

				-- Declare local variables to use in place of input parameters to negate parameter sniffing.
				DECLARE @xLastUpdateByID CHAR(7) = @LastUpdateByID;
				DECLARE @xRegion VARCHAR(50) = @Region;
				DECLARE @xSessionID VARCHAR(19) = @SessionID;
				DECLARE @xAdjGroup TINYINT = @AdjGroup;
			
				-- Declare variables
				DECLARE @SARList VARCHAR(MAX);
				DECLARE @OSNPList VARCHAR(MAX);
				DECLARE @BidYear INT = (SELECT dbo.fngetbidyear());

				DECLARE @SARTypeID INT = 1;
				DECLARE @OSNPTypeID INT = 2;

				DECLARE @SAREnabled BIT = (SELECT TOP 1 IsEnabled
											 FROM dbo.TrendAdj_Controls_LkpAdjTypeID WITH(NOLOCK)
											WHERE AdjTypeID = @SARTypeID);
				DECLARE @OSNPEnabled BIT = (SELECT TOP 1 IsEnabled
											  FROM dbo.TrendAdj_Controls_LkpAdjTypeID WITH(NOLOCK)
											 WHERE AdjTypeID = @OSNPTypeID);

				-- Temporary tables for combining results
				DROP TABLE IF EXISTS #Combined_CalcPlanAdjmt;

				CREATE TABLE #Combined_CalcPlanAdjmt
					(
					 Granularity CHAR(6),
					 AdjustmentDescription VARCHAR(MAX),
					 CPS CHAR(13),
					 TrendYearID SMALLINT,
					 RateType SMALLINT,
					 ReportingCategory VARCHAR(50),
					 BenefitCategoryID INT,
					 CostAdjustment DECIMAL(18,8),
					 UseAdjustment DECIMAL(18,8)
					);

				IF @xAdjGroup = 1

					BEGIN
					
						-- SAR update procedure --
						IF @SAREnabled = 1

							BEGIN
								--Assigning plan list
								SET @SARList = (SELECT STRING_AGG(vw.PlanInfoID,', ') 
												  FROM dbo.TrendAdj_Controls_SavedSelections ss WITH(NOLOCK)
												 INNER JOIN dbo.vwPlanInfo vw WITH(NOLOCK)
													ON vw.PlanInfoID = ss.PlanInfoID
												 WHERE vw.PlanYear = @BidYear
												   AND vw.Region = @xRegion
												   AND ss.AdjTypeID = @SARTypeID);

								-- Execute sub-procedures and insert results into combined tables
								INSERT INTO #Combined_CalcPlanAdjmt EXEC dbo.TrendAdj_spCalcSAR @xSessionID, @SARList, @xLastUpdateByID;

							END;
							
						-- OSNP update procedure --
						IF @OSNPEnabled = 1

							BEGIN

								--Assigning plan list
								SET @OSNPList = (SELECT STRING_AGG(vw.PlanInfoID,', ') 
												   FROM dbo.TrendAdj_Controls_SavedSelections ss WITH(NOLOCK)
												  INNER JOIN dbo.vwPlanInfo vw WITH(NOLOCK)
													 ON vw.PlanInfoID = ss.PlanInfoID
												  WHERE vw.PlanYear = @BidYear
													AND vw.Region = @xRegion
													AND ss.AdjTypeID = @OSNPTypeID);

								-- Execute sub-procedures and insert results into combined tables
								INSERT INTO #Combined_CalcPlanAdjmt EXEC dbo.TrendAdj_spCalcOSNP @xSessionID, @OSNPList, @xLastUpdateByID;
							END;

				END;
	
				-- Writing into the initial Adjustment table
				INSERT INTO dbo.TrendAdj_CalcPlanAdjmt(
				 SessionID 
				,Granularity 
				,AdjustmentDescription 
				,CPS 
				,PlanInfoID 
				,TrendYearID 
				,RateType 
				,ReportingCategory 
				,BenefitCategoryID 
				,CostAdjustment 
				,UseAdjustment )
				SELECT @xSessionID,
					   arc.Granularity,
					   arc.AdjustmentDescription,
					   arc.CPS,
					   vw.PlanInfoID,
					   arc.TrendYearID,
					   arc.RateType,
					   arc.ReportingCategory,
					   arc.BenefitCategoryID,
					   arc.CostAdjustment,
					   arc.UseAdjustment
				  FROM #Combined_CalcPlanAdjmt arc WITH(NOLOCK)
				 INNER JOIN dbo.vwPlanInfo vw WITH(NOLOCK)
				    ON vw.CPS = arc.CPS
				   AND vw.PlanYear = @BidYear
				 WHERE vw.Region = @xRegion;

            COMMIT TRANSACTION transaction_TrendAdj_spRefresh;

			-- Clean temp table.
			DROP TABLE IF EXISTS #Combined_CalcPlanAdjmt;

        END TRY

        BEGIN CATCH

			DECLARE @ErrorMessage NVARCHAR(4000);
		    DECLARE @ErrorSeverity INT;
		    DECLARE @ErrorState INT;
		    DECLARE @ErrorException NVARCHAR(4000);
		    DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
				    @currentdate DATETIME = GETDATE();

			SELECT @ErrorMessage = ERROR_MESSAGE();
			SELECT @ErrorSeverity = ERROR_SEVERITY();
			SELECT @ErrorState = ERROR_STATE();

			RAISERROR(   
				@ErrorMessage,  -- Message text.  
				@ErrorSeverity, -- Severity.  
				@ErrorState     -- State.  
			);

			ROLLBACK TRANSACTION;
			--- Insert into app log for logging error------------------
			INSERT INTO dbo.TrendAdjLog
                (AdjGroupID
                ,ProcName
                ,Region
                ,UserID
                ,AuditTime
                ,AuditMessage)
            VALUES (@xAdjGroup
                   ,'spRefresh'
                   ,LEFT(@xRegion, 40)
                   ,@xLastUpdateByID
                   ,GETDATE ()
                   ,@ErrorMessage);

		END CATCH;

	END;
GO