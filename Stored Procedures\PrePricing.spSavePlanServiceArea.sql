SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spSavePlanServiceArea]   
--    
-- AUTHOR: Sur<PERSON>rthy 
--    
-- CREATED DATE: 2024-Dec-02    
-- Type: 
-- DESCRIPTION: Procedure responsible for saving service area  
--    
-- PARAMETERS:    
-- Input: 
-- @PlanInfoID

-- TABLES:   
--  

-- Read:    
-- PrePricing.ServiceAreaMapping

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Dec-02		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSavePlanServiceArea]
(
	@PlanInfoID INT,
	@CountiesList VARCHAR(MAX),
	@LastUpdateByID VARCHAR(200)
)
AS    
BEGIN   
	SET NOCOUNT ON;
	DECLARE	@OutPutResultCode VARCHAR(20)
	DECLARE @OutPutResult VARCHAR(max);
	BEGIN TRY
		BEGIN TRANSACTION serviceareasave	
			INSERT INTO PrePricing.ServiceAreaMapping
			(
				PlanInfoID,	    
				Notes,
				LastUpdateDateTime,
				LastUpdateByID,
				SSStateCountyCD
			)
			 SELECT  
				@PlanInfoID,     
				'',
				GETDATE(),
				@LastUpdateByID,
				 value   FROM STRING_SPLIT(@CountiesList,',');
		SET @OutPutResultCode='Success'
		SET @OutPutResult='Service Area saved successfully.';	
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
		COMMIT TRANSACTION serviceareasave		
	END TRY
	BEGIN CATCH		
		DECLARE @ErrorMessage NVARCHAR(4000);                    
        DECLARE @ErrorSeverity INT;                    
        DECLARE @ErrorState INT;                    
        DECLARE @ErrorException NVARCHAR(4000);                    
		DECLARE @errSrc VARCHAR(MAX) =ISNULL( ERROR_PROCEDURE(),'SQL')                    
		DECLARE @currentdate DATETIME=GETDATE()                    
		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+        
		' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)                
		ROLLBACK TRANSACTION serviceareasave    
		SET @OutPutResult=@ErrorMessage;	
		SET @OutPutResultCode='Error'
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH	 
END
GO
