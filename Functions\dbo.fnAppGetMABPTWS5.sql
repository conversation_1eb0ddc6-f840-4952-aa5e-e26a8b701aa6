SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetMABPTWS5
--
-- AUTHOR: <PERSON> Ellis 
--
-- CREATED DATE: 2009-Apr-02
-- HEADER UPDATED: 2011-Jan-14
--
-- DESCRIPTION: Function responsible for listing values on the MA-BPT Worksheet 5. This function replaces
--              fnAppGetBPTPlanWS5 for all years.
--
-- PARAMETERS:
--	Input: 
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		CalcBenchmarkSummary
--		LkpExtCMSStateCounty
--		LkpExtCMSStateTerritory 
--		SavedPlanAssumptions
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnAppGetMABPTWS4Expenses
--		fnAppGetMABPTWS4Percent
--		fnGetBidYear
--		fnGetRatebook
--		fnGetSafeDivisionResult
--		fnPlanCountyProjectedMemberMonths
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION     CHANGES MADE														DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2009-Apr-02      1           Initial Version														Sandy Ellis
-- 2009-Dec-29		2			Added calculations for ISARBid, RiskPaymentRateA, and				Joe Casey
--									RiskPaymentRateB for 2010 and later.
-- 2010-Jan-04		3			Changed ISARBid, RiskPaymentRateA, and RiskPaymentRateB				Joe Casey
--									from Decimal (9,6) to Decimal (10,6)
-- 2011-Jan-11      4           Revised for 2012 Database											Michael Siekerka
-- 2011-Jan-12		5			MemberMonths is now Decimal											Jiao Chen
-- 2011-Jan-17      6           Changed ProjMM piece from non-dual to combined                      Michael Siekerka
-- 2011-Jun-14		7			Changed PlanYearId to return SMALLINT instead of INT				Bobby Jaegers
-- 2012-Feb-24		8			Made changes to account for new admin buckets Quality and			Alex Rezmerski
--								TaxesAndFees
-- 2013-Apr-17		9			Comment out tax and quality											Tim Gao
-- 2014-Mar-19     10           Added Regulatory OOA Risk Score Changes                             Lindsay Allen
-- 2019-Mar-21     11           Fix for ISAR Bids used in PREP                                      Keith Galloway
-- 2020-Dec-14     12           Increasing scale to align BPT risk scores with MRA                  Brent Osantowski
-- 2023-Aug-03     13           Added NOLOCK and Internal parameter									Sheetal Patil
-- 2024-Jul-02	   14			Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table	Surya Murthy
------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS5]
	( 
    @ForecastID INT
	)
RETURNS @Results TABLE 
	(
    PlanYearID SMALLINT,
    ForecastID INT,
    CountyCode CHAR(5),
    StateTerritoryShortName CHAR(2),
    CountyName VARCHAR(30),
    MemberMonths DECIMAL (28,6),
    RiskFactor DECIMAL(9, 6),
    RiskRate DECIMAL(8, 4),
    AdjRiskRate DECIMAL(12, 8), 
    ISARScale DECIMAL(16, 15),
    ISARBid DECIMAL(10, 6),
    RiskPaymentRateA DECIMAL (10, 6),
    RiskPaymentRateB DECIMAL (10, 6),
    CMSIPCostShare DECIMAL(7, 6),
    CMSSNFCostShare DECIMAL(7, 6),
    PartBCostShare DECIMAL(7, 6),
    CMSIPFFSCosts DECIMAL(8, 4),
    CMSSNFCosts DECIMAL (8, 4),
    PartBCosts DECIMAL (8, 4),
    CMSPartAEquivCostShare DECIMAL (8, 4),
    CMSPartBEquivCostShare DECIMAL (8, 4)
	) AS
BEGIN
	DECLARE @RevReqCoveredNet Decimal(9,2),
	        @PlanYearID SMALLINT,
	        @ProjectedOOAMemberMonths DECIMAL (28,15),
			@XForecastID INT =@ForecastID 
	SELECT @PlanYearID = dbo.fnGetBidYear()

    SELECT -- This is the OOAMembership Total that is put in cell U17 on the MA Bnchmk tab. 
	@ProjectedOOAMemberMonths = SUM(MemberMonths)
    FROM dbo.SavedPlanOOAMemberMonthDetail ooa WITH (NOLOCK)
    WHERE ooa.ForecastID = @XForecastID 

	SELECT @RevReqCoveredNet = expenses.ReqRevCoveredNet FROM dbo.fnAppGetMABPTWS4Expenses(@XForecastID) expenses

    INSERT @Results
    SELECT
        @PlanYearID, 
        cbs.ForecastID, 
        CountyCode = NULL,
        StateTerritoryShortName = NULL,
        CountyName = 'OOA',
        MemberMonths = @ProjectedOOAMemberMonths,
	    RiskFactor = cbs.PlanRiskFactor,
        RiskRate = cbs.PlanAverageRiskRate,
        AdjRiskRate = ROUND(cbs.PlanAverageRiskRate * cbs.PlanRiskFactor,8),
        ISARScale = NULL,
        ISARBid = NULL,
		RiskPaymentRateA = NULL,
		RiskPaymentRateB = NULL,				
        CMSIPCostShare = NULL,
        CMSSNFCostShare = NULL,
        PartBCostShare = NULL,
        CMSIPFFSCosts = NULL,
        CMSSNFCosts = NULL,                
        PartBCosts = NULL,
        CMSPartAEquivCostShare = NULL,		
        CMSPartBEquivCostShare = NULL
    FROM dbo.CalcBenchmarkSummary cbs WITH (NOLOCK)
    WHERE cbs.ForecastID = @XForecastID

    INSERT @Results
    SELECT
        @PlanYearID, 
        cbs.ForecastID, 
        CountyCode = ST.StateTerritoryCode + RB.CountyCode,
        ST.StateTerritoryShortName,
        SC.CountyName,
        MemberMonths = PMM.ProjectedMemberMonths,
	    RiskFactor = cbs.PlanRiskFactor,
        RiskRate = RB.CMSRiskRate,
        AdjRiskRate = ROUND(RB.CMSRiskRate * cbs.PlanRiskFactor,8),
        ISARScale = RB.CMSRiskRate / cbs.PlanAverageRiskRate,
        ISARBid = dbo.fnGetSafeDivisionResult(@RevReqCoveredNet, --num
		            (1 - spd.SecondaryPayerAdjustment) * cbs.PlanRiskFactor) --denom
				  * dbo.fnGetSafeDivisionResult(RB.CMSRiskRate, cbs.PlanAverageRiskRate),
		--A = ISARBid - B
		RiskPaymentRateA = dbo.fnGetSafeDivisionResult(@RevReqCoveredNet, --num
		            (1 - spd.SecondaryPayerAdjustment) * cbs.PlanRiskFactor) --denom
				    * dbo.fnGetSafeDivisionResult(RB.CMSRiskRate, cbs.PlanAverageRiskRate)
					-
					dbo.fnGetSafeDivisionResult(@RevReqCoveredNet, --num
					(1 - spd.SecondaryPayerAdjustment) * cbs.PlanRiskFactor)
					* dbo.fnGetSafeDivisionResult(RB.CMSRiskRate, cbs.PlanAverageRiskRate)
					* dbo.fnGetSafeDivisionResult(RB.CMSPartBOnlyRiskRate, RB.CMsRiskRate),
		--B = ISARBid * (RB.CMSPartBOnlyRiskRate / RB.CMsRiskRate)
		RiskPaymentRateB = dbo.fnGetSafeDivisionResult(@RevReqCoveredNet, --num
					(1 - spd.SecondaryPayerAdjustment) * cbs.PlanRiskFactor)
					* dbo.fnGetSafeDivisionResult(RB.CMSRiskRate, cbs.PlanAverageRiskRate)
					* dbo.fnGetSafeDivisionResult(RB.CMSPartBOnlyRiskRate, RB.CMsRiskRate),				
        RB.CMSIPCostShare,
        RB.CMSSNFCostShare,
        PartBCostShare = RB.CMSOPCostShare,
        RB.CMSIPFFSCosts,
        RB.CMSSNFCosts,                
        PartBCosts = RB.CMSOPCosts,
        RB.CMSPartAEquivCostShare,		
        RB.CMSPartBEquivCostShare
    FROM dbo.CalcBenchmarkSummary cbs WITH (NOLOCK)
    INNER JOIN dbo.SavedPlanAssumptions spd WITH (NOLOCK)
        ON spd.ForecastID = cbs.ForecastID
    INNER JOIN dbo.fnPlanCountyProjectedMemberMonths (@XForecastID, 2) PMM --Combined
        ON PMM.ForecastID = cbs.ForecastID
    INNER JOIN dbo.fnGetRatebook(@XForecastID) RB
        ON RB.StateTerritoryID = PMM.StateTerritoryID
        AND RB.CountyCode = PMM.CountyCode
    INNER JOIN dbo.LkpExtCMSStateTerritory ST WITH (NOLOCK)
        ON ST.StateTerritoryID = RB.StateTerritoryID
    INNER JOIN dbo.LkpExtCMSStateCounty SC WITH (NOLOCK)
        ON SC.StateTerritoryID = ST.StateTerritoryID
        AND SC.CountyCode = PMM.CountyCode
    WHERE cbs.ForecastID = @XForecastID
RETURN
END
GO
