SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

 
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	fnCalcProfitPctAndMbrPremFromTargetProfitPMPM
--
-- CREATOR:			<PERSON>
--
-- CREATED DATE:	2024-DEC-16
--
-- DESCRIPTION:		Uses the input Target Profit PMPM calculate and return the following values: TotalMemberPremium, ProfitPercent, Expenses, 
--					TotalReqRev, PlanBid, StandardizedBid, Savings, Rebate, BasicMemberPremium, GovtPremiumAdj, UncollectedPremium, InsurerFee
--		
-- PARAMETERS:
--  Input  :		@ForecastID				INT
--					@TargetProfitPMPM		DECIMAL(14, 8)
--					@UserID					CHAR(7)
--
--  Output :		@Results TABLE
--					(Profit					DECIMAL(9, 2)
--					,ProfitPercentage		DECIMAL(10, 8)
--					,Expenses				DECIMAL(14, 8)
--					,TotalReqRev			DECIMAL(14, 8)
--					,PlanBid				DECIMAL(14, 8)
--					,StandardizedBid		DECIMAL(14, 8)
--					,Savings				DECIMAL(14, 8)
--					,Rebate					DECIMAL(14, 8)
--					,BasicMemberPremium		DECIMAL(14, 8)
--					,GovtPremiumAdj			DECIMAL(14, 8)
--					,UncollectedPremium		DECIMAL(14, 8)
--					,InsurerFee				DECIMAL(14, 8)
--					,TotalMemberPremium		DECIMAL(6, 2))
--
-- TABLES : 
--	Read :			SavedForecastSetup
--					SavedPlanInfo
--					LkpProductType
--					LkpSNPType
--					PerExtCMSValues
--					CalcFinalPremium
--					LkpIntUncollectedPremium
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			SavedPlanHeader
--
-- FUNCTIONS:		fnAppGetBidSummary
--					fnAppGetMABPTWS4TotalMedicareCovered
--					fnPrePremium
--					fnGetMARebateAllocation
--					fnGetRebatePercent
--					fnGetSafeDivisionResult
--					fnGetSafeDivisionResultReturnOne
--					fnGetFinalPremiumSavings
--					fnIsNegative
--					fnMin
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-DEC-16		1			Initial Version														Michael Manes
-- ----------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnCalcProfitPctAndMbrPremFromTargetProfitPMPM]
    (@ForecastID       INT
    ,@TargetProfitPMPM DECIMAL(14, 8)
    ,@UserID           CHAR(7))

RETURNS @Results TABLE
    (Profit             DECIMAL(14, 8)
    ,ProfitPercentage   DECIMAL(10, 8)
    ,Expenses           DECIMAL(14, 8)
    ,TotalReqRev        DECIMAL(14, 8)
    ,PlanBid            DECIMAL(14, 8)
    ,StandardizedBid    DECIMAL(14, 8)
    ,Savings            DECIMAL(14, 8)
    ,Rebate             DECIMAL(14, 8)
    ,BasicMemberPremium DECIMAL(14, 8)
    ,GovtPremiumAdj     DECIMAL(14, 8)
    ,UncollectedPremium DECIMAL(14, 8)
    ,InsurerFee         DECIMAL(14, 8)
	,TotalMemberPremium	DECIMAL(14,8))

AS

    BEGIN

        -- Declare variables
        DECLARE @XForecastID INT = @ForecastID; --Internal parameter
        DECLARE @XUserID CHAR(7) = @UserID; --Internal parameter
        DECLARE @P DECIMAL(14, 8) = @TargetProfitPMPM; --Internal parameter
        DECLARE @MemberPremium DECIMAL(6, 2); --Total Member Premium
        DECLARE @ProductTypeID INT; --ProductTypeID
        DECLARE @Savings DECIMAL(14, 8); --Savings
        DECLARE @Rx DECIMAL(9, 2); --Total Drug Premium before rebate allocation (Basic and Supplemental)
        DECLARE @PtB DECIMAL(9, 1); --Rebate Allocated to Part B Premium Giveback
        DECLARE @BM DECIMAL(14, 8); --Plan Benchmark
        DECLARE @SBM DECIMAL(14, 8); --Standardized Benchmark
        DECLARE @PB DECIMAL(14, 8); --Plan Bid
        DECLARE @SPB DECIMAL(14, 8); --Standardized Plan Bid
        DECLARE @PMPMm DECIMAL(14, 8); --Medicare Covered Net PMPM Medical Expenses (excl admin and profit)
        DECLARE @MSB DECIMAL(14, 8); --MSB Claims
        DECLARE @PMPMt DECIMAL(14, 8); --Total PMPM Net Medical Expenses (excl admin and profit)
        DECLARE @APct DECIMAL(8, 6); --Any fixed expenses specified as a percentage of premium (e.g. Pre-2010 Admin, Reinsurance, etc.) (Insurer Fee Percent)
        DECLARE @InsurerFee DECIMAL(8, 6); --InsurerFee (use existing if locked, otherwise recalculate)
        DECLARE @UserFee DECIMAL(7, 6); --UserFee
        DECLARE @ADollar DECIMAL(14, 8); --Any fixed expenses specified as PMPM (e.g. Admin, User Fees, Uncollected Premium Fee, etc.)
        DECLARE @CF DECIMAL(20, 12); --Plan Conversion Factor
        DECLARE @K1 DECIMAL(14, 8); --Rebate %, if Bid < BM
        DECLARE @TRR DECIMAL(14, 8); --Total Required Revenue
        DECLARE @ProfitPercentage DECIMAL(10, 8); --Profit Percentage
        DECLARE @IsLocked BIT; --Admin lock
        DECLARE @UncollectedPremium DECIMAL(5, 2); --Uncollected premium
        DECLARE @DSNP BIT; --Indicator for DSNP exclusion
        DECLARE @Exp DECIMAL(14, 8); --Expenses

        -- Fetch necessary data
        SELECT      @ProductTypeID = lpt.ProductTypeID
                   ,@Rx = mara.RxBasicPremium + mara.RxSuppPremium
                   ,@PtB = bidSumm.PartBPremiumBuydown
                   ,@BM = CAST(bidSumm.PlanBenchmark AS DECIMAL(14, 8))
                   ,@PMPMm = bptws4tmc.MedicareCoveredNet
                   ,@MSB = CAST(bidSumm.TotalNet AS DECIMAL(14, 8)) - bptws4tmc.MedicareCoveredNet
                   ,@ADollar = bidSumm.ExpensePMPM + bidSumm.UserFee
                   ,@CF = prePrem.ConversionFactor
                   ,@IsLocked = sph.IsLocked
                   ,@UserFee = bidSumm.UserFee
        FROM        dbo.SavedForecastSetup sfs WITH (NOLOCK)
        LEFT JOIN   dbo.SavedPlanInfo spi WITH (NOLOCK)
               ON spi.PlanInfoID = sfs.PlanInfoID
        LEFT JOIN   dbo.LkpProductType lpt WITH (NOLOCK)
               ON lpt.ProductTypeID = spi.ProductTypeID
       INNER JOIN   dbo.SavedPlanHeader sph WITH (NOLOCK)
               ON sph.ForecastID = sfs.ForecastID
       INNER JOIN   dbo.fnAppGetBidSummary (@XForecastID) bidSumm
               ON bidSumm.ForecastID = sfs.ForecastID
       INNER JOIN   dbo.fnAppGetMABPTWS4TotalMedicareCovered (@XForecastID) bptws4tmc
               ON bptws4tmc.ForecastID = bidSumm.ForecastID
       INNER JOIN   dbo.fnPrePremium (@XForecastID) prePrem
               ON prePrem.ForecastID = bidSumm.ForecastID
       INNER JOIN   dbo.fnGetMARebateAllocation (@XForecastID) mara
               ON mara.ForecastID = bidSumm.ForecastID
        WHERE       sfs.ForecastID = @XForecastID;

        -- Handle DSNP exclusion
        SELECT      @DSNP = CASE WHEN sph.IsSNP = 1
                                      AND   (PATINDEX ('%D-SNP%', sph.PlanName) <> 0
                                             OR lst.SNPType = 'Dual Eligible') THEN 0
                                 ELSE 1 END
        FROM        dbo.SavedPlanHeader sph WITH (NOLOCK)
       INNER JOIN   dbo.LkpSNPType lst WITH (NOLOCK)
               ON lst.SNPTypeID = sph.SNPTypeID
        WHERE       sph.ForecastID = @XForecastID;

        -- Handle admin locking, set A%, A$, InsurerFee, and UncollectedPremium
        IF @IsLocked = 1
            BEGIN
                SET @APct = 0;
                SELECT  @InsurerFee = InsurerFee
                       ,@UncollectedPremium = ISNULL (UncollectedPremium, 0)
                FROM    dbo.CalcFinalPremium WITH (NOLOCK)
                WHERE   ForecastID = @XForecastID;

                SET @ADollar = @ADollar + @InsurerFee;
            END;
        ELSE
            BEGIN
                SET @InsurerFee = 0;
                SELECT  @APct = InsurerFeePercent FROM  dbo.PerExtCMSValues WITH (NOLOCK);
                SELECT  @UncollectedPremium = ISNULL (UncollectedPremium, 0)
                FROM    dbo.CalcFinalPremium WITH (NOLOCK)
                WHERE   ForecastID = @XForecastID;
            END;

        SET @ADollar = @ADollar + @UncollectedPremium;

        SET @PMPMt = @PMPMm + @MSB; --Total PMPM Net Medical Expenses (excl admin and profit)

        -- Calculate dependent variables
        SET @TRR = dbo.fnGetSafeDivisionResult ((@P + @ADollar + @PMPMt), (1 - @APct));
        SET @ProfitPercentage = dbo.fnGetSafeDivisionResult (@P, @TRR);
        SET @PB = ROUND (
                  dbo.fnGetSafeDivisionResult (
                  (@PMPMm + dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt) * @ADollar), (1 - @ProfitPercentage - @APct))
                 ,2);

        -- Determine K factor based on PB and BM
        DECLARE @K DECIMAL(14, 8);
        SET @K1 = dbo.fnGetRebatePercent (@XForecastID);

        IF @PB <= @BM SET @K = @K1; -- Rebate % if Plan Bid is less than or equal to Plan Benchmark
        ELSE SET @K = 1 / @CF; -- 1 divided by Conversion Factor if Plan Bid is greater

        -- Calculate Total Member Premium (@MemberPremium)
        SET @MemberPremium = ((@P + @ADollar + @PMPMt) * (1 + ((@K - 1) * @PMPMm) / @PMPMt)) / (1 - @APct) + @Rx + @PtB - @K * @BM;
        -- Can't have negative premium, set to 0 and it should come through as unallocated rebate
        SET @MemberPremium = ABS (dbo.fnMIN (-@MemberPremium, 0));

        -- Calculate Uncollected Premium based on the newly calculated @MemberPremium  --Checking with compliance to see if A. This will only be used during rebate reallocation, and B, If so, should it always be locked.
        IF @IsLocked = 0
            BEGIN
                SELECT  @UncollectedPremium = UncollectedPremium * @DSNP
                FROM    dbo.LkpIntUncollectedPremium WITH (NOLOCK)
                WHERE   RangeMax >= @MemberPremium
                        AND RangeMin <= @MemberPremium;
            END;

        SET @SPB = ROUND (
                   dbo.fnGetSafeDivisionResult (
                   ROUND (
                   dbo.fnGetSafeDivisionResult (
                   (@PMPMm + dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt) * @ADollar), (1 - @ProfitPercentage - @APct))
                  ,2)
                  ,@CF)
                  ,2); --Standardized plan bid

        SET @SBM = dbo.fnGetSafeDivisionResult (@BM, @CF); --Standardized benchmark

        SET @Savings = dbo.fnGetFinalPremiumSavings (@ProductTypeID, @BM, @PB, @SBM, @SPB, @CF, @XUserID); --Savings

        SET @Exp = @TRR - @P - @PMPMt - @UserFee - @UncollectedPremium; --Final expenses

        --Populate results table
        INSERT  @Results
            (Profit
            ,ProfitPercentage
            ,Expenses
            ,TotalReqRev
            ,PlanBid
            ,StandardizedBid
            ,Savings
            ,Rebate
            ,BasicMemberPremium
            ,GovtPremiumAdj
            ,UncollectedPremium
            ,InsurerFee
			,TotalMemberPremium)
        SELECT  Profit = @P
               ,ProfitPercentage = @ProfitPercentage
               ,Expenses = @Exp
               ,TotalReqRev = @TRR
               ,PlanBid = @PB
               ,StandardizedBid = @SPB
               ,Savings = @Savings
               ,Rebate = ROUND (CONVERT (DECIMAL(8, 2), @Savings) * @K1, 2)
               ,BasicMemberPremium = ROUND (CASE WHEN @BM >= @PB THEN 0 ELSE @SPB - @SBM END, 2)
               ,GovtPremiumAdj = ROUND (CASE WHEN @BM >= @PB THEN 0 ELSE @SPB - @SBM END * (1 - @CF), 2)
               ,UncollectedPremium = @UncollectedPremium
               ,InsurerFee = @InsurerFee
               ,TotalMemberPremium = @MemberPremium;

        RETURN;
    END;
GO
