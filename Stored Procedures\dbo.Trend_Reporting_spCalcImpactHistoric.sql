SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:  Trend_Reporting_spCalcImpactHistoric
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: Sept-25-2020
--
-- DESCRIPTION: Computes incremental impact to allowed and utilization of each component trend by Plan and RateType for historic years to be used in reporting 
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--				@PlanInfoID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntBenefitCategory
--					LkpIntPlanYear
--					SavedHistoricalCrosswalk
--					Trend_CalcHistoricCostAndUse
--					Trend_CalcHistoricMembership
--					Trend_HistProcess_CalcPlanTrends_RepCat
--					Trend_Reporting_CalcCountyXWalkMembership
--					Trend_SavedComponentInfo			
--		
--          Write:  Trend_Reporting_CalcImpactHistoric
--                  Trend_Reporting_Log
--
-- VIEWS: Read:		vwPlanInfo
--
-- FUNCTIONS:		Trend_fnCalcStringToTable
--					Trend_fnSafeDivide
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- SEP-25-2020		1           Initial Version																								Jake Lewis
-- OCT-19-2020		2			Fixed issues with sonar qube																				Deepali Mittal
-- OCT-21-2020		3			Fixed line 181 for issue with termed plans																	Tanvi Khanna		
-- NOV-09-2020		4			Updates for component order																					Jake Lewis
--								Renamed 'Base' to 'Crosswalk and Terms'
--								Added 'New Plans' component 	
-- NOV-18-2020		5			Set MM=0 for all components (excl. 'New Plans') for new plans												Jake Lewis
-- DEC-10-2020		6			Name changes for the SP and some of the referenced tables													Jake Lewis
--								NEW SP = Trend_Reporting_spCalcImpactHistoric, OLD SP = Trend_Reporting_spCalcPlanImpactHistoric
--								NEW = Trend_HistProcess_CalcPlanTrends_RepCat, OLD = Trend_Reporting_CalcPlanHistoricTrends_RepCat
--								NEW = Trend_Reporting_CalcImpactHistoric, OLD = Trend_Reporting_CalcPlanImpactHistoric
-- MAR-15-2021		7			Code adjustments to improve efficiency and decrease runtime													Jake Lewis
--								Add PlanInfoID input parameter
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_Reporting_spCalcImpactHistoric]

@LastUpdateByID CHAR(7)
,@PlanInfoID    VARCHAR(MAX) = NULL

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            --------------------------------------------------------------------
            -- 0. Declare / set variables and pull parameters and source data --
            --------------------------------------------------------------------
            -- Declare Variables
            DECLARE @BaseComponentOrder INT = 0;
            DECLARE @BaseComponentReporting VARCHAR(50) = 'Crosswalk and Terms'; -- Impact of termed plans and crosswalks from prior year to current year
            DECLARE @MembershipComponentOrder INT = 1;
            DECLARE @MembershipComponentReporting VARCHAR(50) = 'Membership'; -- Impact of membership changes from prior year to current year
            DECLARE @NewPlansComponentOrder INT; -- Set this later
            DECLARE @NewPlansComponentReporting VARCHAR(50) = 'New Plans'; -- Impact of new plans which didn't crosswalk from the prior year
            DECLARE @RateType INT = 1; -- Impacts only calculated for RateType 1
            DECLARE @CurrentYear INT = (SELECT  PlanYearID
                                        FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                                        WHERE   IsCurrentYear = 1);
            DECLARE @MinHistoricYear INT = @CurrentYear - 4;
            DECLARE @NormalizedComponentOrder INT; -- Set this later	
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @LastUpdateDateTime DATETIME;
            DECLARE @errorMsg VARCHAR(500);

            -- Component order
            IF (SELECT  OBJECT_ID ('tempdb..#ComponentOrder')) IS NOT NULL
                DROP TABLE #ComponentOrder;
            SELECT  ComponentOrder + @MembershipComponentOrder AS ComponentOrder
                   ,ComponentReporting
            INTO    #ComponentOrder
            FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
            WHERE   Component NOT LIKE '%Rx%';  -- Exclude Rx components for historic impacts

            -- Set Normalized component order
            SET @NormalizedComponentOrder = (SELECT ComponentOrder
                                             FROM   #ComponentOrder
                                             WHERE  ComponentReporting = 'Normalized');

            -- Set New Plans component order = (maximum component order plus one)
            SET @NewPlansComponentOrder = (SELECT   MAX (ComponentOrder) + 1 FROM   #ComponentOrder);

            -- Component order and reporting for New Plans (no impact) 
            IF (SELECT  OBJECT_ID ('tempdb..#NewPlansComponentList')) IS NOT NULL
                DROP TABLE #NewPlansComponentList;
            SELECT  co.ComponentOrder
                   ,co.ComponentReporting
            INTO    #NewPlansComponentList
            FROM    #ComponentOrder co
            UNION ALL
            SELECT  @BaseComponentOrder, @BaseComponentReporting
            UNION ALL
            SELECT  @MembershipComponentOrder, @MembershipComponentReporting;

            -- Claim category mapping
            IF (SELECT  OBJECT_ID ('tempdb..#ClaimCatMap')) IS NOT NULL
                DROP TABLE #ClaimCatMap;
            SELECT  ReportingCategory
                   ,BenefitCategoryID
            INTO    #ClaimCatMap
            FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
            WHERE   ReportingCategory IS NOT NULL;

            -- Historic cost and use 
            IF (SELECT  OBJECT_ID ('tempdb..#HistoricCostUse')) IS NOT NULL
                DROP TABLE #HistoricCostUse;
            SELECT      PlanInfoID
                       ,BenefitCategoryID
                       ,SUM (ISNULL (Allowed, 0)) AS Allowed
                       ,SUM (ISNULL (Utilization, 0)) AS Utilization
            INTO        #HistoricCostUse
            FROM        dbo.Trend_CalcHistoricCostAndUse WITH (NOLOCK)
            GROUP BY    PlanInfoID
                       ,BenefitCategoryID;

            -- Historic membership
            IF (SELECT  OBJECT_ID ('tempdb..#HistoricMembership')) IS NOT NULL
                DROP TABLE #HistoricMembership;
            SELECT      PlanInfoID
                       ,SUM (ISNULL (MemberMonths, 0)) AS ReportingMemberMonths
                       ,SUM (ISNULL (MemberMonths, 0)) AS MemberMonths
            INTO        #HistoricMembership
            FROM        dbo.Trend_CalcHistoricMembership WITH (NOLOCK)
            GROUP BY    PlanInfoID;

            -- Component trends 
            IF (SELECT  OBJECT_ID ('tempdb..#ComponentTrend')) IS NOT NULL
                DROP TABLE #ComponentTrend;
            SELECT  PlanInfoID
                   ,ReportingCategory
                   ,Component
                   ,(1 + CostAdjustment) * (1 + UseAdjustment) - 1 AS AllowedAdjustment
                   ,UseAdjustment
            INTO    #ComponentTrend
            FROM    dbo.Trend_HistProcess_CalcPlanTrends_RepCat WITH (NOLOCK);

            -- New plans
            IF (SELECT  OBJECT_ID ('tempdb..#NewPlans')) IS NOT NULL
                DROP TABLE #NewPlans;
            SELECT  a.PlanYearID
                   ,a.PlanInfoID
                   ,a.CPS
            INTO    #NewPlans
            FROM    (SELECT     DISTINCT
                                vpi.PlanYear AS PlanYearID
                               ,shc.PlanInfoID
                               ,vpi.CPS AS CPS
                               ,shc.RenewalTypeID
                                --If IsNew = 1 for all counties for a given plan, then the line below will evaluate to 1, and thus this is a new plan. 
                                --If the line below evaluates to anything other than 1, then some (or all) of the counties have crosswalked, and thus the plan itself is not new. 
                               ,CASE WHEN SUM (CAST(shc.IsNew AS FLOAT)) / CAST(COUNT (shc.IsNew) AS FLOAT) = 1 THEN 1
                                     ELSE 0 END AS IsNew
                     FROM       dbo.SavedHistoricalCrosswalk shc WITH (NOLOCK)
                     LEFT JOIN  dbo.vwPlanInfo vpi WITH (NOLOCK)
                            ON vpi.PlanInfoID = shc.PlanInfoID
                     WHERE      vpi.PlanYear > @MinHistoricYear
                                AND vpi.PlanYear < @CurrentYear
                                AND vpi.IsOffMAModel = 'No'
                                AND vpi.IsHidden = 0
                                AND vpi.Region NOT IN ('Unmapped')
                     GROUP BY   vpi.PlanYear
                               ,shc.PlanInfoID
                               ,vpi.CPS
                               ,shc.RenewalTypeID) a
            WHERE   a.IsNew = 1
                    AND (a.PlanInfoID IN (SELECT    Val FROM   dbo.Trend_fnCalcStringToTable (@PlanInfoID, ',', 1) )
                         OR @PlanInfoID IS NULL);


            ------------------------------------------------------------------------------------------
            -- 1. Get experience total projection for Base (Crosswalk and Term) impacts (all years) --
            ------------------------------------------------------------------------------------------
            -- Get plan crosswalk
            IF (SELECT  OBJECT_ID ('tempdb..#Crosswalk')) IS NOT NULL
                DROP TABLE #Crosswalk;
            SELECT      shc.FromPlanInfoID
                       ,shc.PlanInfoID
            INTO        #Crosswalk
            FROM        dbo.SavedHistoricalCrosswalk shc WITH (NOLOCK)
           INNER JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
                   ON vpi.PlanInfoID = shc.FromPlanInfoID
            WHERE       vpi.PlanYear < @CurrentYear - 1
                        AND vpi.PlanYear > @MinHistoricYear - 1
                        AND vpi.IsOffMAModel = 'No'
                        AND vpi.IsHidden = 0
                        AND vpi.Region NOT IN ('Unmapped')
                        AND shc.PlanInfoID <> 0
                        AND (shc.PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@PlanInfoID, ',', 1) )
                             OR @PlanInfoID IS NULL)
            GROUP BY    shc.FromPlanInfoID
                       ,shc.PlanInfoID;

            -- Populate membership, allowed, and utilization data 
            IF (SELECT  OBJECT_ID ('tempdb..#Base')) IS NOT NULL DROP TABLE #Base;
            SELECT      DISTINCT
                        c.FromPlanInfoID
                       ,c.PlanInfoID
                       ,@BaseComponentOrder AS ComponentOrder
                       ,@BaseComponentReporting AS ComponentReporting
                       ,ccm.ReportingCategory
                       ,ccm.BenefitCategoryID
                       ,COALESCE (xw.ReportingMemberMonths, hm.ReportingMemberMonths, 0) AS ReportingMemberMonths
                       ,COALESCE (xw.MemberMonths, hm.MemberMonths, 0) AS MemberMonths
                       ,dbo.Trend_fnSafeDivide (ISNULL (hcu.Allowed, 0), COALESCE (xw.MemberMonths, hm.MemberMonths, 0), 0)
                        * COALESCE (xw.ReportingMemberMonths, hm.ReportingMemberMonths, 0) AS Allowed
                       ,dbo.Trend_fnSafeDivide (ISNULL (hcu.Utilization, 0), COALESCE (xw.MemberMonths, hm.MemberMonths, 0), 0)
                        * COALESCE (xw.ReportingMemberMonths, hm.ReportingMemberMonths, 0) AS Utilization
            INTO        #Base
            FROM        #Crosswalk c
           CROSS JOIN   #ClaimCatMap ccm
            LEFT JOIN   dbo.Trend_Reporting_CalcCountyXWalkMembership xw WITH (NOLOCK)
                   ON xw.FromPlanInfoID = c.FromPlanInfoID
                      AND   xw.PlanInfoID = c.PlanInfoID
            LEFT JOIN   #HistoricMembership hm
                   ON hm.PlanInfoID = c.FromPlanInfoID
            LEFT JOIN   #HistoricCostUse hcu
                   ON hcu.PlanInfoID = c.FromPlanInfoID
                      AND   hcu.BenefitCategoryID = ccm.BenefitCategoryID;


            ----------------------------------------
            -- 2. Roll up base data by PlanInfoID --
            ----------------------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#RollupBase')) IS NOT NULL
                DROP TABLE #RollupBase;
            SELECT      b.PlanInfoID
                       ,b.ComponentOrder
                       ,b.ComponentReporting
                       ,b.ReportingCategory
                       ,b.BenefitCategoryID
                       ,SUM (ISNULL (b.ReportingMemberMonths, 0)) AS ReportingMemberMonths
                       ,SUM (ISNULL (b.ReportingMemberMonths, 0)) AS MemberMonths   --Set MemberMonth = ReportingMemberMonths for base
                       ,SUM (ISNULL (b.Allowed, 0)) AS Allowed
                       ,SUM (ISNULL (b.Utilization, 0)) AS Utilization
            INTO        #RollupBase
            FROM        #Base b
            GROUP BY    b.PlanInfoID
                       ,b.ComponentOrder
                       ,b.ComponentReporting
                       ,b.ReportingCategory
                       ,b.BenefitCategoryID;


            ---------------------------------------
            -- 3. Get Normalized and Adjustments --
            ---------------------------------------
            -- Normalized is set equal to the final Allowed and Utilization
            -- Adjustments are set to the same, as we won't have historic adjustments flowing through 
            -- Set New Plans equal to normalized and adjustments (i.e. these plans are not new so no impact)
            IF (SELECT  OBJECT_ID ('tempdb..#NormalizedAndAdj')) IS NOT NULL
                DROP TABLE #NormalizedAndAdj;

            -- Normalized and adjustments
            SELECT      rb.PlanInfoID
                       ,co.ComponentOrder AS ComponentOrder
                       ,co.ComponentReporting AS ComponentReporting
                       ,rb.ReportingCategory
                       ,rb.BenefitCategoryID
                       ,ISNULL (hm.ReportingMemberMonths, 0) AS ReportingMemberMonths
                       ,ISNULL (hm.MemberMonths, 0) AS MemberMonths
                       ,ISNULL (hcu.Allowed, 0) AS Allowed
                       ,ISNULL (hcu.Utilization, 0) AS Utilization
            INTO        #NormalizedAndAdj
            FROM        #RollupBase rb
            LEFT JOIN   #HistoricCostUse hcu
                   ON hcu.PlanInfoID = rb.PlanInfoID
                      AND   hcu.BenefitCategoryID = rb.BenefitCategoryID
            LEFT JOIN   #HistoricMembership hm
                   ON hm.PlanInfoID = rb.PlanInfoID
           CROSS JOIN   #ComponentOrder co
            WHERE       co.ComponentOrder >= @NormalizedComponentOrder;

            -- New Plans component
            INSERT INTO #NormalizedAndAdj
            SELECT      rb.PlanInfoID
                       ,@NewPlansComponentOrder AS ComponentOrder
                       ,@NewPlansComponentReporting AS ComponentReporting
                       ,rb.ReportingCategory
                       ,rb.BenefitCategoryID
                       ,ISNULL (hm.ReportingMemberMonths, 0) AS ReportingMemberMonths
                       ,ISNULL (hm.MemberMonths, 0) AS MemberMonths
                       ,ISNULL (hcu.Allowed, 0) AS Allowed
                       ,ISNULL (hcu.Utilization, 0) AS Utilization
            FROM        #RollupBase rb
            LEFT JOIN   #HistoricCostUse hcu
                   ON hcu.PlanInfoID = rb.PlanInfoID
                      AND   hcu.BenefitCategoryID = rb.BenefitCategoryID
            LEFT JOIN   #HistoricMembership hm
                   ON hm.PlanInfoID = rb.PlanInfoID;


            -----------------------------
            -- 4. Set up Metrics table --
            -----------------------------
            -- Add Membership metric to #Metrics table.  
            -- Metrics for other components will be iteratively added to #Metrics in the next step.
            IF (SELECT  OBJECT_ID ('tempdb..#Metrics')) IS NOT NULL DROP TABLE #Metrics;
            SELECT      0 AS IncrementControl
                       ,r.PlanInfoID
                       ,@MembershipComponentOrder AS ComponentOrder
                       ,@MembershipComponentReporting AS ComponentReporting
                       ,r.ReportingCategory
                       ,r.BenefitCategoryID
                       ,hm.ReportingMemberMonths
                       ,hm.MemberMonths
                       ,dbo.Trend_fnSafeDivide (r.Allowed, r.ReportingMemberMonths, 0) AS AllowedPMPM
                       ,dbo.Trend_fnSafeDivide (r.Utilization, r.ReportingMemberMonths, 0) AS UtilizationPMPM
                       ,dbo.Trend_fnSafeDivide (r.Allowed, r.ReportingMemberMonths, 0) * hm.MemberMonths AS Allowed
                       ,dbo.Trend_fnSafeDivide (r.Utilization, r.ReportingMemberMonths, 0) * hm.MemberMonths AS Utilization
            INTO        #Metrics
            FROM        #RollupBase r
            LEFT JOIN   #HistoricMembership hm
                   ON hm.PlanInfoID = r.PlanInfoID;


            ------------------------------------------
            -- 5. Apply component trends to metrics --
            ------------------------------------------
            DECLARE @CurIncrementControl INT;
            DECLARE @CurComponentReporting VARCHAR(50);
            DECLARE @CurComponentOrder INT;

            DECLARE Component_Cursor CURSOR LOCAL FAST_FORWARD FOR

            -- Cursor iterates over all components in current year
            SELECT  ROW_NUMBER () OVER (ORDER BY ComponentOrder) AS IncrementControl
                   ,ComponentOrder
                   ,ComponentReporting
            FROM    #ComponentOrder
            WHERE   ComponentOrder < @NormalizedComponentOrder;

            -- Open cursor and begin iteration			  
            OPEN Component_Cursor;
            FETCH NEXT FROM Component_Cursor
            INTO @CurIncrementControl
                ,@CurComponentOrder
                ,@CurComponentReporting;

            WHILE @@Fetch_Status = 0

                BEGIN

                    INSERT INTO #Metrics

                    SELECT      @CurIncrementControl AS IncrementControl
                               ,m.PlanInfoID
                               ,@CurComponentOrder AS ComponentOrder
                               ,@CurComponentReporting AS ComponentReporting
                               ,m.ReportingCategory
                               ,m.BenefitCategoryID
                               ,ISNULL (hm.ReportingMemberMonths, 0) AS ReportingMemberMonths
                               ,ISNULL (hm.MemberMonths, 0) AS MemberMonths
                               ,m.AllowedPMPM * (1 + ISNULL (ct.AllowedAdjustment, 0)) AS AllowedPMPM
                               ,m.UtilizationPMPM * (1 + ISNULL (ct.UseAdjustment, 0)) AS UtilizationPMPM
                               ,m.AllowedPMPM * (1 + ISNULL (ct.AllowedAdjustment, 0)) * ISNULL (hm.MemberMonths, 0) AS Allowed
                               ,m.UtilizationPMPM * (1 + ISNULL (ct.UseAdjustment, 0)) * ISNULL (hm.MemberMonths, 0) AS Utilization
                    FROM        #Metrics m
                    LEFT JOIN   #ComponentTrend ct
                           ON ct.PlanInfoID = m.PlanInfoID
                              AND   ct.ReportingCategory = m.ReportingCategory
                              AND   ct.Component = @CurComponentReporting
                    LEFT JOIN   #HistoricMembership hm
                           ON hm.PlanInfoID = m.PlanInfoID
                    WHERE       m.IncrementControl + 1 = @CurIncrementControl;

                    FETCH NEXT FROM Component_Cursor
                    INTO @CurIncrementControl
                        ,@CurComponentOrder
                        ,@CurComponentReporting;

                END;

            CLOSE Component_Cursor;
            DEALLOCATE Component_Cursor;


            ---------------------
            -- 6. Final output --
            ---------------------

            -- If @PlanInfoID input parameter is NULL, delete everything. 
            IF @PlanInfoID IS NULL
                BEGIN
                    DELETE  FROM dbo.Trend_Reporting_CalcImpactHistoric WHERE   1 = 1;
                END;
            ELSE -- If @PlanInfoID input parameter is not NULL, only delete records for which the PlanInfoID exists in @PlanInfoID
                BEGIN
                    DELETE  t
                    FROM    dbo.Trend_Reporting_CalcImpactHistoric t
                    INNER JOIN    (SELECT Val AS PlanInfoID
                             FROM   dbo.Trend_fnCalcStringToTable (@PlanInfoID, ',', 1) ) p
                      ON p.PlanInfoID = t.PlanInfoID
                    WHERE   p.PlanInfoID IS NOT NULL;
                END;

            SET @LastUpdateDateTime = GETDATE ();

            IF (SELECT  OBJECT_ID ('tempdb..#Output')) IS NOT NULL DROP TABLE #Output;

            -- Base 
            SELECT  PlanInfoID
                   ,@RateType AS RateType
                   ,BenefitCategoryID
                   ,ComponentReporting
                   ,ComponentOrder
                   ,ISNULL (MemberMonths, 0) AS MemberMonths
                   ,ISNULL (Allowed, 0) AS Allowed
                   ,ISNULL (Utilization, 0) AS Utilization
                   ,@LastUpdateByID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
            INTO    #Output
            FROM    #RollupBase;

            -- Component impacts
            INSERT INTO #Output
            SELECT  PlanInfoID
                   ,@RateType AS RateType
                   ,BenefitCategoryID
                   ,ComponentReporting
                   ,ComponentOrder
                   ,ISNULL (MemberMonths, 0)
                   ,ISNULL (Allowed, 0)
                   ,ISNULL (Utilization, 0)
                   ,@LastUpdateByID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM    #Metrics;

            -- Normalized and adjustments
            INSERT INTO #Output
            SELECT  PlanInfoID
                   ,@RateType AS RateType
                   ,BenefitCategoryID
                   ,ComponentReporting
                   ,ComponentOrder
                   ,ISNULL (MemberMonths, 0)
                   ,ISNULL (Allowed, 0)
                   ,ISNULL (Utilization, 0)
                   ,@LastUpdateByID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM    #NormalizedAndAdj;

            -- New Plans: New Plans component
            INSERT INTO #Output
            SELECT      np.PlanInfoID
                       ,@RateType AS RateType
                       ,ccm.BenefitCategoryID
                       ,@NewPlansComponentReporting
                       ,@NewPlansComponentOrder
                       ,ISNULL (hm.MemberMonths, 0)
                       ,ISNULL (hcu.Allowed, 0)
                       ,ISNULL (hcu.Utilization, 0)
                       ,@LastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        #NewPlans np
           CROSS JOIN   #ClaimCatMap ccm
            LEFT JOIN   #HistoricMembership hm
                   ON hm.PlanInfoID = np.PlanInfoID
            LEFT JOIN   #HistoricCostUse hcu
                   ON hcu.PlanInfoID = np.PlanInfoID
                      AND   hcu.BenefitCategoryID = ccm.BenefitCategoryID;

            -- New Plans: all other components 
            -- Need these records for the report, but allowed, utilization, and membership will be 0
            INSERT INTO #Output
            SELECT      np.PlanInfoID
                       ,@RateType AS RateType
                       ,ccm.BenefitCategoryID
                       ,cl.ComponentReporting
                       ,cl.ComponentOrder
                       ,0 AS MemberMonths
                       ,0 AS Allowed
                       ,0 AS Utilization
                       ,@LastUpdateByID AS LastUpdateByID
                       ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM        #NewPlans np
           CROSS JOIN   #ClaimCatMap ccm
           CROSS JOIN   #NewPlansComponentList cl
            LEFT JOIN   #HistoricMembership hm
                   ON hm.PlanInfoID = np.PlanInfoID;

            -- Write output to table
            INSERT INTO dbo.Trend_Reporting_CalcImpactHistoric
                (PlanInfoID
                ,RateType
                ,BenefitCategoryID
                ,ComponentReporting
                ,ComponentOrder
                ,MemberMonths
                ,Allowed
                ,Utilization
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  PlanInfoID
                   ,RateType
                   ,BenefitCategoryID
                   ,ComponentReporting
                   ,ComponentOrder
                   ,MemberMonths
                   ,Allowed
                   ,Utilization
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #Output;

            COMMIT TRANSACTION transactionMain;

        END TRY

        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@LastUpdateByID
               ,GETDATE ();


    END;

GO
