SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
         
      
-- ----------------------------------------------------------------------------------------------------------------------      
-- PROCEDURE NAME: spAppTrendGetPlanInfoIds      
--      
-- AUTHOR: Ramande<PERSON>ni     
--      
-- CREATED DATE: 2020-March-17      
--      
-- DESCRIPTION: Procedure responsible for Fetching PlanInfoID corresponding to CPS and PlanYearID.      
--                
      
-- TYPE: New      
--      
-- PARAMETERS:      
-- Input:      
--       
      
      
      
-- TABLES:       
-- Read:SavedPlanInfo      
--     
--        
      
-- Write:      
--           
      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:      
--       
       
-- $HISTORY       
-- ----------------------------------------------------------------------------------------------------------------------      
-- DATE    VERSION  CHANGES MADE              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------      
-- 2020-March-17  1   Initial Version              Ramandeep Saini    
-- 2020-Dec-07    2   With(Nolock)                 Ramandeep Saini
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [dbo].[spAppTrendGetPlanInfoIds]    
AS      
BEGIN      
      
   SELECT DISTINCT PlanInfoID,CPS,PlanYear  
    From dbo.SavedPlanInfo a With(NOLOCK) INNER JOIN LkpIntPlanYear b With(NOLOCK) ON a.PlanYear=b.PlanYearID  
  WHERE b.IsBidYear=1  
     
      
END      
GO
