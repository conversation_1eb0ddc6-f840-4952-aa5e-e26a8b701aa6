SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO




-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spAppImportMSBSelectionsStagedData
--
-- $HISTORY  
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2025-Jan-08      1			Initial Version						                                Archana Sahu
-- ----------------------------------------------------------------------------------------------------------------------

CREATE  PROCEDURE [dbo].[spAppImportMSBSelectionsStagedData]
(@StageId VARCHAR(100))
AS
BEGIN
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

    DECLARE @tbl__importData TABLE
    (
        ForecastID INT,
        MSBCode VARCHAR(6),
        AddedBenefitName VARCHAR(200),
		IsOverride VARCHAR(2),
        UserID CHAR(7)
    );

    INSERT INTO @tbl__importData
    SELECT ForecastID,
			MSBCode,
			AddedBenefitName,
			CASE WHEN IsOverride = 'Y' THEN 1 ELSE 0 END,
           @UserId
    FROM
        OPENJSON(@jsonData, '$.MSBSelections')
        WITH
        (
            ForecastID INT,
            MSBCode VARCHAR(6),
            AddedBenefitName VARCHAR(200),
			IsOverride VARCHAR(2) '$.Override'
        );

	DROP TABLE IF EXISTS #results;
		CREATE TABLE #results
		(
			planyearid INT,
			forecastid INT,
			AddedBenefitTypeID int,
			Addedbenefitname varchar(50),
			InAddedBenefitDescription VARCHAR(2000),
			InAddedBenefitAllowed DECIMAL(6,2),
			InAddedBenefitUtilization DECIMAL(6,2),
			InAddedBenefitCostShare DECIMAL(6,2),
			OonAddedBenefitDescription VARCHAR(2000),
			OonAddedBenefitAllowed DECIMAL(6,2),
			OonAddedBenefitUtilization DECIMAL(6,2),
			OonAddedBenefitCostShare DECIMAL(6,2),
			BidServiceCatID INT,
			IsvalueAdded INT,
			IsNetwork INT,
			Ishidden INT,
			Isoverride INT,
			UserID CHAR(7)
		)


		INSERT INTO #results
		SELECT lkp.PlanYearID, a.ForecastID, lkp.AddedBenefitTypeID, a.AddedBenefitName, lkp.INAddedBenefitDescription, lkp.INAddedBenefitAllowed,
		lkp.INAddedBenefitUtilization, lkp.INAddedBenefitCostShare, lkp.OONAddedBenefitDescription, lkp.OONAddedBenefitAllowed, lkp.OONAddedBenefitUtilization,
		lkp.OONAddedBenefitCostShare, lkp.BidServiceCatID, lkp.IsValueAdded, lkp.IsNetwork, 0, a.IsOverride, a.UserID --, GETDATE()
		FROM @tbl__importData a INNER JOIN	dbo.LkpIntAddedBenefitType lkp ON a. AddedBenefitName = lkp.AddedBenefitName

	DECLARE @BidYear datetime = dbo.fnGetBidYear();

	MERGE INTO dbo.SavedPlanAddedBenefits AS target
    USING #results AS source
    ON (
           target.ForecastID = source.ForecastID AND
           target.AddedBenefitTypeID = source.AddedBenefitTypeID
       )
    WHEN MATCHED THEN

	 UPDATE SET 
			 target.PlanYearID = source.planyearid,
			  target.AddedBenefitName = source.AddedBenefitName, 
			  target.INAddedBenefitDescription = source.INAddedBenefitDescription,
			  target.INAddedBenefitAllowed = source.InAddedBenefitAllowed,
			  target.INAddedBenefitUtilization = source.InAddedBenefitUtilization,
			  target.INAddedBenefitCostShare = source.InAddedBenefitCostShare,
			  target.OONAddedBenefitDescription = source.OonAddedBenefitDescription,
			  target.OONAddedBenefitAllowed = source.OonAddedBenefitAllowed,
			  target.OONAddedBenefitUtilization = source.OonAddedBenefitUtilization,
			  target.OONAddedBenefitCostShare = source.OonAddedBenefitCostShare,
			  target.BidServiceCatID = source.BidServiceCatID,
			  target.IsValueAdded = source.IsvalueAdded,
			  target.IsNetwork = source.IsNetwork,
			  target.IsHidden = 0,
              target.LastUpdateByID = source.UserID , 
              target.LastUpdateDateTime = GETDATE(),
			  target.IsOverride = source.Isoverride

	WHEN NOT MATCHED BY TARGET THEN

	INSERT (PlanYearID, ForecastID, AddedBenefitTypeID, AddedBenefitName, INAddedBenefitDescription, INAddedBenefitAllowed, INAddedBenefitUtilization, INAddedBenefitCostShare,
	OONAddedBenefitDescription, OONAddedBenefitAllowed, OONAddedBenefitUtilization, OONAddedBenefitCostShare, BidServiceCatID, IsValueAdded, IsNetwork, IsHidden,
	LastUpdateByID, LastUpdateDateTime, IsOverride) 
	VALUES
	(source.planyearid, source.forecastid, source.AddedBenefitTypeID, source.Addedbenefitname, source.InAddedBenefitDescription, source.InAddedBenefitAllowed, 
	source.InAddedBenefitUtilization, source.InAddedBenefitCostShare, source.OonAddedBenefitDescription, source.OonAddedBenefitAllowed, source.OonAddedBenefitUtilization, 
	source.OonAddedBenefitCostShare, source.BidServiceCatID, source.IsvalueAdded, source.IsNetwork, source.Ishidden, source.UserID, GETDATE(), source.Isoverride); 
	
	--reprice plan flag
	UPDATE dbo.SavedForecastSetup
	SET IsToReprice = 1,
	LastUpdateByID = msb.UserID,
	LastUpdateDateTime = GETDATE()
	FROM dbo.SavedForecastSetup sfs
	JOIN #results msb
	ON sfs.forecastid = msb.forecastid
	AND sfs.planyear = @BidYear

	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
GO
