SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-------------------------------------------------------------------------------------------------------------------------
-- /***********************************************************************************************************************/
-- -------------------------------------------------------------------------------------------------------------------------
-- DESCRIPTION: 
--Stored Procedure that Recompiles base data aggregated to Provider Level vs Grouper
--This sp is initially executed by PREP team manually whenever a new DF data sync is completed.  This results in a full recompile.
--This sp is subsequently called from PREP VBA at end of Grouper Upload and PREP passes the old grouper to provider mapping as an input table.
--   A recompile for any changes in Grouper to Provider mapping occurs.
--   The sp uses the updated backend [FEM_GrouperUpload_Direct] table as the new mapping
--
-- Inputs:
--        Data table of user defined type [PREP_RegionGrouperToProviderMap] 
--           that will hold "old" Grouper to Provider mapping
--        Flag to indicate whether a full recompile should be done vs a recompile based only on changes in mapping for a region
-- Reads:
--        [FEM_GrouperUpload_Direct], the table that holds the Grouper Upload from PREP
--        [W_FEM_SavedCUFinance_DFSync], at Grouper Level
--        [W_FEM_SavedCUClaims_DFSync], at Grouper Level
-- Writes:
--        [FEM_GrouperUpload], at Provider Level
--        [W_FEM_SavedCUClaims], at Provider Level
--        [W_FEM_SavedCUFinance], at Provider Level
-- Executes:
--        [spAppTruncateW_FEM_Tables]
--
--
-- -------------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE														DEVELOPER		
-- -------------------------------------------------------------------------------------------------------------------------
--
-- 2022-Aug    		1			Initial Build         												Bob Knadler
--    thru 2022-Oct-17
-- 2023-Feb-01      2           Major Revamp to logic recognizing changes in Grouper Upload         Bob Knadler
--                               Intended to address issues when deletions occur for a Provider
--                               name that is shared by multiple regions
-- 2023-Feb-08      3           Changes to ensure that 'FFS' provider is recompiled when needed     Bob Knadler   
-- 2023-Feb-22      4           Further ensure forced deletion and recompile of Providers           Bob Knadler   
--                                in both old vs new, and showing only a one-sided difference
----------------------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------------------

CREATE PROC [dbo].[spPREPGrouperToProviderAggregation]
(
     @OldGrouperToProviderMap AS [PREP_RegionGrouperToProviderMap] READONLY-- user defined table type
	,@NewDFBaseData AS BIT -- 1 => Complete recompile, 0 => Recompile only for changes in grouper to provider mapping for given region
)
AS

BEGIN -- PROC
 SET NOCOUNT ON;
 DECLARE @DefaultFFSGrouperID AS VARCHAR(8) = '00000000'
  IF @NewDFBaseData = 1
    BEGIN -- Complete recompile
--     SELECT 'Will do complete recompile'

       IF (SELECT OBJECT_ID('tempdb..#GrprMultProv')) IS NOT NULL            
         BEGIN
           DROP TABLE #GrprMultProv
         END;          
       
       --Get groupers appearing in multiple regions
       SELECT [GrouperID]
              ,COUNT(*) AS GrprRecCnt
              ,MIN([Region]) AS MinRgn
              ,MAX([Region]) AS MaxRgn
              ,MIN([Provider]) AS MinProv
              ,MAX([Provider]) AS MaxProv
         INTO #GrprMultProv
         FROM dbo.FEM_GrouperUpload_Direct WITH (NOLOCK)
         WHERE [GrouperID] <> @DefaultFFSGrouperID --Trigger auto generates this one and it will remain
           AND [Provider] <> 'FFS'
         GROUP BY [GrouperID]
         HAVING COUNT(*) > 1
       
       
       IF (SELECT OBJECT_ID('tempdb..#UniqueProv')) IS NOT NULL            
         BEGIN
           DROP TABLE #UniqueProv
         END;          
       
       -- Get unique Providers and assign ProvGrprIDs; using Max Provider name where grouper is in multiple regions
       SELECT gu2.[Provider]
             ,CASE
                WHEN gu2.[Provider] = 'FFS' THEN @DefaultFFSGrouperID
                ELSE RIGHT(@DefaultFFSGrouperID + REPLACE(CAST( ROW_NUMBER() OVER(ORDER BY gu2.[Provider] ASC) AS CHAR(8)), ' ', ''), 8) 
              END AS ProvGrprID
            ,COUNT(*) AS RecCnt
         INTO #UniqueProv
         FROM dbo.FEM_GrouperUpload_Direct gu2 WITH (NOLOCK)
           LEFT JOIN #GrprMultProv gmp
       	  ON gu2.GrouperID = gmp.GrouperID
       	    AND gu2.[Provider]= gmp.MaxProv
         GROUP BY gu2.[Provider]
              

       DELETE FROM dbo.FEM_GrouperUpload WHERE 1=1 --WHERE statement is used to avoid messaging

	   INSERT INTO dbo.FEM_GrouperUpload
        (
          Region,
          [Provider],
          GrouperID,
          UserID,
          [TimeStamp]
        )
       SELECT DISTINCT
              gu3.Region
             ,ISNULL(gmp.MaxProv, gu3.[Provider]) AS AssignedProv
       	     ,ISNULL(pu2.ProvGrprID, pu1.ProvGrprID) AS ProvGrprID
             ,'PREP' AS [UserID]
             ,GETDATE() AS [TimeStamp]
         FROM dbo.FEM_GrouperUpload_Direct gu3 WITH (NOLOCK)
           LEFT JOIN #GrprMultProv gmp
       	  ON gu3.GrouperID = gmp.GrouperID
       	LEFT JOIN #UniqueProv pu1
       	  ON gu3.[Provider] = pu1.[Provider]
       	LEFT JOIN #UniqueProv pu2
       	  ON gmp.MaxProv = pu2.[Provider]
              
	   -- Specific Truncate sp below is for quicker emptying of tables and is set up to work even in PROD
		EXECUTE dbo.spAppTruncateW_FEM_Tables

	/*** Populate PREP base data tables with a full recompile at Provider Level ***/

		INSERT INTO dbo.W_FEM_SavedCUFinance
		(
			PREPRunID,
			Year,
			CPS,
			ContractNumber,
			PlanID,
			SegmentID,
			SSStateCountyCD,
			GrouperID,
			GrouperName,
			IsESRD,
			IsHospice,
			MM,
			RiskScore,
			MAPremium,
			PDPremium,
			RxPaid,
			RxMbrCS,
			OPB,
			PCPCap,
			SpecCap,
			OtherCap,
			Quality,
			OPB_InBid,
			OPB_NotInBid
		)
		SELECT cuf.PREPRunID, 
			   cuf.[Year],
			   cuf.CPS,
			   cuf.ContractNumber,
			   cuf.PlanID,
			   cuf.SegmentID,
			   cuf.SSStateCountyCD,
			   ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID) AS GrouperID,
			   ISNULL(pgm.[Provider], 'FFS') AS GrouperName,
			   cuf.IsESRD,
			   cuf.IsHospice,
			   SUM(cuf.MM) AS MM,
			   SUM(cuf.RiskScore) AS RiskScore,
			   SUM(cuf.MAPremium) AS MAPremium,
			   SUM(cuf.PDPremium) AS PDPremium,
			   SUM(cuf.RxPaid) AS RxPaid,
			   SUM(cuf.RxMbrCS) AS RxMbrCS,
			   SUM(cuf.OPB) AS OPB,
			   SUM(cuf.PCPCap) AS PCPCap,
			   SUM(cuf.SpecCap) AS SpecCap,
			   SUM(cuf.OtherCap) AS OtherCap,
			   SUM(cuf.Quality) AS Quality,
			   SUM(cuf.OPB_InBid) AS OPB_InBid,
			   SUM(cuf.OPB_NotInBid) AS OPB_NotInBid
		  FROM dbo.W_FEM_SavedCUFinance_DFSync cuf WITH (NOLOCK)
			LEFT JOIN (SELECT (pm.ContractNumber + '-' + pm.PlanID + '-' + pm.SegmentID) AS CPS
							   ,pm.Region
							   ,sri.ActuarialRegionID
						  FROM dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
							INNER JOIN dbo.SavedRegionInfo sri WITH (NOLOCK)
							  ON pm.Region = sri.ActuarialRegion
						) pm2
				ON cuf.CPS = pm2.CPS
			LEFT JOIN (SELECT gu.Region
							 ,ISNULL(gmp.MaxProv, gu.[Provider]) AS [Provider]
							 ,ISNULL(pu2.ProvGrprID, pu1.ProvGrprID) AS ProvGrprID
							 ,gu.GrouperID
						 FROM dbo.FEM_GrouperUpload_Direct gu WITH (NOLOCK)
						   LEFT JOIN #GrprMultProv gmp
               				 ON gu.GrouperID = gmp.GrouperID
               			   LEFT JOIN #UniqueProv pu1
               				 ON gu.[Provider] = pu1.[Provider]
               			   LEFT JOIN #UniqueProv pu2
               				 ON gmp.MaxProv = pu2.[Provider]
					  ) pgm
				ON cuf.GrouperID = pgm.GrouperID
				  AND pm2.Region = pgm.Region
		  GROUP BY cuf.PREPRunID,
			   cuf.[Year],
			   cuf.CPS,
			   cuf.ContractNumber,
			   cuf.PlanID,
			   cuf.SegmentID,
			   cuf.SSStateCountyCD,
			   ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID),
			   ISNULL(pgm.[Provider], 'FFS'),
			   cuf.IsESRD,
			   cuf.IsHospice
/* 
Warning: Null value is eliminated by an aggregate or other SET operation.
*/

		INSERT INTO dbo.W_FEM_SavedCUClaims
		(
			PREPRunID,
			Year,
			CPS,
			ContractNumber,
			PlanID,
			SegmentID,
			GrouperID,
			GrouperName,
			IsESRD,
			IsHospice,
			BenefitCategoryID,
			CPaid,
			CMbrCS,
			DEPaid,
			DEMbrCS,
			EPaid,
			EMbrCS,
			PRA,
			OPB_InBid
		)
		SELECT cuc.PREPRunID,
			   cuc.[Year],
			   cuc.CPS,
			   cuc.ContractNumber,
			   cuc.PlanID,
			   cuc.SegmentID,
			   ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID) AS GrouperID,
			   ISNULL(pgm.[Provider], 'FFS') AS GrouperName,
			   cuc.IsESRD,
			   cuc.IsHospice,
			   cuc.BenefitCategoryID,
			   SUM(cuc.CPaid) AS CPaid,
			   SUM(cuc.CMbrCS) AS CMbrCS,
			   SUM(cuc.DEPaid) AS DEPaid,
			   SUM(cuc.DEMbrCS) AS DEMbrCS,
			   SUM(cuc.EPaid) AS EPaid,
			   SUM(cuc.EMbrCS) AS EMbrCS,
			   SUM(cuc.PRA) AS PRA,
			   SUM(cuc.OPB_InBid) AS OPB_InBid
		  FROM dbo.W_FEM_SavedCUClaims_DFSync cuc WITH (NOLOCK)
			LEFT JOIN (SELECT (pm.ContractNumber + '-' + pm.PlanID + '-' + pm.SegmentID) AS CPS
							   ,pm.Region
							   ,sri.ActuarialRegionID
						  FROM dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
							INNER JOIN dbo.SavedRegionInfo sri WITH (NOLOCK)
							  ON pm.Region = sri.ActuarialRegion
						) pm2
				ON cuc.CPS = pm2.CPS
			LEFT JOIN (SELECT gu.Region
							 ,ISNULL(gmp.MaxProv, gu.[Provider]) AS [Provider]
							 ,ISNULL(pu2.ProvGrprID, pu1.ProvGrprID) AS ProvGrprID
							 ,gu.GrouperID
						 FROM dbo.FEM_GrouperUpload_Direct gu WITH (NOLOCK)
						   LEFT JOIN #GrprMultProv gmp
               				 ON gu.GrouperID = gmp.GrouperID
               			   LEFT JOIN #UniqueProv pu1
               				 ON gu.[Provider] = pu1.[Provider]
               			   LEFT JOIN #UniqueProv pu2
               				 ON gmp.MaxProv = pu2.[Provider]
					  ) pgm
				ON cuc.GrouperID = pgm.GrouperID
				  AND pm2.Region = pgm.Region
		  GROUP BY cuc.PREPRunID,
			   cuc.[Year],
			   cuc.CPS,
			   cuc.ContractNumber,
			   cuc.PlanID,
			   cuc.SegmentID,
			   ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID),
			   ISNULL(pgm.[Provider], 'FFS'),
			   cuc.IsESRD,
			   cuc.IsHospice,
			   cuc.BenefitCategoryID

      --Clean up to release temp tables
       IF (SELECT OBJECT_ID('tempdb..#GrprMultProv')) IS NOT NULL            
         BEGIN
           DROP TABLE #GrprMultProv
         END;          

       IF (SELECT OBJECT_ID('tempdb..#UniqueProv')) IS NOT NULL            
         BEGIN
           DROP TABLE #UniqueProv
         END;          


    END  -- Complete recompile

  ELSE
 
/************************************************************************************************/

    BEGIN -- Recompile based on changes in mapping
 
      DECLARE @Rgn AS VARCHAR(40)
      SELECT TOP (1) @Rgn = Region FROM @OldGrouperToProviderMap


		/*** START Get Exapnded FFS/Deemed Non-Risk Groupers wrt to "Old" GrouperUpload passed to sp ***/

		IF (SELECT OBJECT_ID('tempdb..#ExpandedGUOld')) IS NOT NULL            
			BEGIN
			DROP TABLE #ExpandedGUOld
			END;          

		CREATE TABLE #ExpandedGUOld (
			[Region] [varchar](40) NOT NULL,
			[Provider] [varchar](255) NOT NULL,
			[GrouperID] [varchar](10) NOT NULL,
			CONSTRAINT [PK_ExpandedGUOld_RegionProviderGrouperID] PRIMARY KEY CLUSTERED 
		(
			[Region] ASC,
			[Provider] ASC,
			[GrouperID] ASC
		)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90)
		)

		INSERT INTO #ExpandedGUOld
		(
			Region,
			[Provider],
			GrouperID
		)
		SELECT DISTINCT
				Region,
				[Provider],
				GrouperID
			FROM @OldGrouperToProviderMap


		INSERT INTO #ExpandedGUOld
		(
			Region,
			Provider,
			GrouperID
		)
		SELECT DISTINCT
				A.Region
				,A.[Provider]
				,A.GrouperID
		FROM
		(
		SELECT cuf.PREPRunID
				,ISNULL(pm.Region, 'UNMAPPED') AS Region
				,cuf.GrouperID
				,ISNULL(gu.[Provider], 'FFS') AS [Provider]
				,COUNT(*) AS GrprRecCnt
			FROM dbo.W_FEM_SavedCUFinance_DFSync cuf WITH (NOLOCK)
		--LEFT
			INNER JOIN dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
				ON cuf.ContractNumber = pm.ContractNumber
				AND cuf.PlanID = pm.PlanID
				AND cuf.SegmentID = pm.SegmentID
				AND pm.Region = @Rgn
			LEFT JOIN @OldGrouperToProviderMap gu
				ON cuf.GrouperID = gu.GrouperID
			WHERE gu.[Provider] IS NULL -- OR gu.[Provider] = 'FFS'
			GROUP BY cuf.PREPRunID
					,pm.Region
					,cuf.GrouperID
					,ISNULL(gu.[Provider], 'FFS')
		UNION
		SELECT cuc.PREPRunID
				,ISNULL(pm.Region, 'UNMAPPED') AS Region
				,cuc.GrouperID
				,ISNULL(gu.[Provider], 'FFS') AS [Provider]
				,COUNT(*) AS GrprRecCnt
			FROM dbo.W_FEM_SavedCUClaims_DFSync cuc WITH (NOLOCK)
		--LEFT
			INNER JOIN dbo.W_FEM_PlanMapping pm
				ON cuc.ContractNumber = pm.ContractNumber
				AND cuc.PlanID = pm.PlanID
				AND cuc.SegmentID = pm.SegmentID
				AND pm.Region = @Rgn
			LEFT JOIN @OldGrouperToProviderMap gu
				ON cuc.GrouperID = gu.GrouperID
			WHERE gu.[Provider] IS NULL -- OR gu.[Provider] = 'FFS'
			GROUP BY cuc.PREPRunID
					,pm.Region
					,cuc.GrouperID
					,ISNULL(gu.[Provider], 'FFS')
		) A
		ORDER BY A.Region
				,A.[Provider]
				,A.GrouperID

		/*** END Get Exapnded FFS/Deemed Non-Risk Groupers wrt to "Old" GrouperUpload passed to sp ***/


		/*** START Get Exapnded FFS/Deemed Non-Risk Groupers wrt to "New" GrouperUpload_Direct ***/

		IF (SELECT OBJECT_ID('tempdb..#ExpandedGUNew')) IS NOT NULL            
			BEGIN
			  DROP TABLE #ExpandedGUNew
			END;          

		CREATE TABLE #ExpandedGUNew (
			[Region] [varchar](40) NOT NULL,
			[Provider] [varchar](255) NOT NULL,
			[GrouperID] [varchar](10) NOT NULL,
			CONSTRAINT [PK_ExpandedGUNew_RegionProviderGrouperID] PRIMARY KEY CLUSTERED 
		(
			[Region] ASC,
			[Provider] ASC,
			[GrouperID] ASC
		)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90)
		)

		INSERT INTO #ExpandedGUNew
		(
			Region,
			[Provider],
			GrouperID
		)
		SELECT DISTINCT
				Region,
				[Provider],
				GrouperID
			FROM dbo.FEM_GrouperUpload_Direct WITH (NOLOCK)
			WHERE Region = @Rgn


		INSERT INTO #ExpandedGUNew
		(
			Region,
			Provider,
			GrouperID
		)
		SELECT DISTINCT
				A.Region
				,A.[Provider]
				,A.GrouperID
		FROM
		(
		SELECT cuf.PREPRunID
				,ISNULL(pm.Region, 'UNMAPPED') AS Region
				,cuf.GrouperID
				,ISNULL(gu.[Provider], 'FFS') AS [Provider]
				,COUNT(*) AS GrprRecCnt
			FROM dbo.W_FEM_SavedCUFinance_DFSync cuf WITH (NOLOCK)
		--LEFT
			INNER JOIN dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
				ON cuf.ContractNumber = pm.ContractNumber
				AND cuf.PlanID = pm.PlanID
				AND cuf.SegmentID = pm.SegmentID
				AND pm.Region = @Rgn
			LEFT JOIN dbo.FEM_GrouperUpload_Direct gu WITH (NOLOCK)
				ON cuf.GrouperID = gu.GrouperID
				AND gu.Region = @Rgn
			WHERE gu.[Provider] IS NULL -- OR gu.[Provider] = 'FFS'
			GROUP BY cuf.PREPRunID
					,pm.Region
					,cuf.GrouperID
					,ISNULL(gu.[Provider], 'FFS')
		--  ORDER BY 1,2,4,3
		UNION
		SELECT cuc.PREPRunID
				,ISNULL(pm.Region, 'UNMAPPED') AS Region
				,cuc.GrouperID
				,ISNULL(gu.[Provider], 'FFS') AS 'Provider'
				,COUNT(*) AS GrprRecCnt
			FROM dbo.W_FEM_SavedCUClaims_DFSync cuc WITH (NOLOCK)
		--LEFT
			INNER JOIN dbo.W_FEM_PlanMapping pm
				ON cuc.ContractNumber = pm.ContractNumber
				AND cuc.PlanID = pm.PlanID
				AND cuc.SegmentID = pm.SegmentID
				AND pm.Region = @Rgn
			LEFT JOIN dbo.FEM_GrouperUpload_Direct gu WITH (NOLOCK)
				ON cuc.GrouperID = gu.GrouperID
				AND gu.Region = @Rgn
			WHERE gu.[Provider] IS NULL -- OR gu.[Provider] = 'FFS'
			GROUP BY cuc.PREPRunID
					,pm.Region
					,cuc.GrouperID
					,ISNULL(gu.[Provider], 'FFS')
		) A
		ORDER BY A.Region
				,A.[Provider]
				,A.GrouperID

		/*** END Get Exapnded FFS/Deemed Non-Risk Groupers wrt to "New" GrouperUpload_Direct ***/


		/*************************************************************************************************/
		/*** Section Below Determines What Should be Recompiled following Grouper Upload Changes *********/
		/*** This relies on a complete FFS mapping before and after from the work completed above ********/
		/*************************************************************************************************/

		/* START - "Old" Providers w/Grouper mapping that doesn't match the new Grouper Upload
		  Later, there will be a corresponding set of "New" Providers w/Grouper mapping that doesn't match the old Grouper Upload 
		  Both use the expanded mapping versions that include FFS or set of groupers in the base data that are not mapped for a region
		  The approach is to 
		==> Delete "old" Prov with all its groupers
		==> Compile "new" Prov with all its groupers
		*/

		IF (SELECT OBJECT_ID('tempdb..#DelOldProvList')) IS NOT NULL            
			BEGIN
			  DROP TABLE #DelOldProvList
			END;          

		CREATE TABLE #DelOldProvList (
			[Region] VARCHAR(40) NOT NULL
			,[Provider] VARCHAR(255) NOT NULL
			,oldGrpr VARCHAR(10) NULL --NOT NULL
			) 

		INSERT INTO #DelOldProvList
		(
			Region,
			[Provider],
			oldGrpr
		)
		/* "Old" Providers */
		SELECT DISTINCT	
				oldmap.Region
				,oldmap.[Provider]
				,oldmap.[GrouperID] AS oldGrpr
		FROM
			(
			SELECT Region
					,[Provider]
					,GrouperID
				FROM #ExpandedGUOld
			) oldmap
			LEFT JOIN
			(
			SELECT Region
					,[Provider]
					,GrouperID
				FROM #ExpandedGUNew
			) newmap
		ON oldmap.Region = newmap.Region
			AND oldmap.[Provider] = newmap.[Provider]
			AND oldmap.[GrouperID] = newmap.[GrouperID]
		WHERE newmap.[GrouperID] IS NULL
--BK #3 2/8/23 - Ensure that all old FFS is deleted if there is a call for new FFS in the new to be recompiled
		UNION
		SELECT DISTINCT
		       Region
              ,[Provider]
              ,@DefaultFFSGrouperID AS GrouperID
				FROM #ExpandedGUNew
           WHERE [Provider] = 'FFS'


/*********************************************************************/
--#2 START: BK 1/31/23 Try to make things more straight forward 

		/* START - Corresponding "New" Providers w/Grouper mapping that doesn't match the old Grouper Upload 
		  As with the "Old" set or Providers for Deletion, this is based on the expanded mapping versions that include FFS/Groupers in the base data that are not mapped for a region
		  Again, the approach is to 
		==> Delete "old" Prov in Provider Level base data that had been based on all its attached groupers
		==> Compile "new" Prov in Provider Level base data based on all its attached groupers
		*/
		IF (SELECT OBJECT_ID('tempdb..#AddNewProvList')) IS NOT NULL            
			BEGIN
			  DROP TABLE #AddNewProvList
			END;          

		CREATE TABLE #AddNewProvList (
			[Region] VARCHAR(40) NOT NULL
			,[Provider] VARCHAR(255) NOT NULL
			,ProvGrprID VARCHAR(10) NULL
			) 

		INSERT INTO #AddNewProvList
		(
			Region
		   ,[Provider]
		   ,ProvGrprID
		)
		/* Set of "New" Providers to be added AFTER deleting the set of "Old" Providers */
		SELECT DISTINCT	
				newmap.Region
				,newmap.[Provider]
				,NULL AS ProvGrprID
		FROM
(
			SELECT Region
					,[Provider]
					,GrouperID
				FROM #ExpandedGUNew
			) newmap
			LEFT JOIN
			(
			SELECT Region
					,[Provider]
					,GrouperID
				FROM #ExpandedGUOld
			) oldmap
		ON newmap.Region = oldmap.Region
			AND newmap.[Provider] = oldmap.[Provider]
			AND newmap.[GrouperID] = oldmap.[GrouperID]
		WHERE oldmap.[GrouperID] IS NULL
--BK #3 2/8/23 - Ensure that all new FFS is recompiled if there is any change affecting old FFS
		UNION
		SELECT DISTINCT
		       Region
              ,[Provider]
              ,NULL AS GrouperID
				FROM #ExpandedGUOld
           WHERE [Provider] = 'FFS'
--BK #4 2/22/23 - Ensure that add rows to new for recompile record set for any instance of Provider identified for deletion 
--                   only because it has extra groupers in the old set relative to the same Provider in the new set
		UNION
		SELECT DISTINCT
		       do2n.Region
              ,do2n.[Provider]
              ,NULL AS GrouperID
				FROM ( SELECT DISTINCT
				              xn.Region
				             ,xn.[Provider]
				         FROM #DelOldProvList do
						   INNER JOIN #ExpandedGUNew xn
						     ON do.[Provider] = xn.[Provider]
                         WHERE do.[Provider] <> 'FFS'
					  ) do2n


--BK #4 2/22/23 - Ensure that delete old provider record set contains any instance of Provider NOT identified for deletion 
--                   only because its new counterpart has extra groupers in the new set relative to the same Provider in the old set

		INSERT INTO #DelOldProvList
		(
			Region,
			[Provider],
			oldGrpr
		)
		SELECT DISTINCT
		       an2o.Region
              ,an2o.[Provider]
              ,NULL AS GrouperID
				FROM #AddNewProvList an2o
				WHERE NOT EXISTS (SELECT [Provider]
				                         FROM #DelOldProvList
							             WHERE [Provider] <> 'FFS'
							      )


--For any "new" provider, and before deleting "old" providers, determine if there already exists a manufactured Provider "GrouperID"; 
--   this may come from another region but should be kept for [FEM_GrouperUpload]
--Attach any existing manufactured Provider-Grouper IDs
        UPDATE anpl
          SET anpl.ProvGrprID = pid.GrouperID
          FROM #AddNewProvList anpl
            INNER JOIN (SELECT DISTINCT
                               gu.[Provider]
                              ,gu.GrouperID
        	              FROM dbo.FEM_GrouperUpload gu WITH (NOLOCK)
        			    ) pid
               ON anpl.[Provider] = pid.[Provider]
        
        
--DELETE "old" Provider set for Region from Provider Level GrouperUpload
		DELETE gu
			FROM dbo.FEM_GrouperUpload gu
			INNER JOIN (   SELECT DISTINCT
							      dopl.Region,
								  dopl.[Provider]
							FROM #DelOldProvList dopl
						) delpro
				ON gu.Region = delpro.Region
				  AND gu.[Provider] = delpro.[Provider]
			WHERE gu.Region = @Rgn


--Now INSERT any "new" Provider that really already existed
        INSERT INTO dbo.FEM_GrouperUpload
        (
            Region,
            Provider,
            GrouperID,
            UserID,
            TimeStamp
        )
        SELECT anpl.Region,
               anpl.Provider,
               anpl.ProvGrprID,
        	  'PREP' AS [UserID],
        	   GETDATE() AS [TimeStamp]
          FROM #AddNewProvList anpl
          WHERE anpl.ProvGrprID IS NOT NULL


--Now prepare to Insert any truly "new" Providers
		IF (SELECT OBJECT_ID('tempdb..#TrueNewProv')) IS NOT NULL            
			BEGIN
			  DROP TABLE #TrueNewProv
			END;          
       
		DECLARE @MaxProvGrprID VARCHAR(10)

		SELECT @MaxProvGrprID = MAX([GrouperID]) FROM dbo.FEM_GrouperUpload

		--SELECT @MaxProvGrprID AS [MaxProvGrprID]


		-- Assign new Provider GrouperIDs for True "New" Providers needing to be compiled
		SELECT unpr.[Provider]
				,RIGHT(@DefaultFFSGrouperID + REPLACE(CAST( @MaxProvGrprID + ROW_NUMBER() OVER(ORDER BY unpr.[Provider] ASC) AS CHAR(8)), ' ', ''), 8) AS [ProvGrprID]
			INTO #TrueNewProv
			FROM
				( SELECT DISTINCT
						tnp.[Provider]
					FROM #AddNewProvList tnp
					WHERE tnp.ProvGrprID IS NULL
						AND tnp.[Provider] <> 'FFS'
				 ) unpr --Unique New Providers <> 'FFS' to be compiled

       -- Insert True "new" Providers into Provider Level Grouper Upload
			INSERT INTO dbo.FEM_GrouperUpload
				(
					Region,
					Provider,
					GrouperID,
					UserID,
					TimeStamp
				)
				SELECT DISTINCT
						@Rgn AS Region
						,tnp.[Provider]
       					,tnp.ProvGrprID
						,'PREP' AS [UserID]
						,GETDATE() AS [TimeStamp]
					FROM #TrueNewProv tnp
	               

     --Release temp table
		IF (SELECT OBJECT_ID('tempdb..#TrueNewProv')) IS NOT NULL            
			BEGIN
			  DROP TABLE #TrueNewProv
			END;          


--Delete "old" Provider records from W_FEM_SavedCUFinance before recompile later to Provider Level based on "new" Providers and their Grouper Mapping
		DELETE cuf
			FROM dbo.W_FEM_SavedCUFinance cuf 
			INNER JOIN ( SELECT DISTINCT 
								dopl.Region,
								dopl.[Provider]
							FROM #DelOldProvList dopl
                        ) doplx  -- deleting all "old" providers, i.e., with mismatch to "new" grouper upload
									-- will later be inserting complete set of "new" providers needing recompile
				ON cuf.GrouperName = doplx.[Provider]
			INNER JOIN dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
				ON cuf.ContractNumber = pm.ContractNumber
				AND cuf.PlanID = pm.PlanID
				AND cuf.SegmentID = pm.SegmentID
				AND pm.Region = @Rgn

--Delete "old" Provider records from W_FEM_SavedCUClaims before recompile later to Provider Level based on "new" Providers and their Grouper Mapping
		DELETE cuc
			FROM dbo.W_FEM_SavedCUClaims cuc 
			INNER JOIN ( SELECT DISTINCT 
								dopl.Region,
								dopl.[Provider]
							FROM #DelOldProvList dopl
                        ) doplx  -- deleting all "old" providers, i.e., with mismatch to "new" grouper upload
									-- will later be inserting complete set of "new" providers needing recompile
				ON cuc.GrouperName = doplx.[Provider]
			INNER JOIN dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
				ON cuc.ContractNumber = pm.ContractNumber
				AND cuc.PlanID = pm.PlanID
				AND cuc.SegmentID = pm.SegmentID
				AND pm.Region = @Rgn

    --Release temp table
		IF (SELECT OBJECT_ID('tempdb..#DelOldProvList')) IS NOT NULL            
			BEGIN
			  DROP TABLE #DelOldProvList
			END;          

  
--Recompile W_FEM_SavedCUFinance to Provider Level based on "new" Providers and their Grouper Mapping
		INSERT INTO dbo.W_FEM_SavedCUFinance
		(
			PREPRunID,
			Year,
			CPS,
			ContractNumber,
			PlanID,
			SegmentID,
			SSStateCountyCD,
			GrouperID,
			GrouperName,
			IsESRD,
			IsHospice,
			MM,
			RiskScore,
			MAPremium,
			PDPremium,
			RxPaid,
			RxMbrCS,
			OPB,
			PCPCap,
			SpecCap,
			OtherCap,
			Quality,
			OPB_InBid,
			OPB_NotInBid
		)
		SELECT cuf.PREPRunID, 
				cuf.[Year],
				cuf.CPS,
				cuf.ContractNumber,
				cuf.PlanID,
				cuf.SegmentID,
				cuf.SSStateCountyCD,
				ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID) AS GrouperID,
				ISNULL(pgm.[Provider], 'FFS') AS GrouperName,
				cuf.IsESRD,
				cuf.IsHospice,
				SUM(cuf.MM) AS MM,
				SUM(cuf.RiskScore) AS RiskScore,
				SUM(cuf.MAPremium) AS MAPremium,
				SUM(cuf.PDPremium) AS PDPremium,
				SUM(cuf.RxPaid) AS RxPaid,
				SUM(cuf.RxMbrCS) AS RxMbrCS,
				SUM(cuf.OPB) AS OPB,
				SUM(cuf.PCPCap) AS PCPCap,
				SUM(cuf.SpecCap) AS SpecCap,
				SUM(cuf.OtherCap) AS OtherCap,
				SUM(cuf.Quality) AS Quality,
				SUM(cuf.OPB_InBid) AS OPB_InBid,
				SUM(cuf.OPB_NotInBid) AS OPB_NotInBid
			FROM dbo.W_FEM_SavedCUFinance_DFSync cuf WITH (NOLOCK)
			INNER JOIN (SELECT (pm.ContractNumber + '-' + pm.PlanID + '-' + pm.SegmentID) AS CPS
								,pm.Region
							FROM dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
							WHERE pm.Region = @Rgn
						) pm2
				ON cuf.CPS = pm2.CPS
			INNER JOIN ( SELECT DISTINCT
			                    anpl.Region,
								anpl.[Provider],
								gud.GrouperID,
								gu.GrouperID AS ProvGrprID
							FROM #AddNewProvList anpl
							INNER JOIN dbo.FEM_GrouperUpload gu WITH (NOLOCK)
								ON anpl.Region = gu.Region
								  AND anpl.[Provider] = gu.[Provider]
--BK #3 2/9/23 - Need to use Expanded Grouper to Provider Mapping that includes full FFS, rather than straight [FEM_GrouperUpload_Direct]
							INNER JOIN #ExpandedGUNew gud --Get groupers associated with "New" providers, including any for FFS recompile
							    ON anpl.Region = gud.Region
								  AND anpl.[Provider] = gud.[Provider] 
						) pgm
				ON cuf.GrouperID = pgm.GrouperID
					AND pm2.Region = pgm.Region
			GROUP BY cuf.PREPRunID,
				cuf.[Year],
				cuf.CPS,
				cuf.ContractNumber,
				cuf.PlanID,
				cuf.SegmentID,
				cuf.SSStateCountyCD,
				ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID),
				ISNULL(pgm.[Provider], 'FFS'),
				cuf.IsESRD,
				cuf.IsHospice
		/* Warning: Null value is eliminated by an aggregate or other SET operation.*/


--Recompile W_FEM_SavedCUClaims to Provider Level based on "new" Providers and their Grouper Mapping
		INSERT INTO dbo.W_FEM_SavedCUClaims
		(
			PREPRunID,
			Year,
			CPS,
			ContractNumber,
			PlanID,
			SegmentID,
			GrouperID,
			GrouperName,
			IsESRD,
			IsHospice,
			BenefitCategoryID,
			CPaid,
			CMbrCS,
			DEPaid,
			DEMbrCS,
			EPaid,
			EMbrCS,
			PRA,
			OPB_InBid
		)
		SELECT cuc.PREPRunID,
				cuc.[Year],
				cuc.CPS,
				cuc.ContractNumber,
				cuc.PlanID,
				cuc.SegmentID,
				ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID) AS GrouperID,
				ISNULL(pgm.[Provider], 'FFS') AS GrouperName,
				cuc.IsESRD,
				cuc.IsHospice,
				cuc.BenefitCategoryID,
				SUM(cuc.CPaid) AS CPaid,
				SUM(cuc.CMbrCS) AS CMbrCS,
				SUM(cuc.DEPaid) AS DEPaid,
				SUM(cuc.DEMbrCS) AS DEMbrCS,
				SUM(cuc.EPaid) AS EPaid,
				SUM(cuc.EMbrCS) AS EMbrCS,
				SUM(cuc.PRA) AS PRA,
				SUM(cuc.OPB_InBid) AS OPB_InBid
			FROM dbo.W_FEM_SavedCUClaims_DFSync cuc WITH (NOLOCK)
			INNER JOIN (SELECT (pm.ContractNumber + '-' + pm.PlanID + '-' + pm.SegmentID) AS CPS
								,pm.Region
							FROM dbo.W_FEM_PlanMapping pm WITH (NOLOCK)
							WHERE pm.Region = @Rgn
						) pm2
				ON cuc.CPS = pm2.CPS
			INNER JOIN ( SELECT DISTINCT
			                    anpl.Region,
								anpl.[Provider],
								gud.GrouperID,
								gu.GrouperID AS ProvGrprID
							FROM #AddNewProvList anpl
							INNER JOIN dbo.FEM_GrouperUpload gu WITH (NOLOCK)
								ON anpl.Region = gu.Region
								  AND anpl.[Provider] = gu.[Provider]
--BK #3 2/9/23 - Need to use Expanded Grouper to Provider Mapping that includes full FFS, rather than straight [FEM_GrouperUpload_Direct]
							INNER JOIN #ExpandedGUNew gud --Get groupers associated with "New" providers, including any for FFS recompile
							    ON anpl.Region = gud.Region
								  AND anpl.[Provider] = gud.[Provider] 
						) pgm
				ON cuc.GrouperID = pgm.GrouperID
					AND pm2.Region = pgm.Region
			GROUP BY cuc.PREPRunID,
				cuc.[Year],
				cuc.CPS,
				cuc.ContractNumber,
				cuc.PlanID,
				cuc.SegmentID,
				ISNULL(pgm.ProvGrprID, @DefaultFFSGrouperID),
				ISNULL(pgm.[Provider], 'FFS'),
				cuc.IsESRD,
				cuc.IsHospice,
				cuc.BenefitCategoryID

--Clean up; release temp tables
		IF (SELECT OBJECT_ID('tempdb..#ExpandedGUOld')) IS NOT NULL            
			BEGIN
			DROP TABLE #ExpandedGUOld
			END;          

		IF (SELECT OBJECT_ID('tempdb..#ExpandedGUNew')) IS NOT NULL            
			BEGIN
			  DROP TABLE #ExpandedGUNew
			END;          

		IF (SELECT OBJECT_ID('tempdb..#AddNewProvList')) IS NOT NULL            
			BEGIN
			  DROP TABLE #AddNewProvList
			END;          

    END;  -- Recompile based on changes in mapping


END;  -- PROC