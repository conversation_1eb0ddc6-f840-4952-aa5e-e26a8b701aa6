SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- PROCEDURE NAME: spDashboardReportErrorCountPBI2

-- DESCRIPTION: This SP returns extract for "errors extract" user actions from AppLogs table
--
-- PARAMETERS:
--    Input:
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      dbo.DashboardReportAppLogs
--
-- Example
-- Exec [dbo].[spDashboardReportErrorCountPBI2] 0, '2023-09-25 10:26:27.367'

-- HISTORY
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Jun-06			1			Initial Version                                                                         Chaitanya
-- 2023-Sep-19			2			Updated details for BPT Creation MAAUI Error Count                                      Chaitanya Durga
-- 2023-Feb-28			3			Updated Message Column Logic - Target Member Premium									Sheetal/Deepali
-- 2024-SEP-12			4			Modified spAppGetFilterDataReporting to be spAppGetFilterData							Alex Brandt
---------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[spDashboardReportErrorCountPBI2]
 (@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME)


AS
BEGIN

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;


 DECLARE @XLastSuccessfullRunLogid  INT = @LastSuccessfullRunLogid


;WITH ConstantsValues
AS
(
SELECT    'getBTWDataforPOC' AS getBTWDataforPOC,
'spBatchTargetInitialFormPopulation' AS spBatchTargetInitialFormPopulation,
'spTargetPremiumReprice' AS spTargetPremiumReprice,
'spUpdateOrInsertExpenseProfit' AS spUpdateOrInsertExpenseProfit,
'ImportSalesMembership' AS ImportSalesMembership,
'spUploadSalesMembership' AS spUploadSalesMembership,
'ImportMsbUpload' AS ImportMsbUpload,
'ImportActuarialAdj_BenefitCategory' AS ImportActuarialAdj_BenefitCategory,
'spAppTrendUploadActuarialAdjBenefitCategory' AS spAppTrendUploadActuarialAdjBenefitCategory,
'ImportActuarialAdj_ReportingCategory' AS ImportActuarialAdj_ReportingCategory,
'ActAdjRepCat' AS ActAdjRepCat,
'spAppTrendUploadActuarialAdjReportingCategory' AS spAppTrendUploadActuarialAdjReportingCategory,
'spAppGetFilterData' AS spAppGetFilterData,
'BatchUploadForMsb' AS BatchUploadForMsb,
'spAppGetBatchImportMemberMonthsWithPlanAdminBlend' AS spAppGetBatchImportMemberMonthsWithPlanAdminBlend,
'Execution Timeout Expired' AS ExecutionTimeoutExpired,
'deadlock' AS deadlock,
'TrendUpdate' AS TrendUpdate,
'TrendController'AS TrendController,
'UpdateTrend' AS UpdateTrend,
'Trend_ProjProcess_spUpdateAllTrends' AS Trend_ProjProcess_spUpdateAllTrends,
'spAppTrendGetForecastingSelectedPlan' AS spAppTrendGetForecastingSelectedPlan,
'UpdateTargetMemberPremium' AS UpdateTargetMemberPremium,
'BTWUpdateTargets' AS BTWUpdateTargets,
'GetAllBTWData' AS GetAllBTWData,
'ProcedurespAppDeleteMER' AS ProcedurespAppDeleteMER,
'spAppUpdateTargetProfitPercentage' AS spAppUpdateTargetProfitPercentage,
'spAppUpdateSavedTargetInputs' AS spAppUpdateSavedTargetInputs,
'spTargetActAdjForMER' AS spTargetActAdjForMER,
'spTargetActAdj' AS spTargetActAdj,
'spCalcFinalPremium' AS spCalcFinalPremium,
'spUploadRiskFactorBulkUpload' AS spUploadRiskFactorBulkUpload,
'spUploadMemberMonthsWithoutError' AS spUploadMemberMonthsWithoutError,
'spCalcPlanAdminBlendBulkImport' AS spCalcPlanAdminBlendBulkImport,
'ImportBatchBarCmmAndRsData' AS ImportBatchBarCmmAndRsData,
'BatchimportBarCmmAndRsMemberMonths' AS BatchimportBarCmmAndRsMemberMonths,
'BatchimportBarCmmAndRsRiskScores' AS BatchimportBarCmmAndRsRiskScores,
'BatchUploadForSalesMembership' AS BatchUploadForSalesMembership,
'ImportSalesMembershipUpload' AS ImportSalesMembershipUpload,
'ActAdjBenCat' AS ActAdjBenCat,
'ImportBatchTabData' AS ImportBatchTabData,
'spAppTrendGetAvailableAdjustmentID' AS spAppTrendGetAvailableAdjustmentID,
'ProcessResults' AS ProcessResults,
'ResultsMmandRs' AS ResultsMmandRs,
'spAppUpdateTargetMemberPremium' AS spAppUpdateTargetMemberPremium,
'UpdateTargetMER' AS UpdateTargetMER,
'spAppUpdateTargetMER' AS spAppUpdateTargetMER,
'spDeleteMSB' AS spDeleteMSB,
'spUploadMSB' AS spUploadMSB,
'ResultsMmandRscores' AS ResultsMmandRscores,
'ImportBarCmmAndRsMemberMonthsDto' AS ImportBarCmmAndRsMemberMonthsDto,
'ImportBarCmmAndRsRiskScoresDto' AS ImportBarCmmAndRsRiskScoresDto,
'Trend_ProjProcess_spCalcNotPlanLevel_Trends' AS Trend_ProjProcess_spCalcNotPlanLevel_Trends,
'Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault' AS Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault,
'Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides' AS Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides,
'Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal' AS Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal,
'Trend_ProjProcess_spCalcPlanTrends_RepCat' AS Trend_ProjProcess_spCalcPlanTrends_RepCat,
'Trend_ProjProcess_spCalcPlanTrends_BenCat' AS Trend_ProjProcess_spCalcPlanTrends_BenCat,
'Trend_ProjProcess_spCalcPlanTrendsFinal' AS Trend_ProjProcess_spCalcPlanTrendsFinal,
'spUpdatePricingTrends' AS spUpdatePricingTrends,
'SyncToMaauiTpf' AS SyncToMaauiTpf,
'Audit/SyncToMaauiTpf' AS AuditSyncToMaauiTpf,
'spMACTAPTUpload' AS spMACTAPTUpload,
'spMarketAdjUpdate' AS spMarketAdjUpdate,
'spRiskScoreUploadBARC_MAAUI' AS spRiskScoreUploadBARC_MAAUI,
'SyncMaauiTpf' AS SyncMaauiTpf,
'spAppSyncBARCToTPFDataAudit' AS spAppSyncBARCToTPFDataAudit,
'SyncToBARC' AS SyncToBARC,
'spAppGetModelSettings' AS spAppGetModelSettings,
'spAppSyncBARCDataAudit' AS spAppSyncBARCDataAudit,
'ActAdjRepRS' AS ActAdjRepRS,
'ImportBatchData' AS ImportBatchData,
'ActAdjSales' AS ActAdjSales,
'ActAdjMSB' AS ActAdjMSB,
'ActuarialGenerateBpt' AS ActuarialGenerateBpt,
'GenarateBptExcel' AS GenarateBptExcel,
'GenarateBptExcelInternal' AS GenarateBptExcelInternal,
'PopulateBptTemplate' AS PopulateBptTemplate,
'GetMabtCreation' AS GetMabtCreation,
'CreateBptWorkbook' AS CreateBptWorkbook,
'ActuarialGenerateBptBatch' AS ActuarialGenerateBptBatch,
'CreateZipFile' AS CreateZipFile,
'GetFileBytesAndDeleteTheFile' AS GetFileBytesAndDeleteTheFile,
'spAppGetMABPTCreation' AS spAppGetMABPTCreation,
'Error while fetching BPT data' AS ErrorfetchingBPTdata,
'Error while createting BPT' AS ErrorcreatetingBPT,
'Failed to Create BPT' AS FailedtoCreateBPT,
'BPTTemplates' AS BPTTemplates,
'RepricePlan' AS RepricePlan,
'RePricePlans' AS RePricePlans,
'GetPlansToBeRepriced' AS GetPlansToBeRepriced,
'spPlanRefresh' AS spPlanRefresh,
'GetAdminData' AS GetAdminData,
'spAppGetAdminAllocation' AS spAppGetAdminAllocation,
'GetRebateMemberPremiumData' AS GetRebateMemberPremiumData,
'GetRebateAllocation' AS GetRebateAllocation,
'spAppGetScenarioAttributesData' AS spAppGetScenarioAttributesData,
'GetRevenueData' AS GetRevenueData,
'spAppGetBidSummary' AS spAppGetBidSummary,
'GetMarketBidSummary' AS GetMarketBidSummary,
'spAppGetMarketBidSummaryMARebateAllocation' AS spAppGetMarketBidSummaryMARebateAllocation,
'GetRebateSpentAmt' AS GetRebateSpentAmt,
'GetUnspent' AS GetUnspent,
'GetCostShareData' AS GetCostShareData,
'GetBidSummaryFactorsForAll' AS GetBidSummaryFactorsForAll,
'spAppGetBidSummaryFactorsForAll' AS spAppGetBidSummaryFactorsForAll,
'GetReaderData' AS GetReaderData,
'GetBidSummary' AS GetBidSummary,
'GetAddedBenNet' AS GetAddedBenNet,
'spAppGetAddedBenefitNet' AS spAppGetAddedBenefitNet
)

SELECT
[ActionName] 'UserAction',
[user] UserID, startdate 'Run Date',
NULL AS Plans,
0 AS 'Plan Count',
ErrorCount,
maxenddate RunEndDate,
exectime AS ExecutionTime,
CASE WHEN [Message] LIKE '%deadlock%' THEN 'Deadlock'
 WHEN [Message] LIKE '%Execution Timeout Expired%' THEN 'Timeout'
 ELSE 'Other'
 END AS [ErrorMessage]

FROM
(
-- 1. Update Trend
SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
 ((SELECT 'Update Trend' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((([Exception] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%' OR [Exception] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%'
 OR [Exception] LIKE '%'+ConstantsValues.UpdateTrend+'%'
 OR [Exception] LIKE '%'+ConstantsValues.TrendUpdate+'%' OR [Exception] LIKE '%'+ConstantsValues.TrendController+'%'
		OR [Message] LIKE '%'+ConstantsValues.TrendUpdate+'%'  OR [Message] LIKE '%'+ConstantsValues.TrendController+'%'
		OR [Message] LIKE '%'+ConstantsValues.UpdateTrend+'%'
		OR [Message] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%'
		OR [Message] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%' OR
		[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%' OR [Source] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%'
		OR [Source] LIKE '%'+ConstantsValues.TrendUpdate+'%' OR
		[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_Trends+'%'
		OR  [Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault+'%' OR
        [Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides+'%'
		OR [Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal+'%' OR
		[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrends_RepCat+'%'
		OR [Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrends_BenCat+'%' OR
		[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrendsFinal+'%' OR [Source] LIKE '%'+ConstantsValues.spUpdatePricingTrends+'%'
		)AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
UNION ALL
		(SELECT 'Update Trend' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK)
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK)
				WHERE t.LogID < app.logid
				AND (app.[Exception] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%'
				OR app.[Exception] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%'
				OR app.[Exception] LIKE '%'+ConstantsValues.UpdateTrend+'%'
 OR app.[Exception] LIKE '%'+ConstantsValues.TrendUpdate+'%' OR app.[Exception] LIKE '%'+ConstantsValues.TrendController+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.TrendUpdate+'%'  OR app.[Message] LIKE '%'+ConstantsValues.TrendController+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.UpdateTrend+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.TrendUpdate+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_Trends+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault+'%' OR
        app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrends_RepCat+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrends_BenCat+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrendsFinal+'%' OR app.[Source] LIKE '%'+ConstantsValues.spUpdatePricingTrends+'%')
		AND t.[User] = app.[User])
				AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND (ap.[Exception] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%'
				OR ap.[Exception] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%'
				OR ap.[Exception] LIKE '%'+ConstantsValues.UpdateTrend+'%'
 OR ap.[Exception] LIKE '%'+ConstantsValues.TrendUpdate+'%'OR ap.[Exception] LIKE '%'+ConstantsValues.TrendController+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.TrendUpdate+'%'   OR ap.[Message] LIKE '%'+ConstantsValues.TrendController+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.UpdateTrend+'%'
		  OR ap.[Message] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%'
		  OR ap.[Message] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spUpdateAllTrends+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spAppTrendGetForecastingSelectedPlan+'%' OR ap.[Source] LIKE '%'+ConstantsValues.TrendUpdate+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_Trends+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault+'%' OR
        ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrends_RepCat+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrends_BenCat+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.Trend_ProjProcess_spCalcPlanTrendsFinal+'%' OR ap.[Source] LIKE '%'+ConstantsValues.spUpdatePricingTrends+'%'
		)
				WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
				OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
				)
				AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])

		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate),A.[Message]
		UNION ALL
	-- 3. Target Member Premium
	SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,c.startdate,c.maxenddate) AS exectime,c.[Message]
FROM(
 SELECT CASE WHEN B.ExceptionPlan LIKE '%"btwType":"1"%' THEN 'Target Profit %'
 WHEN B.ExceptionPlan LIKE '%"btwType":"2"%' THEN 'Target MER'
  WHEN B.ExceptionPlan LIKE '%"btwType":"3"%' THEN 'Target Member Premium' END AS [ActionName],
  [user],startdate,logid,ErrorCount,maxenddate,B.[Message]
FROM
(
 SELECT [user],startdate,logid,a.ErrorCount,a.maxenddate,
  ExceptionPlan=(SELECT TOP 1 [Message] FROM dbo.DashboardReportAppLogs WITH (NOLOCK)
  CROSS APPLY ConstantsValues WHERE (([Message] LIKE '%Executing POST%' AND [Message] NOT LIKE '%Finished executing POST%') AND  [Message] LIKE '%' + ConstantsValues.getBTWDataforPOC + '%'
  AND LogID<a.logid) ORDER BY logid DESC)
  ,a.[Message]
 FROM
 ((SELECT [user],[t].[Date] startdate,[logid],
 COUNT([logid])  AS ErrorCount,
 [t].[Date] AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
CROSS APPLY ConstantsValues
 WHERE ((( [Exception] LIKE '%'+ConstantsValues.UpdateTargetMemberPremium+'%' OR [Exception] LIKE '%'+ConstantsValues.BTWUpdateTargets+'%' OR
		[Exception] LIKE '%' + ConstantsValues.getBTWDataforPOC + '%' OR 	[Exception] LIKE '%'+ConstantsValues.GetAllBTWData+'%'
		OR	[Exception] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR [Exception] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		[Exception] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR [Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%' OR
			[Source] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR [Source] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		[Source] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR [Source] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%'
		OR [Message] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR [Message] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		[Message] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR [Message] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%'
		OR [Source] LIKE '%'+ ConstantsValues.spTargetPremiumReprice+'%'  OR [Source] LIKE '%'+ConstantsValues.spAppUpdateSavedTargetInputs+'%'
		OR [Source] LIKE '%'+ConstantsValues.spTargetActAdjForMER+'%'
		OR [Source] LIKE '%'+ConstantsValues.spTargetActAdj+'%' OR [Source] LIKE '%'+ConstantsValues.spCalcFinalPremium+'%'
		OR [Source] LIKE '%'+ConstantsValues.spUpdateOrInsertExpenseProfit+'%') AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],[logid],t.[Message])
		UNION ALL
		 (SELECT t.[user],[t].[Date] startdate,t.[logid],
  COUNT(t.[logid])  AS ErrorCount,
 [t].[Date] AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK)  CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK)
				CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND ( app.[Exception] LIKE '%'+ConstantsValues.UpdateTargetMemberPremium+'%' OR app.[Exception] LIKE '%'+ConstantsValues.BTWUpdateTargets+'%' OR
				app.[Message] LIKE '%'+ConstantsValues.BTWUpdateTargets+'%' OR app.[Message] LIKE '%' + ConstantsValues.getBTWDataforPOC + '%' OR
		app.[Exception] LIKE '%' + ConstantsValues.getBTWDataforPOC + '%' OR 	app.[Exception] LIKE '%'+ConstantsValues.GetAllBTWData+'%'
		OR	app.[Exception] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR app.[Exception] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	app.[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR app.[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%' OR
			app.[Source] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		app.[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR app.[Source] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	app.[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		app.[Source] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR app.[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%'
		OR app.[Message] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		app.[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR app.[Message] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	app.[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		app.[Message] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR app.[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%'
		OR app.[Source] LIKE '%'+ ConstantsValues.spTargetPremiumReprice+'%'  OR app.[Source] LIKE '%'+ConstantsValues.spAppUpdateSavedTargetInputs+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spTargetActAdjForMER+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spTargetActAdj+'%' OR app.[Source] LIKE '%'+ConstantsValues.spCalcFinalPremium+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spUpdateOrInsertExpenseProfit+'%') AND t.[User] = app.[User])
				AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND (ap.[Exception] LIKE '%'+ConstantsValues.UpdateTargetMemberPremium+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.BTWUpdateTargets+'%' OR
				ap.[Message] LIKE '%'+ConstantsValues.BTWUpdateTargets+'%' OR ap.[Message] LIKE '%' + ConstantsValues.getBTWDataforPOC + '%' OR
		ap.[Exception] LIKE '%' + ConstantsValues.getBTWDataforPOC + '%' OR 	ap.[Exception] LIKE '%'+ConstantsValues.GetAllBTWData+'%'
		OR	ap.[Exception] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	ap.[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		ap.[Exception] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%' OR
			ap.[Source] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR ap.[Source] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	ap.[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		ap.[Source] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR ap.[Source] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%'
		OR ap.[Message] LIKE '%'+ ConstantsValues.spBatchTargetInitialFormPopulation +'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetMemberPremium+'%' OR ap.[Message] LIKE '%'+ConstantsValues.UpdateTargetMER+'%'
		OR	ap.[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetMER+'%'  OR
		ap.[Message] LIKE '%'+ConstantsValues.ProcedurespAppDeleteMER+'%' OR ap.[Message] LIKE '%'+ConstantsValues.spAppUpdateTargetProfitPercentage+'%'
		OR ap.[Source] LIKE '%'+ ConstantsValues.spTargetPremiumReprice+'%'  OR ap.[Source] LIKE '%'+ConstantsValues.spAppUpdateSavedTargetInputs+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spTargetActAdjForMER+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spTargetActAdj+'%' OR ap.[Source] LIKE '%'+ConstantsValues.spCalcFinalPremium+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spUpdateOrInsertExpenseProfit+'%')
		WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
		OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
		)

	 AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[logid],t.[Message])

		)  AS A) AS B) AS C GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,c.startdate,c.maxenddate),c.[Message]

	UNION ALL
	-- 4. Sync Service Area to BARC
	SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
((SELECT 'Sync Service Area to BARC' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid])  AS ErrorCount,
[t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((([Exception] LIKE '%'+ConstantsValues.SyncToBARC+'%'
 OR 	[Exception] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		[Exception] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR [Message] LIKE '%'+ConstantsValues.SyncToBARC+'%'
		OR 	[Message] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		[Message] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR [Source] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		[Source] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR [Source] LIKE '%'+ConstantsValues.spAppSyncBARCDataAudit+'%'
		)AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
		UNION ALL

		(SELECT 'Sync Service Area to BARC' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
[t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK) CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK)
				CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND (app.[Exception] LIKE '%'+ConstantsValues.SyncToBARC+'%'
				OR 	app.[Exception] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.SyncToBARC+'%'
		OR 	app.[Message] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR	app.[Source] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR	app.[Source] LIKE '%'+ConstantsValues.spAppSyncBARCDataAudit+'%'
		) AND t.[User] = app.[User])
				AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND (ap.[Exception] LIKE '%'+ConstantsValues.SyncToBARC+'%'
				OR 	ap.[Exception] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
				OR ap.[Message] LIKE '%'+ConstantsValues.SyncToBARC+'%'
		OR 	ap.[Message] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR	ap.[Source] LIKE '%'+ConstantsValues.spAppGetModelSettings+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.spAppGetFilterData+'%'
		OR	ap.[Source] LIKE '%'+ConstantsValues.spAppSyncBARCDataAudit+'%'
		)
				WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
				OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
				)

				AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])

		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) ,A.[Message]

		UNION ALL
			-- 5. Sync Membership & Market Adjustment to MAAUI
		SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
 ((SELECT 'Sync Membership & Market Adjustment to MAAUI' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid]) AS ErrorCount,
  [t].[Date] AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((([Exception] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR [Exception] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR [Exception] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	[Exception] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		[Exception] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR  [Exception] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%' OR
		[Message] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR [Message] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR [Message] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	[Message] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		[Message] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR  [Message] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%' OR
		[Source] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR [Source] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR [Source] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	[Source] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		[Source] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR [Source] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%'
		OR [Source] LIKE '%'+ConstantsValues.spAppSyncBARCToTPFDataAudit+'%')
		AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
		UNION ALL
		(SELECT 'Sync Membership & Market Adjustment to MAAUI' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK) CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK) CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND  (app.[Exception] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR app.[Exception] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	app.[Exception] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR  [Exception] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR app.[Message] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	app.[Message] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR  app.[Message] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR app.[Source] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spAppSyncBARCToTPFDataAudit+'%') AND t.[User] = app.[User])
		AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND (ap.[Exception] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	ap.[Exception] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR  ap.[Exception] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR ap.[Message] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	ap.[Message] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR  ap.[Message] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.SyncToMaauiTpf+'%' OR ap.[Source] LIKE '%'+ConstantsValues.AuditSyncToMaauiTpf+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spMACTAPTUpload+'%'
		OR 	ap.[Source] LIKE '%'+ConstantsValues.spMarketAdjUpdate+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.spRiskScoreUploadBARC_MAAUI+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.SyncMaauiTpf+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spAppSyncBARCToTPFDataAudit+'%')
		WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
		OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
		)

		AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])
		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) ,A.[Message]

		UNION ALL
		-- 6. Upload risk score file to Bid Model
				SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
 ((SELECT 'Upload risk score file to Bid Model' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((( [Exception] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
		OR [Exception] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR [Exception] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR [Exception] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
OR [Exception] LIKE '%'+ConstantsValues.ProcessResults+'%'
OR [Exception] LIKE '%'+ConstantsValues.ResultsMmandRs+'%'
OR [Exception] LIKE '%'+ConstantsValues.ResultsMmandRscores+'%' OR [Exception] LIKE '%'+ConstantsValues.ImportBarCmmAndRsMemberMonthsDto+'%'
OR [Exception] LIKE '%'+ConstantsValues.ImportBarCmmAndRsRiskScoresDto+'%'
OR [Exception] LIKE '%'+ConstantsValues.ImportBatchBarCmmAndRsData+'%' OR [Exception] LIKE '%'+ConstantsValues.BatchimportBarCmmAndRsMemberMonths+'%'
OR [Exception] LIKE '%'+ConstantsValues.BatchimportBarCmmAndRsRiskScores+'%'
OR	[Source] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
		OR [Source] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR [Source] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR [Source] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
OR 	[Message] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
		OR [Message] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR [Message] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR [Message] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
) AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
		UNION ALL

		(SELECT 'Upload risk score file to Bid Model' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK) CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK) CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND ( app.[Exception] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
					OR (app.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND app.[Message] LIKE '%'+ConstantsValues.ActAdjRepRS+'%')
		OR app.[Exception] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR app.[Exception] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.ProcessResults+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.ResultsMmandRs+'%' OR app.[Exception] LIKE '%'+ConstantsValues.ResultsMmandRscores+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.ImportBarCmmAndRsMemberMonthsDto+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.ImportBarCmmAndRsRiskScoresDto+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.ImportBatchBarCmmAndRsData+'%' OR app.[Exception] LIKE '%'+ConstantsValues.BatchimportBarCmmAndRsMemberMonths+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.BatchimportBarCmmAndRsRiskScores+'%'
OR	app.[Source] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR app.[Source] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
OR 	app.[Message] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR app.[Message] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
) AND t.[User] = app.[User])
AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND ( ap.[Exception] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
				OR (ap.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND ap.[Message] LIKE '%'+ConstantsValues.ActAdjRepRS+'%')
		OR ap.[Exception] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR ap.[Exception] LIKE'%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.ProcessResults+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.ResultsMmandRs+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.ResultsMmandRscores+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.ImportBarCmmAndRsMemberMonthsDto+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.ImportBarCmmAndRsRiskScoresDto+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.ImportBatchBarCmmAndRsData+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.BatchimportBarCmmAndRsMemberMonths+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.BatchimportBarCmmAndRsRiskScores+'%'
OR	ap.[Source] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR ap.[Source] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
OR 	ap.[Message] LIKE '%'+ConstantsValues.spAppGetBatchImportMemberMonthsWithPlanAdminBlend+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.spUploadRiskFactorBulkUpload+'%' OR ap.[Message] LIKE '%'+ConstantsValues.spUploadMemberMonthsWithoutError+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.spCalcPlanAdminBlendBulkImport+'%'
)

				WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
				OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
				)

				AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])
		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate),A.[Message]

		UNION ALL
		-- 7. Upload sales membership file to Bid Model

						SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
 ((SELECT 'Upload sales membership file to Bid Model' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid])  AS ErrorCount,
  [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((( [Exception] LIKE '%'+ConstantsValues.BatchUploadForSalesMembership+'%'
OR [Exception] LIKE '%'+ConstantsValues.ImportSalesMembershipUpload+'%' OR [Exception] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR  [Message] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR  [Source] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR [Exception] LIKE '%'+ConstantsValues.ImportSalesMembership+'%')
AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
		UNION ALL
		(SELECT 'Upload sales membership file to Bid Model' AS [ActionName],t.[user],[t].[Date] startdate,
COUNT(t.[logid])  AS ErrorCount,
  [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK) CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK)  CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND (app.[Exception] LIKE '%'+ConstantsValues.BatchUploadForSalesMembership+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.ImportSalesMembershipUpload+'%' OR app.[Exception] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
	OR (app.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND app.[Message] LIKE '%'+ConstantsValues.ActAdjSales+'%')
OR  app.[Message] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR  app.[Source] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.ImportSalesMembership+'%') AND t.[User] = app.[User])
AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND ( ap.[Exception] LIKE '%'+ConstantsValues.BatchUploadForSalesMembership+'%'
					OR (ap.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND ap.[Message] LIKE '%'+ConstantsValues.ActAdjSales+'%')
OR ap.[Exception] LIKE '%'+ConstantsValues.ImportSalesMembershipUpload+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR  ap.[Message] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR  ap.[Source] LIKE '%'+ConstantsValues.spUploadSalesMembership+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.ImportSalesMembership+'%')
	WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
	OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
	)

	AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])
		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) ,A.[Message]


	UNION ALL
	-- 8. MSB Imports

						SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
  ((SELECT 'MSB Imports' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid]) AS ErrorCount,
 [t].[Date] AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((([Exception] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR [Exception] LIKE '%'+ConstantsValues.ImportMsbUpload+'%'
OR [Exception] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR [Exception] LIKE '%'+ConstantsValues.spDeleteMSB+'%'
OR [Source] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR [Source] LIKE '%'+ConstantsValues.spDeleteMSB+'%'  OR
 [Source] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR [Source] LIKE '%'+ConstantsValues.ImportMsbUpload+'%'
OR [Message] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR [Message] LIKE '%'+ConstantsValues.spDeleteMSB+'%'  OR
 [Message] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR [Message] LIKE '%'+ConstantsValues.ImportMsbUpload+'%')
AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
		UNION ALL

		(SELECT 'MSB Imports' AS [ActionName],t.[user],[t].[Date] startdate,
  COUNT(t.[logid])  AS ErrorCount,
 [t].[Date] AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK) CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK) CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND (app.[Exception] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR app.[Exception] LIKE '%'+ConstantsValues.ImportMsbUpload+'%'
					OR (app.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND app.[Message] LIKE '%'+ConstantsValues.ActAdjMSB+'%')
OR app.[Exception] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR app.[Exception] LIKE '%'+ConstantsValues.spDeleteMSB+'%'
OR app.[Source] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR app.[Source] LIKE '%'+ConstantsValues.spDeleteMSB+'%'  OR
 app.[Source] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR app.[Source] LIKE '%'+ConstantsValues.ImportMsbUpload+'%'
OR app.[Message] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR app.[Message] LIKE '%'+ConstantsValues.spDeleteMSB+'%'  OR
 app.[Message] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR app.[Message] LIKE '%'+ConstantsValues.ImportMsbUpload+'%') AND t.[User] = app.[User])
 AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND (ap.[Exception] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.ImportMsbUpload+'%'
					OR (ap.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND ap.[Message] LIKE '%'+ConstantsValues.ActAdjMSB+'%')
OR ap.[Exception] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.spDeleteMSB+'%'
OR ap.[Source] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR ap.[Source] LIKE '%'+ConstantsValues.spDeleteMSB+'%'  OR
 ap.[Source] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR ap.[Source] LIKE '%'+ConstantsValues.ImportMsbUpload+'%'
OR ap.[Message] LIKE '%'+ConstantsValues.spUploadMSB+'%' OR ap.[Message] LIKE '%'+ConstantsValues.spDeleteMSB+'%'  OR
 ap.[Message] LIKE '%'+ConstantsValues.BatchUploadForMsb+'%' OR ap.[Message] LIKE '%'+ConstantsValues.ImportMsbUpload+'%')
 WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
 OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
 )

 AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])
		)AS A	 GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate),A.[Message]


	UNION ALL
	-- 9. Benefit upload Imports
	SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
  ((SELECT 'Benefit upload' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid])  AS ErrorCount,
  [t].[Date]   AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((( [Exception] LIKE '%'+ConstantsValues.ImportActuarialAdj_BenefitCategory+'%'
OR ([Exception] LIKE '%'+ConstantsValues.ImportBatchTabData+'%' AND [Exception] LIKE '%'+ConstantsValues.ActAdjBenCat+'%')
OR [Exception] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR [Exception] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
 [Source] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR [Source] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
[Message] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR [Message] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
([Message] LIKE '%'+ConstantsValues.ActAdjBenCat+'%' AND  [Message] LIKE '%'+ConstantsValues.ImportBatchTabData+'%')
OR [Message] LIKE '%'+ConstantsValues.ImportActuarialAdj_BenefitCategory+'%'
)
AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
		UNION ALL
		(SELECT 'Benefit upload' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
  [t].[Date]   AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK) CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK) CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND ( app.[Exception] LIKE '%'+ConstantsValues.ImportActuarialAdj_BenefitCategory+'%'
					OR (app.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND app.[Message] LIKE '%'+ConstantsValues.ActAdjBenCat+'%')
OR (app.[Exception] LIKE '%'+ConstantsValues.ImportBatchTabData+'%' AND app.[Exception] LIKE '%'+ConstantsValues.ActAdjBenCat+'%')
OR app.[Exception] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR app.[Exception] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
 app.[Source] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR app.[Source] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
app.[Message] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR app.[Message] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
(app.[Message] LIKE '%'+ConstantsValues.ActAdjBenCat+'%' AND  app.[Message] LIKE '%'+ConstantsValues.ImportBatchTabData+'%')
OR app.[Message] LIKE '%'+ConstantsValues.ImportActuarialAdj_BenefitCategory+'%'
) AND t.[User] = app.[User])
AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND ( ap.[Exception] LIKE '%'+ConstantsValues.ImportActuarialAdj_BenefitCategory+'%'
					OR (ap.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND ap.[Message] LIKE '%'+ConstantsValues.ActAdjBenCat+'%')
OR (ap.[Exception] LIKE '%'+ConstantsValues.ImportBatchTabData+'%' AND ap.[Exception] LIKE '%'+ConstantsValues.ActAdjBenCat+'%')
OR ap.[Exception] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR ap.[Exception] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
 ap.[Source] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR ap.[Source] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
ap.[Message] LIKE '%'+ConstantsValues.spAppTrendGetAvailableAdjustmentID+'%'
OR ap.[Message] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjBenefitCategory+'%' OR
(ap.[Message] LIKE '%'+ConstantsValues.ActAdjBenCat+'%' AND  ap.[Message] LIKE '%'+ConstantsValues.ImportBatchTabData+'%')
OR ap.[Message] LIKE '%'+ConstantsValues.ImportActuarialAdj_BenefitCategory+'%'
)
WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
)

AND t.[LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])
		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate),A.[Message]

		UNION ALL
		  -- 10. Reporting upload
		SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
 ((SELECT 'Reporting upload' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid]) AS ErrorCount,
 [Date] maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((([Exception] LIKE '%'+ConstantsValues.ImportActuarialAdj_ReportingCategory+'%'
OR ([Exception] LIKE '%'+ConstantsValues.ImportBatchTabData+'%' AND [Exception] LIKE '%'+ConstantsValues.ActAdjRepCat+'%')
OR [Exception] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%' OR
([Message] LIKE '%'+ConstantsValues.ActAdjRepCat+'%' AND  [Message] LIKE '%'+ConstantsValues.ImportBatchTabData+'%')
OR [Message] LIKE '%'+ConstantsValues.ImportActuarialAdj_ReportingCategory+'%'
OR [Message] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%'
OR [Source] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%')
AND [Exception]<>'')
		AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY [user],[t].[Date],t.[Message])
		UNION ALL

		(SELECT 'Reporting upload' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK)
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK) CROSS APPLY ConstantsValues
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK) CROSS APPLY ConstantsValues
				WHERE t.LogID < app.logid
				AND (app.[Exception] LIKE '%'+ConstantsValues.ImportActuarialAdj_ReportingCategory+'%'
				OR (app.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND app.[Message] LIKE '%'+ConstantsValues.ActAdjRepCat+'%')
OR (app.[Exception] LIKE '%'+ConstantsValues.ImportBatchTabData+'%' AND app.[Exception] LIKE '%'+ConstantsValues.ActAdjRepCat+'%')
OR app.[Exception] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%' OR
(app.[Message] LIKE '%'+ConstantsValues.ActAdjRepCat+'%' AND  [Message] LIKE '%'+ConstantsValues.ImportBatchTabData+'%')
OR app.[Message] LIKE '%'+ConstantsValues.ImportActuarialAdj_ReportingCategory+'%'
OR app.[Message] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%'
OR app.[Source] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%') AND t.[User] = app.[User])
AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND ( ap.[Exception] LIKE '%'+ConstantsValues.ImportActuarialAdj_ReportingCategory+'%'
OR ( ap.[Exception] LIKE '%'+ConstantsValues.ImportBatchTabData+'%' AND  ap.[Exception] LIKE '%'+ConstantsValues.ActAdjRepCat+'%')
OR (ap.[Message] LIKE '%'+ConstantsValues.ImportBatchData+'%' AND ap.[Message] LIKE '%'+ConstantsValues.ActAdjRepCat+'%')
OR  ap.[Exception] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%' OR
( ap.[Message] LIKE '%'+ConstantsValues.ActAdjRepCat+'%' AND   ap.[Message] LIKE '%'+ConstantsValues.ImportBatchTabData+'%')
OR  ap.[Message] LIKE '%'+ConstantsValues.ImportActuarialAdj_ReportingCategory+'%'
OR  ap.[Message] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%'
OR  ap.[Source] LIKE '%'+ConstantsValues.spAppTrendUploadActuarialAdjReportingCategory+'%')
WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
)

AND t.[LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])
		)AS A  GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate),A.[Message]
UNION ALL
--11.BPT Creation MAAUI
SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
 ((SELECT 'BPT Creation MAAUI' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((([Exception] LIKE '%'+ConstantsValues.ActuarialGenerateBpt+'%' OR [Exception] LIKE '%'+ConstantsValues.GenarateBptExcel+'%'
 OR [Exception] LIKE '%'+ConstantsValues.GenarateBptExcelInternal+'%'
 OR [Exception] LIKE '%'+ConstantsValues.PopulateBptTemplate+'%' OR [Exception] LIKE '%'+ConstantsValues.GetMabtCreation+'%'
		OR [Message] LIKE '%'+ConstantsValues.PopulateBptTemplate+'%'  OR [Message] LIKE '%'+ConstantsValues.GetMabtCreation+'%'
		OR [Message] LIKE '%'+ConstantsValues.GenarateBptExcelInternal+'%'
		OR [Message] LIKE '%'+ConstantsValues.ActuarialGenerateBpt+'%'
		OR [Message] LIKE '%'+ConstantsValues.GenarateBptExcel+'%'
		OR [Message] LIKE '%'+ConstantsValues.CreateBptWorkbook+'%'
		OR [Exception] LIKE '%'+ConstantsValues.CreateBptWorkbook+'%'
		OR  [Message] LIKE '%'+ConstantsValues.ActuarialGenerateBptBatch+'%' OR
		 [Exception] LIKE '%'+ConstantsValues.ActuarialGenerateBptBatch+'%' OR
        [Message] LIKE '%'+ConstantsValues.CreateZipFile+'%' OR
		 [Exception] LIKE '%'+ConstantsValues.CreateZipFile+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetFileBytesAndDeleteTheFile+'%' OR
		[Exception] LIKE '%'+ConstantsValues.GetFileBytesAndDeleteTheFile+'%' OR
		[Exception] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		[Message] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		[Source] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%'
		OR [Message] LIKE '%'+ConstantsValues.ErrorfetchingBPTdata+'%'
		OR [Exception] LIKE '%'+ConstantsValues.ErrorfetchingBPTdata+'%' OR
		[Message] LIKE '%'+ConstantsValues.ErrorcreatetingBPT+'%' OR
		[Exception] LIKE '%'+ConstantsValues.ErrorcreatetingBPT+'%'
		OR [Message] LIKE '%'+ConstantsValues.FailedtoCreateBPT+'%'
		OR [Exception] LIKE '%'+ConstantsValues.FailedtoCreateBPT+'%'
		OR [Exception] LIKE '%'+ConstantsValues.BPTTemplates+'%'
		OR [Message] LIKE '%'+ConstantsValues.BPTTemplates+'%'
		)AND [Exception]<>'')
			AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
UNION ALL
		(SELECT 'BPT Creation MAAUI' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK)
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM DashboardReportAppLogs app WITH (NOLOCK)
				WHERE t.LogID < app.logid
				AND (app.[Exception] LIKE '%'+ConstantsValues.ActuarialGenerateBpt+'%'
				OR app.[Exception] LIKE '%'+ConstantsValues.GenarateBptExcel+'%'
				OR app.[Exception] LIKE '%'+ConstantsValues.GenarateBptExcelInternal+'%'
 OR app.[Exception] LIKE '%'+ConstantsValues.PopulateBptTemplate+'%' OR app.[Exception] LIKE '%'+ConstantsValues.GetMabtCreation+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.PopulateBptTemplate+'%'  OR app.[Message] LIKE '%'+ConstantsValues.GetMabtCreation+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GenarateBptExcelInternal+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.ActuarialGenerateBpt+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GenarateBptExcel+'%'
       OR app.[Message] LIKE '%'+ConstantsValues.CreateBptWorkbook+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.CreateBptWorkbook+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.ActuarialGenerateBptBatch+'%' OR
		 app.[Exception] LIKE '%'+ConstantsValues.ActuarialGenerateBptBatch+'%' OR
        app.[Message] LIKE '%'+ConstantsValues.CreateZipFile+'%' OR
		app. [Exception] LIKE '%'+ConstantsValues.CreateZipFile+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.GetFileBytesAndDeleteTheFile+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.GetFileBytesAndDeleteTheFile+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		 app.[Message] LIKE '%'+ConstantsValues.ErrorfetchingBPTdata+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.ErrorfetchingBPTdata+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.ErrorcreatetingBPT+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.ErrorcreatetingBPT+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.FailedtoCreateBPT+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.FailedtoCreateBPT+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.BPTTemplates+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.BPTTemplates+'%'  )
		AND t.[User] = app.[User])
				AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND (ap.[Exception] LIKE '%'+ConstantsValues.ActuarialGenerateBpt+'%'
				OR ap.[Exception] LIKE '%'+ConstantsValues.GenarateBptExcel+'%'
				OR ap.[Exception] LIKE '%'+ConstantsValues.GenarateBptExcelInternal+'%'
 OR ap.[Exception] LIKE '%'+ConstantsValues.PopulateBptTemplate+'%'OR ap.[Exception] LIKE '%'+ConstantsValues.GetMabtCreation+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.PopulateBptTemplate+'%'   OR ap.[Message] LIKE '%'+ConstantsValues.GetMabtCreation+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GenarateBptExcelInternal+'%'
		  OR ap.[Message] LIKE '%'+ConstantsValues.ActuarialGenerateBpt+'%'
		  OR ap.[Message] LIKE '%'+ConstantsValues.GenarateBptExcel+'%'
		 OR ap.[Message] LIKE '%'+ConstantsValues.CreateBptWorkbook+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.CreateBptWorkbook+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.ActuarialGenerateBptBatch+'%' OR
		 ap.[Exception] LIKE '%'+ConstantsValues.ActuarialGenerateBptBatch+'%' OR
        ap.[Message] LIKE '%'+ConstantsValues.CreateZipFile+'%' OR
		 ap.[Exception] LIKE '%'+ConstantsValues.CreateZipFile+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.GetFileBytesAndDeleteTheFile+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.GetFileBytesAndDeleteTheFile+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.spAppGetMABPTCreation+'%' OR
		 ap.[Message] LIKE '%'+ConstantsValues.ErrorfetchingBPTdata+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.ErrorfetchingBPTdata+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.ErrorcreatetingBPT+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.ErrorcreatetingBPT+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.FailedtoCreateBPT+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.FailedtoCreateBPT+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.BPTTemplates+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.BPTTemplates+'%'  )
				WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
				OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
				)
				AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])

		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate),A.[Message]
	UNION ALL
--12. SPV Reprice
		SELECT [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate) AS exectime,A.[Message]
FROM
 ((SELECT 'SPV Reprice' AS [ActionName],[user],[t].[Date] startdate,
 COUNT([logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
 WHERE ((([Exception] LIKE '%'+ConstantsValues.RepricePlan+'%' OR [Exception] LIKE '%'+ConstantsValues.RePricePlans+'%'
 OR [Exception] LIKE '%'+ConstantsValues.GetPlansToBeRepriced+'%'
 OR [Exception] LIKE '%'+ConstantsValues.spPlanRefresh+'%'
		OR [Message] LIKE '%'+ConstantsValues.RepricePlan+'%'  OR [Message] LIKE '%'+ConstantsValues.RePricePlans+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetPlansToBeRepriced+'%'
		OR [Message] LIKE '%'+ConstantsValues.spPlanRefresh+'%' OR [Source] LIKE '%'+ConstantsValues.spPlanRefresh+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetAdminData+'%'
		OR  [Message] LIKE '%'+ConstantsValues.GetAdminData+'%'
		OR [Exception] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR  [Message] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR [Source] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetRebateMemberPremiumData+'%' OR
        [Message] LIKE '%'+ConstantsValues.GetRebateMemberPremiumData+'%' OR
		 [Exception] LIKE '%'+ConstantsValues.GetRebateAllocation+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetRebateAllocation+'%' OR
		[Exception] LIKE '%'+ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		[Message] LIKE '%'+ ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		[Source] LIKE '%'+ ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		[Exception] LIKE '%'+ConstantsValues.GetRevenueData+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetRevenueData+'%' OR
		[Exception] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%' OR
		[Message] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%' OR
		[Source] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetMarketBidSummary+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetMarketBidSummary+'%' OR
		[Message] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%' OR
		[Exception] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%' OR
		[Source] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetRebateSpentAmt+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetRebateSpentAmt+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetUnspent+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetUnspent+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetCostShareData+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetCostShareData+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetBidSummaryFactorsForAll+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetBidSummaryFactorsForAll+'%'
		OR [Exception] LIKE '%'+ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR [Message] LIKE '%'+ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR [Source] LIKE '%'+ ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetReaderData+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetReaderData+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetBidSummary+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetBidSummary+'%'
		OR [Exception] LIKE '%'+ConstantsValues.GetAddedBenNet+'%'
		OR [Message] LIKE '%'+ConstantsValues.GetAddedBenNet+'%'
		OR [Exception] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'
		OR [Message] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'
		OR [Source] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'
		)AND [Exception]<>'')
			AND  [LogID] > @XLastSuccessfullRunLogid
		)
		GROUP BY t.[User],[t].[Date],t.[Message])
UNION ALL
		(SELECT 'SPV Reprice' AS [ActionName],t.[user],[t].[Date] startdate,
 COUNT(t.[logid])  AS ErrorCount,
 [t].[Date]  AS maxenddate,t.[Message]
FROM dbo.DashboardReportAppLogs t WITH (NOLOCK) CROSS APPLY ConstantsValues
INNER JOIN dbo.DashboardReportAppLogs ap WITH (NOLOCK)
ON t.LogID<ap.LogID
AND ap.LogID =(SELECT MIN(LogID)
				FROM dbo.DashboardReportAppLogs app WITH (NOLOCK)
				WHERE t.LogID < app.logid
				AND (app.[Exception] LIKE '%'+ConstantsValues.RepricePlan+'%' OR app.[Exception] LIKE '%'+ConstantsValues.RePricePlans+'%'
 OR app.[Exception] LIKE '%'+ConstantsValues.GetPlansToBeRepriced+'%'
 OR app.[Exception] LIKE '%'+ConstantsValues.spPlanRefresh+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.RepricePlan+'%'  OR app.[Message] LIKE '%'+ConstantsValues.RePricePlans+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetPlansToBeRepriced+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.spPlanRefresh+'%' OR app.[Source] LIKE '%'+ConstantsValues.spPlanRefresh+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetAdminData+'%'
		OR  app.[Message] LIKE '%'+ConstantsValues.GetAdminData+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR  app.[Message] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetRebateMemberPremiumData+'%' OR
        app.[Message] LIKE '%'+ConstantsValues.GetRebateMemberPremiumData+'%' OR
		 app.[Exception] LIKE '%'+ConstantsValues.GetRebateAllocation+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetRebateAllocation+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		app.[Message] LIKE '%'+ ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		app.[Source] LIKE '%'+ ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.GetRevenueData+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetRevenueData+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetMarketBidSummary+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetMarketBidSummary+'%' OR
		app.[Message] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%' OR
		app.[Exception] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%' OR
		app.[Source] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetRebateSpentAmt+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetRebateSpentAmt+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetUnspent+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetUnspent+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetCostShareData+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetCostShareData+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetBidSummaryFactorsForAll+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetBidSummaryFactorsForAll+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR app.[Source] LIKE '%'+ ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetReaderData+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetReaderData+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetBidSummary+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetBidSummary+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.GetAddedBenNet+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.GetAddedBenNet+'%'
		OR app.[Exception] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'
		OR app.[Message] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'
		OR app.[Source] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%' )
		AND t.[User] = app.[User])
				AND t.[User] = ap.[User]
				AND CONVERT(DATE,t.[Date],103)=CONVERT(DATE,ap.[Date],103)
				AND ap.[Date] BETWEEN t.[Date] AND DATEADD(MILLISECOND,60000,t.[Date])
				AND (ap.[Exception] LIKE '%'+ConstantsValues.RepricePlan+'%' OR ap.[Exception] LIKE '%'+ConstantsValues.RePricePlans+'%'
 OR ap.[Exception] LIKE '%'+ConstantsValues.GetPlansToBeRepriced+'%'
 OR ap.[Exception] LIKE '%'+ConstantsValues.spPlanRefresh+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.RepricePlan+'%'  OR ap.[Message] LIKE '%'+ConstantsValues.RePricePlans+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetPlansToBeRepriced+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.spPlanRefresh+'%' OR ap.[Source] LIKE '%'+ConstantsValues.spPlanRefresh+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetAdminData+'%'
		OR  ap.[Message] LIKE '%'+ConstantsValues.GetAdminData+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR  ap.[Message] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spAppGetAdminAllocation+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetRebateMemberPremiumData+'%' OR
        ap.[Message] LIKE '%'+ConstantsValues.GetRebateMemberPremiumData+'%' OR
		 ap.[Exception] LIKE '%'+ConstantsValues.GetRebateAllocation+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetRebateAllocation+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		ap.[Message] LIKE '%'+ ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		ap.[Source] LIKE '%'+ ConstantsValues.spAppGetScenarioAttributesData+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.GetRevenueData+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetRevenueData+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.spAppGetBidSummary+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetMarketBidSummary+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetMarketBidSummary+'%' OR
		ap.[Message] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%' OR
		ap.[Exception] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%' OR
		ap.[Source] LIKE '%'+ConstantsValues.spAppGetMarketBidSummaryMARebateAllocation+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetRebateSpentAmt+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetRebateSpentAmt+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetUnspent+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetUnspent+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetCostShareData+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetCostShareData+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetBidSummaryFactorsForAll+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetBidSummaryFactorsForAll+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR ap.[Source] LIKE '%'+ ConstantsValues.spAppGetBidSummaryFactorsForAll+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetReaderData+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetReaderData+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetBidSummary+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetBidSummary+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.GetAddedBenNet+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.GetAddedBenNet+'%'
		OR ap.[Exception] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'
		OR ap.[Message] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'
		OR ap.[Source] LIKE '%'+ConstantsValues.spAppGetAddedBenefitNet+'%'  )
				WHERE (t.[Message] LIKE '%'+ConstantsValues.ExecutionTimeoutExpired+'%'
				OR t.[Message] LIKE '%'+ConstantsValues.deadlock+'%'
				)
				AND t. [LogID] > @XLastSuccessfullRunLogid
				GROUP BY t.[User],[t].[Date],t.[Message])

		)AS A GROUP BY [ActionName],[user], startdate,ErrorCount,maxenddate,DATEDIFF(MILLISECOND,A.startdate,A.maxenddate),A.[Message]
	) AS Apps

END
GO
