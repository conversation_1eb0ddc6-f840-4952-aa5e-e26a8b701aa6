		   
SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCreateBulkCopiesOfPlan
--
-- CREATOR:			Vikrant <PERSON>gal
--
-- CREATED DATE:	2025-Feb-25  
--
-- DESCRIPTION:		Procedure is responsible for Creating bulk copies of a Plan  
-- EXEC dbo.spCreateBulkCopiesOfPlan @sourceForcastId = 15059,                -- int
--                             @CopyServiceArea = 'Copy Crosswalk', -- varchar(100)
--                             @NoOfCopies = 5,                     -- int
--                             @UserId = 'VLB4232'    
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			NONE    
--
--  Write:			BulkCopyNewPlanAudit
--
-- VIEWS: 
--	Read:			vwPlanInformation
--					
--
-- FUNCTIONS:		
--
-- STORED PROCS:	spAppCopyPlanAndScenario
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------------
-- 2025-FEB-25      1           Initial Version														Vikrant Bagal     
-- ---------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE dbo.spCreateBulkCopiesOfPlan
(
    @sourceForcastId INT,
    @CopyServiceArea VARCHAR(100) = 'Copy Crosswalk',
    @NoOfCopies INT,
    @UserId CHAR(7)
)
AS
BEGIN
    SET NOCOUNT ON;

	-- check if the running server is not - 
	-- lousqlwps3047 and lousqlwps3045
	IF @@SERVERNAME =  'lousqlwps3047' OR @@SERVERNAME = 'lousqlwps3045'
	BEGIN
		PRINT 'This Procedure can not be run on Prod DB';
		RETURN
	END 
    BEGIN TRY

        BEGIN TRAN t1;

        DECLARE 
                @PlanYearID INT,
                @ContractPBPSeg VARCHAR(50),
                @Contract VARCHAR(50),	 
                @PBP VARCHAR(4),
                @Iteration INT = 1;

        -- Read data from the view
        SELECT 
               @ContractPBPSeg = ContractPBPSeg,
               @PBP = PBP,
               @PlanYearID = PlanYearID,
               @Contract = Contract
        FROM dbo.vwPlanInformation
        WHERE ForecastID = @sourceForcastId;

        DECLARE @MaxSegmetNo INT;

        SELECT @MaxSegmetNo = MAX(CAST(Segment AS INT))
        FROM dbo.vwPlanInformation
        WHERE Contract = @Contract
              AND @PBP = PBP;

        -- Loop to execute the stored procedure 10 times
        WHILE @Iteration <= @NoOfCopies
        BEGIN

			DECLARE   @CPS CHAR(13),
					  @Result BIT,
					  @NewForecastID INT,
					  @NewPlanInfoId INT ,							
					  @MessageFromBackend VARCHAR(MAX)

            SET @MaxSegmetNo = @MaxSegmetNo + 1;
            -- Increment @CPS for each iteration (example logic, adjust as needed)
            SET @CPS = CONCAT(@Contract, '-', @PBP, '-', FORMAT(@MaxSegmetNo, '000'));

            DECLARE @NewScenarioName VARCHAR(100) = 'copy of ' + @ContractPBPSeg;
		  
			--PRINT CONCAT('creating copy No.: ',@Iteration );

			-- Execute the stored procedure
            EXEC dbo.spAppCopyPlanAndScenario @CPS = @CPS,
                                              @PlanYear = @PlanYearID,
                                              @ScenarioNum = 1,
                                              @ScenarioName = @NewScenarioName,
                                              @ScenarioDecsription = '',
                                              @SelectedCPS = @ContractPBPSeg,
                                              @CopyServiceArea = @CopyServiceArea,
                                              @OldForecastID = @sourceForcastId,
                                              @LastUpdatebyID = @UserId,
                                              @Result = @Result OUTPUT,
                                              @NewForecastID = @NewForecastID OUTPUT,
                                              @NewPlanInfoId = @NewPlanInfoId OUTPUT,
                                              @MessageFromBackend = @MessageFromBackend OUTPUT;

            -- Increment the iteration counter
            SET @Iteration = @Iteration + 1;
			

			IF @NewForecastID IS NOT NULL
			BEGIN
				--PRINT CONCAT('New CPS:',@CPS,', ForcastId:',@NewForecastID );
				SET @MessageFromBackend = ''
			END
			ELSE
			BEGIN 
				 -- PRINT CONCAT('Error while creating :',@CPS,', Source :',@sourceForcastId,' - ',@ContractPBPSeg );
				  SET @CPS= '';
			END 

			INSERT dbo.BulkCopyNewPlanAudit
			(
				SourceForcastId,
				NewForecastId,
				OldCps,
				NewCps,
				ResultText,
				CreatedDate,
				CreatedByUser
			)
			VALUES
			(   @sourceForcastId,    -- SourceForcastId - int
				@NewForecastID,      -- NewForecastId - int
				@ContractPBPSeg,     -- OldCps - varchar(13)
				@CPS,                -- NewCps - varchar(13)
				@MessageFromBackend, -- ResultText - varchar(max)
				GETDATE(),           -- CreatedDate - datetime
				@UserId              -- CreatedByUser - varchar(7)
				);
			
        END;              
        COMMIT TRAN t1;

    END TRY
    BEGIN CATCH
        ROLLBACK TRAN t1;
    END CATCH;		
END;