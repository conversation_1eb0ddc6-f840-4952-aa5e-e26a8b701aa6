SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	Trend_spProjectionID
--
-- CREATOR:			Hannah Harmon
--
-- CREATED DATE:	2023-JUN-20
--
-- DESCRIPTION:		Updates LkpProjectionVersion and related tables. 
--                  Meant to be used within the process of using Trend_spUpdateAll
--		
-- PARAMETERS:
--  Input  :		@LastUpdateByID
--                  @ProjectionID(Should be the last ProjectionID + 1)		
--		            @DataIncurredDate --(Should be in YYYYMM format)
--                  @DataPaidThroughDate --(Should be in YYYYMM format)
--                  @CurrentQuarter
--                  @CurrentYear
--                  @BidYear
--					
--  Output :		NONE
--
-- TABLES : 
--	Read :			NONE
--
--  Write:			LkpProjectionVersion
--                  Trend_SavedManualCutPlanAssignment
--                  SavedForecastSetup 
--                  PerIntMAAssumptions
--                  
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		dbo.fnGetBidYear
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																					DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-JUN-20 		1		    Initial Version															                        Hannah Harmon	
-- 2023-Oct-04 		2		    Added bidseason flag logic															            Hannah Harmon	
-- 2023-Oct-19 		3		    Changed IsSkipInducedUtilization flag logic so it only turns on in Q4.							
--								Changed logic for Q4 LastCurrentYearQuarter now turns to 2 instead of 3 to match new Q4 timing.	Hannah Harmon
--								
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_spProjectionID]
@DataIncurredDate AS INT, --(Should be in YYYYMM format)
@DataPaidThroughDate AS INT, --(Should be in YYYYMM format)
@CurrentQuarter AS INT, --(Should be only 1,2,3,4)
@LastUpdateByID CHAR(7),--(Typically passed in from MAAUI UI)
@ValidateMessage AS VARCHAR(MAX) OUTPUT

AS

	SET NOCOUNT ON;

    BEGIN

		BEGIN TRY


			BEGIN TRANSACTION;


			IF(@CurrentQuarter NOT IN(1,2,3,4))
				BEGIN SET @ValidateMessage = CONCAT(@CurrentQuarter,' is not a valid quarter entry.')
				RAISERROR(@ValidateMessage, 16, 1);
				END


			DECLARE @CurrentYear AS VARCHAR(4)
			DECLARE @BidYear AS VARCHAR(4)
			DECLARE @ProjectionID AS INT --(Should be the last ProjectionID + 1)	
			SET @CurrentYear = dbo.fnGetBidYear()-1
			SET @BidYear = dbo.fnGetBidYear()



			--Creating the newest ProjectionID in LkpProjectionVersion)





			INSERT INTO dbo.LkpProjectionVersion
					(--ProjectionID will automatically insert itself here. No need to list a value it will throw an error if one is listed.
					DataSnapshot
					,ProjectionName
					,IsLiveProjection
					,LastUpdateByID
					,LastUpdateDateTime
					,LastCurrentYearQuarter)
					VALUES (
							concat('Inc ',@DataIncurredDate,' Pd ',@DataPaidThroughDate) 
							,CASE 
								WHEN @CurrentQuarter = '1' THEN CONCAT(@BidYear,'FLP')
								WHEN @CurrentQuarter = '2' THEN	CONCAT(@BidYear,'Bids')
								WHEN @CurrentQuarter = '3' THEN	CONCAT(@CurrentYear,'Q3')
								WHEN @CurrentQuarter = '4' THEN	CONCAT(@CurrentYear,'Q4')
								ELSE 'Invalid Quarter Entered'
							END
							,1                                                 
							,@LastUpdateByID                                   
							,GETDATE ()                                        
							,CASE                                              
								WHEN @CurrentQuarter = 4 THEN 2
								ELSE 0 
							END                                             
					)

			SET @ProjectionID = (SELECT MAX(ProjectionID) FROM dbo.LkpProjectionVersion)

			--Updating all rows of Trend_SavedManualCutPlanAssignment to have the newest ProjectionID
			UPDATE dbo.Trend_SavedManualCutPlanAssignment SET ProjectionID = @ProjectionID 
				WHERE 1 = 1

			--Updating SavedForecastSetup to have or not have IsSkippedInducedUtilization 
			--During FLP and Bids, IsSkippedInducedUtilization is set to 0. Otherwise, it is set to 1. 
			UPDATE dbo.SavedForecastSetup 
				SET IsSkippedInducedUtilization= CASE WHEN @CurrentQuarter IN('1', '2', '3') THEN 0 ELSE 1 END 
				WHERE 1 = 1

			--Updating PerIntMAAssumptions to match whether it is currently Bid Season or not.
			--During FLP and Bids, IsBidSeason is set to 1. Otherwise, it is set to 0. 
			UPDATE dbo.PerIntMAAssumptions 
				SET PerIntMAAssumptions.IsBidSeason= CASE WHEN @CurrentQuarter IN('1', '2') THEN 1 ELSE 0 END
				WHERE 1 = 1


			--Set the IsLiveProjection from the previous row in LkpProjectionVersion to 0 now that it is no longer live.
			UPDATE dbo.LkpProjectionVersion SET IsLiveProjection = 0 
				WHERE ProjectionID <> @ProjectionID AND IsLiveProjection = 1;



            COMMIT TRANSACTION;

        END TRY

        BEGIN CATCH
            ROLLBACK TRANSACTION;
            SET @ValidateMessage = 'ExecutionFailed. Please contact the iRS Model Solutions team for questions';
            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @ErrorException NVARCHAR(4000);
            DECLARE @errSrc      VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL')
                   ,@currentdate DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS

                   VARCHAR);
            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
			IF @@TRANCOUNT > 0
				BEGIN
					ROLLBACK TRANSACTION;
				END;

            ---Insert into app log for logging error------------------    
            EXEC spAppAddLogEntry @currentdate
                                 ,''
                                 ,'ERROR'
                                 ,@errSrc
                                 ,@ErrorMessage
                                 ,@ErrorException
                                 ,'';

        END CATCH;



    END;
GO
