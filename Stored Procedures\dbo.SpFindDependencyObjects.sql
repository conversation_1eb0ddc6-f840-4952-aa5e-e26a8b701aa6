SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------        
-- Author:  <PERSON><PERSON>
-- Create date: 2022-Dec-27
-- Description:  Finding dependecies of given table name and column name   
--          
--        
-- PARAMETERS: 
--			 @tblName
--			 @clmName
-- TABLES:         
-- Read:        
--		sysobjects
--		syscolumns
--		sysdepends
-- Write:        
--        
-- VIEWS:        
--        
-- FUNCTIONS:       
--        
-- STORED PROCS:        
--        
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION      CHANGES MADE														DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2022-12-27		1           Initial Version														Surya Murthy
------------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[SpFindDependencyObjects]
	@tblName VARCHAR(200),
	@clmName VARCHAR(100)
AS
BEGIN
	SET NOCOUNT ON;
	SELECT OBJECT_NAME (referencing_id) objectName,               
         referenced_entity_name tableName
	FROM sys.sql_expression_dependencies d
	WHERE OBJECT_NAME(d.referenced_id) =  @tblName
		  AND OBJECT_DEFINITION (referencing_id)  LIKE '%'+@clmName+'%'
	ORDER BY OBJECT_NAME(referencing_id);
END
