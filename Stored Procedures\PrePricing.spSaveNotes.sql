SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [dbo].[spSaveNotes]   
--    
-- AUTHOR: <PERSON><PERSON> 
--    
-- CREATED DATE: 2024-Oct-29    
-- Type: 
-- DESCRIPTION: Procedure responsible for saving notes
--    
-- PARAMETERS:    
-- Input: 
-- @PlanInfoId
-- @NotesInfo
-- @LastUpdatedByUserId
-- @OutPut<PERSON><PERSON>ult

-- TABLES:   
--  

-- Read:    
--  

-- Write:    
--    PrePricing.PlanNote
--
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-29		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spSaveNotes]
(
	@PlanInfoId INT,
	@NotesInfo VARCHAR(1000),
	@LastUpdatedByUserId VARCHAR(200)	
)
AS    
BEGIN    
	BEGIN TRY
	BEGIN TRANSACTION saveNotes	
		DECLARE	@OutPutResultCode VARCHAR(20)
		SET @OutPutResultCode='Success'
	    DECLARE @OutPutResult VARCHAR(200) 
		SET @OutPutResult = 'Notes Saved Successfully.'
		INSERT INTO PrePricing.PlanNote
		(
		    PlanInfoID,
		    Note,
		    LastUpdateByID,
		    LastUpdateDateTime
		)
		VALUES
		(   @PlanInfoId,        -- PlanInfoID - int
		    @NotesInfo,       -- Note - varchar(1000)
		    @LastUpdatedByUserId,       -- LastUpdateByID - varchar(7)
		    GETDATE() -- LastUpdateDateTime - datetime
		)
	SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	COMMIT TRANSACTION saveNotes
	END TRY
	BEGIN CATCH	
	SET @OutPutResultCode='Error'
	SET @OutPutResult = 'Error While Saving Notes.'	
	SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	ROLLBACK TRANSACTION saveNotes;
	END CATCH
END
GO
