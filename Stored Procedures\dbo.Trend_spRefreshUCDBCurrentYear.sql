SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_spRefreshUCDBCurrentYear
--
-- CREATOR: <PERSON><PERSON><PERSON><PERSON>
--
-- CREATED DATE: Nov-05-2020
--
-- DESCRIPTION: This stored procedure takes membership from MRA deliverables and adds on the UCDB relativites from the Trends team
--        
-- PARAMETERS:
--  Input  : @LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  dbo.LkpIntPlanYear
--					dbo.UCDB_County_FIPS
--                  dbo.UCDB_Final_Relativities
--					dbo.Trend_PerPopulationMRACurrentYear
--					dbo.UCDB_County_FIPS
--					dbo.UCDB_Final_Relativities
--
--          Write:  dbo.Trend_SavedRelativityCMSReimb
--
-- VIEWS: Read: dbo.vwPlanInfo
--
-- FUNCTIONS: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 11/05/20    		0		    Initial Version																Aleksandar Dimitrijevic
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 11/30/20    		1		    Removed temp table #RepCat													
--								Added line "WHERE SSStateCountyCD <> 99999"									Aleksandar Dimitrijevic
--								for the creation of table #mbr_tbl
--								Removed section of the code that replicates relativities for
--								Part B Rx categories
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 12/02/20    		2		    Split into two sp's (current year and bid year)								Aleksandar Dimitrijevic
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 12/22/20    		3		    Added ROWLOCK and NOLOCK for required tables								Surya Murthy
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 02/10/21			4			sp renamed from [dbo].[Trend_CurrentYearUCBD]												
--								replaced the output table:                                                  
--								dbo.Trend_UCDB --> [dbo].[Trend_SavedRelativityCMSReimb]                    Aleksandar Dimitrijevic
--								changed the calculation of the CostRelativity in table #final_predup
--                              to force the CostRelativity = 1 when sum(total_MM) = 0
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 6/29/21    		5		    Made cosmetic changes to the sp based on the TechnicalReview Template		Aleksandar Dimitrijevic
-- 10/15/21         6          Fixed Sonar Qube issues                                                      Ramandeep Saini
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_spRefreshUCDBCurrentYear]
--Declare
@LastUpdateByID CHAR(13)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        ----------------------------------------------------
        -- 0. Declare / set variables and pull parameters --
        ----------------------------------------------------

        -- Current Year
        DECLARE @CurrentYear SMALLINT;
        SET @CurrentYear = (SELECT  PlanYearID - 1 FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);

        ----------------------------------------------------
        -- 1. Pull county level source data ----------------
        ----------------------------------------------------

        --Pulling the Current Year Membership using Population Tables:
        IF (SELECT  OBJECT_ID ('tempdb..#mbr_tbl')) IS NOT NULL DROP TABLE #mbr_tbl;

        -- limiting the table to only one reporting category as membership duplicated for each RC
        SELECT      DISTINCT
                    PlanYearID
                   ,QuarterID
                   ,CPS
                   ,SSStateCountyCD
                   ,SUM (CY_MM) AS CY_MM
        INTO        #mbr_tbl
        FROM        dbo.Trend_PerPopulationMRACurrentYear WITH (NOLOCK)
		
        WHERE       ReportingCategory = 'Ancil'

        GROUP BY    PlanYearID
                   ,QuarterID
                   ,CPS
                   ,SSStateCountyCD;

        -- Relativities and Membership:
        IF (SELECT  OBJECT_ID ('tempdb..#CountyData')) IS NOT NULL
            DROP TABLE #CountyData;

        SELECT      m.CPS
                   ,CASE WHEN LEN (SSStateCountyCD) = 4 THEN '0' + CAST(SSStateCountyCD AS VARCHAR)
                         ELSE SSStateCountyCD END AS SSStateCountyCD
                   ,m.PlanYearID
                   ,QuarterID
                   ,ufr.CBSAStrip
                   ,ufr.ReportingCategory
                   ,vpi.Product
                   ,COALESCE (ufr.relativity, 1) AS Relativity
                   ,COALESCE (SUM (m.CY_MM), 0) AS MemberMonths
        INTO        #CountyData
        FROM        #mbr_tbl m

        LEFT JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
               ON m.PlanYearID = vpi.PlanYear
                  AND   m.CPS = vpi.CPS

       INNER JOIN   dbo.UCDB_County_FIPS ucf WITH (NOLOCK)
               ON m.SSStateCountyCD = ucf.MedicareCntyNbr

        LEFT JOIN   dbo.UCDB_Final_Relativities ufr WITH (NOLOCK)
               ON ufr.PlanYearID = m.PlanYearID
                  AND   ufr.Quarter = m.QuarterID
                  AND   ufr.CBSAStrip = ucf.CBSAStrip
                  AND   ufr.product = vpi.Product
        WHERE       SSStateCountyCD <> 99999

        GROUP BY    m.CPS
                   ,m.SSStateCountyCD
                   ,m.PlanYearID
                   ,m.QuarterID
                   ,ufr.CBSAStrip
                   ,ufr.ReportingCategory
                   ,vpi.Product
                   ,ufr.relativity;

        -------------------------------------------------------
        -- 2. Roll up to plan level and insert final results --
        -------------------------------------------------------

        DELETE  FROM dbo.Trend_SavedRelativityCMSReimb WITH (ROWLOCK)
        WHERE   PlanYearID = @CurrentYear;

        INSERT INTO dbo.Trend_SavedRelativityCMSReimb WITH (ROWLOCK)
        	(CPS,
		PlanYearID,
		QuarterID,
		ReportingCategory,
        CostRelativity,
		UseRelativity,
		LastUpdateByID,
		LastUpdateDateTime
		)
        SELECT      CPS
                   ,PlanYearID
                   ,QuarterID
                   ,ReportingCategory
                   ,dbo.Trend_fnSafeDivide (SUM (MemberMonths * Relativity), SUM (MemberMonths), 1) AS CostRelativity
                   ,1 AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #CountyData

        GROUP BY    CPS
                   ,PlanYearID
                   ,QuarterID
                   ,ReportingCategory;
    END;
GO
