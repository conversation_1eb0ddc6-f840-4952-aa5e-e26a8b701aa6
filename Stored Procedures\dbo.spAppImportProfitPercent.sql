SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Description:	Stored procedure responsible for inserting the profit percernt data into SavedPlanAssumptions
-- and update reprice flag in SaveForecastSetup
-- PARAMETERS:  
-- Input:  
--
-- Output:  
--  
-- TABLES:  
-- Read:
--	   ImportDataStaging
--     
-- Write:

--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION        CHANGES MADE                                                      DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-Dec-04         1          Initial Version                                                  Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------  
-- =======================================================================================================================
CREATE PROCEDURE [dbo].[spAppImportProfitPercent]
(
	 @StageId VARCHAR(100)
)
AS
BEGIN
SET NOCOUNT ON;

		DECLARE @jsonData VARCHAR(MAX);
		DECLARE @UserId VARCHAR(7) ;
		DECLARE @PlanYear INT;
		SELECT @jsonData = JsonData, @UserId = UserId
		FROM dbo.ImportDataStaging WITH (NOLOCK)
		WHERE StageId = @StageId;

		SELECT @PlanYear = dbo.fnGetBidYear();


						
 /***************Import script for the SavedPlanAssumptions Table*****************************/		
		DECLARE @tbl__importData TABLE
		(
		    [PlanYear] [Int] NOT NULL,
			[ForecastID] [Int] NOT NULL,
			[ProfitPercent] [decimal] (10, 8) NOT NULL,
			[LastUpdateByID] CHAR(7) NOT NULL,
			[LastUpdateDateTime] DATETIME NOT NULL
		);

		INSERT INTO @tbl__importData 
		SELECT
		    @PlanYear,
            ForecastID,
			ProfitPercent,
			@UserId,
			GETDATE()	
		FROM 
		OPENJSON(@jsonData, '$.ProfitPercent')
		WITH
	    (
			[ForecastID] [Int],
		    [ProfitPercent] [decimal] (10, 8)
		);

	
	    MERGE INTO [dbo].[SavedPlanAssumptions] AS target
		USING @tbl__importData AS source
		ON (	
				target.ForecastID = source.ForecastID
		   )
		WHEN MATCHED THEN 
			UPDATE	
			SET 	
				target.ProfitPercent = source.ProfitPercent	
		WHEN NOT MATCHED THEN 
		INSERT 
		(
			PlanYearID,
            ForecastID,
            IsMAPD,
			ExpensePercent,
            ProfitPercent,
            RxBasicPremium,
            RxSuppPremium,
            ExpensePMPM,
            SecondaryPayerAdjustment,
            SalesAndMarketingPercent,
            DirectAdminPercent,
            IndirectAdminPercent, 
			SalesMembership,
			QualityInitiatives,
			TaxesAndFees,
			BidBenefitString
		)
		VALUES
		(  source.PlanYear, 
		   source.ForecastID,
		   (SELECT TOP(1) IsMAPD FROM dbo.SavedPlanHeader WHERE ForecastID = source.ForecastID AND IsHidden = 0 ORDER BY ForecastID), 
			0, --ExpensePercent
            ProfitPercent,
            0, --RxBasicPremium
            0, --RxSuppPremium
            0, --ExpensesPMPM
            0, --SecondaryPayerAdjustment
            0, --SalesAndMarketingPercent,
            0, --DirectAdminPercent,
            0,  --IndirectAdminPercent 
			0, --@SalesMembership,
			0, --QualityInitiatives
			0, --TaxesAndFees
			NULL --BidBenefitString
		 ); 


		MERGE INTO dbo.SavedForecastSetup AS target
		USING @tbl__importData AS source
		ON (
			 target.ForecastID = source.ForecastID
		   )
		WHEN MATCHED THEN
		UPDATE 
			   SET 
			   target.IsToReprice = 1,
			   target.LastUpdateByID = source.LastUpdateByID,
			   target.LastUpdateDateTime = source.LastUpdateDateTime;

	  DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;
GO