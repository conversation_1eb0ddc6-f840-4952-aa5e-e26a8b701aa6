SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: zzfnGetOOPC
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Apr-21
-- HEADER UPDATED: 2011-Apr-21
--
-- DESCRIPTION: Returns individual stay OOPC value for OOPC calculation
--
-- PARAMETERS:
--  Input:
--      @MaxDays
--      @LengthOfStay
--      @CopayAmt
--      @Unknown (currently there is an unknown parameter in the model that is 0, this may change)
--  Output:
--
-- RETURNS: 
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Apr-21      1           Initial version                                                     Michael Siekerka
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetOOPCSNF] 
(
    @BenL1 INT,
    @DaysL1 INT,
    @BenL2 INT,
    @DaysL2 INT,
    @BenL3 INT,
    @DaysL3 INT,
    @DaysUtilized INT
)
    RETURNS INT AS
    BEGIN
        DECLARE @Total INT
        SELECT
            @Total = 
                CASE WHEN @DaysUtilized <= @DaysL1 
                    THEN @DaysUtilized * @BenL1
                WHEN @DaysUtilized <= @DaysL2
                    THEN @DaysL1 * @BenL1 + (@DaysUtilized - @DaysL1) * @BenL2
                    ELSE @DaysL1 * @BenL1 + (@DaysL2 - @DaysL1) * @BenL2 + (@DaysUtilized - @DaysL2) * @BenL3
                END

        RETURN @Total
    END
GO
