SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- PROCEDURE NAME: spDashboardReportBPTCreationMAAUIPBI2

-- DESCRIPTION: This SP returns extract for "Create BPT" user action from AppLogs table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--
-- TABLES:
--    Read:
--      [dbo].[DashboardReportAppLogs]
--		
-- Example 
-- Exec [dbo].[spDashboardReportBPTCreationMAAUIPBI2] 0, NULL

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Sep-15			1			Initial Version                                                                         Chaitanya Durga
-- 2024-Feb-20			2			Changed "Message" column logic                                                          Sheetal Patil
---------------------------------------------------------------------------------------------------------------------------------------------------------
 

CREATE PROC [dbo].[spDashboardReportBPTCreationMAAUIPBI2]
(@LastSuccessfullRunLogid INT
,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


DECLARE @XLastSuccessfullRunLogid  INT = @LastSuccessfullRunLogid



IF (SELECT  OBJECT_ID ('tempdb..#AppLogTemp')) IS NOT NULL
BEGIN 
DROP TABLE #AppLogTemp
END

;WITH ConstantsValues 
AS
(  
SELECT 'Finished executing POST: Bpt/ActuarialGenerateBpt' AS ActuarialGenerateBpt,
'Finished executing POST: Bpt/GetZipFolderPath' AS ZipFolder,
'Finished executing POST: Bpt/DownloadZipFolder' AS  DownloadZipFolder,
'Executing POST: Bpt/ActuarialGenerateBpt' AS ExecActuarialGenerateBpt,
'Finished executing ' AS Finishedexecuting 
)

SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
	  ,characterEvenCount
	  ,FormattedMesssage
INTO #AppLogTemp
FROM (SELECT
[LogID]
      ,[Date]
      ,[User]
	  ,CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END AS [Message]
,characterEvenCount
,FormattedMesssage
FROM (
SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
       ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (
SELECT REPLACE(REPLACE(RIGHT(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END, 
		DATALENGTH(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END) - 
		CHARINDEX('{', CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END),0) < 5 
	THEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END),0) 
	ELSE 0 END AS  DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (SELECT CAST([LogID] AS INT) LogID
	  ,[Date]
      ,[User]
      ,[Message]
FROM [dbo].DashboardReportAppLogs WITH (NOLOCK)  CROSS APPLY ConstantsValues
WHERE [LogID] > @XLastSuccessfullRunLogid
AND ([Message] LIKE '%'+ConstantsValues.ActuarialGenerateBpt+'%' OR [Message] LIKE '%'+ConstantsValues.ZipFolder+'%' 
OR [Message] LIKE '%'+ConstantsValues.DownloadZipFolder+'%' 
OR [Message] LIKE '%'+ConstantsValues.ExecActuarialGenerateBpt+'%'
)) AS t1 ) AS t2 ) AS t3 ) AS t4 ) AS t5

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction1')) IS NOT NULL 
BEGIN 
DROP TABLE #AppLogTempAction1
END


;WITH ConstantsValues 
AS
(  
SELECT 
'Finished executing ' AS Finishedexecuting ,
' Bpt/ActuarialGenerateBpt' AS BptActuarialGenerateBpt,
'Executing ' AS Executing,
'BPT Creation MAAUI' AS BPTCreationMAAUI
)

SELECT
ConstantsValues.BPTCreationMAAUI AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, ConstantsValues.BPTCreationMAAUI AS [Type]
, plans AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction1
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM 
(
SELECT UserId
, RunDate
, ExecutionTime
	  ,characterEvenCount
	  ,REPLACE(FormattedMesssage, 'forecastIds', 'forecastId') AS FormattedMesssage 	 
	  FROM
	  (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK) CROSS APPLY ConstantsValues
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%'+ConstantsValues.Finishedexecuting+'%:'+ConstantsValues.BptActuarialGenerateBpt+'%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%'+ConstantsValues.Finishedexecuting+'%:'+ConstantsValues.BptActuarialGenerateBpt+'%'
WHERE a.[Message] LIKE Executing +'%:'+BptActuarialGenerateBpt+'%'
) AS t01) AS t02 ) AS t05 ) t04 
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(MAX) '$.forecastId'
)
) AS t03 CROSS APPLY ConstantsValues

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction2')) IS NOT NULL
BEGIN 
DROP TABLE #AppLogTempAction2
END

;WITH ConstantsValues 
AS
(  
SELECT 
'Finished executing ' AS Finishedexecuting ,
'Executing ' AS Executing,
' Bpt/DownloadZipFolder' AS BptDownloadZipFolder,
'BPT Creation MAAUI' AS BPTCreationMAAUI,
' Bpt/ActuarialGenerateBptBatch' AS ActuarialGenerateBptBatch
)
SELECT
ConstantsValues.BPTCreationMAAUI AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, ConstantsValues.BPTCreationMAAUI AS [Type]
, plans AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction2
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM 
(
SELECT UserId
, RunDate
, ExecutionTime
	  ,characterEvenCount
	  ,REPLACE(FormattedMesssage, 'forecastIds', 'forecastId') AS FormattedMesssage 	 
	  FROM
	  (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK) CROSS APPLY ConstantsValues
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%'+ConstantsValues.Finishedexecuting+'%:'+ConstantsValues.BptDownloadZipFolder+'%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%'+ConstantsValues.Finishedexecuting+'%:'+ConstantsValues.BptDownloadZipFolder+'%'
WHERE a.[Message] LIKE ConstantsValues.Executing+'%:'+ConstantsValues.ActuarialGenerateBptBatch+'%'
) AS t01) AS t02 ) AS t05 ) t04 
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(MAX) '$.forecastId'
)
) AS t03 CROSS APPLY ConstantsValues

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction3')) IS NOT NULL 
BEGIN 
DROP TABLE #AppLogTempAction3
END


;WITH ConstantsValues 
AS
(  
SELECT 
'Finished executing ' AS Finishedexecuting ,
'Executing ' AS Executing,
' Bpt/GetZipFolderPath' AS BptGetZipFolderPath,
'BPT Creation MAAUI' AS BPTCreationMAAUI,
' Bpt/ActuarialGenerateBptBatch' AS ActuarialGenerateBptBatch
)

SELECT
ConstantsValues.BPTCreationMAAUI AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, ConstantsValues.BPTCreationMAAUI AS [Type]
, plans AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(plans) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction3
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM 
(
SELECT UserId
, RunDate
, ExecutionTime
	  ,characterEvenCount
	  ,REPLACE(FormattedMesssage, 'forecastIds', 'forecastId') AS FormattedMesssage 	 
	  FROM
	  (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK) CROSS APPLY ConstantsValues
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON ap.LogID < a.logid
AND ap.LogID = (SELECT MAX(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE app.LogID < a.logid AND app.[Message] LIKE '%'+ConstantsValues.Finishedexecuting+'%:'+ConstantsValues.BptGetZipFolderPath+'%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%'+ConstantsValues.Finishedexecuting+'%:'+ConstantsValues.BptGetZipFolderPath+'%'
WHERE a.[Message] LIKE ConstantsValues.Executing+'%:'+ConstantsValues.ActuarialGenerateBptBatch+'%'
) AS t01) AS t02 ) AS t05 ) t04 
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(MAX) '$.forecastId'
)
) AS t03 CROSS APPLY ConstantsValues

SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],SUM([ExecutionTime]) AS [ExecutionTime] ,[characterEvenCount],[ErrorCount],[RunEndDate],[ErrorMessage] FROM 
(
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction1 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction2 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction3 WITH (NOLOCK)
) t
GROUP BY [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[characterEvenCount],[ErrorCount],[RunEndDate],[ErrorMessage]



END
GO
