SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  <PERSON>
-- Create date: 2024-Jul-16
-- Description: spAppImportRelatedPartyImportStagedData Related Party and RX Rebate Selections Import using staging table 
--      
--      
-- PARAMETERS:      
-- Input:    @StageId,  @IsFullReload  
    
-- TABLES:    
-- Read:        ImportDataStaging
--              
--            
-- Write:		SavedPlanAddedBenefits   
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION   CHANGES MADE														DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2024-Jul-16		1	     Initial version.													Kiran Kola	
-- 2025-Mar-31		2		 Delete which have BidServiceCatID = 35 - i.e. related party only 	Vikrant Bagal
----------------------------------------------------------------------------------------------------------------------
-- ======================================================================================================================
CREATE PROCEDURE [dbo].[spAppImportRelatedPartyImportStagedData]
(
		@StageId VARCHAR(100)
	
)
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;
BEGIN TRY

	DECLARE @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear()
	DECLARE @LastUpdate DATETIME
    SET @LastUpdate = GETDATE() 

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;
		
    DECLARE @tbl__importData TABLE
    (
        ForecastID INT,
        AddedBenefitTypeID INT,
		AddedBenefitName VARCHAR(50),
        INAddedBenefitDescription [varchar] (2000) NULL,
		[INAddedBenefitAllowed] [decimal] (6, 2) NULL,
		[INAddedBenefitUtilization] [decimal] (14, 6) NULL,
		[INAddedBenefitCostShare] [decimal] (14, 6) NULL,
		[OONAddedBenefitDescription] [varchar] (2000) NULL,
		[OONAddedBenefitAllowed] [decimal] (6, 2) NULL,
		[OONAddedBenefitUtilization] [decimal] (14, 6) NULL,
		[OONAddedBenefitCostShare] [decimal] (14, 6) NULL,
		[BidServiceCatID] [smallint] NULL,
		[IsValueAdded] [bit] NOT NULL,
		[IsNetwork] [bit] NOT NULL,
		[IsHidden] [bit] NOT NULL,
		IsOverride [bit] NULL,
		LastUpdateByID CHAR(7),
        LastUpdateDateTime DATETIME
    );

	 BEGIN TRANSACTION 
INSERT INTO @tbl__importData (
				ForecastID,
				AddedBenefitTypeID,
				AddedBenefitName, 
				INAddedBenefitDescription,
				INAddedBenefitAllowed,
				INAddedBenefitUtilization,
				INAddedBenefitCostShare,
				OONAddedBenefitDescription,
				OONAddedBenefitAllowed,
				OONAddedBenefitUtilization,
				OONAddedBenefitCostShare,
				BidServiceCatID,
				IsValueAdded,
				IsNetwork,
				IsHidden,
				IsOverride,
				LastUpdateByID, 
				LastUpdateDateTime)
SELECT 
    j.ForecastID,
	labt.AddedBenefitTypeID,
    labt.AddedBenefitName,
	labt.INAddedBenefitDescription,
				labt.INAddedBenefitAllowed,
				labt.INAddedBenefitUtilization,
				labt.INAddedBenefitCostShare,
				labt.OONAddedBenefitDescription,
				labt.OONAddedBenefitAllowed,
				labt.OONAddedBenefitUtilization,
				labt.OONAddedBenefitCostShare,
				labt.BidServiceCatID,
				labt.IsValueAdded,
				labt.IsNetwork,
				0,--IsHidden,
				0, --IsOverride,
    @UserId,
    @LastUpdate
    
FROM
    OPENJSON(@jsonData, '$.RelatedPartyExtract') 
WITH (
	  ForecastID INT, AddedBenefitName VARCHAR(50)) AS j
	  LEFT JOIN dbo.LkpIntAddedBenefitType labt ON j.AddedBenefitName = labt.AddedBenefitName AND  IsEnabled=1 

	  -- delete which have BidServiceCatID = 35 - i.e. related party only 
	  Delete From dbo.SavedPlanAddedBenefits where ForecastID in (select Distinct ForecastID from @tbl__importData)	and BidServiceCatID = 35
	

	MERGE INTO dbo.SavedPlanAddedBenefits AS target
    USING @tbl__importData AS source
    ON (
           target.ForecastID = source.ForecastID AND target.AddedBenefitTypeID =source.AddedBenefitTypeID AND target.AddedBenefitName = source.AddedBenefitName
       )
    WHEN MATCHED THEN
        UPDATE SET
				PlanYearID = @PlanYearID,
				INAddedBenefitDescription = source.INAddedBenefitDescription, 
				INAddedBenefitAllowed = source.INAddedBenefitAllowed,
				INAddedBenefitUtilization = source.INAddedBenefitUtilization,
				INAddedBenefitCostShare= source.INAddedBenefitCostShare,
				OONAddedBenefitDescription= source.OONAddedBenefitDescription,
				OONAddedBenefitAllowed = source.OONAddedBenefitAllowed ,
				OONAddedBenefitUtilization =source.OONAddedBenefitUtilization,
				OONAddedBenefitCostShare =source.OONAddedBenefitCostShare,
				BidServiceCatID=source.BidServiceCatID,
				IsValueAdded=source.IsValueAdded,
				IsNetwork=source.IsNetwork,
				IsHidden=source.IsHidden,
				IsOverride=source.IsOverride,
                   target.LastUpdateByID = source.LastUpdateByID,
                   target.LastUpdateDateTime = source.LastUpdateDateTime
    WHEN NOT MATCHED BY TARGET THEN
        INSERT
        (
			PlanYearID,
            ForecastID,
			AddedBenefitTypeID,
            AddedBenefitName,
			INAddedBenefitDescription,
				INAddedBenefitAllowed,
				INAddedBenefitUtilization,
				INAddedBenefitCostShare,
				OONAddedBenefitDescription,
				OONAddedBenefitAllowed,
				OONAddedBenefitUtilization,
				OONAddedBenefitCostShare,
				BidServiceCatID,
				IsValueAdded,
				IsNetwork,
				IsHidden,
				IsOverride,
			
            LastUpdateByID,
            LastUpdateDateTime
           
        )
        VALUES
        (@PlanYearID,source.ForecastID,source.AddedBenefitTypeID, source.AddedBenefitName,
		source.INAddedBenefitDescription,
		source.INAddedBenefitAllowed,
				source.INAddedBenefitUtilization,
				source.INAddedBenefitCostShare,
				source.OONAddedBenefitDescription,
				source.OONAddedBenefitAllowed,
				source.OONAddedBenefitUtilization,
				source.OONAddedBenefitCostShare,
				source.BidServiceCatID,
				source.IsValueAdded,
				source.IsNetwork,
				source.IsHidden,
				source.IsOverride,
				source.LastUpdateByID,
				source.LastUpdateDateTime);

		UPDATE s
SET IsToReprice = 1,
    LastUpdateByID = @UserID,
    LastUpdateDateTime = @LastUpdate
FROM dbo.SavedForecastSetup AS s
JOIN @tbl__importData AS f ON s.ForecastID = f.ForecastID;	
COMMIT
  
	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END TRY
BEGIN CATCH    
 IF @@TRANCOUNT > 0
 Begin
                ROLLBACK;
 End

     DECLARE @ErrorMessage NVARCHAR(4000);           
          DECLARE @ErrorSeverity INT;           
          DECLARE @ErrorState INT;           
          DECLARE @ErrorException NVARCHAR(4000);           
          DECLARE @errSrc      VARCHAR(MAX) =ISNULL(ERROR_PROCEDURE(), 'SQL'),           
                  @currentdate DATETIME=GETDATE()           
    
          SELECT @ErrorMessage = ERROR_MESSAGE(),           
                 @ErrorSeverity = ERROR_SEVERITY(),           
                 @ErrorState = ERROR_STATE(),           
                 @ErrorException = 'Line Number :'           
                                   + CAST(ERROR_LINE() AS VARCHAR)           
         + ' .Error Severity :'           
                                   + CAST(@ErrorSeverity AS VARCHAR)           
                                   + ' .Error State :'           
                                   + CAST(@ErrorState AS VARCHAR)           
  
    EXEC dbo.Spappaddlogentry           
            @currentdate,           
            '',           
            'ERROR',           
            @errSrc,           
            @ErrorMessage,           
            @ErrorException,           
            @UserId    
                 
      END CATCH;       


END
GO


