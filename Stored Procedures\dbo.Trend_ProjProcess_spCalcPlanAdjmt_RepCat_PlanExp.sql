/****** Object:  StoredProcedure [dbo].[Trend_ProjProcess_spCalcPlanAdjmt_RepCat_PlanExp]    Script Date: 12/18/2022 11:26:14 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcPlanAdjmt_RepCat_PlanExp
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: APR-07-2020
--
-- DESCRIPTION:   This procedure calculates plan level adjustments to better align actual experience. 
--                Produces experience (Rate Type=1) adjustment only, no manual (Rate Type=2) adjustment.
--              
--              
-- PARAMETERS:
--  Input  :	@PlanInfoIDList
--              @WriteIndicator
--				@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_ProjProcess_CalcPlanTrends_RepCat
--					Trend_ProjProcess_CalcPlanTrends_BenCat
--					Trend_Data_CalcBaseCostAndUse
--					Trend_Data_CalcBaseMembership
--					Trend_ProjProcess_CalcSeasonalityAspt_Use
--					Trend_ProjProcess_CalcSeasonalityAspt_Allowed			
--					Trend_SavedComponentInfo
--					LkpIntPlanYear
--					LkpProjectionVersion
--					LkpIntBenefitCategory
--					SavedPlanInfo
--					
--          Write:  Trend_ProjProcess_CalcPlanTrends_RepCat
--                  Trend_ProjProcess_CalcPlanTrends_RepCat_Staged
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS:	Trend_fnCalcStringToTable
--				Trend_fnSafeDivide
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- APR-07-2020      1           Initial Version                                                     Jake Lewis
-- MAY-06-2020		2			Update BenefitCategory with BenefitCategoryID						Craig Nielsen
-- JUL-06-2020		3			Set CostAdjustment, UseAdjustment = 0 for Part B Rx Pharmacy		Jake Lewis
-- NOV-11-2020		4			Applied fix re: calculating Allowed Adjustments and					Jake Lewis
--								and backing into Cost Adjustments
-- NOV-19-2020      5           Include NOLOCK & ROWLOCK                                            Manisha Tyagi
-- DEC-06-2020      6           Batch delete implemented											Surya Murthy
-- DEC-09-2020		7			Remove references to Trend_ProjProcess_CalcSeasonalityAspt_Cost		Jake Lewis
-- MAR-03-2020		8			Turn PlanExpAdj on/off via SavedPlanInfo.IsCalcPlanExpAdj			Jake Lewis
-- OCT-17-2022		9			Added Column names and NewID() column in insert statement			Sheetal Patil
-- DEC-16-2022		10			Split RepBenCat and RepCat temp tables to correct duplicate issue	Michael Manes
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcPlanAdjmt_RepCat_PlanExp]
@PlanInfoIDList  VARCHAR(MAX) = NULL
,@WriteIndicator TINYINT      = NULL    -- 1 for National Analytics, 2 for Market Support
,@LastUpdateByID CHAR(13)

AS

    BEGIN

        SET NOCOUNT ON;

        -- Declare variables
        DECLARE @BidYear INT;
        SET @BidYear = (SELECT  PlanYearID
                        FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                        WHERE   IsBidYear = 1);

        DECLARE @CurrentYear INT;
        SET @CurrentYear = (SELECT  PlanYearID
                            FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                            WHERE   IsCurrentYear = 1);

        DECLARE @BaseYear INT;
        SET @BaseYear = (SELECT PlanYearID
                         FROM   dbo.LkpIntPlanYear WITH (NOLOCK)
                         WHERE  IsExperienceYear = 1);

        DECLARE @LastCurrentYearQuarter INT;
        SET @LastCurrentYearQuarter = (SELECT   LastCurrentYearQuarter
                                       FROM     dbo.LkpProjectionVersion WITH (NOLOCK)
                                       WHERE    IsLiveProjection = 1);

        -- Plan Info
        IF (SELECT  OBJECT_ID ('tempdb..#PlanInfo')) IS NOT NULL
            DROP TABLE #PlanInfo;
        SELECT  DISTINCT
                PlanInfoID
               ,CPS
               ,PlanYear AS PlanYearID
        INTO    #PlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear
                AND IsHidden = 0
                AND IsOffMAModel = 'No'
                AND Region NOT IN ('Unmapped')
                AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@PlanInfoIDList, ',', 1) )
                     OR @PlanInfoIDList IS NULL);

        -- Reporting and Benefit Category
        IF (SELECT  OBJECT_ID ('tempdb..#RepBenCat')) IS NOT NULL DROP TABLE #RepBenCat;
        SELECT  DISTINCT
                ReportingCategory
               ,BenefitCategoryID
        INTO    #RepBenCat
        FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
        WHERE   ReportingCategory <> 'NULL';

        -- Reporting Category
        IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
        SELECT  DISTINCT
                ReportingCategory
        INTO    #RepCat
        FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
        WHERE   ReportingCategory <> 'NULL';

        -- PlanYearID, MonthID
        IF (SELECT  OBJECT_ID ('tempdb..#YearAndMonth')) IS NOT NULL
            DROP TABLE #YearAndMonth;
        SELECT  DISTINCT
                number AS MonthID
               ,b.PlanYearID
        INTO    #YearAndMonth
        FROM    master..spt_values a
        JOIN    dbo.LkpIntPlanYear b WITH (NOLOCK)
          ON 1 = 1
        WHERE   number BETWEEN 1 AND 12
                AND (b.PlanYearID = @BaseYear
                     OR (b.PlanYearID = @CurrentYear
                         AND a.number <= @LastCurrentYearQuarter * 3));

        -- ComponentReporting
        IF (SELECT  OBJECT_ID ('tempdb..#ComponentReporting')) IS NOT NULL
            DROP TABLE #ComponentReporting;
        SELECT  DISTINCT
                ComponentReporting
        INTO    #ComponentReporting
        FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
        WHERE   ComponentReporting NOT IN ('PlanExpAdj')
                AND ComponentReporting NOT LIKE '%ActAdj%';


        -------------------- PROJECTED TREND --------------------

        ---------- STEP 0: COMPONENT TRENDS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#ComponentTrend')) IS NOT NULL
            DROP TABLE #ComponentTrend;
        SELECT      DISTINCT
                    a.PlanInfoID
                   ,@CurrentYear AS TrendYearID
                   ,b.ReportingCategory
                   ,b.BenefitCategoryID
                   ,EXP (SUM (LOG (1 + COALESCE (d.CostAdjustment, e.CostAdjustment, 0)))) - 1 AS CostTrend
                   ,EXP (SUM (LOG (1 + COALESCE (d.UseAdjustment, e.UseAdjustment, 0)))) - 1 AS UseTrend
        INTO        #ComponentTrend
        FROM        #PlanInfo a
        LEFT JOIN   LkpIntBenefitCategory b WITH (NOLOCK)
               ON 1 = 1
        LEFT JOIN   #ComponentReporting c
               ON 1 = 1
        LEFT JOIN   dbo.Trend_ProjProcess_CalcPlanTrends_RepCat d WITH (NOLOCK)
               ON d.PlanInfoID = a.PlanInfoID
                  AND   d.ReportingCategory = b.ReportingCategory
                  AND   @CurrentYear = d.TrendYearID
                  AND   1 = d.RateType
                  AND   d.ComponentReporting = c.ComponentReporting
        LEFT JOIN   dbo.Trend_ProjProcess_CalcPlanTrends_BenCat e WITH (NOLOCK)
               ON e.PlanInfoID = a.PlanInfoID
                  AND   e.BenefitCategoryID = b.BenefitCategoryID
                  AND   @CurrentYear = e.TrendYearID
                  AND   1 = e.RateType
                  AND   e.ComponentReporting = c.ComponentReporting
        WHERE       b.ReportingCategory IS NOT NULL
        GROUP BY    a.PlanInfoID
                   ,b.ReportingCategory
                   ,b.BenefitCategoryID
        ORDER BY    a.PlanInfoID
                   ,ReportingCategory
                   ,BenefitCategoryID;


        ---------- STEP 1: BASE DATA ----------
        IF (SELECT  OBJECT_ID ('tempdb..#BaseData')) IS NOT NULL
            DROP TABLE #BaseData;
        SELECT      DISTINCT
                    a.PlanInfoID
                   ,@BaseYear AS TrendYearID
                   ,b.ReportingCategory
                   ,b.BenefitCategoryID
                   ,SUM (COALESCE (cbcau.Allowed, 0)) AS AllowedTotal
                   ,SUM (COALESCE (cbcau.Utilization, 0)) AS UtilizationTotal
        INTO        #BaseData
        FROM        #PlanInfo a
        LEFT JOIN   LkpIntBenefitCategory b WITH (NOLOCK)
               ON 1 = 1
        LEFT JOIN   dbo.Trend_Data_CalcBaseCostAndUse cbcau WITH (NOLOCK)
               ON cbcau.PlanInfoID = a.PlanInfoID
                  AND   cbcau.BenefitCategoryID = b.BenefitCategoryID
                  AND   @BaseYear = FLOOR (cbcau.IncurredMonth / 100)
                  AND   1 = cbcau.RateType
        WHERE       b.ReportingCategory IS NOT NULL
        GROUP BY    a.PlanInfoID
                   ,b.ReportingCategory
                   ,b.BenefitCategoryID;


        ---------- STEP 2: BASE METRICS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#BaseMetrics')) IS NOT NULL
            DROP TABLE #BaseMetrics;
        SELECT      bd.PlanInfoID
                   ,bd.TrendYearID
                   ,bd.ReportingCategory
                   ,bd.BenefitCategoryID
                   ,dbo.Trend_fnSafeDivide (bd.AllowedTotal, SUM (COALESCE (cbm.MemberMonths, 0)), 0) AS AllowedMetric
                   ,dbo.Trend_fnSafeDivide (bd.AllowedTotal, bd.UtilizationTotal, 0) AS CostMetric
                   ,dbo.Trend_fnSafeDivide (bd.UtilizationTotal, SUM (COALESCE (cbm.MemberMonths, 0)), 0) * 12000 AS UseMetric
        INTO        #BaseMetrics
        FROM        #BaseData bd
        LEFT JOIN   dbo.Trend_Data_CalcBaseMembership cbm WITH (NOLOCK)
               ON cbm.PlanInfoID = bd.PlanInfoID
                  AND   FLOOR (cbm.IncurredMonth / 100) = bd.TrendYearID
                  AND   cbm.RateType = 1
        GROUP BY    dbo.Trend_fnSafeDivide (bd.AllowedTotal, bd.UtilizationTotal, 0)
                   ,bd.PlanInfoID
                   ,bd.TrendYearID
                   ,bd.ReportingCategory
                   ,bd.BenefitCategoryID
                   ,bd.AllowedTotal
                   ,bd.UtilizationTotal;


        ---------- STEP 3: PROJECTED METRICS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#ProjMetrics')) IS NOT NULL
            DROP TABLE #ProjMetrics;
        SELECT      bm.PlanInfoID
                   ,bm.TrendYearID + 1 AS TrendYearID
                   ,bm.ReportingCategory
                   ,bm.BenefitCategoryID
                   ,bm.AllowedMetric * (1 + ct.CostTrend) * (1 + ct.UseTrend) AS AllowedMetric
                   ,bm.CostMetric * (1 + ct.CostTrend) AS CostMetric
                   ,bm.UseMetric * (1 + ct.UseTrend) AS UseMetric
                   ,SUM (COALESCE (cbm.MemberMonths, 0)) AS MM
        INTO        #ProjMetrics
        FROM        #BaseMetrics bm
        LEFT JOIN   #ComponentTrend ct
               ON ct.PlanInfoID = bm.PlanInfoID
                  AND   ct.TrendYearID = bm.TrendYearID + 1
                  AND   ct.ReportingCategory = bm.ReportingCategory
                  AND   ct.BenefitCategoryID = bm.BenefitCategoryID
        LEFT JOIN   dbo.Trend_Data_CalcBaseMembership cbm WITH (NOLOCK)
               ON cbm.PlanInfoID = bm.PlanInfoID
                  AND   FLOOR (cbm.IncurredMonth / 100) = bm.TrendYearID
                  AND   cbm.RateType = 1
        GROUP BY    bm.TrendYearID + 1
                   ,bm.AllowedMetric * (1 + ct.CostTrend) * (1 + ct.UseTrend)
                   ,bm.CostMetric * (1 + ct.CostTrend)
                   ,bm.UseMetric * (1 + ct.UseTrend)
                   ,bm.PlanInfoID
                   ,bm.ReportingCategory
                   ,bm.BenefitCategoryID;


        ---------- STEP 4: PROJECTED TOTALS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#ProjTotals')) IS NOT NULL
            DROP TABLE #ProjTotals;
        SELECT      pm.PlanInfoID
                   ,pm.TrendYearID
                   ,pm.ReportingCategory
                   ,pm.BenefitCategoryID
                   ,pm.AllowedMetric * pm.MM AS AllowedTotal
                   ,pm.UseMetric * pm.MM / 12000 AS UtilizationTotal
        INTO        #ProjTotals
        FROM        #ProjMetrics pm
        GROUP BY    pm.AllowedMetric * pm.MM
                   ,pm.UseMetric * pm.MM / 12000
                   ,pm.PlanInfoID
                   ,pm.TrendYearID
                   ,pm.ReportingCategory
                   ,pm.BenefitCategoryID;


        ---------- STEP 4: TOTALS ACROSS BENEFIT CATEGORIES ----------
        IF (SELECT  OBJECT_ID ('tempdb..#BenCatTotals')) IS NOT NULL
            DROP TABLE #BenCatTotals;
        SELECT      bd.PlanInfoID
                   ,bd.ReportingCategory
                   ,SUM (COALESCE (bd.AllowedTotal, 0)) AS BaseAllowed
                   ,SUM (COALESCE (bd.UtilizationTotal, 0)) AS BaseUtilization
                   ,SUM (COALESCE (pt.AllowedTotal, 0)) AS ProjAllowed
                   ,SUM (COALESCE (pt.UtilizationTotal, 0)) AS ProjUtilization
        INTO        #BenCatTotals
        FROM        #BaseData bd
        LEFT JOIN   #ProjTotals pt
               ON pt.PlanInfoID = bd.PlanInfoID
                  AND   pt.ReportingCategory = bd.ReportingCategory
                  AND   pt.BenefitCategoryID = bd.BenefitCategoryID
        GROUP BY    bd.PlanInfoID
                   ,bd.ReportingCategory;


        ---------- STEP 5: TOTAL METRICS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#TotalMetrics')) IS NOT NULL
            DROP TABLE #TotalMetrics;
        SELECT      DISTINCT
                    bct.PlanInfoID
                   ,bct.ReportingCategory
                   ,dbo.Trend_fnSafeDivide (bct.BaseAllowed, bct.BaseUtilization, 0) AS BaseCostMetric
                   ,dbo.Trend_fnSafeDivide (bct.BaseUtilization, pm.MM, 0) * 12000 AS BaseUseMetric
                   ,dbo.Trend_fnSafeDivide (bct.ProjAllowed, bct.ProjUtilization, 0) AS ProjCostMetric
                   ,dbo.Trend_fnSafeDivide (bct.ProjUtilization, pm.MM, 0) * 12000 AS ProjUseMetric
        INTO        #TotalMetrics
        FROM        #BenCatTotals bct
        LEFT JOIN   #ProjMetrics pm
               ON pm.PlanInfoID = bct.PlanInfoID
                  AND   pm.ReportingCategory = bct.ReportingCategory;


        ---------- STEP 6: FINAL TRENDS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#FinalProjTrends')) IS NOT NULL
            DROP TABLE #FinalProjTrends;
        SELECT  tm.PlanInfoID
               ,tm.ReportingCategory
               ,dbo.Trend_fnSafeDivide (tm.ProjCostMetric, tm.BaseCostMetric, 0) - 1 AS CostTrend
               ,dbo.Trend_fnSafeDivide (tm.ProjUseMetric, tm.BaseUseMetric, 0) - 1 AS UseTrend
        INTO    #FinalProjTrends
        FROM    #TotalMetrics tm;


        -------------------- HISTORIC DATA --------------------

        ---------- STEP 0: DATA AND ASSUMPTIONS ----------

        -- Historic Allowed and Utilization (monthly)
        IF (SELECT  OBJECT_ID ('tempdb..#HistoricAllowedAndUtil')) IS NOT NULL
            DROP TABLE #HistoricAllowedAndUtil;
        SELECT      pi.PlanInfoID
                   ,rep.ReportingCategory
                   ,ym.PlanYearID
                   ,ym.MonthID
                   ,SUM (COALESCE (cbcan.Allowed, 0)) AS Allowed
                   ,SUM (COALESCE (cbcan.Utilization, 0)) AS Utilization
        INTO        #HistoricAllowedAndUtil
        FROM        #PlanInfo pi
       INNER JOIN   #RepBenCat rep
               ON 1 = 1
       INNER JOIN   #YearAndMonth ym
               ON 1 = 1
        LEFT JOIN   dbo.Trend_Data_CalcBaseCostAndUse cbcan WITH (NOLOCK)
               ON cbcan.PlanInfoID = pi.PlanInfoID
                  AND   cbcan.BenefitCategoryID = rep.BenefitCategoryID
                  AND   cbcan.IncurredMonth = CONCAT (ym.PlanYearID, RIGHT('00' + CAST(ym.MonthID AS VARCHAR(2)), 2))
                  AND   cbcan.RateType = 1
        GROUP BY    pi.PlanInfoID
                   ,rep.ReportingCategory
                   ,ym.PlanYearID
                   ,ym.MonthID;

        -- Historic Allowed and Utilization (totals)
        IF (SELECT  OBJECT_ID ('tempdb..#HistoricAllowedAndUtilTotals')) IS NOT NULL
            DROP TABLE #HistoricAllowedAndUtilTotals;
        SELECT      pi.PlanInfoID
                   ,rep.ReportingCategory
                   ,ym.PlanYearID
                   ,SUM (COALESCE (cbcan.Allowed, 0)) AS Allowed
                   ,SUM (COALESCE (cbcan.Utilization, 0)) AS Utilization
        INTO        #HistoricAllowedAndUtilTotals
        FROM        #PlanInfo pi
       INNER JOIN   #RepBenCat rep
               ON 1 = 1
       INNER JOIN   #YearAndMonth ym
               ON 1 = 1
        LEFT JOIN   dbo.Trend_Data_CalcBaseCostAndUse cbcan WITH (NOLOCK)
               ON cbcan.PlanInfoID = pi.PlanInfoID
                  AND   cbcan.BenefitCategoryID = rep.BenefitCategoryID
                  AND   cbcan.IncurredMonth = CONCAT (ym.PlanYearID, RIGHT('00' + CAST(ym.MonthID AS VARCHAR(2)), 2))
                  AND   cbcan.RateType = 1
        GROUP BY    pi.PlanInfoID
                   ,rep.ReportingCategory
                   ,ym.PlanYearID;

        -- Add membership and seasonaltiy assumptions to historic allowed and utilization
        IF (SELECT  OBJECT_ID ('tempdb..#HistoricData')) IS NOT NULL
            DROP TABLE #HistoricData;
        SELECT      au.PlanInfoID
                   ,au.ReportingCategory
                   ,au.PlanYearID
                   ,au.MonthID
                   ,SUM (COALESCE (au.Allowed, 0)) AS Allowed
                   ,SUM (COALESCE (au.Utilization, 0)) AS Utilization
                   ,SUM (COALESCE (cbm.MemberMonths, 0)) AS MM
        INTO        #HistoricData
        FROM        #HistoricAllowedAndUtil au
        LEFT JOIN   dbo.Trend_Data_CalcBaseMembership cbm WITH (NOLOCK)
               ON cbm.PlanInfoID = au.PlanInfoID
                  AND   cbm.IncurredMonth = CONCAT (au.PlanYearID, RIGHT('00' + CAST(au.MonthID AS VARCHAR(2)), 2))
                  AND   cbm.RateType = 1
        GROUP BY    au.PlanInfoID
                   ,au.ReportingCategory
                   ,au.PlanYearID
                   ,au.MonthID;

        -- Add membership and seasonaltiy assumptions to historic allowed and utilization totals
        IF (SELECT  OBJECT_ID ('tempdb..#HistoricDataTotals')) IS NOT NULL
            DROP TABLE #HistoricDataTotals;
        SELECT      au.PlanInfoID
                   ,au.ReportingCategory
                   ,au.PlanYearID
                   ,SUM (COALESCE (au.Allowed, 0)) AS Allowed
                   ,SUM (COALESCE (au.Utilization, 0)) AS Utilization
                   ,SUM (COALESCE (cbm.MemberMonths, 0)) AS MM
        INTO        #HistoricDataTotals
        FROM        #HistoricAllowedAndUtil au
        LEFT JOIN   dbo.Trend_Data_CalcBaseMembership cbm WITH (NOLOCK)
               ON cbm.PlanInfoID = au.PlanInfoID
                  AND   cbm.IncurredMonth = CONCAT (au.PlanYearID, RIGHT('00' + CAST(au.MonthID AS VARCHAR(2)), 2))
                  AND   cbm.RateType = 1
        GROUP BY    au.PlanInfoID
                   ,au.ReportingCategory
                   ,au.PlanYearID;

        -- Calculate historic metrics
        IF (SELECT  OBJECT_ID ('tempdb..#HistoricMetrics')) IS NOT NULL
            DROP TABLE #HistoricMetrics;
        SELECT  hd.PlanInfoID
               ,hd.ReportingCategory
               ,hd.PlanYearID
               ,hd.MonthID
               ,dbo.Trend_fnSafeDivide (hd.Allowed, hd.MM, 0) AS AllowedMetric
               ,dbo.Trend_fnSafeDivide (hd.Allowed, hd.Utilization, 0) AS CostMetric
               ,dbo.Trend_fnSafeDivide (hd.Utilization, hd.MM, 0) * 12000 AS UseMetric
        INTO    #HistoricMetrics
        FROM    #HistoricData hd;

        -- Calculate historic metrics on totals
        IF (SELECT  OBJECT_ID ('tempdb..#HistoricMetricsTotals')) IS NOT NULL
            DROP TABLE #HistoricMetricsTotals;
        SELECT  hd.PlanInfoID
               ,hd.ReportingCategory
               ,hd.PlanYearID
               ,dbo.Trend_fnSafeDivide (hd.Allowed, hd.MM, 0) AS AllowedMetric
               ,dbo.Trend_fnSafeDivide (hd.Allowed, hd.Utilization, 0) AS CostMetric
               ,dbo.Trend_fnSafeDivide (hd.Utilization, hd.MM, 0) * 12000 AS UseMetric
        INTO    #HistoricMetricsTotals
        FROM    #HistoricDataTotals hd;

        -- Membership Relativities
        IF (SELECT  OBJECT_ID ('tempdb..#MemRelativities')) IS NOT NULL
            DROP TABLE #MemRelativities;
        SELECT      DISTINCT
                    hd.PlanInfoID
                   ,hd.PlanYearID
                   ,hd.MonthID
                   ,dbo.Trend_fnSafeDivide (hd.MM, (hdt.MM * 1.0 / 12), 1) AS Relativity
                   ,alls.SeasonalityFactor AS AllowedSeasonality
                   ,us.SeasonalityFactor AS UseSeasonality
        INTO        #MemRelativities
        FROM        #HistoricData hd
        LEFT JOIN   #HistoricDataTotals hdt
               ON hdt.PlanInfoID = hd.PlanInfoID
                  AND   hdt.PlanYearID = hd.PlanYearID
                  AND   hdt.ReportingCategory = hd.ReportingCategory
        LEFT JOIN   dbo.Trend_ProjProcess_CalcSeasonalityAspt_Use us WITH (NOLOCK)
               ON us.MonthID = hd.MonthID
        LEFT JOIN   dbo.Trend_ProjProcess_CalcSeasonalityAspt_Allowed alls WITH (NOLOCK)
               ON alls.MonthID = hd.MonthID
        WHERE       hd.PlanYearID = @BaseYear;

        -- Membership calibration factors
        IF (SELECT  OBJECT_ID ('tempdb..#MemCalibrationFactors')) IS NOT NULL
            DROP TABLE #MemCalibrationFactors;
        SELECT      mr.PlanInfoID
                   ,POWER (SUM (mr.Relativity * mr.AllowedSeasonality) / 12, -1) AS AllowedCalibrationFactor
                   ,POWER (SUM (mr.Relativity * mr.UseSeasonality) / 12, -1) AS UseCalibractionFactor
        INTO        #MemCalibrationFactors
        FROM        #MemRelativities mr
        GROUP BY    mr.PlanInfoID;


        ---------- STEP 1: PROJECTION WITHOUT ACTUALS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#ProjWithoutActuals')) IS NOT NULL
            DROP TABLE #ProjWithoutActuals;
        SELECT      hmt.PlanInfoID
                   ,hmt.ReportingCategory
                   ,hmt.PlanYearID
                   ,hmt.AllowedMetric * (1 + pt.CostTrend) * (1 + pt.UseTrend) AS AllowedFullYear
                   ,hmt.CostMetric * (1 + pt.CostTrend) AS CostFullYear
                   ,hmt.UseMetric * (1 + pt.UseTrend) AS UseFullYear
                   ,mr.MonthID
                   ,mr.AllowedSeasonality
                   ,mr.UseSeasonality
                   ,hmt.AllowedMetric * (1 + pt.CostTrend) * (1 + pt.UseTrend) * mr.AllowedSeasonality
                    * mcf.AllowedCalibrationFactor AS AllowedProj
                   ,hmt.UseMetric * (1 + pt.UseTrend) * mr.UseSeasonality * mcf.UseCalibractionFactor AS UseProj
                   ,hm.CostMetric
                   ,hm.UseMetric
                   ,hma.AllowedMetric AS ActualAllowed
                   ,hma.CostMetric AS ActualCost
                   ,hma.UseMetric AS ActualUse
        INTO        #ProjWithoutActuals
        FROM        #HistoricMetricsTotals hmt
        LEFT JOIN   #HistoricMetrics hm
               ON hm.PlanInfoID = hmt.PlanInfoID
                  AND   hm.PlanYearID = hmt.PlanYearID
                  AND   hm.ReportingCategory = hmt.ReportingCategory
        LEFT JOIN   #HistoricMetrics hma
               ON hma.PlanInfoID = hm.PlanInfoID
                  AND   hma.ReportingCategory = hm.ReportingCategory
                  AND   hma.PlanYearID - 1 = hm.PlanYearID
                  AND   hma.MonthID = hm.MonthID
        LEFT JOIN   #FinalProjTrends pt
               ON pt.PlanInfoID = hmt.PlanInfoID
                  AND   pt.ReportingCategory = hmt.ReportingCategory
        LEFT JOIN   #MemRelativities mr
               ON mr.PlanInfoID = hmt.PlanInfoID
                  AND   mr.PlanYearID = hmt.PlanYearID
                  AND   mr.MonthID = hm.MonthID
        LEFT JOIN   #MemCalibrationFactors mcf
               ON mcf.PlanInfoID = hmt.PlanInfoID
        WHERE       hmt.PlanYearID = @BaseYear;


        ---------- STEP 2: PROJECTION WITH ACTUALS ----------
        IF (SELECT  OBJECT_ID ('tempdb..#ProjWithActuals')) IS NOT NULL
            DROP TABLE #ProjWithActuals;
        SELECT      DISTINCT
                    pwoa.PlanInfoID
                   ,pwoa.ReportingCategory
                   ,pwoa.PlanYearID + 1 AS PlanYearID
                   ,pwoa.MonthID
                   ,COALESCE (pwoa.ActualAllowed, pwoa.AllowedProj) AS AllowedMetric
                   ,COALESCE (pwoa.ActualUse, pwoa.UseProj) AS UseMetric
                   ,hd.MM
        INTO        #ProjWithActuals
        FROM        #ProjWithoutActuals pwoa
        LEFT JOIN   #HistoricData hd
               ON hd.PlanInfoID = pwoa.PlanInfoID
                  AND   hd.PlanYearID = pwoa.PlanYearID
                  AND   hd.MonthID = pwoa.MonthID;


        ---------- STEP 3: CALCULATE DESIRED TREND ----------
        IF (SELECT  OBJECT_ID ('tempdb..#FinalTrends')) IS NOT NULL
            DROP TABLE #FinalTrends;
        SELECT      pwa.PlanInfoID
                   ,pwa.ReportingCategory
                   ,pwa.PlanYearID
                   ,ROUND (
                    dbo.Trend_fnSafeDivide (
                    dbo.Trend_fnSafeDivide (
                    dbo.Trend_fnSafeDivide (SUM (pwa.AllowedMetric * pwa.MM), SUM (pwa.MM), 0), hmt.AllowedMetric, 0)
                   ,(1 + pt.CostTrend) * (1 + pt.UseTrend)
                   ,0) - 1
                   ,6) AS AllowedTrend
                   ,ROUND (
                    dbo.Trend_fnSafeDivide (
                    dbo.Trend_fnSafeDivide (
                    dbo.Trend_fnSafeDivide (SUM (pwa.UseMetric * pwa.MM), SUM (pwa.MM), 0), hmt.UseMetric, 0)
                   ,(1 + pt.UseTrend)
                   ,0) - 1
                   ,6) AS UseTrend
                   ,spi.IsCalcPlanExpAdj
        INTO        #FinalTrends
        FROM        #ProjWithActuals pwa
        LEFT JOIN   #HistoricMetricsTotals hmt
               ON hmt.PlanInfoID = pwa.PlanInfoID
                  AND   hmt.PlanYearID + 1 = pwa.PlanYearID
                  AND   hmt.ReportingCategory = pwa.ReportingCategory
        LEFT JOIN   #FinalProjTrends pt
               ON pt.PlanInfoID = pwa.PlanInfoID
                  AND   pt.ReportingCategory = pwa.ReportingCategory
        LEFT JOIN   dbo.SavedPlanInfo spi
               ON spi.PlanInfoID = pwa.PlanInfoID
        GROUP BY    pwa.PlanInfoID
                   ,pwa.ReportingCategory
                   ,pwa.PlanYearID
                   ,hmt.AllowedMetric
                   ,pt.CostTrend
                   ,pt.UseTrend
                   ,hmt.UseMetric
                   ,spi.IsCalcPlanExpAdj;



        IF @WriteIndicator = 1 --NA

            BEGIN

                -- Final output table. 
                DELETE  FROM dbo.Trend_ProjProcess_CalcPlanTrends_RepCat_staged WITH (ROWLOCK)
                WHERE   (ComponentReporting = 'PlanExpAdj'
                         AND PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo));
                INSERT INTO dbo.Trend_ProjProcess_CalcPlanTrends_RepCat_staged WITH (ROWLOCK)
				(PlanInfoID
					,CPS
					,PlanYearID
					,BaseYearID
					,TrendYearID
					,RateType
					,ReportingCategory
					,PackageOptionID
					,ComponentVersionID
					,ComponentReporting
					,PlanTypeGranularity
					,PlanTypeGranularityValue
					,CostAdjustment
					,UseAdjustment
					,IsOverride
					,OverrideID
					,OverrideDescription
					,LastUpdateByID
					,LastUpdateDateTime
					,ID)
                SELECT      DISTINCT
                            a.PlanInfoID
                           ,a.CPS
                           ,a.PlanYearID
                           ,@BaseYear AS BaseYearID
                           ,@CurrentYear AS TrendYearID
                           ,1 AS RateType
                           ,b.ReportingCategory
                           ,NULL AS 'PackageOptionID'
                           ,NULL AS 'ComponentVersionID'
                           ,'PlanExpAdj' AS ComponentReporting
                           ,'Plan' AS PlanTypeGranularity
                           ,NULL AS PlanTypeGranularityValue
                           ,CASE WHEN ft.IsCalcPlanExpAdj = 0 THEN 0
                                 WHEN b.ReportingCategory = 'Part B Rx Pharmacy' THEN 0
                                 ELSE dbo.Trend_fnSafeDivide ((1 + ft.AllowedTrend), (1 + ft.UseTrend), 1) - 1 END  -- Cost
                           ,CASE WHEN ft.IsCalcPlanExpAdj = 0 THEN 0
                                 WHEN b.ReportingCategory = 'Part B Rx Pharmacy' THEN 0
                                 WHEN ft.UseTrend = -1 THEN 0
                                 ELSE ft.UseTrend END                                                               -- Use
                           ,0 AS 'IsOverride'
                           ,NULL AS 'OverrideID'
                           ,NULL AS 'OverrideDescription'
                           ,@LastUpdateByID
                           ,GETDATE ()
						   ,NEWID()
                FROM        #PlanInfo a
               INNER JOIN   #RepCat b
                       ON 1 = 1
                LEFT JOIN   #FinalTrends ft
                       ON ft.PlanInfoID = a.PlanInfoID
                          AND   ft.ReportingCategory = b.ReportingCategory;

            END;


        IF @WriteIndicator = 2 --MS

            BEGIN

                -- Final output table. 
                /*DELETE  FROM dbo.Trend_ProjProcess_CalcPlanTrends_RepCat WITH (ROWLOCK)
                WHERE   (ComponentReporting = 'PlanExpAdj'
                         AND PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo));*/

                -- Deleting with BATCH process
                DECLARE @RepCatRowcount INT = 1;
                WHILE @RepCatRowcount > 0
                    BEGIN
                        DELETE TOP (10000)
                        FROM dbo.Trend_ProjProcess_CalcPlanTrends_RepCat WITH (ROWLOCK)
                        WHERE   (ComponentReporting = 'PlanExpAdj'
                                 AND PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo));
                        SET @RepCatRowcount = @@RowCount;
                    END;

                INSERT INTO dbo.Trend_ProjProcess_CalcPlanTrends_RepCat WITH (ROWLOCK)
				(PlanInfoID
				,CPS
				,PlanYearID
				,BaseYearID
				,TrendYearID
				,RateType
				,ReportingCategory
				,PackageOptionID
				,ComponentVersionID
				,ComponentReporting
				,PlanTypeGranularity
				,PlanTypeGranularityValue
				,CostAdjustment
				,UseAdjustment
				,IsOverride
				,OverrideID
				,OverrideDescription
				,LastUpdateByID
				,LastUpdateDateTime
				,ID)
                SELECT      DISTINCT
                            a.PlanInfoID
                           ,a.CPS
                           ,a.PlanYearID
                           ,@BaseYear AS BaseYearID
                           ,@CurrentYear AS TrendYearID
                           ,1 AS RateType
                           ,b.ReportingCategory
                           ,NULL AS 'PackageOptionID'
                           ,NULL AS 'ComponentVersionID'
                           ,'PlanExpAdj' AS ComponentReporting
                           ,'Plan' AS PlanTypeGranularity
                           ,NULL AS PlanTypeGranularityValue
                           ,CASE WHEN ft.IsCalcPlanExpAdj = 0 THEN 0
                                 WHEN b.ReportingCategory = 'Part B Rx Pharmacy' THEN 0
                                 ELSE dbo.Trend_fnSafeDivide ((1 + ft.AllowedTrend), (1 + ft.UseTrend), 1) - 1 END  -- Cost
                           ,CASE WHEN ft.IsCalcPlanExpAdj = 0 THEN 0
                                 WHEN b.ReportingCategory = 'Part B Rx Pharmacy' THEN 0
                                 WHEN ft.UseTrend = -1 THEN 0
                                 ELSE ft.UseTrend END                                                               -- Use
                           ,0 AS 'IsOverride'
                           ,NULL AS 'OverrideID'
                           ,NULL AS 'OverrideDescription'
                           ,@LastUpdateByID
                           ,GETDATE ()
						   ,NEWID()
                FROM        #PlanInfo a
               INNER JOIN   #RepCat b
                       ON 1 = 1
                LEFT JOIN   #FinalTrends ft
                       ON ft.PlanInfoID = a.PlanInfoID
                          AND   ft.ReportingCategory = b.ReportingCategory;

            END;

    END;
