SET ANSI_NULLS ON;
GO
SET QUOTED_IDENTIFIER ON;
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateBenefitCategoryIsProng2Flag
--
-- Author:		Vikrant <PERSON>gal
--
-- Create date: 19-dec-2023
-- HEADER UPDATED: 19-dec-2023
--
-- DESCRIPTION: Updates IsProng2 flag of LkpIntBenefitCategory table if BenefitCategoryID exists in LkpIntCostSharingStandards
--
-- PARAMETERS:
--	Input:
--		
--
-- RETURNS: 
--		
--
-- TABLES:
--  Read:LkpIntCostSharingStandards
--
--Write: LkpIntB<PERSON>fitCategory

--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:	   
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
--2023-Dec-19			1			Intial version													 Vikrant Bagal                       
-- ----------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spUpdateBenefitCategoryIsProng2Flag]
AS
BEGIN

    SET NOCOUNT ON;

    UPDATE bc
    SET bc.IsProng2 = CASE
                          WHEN cs.BenefitCategoryID IS NULL THEN
                              0
                          ELSE
                              1
                      END
    FROM dbo.LkpIntBenefitCategory AS bc
        LEFT JOIN
        (
            SELECT DISTINCT
                   BenefitCategoryID
            FROM dbo.LkpIntCostSharingStandards WITH (NOLOCK)
        ) cs
            ON cs.BenefitCategoryID = bc.BenefitCategoryID
    WHERE 1 = 1;

END;

