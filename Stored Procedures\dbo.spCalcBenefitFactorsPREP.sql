SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: spCalcBenefitFactorsPREP  
--   
-- CREATOR: <PERSON> (for REVAMP of 1/17/23)
--   originally <PERSON> Nielsen  
--    
-- CREATED DATE: 2018-Nov-07  
--    
-- DESCRIPTION: Stored Procedure responsible for determining and posting Benefit Factors for use in PREP.   
--    
-- PARAMETERS:   
--    
-- INPUT: @BidYear  
--        @CPBPList  
--  
-- OUTPUT:  
--  
-- TABLES:    
--  
-- READ: CalcBenefitProjection<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>CostShare, Calc<PERSON><PERSON>SFactors,
--        Save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Saved<PERSON>lanInfoID
--    before 1/17/23 included <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ategoryDetail, LkpIntBenefitCategory, LkpProductType, CalcDeductibleMOOPFactors  
--                         CalcPlanExperienceByBenefitCat, SavedPlanINOONDistributionDetail  
--  
-- WRITE:   W_FEM_BenefitFactorBase     
--    
-- VIEWS: None (before 1/17/23 included vwLkpPartBOnlyCoveredPercent)
--    
-- FUNCTIONS: fnSplitString, fnGetSafeDivisionResult 
--    before 1/17/23 included fnAppGetBenefitsChangeDetail, fnGetINOONProjectionFactors, fnGetCredibilityFactor, fnIsPartBOnly  
--    
-- STORED PROCS:  
--    
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE   VERSION  CHANGES MADE              DEVELOPER      
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2018-Nov-07  1  Initial Version														Craig Nielsen  
-- 2019-Feb-07  2  Have SQL directly populate the Benefit Factor table for PREP			Alex Beruscha  
-- 2019-Apr-15  3  Update DELETE criteria error in Benefit Factor Table for PREP		Alex Beruscha  
-- 2022-Jul-05  4  Replaced SynchronizedBenefitCategoryID with 0						Aleksandar Dimitrijevic  
-- 2022-Aug-30  5  Added missing WITH (NOLOCK), @@variables,							Phani Adduri
----			   release temp table memory after use, enable run in parallel		
-- 2022-Oct-12  6  Updated to @Xvariables, disabled parallel run						Phani Adduri
-- 2023-Jan-17  7  COMPLETE REVAMP to use Bid Model/MAAUI Projection Results rather     Bob Knadler
--                   than re-project for PREP, and to take care of issues. 
--                   The issues relate to a faulty basis / denominator since the sp never   
--                   properly aligned w/Trend Overhaul from 3-4 years ago; for instance
--                   the Target MER Factor was part of the basis in determining the benefit factors
-- 2024-Sep-20	8  Updated table pull to dbo.CalcTotalCostShare instead of				Devin Goodman
--					dbo.CalcTotalBenefitsByBenefitCategory as this table is being 
--					replaced.  All other needed updates to accomedate this are 
--					updated accordingly.
-- 2024-Nov-20	9	Updated to reflect exclusion of settlement cap in cost share basis	Jordan Lake
--					calculations in MAAUI
-- 2024-Nov-21  10  Error handling when allowed amounts are 0. Setting benefit factor to 0 Devin Goodman
-- ----------------------------------------------------------------------------------------------------------------------   


CREATE PROCEDURE [dbo].[spCalcBenefitFactorsPREP]   
(  
 @BidYear INT,  
 @CPBPList VARCHAR(MAX),  
 @UserID char(7),  
 @TimeStamp datetime  
 )  

AS   

BEGIN  



SET NOCOUNT ON;  
 
--Declare local variables and set equal to passed parameters in order to prevent any parameter sniffing inefficiencies

DECLARE	 @XBidYear INT = @BidYear,  
 @XCPBPList VARCHAR(MAX) = @CPBPList,  
 @XUserID char(7) = @UserID,  
 @XTimeStamp datetime = @TimeStamp;



DECLARE @PlanList   
TABLE (  
   CPS varchar(13)  
  )  

INSERT INTO @PlanList  
 SELECT DISTINCT 
        [Value]  
 FROM dbo.fnStringSplit(@XCPBPList , ',')  


/*** Get the Allowed by Benefit Category that serves as Cost Share Basis for Bid Model/MAAUI ***/
--Cost Share ratios are based on Non-DE#

DECLARE @CalcTotalBenefitsPreMbrCS TABLE
		(
		    ForecastID INT,
		    CPS VARCHAR(13),
			DualEligibleTypeID INT, 
			Credibility DECIMAL(18,8),
			BenefitCategoryID INT, 
			BlendedAllowedNonSQS DECIMAL(20,12),
			ExperAllowedNonSQS DECIMAL(20,12),
			ManualAllowedNonSQS DECIMAL(20,12),
	        [ExperSQSFxWDampening] [decimal](10, 8) NULL,
	        [ManualSQSFxWDampening] [decimal](10, 8) NULL
		)


INSERT INTO @CalcTotalBenefitsPreMbrCS

(
    ForecastID,
    CPS,
    DualEligibleTypeID,
    Credibility,
    BenefitCategoryID,
    BlendedAllowedNonSQS,
	ExperAllowedNonSQS,
	ManualAllowedNonSQS,
	ExperSQSFxWDampening,
	ManualSQSFxWDampening	
)


SELECT bc.ForecastID, 
       spi.CPS,
       bc.DualEligibleTypeID, 
	   dbo.fnGetCredibilityFactor(bc.ForecastID) AS Credibility,
       bc.BenefitCategoryID, 
       BlendedAllowedNonSQS = SUM( (CASE bc.MARatingOptionID 
	                                  WHEN 3 THEN 1 ELSE 0 
							        END) *  (bc.INAllowed + bc.OONAllowed) ) , 
       ExperAllowedNonSQS = SUM( (CASE bc.MARatingOptionID 
	                                WHEN 1 THEN 1 ELSE 0 
							      END) *  (bc.INAllowed + bc.OONAllowed) ) , 
       ManualAllowedNonSQS = SUM( (CASE bc.MARatingOptionID 
	                                 WHEN 2 THEN 1 ELSE 0 
							       END) *  (bc.INAllowed + bc.OONAllowed) ) ,
       ExperSQSFxWDampening = MAX( (CASE bc.MARatingOptionID 

	                                  WHEN 1 THEN 1 ELSE 0 
							        END) *  ISNULL(sqs.PREPProjectedSQSIncludeInCostShareBasis,0) ) , --#9

       ManualSQSFxWDampening = MAX( (CASE bc.MARatingOptionID 
	                                   WHEN 2 THEN 1 ELSE 0 
							         END) *  ISNULL(sqs.PREPProjectedSQSIncludeInCostShareBasis,0) ) --#9

  FROM dbo.CalcBenefitProjectionPreMbrCS bc WITH (NOLOCK)
    INNER JOIN dbo.SavedForecastSetup sfs WITH (NOLOCK)
	  ON bc.ForecastID = sfs.ForecastID
	INNER JOIN dbo.SavedPlanInfo spi WITH (NOLOCK)
	  ON sfs.PlanInfoID = spi.PlanInfoID
	    AND spi.PlanYear = @XBidYear
	INNER JOIN @PlanList pl
	  ON spi.CPS = pl.CPS

    LEFT JOIN dbo.CalcSQSFactors sqs
	    ON bc.PlanYearID = sqs.PlanYearID
		  AND bc.ForecastID = sqs.ForecastID
		  AND bc.MARatingOptionID = sqs.MARatingOptionID
		  AND bc.BenefitCategoryID = sqs.BenefitCategoryID

  WHERE sfs.IsLiveIndex = 1
    AND sfs.IsHidden = 0
	AND bc.DualEligibleTypeID = 0 
	AND bc.IsIncludeInCostShareBasis=1 --#9
  GROUP BY bc.ForecastID, 
       spi.CPS,
       bc.DualEligibleTypeID, 
       bc.BenefitCategoryID


/*** Manipulate Allowed to put on a Sequestered basis for use by PREP ***/
--Cost Share pmpm is captured directly

DECLARE @PREPBenFx AS TABLE
 (  
	[PlanYearID] [int] NOT NULL,
	[ForecastID] [int] NOT NULL,
	[CPS] [varchar](13) NOT NULL,
	[DualEligibleTypeID] [int] NOT NULL,
	[MARatingOptionID] [int] NOT NULL,
	[BenefitCategoryID] [int] NOT NULL,
	[BlendedAllowedNonSQS] [decimal](18, 8) NULL,
	[CostShare] [decimal](18, 8) NULL,
	[BlendedAllowed_SQSAdjOnly] [decimal](18, 8) NULL,
	[PlanCSRatio] [decimal](18, 8) NULL,
	[PREP_PlanCSRatio] [decimal](18, 8) NULL,
	[PREP_BenefitFactor] [decimal](18, 8) NULL,
	[UserID] [varchar](7) NULL,
	[Timestamp] [datetime] NULL
  )   

INSERT INTO @PREPBenFx

(
    PlanYearID,
    ForecastID,
    CPS,
    DualEligibleTypeID,
	MARatingOptionID,





    BenefitCategoryID,
    BlendedAllowedNonSQS,
    CostShare,
	BlendedAllowed_SQSAdjOnly,
    PlanCSRatio,
    PREP_PlanCSRatio,
    PREP_BenefitFactor,
    UserID,
    Timestamp
)
SELECT  @XBidYear AS PlanYearID, --Added X

        pmcs.ForecastID,
        pmcs.CPS,
        pmcs.DualEligibleTypeID,
		3 AS MARationgOptionID,
        pmcs.BenefitCategoryID,

		SUM(pmcs.BlendedAllowedNonSQS) AS BlendedAllowedNonSQS,
		SUM(bc.TotalCostShare) AS CostShare, --Pull in Total Cost Share, #8

		SUM( (pmcs.ExperAllowedNonSQS * pmcs.ExperSQSFxWDampening * pmcs.Credibility)
		       + (pmcs.ManualAllowedNonSQS * pmcs.ManualSQSFxWDampening * (1 - pmcs.Credibility)) ) AS BlendedAllowed_SQSAdjOnly,

        PlanCostShareRatio =
					dbo.fnGetSafeDivisionResult(CONVERT(DECIMAL(20,12), SUM(bc.TotalCostShare))--Pull in Total Cost Share, #8
						,CONVERT(DECIMAL(20,12), SUM(pmcs.BlendedAllowedNonSQS))),		

        PREP_PlanCSRatio = 
					dbo.fnGetSafeDivisionResult(CONVERT(DECIMAL(20,12), SUM(bc.TotalCostShare))--Pull in Total Cost Share, #8
						,CONVERT(DECIMAL(20,12), SUM( (pmcs.ExperAllowedNonSQS * pmcs.ExperSQSFxWDampening * pmcs.Credibility)
		                                                 + (pmcs.ManualAllowedNonSQS * pmcs.ManualSQSFxWDampening * (1 - pmcs.Credibility)) ))),

		PREP_BenefitFactor = CASE -- If Cost Share is more than Allowed for some reason, set Benefit Factor to 0, assuming that's what plan would pay
		                       WHEN dbo.fnGetSafeDivisionResult(CONVERT(DECIMAL(20,12), SUM(bc.TotalCostShare))--Pull in Total Cost Share, #8
						               ,CONVERT(DECIMAL(20,12), SUM( (pmcs.ExperAllowedNonSQS * pmcs.ExperSQSFxWDampening * pmcs.Credibility)
		                                                                + (pmcs.ManualAllowedNonSQS * pmcs.ManualSQSFxWDampening * (1 - pmcs.Credibility)) ))) > 1 THEN 0
							   WHEN dbo.fnGetSafeDivisionResult(CONVERT(DECIMAL(20,12), SUM(bc.TotalCostShare))
						               ,CONVERT(DECIMAL(20,12), SUM( (pmcs.ExperAllowedNonSQS * pmcs.ExperSQSFxWDampening * pmcs.Credibility)
		                                                                + (pmcs.ManualAllowedNonSQS * pmcs.ManualSQSFxWDampening * (1 - pmcs.Credibility)) ))) < 0 THEN 0 --#10
							   ELSE	1 - 
					                  dbo.fnGetSafeDivisionResult(CONVERT(DECIMAL(20,12), SUM(bc.TotalCostShare))--Pull in Total Cost Share, #8
						                 ,CONVERT(DECIMAL(20,12), SUM( (pmcs.ExperAllowedNonSQS * pmcs.ExperSQSFxWDampening * pmcs.Credibility)
		                                                                  + (pmcs.ManualAllowedNonSQS * pmcs.ManualSQSFxWDampening * (1 - pmcs.Credibility)) )))

							  END ,		

		@XUserID,
		@XTimeStamp

   FROM @CalcTotalBenefitsPreMbrCS pmcs --pre mbrcs
      INNER JOIN dbo.CalcTotalCostShare bc WITH (NOLOCK) --Change Table Name to dbo.CalcTotalCostShare --#8 DG 9/19/2024

		ON pmcs.ForecastID = bc.ForecastID
			AND pmcs.BenefitCategoryID = bc.BenefitCategoryID

   WHERE bc.IsBenefitYearCurrentYear = 0 

   GROUP BY pmcs.ForecastID, 
        pmcs.CPS,
        pmcs.DualEligibleTypeID,

        pmcs.BenefitCategoryID

   ORDER BY PlanYearID,
            pmcs.CPS,
            pmcs.BenefitCategoryID







DELETE FROM dbo.W_FEM_BenefitFactorBase  
WHERE ContractPBPSegmentID IN ( SELECT DISTINCT CPS  FROM @PlanList )  
  AND PlanYearID = @XBidYear  

INSERT INTO dbo.W_FEM_BenefitFactorBase
(
    PlanYearID,
    ContractPBPSegmentID,
    BenefitCategoryID,
   BenefitFactor,
    TotalBlendedAllowed,
    UserID,
    Timestamp
)
SELECT pbx.PlanYearID ,   
       pbx.CPS ,
	   pbx.BenefitCategoryID ,
	   pbx.PREP_BenefitFactor ,
	   pbx.BlendedAllowed_SQSAdjOnly ,
       @XUserID AS UserID ,  
       @XTimeStamp AS [TimeStamp]  
FROM @PREPBenFx pbx  

ORDER BY pbx.PlanYearID ,   
       pbx.CPS ,
	   pbx.BenefitCategoryID




END;
GO
