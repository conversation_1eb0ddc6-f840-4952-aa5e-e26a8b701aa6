SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetPlans]   
--    
-- AUTHOR: <PERSON><PERSON>y 
--    
-- CREATED DATE: 2024-Oct-28    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Regions  
--    
-- PARAMETERS:   
	--@RegionID
	--@PlanInfoID
-- Input: 

-- TABLES:   
-- PrePricing.PlanInfo
-- dbo.SavedMarketInfo
-- dbo.LkpProductType
-- PrePricing.PlanGroupMapping
-- PrePricing.PlanGroup

-- Read:    
--  

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE							DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-28		1		Initial Version							Surya Murthy
-- 2025-Feb-21		2		Sorting order changes by CPS            Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetPlans]
(
	@RegionID VARCHAR(MAX)
)
AS    
BEGIN    
		SELECT pinfo.PlanInfoID , smi.ActuarialMarketID AS MarketID,
		smi.ActuarialRegionID AS RegionID,
		lkptype.ProductType AS Product,pinfo.IsEnabled,
		pinfo.CPS,pinfo.PlanDescription,
		PlanGroups=STUFF((SELECT ',' +  CAST(b.PlanGroupID AS VARCHAR(7))
				   FROM PrePricing.PlanGroup b WITH(NOLOCK)
				   JOIN PrePricing.PlanGroupMapping c WITH(NOLOCK) ON c.PlanInfoID=pinfo.PlanInfoID AND b.PlanGroupID=c.PlanGroupID           
				  FOR XML PATH('')),1, 1, '')
		FROM PrePricing.PlanInfo AS pinfo WITH(NOLOCK)
		JOIN dbo.SavedMarketInfo AS smi ON smi.ActuarialMarketID = pinfo.MarketID AND smi.ActuarialRegionID IN(SELECT VALUE  FROM STRING_SPLIT(@RegionID,','))
		JOIN dbo.LkpProductType AS lkptype ON lkptype.ProductTypeID = pinfo.ProductTypeID
		LEFT JOIN PrePricing.PlanGroupMapping AS pgpm ON pgpm.PlanInfoID=pinfo.PlanInfoID
		LEFT JOIN PrePricing.PlanGroup AS pgrp ON pgrp.PlanGroupID=pgpm.PlanGroupID AND pgrp.RegionID=smi.ActuarialRegionID	
		GROUP BY pinfo.PlanInfoID ,smi.ActuarialMarketID,smi.ActuarialRegionID,lkptype.ProductType,pinfo.IsEnabled,pinfo.CPS,pinfo.PlanDescription, pinfo.LastUpdateDateTime 
		ORDER BY  CPS	ASC
END
GO

