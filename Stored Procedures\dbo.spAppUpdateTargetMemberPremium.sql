SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  <PERSON> Smith      
-- Create date: 28 Aug 2020
-- Description:  spAppUpdateTargetMemberPremium

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION		CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 28 Aug 2020  1			Initial version.											<PERSON>
-- 26 Aug 2021  2           Updated for story-2451521 to include sp-[spAppUpdateSavedTargetInputs]    Ramandeep Saini
-- 18 Sep 2021  3			This is 21.11 fix so commenting out part of Ramandeep's change for 21.12	Bob Knadler
-- 01-Oct-2021   5           Uncommented execution of  sp-[spAppUpdateSavedTargetInputs]   Ramandeep Saini
-- ----------------------------------------------------------------------------------------------------------------------        
  
-- =============================================   
CREATE PROCEDURE [dbo].[spAppUpdateTargetMemberPremium]
    @ForecastID INT,
	@TargetPremium FLOAT,
    @UserID VARCHAR(7),
	@MessageFromBackend VARCHAR(MAX) OUT,
	@Result BIT OUT
AS
BEGIN
--	BEGIN TRANSACTION;
        BEGIN TRY 
			BEGIN					
				EXEC	[dbo].[spTargetPremiumReprice] @ForecastID, @UserID, @MessageFromBackend OUTPUT, @TargetPremium
			END
--	COMMIT TRANSACTION;

			IF (@MessageFromBackend = '0')
			  BEGIN
				SET @MessageFromBackend =  'The Target Member Premium calculation was successful and the scenario has been repriced.'
				SET @Result=1 
-- Bob K 9/18/21 commented out below temporarily, until 21.12 ready for environments above DEV_Grow
			EXECUTE [dbo].[spAppUpdateSavedTargetInputs] @ForecastID, @TargetPremium, @UserID, 2     --2 implies update Target Member Premium in table
			  END
			ELSE
			  BEGIN
				SET @MessageFromBackend =  @MessageFromBackend
				SET @Result=0 
			  END
  		END TRY
        BEGIN CATCH
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;
			 DECLARE @ErrorException NVARCHAR(4000); 

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

--			ROLLBACK TRANSACTION; 
			SET @MessageFromBackend =  @ErrorMessage
			SET @Result=0 
        END CATCH;  
END;
GO
