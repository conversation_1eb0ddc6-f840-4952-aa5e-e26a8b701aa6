SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- PROCEDURE NAME: spDashboardReportImportPlansIntoPREPPBI2

-- DESCRIPTION: This SP returns extract for "Import Plans into PREP" user action from W_FEM_Runtime table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      dbo.DashboardReport_W_FEM_Runtime
--		
-- Example 
-- Exec [dbo].[spDashboardReportImportPlansIntoPREPPBI2] '12345', '2023-08-03'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-22			1			Initial Version                                                                         Sheetal Patil
---------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROC [dbo].[spDashboardReportImportPlansIntoPREPPBI2]
 (@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


 DECLARE @XLastSuccessfulRunTimestampFEM  DATETIME = @LastSuccessfulRunTimestampFEM

IF (SELECT  OBJECT_ID ('tempdb..#AppLogprepTemp')) IS NOT NULL DROP TABLE #AppLogprepTemp;

SELECT 
	[DateTime] AS [Date]
	,[RegionList]
	,[Runtime] ExecutionTimeinSeconds
	,[UserID]
INTO #AppLogprepTemp
FROM dbo.DashboardReport_W_FEM_Runtime WITH (NOLOCK)
WHERE [DateTime] > @XLastSuccessfulRunTimestampFEM
AND BidModelCheck = 'True' 
AND SCTCheck = 'True'


SELECT 
	'Import Plans into PREP' AS 'UserActions'
	,[UserID] AS 'UserID'
	,[Date] AS 'Run Date'
	,'Import Plans into PREP' AS 'Type'
	,[RegionList] AS 'Plans'
	,1 AS 'Plan Count'
	,ExecutionTimeinSeconds
	,0 AS characterEvenCount
	,0 AS ErrorCount
	,' ' AS [ErrorMessage]
FROM #AppLogprepTemp 

END
GO
