SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author: spAppTriggerReprice 
-- Create date: 2020-Jan-13
-- Description:  Triggers reprice
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2020-Jan-13  1		  Initial version.											                      Deepali
-- 2020-Jan-14  2		  Updated parameter name.									                      Deepali
-- 2023-Jan-27  3         Adding a where Clause to only pull BY data from SavedForecastSetup table        Abraham Ndabian
-- ----------------------------------------------------------------------------------------------------------------------     
CREATE PROCEDURE [dbo].[spAppTriggerReprice] 
	@ForecastID INT,
	@LastUpdateByID CHAR(7) ,
    @LastUpdateDateTime DATETIME 

AS

	BEGIN
		 BEGIN TRANSACTION;
        BEGIN TRY

		UPDATE dbo.SavedForecastSetup 
		SET IsToReprice=1,
		LastUpdateByID=@LastUpdateByID,
		LastUpdateDateTime=@LastUpdateDateTime
		WHERE ForecastID=@ForecastID 
		-- added this filter to restrict data to new ForecastIDs values for new Bid Year, by Abe 1/26/23
		AND PlanYear= dbo.fnGetBidYear() 




		COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
            ROLLBACK;

        END CATCH;  
END;
GO
