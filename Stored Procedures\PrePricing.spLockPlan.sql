SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spLockPlan]   
--    
-- AUTHOR: <PERSON><PERSON>y 
--    
-- CREATED DATE: 2024-Nov-04    
-- Type: 
-- DESCRIPTION: Procedure responsible for lock the plan  
--    
-- PARAMETERS:    
-- Input: 
-- @PlanInfoID
-- @LastUpdatedByUserID
-- @OutPutResult
   
-- TABLES:   
--  
 
-- Read:    
--  PrePricing.PlanInfo

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Nov-04		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spLockPlan]
(
	@PlanInfoID INT, 
	@LastUpdatedByUserID VARCHAR(7),
	@OutPutResult VARCHAR(200)  OUTPUT
)
AS    
BEGIN    
	SET NOCOUNT ON
	BEGIN TRY
		BEGIN TRANSACTION planlock	
			IF EXISTS (SELECT 1 FROM PrePricing.PlanInfo WITH(NOLOCK) WHERE PlanInfoID =@PlanInfoID)
			BEGIN
				DECLARE @lastlockedbyUserID VARCHAR(7);
				SET @lastlockedbyUserID = '';
				IF EXISTS(SELECT 1 FROM PrePricing.PlanInfo WITH(NOLOCK) WHERE (IsLocked IS NOT NULL AND IsLocked=1) AND PlanInfoID =@PlanInfoID AND ( LockedBy IS NOT NULL AND LockedBy<>@LastUpdatedByUserID))
				BEGIN
					SELECT  @lastlockedbyUserID = LockedBy FROM PrePricing.PlanInfo WITH(NOLOCK) WHERE IsLocked=1 AND PlanInfoID =@PlanInfoID AND LockedBy<>@LastUpdatedByUserID;
					SET @OutPutResult = 'Error, Plan was locked by:'+@lastlockedbyUserID;
				END			
				ELSE
				BEGIN
					UPDATE PrePricing.PlanInfo SET IsLocked=1, LockedDate=GETDATE(),LockedBy=@LastUpdatedByUserID WHERE PlanInfoID =@PlanInfoID;
					SET @OutPutResult = 'Plan Locked Successfully.'
				END
			END
			ELSE
			BEGIN
				SET @OutPutResult = 'Invalid PlanInfoID:'+CAST(@PlanInfoID AS VARCHAR(10))
			END
		COMMIT TRANSACTION planlock;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION planlock;
		SET @OutPutResult='Error, while Locking the PlanInfoID:'+CAST(@PlanInfoID AS VARCHAR(10));	
	END CATCH
END
GO
