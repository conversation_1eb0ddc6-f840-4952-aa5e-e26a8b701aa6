SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- User Defined Function

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: PrePricing.fnValidateMarketInputSave    
--    
-- AUTHOR: <PERSON> 
--    
-- CREATED DATE: 2024-Jan-31   
-- Type: 
-- DESCRIPTION: Function responsible for validating market input saves
--    
-- PARAMETERS:    
-- Input: 
--	@SubCategoryID VARCHAR(MAX), 
--	@Inputvalue VARCHAR(500) 

-- TABLES:   
--
-- Read:    
--  
-- Write:    
--  
-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS: 
-- [PrePricing].[spSaveMarketInputValues] 
--       
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-Jan-31 		1		Initial Version                 Adam Gilbert
-- 2025-Feb-12 		2		MSB Validation Logic added		Surya Murthy
-- 2025-Mar-24 		3		Coinsurance data type casting   Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    

CREATE FUNCTION  [PrePricing].[fnValidateMarketInputSave] 
(
	@SubCategoryID VARCHAR(MAX) 
	,@CostShareType INT
	,@FlagType VARCHAR(10)
	,@Inputvalue VARCHAR(500) 
)
RETURNS @Results TABLE
(
	OutPutCode VARCHAR(20),    
	OutputMessage VARCHAR(MAX)   
) 
AS BEGIN

	DECLARE @OutPutCode VARCHAR(20)     = '';--Error, Success
	DECLARE @OutputMessage VARCHAR(MAX) = '';--Freeform
	DECLARE @CategoryID INT ;	
	DECLARE @SubCatName VARCHAR(max)     = '';
	SELECT @CategoryID = categoryid, @SubCatName = SubCategoryName FROM PrePricing.MarketInputSubCategory WITH(NOLOCK) WHERE SubCategoryID=@SubCategoryID	 


		--Blank Input Value
	IF @Inputvalue ='' 
		BEGIN 
			SET @OutPutCode ='Success'; SET @OutputMessage = 'Values removed';
			GOTO ReturnResult;
		END

	-- Check if benefit change values are numeric.
	IF @FlagType = ('$ Change') AND ISNUMERIC(@Inputvalue) <> 1 AND @Inputvalue<>''
		BEGIN
			SET @OutPutCode ='Error'; SET @OutputMessage = 'Benefit Change value must be numeric.';
			GOTO ReturnResult;
        END

	--not saving a $ Change
	IF @FlagType IN ('IN','OON','BOTH')
	BEGIN
		/*General benefit validations*/
		--Numeric Check.
		DECLARE @BenefitIsNumber BIT = (SELECT isnumber FROM PrePricing.MarketInputSubCategory WHERE SubCategoryID = @SubCategoryID)
		IF @BenefitIsNumber = 1 AND ISNUMERIC(@Inputvalue) = 0
			BEGIN 
				SET @OutPutCode ='Error'; SET @OutputMessage = 'Input value must be numeric.';
			GOTO ReturnResult;
			END

		--Positive numbers only
		DECLARE @BenefitIsPositiveNumber BIT = (SELECT IsPositiveNumber FROM PrePricing.MarketInputSubCategory WHERE SubCategoryID = @SubCategoryID)
		IF @BenefitIsPositiveNumber = 1 AND ISNUMERIC(@Inputvalue) = 1 
			BEGIN 
				IF  CAST(@Inputvalue AS DECIMAL(18,6)) < 0
				BEGIN
					SET @OutPutCode ='Error'; SET @OutputMessage = 'Input value must be positive.';
					GOTO ReturnResult;
				END
			END

		--IN Value only
		DECLARE @BenefitIsINOnly BIT = (SELECT IsINValueOnly FROM PrePricing.MarketInputSubCategory WHERE SubCategoryID = @SubCategoryID)
		IF @BenefitIsINOnly = 1 AND @FlagType in ('OON','BOTH')
			BEGIN 
				SET @OutPutCode ='Error'; SET @OutputMessage = '"'+@FlagType+'"' + ' Not valid for this benefit. Please select "IN" toggle.';
			GOTO ReturnResult;
			END

		--Read only benefit check
		IF EXISTS(SELECT 1 FROM PrePricing.MarketInputSubCategory WHERE isreadonly = 1 AND SubCategoryID = @SubCategoryID )
			BEGIN 
				SET @OutPutCode ='Error'; SET @OutputMessage = 'Benefit selection contains read only benefits.';
			GOTO ReturnResult;
			END

		--CostShareType	--Reference: "Coinsurance",1,"Copay",2,"Admit",3,"Other",4,"Day Range",5
		--Coinsurance range
		IF @CostShareType = 1 AND ISNUMERIC(@InputValue)=1
			BEGIN
				IF CAST(@Inputvalue AS DECIMAL(18,6)) <0 OR CAST(@Inputvalue AS DECIMAL(18,6)) > .5
				BEGIN
					SET @OutPutCode ='Error'; SET @OutputMessage ='Coinsurance should be between 0 and .5';
				END
			GOTO ReturnResult;
			END

		/*Benefit specific validations*/
		--Subcategory 4, Valid Deductible Type
		IF @SubCategoryID = 4
		BEGIN
			IF NOT EXISTS (SELECT 1 FROM dbo.LkpDeductTypeDesc WHERE DeductTypeDesc = @Inputvalue)
			BEGIN
				SET @OutPutCode ='Error'; SET @OutputMessage = @Inputvalue + ' is not a valid deductible type. Refer to MAAUI for acceptable input.';
			END
		GOTO ReturnResult;
		END


		--MSB Code Validation; category = 2
		IF @CategoryID = 2
			BEGIN
				--validate input value
				DECLARE @inputcode VARCHAR(10)
				SELECT @inputcode =  REPLACE(RIGHT( @SubCatName, 4 ),')','');
				IF (@inputcode <> LEFT(@Inputvalue,3))
				BEGIN
					SET @OutPutCode ='Error'; SET @OutputMessage = 'MSB Code not valid for selected benefit. Expected format: '+@inputcode+'###';
					GOTO ReturnResult;
				END

				IF NOT EXISTS (SELECT 1 FROM LkpIntAddedBenefitType WHERE @Inputvalue = LEFT(AddedBenefitName,6))
				BEGIN
					SET @OutPutCode ='Error'; SET @OutputMessage = 'MSB Code not found. Review input.';
				END
			GOTO ReturnResult;
			END

	/********************************************/
	/**** NO VALIDATIONS BEYOND THIS LINE  ******/
	/********************************************/
	END -- End validations for populated value.

	--No Errors Found
	IF @OutPutCode <> 'Error'
		BEGIN
			SET @OutPutCode ='Success'; SET @OutputMessage = '';
		END

	ReturnResult:--go to branch
		INSERT INTO @Results
		SELECT @OutPutCode AS OutputCode,@OutputMessage  AS OutputMessage

	RETURN 

END --end of function
GO
