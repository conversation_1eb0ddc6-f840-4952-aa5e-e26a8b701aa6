SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- PROCEDURE NAME: spDashboardReportReportinguploadImportPBI2

-- DESCRIPTION: This SP returns extract for "Reporting upload" user action from AppLogs table 
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      [dbo].[DashboardReportAppLogs]
--		
-- Example 
-- Exec [dbo].[spDashboardReportReportinguploadImportPBI2] 20234757, '2023-09-25 11:24:03.200'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-24			1			Initial Version                                                                         Sheetal Patil
-- 2024-Jun-24          2           Replacing /Scenario with /BatchImport based on the new path of batch Page               Chaitanya Durga
-- 2024-Sep-24          3           Replacing /BatchImport with /ImportExport based on the new feature of batch Page Import Chaitanya Durga
---------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROC [dbo].[spDashboardReportReportinguploadImportPBI2]
 (@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


 DECLARE @XLastSuccessfullRunLogid  INT = @LastSuccessfullRunLogid

IF (SELECT  OBJECT_ID ('tempdb..#AppLogRepImpTemp')) IS NOT NULL DROP TABLE #AppLogRepImpTemp;


SELECT [LogID],[User],[Date],[q].[Message],q.Thread AS Thread, DATEDIFF_BIG(MILLISECOND, pDataDate, [Date]) Executiontime,pDataDate AS StartDate
INTO #AppLogRepImpTemp 
FROM    (
         SELECT  *,
                LAG([Date]) OVER (PARTITION BY [User] ORDER BY [Date]) pDataDate
        FROM    dbo.DashboardReportAppLogs WITH (NOLOCK) WHERE  
((([message] LIKE '%ActAdjRepCat%' AND [message] LIKE '%"isExecuted"%') OR Message LIKE '%ImportBatchData%')
       AND  [LogID] > @XLastSuccessfullRunLogid
	   )
        ) q
WHERE   pDataDate IS NOT NULL



IF (SELECT  OBJECT_ID ('tempdb..#AppLogRepImpTemp1')) IS NOT NULL DROP TABLE #AppLogRepImpTemp1;


SELECT [LogID]
		,[User]
		,[Date]
	,[Message]
	,thread 
	,ExecutionTime
	,pDataDate AS StartDate
	INTO #AppLogRepImpTemp1
	FROM 
	(
SELECT [LogID],[User],[Date],[q].[Message],q.Thread AS Thread, DATEDIFF_BIG(MILLISECOND, pDataDate, [Date]) Executiontime,pDataDate
FROM    (
         SELECT  *,
                LAG([Date]) OVER (PARTITION BY [User] ORDER BY [Date]) pDataDate
        FROM    dbo.DashboardReportAppLogs WITH (NOLOCK) WHERE  
((([message] LIKE '%ActAdjRepCat%' AND [message] LIKE '%"isExecuted"%'))
       AND  [LogID] > @XLastSuccessfullRunLogid
	   )
        ) q
WHERE   pDataDate IS NOT NULL) AS t5 WHERE [message] LIKE '%"importType":"ActAdjRepCat","isExecuted":"1"%' 


IF (SELECT  OBJECT_ID ('tempdb..#AppLogRepImpTemp2')) IS NOT NULL DROP TABLE #AppLogRepImpTemp2;


	 SELECT a.LogID,a.[User],a.[Date],a.[Message],a.StartDate,ap.Executiontime AS PostExectime,ap.StartDate AS PostExedate,ap.[date] AS enddate 
	INTO #AppLogRepImpTemp2 FROM #AppLogRepImpTemp a WITH (NOLOCK)
INNER JOIN #AppLogRepImpTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogRepImpTemp app WITH (NOLOCK) WHERE a.logid < app.LogID 
AND (app.[Message] LIKE '%Finished executing POST: ImportExport/ImportBatchData%') AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND (ap.[Message] LIKE '%Finished executing POST: ImportExport/ImportBatchData%')
WHERE a.[Message]  LIKE '%"importType":"ActAdjRepCat","isExecuted":"1","isConfirm":"true"%'


SELECT 
'Reporting upload' AS [UserActions]
,[User] AS [UserID]
,StartDate AS [RunStartDate]
, 'Reporting upload' AS [Type]
, 'NULL' AS [Plans]
, 0 AS [Plan Count]
, ExecutionTime
, 0 AS characterEvenCount
, 0 AS ErrorCount
, enddate AS [RunEndDate]
, ' ' AS [ErrorMessage]
FROM
(SELECT temp3.LogID,temp3.[User],temp4.StartDate,temp3.Message,temp3.enddate,SUM(temp3.PostExectime+temp4.Executiontime) AS ExecutionTime FROM #AppLogRepImpTemp1 temp4
JOIN 
#AppLogRepImpTemp2 temp3 ON temp3.LogID=temp4.LogID
GROUP BY temp3.LogID,temp3.[User],temp4.StartDate,temp3.Message,temp3.enddate) AS g

END
GO
