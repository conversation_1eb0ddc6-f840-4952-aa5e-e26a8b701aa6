SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetMAReportPlanType]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportPlanType
--
-- AUTHOR: <PERSON><PERSON><PERSON>    
--
-- CREATED DATE: 2009-Mar-20
-- HEADER UPDATED: 2010-Nov-05
--
-- DESCRIPTION:   Get list of PlanTypes for MAMBA MAReport feature
--              Test: select * from fnGetMAReportPlanType()
--
-- PARAMETERS:
--	Input:
--  Output:
--
-- TABLES: 
--	Read:
--      LkpProductType    
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-20		1		    Initial Version										                Aleksey Titievsky
-- 2010-Nov-05      2           Removed @PlanYearID, @GeoFilterTypeID as parameters                 Joe Casey 
-- 2018-Apr-26		3			Modified LkpExtCMSPlanType for new UI table modifications			Jordan Purdue
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportPlanType]()
RETURNS TABLE
AS    
RETURN
    SELECT
        ProductTypeID AS PlanTypeID, 
        ProductType AS  PlanTypeName, 
        Description, 
        ProductID, 
        IsEnabled
    FROM LkpProductType
    WHERE IsEnabled = 1
GO
