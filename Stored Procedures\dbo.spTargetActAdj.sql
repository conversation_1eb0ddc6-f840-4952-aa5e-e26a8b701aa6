SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

-- Stored Procedure

-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spTargetActAdj
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2013-Mar-29
-- HEADER UPDATED: 2013-Mar-29
--
-- DESCRIPTION: Procedure used to  goal-seek an Act Adj Factor value that will result in a desired MER
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@UserID
--		@TargetedMER
--		@TargetPremium
--	Output:
--		@ActAdjFactor
--		@NewMER1
--
-- TABLES:
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnAppGetBidSummary
--
-- STORED PROCS:
--		spPlanRefresh
--		spTargetPremiumReprice
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE														DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2013-Mar-29      1			Initial Version                                                     Mike Deren
-- 2013-Oct-07      2           Included Join on Segment ID                                         Manisha Tyagi
-- 2014-Apr-10	    3		    Changed use factor to cost factor for Adjustment					Mike Deren
-- 2014-Nov-26		4			added upadte condition for IsRiskPlan in savedPalnHeader			Deepali Mittal
-- 2015-Nov-18		5			Moved Target MER factor to SavedMERActAdj							Mark Freel
-- 2016-Feb-10		6			Functionality now updates UserID and time							Mark Freel
-- 2016-Oct-03		7			Added MER Log														Mark Freel
-- 2016-Oct-03		7			Updated MER guess													Mark Freel
-- 2017-Jan-19		8			Added TargetPremium, TargetProfit1 to MER Log						Chris Fleming
-- 2017-Jan-25		9			Added TargetPremium as a passed variable, premium targeting			Mark Freel
--								now uses spTargetPremiumReprice
-- 2019-Aug-05     10           Now setting isRiskPlan flag on SavedForecastSetup                   Brent Osantowski
--2019-oct-30		11			Removed 'HUMAD\' to UserID											Chhavi Sinha  
--2019-Dec-18		12			Replaced USER from @UserID											Deepali Mittal
-- 2020-Oct-30		13			Removing GRANT EXECUTE												Rodney Smith
-- 2020-Dec-28		14			Added NOLOCK												        Mahendran Chinnaiah
-- 2021-Sep-19	    15			Revisions to capture and deal with error/validation 				Bob Knadler
--								  messages from executing pre-requisite Target Member Premium sp
-- 2022-Aug-30-22	16    		Added internal variables for input parameters     					Khurram Minhas
--						    	Added  WITH (NOLOCK)                        
-- ---------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spTargetActAdj]
(
    @ForecastID INT,
    @UserID CHAR(7),
	@TargetedMER DECIMAL(10,6),
	@TargetPremium DECIMAL(12,8),
	@ActAdjFactor DECIMAL(10,6) OUT,
	@NewMER1 DECIMAL(10,6) OUT,
	@ValidationMessage VARCHAR(MAX) OUT		--BK added 9/4/21
)
AS
BEGIN
	-- --------------------------------------------------------------------------------------------
    -- ---Variable declartion and constant population----------------------------------------------
	    DECLARE @XForecastID INT = @ForecastID, 
	    @XUserID CHAR(7) = @UserID,
		@XTargetedMER DECIMAL(10,6) = @TargetedMER,
		@XTargetPremium DECIMAL(12,8) = @TargetPremium 

		DECLARE @NewMER DECIMAL(10,6),
				@FirstTest DECIMAL(10,6),
				@Test DECIMAL(10,6),
				@TargetProfit1 DECIMAL(10,6),
				@ContractNumber VARCHAR(MAX),
				@PlanID VARCHAR(MAX),
				@SegmentId VARCHAR(MAX)  --Including @ContractNumber,@PlanID and @SegmentId



	--Bob K 9/19/21 - Variables to hold current values of SavedMERActAdj in case Target Mbr Prem fails and need to restore
		DECLARE	@OldC2PMERCostMultAdj DECIMAL(24,15) 
		DECLARE	@OldUserID CHAR(7)
		DECLARE	@OldLastUpdateTS DATETIME

	-- --------------------------------------------------------------------------------------------

		--Set NewMER to the current MER to prevent running this query, in case they target current MER

		SELECT @NewMER = 1 - (ExpensePMPM + UncollectedPremium + UserFee + Profit + InsurerFee) / TotalReqRev
		FROM dbo.fnAppGetBidSummary(@XForecastID)
		
		SELECT 
			@Test = @XTargetedMER+((cfp.Allowed-cfp.NetNonESRD)-mcfp.AddedBenefitNet
						+@XTargetedMER*(mcfp.AddedBenefitNet-(cfp.Allowed-cfp.NetNonESRD)+gbs.ExpensePMPM + gbs.UncollectedPremium + gbs.UserFee + gbs.Profit + gbs.InsurerFee)) 
						/ (cfp.Allowed-mcfp.AddedBenefitNet)
		FROM dbo.fnAppGetBidSummary(@XForecastID) gbs
		INNER JOIN dbo.CalcFinalPremium cfp WITH(NOLOCK)
		ON cfp.ForecastID=gbs.ForecastID
		INNER JOIN dbo.CalcMedicareCoveredFinalPricing mcfp WITH(NOLOCK)
		ON mcfp.ForecastID=cfp.ForecastID
		WHERE mcfp.DualEligibleTypeID=2
		
		-- Also equal to ProposedMER/CurrentMER
		SET @FirstTest = @Test

		--SELECT @Contract = ContractNumber + '-' + planid FROM SavedPlanHeader WHERE ForecastID = @ForecastID AND IsLiveIndex = 1 AND IsHidden = 0 
		SELECT @ContractNumber = ContractNumber 
				,@PlanID = PlanID 
				,@SegmentId = SegmentId 
			FROM dbo.SavedPlanHeader WITH(NOLOCK) 
			WHERE ForecastID = @XForecastID 
			  AND IsLiveIndex = 1 
			  AND IsHidden = 0 

	--Bob K 9/19/21 - Get current values of SavedMERActAdj in case Target Mbr Prem fails and need to restore
			SELECT @OldC2PMERCostMultAdj = A.C2PMERCostMultAdj
				  ,@OldUserID = A.UserID
				  ,@OldLastUpdateTS = A.LastUpdateDateTime
			  FROM (SELECT C2PMERCostMultAdj
						  ,MAX(UserID) AS UserID
						  ,MAX(LastUpdateDateTime) AS LastUpdateDateTime
					  FROM dbo.SavedMERActAdj WITH(NOLOCK)
					  WHERE ContractNumber = @ContractNumber AND PlanID = @PlanID AND SegmentId = @SegmentId
					  GROUP BY C2PMERCostMultAdj
					) A

			UPDATE dbo.SavedMERActAdj
			SET /*C2PActAdjUse = C2PActAdjUse * @FirstTest Old -- For Target MER Adjustment, make this step = SET */  
				C2PMERCostMultAdj = C2PMERCostMultAdj * @FirstTest 
				,UserID = @XUserID
				,LastUpdateDateTime = GETDATE()
			WHERE ContractNumber = @ContractNumber AND PlanID = @PlanID AND SegmentId = @SegmentId

--BK 9/19/21 - added getting error message and shifted this execution above accepting updates to SavedMERActAdj table
			EXEC dbo.spTargetPremiumReprice @XForecastID,@XUserID,@ValidationMessage = @ValidationMessage OUTPUT, @TargetPremium = @XTargetPremium							
			
--BK added 9/19/21
/*************************/
	IF @ValidationMessage = '0'  --Continue if there were no problems targeting member premium
        BEGIN

			SELECT @TargetProfit1 = ProfitPercent FROM dbo.SavedPlanAssumptions WITH(NOLOCK) WHERE ForecastID=@XForecastID

			SELECT @NewMER1 = 1 - (ExpensePMPM + UncollectedPremium + UserFee + Profit + InsurerFee) / TotalReqRev
			  FROM dbo.fnAppGetBidSummary(@XForecastID)


			SELECT @ActAdjFactor = @FirstTest


			UPDATE dbo.SavedForecastSetup SET IsRiskPlan = 1,LastUpdateByID=@XUserID WHERE ForecastID = @XForecastID
		
			INSERT INTO dbo.MERLog (ForecastID, InitialMER, TargetedMER, ActAdjFactor, TargetPremium, TargetProfit1, AuditUserID, AuditTime)
			VALUES (@XForecastID, @NewMER, @XTargetedMER,@FirstTest, @XTargetPremium, @TargetProfit1, @XUserID, GETDATE())
		END
	ELSE --Restore prior SavedMERActAdj values and set to need reprice because of failed pre-requixite targeting of member premium
		BEGIN
			UPDATE dbo.SavedMERActAdj
			  SET C2PMERCostMultAdj = @oldC2PMERCostMultAdj
				  ,UserID = @OldUserID
				  ,LastUpdateDateTime = @OldLastUpdateTS
			  WHERE ContractNumber = @ContractNumber AND PlanID = @PlanID AND SegmentId = @SegmentId

			UPDATE dbo.SavedForecastSetup
			  SET IsToReprice = 1
			  WHERE ForecastID = @XForecastID

		END
    -- --------------------------------------------------------------------------------------------
	-- --------------------------------------------------------------------------------------------
END
GO