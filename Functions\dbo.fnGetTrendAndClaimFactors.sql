SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnGetTrendAndClaimFactors]  ******/

-- FUNCTION NAME: fnGetClaimFactors 
--
-- AUTHOR: Tim Gao 
--
-- CREATED DATE: 3/26/3013
-- HEADER UPDATED: 3/26/3013
--
-- DESCRIPTION: Multiplies all ClaimFactors associated with the specified Benefitcategory
--  that are stored in SavedBenefitLevelClaimFactors(New MACTAPT data).  These values originate from
--  The returned Trend and claim factors needed to displayed in Actuarial Summary tab
--
-- PARAMETERS: 
--	Input:
--      @ForecastID
--
--  Output:
--
-- TABLES: 
--	Read:
--      LkpExtCMSWorksheet1Mapping
--      LkpIntBenefitCategory
--      LkpIntProjectionYear
--      PerIntClaimFactorTypeTest
--      SavedClaimFactorBenefitLevel
--      
--      SavedPlanDetail
--
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION	    CHANGES MADE						                                DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2013-Mar-26		1			Initial	Version														Tim Gao
-- 2014-Jan-09		2			Changing Table Name													Mike Deren
-- 2014-Jan-17		3			Updating Trend and Claim Factor columns								Mike Deren
-- 2014-Mar-10		4			Modified to include Sequestration Factors							Mike Deren
-- 2014-Apr-24		5			Moved Sequestration factors											Mike Deren
-- 2015-May-08      6           Added @SavedBenefitLevel,@SavedPlanDetail variable table and nolock for fetched table  Manisha Tyagi 
-- 2015-Nov-17		7			Added 35, 36 (Target MER) to claim factors.							Mark Freel
-- 2017-Oct-12		8			Removed partial SQS factor, no longer needed						Chris Fleming
-- 2020-Apr-08		9			Modified temp table SavedBenefitLevel to allow trend > 10			Alex Beruscha
-- 2021-Nov-11		10			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's	        Franklin Fu
-- 2022-Nov-21		11			Removed Distinct from @SavedBenefitLevel table insert
-- 2023-Aug-02		12			Added schema name before objects, Internal Parameter									
--								Created Primary key on table variable								Sheetal Patil 
-- 2024-MAY-05		13		    Using CTE for Improved Performance									Kiran Kola								
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetTrendAndClaimFactors]
(
  @ForecastID INT
)
RETURNS @Results TABLE(
  ForecastID INT,
  ClaimForecastID INT,
  MARatingOptionID INT,
  IsInNetwork INT,
  BenefitCategoryID INT,
  E2CTrendFactor DECIMAL(24, 15),
  C2BTrendFactor DECIMAL(24, 15),
  E2CClaimFactor DECIMAL(24, 15),
  C2BClaimFactor DECIMAL(24, 15),
  TrendAndClaimFactor DECIMAL(24, 15)
)
AS
BEGIN
DECLARE @XForecastID INT = @ForecastID;

  WITH SavedBenefitLevel AS (
    SELECT bl.ClaimForecastID, bl.ClaimFactorTypeID, bl.ProjectionYearID, bl.IsInNetwork, bl.BenefitCategoryID, bl.ClaimFactor
    FROM dbo.SavedBenefitLevelClaimFactors bl WITH(nolock)
    INNER JOIN dbo.SavedPlanDetail spd WITH(nolock)
    ON bl.ClaimForecastID = spd.ClaimForecastID
    WHERE spd.ForecastID = @XForecastID
  ),
  SavedPlanDetail AS (
    SELECT
      spd.ForecastID,
      spd.ClaimForecastID,
      spd.MARatingOptionID,
      cfd.IsInNetwork,
      ben.BenefitCategoryID,
      E2CTrendFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
        EXP(SUM(LOG(NULLIF(
          CASE WHEN py.ProjectionYearID =1 AND ctt.ClaimFactorTypeID IN (1,7,8,10,11,12,18,24,25,27,28,29)
            THEN cfd.ClaimFactor
            ELSE 1
          END
        ,0)))) END,
           C2BTrendFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
        EXP(SUM(LOG(NULLIF(
          CASE WHEN py.ProjectionYearID =2 AND ctt.ClaimFactorTypeID IN (1,7,8,10,11,12,18,24,25,27,28,29)
            THEN cfd.ClaimFactor
            ELSE 1
          END
        ,0)))) END,
      E2CClaimFactor =
        sqs.ProjectedSQS * CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
          EXP(SUM(LOG(NULLIF(
            CASE WHEN py.ProjectionYearID = 1 AND ctt.ClaimFactorTypeID IN (2,3,4,5,6,9,13,14,15,16,17,19,20,21,22,23,26,30,31,32,33,34,35,36)
              THEN cfd.ClaimFactor
              ELSE 1
            END
          ,0)))) END,
      C2BClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
        EXP(SUM(LOG(NULLIF(
          CASE WHEN py.ProjectionYearID = 2 AND ctt.ClaimFactorTypeID IN (2,3,4,5,6,9,13,14,15,16,17,19,20,21,22,23,26,30,31,32,33,34,35,36)
            THEN cfd.ClaimFactor
            ELSE 1
          END
        ,0)))) END,
      TrendAndClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0
        ELSE EXP(SUM(LOG(NULLIF(cfd.ClaimFactor,0)))) END
    FROM dbo.SavedPlanDetail spd WITH(nolock)
    INNER JOIN SavedBenefitLevel cfd ON spd.ClaimForecastID = cfd.ClaimForecastID
    INNER JOIN dbo.LkpIntProjectionYear py WITH(nolock) ON py.ProjectionYearID = cfd.ProjectionYearID
    INNER JOIN dbo.PerIntClaimFactorTypeTest ctt WITH(nolock) ON ctt.ClaimFactorTypeID = cfd.ClaimFactorTypeID
    INNER JOIN dbo.LkpExtCMSWorksheet1Mapping ws1 WITH(nolock) ON ctt.ColumnIDBPT = ws1.ColumnID
    INNER JOIN dbo.LkpIntBenefitCategory ben WITH(nolock) ON cfd.BenefitCategoryID = ben.BenefitCategoryID
    INNER JOIN dbo.CalcSQSFactors SQS WITH(nolock) ON SQS.ForecastID = spd.ForecastID
      AND SQS.MARatingOptionID = spd.MARatingOptionID
      AND SQS.BenefitCategoryID = ben.BenefitCategoryID
    WHERE spd.ForecastID = @XForecastID
    GROUP BY
      spd.ClaimForecastID,
      spd.MARatingOptionID,
      cfd.IsInNetwork,
      ben.BenefitCategoryID,
      spd.ForecastID,
      SQS.ProjectedSQS
  )
  INSERT INTO @Results SELECT * FROM SavedPlanDetail WHERE MARatingOptionID IN (1, 2);
  RETURN;

END
GO
