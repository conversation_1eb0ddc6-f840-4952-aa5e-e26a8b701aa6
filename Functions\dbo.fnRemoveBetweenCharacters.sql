SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================
-- Author:		Sheetal Patil 
-- Create date: 18-Aug-2023
-- Description:	Removes all characters between 2 passed characters and stuff , in between.


-- $HISTORY 
-- =======================================================================================================================================
-- DATE			VERSION		    CHANGES MADE						                    DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------
-- 18-Aug-2023		1			Initial Version							                Sheetal Patil
-- =======================================================================================================================================

CREATE FUNCTION [dbo].[fnRemoveBetweenCharacters]
(
    @inputString VARCHAR(MAX),
    @startChar CHAR(1),
    @endChar CHAR(1)
)
RETURNS VARCHAR(MAX)
AS
BEGIN
    DECLARE @startIndex INT, @endIndex INT, @length INT;

    SET @startIndex = CHARINDEX(@startChar, @inputString);
    SET @endIndex = CHARINDEX(@endChar, @inputString, @startIndex + 1);
    SET @length = @endIndex - @startIndex + 1;

    WHILE @startIndex > 0 AND @endIndex > 0
    BEGIN
        SET @inputString = STUFF(@inputString, @startIndex, @length, ',');
        SET @startIndex = CHARINDEX(@startChar, @inputString);
        SET @endIndex = CHARINDEX(@endChar, @inputString, @startIndex + 1);
        SET @length = @endIndex - @startIndex + 1;
    END

    RETURN @inputString;
END
GO
