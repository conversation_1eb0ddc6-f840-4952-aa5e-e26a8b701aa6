SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetMABPTWS1Digits] ******/

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:	fnAppGetMABPTWS1Digits
--
-- CREATOR:			<PERSON> Lake 
--
-- CREATED DATE:	2008-AUG-15
--
-- DESCRIPTION:		Function returns a list of the number of significant digits needed for the Avg Cost 
--					field found on WS 1.  The number of decimals will always be 15 - x, where x is the length
--					of the whole number
--		
-- PARAMETERS:
--  Input  :		@ForecastID 
--  Output :		@Results
--
-- TABLES : 
--	Read :			CalcPlanExperiencebyBenefitCat
--					LkpExtCMSBidServiceCategory
--					LkpIntBenefitCategory
--					SavedPlanDetail
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetSafeDivisionResult
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2008-AUG-15		1			Initial Version														Brian Lake
-- 2009-FEB-25		2			Revised for 2010 pricing											Sandy Ellis 
-- 2009-FEB-26		3			Removed @UserID														Brian Lake
-- 2009-MAR-17		4			Data types															Sandy Ellis
-- 2009-APR-01		5			Restricted to dual eligible type id 3 since this should be			Sandy Ellis
--									for WS1 values only
-- 2009-MAY-15		6			Removed call to SavedPlanTypeCostAndUseDetail for 2010				Brian Lake
-- 2009-JAN-04		7			Added statement for 2011, changed join to reflect					Nick Skeen
--									CalcPlanExperiencebyBenefitcat changes
-- 2010-JUN-16		8			Revised for 2012 database											Jake Gaecke
-- 2010-AUG-23		9			Removed PlanVersion													Jake Gaecke
-- 2011-JAN-05      10			Removed SavedStandardCostAndUseDetail								Michael Siekerka
-- 2011-JAN-05      11			Changed DETypeID from 3 to 2										Michael Siekerka
-- 2011-JAN-07		12			Removed join to SavedPlanHeader										Joe Casey
-- 2022-OCT-12		13			Remove fnMAAnnualTrend reference since function has been dropped	Peter Schelble
-- 2023-Aug-03		14			Added NOLOCK, internal parameter and Table schema				    Sheetal Patil
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnAppGetMABPTWS1Digits]
    (@ForecastID INT)
RETURNS @Results TABLE
    (ForecastID          INT
    ,ServiceCategoryCode VARCHAR(4)
    ,BaseUnits           DECIMAL(23, 15)
    ,AvgCostDecimals     INT)
AS
    BEGIN
DECLARE @XForecastID INT = @ForecastID 
        INSERT  @Results
        SELECT      DISTINCT
                    SPD.ForecastID
                   ,BSC.ServiceCategoryCode
                   ,BaseUnits = -- accurate to 15 decimal places
                   CONVERT (DECIMAL(26, 15), SUM (PEB.INUnits + PEB.OONUnits))
                   ,AvgCostDecimals = 15
                                      - LEN (
                                        FLOOR (
                                        dbo.fnGetSafeDivisionResult (
                                        CONVERT (DECIMAL(38, 28), SUM (PEB.INAllowed + ISNULL (PEB.OONAllowed, 0))) * 12000
                                       ,SUM (INUnits + OONUnits))))
    FROM dbo.SavedPlanDetail SPD WITH (NOLOCK)
    INNER JOIN dbo.CalcPlanExperiencebyBenefitCat PEB WITH (NOLOCK)
               ON SPD.ForecastID = PEB.ForecastID
    INNER JOIN dbo.LkpIntBenefitCategory BC  WITH (NOLOCK) 
               ON PEB.BenefitCategoryID = BC.BenefitCategoryID
    INNER JOIN dbo.LkpExtCMSBidServiceCategory BSC WITH (NOLOCK)
               ON BC.BidServiceCatID = BSC.BidServiceCategoryID
    WHERE SPD.ForecastID = @XForecastID
                    AND SPD.MARatingOptionID = 1 --Experience rating.
                    AND PEB.DualEligibleTypeID = 2
        GROUP BY    SPD.ForecastID
                   ,BSC.ServiceCategoryCode;
        RETURN;
    END;
GO
