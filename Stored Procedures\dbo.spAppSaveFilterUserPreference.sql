SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================
-- Author: <PERSON><PERSON><PERSON>
-- Create date: 26-01-2018
-- Description:  Save Filter User Prefrence
--
--
-- PARAMETERS:
-- Input:

-- TABLES:
-- Read:                AppSavedFilterUserPreference
-- Write:	            AppSavedFilterUserPreference
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:


-- $HISTORY

-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2018-Jan-01  1		  Initial version.											Manisha Tyagi
-- 2018-Oct-30	2		  Added columns to save indicators							Deepali Mittal
-- 2018-DEC-17  3         Added SELECT ERROR_MESSAGE() AS ErrorMessage              Kritika Singh
--                        in catch block
-- 2018-DEC-18  4         Added raiserror                                           Kritika Singh
-- 2018-Dec-18	5		  Updated Error message and included logging exception	    Pooja Dahiya
-- 2020-Mar-18  6		  Included IsIncludeInTrend column                          Satyam Singhal
-- 2024-NOV-22	7		  Added SNP Type Name data to filter preference and         Alex Brandt
--                            removed IsSNP reference
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spAppSaveFilterUserPreference]
    @SelectedYearList VARCHAR(MAX) = NULL,
    @SelectedDivisionList VARCHAR(MAX) = NULL,
    @SelectedRegionList VARCHAR(MAX) = NULL,
    @SelectedMarketList VARCHAR(MAX) = NULL,
    @SelectedProductList VARCHAR(MAX) = NULL,
    @SelectedSNPTypeNameList VARCHAR(MAX) = NULL,
    @SelectedPlanList VARCHAR(MAX) = NULL,
    @SelectedForecastIdList VARCHAR(MAX) = NULL,
    @SelectedSortedPlanList VARCHAR(MAX) = NULL,
    @MAPD VARCHAR(10) = NULL,
    @offModel VARCHAR(10) = NULL,
    @hidden VARCHAR(10) = NULL,
    @IncludeInTrend VARCHAR(10) = NULL,
    @LastUpdateByID VARCHAR(7),
    @Result BIT OUT
AS
BEGIN
    BEGIN TRANSACTION;
    BEGIN TRY
        IF EXISTS
        (
            SELECT TOP 1
                   *
            FROM dbo.AppSavedFilterUserPreference
            WHERE UserID = @LastUpdateByID
        )
        BEGIN
            UPDATE [dbo].[AppSavedFilterUserPreference]
            SET SelectedYearList = @SelectedYearList,
                SelectedDivisionList = @SelectedDivisionList,
                SelectedRegionList = @SelectedRegionList,
                SelectedMarketList = @SelectedMarketList,
                SelectedProductList = @SelectedProductList,
				SelectedSNPTypeNameList = @SelectedSNPTypeNameList,
                SelectedPlanList = @SelectedPlanList,
                SelectedForecastIdList = @SelectedForecastIdList,
                SelectedSortedPlanList = @SelectedSortedPlanList,
                IsMAPD = @MAPD,
                IsOffModel = @offModel,
                isHidden = @hidden,
                IsIncludeInTrend = @IncludeInTrend,
                LastUpdateDateTime = GETDATE()
            WHERE UserID = @LastUpdateByID;
        END;
        ELSE
            INSERT INTO [dbo].[AppSavedFilterUserPreference]
            (
                [UserID],
                [SelectedYearList],
                [SelectedDivisionList],
                [SelectedRegionList],
                [SelectedMarketList],
                [SelectedProductList],
				[SelectedSNPTypeNameList],
                [SelectedPlanList],
                [SelectedForecastIdList],
                [SelectedSortedPlanList],
                IsMAPD,
                IsOffModel,
                isHidden,
                IsIncludeInTrend,
                [LastUpdateDateTime]
            )
            SELECT @LastUpdateByID,
                   @SelectedYearList,
                   @SelectedDivisionList,
                   @SelectedRegionList,
                   @SelectedMarketList,
                   @SelectedProductList,
				   @SelectedSNPTypeNameList,
                   @SelectedPlanList,
                   @SelectedForecastIdList,
                   @SelectedSortedPlanList,
                   @MAPD,
                   @offModel,
                   @hidden,
                   @IncludeInTrend,
                   GETDATE();
        SET @Result = 1;
            COMMIT TRANSACTION;
        END TRY
      BEGIN CATCH
				SET @Result = 0;
			--SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'
			 DECLARE @ErrorMessage NVARCHAR(4000);
			 DECLARE @ErrorSeverity INT;
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000);
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

			ROLLBACK TRANSACTION;

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID

        END CATCH;
    END;
GO
