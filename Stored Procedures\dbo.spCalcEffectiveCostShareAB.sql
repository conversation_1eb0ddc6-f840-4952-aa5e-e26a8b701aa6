SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcEffectiveCostShareAB
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2021-Aug-01
-- HEADER UPDATED: 2021-Aug-01
--
-- DESCRIPTION: Calculates effective cost share for day range benefits.
--
-- PARAMETERS:
--	Input: 
--     @Level ('Plan' if temporary calc for unsaved plan changes in benefit editor, 'Nationwide' if permanently updating all plans)
--      
--	Output:
--
-- TABLES:
--	Read:
--		#TempDRD (for live calculation within benefit editor)
--		Benefits_SavedBenefitOptionDetail
--      Benefits_SavedBenefitOptionDayRangeDetail
--		CalcBenefitCategoryUtilization
--		SavedPlanInfo
--		SavedMarketInfo
--		LkpIntCostShareDampening
--	Write:
--		Benefits_SavedBenefitOptionDetail (for nationwide effective copay update)
--      #EffCSFinal (for live calculation in benefit editor)
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             				DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2021-Aug-01		1			Initialization of effective cost share calc with Dampening				Michael Manes
-- 2021-Sep-13		2			Added 2 parameters PlanInfoID and datable of SharedCost Values			Paul Smith
-- 2021-Dec-2       3           Added a step that validates that limits do not break a CMS limit
--                              The limit is chosen arbitrary, and an additional coding needs to 
--								be done for the final revision. Adding Limit_Test variable to the
--								output table															Aleksandar Dimitrijevic
-- 2021-Dec-20      4           Rounding to two decimal points for BenefitValue                         Surya Murthy
-- 2021-Jan-22      5           DF-9261 fix for Factor value null to 1                                  Surya Murthy
-- 2022-Sep-29	    6           Remove CSD Factors from plan level calculation                          Alex Hutchison, Aleksandar Dimitrijevic
-- 2022-Nov-22      7           Added alias name for effCS in plan level                                Archana Sahu
-- ---------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spCalcEffectiveCostShareAB]
@Level CHAR(10),
@tblSavedBenefitOptiondayrange Benefits_SavedBenefitOptionDayRangeDetail_DataTable READONLY,
@PlanInfoID int

AS
/* This procedure can either be run at a plan level when inside the benefit editor for on-the-fly effective copay calculation,
	or for updating nationwide effective copay calc when backend information updates like the utilization distribution*/

/*Initialize table with day range benefits*/
IF (SELECT  OBJECT_ID ('tempdb..#TempDRD')) IS NOT NULL DROP TABLE #TempDRD;

CREATE TABLE #TempDRD
    (PlanInfoID        SMALLINT
    ,BenefitOptionID   TINYINT
    ,BenefitCategoryID SMALLINT
    ,BenefitOrdinalID  TINYINT
    ,IsOON             BIT
    ,DayRangeBegin     TINYINT
    ,DayRangeEnd       TINYINT
    ,BenefitValue      DECIMAL(12, 6));

/*When running plan level with unsaved data, use a temporary table from the live benefit editor values*/
IF @Level = 'Plan'
    BEGIN
        INSERT INTO #TempDRD
            (PlanInfoID
            ,BenefitOptionID
            ,BenefitCategoryID
            ,BenefitOrdinalID
            ,IsOON
            ,DayRangeBegin
            ,DayRangeEnd
            ,BenefitValue)
        SELECT  @PlanInfoID as PlanInfoID
				,'0' as BenefitOptionID
               ,BenefitCategoryID
               ,BenefitOrdinalID
               ,IsOON
               ,DayRangeBegin
               ,DayRangeEnd
               ,Copay as BenefitValue
        FROM    @tblSavedBenefitOptiondayrange;
    END;

/*When running nationwide, use the actual DayRangeDetail benefitstable*/
IF @Level = 'Nationwide'
    BEGIN
        INSERT INTO #TempDRD
            (PlanInfoID
            ,BenefitOptionID
            ,BenefitCategoryID
            ,BenefitOrdinalID
            ,IsOON
            ,DayRangeBegin
            ,DayRangeEnd
            ,BenefitValue)
        SELECT  PlanInfoID
               ,BenefitOptionID
               ,BenefitCategoryID
               ,BenefitOrdinalID
               ,IsOON
               ,DayRangeBegin
               ,DayRangeEnd
               ,BenefitValue
        FROM    dbo.Benefits_SavedBenefitOptionDayRangeDetail;
    END;

IF (SELECT  OBJECT_ID ('tempdb..#SumUtil')) IS NOT NULL DROP TABLE #SumUtil;

/* Pull in plan benefit day ranges and sum benefit utilization per day range*/
SELECT      drd.PlanInfoID
           ,drd.BenefitOptionID
           ,drd.BenefitCategoryID
           ,drd.BenefitOrdinalID
           ,drd.IsOON
           ,drd.DayRangeBegin
           ,drd.DayRangeEnd
           ,drd.BenefitValue
           ,SUM (CASE WHEN bcu.DayRangeBegin >= drd.DayRangeBegin
                           AND  bcu.DayRangeEnd <= drd.DayRangeEnd THEN bcu.UtilizationPercent
                      ELSE 0 END) AS UtilizationFactor
INTO        #SumUtil
FROM        #TempDRD drd
LEFT JOIN   dbo.CalcBenefitCategoryUtilization bcu
       ON drd.BenefitCategoryID = bcu.BenefitCategoryID
GROUP BY    drd.PlanInfoID
           ,drd.BenefitOptionID
           ,drd.BenefitCategoryID
           ,drd.BenefitOrdinalID
           ,drd.IsOON
           ,drd.DayRangeBegin
           ,drd.DayRangeEnd
           ,drd.BenefitValue;



IF (SELECT  OBJECT_ID ('tempdb..#EffCSFinal')) IS NOT NULL
    DROP TABLE #EffCSFinal;

/*Sumproduct of copay and utilization percent over all benefit levels*/
/*If plan level, output these values to the benefit editor*/
SELECT      PlanInfoID
           ,BenefitOptionID
           ,BenefitCategoryID
           ,IsOON
           ,ROUND(SUM (BenefitValue * UtilizationFactor),2) AS effCS
INTO        #EffCSFinal
FROM        #SumUtil
GROUP BY    PlanInfoID
           ,BenefitOptionID
           ,BenefitCategoryID
           ,IsOON;


IF @Level = 'Plan'
	BEGIN
		select 
		PlanInfoID
        ,BenefitOptionID
        ,BenefitCategoryID
        ,IsOON
        ,effCS as BenefitValue
		FROM #EffCSFinal
	END


/*If running nationwide, update the benefit values with the effective cost share for day range benefits in Benefits_SavedBenefitOptionDetail*/

IF @Level = 'Nationwide'
    BEGIN

        UPDATE      t1
        SET         t1.BenefitValue = t2.BenefitValue
                   ,t1.LastUpdateByID = 'MdlTeam'
                   ,t1.LastUpdateDateTime = GETDATE ()
        FROM        dbo.Benefits_SavedBenefitOptionDetail t1
       INNER JOIN   #EffCSFinal t2
               ON t1.PlanInfoID = t2.PlanInfoID
                  AND   t1.BenefitOptionID = t2.BenefitOptionID
                  AND   t1.BenefitCategoryID = t2.BenefitCategoryID
                  AND   t1.IsOON = t2.IsOON
        WHERE       t1.BenefitTypeID = 5;
    END;
GO
