SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO




-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcProjRiskscoreFromTarget
--
-- CREATOR: Sule <PERSON>
--
-- CREATED DATE: 2011-Mar-22
-- HEADER UPDATED: 
--
-- DESCRIPTION: Stored Procedure responsible for Calculating projected risk score from target risk score
-- PARAMETERS:
--  Input:
--		@ForecastID INT,
--		@ProjNonDE#RiskScore DECIMAL(16,15),
--		@ProjDE#RiskScore DECIMAL(16,15),
--		@ProjRiskScore DECIMAL(16,15),
--		@Justification VARCHAR(500),
--		@<PERSON><PERSON><PERSON>AR(7)
--  Output:
--
-- TABLES:
--  Read:
--      SavedPlanRiskScoreOverride
--      
--  Write:
--      SavedPlanRiskScoreOverride
--      SavedPlanRiskFactorDetail
--     
--      
--
-- VIEWS:
--
-- FUNCTIONS:
--      
--      
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE														 DEVELOPER        
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Mar-22      1        Initial Version															Sule Dauda
-- 2019-Jun-28		2		 Replace SavedPlanHeader with SavedForecastSetup							Pooja Dahiya
-- 2019-Oct-30		3		Removed 'HUMAD\' from UserID												Chhavi Sinha
-- ---------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcProjRiskscoreFromTarget]
    @ForecastID INT,
    @ProjNonDE#RiskScore DECIMAL(16,15),
    @ProjDE#RiskScore DECIMAL(16,15),
	@ProjRiskScore DECIMAL(16,15),
	@Justification VARCHAR(500),
    @UserID CHAR(7)
AS
SET NOCOUNT ON;
BEGIN
	DECLARE @ProjW8edRiskScore DECIMAL(16,15)
	DECLARE @NewProjNonDE#RiskScore DECIMAL(16,15)
	DECLARE @NewProjDE#RiskScore DECIMAL(16,15)
	
	SELECT @ProjW8edRiskScore = ProjW8edRiskScore FROM fnAppGetBenchmarkSummary(@ForecastID)

-- Calculates projected risk scores from target risk score
	SET @NewProjNonDE#RiskScore = (@ProjNonDE#RiskScore/@ProjW8edRiskScore)* @ProjRiskScore
	SET @NewProjDE#RiskScore = (@ProjDE#RiskScore/@ProjW8edRiskScore)* @ProjRiskScore
	
	
---Captures target risk score and justification for change
	IF EXISTS (SELECT 1 FROM SavedPlanRiskScoreOverride WHERE ForecastID = @ForecastID)
		BEGIN
			 UPDATE SavedPlanRiskScoreOverride
                SET TargetRiskScore = @ProjRiskScore,
					Justification = @Justification,
                    LastUpdateByID = @UserID,
					LastUpdateDateTime = GETDATE()					
                WHERE ForecastID = @ForecastID
			
		END
	ELSE
		BEGIN
			INSERT INTO SavedPlanRiskScoreOverride
			(
				ForecastID,
				TargetRiskScore,
				Justification,
				LastUpdateByID,
				LastUpdateDateTime
			)
			 VALUES 
			 (
				@ForecastID,
				@ProjRiskScore,
				@Justification,
				@UserID,
				GETDATE()
			)  			
		END

---Updates SavedPlanRiskFactorDetail based on calculated projected risk scores    
    BEGIN

		-- Update the Non-DE# RiskScores
		BEGIN
			UPDATE SavedPlanRiskFactorDetail
			SET RiskFactor = @NewProjNonDE#RiskScore,
				LastUpdateByID = @UserID,
				LastUpdateDateTime = GETDATE()					
			WHERE ForecastID = @ForecastID
				 AND DemogIndicator = 1
				 AND IsExperience = 0
		END
        
        -- Update the DE# RiskScores
		BEGIN
			UPDATE SavedPlanRiskFactorDetail
			SET RiskFactor = @NewProjDE#RiskScore,
				LastUpdateByID = @UserID,
				LastUpdateDateTime = GETDATE()
		   WHERE ForecastID = @ForecastID
				AND DemogIndicator = 2
				AND IsExperience = 0
		END
        
	END
    
    --Set Reprice = 1
    BEGIN
        UPDATE SavedForecastSetup
        SET IsToReprice = 1,
		LastUpdateByID=@UserID,
		LastUpdateDateTime=GETDATE()
        WHERE ForecastID = @ForecastID
    END
END
GO
