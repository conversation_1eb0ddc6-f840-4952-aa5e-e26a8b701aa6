SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcSQSFactors
--
-- CREATOR:			<PERSON>
--
-- CREATED DATE:	2014-FEB-17
--
-- DESCRIPTION:		Calculates projected sequestration (SQS) factors. 
--					Note that the ProjectedSQS and ProjectedSQSNoDampening fields are calculated using total allowed, and are 
--						used by the Pricing Model. Two additional fields (PREPProjectedSQSIncludeInCostShareBasis and 
--						PREPProjectedSQSNoDampeningIncludeInCostShareBasis) are calculated based only on allowed that is 
--						included in the Cost Share Basis; these fields are only for PREP use and are not used elsewhere
--						in the Pricing Model. 
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT
--					@UserID CHAR(7)
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			CalcPlanExperienceByBenefitCatNonSQS
--					CalcPlanExperienceByBenefitCat
--					PerExtCMSValues
--
--  Write:			CalcSQSFactors
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS:	NONE
--
-- OVERRIDE EXAMPLE:
--					ProjectedSQS Factor = .98
--					Dampening Factor = .80
--					Final ProjectedSQS Factor = 1-[(1 - .98)*.80] = .984
--					In this example, the override factor scales the reduction amount of 2% by 80% and then 
--						reapplies it as a multiplier.
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																			DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2014-FEB-27		1			Initial Version                                                                         Mike Deren
-- 2014-MAR-13		2			Corrected Override Formula to scale the % of reduction to the Proj SQS Factor           Aaron Schaffer   
-- 2014-NOV-26		3           Renamed tables CalcPlanExperienceByBenefitCatNonSQS9mth and         Amit Tiwari
--									CalcPlanExperienceByBenefitCat9mth to CalcPlanExperienceByBenefitCatNonSQS  
--									and CalcPlanExperienceByBenefitCat respectivily and also renamed aliases too. 
-- 2017-FEB-10					Implementing permanent SQS Factor														Jordan Purdue
-- 2017-OCT-12		4			Removed Partial SQS factor, no longer needed to backout SQS, updated SQS formula		Chris Fleming & Jordan Purdue
-- 2018-JAN-30		5			Add column for SQS factor without the dampening factor (for SCT use)					Matthew Evans
-- 2018-FEB-5		6			Changing AllowedWS1 to be AllowedWS1WRPP												Jordan Purdue
-- 2019-OCT-30		7			Removed 'HUMAD\' to UserID																Chhavi Sinha 
-- 2021-DEC-02		8			Changes for 2023 bids where 2021 base data has no sequestration; 						Bob Knadler
--                                   Final after Denward review
-- 2023-JAN-10		9			Reverting 2023 bid changes																Michael Manes
-- 2024-OCT-04		10			Add handling for new IsIncludeInCostShareBasis flag in the base data tables				Jake Lewis
-- 2024-NOV-08		11			Add two new PREP fields for use by the PREP; these fields are calculated only using 
--									allowed that is included in the Cost Share Basis									Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spCalcSQSFactors]
    (
    @ForecastID INT
   ,@UserID     CHAR(7))
AS

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @XForecastID INT = @ForecastID;
DECLARE @XUserID CHAR(7) = @UserID;

DELETE  FROM dbo.CalcSQSFactors WHERE   ForecastID = @XForecastID;

INSERT INTO dbo.CalcSQSFactors
    (PlanYearID
    ,ForecastID
    ,MARatingOptionID
    ,BenefitCategoryID
    ,DualEligibleTypeID
    ,ProjectedSQS
    ,ProjectedSQSNoDampening
    ,PREPProjectedSQSIncludeInCostShareBasis
    ,PREPProjectedSQSNoDampeningIncludeInCostShareBasis
    ,LastUpdateByID
    ,LastUpdateDateTime)
SELECT      SQS.PlanYearID AS PlanYear
           ,SQS.ForecastID AS ForecastID
           ,SQS.MARatingOptionID AS MARatingOptionID
           ,SQS.BenefitCategoryID AS BenefitCategory
           ,SQS.DualEligibleTypeID AS DualEligibleTypeID
           ,1
            - (pec.SQSDampeningFactor
               * (1
                  - dbo.fnGetSafeDivisionResultReturnOne (
                    SUM (SQS.AllowedWS1WRPP), SUM (NonSQS.INAllowed + NonSQS.OONAllowed)))) AS ProjectedSQS
           ,dbo.fnGetSafeDivisionResultReturnOne (SUM (SQS.AllowedWS1WRPP), SUM (NonSQS.INAllowed + NonSQS.OONAllowed)) AS ProjectedSQSNoDampening                              --No Dampening Factor
           ,1
            - (pec.SQSDampeningFactor
               * (1
                  - dbo.fnGetSafeDivisionResultReturnOne (
                    SUM (CASE WHEN NonSQS.IsIncludeInCostShareBasis = 1 THEN SQS.AllowedWS1WRPP ELSE 0 END)
                   ,SUM (
                    CASE WHEN NonSQS.IsIncludeInCostShareBasis = 1 THEN NonSQS.INAllowed + NonSQS.OONAllowed ELSE 0 END)))) AS PREPProjectedSQSIncludeInCostShareBasis          --Only used by the PREP
           ,dbo.fnGetSafeDivisionResultReturnOne (
            SUM (CASE WHEN NonSQS.IsIncludeInCostShareBasis = 1 THEN SQS.AllowedWS1WRPP ELSE 0 END)
           ,SUM (CASE WHEN NonSQS.IsIncludeInCostShareBasis = 1 THEN NonSQS.INAllowed + NonSQS.OONAllowed ELSE 0 END)) AS PREPProjectedSQSNoDampeningIncludeInCostShareBasis    --No Dampening Factor, only used by the PREP
           ,@XUserID AS LastUpdateByID
           ,GETDATE () AS LastUpdateDateTime
FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS NonSQS WITH (ROWLOCK)
INNER JOIN  dbo.CalcPlanExperienceByBenefitCat SQS WITH (ROWLOCK)
        ON NonSQS.PlanYearID = SQS.PlanYearID
           AND  NonSQS.ForecastID = SQS.ForecastID
           AND  NonSQS.MARatingOptionID = SQS.MARatingOptionID
           AND  NonSQS.BenefitCategoryID = SQS.BenefitCategoryID
           AND  NonSQS.DualEligibleTypeID = SQS.DualEligibleTypeID
           AND  NonSQS.IsIncludeInCostShareBasis = SQS.IsIncludeInCostShareBasis
INNER JOIN  dbo.PerExtCMSValues pec WITH (NOLOCK)
        ON NonSQS.PlanYearID = pec.PlanYearID
WHERE       SQS.ForecastID = @XForecastID
            AND SQS.DualEligibleTypeID = 2  --Both Dual and Non-Dual
GROUP BY    SQS.PlanYearID
           ,SQS.ForecastID
           ,SQS.MARatingOptionID
           ,SQS.BenefitCategoryID
           ,SQS.DualEligibleTypeID
           ,pec.SQSDampeningFactor;
GO
