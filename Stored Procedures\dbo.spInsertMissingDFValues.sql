SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Author: <PERSON>
-- Create date: 01-Jan-2023  
-- Description: Checks to see if a PlanInfoID has a corresponding record in SavedDFFinance and SavedDFClaims for a given DFVersionID.
-- If no match is found, a default record is inserted. 
-- This is required to fix an issue with claims runs.
-- 
--
-- PARAMETERS:  
-- Input:  
-- @DfVersionID int
-- TABLES:
--	Read:
--      SavedPlanInfo
--      BidModel_Isar
--		SavedDFFinance
--		SavedDFClaims
--		SavedDFVersionHeader
--	Write:
--		SavedDFFinance
--		SavedDFClaims
-- $HISTORY   
-- Sample Call:
-- EXEC [dbo].[spInsertMissingDFValues] 11
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE				VERSION		CHANGES MADE                                                DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------    
-- 01-Jan-2023		1			Initial version.											Adam Gilbert 
-- 01-Dec-2023      2			Added rows for demog3, Default county = 99999				Adam Gilbert
-- 24-Oct-2024		3			Cost Share Basis: handle and return new fields from 
--								SavedDFClaims												Franklin Fu
-- ----------------------------------------------------------------------------------------------------------------------  
CREATE PROCEDURE [dbo].[spInsertMissingDFValues]
@DFVersionID INT
AS

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;

DECLARE @PlanYear SMALLINT;
DECLARE @GrouperID VARCHAR(8) = '00000000';
DECLARE @CountyCode VARCHAR(5) = '99999';
DECLARE @XDFVersionID INT = @DFVersionID;

    BEGIN TRY

        BEGIN TRANSACTION;
        /*Set the plan year*/
        SELECT  @PlanYear = YEAR (IncurredEndDate)
        FROM    dbo.SavedDFVersionHeader WITH (NOLOCK)
        WHERE   DFVersionID = @XDFVersionID;

        /*Insert placeholder records for SavedDFFinance*/
        INSERT INTO dbo.SavedDFFinance
            (DFVersionID
            ,PlanInfoID
            ,IncurredYear
            ,DemogIndicator
            ,SSStateCountyCD
            ,GrouperID
            ,MemberMonths
            ,MAReconRiskScore
            ,CapDirectPay
            ,CapSurplusDeficit
            ,CapMSBs
            ,CapProviderRewards
            ,TotalOPBInBids
            ,TotalOPBNotInBids
            ,MAPremium
            ,PDPremium
            ,Quality
            ,PDNetPaid
            ,PartDCapAdj
            ,MSCapAdj
            ,PartBRxRebates)
        SELECT      @XDFVersionID                           -- DFVersionID - smallint
                   ,spi.PlanInfoID                          -- PlanInfoID - smallint
                   ,@PlanYear                               -- IncurredYear - smallint
                   ,demogs.demogindicator                   -- DemogIndicator - tinyint
                   ,ISNULL (bmi.CountyCode, @CountyCode)    -- SSStateCountyCD - varchar(5) 
                   ,@GrouperID                              -- GrouperID - varchar(8)
                   ,0                                       -- MemberMonths - int
                   ,1                                       -- MAReconRiskScore - decimal(19, 9)
                   ,0                                       -- CapDirectPay - decimal(19, 9)
                   ,0                                       -- CapSurplusDeficit - decimal(19, 9)
                   ,0                                       -- CapMSBs - decimal(19, 9)
                   ,0                                       -- CapProviderRewards - decimal(19, 9)
                   ,0                                       -- TotalOPBInBids - decimal(19, 9)
                   ,0                                       -- TotalOPBNotInBids - decimal(19, 9)
                   ,0                                       -- MAPremium - decimal(19, 9)
                   ,0                                       -- PDPremium - decimal(19, 9)
                   ,0                                       -- Quality - decimal(19, 9)
                   ,0                                       -- PDNetPaid - decimal(19, 9)
                   ,0                                       -- PartDCapAdj - decimal(19, 9)
                   ,0                                       -- MSCapAdj - decimal(19, 9)
                   ,0                                       -- PartBRxRebates - decimal(19, 9) 
        FROM        dbo.SavedPlanInfo spi WITH (NOLOCK)
        LEFT JOIN   dbo.SavedDFFinance sdfc WITH (NOLOCK)
               ON spi.PlanInfoID = sdfc.PlanInfoID
                  AND   sdfc.DFVersionID IN (@XDFVersionID)
        LEFT JOIN   dbo.BidModel_ISAR bmi WITH (NOLOCK) --Create record for all county codes in Bidmodel_Isar for PREP use
               ON spi.CPS = bmi.ContractPBPSegmentID
                  AND   spi.PlanYear = bmi.PlanYearID
       CROSS APPLY  (SELECT 1 AS demogindicator UNION SELECT    3 AS demogindicator) demogs    -- create record for demog 1 and 3 (prep use)
        WHERE       1 = 1
                    AND spi.PlanYear = @PlanYear
                    AND sdfc.DFVersionID IS NULL;

        /*Insert placeholder records for SavedDFFinance*/
        INSERT INTO dbo.SavedDFClaims
            (DFVersionID
            ,PlanInfoID
            ,IncurredYear
            ,DemogIndicator
            ,GrouperID
            ,BenefitCategoryID
            ,Paid
            ,MbrCS
            ,PymtReductionAmt
            ,AdmitCnt
            ,UnitCnt
            ,EncounterMbrCS
            ,EncounterAdmitCnt
            ,EncounterUnitCnt
            ,DelegatedEncounterMbrCS
            ,DelegatedEncounterAdmitCnt
            ,DelegatedEncounterUnitCnt
            ,CapDirectPayEPaidClaims
            ,CapDirectPayDEPaidClaims
            ,CapDirectPayBCAllocated
            ,CapDirectPayScenarioAllocated
            ,CapDirectPayBCAllocatedDelegated
            ,CapDirectPayScenarioAllocatedDelegated
            ,CapSurplusDeficitEPaidClaims
            ,CapSurplusDeficitDEPaidClaims
            ,CapSurplusDeficitBCAllocated
            ,CapSurplusDeficitScenarioAllocated
            ,CapSurplusDeficitBCAllocatedDelegated
            ,CapSurplusDeficitScenarioAllocatedDelegated
            ,CapMSBs
            ,CapProviderRewards
            ,OPBOtherNonHospitalBCAllocated
            ,OPBOtherNonHospitalScenarioAllocated
            ,OPBClaimsBuyDownBCAllocated
            ,OPBClaimsBuyDownScenarioAllocated
            ,OPBProviderClaimSettlementsBCAllocated
            ,OPBProviderClaimSettlementsScenarioAllocated
            ,OPBAccessFeesAndOtherBCAllocated
            ,OPBAccessFeesAndOtherScenarioAllocated
            ,PartDCapAdj
            ,PartDCapAdjDelegated
            ,MSCapAdj
            ,MSCapAdjDelegated
            ,SubCapAdj
            ,SubCapAdjExclude
            ,MedicaidAdjPaid
            ,MedicaidAdjMbrCS
            ,ImplicitMarginPaid
            ,ImplicitMarginMbrCS
            ,AdditiveAdjPaid
            ,AdditiveAdjMbrCS
            ,AdditiveAdjAdmits
            ,AdditiveAdjUnits
            ,ModelOfCareAdjPaid
            ,ModelOfCareAdjMbrCS
            ,ModelOfCareAdjUnits
            ,UCAdmitsAdj
            ,UCUnitsAdj
            ,PartBRxRebatesPharmacy
            ,PartBRxRebatesQN
            ,RelatedPartiesAdj
            ,ProfitAdj
            ,MSBPaid
            ,MSBMbrCS
            ,MSBUnits
            ,MSBReductionCap
            ,MSBReductionClaimsPaid
            ,MSBReductionClaimsMbrCS
            ,MSBReductionClaimsUnits
            ,MSBReductionQuality)
        SELECT      @XDFVersionID           -- DFVersionID - int
                   ,spi.PlanInfoID          -- PlanInfoID - smallint 
                   ,@PlanYear               -- IncurredYear - smallint
                   ,demogs.demogindicator   -- DemogIndicator - tinyint
                   ,@GrouperID              -- GrouperID - varchar(8)
                   ,50                      -- BenefitCategoryID - smallint
                   ,0                       -- Paid - decimal(19, 9)
                   ,0                       -- MbrCS - decimal(19, 9)
                   ,0                       -- PymtReductionAmt - decimal(19, 9)
                   ,0                       -- AdmitCnt - decimal(19, 9)
                   ,0                       -- UnitCnt - decimal(19, 9)
                   ,0                       -- EncounterMbrCS - decimal(19, 9)
                   ,0                       -- EncounterAdmitCnt - decimal(19, 9)
                   ,0                       -- EncounterUnitCnt - decimal(19, 9)
                   ,0                       -- DelegatedEncounterMbrCS - decimal(19, 9)
                   ,0                       -- DelegatedEncounterAdmitCnt - decimal(19, 9)
                   ,0                       -- DelegatedEncounterUnitCnt - decimal(19, 9)
                   ,0                       -- CapDirectPayEPaidClaims - decimal(19, 9)
                   ,0                       -- CapDirectPayDEPaidClaims - decimal(19, 9)
                   ,0                       -- CapDirectPayBCAllocated - decimal(19, 9)
                   ,0                       -- CapDirectPayScenarioAllocated - decimal(19, 9)
                   ,0                       -- CapDirectPayBCAllocatedDelegated - decimal (19,9)
                   ,0                       -- CapDirectPayScenarioAllocatedDelegated - decimal (19,9)
                   ,0                       -- CapSurplusDeficitEPaidClaims - decimal(19, 9)
                   ,0                       -- CapSurplusDeficitDEPaidClaims - decimal(19, 9)
                   ,0                       -- CapSurplusDeficitBCAllocated - decimal(19, 9)
                   ,0                       -- CapSurplusDeficitScenarioAllocated - decimal(19, 9)
                   ,0                       -- CapSurplusDeficitBCAllocatedDelegated - decimal(19,9)
                   ,0                       -- CapSurplusDeficitScenarioAllocatedDelegated - decimal(19,9)
                   ,0                       -- CapMSBs - decimal(19, 9)
                   ,0                       -- CapProviderRewards - decimal(19, 9)
                   ,0                       -- OPBOtherNonHospitalBCAllocated - decimal(19, 9)
                   ,0                       -- OPBOtherNonHospitalScenarioAllocated - decimal(19, 11)
                   ,0                       -- OPBClaimsBuyDownBCAllocated - decimal(19, 9)
                   ,0                       -- OPBClaimsBuyDownScenarioAllocated - decimal(19, 9)
                   ,0                       -- OPBProviderClaimSettlementsBCAllocated - decimal(19, 9)
                   ,0                       -- OPBProviderClaimSettlementsScenarioAllocated - decimal(19, 9)
                   ,0                       -- OPBAccessFeesAndOtherBCAllocated - decimal(19, 9)
                   ,0                       -- OPBAccessFeesAndOtherScenarioAllocated - decimal(19, 9)
                   ,0                       -- PartDCapAdj - decimal(19, 9)
                   ,0                       -- PartDCapAdjDelegated - decimal(19,9)
                   ,0                       -- MSCapAdj - decimal(19, 9)
                   ,0                       -- MSCapAdjDelegated - decimal(19,9)
                   ,0                       -- SubCapAdj - decimal(19, 9)
                   ,0                       -- SubCapAdjExclude - decimal(19,9)
                   ,0                       -- MedicaidAdjPaid - decimal(19, 9)
                   ,0                       -- MedicaidAdjMbrCS - decimal(19, 9)
                   ,0                       -- ImplicitMarginPaid - decimal(19, 9)
                   ,0                       -- ImplicitMarginMbrCS - decimal(19, 9)
                   ,0                       -- AdditiveAdjPaid - decimal(19, 9)
                   ,0                       -- AdditiveAdjMbrCS - decimal(19, 9)
                   ,0                       -- AdditiveAdjAdmits - decimal(19, 9)
                   ,0                       -- AdditiveAdjUnits - decimal(19, 9)
                   ,0                       -- ModelOfCareAdjPaid - decimal(19, 9)
                   ,0                       -- ModelOfCareAdjMbrCS - decimal(19, 9)
                   ,0                       -- ModelOfCareAdjUnits - decimal(19, 9)
                   ,0                       -- UCAdmitsAdj - decimal(19, 9)
                   ,0                       -- UCUnitsAdj - decimal(19, 9)
                   ,0                       -- PartBRxRebatesPharmacy - decimal(19, 9)
                   ,0                       -- PartBRxRebatesQN - decimal(19, 9)
                   ,0                       -- RelatedPartiesAdj - decimal(19, 9)
                   ,0                       -- ProfitAdj - decimal(19, 9)
                   ,0                       -- MSBPaid - decimal(19, 9)
                   ,0                       -- MSBMbrCS - decimal(19, 9)
                   ,0                       -- MSBUnits - decimal(19, 9)
                   ,0                       -- MSBReductionCap - decimal(19, 9)
                   ,0                       -- MSBReductionClaimsPaid - decimal(19, 9)
                   ,0                       -- MSBReductionClaimsMbrCS - decimal(19, 9)
                   ,0                       -- MSBReductionClaimsUnits - decimal(19, 9)
                   ,0                       -- MSBReductionQuality - decimal(19, 9)
        FROM        dbo.SavedPlanInfo spi WITH (NOLOCK)
        LEFT JOIN   dbo.SavedDFClaims sdfc WITH (NOLOCK)
               ON spi.PlanInfoID = sdfc.PlanInfoID
                  AND   sdfc.DFVersionID IN (@XDFVersionID)
       CROSS APPLY  (SELECT 1 AS demogindicator UNION SELECT    3 AS demogindicator) demogs    -- create record for demog 1 and 3 (prep use)
        WHERE       1 = 1
                    AND spi.PlanYear = @PlanYear
                    AND sdfc.DFVersionID IS NULL;

        COMMIT;

    END TRY

    BEGIN CATCH

        ROLLBACK;

    END CATCH;
GO
