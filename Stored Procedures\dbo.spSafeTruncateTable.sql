SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------          
-- FUNCTION NAME: spSafeTruncateTable          
--          
-- AUTHOR: Sumit Kumar          
--          
-- CREATED DATE: 2015-08-14          
--          
-- DESCRIPTION: Procedure responsible for safely truncate the table
-- Drop all the constraints then truncate and finaly recreate those constraints       
--          
-- PARAMETERS: @TableName table name to truncate                
--          
-- TABLES:           
-- Read:          
         
-- Write:          
--          
-- VIEWS:          
--          
-- FUNCTIONS:         
--          
-- STORED PROCS:          
--          
-- $HISTORY           
-- ----------------------------------------------------------------------------------------------------------------------          
-- DATE          VERSION      CHANGES MADE										DEVELOPER            
-- ----------------------------------------------------------------------------------------------------------------------          
--2015-12-04	 1				Initial Version									Sumit Kumar
-- ----------------------------------------------------------------------------------------------------------------------   
CREATE PROCEDURE [dbo].[spSafeTruncateTable] 
 @TableName NVARCHAR(250)
    WITH EXECUTE AS OWNER
AS
    BEGIN

	----------prepare drop and create starement first
DECLARE @drop   NVARCHAR(MAX) = N'' ,
        @create NVARCHAR(MAX) = N'',
		@truncate NVARCHAR(MAX)= N'' ;


-- drop is easy, just build a simple concatenated list from sys.foreign_keys:
SELECT @drop += N'
ALTER TABLE ' + QUOTENAME(cs.name) + '.' + QUOTENAME(ct.name) 
    + ' DROP CONSTRAINT ' + QUOTENAME(fk.name) + ';'
FROM sys.foreign_keys AS fk
INNER JOIN sys.tables AS ct
  ON fk.parent_object_id = ct.[object_id]
INNER JOIN sys.schemas AS cs 
  ON ct.[schema_id] = cs.[schema_id]
   WHERE   fk.referenced_object_id = OBJECT_ID(@TableName);



-- create script preparation. 
SELECT @create += N'
ALTER TABLE ' 
   + QUOTENAME(cs.name) + '.' + QUOTENAME(ct.name) 
   +'WITH NOCHECK '+
   + ' ADD CONSTRAINT ' + QUOTENAME(fk.name) 
   + ' FOREIGN KEY (' + STUFF((SELECT ',' + QUOTENAME(c.name)
   -- get all the columns in the constraint table
    FROM sys.columns AS c 
    INNER JOIN sys.foreign_key_columns AS fkc 
    ON fkc.parent_column_id = c.column_id
    AND fkc.parent_object_id = c.[object_id]
    WHERE fkc.constraint_object_id = fk.[object_id]
    ORDER BY fkc.constraint_column_id 
    FOR XML PATH(N''), TYPE).value(N'.[1]', N'nvarchar(max)'), 1, 1, N'')
  + ') REFERENCES ' + QUOTENAME(rs.name) + '.' + QUOTENAME(rt.name)
  + '(' + STUFF((SELECT ',' + QUOTENAME(c.name)
   -- get all the referenced columns
    FROM sys.columns AS c 
    INNER JOIN sys.foreign_key_columns AS fkc 
    ON fkc.referenced_column_id = c.column_id
    AND fkc.referenced_object_id = c.[object_id]
    WHERE fkc.constraint_object_id = fk.[object_id]
    ORDER BY fkc.constraint_column_id 
    FOR XML PATH(N''), TYPE).value(N'.[1]', N'nvarchar(max)'), 1, 1, N'') + ');'
FROM sys.foreign_keys AS fk
INNER JOIN sys.tables AS rt -- referenced table
  ON fk.referenced_object_id = rt.[object_id]
INNER JOIN sys.schemas AS rs 
  ON rt.[schema_id] = rs.[schema_id]
INNER JOIN sys.tables AS ct -- constraint table
  ON fk.parent_object_id = ct.[object_id]
INNER JOIN sys.schemas AS cs 
  ON ct.[schema_id] = cs.[schema_id]
WHERE rt.is_ms_shipped = 0 AND ct.is_ms_shipped = 0
AND     fk.referenced_object_id = OBJECT_ID(@TableName);


SET @truncate='TRUNCATE TABLE DBO.' + @TableName ;

--PRINT @drop;
--PRINT @truncate;
--PRINT @create;
--print(datalength(@drop));

IF LEN(@drop) >10
EXEC sp_executesql @drop;

EXEC sp_executesql  @truncate;

IF LEN(@create) >10
EXEC sp_executesql @create;

END
GO
