SET ANSI_NULLS ON;
GO
SET QUOTED_IDENTIFIER ON;
GO
-- PROCEDURE NAME: spAppUpdateSavedPlanBenefitDetail
--
-- CREATOR: Deepali Mittal
--
-- CREATED DATE: 2015-06-29
--DESCRIPTION: Stored Procedure responsible for updating SavedPlanBenefitDetail from Sigma Batch Import.
--
-- PARAMETERS:
--	Input:
--		 @PlanYearID 
--		 @Contract
--		 @PlanID
--		 @SegID
--		 @IsBenefitYearCurrentYear
--		 @BenefitCategoryID
--		 @PercentCoveredAllowed
--		 @PercentCoveredCostShare
--		 @CreatedBy

--  Output:
--
-- TABLES:
--	Read:
--     SavedPlanBenefitDetail
--	   Savedplanheader
--	Write:
--  SavedPlanBenefitDetail
--
-- VIEWS:
--
-- FUNCTIONS: fnStringSplit
--
-- STORED PROCS:
--     
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2015-Jun-29			1		Initial																Deepali Mittal
-- 2015-Oct-14          2       Replace UpdatedBy by LastUpdateByID and UpdatedDateTime by
--                              LastUpdateDateTime                                                  Manisha Tyagi
-- 2017-Jun-21			3		Added code to trigger reprice notification when % covered loaded	Chris Fleming
-- 2019-Jun-28		    4	    Replace SavedPlanHeader with SavedForecastSetup				        Pooja Dahiya
-- 2019-Nov-06			5		Replace savedforecastsetup to savedplanheader						Chhavi Sinha
-- 2019-Dec-17			6		Update Reprice Section and changes length of Created BY
--								and updated LastUpdatedbyID and datetime while updating reprice column				Deepali Mittal
-- 2023-May-02			7		Updated SavedPlanBenefitDetail update to update only data for		Alexander Brandt
--								plans when IsLiveIndex = 1
-- 2023-Sep-27			8		Update % covered for all benefit options. Remove isLiveIndex=1		Adam Gilbert
-- -- ----------------------------------------------------------------------------------------------------------------------


CREATE   PROCEDURE [dbo].[spAppUpdateSavedPlanBenefitDetail]
(@PlanYearID               INT, 
 @Contract                 VARCHAR(5), 
 @PlanID                   VARCHAR(3), 
 @SegID                    VARCHAR(3), 
 @IsBenefitYearCurrentYear INT, 
 @BenefitCategoryID        INT, 
 @PercentCoveredAllowed    DECIMAL(7, 6), 
 @PercentCoveredCostShare  DECIMAL(7, 6), 
 @CreatedBy                VARCHAR(7)
)
AS

     DECLARE @ForecastID VARCHAR(MAX);
     SET @ForecastID =
     (
         SELECT CAST(s.ForecastID AS VARCHAR(MAX)) + ','
         FROM SavedPlanBenefitDetail s
              INNER JOIN SavedPlanHeader p ON p.ForecastID = s.ForecastID
              INNER JOIN dbo.SavedForecastSetup sfs ON sfs.ForecastID = p.ForecastID
         WHERE p.ContractNumber = @Contract
               AND p.Planid = @PlanID
               AND p.SegmentID = @SegID
               AND p.PlanYearID = @PlanYearID
               AND s.IsBenefitYearCurrentYear = @IsBenefitYearCurrentYear
               AND s.IsLiveIndex = 1
               AND sfs.IsLiveIndex = 1 FOR XML PATH('')
     );

    BEGIN
        UPDATE SavedPlanBenefitDetail
          SET 
              PercentCoveredAllowed = @PercentCoveredAllowed, 
              PercentCoveredCostShare = @PercentCoveredCostShare, 
              LastUpdateByID = @CreatedBy, 
              LastUpdateDateTime = GETDATE()
        WHERE ForecastID IN
        (
            SELECT Value
            FROM string_split(@ForecastID, ',')
        )
        AND BenefitCategoryID = @BenefitCategoryID
        AND PlanYearID = @PlanYearID
        AND IsBenefitYearCurrentYear = @IsBenefitYearCurrentYear;
    END;

     --Trigger Reprice Notification
    BEGIN
        UPDATE FS
          SET 
              IsToReprice = 1, 
              LastUpdateByID = @CreatedBy, 
              LastUpdateDateTime = GETDATE()
        FROM SavedForecastSetup FS
        WHERE FS.ForecastID IN
        (
            SELECT Value
            FROM string_split(@ForecastID, ',')
        );
    END;
GO
