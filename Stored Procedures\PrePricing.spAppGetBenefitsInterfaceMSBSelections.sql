SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------      
-- PROCEDURE NAME: [PrePricing].[spAppGetBenefitsInterfaceMSBSelections]      
--      
-- TYPE: Stored Procedure      
--      
-- AUTHOR: <PERSON><PERSON>      
--      
-- CREATED DATE: 2025-JAN-13      
--      
-- DESCRIPTION: Procedure responsible for extracting MSB projections from prericeing schema    
--    by ForecastID.      
--      
-- PARAMETERS:      
-- Input:      
--     @ForecastID      
-- TABLES:       
-- Read:     
-- PrePricing.MarketInputValue
-- prepricing.MarketInputSubCategory
-- PrePricing.PlanInfo         
-- dbo.SavedPlanInfo
-- dbo.SavedForecastSetup
--         
-- Write:      
--      
-- VIEWS:      
--      
-- FUNCTIONS:      
-- dbo.fnGetBidYear()
--        
-- STORED PROCS:      
--        
-- $HISTORY       
-- ----------------------------------------------------------------------------------------------------------------------      
-- DATE			 VERSION  CHANGES MADE																	DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------      
-- 2025-Jan-14		1    Initial Version																Surya Murthy        
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [PrePricing].[spAppGetBenefitsInterfaceMSBSelections]      
	@WhereIn VARCHAR(MAX)    
AS    
BEGIN    
	IF @WhereIn IS NULL-- No Plans selected, extract all
	BEGIN
			SELECT d.ForecastID ,a.INValue AS MSBCode, ISNULL(e.AddedBenefitName,'') AS AddedBenefitName,
			CASE WHEN(IsOverride=1)THEN 'y' ELSE '' end AS [Override],'' Instructions			
			FROM PrePricing.MarketInputValue a (NOLOCK)
			JOIN prepricing.MarketInputSubCategory z ON a.SubcategoryID=z.SubCategoryID
			JOIN PrePricing.PlanInfo b (NOLOCK) ON b.PlanInfoID=a.PlanInfoID
			JOIN dbo.SavedPlanInfo c (NOLOCK) ON c.CPS=b.CPS
			JOIN dbo.SavedForecastSetup d (NOLOCK) ON d.PlanInfoID=c.PlanInfoID AND d.PlanYear=dbo.fnGetBidYear()
			LEFT JOIN dbo.SavedPlanAddedBenefits e (NOLOCK) ON e.ForecastID=d.ForecastID AND a.INValue=LEFT(e.AddedBenefitName,6)
			WHERE z.Categoryid=2 AND a.INValue IS NOT NULL AND a.INValue <>'' 			 
			ORDER BY d.ForecastID,MSBCode		
	END 

	ELSE --Export selected plans
		BEGIN
			SELECT d.ForecastID ,a.INValue AS MSBCode, ISNULL(e.AddedBenefitName,'') AS AddedBenefitName,
			CASE WHEN(IsOverride=1)THEN 'y' ELSE '' end AS [Override],'' Instructions			
			FROM PrePricing.MarketInputValue a (NOLOCK)
			JOIN prepricing.MarketInputSubCategory z ON a.SubcategoryID=z.SubCategoryID
			JOIN PrePricing.PlanInfo b (NOLOCK) ON b.PlanInfoID=a.PlanInfoID
			JOIN dbo.SavedPlanInfo c (NOLOCK) ON c.CPS=b.CPS
			JOIN dbo.SavedForecastSetup d (NOLOCK) ON d.PlanInfoID=c.PlanInfoID AND d.PlanYear=dbo.fnGetBidYear()
			LEFT JOIN dbo.SavedPlanAddedBenefits e (NOLOCK) ON e.ForecastID=d.ForecastID AND a.INValue=LEFT(e.AddedBenefitName,6)
			WHERE z.Categoryid=2 AND a.INValue IS NOT NULL AND a.INValue <>'' 
			AND d.ForecastID	IN (SELECT Value FROM STRING_SPLIT(@WhereIN, ','))
			ORDER BY d.ForecastID,MSBCode		 
		END
END
GO
