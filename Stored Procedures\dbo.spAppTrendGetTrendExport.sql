SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------              
-- PROCEDURE NAME: [dbo].[spAppTrendGetTrendExport]    
--              
-- TYPE: SAME              
--              
-- AUTHOR: Rama<PERSON><PERSON>            
--              
-- CREATED DATE: 2020-Mar-12	             
--              
-- DESCRIPTION: Procedure responsible for retrieving TREND extrect  infomation based on extract type              
--    by ForecastID.              
--              
-- PARAMETERS:              
-- Input:              
--     @ForecastID              
-- TABLES:               
-- Read:              
--                  
--                 
-- Write:              
--              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:              
--                
-- $HISTORY               
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE			VERSION		CHANGES MADE                 DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Mar-12		1		Initial Version				Ramandeep Saini   
-- 2023-Feb-01		2		Added AND ss.PlanYear = dbo.fnGetBidYear()  Phillil Leigh
-- ----------------------------------------------------------------------------------------------------------------------              
CREATE PROCEDURE [dbo].[spAppTrendGetTrendExport]          
 @ForecastID VARCHAR(MAX),
 @LastUpdateByID     VARCHAR(7) 

AS   
BEGIN
BEGIN TRY
	 BEGIN TRANSACTION

 SELECT Sp.CPS,Sp.PlanYear,Sp.PlanInfoID INTO #tempPlanInfoId FROM dbo.SavedPlanInfo Sp
 INNER JOIN dbo.SavedForecastSetup SS ON SS.PlanInfoID=Sp.PlanInfoID WHERE 
		SS.ForecastID IN (SELECT  Value FROM    dbo.fnStringSplit(@ForecastID, ',') )  
		AND ss.PlanYear = dbo.fnGetBidYear() --Added 2/1/2023 PL 
		ORDER BY CHARINDEX(CAST(SS.ForecastID AS VARCHAR), @ForecastID)
SELECT DISTINCT bc.*
FROM dbo.Trend_ProjProcess_CalcPlanTrendsFinal bc LEFT JOIN #tempPlanInfoId pl ON bc.PlanInfoID=pl.PlanInfoID
WHERE pl.PlanInfoID=bc.PlanInfoID 
COMMIT TRANSACTION;

END TRY    
BEGIN CATCH

		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),
	@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'
	+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)


	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

	ROLLBACK TRANSACTION; 
	---Insert into app log for logging error------------------
	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID;
 END CATCH;  
END
GO
