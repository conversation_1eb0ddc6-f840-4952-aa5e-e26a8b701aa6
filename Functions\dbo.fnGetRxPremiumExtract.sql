SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetRxPremiumExtract]  ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetRxPremiumExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-21
-- HEADER UPDATED: 2011-Dec-21
--
-- DESCRIPTION: Designed to extract fields for Rx Premium - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	
--		SavedPlanAssumptions
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-21		1			Initial Version														Bobby Jaegers
-- 2024-Dec-19		2			Add CPS to export													Ramaraj Kumar
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetRxPremiumExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	CPS CHAR(13),
	RxBasicPremium DECIMAL(9,2),
    RxSuppPremium DECIMAL(9,2)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
	BEGIN
		INSERT @Results
			SELECT  SPA.ForecastID,			
					SPI.CPS,
					SPA.RxBasicPremium,
					SPA.RxSuppPremium
			FROM SavedPlanAssumptions SPA
					JOIN SavedForecastSetup SFS ON SFS.ForecastID = SPA.ForecastID
					JOIN SavedPlanInfo SPI ON SPI.PlanInfoID = SFS.PlanInfoID
	END
       
	ELSE
	BEGIN
		INSERT @Results
			SELECT  SPA.ForecastID,			
					SPI.CPS,
					SPA.RxBasicPremium,
					SPA.RxSuppPremium
			FROM SavedPlanAssumptions SPA
					JOIN SavedForecastSetup SFS ON SFS.ForecastID = SPA.ForecastID
					JOIN SavedPlanInfo SPI ON SPI.PlanInfoID = SFS.PlanInfoID
			WHERE SPA.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
	END

RETURN
END
GO
