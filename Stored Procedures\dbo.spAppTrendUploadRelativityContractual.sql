SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------              
-- PROCEDURE NAME: [dbo].[spAppTrendUploadRelativityContractual]    
--              
-- TYPE: SAME              
--              
-- AUTHOR: <PERSON> Pant            
--              
-- CREATED DATE: 2020-Apr-16	             
--              
-- DESCRIPTION: Procedure responsible for executing FROM Trend_SavedRelativityContractual OC

-- PARAMETERS:              
-- Input:               
--@UserID             
-- TABLES:               
-- Read:              
--                  
--                 
-- Write:              
--              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:              
--                
-- $HISTORY               
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE			VERSION		CHANGES MADE														 DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Apr-16		1		Initial Version														Kiran Pant  
-- 2020-Jun-10		2	Renamed the sp name in the comment section with prefix spAppTrend	   Kiran Pant  
-- ----------------------------------------------------------------------------------------------------------------------   
--Trend_SavedRelativityContractual
CREATE PROCEDURE [dbo].[spAppTrendUploadRelativityContractual]
(     @RelativityImportData RelativityImports READONLY,
      @UserID varchar(7),
	  @MessageFromBackend VARCHAR(MAX) OUTPUT,
	  @Result BIT OUT
)
AS
 SET NOCOUNT ON
    
       BEGIN
	
	   DECLARE @Count int=0;
	   SELECT *  INTO #Temp FROM  @RelativityImportData
	 
BEGIN TRY
    BEGIN TRANSACTION

DELETE OC 
FROM Trend_SavedRelativityContractual OC
--INNER JOIN #Temp a 
--ON  OC.CPS=a.CPS AND OC.PlanYearID=a.PlanYearID
--WHERE  OC.CPS=a.CPS AND OC.PlanYearID=a.PlanYearID

INSERT INTO dbo.Trend_SavedRelativityContractual
				
					SELECT 
					    CPS,
						PlanYearID,  
						QuarterID,
					    ReportingCategory,
						CostRelativity,
					    UseRelativity,
						@UserID,
						GETDATE()
						FROM #Temp 
			
						
			SET @Count = (SELECT COUNT(*) FROM #Temp)

		set @Result=1

	 SET @MessageFromBackend='Successfully Imported '+Convert(varchar,@Count)+': Row(s)';	 

	    COMMIT TRANSACTION
    END TRY
   
BEGIN CATCH

		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()
		SET @Result=0;
	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			

	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
	ROLLBACK TRANSACTION;
	set @Result=0
     
	---Insert into app log for logging error------------------
	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@UserID;
 END CATCH;
    
	--ELSE 
	--	SET @ValidationMessage = 'You Do Not Have The Authority To Modify OSB Data' 
	END
GO
