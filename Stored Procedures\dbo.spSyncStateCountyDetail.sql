SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spSyncStateCountyDetail
--
-- AUTHOR: <PERSON> Fleming
--
--
-- DESCRIPTION: Pull updated service area from SAM via passed ForecastID and sync entries in SavedPlanStateCountyDetail.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--
--	Output: 
--		NONE
--
-- RETURNS: 
--
-- TABLES:
--		Read: 
--			MAPD
--				SavedPlanHeader 
--				SavedPlanStateCountyDetail
--			MAAModels
--				SavedPlanInfo
--				SavedProjectedCrosswalk 
--			Temp
--				#ServiceArea
--				#ForecastIDList
--
--		Write:
--			MAPD
--				SavedPlanStateCountyDetail
--				PlanChangeLog
--			MAAModels
--				NONE
--			Temp
--				#ServiceArea
--				#ForecastIDList
--
-- VIEWS: 
--		NONE
--
-- FUNCTIONS:
--      NONE
--
-- STORED PROCS: 
--		NONE
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			 VERSION			CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2018-07-13		1				Initial Version                                                     Chris Fleming
-- 2019-03-26       2               Correcting Plan Consolidations #ServiceArea                         Brent Osantowski
-- 2019-Oct-16	    3			    SIT defect shared by Abraham for sync not working
--						            in case of of plan consolidation									Pooja Dahiya
-- 2019-oct-30		4				Removed 'HUMAD\' to UserID											Chhavi Sinha
---2021-Jan-20      5				Added WITH(NOLOCK)&Top1 map id										 Mahendran Chinnaiah
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spSyncStateCountyDetail]
	(
	@ForecastID INT
	) AS
BEGIN
	DECLARE @PlanYearID SMALLINT;
	DECLARE @LastUpdated DATETIME;
	DECLARE @MapID SMALLINT;

			SELECT  @PlanYearID = dbo.fnGetBidYear();
			SET @LastUpdated = ( SELECT GETDATE());
			SELECT  @MapID = ( SELECT TOP 1 MapID
			FROM   dbo.SavedForecastSetup sfs WITH(NOLOCK)
			inner join SavedProjectedCrosswalk ssa WITH(NOLOCK)
			on sfs.PlanInfoID=ssa.PlanInfoID AND ISNULL(sfs.ServiceAreaOptionID,'')=ssa.ServiceAreaOptionID
			WHERE  ForecastID = @ForecastID
			GROUP BY MapID
			);
 
			--Temp table to hold service area
			IF OBJECT_ID('tempdb.dbo.#ServiceArea') IS NOT NULL
				DROP TABLE #ServiceArea
			CREATE TABLE #ServiceArea
				(
					ForecastID INT ,
					[SSStateCountyCD] CHAR(5) ,
					[LastUpdateByID] CHAR(7)
				);

			--Temp table to hold plan index list
			IF OBJECT_ID('tempdb.dbo.#ForecastIDList') IS NOT NULL
				DROP TABLE #ForecastIDList
			CREATE TABLE #ForecastIDList 
				( 
					[ForecastID] INT
				);

			--Populate temp table with service area from MAAModels
			INSERT  INTO #ServiceArea
					SELECT  ForecastID ForecastID,
							spc.SSStateCountyCD ,
							spc.LastUpdateByID
					FROM	savedforecastsetup sfs WITH(NOLOCK)
							INNER JOIN dbo.SavedProjectedCrosswalk spc WITH(NOLOCK)
							ON sfs.PlanInfoID=spc.PlanInfoID AND ISNULL(sfs.ServiceAreaOptionID,'')=spc.ServiceAreaOptionID
					WHERE   spc.MapID=@MapID
							AND	spc.IsTermed = 0
					Group By ForecastID ,
							spc.SSStateCountyCD ,
							spc.LastUpdateByID
			--Populate plan index temp table with all plan indexes that map to CPS associated with passed ForecastID
			INSERT  INTO #ForecastIDList
					SELECT  ForecastID ForecastID
					FROM    savedforecastsetup sfs WITH(NOLOCK)
					INNER JOIN dbo.SavedProjectedCrosswalk spc WITH(NOLOCK)
					ON sfs.PlanInfoID=spc.PlanInfoID AND ISNULL(sfs.ServiceAreaOptionID,'')=spc.ServiceAreaOptionID
					WHERE   IsHidden = 0 AND spc.MapID=@MapID
					GROUP BY ForecastID

			--Log change in service area to PlanChangeLog
			INSERT  INTO PlanChangeLog
					SELECT  ForecastID = pil.ForecastID ,
							ProcName = 'Service Area' ,
							Value = NULL ,
							AuditUserID = sam.LastUpdateByID ,
							AuditTime = @LastUpdated,
							ID = NEWID()
					FROM    #ForecastIDList pil
							INNER JOIN dbo.SavedPlanHeader sph WITH(NOLOCK)
							ON sph.ForecastID = pil.ForecastID
							LEFT JOIN #ServiceArea sam 
							ON sam.ForecastID = sph.ForecastID

			--Clear out current service area in MAPD from all applicable plan indexes in the temp planlist tbl
			DELETE  FROM SavedPlanStateCountyDetail
			WHERE   PlanYearID = @PlanYearID
					AND ForecastID IN ( SELECT ForecastID FROM #ForecastIDList );

			--Sync new service area to MAPD table from MAAModels 
			INSERT  INTO SavedPlanStateCountyDetail
					SELECT  PlanYearID = @PlanYearID ,
							ForecastID = pil.ForecastID ,
							StateTerritoryID = LEFT(sam.SSStateCountyCD, 2) ,
							CountyCode = RIGHT(sam.SSStateCountyCD, 3) ,
							IsCountyExcludedFromBPTOutput = 0 ,
							LastUpdateByID = sam.LastUpdateByID ,
							LastUpdateDateTime = @LastUpdated
					FROM    #ServiceArea sam
							INNER JOIN #ForecastIDList pil 
							ON sam.ForecastID = pil.ForecastID;
END
GO
