SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetBidSummaryBenefits] ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetBidSummaryBenefits
--
-- AUTHOR: NATE JACOBY
--
-- CREATED DATE: 2010-Dec-17
-- HEADER UPDATED: 2010-Dec-17
--
-- DESCRIPTION:  To pull benefits for the market bid summary tab
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--        LkpIntBenefitCategory
--        SavedPlanBenefitDetail
--        Benefits_SavedBenefitOption
--
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Dec-17		1		    Initial Version										                Nate Jacoby
-- 2011-Mar-03		2			Added Part B Deductible												Nate Jacoby
-- 2011-Apr-25		3			Removed 286 hardcoding for SNF										Joe Casey
-- 2011-Jun-06		4			Added '/admit' criteria for IPAcute and SNF IN						Bobby Jaegers														
-- 2011-Jun-14		5			Changed PlanYearId to return SMALLINT instead of INT				Bobby Jaegers
-- 2011-Jul-05		6			Updated IP Accute Piece to allow for Coins and Copay in both yrs	Craig Wright
-- 2011-Jul-06		7			Removed "/day" for COINS on IP Accute and SNF						Craig Wright
-- 2011-Jul-15		8			Updated SNF and PCP/SPEC to allow for Coins and Copay in both yrs	Craig Wright
-- 2012-Jan-20		9			Changed INNER JOIN to FULL OUTER JOIN in SNF to account for			Alex Rezmerski
--								cases where Current has 2 Tiers but projected has 3 Tiers 
-- 2012-Jan-25		10			Inserted UNION in SNF to correct problems caused by FULL OUTER JOIN	Alex Rezmerski
-- 2022-May-25		11			MAAUI migration; replaced @PlanIndex with @ForecastID; replaced 
--								reference from PlanIndex to ForecastID; in the output table, 
--								replaced PlanIndex with ForecastID; removed 
--								SavedPlanDeductibleMOOPDetail; MOOP/Deductible data now pulled from 
--								Benefits_SavedBenefitOption; adding @PlanInfoIDcur/@PlanInfoIDbid 
--								and @BenefitOptionIDcur/@BenefitOptionIDcur to limit the pull from 
--								Benefits_SavedBenefitOption; removed nested queries; for 
--								SavedPlanBenefitDetail, added further specification for 
--								current year, "WHERE BenefitOptionID = @BenefitOptionIDcur", and 
--								for bid year "WHERE BenefitOptionID = @BenefitOptionIDbid", to limit 
--								the pull to only one option; SNF IN was being pulled twice, so made
--								a change to pull IN & OON now; recoded to fit coding standards:
--								variable [Category] --> BenefitCategory, [PCPCurrent] --> PCPCurrent,
--								[PCPProjected] --> PCPProjected, [SPECCurrent] --> SPECCurrent,
--								[SPECProjected] --> SPECProjected, [Current] --> CurrentVal, 
--								[Projected] --> ProjectedVal; added pulls for IP Phych and Rehab;
--								added handling of NULL values for MOOP, Deductible and handling cases
--								WHEN ' Part B Only '; changed the pull for IP benefits to include all
--								ordinal levels; added logic to exclude rows when IN is DayRange but
--								OON is not, and other way around									Aleksandar Dimitrijevic
-- 2024- May 10      12         Updating column DeductiblePtBProjected to pull Projected deductable data 
--                              instead of current deductible data on line 203                      Abraham Ndabian
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetBidSummaryBenefits]
    (
    @ForecastID INT
    )

RETURNS @RESULTS TABLE
    (
    PlanYearID SMALLINT, 
    ForecastID INT,
    Category VARCHAR(MAX),
    CurrentVal VARCHAR(MAX),
    ProjectedVal VARCHAR(MAX)
    ) AS
BEGIN    

    DECLARE @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear();

	DECLARE @INAdd CHAR(9) = ' (IN) - L',
			@OONAdd CHAR(10) = ' (OON) - L',
			@Days CHAR(8) = ', Days (',
			@Admit CHAR(6) = '/admit',
			@PerDay CHAR(12) = '/day, Days ('

	-- Declaring the PlanInfoID and BenefitOptionID, needed to pull data from dbo.Benefits_SavedBenefitOption
	DECLARE @PlanInfoIDBid INT,
			@PlanInfoIDCur INT,
			@BenefitOptionIDBid TINYINT,
			@BenefitOptionIDCur TINYINT

		SET @PlanInfoIDBid = (SELECT DISTINCT PlanInfoID 
							FROM dbo.SavedPlanBenefitDetail
							WHERE IsBenefitYearCurrentYear = 0 -- pulling bid benefit design
								AND ForecastID = @ForecastID
								AND IsLiveIndex = 1);
		SET @PlanInfoIDCur = (SELECT DISTINCT PlanInfoID 
							  FROM dbo.SavedPlanBenefitDetail
							  WHERE IsBenefitYearCurrentYear = 1 -- pulling current year benefit design
								AND ForecastID = @ForecastID);

		SET @BenefitOptionIDBid = (SELECT DISTINCT BenefitOptionID 
								   FROM dbo.SavedPlanBenefitDetail
								   WHERE IsBenefitYearCurrentYear = 0 -- pulling bid benefit design
									 AND ForecastID = @ForecastID
									 AND IsLiveIndex = 1);
		SET @BenefitOptionIDCur = (SELECT DISTINCT BenefitOptionID 
								   FROM dbo.SavedPlanBenefitDetail
								   WHERE IsBenefitYearCurrentYear = 1 -- pulling bid benefit design
									 AND ForecastID = @ForecastID);

---------------------------------------------------------------------------------------------------------
--Starting the data generate process
---------------------------------------------------------------------------------------------------------
		--MOOP
		DECLARE @MOOP TABLE
			(
			 PlanYearID SMALLINT,
			 ForecastID INT,
			 INMOOPCurrent SMALLINT,
			 INMOOPProjected SMALLINT,
			 OONMOOPCurrent SMALLINT,
			 OONMOOPProjected SMALLINT,
			 COMBOMOOPCurrent SMALLINT,
			 COMBOMOOPProjected SMALLINT
			)

		INSERT INTO @MOOP
		SELECT PlanYearID = @PlanYearID,
			   ForecastID = @ForecastID,
               INMOOPCurrent = curr.INMOOP,
               INMOOPProjected = bid.INMOOP,
               OONMOOPCurrent = curr.OONMOOP,
               OONMOOPProjected = bid.OONMOOP,
               COMBOMOOPCurrent = curr.CombinedMOOP,
               COMBOMOOPProjected = bid.CombinedMOOP
        FROM dbo.Benefits_SavedBenefitOption curr
		CROSS JOIN dbo.Benefits_SavedBenefitOption bid
        WHERE curr.PlanInfoID = @PlanInfoIDCur
		  AND bid.PlanInfoID = @PlanInfoIDBid
          AND curr.BenefitOptionID = @BenefitOptionIDCur
		  AND bid.BenefitOptionID = @BenefitOptionIDBid
		--------------------------------------------------------------

		--Deductible
		DECLARE @Deductible TABLE
			(
			 PlanYearID SMALLINT,
			 ForecastID INT,
			 INDeductibleCurrent SMALLINT,
			 INDeductibleProjected SMALLINT,
			 OONDeductibleCurrent SMALLINT,
			 OONDeductibleProjected SMALLINT,
			 DeductiblePtBCurrent SMALLINT,
			 DeductiblePtBProjected SMALLINT
			)

		INSERT INTO @Deductible
		SELECT @PlanYearID AS PlanYearID,
               @ForecastID AS ForecastID,
               INDeductibleCurrent = CASE WHEN curr.IsPartBDeductible = 0
										  THEN COALESCE(curr.INDeductible,0)
										  ELSE NULL
									 END,
               INDeductibleProjected = CASE WHEN bid.IsPartBDeductible = 0
											THEN COALESCE(bid.INDeductible,0)
											ELSE NULL
									   END,
               OONDeductibleCurrent = CASE WHEN curr.IsPartBDeductible = 0
										   THEN COALESCE(curr.OONDeductible,0)
										   ELSE NULL
								      END,
               OONDeductibleProjected = CASE WHEN bid.IsPartBDeductible = 0
										     THEN COALESCE(bid.OONDeductible,0)
										     ELSE NULL
									    END,
               DeductiblePtBCurrent = CASE WHEN curr.IsPartBDeductible = 1
										   THEN 
												CASE WHEN curr.INDeductible IS NULL
													 THEN CASE WHEN curr.CombinedDeductible IS NULL
															   THEN 0
															   ELSE curr.CombinedDeductible
														  END
													 ELSE curr.INDeductible
												END
										   ELSE 0
								      END,
               DeductiblePtBProjected = CASE WHEN bid.IsPartBDeductible = 1
											 THEN 
												 CASE WHEN bid.INDeductible IS NULL
												      THEN CASE WHEN bid.CombinedDeductible IS NULL
															    THEN 0
															    ELSE bid.CombinedDeductible
														   END
													  ELSE bid.INDeductible
												 END
											 ELSE 0
								        END
        FROM dbo.Benefits_SavedBenefitOption curr
		CROSS JOIN dbo.Benefits_SavedBenefitOption bid
        WHERE curr.PlanInfoID = @PlanInfoIDCur
		  AND bid.PlanInfoID = @PlanInfoIDBid
          AND curr.BenefitOptionID = @BenefitOptionIDCur
		  AND bid.BenefitOptionID = @BenefitOptionIDBid
		-------------------------------------------------------------------------------------

			--IP Acute IN Current
			DECLARE @IPAcuteINCurrent TABLE
				(
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueCurrent DECIMAL(9, 4),
				 INDayRangeBeginCurrent TINYINT,
				 INDayRangeEndCurrent TINYINT
				)

			INSERT INTO @IPAcuteINCurrent
			SELECT A.INBenefitTypeID,
				   A.BenefitOrdinalID,
				   INBenefitValueCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN	ISNULL(INBenefitValue,0)
											ELSE 0
										   END,
				   INDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(INDayRangeBegin, 0)
												ELSE 0
											END,
				   INDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										  END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDCur
			  AND A.BenefitOptionID = @BenefitOptionIDCur
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (65)
			  AND A.IsBenefitYearCurrentYear = 1
			  AND A.INBenefitTypeID IS NOT NULL
			--------------------------------------------------------------------

			--IP Acute IN Bid
			DECLARE @IPAcuteINBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueProjected DECIMAL(9, 4),
				 INDayRangeBeginProjected TINYINT,
				 INDayRangeEndProjected TINYINT
				)

			INSERT INTO @IPAcuteINBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.INBenefitTypeID,
				A.BenefitOrdinalID,
				INBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INBenefitValue,0)
											ELSE 0
										  END,
				INDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeBegin, 0)
											ELSE 0
										   END,
				INDayRangeEndProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										 END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (65)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.INBenefitTypeID IS NOT NULL
		----------------------------------------------------------------------

		--IP Acute IN
		DECLARE @IPAcuteIN TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			INBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			INBenefitValueCurrent DECIMAL(9, 4),
			INDayRangeBeginCurrent TINYINT,
			INDayRangeEndCurrent TINYINT,
			INBenefitTypeIDProjected TINYINT,
			INBenefitValueProjected DECIMAL(9, 4),
			INDayRangeBeginProjected TINYINT,
			INDayRangeEndProjected TINYINT
			)

		INSERT INTO @IPAcuteIN
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.INBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.INBenefitValueCurrent,
			   Cur.INDayRangeBeginCurrent,
			   Cur.INDayRangeEndCurrent,
			   Proj.INBenefitTypeID,
			   Proj.INBenefitValueProjected,
			   Proj.INDayRangeBeginProjected,
			   Proj.INDayRangeEndProjected
		FROM @IPAcuteINCurrent Cur
		RIGHT OUTER JOIN @IPAcuteINBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'

		------------------------------------------------------------------
			--IP Acute OON Current
			DECLARE @IPAcuteOONCurrent TABLE
				(
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueCurrent DECIMAL(9, 4),
				 OONDayRangeBeginCurrent TINYINT,
				 OONDayRangeEndCurrent TINYINT
				)

			INSERT INTO @IPAcuteOONCurrent
			SELECT A.OONBenefitTypeID,
				   A.BenefitOrdinalID,
				   OONBenefitValueCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONBenefitValue,0)
												ELSE 0
											END,
				   OONDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeBegin, 0)
												ELSE 0
											 END,
				   OONDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeEnd, 0)
												ELSE 0
										   END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID


			AND A.PlanInfoID = @PlanInfoIDCur
			AND A.BenefitOptionID = @BenefitOptionIDCur
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (65)
			AND A.IsBenefitYearCurrentYear = 1
			AND A.OONBenefitTypeID IS NOT NULL

			--------------------------------------------------------------------

			--IP Acute OON Bid
			DECLARE @IPAcuteOONBid TABLE

				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueProjected DECIMAL(9, 4),
				 OONDayRangeBeginProjected TINYINT,
				 OONDayRangeEndProjected TINYINT
				)

			INSERT INTO @IPAcuteOONBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.OONBenefitTypeID,
				A.BenefitOrdinalID,
				OONBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONBenefitValue,0)
											ELSE 0
											END,
				OONDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeBegin, 0)
											ELSE 0
											END,
				OONDayRangeEndProjected =  CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeEnd, 0)
											ELSE 0
											END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID


			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (65)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.OONBenefitTypeID IS NOT NULL
		----------------------------------------------------------------------

		--IP Acute OON
		DECLARE @IPAcuteOON TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			OONBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			OONBenefitValueCurrent DECIMAL(9, 4),
			OONDayRangeBeginCurrent TINYINT,
			OONDayRangeEndCurrent TINYINT,
			OONBenefitTypeIDProjected TINYINT,
			OONBenefitValueProjected DECIMAL(9, 4),
			OONDayRangeBeginProjected TINYINT,
			OONDayRangeEndProjected TINYINT
			)

		INSERT INTO @IPAcuteOON
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.OONBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.OONBenefitValueCurrent,
			   Cur.OONDayRangeBeginCurrent,
			   Cur.OONDayRangeEndCurrent,
			   Proj.OONBenefitTypeID,
			   Proj.OONBenefitValueProjected,
			   Proj.OONDayRangeBeginProjected,
			   Proj.OONDayRangeEndProjected
		FROM @IPAcuteOONCurrent Cur
		RIGHT OUTER JOIN @IPAcuteOONBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'
		-------------------------------------------------------------------------------------

			--IP Psych IN Current
			DECLARE @IPPsychINCurrent TABLE
				(
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueCurrent DECIMAL(9, 4),
				 INDayRangeBeginCurrent TINYINT,
				 INDayRangeEndCurrent TINYINT
				)

			INSERT INTO @IPPsychINCurrent
			SELECT A.INBenefitTypeID,
				   A.BenefitOrdinalID,
				   INBenefitValueCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN	ISNULL(INBenefitValue,0)
											ELSE 0
										   END,
				   INDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(INDayRangeBegin, 0)
												ELSE 0
											END,
				   INDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										  END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDCur
			  AND A.BenefitOptionID = @BenefitOptionIDCur
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (66)
			  AND A.IsBenefitYearCurrentYear = 1
			  AND A.INBenefitTypeID IS NOT NULL
			--------------------------------------------------------------------

			--IP Psych IN Bid
			DECLARE @IPPsychINBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueProjected DECIMAL(9, 4),
				 INDayRangeBeginProjected TINYINT,
				 INDayRangeEndProjected TINYINT
				)

			INSERT INTO @IPPsychINBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.INBenefitTypeID,
				A.BenefitOrdinalID,
				INBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INBenefitValue,0)
											ELSE 0
										  END,
				INDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeBegin, 0)
											ELSE 0
										   END,
				INDayRangeEndProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										 END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (66)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.INBenefitTypeID IS NOT NULL

		----------------------------------------------------------------------


		--IP Psych IN
		DECLARE @IPPsychIN TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			INBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			INBenefitValueCurrent DECIMAL(9, 4),
			INDayRangeBeginCurrent TINYINT,
			INDayRangeEndCurrent TINYINT,
			INBenefitTypeIDProjected TINYINT,
			INBenefitValueProjected DECIMAL(9, 4),
			INDayRangeBeginProjected TINYINT,
			INDayRangeEndProjected TINYINT
			)

		INSERT INTO @IPPsychIN
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.INBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.INBenefitValueCurrent,
			   Cur.INDayRangeBeginCurrent,
			   Cur.INDayRangeEndCurrent,
			   Proj.INBenefitTypeID,
			   Proj.INBenefitValueProjected,
			   Proj.INDayRangeBeginProjected,
			   Proj.INDayRangeEndProjected
		FROM @IPPsychINCurrent Cur
		RIGHT OUTER JOIN @IPPsychINBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'

		------------------------------------------------------------------
			--IP Psych OON Current
			DECLARE @IPPsychOONCurrent TABLE
				(
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueCurrent DECIMAL(9, 4),
				 OONDayRangeBeginCurrent TINYINT,
				 OONDayRangeEndCurrent TINYINT
				)

			INSERT INTO @IPPsychOONCurrent
			SELECT A.OONBenefitTypeID,
				   A.BenefitOrdinalID,
				   OONBenefitValueCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONBenefitValue,0)
												ELSE 0
											END,
				   OONDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeBegin, 0)
												ELSE 0
											 END,
				   OONDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeEnd, 0)
												ELSE 0
										   END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDCur
			AND A.BenefitOptionID = @BenefitOptionIDCur
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (66)
			AND A.IsBenefitYearCurrentYear = 1
			AND A.OONBenefitTypeID IS NOT NULL

			--------------------------------------------------------------------

			--IP Psych OON Bid
			DECLARE @IPPsychOONBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueProjected DECIMAL(9, 4),
				 OONDayRangeBeginProjected TINYINT,
				 OONDayRangeEndProjected TINYINT
				)

			INSERT INTO @IPPsychOONBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.OONBenefitTypeID,
				A.BenefitOrdinalID,
				OONBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONBenefitValue,0)
											ELSE 0
											END,
				OONDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeBegin, 0)
											ELSE 0
											END,
				OONDayRangeEndProjected =  CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeEnd, 0)
											ELSE 0
											END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (66)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.OONBenefitTypeID IS NOT NULL
		----------------------------------------------------------------------

		--IP Psych OON
		DECLARE @IPPsychOON TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			OONBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			OONBenefitValueCurrent DECIMAL(9, 4),
			OONDayRangeBeginCurrent TINYINT,
			OONDayRangeEndCurrent TINYINT,
			OONBenefitTypeIDProjected TINYINT,
			OONBenefitValueProjected DECIMAL(9, 4),
			OONDayRangeBeginProjected TINYINT,
			OONDayRangeEndProjected TINYINT
			)

		INSERT INTO @IPPsychOON
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.OONBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.OONBenefitValueCurrent,
			   Cur.OONDayRangeBeginCurrent,
			   Cur.OONDayRangeEndCurrent,
			   Proj.OONBenefitTypeID,
			   Proj.OONBenefitValueProjected,
			   Proj.OONDayRangeBeginProjected,
			   Proj.OONDayRangeEndProjected
		FROM @IPPsychOONCurrent Cur
		RIGHT OUTER JOIN @IPPsychOONBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'
		-------------------------------------------------------------------------------------

			--IP Rehab IN Current
			DECLARE @IPRehabINCurrent TABLE
				(
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueCurrent DECIMAL(9, 4),
				 INDayRangeBeginCurrent TINYINT,
				 INDayRangeEndCurrent TINYINT
				)

			INSERT INTO @IPRehabINCurrent
			SELECT A.INBenefitTypeID,
				   A.BenefitOrdinalID,
				   INBenefitValueCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN	ISNULL(INBenefitValue,0)
											ELSE 0
										   END,
				   INDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(INDayRangeBegin, 0)
												ELSE 0
											END,
				   INDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										  END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDCur
			  AND A.BenefitOptionID = @BenefitOptionIDCur
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (131)
			  AND A.IsBenefitYearCurrentYear = 1
			  AND A.INBenefitTypeID IS NOT NULL
			--------------------------------------------------------------------

			--IP Rehab IN Bid
			DECLARE @IPRehabINBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueProjected DECIMAL(9, 4),
				 INDayRangeBeginProjected TINYINT,
				 INDayRangeEndProjected TINYINT
				)

			INSERT INTO @IPRehabINBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.INBenefitTypeID,
				A.BenefitOrdinalID,
				INBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INBenefitValue,0)
											ELSE 0
										  END,
				INDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeBegin, 0)
											ELSE 0
										   END,
				INDayRangeEndProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										 END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (131)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.INBenefitTypeID IS NOT NULL

		----------------------------------------------------------------------

		--IP Rehab IN
		DECLARE @IPRehabIN TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			INBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			INBenefitValueCurrent DECIMAL(9, 4),
			INDayRangeBeginCurrent TINYINT,
			INDayRangeEndCurrent TINYINT,
			INBenefitTypeIDProjected TINYINT,
			INBenefitValueProjected DECIMAL(9, 4),
			INDayRangeBeginProjected TINYINT,
			INDayRangeEndProjected TINYINT
			)

		INSERT INTO @IPRehabIN
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.INBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.INBenefitValueCurrent,
			   Cur.INDayRangeBeginCurrent,
			   Cur.INDayRangeEndCurrent,
			   Proj.INBenefitTypeID,
			   Proj.INBenefitValueProjected,
			   Proj.INDayRangeBeginProjected,
			   Proj.INDayRangeEndProjected
		FROM @IPRehabINCurrent Cur
		RIGHT OUTER JOIN @IPRehabINBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'

		------------------------------------------------------------------

			--IP Rehab OON Current

			DECLARE @IPRehabOONCurrent TABLE
				(
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueCurrent DECIMAL(9, 4),
				 OONDayRangeBeginCurrent TINYINT,
				 OONDayRangeEndCurrent TINYINT
				)

			INSERT INTO @IPRehabOONCurrent
			SELECT A.OONBenefitTypeID,
				   A.BenefitOrdinalID,
				   OONBenefitValueCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONBenefitValue,0)
												ELSE 0
											END,
				   OONDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeBegin, 0)
												ELSE 0
											 END,
				   OONDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeEnd, 0)
												ELSE 0
										   END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDCur
			AND A.BenefitOptionID = @BenefitOptionIDCur
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (131)
			AND A.IsBenefitYearCurrentYear = 1
			AND A.OONBenefitTypeID IS NOT NULL

			--------------------------------------------------------------------

			--IP Rehab OON Bid
			DECLARE @IPRehabOONBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueProjected DECIMAL(9, 4),
				 OONDayRangeBeginProjected TINYINT,
				 OONDayRangeEndProjected TINYINT
				)

			INSERT INTO @IPRehabOONBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.OONBenefitTypeID,
				A.BenefitOrdinalID,
				OONBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONBenefitValue,0)
											ELSE 0
											END,
				OONDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeBegin, 0)
											ELSE 0
											END,
				OONDayRangeEndProjected =  CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeEnd, 0)
											ELSE 0
											END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (131)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.OONBenefitTypeID IS NOT NULL
		----------------------------------------------------------------------

		--IP Rehab OON
		DECLARE @IPRehabOON TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			OONBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			OONBenefitValueCurrent DECIMAL(9, 4),
			OONDayRangeBeginCurrent TINYINT,
			OONDayRangeEndCurrent TINYINT,
			OONBenefitTypeIDProjected TINYINT,
			OONBenefitValueProjected DECIMAL(9, 4),
			OONDayRangeBeginProjected TINYINT,
			OONDayRangeEndProjected TINYINT
			)

		INSERT INTO @IPRehabOON
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.OONBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.OONBenefitValueCurrent,
			   Cur.OONDayRangeBeginCurrent,
			   Cur.OONDayRangeEndCurrent,
			   Proj.OONBenefitTypeID,
			   Proj.OONBenefitValueProjected,
			   Proj.OONDayRangeBeginProjected,
			   Proj.OONDayRangeEndProjected
		FROM @IPRehabOONCurrent Cur
		RIGHT OUTER JOIN @IPRehabOONBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'
		-----------------------------------------------------------------------------

			--SNF IN Current
			DECLARE @SNFINCurrent TABLE
				(
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueCurrent DECIMAL(9, 4),
				 INDayRangeBeginCurrent TINYINT,
				 INDayRangeEndCurrent TINYINT
				)

			INSERT INTO @SNFINCurrent
			SELECT A.INBenefitTypeID,
				   A.BenefitOrdinalID,
				   INBenefitValueCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN	ISNULL(INBenefitValue,0)
											ELSE 0
										   END,
				   INDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(INDayRangeBegin, 0)
												ELSE 0
											END,
				   INDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										  END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDCur
			  AND A.BenefitOptionID = @BenefitOptionIDCur
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (83)
			  AND A.IsBenefitYearCurrentYear = 1
			  AND A.INBenefitTypeID IS NOT NULL
			--------------------------------------------------------------------

			--SNF IN Bid
			DECLARE @SNFINBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueProjected DECIMAL(9, 4),
				 INDayRangeBeginProjected TINYINT,
				 INDayRangeEndProjected TINYINT
				)

			INSERT INTO @SNFINBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.INBenefitTypeID,
				A.BenefitOrdinalID,
				INBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INBenefitValue,0)
											ELSE 0
										  END,
				INDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeBegin, 0)
											ELSE 0
										   END,
				INDayRangeEndProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										 END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (83)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.INBenefitTypeID IS NOT NULL

		----------------------------------------------------------------------

		--SNF IN
		DECLARE @SNFIN TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			INBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			INBenefitValueCurrent DECIMAL(9, 4),
			INDayRangeBeginCurrent TINYINT,
			INDayRangeEndCurrent TINYINT,
			INBenefitTypeIDProjected TINYINT,
			INBenefitValueProjected DECIMAL(9, 4),
			INDayRangeBeginProjected TINYINT,
			INDayRangeEndProjected TINYINT
			)

		INSERT INTO @SNFIN
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.INBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.INBenefitValueCurrent,
			   Cur.INDayRangeBeginCurrent,
			   Cur.INDayRangeEndCurrent,
			   Proj.INBenefitTypeID,
			   Proj.INBenefitValueProjected,
			   Proj.INDayRangeBeginProjected,
			   Proj.INDayRangeEndProjected
		FROM @SNFINCurrent Cur
		RIGHT OUTER JOIN @SNFINBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'

		------------------------------------------------------------------


			--SNF OON Current
			DECLARE @SNFOONCurrent TABLE
				(
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueCurrent DECIMAL(9, 4),
				 OONDayRangeBeginCurrent TINYINT,
				 OONDayRangeEndCurrent TINYINT
				)

			INSERT INTO @SNFOONCurrent
			SELECT A.OONBenefitTypeID,
				   A.BenefitOrdinalID,
				   OONBenefitValueCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONBenefitValue,0)
												ELSE 0
											END,
				   OONDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeBegin, 0)
												ELSE 0
											 END,
				   OONDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(OONDayRangeEnd, 0)
												ELSE 0
										   END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDCur
			AND A.BenefitOptionID = @BenefitOptionIDCur
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (83)
			AND A.IsBenefitYearCurrentYear = 1
			AND A.OONBenefitTypeID IS NOT NULL
			--------------------------------------------------------------------

			--SNF OON Bid
			DECLARE @SNFOONBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 OONBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 OONBenefitValueProjected DECIMAL(9, 4),
				 OONDayRangeBeginProjected TINYINT,
				 OONDayRangeEndProjected TINYINT
				)

			INSERT INTO @SNFOONBid
			SELECT
				A.PlanYearID,
				A.ForecastID,
				B.BenefitCategoryName,
				A.OONBenefitTypeID,
				A.BenefitOrdinalID,
				OONBenefitValueProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONBenefitValue,0)
											ELSE 0
											END,
				OONDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeBegin, 0)
											ELSE 0
											END,
				OONDayRangeEndProjected =  CASE IsBenefitYearCurrentYear
											WHEN 0 THEN ISNULL(OONDayRangeEnd, 0)
											ELSE 0
											END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			AND A.PlanInfoID = @PlanInfoIDBid
			AND A.BenefitOptionID = @BenefitOptionIDBid
			AND A.PlanYearID = @PlanYearID
			AND B.BenefitCategoryID IN (83)
			AND A.IsBenefitYearCurrentYear = 0
			AND A.OONBenefitTypeID IS NOT NULL
		----------------------------------------------------------------------

		--SNF OON
		DECLARE @SNFOON TABLE
			(
			PlanYearID SMALLINT,
			ForecastID INT,
			BenefitCategoryName VARCHAR(80),
			OONBenefitTypeIDCurrent TINYINT,
			BenefitOrdinalID TINYINT,
			OONBenefitValueCurrent DECIMAL(9, 4),
			OONDayRangeBeginCurrent TINYINT,
			OONDayRangeEndCurrent TINYINT,
			OONBenefitTypeIDProjected TINYINT,
			OONBenefitValueProjected DECIMAL(9, 4),
			OONDayRangeBeginProjected TINYINT,
			OONDayRangeEndProjected TINYINT
			)

		INSERT INTO @SNFOON
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   Proj.BenefitCategoryName,
			   Cur.OONBenefitTypeID,
			   Cur.BenefitOrdinalID,
			   Cur.OONBenefitValueCurrent,
			   Cur.OONDayRangeBeginCurrent,
			   Cur.OONDayRangeEndCurrent,
			   Proj.OONBenefitTypeID,
			   Proj.OONBenefitValueProjected,
			   Proj.OONDayRangeBeginProjected,
			   Proj.OONDayRangeEndProjected
		FROM @SNFOONCurrent Cur
		RIGHT OUTER JOIN @SNFOONBid Proj	
			ON Cur.BenefitOrdinalID=Proj.BenefitOrdinalID
		WHERE Proj.BenefitCategoryName <> 'NULL'
		--------------------------------------------------------------

			--PCP Current
			DECLARE @PCPCurrent TABLE
				(
				 INBenefitTypeID TINYINT,
				 INBenefitValueCurrent DECIMAL(9, 4),
				 INDayRangeBeginCurrent TINYINT,
				 INDayRangeEndCurrent TINYINT
				)

			INSERT INTO @PCPCurrent
			SELECT A.INBenefitTypeID,
				   INBenefitValueCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(INBenefitValue,0)
												ELSE 0
										   END,
				   INDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(INDayRangeBegin, 0)
												ELSE 0
											END,
				   INDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										  END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDCur
			  AND A.BenefitOptionID = @BenefitOptionIDCur
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (102)
			  AND BenefitOrdinalID = 1
			  AND A.IsBenefitYearCurrentYear = 1
			----------------------------------------------------------------

			--PCP Bid
			DECLARE @PCPBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueProjected DECIMAL(9, 4),
				 INDayRangeBeginProjected TINYINT,
				 INDayRangeEndProjected TINYINT
				)

			INSERT INTO @PCPBid
			SELECT A.PlanYearID,
				   A.ForecastID,
				   B.BenefitCategoryName,
				   A.INBenefitTypeID,
				   A.BenefitOrdinalID,
				   INBenefitValueProjected = CASE IsBenefitYearCurrentYear
												WHEN 0 THEN ISNULL(INBenefitValue,0)
												ELSE 0
											 END,
				   INDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
												WHEN 0 THEN ISNULL(INDayRangeBegin, 0)
												ELSE 0
											  END,
				   INDayRangeEndProjected = CASE IsBenefitYearCurrentYear
												WHEN 0 THEN ISNULL(INDayRangeEnd, 0)
												ELSE 0
											END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDBid
			  AND A.BenefitOptionID = @BenefitOptionIDBid
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (102)
			  AND BenefitOrdinalID = 1
			  AND A.IsBenefitYearCurrentYear = 0
		---------------------------------------------------------------------------------------

		--PCP Info
		DECLARE @PCPINFO TABLE

			(
			 PlanYearID SMALLINT,
			 ForecastID INT,
			 BenefitCategory VARCHAR(80),
			 PCPCurrent VARCHAR(MAX),
			 PCPProjected VARCHAR(MAX)
			)

		INSERT INTO @PCPINFO
		SELECT PlanYearID,
               ForecastID,
               BenefitCategory = BenefitCategoryName,
               PCPCurrent = CASE MAX(Cur.InBenefitTypeID)
                                WHEN 1 THEN
                                    CAST(CAST(ROUND(SUM(INBenefitValueCurrent) * 100,0) AS INT) AS VARCHAR) + '%'
                                ELSE
                                     '$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT) AS VARCHAR)
                            END,
               PCPProjected = CASE MAX(Proj.InBenefitTypeID)
                                WHEN 1 THEN
                                    CAST(CAST(ROUND(SUM(INBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
                                ELSE
                                     '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR)
                              END
        FROM @PCPCurrent Cur,
			 @PCPBid Proj    
		GROUP BY Proj.PlanYearID, 
				 Proj.ForecastID, 
				 Proj.BenefitCategoryName
		-----------------------------------------------------------------------------------------

			--SPEC Current
			DECLARE @SPECCurrent TABLE
				(
				 INBenefitTypeID TINYINT,
				 INBenefitValueCurrent DECIMAL(9, 4),
				 INDayRangeBeginCurrent TINYINT,
				 INDayRangeEndCurrent TINYINT
				)

			INSERT INTO @SPECCurrent
			SELECT A.INBenefitTypeID,
				   INBenefitValueCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN ISNULL(INBenefitValue,0)
										    ELSE 0
										   END,
				   INDayRangeBeginCurrent = CASE IsBenefitYearCurrentYear
												WHEN 1 THEN ISNULL(INDayRangeBegin, 0)
												ELSE 0
											END,
				   INDayRangeEndCurrent = CASE IsBenefitYearCurrentYear
											WHEN 1 THEN ISNULL(INDayRangeEnd, 0)
											ELSE 0
										  END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDCur
			  AND A.BenefitOptionID = @BenefitOptionIDCur
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (103)
			  AND BenefitOrdinalID = 1
			  AND A.IsBenefitYearCurrentYear = 1
			-------------------------------------------------------------

			--SPEC Bid
			DECLARE @SPECBid TABLE
				(
				 PlanYearID SMALLINT,
				 ForecastID INT,
				 BenefitCategoryName VARCHAR(80),
				 INBenefitTypeID TINYINT,
				 BenefitOrdinalID TINYINT,
				 INBenefitValueProjected DECIMAL(9, 4),
				 INDayRangeBeginProjected TINYINT,
				 INDayRangeEndProjected TINYINT
				)

			INSERT INTO @SPECBid
			SELECT A.PlanYearID,
				   A.ForecastID,
				   B.BenefitCategoryName,
				   A.INBenefitTypeID,
				   A.BenefitOrdinalID,
				   INBenefitValueProjected = CASE IsBenefitYearCurrentYear
												WHEN 0 THEN
													ISNULL(INBenefitValue,0)
												ELSE 0
												END,
				   INDayRangeBeginProjected = CASE IsBenefitYearCurrentYear
												WHEN 0 THEN
													ISNULL(INDayRangeBegin, 0)
												ELSE 0
												END,
				   INDayRangeEndProjected =  CASE IsBenefitYearCurrentYear
												WHEN 0 THEN
													ISNULL(INDayRangeEnd, 0)
												ELSE 0
												END
			FROM dbo.SavedPlanBenefitDetail A
			INNER JOIN dbo.LkpIntBenefitCategory B
				ON A.PlanYearID = B.PlanYearID
			   AND A.BenefitCategoryID = B.BenefitCategoryID
			WHERE A.ForecastID = @ForecastID
			  AND A.PlanInfoID = @PlanInfoIDBid
			  AND A.BenefitOptionID = @BenefitOptionIDBid
			  AND A.PlanYearID = @PlanYearID
			  AND B.BenefitCategoryID IN (103)
			  AND BenefitOrdinalID = 1
			  AND A.IsBenefitYearCurrentYear = 0
		--------------------------------------------------------------------------------------------

		--SPEC Info
		DECLARE @SPECINFO TABLE
			(
			 PlanYearID SMALLINT,
			 ForecastID INT,
			 BenefitCategory VARCHAR(80),
			 SPECCurrent VARCHAR(MAX),
			 SPECProjected VARCHAR(MAX)
			)

		INSERT INTO @SPECINFO
		SELECT Proj.PlanYearID,
			   Proj.ForecastID,
			   BenefitCategory = BenefitCategoryName,
			   SPECCurrent = 
							CASE MAX(Cur.InBenefitTypeID)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueCurrent) * 100,0) AS INT) AS VARCHAR) + '%'
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT) AS VARCHAR)
							END,
               SPECProjected =
								CASE MAX(Proj.InBenefitTypeID)
									WHEN 1 THEN
										CAST(CAST(ROUND(SUM(INBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									ELSE
										 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR)
								END
    FROM @SPECCurrent Cur,
		 @SPECBid Proj    
    GROUP BY Proj.PlanYearID, 
			 Proj.ForecastID, 
			 Proj.BenefitCategoryName
	---------------------------------------------------------------------------------------------------------------------

	INSERT INTO @RESULTS
    -- ------------------------------------------------------------------------------------------------------------------
    -- DISPLAYS MOOP IN/OON/Combined                                               
    -- ------------------------------------------------------------------------------------------------------------------
    SELECT PlanYearID,
		   ForecastID,
           Category = 'MOOP (IN/OON/Combined)',
		   CurrentVal = '$' + CASE CAST(ROUND(SUM(COALESCE(INMOOPCurrent,0)),0) AS INT)
                                WHEN 0 THEN '-' + ' / '
                                ELSE CAST(CAST(ROUND(SUM(INMOOPCurrent),0) AS INT) AS VARCHAR) + ' / '
							  END +
                        '$' + CASE CAST(ROUND(SUM(COALESCE(OONMOOPCurrent,0)),0) AS INT)
                                WHEN 0 THEN '-' + ' / '
								ELSE CAST(CAST(ROUND(SUM(OONMOOPCurrent),0) AS INT) AS VARCHAR) + ' / '
							  END +
                        '$' + CASE CAST(ROUND(SUM(COALESCE(COMBOMOOPCurrent,0)),0) AS INT)
                                WHEN 0 THEN '-' 
								ELSE CAST(CAST(ROUND(SUM(COMBOMOOPCurrent),0) AS INT) AS VARCHAR) 
							  END,                    
           ProjectedVal = '$' + CASE CAST(ROUND(SUM(COALESCE(INMOOPProjected,0)),0) AS INT)
									WHEN 0 THEN '-' + ' / '
									ELSE CAST(CAST(ROUND(SUM(INMOOPProjected),0) AS INT) AS VARCHAR) + ' / '
								END +
                          '$' + CASE CAST(ROUND(SUM(COALESCE(OONMOOPProjected,0)),0) AS INT)
									WHEN 0 THEN '-' + ' / '
									ELSE CAST(CAST(ROUND(SUM(OONMOOPProjected),0) AS INT)AS VARCHAR) + ' / '
								END +
                          '$' + CASE CAST(ROUND(SUM(COALESCE(COMBOMOOPProjected,0)),0) AS INT)
									WHEN 0 THEN '-' 
									ELSE CAST(CAST(ROUND(SUM(COMBOMOOPProjected),0) AS INT) AS VARCHAR)
								END
    FROM @MOOP
    GROUP BY PlanYearID, 
			 ForecastID
	-- ------------------------------------------------------------------------------------------------------------------
	UNION ALL
    -- ------------------------------------------------------------------------------------------------------------------
    -- DISPLAYS Deductible IN/OON
    -- ------------------------------------------------------------------------------------------------------------------
    SELECT PlanYearID,
           ForecastID,
           Category = CASE SUM(DeductiblePtBCurrent) WHEN 0 THEN 'Deductible (IN/OON)' ELSE 'Part B Deductible' END,
           CurrentVal = CASE SUM(DeductiblePtBCurrent) 
							WHEN 0 THEN
								'$' + CAST(CAST(ROUND(SUM(INDeductibleCurrent),0) AS INT)
										   AS VARCHAR) + ' / ' +
								'$' + CAST(CAST(ROUND(SUM(OONDeductibleCurrent),0) AS INT)
										   AS VARCHAR)
							ELSE '$' + CAST(CAST(ROUND(SUM(DeductiblePtBCurrent),0) AS INT) AS VARCHAR) 
									 + CASE SUM(DeductiblePtBCurrent) WHEN 0 THEN ' Part B Only ' ELSE '' END
						END,
           ProjectedVal = CASE SUM(DeductiblePtBProjected) 
							WHEN 0 THEN
							  '$' + CAST(
										CAST(ROUND(SUM(INDeductibleProjected),0) AS INT)
									AS VARCHAR) + ' / ' +
							  '$' + CAST(
										CAST(ROUND(SUM(OONDeductibleProjected),0) AS INT)
									AS VARCHAR)
							ELSE '$' + CAST(CAST(ROUND(SUM(DeductiblePtBProjected),0) AS INT) AS VARCHAR) 
									 + CASE SUM(DeductiblePtBCurrent) WHEN 0 THEN ' Part B Only ' ELSE '' END
						  END
    FROM @Deductible
    GROUP BY PlanYearID, 
			 ForecastID
    -- ------------------------------------------------------------------------------------------------------------------
	UNION ALL
    -- ------------------------------------------------------------------------------------------------------------------
    -- DISPLAYS IP Acute (ADD BENEFITS HERE)
    -- ------------------------------------------------------------------------------------------------------------------    
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @INAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(INBenefitTypeIDCurrent)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueCurrent) * 100,0) AS INT)	AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT)	AS VARCHAR) + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT) AS VARCHAR)
										 + @PerDay + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
										 + CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(INBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR) 
										 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
						   END
		FROM @IPAcuteIN
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
		----------------------------------------------------------------------------------------------------------------------------------------------------
		UNION ALL
		----------------------------------------------------------------------------------------------------------------------------------------------------
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @OONAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueCurrent) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(	CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT) AS VARCHAR)
									+ @Admit
								ELSE
									 '$' + 	CAST(CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT)AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR) 
									 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
							END
		FROM @IPAcuteOON
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
    -- ------------------------------------------------------------------------------------------------------------------
	UNION ALL
    -- ------------------------------------------------------------------------------------------------------------------
    -- DISPLAYS IP Psych
    -- ------------------------------------------------------------------------------------------------------------------    
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @INAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(INBenefitTypeIDCurrent)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueCurrent) * 100,0) AS INT)	AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT)	AS VARCHAR) + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT) AS VARCHAR)
										 + @PerDay + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
										 + CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(INBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR) 
										 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
						   END
		FROM @IPPsychIN
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
		----------------------------------------------------------------------------------------------------------------------------------------------------
		UNION ALL
		----------------------------------------------------------------------------------------------------------------------------------------------------
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @OONAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueCurrent) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(	CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT) AS VARCHAR)
									+ @Admit
								ELSE
									 '$' + 	CAST(CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT)AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR) 
									 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
							END
		FROM @IPPsychOON
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
    -- ------------------------------------------------------------------------------------------------------------------
	UNION ALL
    -- ------------------------------------------------------------------------------------------------------------------
    -- DISPLAYS IP Rehab
    -- ------------------------------------------------------------------------------------------------------------------    
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @INAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(INBenefitTypeIDCurrent)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueCurrent) * 100,0) AS INT)	AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT)	AS VARCHAR) + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT) AS VARCHAR)
										 + @PerDay + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
										 + CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(INBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR) 
										 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
						   END
		FROM @IPRehabIN
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
		----------------------------------------------------------------------------------------------------------------------------------------------------
		UNION ALL
		----------------------------------------------------------------------------------------------------------------------------------------------------
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @OONAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueCurrent) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(	CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT) AS VARCHAR)
									+ @Admit
								ELSE
									 '$' + 	CAST(CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT)AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR) 
									 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
							END
		FROM @IPRehabOON
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
	-- ------------------------------------------------------------------------------------------------------------------
	UNION ALL
    -- ------------------------------------------------------------------------------------------------------------------
    -- DISPLAYS SNF IN / OON
    -- ------------------------------------------------------------------------------------------------------------------
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @INAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(INBenefitTypeIDCurrent)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueCurrent) * 100,0) AS INT)	AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT)	AS VARCHAR) + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueCurrent),0) AS INT) AS VARCHAR)
										 + @PerDay + CAST(SUM(INDayRangeBeginCurrent) AS VARCHAR) +'-'
										 + CAST(SUM(INDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(INBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(INBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR) 
										 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(INBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(INDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(INDayRangeEndProjected) AS VARCHAR) +')' 
						   END
		FROM @SNFIN
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
		----------------------------------------------------------------------------------------------------------------------------------------------------
		UNION ALL
		----------------------------------------------------------------------------------------------------------------------------------------------------
		SELECT 
			PlanYearID,
			ForecastID,
			BenefitCategory = BenefitCategoryName + @OONAdd + CAST(BenefitOrdinalID AS VARCHAR),
			CurrentVal =	CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueCurrent) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
								WHEN 3 THEN
									'$' + CAST(	CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT) AS VARCHAR)
									+ @Admit
								ELSE
									 '$' + 	CAST(CAST(ROUND(SUM(OONBenefitValueCurrent),0) AS INT)AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginCurrent) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndCurrent) AS VARCHAR) +')'
							END,
			ProjectedVal = CASE MAX(OONBenefitTypeIDProjected)
								WHEN 1 THEN
									CAST(CAST(ROUND(SUM(OONBenefitValueProjected) * 100,0) AS INT) AS VARCHAR) + '%'
									+ @Days + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
								WHEN 3 THEN
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR) 
									 + @Admit
								ELSE
									 '$' + CAST(CAST(ROUND(SUM(OONBenefitValueProjected),0) AS INT) AS VARCHAR)
									+ @PerDay + CAST(SUM(OONDayRangeBeginProjected) AS VARCHAR) +'-'
									+ CAST(SUM(OONDayRangeEndProjected) AS VARCHAR) +')' 
							END
		FROM @SNFOON
		GROUP BY PlanYearID, 
				 ForecastID, 
				 BenefitCategoryName, 
				 BenefitOrdinalID
    -- ------------------------------------------------------------------------------------------------------------------
	UNION ALL
    -- ------------------------------------------------------------------------------------------------------------------
    -- DISPLAYS PCP/SPEC IN/OON
    -- ------------------------------------------------------------------------------------------------------------------
	SELECT
        PCPINFO.PlanYearID,
        PCPINFO.ForecastID,
        Category = 'PCP Cap (IN) / SPEC Cap (IN)',
        CurrentVal = CAST(PCPCurrent AS VARCHAR) + ' / ' + CAST(SPECCurrent AS VARCHAR),
        ProjectedVal = CAST(PCPProjected AS VARCHAR) + ' / ' + CAST(SPECProjected AS VARCHAR)
    FROM @PCPINFO PCPINFO,
		 @SPECINFO SPECINFO

RETURN
END
GO
