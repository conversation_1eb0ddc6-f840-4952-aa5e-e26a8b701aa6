SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spDeleteMSB
--
-- AUTHOR: Adam Gilbert
--
-- CREATED DATE: 2023-AUG-28
--
-- DESCRIPTION: Responsible for deleting all RPRXs in SavedPlanAddedBenefits for a plan during batch import
--
-- PARAMETERS:
--	Input:
--    @ForecastID
--
--	Output: NONE
--
-- RETURNS:
--
-- TABLES:
--	Read:
--    SavedPlanAddedBenefits
--
--	Write:
--    SavedPlanAddedBenefits
--
-- VIEWS:
--	Read:
--
-- STORED PROCS:
--	Executed:
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------
-- DATE         VERSION CHANGES MADE                                                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------
-- 2023-AUG-28  1       INITIAL VERSION. Cloned from SPDeleteMSB.                        Adam Gilbert
-- --------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spDeleteRPRXSelections]
	(
	@ForecastID INT
	)
AS

DELETE FROM SavedPlanAddedBenefits 
	WHERE ForecastID = @ForecastID
	AND BidServiceCatID = 35 --Related Party Only
GO
