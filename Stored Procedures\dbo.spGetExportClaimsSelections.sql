SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spGetExportClaimsSelections
--
-- CREATOR:			Nate Jacoby
--
-- CREATED DATE:	2010-MAR-29
--
-- DESCRIPTION:		Supports the Claims Selection Tab in the Model.
--		
-- PARAMETERS:
--  Input  :				@ForecastID INT
--							@WorkSheetID INT
--							@TrendID INT
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			CalcBenefitProjection
--					CalcPlanExperienceByBenefitCat
--					LkpExtCMSBidServiceCategory
--					LkpIntBenefitCategory
--					LkpIntFinanceCategory
--					SavedMATrendFactorDetail  
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnAppGetClaimForecastFactors
--					fnBenefitDataForClaimTool
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2010-MAR-29		1		    Initial Version											            Nate Jacoby
-- 2010-APR-07		2		    Corrected definition of TotalUnit						            Casey Sanders
-- 2010-OCT-05      3           Updated for 2012 logic                                              Nate Jacoby
-- 2010-OCT-15      4           Changed TrendYearID to TrendRefPoint and removed PlanYearID joins.  Joe Casey
-- 2011-JAN-28      5           Updated procedure as fnAppGetClaimForecastFactors has changed       Nate Jacoby
-- 2024-OCT-05		6			Add handling for IsIncludeInCostShareBasis field in base data
--									table CalcPlanExperienceByBenefitCat							Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spGetExportClaimsSelections]
    (
    @ForecastID  INT
   ,@WorkSheetID INT
   ,@TrendID     INT = NULL)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        --Variables
        DECLARE @XForecastID INT = @ForecastID;
        DECLARE @XWorkSheetID INT = @WorkSheetID;
        DECLARE @XTrendID INT = @TrendID;
        DECLARE @PlanYearID SMALLINT;
        DECLARE @NonDE VARCHAR(10);
        DECLARE @DE VARCHAR(10);
        DECLARE @Combined VARCHAR(10);

        SET @PlanYearID = dbo.fnGetBidYear ();
        SET @NonDE = 'Non-DE#';
        SET @DE = 'DE#';
        SET @Combined = 'Combined';

        -------------------------------------------------------------------------------------------------------------------------
        -- Base Exp C&U ---------------------------------------------------------------------------------------------------------
        IF @XWorkSheetID = 2

            BEGIN

                SELECT      b.BenefitCategoryID
                           ,b.BenefitCategoryName
                           ,b.BidServiceCatID
                           ,d.ServiceCategory
                           ,d.SubServiceCategory
                           ,c.FinanceCategoryID
                           ,c.FinanceCategoryCode
                           ,DualEligibleType = CASE a.DualEligibleTypeID WHEN 0 THEN @NonDE WHEN 1 THEN @DE WHEN 2 THEN
                                                                                                        @Combined ELSE
                                                                                                                  NULL END
                           ,SUM (INAdmits + OONAdmits) AS TotalAdmit
                           ,SUM (INUnits + OONUnits) AS TotalUnit
                           ,SUM (INAllowed + OONAllowed) AS TotalAllowed
                           ,SUM (INAdmits) AS INAdmits
                           ,SUM (INUnits) AS INUnits
                           ,SUM (INAllowed) AS INAllowed
                           ,SUM (OONAdmits) AS OONAdmits
                           ,SUM (OONUnits) AS OONUnits
                           ,SUM (OONAllowed) AS OONAllowed
                FROM        dbo.CalcPlanExperienceByBenefitCat a WITH (NOLOCK)
               INNER JOIN   dbo.LkpIntBenefitCategory b
                       ON a.BenefitCategoryID = b.BenefitCategoryID
               INNER JOIN   dbo.LkpIntFinanceCategory c WITH (NOLOCK)
                       ON c.FinanceCategoryID = b.FinanceCategoryID
               INNER JOIN   dbo.LkpExtCMSBidServiceCategory d WITH (NOLOCK)
                       ON d.BidServiceCategoryID = b.BidServiceCatID
                WHERE       a.ForecastID = @XForecastID
                            AND a.MARatingOptionID = 1  --Experience
                GROUP BY    CASE a.DualEligibleTypeID WHEN 0 THEN @NonDE WHEN 1 THEN @DE WHEN 2 THEN @Combined ELSE NULL END
                           ,b.BenefitCategoryID
                           ,b.BenefitCategoryName
                           ,b.BidServiceCatID
                           ,d.ServiceCategory
                           ,d.SubServiceCategory
                           ,c.FinanceCategoryID
                           ,c.FinanceCategoryCode
                ORDER BY    BenefitCategoryID;

            END;

        -------------------------------------------------------------------------------------------------------------------------
        -- Base Man C&U ---------------------------------------------------------------------------------------------------------

        IF @XWorkSheetID = 3

            BEGIN

                SELECT      b.BenefitCategoryID
                           ,b.BenefitCategoryName
                           ,b.BidServiceCatID
                           ,d.ServiceCategory
                           ,d.SubServiceCategory
                           ,c.FinanceCategoryID
                           ,c.FinanceCategoryCode
                           ,DualEligibleType = CASE a.DualEligibleTypeID WHEN 0 THEN @NonDE WHEN 1 THEN @DE WHEN 2 THEN
                                                                                                        @Combined ELSE
                                                                                                                  NULL END
                           ,SUM (INAdmits + OONAdmits) AS TotalAdmit
                           ,SUM (INUnits + OONUnits) AS TotalUnit
                           ,SUM (INAllowed + OONAllowed) AS TotalAllowed
                           ,SUM (INAdmits) AS INAdmits
                           ,SUM (INUnits) AS INUnits
                           ,SUM (INAllowed) AS INAllowed
                           ,SUM (OONAdmits) AS OONAdmits
                           ,SUM (OONUnits) AS OONUnits
                           ,SUM (OONAllowed) AS OONAllowed
                FROM        dbo.CalcPlanExperienceByBenefitCat a WITH (NOLOCK)
               INNER JOIN   dbo.LkpIntBenefitCategory b WITH (NOLOCK)
                       ON a.BenefitCategoryID = b.BenefitCategoryID
               INNER JOIN   dbo.LkpIntFinanceCategory c WITH (NOLOCK)
                       ON c.FinanceCategoryID = b.FinanceCategoryID
               INNER JOIN   dbo.LkpExtCMSBidServiceCategory d WITH (NOLOCK)
                       ON d.BidServiceCategoryID = b.BidServiceCatID
                WHERE       a.ForecastID = @XForecastID
                            AND a.MARatingOptionID = 2  --Manual
                GROUP BY    CASE a.DualEligibleTypeID WHEN 0 THEN @NonDE WHEN 1 THEN @DE WHEN 2 THEN @Combined ELSE NULL END
                           ,b.BenefitCategoryID
                           ,b.BenefitCategoryName
                           ,b.BidServiceCatID
                           ,d.ServiceCategory
                           ,d.SubServiceCategory
                           ,c.FinanceCategoryID
                           ,c.FinanceCategoryCode
                ORDER BY    BenefitCategoryID;
            END;

        -------------------------------------------------------------------------------------------------------------------------
        -- Proj Exp C&U ---------------------------------------------------------------------------------------------------------
        IF @XWorkSheetID = 4
            BEGIN
                SELECT      b.BenefitCategoryID
                           ,b.BenefitCategoryName
                           ,b.BidServiceCatID
                           ,d.ServiceCategory
                           ,d.SubServiceCategory
                           ,c.FinanceCategoryID
                           ,c.FinanceCategoryCode
                           ,DualEligibleType = CASE a.DualEligibleTypeID WHEN 0 THEN @NonDE WHEN 1 THEN @DE WHEN 2 THEN
                                                                                                        @Combined ELSE
                                                                                                                  NULL END
                           ,TotalAdmit = (INAdmits + OONAdmits)
                           ,TotalUnit = (INUnits + OONUnits)
                           ,TotalAllowed = (INAllowed + OONAllowed)
                           ,INAdmits
                           ,INUnits
                           ,INAllowed
                           ,OONAdmits
                           ,OONUnits
                           ,OONAllowed
                FROM        dbo.CalcBenefitProjection a WITH (NOLOCK)
               INNER JOIN   dbo.LkpIntBenefitCategory b WITH (NOLOCK)
                       ON a.BenefitCategoryID = b.BenefitCategoryID
               INNER JOIN   dbo.LkpIntFinanceCategory c WITH (NOLOCK)
                       ON c.FinanceCategoryID = b.FinanceCategoryID
               INNER JOIN   dbo.LkpExtCMSBidServiceCategory d WITH (NOLOCK)
                       ON d.BidServiceCategoryID = b.BidServiceCatID
                WHERE       a.ForecastID = @XForecastID
                            AND a.MARatingOptionID = 1
                ORDER BY    BenefitCategoryID;
            END;

        -------------------------------------------------------------------------------------------------------------------------
        --Proj Man C&U ----------------------------------------------------------------------------------------------------------

        IF @XWorkSheetID = 5
            BEGIN
                SELECT      b.BenefitCategoryID
                           ,b.BenefitCategoryName
                           ,b.BidServiceCatID
                           ,d.ServiceCategory
                           ,d.SubServiceCategory
                           ,c.FinanceCategoryID
                           ,c.FinanceCategoryCode
                           ,DualEligibleType = CASE a.DualEligibleTypeID WHEN 0 THEN @NonDE WHEN 1 THEN @DE WHEN 2 THEN
                                                                                                        @Combined ELSE
                                                                                                                  NULL END
                           ,TotalAdmit = (INAdmits + OONAdmits)
                           ,TotalUnit = (INUnits + OONUnits)
                           ,TotalAllowed = (INAllowed + OONAllowed)
                           ,INAdmits
                           ,INUnits
                           ,INAllowed
                           ,OONAdmits
                           ,OONUnits
                           ,OONAllowed
                FROM        dbo.CalcBenefitProjection a WITH (NOLOCK)
               INNER JOIN   dbo.LkpIntBenefitCategory b WITH (NOLOCK)
                       ON a.BenefitCategoryID = b.BenefitCategoryID
               INNER JOIN   dbo.LkpIntFinanceCategory c WITH (NOLOCK)
                       ON c.FinanceCategoryID = b.FinanceCategoryID
               INNER JOIN   dbo.LkpExtCMSBidServiceCategory d WITH (NOLOCK)
                       ON d.BidServiceCategoryID = b.BidServiceCatID
                WHERE       a.ForecastID = @XForecastID
                            AND a.MARatingOptionID = 2
                ORDER BY    BenefitCategoryID;
            END;

        -------------------------------------------------------------------------------------------------------------------------
        --Proj Blended C&U ------------------------------------------------------------------------------------------------------
        IF @XWorkSheetID = 6
            BEGIN
                SELECT      b.BenefitCategoryID
                           ,b.BenefitCategoryName
                           ,b.BidServiceCatID
                           ,d.ServiceCategory
                           ,d.SubServiceCategory
                           ,c.FinanceCategoryID
                           ,c.FinanceCategoryCode
                           ,DualEligibleType = CASE a.DualEligibleTypeID WHEN 0 THEN @NonDE WHEN 1 THEN @DE WHEN 2 THEN
                                                                                                        @Combined ELSE
                                                                                                                  NULL END
                           ,TotalAdmit = (INAdmits + OONAdmits)
                           ,TotalUnit = (INUnits + OONUnits)
                           ,TotalAllowed = (INAllowed + OONAllowed)
                           ,INAdmits
                           ,INUnits
                           ,INAllowed
                           ,OONAdmits
                           ,OONUnits
                           ,OONAllowed
                FROM        dbo.CalcBenefitProjection a WITH (NOLOCK)
               INNER JOIN   dbo.LkpIntBenefitCategory b WITH (NOLOCK)
                       ON a.BenefitCategoryID = b.BenefitCategoryID
               INNER JOIN   dbo.LkpIntFinanceCategory c WITH (NOLOCK)
                       ON c.FinanceCategoryID = b.FinanceCategoryID
               INNER JOIN   dbo.LkpExtCMSBidServiceCategory d WITH (NOLOCK)
                       ON d.BidServiceCategoryID = b.BidServiceCatID
                WHERE       a.ForecastID = @XForecastID
                            AND a.MARatingOptionID = 3
                ORDER BY    BenefitCategoryID;
            END;

        -------------------------------------------------------------------------------------------------------------------------
        -- Experience Claim Factors ---------------------------------------------------------------------------------------------
        IF @XWorkSheetID = 7
            BEGIN
                SELECT  FactorName
                       ,WS1Column
                       ,FactorType
                       ,Exp_E2C
                       ,Exp_C2P
                FROM    dbo.fnAppGetClaimForecastFactors (@XForecastID);
            END;

        -------------------------------------------------------------------------------------------------------------------------
        -- Manual Claim Factors -------------------------------------------------------------------------------------------------
        IF @XWorkSheetID = 8
            BEGIN
                SELECT  FactorName
                       ,WS1Column
                       ,FactorType
                       ,Man_E2C
                       ,Man_C2P
                FROM    dbo.fnAppGetClaimForecastFactors (@XForecastID);
            END;

        -------------------------------------------------------------------------------------------------------------------------
        -- Annual Trends --------------------------------------------------------------------------------------------------------
        IF (@XWorkSheetID = 9 AND   @XTrendID IS NOT NULL)
            BEGIN
                SELECT      TrendYearID = (@PlanYearID - td.TrendRefPoint)
                           ,bc.BenefitCategoryName
                           ,bs.ServiceCategory
                           ,fc.FinanceCategoryCode
                           ,INCostTrend = SUM (
                                          CASE IsInNetwork WHEN 0 THEN 0
                                                           WHEN 1 THEN
                                                               CASE TrendTypeID WHEN 1 THEN TrendFactor WHEN 2 THEN 0 ELSE
                                                                                                                  NULL END
                                                           ELSE NULL END)
                           ,INUtilTrend = SUM (
                                          CASE IsInNetwork WHEN 0 THEN 0
                                                           WHEN 1 THEN
                                                               CASE TrendTypeID WHEN 1 THEN 0 WHEN 2 THEN TrendFactor ELSE
                                                                                                                  NULL END
                                                           ELSE NULL END)
                           ,OONCostTrend = SUM (
                                           CASE IsInNetwork WHEN 0 THEN
                                                                CASE TrendTypeID WHEN 1 THEN TrendFactor WHEN 2 THEN 0 ELSE
                                                                                                                   NULL END
                                                            WHEN 1 THEN 0
                                                            ELSE NULL END)
                           ,OONUtilTrend = SUM (
                                           CASE IsInNetwork WHEN 0 THEN
                                                                CASE TrendTypeID WHEN 1 THEN 0 WHEN 2 THEN TrendFactor ELSE
                                                                                                                   NULL END
                                                            WHEN 1 THEN 0
                                                            ELSE NULL END)

                FROM        dbo.SavedMATrendFactorDetail td WITH (NOLOCK)
               INNER JOIN   dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
                       ON td.BenefitCategoryID = bc.BenefitCategoryID
               INNER JOIN   dbo.LkpExtCMSBidServiceCategory bs WITH (NOLOCK)
                       ON bc.BidServiceCatID = bs.BidServiceCategoryID
               INNER JOIN   dbo.LkpIntFinanceCategory fc WITH (NOLOCK)
                       ON bc.FinanceCategoryID = fc.FinanceCategoryID
                WHERE       td.TrendID = @TrendID
                GROUP BY    td.TrendID
                           ,td.TrendRefPoint
                           ,bc.BenefitCategoryName
                           ,bs.ServiceCategory
                           ,fc.FinanceCategoryCode;
            END;

        -------------------------------------------------------------------------------------------------------------------------
        -- Plan Level -----------------------------------------------------------------------------------------------------------
        IF @XWorkSheetID = 10
            BEGIN
                SELECT  AddedBenefits
                       ,BenefitFactor
                       ,Credibility = dbo.fnGetCredibilityFactor (@XForecastID)
                FROM    dbo.fnBenefitDataForClaimTool (@XForecastID);
            END;

    END;
GO
