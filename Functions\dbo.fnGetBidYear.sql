SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetBidYear
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2010-Dec-15
--
-- DESCRIPTION: Returns the bid year
--
-- PARAMETERS:
--	Input: None
--
-- RETURNS: Returns the bid year
--
-- TABLES: 
--	Read: LkpIntPlanYear
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		    CHANGES MADE						                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2010-Dec-15		1			Initial Version							                Michael Siekerka
--
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBidYear]()
RETURNS INT AS
BEGIN
    DECLARE @PlanYearID INT
    SELECT @PlanYearID = ISNULL(PlanYearID, 0) FROM LkpIntPlanYear WHERE IsBidYear = 1
    RETURN @PlanYearID
END

GO
