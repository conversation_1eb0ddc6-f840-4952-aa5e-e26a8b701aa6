SET QUOTED_IDENTIFIER ON;
GO
SET ANSI_NULLS ON;
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcMOOPFactors
--
-- CREATOR:			Franklin Fu
--
-- CREATED DATE:	2024-AUG-19
--
-- DESCRIPTION:		Stored Procedure responsible for updating CalcMOOPFactors table.
--					It is calculated separately for IN vs OON.
--					The MOOP factors are used later in the cost share process, where they
--						are multiplied by cost share* (copay/coinsurance) claims
--						to obtain the post-MOOP cost share* (copay/coinsurance) claims.
--
-- PARAMETERS:
--  Input  :		@ForecastID               INT
--					@IsBenefitYearCurrentYear BIT
--					@UserID                   CHAR(7)
--
--  Output :		NONE
--
-- TABLES :
--	Read :			Benefits_SavedBenefitOption (deductible and MOOP values)
--					CalcAdjustedContinuance		(main table for calculating initial MOOP factors)
--					EffectiveCoinsurance		(used in MOOP factor calculation)
--					CalcBenefitProjectionPreMbrCS
--					LkpIntBenefitCategory
--					LkpExtCMSBidServiceCategory
--					SavedForecastSetup
--					SavedPlanInfo
--					LkpIntMOOPCategoryDetail
--
--  Write:			CalcMOOPFactors
--
-- VIEWS:
--	Read:			SavedPlanHeader
--
-- FUNCTIONS:		fnGetBidYear
--					fnGetSafeDivisionResult
--					fnMin
--
-- STORED PROCS:	NONE
--
-- $HISTORY
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-AUG-19		1		    Initial Version                                                     Franklin Fu
-- 2024-SEP-27		2			Added CASE WHEN statement when calculating EffectiveCoinsurance
--								to fix Div/0 errors													Franklin Fu
-- 2024-SEP-30		3			Calculate percentage allowed subject to deductible (see temp
--									table #tempAllowedSubjectToDeductible) to correct an issue
--									with the effective coinsurance calculations.					Jake Lewis
-- 2024-OCT-09		4			Only pull in cost share basis data from
--									table CalcBenefitProjectionPreMbrCS								Jake Lewis
-- 2026-APR-21		5			Correct an issue in the calculation for mid year cost share
--									i.e. IsBenefitYearCurrentYear = 1								Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spCalcMOOPFactors]
(
    @ForecastID INT,
    @IsBenefitYearCurrentYear BIT,
    @UserID CHAR(7)
)
AS
BEGIN

    SET NOCOUNT ON; --Suppress row count messages
    SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages

    BEGIN TRY

        DECLARE @tranCount INT = @@TranCount; --Current transaction count

        BEGIN TRANSACTION transaction_spCMF;

        DECLARE @XForecastID INT = @ForecastID,
                @XIsBenefitYearCurrentYear BIT = @IsBenefitYearCurrentYear,
                @XUserID CHAR(7) = @UserID;

        DECLARE @PlanYearID SMALLINT,
                @PlanInfoID INT,
                @BenefitOptionID TINYINT,
                @LastDateTime DATETIME,
                @NoMOOP INT;

        SELECT @PlanYearID = dbo.fnGetBidYear();
        SET @PlanInfoID =
        (
            SELECT DISTINCT
                   PlanInfoID
            FROM dbo.SavedPlanBenefitDetail WITH (NOLOCK)
            WHERE ForecastID = @XForecastID
                  AND IsLiveIndex = 1
        );
        SET @BenefitOptionID =
        (
            SELECT DISTINCT
                   BenefitOptionID
            FROM dbo.SavedPlanBenefitDetail WITH (NOLOCK)
            WHERE PlanInfoID = @PlanInfoID
                  AND IsLiveIndex = 1
        );

        SET @LastDateTime = GETDATE();
        SET @NoMOOP = 99999999;

        DECLARE @HasOON BIT;
        SELECT @HasOON = IsOONPermitted
        FROM dbo.SavedPlanHeader sph WITH (NOLOCK)
            INNER JOIN dbo.LkpProductType cpt WITH (NOLOCK)
                ON sph.PlanTypeID = cpt.ProductTypeID
        WHERE ForecastID = @XForecastID;

        --Read in deductible values from source table. When deductible is combined, fill in IN/OON fields with it since our calcs are IN/OON-based

        DECLARE @INDeductible DECIMAL(20, 12),
                @OONDeductible DECIMAL(20, 12);

        DECLARE @NoDeductible INT;
        SET @NoDeductible = 0;

        SELECT @INDeductible
            = ISNULL(CAST(INDeductible AS INT), ISNULL(CAST(CombinedDeductible AS INT), @NoDeductible)),
               @OONDeductible
                   = ISNULL(CAST(OONDeductible AS INT), ISNULL(CAST(CombinedDeductible AS INT), @NoDeductible))
        FROM dbo.Benefits_SavedBenefitOption WITH (NOLOCK)
        WHERE PlanInfoID = @PlanInfoID
              AND BenefitOptionID = CASE
                                        WHEN @XIsBenefitYearCurrentYear = 0 THEN
                                            @BenefitOptionID
                                        ELSE
                                            1
                                    END;

        --Read in MOOP values from source table. When there is no IN/OON MOOP, use Combined MOOP in its place since our calcs are IN/OON based
        DECLARE @INMOOP INT,
                @OONMOOP INT,
                @CombinedMOOP INT;

        SELECT @INMOOP = ISNULL(CAST(INMOOP AS INT), ISNULL(CAST(CombinedMOOP AS INT), @NoMOOP)),
               @OONMOOP = ISNULL(CAST(OONMOOP AS INT), ISNULL(CAST(CombinedMOOP AS INT), @NoMOOP)),
               @CombinedMOOP = ISNULL(CAST(CombinedMOOP AS INT), @NoMOOP)
        FROM dbo.Benefits_SavedBenefitOption WITH (NOLOCK)
        WHERE PlanInfoID = @PlanInfoID
              AND BenefitOptionID = CASE
                                        WHEN @XIsBenefitYearCurrentYear = 0 THEN
                                            @BenefitOptionID
                                        ELSE
                                            1
                                    END;

        --Bring back Adjusted Continuance Table (created in spCalcDeductibleFactors)
        DROP TABLE IF EXISTS #CalcAdjustedContinuance;
        CREATE TABLE #CalcAdjustedContinuance
        (
            ForecastID INT,
            IsBenefitYearCurrentYear INT,
            IsInNetwork INT,
            MOOPBucketID INT,
            Members INT,
            [Distribution] DECIMAL(12, 10),
            AdjAvgAllowed DECIMAL(38, 12),
            IPAdjAvgAllowed DECIMAL(38, 12),
            SNFAdjAvgAllowed DECIMAL(38, 12),
            OtherAdjAvgAllowed DECIMAL(38, 12),
            AdjAvgDedAllowed DECIMAL(38, 12),
            IPAdjAvgDedAllowed DECIMAL(38, 12),
            SNFAdjAvgDedAllowed DECIMAL(38, 12),
            OtherAdjAvgDedAllowed DECIMAL(38, 12),
            IPMinDeductibleAllowed DECIMAL(38, 12),
            SNFMinDeductibleAllowed DECIMAL(38, 12),
            OtherMinDeductibleAllowed DECIMAL(38, 12),
            MinDeductibleAllowed DECIMAL(38, 12)
        );
        INSERT INTO #CalcAdjustedContinuance
        (
            ForecastID,
            IsBenefitYearCurrentYear,
            IsInNetwork,
            MOOPBucketID,
            Members,
            Distribution,
            AdjAvgAllowed,
            IPAdjAvgAllowed,
            SNFAdjAvgAllowed,
            OtherAdjAvgAllowed,
            AdjAvgDedAllowed,
            IPAdjAvgDedAllowed,
            SNFAdjAvgDedAllowed,
            OtherAdjAvgDedAllowed,
            IPMinDeductibleAllowed,
            SNFMinDeductibleAllowed,
            OtherMinDeductibleAllowed,
            MinDeductibleAllowed
        )
        SELECT ForecastID,
               IsBenefitYearCurrentYear,
               IsInNetwork,
               MOOPBucketID,
               Members,
               [Distribution],
               AdjAvgAllowed,
               IPAdjAvgAllowed,
               SNFAdjAvgAllowed,
               OtherAdjAvgAllowed,
               AdjAvgDedAllowed,
               IPAdjAvgDedAllowed,
               SNFAdjAvgDedAllowed,
               OtherAdjAvgDedAllowed,
               IPMinDeductibleAllowed,
               SNFMinDeductibleAllowed,
               OtherMinDeductibleAllowed,
               MinDeductibleAllowed
        FROM dbo.CalcAdjustedContinuance WITH (NOLOCK)
        WHERE ForecastID = @XForecastID
              AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear;

        CREATE NONCLUSTERED INDEX [IX_NC_CalcAdjustedContinuance]
        ON #CalcAdjustedContinuance (
                                        IsBenefitYearCurrentYear,
                                        IsInNetwork
                                    );

        --Create table to match BenefitCategoryID with MOOPCategoryID
        DROP TABLE IF EXISTS #tempLkpIntMOOPCategoryDetail;
        CREATE TABLE #tempLkpIntMOOPCategoryDetail
        (
            MOOPCategoryID INT,
            BenefitCategoryID INT
        );
        INSERT INTO #tempLkpIntMOOPCategoryDetail
        (
            MOOPCategoryID,
            BenefitCategoryID
        )
        SELECT DISTINCT
               MOOPCategoryID,
               BenefitCategoryID
        FROM dbo.LkpIntMOOPCategoryDetail WITH (NOLOCK)
        WHERE MOOPCategoryID IN ( 1, 2, 3 );

        --Break out Effective Coinsurance and Allowed by MOOPCategoryID
        DROP TABLE IF EXISTS #tempCalcEffectiveCoinsurance;
        CREATE TABLE #tempCalcEffectiveCoinsurance
        (
            ForecastID INT,
            INEffectiveCoinsurance DECIMAL(38, 20),
            OONEffectiveCoinsurance DECIMAL(38, 20),
            INAllowed DECIMAL(20, 12),
            OONAllowed DECIMAL(20, 12),
            IsBenefitYearCurrentYear INT,
            BenefitCategoryID INT,
            INIPEffectiveCoinsurance DECIMAL(38, 20),
            INSNFEffectiveCoinsurance DECIMAL(38, 20),
            INOtherEffectiveCoinsurance DECIMAL(38, 20),
            OONIPEffectiveCoinsurance DECIMAL(38, 20),
            OONSNFEffectiveCoinsurance DECIMAL(38, 20),
            OONOtherEffectiveCoinsurance DECIMAL(38, 20),
            INIPAllowed DECIMAL(20, 12),
            INSNFAllowed DECIMAL(20, 12),
            INOtherAllowed DECIMAL(20, 12),
            OONIPAllowed DECIMAL(20, 12),
            OONSNFAllowed DECIMAL(20, 12),
            OONOtherAllowed DECIMAL(20, 12)
        );
        INSERT INTO #tempCalcEffectiveCoinsurance
        (
            ForecastID,
            INEffectiveCoinsurance,
            OONEffectiveCoinsurance,
            INAllowed,
            OONAllowed,
            IsBenefitYearCurrentYear,
            BenefitCategoryID,
            INIPEffectiveCoinsurance,
            INSNFEffectiveCoinsurance,
            INOtherEffectiveCoinsurance,
            OONIPEffectiveCoinsurance,
            OONSNFEffectiveCoinsurance,
            OONOtherEffectiveCoinsurance,
            INIPAllowed,
            INSNFAllowed,
            INOtherAllowed,
            OONIPAllowed,
            OONSNFAllowed,
            OONOtherAllowed
        )
        SELECT CEC.ForecastID,
               CEC.INEffectiveCoinsurance,
               CEC.OONEffectiveCoinsurance,
               CEC.INAllowed,
               CEC.OONAllowed,
               CEC.IsBenefitYearCurrentYear,
               CEC.BenefitCategoryID,
               INIPEffectiveCoinsurance = IIF(MOOP.MOOPCategoryID = 1, CEC.INEffectiveCoinsurance, 0),
               INSNFEffectiveCoinsurance = IIF(MOOP.MOOPCategoryID = 2, CEC.INEffectiveCoinsurance, 0),
               INOtherEffectiveCoinsurance = IIF(MOOP.MOOPCategoryID = 3, CEC.INEffectiveCoinsurance, 0),
               OONIPEffectiveCoinsurance = IIF(MOOP.MOOPCategoryID = 1, CEC.OONEffectiveCoinsurance, 0),
               OONSNFEffectiveCoinsurance = IIF(MOOP.MOOPCategoryID = 2, CEC.OONEffectiveCoinsurance, 0),
               OONOtherEffectiveCoinsurance = IIF(MOOP.MOOPCategoryID = 3, CEC.OONEffectiveCoinsurance, 0),
               INIPAllowed = IIF(MOOP.MOOPCategoryID = 1, CEC.INAllowed, 0),
               INSNFAllowed = IIF(MOOP.MOOPCategoryID = 2, CEC.INAllowed, 0),
               INOtherAllowed = IIF(MOOP.MOOPCategoryID = 3, CEC.INAllowed, 0),
               OONIPAllowed = IIF(MOOP.MOOPCategoryID = 1, CEC.OONAllowed, 0),
               OONSNFAllowed = IIF(MOOP.MOOPCategoryID = 2, CEC.OONAllowed, 0),
               OONOtherAllowed = IIF(MOOP.MOOPCategoryID = 3, CEC.OONAllowed, 0)
        FROM dbo.CalcEffectiveCoinsurance CEC WITH (NOLOCK)
            JOIN #tempLkpIntMOOPCategoryDetail MOOP
                ON MOOP.BenefitCategoryID = CEC.BenefitCategoryID
        WHERE CEC.ForecastID = @XForecastID
              AND CEC.IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear;


        -- Calculate % Allowed Subject to Deductible for IP, SNF, Other, and Total
        DROP TABLE IF EXISTS #tempAllowedSubjectToDeductible;
        CREATE TABLE #tempAllowedSubjectToDeductible
        (
            ForecastID INT,
            INIPPercentAllowedSubjectToDeductible DECIMAL(38, 12),
            INSNFPercentAllowedSubjectToDeductible DECIMAL(38, 12),
            INOtherPercentAllowedSubjectToDeductible DECIMAL(38, 12),
            INTotalPercentAllowedSubjectToDeductible DECIMAL(38, 12),
            OONIPPercentAllowedSubjectToDeductible DECIMAL(38, 12),
            OONSNFPercentAllowedSubjectToDeductible DECIMAL(38, 12),
            OONOtherPercentAllowedSubjectToDeductible DECIMAL(38, 12),
            OONTotalPercentAllowedSubjectToDeductible DECIMAL(38, 12)
        );
        INSERT INTO #tempAllowedSubjectToDeductible
        (
            ForecastID,
            INIPPercentAllowedSubjectToDeductible,
            INSNFPercentAllowedSubjectToDeductible,
            INOtherPercentAllowedSubjectToDeductible,
            INTotalPercentAllowedSubjectToDeductible,
            OONIPPercentAllowedSubjectToDeductible,
            OONSNFPercentAllowedSubjectToDeductible,
            OONOtherPercentAllowedSubjectToDeductible,
            OONTotalPercentAllowedSubjectToDeductible
        )
        SELECT t1.ForecastID,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 1 THEN
                              t1.INPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS INIPPercentAllowedSubjectToDeductible,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 2 THEN
                              t1.INPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS INSNFPercentAllowedSubjectToDeductible,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 3 THEN
                              t1.INPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS INOtherPercentAllowedSubjectToDeductible,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 5 THEN
                              t1.INPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS INTotalPercentAllowedSubjectToDeductible,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 1 THEN
                              t1.OONPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS OONIPPercentAllowedSubjectToDeductible,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 2 THEN
                              t1.OONPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS OONSNFPercentAllowedSubjectToDeductible,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 3 THEN
                              t1.OONPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS OONOtherPercentAllowedSubjectToDeductible,
               SUM(   CASE
                          WHEN t1.MOOPCategoryID = 5 THEN
                              t1.OONPercentAllowedSubjectToDeductible
                          ELSE
                              0
                      END
                  ) AS OONTotalPercentAllowedSubjectToDeductible
        FROM
        (
            SELECT pmc.ForecastID,
                   mcd.MOOPCategoryID,
                   dbo.fnGetSafeDivisionResult(SUM(pmc.INNetworkAllowed * spbd.INDedApplies), SUM(pmc.INNetworkAllowed)) AS INPercentAllowedSubjectToDeductible,
                   dbo.fnGetSafeDivisionResult(
                                                  SUM(pmc.OONNetworkAllowed * spbd.OONDedApplies),
                                                  SUM(pmc.OONNetworkAllowed)
                                              ) AS OONPercentAllowedSubjectToDeductible
            FROM dbo.CalcBenefitProjectionPreMbrCS pmc
                LEFT JOIN dbo.LkpIntBenefitCategory bc
                    ON bc.BenefitCategoryID = pmc.BenefitCategoryID
                LEFT JOIN dbo.LkpExtCMSBidServiceCategory sc
                    ON sc.BidServiceCategoryID = bc.BidServiceCatID
                LEFT JOIN dbo.SavedForecastSetup sfs
                    ON sfs.ForecastID = pmc.ForecastID
                LEFT JOIN dbo.SavedPlanInfo spi
                    ON spi.PlanInfoID = sfs.PlanInfoID
                LEFT JOIN dbo.LkpProductType pt
                    ON pt.ProductTypeID = spi.ProductTypeID
                LEFT JOIN dbo.LkpIntMOOPCategoryDetail mcd
                    ON mcd.ProductID = pt.ProductMOOPID
                       AND mcd.BenefitCategoryID = pmc.BenefitCategoryID
                LEFT JOIN dbo.SavedPlanBenefitDetail spbd
                    ON spbd.ForecastID = pmc.ForecastID
                       AND spbd.BenefitCategoryID = pmc.BenefitCategoryID
            WHERE pmc.ForecastID = @XForecastID
                  AND pmc.DualEligibleTypeID = 0 --NonDual
                  AND pmc.MARatingOptionID = 3 --Blended
                  AND pmc.IsIncludeInCostShareBasis = 1 --Included in cost share basis
                  AND spbd.BenefitOptionID = CASE
                                                 WHEN @XIsBenefitYearCurrentYear = 0 THEN
                                                     @BenefitOptionID
                                                 ELSE
                                                     1
                                             END
                  AND spbd.IsLiveIndex = CASE
                                             WHEN @XIsBenefitYearCurrentYear = 0 THEN
                                                 1
                                             ELSE
                                                 0
                                         END
                  AND spbd.BenefitOrdinalID = 1 --Needed to eliminate double counting, if deductible exclusions ever varied by BenefitOrdinalID within a Benefit Category we would need to rework this
            GROUP BY pmc.ForecastID,
                     mcd.MOOPCategoryID
        ) t1
        GROUP BY t1.ForecastID;


        --Bring in Total Effective Coinsurance by Plan as weighted average of benefit-level values (calculated in spCalcEffectiveCoinsurance)

        DECLARE @INEffectiveCoinsurance DECIMAL(38, 20),
                @INIPEffectiveCoinsurance DECIMAL(38, 20),
                @INSNFEffectiveCoinsurance DECIMAL(38, 20),
                @INOtherEffectiveCoinsurance DECIMAL(38, 20),
                @OONEffectiveCoinsurance DECIMAL(38, 20),
                @OONIPEffectiveCoinsurance DECIMAL(38, 20),
                @OONSNFEffectiveCoinsurance DECIMAL(38, 20),
                @OONOtherEffectiveCoinsurance DECIMAL(38, 20);

        SELECT @INEffectiveCoinsurance
            = CASE
                  WHEN SUM(CEC.INAllowed
                           * (1 - CDF.INTotalDeductibleFactor * astd.INTotalPercentAllowedSubjectToDeductible)
                          ) = 0 THEN
                      0
                  ELSE
                      SUM(CEC.INEffectiveCoinsurance * CEC.INAllowed
                          * (1 - CDF.INTotalDeductibleFactor * SPBD.INDedApplies)
                         )
                      / SUM(CEC.INAllowed
                            * (1 - CDF.INTotalDeductibleFactor * astd.INTotalPercentAllowedSubjectToDeductible)
                           )
              END,
               @INIPEffectiveCoinsurance
                   = CASE
                         WHEN SUM(CEC.INIPAllowed
                                  * (1 - CDF.INIPDeductibleFactor * astd.INIPPercentAllowedSubjectToDeductible)
                                 ) = 0 THEN
                             0
                         ELSE
                             SUM(CEC.INIPEffectiveCoinsurance * CEC.INIPAllowed
                                 * (1 - CDF.INIPDeductibleFactor * SPBD.INDedApplies)
                                )
                             / SUM(CEC.INIPAllowed
                                   * (1 - CDF.INIPDeductibleFactor * astd.INIPPercentAllowedSubjectToDeductible)
                                  )
                     END,
               @INSNFEffectiveCoinsurance
                   = CASE
                         WHEN SUM(CEC.INSNFAllowed
                                  * (1 - CDF.INSNFDeductibleFactor * astd.INSNFPercentAllowedSubjectToDeductible)
                                 ) = 0 THEN
                             0
                         ELSE
                             SUM(CEC.INSNFEffectiveCoinsurance * CEC.INSNFAllowed
                                 * (1 - CDF.INSNFDeductibleFactor * SPBD.INDedApplies)
                                )
                             / SUM(CEC.INSNFAllowed
                                   * (1 - CDF.INSNFDeductibleFactor * astd.INSNFPercentAllowedSubjectToDeductible)
                                  )
                     END,
               @INOtherEffectiveCoinsurance
                   = CASE
                         WHEN SUM(CEC.INOtherAllowed
                                  * (1 - CDF.INOtherDeductibleFactor * astd.INOtherPercentAllowedSubjectToDeductible)
                                 ) = 0 THEN
                             0
                         ELSE
                             SUM(CEC.INOtherEffectiveCoinsurance * CEC.INOtherAllowed
                                 * (1 - CDF.INOtherDeductibleFactor * SPBD.INDedApplies)
                                )
                             / SUM(CEC.INOtherAllowed
                                   * (1 - CDF.INOtherDeductibleFactor * astd.INOtherPercentAllowedSubjectToDeductible)
                                  )
                     END,
               @OONEffectiveCoinsurance
                   = CASE
                         WHEN SUM(CEC.OONAllowed
                                  * (1 - CDF.OONTotalDeductibleFactor * astd.OONTotalPercentAllowedSubjectToDeductible)
                                 ) = 0 THEN
                             0
                         WHEN @HasOON = 1 THEN
                             SUM(CEC.OONEffectiveCoinsurance * CEC.OONAllowed
                                 * (1 - CDF.OONTotalDeductibleFactor * SPBD.OONDedApplies)
                                )
                             / SUM(CEC.OONAllowed
                                   * (1 - CDF.OONTotalDeductibleFactor * astd.OONTotalPercentAllowedSubjectToDeductible)
                                  )
                         ELSE
                             0
                     END,
               @OONIPEffectiveCoinsurance
                   = CASE
                         WHEN SUM(CEC.OONIPAllowed
                                  * (1 - CDF.OONIPDeductibleFactor * astd.OONIPPercentAllowedSubjectToDeductible)
                                 ) = 0 THEN
                             0
                         WHEN @HasOON = 1 THEN
                             SUM(CEC.OONIPEffectiveCoinsurance * CEC.OONIPAllowed
                                 * (1 - CDF.OONIPDeductibleFactor * SPBD.OONDedApplies)
                                )
                             / SUM(CEC.OONIPAllowed
                                   * (1 - CDF.OONIPDeductibleFactor * astd.OONIPPercentAllowedSubjectToDeductible)
                                  )
                         ELSE
                             0
                     END,
               @OONSNFEffectiveCoinsurance
                   = CASE
                         WHEN SUM(CEC.OONSNFAllowed
                                  * (1 - CDF.OONSNFDeductibleFactor * astd.OONSNFPercentAllowedSubjectToDeductible)
                                 ) = 0 THEN
                             0
                         WHEN @HasOON = 1 THEN
                             SUM(CEC.OONSNFEffectiveCoinsurance * CEC.OONSNFAllowed
                                 * (1 - CDF.OONSNFDeductibleFactor * SPBD.OONDedApplies)
                                )
                             / SUM(CEC.OONSNFAllowed
                                   * (1 - CDF.OONSNFDeductibleFactor * astd.OONSNFPercentAllowedSubjectToDeductible)
                                  )
                         ELSE
                             0
                     END,
               @OONOtherEffectiveCoinsurance
                   = CASE
                         WHEN SUM(CEC.OONOtherAllowed
                                  * (1 - CDF.OONOtherDeductibleFactor * astd.OONOtherPercentAllowedSubjectToDeductible)
                                 ) = 0 THEN
                             0
                         WHEN @HasOON = 1 THEN
                             SUM(CEC.OONOtherEffectiveCoinsurance * CEC.OONOtherAllowed
                                 * (1 - CDF.OONOtherDeductibleFactor * SPBD.OONDedApplies)
                                )
                             / SUM(CEC.OONOtherAllowed
                                   * (1 - CDF.OONOtherDeductibleFactor * astd.OONOtherPercentAllowedSubjectToDeductible)
                                  )
                         ELSE
                             0
                     END
        FROM #tempCalcEffectiveCoinsurance CEC
            INNER JOIN dbo.CalcDeductibleFactors CDF WITH (NOLOCK)
                ON CDF.ForecastID = CEC.ForecastID
                   AND CDF.IsBenefitYearCurrentYear = CEC.IsBenefitYearCurrentYear
            INNER JOIN dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
                ON SPBD.ForecastID = CEC.ForecastID
                   AND SPBD.IsBenefitYearCurrentYear = CEC.IsBenefitYearCurrentYear
                   AND SPBD.BenefitCategoryID = CEC.BenefitCategoryID
            LEFT JOIN #tempAllowedSubjectToDeductible astd
                ON astd.ForecastID = CEC.ForecastID
        WHERE CEC.ForecastID = @XForecastID
              AND CEC.IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear
              AND SPBD.IsLiveIndex = (CASE
                                          WHEN @XIsBenefitYearCurrentYear = 0 THEN
                                              1
                                          ELSE
                                              0
                                      END
                                     )
              AND SPBD.BenefitOrdinalID = 1; --Needed to eliminate double counting, if deductible exclusions ever varied by BenefitOrdinalID within a Benefit Category we would need torework this

        --Calculate Initial MOOP Impact as difference between Cost Share* with and without MOOP
        DROP TABLE IF EXISTS #INCalcMOOPInitialImpact;
        CREATE TABLE #INCalcMOOPInitialImpact
        (
            PlanYearID INT,
            IsBenefitYearCurrentYear INT,
            IsInNetwork INT,
            MOOPBucketID INT,
            Members INT,
            [Distribution] DECIMAL(38, 20),
            PreMOOPCostShare DECIMAL(38, 20),
            IPPreMOOPCostShare DECIMAL(38, 20),
            SNFPreMOOPCostShare DECIMAL(38, 20),
            OtherPreMOOPCostShare DECIMAL(38, 20),
            PostMOOPCostShare DECIMAL(38, 20),
            MOOPImpact DECIMAL(38, 20)
        );
        INSERT INTO #INCalcMOOPInitialImpact
        (
            PlanYearID,
            IsBenefitYearCurrentYear,
            IsInNetwork,
            MOOPBucketID,
            Members,
            Distribution,
            PreMOOPCostShare,
            IPPreMOOPCostShare,
            SNFPreMOOPCostShare,
            OtherPreMOOPCostShare,
            PostMOOPCostShare,
            MOOPImpact
        )
        SELECT @PlanYearID AS PlanYearID,
               ac.IsBenefitYearCurrentYear,
               ac.IsInNetwork,
               ac.MOOPBucketID,
               ac.Members,
               ac.[Distribution],
               PreMOOPCostShare = SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed) * @INIPEffectiveCoinsurance)
                                  + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed) * @INSNFEffectiveCoinsurance)
                                  + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                        * @INOtherEffectiveCoinsurance
                                       ),
               IPPreMOOPCostShare = SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed) * @INIPEffectiveCoinsurance),
               SNFPreMOOPCostShare = SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed)
                                         * @INSNFEffectiveCoinsurance
                                        ),
               OtherPreMOOPCostShare = SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                           * @INOtherEffectiveCoinsurance
                                          ),
               PostMOOPCostShare = dbo.fnMIN(
                                                SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed)
                                                    * @INIPEffectiveCoinsurance
                                                   )
                                                + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed)
                                                      * @INSNFEffectiveCoinsurance
                                                     )
                                                + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                                      * @INOtherEffectiveCoinsurance
                                                     ),
                                                dbo.fnMIN(@INMOOP, @CombinedMOOP) - @INDeductible
                                            ),
               MOOPImpact = (SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed) * @INIPEffectiveCoinsurance)
                             + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed) * @INSNFEffectiveCoinsurance)
                             + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                   * @INOtherEffectiveCoinsurance
                                  )
                            )
                            - dbo.fnMIN(
                                           SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed)
                                               * @INIPEffectiveCoinsurance
                                              )
                                           + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed)
                                                 * @INSNFEffectiveCoinsurance
                                                )
                                           + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                                 * @INOtherEffectiveCoinsurance
                                                ),
                                           dbo.fnMIN(@INMOOP, @CombinedMOOP) - @INDeductible
                                       )
        FROM #CalcAdjustedContinuance ac
        WHERE ac.IsInNetwork = 1
        GROUP BY ac.IsBenefitYearCurrentYear,
                 ac.IsInNetwork,
                 ac.MOOPBucketID,
                 ac.Members,
                 ac.[Distribution];

        -- Add on the equivalent Out of Network part
        DROP TABLE IF EXISTS #OONCalcMOOPInitialImpact;
        CREATE TABLE #OONCalcMOOPInitialImpact
        (
            PlanYearID INT,
            IsBenefitYearCurrentYear INT,
            IsInNetwork INT,
            MOOPBucketID INT,
            Members INT,
            [Distribution] DECIMAL(38, 20),
            PreMOOPCostShare DECIMAL(38, 20),
            IPPreMOOPCostShare DECIMAL(38, 20),
            SNFPreMOOPCostShare DECIMAL(38, 20),
            OtherPreMOOPCostShare DECIMAL(38, 20),
            PostMOOPCostShare DECIMAL(38, 20),
            MOOPImpact DECIMAL(38, 20)
        );
        INSERT INTO #OONCalcMOOPInitialImpact
        (
            PlanYearID,
            IsBenefitYearCurrentYear,
            IsInNetwork,
            MOOPBucketID,
            Members,
            Distribution,
            PreMOOPCostShare,
            IPPreMOOPCostShare,
            SNFPreMOOPCostShare,
            OtherPreMOOPCostShare,
            PostMOOPCostShare,
            MOOPImpact
        )
        SELECT @PlanYearID AS PlanYearID,
               ac.IsBenefitYearCurrentYear,
               ac.IsInNetwork,
               ac.MOOPBucketID,
               ac.Members,
               ac.[Distribution],
               PreMOOPCostShare = SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed) * @OONIPEffectiveCoinsurance)
                                  + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed)
                                        * @OONSNFEffectiveCoinsurance
                                       )
                                  + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                        * @OONOtherEffectiveCoinsurance
                                       ),
               IPPreMOOPCostShare = SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed) * @OONIPEffectiveCoinsurance),
               SNFPreMOOPCostShare = SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed)
                                         * @OONSNFEffectiveCoinsurance
                                        ),
               OtherPreMOOPCostShare = SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                           * @OONOtherEffectiveCoinsurance
                                          ),
               PostMOOPCostShare = dbo.fnMIN(
                                                SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed)
                                                    * @OONIPEffectiveCoinsurance
                                                   )
                                                + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed)
                                                      * @OONSNFEffectiveCoinsurance
                                                     )
                                                + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                                      * @OONOtherEffectiveCoinsurance
                                                     ),
                                                dbo.fnMIN(@OONMOOP, @CombinedMOOP) - @OONDeductible
                                            ),
               MOOPImpact = (SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed) * @OONIPEffectiveCoinsurance)
                             + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed) * @OONSNFEffectiveCoinsurance)
                             + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                   * @OONOtherEffectiveCoinsurance
                                  )
                            )
                            - dbo.fnMIN(
                                           SUM((ac.IPAdjAvgAllowed - ac.IPMinDeductibleAllowed)
                                               * @OONIPEffectiveCoinsurance
                                              )
                                           + SUM((ac.SNFAdjAvgAllowed - ac.SNFMinDeductibleAllowed)
                                                 * @OONSNFEffectiveCoinsurance
                                                )
                                           + SUM((ac.OtherAdjAvgAllowed - ac.OtherMinDeductibleAllowed)
                                                 * @OONOtherEffectiveCoinsurance
                                                ),
                                           dbo.fnMIN(@OONMOOP, @CombinedMOOP) - @OONDeductible
                                       )
        FROM #CalcAdjustedContinuance ac
        WHERE ac.IsInNetwork = 0
        GROUP BY ac.IsBenefitYearCurrentYear,
                 ac.IsInNetwork,
                 ac.MOOPBucketID,
                 ac.Members,
                 ac.[Distribution];

        --Split up MOOP Impact by MOOPCategoryID and combine IN/OON together
        DROP TABLE IF EXISTS #CalcMOOPInitialImpact;
        CREATE TABLE #CalcMOOPInitialImpact
        (
            PlanYearID INT,
            IsBenefitYearCurrentYear INT,
            IsInNetwork INT,
            MOOPBucketID INT,
            Members INT,
            [Distribution] DECIMAL(38, 20),
            PreMOOPCostShare DECIMAL(38, 20),
            IPPreMOOPCostShare DECIMAL(38, 20),
            SNFPreMOOPCostShare DECIMAL(38, 20),
            OtherPreMOOPCostShare DECIMAL(38, 20),
            PostMOOPCostShare DECIMAL(38, 20),
            MOOPImpact DECIMAL(38, 20),
            IPMOOPImpact DECIMAL(38, 20),
            SNFMOOPImpact DECIMAL(38, 20),
            OtherMOOPImpact DECIMAL(38, 20)
        );
        INSERT INTO #CalcMOOPInitialImpact
        (
            PlanYearID,
            IsBenefitYearCurrentYear,
            IsInNetwork,
            MOOPBucketID,
            Members,
            Distribution,
            PreMOOPCostShare,
            IPPreMOOPCostShare,
            SNFPreMOOPCostShare,
            OtherPreMOOPCostShare,
            PostMOOPCostShare,
            MOOPImpact,
            IPMOOPImpact,
            SNFMOOPImpact,
            OtherMOOPImpact
        )
        SELECT PlanYearID,
               IsBenefitYearCurrentYear,
               IsInNetwork,
               MOOPBucketID,
               Members,
               [Distribution],
               PreMOOPCostShare,
               IPPreMOOPCostShare,
               SNFPreMOOPCostShare,
               OtherPreMOOPCostShare,
               PostMOOPCostShare,
               MOOPImpact,
               IPMOOPImpact = IIF(PreMOOPCostShare = 0, 0, MOOPImpact * IPPreMOOPCostShare / PreMOOPCostShare),
               SNFMOOPImpact = IIF(PreMOOPCostShare = 0, 0, MOOPImpact * SNFPreMOOPCostShare / PreMOOPCostShare),
               OtherMOOPImpact = IIF(PreMOOPCostShare = 0, 0, MOOPImpact * OtherPreMOOPCostShare / PreMOOPCostShare)
        FROM #INCalcMOOPInitialImpact
        UNION ALL

        -- Add on the equivalent Out of Network part
        SELECT PlanYearID,
               IsBenefitYearCurrentYear,
               IsInNetwork,
               MOOPBucketID,
               Members,
               [Distribution],
               PreMOOPCostShare,
               IPPreMOOPCostShare,
               SNFPreMOOPCostShare,
               OtherPreMOOPCostShare,
               PostMOOPCostShare,
               MOOPImpact,
               IPMOOPImpact = IIF(PreMOOPCostShare = 0, 0, MOOPImpact * IPPreMOOPCostShare / PreMOOPCostShare),
               SNFMOOPImpact = IIF(PreMOOPCostShare = 0, 0, MOOPImpact * SNFPreMOOPCostShare / PreMOOPCostShare),
               OtherMOOPImpact = IIF(PreMOOPCostShare = 0, 0, MOOPImpact * OtherPreMOOPCostShare / PreMOOPCostShare)
        FROM #OONCalcMOOPInitialImpact;

        -- Calculate Initial MOOP Factors before the Combined MOOP Factor Load
        DECLARE @INMOOPFactorInitial DECIMAL(20, 18),
                @INIPMOOPFactorInitial DECIMAL(20, 18),
                @INSNFMOOPFactorInitial DECIMAL(20, 18),
                @INOtherMOOPFactorInitial DECIMAL(20, 18),
                @OONMOOPFactorInitial DECIMAL(20, 18),
                @OONIPMOOPFactorInitial DECIMAL(20, 18),
                @OONSNFMOOPFactorInitial DECIMAL(20, 18),
                @OONOtherMOOPFactorInitial DECIMAL(20, 18);

        SELECT @INMOOPFactorInitial
            = dbo.fnGetSafeDivisionResult(
                                             SUM(MI.MOOPImpact * [Distribution]),
                                             SUM(MI.PreMOOPCostShare * [Distribution])
                                         ),
               @INIPMOOPFactorInitial
                   = dbo.fnGetSafeDivisionResult(
                                                    SUM(MI.IPMOOPImpact * [Distribution]),
                                                    SUM(MI.IPPreMOOPCostShare * [Distribution])
                                                ),
               @INSNFMOOPFactorInitial
                   = dbo.fnGetSafeDivisionResult(
                                                    SUM(MI.SNFMOOPImpact * [Distribution]),
                                                    SUM(MI.SNFPreMOOPCostShare * [Distribution])
                                                ),
               @INOtherMOOPFactorInitial
                   = dbo.fnGetSafeDivisionResult(
                                                    SUM(MI.OtherMOOPImpact * [Distribution]),
                                                    SUM(MI.OtherPreMOOPCostShare * [Distribution])
                                                )
        FROM #CalcMOOPInitialImpact MI
        WHERE MI.IsInNetwork = 1;

        SELECT @OONMOOPFactorInitial
            = dbo.fnGetSafeDivisionResult(
                                             SUM(MI.MOOPImpact * [Distribution]),
                                             SUM(MI.PreMOOPCostShare * [Distribution])
                                         ),
               @OONIPMOOPFactorInitial
                   = dbo.fnGetSafeDivisionResult(
                                                    SUM(MI.IPMOOPImpact * [Distribution]),
                                                    SUM(MI.IPPreMOOPCostShare * [Distribution])
                                                ),
               @OONSNFMOOPFactorInitial
                   = dbo.fnGetSafeDivisionResult(
                                                    SUM(MI.SNFMOOPImpact * [Distribution]),
                                                    SUM(MI.SNFPreMOOPCostShare * [Distribution])
                                                ),
               @OONOtherMOOPFactorInitial
                   = dbo.fnGetSafeDivisionResult(
                                                    SUM(MI.OtherMOOPImpact * [Distribution]),
                                                    SUM(MI.OtherPreMOOPCostShare * [Distribution])
                                                )
        FROM #CalcMOOPInitialImpact MI
        WHERE MI.IsInNetwork = 0;

        -- Determine whether to add load for Combined MOOP

        DECLARE @INNetworkPreMOOPCostShare DECIMAL(32, 15),
                @OONNetworkPreMOOPCostShare DECIMAL(32, 15),
                @INPercentageCostShare DECIMAL(20, 12),
                @OONPercentageCostShare DECIMAL(20, 12),
                @CombinedMOOPFactorLoad DECIMAL(4, 2),
                @CombinedINMOOPFactorLoad DECIMAL(4, 2),
                @CombinedOONMOOPFactorLoad DECIMAL(4, 2);

        SET @CombinedMOOPFactorLoad = 0.1;

        SELECT @INNetworkPreMOOPCostShare = SUM(BP.INNetworkAllowed * @INEffectiveCoinsurance),
               @OONNetworkPreMOOPCostShare = SUM(BP.OONNetworkAllowed * @OONEffectiveCoinsurance)
        FROM dbo.CalcBenefitProjectionPreMbrCS BP WITH (NOLOCK)
            INNER JOIN dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
                ON BP.BenefitCategoryID = BC.BenefitCategoryID
                   AND BC.IsEnabled = 1
                   AND BC.IsUsed = 1
        WHERE BP.ForecastID = @XForecastID
              AND BP.DualEligibleTypeID = 0 --NonDual
              AND BP.MARatingOptionID = 3 --Blended
              AND BP.IsIncludeInCostShareBasis = 1; --Included in cost share basis

        SET @INPercentageCostShare
            = dbo.fnGetSafeDivisionResult(
                                             @INNetworkPreMOOPCostShare,
                                             (@INNetworkPreMOOPCostShare + @OONNetworkPreMOOPCostShare)
                                         );
        SET @OONPercentageCostShare = 1 - @INPercentageCostShare;

        IF @CombinedMOOP * @INPercentageCostShare < @INMOOP
        BEGIN
            SET @CombinedINMOOPFactorLoad = @CombinedMOOPFactorLoad;
        END;
        ELSE
        BEGIN
            SET @CombinedINMOOPFactorLoad = 0;
        END;

        IF @CombinedMOOP * @OONPercentageCostShare < @OONMOOP
        BEGIN
            SET @CombinedOONMOOPFactorLoad = @CombinedMOOPFactorLoad;
        END;
        ELSE
        BEGIN
            SET @CombinedOONMOOPFactorLoad = 0;
        END;

        --==========================================================================================================================--
        ----------------------------------------   Begin Output piece    -------------------------------------------------------------
        --==========================================================================================================================--
        DELETE FROM dbo.CalcMOOPFactors
        WHERE ForecastID = @XForecastID
              AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear;

        INSERT INTO dbo.CalcMOOPFactors
        (
            PlanYearID,
            ForecastID,
            IsBenefitYearCurrentYear,
            INMOOPFactor,
            INIPMOOPFactor,
            INSNFMOOPFactor,
            INOtherMOOPFactor,
            OONMOOPFactor,
            OONIPMOOPFactor,
            OONSNFMOOPFactor,
            OONOtherMOOPFactor,
            LastUpdateByID,
            LastUpdateDateTime
        )
        SELECT @PlanYearID AS PlanYearID,
               @XForecastID AS ForecastID,
               @XIsBenefitYearCurrentYear AS IsBenefitYearCurrentYear,
               INMOOPFactor = 1 - (@INMOOPFactorInitial) * (1 + @CombinedINMOOPFactorLoad),
               INIPMOOPFactor = 1 - (@INIPMOOPFactorInitial) * (1 + @CombinedINMOOPFactorLoad),
               INSNFMOOPFactor = 1 - (@INSNFMOOPFactorInitial) * (1 + @CombinedINMOOPFactorLoad),
               INOtherMOOPFactor = 1 - (@INOtherMOOPFactorInitial) * (1 + @CombinedINMOOPFactorLoad),
               OONMOOPFactor = 1 - (@OONMOOPFactorInitial) * (1 + @CombinedOONMOOPFactorLoad),
               OONIPMOOPFactor = 1 - (@OONIPMOOPFactorInitial) * (1 + @CombinedOONMOOPFactorLoad),
               OONSNFMOOPFactor = 1 - (@OONSNFMOOPFactorInitial) * (1 + @CombinedOONMOOPFactorLoad),
               OONOtherMOOPFactor = 1 - (@OONOtherMOOPFactorInitial) * (1 + @CombinedOONMOOPFactorLoad),
               LastUpdateByID = @XUserID,
               LastUpdateDateTime = @LastDateTime;


        COMMIT TRANSACTION transaction_spCMF;

    END TRY
    BEGIN CATCH

        IF (@@TranCount > @tranCount) --Check if transaction in TRY block was not closed
        BEGIN
            ROLLBACK TRANSACTION transaction_spCMF;

        END;
    END CATCH;

END;