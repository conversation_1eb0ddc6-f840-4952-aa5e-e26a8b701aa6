SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnPrePremium
--
-- CREATOR: Christian Cofie
--
-- CREATED DATE: 2007-Feb-03
-- HEADERUPDATED: 2010-Sep-29
--
-- DESCRIPTION: function responsible for listing Preliminary Benchmarks 
--
-- PARAMETERS: 
--  Input: 
--      @ForecastID
--  Output: 
--      Table
--
-- USED BY: This function is used as part of building the Pre Premium in spFinalPremium
--
-- TABLES:
--  Read:
--      SavedPlanHeader
--      SavedPlanDetail
--      LkpProductType
--      PerExtCMSValues
--      CalcBenchmarkSummary
--      SavedMarketHeader
--      dbo.fnGetRebate<PERSON><PERSON><PERSON>(@ForecastID)
--		fnGetCMSMARegionRate(@ForecastID)
--  Write:
--      NONE
--
-- VIEWS: Read: NONE
--
-- FUNCTIONS:
--  fnTotalMedical
--		fnAppGetPlanExpenseProfit
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE        VERSION  CHANGES MADE                                                                DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007-Feb-03       1  Initial Version.                                                            Christian Cofie
-- 2008-Feb-27       2  Coding Standards.                                                           Brian Lake
-- 2008-Feb-28       3  Added null for @StartVersion param in                                       Brian Lake
--                      fnGetPlanExpenseProfit
-- 2008-Apr-15       4  Added @PlanVersion, required for call to                                    Brian Lake
--                      spa.
-- 2008-Apr-21       5  Replaced fnGetPlanExpenseProfit with                                        Brian Lake
--                      fnAppGetPlanExpenseProfit.
-- 2008-Apr-30       6  Added spd.MARatingOptionID = 1 to join.										Brian Lake
-- 2008-May-04       7  Forced conversion to decimal(x.y) on some values.                           Sandy Ellis
-- 2008-May-04       8  Updated Expense Calculation.                                                Sandy Ellis
-- 2008-May-06       9  Updated PreReqRev Calc.                                                     Sandy Ellis
-- 2008-May-06      10  Removed Userfee from the calculation of 			                        Christian Cofie
--                      PreStandardizedBenchmark.
-- 2008-Sep-25      11  Added @UserID to the list of parameters.                                    Shannon Boykin
-- 2009-Jan-13      12  Removed join on fnAppGetRxPremium, which is now                             Tonya Cockrell
--                      obsolete.
-- 2009-Feb-23      13  Removed references to CalcPlanCountySummary and                             Brian Lake
--                          replaced with CalcBenchmarkSummary
-- 2009-Mar-17      14  Data types                                                                  Sandy Ellis
-- 2009-Apr-01      15  Using PlanTypeID instead of PlanTypeName, formatting                        Sandy Ellis
-- 2009-Apr-30      16  PMPM Expenses - Split methodology for 2010+ vs prior						Keith Galloway
-- 2010-Jan-19		17	Updated for 2010 requirements												Jake Gaecke
-- 2010-Feb-12		18	Added a PlanYearID join to LkpIntMARegionMarketDetail for 2011				Joe Casey
-- 2010-Jul-23		19	Revised for move to 2012 database and for coding standards					Michael Siekerka
-- 2010-Jul-26      20  Replaced Start and End indexes with ForecastID,                              Jake Gaecke
--                      replaced fnAppGetPlanExpenseProfit and fnAppGetRxPremiumSaved
--                      with SavedPlanAssumptions
-- 2010-Sep-28      21  Removed PlanVersion                                                         Michael Siekerka
-- 2010-Sep-29      22  Revised for fnGetRebatePercent and coding standards                         Jake Gaecke
--                      also renamed LkpExtCMSValues to PerExtCMSValues
-- 2010-Sep-29      23  Renamed LkpIntMarketHeader to SavedMarketHeader                             Michael Siekerka
-- 2010-Sep-29      24  Updated SecondaryPayerAdjustment location                                   Nate Jacoby
-- 2010-Oct-04      25  Changed reference to RRegionID to MARegionID                                Michael Siekerka
-- 2010-Oct-06      26  Changed reference from LkpExtCMSMARegionRatebook to PerExtCMSRatebookRPPO   Jake Gaecke
-- 2010-Dec-20		20	Changed reference from PerExtCMSRatebookRPPO to fnGetCMSMARegionRate		Jiao Chen
-- 2011-FEB-02		21	INNER joins SavedPlanAssumptions instead of left join						Jiao Chen
--						fnGetCMSMARegionRate(@ForecastID) joins directly instead of SELECT * From
-- 2011-Feb-04      22  Added rounding to PlanRiskFactor within the ConversionFactor Calculation    Michael Siekerka
--                          to fix discrepancies between BPT and Model output
-- 2011-Feb-08		23	Added fnAppGetPlanExpenseProfit to get new ExpensePMPM location			    Joe Casey
-- 2012-Aug-28		24	Added a case statement to calculate CostshareReductionPct for Group 		Tim Gao
--						plans if there is a profit adjustment
-- 2012-Dec-6		25	Comment the above adjustment												Tim Gao
-- 2013-Feb-24		26  Added Case Statement for CS Reduction if UnspentRebate = 0					Trevor Mahoney
-- 2013-Feb-28		27	0379 - Added in calculation for InsurerFee									Mason Roberts
-- 2013-Mar-01		28	Changed Insurer fee to Decimal(8,6)											Mike Deren
-- 2013-Mar-09		29	Added related party Medical expense and profit								Tim Gao
--						to corresponding computation
-- 2013-Mar-13		30	Changed Insurer Fee calculation methodology									Mason Roberts
-- 2013-Mar-13		31	Adjusted related party admin and profit calculations						Trevor Mahoney
-- 2013-Mar-14      32  Added RPAdmin to Profit, removed subtraction of RPProfit from PRR           Lindsay Allen
-- 2013-Mar-18		33	Add RPProfit back in to required revenue calculation						Trevor Mahoney
-- 2013-Jul-29      34  Locking Admin for Insurer Fees                                              Mike Deren
-- 2013-Jul-30		35  Added insurer fees in calculation for PreReqRev and PreStandBM				Trevor Mahoney
-- 2016-Nov-29		36	Updated @locked to pull from SavedPlanHeader, updated @InsurerFee			Chris Fleming
-- 2017-Jun-20		37	Removed PerIntRelatedPartiesMedProjected									Crystal Quirino
-- 2018-May-10		38	Changing LkpExtCMSPlanType to LkpProductType								Jordan Purdue
-- 2018-Oct-30		39	Removing LkpIntMARegionDetail												Jordan Purdue
-- 2020-Sep-08		40  Fixed sonarQube Fixes														Deepali	
-- 2024-Mar-29      41  Adding nolock for the tables                                                Chaitanya Durga 
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnPrePremium] 
(
    @ForecastID INT
)
RETURNS @Results TABLE
(
	ForecastID INT,
    MedicareCoveredPct DECIMAL(20,12),
    Expenses DECIMAL(20,12),
    Profit DECIMAL(20,12),
    Reins DECIMAL(20,12), 
    UserFee DECIMAL(20,12),
    ConversionFactor DECIMAL(20,12),
    PreReqRev DECIMAL(20,12), 
    PrePlanBid DECIMAL(20,12),
    PreStandardizedBid DECIMAL(20,12),
    PreStandardizedBenchmark DECIMAL(20,12),
    PrePlanBenchmark DECIMAL(20,12),
    RxBasicPremium DECIMAL(20,12),
    RxSuppPremium DECIMAL(20,12),
    PreRebate DECIMAL(20,12),
    PreBasicMemberPremium DECIMAL(20,12),
    PrePlanPremium DECIMAL(20,12),
    AddedBenefitPct DECIMAL(20,12),
    CostShareReductionPct DECIMAL(20,12),
    NetNonESRD DECIMAL(20,12), 
    Allowed DECIMAL(20,12),
	ProfitFactor DECIMAL(20,12),
	InsurerFee DECIMAL (8,6),
	InsurerFeePercent DECIMAL (8,6)
)

AS
BEGIN

    DECLARE @locked INT 
    SET @locked = (SELECT IsLocked FROM SavedPlanHeader  WITH(NOLOCK)  WHERE ForecastID = @ForecastID) 

    DECLARE @InsurerFeePercent DECIMAL (8,6) 
	SET  @InsurerFeePercent = CASE WHEN @locked = 1 THEN 0 
								ELSE (SELECT InsurerFeePercent FROM dbo.PerExtCMSValues WITH(NOLOCK))
								END
								
    DECLARE @InsurerFee DECIMAL(8,6)
	SET @InsurerFee = CASE WHEN @locked = 1 THEN (SELECT InsurerFee FROM CalcFinalPremium  WITH(NOLOCK) WHERE ForecastID = @ForecastID)
						   ELSE 0
					  END

    INSERT @Results
	SELECT 
		ForecastID, 
		MedicareCoveredPct,
		Expenses,
		Profit,
		Reins, 
		UserFee,
		ConversionFactor,
		PreReqRev, 
		PrePlanBid,
		PreStandardizedBid,
		PreStandardizedBenchmark,
		PrePlanBenchmark,
		RxBasicPremium,
		RxSuppPremium,
		PreRebate,
		PreBasicMemberPremium,
		PrePlanPremium=
			(ROUND(
				CASE 
					WHEN PreBasicMemberPremium>0 
						THEN (PreBasicMemberPremium 
							+ ((1- MedicareCoveredPct) * PreReqRev)
							+ ISNULL(RxBasicPremium,0)
							+ ISNULL(RxSuppPremium,0))
						ELSE (((1 - MedicareCoveredPct) * PreReqRev)
							+ ISNULL(RxBasicPremium,0)
							+ ISNULL(RxSuppPremium,0)
							- PreRebate) 
				END,2)), 
		AddedBenefitPct,
		CostShareReductionPct,
		NetNonESRD, 
		Allowed,
		ProfitFactor = CASE WHEN PreReqRev <> 0 THEN (Profit/PreReqRev) ELSE 0 END,
		InsurerFee,
		InsurerFeePercent
	FROM -- Begin T2
	(
		SELECT --Start of TempTable2
			ForecastID,  
			MedicareCoveredPct,
			Expenses,
			Profit,
			Reins, 
			UserFee,
			ConversionFactor,
			PreReqRev, --Fix
			PrePlanBid =(MedicareCoveredPct*PreReqRev),
			PreStandardizedBid = dbo.fnGetSafeDivisionResult(MedicareCoveredPct*PreReqRev,ConversionFactor), 
			PreStandardizedBenchmark,
			PrePlanBenchmark =(PreStandardizedBenchmark*ConversionFactor), 
			RxBasicPremium,
			RxSuppPremium,
			PreRebate=
				(ROUND(RebatePercent * (
					CASE WHEN (PreStandardizedBenchmark*ConversionFactor)<=(MedicareCoveredPct*PreReqRev) 
						THEN 0 
						ELSE ROUND(((PreStandardizedBenchmark*ConversionFactor)-(MedicareCoveredPct*PreReqRev)),2) 
					END),2)),
			PreBasicMemberPremium=
				(ROUND(
					CASE WHEN (PreStandardizedBenchmark*ConversionFactor)>=(MedicareCoveredPct*PreReqRev) 
						THEN 0 
						ELSE (dbo.fnGetSafeDivisionResult(MedicareCoveredPct*PreReqRev,ConversionFactor)-PreStandardizedBenchmark) 
					END,2)) ,
			AddedBenefitPct,
			CostShareReductionPct,
			NetNonESRD, 
			Allowed,
			InsurerFee,
			InsurerFeePercent
		FROM  --Start of T1
		(
			SELECT
				TotMed.ForecastID,
				MedicareCoveredPct = (TotMed.CoveredActEquivNetnonESRD/TotMed.NetnonESRD), 
				Expenses = ae.ExpensePMPM 
										+ ((TotMed.NetNonESRD + ae.ExpensePMPM + pcv.UserFee + @InsurerFee)  --Add in Related Party Admin
										* @InsurerFeePercent / (1 - @InsurerFeePercent - spa.ProfitPercent)),-- This portion the new PreReqRev 0379, 
				Profit = (TotMed.NetNonESRD + @InsurerFee + ae.ExpensePMPM + pcv.UserFee) * spa.ProfitPercent
								/ (1 - spa.ProfitPercent - @InsurerFeePercent), -- This portion is the new PreReqRev 0379
				Reins = 0,
				UserFee = pcv.UserFee,
				ConversionFactor = ((1-ISNULL(spa.SecondaryPayerAdjustment,0)) * ROUND(bms.PlanRiskFactor,6)),
				PreReqRev =(TotMed.NetNonESRD + ae.ExpensePMPM + pcv.UserFee + @InsurerFee)
									/ (1 - spa.ProfitPercent - @InsurerFeePercent), -- This portion is the new PreReqRev 0379
				PreStandardizedBenchmark =
					CASE WHEN cmspt.ProductTypeID = 3 -- RPPO
						THEN (mar.MARegionRate * pcv.StatCompPercent
							+ (CASE WHEN (EstimatedPlanBidComponent IS NULL) THEN
										(TotMed.NetNonESRD + ae.ExpensePMPM + pcv.UserFee + @InsurerFee)
										/ (1 - spa.ProfitPercent - @InsurerFeePercent) -- This portion is the new PreReqRev 0379
										* (TotMed.CoveredActEquivNetnonESRD / TotMed.NetnonESRD)
										/ (1 - ISNULL(spa.SecondaryPayerAdjustment,0))
										* bms.PlanRiskFactor -- Conversion factor
										* (1 - pcv.StatCompPercent) -- Estimated standardized bid                                 
									ELSE EstimatedPlanBidComponent * (1 - pcv.StatCompPercent)
								END) -- Plan bid component
							  )  -- Standardized A/B benchmark 
						ELSE bms.PlanAverageRiskRate
					END,
				RxBasicPremium = CASE WHEN sph.IsMAPD = 1 THEN ISNULL(spa.RxBasicPremium,0) ELSE 0 END, 
				RxSuppPremium = CASE WHEN sph.IsMAPD = 1 THEN ISNULL(spa.RxSuppPremium,0) ELSE 0 END, 
				AddedBenefitPct = TotMed.AddedBenefitNet / TotMed.NetnonESRD, 
				CostShareReductionPct =TotMed.CostShareReduction / (TotMed.NetnonESRD) ,
														
				--= CASE-- added to deal with group plan profit adjustment if EGWPMSB is not zero
				--							WHEN sph.IsEGWP = 1 THEN 
				--									CASE WHEN (SELECT EGWPMSB FROM fnGetUnspentRebateForEGWPMSB(@ForecastID))> 0 THEN 
				--										TotMed.CostShareReduction / (TotMed.NetnonESRD) 
				--										ELSE TotMed.CostShareReduction / (TotMed.NetnonESRD) ---Originally left out case if UnspentRebate = 0 (Trevor, 2/24/2013)
				--											---+ (SELECT ProfitAdjustment FROM fnGetProfitAdjForGroup(@ForecastID)))
				--									END
				--							ELSE TotMed.CostShareReduction / TotMed.NetnonESRD
				--						END, 

										 
											--ELSE TotMed.CostShareReduction / TotMed.NetnonESRD
										--END, 
				RebatePercent = dbo.fnGetRebatePercent(@ForecastID),
				TotMed.NetNonESRD, 
				TotMed.Allowed,
				InsurerFee = (TotMed.NetNonESRD + ae.ExpensePMPM + pcv.UserFee)
										* @InsurerFeePercent / (1 - @InsurerFeePercent - spa.ProfitPercent),
				[InsurerFeePercent] = @InsurerFeePercent
			FROM fnTotalMedical (@ForecastID) TotMed		
			INNER JOIN SavedPlanHeader sph WITH(NOLOCK)
				ON sph.ForecastID = TotMed.ForecastID
				AND sph.IsHidden = 0
			INNER JOIN SavedPlanAssumptions spa WITH(NOLOCK)
				ON spa.ForecastID = sph.ForecastID
			INNER JOIN fnAppGetPlanExpenseProfit(@ForecastID) ae 
				ON sph.ForecastID = ae.ForecastID
			INNER JOIN SavedPlanDetail spd WITH(NOLOCK)
				ON spd.ForecastID = sph.ForecastID
				AND spd.MARatingOptionID = 1
			INNER JOIN LkpProductType cmspt WITH(NOLOCK)
				ON cmspt.ProductTypeID = sph.PlanTypeID
			LEFT JOIN PerExtCMSValues pcv WITH(NOLOCK)
				ON pcv.IsEnabled = 1
			INNER JOIN CalcBenchmarkSummary bms WITH(NOLOCK)
				ON bms.ForecastID = TotMed.ForecastID
			LEFT JOIN fnGetCMSMARegionRate(@ForecastID) mar 
				ON mar.MARegionID = sph.MARegionID
		) T1   --End of temp table T1
	) T2 --End of temp Table T2
    RETURN
END
GO
