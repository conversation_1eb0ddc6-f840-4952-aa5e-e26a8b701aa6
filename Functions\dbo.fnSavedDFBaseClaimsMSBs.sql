SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- FUNCTION NAME:	fnSavedDFBaseClaimsMSBs
--
-- CREATOR:			<PERSON> Galloway
--
-- CREATED DATE:	2020-AUG-06
--
-- DESCRIPTION:		Function that returns per member per month dollars and annual per member per thousand units
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT,
--					@MARatingOptionID TINYINT
--
--  Output :		@Results TABLE
--
-- TABLES : 
--	Read :			SavedDFClaims
--					SavedDFFinance
--					SavedPlanDFSummary
--					SavedForecastSetup
--					SavedPlanInfo
--					SavedMarketInfo
--					SavedRegionGrouperMapping
--					LkpIntBenefitCategory
--					LkpIntDemogIndicators
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetSafeDivisionResult
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-SEP-21		1		Initial Version															Keith Galloway / Alex Beruscha
-- 2023-AUG-03		2		Added Internal Parameter and NOLOCK										Sheetal Patil
-- 2024-OCT-22		3		Cost Share Basis: handle and return new fields from SavedDFClaims		Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnSavedDFBaseClaimsMSBs]
    (@ForecastID       INT
    ,@MARatingOptionID TINYINT)

RETURNS @Results TABLE
    (MARatingOptionID                             TINYINT        NOT NULL
    ,PlanInfoID                                   INT            NOT NULL
    ,DFVersionID                                  INT            NOT NULL
    ,IncurredYear                                 INT            NOT NULL
    ,DemogIndicator                               TINYINT        NOT NULL
    ,[Provider]                                   VARCHAR(255)   NOT NULL
    ,BenefitCategoryID                            SMALLINT       NOT NULL
    ,BidServiceCatID                              SMALLINT       NOT NULL
    ,Paid                                         DECIMAL(19, 9) NULL
    ,MbrCS                                        DECIMAL(19, 9) NULL
    ,PymtReductionAmt                             DECIMAL(19, 9) NULL
    ,AdmitCnt                                     DECIMAL(19, 9) NULL
    ,UnitCnt                                      DECIMAL(19, 9) NULL
    ,EncounterMbrCS                               DECIMAL(19, 9) NULL
    ,EncounterAdmitCnt                            DECIMAL(19, 9) NULL
    ,EncounterUnitCnt                             DECIMAL(19, 9) NULL
    ,DelegatedEncounterMbrCS                      DECIMAL(19, 9) NULL
    ,DelegatedEncounterAdmitCnt                   DECIMAL(19, 9) NULL
    ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9) NULL
    ,CapDirectPayEPaidClaims                      DECIMAL(19, 9) NULL
    ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9) NULL
    ,CapDirectPayBCAllocated                      DECIMAL(19, 9) NULL
    ,CapDirectPayScenarioAllocated                DECIMAL(19, 9) NULL
    ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9) NULL
    ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9) NULL
    ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9) NULL
    ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9) NULL
    ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9) NULL
    ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9) NULL
    ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9) NULL
    ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9) NULL
    ,CapMSBs                                      DECIMAL(19, 9) NULL
    ,CapProviderRewards                           DECIMAL(19, 9) NULL
    ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9) NULL
    ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9) NULL
    ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9) NULL
    ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9) NULL
    ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9) NULL
    ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9) NULL
    ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9) NULL
    ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9) NULL
    ,PartDCapAdj                                  DECIMAL(19, 9) NULL
    ,PartDCapAdjDelegated                         DECIMAL(19, 9) NULL
    ,MSCapAdj                                     DECIMAL(19, 9) NULL
    ,MSCapAdjDelegated                            DECIMAL(19, 9) NULL
    ,SubCapAdj                                    DECIMAL(19, 9) NULL
    ,SubCapAdjExclude                             DECIMAL(19, 9) NULL
    ,MedicaidAdjPaid                              DECIMAL(19, 9) NULL
    ,MedicaidAdjMbrCS                             DECIMAL(19, 9) NULL
    ,ImplicitMarginPaid                           DECIMAL(19, 9) NULL
    ,ImplicitMarginMbrCS                          DECIMAL(19, 9) NULL
    ,AdditiveAdjPaid                              DECIMAL(19, 9) NULL
    ,AdditiveAdjMbrCS                             DECIMAL(19, 9) NULL
    ,AdditiveAdjAdmits                            DECIMAL(19, 9) NULL
    ,AdditiveAdjUnits                             DECIMAL(19, 9) NULL
    ,ModelOfCareAdjPaid                           DECIMAL(19, 9) NULL
    ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9) NULL
    ,ModelOfCareAdjUnits                          DECIMAL(19, 9) NULL
    ,UCAdmitsAdj                                  DECIMAL(19, 9) NULL
    ,UCUnitsAdj                                   DECIMAL(19, 9) NULL
    ,PartBRxRebatesPharmacy                       DECIMAL(19, 9) NULL
    ,PartBRxRebatesQN                             DECIMAL(19, 9) NULL
    ,RelatedPartiesAdj                            DECIMAL(19, 9) NULL
    ,ProfitAdj                                    DECIMAL(19, 9) NULL
    ,MSBPaid                                      DECIMAL(19, 9) NULL
    ,MSBMbrCS                                     DECIMAL(19, 9) NULL
    ,MSBUnits                                     DECIMAL(19, 9) NULL
    ,MSBReductionCap                              DECIMAL(19, 9) NULL
    ,MSBReductionClaimsPaid                       DECIMAL(19, 9) NULL
    ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9) NULL
    ,MSBReductionClaimsUnits                      DECIMAL(19, 9) NULL
    ,MSBReductionQuality                          DECIMAL(19, 9) NULL
    ,MemberMonths                                 INT            NULL PRIMARY KEY CLUSTERED (
                                                                      BenefitCategoryID ASC
                                                                     ,[Provider] ASC
                                                                     ,PlanInfoID ASC
                                                                     ,MARatingOptionID ASC
                                                                     ,DemogIndicator ASC
                                                                     ,DFVersionID))
AS

    BEGIN

        --Variables 
        DECLARE @XForecastID       INT     = @ForecastID
               ,@XMARatingOptionID TINYINT = @MARatingOptionID;
        DECLARE @DeemedNonRisk VARCHAR(20) = 'Deemed NonRisk';
        DECLARE @TotalExperience VARCHAR(20) = 'Total Experience';
        DECLARE @TotalManual VARCHAR(20) = 'Total Manual';


        -----  Declare and populate the relevant Grouper to Provider Mapping defined through PREP uploaded ------
        DECLARE @RegionGrouperMap TABLE
            (GrouperID  VARCHAR(8)   NOT NULL
            ,[Provider] VARCHAR(255) NOT NULL PRIMARY KEY (GrouperID));
        INSERT INTO @RegionGrouperMap
            (GrouperID
            ,[Provider])
        SELECT      SRG.GrouperID
                   ,SRG.[Provider]
        FROM        dbo.SavedForecastSetup SFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanInfo SPI WITH (NOLOCK)
               ON SFS.PlanInfoID = SPI.PlanInfoID
       INNER JOIN   dbo.SavedMarketInfo SMI WITH (NOLOCK)
               ON SMI.ActuarialMarketID = SPI.ActuarialMarketID
       INNER JOIN   dbo.SavedRegionGrouperMapping SRG WITH (NOLOCK)
               ON SRG.PlanYear = SPI.PlanYear
                  AND   SRG.ActuarialRegionID = SMI.ActuarialRegionID
        WHERE       SFS.ForecastID = @XForecastID;

        --Base Plans
        DECLARE @BasePlans TABLE
            (MARatingOptionID TINYINT NOT NULL
            ,PlanInfoID       INT     NOT NULL
            ,DFVersionID      INT     NOT NULL PRIMARY KEY CLUSTERED (
                                               PlanInfoID ASC
                                              ,MARatingOptionID ASC
                                              ,DFVersionID ASC));
        INSERT INTO @BasePlans
            (MARatingOptionID
            ,PlanInfoID
            ,DFVersionID)
        SELECT      DFS.MARatingOptionID
                   ,DFS.PlanInfoID
                   ,SFS.DFVersionID
        FROM        dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedForecastSetup SFS WITH (NOLOCK)
               ON SFS.ForecastID = DFS.ForecastID
        WHERE       DFS.ForecastID = @XForecastID
        GROUP BY    DFS.MARatingOptionID
                   ,DFS.PlanInfoID
                   ,SFS.DFVersionID;

        IF @XMARatingOptionID < 3
            -- delete any unnecessary group by records
            BEGIN
                DELETE  FROM @BasePlans WHERE   MARatingOptionID = 3 - @XMARatingOptionID;
            END;

        --SavedDFClaims
        DECLARE @SavedDFClaims TABLE
            (MARatingOptionID                             TINYINT        NOT NULL
            ,PlanInfoID                                   INT            NOT NULL
            ,DFVersionID                                  INT            NOT NULL
            ,IncurredYear                                 INT            NOT NULL
            ,DemogIndicator                               TINYINT        NOT NULL
            ,GrouperID                                    VARCHAR(8)     NOT NULL
            ,BenefitCategoryID                            SMALLINT       NOT NULL
            ,Paid                                         DECIMAL(19, 9) NULL
            ,MbrCS                                        DECIMAL(19, 9) NULL
            ,PymtReductionAmt                             DECIMAL(19, 9) NULL
            ,AdmitCnt                                     DECIMAL(19, 9) NULL
            ,UnitCnt                                      DECIMAL(19, 9) NULL
            ,EncounterMbrCS                               DECIMAL(19, 9) NULL
            ,EncounterAdmitCnt                            DECIMAL(19, 9) NULL
            ,EncounterUnitCnt                             DECIMAL(19, 9) NULL
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9) NULL
            ,DelegatedEncounterAdmitCnt                   DECIMAL(19, 9) NULL
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9) NULL
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9) NULL
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9) NULL
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9) NULL
            ,CapMSBs                                      DECIMAL(19, 9) NULL
            ,CapProviderRewards                           DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9) NULL
            ,PartDCapAdj                                  DECIMAL(19, 9) NULL
            ,PartDCapAdjDelegated                         DECIMAL(19, 9) NULL
            ,MSCapAdj                                     DECIMAL(19, 9) NULL
            ,MSCapAdjDelegated                            DECIMAL(19, 9) NULL
            ,SubCapAdj                                    DECIMAL(19, 9) NULL
            ,SubCapAdjExclude                             DECIMAL(19, 9) NULL
            ,MedicaidAdjPaid                              DECIMAL(19, 9) NULL
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9) NULL
            ,ImplicitMarginPaid                           DECIMAL(19, 9) NULL
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9) NULL
            ,AdditiveAdjPaid                              DECIMAL(19, 9) NULL
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9) NULL
            ,AdditiveAdjAdmits                            DECIMAL(19, 9) NULL
            ,AdditiveAdjUnits                             DECIMAL(19, 9) NULL
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9) NULL
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9) NULL
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9) NULL
            ,UCAdmitsAdj                                  DECIMAL(19, 9) NULL
            ,UCUnitsAdj                                   DECIMAL(19, 9) NULL
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9) NULL
            ,PartBRxRebatesQN                             DECIMAL(19, 9) NULL
            ,RelatedPartiesAdj                            DECIMAL(19, 9) NULL
            ,ProfitAdj                                    DECIMAL(19, 9) NULL
            ,MSBPaid                                      DECIMAL(19, 9) NULL
            ,MSBMbrCS                                     DECIMAL(19, 9) NULL
            ,MSBUnits                                     DECIMAL(19, 9) NULL
            ,MSBReductionCap                              DECIMAL(19, 9) NULL
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9) NULL
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9) NULL
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9) NULL
            ,MSBReductionQuality                          DECIMAL(19, 9) NULL PRIMARY KEY CLUSTERED (
                                                                              GrouperID ASC
                                                                             ,BenefitCategoryID ASC
                                                                             ,MARatingOptionID ASC
                                                                             ,PlanInfoID ASC
                                                                             ,DemogIndicator ASC
                                                                             ,DFVersionID ASC));

        INSERT  @SavedDFClaims
            (MARatingOptionID
            ,PlanInfoID
            ,DFVersionID
            ,IncurredYear
            ,DemogIndicator
            ,GrouperID
            ,BenefitCategoryID
            ,Paid
            ,MbrCS
            ,PymtReductionAmt
            ,AdmitCnt
            ,UnitCnt
            ,EncounterMbrCS
            ,EncounterAdmitCnt
            ,EncounterUnitCnt
            ,DelegatedEncounterMbrCS
            ,DelegatedEncounterAdmitCnt
            ,DelegatedEncounterUnitCnt
            ,CapDirectPayEPaidClaims
            ,CapDirectPayDEPaidClaims
            ,CapDirectPayBCAllocated
            ,CapDirectPayScenarioAllocated
            ,CapDirectPayBCAllocatedDelegated
            ,CapDirectPayScenarioAllocatedDelegated
            ,CapSurplusDeficitEPaidClaims
            ,CapSurplusDeficitDEPaidClaims
            ,CapSurplusDeficitBCAllocated
            ,CapSurplusDeficitScenarioAllocated
            ,CapSurplusDeficitBCAllocatedDelegated
            ,CapSurplusDeficitScenarioAllocatedDelegated
            ,CapMSBs
            ,CapProviderRewards
            ,OPBOtherNonHospitalBCAllocated
            ,OPBOtherNonHospitalScenarioAllocated
            ,OPBClaimsBuyDownBCAllocated
            ,OPBClaimsBuyDownScenarioAllocated
            ,OPBProviderClaimSettlementsBCAllocated
            ,OPBProviderClaimSettlementsScenarioAllocated
            ,OPBAccessFeesAndOtherBCAllocated
            ,OPBAccessFeesAndOtherScenarioAllocated
            ,PartDCapAdj
            ,PartDCapAdjDelegated
            ,MSCapAdj
            ,MSCapAdjDelegated
            ,SubCapAdj
            ,SubCapAdjExclude
            ,MedicaidAdjPaid
            ,MedicaidAdjMbrCS
            ,ImplicitMarginPaid
            ,ImplicitMarginMbrCS
            ,AdditiveAdjPaid
            ,AdditiveAdjMbrCS
            ,AdditiveAdjAdmits
            ,AdditiveAdjUnits
            ,ModelOfCareAdjPaid
            ,ModelOfCareAdjMbrCS
            ,ModelOfCareAdjUnits
            ,UCAdmitsAdj
            ,UCUnitsAdj
            ,PartBRxRebatesPharmacy
            ,PartBRxRebatesQN
            ,RelatedPartiesAdj
            ,ProfitAdj
            ,MSBPaid
            ,MSBMbrCS
            ,MSBUnits
            ,MSBReductionCap
            ,MSBReductionClaimsPaid
            ,MSBReductionClaimsMbrCS
            ,MSBReductionClaimsUnits
            ,MSBReductionQuality)
        SELECT      BP.MARatingOptionID
                   ,BP.PlanInfoID
                   ,BP.DFVersionID
                   ,DFC.IncurredYear
                   ,DFC.DemogIndicator
                   ,DFC.GrouperID
                   ,DFC.BenefitCategoryID
                   ,DFC.Paid
                   ,DFC.MbrCS
                   ,DFC.PymtReductionAmt
                   ,DFC.AdmitCnt
                   ,DFC.UnitCnt
                   ,DFC.EncounterMbrCS
                   ,DFC.EncounterAdmitCnt
                   ,DFC.EncounterUnitCnt
                   ,DFC.DelegatedEncounterMbrCS
                   ,DFC.DelegatedEncounterAdmitCnt
                   ,DFC.DelegatedEncounterUnitCnt
                   ,DFC.CapDirectPayEPaidClaims
                   ,DFC.CapDirectPayDEPaidClaims
                   ,DFC.CapDirectPayBCAllocated
                   ,DFC.CapDirectPayScenarioAllocated
                   ,DFC.CapDirectPayBCAllocatedDelegated
                   ,DFC.CapDirectPayScenarioAllocatedDelegated
                   ,DFC.CapSurplusDeficitEPaidClaims
                   ,DFC.CapSurplusDeficitDEPaidClaims
                   ,DFC.CapSurplusDeficitBCAllocated
                   ,DFC.CapSurplusDeficitScenarioAllocated
                   ,DFC.CapSurplusDeficitBCAllocatedDelegated
                   ,DFC.CapSurplusDeficitScenarioAllocatedDelegated
                   ,DFC.CapMSBs
                   ,DFC.CapProviderRewards
                   ,DFC.OPBOtherNonHospitalBCAllocated
                   ,DFC.OPBOtherNonHospitalScenarioAllocated
                   ,DFC.OPBClaimsBuyDownBCAllocated
                   ,DFC.OPBClaimsBuyDownScenarioAllocated
                   ,DFC.OPBProviderClaimSettlementsBCAllocated
                   ,DFC.OPBProviderClaimSettlementsScenarioAllocated
                   ,DFC.OPBAccessFeesAndOtherBCAllocated
                   ,DFC.OPBAccessFeesAndOtherScenarioAllocated
                   ,DFC.PartDCapAdj
                   ,DFC.PartDCapAdjDelegated
                   ,DFC.MSCapAdj
                   ,DFC.MSCapAdjDelegated
                   ,DFC.SubCapAdj
                   ,DFC.SubCapAdjExclude
                   ,DFC.MedicaidAdjPaid
                   ,DFC.MedicaidAdjMbrCS
                   ,DFC.ImplicitMarginPaid
                   ,DFC.ImplicitMarginMbrCS
                   ,DFC.AdditiveAdjPaid
                   ,DFC.AdditiveAdjMbrCS
                   ,DFC.AdditiveAdjAdmits
                   ,DFC.AdditiveAdjUnits
                   ,DFC.ModelOfCareAdjPaid
                   ,DFC.ModelOfCareAdjMbrCS
                   ,DFC.ModelOfCareAdjUnits
                   ,DFC.UCAdmitsAdj
                   ,DFC.UCUnitsAdj
                   ,DFC.PartBRxRebatesPharmacy
                   ,DFC.PartBRxRebatesQN
                   ,DFC.RelatedPartiesAdj
                   ,DFC.ProfitAdj
                   ,DFC.MSBPaid
                   ,DFC.MSBMbrCS
                   ,DFC.MSBUnits
                   ,DFC.MSBReductionCap
                   ,DFC.MSBReductionClaimsPaid
                   ,DFC.MSBReductionClaimsMbrCS
                   ,DFC.MSBReductionClaimsUnits
                   ,DFC.MSBReductionQuality
        FROM        @BasePlans BP
        LEFT JOIN   dbo.SavedDFClaims DFC WITH (NOLOCK)
               ON DFC.PlanInfoID = BP.PlanInfoID
                  AND   DFC.DFVersionID = BP.DFVersionID
                  AND   DFC.BenefitCategoryID > 1000 --MSBs
                  AND   DFC.DemogIndicator IN (1, 2)
       INNER JOIN   dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
               ON DFC.BenefitCategoryID = BC.BenefitCategoryID
        WHERE       BC.IsUsed = 1
                    AND BC.IsEnabled = 1;


        --SavedDFFinance
        DECLARE @SavedDFFinance TABLE
            (MARatingOptionID TINYINT    NOT NULL
            ,PlanInfoID       INT        NOT NULL
            ,DFVersionID      INT        NOT NULL
            ,IncurredYear     INT        NOT NULL
            ,DemogIndicator   TINYINT    NOT NULL
            ,GrouperID        VARCHAR(8) NOT NULL
            ,MemberMonths     INT        NOT NULL PRIMARY KEY CLUSTERED (
                                                  GrouperID ASC
                                                 ,PlanInfoID ASC
                                                 ,MARatingOptionID ASC
                                                 ,DemogIndicator ASC
                                                 ,DFVersionID ASC));

        INSERT INTO @SavedDFFinance
            (MARatingOptionID
            ,PlanInfoID
            ,DFVersionID
            ,IncurredYear
            ,DemogIndicator
            ,GrouperID
            ,MemberMonths)
        SELECT      BP.MARatingOptionID
                   ,BP.PlanInfoID
                   ,BP.DFVersionID
                   ,DFF.IncurredYear
                   ,DFF.DemogIndicator
                   ,DFF.GrouperID
                   ,SUM (DFF.MemberMonths) AS MemberMonths
        FROM        @BasePlans BP
        LEFT JOIN   dbo.SavedDFFinance DFF WITH (NOLOCK)
               ON DFF.PlanInfoID = BP.PlanInfoID
                  AND   DFF.DFVersionID = BP.DFVersionID
                  AND   DFF.DemogIndicator IN (1, 2)
        GROUP BY    BP.MARatingOptionID
                   ,BP.PlanInfoID
                   ,BP.DFVersionID
                   ,DFF.IncurredYear
                   ,DFF.DemogIndicator
                   ,DFF.GrouperID;

        ---  Declare and populate the Complete set of GrouperIDs, Benefit Category IDs, Base PlanInfoIDs and DemogIndicators 1 & 2 -----
        DECLARE @GroupByDEvsNonDE TABLE
            (MARatingOptionID  TINYINT    NOT NULL
            ,PlanInfoID        INT        NOT NULL
            ,DFVersionID       INT        NOT NULL
            ,IncurredYear      SMALLINT   NOT NULL
            ,GrouperID         VARCHAR(8) NOT NULL
            ,DemogIndicator    TINYINT    NOT NULL
            ,BenefitCategoryID INT        NOT NULL
            ,BidServiceCatID   INT        NOT NULL PRIMARY KEY CLUSTERED (
                                                   GrouperID
                                                  ,BenefitCategoryID
                                                  ,MARatingOptionID
                                                  ,PlanInfoID
                                                  ,DemogIndicator
                                                  ,DFVersionID));
        DELETE  FROM @GroupByDEvsNonDE;
        INSERT INTO @GroupByDEvsNonDE
        SELECT          ISNULL (DFC.MARatingOptionID, DFF.MARatingOptionID) MARatingOptionID
                       ,ISNULL (DFC.PlanInfoID, DFF.PlanInfoID) PlanInfoID
                       ,ISNULL (DFC.DFVersionID, DFF.DFVersionID) DFVersionID
                       ,ISNULL (DFC.IncurredYear, DFF.IncurredYear) IncurredYear
                       ,ISNULL (DFC.GrouperID, DFF.GrouperID) GrouperID
                       ,DI.DemogIndicator
                       ,BC.BenefitCategoryID
                       ,BC.BidServiceCatID
        FROM            @SavedDFClaims DFC
        FULL OUTER JOIN @SavedDFFinance DFF
                     ON DFC.MARatingOptionID = DFF.MARatingOptionID
                        AND DFC.PlanInfoID = DFF.PlanInfoID
                        AND DFC.DFVersionID = DFF.DFVersionID
                        AND DFC.GrouperID = DFF.GrouperID
                        AND DFC.IncurredYear = DFF.IncurredYear
       CROSS JOIN       dbo.LkpIntDemogIndicators DI WITH (NOLOCK)
       CROSS JOIN       dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
        WHERE           DI.DemogIndicator IN (1, 2)
                        AND BC.BenefitCategoryID > 1000 --MSBs
                        AND BC.IsUsed = 1
                        AND BC.IsEnabled = 1
        GROUP BY        ISNULL (DFC.MARatingOptionID, DFF.MARatingOptionID)
                       ,ISNULL (DFC.PlanInfoID, DFF.PlanInfoID)
                       ,ISNULL (DFC.DFVersionID, DFF.DFVersionID)
                       ,ISNULL (DFC.IncurredYear, DFF.IncurredYear)
                       ,ISNULL (DFC.GrouperID, DFF.GrouperID)
                       ,DI.DemogIndicator
                       ,BC.BenefitCategoryID
                       ,BC.BidServiceCatID;

        -----  Declare and populate the Complete set of benefit category IDs by Base PlanInfoIDs and Providers -----
        DECLARE @CombinedExperienceProviderMembership TABLE
            (MARatingOptionID  TINYINT      NOT NULL
            ,PlanInfoID        INT          NOT NULL
            ,DFVersionID       INT          NOT NULL
            ,IncurredYear      SMALLINT     NOT NULL
            ,GrouperID         VARCHAR(8)   NOT NULL
            ,[Provider]        VARCHAR(255) NOT NULL
            ,DemogIndicator    TINYINT      NOT NULL
            ,BenefitCategoryID SMALLINT     NOT NULL
            ,BidServiceCatID   SMALLINT     NOT NULL
            ,MemberMonths      INT          NOT NULL
            ,PRIMARY KEY CLUSTERED (
             GrouperID
            ,BenefitCategoryID
            ,DemogIndicator
            ,PlanInfoID
            ,DFVersionID));
        DELETE  FROM @CombinedExperienceProviderMembership WHERE    1 = 1;
        INSERT INTO @CombinedExperienceProviderMembership
            (MARatingOptionID
            ,PlanInfoID
            ,DFVersionID
            ,IncurredYear
            ,GrouperID
            ,[Provider]
            ,DemogIndicator
            ,BenefitCategoryID
            ,BidServiceCatID
            ,MemberMonths)
        SELECT          1 MARatingOptionID
                       ,ISNULL (DFC.PlanInfoID, DFF.PlanInfoID) PlanInfoID
                       ,ISNULL (DFC.DFVersionID, DFF.DFVersionID) DFVersionID
                       ,ISNULL (DFC.IncurredYear, DFF.IncurredYear) IncurredYear
                       ,ISNULL (DFC.GrouperID, DFF.GrouperID) GrouperID
                       ,ISNULL (RGM.[Provider], @DeemedNonRisk) [Provider]
                       ,DI.DemogIndicator
                       ,BC.BenefitCategoryID
                       ,BC.BidServiceCatID
                       ,SUM (ISNULL (DFF.MemberMonths, 0)) MemberMonths
        FROM            @SavedDFClaims DFC
        FULL OUTER JOIN @SavedDFFinance DFF
                     ON DFC.MARatingOptionID = DFF.MARatingOptionID
                        AND DFC.PlanInfoID = DFF.PlanInfoID
                        AND DFC.DFVersionID = DFF.DFVersionID
                        AND DFC.IncurredYear = DFF.IncurredYear
                        AND DFC.GrouperID = DFF.GrouperID
        LEFT JOIN       @RegionGrouperMap RGM
               ON DFC.GrouperID = RGM.GrouperID
                  AND   DFF.GrouperID = RGM.GrouperID
       CROSS JOIN       dbo.LkpIntDemogIndicators DI WITH (NOLOCK)
       CROSS JOIN       dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
        WHERE           DFC.MARatingOptionID = 1 --Experience
                        AND DI.DemogIndicator IN (1, 2)
                        AND BC.IsUsed = 1
                        AND BC.IsEnabled = 1
                        AND BC.BenefitCategoryID > 1000
        GROUP BY        ISNULL (DFC.PlanInfoID, DFF.PlanInfoID)
                       ,ISNULL (DFC.DFVersionID, DFF.DFVersionID)
                       ,ISNULL (DFC.IncurredYear, DFF.IncurredYear)
                       ,ISNULL (DFC.GrouperID, DFF.GrouperID)
                       ,ISNULL (RGM.Provider, @DeemedNonRisk)
                       ,DI.DemogIndicator
                       ,BC.BenefitCategoryID
                       ,BC.BidServiceCatID;

        INSERT INTO @Results
        --  Insert the Base Plan Totals for Experience and Manual by DE# and NonDE# Demographic Indicators
        SELECT      GBI.MARatingOptionID
                   ,GBI.PlanInfoID
                   ,GBI.DFVersionID
                   ,GBI.IncurredYear
                   ,GBI.DemogIndicator
                   ,[Provider] = CASE WHEN GBI.MARatingOptionID = 1 THEN @TotalExperience
                                      WHEN GBI.MARatingOptionID = 2 THEN @TotalManual
                                      ELSE NULL END
                   ,GBI.BenefitCategoryID
                   ,GBI.BidServiceCatID
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.Paid, 0)), SUM (ISNULL (M.MemberMonths, 0))) Paid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PymtReductionAmt, 0)), SUM (ISNULL (M.MemberMonths, 0))) PymtReductionAmt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdmitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UnitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.EncounterMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) EncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) EncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterUnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) EncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterUnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayDEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitDEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapMSBs, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapMSBs
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapProviderRewards, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapProviderRewards
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBOtherNonHospitalBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBOtherNonHospitalScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBClaimsBuyDownBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBClaimsBuyDownScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBProviderClaimSettlementsBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBProviderClaimSettlementsScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBAccessFeesAndOtherBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBAccessFeesAndOtherScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartDCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartDCapAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartDCapAdjDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartDCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdjDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) SubCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdjExclude, 0)), SUM (ISNULL (M.MemberMonths, 0))) SubCapAdjExclude
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MedicaidAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MedicaidAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ImplicitMarginPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) ImplicitMarginPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ImplicitMarginMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) ImplicitMarginMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjAdmits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjAdmits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ModelOfCareAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.UCAdmitsAdj, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UCAdmitsAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UCUnitsAdj, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UCUnitsAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartBRxRebatesPharmacy, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartBRxRebatesPharmacy
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartBRxRebatesQN, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartBRxRebatesQN
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.RelatedPartiesAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) RelatedPartiesAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ProfitAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) ProfitAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) MSBUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBReductionCap, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionCap
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionQuality, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionQuality
                   ,SUM (ISNULL (M.MemberMonths, 0)) MemberMonths
        FROM        @GroupByDEvsNonDE GBI
        LEFT JOIN   @SavedDFClaims DFC
               ON GBI.GrouperID = DFC.GrouperID
                  AND   GBI.BenefitCategoryID = DFC.BenefitCategoryID
                  AND   GBI.MARatingOptionID = DFC.MARatingOptionID
                  AND   GBI.PlanInfoID = DFC.PlanInfoID
                  AND   GBI.DemogIndicator = DFC.DemogIndicator
                  AND   GBI.DFVersionID = DFC.DFVersionID
        LEFT JOIN   @SavedDFFinance M
               ON GBI.GrouperID = M.GrouperID
                  AND   GBI.MARatingOptionID = M.MARatingOptionID
                  AND   GBI.PlanInfoID = M.PlanInfoID
                  AND   GBI.DemogIndicator = M.DemogIndicator
                  AND   GBI.DFVersionID = M.DFVersionID
        WHERE       GBI.DemogIndicator IN (1, 2)
        GROUP BY    GBI.MARatingOptionID
                   ,CASE WHEN GBI.MARatingOptionID = 1 THEN @TotalExperience
                         WHEN GBI.MARatingOptionID = 2 THEN @TotalManual
                         ELSE NULL END
                   ,GBI.BenefitCategoryID
                   ,GBI.PlanInfoID
                   ,GBI.DemogIndicator
                   ,GBI.DFVersionID
                   ,GBI.IncurredYear
                   ,GBI.BidServiceCatID

        UNION ALL
        --  Insert the Base Plan Totals for the Manual Combined Biddable Members (DE# & NonDE# combined)
        SELECT      2 MARatingOptionID
                   ,GBI.PlanInfoID
                   ,GBI.DFVersionID
                   ,GBI.IncurredYear
                   ,3 DemogIndicator
                   ,[Provider] = @TotalManual
                   ,GBI.BenefitCategoryID
                   ,GBI.BidServiceCatID
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.Paid, 0)), SUM (ISNULL (M.MemberMonths, 0))) Paid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PymtReductionAmt, 0)), SUM (ISNULL (M.MemberMonths, 0))) PymtReductionAmt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdmitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UnitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.EncounterMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) EncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) EncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterUnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) EncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterUnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayDEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitDEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapMSBs, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapMSBs
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapProviderRewards, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapProviderRewards
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBOtherNonHospitalBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBOtherNonHospitalScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBClaimsBuyDownBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBClaimsBuyDownScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBProviderClaimSettlementsBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBProviderClaimSettlementsScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBAccessFeesAndOtherBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBAccessFeesAndOtherScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartDCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartDCapAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartDCapAdjDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartDCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdjDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) SubCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdjExclude, 0)), SUM (ISNULL (M.MemberMonths, 0))) SubCapAdjExclude
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MedicaidAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MedicaidAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ImplicitMarginPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) ImplicitMarginPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ImplicitMarginMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) ImplicitMarginMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjAdmits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjAdmits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ModelOfCareAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.UCAdmitsAdj, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UCAdmitsAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UCUnitsAdj, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UCUnitsAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartBRxRebatesPharmacy, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartBRxRebatesPharmacy
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartBRxRebatesQN, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartBRxRebatesQN
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.RelatedPartiesAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) RelatedPartiesAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ProfitAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) ProfitAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) MSBUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBReductionCap, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionCap
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionQuality, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionQuality
                   ,SUM (ISNULL (M.MemberMonths, 0)) MemberMonths
        FROM        @GroupByDEvsNonDE GBI
        LEFT JOIN   @SavedDFClaims DFC
               ON GBI.GrouperID = DFC.GrouperID
                  AND   GBI.BenefitCategoryID = DFC.BenefitCategoryID
                  AND   GBI.MARatingOptionID = DFC.MARatingOptionID
                  AND   GBI.PlanInfoID = DFC.PlanInfoID
                  AND   GBI.DemogIndicator = DFC.DemogIndicator
                  AND   GBI.DFVersionID = DFC.DFVersionID
        LEFT JOIN   @SavedDFFinance M
               ON GBI.GrouperID = M.GrouperID
                  AND   GBI.MARatingOptionID = M.MARatingOptionID
                  AND   GBI.PlanInfoID = M.PlanInfoID
                  AND   GBI.DemogIndicator = M.DemogIndicator
                  AND   GBI.DFVersionID = M.DFVersionID
        WHERE       GBI.MARatingOptionID = 2    --Manual
        GROUP BY    GBI.MARatingOptionID
                   ,GBI.BenefitCategoryID
                   ,GBI.PlanInfoID
                   ,GBI.DFVersionID
                   ,GBI.IncurredYear
                   ,GBI.BidServiceCatID

        UNION ALL
        --  Finally Insert the Base Plan Totals for the Experience for Combined Biddable Members BY PROVIDER!

        SELECT      GBI.MARatingOptionID
                   ,GBI.PlanInfoID
                   ,GBI.DFVersionID
                   ,GBI.IncurredYear
                   ,3 DemogIndicator
                   ,ISNULL (GBI.[Provider], @DeemedNonRisk) [Provider]
                   ,GBI.BenefitCategoryID
                   ,GBI.BidServiceCatID
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.Paid, 0)), SUM (ISNULL (M.MemberMonths, 0))) Paid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PymtReductionAmt, 0)), SUM (ISNULL (M.MemberMonths, 0))) PymtReductionAmt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdmitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UnitCnt
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.EncounterMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) EncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) EncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.EncounterUnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) EncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterAdmitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterAdmitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.DelegatedEncounterUnitCnt, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) DelegatedEncounterUnitCnt
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayDEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayBCAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapDirectPayScenarioAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapDirectPayScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitDEPaidClaims, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitDEPaidClaims
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitBCAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitBCAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.CapSurplusDeficitScenarioAllocatedDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapSurplusDeficitScenarioAllocatedDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapMSBs, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapMSBs
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.CapProviderRewards, 0)), SUM (ISNULL (M.MemberMonths, 0))) CapProviderRewards
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBOtherNonHospitalBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBOtherNonHospitalScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBOtherNonHospitalScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBClaimsBuyDownBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBClaimsBuyDownScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBClaimsBuyDownScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBProviderClaimSettlementsBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBProviderClaimSettlementsScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBProviderClaimSettlementsScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherBCAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBAccessFeesAndOtherBCAllocated
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.OPBAccessFeesAndOtherScenarioAllocated, 0)), SUM (ISNULL (M.MemberMonths, 0))) OPBAccessFeesAndOtherScenarioAllocated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartDCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartDCapAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartDCapAdjDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartDCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSCapAdjDelegated, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSCapAdjDelegated
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) SubCapAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.SubCapAdjExclude, 0)), SUM (ISNULL (M.MemberMonths, 0))) SubCapAdjExclude
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MedicaidAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MedicaidAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MedicaidAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ImplicitMarginPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) ImplicitMarginPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ImplicitMarginMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) ImplicitMarginMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.AdditiveAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjAdmits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjAdmits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.AdditiveAdjUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) AdditiveAdjUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ModelOfCareAdjPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.ModelOfCareAdjUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) ModelOfCareAdjUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.UCAdmitsAdj, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UCAdmitsAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.UCUnitsAdj, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) UCUnitsAdj
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.PartBRxRebatesPharmacy, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartBRxRebatesPharmacy
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.PartBRxRebatesQN, 0)), SUM (ISNULL (M.MemberMonths, 0))) PartBRxRebatesQN
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.RelatedPartiesAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) RelatedPartiesAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.ProfitAdj, 0)), SUM (ISNULL (M.MemberMonths, 0))) ProfitAdj
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBPaid
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBMbrCS
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) MSBUnits
                   ,dbo.fnGetSafeDivisionResult (SUM (ISNULL (DFC.MSBReductionCap, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionCap
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsPaid, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsPaid
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsMbrCS, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsMbrCS
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionClaimsUnits, 0)) * 12000, SUM (ISNULL (M.MemberMonths, 0))) MSBReductionClaimsUnits
                   ,dbo.fnGetSafeDivisionResult (
                    SUM (ISNULL (DFC.MSBReductionQuality, 0)), SUM (ISNULL (M.MemberMonths, 0))) MSBReductionQuality
                   ,SUM (ISNULL (M.MemberMonths, 0)) MemberMonths
        FROM        @CombinedExperienceProviderMembership GBI
        LEFT JOIN   @SavedDFClaims DFC
               ON GBI.MARatingOptionID = DFC.MARatingOptionID
                  AND   GBI.GrouperID = DFC.GrouperID
                  AND   GBI.PlanInfoID = DFC.PlanInfoID
                  AND   GBI.DFVersionID = DFC.DFVersionID
                  AND   GBI.DemogIndicator = DFC.DemogIndicator
                  AND   GBI.BenefitCategoryID = DFC.BenefitCategoryID
        LEFT JOIN   @SavedDFFinance M
               ON GBI.MARatingOptionID = M.MARatingOptionID
                  AND   GBI.GrouperID = M.GrouperID
                  AND   GBI.PlanInfoID = M.PlanInfoID
                  AND   GBI.DFVersionID = M.DFVersionID
                  AND   GBI.DemogIndicator = M.DemogIndicator
        GROUP BY    GBI.MARatingOptionID
                   ,ISNULL (GBI.[Provider], @DeemedNonRisk)
                   ,GBI.BenefitCategoryID
                   ,GBI.PlanInfoID
                   ,GBI.DFVersionID
                   ,GBI.IncurredYear
                   ,GBI.BidServiceCatID;

        RETURN;
    END;
GO
