SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcPlanAdminBlendBulkImport
--
-- AUTHOR: Surya Murthy
--
-- CREATED DATE: 2021-Jan-20
--
-- DESCRIPTION: Responsible for blending the admin rates to create CalcPlanAdminBlend with bulk upload
--
-- PARAMETERS:
--  Input:
--		@ForecastIDVal
--		@UserID
--
--  Output:
--
-- TABLES:
--  Read:
--		SavedPlanHeader
--		LkpProductType
--		SavedMemberMonthDetail
--		SavedMarketInfo
--		SavedPlanStateCountyDetail
--		PerIntAdminExpenses
--		LkpExtCMSStateTerritory
--		LkpExtCMSStateCounty
--		LkpIntDemogIndicator
--
--  Write:
--		CalcPlanAdminBlend
--
-- VIEWS:
--
-- FUCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER  
-- ------------------------------------------------------------------------------------------------------------------------------
-- 2021-Jan-20      1			Initial Version														Surya Murthy
-- 2022-Nov-23      2           Replace Product type of 'HMOPOS' with 'HMO' for joins               Adam Gilbert
-- 2023-Sep-25	    3		    Rowlocks added for deadlock issues									Surya Murthy
-- 2024-Sep-25		4			Restructure Code. Added Legal Entity Logic.							Adam Gilbert
-- 2024-Nov-20      5           Compare Org Name with case sensitivity to avoid duplicate           Adam Gilbert
-- 2024-Dec-05      6           Change type of join #FinalCalcPlanAdminBlend to not miss records    Ramaraj Kumar
--                              Remove the case sensitive check for Org Name
-- 2024-Dec-05      7           Admin Data Audit and Sync                                           Ramaraj Kumar
-- 2024-Dec-17      8           Same retrieval process of ForeCastID for Validation and Sync        Ramaraj Kumar
-- 2024-Dec-23      9           Revert version 7 and receive DISTINCT ForeCastIDs for Validation    Ramaraj Kumar
-- 2025-Apr-2       10          Altered join logic and compare plan list						    Adam Gilbert
-- 2025-Apr-14      11          Exclude termed plans from sync										Adam Gilbert
-- --------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcPlanAdminBlendBulkImport]
(
    @ForecastIDVal VARCHAR(MAX),	
    @UserID CHAR(7),
	@IsValidation BIT = 0, -- if true, the SP is used only for auditing/ validating the Admin Data.
	@IsSync BIT = 0, --if true, the sync operation will be performed.
	@DivisionList VARCHAR(MAX) = NULL,
	@RegionList VARCHAR(MAX) = NULL
)
AS
BEGIN
    SET NOCOUNT ON

    --Split Input forecastid
    DROP TABLE IF EXISTS #TempBulkImportPlanAdminForecastIDTable

	CREATE TABLE #TempBulkImportPlanAdminForecastIDTable
	(ForeCastID VARCHAR(MAX))

	IF @IsValidation = 1
	BEGIN
	INSERT INTO #TempBulkImportPlanAdminForecastIDTable
	SELECT DISTINCT SFS.ForecastID	
	FROM dbo.SavedPlanInfo SPI
				JOIN SavedForecastSetup sfs ON spi.PlanInfoID = sfs.planinfoid
				JOIN dbo.SavedMarketInfo SMI ON Spi.ActuarialMarketID = SMI.ActuarialMarketID
				JOIN dbo.SavedRegionInfo SRI ON SRI.ActuarialRegionID = SMI.ActuarialRegionID
				JOIN dbo.SavedDivisionInfo SDI ON SDI.ActuarialDivisionID = SRI.ActuarialDivisionID
				JOIN dbo.SavedServiceAreaOption ssa WITH (NOLOCK) ON ssa.PlanInfoID = sfs.PlanInfoID AND  ssa.ServiceAreaOptionID = sfs.ServiceAreaOptionID
				WHERE ( (@DivisionList is null) or SDI.ActuarialDivision in (select value from dbo.fnStringSplit(@DivisionList,',')) )
				AND
				((@RegionList is null) or SRI.ActuarialRegion in (select value from dbo.fnStringSplit(@RegionList,',')))
				AND IsLiveIndex = 1
				AND isfiledplan=1
				AND ishidden = 0
				AND ssa.RenewalTypeID <> 2
				AND sfs.Planyear = dbo.fngetbidyear()
	END

	ELSE
	BEGIN
	INSERT INTO #TempBulkImportPlanAdminForecastIDTable
    SELECT VALUE ForecastID FROM dbo.fnStringSplit(@ForecastIDVal, ',')
	END


    -- Declare Variables
    DECLARE @PlanYearID SMALLINT,
            @LastDateTime DATETIME,
            @LocalError INT,
            @ReturnStatus INT

    -- Set Variables
    SET @LastDateTime = GETDATE()
    SET @ReturnStatus = 0
    SET @PlanYearID = dbo.fnGetBidYear()

    ----If an error occurs, we need to note it and cancel the DELETE and INSERT
    BEGIN TRANSACTION AppendData
    BEGIN TRY
        BEGIN

            --Gather member month detail records
            DROP TABLE IF EXISTS #MMD
            SELECT mmd.ForecastID,
                   STT.StateTerritoryCode,
                   CTY.StateTerritoryID,
                   CTY.CountyCode,
                   StateTerritoryName,
                   CountyName,
                   MemberMonths,
                   DualEligibleTypeID,
                   ROW_NUMBER() OVER (PARTITION BY mmd.ForecastID,
                                                   STT.StateTerritoryCode,
                                                   CTY.StateTerritoryID,
                                                   CTY.CountyCode,
                                                   StateTerritoryName,
                                                   CountyName
                                      ORDER BY DualEligibleTypeID
                                     ) RowNum
            INTO #MMD
            FROM dbo.SavedPlanMemberMonthDetail MMD WITH (NOLOCK)
                INNER JOIN LkpExtCMSStateCounty CTY WITH (NOLOCK)
                    ON CTY.StateTerritoryID = MMD.StateTerritoryID
                       AND CTY.COuntyCode = MMD.CountyCode
                INNER JOIN LkpExtCMSStateTerritory STT WITH (NOLOCK)
                    ON STT.StateTerritoryID = MMD.StateTerritoryID
                INNER JOIN LkpIntDemogIndicators LKP WITH (NOLOCK)
                    ON LKP.DemogIndicator = MMD.DemogIndicator
                INNER JOIN SavedPlanStateCountyDetail SCD WITH (NOLOCK)
                    ON SCD.ForecastID = MMD.ForecastID
                       AND SCD.StateTerritoryID = MMD.StateTerritoryID
                       AND SCD.CountyCode = MMD.CountyCode
                INNER JOIN #TempBulkImportPlanAdminForecastIDTable a
                    ON a.ForecastID = MMD.ForecastID
            WHERE IsBiddable = 1
                  AND IsCountyExcludedFromBPTOutput = 0
                  AND DualEligibleTypeID IN ( 0, 1 )

            -- Projected County Member Months
            DROP TABLE IF EXISTS #PCMM
            SELECT ForecastID,
                   StateTerritoryName,
                   CountyName,
                   StateTerritoryCode,
                   CountyCode,
                   SUM(   CASE
                              WHEN DualEligibleTypeID = 0 THEN
                                  MemberMonths
                              ELSE
                                  0
                          END
                      ) + SUM(   CASE
                                     WHEN DualEligibleTypeID = 1 THEN
                                         MemberMonths
                                     ELSE
                                         0
                                 END
                             ) ProjectedCountyMemberMonths
            INTO #PCMM
            FROM #MMD
            GROUP BY ForecastID,
                     StateTerritoryName,
                     CountyName,
                     StateTerritoryCode,
                     CountyCode

            --ProjectedTotalMemberMonths
            DROP TABLE IF EXISTS #PTMM
            SELECT ForecastID,
                   SUM(   CASE
                              WHEN DualEligibleTypeID = 0 THEN
                                  MemberMonths
                              ELSE
                                  0
                          END
                      ) + SUM(   CASE
                                     WHEN DualEligibleTypeID = 1 THEN
                                         MemberMonths
                                     ELSE
                                         0
                                 END
                             ) ProjectedTotalMemberMonths
            INTO #PTMM
            FROM #MMD
            GROUP BY ForecastID

            --Admin County Member Months
            DROP TABLE IF EXISTS #ACM
            SELECT sph.PlanyearID,
                   sph.ForecastID,
                   sph.contractNumber,
                   MMD.StateTerritoryCode,
                   MMD.CountyCode,
                   MMD.StateTerritoryName,
                   MMD.CountyName,
                   'OrganizationCode' = CASE
                                         WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN
                                             ae.OrganizationCode
                                         ELSE
                                             ae2.OrganizationCode
                                     END,
                   'TotalMAMarketingAdmin' = CASE
                                                 WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                               + sph.SegmentID THEN
                                                     ae.MAMarketingAdminPMPM
                                                 ELSE
                                                     ae2.MAMarketingAdminPMPM
                                             END * pcmm.ProjectedCountyMemberMonths,
                   'TotalDirectAdmin' = CASE
                                            WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN
                                                ae.MADirectAdminPMPM
                                            ELSE
                                                ae2.MADirectAdminPMPM
                                        END * pcmm.ProjectedCountyMemberMonths,
                   'TotalMAIndirectAdmin' = CASE
                                                WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                              + sph.SegmentID THEN
                                                    ae.MAIndirectAdminPMPM
                                                ELSE
                                                    ae2.MAIndirectAdminPMPM
                                            END * pcmm.ProjectedCountyMemberMonths,
                   'TotalMAQualityAdmin' = CASE
                                               WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                             + sph.SegmentID THEN
                                                   ae.MAQualityAdminPMPM
                                               ELSE
                                                   ae2.MAQualityAdminPMPM
                                           END * pcmm.ProjectedCountyMemberMonths,
                   'TotalMATaxesAndFeesAdminPMPM' = CASE
                                                        WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                                      + sph.SegmentID THEN
                                                            ae.MATaxesAndFeesAdminPMPM
                                                        ELSE
                                                            ae2.MATaxesAndFeesAdminPMPM
                                                    END * pcmm.ProjectedCountyMemberMonths,
                   'TotalPDMarketingAdmin' = CASE
                                                 WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                               + sph.SegmentID THEN
                                                     ae.PDMarketingAdminPMPM
                                                 ELSE
                                                     ae2.PDMarketingAdminPMPM
                                             END * pcmm.ProjectedCountyMemberMonths,
                   'TotalPDDirectAdmin' = CASE
                                              WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN
                                                  ae.PDDirectAdminPMPM
                                              ELSE
                                                  ae2.PDDirectAdminPMPM
                                          END * pcmm.ProjectedCountyMemberMonths,
                   'TotalPDIndirectAdmin' = CASE
                                                WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                              + sph.SegmentID THEN
                                                    ae.PDIndirectAdminPMPM
                                                ELSE
                                                    ae2.PDIndirectAdminPMPM
                                            END * pcmm.ProjectedCountyMemberMonths,
                   'TotalPDQualityAdmin' = CASE
                                               WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                             + sph.SegmentID THEN
                                                   ae.PDQualityAdminPMPM
                                               ELSE
                                                   ae2.PDQualityAdminPMPM
                                           END * pcmm.ProjectedCountyMemberMonths,
                   'TotalPDTaxesAndFeesAdmin' = CASE
                                                    WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-'
                                                                  + sph.SegmentID THEN
                                                        ae.PDTaxesAndFeesAdminPMPM
                                                    ELSE
                                                        ae2.PDTaxesAndFeesAdminPMPM
                                                END * pcmm.ProjectedCountyMemberMonths,
					sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID as CPS,
					(select top 1 ActuarialRegion from SavedRegionInfo where ActuarialRegionID = sph.MARegionID) as Region,
					sdi.ActuarialDivision as Division
            INTO #ACM
            FROM #MMD MMD
                JOIN SavedPlanHeader SPH WITH (NOLOCK)
                    ON MMD.ForecastID = SPH.ForecastID
                JOIN SavedMarketInfo srd WITH (NOLOCK)
                    ON sph.MarketID = srd.ActuarialMarketID
				JOIN SavedRegionInfo sri WITH (NOLOCK)
					ON srd.ActuarialRegionID = sri.ActuarialRegionID
				JOIN SavedDivisionInfo sdi WITH (NOLOCK)
					ON sdi.ActuarialDivisionID = sri.ActuarialDivisionID
                JOIN LkpProductType lkp WITH (NOLOCK)
                    ON sph.PlanTypeID = lkp.ProductTypeID
                JOIN #PCMM PCMM
                    ON MMD.ForecastID = PCMM.ForecastID
                       and MMD.StateTerritoryName = PCMM.StateTerritoryName
                       and MMD.CountyName = PCMM.CountyName
                       and MMD.StateTerritoryCode = PCMM.StateTerritoryCode
                       and MMD.CountyCode = PCMM.CountyCode
                LEFT JOIN PerIntAdminExpenses ae WITH (NOLOCK)
                    ON ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID
                       AND sph.IsSNP = ae.IsSNP
                       AND replace(lkp.ProductType, 'HMOPOS', 'HMO') = ae.Product
                LEFT JOIN PerIntAdminExpenses ae2 WITH (NOLOCK)
                    ON (
                           (
                               ae2.HumanaRegionID = srd.ActuarialRegionID
                               AND ae2.StateID = mmd.StateTerritoryID
                               AND ae2.CountyCode = mmd.CountyCode
                               AND sph.IsSNP = ae2.IsSNP
                               AND replace(lkp.ProductType, 'HMOPOS', 'HMO') = ae2.Product
                           )
                           or (
                                  ae2.StateID = mmd.StateTerritoryID
                                  AND ae2.CountyCode = mmd.CountyCode
                                  AND ae2.HumanaRegionID = 0
                                  AND sph.IsSNP = ae2.IsSNP
                                  AND replace(lkp.ProductType, 'HMOPOS', 'HMO') = ae2.Product
                              )
                           or (
                                  ae2.StateID = mmd.StateTerritoryID
                                  AND ae2.HumanaRegionID = 0
                                  AND sph.IsSNP = ae2.IsSNP
                                  AND replace(lkp.ProductType, 'HMOPOS', 'HMO') = ae2.Product
                              )
                       )
                       AND (
                               AE.OrganizationCode IS NULL
                               OR AE.OrganizationCode = ae2.OrganizationCode
                           )
            WHERE RowNum = 1 --MMD Contains DE in (0,1). This reduces the dataset to one record per plan/Legal entity.


            DROP TABLE IF EXISTS #CalcPlanAdminBlendTemp
            SELECT DISTINCT
                acm.planyearid,
                acm.ForecastID,
                acm.OrganizationCode,
                acm.ContractNumber,
                'MAMarketingAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                        SUM(ACM.TotalMAMarketingAdmin),
                                                                        tmm.ProjectedTotalMemberMonths
                                                                    ),
                'MADirectAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                     SUM(ACM.TotalDirectAdmin),
                                                                     tmm.ProjectedTotalMemberMonths
                                                                 ),
                'MAIndirectAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                       SUM(ACM.TotalMAIndirectAdmin),
                                                                       tmm.ProjectedTotalMemberMonths
                                                                   ),
                'MAQualityAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                      SUM(ACM.TotalMAQualityAdmin),
                                                                      tmm.ProjectedTotalMemberMonths
                                                                  ),
                'MATaxesAndFeesAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                           SUM(ACM.TotalMATaxesAndFeesAdminPMPM),
                                                                           tmm.ProjectedTotalMemberMonths
                                                                       ),
                'PDMarketingAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                        SUM(ACM.TotalPDMarketingAdmin),
                                                                        tmm.ProjectedTotalMemberMonths
                                                                    ),
                'PDDirectAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                     SUM(ACM.TotalPDDirectAdmin),
                                                                     tmm.ProjectedTotalMemberMonths
                                                                 ),
                'PDIndirectAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                       SUM(ACM.TotalPDIndirectAdmin),
                                                                       tmm.ProjectedTotalMemberMonths
                                                                   ),
                'PDQualityAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                      SUM(ACM.TotalPDQualityAdmin),
                                                                      tmm.ProjectedTotalMemberMonths
                                                                  ),
                'PDTaxesAndFeesAdminPMPM' = dbo.fnGetSafeDivisionResult(
                                                                           SUM(ACM.TotalPDTaxesAndFeesAdmin),
                                                                           tmm.ProjectedTotalMemberMonths
                                                                       ),
				acm.CPS,
				acm.Region,
				acm.Division
            INTO #CalcPlanAdminBlendTemp
            FROM #acm acm
                JOIN #PTMM tmm
                    ON acm.forecastid = tmm.forecastid
            GROUP BY acm.planyearid,
                     acm.ForecastID,
                     acm.OrganizationCode,
                     acm.ContractNumber,
                     ProjectedTotalMemberMonths,
					 acm.CPS,
					 acm.Region,
					 acm.Division

            DROP TABLE IF EXISTS #LkpLegalEntity
            --LE Lookup
            SELECT forecastid,
                   Adm.contractnumber,
                   Adm.OrganizationCode,
                   ORG.OrganizationName
            INTO #LkpLegalEntity
            FROM #CalcPlanAdminBlendTemp Adm
                JOIN PerExtContractNumberHeader CNH WITH (NOLOCK)
                    ON Adm.contractnumber = CNH.ContractNumber
                       AND Adm.PlanYearID = cnh.PlanYearID
                LEFT JOIN dbo.lkporganization ORG WITH (NOLOCK)
                    ON ADM.OrganizationCode = ORG.OrganizationCode
            WHERE CNH.OrganizationName = ORG.OrganizationName

            --Validation -- ensure the legal entity org exists.
            DECLARE @ErrorMSG VARCHAR(MAX);
            DECLARE @List VARCHAR(MAX) = NULL;
            SELECT @List = STRING_AGG(cast(forecastid AS VARCHAR(MAX)), ',')
            FROM #LkpLegalEntity
            WHERE OrganizationName IS NULL
            IF @List IS NOT NULL
            BEGIN
                SET @ErrorMSG
                    = 'Review org names on LkpIntOrganization and PerExtContractNumberHeader. Organization not found for the primary legal entity for the following forecastid: '
                      + @List;
                RAISERROR(@ErrorMSG, 16, 1);
            END

            DROP TABLE IF EXISTS #LegalEntityValue
            --Only LegalEntity matching Org
            select planyearid,
                   adm.forecastid,
                   adm.OrganizationCode,
                   sum(MAMarketingAdminPMPM + MADirectAdminPMPM + MAIndirectAdminPMPM + MAQualityAdminPMPM
                       + MATaxesAndFeesAdminPMPM
                      ) MARPCoreNBELegalEntity,
                   sum(PDMarketingAdminPMPM + PDDirectAdminPMPM + PDIndirectAdminPMPM + PDQualityAdminPMPM
                       + PDTaxesAndFeesAdminPMPM
                      ) PDRPCoreNBELegalEntity
            INTO #LegalEntityValue
            from #CalcPlanAdminBlendTemp ADM
                JOIN #LkpLegalEntity LE
                    ON ADM.ForecastID = LE.ForecastID
                       AND adm.OrganizationCode = LE.OrganizationCode
            group by planyearid,
                     adm.forecastid,
                     adm.OrganizationCode

            --aggregated table
            DROP TABLE IF EXISTS #aggCalcPlanAdminBlendTemp
            select ADM.PlanYearID,
                   ADM.ForecastID,
				   ADM.CPS,
				   ADM.Region,
				   ADM.Division,
                   sum(MAMarketingAdminPMPM) MAMarketingAdminPMPM,
                   sum(MADirectAdminPMPM) MADirectAdminPMPM,
                   sum(MAIndirectAdminPMPM) MAIndirectAdminPMPM,
                   sum(MAQualityAdminPMPM) MAQualityAdminPMPM,
                   sum(MATaxesAndFeesAdminPMPM) MATaxesAndFeesAdminPMPM,
                   sum(PDMarketingAdminPMPM) PDMarketingAdminPMPM,
                   sum(PDDirectAdminPMPM) PDDirectAdminPMPM,
                   sum(PDIndirectAdminPMPM) PDIndirectAdminPMPM,
                   sum(PDQualityAdminPMPM) PDQualityAdminPMPM,
                   sum(PDTaxesAndFeesAdminPMPM) PDTaxesAndFeesAdminPMPM
            INTO #aggCalcPlanAdminBlendTemp
            FROM #CalcPlanAdminBlendTemp ADM
            GROUP BY ADM.planyearid,
                     ADM.forecastid,
					 ADM.CPS,
					 ADM.Region,
					 ADM.Division

            --Final
            DROP TABLE IF EXISTS #FinalCalcPlanAdminBlend
            SELECT agg.*,
                   MARelatedPartyCoreNBEPMPM = MAMarketingAdminPMPM + MADirectAdminPMPM + MAIndirectAdminPMPM
                                               + MAQualityAdminPMPM + MATaxesAndFeesAdminPMPM - MARPCoreNBELegalEntity,
                   PDRelatedPartyCoreNBEPMPM = PDMarketingAdminPMPM + PDDirectAdminPMPM + PDIndirectAdminPMPM
                                               + PDQualityAdminPMPM + PDTaxesAndFeesAdminPMPM - PDRPCoreNBELegalEntity
            INTO #FinalCalcPlanAdminBlend
            FROM #aggCalcPlanAdminBlendTemp agg
                LEFT JOIN #LegalEntityValue lev
                    ON agg.forecastid = lev.ForecastID

			IF @IsValidation = 1
			BEGIN

			DROP TABLE IF EXISTS #CalcPlanAdminBlendTempValidate
			SELECT SPH.PlanYearID,
				CPA.ForecastID, 
				SPH.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID as CPS,
				CPA.MAMarketingAdminPMPM,
				CPA.MADirectAdminPMPM,
				CPA.MAIndirectAdminPMPM,
				CPA.MAQualityAdminPMPM,
				CPA.MATaxesAndFeesAdminPMPM,
				CPA.PDMarketingAdminPMPM,
				CPA.PDDirectAdminPMPM,
				CPA.PDIndirectAdminPMPM,
				CPA.PDQualityAdminPMPM,
				CPA.PDTaxesAndFeesAdminPMPM,
				CPA.MARelatedPartyCoreNBEPMPM,
				CPA.PDRelatedPartyCoreNBEPMPM
			INTO #CalcPlanAdminBlendTempValidate
			FROM dbo.CalcPlanAdminBlend CPA 
			JOIN #TempBulkImportPlanAdminForecastIDTable F
			ON cpa.ForecastID=f.ForeCastID
			JOIN SavedPlanHeader sph
			ON f.ForeCastID=sph.ForecastID


			DROP TABLE IF EXISTS #FinalCalcPlanAdminBlendValidate
			SELECT PlanYearID,
				ForecastID,
				CPS,
				CAST(MAMarketingAdminPMPM as decimal(12,6)) as MAMarketingAdminPMPM,
				CAST(MADirectAdminPMPM as decimal(12,6)) as MADirectAdminPMPM,
				CAST(MAIndirectAdminPMPM as decimal(12,6)) as MAIndirectAdminPMPM,
				CAST(MAQualityAdminPMPM as decimal(12,6)) as MAQualityAdminPMPM,
				CAST(MATaxesAndFeesAdminPMPM as decimal(12,6)) as MATaxesAndFeesAdminPMPM,
				CAST(PDMarketingAdminPMPM as decimal(12,6)) as PDMarketingAdminPMPM,
				CAST(PDDirectAdminPMPM as decimal(12,6)) as PDDirectAdminPMPM,
				CAST(PDIndirectAdminPMPM as decimal(12,6)) as PDIndirectAdminPMPM,
				CAST(PDQualityAdminPMPM as decimal(12,6)) as PDQualityAdminPMPM,
				CAST(PDTaxesAndFeesAdminPMPM as decimal(12,6)) as PDTaxesAndFeesAdminPMPM,
				CAST(MARelatedPartyCoreNBEPMPM as decimal(12,6)) as MARelatedPartyCoreNBEPMPM,
				CAST(PDRelatedPartyCoreNBEPMPM as decimal(12,6)) as PDRelatedPartyCoreNBEPMPM
			INTO #FinalCalcPlanAdminBlendValidate
			FROM #FinalCalcPlanAdminBlend

			-- Returns data from CalcPlanAdminBlend that are differ from and not in #FinalCalcPlanAdminBlend
			SELECT * FROM #CalcPlanAdminBlendTempValidate
			EXCEPT
			SELECT * FROM #FinalCalcPlanAdminBlendValidate

			UNION ALL

			-- Returns data from #FinalCalcPlanAdminBlend that are not in CalcPlanAdminBlend
			SELECT * FROM #FinalCalcPlanAdminBlendValidate
			WHERE NOT EXISTS (SELECT * FROM #CalcPlanAdminBlendTempValidate)

			END

			ELSE IF @IsSync = 1
			BEGIN

			MERGE dbo.CalcPlanAdminBlend AS trgt
			USING (
					SELECT ForeCastID,
					PlanYearID,
					CAST(MAMarketingAdminPMPM as decimal(12,6)) as MAMarketingAdminPMPM,
					CAST(MADirectAdminPMPM as decimal(12,6)) as MADirectAdminPMPM,
					CAST(MAIndirectAdminPMPM as decimal(12,6)) as MAIndirectAdminPMPM,
					CAST(MAQualityAdminPMPM as decimal(12,6)) as MAQualityAdminPMPM,
					CAST(MATaxesAndFeesAdminPMPM as decimal(12,6)) as MATaxesAndFeesAdminPMPM,
					CAST(PDMarketingAdminPMPM as decimal(12,6)) as PDMarketingAdminPMPM,
					CAST(PDDirectAdminPMPM as decimal(12,6)) as PDDirectAdminPMPM,
					CAST(PDIndirectAdminPMPM as decimal(12,6)) as PDIndirectAdminPMPM,
					CAST(PDQualityAdminPMPM as decimal(12,6)) as PDQualityAdminPMPM,
					CAST(PDTaxesAndFeesAdminPMPM as decimal(12,6)) as PDTaxesAndFeesAdminPMPM,
					CAST(MARelatedPartyCoreNBEPMPM as decimal(12,6)) as MARelatedPartyCoreNBEPMPM,
					CAST(PDRelatedPartyCoreNBEPMPM as decimal(12,6)) as PDRelatedPartyCoreNBEPMPM				
					FROM #FinalCalcPlanAdminBlend ) AS src
					ON trgt.ForeCastID = src.ForeCastID					
					AND trgt.ForeCastID in (SELECT VALUE FROM dbo.fnStringSplit(@ForecastIDVal,','))

			WHEN MATCHED
				THEN 
					UPDATE 
					SET trgt.MAMarketingAdminPMPM = src.MAMarketingAdminPMPM,
					trgt.MADirectAdminPMPM = src.MADirectAdminPMPM,
					trgt.MAIndirectAdminPMPM = src.MAIndirectAdminPMPM,
					trgt.MAQualityAdminPMPM = src.MAQualityAdminPMPM,
					trgt.MATaxesAndFeesAdminPMPM = src.MATaxesAndFeesAdminPMPM,
					trgt.MARelatedPartyCoreNBEPMPM = src.MARelatedPartyCoreNBEPMPM,
					trgt.PDMarketingAdminPMPM = src.PDMarketingAdminPMPM,
					trgt.PDDirectAdminPMPM = src.PDDirectAdminPMPM,
					trgt.PDIndirectAdminPMPM = src.PDIndirectAdminPMPM,
					trgt.PDQualityAdminPMPM = src.PDQualityAdminPMPM,
					trgt.PDTaxesAndFeesAdminPMPM = src.PDTaxesAndFeesAdminPMPM,
					trgt.PDRelatedPartyCoreNBEPMPM = src.PDRelatedPartyCoreNBEPMPM,
					trgt.LastUpdateByID = @userid,
					trgt.LastUpdateDateTime = @LastDateTime

			WHEN NOT MATCHED
				THEN
					INSERT(
						[PlanYearID],
						[ForecastID],
						[MAMarketingAdminPMPM],
						[MADirectAdminPMPM],
						[MAIndirectAdminPMPM],
						[MAQualityAdminPMPM],
						[MATaxesAndFeesAdminPMPM],
						[PDMarketingAdminPMPM],
						[PDDirectAdminPMPM],
						[PDIndirectAdminPMPM],
						[PDQualityAdminPMPM],
						[PDTaxesAndFeesAdminPMPM],
						[LastUpdateByID],
						[LastUpdateDateTime],
						[MARelatedPartyCoreNBEPMPM],
						[PDRelatedPartyCoreNBEPMPM]
					)
					VALUES(
						[PlanYearID],
						[ForecastID],
						[MAMarketingAdminPMPM],
						[MADirectAdminPMPM],
						[MAIndirectAdminPMPM],
						[MAQualityAdminPMPM],
						[MATaxesAndFeesAdminPMPM],
						[PDMarketingAdminPMPM],
						[PDDirectAdminPMPM],
						[PDIndirectAdminPMPM],
						[PDQualityAdminPMPM],
						[PDTaxesAndFeesAdminPMPM],
						@userid,
						@LastDateTime,
						[MARelatedPartyCoreNBEPMPM],
						[PDRelatedPartyCoreNBEPMPM]
					);

			--Set Reprice flag for the ForecastIDs that are Synced.
			UPDATE dbo.SavedForecastSetup
			SET IsToReprice = 1 WHERE ForecastID IN (SELECT VALUE FROM dbo.fnStringSplit(@ForecastIDVal,','))

			END

			ELSE			
			BEGIN
				-- Populate table for unadjusted values
				DELETE FROM CalcPlanAdminBlend WITH (ROWLOCK)
				WHERE ForecastID IN (SELECT a.ForecastID FROM #FinalCalcPlanAdminBlend a) AND PlanYearID = @PlanYearID
				INSERT INTO CalcPlanAdminBlend WITH (ROWLOCK)
				(
					PlanYearID,
					ForecastID,
					MAMarketingAdminPMPM,
					MADirectAdminPMPM,
					MAIndirectAdminPMPM,
					MAQualityAdminPMPM,
					MATaxesAndFeesAdminPMPM,
					MARelatedPartyCoreNBEPMPM,
					PDMarketingAdminPMPM,
					PDDirectAdminPMPM,
					PDIndirectAdminPMPM,
					PDQualityAdminPMPM,
					PDTaxesAndFeesAdminPMPM,
					PDRelatedPartyCoreNBEPMPM,
					LastUpdateByID,
					LastUpdateDateTime
				)
				SELECT PlanYearID,
					   ForecastID,
					   MAMarketingAdminPMPM,
					   MADirectAdminPMPM,
					   MAIndirectAdminPMPM,
					   MAQualityAdminPMPM,
					   MATaxesAndFeesAdminPMPM,
					   MARelatedPartyCoreNBEPMPM,
					   PDMarketingAdminPMPM,
					   PDDirectAdminPMPM,
					   PDIndirectAdminPMPM,
					   PDQualityAdminPMPM,
					   PDTaxesAndFeesAdminPMPM,
					   PDRelatedPartyCoreNBEPMPM,
					   @UserID AS LastUpdateByID,
					   @LastDateTime AS LastUpdateDateTime
				FROM #FinalCalcPlanAdminBlend
			END


				--SELECT * from CalcPlanAdminBlend CP LEFT JOIN #FinalCalcPlanAdminBlend ON CP.
            ---------------------------------------------------------------------------------------------------------------------
            -- Update the return status if an error occurred.
            SET @LocalError = @@ERROR
            IF @ReturnStatus = 0
                SET @ReturnStatus = @LocalError
        END

        IF @ReturnStatus != 0
            ROLLBACK TRANSACTION AppendData
        ELSE
            COMMIT TRANSACTION AppendData

        RETURN 0
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION AppendData;
        ---Insert into app log for logging error Start------------------  
        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;
        DECLARE @ErrorException NVARCHAR(4000);
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
                @currentdate DATETIME = GETDATE();

        SELECT @ErrorMessage = ERROR_MESSAGE(),
               @ErrorSeverity = ERROR_SEVERITY(),
               @ErrorState = ERROR_STATE(),
               @ErrorException
                   = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);
        EXEC [dbo].[spAppAddLogEntry] @currentdate,
                                      '',
                                      'ERROR',
                                      @errSrc,
                                      @ErrorMessage,
                                      @ErrorException,
                                      @UserID;
    ---Insert into app log for logging error End------------------ 
    END CATCH;
END;
GO
