SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spTargetProfitPMPMReprice
--
-- CREATOR:			<PERSON>cha
--
-- CREATED DATE:	2024-DEC-24
--
-- DESCRIPTION:		Allows user to reprice by targeting profit pmpm or keeping the current profit percent if no target is specified.
--		
-- PARAMETERS:
--  Input  :		@ForecastID        INT
--					@UserID            VARCHAR(7)
--					@TargetProfitPMPM     DECIMAL(14, 8)
--
--  Output :		@ValidationMessage VARCHAR(MAX)
--
-- TABLES : 
--	Read :			SavedPlanAssumptions
--
--  Write:			CalcFinalPremium
--					SavedPlanAssumptions
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnAppGetBidSummary
--					fnGetMARebateAllocation
--					fnCalcProfitPctAndMbrPremFromTargetProfitPMPM
--					fnAppGetRevenueAndPremium
--
-- STORED PROCS:	spPlanRefresh
--					spUpdateOrInsertExpenseProfit
--					spUpdateOrInsertMAReportPlanLevel
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2024-DEC-24      1       Initial Version                                                         Alex Beruscha

-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spTargetProfitPMPMReprice]
    (
    @ForecastID        INT
   ,@UserID            VARCHAR(7)
   ,@ValidationMessage VARCHAR(MAX) OUT
   ,@TargetProfitPMPM     DECIMAL(14,8))
AS
BEGIN

	SET NOCOUNT ON;
	SET ANSI_WARNINGS OFF;

    --Internal parameters
    DECLARE @XForecastID    INT           = @ForecastID
           ,@XUserID        VARCHAR(7)    = @UserID
           ,@XTargetProfitPMPM DECIMAL(14,8) = @TargetProfitPMPM;

    DECLARE @ProfitPct DEC(10, 8);
    DECLARE @IsMAPD BIT;

    BEGIN TRY
        IF @XTargetProfitPMPM IS NULL --No target profit pmpm was provided, use the current profit pmpm if one exists; First checking to ensure a plan has premium and rebates
            BEGIN
                DECLARE @result_fnAppGetBidSummary TABLE
                    (BasicMemberPremium FLOAT
                    ,RxBasicPremium     DECIMAL(9, 2)
                    ,RxSuppPremium      DECIMAL(9, 2)
                    ,CS                 DECIMAL(10, 2)
                    ,Sup_Prem           DECIMAL(10, 2));
                INSERT  @result_fnAppGetBidSummary
                SELECT  BasicMemberPremium
                       ,RxBasicPremium
                       ,RxSuppPremium
                       ,CS
                       ,Sup_Prem
                FROM    dbo.fnAppGetBidSummary (@XForecastID);

                DECLARE @result_fnGetMARebateAllocation TABLE
                    (RedABCostShare  DECIMAL(9, 2)
                    ,SuppPremBuydown DECIMAL(9, 2)
                    ,RxBasicBuyDown  DECIMAL(8, 1)
                    ,RxSuppBuyDown   DECIMAL(8, 1));
                INSERT  @result_fnGetMARebateAllocation
                SELECT  RedABCostShare
                       ,SuppPremBuydown 
                       ,RxBasicBuyDown
                       ,RxSuppBuyDown
                FROM    dbo.fnGetMARebateAllocation (@XForecastID);

                --Make sure the plan exists in the two functions first, if not then error out
                IF EXISTS (SELECT   1 FROM  @result_fnAppGetBidSummary)
                   AND EXISTS (SELECT  1 FROM  @result_fnGetMARebateAllocation)
                    BEGIN
                        SELECT  @XTargetProfitPMPM = Profit
                        FROM    dbo.CalcFinalPremium
						WHERE	ForecastID = @XForecastID;
                    END;
                ELSE 
                    BEGIN
                        RAISERROR ('Cannot determine target profit pmpm.', 16, 1);
                    END;
            END;

        --Now that we have a target profit PMPM, reprice the plan normally
        EXEC dbo.spPlanRefresh @XForecastID
                              ,@XUserID
                              ,@ValidationMessage = @ValidationMessage OUTPUT;

        IF @ValidationMessage = '' --Continue if there are no problems repricing the plan
            BEGIN

                -- Calculate and pull values needed to update CalcFinalPremium and SavedPlanAssumptions  
                DECLARE @result_fnCalcProfitPctAndMemberPremiumFromTargetProfitPMPM TABLE
                    (Profit             DECIMAL(14, 8)
                    ,ProfitPercentage   DECIMAL(10, 8)
                    ,Expenses           DECIMAL(14, 8)
                    ,TotalReqRev        DECIMAL(14, 8)
                    ,PlanBid            DECIMAL(14, 8)
                    ,StandardizedBid    DECIMAL(14, 8)
                    ,Savings            DECIMAL(14, 8)
                    ,Rebate             DECIMAL(14, 8)
                    ,BasicMemberPremium DECIMAL(14, 8)
                    ,GovtPremiumAdj     DECIMAL(14, 8)
                    ,UncollectedPremium DECIMAL(14, 8)
                    ,InsurerFee         DECIMAL(14, 8));
                INSERT INTO @result_fnCalcProfitPctAndMemberPremiumFromTargetProfitPMPM
                SELECT  Profit
                       ,ProfitPercentage
                       ,Expenses
                       ,TotalReqRev
                       ,PlanBid
                       ,StandardizedBid
                       ,Savings
                       ,Rebate
                       ,BasicMemberPremium
                       ,GovtPremiumAdj
                       ,UncollectedPremium
                       ,InsurerFee
                FROM    dbo.fnCalcProfitPctAndMbrPremFromTargetProfitPMPM (@XForecastID, @XTargetProfitPMPM, @XUserID);

                --Get other values needed to update profit percentage in SavedPlanAssumptions
                SELECT  @IsMAPD = IsMAPD
                FROM    dbo.SavedPlanAssumptions WITH (NOLOCK)
                WHERE   ForecastID = @XForecastID;

                --Load ProfitPct into SavedPlanAssumptions
                SET @ProfitPct = (SELECT    ProfitPercentage FROM  @result_fnCalcProfitPctAndMemberPremiumFromTargetProfitPMPM);
                EXEC dbo.spUpdateOrInsertExpenseProfit @XForecastID, @IsMAPD, @ProfitPct;

                --Write final values to CalcFinalPremium
                UPDATE  dbo.CalcFinalPremium
                SET     Profit = fcp.Profit
                       ,Expenses = fcp.Expenses
                       ,TotalReqRev = fcp.TotalReqRev
                       ,PlanBid = fcp.PlanBid
                       ,StandardizedBid = fcp.StandardizedBid
                       ,Savings = fcp.Savings
                       ,Rebate = fcp.Rebate
                       ,BasicMemberPremium = fcp.BasicMemberPremium
                       ,GovtPremiumAdj = fcp.GovtPremiumAdj
                       ,UncollectedPremium = fcp.UncollectedPremium
                       ,InsurerFee = fcp.InsurerFee
                       ,LastUpdateByID = @XUserID
                       ,LastUpdateDateTime = GETDATE ()
                FROM    @result_fnCalcProfitPctAndMemberPremiumFromTargetProfitPMPM fcp
                WHERE   ForecastID = @XForecastID;

                --Final step in plan reprice
                EXEC dbo.spUpdateOrInsertMAReportPlanLevel @XForecastID, @XUserID;

                --Write TotalMemberPremium and RoundedMemberPrem to CalcFinalPremium table
                UPDATE  dbo.CalcFinalPremium
                SET     TotalMemberPremium = (SELECT    TotalMemberPremium FROM fnAppGetRevenueAndPremium (@XForecastID) )
                       ,RoundedMemberPrem = (SELECT RoundedMemberPrem FROM  fnAppGetRevenueAndPremium (@XForecastID) )
                WHERE   CalcFinalPremium.ForecastID = @XForecastID;

            END;

        ELSE --Error Trapping
            BEGIN
                RAISERROR (@ValidationMessage, 16, 1);
            END;

        --All code executed properly, return 0 to indicate success
        SET @ValidationMessage = '0';
    END TRY

    -- In the event of an error, we return the error details to the client
    BEGIN CATCH
        SELECT  @ValidationMessage = ERROR_MESSAGE ();
    END CATCH;
END;
GO
