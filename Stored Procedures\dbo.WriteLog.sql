SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
CREATE PROCEDURE [dbo].[WriteLog]  
	(  
	 @EventID int,   
	 @Priority int,   
	 @Severity nvarchar(32),   
	 @Title nvarchar(256),   
	 @Timestamp datetime,  
	 @MachineName nvarchar(32),   
	 @AppDomainName nvarchar(512),  
	 @ProcessID nvarchar(256),  
	 @ProcessName nvarchar(512),  
	 @ThreadName nvarchar(512),  
	 @Win32ThreadId nvarchar(128),  
	 @Message nvarchar(1500),  
	 @FormattedMessage ntext,  
	 @LogId int OUTPUT  
	)  
	AS   

	 INSERT INTO [Log] (  
	  EventID,  
	  Priority,  
	  Severity,  
	  Title,  
	  [Timestamp],  
	  MachineName,  
	  AppDomainName,  
	  ProcessID,  
	  ProcessName,  
	  ThreadName,  
	  Win32ThreadId,  
	  Message,  
	  FormattedMessage  
	 )  
	 VALUES (  
	  @EventID,   
	  @Priority,   
	  @Severity,   
	  @Title,   
	  @Timestamp,  
	  @MachineName,   
	  @AppDomainName,  
	  @ProcessID,  
	  @ProcessName,  
	  @ThreadName,  
	  @Win32ThreadId,  
	  @Message,  
	  @FormattedMessage)  

	 SET @LogID = @@IDENTITY  

	 DECLARE @EmailSubject VARCHAR(255)  
	 SET @EmailSubject = 'MA Bid Model Exception: ' + @Severity  

	   DECLARE @str varchar(max)

	SELECT @str= coalesce(@str + '; ', '') + a.UserEmail 
	FROM (SELECT DISTINCT UserEmail from AdminUserHeader where RoleID=0 ) a
	 DECLARE @EmailTo VARCHAR(max)  

	 SET @EmailTo = @str


	 -- Send
	 EXEC msdb.dbo.sp_send_dbmail  
	  @recipients=@EmailTo,  
	  @subject=@EmailSubject,  
	  @importance=N'High',  
	  @body=@FormattedMessage  

	 RETURN @LogID
GO
