SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO
	 
-- =============================================   
-- PROCEDURE NAME: dbo.spBatchTargetInitialFormPopulation
-- Author:  <PERSON><PERSON><PERSON><PERSON>   
-- Create date: 10-06-2021
-- Description: Get BTW Data for initial Form population
--      
--      
-- PARAMETERS:      
-- Input: requestTable (1 - Target Profit Pct, 2 - Target MER, 3 - Target Member Premium , 4 - Target Profit PMPM)
-- Input: ForecastID list; Comma separated list of ForecastIDs from MAAUI filtering
-- Input: LastUpdateByID       

-- TABLES:      
-- Read: SavedForecastSetup 
--	     SavedPlanInfo
--	     SavedMarketInfo
--	     SavedRegionInfo
--	     SavedDivisionInfo
--	     MAReportPlanLevel
--	     PlanChangeLog
--	     LkpMAPlanDesign
--	     LkpSNPType
--	     PlanRepriceLog
--	     CalcFinalPremium
--       LkpLowIncomeBenchmark
--       LkpStateTerritory
--       SavedPlanAssumptions
--       SavedPlanStateCountyDetail
--       SavedTargetingInputs

-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS: fnGetBidYear
--			  fnGetSafeDivisionResult
--			  fnStringSplit
--			  fnAppGetRevenueAndPremium
--        
-- STORED PROCS:        

-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION     CHANGES MADE                                                            DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2021-Jun-10  1		    Initial version.											            Umaprasad shetpally
-- 2021-Jun-28  2		    Revise to have ForecastID focus							                Bob Knadler
-- 2021-Aug-05  3           Revise to use SavedTargetingInputs table  Recognize Proc                Bob Knadler
--						    25 in reprice log as last proc in spPlanRefresh, not 23
-- 2021-Sep-27  4		    Original value field added								                Surya Murthy
-- 2021-Nov-30  5           Default Target Member Premium for DSNP and VPP                          Umaprasad shetpally
-- 2021-Dec-03  5           Fixed difference issue                                                  Ramandeep Saini
-- 2022-Feb-09  6           Difference/calculation reverted                                         Umaprasad shetpally
-- 2022-Feb-16  7		    Comment out "AND sti.UsedInTargetingDateTime IS NULL for	            Siliang Hu
--						    default target value
-- 2022-Oct-6   8           Add Test for LISPA in Target Member Premium if lowincome then        
--						    LkpLowIncomeBenchmark value else default targetvalue                    Latoya Garvey 
-- 2022-Oct-22  9		    Changed ProcNumber 25 to 22								                Shivgopal
-- 2022-Oct-23  10		    Change													                Shivgopal
-- 2022-Dec-23  11          Calculate the difference for all Tables 
--                          Change the Target Member calculations                                   Latoya Garvey
-- 2022-Feb-15  12	        Handle negative value for Target Member Premium			                Shivgopal
-- 2023-Feb-28  13          Change the Target Mer Difference to display as                          Latoya Garvey
--                          percent 5 decimal places
-- 2023-Apr-24  14          updated [TargetMemberPremium] output field to                           Vikrant Bagal
--                          use MAReportPlanLevel.MemberPremiumRounded instead of 
--                          SavedTargetingInputs.TargetInputValue
-- 2024-Feb-29	15		    MPV>Target Mem Premium. Move formatting to final select. 
--						    UI should be reworked to handle formatting instead of SP.	            Adam Gilbert
-- 2024-June-23	16		    Added PlanIntentionID	in select query						            Archana Sahu
-- 2024-Nov-11  17          Change the [TargetMemberPremium] output field to        
--                          use SavedTargetingInputs.TargetInputValue instead of                    Latoya Garvey
--                          MAReportPlanLevel.MemberPremiumRounded 
-- 2024-No-13   18          Added SpaProfitPercent field and roundiin off calclations to            Kiran Kola
--                          6 digits
-- 2025-Jan-14	19		    Add fourth request table for Target Profit PMPM				            Michael Manes
-- 2025-Jan-22	20		    if SNPT plans for Target Member Premium   	                            Vikrant Bagal
-- 						    if TMP value is -ve then return 0 	
-- 2025-Apr-14	21		    5 digits after decimals for TargetProfitPMPM				            Vikrant Bagal
-- ---------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[spBatchTargetInitialFormPopulation]
    (
    @requestTable   INT
   ,@ForecastIDList VARCHAR(MAX)
   ,@LastUpdateByID CHAR(7))
AS


    ------DECLARE	@requestTable INT = 2		--1=Target Profit%, 3=Target Member Premium, 2=Target MER, 4=Target Profit PMPM
    ------DECLARE	@ForecastIDList VARCHAR(MAX) = 'H1468-007-000,H1468-013-000,H2944-114-000,H4461-004-000,H4461-022-000,H4461-025-000,H4461-029-000,H4461-030-001,H4461-030-002,H4461-030-003,H4461-031-001,H4461-031-002,H4461-031-003,H4461-034-000,H4461-035-000,H4461-036-000,H5216-093-000,H5216-096-000,H5216-097-000,H5216-099-000,H5216-212-000,H5216-214-001,H5525-020-000,H5619-088-000,H5619-089-000,H5619-090-000,H5619-091-000,H5619-093-000,H5619-094-000,H5619-095-000,R7315-001-000,R7315-002-000'
    ------DECLARE	@LastUpdateByID CHAR(7) = 'RJK2133'


    BEGIN
        SET NOCOUNT ON;
        SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
        BEGIN TRANSACTION;
        BEGIN TRY

            DECLARE @FalseVariable NVARCHAR(10);
            SET @FalseVariable = N'FALSE';

            IF OBJECT_ID (N'tempdb ..#BTWPlanlist') IS NOT NULL DROP TABLE #BTWPlanlist;
            SELECT  Value Forecastid
            INTO    #BTWPlanlist
            FROM    dbo.fnStringSplit (@ForecastIDList, ',');

            IF OBJECT_ID (N'tempdb ..#BTWsfslist') IS NOT NULL DROP TABLE #BTWsfslist;
            SELECT      f.ForecastID
            INTO        #BTWsfslist
            FROM        dbo.SavedPlanInfo (NOLOCK) p
           INNER JOIN   dbo.SavedForecastSetup (NOLOCK) f
                   ON f.PlanInfoID = p.PlanInfoID
            WHERE       CPS IN (SELECT  * FROM  #BTWPlanlist);

/************************
--Batch Member MER - requestTable = 2
******************************/
            IF @requestTable = 2 --Batch Member MER
                BEGIN
                    SET NOCOUNT ON;
                    PRINT @requestTable;
                    ;WITH TargetMer
                    AS (SELECT      CONCAT (spi.PlanYear, ':', spi.CPS, ':', sfs.ScenarioNbr) ScenarioTag
                                   ,smi.ActuarialMarket
                                   ,lmapd.MAPlanDesign
                                   ,lsnpt.SNPType
                                   ,CASE WHEN sfs.IsRiskPlan = 0 THEN @FalseVariable ELSE 'TRUE' END AS RiskPlan
                                   ,CASE WHEN sfs.IsToReprice = 0 THEN @FalseVariable ELSE 'TRUE' END RepriceRequired
                                   ,CAST(sfs.ForecastID AS VARCHAR(10)) ForecastID
                                   ,ISNULL (CASE WHEN sti.TargetingInputTypeID = 3
                                                      AND   sti.TargetInputValue IS NOT NULL
                                                      AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                                 ELSE ISNULL (ma.[Current MER], 0.000) END
                                           ,0.000) AS TargetMER
                                   ,CASE WHEN sti.TargetingInputTypeID = 3
                                              AND   sti.TargetInputValue IS NOT NULL
                                              AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                         ELSE ISNULL (ma.[Current MER], 0.000) END OriginalMerValue
                                   ,ISNULL (ma.[Current MER], 0.000) CurrentMER
                                   ,CASE WHEN sti.TargetingInputTypeID = 3
                                              AND   sti.TargetInputValue IS NOT NULL
                                              AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                         ELSE ISNULL (ma.[Current MER], 0.000) END - ISNULL (ma.[Current MER], 0.000)

                                   [Difference]
                                   ,'' [Time]
                                   ,CONVERT (VARCHAR(10), pcl.[Last Target MER], 101)

                                    + RIGHT(CONVERT (VARCHAR(32), pcl.[Last Target MER], 100), 8) LastTargetMER
                                   ,sfs.PlanIntentionID
                        FROM        dbo.SavedForecastSetup (NOLOCK) sfs
                       INNER JOIN   #BTWsfslist
                               ON #BTWsfslist.ForecastID = sfs.ForecastID
                       INNER JOIN   dbo.SavedPlanInfo (NOLOCK) spi
                               ON sfs.PlanInfoID = spi.PlanInfoID
                       INNER JOIN   dbo.SavedMarketInfo (NOLOCK) smi
                               ON spi.ActuarialMarketID = smi.ActuarialMarketID
                       INNER JOIN   dbo.SavedRegionInfo (NOLOCK) sri
                               ON smi.ActuarialRegionID = sri.ActuarialRegionID
                       INNER JOIN   dbo.SavedDivisionInfo (NOLOCK) sdi
                               ON sri.ActuarialDivisionID = sdi.ActuarialDivisionID
                       INNER JOIN   dbo.LkpMAPlanDesign (NOLOCK) lmapd
                               ON spi.MAPlanDesignID = lmapd.MAPlanDesignID
                       INNER JOIN   dbo.LkpSNPType (NOLOCK) lsnpt
                               ON spi.SNPTypeID = lsnpt.SNPTypeID
                        LEFT JOIN   (SELECT     ForecastID
                                               ,MemberPremiumRounded
                                               ,(NetNonESRD / TotalReqRev) AS [Current MER]
                                               ,RebateUnspent
                                     FROM       dbo.MAReportPlanLevel
                                     WHERE      PlanYearID = dbo.fnGetBidYear ()
                                                AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                     GROUP BY   ForecastID
                                               ,MemberPremiumRounded
                                               ,NetNonESRD / TotalReqRev
                                               ,RebateUnspent) ma
                               ON ma.ForecastID = sfs.ForecastID
                        LEFT JOIN   (SELECT     ForecastID
                                               ,MAX (AuditTime) AS [Last Target MER]
                                     FROM       dbo.PlanChangeLog (NOLOCK)
                                     WHERE      ProcName = 'MER'
                                                AND AuditTime >= CAST('2/1/' + CAST(dbo.fnGetBidYear () - 1 AS CHAR(4)) AS DATETIME) -- From FLP on
                                                AND Value <> 1 -- Actually targeted with resulting adjustment factor not equal to one
                                                AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                     GROUP BY   ForecastID) pcl
                               ON pcl.ForecastID = sfs.ForecastID
                        LEFT JOIN   dbo.SavedTargetingInputs sti
                               ON sti.ForecastID = sfs.ForecastID
                                  AND   sti.TargetingInputTypeID = 3    -- Target MER
                        WHERE       sfs.IsLiveIndex = 1
                                    AND sfs.IsHidden = 0)
                    SELECT      ScenarioTag
                               ,ActuarialMarket
                               ,MAPlanDesign
                               ,SNPType
                               ,RiskPlan
                               ,RepriceRequired
                               ,ForecastID
                               ,FORMAT (ROUND (TargetMER, 5), 'P3') AS TargetMER
                               ,OriginalMerValue
                               ,FORMAT (ROUND (CurrentMER, 5), 'P3') AS CurrentMER
                               ,CONVERT (VARCHAR(30), [Difference]) AS [Difference]
                               ,[Time]
                               ,LastTargetMER
                               ,PlanIntentionID
                    FROM        TargetMer
                    ORDER BY    ScenarioTag;
                END;

/************************
--Batch Target Member Premium - requestTable = 3 
******************************/
            ELSE IF @requestTable = 3
                     BEGIN
                         SET NOCOUNT ON;
                         PRINT @requestTable;
                         ;WITH TargetMemberPremiumCte
                         AS (SELECT     CONCAT (spi.PlanYear, ':', spi.CPS, ':', sfs.ScenarioNbr) ScenarioTag
                                       ,CASE WHEN smi.ActuarialMarket IS NULL THEN 'Null' ELSE smi.ActuarialMarket END AS ActuarialMarket
                                       ,lmapd.MAPlanDesign
                                       ,lsnpt.SNPType
                                       ,CASE WHEN sfs.IsToReprice = 0 THEN @FalseVariable ELSE 'TRUE' END AS RepriceRequired
                                       ,CAST(sfs.ForecastID AS VARCHAR(10)) ForecastID
                                       ,

                                       ISNULL (
                                       CASE WHEN sti.TargetingInputTypeID = 2
                                                 AND sti.TargetInputValue IS NOT NULL
                                                 AND sfs.PlanIntentionID <> 2 THEN ISNULL (sti.TargetInputValue, 0.00)
                                            WHEN sfs.PlanIntentionID = 2
                                                 AND sti.TargetingInputTypeID = 2
                                                 AND (SELECT    MAX (RxBasicPremiumBuydownMax)
                                                      FROM      dbo.fnAppGetRevenueAndPremium (sfs.ForecastID) ) < (SELECT      DISTINCT TOP (1)

                                                                                                                                    IB.LIBenchmark
                                                                                                                        FROM        dbo.SavedForecastSetup A
                                                                                                                        JOIN        dbo.SavedPlanStateCountyDetail B
                                                                                                                          ON A.ForecastID = B.ForecastID
                                                                                                                        JOIN        dbo.LkpStateTerritory ST
                                                                                                                          ON ST.StateTerritoryID = B.StateTerritoryID
                                                                                                                        JOIN        dbo.LkpLowIncomeBenchmark IB
                                                                                                                          ON IB.States = ST.StateTerritoryCD
                                                                                                                        WHERE       A.ForecastID = sfs.ForecastID
                                                                                                                        ORDER BY    IB.LIBenchmark)

                                       THEN         (SELECT MAX (RxBasicPremiumBuydownMax)
                                                     FROM   dbo.fnAppGetRevenueAndPremium (sfs.ForecastID) )
                                            ELSE (SELECT    DISTINCT TOP (1)
                                                            IB.LIBenchmark
                                                  FROM      dbo.SavedForecastSetup A
                                                  JOIN      dbo.SavedPlanStateCountyDetail B
                                                    ON A.ForecastID = B.ForecastID
                                                  JOIN      dbo.LkpStateTerritory ST
                                                    ON ST.StateTerritoryID = B.StateTerritoryID
                                                  JOIN      dbo.LkpLowIncomeBenchmark IB
                                                    ON IB.States = ST.StateTerritoryCD
                                                  WHERE     A.ForecastID = sfs.ForecastID
                                                  ORDER BY  IB.LIBenchmark)

                                       END

                                      ,0.00) AS TargetMemberPremium
                                       ,

                                       CONVERT (
                                       VARCHAR(100)
                                      ,CASE WHEN sfs.PlanIntentionID <> 2
                                                 AND sti.TargetingInputTypeID = 2
                                                 AND sti.TargetInputValue IS NOT NULL
                                                 AND sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                            ELSE ISNULL (ma.MemberPremiumRounded, $0.00) END) AS OriginalValue
                                       ,ISNULL (ma.MemberPremiumRounded, 0.00) CurrentMbrPremium
                                       ,CASE WHEN sti.TargetingInputTypeID = 2
                                                  AND   sti.TargetInputValue IS NOT NULL
                                                  AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                             ELSE ISNULL (ma.MemberPremiumRounded, 0.00) END
                                        - ISNULL (ma.MemberPremiumRounded, 0.00) [Difference]
                                       ,'' [Time]
                                       ,CONVERT (VARCHAR(10), prl.[Last Reprice], 101)
                                        + RIGHT(CONVERT (VARCHAR(32), prl.[Last Reprice], 100), 8) LastReprice
                                       ,(SELECT UnallocatedRebate FROM  dbo.fnAppGetRevenueAndPremium (sfs.ForecastID) ) AS UnallocatedRebate
                                       ,sfs.PlanIntentionID
                             FROM       dbo.SavedForecastSetup (NOLOCK) sfs
                            INNER JOIN  #BTWsfslist
                                    ON #BTWsfslist.ForecastID = sfs.ForecastID
                            INNER JOIN  dbo.SavedPlanInfo (NOLOCK) spi
                                    ON sfs.PlanInfoID = spi.PlanInfoID
                            INNER JOIN  dbo.SavedMarketInfo (NOLOCK) smi
                                    ON spi.ActuarialMarketID = smi.ActuarialMarketID
                            INNER JOIN  dbo.SavedRegionInfo (NOLOCK) sri
                                    ON smi.ActuarialRegionID = sri.ActuarialRegionID
                            INNER JOIN  dbo.SavedDivisionInfo (NOLOCK) sdi
                                    ON sri.ActuarialDivisionID = sdi.ActuarialDivisionID
                            INNER JOIN  dbo.LkpMAPlanDesign (NOLOCK) lmapd
                                    ON spi.MAPlanDesignID = lmapd.MAPlanDesignID
                            INNER JOIN  dbo.LkpSNPType (NOLOCK) lsnpt
                                    ON spi.SNPTypeID = lsnpt.SNPTypeID
                             LEFT JOIN  (SELECT     ForecastID
                                                   ,MemberPremiumRounded
                                                   ,dbo.fnGetSafeDivisionResult (NetNonESRD, TotalReqRev) AS [Current MER]
                                                   ,RebateUnspent
                                         FROM       dbo.MAReportPlanLevel
                                         WHERE      PlanYearID = dbo.fnGetBidYear ()
                                                    AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                         GROUP BY   ForecastID
                                                   ,MemberPremiumRounded
                                                   ,dbo.fnGetSafeDivisionResult (NetNonESRD, TotalReqRev)
                                                   ,RebateUnspent) ma
                                    ON ma.ForecastID = sfs.ForecastID
                             LEFT JOIN  (SELECT     ForecastID
                                                   ,MAX (AuditTime) AS [Last Reprice]
                                         FROM       dbo.PlanRepriceLog (NOLOCK)
                                         WHERE      ProcNumber = 22 -- Should be last proc #25, not 23 
                                                    AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                         GROUP BY   ForecastID) prl
                                    ON prl.ForecastID = sfs.ForecastID
                             LEFT JOIN  dbo.SavedTargetingInputs (NOLOCK) sti
                                    ON sti.ForecastID = sfs.ForecastID
                                       AND  sti.TargetingInputTypeID = 2    -- Target Member Premium
                             WHERE      sfs.IsLiveIndex = 1
                                        AND sfs.IsHidden = 0)
                         /*
		2024-Feb-29 - Consider removing FORMAT from this SP. SP should return numeric values. 
		UI should format.-- TargetMemberPremium, CurrentMbrPremium, and Difference 
		UI returns JSON error if users do not remove negative formatting ($#.##)
		*/
                         SELECT     ScenarioTag
                                   ,ActuarialMarket
                                   ,MAPlanDesign
                                   ,SNPType
                                   ,RepriceRequired
                                   ,ForecastID
		                           ,FORMAT(IIF(SNPType <> 'NA' and TargetMemberPremium < 0 ,0,TargetMemberPremium),'C2') AS TargetMemberPremium --formats negatives as ($#.##)  
                                   ,OriginalValue
                                   ,FORMAT (CurrentMbrPremium, 'C2') AS CurrentMbrPremium                   --formats negatives as ($#.##)
                                   ,FORMAT(IIF(SNPType <> 'NA' and TargetMemberPremium <0 ,0,TargetMemberPremium)-CurrentMbrPremium, 'C2') AS [Difference]--formats negatives as ($#.##)                                   ,[Time]
                                   ,LastReprice
                                   ,UnallocatedRebate
                                   ,PlanIntentionID
                         FROM       TargetMemberPremiumCte
                         ORDER BY   ScenarioTag;
                     END;

/********************************
--Batch Member Target Profit - requestTable = 1 
********************************/
            ELSE IF @requestTable = 1
                     BEGIN
                         SET NOCOUNT ON;
                         PRINT @requestTable;
                         ;WITH TargetProfit
                         AS (SELECT     CONCAT (spi.PlanYear, ':', spi.CPS, ':', sfs.ScenarioNbr) ScenarioTag
                                       ,smi.ActuarialMarket
                                       ,lmapd.MAPlanDesign
                                       ,lsnpt.SNPType
                                       ,CASE WHEN sfs.IsToReprice = 0 THEN @FalseVariable ELSE 'TRUE' END RepriceRequired

                                       ,

                                       CAST(sfs.ForecastID AS VARCHAR(10)) ForecastID
                                       ,FORMAT (
                                        ISNULL (
                                        CASE WHEN sti.TargetingInputTypeID = 1
                                                  AND   sti.TargetInputValue IS NOT NULL
                                                  AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                             ELSE CASE WHEN cfp.Profit IS NULL THEN spa.ProfitPercent
                                                       ELSE dbo.fnGetSafeDivisionResult (cfp.Profit, cfp.TotalReqRev) END END
                                       ,0.00)
                                       ,'P2') AS TargetProfitPct
                                       ,CONVERT (
                                        VARCHAR(100)
                                       ,CASE WHEN sti.TargetingInputTypeID = 1
                                                  AND sti.TargetInputValue IS NOT NULL
                                                  AND sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                             ELSE CASE WHEN cfp.Profit IS NULL THEN spa.ProfitPercent
                                                       ELSE dbo.fnGetSafeDivisionResult (cfp.Profit, cfp.TotalReqRev) END END) AS OriginalValue
                                       ,FORMAT (
                                        ISNULL (
                                        CASE WHEN cfp.Profit IS NULL THEN spa.ProfitPercent
                                             ELSE dbo.fnGetSafeDivisionResult (cfp.Profit, cfp.TotalReqRev) END
                                       ,0.00)
                                       ,'P2') ProfitPercent
                                       ,FORMAT (ROUND (spa.ProfitPercent, 6), '0.000000') AS SpaProfitPercent
                                       ,FORMAT (
                                        ISNULL (
                                        CASE WHEN sti.TargetingInputTypeID = 1
                                                  AND   sti.TargetInputValue IS NOT NULL
                                                  AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                             ELSE CASE WHEN cfp.Profit IS NULL THEN spa.ProfitPercent
                                                       ELSE dbo.fnGetSafeDivisionResult (cfp.Profit, cfp.TotalReqRev) END END
                                        - CASE WHEN cfp.Profit IS NULL THEN spa.ProfitPercent
                                               ELSE dbo.fnGetSafeDivisionResult (cfp.Profit, cfp.TotalReqRev) END
                                       ,0.00)
                                       ,'P2') [Difference]
                                       ,'' [Time]
                                       ,CONVERT (VARCHAR(10), prl.[Last Reprice], 101)
                                        + RIGHT(CONVERT (VARCHAR(32), prl.[Last Reprice], 100), 8) LastReprice
                                       ,sfs.PlanIntentionID
                             FROM       dbo.SavedForecastSetup (NOLOCK) sfs
                            INNER JOIN  #BTWsfslist
                                    ON #BTWsfslist.ForecastID = sfs.ForecastID
                            INNER JOIN  dbo.SavedPlanInfo (NOLOCK) spi
                                    ON sfs.PlanInfoID = spi.PlanInfoID
                            INNER JOIN  dbo.SavedMarketInfo (NOLOCK) smi
                                    ON spi.ActuarialMarketID = smi.ActuarialMarketID
                            INNER JOIN  dbo.SavedRegionInfo (NOLOCK) sri
                                    ON smi.ActuarialRegionID = sri.ActuarialRegionID
                            INNER JOIN  dbo.SavedDivisionInfo (NOLOCK) sdi
                                    ON sri.ActuarialDivisionID = sdi.ActuarialDivisionID
                            INNER JOIN  dbo.LkpMAPlanDesign (NOLOCK) lmapd
                                    ON spi.MAPlanDesignID = lmapd.MAPlanDesignID
                            INNER JOIN  dbo.LkpSNPType (NOLOCK) lsnpt
                                    ON spi.SNPTypeID = lsnpt.SNPTypeID
                             LEFT JOIN  (SELECT     ForecastID
                                                   ,ProfitPercent
                                         FROM       dbo.SavedPlanAssumptions (NOLOCK)
                                         WHERE      PlanYearID = dbo.fnGetBidYear ()
                                                    AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                         GROUP BY   ForecastID
                                                   ,ProfitPercent) spa
                                    ON spa.ForecastID = sfs.ForecastID
                             LEFT JOIN  (SELECT     ForecastID
                                                   ,Profit
                                                   ,TotalReqRev
                                         FROM       dbo.CalcFinalPremium (NOLOCK)
                                         WHERE      PlanYearID = dbo.fnGetBidYear ()
                                                    AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                         GROUP BY   ForecastID
                                                   ,Profit
                                                   ,TotalReqRev) cfp
                                    ON cfp.ForecastID = sfs.ForecastID
                             LEFT JOIN  (SELECT     ForecastID
                                                   ,MAX (AuditTime) AS [Last Reprice]
                                         FROM       dbo.PlanRepriceLog (NOLOCK)
                                         WHERE      ProcNumber = 22 -- Should be last proc #25, not 23
                                                    AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                         GROUP BY   ForecastID) prl
                                    ON prl.ForecastID = sfs.ForecastID
                             LEFT JOIN  dbo.SavedTargetingInputs (NOLOCK) sti
                                    ON sti.ForecastID = sfs.ForecastID
                                       AND  sti.TargetingInputTypeID = 1    -- Target Profit %
                             WHERE      sfs.IsLiveIndex = 1
                                        AND sfs.IsHidden = 0)
                         SELECT     ScenarioTag
                                   ,ActuarialMarket
                                   ,MAPlanDesign
                                   ,SNPType
                                   ,RepriceRequired
                                   ,ForecastID
                                   ,TargetProfitPct
                                   ,FORMAT (ROUND (OriginalValue, 6), '0.000000') AS OriginalValue
                                   ,ProfitPercent
                                   ,SpaProfitPercent
                                   ,FORMAT (
                                    ROUND (
                                    CAST(ROUND (
                                         (CAST(OriginalValue AS DECIMAL(18, 6)) - CAST(SpaProfitPercent AS DECIMAL(18, 6)))
                                        ,3) AS DECIMAL(18, 6))
                                   ,3)
                                   ,'0.000%') AS [Difference]
                                   ,[Time]
                                   ,LastReprice
                                   ,PlanIntentionID
                         FROM       TargetProfit
                         ORDER BY   ScenarioTag;
                     END;
/********************************
--Batch Member Target Profit PMPM - requestTable = 4
********************************/
            ELSE IF @requestTable = 4
                     BEGIN
                         SET NOCOUNT ON;
                         PRINT @requestTable;
                         ;WITH TargetProfitPMPM
                         AS (SELECT     CONCAT (spi.PlanYear, ':', spi.CPS, ':', sfs.ScenarioNbr) ScenarioTag
                                       ,smi.ActuarialMarket
                                       ,lmapd.MAPlanDesign
                                       ,lsnpt.SNPType
                                       ,CASE WHEN sfs.IsToReprice = 0 THEN @FalseVariable ELSE 'TRUE' END RepriceRequired
                                       ,

                                       CAST(sfs.ForecastID AS VARCHAR(10)) ForecastID
                                       ,ISNULL (
                                        CASE WHEN sti.TargetingInputTypeID = 4
                                                  AND   sti.TargetInputValue IS NOT NULL
                                                  AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                             ELSE ISNULL (cfp.Profit, 0.000) END
                                       ,0.00) AS [TargetProfitPMPM]
                                       ,CASE WHEN sti.TargetingInputTypeID = 4
                                                  AND   sti.TargetInputValue IS NOT NULL
                                                  AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                             ELSE ISNULL (cfp.Profit, 0.000) END AS OriginalValue
                                       ,ISNULL (cfp.Profit, 0.000) CurrentProfitPMPM
                                        --FORMAT(ROUND(spa.ProfitPercent, 6), '0.000000')
                                        --      AS SpaProfitPercent,
                                       ,CASE WHEN sti.TargetingInputTypeID = 4
                                                  AND   sti.TargetInputValue IS NOT NULL
                                                  AND   sti.UsedInTargetingDateTime IS NULL THEN sti.TargetInputValue
                                             ELSE ISNULL (cfp.Profit, 0.000) END - ISNULL (cfp.Profit, 0.000) [Difference]
                                       ,ISNULL (cfp.RoundedMemberPrem, 0.00) AS MemberPremium
                                       ,(SELECT UnallocatedRebate FROM dbo.fnAppGetRevenueAndPremium (sfs.ForecastID) ) AS UnallocatedRebate
                                       ,'' [Time]
                                       ,CONVERT (VARCHAR(10), prl.[Last Reprice], 101)
                                        + RIGHT(CONVERT (VARCHAR(32), prl.[Last Reprice], 100), 8) LastReprice
                                       ,sfs.PlanIntentionID
                             FROM       dbo.SavedForecastSetup (NOLOCK) sfs
                            INNER JOIN  #BTWsfslist
                                    ON #BTWsfslist.ForecastID = sfs.ForecastID
                            INNER JOIN  dbo.SavedPlanInfo (NOLOCK) spi
                                    ON sfs.PlanInfoID = spi.PlanInfoID
                            INNER JOIN  dbo.SavedMarketInfo (NOLOCK) smi
                                    ON spi.ActuarialMarketID = smi.ActuarialMarketID
                            INNER JOIN  dbo.SavedRegionInfo (NOLOCK) sri
                                    ON smi.ActuarialRegionID = sri.ActuarialRegionID
                            INNER JOIN  dbo.SavedDivisionInfo (NOLOCK) sdi
                                    ON sri.ActuarialDivisionID = sdi.ActuarialDivisionID
                            INNER JOIN  dbo.LkpMAPlanDesign (NOLOCK) lmapd
                                    ON spi.MAPlanDesignID = lmapd.MAPlanDesignID
                            INNER JOIN  dbo.LkpSNPType (NOLOCK) lsnpt
                                    ON spi.SNPTypeID = lsnpt.SNPTypeID
                             --LEFT JOIN (SELECT ForecastID, ProfitPercent FROM dbo.SavedPlanAssumptions (NOLOCK) WHERE PlanYearID = dbo.fnGetBidYear() AND ForecastID IN (SELECT ForecastID FROM #BTWsfslist) GROUP BY ForecastID, ProfitPercent) spa
                             --  ON spa.ForecastID = sfs.ForecastID
                             LEFT JOIN  (SELECT     ForecastID
                                                   ,Profit
                                                   ,RoundedMemberPrem
                                         --, TotalReqRev 
                                         FROM       dbo.CalcFinalPremium (NOLOCK)
                                         WHERE      PlanYearID = dbo.fnGetBidYear ()
                                                    AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                         GROUP BY   ForecastID
                                                   ,Profit
												   ,RoundedMemberPrem
                             --, TotalReqRev
                             ) cfp
                                    ON cfp.ForecastID = sfs.ForecastID
                             LEFT JOIN  (SELECT     ForecastID
                                                   ,MAX (AuditTime) AS [Last Reprice]
                                         FROM       dbo.PlanRepriceLog (NOLOCK)
                                         WHERE      ProcNumber = 22 -- Should be last proc #25, not 23
                                                    AND ForecastID IN (SELECT   ForecastID FROM #BTWsfslist)
                                         GROUP BY   ForecastID) prl
                                    ON prl.ForecastID = sfs.ForecastID
                             LEFT JOIN  dbo.SavedTargetingInputs (NOLOCK) sti
                                    ON sti.ForecastID = sfs.ForecastID
                                       AND  sti.TargetingInputTypeID = 4    -- Target Profit PMPM
                             WHERE      sfs.IsLiveIndex = 1
                                        AND sfs.IsHidden = 0)
                         SELECT     ScenarioTag
                                   ,ActuarialMarket
                                   ,MAPlanDesign
                                   ,SNPType
                                   ,RepriceRequired
                                   ,ForecastID
                                   ,FORMAT (TargetProfitPMPM, '$0.#####;($0.#####)') AS TargetProfitPMPM                 --formats negatives as ($#.##)
                                   ,OriginalValue
                                   ,FORMAT (CurrentProfitPMPM, '$0.#####;($0.#####)') AS CurrentProfitPMPM               --formats negatives as ($#.##)
                                   ,FORMAT (TargetProfitPMPM - CurrentProfitPMPM,'$0.#####;($0.#####)') AS [Difference] --formats negatives as ($#.##)
                                   ,MemberPremium
                                   ,UnallocatedRebate
                                   ,[Time]
                                   ,LastReprice
                                   ,PlanIntentionID
                         FROM       TargetProfitPMPM
                         ORDER BY   ScenarioTag;
                     END;
            COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @ErrorException NVARCHAR(4000);
            DECLARE @errSrc VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL');
            DECLARE @currentdate DATETIME = GETDATE ();
            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :'
                                      + CAST(@ErrorState AS VARCHAR);
            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);

            ROLLBACK TRANSACTION;
            ---Insert into app log for logging error------------------
            EXEC dbo.spAppAddLogEntry @currentdate
                                     ,''
                                     ,'ERROR'
                                     ,@errSrc
                                     ,@ErrorMessage
                                     ,@ErrorException
                                     ,@LastUpdateByID;

        END CATCH;
    END;


GO
