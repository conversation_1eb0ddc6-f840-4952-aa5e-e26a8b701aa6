SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [spAppGetMailStatus]    
--    
-- AUTHOR: Senthilkumar
--    
-- CREATED DATE: 2012-Mar-05    
--    
-- DESCRIPTION: Procedure responsible for sending mail.

-- TYPE: New.
    
-- PARAMETERS:    
-- Input:    
    
-- TABLES:
--	sysmail_allitems     
-- Read:    
--      
    
-- Write:    
--         
    
-- VIEWS:    
--    
-- FUNCTIONS:    
--      
-- STORED PROCS:        
     
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION  CHANGES MADE              DEVELOPER      
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2012-Mar-05  1		Initial Version           Senthilkumar
-- ---------------------------------------------------------------------------------------------------------------------- 
CREATE PROCEDURE [dbo].[spAppSendMail]
@recipients VARCHAR(max),
@subject NVARCHAR(510),
@body NVARCHAR(max)
AS 
BEGIN

DECLARE @mailitem_id INT
EXEC msdb.dbo.sp_send_dbmail @recipients = @recipients,
@subject = @subject,@body = @body,@body_Format = 'HTML', @mailitem_id =  @mailitem_id OUTPUT
SELECT @mailitem_id
END
GO
