SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetAddedBenefits]    ******/

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetAddedBenefits
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Feb-26
-- HEADER UPDATED: 2011-Mar-24
--
-- DESCRIPTION: Function responsible for listing values on the Added Benefit tab in the MAPD Model.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--      LkpExtBidServiceCategory
--      LkpIntAddedBenefitType
--      SavedPlanAddedBenefits     
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE						                                    DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2007-Feb-26		1			Initial Version															Christian Cofie
-- 2007-Sep-10		2			Revised code to bring into coding standards.			                Shannon Boykin
-- 2007-Sep-17		3			Additional revisions to bring code into coding standards.		        Shannon Boykin
-- 2007-Sep-24		4			Additional revisions to bring code into coding standards - Lined up	    Shannon Boykin
--						            paranthesis differently to conserve line space.
-- 2007-Nov-27		5			Added IsHidden=0 to where condition                        		        Christian Cofie
-- 2008-Feb-23      6           Added PlanYear to LkpExtCMSBidServiceCategory                           Sandy Ellis
-- 2009-Mar-17      7           Data type conversion                                                    Sandy Ellis
-- 2010-Oct-09      8           Formated to 2012 structure                                              Nate Jacoby
-- 2011-Mar-23		9			Added ISNULL to prevent Nulls from showing up in the model				Craig Wright
-- 2011-Mar-24		10			Changed IsStandard to BIT and removed all PlanYearID related material	Joe Casey
-- 2015-Apr-21      11          Updated the length of columns INAddedBenefitAllowed and					Deepthi Thiyagu
--								OONAddedBenefitAllowed
-- 2015-May-28		12			Changed INAddedBenefitDescription & OONAddedBenefitDescription 
--								Datatypes to Varchar(60)												Sharath Chandra
-- 2021-Nov-15		11			Changed @AddedBenefitTypeID to INT								    Umaprasad shetpally
-- 2023-Oct-04      12          BenefitDescription size increases                                       Adam Gilbert
-- 2024-Jul-17      13          BenefitDescription size increases                                       Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetAddedBenefits]
    (
    @ForecastID INT
    )
RETURNS @Results TABLE
    (
    IsStandard BIT,
    AddedBenefitTypeID INT,
    AddedBenefitName CHAR(60),
    INAddedBenefitDescription VARCHAR(2000),
    INAddedBenefitAllowed DECIMAL(6,2),
    INAddedBenefitUtilization DECIMAL(14,6),
    INAddedBenefitCostShare DECIMAL(14,6),
    OONAddedBenefitDescription VARCHAR(2000),
    OONAddedBenefitAllowed DECIMAL(6,2),
    OONAddedBenefitUtilization DECIMAL(14,6),
    OONAddedBenefitCostShare DECIMAL(14,6),
    IsValueAdded BIT,
    BidServiceCatID SMALLINT,
    ServiceCategory VARCHAR(50)
    ) 
    AS
BEGIN
    INSERT @Results
    SELECT
		t.IsStandard,
        t.AddedBenefitTypeID,
        t.AddedBenefitName, 
        t.INAddedBenefitDescription,
        t.INAddedBenefitAllowed, 
        t.INAddedBenefitUtilization,
        t.INAddedBenefitCostShare,
        t.OONAddedBenefitDescription, 
        t.OONAddedBenefitAllowed,
        t.OONAddedBenefitUtilization, 
        t.OONAddedBenefitCostShare,
        t.IsValueAdded,
        t.BidServiceCatID,
        l.ServiceCategory 
    FROM (
            SELECT
                ABT.IsStandard,
                AddedBenefitTypeID = 
                    CASE ABT.IsStandard 
                        WHEN 1 THEN ABT.AddedBenefitTypeID
                        ELSE SAB.AddedBenefitTypeID
                    END, 
                AddedBenefitName =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.AddedBenefitName
                        ELSE SAB.AddedBenefitName
                    END, 
                INAddedBenefitDescription =
                    ISNULL(CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitDescription
                        ELSE SAB.INAddedBenefitDescription
                    END,''),
                INAddedBenefitAllowed =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitAllowed
                        ELSE SAB.INAddedBenefitAllowed
                    END, 
                INAddedBenefitUtilization =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitUtilization
                        ELSE SAB.INAddedBenefitUtilization
                    END,
                INAddedBenefitCostShare =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitCostShare
                        ELSE SAB.INAddedBenefitCostShare
                    END,
                OONAddedBenefitDescription =
                    ISNULL(CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitDescription
                        ELSE SAB.OONAddedBenefitDescription
                    END,''), 
                OONAddedBenefitAllowed =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitAllowed
                        ELSE SAB.OONAddedBenefitAllowed
                    END,
                OONAddedBenefitUtilization =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitUtilization
                        ELSE SAB.OONAddedBenefitUtilization
                    END, 
                OONAddedBenefitCostShare =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitCostShare
                        ELSE SAB.OONAddedBenefitCostShare
                    END,
                IsValueAdded =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.IsValueAdded
                        ELSE SAB.IsValueAdded
                    END,
                BidServiceCatID =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.BidServiceCatID
                        ELSE SAB.BidServiceCatID
                    END
            FROM LkpIntAddedBenefitType ABT
            INNER JOIN SavedPlanAddedBenefits SAB
                ON ABT.AddedBenefitTypeID = SAB.AddedBenefitTypeID
            WHERE
                SAB.ForecastID = @ForecastID
                AND SAB.IsHidden=0
        ) t
    INNER JOIN LkpExtCMSBidServiceCategory l
        ON l.BidServiceCategoryID = t.BidServiceCatID

    RETURN
END

GO
