SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Author: <PERSON><PERSON><PERSON>
-- Create date: 05-Jul-2022
-- Description: Get Query
--
--
-- PARAMETERS:
-- Input:
-- @RequestID INT,
-- @DFRunIDList VARCHAR(MAX)=NULL,
-- @TableName VARCHAR(MAX),
-- @LastUpdateByID CHAR(7) ,
-- @QueryID INT
-- Output:
-- @Query
-- $HISTORY
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE    VERSION  CHANGES MADE                                                DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 05-Jul-2022  1   Initial version.                                            Manisha Tyagi
-- 30-OCT-2024  2   Cost Share Basis: Update to add new SavedDFClaims columns   <PERSON> <PERSON>
-- 28-DEC-2024  3   Revert accidental change to SavedDFBasePeriodSummary query  Alex Brandt
-- 25-Mar-2025	4	Updated DF claims Query										Deepali
-- 06-MAY-2025	5	Column Consolidation: Remove calc columns and use Bid Cut	Alex Brandt
--						in SavedDFClaims queries
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spGetDFDataQuery]
    @RequestID INT,
    @DFRunIDList VARCHAR(MAX) = NULL,
    @TableName VARCHAR(MAX),
    @LastUpdateByID CHAR(7),
    @QueryID INT = NULL,
    @Query VARCHAR(8000) OUTPUT
AS
BEGIN
    DECLARE @DFVersionID INT,
            @DFVersionDescription VARCHAR(MAX);
    SELECT @DFVersionID = DFVersionID
    FROM dbo.SavedUtilityRequestDetail
    WHERE RequestID = @RequestID;
    SELECT @DFVersionDescription = RequestorComments
    FROM dbo.SavedUtilityRequestHeader
    WHERE RequestID = @RequestID;
    IF @TableName = 'SavedDFHeader' --to 'SavedDFVersionHeader'
    BEGIN
        SET @Query
            = 'SELECT DISTINCT ' + CAST(@DFVersionID AS VARCHAR(4)) + ' AS DFVersionID,''' + CAST(@DFVersionDescription AS VARCHAR(200))
              + ''' AS DFVersionDescription, getdate() LastUpdateDateTime,DFRunID,DFRunName,IncurredStartDate,IncurredEndDate,PaidThroughDate,'''
              + @LastUpdateByID + ''' AS LastUpdateByID FROM DF.dbo.' + @TableName
              + ' WHERE CAST(DFRunID AS VARCHAR(4)) IN (SELECT VALUE FROM dbo.fnStringSplit(''' + @DFRunIDList
              + ''','',''))';

    END;
    --SavedDFClaims
    ELSE IF (@TableName = 'SavedDFClaims' AND @QueryID = 1)
    BEGIN
        SET @Query
           = 'SELECT ' + CAST(@DFVersionID AS VARCHAR(4))
              + ' DFVersionID,C.PlanInfoID,LEFT(C.IncurredMonth,4) IncurredYear,L.BidCut,C.GrouperID,C.BenefitCategoryID,C.FusionLineNumber,SUM(C.Value) Value FROM DF.dbo.'
              + @TableName
              + ' C WITH (NOLOCK) INNER JOIN dbo.LkpIntDemog L ON L.BidCut = C.BidCut INNER JOIN dbo.SavedDFVersionHeader D WITH (NOLOCK) ON D.DFRunID = C.DFRunID WHERE L.Biddable = 1 AND D.DFVersionID = '
              + CAST(@DFVersionID AS VARCHAR(4)) + ' GROUP BY D.DFVersionID,C.PlanInfoID,LEFT(C.IncurredMonth, 4),L.BidCut,C.GrouperID,C.BenefitCategoryID,C.FusionLineNumber';

    END;
    ELSE IF (@TableName = 'SavedDFClaims' AND @QueryID = 2)
    BEGIN
        SET @Query
            = 'SELECT ' + CAST(@DFVersionID AS VARCHAR(4))
              + ' DFVersionID,C.PlanInfoID,LEFT(C.IncurredMonth,4) IncurredYear,L.BidCut,''Plan Lvl'' GrouperID,C.BenefitCategoryID,C.FusionLineNumber,SUM(C.Value) Value FROM DF.dbo.'
              + @TableName
              + ' C INNER JOIN dbo.LkpIntDemog L ON L.BidCut = C.BidCut INNER JOIN dbo.SavedDFVersionHeader D ON D.DFRunID = C.DFRunID WHERE D.DFVersionID = '
              + CAST(@DFVersionID AS VARCHAR(4)) + ' GROUP BY D.DFVersionID,C.PlanInfoID,LEFT(C.IncurredMonth,4),L.BidCut,C.BenefitCategoryID,C.FusionLineNumber';
    END;

    --SavedDFFinance
    ELSE IF (@TableName = 'SavedDFFinance' AND @QueryID = 1)
    BEGIN
        SET @Query
            = 'SELECT ' + CAST(@DFVersionID AS VARCHAR(4))
              + ' DFVersionID,F.PlanInfoID,LEFT(F.IncurredMonth,4) IncurredYear,3 DemogIndicator,F.SSStateCountyCD,F.GrouperID,SUM(F.MemberMonths) MemberMonths,SUM(F.MAReconRiskScore) MAReconRiskScore,SUM(F.CapDirectPay) CapDirectPay,SUM(F.CapSurplusDeficit) CapSurplusDeficit,SUM(F.CapMSBs) CapMSBs,SUM(F.CapProviderRewards) CapProviderRewards,SUM(F.TotalOPBInBids) TotalOPBInBids,SUM(F.TotalOPBNotInBids) TotalOPBNotInBids,SUM(F.MAPremium) MAPremium,SUM(F.PDPremium) PDPremium,SUM(F.Quality) Quality,SUM(F.PDNetPaid) PDNetPaid,SUM(F.PartDCapAdj) PartDCapAdj,SUM(F.MSCapAdj) MSCapAdj,SUM(F.PartBRxRebates) PartBRxRebates FROM DF.dbo.'
			  + @TableName
              + ' F INNER JOIN dbo.LkpIntDemog L ON L.Demog = F.Demog INNER JOIN dbo.SavedDFVersionHeader D ON D.DFRunID = F.DFRunID WHERE L.Biddable = 1 AND D.DFVersionID = '
              + CAST(@DFVersionID AS VARCHAR(4))
              + ' GROUP BY D.DFVersionID,F.PlanInfoID,LEFT(F.IncurredMonth,4),F.SSStateCountyCD,F.GrouperID';
    END;
    ELSE IF (@TableName = 'SavedDFFinance' AND @QueryID = 2)
    BEGIN
        SET @Query
            = 'SELECT ' + CAST(@DFVersionID AS VARCHAR(4))
              + ' DFVersionID,F.PlanInfoID,LEFT(F.IncurredMonth,4) IncurredYear,L.DemogIndicator,F.SSStateCountyCD,''Plan Lvl'' GrouperID,SUM(F.MemberMonths) MemberMonths,SUM(F.MAReconRiskScore) MAReconRiskScore,SUM(F.CapDirectPay) CapDirectPay,SUM(F.CapSurplusDeficit) CapSurplusDeficit,SUM(F.CapMSBs) CapMSBs,SUM(F.CapProviderRewards) CapProviderRewards,SUM(F.TotalOPBInBids) TotalOPBInBids,SUM(F.TotalOPBNotInBids) TotalOPBNotInBids,SUM(F.MAPremium) MAPremium,SUM(F.PDPremium) PDPremium,SUM(F.Quality) Quality,SUM(F.PDNetPaid) PDNetPaid,SUM(F.PartDCapAdj) PartDCapAdj,SUM(F.MSCapAdj) MSCapAdj,SUM(F.PartBRxRebates) PartBRxRebates FROM DF.dbo.'
              + @TableName
              + ' F INNER JOIN dbo.LkpIntDemog L ON L.Demog = F.Demog INNER JOIN dbo.SavedDFVersionHeader D ON D.DFRunID = F.DFRunID WHERE D.DFVersionID = '
              + CAST(@DFVersionID AS VARCHAR(4))
              + ' GROUP BY D.DFVersionID,F.PlanInfoID,LEFT(F.IncurredMonth,4),L.DemogIndicator,F.SSStateCountyCD';
    END;
    --SavedDFBasePeriodSummary
    ELSE IF @TableName = 'SavedDFBasePeriodSummary' --to SavedDFBaseAdminAndRevenue
    BEGIN
       SET @Query
            = 'SELECT D.DFVersionID,S.PlanInfoID,YEAR(D.IncurredStartDate) as IncurredYear,CMSESRDRevenue = SUM(S.ESRDCMSRevenueMATotalPremium + s.ESRDCMSRevenueReduceMATotalPremiumByMAMbrPremium + s.ESRDCMSRevenuePremiumPymtReductionAmt + s.ESRDCMSRevenueMLRAdjustment + s.ESRDCMSRevenueAdjForNegativePremiumRevenue + s.ESRDCMSRevenueAdjForNegative),CMSHospiceRevenue = SUM(s.HospiceCMSRevenueMATotalPremium + s.HospiceCMSRevenueReduceTotalMAPremByMAMbrPrem  + s.HospiceCMSRevenueMLRAdjustment + s.HospiceCMSRevenuePremiumPymtReductionAmt + s.HospiceCMSRevenueAdjForNegativePremiumRevenue + s.HospiceCMSRevenueAdjForNegative),CMSOtherRevenue = SUM(s.AllOtherCMSRevenueMATotalPremium + s.AllOtherCMSRevenueReduceTotalMAPremByMAMbrPrem + s.AllOtherCMSRevenuePremiumPymtReductionAmt + s.AllOtherCMSRevenueMLRAdjustment + s.AllOtherCMSRevenueAdjForNegativePremiumRevenue + s.AllOtherCMSRevenueAdjForNegativeESRD + s.AllOtherCMSRevenueAdjForNegativeHospice) ,ESRDPremium = SUM(s.ESRDPremiumRevenueMAMbrPremium + s.ESRDPremiumRevenueMAGainShare+ s.ESRDPremiumRevenueAdjForNegative) ,HospicePremium = SUM(s.HospicePremiumRevenueMAMbrPremium+ s.HospicePremiumRevenueMAGainShare + s.HospicePremiumRevenueAdjForNegative) ,OtherPremium = SUM(s.AllOtherPremiumRevenueMAMbrPremium + s.AllOtherPremiumRevenueMAGainShare + s.AllOtherPremiumRevenueAdjForNegative) ,ESRDNetMedicalExpenses = SUM(S.ESRDNetMedicalExpensesBeforeAdj+S.ESRDNetMedicalExpensesAdjForNegativeHospice),HospiceNetMedicalExpenses = SUM(S.HospiceNetMedicalExpensesBeforeAdj+S.HospiceNetMedicalExpensesAdjForNegative),OtherNetMedicalExpenses = SUM(S.AllOtherNetMedicalExpenses),ESRDMemberMonths = SUM(S.ESRDMemberMonths),HospiceMemberMonths = SUM(S.HospiceMemberMonths),MarketingandSalesAdminExpense = SUM(S.SalesAndMarketingFinanceAdminMarketing+S.SalesAndMarketingMSBINCSalesAndMarketing),DirectAdminExpense = SUM(S.DirectAdminFinanceAdminDirect+S.DirectAdminFinanceAdminTaxes+S.DirectAdminFinanceAdminQuality+S.DirectAdminRPAdmin+S.DirectAdminRPQuality+S.DirectAdminMSBClaimsAdj+S.DirectAdminMSBProfitAdj+S.DirectAdminMSBAdminAdj+S.DirectAdminMSBOTCAdmin+S.DirectAdminMSBOTCQuality+S.DirectAdminUncollectedPremiumAdjustment),IndirectAdminExpense = SUM(S.IndirectAdminNonBenefitExpenses),NetCostofPrivateInsuranceExpense = SUM(S.NetCostofPrivateReinsuranceNonBenefitExpenses),0 QualityInitiativesAdminExpense,0 TaxesAndFeesAdminExpense,InsurerFeesAdminExpense = SUM(S.InsurerFeesNonBenefitExpenses),MedicaidRevenue = SUM(S.MedicaidRevenue),MedicaidBenefitExpenses = SUM(S.MedicaidBenefitExpensesFromSources+S.MedicaidBenefitExpensesAdjDueToNoMedicaidRevenue+S.MedicaidBenefitExpensesCalculated),MedicaidNonBenefitExpenses = SUM(S.MedicaidNonBenefitExpenses) FROM DF.dbo.'
              + @TableName + ' S INNER JOIN dbo.SavedDFVersionHeader D ON D.DFRunID = S.DFRunID WHERE D.DFVersionID = '
              + CAST(@DFVersionID AS VARCHAR(4)) + ' GROUP BY S.PlanInfoID,D.DFVersionID,YEAR(D.IncurredStartDate)';
    END;
    SELECT (@Query) AS Query;
END;