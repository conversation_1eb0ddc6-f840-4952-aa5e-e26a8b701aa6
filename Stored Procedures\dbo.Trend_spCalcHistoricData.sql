SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- PROCEDURE NAME: [spPopulateCapCompiledTables]                                                                  

-- AUTHOR: <PERSON>                                                       

-- DESCRIPTION:
--Step 1:       
--Step 2:   
--Step 3:   

-- PARAMETERS:                                                                  
--  Input: 
--         
-- Output: None                                                                  
-- Returns: N/A                                                                  
--                                                                  
-- TABLES:                                                                  
-- Read:                                                                  
--   Saved<PERSON>Header (header table that has information about the DFRunID:  DFRunID, PlanInfoID, DFRunDescription, CPS, IncurredStartDate, IncurredEndDate, PaidThroughDate)
--   SavedDFClaims (stores the benefit cateogry level claims data as well as cap, opb data allocated to benefit categories and inclusion of all adjustments and assumptions 
--   SavedDFFinance (this is basically MMMUF data, all cap/opb dadta allocated in claims table will reconcile to the data in this table for the given DFRunID/PlanInfoID; table also has member months, risk score, and other fields)    
--   LkpIntMACTAPTCategoryMapping -- i don't think we need this (was used for outlier claims & the utilization field)
--	 LkpIntRiskDefnDetail (goal is to get this into SAM so it's updated in one location by MS)
--   LkpIntMAIndPlanInfo (goal is to get rid of this and replace with MAAModels vwPlanInfo so all tools/processes use same source)

--   
-- Write:                                                                  
--   Cap_CompiledData (these will be whatever you want, just using current names)   
--   Cap_CompiledMembership                                                                 
--                                                               
-- VIEWS: Read: NONE                                                                  
--                                                                  
-- STORED PROCS: Executed: NONE                                                                  
--                                                                  
-- HISTORY                                                                   
-- -------------------------------------------------------------------------------------------------------------------------------                                                      
---                                     
-- DATE         VERSION     DESCRITPTION														DEVELOPER                                                                    
-- -------------------------------------------------------------------------------------------------------------------------------                            
-- 2020-01-17   1           Initial version (to populate Cap_Compiled tables with expereince 
--                          data to be used for trend tool; mimics current methodology/assumptions 
--                          used to populate Cap_Compiled* tables but sourced from new Data 
--                          Foundations' process/tables                                         Wendy Bowers
-- -------------------------------------------------------------------------------------------------------------------------------                                                              
Create PROCEDURE [dbo].[Trend_spCalcHistoricData]
  --@VersionID INT 
  @LastUpdateByID CHAR(13)

AS 

--DECLARE @VersionID INT = 4; --typically created & passed via UI, stored in some type of header table
--DECLARE @LastUpdateByID CHAR(13) = 'humad/ajb8442';

/*
This process will be going through a lot more data than your previous sp_MACTAPT_CompiledDate_NewSet did
and you may need header table to store IDs, timestamps or other metadata to help you identify applicable info about the data; 

I think you only sync over one data set now & it has one MACTAPTRunID applicable to all experience years, 
but moving forward you will need to sync over several DFRunIDs - there will be one DFRunID for each year of experience you want;

For my own testing (in the future :)), I will use DFRunIDs and any other parameters needed to populate the new cap compiled tables (fields in Kim's email):
DFRunIDs (on SavedDFHeader, SavedDFFinance & SavedDFClaims tables)
VersionID for LkpIntMAIndPlanInfo -- this table mimics MAAModels vwPlanInfo, but has some fields used for current DF mactapt process
There is a VersionID for LkpIntMACTAPTCategoryMapping, but it's been the same since creation; we need to 
determine who/how this will be maintained and updated; it has predictive categories which I don't think are
used anymore; i think if anything it should be a bpt service cat to rep cat mapping so that if at any time 
categorization changes the way benefit categories map to bpt service categories, they will still be in alignment;
I think we need to take another look at this mapping and make sure it's up to date 
with how bpt cats are mapped to rep cats so that any ben cat changes can stay aligned 
*data are not available for testing yet, but when they become available, frozen data (full year not currently in EDW MMMUF) will not align 
because we need to update our spComputeFusion to pull the correct $ fields from Claims Completion tables - our goal is to update this for 2020Q3 run
*/


DECLARE @LastUpdateDateTime DATETIME;
DECLARE @DFRunID1 INT;
DECLARE @DFRunID2 INT;
DECLARE @DFRunID3 INT;
DECLARE @DFRunID4 INT;
DECLARE @DFRunID5 INT;
DECLARE @PlanInfoTableVersionID INT;
DECLARE @IsHospice BIT;
DECLARE @IsESRD BIT;
DECLARE @CurrentYear INT;
DECLARE @MaxCurrentYearQuarter TINYINT;

SET @LastUpdateDateTime = GETDATE ();
SET @DFRunID1 = 39;
SET @DFRunID2 = 40;
SET @DFRunID3 = 41;
SET @DFRunID4 = 45;
SET @DFRunID5 = 37;
SET @PlanInfoTableVersionID = 1; --current versionid; may change as MAAModels.vwPlanInfo changes; note to self here:  we may need to change saveddfheader to hold same info as fusion so users get full info 
SET @IsHospice = 0; --Biddable data only for trend tool: Non-Hospice/Non-ESRD
SET @IsESRD = 0; --Biddable data only for trend tool: Non-Hospice/Non-ESRD --you may want to reconsider this & instead include a demog of some type to be able to include/exclude certain demographics
SET @CurrentYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);
SET @MaxCurrentYearQuarter = (SELECT    LastCurrentYearQuarter
                              FROM      dbo.LkpProjectionVersion
                              WHERE     IsLiveProjection = 1);

--Clear cost & use and membership tables
DELETE  FROM dbo.Trend_CalcHistoricCostAndUseAJB --will need to update if introduce some type of version id, but for now just keeping in step w/ sp_MACTAPT_CompiledData_NewSet 
DELETE  FROM dbo.Trend_CalcHistoricMembershipAJB

--create and populate a table to monitor status of sp that will hold all DFRunID/PlanInfoID combinations for a given expereince data run
IF (SELECT  OBJECT_ID ('tempdb..##CapCompiledHeader')) IS NOT NULL
    DROP TABLE ##CapCompiledHeader;
SELECT      DISTINCT
            h.DFRunID
           ,h.PlanInfoID
           ,CAST(NULL AS DATETIME) AS StartTime
           ,CAST(NULL AS DATETIME) AS EndTime
INTO        ##CapCompiledHeader
FROM        dbo.Trend_SavedDFHeader h
INNER JOIN  dbo.LkpIntMAIndPlanInfo pl --ensures you only have real Individual MA plans on maaui maintained plan list (MAAModels vwPlanInfo + other fields we have to maintain: RiskDefnID, IsUseEData, IsUseDEData, VersionID, LastUpdateByID, LastUpdateByDate)
        ON h.PlanInfoID = pl.PlanInfoID --correct plan
WHERE       h.DFRunID IN (@DFRunID1, @DFRunID2, @DFRunID3, @DFRunID4, @DFRunID5) --DFRunIDs you will by syncing  
            AND pl.VersionID = @PlanInfoTableVersionID --correct version
            AND pl.SNPType <> 'ESRD';   --no esrd snps per trend team; may want to consider pulling in all data and updating sp as needed                                           

--Declare variables and cursor to run on a loop; 
DECLARE @CurDFRunID    INT
       ,@CurPlanInfoID INT;

DECLARE RunPlan_Cursor CURSOR FOR
SELECT  DFRunID, PlanInfoID FROM ##CapCompiledHeader;

OPEN RunPlan_Cursor;
FETCH NEXT FROM RunPlan_Cursor
INTO @CurDFRunID
    ,@CurPlanInfoID;
WHILE @@Fetch_Status = 0
    BEGIN

        UPDATE  ##CapCompiledHeader
        SET     StartTime = GETDATE ()
        WHERE   DFRunID = @CurDFRunID
                AND PlanInfoID = @CurPlanInfoID;

        --create a table to use as base for joins (mimcs cursor but contains all details needed to run through a DFRunID/PlanInfoID)
        IF (SELECT  OBJECT_ID ('tempdb..#LoopInfo')) IS NOT NULL
            DROP TABLE #LoopInfo;
        SELECT      @CurDFRunID AS DFRunID
                   ,h.IncurredEndDate
                   ,h.PaidThroughDate
                   ,h.PlanInfoID
                   ,h.CPS
                   ,@PlanInfoTableVersionID AS PlanInfoTableVersionID
                   ,pl.RiskDefnID   --Market Support approved risk definitions (we'd like get rid of these extra fields on the plan info table and remove the riskdefndetail table & instead provide all data and the decision to include/exclude certain grouper ids/contract cat cds can be made by tool logic or MS actuary using the tool 
        INTO        #LoopInfo
        FROM        dbo.Trend_SavedDFHeader h
       INNER JOIN   dbo.LkpIntMAIndPlanInfo pl --makes sure you only have real plans on maaui maintained plan list (MAAModels vwPlanInfo + other fields we have to maintain: RiskDefnID, IsUseEData, IsUseDEData, VersionID, LastUpdateByID, LastUpdateByDate)
               ON h.PlanInfoID = pl.PlanInfoID  --correct plan/yr
        WHERE       h.DFRunID = @CurDFRunID
                    AND h.PlanInfoID = @CurPlanInfoID
                    AND pl.VersionID = @PlanInfoTableVersionID; --correct version                                            

        --SELECT * FROM #LoopInfo

        --create a temporary membership table 
        IF (SELECT  OBJECT_ID ('tempdb..#MMTemp')) IS NOT NULL DROP TABLE #MMTemp;
        SELECT      TOP 0
                    f.DFRunID
                   ,f.PlanInfoID
                   ,pl.CPS
                   ,pl.PlanYearID
                   ,pl.PlanYearID AS [Quarter]
                   ,f.IncurredMonth
                   ,f.SSStateCountyCD
                   ,d.IsHospice AS IsRisk   --was not on mbrshp table field list in email but i assume if keeping on clms want to keep her for applicable membership
                   ,f.MemberMonths
        INTO        #MMTemp
        FROM        dbo.Trend_SavedDFFinance f
        LEFT JOIN   dbo.LkpIntDemog d
               ON d.Demog = f.Demog
        LEFT JOIN   dbo.LkpIntMAIndPlanInfo pl
               ON f.PlanInfoID = pl.PlanInfoID;

        --popualte temp membership table
        INSERT INTO #MMTemp
            (DFRunID
            ,PlanInfoID
            ,CPS
            ,IncurredMonth
            ,SSStateCountyCD
            ,IsRisk
            ,MemberMonths)
        SELECT      li.DFRunID
                   ,li.PlanInfoID
                   ,li.CPS
                   ,f.IncurredMonth
                   ,f.SSStateCountyCD
                   ,CASE WHEN rd.IsRiskCtrctCatCD IS NULL THEN 0 ELSE 1 END AS IsRisk
                   ,SUM (f.MemberMonths) AS MemberMonths
        FROM        #LoopInfo li
        LEFT JOIN   dbo.Trend_SavedDFFinance f
               ON li.DFRunID = f.DFRunID
                  AND   li.PlanInfoID = f.PlanInfoID
       INNER JOIN   dbo.LkpIntDemog d
               ON f.Demog = d.Demog
        LEFT JOIN   dbo.LkpIntRiskDefnDetail rd --identify IsRisk 
               ON li.RiskDefnID = rd.RiskDefnID
                  AND   f.CtrctCatCD = rd.IsRiskCtrctCatCD
        WHERE       f.DFRunID = @CurDFRunID
                    AND f.PlanInfoID = @CurPlanInfoID
                    AND d.IsHospice = @IsHospice --exclude ESRD/Hospice 
                    AND d.IsESRD = @IsESRD  --exclude ESRD/Hospice
        GROUP BY    li.DFRunID
                   ,li.PlanInfoID
                   ,li.CPS
                   ,f.IncurredMonth
                   ,CASE WHEN rd.IsRiskCtrctCatCD IS NULL THEN 0 ELSE 1 END;



        --create temporary claims table 
        IF (SELECT  OBJECT_ID ('tempdb..#ClaimsTemp')) IS NOT NULL
            DROP TABLE #ClaimsTemp;
        SELECT      TOP 0
                    c.DFRunID
                   ,c.PlanInfoID
                   ,pl.CPS
                   ,c.IncurredMonth
                   ,c.SSStateCountyCD
                   ,d.IsHospice AS IsRisk
                   ,c.BenefitCategoryID AS BenefitCategoryID
                   ,c.Paid AS Paid
                   ,c.Paid AS Allowed
                   ,c.AdmitCnt
                   ,c.UnitCnt
        INTO        #ClaimsTemp
        FROM        dbo.Trend_SavedDFClaims c
        LEFT JOIN   dbo.LkpIntDemog d
               ON d.Demog = c.Demog
        LEFT JOIN   dbo.LkpIntMAIndPlanInfo pl
               ON c.PlanInfoID = pl.PlanInfoID;

        --Populate temporary claims table  
        INSERT INTO #ClaimsTemp
            (DFRunID
            ,PlanInfoID
            ,CPS
            ,IncurredMonth
            ,SSStateCountyCD
            ,IsRisk
            ,BenefitCategoryID
            ,Paid
            ,Allowed
            ,AdmitCnt
            ,UnitCnt)
        SELECT      li.DFRunID
                   ,li.PlanInfoID
                   ,li.CPS
                   ,c.IncurredMonth
                   ,c.SSStateCountyCD
                   ,CASE WHEN rd.IsRiskCtrctCatCD IS NULL THEN 0 ELSE 1 END AS IsRisk
                   ,c.BenefitCategoryID
                                                        --fields equivalent to make up paid/allowed/admits/units fields currently synced; more options like inclduing medicaid adj and others, but timing may be an issue, we can talk through them all
                                                        --encounter conversion process will take over here based on scnearios and avialable cap so may not directly tie to the Q4 data when we get these tables popualted with those data
                   ,SUM (
                    c.Paid + c.CapDirectPayEPaidClaims + c.CapDirectPayDEPaidClaims + c.CapSurplusDeficitEPaidClaims
                    + c.CapSurplusDeficitDEPaidClaims) AS Paid
                   ,SUM (
                    c.Paid + c.CapDirectPayEPaidClaims + c.CapDirectPayDEPaidClaims + c.CapSurplusDeficitEPaidClaims
                    + c.CapSurplusDeficitDEPaidClaims + c.MbrCS + c.EncounterMbrCS + c.DelegatedEncounterMbrCS
                    + c.PymtReductionAmt) AS Allowed    --the mactapt has always had SQS amt in allowed so that all years were on same basis but all years of experience that are now in mactapt are sequestered so we may want to think about this more - do we still need it & what implications does it have
                   ,SUM (c.AdmitCnt + c.EncounterAdmitCnt + c.DelegatedEncounterUnitCnt) AS AdmitCnt
                   ,SUM (c.UnitCnt + c.EncounterUnitCnt + c.DelegatedEncounterUnitCnt) AS UnitCnt
        FROM        #LoopInfo li
        LEFT JOIN   dbo.Trend_SavedDFClaims c (NOLOCK)
               ON li.DFRunID = c.DFRunID
                  AND   li.PlanInfoID = c.PlanInfoID
       INNER JOIN   dbo.LkpIntDemog d
               ON c.Demog = d.Demog
        LEFT JOIN   dbo.LkpIntRiskDefnDetail rd --identify IsRisk 
               ON li.RiskDefnID = rd.RiskDefnID
                  AND   c.CtrctCatCD = rd.IsRiskCtrctCatCD
        WHERE       c.DFRunID = @CurDFRunID
                    AND c.PlanInfoID = @CurPlanInfoID
                    AND d.IsHospice = @IsHospice --exclude ESRD/Hospice
                    AND d.IsESRD = @IsESRD  --exclude ESRD/Hospice
        GROUP BY    li.DFRunID
                   ,li.PlanInfoID
                   ,li.CPS
                   ,c.IncurredMonth
                   ,CASE WHEN rd.IsRiskCtrctCatCD IS NULL THEN 0 ELSE 1 END
                   ,c.BenefitCategoryID;



        --below was modified from lousqlwts690.tpf.dbo.[sp_MACTAPT_CompiledData_NewSet]  
        --Build Compiled Data        
        INSERT  INTO dbo.Trend_CalcHistoricCostAndUseAJB
        SELECT      a.CPS
                   ,a.IncurredMonth / 100 AS 'PlanYearID'
                   ,CASE WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 1 AND 3 THEN 1
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 4 AND 6 THEN 2
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 7 AND 9 THEN 3
                         ELSE 4 END AS 'QuarterID'
                   ,a.BenefitCategoryID
                   ,a.IsRisk
                   ,SUM (a.Allowed) AS 'Allowed'
                   ,SUM (a.Paid) AS 'Net'
                   ,SUM (CASE WHEN c.ReportingCategory = 'IP' --Set utilization measure by reporting category
                    THEN          a.AdmitCnt
                              ELSE a.UnitCnt END) AS 'Utilization'
                   ,'humad/ajb8442'
                   ,GETDATE ()
        FROM        #ClaimsTemp a
       --will need to determine how to update HistYearQuarter or if even need it; will need to put Yr & Qtr on the temp claims and membership tables to do like currently done in sp_MACTAPT_Compileda_NewSet
       INNER JOIN   dbo.SavedPlanInfo b
               ON a.CPS = b.CPS
                  AND   a.IncurredMonth / 100 = b.PlanYear
        LEFT JOIN   dbo.LkpIntBenefitCategory c
               ON a.BenefitCategoryID = c.BenefitCategoryID
        WHERE       a.IncurredMonth / 100
                    + CASE WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 1 AND 3 THEN 1
                           WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 4 AND 6 THEN 2
                           WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 7 AND 9 THEN 3
                           ELSE 4 END <= 2019 + 3
        GROUP BY    a.CPS
                   ,a.IncurredMonth / 100
                   ,CASE WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 1 AND 3 THEN 1
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 4 AND 6 THEN 2
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 7 AND 9 THEN 3
                         ELSE 4 END
                   ,a.BenefitCategoryID
                   ,a.IsRisk;

--SELECT * FROM #ClaimsTemp
        --Build Compiled Membership        
        INSERT  INTO Trend_CalcHistoricMembershipAJB
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,CASE WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 1 AND 3 THEN 1
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 4 AND 6 THEN 2
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 7 AND 9 THEN 3
                         ELSE 4 END AS 'QuarterID'
                   ,a.IsRisk
                   ,SUM (a.MemberMonths)
                   ,@LastUpdateByID
                   ,@LastUpdateDateTime
        FROM        #MMTemp a
       INNER JOIN   dbo.SavedPlanInfo b
               ON a.CPS = b.CPS
        WHERE       a.PlanYearID + CASE WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 1 AND 3 THEN 1
                                        WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 4 AND 6 THEN 2
                                        WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 7 AND 9 THEN 3
                                        ELSE 4 END <= @CurrentYear + @MaxCurrentYearQuarter
                    AND a.CPS = b.CPS
        GROUP BY    a.PlanYearID
                   ,CASE WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 1 AND 3 THEN 1
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 4 AND 6 THEN 2
                         WHEN a.IncurredMonth - ((a.IncurredMonth / 100) * 100) BETWEEN 7 AND 9 THEN 3
                         ELSE 4 END
                   ,a.CPS
                   ,a.IsRisk;

        --update progress table	   
        UPDATE  ##CapCompiledHeader
        SET     EndTime = GETDATE ()
        WHERE   DFRunID = @CurDFRunID
                AND PlanInfoID = @CurPlanInfoID;

        --loop around to next DFRunID/PlanInfoID		 
        FETCH NEXT FROM RunPlan_Cursor
        INTO @CurDFRunID
            ,@CurPlanInfoID;

    --Deallocate cursor 
    END;
CLOSE RunPlan_Cursor;
DEALLOCATE RunPlan_Cursor;



--INSERT Values into dbo.Trend_PerHistoricYearQuarter based on the historical membership table
--  INSERT  INTO dbo.Trend_PerHistoricYearQuarter
--          ( PlanYearID ,
--            QuarterID 
--          )
--          SELECT DISTINCT
--                  PlanyearID ,
--                  QuarterID
--          FROM    dbo.Trend_CalcHistoricMembership
--          WHERE    PlanyearID * 10 + QuarterID > 
--( SELECT  
--	MinYear * 10 + MinQuarter
--                    FROM    ( SELECT
--                                 MIN(PlanYearID) AS MinYear
--                              FROM dbo.Trend_PerHistoricYearQuarter
--                            ) a
--                            INNER JOIN ( SELECT
--						 MIN(QuarterID) AS MinQuarter ,
--                                           PlanYearID
--                                         FROM dbo.Trend_PerHistoricYearQuarter
--                                         GROUP BY PlanYearID
--                                        ) B 
--		  ON b.PlanYearID = a.MinYear
--                    )
--                  AND PlanyearID * 10 + QuarterID NOT IN (
--                  SELECT DISTINCT
--                          PlanyearID * 10 + QuarterID
--                  FROM    HistYearQuarter )



--Drop temporary tables
IF (SELECT  OBJECT_ID ('tempdb..#LoopInfo')) IS NOT NULL
    DROP TABLE #LoopInfo;
IF (SELECT  OBJECT_ID ('tempdb..#ClaimsTemp')) IS NOT NULL
    DROP TABLE #ClaimsTemp;
IF (SELECT  OBJECT_ID ('tempdb..#MMTemp')) IS NOT NULL DROP TABLE #MMTemp;
IF (SELECT  OBJECT_ID ('tempdb..##CapCompiledHeader')) IS NOT NULL
    DROP TABLE ##CapCompiledHeader;

GO
