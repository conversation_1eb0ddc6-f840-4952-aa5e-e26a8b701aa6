SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- FUNCTION NAME: fnPlanCountyProjectedMemberMonths
--
-- CREATOR: Christian Cofie 
--
-- CREATED DATE: 2007-Feb-05
-- HEADER UPDATED: 2011-Mar-30
--
-- DESCRIPTION: function responsible for listing Plan County Projected Member Months
--
-- PARAMETERS:
--	Input: 
--		@XForecastID 
--		@DEType 0: non-dual 1: dual 2: combined
--	Output:
--
-- TABLES:
--	Read:
--		LkpIntDemogIndicators
--		SavedPlanHeader
--		SavedPlanMemberMonthDetail
--		SavedPlanStateCountyDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS: fnSalesMembershipAdjustmentFactor
--
-- STORED PROCS:
--
-- NOTE: When updated, notify IT to update their code for spAppGetPlansInfo.
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- Feb-05-2007      1           Initial Version														Christian Cofie
-- Jun-12-2008      2           Replaced StartIndex, EndIndex w ForecastID, PlanVersion				Brian Lake
-- 2008-Sep-25		3			Added @UserID to the list of parameters.							Shannon Boykin
-- 2009-Feb-21      4           Added new dual/non dual member types for 2010 pricing				Brian Lake
-- 2009-Feb-22      5           Added @IsDual parameter to allow for returning duals and non		Brian Lake
--                                  duals by themselves.  2 for both
-- 2009-Feb-23      6           Changed IsDual to an int, added MARatingOptionID. Added join on  	Sandy Ellis
--                                  County Exclusions table, switched dual/nondual types
-- 2009-Mar-17      7           Data types															Sandy Ellis
-- 2010-Mar-09		8			Implemented new PlanYear methodology and independence for 2012		Joe Casey
-- 2010-Jun-14		9			Removed @UsedID and revised for coding standards					Michael Siekerka
-- 2010-Jul-08      10          Added join to LkpIntDemogIndicators to support change in            Jake Gaecke
--                                  SavedPlanMemberMonthDetail.
-- 2010-Aug-16		11			Removed PlanVersion													Michael Siekerka
-- 2010-Aug-23		12			Removed MM.IsExperience = 0											Joe Casey
-- 2010-Nov-08      13          Cleaned up joins.                                                   Casey Sanders
-- 2010-Nov-09      14          Combined 3 queries into 1 with fancy WHERE statement                Michael Siekerka
-- 2011-Jan-12		15			ProjectedMemberMonths is now Decimal								Jiao Chen
-- 2011-Feb-08		16			Added ISNULL check for ProjectedMemberMonths						Joe Casey
-- 2011-Mar-18		17			Changed ISNULL(SUM(... to SUM(ISNULL(...							Craig Wright
-- 2011-Mar-22		18			Added Case Statement so that the Sum of the Member Months			Craig Wright
--									now equals Sales Membership
-- 2011-Mar-30		19			Replaced Case Statement with fnSalesMembershipAdjustmentFactor		Craig Wright
-- 2011-Aug-25		20			Changed to account for Sales membership returning a table			Craig Wright
-- 2011-Sep-08		21			Changed to pull SalesAdjustmentFactor from SavedPlanAssumptions		Alex Rezmerski
-- 2011-Sep-09		22			Changed to account for SalesAdjustmentFactor being null				Alex Rezmerski
-- 2014-Mar-28		23			Added in OOA MM														Mason Roberts
-- 2014-Apr-04		24			Replaced 804 with @ForecastID										Mike Deren
-- 2020-Dec-14      25          Increasing scale to align BPT risk scores with MRA                  Brent Osantowski
-- 2022-Oct-05      26          Add @XVariable, (NOLOCK)											Phani Adduri
-- 2023-Aug-02		27			Added object schema	 												Sheetal Patil
-- 2024-Jul-02		28		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnPlanCountyProjectedMemberMonths]
(
    @ForecastID INT,
    @DEType INT
)
RETURNS @Results TABLE
(
    ForecastID INT,
    StateTerritoryID INT,
    CountyCode CHAR(3),
    ProjectedMemberMonths DECIMAL (28,15)
) AS
    BEGIN

    DECLARE @DEPoundPercent FLOAT,
	@XForecastID INT = @ForecastID;

    SELECT @DEPoundPercent = dbo.fnGetSafeDivisionResult(SUM(DE#Aged) , SUM(TotalAged))
	FROM dbo.fnAppGetBenchmarkMembership(@XForecastID)

        INSERT @Results
        SELECT
            @XForecastID,
            MM.StateTerritoryID, 
            MM.CountyCode, 
            ProjectedMemberMonths = --Scale to Sales Membership
				SUM(ISNULL(MM.MemberMonths,0))
        FROM dbo.SavedPlanMemberMonthDetail MM WITH (NOLOCK)
        INNER JOIN dbo.SavedPlanStateCountyDetail SC WITH (NOLOCK)
            ON SC.ForecastID = MM.ForecastID
            AND SC.StateTErritoryID = MM.StateTerritoryID
            AND SC.CountyCode = MM.CountyCode
        INNER JOIN dbo.LkpIntDemogIndicators dem WITH  (NOLOCK)
            ON MM.DemogIndicator = dem.DemogIndicator
        INNER JOIN dbo.SavedPlanAssumptions spd WITH (NOLOCK)
			ON spd.ForecastID = MM.ForecastID
        WHERE MM.ForecastID = @XForecastID
            AND dem.IsBiddable = 1 --biddable
            AND (dem.DualEligibleTypeID = CASE WHEN @DEType < 2 THEN @DEType END --Dual or Non-dual
                OR dem.DualEligibleTypeID < CASE WHEN @DEType = 2 THEN @DEType END) --Combined
            AND MM.MemberMonths >= 0 
            AND MM.MemberMonths IS NOT NULL
            AND SC.IsCountyExcludedFromBPTOutput = 0
        GROUP BY 
            MM.ForecastID, 
            MM.StateTerritoryID, 
            MM.CountyCode,
            spd.SalesMembership 

        UNION

		SELECT
	        OOA.ForecastID ,
	        StateTerritoryID = '00' ,
	        CountyCode = '000',
	        ProjectedMemberMonths = OOA.MemberMonths
				* --Making it DE specific
				CASE
					WHEN @DEType = 0 THEN (1 - @DEPoundPercent)
					WHEN @DEType = 1 THEN @DEPoundPercent
					ELSE 1
				END
		FROM dbo.SavedPlanOOAMemberMonthDetail OOA WITH (NOLOCK)
		WHERE OOA.ForecastID = @XForecastID

    RETURN
    END
GO
