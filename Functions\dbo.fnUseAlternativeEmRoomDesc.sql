SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnUseAlternativeEmRoomDesc]  ******/

--Creator: Christian Cofie		Date: May-09-2007
--Desc:This a special function written specically to provded <PERSON> with an alternative way for describing Emergence Room IN Cost Share .. Line f on worksheet 3 sec III.

CREATE FUNCTION [dbo].[fnUseAlternativeEmRoomDesc]
(	
	@ForecastID int
)
RETURNS bit
AS
BEGIN 
	
    Declare  @Result bit

    IF @ForecastID IN
		(	--This list was provided by <PERSON>.
			346,349,350,351,352,353,355,357,358,360,362,363,364,365,366,368,401,402,403,405,
			406,407,408,415,417,421,423,426,427,429,430,431,433,434,435,437,438,443,448,449,
			450,453,454,455,457,458,461,462,463,464,465,469,480,481,482,889,890,891,927,928,
			929,931,934,935,936,937,939,940,941,942,943,944,945,947,948,950,952,953,956,957,
			959,961,963,964,965,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,
			1027,1140,1217,1377,1459,1569
		)
        	Set @Result=1
   ELSE
            	Set @Result=0

	Return 	@Result 
END
GO
