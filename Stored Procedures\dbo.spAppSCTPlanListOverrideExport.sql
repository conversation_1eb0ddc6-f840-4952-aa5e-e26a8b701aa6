﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- Stored Procedure
-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 2024-OCT-26 

-- Description: spAppSCTPlanListOverrideExport 
-- LkpIntAddedBenefitType and LkpIntAddedBenefitExpenseDetail table.

-- PARAMETERS:  
-- Input:  
--
-- Output:  
--  
-- TABLES:  
-- Read:
--	  SCTPlanListOverride
--  
-- Write:
--		
--      
-- VIEWS:  
--  
-- FUNCTIONS:
--
-- STORED PROCS:  
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-OCT-26         1          Initial Version                                                    Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------  
-- =======================================================================================================================
CREATE PROCEDURE [dbo].[spAppSCTPlanListOverrideExport]
@WhereIn VARCHAR(MAX)   
AS
BEGIN
SET NOCOUNT ON;

SELECT 'None' AS CPS

SELECT a.BidYear,a.ContractPBPSegment,a.PlanYearID,a.PlanYearContractPBPSegment
FROM dbo.SCTPlanListOverride a WITH(NOLOCK) 
JOIN dbo.SavedPlanInfo  b WITH(NOLOCK) ON b.CPS=a.ContractPBPSegment
JOIN dbo.SavedForecastSetup c WITH(NOLOCK) ON c.PlanInfoID=b.PlanInfoID AND c.ForecastID IN(SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
ORDER BY ContractPBPSegment 

SELECT  DISTINCT ContractPBPSegment,Division,Region,SubRegion,PrimaryState,
Market,PlanType, CASE WHEN IsSNP = 1 THEN 'Yes' ELSE 'No' END AS IsSNP
,SNPType,DSNPSubType,Product,PlanCategory
,TargetedSegment,CASE WHEN IsPassive = 1 THEN 'Yes' ELSE 'No' END AS IsPassive
,GivebackRange,INNMedicalDeductibleRange, CASE WHEN a.IsRiskPlan = 1 THEN 'Yes' ELSE 'No' END AS IsRiskPlan
,IsDelegated
,NewPlanFlag 
,ConcurrentPlan
FROM dbo.SCTPlanListOverride a WITH(NOLOCK) 
JOIN dbo.SavedPlanInfo  b WITH(NOLOCK) ON b.CPS=a.ContractPBPSegment
JOIN dbo.SavedForecastSetup c WITH(NOLOCK) ON c.PlanInfoID=b.PlanInfoID AND c.ForecastID IN(SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
ORDER BY ContractPBPSegment 


END
GO
