SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO


-- ----------------------------------------------------------------------------------------------------------------------              
-- PROCEDURE NAME: [dbo].[spAppTrendUploadRelativityCMSReimb]    
--              
-- TYPE: SAME              
--              
-- AUTHOR: <PERSON> Pant            
--              
-- CREATED DATE: 2020-Apr-16	             
--              
-- DESCRIPTION: Procedure responsible for executing Trend_SavedRelativityCMSReimb
-- PARAMETERS:              
-- Input:               
--@UserID             
-- TABLES:               
-- Read:              
--                  
--                 
-- Write:              
--              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:              
--                
-- $HISTORY               
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE			VERSION		CHANGES MADE													DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Apr-16		1		Initial Version													   Kiran Pant  
-- 2020-Jun-10		2	Renamed the sp name in the comment section with prefix spAppTrend	   Kiran Pant 
-- 2021-Mar-02		3	Changes done w.r.t. User story : 1986699							Priyesh Singh
-- ----------------------------------------------------------------------------------------------------------------------   
--Trend_SavedRelativityCMSReimb
CREATE PROCEDURE [dbo].[spAppTrendUploadRelativityCMSReimb]
(     @RelativityImportData RelativityImports READONLY,
      @UserID varchar(7),
	  @MessageFromBackend VARCHAR(MAX) OUTPUT,
	  @Result BIT OUT
)
AS
 SET NOCOUNT ON
    
       BEGIN
	
	   DECLARE @Count int=0;
	   SELECT *  INTO #Temp FROM  @RelativityImportData
	 
BEGIN TRY
    BEGIN TRANSACTION

DELETE OC 
FROM Trend_SavedRelativityCMSReimb OC
--INNER JOIN #Temp a 
--ON  OC.CPS=a.CPS AND OC.PlanYearID=a.PlanYearID
--WHERE  OC.CPS=a.CPS AND OC.PlanYearID=a.PlanYearID

INSERT INTO dbo.Trend_SavedRelativityCMSReimb
				
					SELECT 
					    CPS,
						PlanYearID,  
						QuarterID,
					    ReportingCategory,
						CostRelativity,
					    UseRelativity,
						@UserID,
						GETDATE()
						FROM #Temp 

	 -- Change w.r.t. user story : 1986699 : starts --

	 -- 1: Execute SP1 : Trend_spRefreshUCDBCurrentYear
	 EXECUTE Trend_spRefreshUCDBCurrentYear @UserID

	 -- 2: Execute SP2 : Trend_NormProcess_spCalcIsPlanLevelCMSReimb
	 EXECUTE Trend_NormProcess_spCalcIsPlanLevelCMSReimb @UserID

	 -- 3: Execute SP3 : Trend_spCalcUCDBBidYear
	 EXECUTE Trend_spCalcUCDBBidYear NULL,NULL,NULL,@UserID

	 -- 4: Execute SP4 : Trend_ProjProcess_spCalcIsPlanLevel_Trends (@ComponentID=9)
	 --EXECUTE Trend_ProjProcess_spCalcIsPlanLevel_Trends 9, NULL, NULL, NULL, @UserID

	 -- Change w.r.t. user story : 1986699 : Ends --


	 SET @Count = (SELECT COUNT(*) FROM dbo.Trend_SavedRelativityCMSReimb WITH (NOLOCK))

	 SET @Result=1

	 SET @MessageFromBackend='Successfully Imported '+Convert(varchar,@Count)+': Row(s)';	

	    COMMIT TRANSACTION
    END TRY
   
BEGIN CATCH

		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()
		SET @Result=0;
	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			

	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
	ROLLBACK TRANSACTION;
	set @Result=0
     
	---Insert into app log for logging error------------------
	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@UserID;
 END CATCH;
    
	--ELSE 
	--	SET @ValidationMessage = 'You Do Not Have The Authority To Modify OSB Data' 
	END

GO
