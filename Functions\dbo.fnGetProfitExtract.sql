SET QUOTED_IDENTIFIER ON
GO
SET AN<PERSON>_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetProfitExtract]   ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetProfitExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-21
-- HEADER UPDATED: 2011-Dec-21
--
-- DESCRIPTION: Designed to extract fields for Profit Percent - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	
--		SavedPlanAssumptions
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-21		1			Initial Version														Bobby Jaegers
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetProfitExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	ProfitPercent Decimal(10,8)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT  ForecastID,
					ProfitPercent
			FROM SavedPlanAssumptions
			
       
	ELSE
		INSERT @Results
			SELECT  ForecastID,
					ProfitPercent
			FROM SavedPlanAssumptions
			WHERE ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))

RETURN
END
GO
