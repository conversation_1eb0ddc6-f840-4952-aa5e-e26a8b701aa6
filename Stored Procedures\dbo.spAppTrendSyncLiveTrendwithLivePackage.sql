SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================              
-- Author:  Chhavi <PERSON>          
-- Create date: 03-27-2020        
-- Description: sync stored procedures on button click    
--              
--              
-- PARAMETERS:              
--	Input:	@LastUpdateByID        
--
--	Output: @MessageFromBackend, @Result
--
-- TABLES:	Read: None
--
--			Write: None   
--
-- VIEWS: None            
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:	Trend_spCalcFileSync
--					Trend_NormProcess_spCalcIsPlanLevelCMSReimb
--					Trend_NormProcess_spCalcIsPlanLevelContractual
--					Trend_NormProcess_spCalcIsPlanLevelInducedUtilization
--					Trend_NormProcess_spCalcIsPlanLevelPopulation
--					Trend_NormProcess_spCalcIsPlanLevelOutlierClaims
--					Trend_NormProcess_spCalcNotPlanLevel
--					Trend_NormProcess_spCalcCombined
--					Trend_NormProcess_spCalcNormalized
--					Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault
--					Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides
--					Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal
--					Trend_ProjProcess_spCalcPlanTrends_RepCat
--					Trend_ProjProcess_spCalcPlanAdjmt_RepCat_PlanExp
--					spAppAddLogEntry
--
-- $HISTORY                 
-- ----------------------------------------------------------------------------------------------------------------------                
-- DATE			VERSION		CHANGES MADE														DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------                
-- 2020-Mar-27		1		Initial version.													Chhavi Sinha  
-- 2020-Apr-10		2		Passing @writeindicator parameter in								Chhavi Sinha  
--							Trend_ProjProcess_spCalcPlanTrends_RepCat  
-- 2020-Jun-22		3		Corrected Begin Try													Deepali
-- 2021-Mar-31		4		Add Trend_NormProcess_spCalcIsPlanLevel SPs to execution list		Jake Lewis
-- 2021-May-25		5		Referece IsIncludeInTrendSnapshot for normalized audit fix			Jake Lewis
-- 2022-Feb-03		6		Remove IsIncludeInTrendSnapshot reference. We will be executing		Craig Nielsen
--							this sp manually so we do not inadvertantly change the snapshot	
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [dbo].[spAppTrendSyncLiveTrendwithLivePackage]
@LastUpdateByID      CHAR(7)
,@MessageFromBackend NVARCHAR(MAX) OUTPUT
,@Result             BIT           OUT

AS
    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        BEGIN TRY

            BEGIN TRANSACTION;

            BEGIN
                EXEC dbo.Trend_spCalcFileSync @LastUpdateByID;
                --EXEC dbo.Trend_NormProcess_spIsIncludeInTrendSnapshot @LastCopyByID = @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcIsPlanLevelCMSReimb @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcIsPlanLevelContractual @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcIsPlanLevelInducedUtilization @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcIsPlanLevelPopulation @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcIsPlanLevelOutlierClaims @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcNotPlanLevel @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcCombined @LastUpdateByID;
                EXEC dbo.Trend_NormProcess_spCalcNormalized @LastUpdateByID;
                EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_Trends @LastUpdateByID;
                EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingDefault NULL
                                                                                ,@LastUpdateByID;
                EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides NULL
                                                                                  ,@LastUpdateByID;
                EXEC dbo.Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal NULL
                                                                              ,@LastUpdateByID;
                EXEC dbo.Trend_ProjProcess_spCalcPlanTrends_RepCat NULL, 1, @LastUpdateByID;    --Andy Blink: Changed the second paramter to 1 instead of NULL
                EXEC dbo.Trend_ProjProcess_spCalcPlanAdjmt_RepCat_PlanExp NULL
                                                                         ,1
                                                                         ,@LastUpdateByID;
            END;

            COMMIT TRANSACTION;

            SET @MessageFromBackend = 'Your trends were successfully updated'; -- SET @MessageFromBackend = 'Your changes were not saved' ;  
            SET @Result = 1;

        END TRY

        BEGIN CATCH

            ROLLBACK TRANSACTION;

            SET @Result = 0;
            SET @MessageFromBackend = 'Your trends were not successfully updated, please try again';
            --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'      
            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @ErrorException NVARCHAR(4000);
            DECLARE @errSrc      VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL')
                   ,@currentdate DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :'
                                      + CAST(@ErrorState AS VARCHAR);
            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);

            ---Insert into app log for logging error------------------      
            EXEC dbo.spAppAddLogEntry @currentdate
                                     ,''
                                     ,'ERROR'
                                     ,@errSrc
                                     ,@ErrorMessage
                                     ,@ErrorException
                                     ,@LastUpdateByID;

        END CATCH;

    END;
GO
