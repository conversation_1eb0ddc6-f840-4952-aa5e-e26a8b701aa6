SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnMIN
--
-- AUTHOR: <PERSON> Cofie
--
-- CREATED DATE: 2007-Apr-23
--
-- DESCRIPTION: Compares two values and returns the minimum.  Used to simplify otherwise more complex statements.
--
-- PARAMETERS:
--	Input:
--		@Number1
--		@Number2
--
-- RETURNS: @Min, minimum value
--
-- TABLES: 
--	Read: NONE
--	Write: NONE 
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		    CHANGES MADE						                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Apr-23		1			Initial Version							                Christian Cofie
-- 2007-Oct-15		2			Revised code to bring it into coding standards.			Shannon Boykin
-- 2010-Sep-27      3           Revised for 2012 database                               Michael Siekerka
--
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnMIN]
(
    @Number1 FLOAT,
    @Number2 FLOAT
)
RETURNS FLOAT AS
    BEGIN
        DECLARE @Min FLOAT
        IF @Number1 < @Number2
            SET @Min = @Number1
        ELSE
            SET @Min = @Number2
        RETURN  @Min
    END
GO
