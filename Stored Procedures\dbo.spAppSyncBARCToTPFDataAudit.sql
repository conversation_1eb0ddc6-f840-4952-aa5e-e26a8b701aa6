SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  Pooja Dahiya      
-- Create date: 12-31-2019
-- Description:  spAppSyncBARCToTPFDataAudit
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:    
-- Read:      

-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			 VERSION    CHANGES MADE													DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2019-Dec-31		1		Initial version.												Pooja Dahiya
-- 2020-Feb-02      2       Included Market Adjustment synch                                Ramandeep Saini
-- 2020-Mar-12      3       updated the commit transaction                                  Ramandeep Saini     
-- 2020-Jun-23      4	    Removing TPF related tables for BARC sync process               Surya Murthy
-- 2020-Jul-14      5		Optimized BARC to MAAUI logic 							        Ramandeep Saini
-- 2020-Sep-14      6		Snap shot isolation implemented									Surya Murthy
-- 2020-Nov-02      7		Primary key issue												Surya Murthy
-- 2022-Aug-25		8		Added Risk score sync logic										Vikrant Bagal
-- 2022-Dec-02      9		Alternative for SNAPsot isolcation								Surya Murthy
-- 2023-Jan-03		10		Removed Risk score sync logic									Vikrant Bagal
-- 2023-Jun-05		11		BARC sync optimization											Surya Murthy
-- 2023-Sep-05		12		BARC date added													Surya Murthy
-- 2023-Sep-25	    13		Rowlocks added for deadlock issues								Surya Murthy
-- 2024-Feb-16	    14		Missing NOLOCK for memeber months stage table					Surya Murthy
-- 2024-Jun-18	    15		Adding Async logic for BARC process								Surya Murthy
-- 2024-Oct-17	    16		Adding SCT Process												Surya Murthy
-- 2025-Mar-27	    17		Running risk score for one plan logic added						Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------           
CREATE PROCEDURE [dbo].[spAppSyncBARCToTPFDataAudit]
@PlanIDList VARCHAR(MAX),
@ProcessId INT,
@Result VARCHAR(MAX) OUTPUT,
@LastUpdateByID CHAR(7)

AS
BEGIN 

DECLARE @ErrorMessage NVARCHAR(4000);  
DECLARE @ErrorSeverity INT;  
DECLARE @ErrorState INT;
DECLARE @ErrorException NVARCHAR(4000); 
DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),
@currentdate datetime=getdate();
BEGIN TRY
BEGIN TRANSACTION initilizedata;	
--SCT memeber months logic starts here
IF @ProcessId=4
BEGIN
	DELETE FROM dbo.BarcSctRiskScores WITH(ROWLOCK) Where BidYearPlanInfoID in (SELECT BidYearPlanInfoID FROM dbo.BarcSctRiskScores_Stage WITH (NOLOCK) WHERE CurrentUserId = @LastUpdateByID);
	INSERT INTO dbo.BarcSctRiskScores WITH(ROWLOCK)
	(
	[PlanYear],	
	[CPS],
	[BidYearPlanInfoID],
	[startRs],
	[CMS],
	[CI],
	[endRs],
	[avgMbr],
	[LastUpdateByID],
	[LastUpdateDateTime])	
	SELECT
	[PlanYear],	
	[CPS],
	[BidYearPlanInfoID],
	[startRs],
	[CMS],
	[CI],
	[endRs],
	[avgMbr],
	[LastUpdateByID],
	[LastUpdateDateTime]
	FROM [dbo].[BarcSctRiskScores_Stage] WITH (NOLOCK) WHERE CurrentUserId = @LastUpdateByID;
	DELETE FROM [dbo].[BarcSctRiskScores_Stage] WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;

	DELETE FROM dbo.BarcSctMemberMonths WITH(ROWLOCK) Where PlanInfoID in (SELECT PlanInfoID FROM dbo.BarcSctMemberMonths_Stage WITH (NOLOCK) WHERE CurrentUserId = @LastUpdateByID);
	INSERT INTO dbo.BarcSctMemberMonths WITH(ROWLOCK)
	([PlanInfoID],
	[CPS],
	[bidYearClass],
	[dePound],
	[bidYearMemberMonths],
	[LastUpdateByID],
	[LastUpdateDateTime])	
	SELECT
	[PlanInfoID],
	[CPS],
	[bidYearClass],
	[dePound],
	[bidYearMemberMonths],
	[LastUpdateByID],
	[LastUpdateDateTime]
	FROM [dbo].[BarcSctMemberMonths_Stage] WITH (NOLOCK) WHERE CurrentUserId = @LastUpdateByID;
	DELETE FROM [dbo].[BarcSctMemberMonths_Stage] WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
END
--SCT memeber months logic ends here

--Market Adjustment and Membership logic starts here
IF @ProcessId=1
BEGIN
	DECLARE @tblRollupQuarterizedData RollupQuarterizedType ,
	@tblMarketADJData MarketADJType;
	 INSERT INTO @tblRollupQuarterizedData
	 SELECT 
		   [CPS]
		  ,[countyCode]
		  ,[PlanYearID]
		  ,[quarter]
		  ,[currentYearClass]
		  ,[bidYearClass]
		  ,[dePound]
		  ,[bidYearMemberMonths]
		  ,[userID]
		  ,[updated] 
	 FROM dbo.TblRollupQuarterizedData_Stage WITH (NOLOCK) WHERE CurrentUserId = @LastUpdateByID;
	 INSERT INTO @tblMarketADJData
	 SELECT 
		  [CPS]
		  ,[bidYearClass]
		  ,CASE WHEN [dePound]='Y'THEN 1 ELSE 0 END AS [dePound]
		  ,[marketAdjustment]
		  ,[userID]
		  ,[updated]      
	 FROM dbo.TblMarketADJData_Stage WITH (NOLOCK) WHERE CurrentUserId = @LastUpdateByID;

	 DELETE FROM dbo.TblRollupQuarterizedData_Stage WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
	 DELETE FROM dbo.TblMarketADJData_Stage  WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
 END
 --Market Adjustment and Membership logic ends here

 --Member months logic starts here
 IF @ProcessId=2
 BEGIN

	 DECLARE @barc_membership_inputTable ImportMemberMonthsDataTable;

	 IF (SELECT  OBJECT_ID ('tempdb..#temo_barc_membership_inputTable')) IS NOT NULL
	 BEGIN
			  Drop table #temo_barc_membership_inputTable 
	 END
		CREATE TABLE #temo_barc_membership_inputTable(
			[id] [INT] NOT NULL IDENTITY PRIMARY KEY,
			[FilePath] [VARCHAR](50) NULL,
			[ForecastID] [INT] NOT NULL,
			[ZipCode] [VARCHAR](10) NULL,
			[AgedNonDual] [DECIMAL](28, 15) NOT NULL,
			[ESRDNonDual] [DECIMAL](28, 15) NOT NULL,
			[HospiceNonDual] [DECIMAL](28, 15) NOT NULL,
			[AgedDual] [DECIMAL](28, 15) NOT NULL,
			[ESRDDual] [DECIMAL](28, 15) NOT NULL,
			[HospiceDual] [DECIMAL](28, 15) NOT NULL,
			[OOAMemberMonths] [DECIMAL](28, 15) NOT NULL,
			[Updated] DATETIME NULL,
			[UserId] [CHAR](7) NULL,
			[LastUpdateByID] [CHAR](7) NOT NULL
		);
		INSERT INTO #temo_barc_membership_inputTable SELECT * FROM dbo.ImportMemberMonthsDataTable_Stage WITH(NOLOCK) WHERE LastUpdateByID = @LastUpdateByID;
		INSERT INTO @barc_membership_inputTable SELECT * FROM #temo_barc_membership_inputTable;
		DELETE FROM dbo.ImportMemberMonthsDataTable_Stage  WITH(ROWLOCK) WHERE LastUpdateByID = @LastUpdateByID;
		declare @memberForcastIds varchar(max);	
		select @memberForcastIds = coalesce(@memberForcastIds + ',', '') +  convert(varchar(12),ForecastID)from @barc_membership_inputTable group by ForecastID;
	END
	--Member months logic ends here
--Risk score logic starts here
 IF @ProcessId=3
 BEGIN

	DECLARE @rsInputTable ImportRiskFactorsDataTable;
	INSERT INTO @rsInputTable SELECT FilePath,ForecastID,ZipCode,ExpNonDual,ProjNonDual,ExpDual,ProjDual
	FROM dbo.ImportRiskFactorsDataTable_Stage WITH(NOLOCK) WHERE CurrentUserId = @LastUpdateByID;
	DELETE FROM dbo.ImportRiskFactorsDataTable_Stage  WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
 END
 --Risk score logic ends here
COMMIT TRANSACTION initilizedata;
END TRY
BEGIN CATCH
	SET @Result=0;
	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)

	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID;	 
	SET @Result = '0:Data Initilization Failed :'+@ErrorMessage;
END CATCH

	--BARC MM RS import functionalty starts here
	DECLARE @MessageFromBackend VARCHAR(MAX),
	        @MemResult BIT;
	IF EXISTS (SELECT 1 FROM @barc_membership_inputTable) AND @ProcessId=2
	BEGIN
		BEGIN TRY
			--loop the each row
			 WHILE (SELECT COUNT(*) FROM @barc_membership_inputTable WHERE 1=1) > 0
			 BEGIN
					DECLARE @mmforecastid INT;
					SELECT TOP 1 @mmforecastid = ForecastID FROM @barc_membership_inputTable ORDER BY ForecastID;

					DECLARE @tempbarc_membership_inputTable ImportMemberMonthsDataTable;
					INSERT INTO @tempbarc_membership_inputTable SELECT * FROM @barc_membership_inputTable WHERE ForecastID=@mmforecastid;					

					EXEC dbo.spAppGetBatchImportMemberMonthsWithPlanAdminBlend @inputTable = @tempbarc_membership_inputTable,
																   @ForecastIDVal = @memberForcastIds,
																   @UserID = @LastUpdateByID,
																   @MessageFromBackend = @MessageFromBackend OUTPUT,
																   @Result = @MemResult OUTPUT
					DELETE FROM @barc_membership_inputTable WHERE ForecastID=@mmforecastid;
			END --ended
		END TRY
		BEGIN CATCH
			SELECT @ErrorMessage='Error in member months data '+ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE();		 
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			RETURN 0
		END CATCH	
	END
	IF EXISTS (SELECT 1 FROM @rsInputTable) AND @ProcessId=3
	BEGIN
		BEGIN TRY
			--loop the each row
			 WHILE (SELECT COUNT(*) FROM @rsInputTable WHERE 1=1) > 0
			 BEGIN
					DECLARE @rsforecastid INT;
					SELECT TOP 1 @rsforecastid = ForecastID FROM @rsInputTable ORDER BY ForecastID;

					DECLARE @rstempinputtable ImportRiskFactorsDataTable;
					INSERT INTO @rstempinputtable SELECT * FROM @rsInputTable WHERE ForecastID=@rsforecastid;
					EXEC dbo.spUploadRiskFactorBulkUpload @inputTable = @rstempinputtable,
											  @UserID = @LastUpdateByID,
											  @MessageFromBackend = @MessageFromBackend OUTPUT,
											  @Result = @MemResult OUTPUT;
				   DELETE FROM @rsInputTable WHERE ForecastID=@rsforecastid;
			 END --ended

		END TRY
		BEGIN CATCH
			SELECT @ErrorMessage='Error in Risk score data '+ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE();		
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			RETURN 0
		END CATCH	
	END
	--BARC MM RS import functionalty ends here


	BEGIN TRY
	BEGIN TRANSACTION barcsyncprocess;	



	DECLARE @errorMSg VARCHAR(MAX);

		IF EXISTS (SELECT 1 FROM @tblRollupQuarterizedData) AND @ProcessId=1
		BEGIN
		--Step 1

		 DELETE  a FROM dbo.Trend_SavedPopulationBarcBidYearMembership a  WITH(ROWLOCK)
		 JOIN @tblRollupQuarterizedData b ON a.CPS = b.CPS		 

		--Step 2 --Insert into TPF.Assumptions_DeMo_Projected_BidYear_Regression_MRA values for these plans from [BARC].bidYearRollupQuarterized]
		  INSERT INTO [dbo].Trend_SavedPopulationBarcBidYearMembership WITH(ROWLOCK)
           ([CPS]
           ,[SSStateCountyCD]
           ,[PlanYearID]
           ,[QuarterID]
		   ,[PriorYearBarcClass]
           ,[BarcClass]
           ,[IsDEPound]
           ,[Bid_MM]
           ,[LastUpdateByID]
           ,[LastUpdateDateTime]) 
     	SELECT  [CPS]
      ,[countyCode]
      ,[PlanYearID]
      ,[quarter]
      ,[currentYearClass]
	  ,[bidYearClass]
      ,[dePound]
      ,[bidYearMemberMonths]
      ,[userID]
      ,[updated] FROM @tblRollupQuarterizedData  

		END;
		------------Market Adj---------
		  IF EXISTS (SELECT 1 FROM @tblMarketADJData) AND @ProcessId=1
		  BEGIN


			DELETE a FROM [dbo].[Trend_SavedPopulationMarketAdjustment] a WITH(ROWLOCK)

			JOIN @tblMarketADJData b ON a.CPS = b.CPS




		  INSERT INTO [dbo].[Trend_SavedPopulationMarketAdjustment] WITH(ROWLOCK)
           ([CPS]
           ,[BarcClass]
           ,[IsDEPound]
           ,[MarketAdjustment]
           ,[LastUpdateByID]
           ,[LastUpdateDateTime])
       	SELECT  [CPS]
      ,[bidYearClass]
      ,[dePound]
      ,[marketAdjustment]
      ,[userID]
      ,[updated] FROM @tblMarketADJData  

		END;



	  Declare @setp3 bit =1
				---------end-------------

		IF(EXISTS(SELECT 1 FROM @tblMarketADJData) OR EXISTS(SELECT 1 FROM @tblRollupQuarterizedData)) AND @ProcessId=1
		BEGIN
		--logic to get required planinfoids from market adjustment and membership tables to send it further processsing starts here
			IF (SELECT  OBJECT_ID ('tempdb..#cpstemp')) IS NOT NULL
			BEGIN
				DROP table #cpstemp
			END
					  CREATE TABLE #cpstemp (PlanInfoId int)
					  INSERT INTO #cpstemp
					   (
						PlanInfoId
					   )		 
			SELECT DISTINCT(b.PlanInfoID) AS PlanInfoId FROM @tblMarketADJData a
			INNER JOIN dbo.SavedPlanInfo  b WITH(NOLOCK) ON a.CPS=b.CPS AND b.PlanYear=dbo.fnGetBidYear()

			INSERT INTO #cpstemp
					   (
						PlanInfoId
					   )		 
			SELECT DISTINCT(b.PlanInfoID) AS PlanInfoId FROM @tblRollupQuarterizedData a
			INNER JOIN dbo.SavedPlanInfo b WITH(NOLOCK) ON a.CPS=b.CPS AND b.PlanYear=dbo.fnGetBidYear()

			IF (SELECT  OBJECT_ID ('tempdb..#uniquecpstemp')) IS NOT NULL
			BEGIN
				DROP table #uniquecpstemp
			END
			SELECT DISTINCT(PlanInfoId) INTO #uniquecpstemp  FROM #cpstemp

			select @PlanIDList = coalesce(@PlanIDList + ',', '') +  convert(varchar(12),PlanInfoId) FROM #uniquecpstemp

		--logic to get required planinfoids from market adjustment and membership tables to send it further processsing ends here

			EXEC [dbo].[Trend_ProjProcess_spUpdateBARCSync]  @LastUpdateByID,@PlanIDList,NULL,NULL,@errorMSg OUT;
		END		


		 IF @errorMSg <> 'N/A' --Default value which indicates no errors

		 BEGIN 
			 set @setp3=0
			 SET @Result = '0:Failed to Sync BARC to TPF because :'+@errorMSg;


			 Rollback TRANSACTION barcsyncprocess 
			 RAISERROR(@errorMSg,16,1) 
		END 
		ELSE IF @errorMSg='N/A'
		begin 
			SET @errorMSg=''

		end 
	COMMIT TRANSACTION barcsyncprocess;	
	SET @Result = '1';
END TRY
BEGIN CATCH




	SET @Result=0;

	SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)

	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

	Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID;

	IF @setp3=0 
	begin
	SET @Result = '0:Failed to Sync BARC to TPF because :'+@errorMSg;

	end 
	else
	begin
	Rollback TRANSACTION barcsyncprocess 
	SET @Result = '0:Failed to Sync BARC to TPF because :'+CAST(ERROR_MESSAGE() AS VARCHAR(MAX));

	end 


END CATCH;
END;
GO
