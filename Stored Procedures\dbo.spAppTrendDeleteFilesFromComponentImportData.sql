SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
  
-- =============================================              
-- Author:  Chhavi Sinha              
-- Create date: 2020-05-11        
-- Description: Delete Files From Component Import Annual and Quarterly Tables.     
--              
--              
-- PARAMETERS:              
-- Input:                
            
-- TABLES:              
-- Read:              
-- Write:              
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:               
             
        
-- $HISTORY                 
        
-- ----------------------------------------------------------------------------------------------------------------------                
-- DATE    VERSION   CHANGES MADE                                      DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------                
-- 2020-05-11  1    Initial version.         Chhavi Sinha 
-- 2020-17-06	2	Added logic to get count of files	Deepali     
-- ----------------------------------------------------------------------------------------------------------------------                
 -- =============================================           
    
       
   CREATE Procedure [dbo].[spAppTrendDeleteFilesFromComponentImportData]   
     
   @DeleteData VARCHAR(MAX),  
   @LastUpdateByID CHAR(7),  
   @MessageFromBackend NVARCHAR(MAX) OUTPUT,   
 @Result BIT OUT    
AS  
    BEGIN   
  
        BEGIN TRANSACTION;  
        BEGIN TRY     
   CREATE TABLE #temp
   (DELETEfiles VARCHAR(MAX))
   INSERT INTO #temp
   (
       DELETEfiles
   )   
   (SELECT  Value FROM    dbo.fnStringSplit(@DeleteData, ','))
   
   DECLARE @count VARCHAR(20)
   SET @count = (SELECT COUNT(*) FROM #temp)
      DELETE  
            FROM    dbo.Trend_SavedComponentImportAnnual                       
            WHERE  ComponentVersionID in (SELECT  Value FROM    dbo.fnStringSplit(@DeleteData, ',') )   
               DELETE  
            FROM    dbo.Trend_SavedComponentImportQuarterly                       
           WHERE  ComponentVersionID in (SELECT  Value FROM    dbo.fnStringSplit(@DeleteData, ',') )   
             
             
 COMMIT TRANSACTION;       
   SET @MessageFromBackend = 'Deleted ' +@count+ ' import file(s) successfully';     
         SET @Result = 1;       
    END TRY        
    BEGIN CATCH        
    SET @Result=0;     
        SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'        
        DECLARE @ErrorMessage NVARCHAR(4000);        
        DECLARE @ErrorSeverity INT;        
        DECLARE @ErrorState INT;        
        DECLARE @ErrorException NVARCHAR(4000);        
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),        
                @currentdate DATETIME = GETDATE();        
        
        SELECT @ErrorMessage = ERROR_MESSAGE(),        
               @ErrorSeverity = ERROR_SEVERITY(),        
               @ErrorState = ERROR_STATE(),        
               @ErrorException        
             = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'        
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);        
        
        ROLLBACK TRANSACTION;        
        
        ---Insert into app log for logging error------------------        
 EXEC spAppAddLogEntry @currentdate,        
                              '',        
                              'ERROR',        
                              @errSrc,        
                              @ErrorMessage,        
                              @ErrorException,        
                             @LastUpdateByID;        
        
    END CATCH;        
END;     
   
  
GO
