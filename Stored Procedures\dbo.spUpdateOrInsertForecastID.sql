SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateOrInsertForecastID
--
-- AUTHOR: Nate Jacoby
--
-- CREATED DATE: 2011-Jan-14
--
-- DESCRIPTION: Responsible for updating the ClaimForecastIDs.
--
-- PARAMETERS:
--	Input:
--    @ForecastID
--    @MARatingOptionID
--    @ClaimForecastID
--	Output: NONE
--
-- RETURNS:
--
-- TABLES:
--	Read:
--    SavedPlanDetail
--
--	Write:
--    SavedPlanDetail
--
-- VIEWS:
--	Read:
--
-- STORED PROCS:
--	Executed:
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------
-- DATE         VERSION CHANGES MADE                                                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------
-- 2011-JAN-14  1       INITIAL VERSION                                                 Nate Jacoby
-- 2011-Feb-02	2		Updated to include UserID, LastDateTime and IsToReprice 		Nate Jacoby
-- 2019-Jun-28	3		Replace SavedPlanHeader with SavedForecastSetup			        Pooja Dahiya
-- 2019-Oct-30	4       Replace @UserID from char(13) to char(7)						Chhavi Sinha
-- --------------------------------------------------------------------------------------------------------------------
CREATE  PROCEDURE [dbo].[spUpdateOrInsertForecastID]
    (
    @ForecastID INT,
    @MARatingOptionID INT,
    @ClaimForecastID INT,
    @UserID VARCHAR(7)
	)
AS


DECLARE @LastDateTime DATETIME
SET @LastDateTime = GETDATE()

SET NOCOUNT ON
----------------------------------------------------------------------
-- UPDATES IsToReprice = TRUE                                       --
----------------------------------------------------------------------    
    UPDATE SavedForecastSetup
		SET IsToReprice = 1,
			LastUpdateByID = @UserID,
			LastUpdateDateTime = @LastDateTime
	WHERE ForecastID = @ForecastID
		
----------------------------------------------------------------------
-- IF NULL OR '' UPDATE INFO                                        --
----------------------------------------------------------------------
IF @ClaimForecastID IS NULL OR @ClaimForecastID = ''
    ------------------------------------------------------------------
    -- REMOVES FORECASTID                                           --
    ------------------------------------------------------------------
    BEGIN     
        UPDATE SavedPlanDetail
        SET ClaimForecastID = NULL
        WHERE PlanYearID = dbo.fnGetBidYear()
            AND ForecastID = @ForecastID
            AND MARatingOptionID = @MARatingOptionID
    END
----------------------------------------------------------------------
-- IF NOT NULL OR '' UPDATE INFO                                    --
----------------------------------------------------------------------
ELSE
    BEGIN
        --------------------------------------------------------------
        -- UPDATE FORECASTID                                        --      
        --------------------------------------------------------------
        IF @ClaimForecastID > 0
            BEGIN     
                UPDATE SavedPlanDetail
                SET ClaimForeCastID = @ClaimForecastID,
					LastUpdateByID = @UserID,
					LastUpdateDateTime = @LastDateTime
                WHERE PlanYearID = dbo.fnGetBidYear()
                    AND ForecastID = @ForecastID
                    AND MARatingOptionID = @MARatingOptionID
            END
        --------------------------------------------------------------
        -- INFO NOT LOADED OR NOT ASSIGNED, SELECT FROM ELSEWHERE   --   
        --------------------------------------------------------------
        ELSE        
            BEGIN            
                UPDATE SavedPlanDetail
                SET ClaimForecastID = 
                        (SELECT ClaimForecastID
                        FROM SavedPlanDetail
                        WHERE ForecastID = @ForecastID
                            AND PlanYearID = dbo.fnGetBidYear()
                            AND MARatingOptionID = @MARatingOptionID),
					LastUpdateByID = @UserID,
					LastUpdateDateTime = @LastDateTime                            
                WHERE PlanYearID = dbo.fnGetBidYear()
                        AND ForecastID = @ForecastID
                        AND MARatingOptionID = @MARatingOptionID
            END
    END
GO
