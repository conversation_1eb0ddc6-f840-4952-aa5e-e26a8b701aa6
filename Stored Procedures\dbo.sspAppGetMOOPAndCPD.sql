SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spAppGetMOOPAndCPD
--
-- CREATOR:			<PERSON><PERSON><PERSON> Garvey
--
-- CREATED DATE:	2024-MAY-08 
--
-- DESCRIPTION:		Stored procedure responsible for getting the data export Data for the MOOP And CPD from the 
--						Cal<PERSON><PERSON><PERSON><PERSON>edPMPM and CalcMOOPInitialContinuance table.
--						Used in MAAUI > Data Owner Imports > MOOP and CPD
--		
-- PARAMETERS:
--  Input  :		NONE
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			CalcCPDAllowedPMPM
--					CalcMOOPInitialContinuance
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-MAY-08		1			Initial Version                                                     Latoya Garvey
-- 2024-NOV-26		2			Add new table fields for Deductible Pricing project					Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spAppGetMOOPAndCPD]

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        -- Data pull from dbo.CalcCPDAllowedPMPM
        SELECT  PlanYearID
               ,Product
               ,CPDID
               ,MM_Total
               ,TotalAllowedPMPM
               ,IPAllowedPMPM
               ,SNFAllowedPMPM
               ,OtherAllowedPMPM
               ,All_TotalAllowedPMPM
               ,All_IPAllowedPMPM
               ,All_SNFAllowedPMPM
               ,All_OtherAllowedPMPM
               ,PartB_TotalAllowedPMPM
               ,PartB_IPAllowedPMPM
               ,PartB_SNFAllowedPMPM
               ,PartB_OtherAllowedPMPM
               ,PhysIncl_TotalAllowedPMPM
               ,PhysIncl_IPAllowedPMPM
               ,PhysIncl_SNFAllowedPMPM
               ,PhysIncl_OtherAllowedPMPM
               ,PhysExcl_TotalAllowedPMPM
               ,PhysExcl_IPAllowedPMPM
               ,PhysExcl_SNFAllowedPMPM
               ,PhysExcl_OtherAllowedPMPM
               ,LastUpdateByID
               ,LastUpdateDateTime
        FROM    dbo.CalcCPDAllowedPMPM WITH (NOLOCK);

        -- Data pull from dbo.CalcMOOPInitialContinuance
        SELECT  PlanYearID
               ,MOOPBucketID
               ,CPDID
               ,Product
               ,MemberCount_Total
               ,MM_Total
               ,Distribution_Total
               ,RollingDistribution
               ,TotalAllowedAmt
               ,IPAllowedAmt
               ,SNFAllowedAmt
               ,OtherAllowedAmt
               ,All_TotalAllowedAmt
               ,All_IPAllowedAmt
               ,All_SNFAllowedAmt
               ,All_OtherAllowedAmt
               ,PartB_TotalAllowedAmt
               ,PartB_IPAllowedAmt
               ,PartB_SNFAllowedAmt
               ,PartB_OtherAllowedAmt
               ,PhysIncl_TotalAllowedAmt
               ,PhysIncl_IPAllowedAmt
               ,PhysIncl_SNFAllowedAmt
               ,PhysIncl_OtherAllowedAmt
               ,PhysExcl_TotalAllowedAmt
               ,PhysExcl_IPAllowedAmt
               ,PhysExcl_SNFAllowedAmt
               ,PhysExcl_OtherAllowedAmt
               ,LastUpdateByID
               ,LastUpdateDateTime
        FROM    dbo.CalcMOOPInitialContinuance WITH (NOLOCK);

    END;
GO
