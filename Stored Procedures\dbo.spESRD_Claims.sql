SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
--------------------------------------------------------------------------------------------------------------------------            
------ Stored Procedure NAME: [spESRD_Claims]       
------ ----------------------------------------------------------------------------------------------------------------           
------ DATE			VERSION		CHANGES MADE														DEVELOPER              
------ ----------------------------------------------------------------------------------------------------------------            
------ 2023-01-22 		1		Initial Version														Adam Keach
------ 2023-02-02		2		Updated the setup for parameters and to use #PLans table			Tingyao Wang
------							instead of dbo.ESRDClaims_ActivePlanList to allow multiple 
------							users to run the code at the same time
------2023-04-29		3		Added one line to plan benefits pull to pull in live benefit scenario Adam Keach
------2023-05-01        4		Added condition to plan benefits to for  InBenefitTypeID is not null  Adam Gilbert

CREATE PROCEDURE [dbo].[spESRD_Claims]
(
   @RegionId INT,
  @ForecastID VARCHAR(MAX),		-- updated 2/2
  @DivisionID INT,
  @SubRegion VARCHAR(MAX)		-- updated 2/2

)
AS
SET NOCOUNT ON
SET ANSI_WARNINGS OFF


--DECLARE @RegionId INT = 0 --comment out for actual stored procedure, only use for testing
--DECLARE @ForecastID VARCHAR(MAX) = NULL
--DECLARE @DivisionID INT = 0
--DECLARE @Subregion VARCHAR(MAX) = NULL

DECLARE @Local_RegionId INT = @RegionId					-- added 2/2
DECLARE @Local_ForecastID VARCHAR(MAX) = @ForecastID	-- added 2/2
DECLARE @Local_DivisionID INT = @DivisionID				-- added 2/2
DECLARE @Local_Subregion VARCHAR(MAX) = @Subregion		-- added 2/2


DECLARE @BaseYear INT
DECLARE @BidYear INT
DECLARE @Iteration VARCHAR(13)

/************ Replace Hardcoding before Savings **************************/
SET @BaseYear = (SELECT MIN(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive='N')
SET @BidYear = (SELECT MAX(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive = 'N'); /* For Q3, Q4, FLP, these years will likely need to be hardcoded */
SET @Iteration = CONCAT(
                 (
                     SELECT Year  FROM dbo.SCT_CurrentToolVersion
                 ),
                 'Q',
                 (
                     SELECT Quarter FROM dbo.SCT_CurrentToolVersion
                 )
                       )



--Check to see if there's a timestamp, if not set current timestamp







--=================================================================================================================================================
--IF (SELECT OBJECT_ID('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans
--This pulls the live plans from the SCT.
	--The inner join ensures only SQL updated plans will be refreshed

--Select * from #Plans ORDER BY PlanName

--======================================================================
/* ************************************************************************************************************************************************* *
Get Plan List corresponding to chosen plan/region/division.  For Q3/Q4/FLP purposes, all plans are chose by setting regionID, ForecastID, and DivisionID to 0 
* =================================================================================================================================================== */
IF (SELECT OBJECT_ID('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans		-- added 2/2

SELECT PlanName,RFI.Region													
INTO #Plans																	-- create #Plans instead of dbo.ESRDClaims_ActivePlanList 2/2
FROM dbo.SCT_vwRollupForecastInfo_SCT   RFI WITH (NOLOCK)

INNER JOIN 
	(SELECT PlanYearContractPBPSegment
	FROM dbo.SCT_savedplanlist		-- updated to use SCT_savedplanlist 2/2
	WHERE  PlanYearID = @BidYear) b
				ON RFI.PlanName = b.PlanYearContractPBPSegment

WHERE RFI.RollupName = 'LIVE'
      AND RFI.IsSctPlan = 1
      AND RFI.IsToReprice = 0--Leave in - Don't remove for Bids
      AND RFI.IsLiveIndex = 1
      AND (@Local_RegionId = 0 OR RFI.ActuarialRegionID = @Local_RegionId)													-- updated 2/2
      AND (@Local_ForecastID IS NULL OR RFI.ForecastID IN (Select value from dbo.fnStringSplit(@Local_ForecastID,',')))		-- updated 2/2
      AND (@Local_DivisionID = 0 OR RFI.ActuarialDivisionID = @Local_DivisionID)											-- updated 2/2
	  AND (@Local_Subregion IS NULL OR RFI.Subregion IN (Select value from dbo.fnStringSplit(@Local_Subregion,',')));		-- updated 2/2

--=================================================================================================================================================
--IF (SELECT OBJECT_ID('tempdb..dbo.ESRDClaims_BYCrosswalkPlans')) IS NOT NULL DROP TABLE dbo.ESRDClaims_BYCrosswalkPlans
--This pulls the plan list from the SCT.
/* ************************************************************************************************************************************************* *
Pulls Bid Year Plan list along with all base year plans that map to the bid year plan 
* =================================================================================================================================================== */
--Select * from dbo.ESRDClaims_BYCrosswalkPlans  ORDER BY Plan_BaseYear, BY_Plan
-- Select * from dbo.ESRDClaims_BYCrosswalkPlans where BY_Plan='H1019-109-000'
--======================================================================
delete from dbo.ESRDClaims_BYCrosswalkPlans
insert INTO dbo.ESRDClaims_BYCrosswalkPlans
SELECT b.PlanYearID AS BaseYear,
       b.PlanYearContractPBPSegment AS Plan_BaseYear,
       a.BidYear AS BidYear,
       a.ContractPBPSegment AS BY_Plan,
       a.Division AS BY_Division,
       a.Region AS BY_Region,
       a.Product AS OriginalProduct_BY,
  --     a.IsSNP,
	   rfi.SNPType

FROM #Plans c inner join dbo.SCT_SavedPlanList a on c.PlanName=a.ContractPBPSegment		-- updated to #Plans 2/2

    LEFT JOIN
    (
        SELECT *
        FROM dbo.SCT_SavedPlanList 
        WHERE PlanYearID =  @BaseYear
              AND ConcurrentPlan <> 'Inactive'
    ) b
        ON a.BidYear = b.BidYear
           AND a.ContractPBPSegment = b.ContractPBPSegment
		   left join dbo.vwRollupForecastInfo rfi 
		   on a.ContractPBPSegment=rfi.PlanName
WHERE a.PlanYearID = @BidYear;



--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYTrendedClaims')) IS NOT NULL DROP TABLE #BYTrendedClaims
/* ************************************************************************************************************************************************* *
This trends the base claims forward to the BY. (Using the ESRDBYTrendAmounts) Trends are applied to allowed, utilization, and admits
* =================================================================================================================================================== */

 --select * from #BYTrendedClaims where BY_Plan='H0028-013-000'

--Select BY_Plan,Cohort,DialysisSubtype,Vendor,DEPound,SUM(TrendedAllowed/Trend) as Allowed,SUM(TrendedAllowed) as TrendedAllowed,avg(DEPoundLevelMM) as MM
  -- from #BYTrendedClaims  group by BY_plan,cohort,dialysisSubtype,Vendor,DEPound
--198,330 Rows Affected
--======================================================================
SELECT CASE WHEN a.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN a.SNPType = 'Chronic' THEN 'CSNP'
		WHEN a.SNPType = 'Institutional' THEN 'CSNP'
		Else a.OriginalProduct_BY End as Product
	,a.BY_Division
	,a.BY_Region	
	,a.BY_Plan
	,CLMs.CPS_ID
	,Clms.Cohort
	,Clms.class
	,clms.DEPound
	,clms.BenefitCategoryName
	,clms.OMCSBenefitType
	,clms.DePoundLevelMM	
	,clms.PlanLevelMM
	,b.Trend
	,(clms.TotalAllowedAmt*ISNULL(b.Trend,1)) AS TrendedAllowed
	,(clms.Utilization*sqrt(ISNULL(b.Trend,1))) AS TrendedUse
	,(clms.Admit*sqrt(ISNULL(b.Trend,1))) AS TrendedAdmit
INTO #BYTrendedClaims
FROM dbo.ESRDClaims_BYCrosswalkPlans a
inner join 
  dbo.ESRDInpt_BaseClaims Clms 
on a.plan_BaseYear=CLMS.CPS_Id
LEFT JOIN  dbo.ESRDInpt_TrendsBY b
	ON clms.Cohort=b.Cohort
	AND a.BY_Region=b.Region
	AND CASE WHEN a.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN a.SNPType = 'Chronic' THEN 'CSNP'
		WHEN a.SNPType = 'Institutional' THEN 'CSNP'
		Else a.OriginalProduct_BY End=b.Product
	AND clms.class=B.class
	AND clms.BenefitCategoryName=b.BenefitCategoryName
	where Plan_baseYear is not null



--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYTrendedManualRates')) IS NOT NULL DROP TABLE #BYTrendedManualRates
/* ************************************************************************************************************************************************* *
This trends the Manual Rates forward to the BY. (Using the ESRDBYTrendAmounts) Trends are applied to allowed, utilization, and admits
* =================================================================================================================================================== */
--Select * from #BYTrendedManualRates where region='Gulf States' and Product='CSNP'
--198,330 Rows Affected
--======================================================================
SELECT clms.product
	,clms.Region	
	,Clms.Cohort
	,Clms.class
	,clms.DEPound
	,clms.BenefitCategoryName
	,b.Trend

	,(clms.ManualAllowedPMPM*ISNULL(b.Trend,1)) AS TrendedManualAllowed
	,(clms.ManualUtilizationPTMPY*sqrt(ISNULL(b.Trend,1))) AS TrendedManualUse
	,(clms.ManualAdmitPTMPY*sqrt(ISNULL(b.Trend,1))) AS TrendedManualAdmit
INTO #BYTrendedmanualrates
FROM 
dbo.ESRDInpt_FinalManualRates Clms 
inner join (select distinct Region from #Plans) P on Clms.Region=P.Region		-- updated to #Plans 2/2
LEFT JOIN dbo.ESRDInpt_TrendsBY b
	ON clms.Cohort=b.Cohort
	AND clms.Region=b.Region
	AND Clms.product=b.Product
	AND clms.class=B.class
		AND clms.BenefitCategoryName=b.BenefitCategoryName



--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYCrosswalkPlanUnion')) IS NOT NULL DROP TABLE #BYCrosswalkPlanUnion

	--The first section of this is nulling the new plans in the bid year. 
/* ************************************************************************************************************************************************* *
For all plans, generate all combinations of cohort, DialysisSubtype, Vendor, DE#, and benefit category
New plans won't have any base period experience, but we still need a record of base experience for every benefit category, 
cohort, dialysissubtype, vendor, and DE# cut, so these are set to null
* =================================================================================================================================================== */
--Select * from #BYCrosswalkPlanUnion 
--254,430 Rows Affected
--======================================================================
SELECT CASE WHEN a.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN a.SNPType = 'Chronic' THEN 'CSNP'
		WHEN a.SNPType = 'Institutional' THEN 'CSNP'
		ELSE a.OriginalProduct_BY END AS BY_Product,
       a.BY_Region,
       a.BY_Division,
       a.BY_Plan,
       CPS_ID = NULL,
       b.Cohort,
	   b.class,
       b.DEPound,
       b.BenefitCategoryName,
       b.OMCSBenefitType,
       DePoundLevelMM = NULL,
       PlanLevelMM = NULL,
       TrendedAllowed = NULL,
       TrendedUse = NULL,
       TrendedAdmit = NULL
INTO #BYCrosswalkPlanUnion
FROM(
    SELECT DISTINCT
           OriginalProduct_BY,
		   BY_Region,
		   BY_Division,
		   BY_Plan,
		   SNPType
    FROM dbo.ESRDClaims_BYCrosswalkPlans
    WHERE Plan_BaseYear IS NULL
)a,  
(
	SELECT * FROM (SELECT DISTINCT cohort,class FROM DBO.ESRDInpt_BaseClaims) w,
	(SELECT DISTINCT DEPound,BenefitCategoryName,OMCSBenefitType from dbo.ESRDInpt_BenefitCatList) y
	)  b
UNION
SELECT Product as BY_Product,
       BY_Region,
       BY_Division,
       BY_Plan,
       CPS_ID,
       Cohort,
	   class,
       DEPound,
       BenefitCategoryName,
       OMCSBenefitType,
       DePoundLevelMM,
       PlanLevelMM,
       TrendedAllowed,
       TrendedUse,
       TrendedAdmit
FROM #BYTrendedClaims
WHERE BY_Plan IS NOT NULL;
--select * from dbo.ESRDClaims_BYCrosswalkPlans where BY_Plan='H5216-267-000'

--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYCrosswalkRollup')) IS NOT NULL DROP TABLE #BYCrosswalkRollup
/* ************************************************************************************************************************************************* *
Convert Crosswalked base claims to PMPMs
* =================================================================================================================================================== */

--Select * from #BYCrosswalkRollup 
--226,380 Rows Affected
--======================================================================
SELECT BY_Product
    ,BY_Region
    ,BY_Division
	,BY_Plan
	,Cohort
	,class
	,DEPound
	,BenefitCategoryName
	,OMCSBenefitType
	,CONVERT(DECIMAL(10,2),SUM(ISNULL((DePoundLevelMM),0))) AS CrosswalkDePoundMM
	,CONVERT(DECIMAL(10,2),SUM(ISNULL((PlanLevelMM),0))) AS CrosswalkPlanMM
	,SUM(ISNULL((TrendedAllowed),0)) AS CrosswalkAllowed
	,SUM(ISNULL((TrendedUse),0)) AS CrosswalkUse
	,SUM(ISNULL((TrendedAdmit),0)) AS CrosswalkAdmit
	,ISNULL((SUM(TrendedAllowed)/(NULLIF(SUM(DePoundLevelMM),0))),0) AS CrosswalkAllowedPMPM
	,ISNULL((SUM(TrendedUse)/(NULLIF(SUM(DePoundLevelMM),0))),0)*12000 AS CrosswalkUsePTMPY
	,ISNULL((SUM(TrendedAdmit)/(NULLIF(SUM(DePoundLevelMM),0))),0)*12000 AS CrosswalkAdmitPTMPY
INTO #BYCrosswalkRollup
FROM #BYCrosswalkPlanUnion
GROUP BY BY_Product
    ,BY_Region
    ,BY_Division
	,BY_Plan
	,Cohort
	,class
	,DEPound
	,BenefitCategoryName
	,OMCSBenefitType




--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYManualBlend')) IS NOT NULL DROP TABLE #BYManualBlend
/* ************************************************************************************************************************************************* *
Calculate credibility for each cut of data and bring in the manual rates
* =================================================================================================================================================== */
--Select * from #BYManualBlend where BY_Plan='H0028-013-000' and cohort='Dialysis' and Class = 'All Other Dialysis' order by class,DEPound,BenefitCategoryName credibilitybyplan < 1
--226,380 Rows Affected
--======================================================================
SELECT a.BY_Product
				,a.BY_Region
				,a.BY_Division
				,a.BY_Plan
				,a.Cohort
				,a.class
				,a.DEPound
				,a.BenefitCategoryName
				,a.OMCSBenefitType
				,a.CrosswalkDePoundMM
				,a.CrosswalkPlanMM
				,a.CrosswalkUsePTMPY
				,a.CrosswalkAllowedPMPM
				,a.CrosswalkAdmitPTMPY
				,b.TrendedManualAllowed
				,b.TrendedManualUse
				,b.TrendedManualAdmit
				,d.CredibleMemberMonths
				,IIF(a.CrosswalkDePoundMM=0,0,
		CASE WHEN  (SQRT(a.CrosswalkDEPoundMM/(d.CredibleMemberMonths)))<1
		THEN SQRT(a.CrosswalkDEPoundMM/(d.CredibleMemberMonths))
		ELSE 1 END)  
		 AS CredibilityByPlan
INTO #BYManualBlend
FROM  #BYCrosswalkRollup a
			left Join  dbo.ESRDInpt_CredibleMM d
			on a.Cohort=d.Cohort
			LEFT JOIN #BYTrendedManualRates b
				ON b.Region = a.BY_Region
				AND b.Product = a.BY_Product
				AND b.Cohort = a.Cohort
				AND B.class=a.class
				AND b.DEPound = a.DEPound
				AND b.BenefitCategoryName = a.BenefitCategoryName



--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYManualCalculations')) IS NOT NULL DROP TABLE #BYManualCalculations

/* ************************************************************************************************************************************************* *
Use credibility to blend manual rates with the base experience to produce final projected experience for all plans
* =================================================================================================================================================== */
--Select * from #BYManualCalculations 
--226,380 Rows Affected
--======================================================================
SELECT BY_Product
	,BY_Region
	,BY_Division
	,BY_Plan
	,Cohort
	,class
	,DEPound
	,BenefitCategoryName
	,OMCSBenefitType
	,CrosswalkDePoundMM
	,CredibilityByPlan
	,CrosswalkUsePTMPY
    ,CrosswalkAllowedPMPM
	,CrosswalkAdmitPTMPY
	,TrendedManualAllowed
	,TrendedManualUse
	,TrendedManualAdmit
	,((CrosswalkUsePTMPY*CredibilityByPlan)+(TrendedManualUse*(1-CredibilityByPlan))) AS ManualBlendedUsePTMPY
	,((CrosswalkAllowedPMPM*CredibilityByPlan)+(TrendedManualAllowed*(1-CredibilityByPlan))) AS ManualBlendedAllowedPMPM
	,((CrosswalkAdmitPTMPY*CredibilityByPlan)+(TrendedManualAdmit*(1-CredibilityByPlan))) AS ManualBlendedAdmitPTMPY
INTO #BYManualCalculations
FROM #BYManualBlend






--PLAN BENEFITS


--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYPlanBenefits')) IS NOT NULL DROP TABLE #BYPlanBenefits
--If problems occur, try IsFiledPlan=1 or IsSCTPlan=1. These filters could eliminate duplicate rows in the next table. 
	--(IsFiled excludes plans that termed in the bid year.)

--Select * from #BYPlanBenefits
--37,950 Rows Affected
--======================================================================

/* ************************************************************************************************************************************************* *
Bring in all plan benefits - copay, coinsurance, MOOP
* =================================================================================================================================================== */
SELECT b.CPS as CPS_ID
	   ,a.ForecastID
       ,c.IsBenefitYearCurrentYear
       ,c.BenefitCategoryID
       ,d.BenefitCategoryName
       ,c.INBenefitTypeID
       ,AVG(c.INBenefitValue) AS INBenefitValue
       ,e.INMOOP
       ,e.CombinedMOOP
       ,a.IsHidden
       ,a.IsLiveIndex
	   ,a.IsFiledPlan
INTO #BYPlanBenefits
FROM  dbo.SavedForecastSetup   a
LEFT JOIN dbo.SavedPlanInfo  b
	   ON a.PlanInfoID=b.PlanInfoID
LEFT JOIN dbo.SavedPlanBenefitDetail  c
       ON a.ForecastID=c.ForecastID
LEFT JOIN dbo.SCT_BYLkpIntBenefitCategory  d
       ON c.BenefitCategoryID = d.BenefitCategoryID
LEFT JOIN  dbo.vwAuditPlanLevelValues e
       ON c.ForecastID=e.ForecastID

where c.IsBenefitYearCurrentYear = 0
		AND a.IsHidden = 0
		AND a.IsLiveIndex = 1
		AND a.IsFiledPlan = 1
		and c.isliveindex =1 --Added 4/29/2023 for multiple benefit scenario issue
		AND c.INBenefitTypeID IS NOT NULL --Added to remove OON only day range benefit rows
		AND e.benefitYearID=@BidYear
GROUP BY b.CPS,
         a.ForecastID,
         c.IsBenefitYearCurrentYear,
         c.BenefitCategoryID,
         d.BenefitCategoryName,
         c.INBenefitTypeID,
         e.INMOOP,
         e.CombinedMOOP,
         a.IsHidden,
         a.IsLiveIndex,
		 a.IsFiledPlan



--The # of rows in this table should be equal to the #BYManualBlend table. 


/* ************************************************************************************************************************************************* *
Calculate Pre-MOOP cost share
* =================================================================================================================================================== */

IF (SELECT OBJECT_ID('tempdb..#ESRDBYPlanBenefitsBlend')) IS NOT NULL DROP TABLE #ESRDBYPlanBenefitsBlend		 

--=================================================================================================================================================
--Select * from #EsrdBYPlanBenefitsBlend where BY_plan='H0028-021-000' and class='All Other Dialysis' ORDER BY BY_Plan, Cohort, DEPound, BenefitCategoryName
--226,380 Rows Affected
--======================================================================

SELECT DISTINCT
	a.BY_Product,
	a.BY_Region,
	a.BY_Division,
	a.BY_Plan,
	a.Cohort,
	a.Class,
	a.DEPound,
	a.BenefitCategoryName,
	a.OMCSBenefitType,
	a.CrosswalkDePoundMM,
	a.ManualBlendedUsePTMPY,
	a.ManualBlendedAllowedPMPM,
	a.ManualBlendedAdmitPTMPY
	,b.INBenefitTypeID
	,b.INBenefitValue
	,b.INMOOP
	,b.CombinedMOOP
	,IIF(b.INMOOP IS NULL, b.CombinedMOOP, b.INMOOP) AS MOOP
	,CASE 
		WHEN a.OMCSBenefitType='IP' THEN a.ManualBlendedAllowedPMPM*0.058
		WHEN a.OMCSBenefitType='SNF' THEN a.ManualBlendedAllowedPMPM*0.19
		WHEN a.OMCSBenefitType='Other' THEN a.ManualBlendedAllowedPMPM*0.198
		END AS OMCostShare
	,CASE 
		WHEN(b.INBenefitTypeID=1) THEN 'CoInsurance'
		WHEN(b.INBenefitTypeID=2) THEN 'Copay'
		WHEN(b.INBenefitTypeID=3) THEN 'Admit'
		WHEN(b.INBenefitTypeID=4) THEN 'Other'
		ELSE 'N/A' END
	AS INBenefitType
	,ROUND(CASE 
			WHEN(b.INBenefitTypeID='1') THEN ((a.ManualBlendedAllowedPMPM)*(b.INBenefitValue))
			WHEN(b.INBenefitTypeID='2') THEN ((a.ManualBlendedUsePTMPY/12000)*(b.INBenefitValue))
			WHEN(b.INBenefitTypeID='3') THEN ((a.ManualBlendedAdmitPTMPY/12000)*(b.INBenefitValue))
		Else 0 END,5) 
	as PreMoopCs
into #EsrdBYPlanBenefitsBlend
FROM #BYManualCalculations a
LEFT JOIN #BYPlanBenefits b
	ON a.BY_Plan=b.CPS_ID
	AND b.BenefitCategoryName = a.BenefitCategoryName


--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYPreMOOPCs')) IS NOT NULL DROP TABLE #BYPreMOOPCs
--The number of rows in this table should be equal to the (#BYPlanBenefitsBlend/330)*6

/* ************************************************************************************************************************************************* *
Summarize experience (remove benefit category level)
* =================================================================================================================================================== */

--Select * from #BYPreMOOPCs where BY_Plan='H5216-334-000' and class='All Other Dialysis' ORDER BY BY_Plan, Cohort, DEPound
--4,116 Rows Affected
--======================================================================
SELECT BY_Product
	,BY_Region
	,BY_Division
	,BY_Plan
    ,Cohort
	,Class
    ,DEPound
    ,AVG(CrosswalkDePoundMM) AS DePoundMM
    ,SUM(ManualBlendedUsePTMPY) AS DePoundUsePTMPY
    ,SUM(ManualBlendedAllowedPMPM) AS DePoundAllowedPMPM
	,SUM(ManualBlendedAdmitPTMPY) AS DePoundAdmitPTMPY
    ,AVG(MOOP) AS PlanMOOP
	,SUM(OMCostShare) AS OMCostSharePMPM
    ,SUM(PreMoopCs) AS PreMoopCsPMPM
INTO #BYPreMOOPCs
FROM #EsrdBYPlanBenefitsBlend
GROUP BY BY_Product
	,BY_Region
	,BY_Division
	,BY_Plan
    ,Cohort
	,Class
    ,DEPound


--Check:




--MOOP ADJUSTMENT


--================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYMoopBounds')) IS NOT NULL DROP TABLE #BYMoopBounds

--First, look at the second Select statement: (This is used find the first and second numbers in the MOOP and allowed fields)
	--The left(PlanMOOP,1) takes the first item in the MOOP. For example, for a $6700 MOOP, it would output a 6. 
	--The SUBSTRING(CAST(PlanMOOP AS CHAR),2, 1) section converts the MOOP to a string and then takes the 1 item in the second positon. For example, for a $6700 MOOP, this would output a 7.
--Next, look at the first Select Statement: 
	--For the MOOPLowBound, this takes the first digit, and states that is the second digit<5, then it results in a 0. It then adds two 0's for the last two digits. 
		--For example, for $6700 MOOP, it would result in a 6, a 5 since 7 is not <5, and then two 0's. (So, the low bound would be 6500)
	--A similar process is done for the MOOPUpperBound and the allowed bounds. 

--Select * from #BYMoopBounds ORDER BY BY_Plan, Cohort, DEPound
--4,116 Rows Affected (This should match #BYPreMOOPCs table)
--==========================================================================
SELECT BY_Product
	,BY_Region
	,BY_Division
	,BY_Plan
    ,Cohort
	,class
    ,DEPound
	,DEPoundMM
	,DePoundAllowedPMPM
	,PlanMOOP
	,OMCostSharePMPM
	,PreMoopCsPMPM
	,MOOPLowBound = CASE WHEN MOOPDigitNumber=3 THEN (IIF(PlanMOOP<500,0,500))
						WHEN MOOPDigitNumber=4 THEN CONCAT(MOOPFirstDigit , CASE WHEN MOOPSecondDigit <5 THEN 0 ELSE 5 END,0,0) END 
	,MOOPUpperBound = CASE WHEN MOOPDigitNumber=3 THEN (IIF(PlanMOOP<500,500,1000))
							WHEN MOOPDigitNumber=4 THEN CONCAT(CASE WHEN MOOPSecondDigit <5 THEN MOOPFirstDigit ELSE MOOPFirstDigit+1 END, CASE WHEN MOOPSecondDigit <5 THEN 5 ELSE 0 END,0,0) END 
	,AllowedLowBound  = CASE WHEN DePoundAllowedPMPM<=0 THEN NULL
								WHEN DePoundAllowedPMPM>20000 THEN 20000
								ELSE CASE WHEN AllowedDigitNumber=2 OR AllowedDigitNumber=3 THEN (IIF(DePoundAllowedPMPM<500,0,500))
											WHEN AllowedDigitNumber=4 THEN CONCAT(AllowedFirstDigit, CASE WHEN AllowedSecondDigit <5 THEN 0 ELSE 5 END,0,0)
											WHEN AllowedDigitNumber=5 THEN CONCAT(AllowedFirstDigit, AllowedSecondDigit, CASE WHEN AllowedThirdDigit <5 THEN 0 ELSE 5 END,0,0) END END
	,AllowedUpperBound = CASE WHEN DePoundAllowedPMPM<=0 THEN NULL
								WHEN DePoundAllowedPMPM>20000 THEN 20000
								ELSE CASE WHEN AllowedDigitNumber=2 OR AllowedDigitNumber=3 THEN (IIF(DePoundAllowedPMPM<500,500,1000))
											WHEN AllowedDigitNumber=4 THEN CONCAT(CASE WHEN AllowedSecondDigit <5 THEN AllowedFirstDigit ELSE AllowedFirstDigit+1 END, CASE WHEN AllowedSecondDigit <5 THEN 5 ELSE 0 END,0,0)
											WHEN AllowedDigitNumber=5 THEN CONCAT(AllowedFirstDigit,CASE WHEN AllowedThirdDigit <5 THEN AllowedSecondDigit ELSE AllowedSecondDigit+1 END, CASE WHEN AllowedThirdDigit <5 THEN 5 ELSE 0 END,0,0)	END END 
INTO #BYMoopBounds
FROM 
(SELECT  
	BY_Product
	,BY_Region
	,BY_Division
	,BY_Plan
    ,Cohort
	,class
    ,DEPound
	,DEPoundMM
	,DePoundAllowedPMPM
	,PlanMOOP
	,OMCostSharePMPM
	,PreMoopCsPMPM
	,MOOPFirstDigit = left(PlanMOOP,1)
	,MOOPSecondDigit = SUBSTRING(CAST(PlanMOOP AS CHAR),2, 1)
	,MOOPDigitNumber = LEN(CAST(PlanMOOP AS int))
	,AllowedFirstDigit = left(DePoundAllowedPMPM,1)
	,AllowedSecondDigit = SUBSTRING(CAST(DePoundAllowedPMPM AS CHAR),2, 1) 
	,AllowedThirdDigit = SUBSTRING(CAST(DePoundAllowedPMPM AS CHAR),3, 1) 
	,AllowedDigitNumber = LEN(CAST(DePoundAllowedPMPM AS int))
FROM #BYPreMOOPCs
)a



--================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYCsMatch')) IS NOT NULL DROP TABLE #BYCsMatch
--Each one of these Left Join statements is joining on a different MOOP and Allowed combination. 
	--These tables are then rolled up and joined in the first select statement by matching on CPS_ID, Cohort, & DEPound indicator. 
		--This produces four CS amounts from the MoopInputTable2021 table that will be interpolated to arrive at the final CS.
			--See the "SQL - MOOP Impact Example" File for a simplistic example. 

--Select * from #BYCsMatch ORDER BY BY_Plan, Cohort, DEPound
--4,116 Rows Affected (This should match #BYPreMOOPCs table)
--==========================================================================
SELECT d.BY_Product,
	   d.BY_Region,
	   d.BY_Division,
	   d.BY_Plan,
       d.Cohort,
	   d.class,
       d.DEPound,
       d.DePoundMM,
       d.DePoundAllowedPMPM,
       CONVERT(DEC,d.PlanMOOP) AS MOOP,
	   d.OMCostSharePMPM,
       d.PreMoopCsPMPM,
       CONVERT(INT, d.MOOPLowBound) AS MOOPLow,
       CONVERT(INT, d.MOOPUpperBound) AS MOOPHigh,
       CONVERT(INT, d.AllowedLowBound) AS AllowedLow,
       CONVERT(INT, d.AllowedUpperBound) AS AllowedHigh,
       MlowAlow.CSBothLow,
       MlowAup.CSMoopLowAllowedHigh,
       MupAlow.CSMoopHighAllowedLow,
       MupAup.CSBothHigh
INTO #BYCsMatch
FROM #BYMoopBounds d
    LEFT JOIN
    (
        SELECT b.BY_Plan,
               a.Cohort,
			   a.class,
               b.DEPound,
               b.MOOPUpperBound,
               b.AllowedUpperBound,
               CSBothLow = a.CostSharePMPM
      FROM dbo.ESRDInpt_MOOPTable a
            INNER JOIN #BYMoopBounds b
                ON a.Cohort = b.Cohort
				and a.class=b.class
                   AND a.MOOP = b.MOOPLowBound
                   AND a.AllowedAmountPMPM = b.AllowedLowBound
       /* WHERE b.DEPound = 'NonDE#' */
    ) MlowAlow
        ON MlowAlow.BY_Plan = d.BY_Plan

           AND MlowAlow.Cohort = d.Cohort
		   and MlowAlow.class=d.class
           AND MlowAlow.DEPound = d.DEPound
    LEFT JOIN
    (
        SELECT b.BY_Plan,
               a.Cohort,
			   a.class,
               b.DEPound,
               b.MOOPUpperBound,
               b.AllowedUpperBound,
               CSBothHigh = a.CostSharePMPM
        FROM dbo.EsrdInpt_MOOPTable a
            INNER JOIN #BYMoopBounds b
                ON a.Cohort = b.Cohort
				and a.class=b.class
                   AND a.MOOP = b.MOOPUpperBound
                   AND a.AllowedAmountPMPM = b.AllowedUpperBound
       /* WHERE b.DEPound = 'NonDE#' */
    ) MupAup
        ON MupAup.BY_Plan = d.BY_Plan
           AND MupAup.Cohort = d.Cohort
		   And MUPAUP.class=d.class
           AND MupAup.DEPound = d.DEPound
    LEFT JOIN
    (
        SELECT b.BY_Plan,
               a.Cohort,
			   a.class,
               b.DEPound,
               b.MOOPUpperBound,
               b.AllowedUpperBound,
               CSMoopLowAllowedHigh = a.CostSharePMPM
        FROM dbo.EsrdInpt_MOOPTable a
            INNER JOIN #BYMoopBounds b
                ON a.Cohort = b.Cohort
				and a.class=b.class
                   AND a.MOOP = b.MOOPLowBound
                   AND a.AllowedAmountPMPM = b.AllowedUpperBound
      /*  WHERE b.DEPound = 'NonDE#' */
    ) MlowAup
        ON MlowAup.BY_Plan = d.BY_Plan
           AND MlowAup.Cohort = d.Cohort
		   And MlowAUp.class=d.class
           AND MlowAup.DEPound = d.DEPound
    LEFT JOIN
    (
        SELECT b.BY_Plan,
               a.Cohort,
			   a.class,
               b.DEPound,
               b.MOOPUpperBound,
               b.AllowedUpperBound,
               CSMoopHighAllowedLow = a.CostSharePMPM
        FROM dbo.EsrdInpt_MoopTable a
            INNER JOIN #BYMoopBounds b
                ON a.Cohort = b.Cohort
				and a.class=b.class
                   AND a.MOOP = b.MOOPUpperBound
                   AND a.AllowedAmountPMPM = b.AllowedLowBound
        /*WHERE b.DEPound = 'NonDE#' */
    ) MupAlow
        ON MupAlow.BY_Plan = d.BY_Plan
           AND MupAlow.Cohort = d.Cohort
		   and MUPAlow.class=d.class
           AND MupAlow.DEPound = d.DEPound;


--================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYPostMoopCs')) IS NOT NULL DROP TABLE #BYPostMoopCs


--This first interpolates on the allowed amounts (Second select statement) and then on the MOOP amounts (First Select Statement)
	--See the "SQL - MOOP Impact Example" File for a simplistic example. 

--Select * from #BYPostMoopCs ORDER BY BY_Plan, Cohort, DEPound
--4,116 Rows Affected (This should match #BYPreMOOPCs table)
--==========================================================================
SELECT BY_Product,
	   BY_Region,
	   BY_Division,
	   BY_Plan,
       Cohort,
	   Class,
       DEPound,
       DePoundMM,
       DePoundAllowedPMPM,
       MOOP,
	   OMCostSharePMPM,
       PreMoopCsPMPM,
       PostMOOPCsPMPM = Interpolation2 - ((MOOPHigh - MOOP) / (MOOPHigh - MOOPLow)) * (Interpolation2 - Interpolation1)
INTO #BYPostMoopCs
FROM
(
    SELECT BY_Product,
		   BY_Region,
		   BY_Division,
		   BY_Plan,
           Cohort,
		   Class,
           DEPound,
           DePoundMM,
           DePoundAllowedPMPM,
           MOOP,
		   OMCostSharePMPM,
           PreMoopCsPMPM,
           MOOPHigh,
           MOOPLow,
           Interpolation1 = IIF(AllowedLow=20000,CSBothLow,CSMoopLowAllowedHigh - ((AllowedHigh - DePoundAllowedPMPM) / (AllowedHigh - AllowedLow)) * (CSMoopLowAllowedHigh - CSBothLow)),
           Interpolation2 = IIF(AllowedLow=20000,CSBothHigh,CSBothHigh - ((AllowedHigh - DePoundAllowedPMPM) / (AllowedHigh - AllowedLow)) * (CSBothHigh - CSMoopHighAllowedLow))
    FROM #BYCsMatch
) a;


--================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#BYTotalCs')) IS NOT NULL DROP TABLE #BYTotalCs

--Select * from #BYTotalCs ORDER BY BY_Plan, DEPound, Cohort
--4,116 Rows Affected (This should match #BYPreMOOPCs table)
--==========================================================================
SELECT 
	* 
	,(OMCostSharePMPM-TotalCSPMPM) AS SuppCSPMPM
	,(DePoundAllowedPMPM-TotalCSPMPM) AS NetPMPM
	,(DePoundAllowedPMPM-OMCostSharePMPM) AS BasicNetPMPM
INTO #BYTotalCs
FROM 
(
	SELECT 
		BY_Product,
		BY_Region,
		BY_Division,
		BY_Plan,
		Cohort,
		class,
		DEPound,
		DEPoundMM,
		DePoundAllowedPMPM,
		MOOP,
		OMCostSharePMPM,
		PreMOOPCsPMPM,
		PostMOOPCsPMPM,
			CASE WHEN /*DEPound='DE#' THEN PreMOOPCsPMPM
			WHEN DEPound='NonDE#' AND MOOP FIX */ PostMOOPCsPMPM IS NOT NULL THEN IIF(PreMoopCsPMPM<PostMOOPCsPMPM,PreMoopCsPMPM,PostMOOPCsPMPM)
			ELSE IIF(PreMoopCsPMPM<(MOOP/12),PreMoopCsPMPM,(MOOP/12)) END AS TotalCSPMPM
	FROM #BYPostMoopCs
) a
ORDER BY BY_Plan, Cohort, DEPound



--================================================================================================================================




--==========================================================================

/* ************************************************************************************************************************************************* *
Final Claims - Subtracts the MOOP impact from the Pre-MOOP cost sahre to get final cost share.  Subtracts final cost share from allowed to get final net. Apply Sequestration Claims estimate to final net.
* =================================================================================================================================================== */
--select * from dbo.ESRDClaims_ProjESRDClaims order by CPS, Cohort, Class, DEPound

--IF (SELECT OBJECT_ID('dbo.ESRDClaims_ProjESRDClaims')) IS NOT NULL DROP TABLE dbo.ESRDClaims_ProjESRDClaims
delete from dbo.ESRDClaims_ProjESRDClaims
insert into dbo.ESRDClaims_ProjESRDClaims
SELECT 
	CS.BY_Plan as CPS,
    CS.BY_Region as region,
    CS.BY_Division as division,
    CS.BY_product as product,
    CS.Cohort,
	CS.Class,
    CS.DEPound,
    CS.DePoundAllowedPMPM AS AllowedPMPM,
    CS.TotalCSPMPM,
	cdf.ClmsAdjFx,
	NetPMPM * ISNull(cdf.ClmsAdjFx,1) AS OrigNetPMPM,
	CASE WHEN NetPMPM IS NULL THEN 0.99999 
		WHEN NetPMPM<0 THEN 0.99999 
		ELSE NetPMPM * ISNull(cdf.ClmsAdjFx,1) *sqrt(SI.SequestrationAmount) END AS NetPMPM 

FROM 
(
	#BYTotalCs CS 
	inner join 
	#Plans Z										-- updated to #Plans 2/2
	on CS.BY_Plan=Z.PlanName
left join dbo.ESRDInpt_ClaimsDampeningFactor cdf
on cs.BY_region=cdf.region and
	cs.BY_Product=cdf.Product and
	cs.DEPound=cdf.DEPound and
	cs.Cohort=cdf.Cohort and
	cs.class=cdf.Class
) , dbo.ESRDInpt_Sequestration SI


order by CPS,Cohort,Class, DEPound
GO
