SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetBaseAdminAndRevenueCombined]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetBaseAdminAndRevenueCombined
--
-- AUTHOR: Joe Casey
--
-- CREATED DATE: 2010-Jun-17
-- HEADER UPDATED: 2011-Feb-07
--
-- DESCRIPTION: Returns the sum of Admin and Revenue if multiple PlanInfo IDs are used to populate the bottom section of BPT WS1
--
-- PARAMETERS: 
--  Input: 
--      @ForecastID
--      @MARatingOptionID 
--
--  Output:
--
-- TABLES: 
--  Read:
--      SavedPlanDFSummary
--      SavedForecastSetup
--      SavedDFBaseAdminAndRevenue
--
--  Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Jun-17      1           Initial version.                                                    Joe Casey
-- 2010-Jun-22      2           Added functionality - summed CostAndUseIDs                          John Dixon
-- 2010-Aug-11      3           Removed PlanVersion                                                 Joe Casey
-- 2010-Sep-13      4           Added SavedCUOverrideAdminRev concept.                              Joe Casey
-- 2011-Jan-31      5           Added new CMS requirements.                                         Joe Casey
-- 2011-Feb-07      6           Added join to SavedCUHeader                                         Joe Casey
-- 2012-Feb-24      7           Made changes to account for new admin buckets Quality and           Alex Rezmerski
--                                TaxesAndFees
-- 2013-Apr-15      8           Added InsurefeesAdmin Medicaid revenuce and expenses                Tim Gao
-- 2014-Oct-28      9           Renambed table PerIntBaseAdminAndRevenue to SavedCUBaseAdminAndRevenue  Deepali Mittal
-- 2020-Sep-24      10          Backend Alignment and Restructuring                                 Keith Galloway
-- 2023-Aug-03      11          Added Internal Parameter and Nolock                                 Sheetal Patil
-- 2024-Oct-08		12			Remove references to SavedCUOverrideAdminRev, as this override
--									is no longer used.												Michael Manes
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBaseAdminAndRevenueCombined]
    (@ForecastID       INT
    ,@MARatingOptionID SMALLINT)
RETURNS @Results TABLE
    (ForecastID                       INT
    ,MARatingOptionID                 SMALLINT
    ,CMSESRDRevenue                   DECIMAL(18, 2)
    ,CMSHospiceRevenue                DECIMAL(18, 2)
    ,CMSOtherRevenue                  DECIMAL(18, 2)
    ,ESRDPremium                      DECIMAL(18, 2)
    ,HospicePremium                   DECIMAL(18, 2)
    ,OtherPremium                     DECIMAL(18, 2)
    ,ESRDNetMedicalExpenses           DECIMAL(18, 2)
    ,HospiceNetMedicalExpenses        DECIMAL(18, 2)
    ,OtherNetMedicalExpenses          DECIMAL(18, 2)
    ,ESRDMemberMonths                 INT
    ,HospiceMemberMonths              INT
    ,MarketingandSalesAdminExpense    DECIMAL(17, 2)
    ,DirectAdminExpense               DECIMAL(17, 2)
    ,IndirectAdminExpense             DECIMAL(17, 2)
    ,QualityInitiativesAdminExpense   DECIMAL(17, 2)
    ,TaxesAndFeesAdminExpense         DECIMAL(17, 2)
    ,NetCostofPrivateInsuranceExpense DECIMAL(17, 2)
    ,InsurerFeesAdminExpense          DECIMAL(18, 2)
    ,MedicaidRevenue                  DECIMAL(18, 2)
    ,MedicaidBenefitExpenses          DECIMAL(18, 2)
    ,MedicaidNonBenefitExpenses       DECIMAL(18, 2) PRIMARY KEY (
                                                     ForecastID
                                                    ,MARatingOptionID))
AS
    BEGIN

        DECLARE @XForecastID INT = @ForecastID;
        DECLARE @XMARatingOptionID SMALLINT = @MARatingOptionID;

        INSERT INTO @Results
        SELECT      ForecastID = @XForecastID
                   ,MARatingOptionID = @XMARatingOptionID
                   ,CMSESRDRevenue = SUM (r.CMSESRDRevenue)
                   ,CMSHospiceRevenue = SUM (r.CMSHospiceRevenue)
                   ,CMSOtherRevenue = SUM (r.CMSOtherRevenue)
                   ,ESRDPremium = SUM (r.ESRDPremium)
                   ,HospicePremium = SUM (r.HospicePremium)
                   ,OtherPremium = SUM (r.OtherPremium)
                   ,ESRDNetMedicalExpenses = SUM (r.ESRDNetMedicalExpenses)
                   ,HospiceNetMedicalExpenses = SUM (r.HospiceNetMedicalExpenses)
                   ,OtherNetMedicalExpenses = SUM (r.OtherNetMedicalExpenses)
                   ,ESRDMemberMonths = SUM (r.ESRDMemberMonths)
                   ,HospiceMemberMonths = SUM (r.HospiceMemberMonths)
                   ,MarketingAndSalesAdminExpense = SUM (r.MarketingandSalesAdminExpense)
                   ,DirectAdminExpense = SUM (r.DirectAdminExpense)
                   ,IndirectAdminExpense = SUM (r.IndirectAdminExpense)
                   ,QualityInitiativesAdminExpense = ISNULL (SUM (r.QualityInitiativesAdminExpense), 0)
                   ,TaxesAndFeesAdminExpense = ISNULL (SUM (r.TaxesAndFeesAdminExpense), 0)
                   ,NetCostofPrivateInsuranceExpense = SUM (r.NetCostofPrivateInsuranceExpense)
                   ,InsurerFeesAdminExpense = SUM (r.InsurerFeesAdminExpense)
                   ,MedicaidRevenue = SUM (r.MedicaidRevenue)
                   ,MedicaidBenefitExpenses = SUM (r.MedicaidBenefitExpenses)
                   ,MedicaidNonBenefitExpenses = SUM (r.MedicaidNonBenefitExpenses)
        FROM        dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedForecastSetup SFS WITH (NOLOCK)
               ON SFS.ForecastID = DFS.ForecastID
       INNER JOIN   dbo.SavedDFBaseAdminAndRevenue r WITH (NOLOCK)
               ON DFS.PlanInfoID = r.PlanInfoID
                  AND   SFS.DFVersionID = r.DFVersionID
        WHERE       SFS.ForecastID = @XForecastID
                    AND DFS.MARatingOptionID = @XMARatingOptionID;

        RETURN;
    END;
GO
