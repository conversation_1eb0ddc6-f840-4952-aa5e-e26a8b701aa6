SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spTargetActAdjForMER
--
-- AUTHOR: <PERSON> 
--
-- CREATED DATE: 2013-Mar-29
-- HEADER UPDATED: 2013-Mar-29
--
-- DESCRIPTION: Procedure used to iteratively goal-seek an ACT ADJ Factor value that will result in a desired MER
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@UserID
--		@TargetedMER
--	Output:
--		@ActAdj
--
-- TABLES:
--	Read:
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnAppGetBidSummary
--
-- STORED PROCS:
--		spTargetActAdj
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE														DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2013-Mar-29      1			Initial Version                                                     Mike Deren
-- 2013-Apr-25		2			Increased loopies to 6 from 3										Mike Deren
-- 2016-Sep-15		3			Clean-up while loop logic and add PlanChangeLog entry			    Chris Fleming
-- 2016-Oct-5		4			Updated rounding in while loop to round after subtraction		    Chris Fleming
-- 2017-Jan-25		5			Added TargetPremium variable, and added Premium calculation			Mark Freel
--								Target Premium now passed to spTargetActAdj
-- 2019-Sep-27      6           Update the SP to calculate run time of spAppCalculateMER            Satyam Singhal
-- 2019-Oct-16      7           Updated the code to calculate ExecutionEndTime						Pooja Dahiya
--2019-oct-30		8			Removed 'HUMAD\' to UserID											Chhavi Sinha 
-- 2019-dec-14		9			Removed logic for ExecutionEndTime									Deepali
--2019-dec-20		10			removed unnecessary code for UserID									Deepali
--2020-Oct-30		11			Removing GRANT EXECUTE												Rodney Smith
-- 2021-Sep-19	    12			Revisions to capture error/validation messages						Bob Knadler
--								  from upstream execution of Target Mbr Prem
-- 2023-Aug-01		13			Added INternal Parameter 											Sheetal Patil 
-- ---------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spTargetActAdjForMER]
(
    @ForecastID INT,
    @UserID CHAR(7),
	@TargetedMER DECIMAL(10,8),
	@ActAdj DECIMAL(10,6) OUT,
	@ValidationMessage VARCHAR(MAX) OUT		--BK added 9/4/21
)
AS
BEGIN
	-- --------------------------------------------------------------------------------------------
    -- ---Variable declartion and constant population----------------------------------------------
		DECLARE @NewMER DECIMAL(10,8),
				@NewMER1 DECIMAL(10,6),
				@ActAdjFactor DECIMAL(10,6),
				@IterationCount INT,
				@TargetPremium DECIMAL(12,8),
				@XForecastID INT = @ForecastID

		SET @IterationCount = 0

		SET @ValidationMessage = '0'  --BK added 9/4/21, set to what Target Member Premium returns if no error
		
		SELECT @NewMER  = CAST( 1 - (ExpensePMPM + UncollectedPremium + UserFee + Profit + InsurerFee) / TotalReqRev AS DECIMAL (10,8))
		FROM dbo.fnAppGetBidSummary(@XForecastID)
		
		SELECT @TargetPremium = 
                ROUND(bs.BasicMemberPremium + bs.CS - 
                ra.RedABCostShare + bs.Sup_Prem - 
                ra.SuppPremBuydown + bs.RxBasicPremium - 
                ra.RxBasicBuyDown + bs.RxSuppPremium - ra.RxSuppBuyDown,1)
        FROM    dbo.fnAppGetBidSummary(@XForecastID) bs
		  INNER JOIN dbo.fnGetMARebateAllocation(@XForecastID) ra
			ON bs.ForecastID = ra.ForecastID

	-- --------------------------------------------------------------------------------------------
    -- ---Run the iterations to determine the Util factor that would produce the targeted MER------
    -- ---This section is the bulk of the processing time------------------------------------------
	
	
		SET @ActAdj = 1.0
		WHILE ((ABS(ROUND((@NewMER - @TargetedMER),4))) > 0) AND (@IterationCount < 6)
					AND (@ValidationMessage = '0')		-- Bob K 9/20/21 added this last condition for success of Targeting Member Prem pre-requisite
		BEGIN
		
			EXEC dbo.spTargetActAdj @XForecastID, @UserID, @TargetedMER, @TargetPremium, @ActAdjFactor = @ActAdjFactor OUT, @NewMER1 = @NewMER OUT,
					@ValidationMessage = @ValidationMessage OUT
			SET @ActAdj = @ActAdj * @ActAdjFactor
			SET @IterationCount = @IterationCount + 1

			 
		END
		

	-- --------------------------------------------------------------------------------------------
	IF @ValidationMessage = '0' --No Error messages from step to target member premium first in spTargetActAdj
		BEGIN
    -- ---PlanChangeLog Entry----------------------------------------------------------------------
			INSERT INTO dbo.PlanChangeLog (ForecastID, ProcName, Value, AuditUserID, AuditTime)
			VALUES (@XForecastID, 'MER', @ActAdj, @UserID, GETDATE())
		END
    -- --------------------------------------------------------------------------------------------
	-- --------------------------------------------------------------------------------------------

END
GO
