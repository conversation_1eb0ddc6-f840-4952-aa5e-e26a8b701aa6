SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnCostSharingStandards
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2010-Apr-26
-- HEADER UPDATED: 2011-Apr-07
--
-- DESCRIPTION: This function returns a table of benefits and CMS standards as well as a 'Pass' or 'Fail'
--
-- PARAMETERS:
--	Input:
--		@PlanIndex
--	Output:
--
-- TABLES:
--	Read:
--		LkpIntBenefitCategory
--		LkpIntCostSharingStandards
--		SavedPlanBenefitDetail
--		Benefits_SavedBenefitOption
--		Saved<PERSON><PERSON><PERSON>eader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Apr-26		1			Initial Version														Michael Siekerka
-- 2010-Apr-27      2			Corrected handling of HMO-POS (Combined MOOP specified,	as  		Casey Sanders
--									opposed to IN MOOP).
-- 2010-Jun-03      3			Increased CostShareType's character length from 9 to 20. For 		James Wu
--									product type ID = 4, changed the criteria to look at
--									@INMOOP instead of @CombinedMOOP
-- 2010-Jul-28		4			Moved to 2012														Joe Casey
-- 2010-Aug-23		5			Removed IsActivePlan And PlanVersion								Joe Casey
-- 2010-Sep-21      6           Changed INBenefitDayRange -> INDayRangeEnd                          Joe Casey
-- 2011-Apr-06		7			Added Benefit Categories per updated CMS regulations				Craig Wright
-- 2011-Apr-07		8			Restructured the function.  Altered LkpIntCostSharingStandards		Joe Casey
--									and changed joins and select to account for those changes.
-- 2011-Apr-18		9			Removed Nuclear benefit from list.									Craig Wright
-- 2011-Apr-18		10			Added an ISNULL clause in the inner join to account for benefits	Craig Wright
--									who have no restrictions under voluntary moop, but do for
--									mandatory moop.
-- 2011-Apr-19		11			Made sure Pass/Fail will fail if MOOP is fail.						Joe Casey
-- 2011-Apr-20		12			Updated restrictions for Pass/Fail for Voluntary N/A				Joe Casey
-- 2011-Apr-21		13			Removed Psych - OP													Joe Casey
-- 2011-Apr-28		14			Made sure Pass/Fail will pass if Benefit is $0						Craig Wright
-- 2011-May-09		15			Corrected EndDayRange comparison to account for multiple tiers		Joe Casey
-- 2011-May-10		16			Multiple tier correction to only account for SNF					Joe Casey
-- 2011-May-12		17			UPdated for new CMS MOOP standards									Craig Wright
-- 2012-Mar-27		18			Updated for new CMS MOOP Standards (HMOPOS)							Trevor Mahoney
-- 2012-Mar-28		19			Deleted Benefit Category 60; Added 128-130							Trevor Mahoney
-- 2012-Apr-06		20			Corrected Volunatary Diabetic/DME categories (see note below)		Trevor Mahoney
-- 2012-Apr-12		21			Added Benefit Category 61											Mike Deren
-- 2013-Jan-25		22			0320 - Added functionality for the multi-tiered IP					Mason Roberts
-- 2013-Mar-25      23          0383 - Added BenCat 72 to reflect new CS req for 2014               Lindsay Allen
-- 2013-Mar-28      24          0383 - Removed BenCat 119 per Denward Chung's email                 Lindsay Allen
-- 2014-Apr-04      25          Add in 87 & 88 for OT. Also 131 & 132 that are new                  Lewis Ng
-- 2014-Apr-17		26			Add in 77 & 113														Siliang Hu
-- 2014-Apr-17		27			Add in 74 & 119														Siliang Hu
-- 2014-Apr-22		28			Remove 74 (Psych-OP), 77(Radiology-FSR) and 113
--								(Radiology OP-Facility) from Prong 2 Test per Trevor's request		Siliang Hu
-- 2014-Apr-23		29			Replace the list of benefit category with							Siliang Hu
--								LkpIntBenefitCategoryProng2
-- 2017-Jun-20		30			Changed LkpIntBenefitCategoryProng2 to = IsProng2 from				Crystal Quirino
--									LkpIntBenefitCategory					
-- 2020-Mar-12		31			Updated MOOP Limits to reflect the changes in the BPT				Alex Beruscha
-- 2020-Nov-11		32			Adding an intermediate MOOP per CMS potential request and removed
--								EGWP reference														Jennifer Chapman
-- 2021-Apr-13		33			Removing Intermediate MOOP logic as CMS is nto requiring for BY2022 Jennifer Chapman
-- 2021-Aug-06		34			Extensive Update to CostSharType to allow updates 
--								from dbo.LkpExtCMSMOOPLimits										Phillip Leigh
-- 2022-May-31					MAAUI migration; removed table SavedPlanDeductibleMOOPDetail -
--								using table Benefits_SavedBenefitOption to extract MOOP values, 
--								for table SavedPlanBenefitDetail, replaced reference from PlanIndex 
--								to ForecastID; removed nested queries								Aleksandar Dimitrijevic
--2022-Dec-16		35			4119454 Deployment objects from Dev-Grow							Aleksandar Dimitrijevic
--2023-Apr-19		36			Removed CASE clause that replaces INBenefitTypeID with 2 if the		Alex Brandt
--								INBenefitTypeID is 5
--2023-May-02		37			Check and exclude null INBenefitTypeID								Adam Gilbert
--2023-Dec-21		38			IsProng2 INNER JOIN issue											Alex Beruscha
-- 2024-May-05      39			Added NOLOCK Table Hint												Kiran Kola
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnCostSharingStandards]   
    (@ForecastID INT
    )

RETURNS @Return TABLE
    (
	BenefitCategoryName VARCHAR(50),
	CostShareType VARCHAR(20) DEFAULT NULL,
	INBenefitValue DECIMAL(21,16) DEFAULT NULL,
	CMSValue DECIMAL(21,16) DEFAULT NULL,
	INDayRangeEnd TINYINT DEFAULT NULL,
	PassOrFail VARCHAR(4) DEFAULT 'Fail'
	)
AS

BEGIN 
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
DECLARE @NoMOOP CHAR(12) = 'Missing MOOP';	

--New Code to accommodate updates from table LkpExtCMSMOOPLimits		
		--moops
		DECLARE @MOOPs TABLE
			(
			 ForecastID INT,
			 CostShareType VARCHAR(20),
			 INMOOP1 INT,
			 OONMOOP1 INT,
			 CombinedMOOP1 INT,
			 INMOOP INT,
			 OONMOOP INT,
			 CombinedMOOP INT,
			 ProductTypeID TINYINT
			)

		INSERT INTO @MOOPs
		SELECT DISTINCT sfs.ForecastID,
						ISNULL(LMT.CostShareType,@NoMOOP) as CostShareType,
						MAX(ISNULL(LMT.MaxINMOOP,0)) as INMOOP1,
						MAX(ISNULL(LMT.MaxOONMOOP,0)) as OONMOOP1,
						MAX(ISNULL(LMT.MaxCOMBMOOP,0)) as CombinedMOOP1,
						sbo.INMOOP,	
						sbo.OONMOOP,
						sbo.CombinedMOOP,
						spi.ProductTypeID
		FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)
		INNER JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
			ON SFS.PlanInfoID=spi.PlanInfoID
		LEFT JOIN dbo.Benefits_SavedBenefitOption sbo WITH (NOLOCK)
			ON sfs.PlanInfoID = sbo.PlanInfoID
		   AND sfs.BenefitOptionID = sbo.BenefitOptionID
		LEFT JOIN  dbo.LkpExtCMSMOOPLimits LMT WITH (NOLOCK)
			ON lmt.ProductTypeID = spi.ProductTypeID
		   AND  sbo.INMOOP BETWEEN lmt.MinINMOOP AND lmt. MaxINMOOP
		WHERE spi.ProductTypeID IN(1,5) 
		  AND sfs.ForecastID = @ForecastID
		GROUP BY sfs.ForecastID, 
			     spi.ProductTypeID,
				 sbo.INMOOP,
				 sbo.OONMOOP,
				 sbo.CombinedMOOP,
				 lmt.CostShareType
	--------------------------------------------------------------------

	DECLARE @tableCostShareType TABLE
		(
		 ForecastID INT,
		 InMOOP INT,
		 OONMOOP INT,
		 COMBMOOP INT,
		 PlanTypeID INT,
		 CostShareType VARCHAR(20)
		)
---------------------------------------------------------------------------------------------------------
	-- variable table @tableCostShareType recieves data from three queries below.
	INSERT INTO @tableCostShareType
	--This query gathers the data from HMO and HMOPOS plans. Cost Share Type is deterimined from In-Network MOOP only. Having combined will not cause Fail for HMOPOS but Combined is not evaulated
	-- Bid Model prevents HMO plans from having Combined

	SELECT a.ForecastID,
		   a.INMOOP,
		   a.OONMOOP,
		   a.CombinedMOOP,
		   a.ProductTypeID,
		   a.CostShareType
	FROM @MOOPs a
    LEFT JOIN dbo.LkpExtCMSMOOPLimits LMT WITH (NOLOCK)
		ON LMT.ProductTypeID = a.ProductTypeID
       AND LMT.MaxINMOOP = isnull(a.INMOOP,0)
       AND LMT.MaxOONMOOP = isnull(a.OONMOOP,0)
       AND LMT.MaxCOMBMOOP = isnull(a.CombinedMOOP,0)
--------------------------------------------------------------------------------------------------------------------------------------------

		--MOOPs2
		DECLARE @MOOPs2 TABLE
			(
			 ForecastID INT,
			 INMOOP1 INT,
			 OONMOOP1 INT,
			 CombinedMOOP1 INT,
			 INMOOP INT,
			 OONMOOP INT,
			 CombinedMOOP INT,
			 ProductTypeID TINYINT
			)

		INSERT INTO @MOOPs2

		SELECT DISTINCT sfs.ForecastID,
						MAX(ISNULL(LMT.MaxINMOOP,0)) as INMOOP1,
						MAX(ISNULL(LMT.MaxOONMOOP,0)) as OONMOOP1,
						MAX(ISNULL(LMT.MaxCOMBMOOP,0)) as CombinedMOOP1,
						sbo.INMOOP,	
						sbo.OONMOOP,
						sbo.CombinedMOOP,
						spi.ProductTypeID

		FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)
		INNER JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
			ON SFS.PlanInfoID=spi.PlanInfoID
		LEFT JOIN dbo.Benefits_SavedBenefitOption sbo WITH (NOLOCK)
			ON sfs.PlanInfoID = sbo.PlanInfoID
		    AND sfs.BenefitOptionID = sbo.BenefitOptionID
		LEFT JOIN dbo.LkpExtCMSMOOPLimits LMT WITH (NOLOCK)
			ON lmt.ProductTypeID = spi.ProductTypeID
		   AND (sbo.INMOOP BETWEEN lmt.MinINMOOP AND lmt. MaxINMOOP)
		   AND (sbo.CombinedMOOP BETWEEN lmt.MinCOMBMOOP AND lmt.MaxCOMBMOOP)
		WHERE spi.ProductTypeID IN(2, 3) 

		  AND sfs.ForecastID=@ForecastID 
		  AND sbo.INMOOP IS NOT NULL 
		  AND sbo.CombinedMOOP IS NOT NULL
		GROUP BY sfs.ForecastID, 
				 spi.ProductTypeID,
				 sbo.INMOOP,
				 sbo.OONMOOP,
				 sbo.CombinedMOOP

	--------------------------------------------------------------------------------------------------------------------------
	INSERT INTO @tableCostShareType
	--This query gathers data from LPPO, RPPO Cost Share Type depends on both In-Network and Combined Network MOOP when both are present
	SELECT a.ForecastID,
		   a.INMOOP,
		   a.OONMOOP,
		   a.CombinedMOOP,
		   a.ProductTypeID,
		   ISNULL(LMT.CostShareType,'Inconsistent MOOP')

	FROM @MOOPs2 a

	LEFT JOIN dbo.LkpExtCMSMOOPLimits LMT

	   ON LMT.ProductTypeID = a.ProductTypeID
	  AND LMT.MaxINMOOP = ISNULL(a.INMOOP1,0)
	  AND LMT.MaxOONMOOP = ISNULL(a.OONMOOP1,0)
	  AND LMT.MaxCOMBMOOP = ISNULL(a.CombinedMOOP1,0)

	INSERT INTO @tableCostShareType
	--This query gathers data from LPPO, RPPO Cost Share Type depends on both In-Network and Combined Network MOOP when one or both are missing 

	SELECT ForecastID,
		   INMOOP,
		   OONMOOP,
		   CombinedMOOP,
		   ProductTypeID,
		   @NoMOOP
    FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)
	INNER JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
		ON SFS.PlanInfoID=spi.PlanInfoID
    LEFT JOIN dbo.Benefits_SavedBenefitOption sbo  WITH (NOLOCK)
		ON sfs.PlanInfoID = sbo.PlanInfoID
	   AND sfs.BenefitOptionID = sbo.BenefitOptionID
    WHERE spi.ProductTypeID IN(2, 3) 
	  AND sfs.ForecastID = @ForecastID 
	  AND sbo.INMOOP IS NULL 
	   OR spi.ProductTypeID IN(2, 3) 
	  AND sfs.ForecastID = @ForecastID 
	  AND sbo.CombinedMOOP is NULL
--------------------------------------------------------------------------------------------------------------------------------------------------

		--MOOPs3
		DECLARE @MOOPs3 TABLE
			(
			 ForecastID INT,
			 CostShareType  VARCHAR(20),
			 INMOOP1 INT,
			 OONMOOP1 INT,
			 CombinedMOOP1 INT,
			 INMOOP INT,

			 OONMOOP INT,
			 CombinedMOOP INT,
			 ProductTypeID TINYINT
			)

		INSERT INTO @MOOPs3
		SELECT DISTINCT sfs.ForecastID,
						ISNULL(LMT.CostShareType, @NoMOOP) as CostShareType,
						max(isnull(LMT.MaxINMOOP,0)) as INMOOP1,
						max(isnull(LMT.MaxOONMOOP,0)) as OONMOOP1,
						max(isnull(LMT.MaxCOMBMOOP,0)) as COMBMOOP1,
						sbo.INMOOP,	
						sbo.OONMOOP,
						sbo.CombinedMOOP,
						spi.ProductTypeID
		FROM dbo.SavedForecastSetup sfs WITH (NOLOCK)
		LEFT JOIN dbo.Benefits_SavedBenefitOption sbo WITH (NOLOCK)
			ON sfs.PlanInfoID = sbo.PlanInfoID
		   AND sfs.BenefitOptionID = sbo.BenefitOptionID
		INNER JOIN SavedPlanInfo SPI WITH (NOLOCK)
			ON SFS.PlanInfoID=spi.PlanInfoID
		LEFT JOIN dbo.LkpExtCMSMOOPLimits LMT WITH (NOLOCK)
			ON lmt.ProductTypeID = spi.ProductTypeID
		   AND  sbo.CombinedMOOP BETWEEN lmt.MinCOMBMOOP AND lmt.MaxCOMBMOOP
		WHERE spi.ProductTypeID IN(4,6) 
		  AND sfs.ForecastID = @ForecastID
		GROUP BY sfs.ForecastID, 
				 lmt.CostShareType, 
				 spi.ProductTypeID,
				 sbo.INMOOP,
				 sbo.OONMOOP,
				 sbo.CombinedMOOP

	INSERT INTO @tableCostShareType
	--This Query gathers data for PFFS and PFFSPOS plans. These plans must have Combined MOOP
	SELECT ForecastID,
		   INMOOP,
		   OONMOOP,
		   CombinedMOOP,
		   ProductTypeID,
		   CostShareType
	FROM @MOOPs3

	----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
	-- Run comparisons and return
		INSERT INTO @Return
		SELECT BenefitCategoryName = bc.BenefitCategoryName,
			   CostShareType = tcs.CostShareType,
			   INBenefitValue = bd.INBenefitValue,
			   CMSValue = css.INMaxBenefit,
			   INDayRangeEnd = CASE ISNULL(bd.INDayRangeEnd,0)
								WHEN 0 THEN NULL
								ELSE bd.INDayRangeEnd
							   END,
			   PassOrFail = 
							CASE
								WHEN tcs.CostShareType IN ('No MOOP', @NoMOOP, 'Fail')
									THEN 'Fail'
								WHEN bd.INBenefitValue = 0
									THEN 'Pass'
								WHEN bd.INBenefitValue <= css.INMaxBenefit 
									THEN 'Pass'
								WHEN tcs.CostShareType IN ('Lower') AND bc.BenefitCategoryID IN (104,105,124,128,129,130)--Voluntary N/A; left Intermediate for future updates, but it should never come through
									THEN 'Pass'
								ELSE 'Fail'
							END
		FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)
		INNER JOIN dbo.SavedPlanBenefitDetail bd  WITH (NOLOCK)
			ON sfs.ForecastID = bd.ForecastID 
		   AND sfs.PlanInfoID = bd.PlanInfoID
		   AND sfs.BenefitOptionID = bd.BenefitOptionID
		INNER JOIN @tableCostShareType tcs
			ON sfs.ForecastID= tcs.ForecastID
		INNER JOIN dbo.LkpIntBenefitCategory bc  WITH (NOLOCK)
			ON bd.BenefitCategoryID = bc.BenefitCategoryID 
		INNER JOIN dbo.LkpIntCostSharingStandards css WITH (NOLOCK)
			ON css.BenefitCategoryID = bd.BenefitCategoryID
		   AND css.INEndDayRange =
									--If SNF, use the BeginDay because that will have the lowest value in the range
									CASE WHEN bd.BenefitCategoryID = 83 THEN bd.INDayRangeBegin 
										WHEN bd.BenefitCategoryID IN(65, 66, 131) THEN
											CASE WHEN bd.INDayRangeBegin = 0 THEN 1
												 ELSE bd.INDayRangeEnd
											END
										ELSE
											CASE WHEN ISNULL(bd.INDayRangeEnd, 0) = 0
												 THEN 1
												 ELSE ISNULL(bd.INDayRangeEnd, 1)
											END
									END
		   AND css.INBenefitTypeID = bd.INBenefitTypeID
		   AND css.MOOPCategory =  tcs.CostShareType
		WHERE sfs.ForecastID = @ForecastID
		  AND bd.IsBenefitYearCurrentYear = 0
		  AND IsProng2 = 1
		  AND bd.INBenefitTypeID IS NOT NULL --added to filter OON day range benefit rows
		ORDER BY bc.BenefitCategoryName, 


				 bd.INDayRangeEnd ASC

		IF NOT EXISTS (SELECT 1 FROM @Return) AND EXISTS (SELECT 1 FROM @tableCostShareType WHERE CostShareType IN ('No MOOP', @NoMOOP))
			BEGIN
				INSERT INTO @Return(BenefitCategoryName, CostShareType)
				VALUES(@NoMOOP, 'Error')
			END		 
		ELSE IF NOT EXISTS (SELECT 1 FROM @Return)
			BEGIN
				INSERT INTO @Return(BenefitCategoryName, CostShareType)
				VALUES('Check MOOP', 'Error')
			END	 

    RETURN
END
