
/****** Object:  StoredProcedure [dbo].[Trend_ProjProcess_spCalcPlanTrends_RepCat]    Script Date: 9/10/2021 9:44:29 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---
-- PROCEDURE NAME: Trend_ProjProcess_spCalcPlanTrends_RepCat
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: MAR-18-2020
--
-- DESCRIPTION: Trends are brought in from Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal, and adjustments from Trend_ProjProcess_CalcPlanAdjmt_RepCat are aggregated and appended. 
--				Additionally, data from the Trend_ProjProcess_CalcIsPlanLevel tables (_Population, _OulierClaims, _InducedUtilization, _Contractual, and _CMSReimb) are
--				appended.  All cost and use adjustments are rounded to six decimal places.  
--              
-- PARAMETERS:
--  Input  :	@PlanInfoIDList
--				@LastUpdateByID
--				@WriteIndicator (1=National Analytics triggers proc, 2=Market Support triggers proc)
--
--  Output : NONE
--
-- TABLES : Read :  Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal
--					Trend_ProjProcess_CalcNotPlanLevel_Trends
--					Trend_ProjProcess_CalcPlanAdjmt_RepCat
--					Trend_ProjProcess_CalcIsPlanLevel_Population
--					Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims
--					Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization
--					Trend_ProjProcess_CalcIsPlanLevel_Contractual
--					Trend_ProjProcess_CalcIsPlanLevel_CMSReimb
--					Trend_SavedComponentInfo
--					LkpIntBenefitCategory
--					LkpIntPlanYear
--					
--          Write:  Trend_ProjProcess_CalcPlanTrends_RepCat
--                  Trend_ProjProcess_CalcPlanTrends_RepCat_staged
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---
-- DATE            VERSION      CHANGES MADE																DEVELOPER        
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

--
-- MAR-18-2020      1           Initial Version																Jake Lewis
-- JUN-25-2020		2			Code change to allow zero trend override to flow through properly			Jake Lewis
-- NOV-19-2020      3           Include NOLOCK & ROWLOCK                                                    Manisha Tyagi
-- Dec-05-2020		4			Adding local temp table to improve db performance					        Surya Murthy
-- MAR-10-2021		5			Handle -1 adjustments														Jake Lewis
-- AUG-08-2021		6			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's		            Franklin Fu
-- OCT-17-2022		7			Added Column names and NewID() column in insert statement					Sheetal Patil

-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcPlanTrends_RepCat]
@PlanInfoIDList  VARCHAR(MAX) = NULL
,@WriteIndicator TINYINT    -- 1 for National Analytics, 2 for Market Support
,@LastUpdateByID CHAR(7)

AS
    BEGIN
        SET NOCOUNT ON;		 
        -- Declare variables
        DECLARE @PackageOptionID AS INT;
        SET @PackageOptionID = (SELECT  DISTINCT TOP(1)
                                        PackageOptionID
                                FROM    dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends WITH (NOLOCK) ORDER BY PackageOptionID);

        DECLARE @BidYear INT;
        SET @BidYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);

        DECLARE @CurrentYear INT;
        SET @CurrentYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);

        -- Plan Info
        IF (SELECT  OBJECT_ID ('tempdb..#PlanInfo')) IS NOT NULL
            DROP TABLE #PlanInfo;
        SELECT  DISTINCT
                PlanInfoID
               ,CPS
               ,PlanYear AS PlanYearID
        INTO    #PlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear
                AND IsHidden = 0
                AND IsOffMAModel = 'No'
                AND Region NOT IN ('Unmapped')
                AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@PlanInfoIDList, ',', 1) )
                     OR @PlanInfoIDList IS NULL);

        -- Trend Year
        IF (SELECT  OBJECT_ID ('tempdb..#TrendYr')) IS NOT NULL DROP TABLE #TrendYr;
        SELECT  PlanYearID
        INTO    #TrendYr
        FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
        WHERE   PlanYearID >= @CurrentYear;

        -- Reporting Category
        IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
        SELECT  DISTINCT
                ReportingCategory
        INTO    #RepCat
        FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
        WHERE   ReportingCategory <> 'NULL';

        --Component Reporting
        IF (SELECT  OBJECT_ID ('tempdb..#ComponentReporting')) IS NOT NULL
            DROP TABLE #ComponentReporting;
        SELECT  DISTINCT
                ComponentReporting AS Component
        INTO    #ComponentReporting
        FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
        WHERE   IsPartOfPackage = 1;

        --Component
        IF (SELECT  OBJECT_ID ('tempdb..#Component')) IS NOT NULL
            DROP TABLE #Component;
        SELECT  DISTINCT
                Component
        INTO    #Component
        FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
        WHERE   IsPartOfPackage = 1;

        --RateType
        IF (SELECT  OBJECT_ID ('tempdb..#RateType')) IS NOT NULL
            DROP TABLE #RateType;
        SELECT  DISTINCT
                number AS RateType
        INTO    #RateType
        FROM    master..spt_values WITH(NOLOCK)
        WHERE   number BETWEEN 1 AND 2;

        --Shell
        IF (SELECT  OBJECT_ID ('tempdb..#Shell')) IS NOT NULL DROP TABLE #Shell;
        SELECT      a.PlanInfoID
                   ,a.CPS
                   ,a.PlanYearID
                   ,d.Component AS Component
                   ,c.ReportingCategory
                   ,b.PlanYearID AS TrendYearID
                   ,e.RateType AS RateType
                   ,CASE WHEN ReportingCategory = 'Part B Rx Pharmacy' THEN CONCAT ('Rx', Component) ELSE Component END AS 'ComponentLookup'
                   ,CASE WHEN ReportingCategory = 'Part B Rx Pharmacy'
                              AND  CONCAT ('Rx', Component) NOT IN (SELECT Component FROM  #Component) THEN 1
                         ELSE 0 END AS 'NoTrendForPBRxPharm'
        INTO        #Shell
        FROM        #PlanInfo a
       INNER JOIN   #TrendYr b
               ON 1 = 1
       INNER JOIN   #RepCat c
               ON 1 = 1
       INNER JOIN   #ComponentReporting d
               ON 1 = 1
       INNER JOIN   #RateType e
               ON 1 = 1;


        IF @WriteIndicator = 1 --National Analytics triggers the procedure

            BEGIN

                -- Delete from and insert into final output table
                DELETE  FROM dbo.Trend_ProjProcess_CalcPlanTrends_RepCat_staged WITH (ROWLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo));
                INSERT INTO dbo.Trend_ProjProcess_CalcPlanTrends_RepCat_staged WITH (ROWLOCK)
                    (PlanInfoID
                    ,CPS
                    ,PlanYearID
                    ,BaseYearID
               ,TrendYearID
                    ,RateType
                    ,ReportingCategory
                    ,PackageOptionID
                    ,ComponentVersionID
                    ,ComponentReporting
                    ,PlanTypeGranularity
                    ,PlanTypeGranularityValue
                    ,CostAdjustment
                    ,UseAdjustment
                    ,IsOverride
                    ,OverrideID
                    ,OverrideDescription
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT      DISTINCT
                            a.PlanInfoID
                           ,a.CPS
                           ,a.PlanYearID
                           ,BaseYearID = a.PlanYearID - 2
                           ,a.TrendYearID
                           ,a.RateType
                           ,a.ReportingCategory
                           ,@PackageOptionID
                           ,b.ComponentVersionID
                           ,a.Component
                           ,b.PlanTypeGranularity
                           ,b.PlanTypeGranularityValue
                           ,CASE WHEN a.NoTrendForPBRxPharm = 1 THEN 0 ELSE ROUND (CASE WHEN b.PlanTypeGranularity = '1.0' THEN 0 ELSE c.CostAdjustment END, 6) END
                           ,CASE WHEN a.NoTrendForPBRxPharm = 1 THEN 0 ELSE ROUND (CASE WHEN b.PlanTypeGranularity = '1.0' THEN 0 ELSE c.UseAdjustment END, 6) END
                           ,b.IsOverride
                           ,b.OverrideRuleID
                           ,b.OverrideDescription
                           ,@LastUpdateByID
                           ,GETDATE ()
                FROM        #Shell a
                LEFT JOIN   dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal b WITH (NOLOCK)
                       ON a.PlanInfoID = b.PlanInfoID
                          AND   a.ComponentLookup = b.Component
                LEFT JOIN   dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends c WITH (NOLOCK)
                       ON a.TrendYearID = c.PlanYearID
                          AND   a.ReportingCategory = c.ReportingCategory
                          AND   b.ComponentVersionID = c.ComponentVersionID
                          AND   b.PlanTypeGranularityValue = c.PlanTypeGranularityValue
                WHERE       (a.PlanInfoID IN (SELECT    PlanInfoID FROM #PlanInfo))
                UNION
                SELECT      PlanInfoID
                           ,CPS
                           ,PlanYearID
                           ,BaseYearID
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,NULL
                           ,NULL
                           ,AdjustmentType
                           ,NULL
                           ,NULL
                            --,ROUND (EXP (SUM (LOG (1 + CostAdjustment))) - 1, 6) -- JL 03/10/2021
                            --,ROUND (EXP (SUM (LOG (1 + UseAdjustment))) - 1, 6) -- JL 03/10/2021
                           ,CASE WHEN MIN (CostAdjustment) = -1 THEN -1
                                 ELSE ROUND (EXP (SUM (LOG (NULLIF(1 + CostAdjustment,0)))) - 1, 6) END AS CostAdjustment 
                           ,CASE WHEN MIN (UseAdjustment) = -1 THEN -1
                                 ELSE ROUND (EXP (SUM (LOG (NULLIF(1 + UseAdjustment,0)))) - 1, 6) END AS UseAdjustment 
                           ,NULL
                           ,NULL
                           ,NULL
                           ,@LastUpdateByID
                           ,GETDATE ()
                FROM        dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat WITH (NOLOCK)
                WHERE       (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))
                GROUP BY    PlanInfoID
                           ,CPS
                           ,PlanYearID
                           ,BaseYearID
           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,AdjustmentType
                UNION
                SELECT  PlanInfoID
                       ,CPS
                     ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,ROUND (CostAdjustment, 6)
                       ,ROUND (UseAdjustment, 6)
                       ,NULL
                       ,NULL
                       ,NULL
                       ,@LastUpdateByID
                       ,GETDATE ()
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,ROUND (CostAdjustment, 6)
                       ,ROUND (UseAdjustment, 6)
                       ,NULL
                       ,NULL
                       ,NULL
                       ,@LastUpdateByID
                       ,GETDATE ()
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,ROUND (CostAdjustment, 6)
                       ,ROUND (UseAdjustment, 6)
                       ,NULL
                       ,NULL
                       ,NULL
                       ,@LastUpdateByID
                       ,GETDATE ()
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,ROUND (CostAdjustment, 6)
                       ,ROUND (UseAdjustment, 6)
                       ,NULL
                       ,NULL
                       ,NULL
                       ,@LastUpdateByID
                       ,GETDATE ()
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
      ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,ROUND (CostAdjustment, 6)
                       ,ROUND (UseAdjustment, 6)
                       ,NULL
                       ,NULL
                       ,NULL
                       ,@LastUpdateByID
                       ,GETDATE ()
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo));

            END;

        IF @WriteIndicator = 2 --Market Support triggers the procedure

            BEGIN

                -- Delete from and insert into final output table
                --DELETE  FROM dbo.Trend_ProjProcess_CalcPlanTrends_RepCat WITH (ROWLOCK)
                --WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo));

				-- Deleting with BATCH process
				DECLARE @RepCatRowcount INT = 1
				WHILE @RepCatRowcount > 0
				BEGIN
					DELETE TOP (10000)
					FROM dbo.Trend_ProjProcess_CalcPlanTrends_RepCat
					WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))
					SET @RepCatRowcount = @@ROWCOUNT
				END

				--Creating temp table for union1 
				IF (SELECT  OBJECT_ID ('tempdb..#TempforUnionForPart1')) IS NOT NULL
					DROP TABLE #TempforUnionForPart1;
					
				SELECT     DISTINCT
                            a.PlanInfoID
                           ,a.CPS
                           ,a.PlanYearID
                           ,BaseYearID = a.PlanYearID - 2
                           ,a.TrendYearID
                           ,a.RateType
                           ,a.ReportingCategory
                           ,'PackageOptionID' = @PackageOptionID 
                           ,b.ComponentVersionID
                           ,a.Component
                           ,b.PlanTypeGranularity
                           ,b.PlanTypeGranularityValue
                           ,'CostAdjustment' = CASE WHEN a.NoTrendForPBRxPharm = 1 THEN 0 ELSE ROUND (CASE WHEN b.PlanTypeGranularity = '1.0' THEN 0 ELSE c.CostAdjustment END, 6) END
                           ,'UseAdjustment' = CASE WHEN a.NoTrendForPBRxPharm = 1 THEN 0 ELSE ROUND (CASE WHEN b.PlanTypeGranularity = '1.0' THEN 0 ELSE c.UseAdjustment END, 6) END
                           ,b.IsOverride
                           ,b.OverrideRuleID
                           ,b.OverrideDescription
                           ,'LastUpdateByID' = @LastUpdateByID
                           ,'LastUpdateDateTime' = GETDATE ()
				INTO #TempforUnionForPart1
                FROM        #Shell a
                LEFT JOIN   dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal b WITH (NOLOCK)
                       ON a.PlanInfoID = b.PlanInfoID
                          AND   a.ComponentLookup = b.Component
                LEFT JOIN   dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends c WITH (NOLOCK)
                       ON a.TrendYearID = c.PlanYearID
                          AND   a.ReportingCategory = c.ReportingCategory
                          AND   b.ComponentVersionID = c.ComponentVersionID
                          AND   b.PlanTypeGranularityValue = c.PlanTypeGranularityValue
                WHERE       (a.PlanInfoID IN (SELECT    PlanInfoID FROM #PlanInfo))

				--Creating temp table for union2
				IF (SELECT  OBJECT_ID ('tempdb..#TempforUnionForPart2')) IS NOT NULL
					DROP TABLE #TempforUnionForPart2;

				SELECT      PlanInfoID
                           ,CPS
                           ,PlanYearID
                           ,BaseYearID
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,'1' = NULL
                           ,'2' = NULL
						   ,AdjustmentType
                           ,'3' = NULL
                           ,'4' = NULL
                         --,'CostAdjustment' = ROUND (EXP (SUM (LOG (1 + CostAdjustment))) - 1, 6) -- JL 03/10/2021
						 --,'UseAdjustment' = ROUND (EXP (SUM (LOG (1 + UseAdjustment))) - 1, 6) -- JL 03/10/2021
                           ,'CostAdjustment' = CASE WHEN MIN (CostAdjustment) = -1 THEN -1
                                                    ELSE ROUND (EXP (SUM (LOG (NULLIF(1 + CostAdjustment,0)))) - 1, 6) END
                           ,'UseAdjustment' = CASE WHEN MIN (UseAdjustment) = -1 THEN -1
                                                   ELSE ROUND (EXP (SUM (LOG (NULLIF(1 + UseAdjustment,0)))) - 1, 6) END
                           ,'5' = NULL
                           ,'6' = NULL
                           ,'7' = NULL
                           ,'LastUpdateByID' = @LastUpdateByID
                           ,'LastUpdateDateTime' = GETDATE ()
				INTO #TempforUnionForPart2
                FROM        dbo.Trend_ProjProcess_CalcPlanAdjmt_RepCat WITH (NOLOCK)
                WHERE       (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))
                GROUP BY    PlanInfoID
                           ,CPS
                           ,PlanYearID
                           ,BaseYearID
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,AdjustmentType

				--Creating temp table for union3
				IF (SELECT  OBJECT_ID ('tempdb..#TempforUnionForPart3')) IS NOT NULL
					DROP TABLE #TempforUnionForPart3;
				SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,'1' = NULL
                       ,'2' = NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,'CostAdjustment' = ROUND (CostAdjustment, 6)
                       ,'UseAdjustment' = ROUND (UseAdjustment, 6)
                       ,'3' = NULL
                       ,'4' = NULL
                       ,'5' = NULL
                       ,'LastUpdateByID' = @LastUpdateByID
                       ,'LastUpdateDateTime' = GETDATE ()
				INTO #TempforUnionForPart3
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_Population WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))

				--Creating temp table for union4
				IF (SELECT  OBJECT_ID ('tempdb..#TempforUnionForPart4')) IS NOT NULL
					DROP TABLE #TempforUnionForPart4;

				SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,'1' = NULL
                       ,'2' = NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,'CostAdjustment' = ROUND (CostAdjustment, 6)
                       ,'UseAdjustment' = ROUND (UseAdjustment, 6)
                       ,'3' = NULL
                       ,'4' = NULL
                       ,'5' = NULL
                       ,'LastUpdateByID' = @LastUpdateByID
                       ,'LastUpdateDateTime' = GETDATE ()
				INTO #TempforUnionForPart4
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_OutlierClaims WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))

				--Creating temp table for union5
				IF (SELECT  OBJECT_ID ('tempdb..#TempforUnionForPart5')) IS NOT NULL
					DROP TABLE #TempforUnionForPart5;
				SELECT  PlanInfoID
                      ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                   ,'1' = NULL
                       ,'2' = NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,'CostAdjustment' = ROUND (CostAdjustment, 6)
                       ,'UseAdjustment' = ROUND (UseAdjustment, 6)
                       ,'3' = NULL
                       ,'4' = NULL
                       ,'5' = NULL
                       ,'LastUpdateByID' = @LastUpdateByID
                       ,'LastUpdateDateTime' = GETDATE ()
				INTO #TempforUnionForPart5
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_InducedUtilization WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))

				--Creating temp table for union6
				IF (SELECT  OBJECT_ID ('tempdb..#TempforUnionForPart6')) IS NOT NULL
					DROP TABLE #TempforUnionForPart6;
				SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,'1' = NULL
                       ,'2' = NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,'CostAdjustment' = ROUND (CostAdjustment, 6)
                       ,'UseAdjustment' = ROUND (UseAdjustment, 6)
                       ,'3' = NULL
                       ,'4' = NULL
                       ,'5' = NULL
                       ,'LastUpdateByID' = @LastUpdateByID
                       ,'LastUpdateDateTime' = GETDATE ()
				INTO #TempforUnionForPart6
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_Contractual WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo))

				--Creating temp table for union7
				IF (SELECT  OBJECT_ID ('tempdb..#TempforUnionForPart7')) IS NOT NULL
					DROP TABLE #TempforUnionForPart7;
				SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,'1' = NULL
                       ,'2' = NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,'CostAdjustment' = ROUND (CostAdjustment, 6)
                       ,'UseAdjustment' = ROUND (UseAdjustment, 6)
                       ,'3' = NULL
                       ,'4' = NULL
                       ,'5' = NULL
                       ,'LastUpdateByID' = @LastUpdateByID
                       ,'LastUpdateDateTime' = GETDATE ()
				INTO #TempforUnionForPart7
                FROM    dbo.Trend_ProjProcess_CalcIsPlanLevel_CMSReimb WITH (NOLOCK)
                WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo)); 

				--Creating temp table for RepCat table
				IF (SELECT  OBJECT_ID ('tempdb..#TempUpdateTrendRepCat')) IS NOT NULL
					DROP TABLE #TempUpdateTrendRepCat;

				CREATE TABLE #TempUpdateTrendRepCat
							([PlanInfoID] [int] ,
							[CPS] [char](13) ,
							[PlanYearID] [int] ,
							[BaseYearID] [int] ,
							[TrendYearID] [int] ,
							[RateType] [smallint] ,
							[ReportingCategory] [varchar](50) ,
							[PackageOptionID] [int] ,
							[ComponentVersionID] [int] ,
							[ComponentReporting] [varchar](50) ,
							[PlanTypeGranularity] [varchar](250) ,
							[PlanTypeGranularityValue] [varchar](500) ,
							[CostAdjustment] [decimal](18, 8) ,
							[UseAdjustment] [decimal](18, 8) ,
							[IsOverride] [bit] ,
							[OverrideID] [int] ,
							[OverrideDescription] [varchar](100) ,
							[LastUpdateByID] [char](13) ,
							[LastUpdateDateTime] [datetime] )

              INSERT INTO #TempUpdateTrendRepCat
                    (PlanInfoID
                    ,CPS
                    ,PlanYearID
                    ,BaseYearID
                    ,TrendYearID
                    ,RateType
                    ,ReportingCategory
                    ,PackageOptionID
                    ,ComponentVersionID
                    ,ComponentReporting
                    ,PlanTypeGranularity
                    ,PlanTypeGranularityValue
                    ,CostAdjustment
                    ,UseAdjustment
                    ,IsOverride
                    ,OverrideID
                    ,OverrideDescription
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
				SELECT      DISTINCT
                            PlanInfoID
                           ,CPS
                           ,PlanYearID
                           ,BaseYearID
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,@PackageOptionID
                           ,ComponentVersionID
                           ,Component
                           ,PlanTypeGranularity
                           ,PlanTypeGranularityValue
                           ,CostAdjustment
                           ,UseAdjustment
                           ,IsOverride
                           ,OverrideRuleID
                           ,OverrideDescription
                           ,LastUpdateByID
                           ,LastUpdateDateTime FROM #TempforUnionForPart1                
                UNION
                SELECT      PlanInfoID
                           ,CPS
                           ,PlanYearID
                           ,BaseYearID
                           ,TrendYearID
                           ,RateType
                           ,ReportingCategory
                           ,NULL
                           ,NULL
                           ,AdjustmentType
                           ,NULL
                           ,NULL
                           ,CostAdjustment
                           ,UseAdjustment
                           ,NULL
                           ,NULL
                           ,NULL
                           ,LastUpdateByID
                           ,LastUpdateDateTime FROM #TempforUnionForPart2
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,CostAdjustment
                       ,UseAdjustment
                       ,NULL
                       ,NULL
                       ,NULL
                       ,LastUpdateByID
                       ,LastUpdateDateTime FROM #TempforUnionForPart3
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,CostAdjustment
                       ,UseAdjustment
                       ,NULL
                       ,NULL
                 ,NULL
                       ,LastUpdateByID
                       ,LastUpdateDateTime FROM #TempforUnionForPart4
                UNION
                SELECT  PlanInfoID
     ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,CostAdjustment
                       ,UseAdjustment
                       ,NULL
                       ,NULL
                       ,NULL
                       ,LastUpdateByID
                       ,LastUpdateDateTime FROM #TempforUnionForPart5
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,CostAdjustment
                       ,UseAdjustment
                       ,NULL
                       ,NULL
                       ,NULL
                       ,LastUpdateByID
                       ,LastUpdateDateTime FROM #TempforUnionForPart6
                UNION
                SELECT  PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,BaseYearID
                       ,TrendYearID
                       ,RateType
                       ,ReportingCategory
                       ,NULL
                       ,NULL
                       ,Component
                       ,PlanTypeGranularity
                       ,PlanTypeGranularityValue
                       ,CostAdjustment
                       ,UseAdjustment
                       ,NULL
                       ,NULL
                       ,NULL
                       ,LastUpdateByID
                       ,LastUpdateDateTime FROM #TempforUnionForPart7

				--Finally inserting into main table from temp table
					INSERT INTO dbo.Trend_ProjProcess_CalcPlanTrends_RepCat 
							(PlanInfoID
							,CPS
							,PlanYearID
							,BaseYearID
							,TrendYearID
							,RateType
							,ReportingCategory
							,PackageOptionID
							,ComponentVersionID
							,ComponentReporting
							,PlanTypeGranularity
							,PlanTypeGranularityValue
							,CostAdjustment
							,UseAdjustment
							,IsOverride
							,OverrideID
							,OverrideDescription
							,LastUpdateByID
							,LastUpdateDateTime
							,ID)				
							SELECT 
							PlanInfoID
							,CPS
							,PlanYearID
							,BaseYearID
							,TrendYearID
							,RateType
							,ReportingCategory
							,PackageOptionID
							,ComponentVersionID
							,ComponentReporting
							,PlanTypeGranularity
							,PlanTypeGranularityValue
							,CostAdjustment
							,UseAdjustment
							,IsOverride
							,OverrideID
							,OverrideDescription
							,LastUpdateByID
							,LastUpdateDateTime
							,NEWID() FROM #TempUpdateTrendRepCat;
				--End
            END;
    END;




GO


