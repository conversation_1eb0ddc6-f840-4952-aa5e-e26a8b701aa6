SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetPlanLevelNotes]   
--    
-- AUTHOR: <PERSON><PERSON>y 
--    
-- CREATED DATE: 2024-Oct-28    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Regions  
--    
-- PARAMETERS:    
-- Input: 
-- @RegionID
   
-- TABLES:   
--  
 
-- Read:    
-- PrePricing.PlanInfo
-- dbo.SavedMarketInfo
-- PrePricing.PlanNote

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-28		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetPlanLevelNotes]
(
	@RegionID INT 
)
AS    
BEGIN 
    SELECT pinfo.PlanInfoID,pn.PlanNoteID,pn.Note,pn.LastUpdateByID,pn.LastUpdateDateTime
	FROM PrePricing.PlanInfo AS pinfo WITH(NOLOCK)
	JOIN dbo.SavedMarketInfo AS smi WITH(NOLOCK)ON smi.ActuarialMarketID = pinfo.MarketID AND smi.ActuarialRegionID=@RegionID
	JOIN PrePricing.PlanNote AS pn WITH(NOLOCK) ON pn.PlanInfoID=pinfo.PlanInfoID
	ORDER BY pn.LastUpdateDateTime desc
	--GROUP BY pinfo.PlanInfoID,pn.PlanNoteID,pn.Note,pn.LastUpdateByID,pn.LastUpdateDateTime	
END
GO
