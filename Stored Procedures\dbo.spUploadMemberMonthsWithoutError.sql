GO
/****** Object:  StoredProcedure [dbo].[spUploadMemberMonthsWithoutError]    Script Date: 12/3/2022 2:25:55 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUploadMemberMonths_Updated
--
-- CREATOR: <PERSON><PERSON>y
--
-- CREATED DATE: 2021-Jan-19

--
-- DESCRIPTION: Stored Procedure responsible for Membership Upload in the MA Model
--
-- PARAMETERS:
--	Input:
--		@FilePath
--		@ForecastID
--		@ZipCode
--		@UserID
--		@AgedNonDual
--		@ESRDNonDual
--		@HospiceNonDual
--		@ESRDHospiceNonDual
--		@AgedDual
--		@ESRDDual
--		@HospiceDual
--		@ESRDHospiceDual
--      @UserID
--  Output:
--
-- TABLES:
--	Read:
--		LkpIntDemogIndicators
--      LkpIntPlanYear
--		SavedPlanHeader
--		SavedPlanMemberMonthDetail
--	Write:
--		SavedPlanMemberMonthDetail
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--		spCalcPlanAdminBlend
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2021-Jan-19      1			Initial Version														Surya Murthy
-- 2022-Dec-03		2			Added TABLOCKX and logging											Vikrant Rajana Bagal
-- 2023-Sep-05		3			BARC date added														Surya Murthy
-- 2023-Sep-25		4			Rowlocks added for deadlock issues									Surya Murthy
-- 2023-Nov-16		5			Removal of SalesAdjustmentFactor 24.01                              Surya Murthy
-- 2024-Jun-18      6			Removing date time depedency for MM validation						Surya Murthy
-- 2024-Sep-11      7			Sales Membership logic added										Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUploadMemberMonthsWithoutError]
(
	@FilePath VARCHAR(500),
    @ForecastID INT,
    @ZipCode VARCHAR(5),
    @AgedNonDual DECIMAL(28,15) = 0,
    @ESRDNonDual DECIMAL(28,15) = 0,
    @HospiceNonDual DECIMAL(28,15) = 0,
    @AgedDual DECIMAL(28,15) = 0,
    @ESRDDual DECIMAL(28,15) = 0,
    @HospiceDual DECIMAL(28,15) = 0,
    @OOAMemberMonths DECIMAL(28,15) = 0,
	@Updated DATETIME,
    @UserID CHAR(7)
)
AS
BEGIN
    SET NOCOUNT ON;

    --------------------------------------------------------------------------------------------------------------------
    --Declarations------------------------------------------------------------------------------------------------------
    DECLARE @StateID INT,
		    @CountyCode CHAR(3)

    DECLARE @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear()

    DECLARE @LastUpdate DATETIME
    SET @LastUpdate = @Updated

    --Grab State and County code first
    SET @ZipCode= RIGHT('00000'+ LTRIM(@ZipCode),5)
    SET @StateID=CAST(SUBSTRING(@ZipCode,1,2) AS INT)
    SET @CountyCode=SUBSTRING(@ZipCode,3,3)	 
    --------------------------------------------------------------------------------------------------------------------
    --Processing--------------------------------------------------------------------------------------------------------
      BEGIN TRY 
	   BEGIN TRANSACTION  mmtransaction	     
	    BEGIN
		    INSERT INTO dbo.SavedPlanMemberMonthDetail WITH(TABLOCKX)
			 (PlanYearID, ForecastID, StateTerritoryID, 
					CountyCode, DemogIndicator, MemberMonths, FilePath, 
					LastUpdateByID, LastUpdateDateTime)		
		    SELECT
			    @PlanYearID,
			    @ForecastID,
			    @StateID,
			    @CountyCode,
			    t1.DemogIndicator,
			    ISNULL((CASE t1.DemogIndicator
				    WHEN 1 THEN @AgedNonDual
				    WHEN 2 THEN @AgedDual
				    WHEN 4 THEN @HospiceNonDual
				    WHEN 5 THEN @HospiceDual
				    WHEN 7 THEN @ESRDNonDual
				    WHEN 8 THEN @ESRDDual
			    END),0),
				@FilePath,
			    @UserID,
			    @LastUpdate
			FROM dbo.LkpIntDemogIndicators AS t1 WITH(NOLOCK)
            WHERE t1.DualEligibleTypeID <> 2

			IF NOT EXISTS (SELECT 1 FROM dbo.SavedPlanOOAMemberMonthDetail WITH(NOLOCK) WHERE ForecastID = @ForecastID)
				BEGIN

					INSERT INTO dbo.SavedPlanOOAMemberMonthDetail  WITH(TABLOCKX)
								(PlanYearID, 
								ForecastID, 
								MemberMonths, 
								FilePath, 
								LastUpdateByID, 
								LastUpdateDateTime) 					
						  SELECT 
								@PlanYearID,
								@ForecastID,
								MAX(@OOAMemberMonths),
								@FilePath,
								@UserID,
								@LastUpdate
				END
			ELSE
				BEGIN
					DELETE FROM dbo.SavedPlanOOAMemberMonthDetail  WITH (TABLOCKX)  WHERE ForecastID = @ForecastID

					INSERT INTO dbo.SavedPlanOOAMemberMonthDetail    WITH (TABLOCKX) 
						(PlanYearID, 
						 ForecastID, 
						 MemberMonths, 
						 FilePath, 
						 LastUpdateByID, 
						 LastUpdateDateTime)
					SELECT 
						@PlanYearID,
						@ForecastID,
						MAX(@OOAMemberMonths),
						@FilePath,
						@UserID,
						@LastUpdate
				END
	    END
		 BEGIN			  
	    UPDATE dbo.SavedForecastSetup	 WITH (TABLOCKX)
        SET IsToReprice = 1,
            LastUpdateByID = @UserID,
            LastUpdateDateTime = @LastUpdate
        WHERE ForecastID = @ForecastID
		 END

	-- ---------------------------------------------------------------------------------------------
	-- --PlanChangeLog Entry------------------------------------------------------------------------
		INSERT INTO dbo.PlanChangeLog WITH (TABLOCKX) (ForecastID, ProcName, Value, AuditUserID, AuditTime)
		VALUES (@ForecastID, 'Member Months', NULL, @UserID, GETDATE())

		--Sales Membership logic starts here--
		DECLARE @sumMM1 DECIMAL(20,10) = 0
		DECLARE @sumMM2 DECIMAL(20,10) = 0
		SELECT @sumMM1=ROUND(SUM(MemberMonths),10)/12 FROM dbo.SavedPlanMemberMonthDetail WITH(NOLOCK)  WHERE ForecastID= @ForecastID
		SELECT @sumMM2=ROUND(SUM(MemberMonths),10)/12 FROM dbo.SavedPlanOOAMemberMonthDetail WITH(NOLOCK) WHERE ForecastID= @ForecastID		 
		UPDATE dbo.SavedPlanAssumptions WITH(TABLOCKX) SET SalesMembership= @sumMM1+@sumMM2 WHERE ForecastID=@ForecastID
		--Sales Membership logic ends here--

		COMMIT TRANSACTION mmtransaction;
		 END TRY
		   BEGIN CATCH
		    ROLLBACK TRANSACTION mmtransaction; 
		    ---Insert into app log for logging error Start------------------  
			  DECLARE @ErrorMessage NVARCHAR(4000);  
        DECLARE @ErrorSeverity INT;  
        DECLARE @ErrorState INT;  
        DECLARE @ErrorException NVARCHAR(4000);  
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),  
                @currentdate DATETIME = GETDATE(); 

        SELECT @ErrorMessage = ERROR_MESSAGE(),  
               @ErrorSeverity = ERROR_SEVERITY(),  
               @ErrorState = ERROR_STATE(),  
               @ErrorException  
                   = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'  
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);  
        EXEC [dbo].[spAppAddLogEntry] @currentdate,  
                                      '',  
                                      'ERROR',  
                                      @errSrc,  
                                      @ErrorMessage,  
                                      @ErrorException,  
                                      @UserID;  
       ---Insert into app log for logging error End------------------ 

		   END CATCH; 
		     END;
GO
