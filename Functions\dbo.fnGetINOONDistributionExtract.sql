SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetINOONDistributionExtract]  ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetINOONDistributionExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-21
-- HEADER UPDATED: 2011-Dec-21
--
-- DESCRIPTION: Designed to extract fields for IN/OON Distributions - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	
--		SavedPlanINOONDistributionDetail
--		SavedPlanINOONDistributionHeader
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-21		1			Initial Version														Bobby Jaegers
-- 2024-NOV-17      2           Add lastupdateby and lastupdatedate                                 Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetINOONDistributionExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	DistrDescription VarChar(200),
	BenefitCategoryID Int,
	INDistributionPercent Decimal(12,10),
	OONDistributionPercent Decimal(12,10),
	INCostFactor Decimal(12,10),
	OONCostFactor Decimal(12,10),
	LastUpdateByID CHAR(7),
	LastUpdateDateTime DATETIME
    ) AS

BEGIN

	IF @WhereIn IS NULL
		INSERT @Results
			SELECT  dd.ForecastID,
					DistrDescription,
					BenefitCategoryID,
					INDistributionPercent,
					OONDistributionPercent,
					INCostFactor,
					OONCostFactor, 
					dd.LastUpdateByID,
					dd.LastUpdateDateTime
			FROM SavedPlanINOONDistributionDetail dd
			INNER JOIN SavedPlanINOONDistributionHeader dh
				ON dd.ForecastID = dh.ForecastID
			
       
	ELSE
	BEGIN
		INSERT @Results
			SELECT  dd.ForecastID,
					DistrDescription,
					BenefitCategoryID,
					INDistributionPercent,
					OONDistributionPercent,
					INCostFactor,
					OONCostFactor,
					dd.LastUpdateByID,
					dd.LastUpdateDateTime
			FROM SavedPlanINOONDistributionDetail dd
			INNER JOIN SavedPlanINOONDistributionHeader dh
				ON dd.ForecastID = dh.ForecastID
			WHERE dd.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
	  END
RETURN
END
GO
