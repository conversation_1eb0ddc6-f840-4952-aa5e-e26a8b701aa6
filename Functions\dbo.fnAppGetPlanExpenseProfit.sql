SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetPlanExpenseProfit]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetPlanExpenseProfit
--
-- AUTHOR: <PERSON> Lake 
--
-- CREATED DATE: 2008-Apr-21
-- HEADER UPDATED: 2010-Aug-23
--
-- DESCRIPTION: Function responsible for listing expense and profit numbers. 
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--	Output: 
--
-- TABLES:
--	Read:
--		Cal<PERSON><PERSON><PERSON>AdminBlend
--		SavedPlanAssumptions
--		Saved<PERSON>lan<PERSON>eader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		CHANGES MADE						                                    DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-Apr-21		1			Initial Version							                            Brian Lake
-- 2009-Mar-17      2           Data types                                                          Sandy Ellis
-- 2009-Apr01       3           Formatting cleanup													Sandy Ellis
-- 2009-Apr-30		4			Added ExpensePMPM													Keith Galloway
-- 2010-Feb-11		5			Made change to prevent returning NULL values (set to 0, instead)	Casey Sanders
-- 2010-Jul-27		6			Moved to 2012														Joe Casey
-- 2010-Aug-23		7			Removed PlanVersion													Joe Casey
-- 2010-Sep-21      8           Restructured to be based off SavedPlanAssumptions                   Joe Casey
-- 2011-Feb-08		9			Changed where ExpensesPMPM is coming from							Joe Casey
-- 2012-Feb-24		10			Made changes to account for new admin buckets Quality and			Alex Rezmerski
--								TaxesAndFees
-- 2012-Mar-07		11			Set new admin buckets to 0 when null to prevent ExpensePMPM			Alex Rezmerski
--								from pulling through as null in the model			
-- 2012-May-14		12			Included MSB Admin in calculation of Direct Admin					Trevor Mahoney
-- 2012-May-15		13			Added spab.Ishidden = 0 in the Where Clause for ExpensePMPM			Craig Wright
-- 2012-May-16		14			Revised IsHidden statement/left join								Trevor Mahoney
-- 2012-Aug-07		15			Added CAST to MSBAdmin to show ExpensePMPM to 5 decimal places		Alex Rezmerski
-- 2013-Mar-10		16			Added and code for MSB related party								Tim Gao
-- 2013-Mar-15		17			Changed added.MSBAdmin to cast as same as Expense Percent			Mike Deren
-- 2013-Mar-16		18			Added MSBQuality													Tim Gao
-- 2013-Apr-02		19			Commented out IsRelatedParty for Admin and Quality					Trevor Mahoney
-- 2013-Apr-02		20			Deleted HumanaOwnership from Related Party Admin piece				Trevor Mahoney
-- 2015-Mar-19		21			Updated rewards and incentives logic								Mark Freel
-- 2016-Oct-19		22			Added a Cast around Added MSB Quality								Jordan Purdue
-- 2018-Sep-04		23			Replaced PerIntAdminExpenses with new blended calc table			Jordan Purdue
-- 2023-Aug-02		24			Added Internal parameter, WITH (NOLOCK), added Schema				Sheetal Patil 
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetPlanExpenseProfit]
    (
    @ForecastID INT
    )
RETURNS @Results TABLE
    (
    ForecastID INT,
    ExpensePercent DECIMAL(10, 8),
	ExpensePMPM	DECIMAL(18, 12),
    ProfitPercent DECIMAL(10, 8),
    MSBRPProfit DECIMAL(18,12) -- added for Related Party
    )
AS  
BEGIN 

    DECLARE @ExpensePercent FLOAT, @XForecastID INT = @ForecastID
	DECLARE @ExpensePMPM FLOAT
    DECLARE @ProfitPercent FLOAT
    DECLARE @MSBRPProfit FLOAT -- added for MSB for related party

    --Determine if data for this plan exists, if not, default to zeros
    IF EXISTS (SELECT 1 FROM dbo.SavedPlanAssumptions WITH(NOLOCK) WHERE ForecastID = @XForecastID)
        BEGIN
            SET @ExpensePercent = 
                (SELECT ExpensePercent 
                FROM dbo.SavedPlanAssumptions WITH (NOLOCK)
                WHERE ForecastID = @XForecastID)
            SET @ExpensePMPM = 
                (SELECT MAMarketingAdminPMPM + MADirectAdminPMPM + MAIndirectAdminPMPM
				+ ISNULL(ae.MAQualityAdminPMPM,0) + ISNULL(CAST(added.MSBQuality AS DECIMAL(18,10)),0) + ISNULL(ae.MATaxesAndFeesAdminPMPM,0) 
				+ ISNULL(CAST(added.MSBAdmin AS DECIMAL(18,10)),0)--mike
				+ ISNULL(CAST(added.MSBSales AS DECIMAL(18,10)),0) 
                FROM dbo.SavedPlanHeader sph WITH(NOLOCK)
                INNER JOIN dbo.CalcPlanAdminBlend ae WITH(NOLOCK)
					ON ae.ForecastID = sph.ForecastID
					AND ae.PlanYearID = sph.PlanYearID
				LEFT JOIN 
				(SELECT 
					ForecastID, 
					SUM(AddedBenefitQuality) MSBQuality,  --- just added --Tim
					SUM(AddedBenefitAdmin) MSBAdmin,
					SUM(AddedBenefitSales) MSBSales
				 FROM dbo.LkpIntAddedBenefitExpenseDetail added WITH(NOLOCK)
				 INNER JOIN dbo.SavedPlanAddedBenefits spab WITH(NOLOCK)
					ON spab.AddedBenefitTypeID = added.AddedBenefitTypeID
				 WHERE ForecastID = @XForecastID
					AND spab.Ishidden = 0 
					--AND added.IsRelatedParty = 1        --for related party, commented out (Trevor, 04022013)
				 GROUP BY ForecastID) added
			ON added.ForecastID = sph.ForecastID
                WHERE sph.ForecastID = @XForecastID)
            SET @ProfitPercent = 
                (SELECT ProfitPercent 
                FROM dbo.SavedPlanAssumptions WITH(NOLOCK) 
                WHERE ForecastID = @XForecastID)
            SET @MSBRPProfit =                             -- Added to get the MSB profit for related party
				(SELECT MSBProfit
				 FROM
					 (SELECT 
						ForecastID, 
						SUM(AddedBenefitProfit*HumanaOwnership) MSBProfit
					 FROM dbo.LkpIntAddedBenefitExpenseDetail rpadded WITH(NOLOCK)
					 INNER JOIN dbo.SavedPlanAddedBenefits spab WITH(NOLOCK)
						ON spab.AddedBenefitTypeID = rpadded.AddedBenefitTypeID
					 WHERE ForecastID = @XForecastID
						AND spab.Ishidden = 0 AND rpadded.IsRelatedParty = 1
					 GROUP BY ForecastID)msb
				 )
			
        END
    ELSE
        BEGIN
            SET @ExpensePercent = 0
            SET @ExpensePMPM = 0
            SET @ProfitPercent = 0
            SET @MSBRPProfit = 0
        END

    INSERT @Results
    SELECT
        @XForecastID AS ForecastID,
        CAST(ISNULL(@ExpensePercent,0) AS DECIMAL(10,8)) AS ExpensePercent,
		CAST(ISNULL(@ExpensePMPM,0) AS DECIMAL(18, 12)) AS ExpensePMPM,
        CAST(ISNULL(@ProfitPercent,0) AS DECIMAL(10,8)) AS ProfitPercent,
        CAST(ISNULL(@MSBRPProfit,0) AS DECIMAL(18,12)) AS MSBRPProfit
    RETURN
END
GO
