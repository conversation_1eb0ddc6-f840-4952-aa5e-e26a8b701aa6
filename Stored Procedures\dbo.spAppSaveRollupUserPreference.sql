SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- =============================================      
-- Author: <PERSON><PERSON><PERSON>   
-- Create date: 26-03-2018
-- Description:  Save Rollup User Prefrence
--      
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Mar-26    1		  Initial version.											Manisha Tyagi 
-- 2018-Dec-19	  2		  Updated Error message and included logging exception	    Kritika Singh
-- ----------------------------------------------------------------------------------------------------------------------                
CREATE PROCEDURE [dbo].[spAppSaveRollupUserPreference]
    @SelectedRollupList VARCHAR(MAX) = NULL , 
    @LastUpdateByID VARCHAR(7),
	@Result BIT OUT
AS
    BEGIN   
        BEGIN TRANSACTION;
        BEGIN TRY 
			     
            IF EXISTS ( SELECT TOP 1
                                *
                        FROM    dbo.AppSavedRollupUserPreference
                        WHERE   UserID = @LastUpdateByID )
                BEGIN
                    UPDATE  [dbo].[AppSavedRollupUserPreference]
                    SET     SelectedRollupList = @SelectedRollupList ,                           
                            LastUpdateDateTime = GETDATE()
                    WHERE   UserID = @LastUpdateByID;
                END;
            ELSE
                INSERT  INTO [dbo].[AppSavedRollupUserPreference]
                        ( [UserID] ,
                          [SelectedRollupList] ,
                          [LastUpdateDateTime]
                        )
                        SELECT  @LastUpdateByID ,
                                @SelectedRollupList ,
                                GETDATE();
            SET @Result = 1; 
            COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
            SET @Result = 0;
			
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
            ROLLBACK TRANSACTION; 
				---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
        END CATCH;  
    END;
GO
