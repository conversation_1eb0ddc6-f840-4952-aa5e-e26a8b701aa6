SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author: <PERSON>
-- Create date: 2023-Sep-15
-- Description:  Get DualStatus and Age
    
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES:    
-- Read:      dbo.LkpDualStatusAge        
-- Write:      
-- VIEWS:      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION   CHANGES MADE											DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2023-Sep-15		1	     Initial version.										Kiran Kolachalama  
-- 2023-Nov-20		2		 Added NOLOCK											Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
-- ======================================================================================================================           
CREATE PROCEDURE [dbo].[spGetDualStatusAndAge]
AS
BEGIN
    SET NOCOUNT ON;
    SELECT DualStatusAge FROM dbo.LkpDualStatusAge WITH (NOLOCK);
END
GO
