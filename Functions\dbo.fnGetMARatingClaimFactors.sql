SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetMARatingClaimFactors] ******/

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMARatingClaimFactors
--
-- AUTHOR: Joe Casey
--
-- CREATED DATE: 2009-Nov-24
--
-- DESCRIPTION: Function responsible for getting claim factors based on MARatingOptionID
--
-- PARAMETERS:
--	Input:
--      @ForecastID INT,
--      @MARatingOptionID SMALLINT
--
-- RETURNS: Table
--
-- TABLES: 
--	Read:
--		SavedPlanDetail
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Called: fnGetClaimFactor
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION			CHANGES MADE													DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2009-Nov-24		1			Initial Version													Joe Casey
-- 2010-Jun-09		2			Revised for 2012 database										Michael Siekerka
-- 2010-Jun-14      3           Eliminated need for CalcClaimForecastFactors table				Jake Gaecke    
-- 2010-Jun-15      4           Added Benefit level functionality                               Jake Gaecke
-- 2010-Sep-20      5           Removed PlanVersion                                             Jake Gaecke
-- 2010-Sep-23      6           Split out Factors by Exp to Cur (EC2) and Cur to Bid (C2B)      Jake Gaecke
-- 2020-Apr-08		7			Modified variable declarations to allow more digits				Alex Beruscha
-- 2021-Aug-08		8			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's		Franklin Fu
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE  FUNCTION [dbo].[fnGetMARatingClaimFactors]
(
    @ForecastID INT,
    @MARatingOptionID SMALLINT
)
RETURNS @Results TABLE 
(
	ForecastID INT,
	BenefitCategoryID SMALLINT,
    E2CINClaimFactorUnits DECIMAL(24 ,15),
    C2BINClaimFactorUnits DECIMAL(24 ,15),
    INClaimFactorUnits DECIMAL(24 ,15),
    E2CINClaimFactorAllowed  DECIMAL(24 ,15),
    C2BINClaimFactorAllowed  DECIMAL(24 ,15),
    INClaimFactorAllowed  DECIMAL(24 ,15),
    E2COONClaimFactorUnits DECIMAL(24 ,15),
    C2BOONClaimFactorUnits DECIMAL(24 ,15),
    OONClaimFactorUnits DECIMAL(24 ,15),
    E2COONClaimFactorAllowed  DECIMAL(24 ,15),
    C2BOONClaimFactorAllowed  DECIMAL(24 ,15),
    OONClaimFactorAllowed  DECIMAL(24 ,15)
)
AS
BEGIN
	INSERT @Results
	SELECT
		ccf.ForecastID,
		ccf.BenefitCategoryID,
		E2CINClaimFactorUnits = CASE WHEN MIN(E2CClaimFactor) = 0 THEN 0 ELSE 
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 1 AND ccf.TrendTypeID = 2 THEN E2CClaimFactor ELSE 1 END
			,0)))) END
		,C2BINClaimFactorUnits = CASE WHEN MIN(ccf.C2BClaimFactor) = 0 THEN 0 ELSE 
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 1 AND ccf.TrendTypeID = 2 THEN C2BClaimFactor ELSE 1 END
			,0)))) END,
		INClaimFactorUnits = CASE WHEN MIN(ccf.ClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 1 AND ccf.TrendTypeID = 2 THEN ClaimFactor ELSE 1 END
			,0)))) END,
		E2CINClaimFactorAllowed = CASE WHEN MIN(ccf.E2CClaimFactor ) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 1 THEN E2CClaimFactor ELSE 1 END
			,0)))) END,
		C2BINClaimFactorAllowed = CASE WHEN MIN(ccf.C2BClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 1 THEN C2BClaimFactor ELSE 1 END
			,0)))) END,
		INClaimFactorAllowed = CASE WHEN MIN(ccf.ClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 1 THEN ClaimFactor ELSE 1 END
			,0)))) END,
		E2COONClaimFactorUnits = CASE WHEN MIN(ccf.E2CClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 0 AND ccf.TrendTypeID = 2 THEN E2CClaimFactor ELSE 1 END
			,0)))) END,
		C2BOONClaimFactorUnits = CASE WHEN MIN(ccf.C2BClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 0 AND ccf.TrendTypeID = 2 THEN C2BClaimFactor ELSE 1 END
			,0)))) END,
		OONClaimFactorUnits = CASE WHEN MIN(ccf.ClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 0 AND ccf.TrendTypeID = 2 THEN ClaimFactor ELSE 1 END
			,0)))) END,
		E2COONClaimFactorAllowed = CASE WHEN MIN(ccf.E2CClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 0 THEN E2CClaimFactor ELSE 1 END
			,0)))) END,
		C2BOONClaimFactorAllowed = CASE WHEN MIN(ccf.C2BClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 0 THEN C2BClaimFactor ELSE 1 END
			,0)))) END,
		OONClaimFactorAllowed = CASE WHEN MIN(ccf.ClaimFactor) = 0 THEN 0 ELSE
			EXP(SUM(LOG(NULLIF(
				CASE WHEN ccf.IsInNetwork = 0 THEN ClaimFactor ELSE 1 END
			,0)))) END
	FROM (
	    SELECT 
		    spd.ForecastID,
		    spd.MARatingOptionID,
		    spd.ClaimForecastID,
		    net.IsInNetwork,
		    col.ColumnID,
		    map.TrendTypeID,
		    ben.BenefitCategoryID,
		    E2CClaimFactor,
			C2BClaimFactor,
			ClaimFactor -- = E2CClaimFactor * C2BClaimFactor (overall factor)
	    FROM SavedPlanDetail spd
	    INNER JOIN SavedPlanHeader sph
		    ON sph.ForecastID = spd.ForecastID
	    CROSS JOIN 
		    (SELECT DISTINCT ColumnID -- Columns in WS1 
		    FROM LkpExtCMSWorksheet1Mapping) col
		INNER JOIN LkpExtCMSWorksheet1Mapping map
		    ON col.ColumnID = map.ColumnID
	    CROSS JOIN
		    (SELECT 1 AS IsInNetwork
		    UNION 
		    SELECT 0 ) net
	    CROSS JOIN
	        (SELECT BenefitCategoryID
	        FROM LkpIntBenefitCategory
	        WHERE IsEnabled = 1
	        ) ben
	    INNER JOIN fnGetClaimFactors(@ForecastID) cf
            ON cf.ForecastID = spd.ForecastID
            AND cf.ClaimForecastID = spd.ClaimForecastID
            AND cf.ColumnID = col.ColumnID
            AND cf.IsInNetwork = net.IsInNetwork
            AND cf.BenefitCategoryID = ben.BenefitCategoryID
	    WHERE 
		    sph.ForecastID = @ForecastID
		    AND spd.ClaimForecastID IS NOT NULL
	    ) ccf
	INNER JOIN SavedPlanDetail spd
		ON ccf.ForecastID = spd.ForecastID
		AND ccf.ClaimForecastID = spd.ClaimForecastID
		AND ccf.MARatingOptionID = spd.MARatingOptionID
	WHERE ccf.MARatingOptionID = @MARatingOptionID
	GROUP BY
		ccf.ForecastID,
		ccf.BenefitCategoryID
		
RETURN
END
GO
