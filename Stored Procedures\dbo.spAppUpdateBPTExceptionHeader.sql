SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: [dbo].[spAppUpdateBPTExceptionHeader]
--
-- TYPE: SAME
--
-- AUTHOR: Gowri.G
--
-- CREATED DATE: 2011-Jan-20
--
-- DESCRIPTION: Procedure responsible for retrieving credibility details
--				by ForecastID.
--
-- PARAMETERS:
--	Input:
--	    @ForecastID INT,
--	@IsBasePlan BIT,
--	@IsOverride BIT,
--	@Credibility DECIMAL(5,8),
--	0,
--	@<PERSON><PERSON><PERSON> VARCHAR(7),
--	@Error_Flag BIT
-- TABLES: 
--	Read:
--				
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetCredibilityFactor
-- STORED PROCS:
--		
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-20		1			Initial Version														Gowri.G
-- 2019-oct-30		2			Removed 'HUMAD\' to UserID											Chhavi Sinha
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spAppUpdateBPTExceptionHeader]
	@ForecastID INT,
	@IsBasePlan BIT,
	@IsOverride BIT,
	@Credibility DECIMAL(8,6),
	@UserID VARCHAR(7),
	@Error_Flag BIT OUTPUT
AS
BEGIN
	SET @Error_Flag = 0        
  
	BEGIN TRY                                                                    
	                                                     
		BEGIN TRANSACTION A 
		EXEC spHideBPTExceptionHeader @ForecastID
		EXEC spUpdateOrInsertBPTExceptionHeader
		@ForecastID,
		@IsBasePlan,
		@IsOverride,
		@Credibility,
		0,
		@UserID
		
	END TRY     
  
	BEGIN CATCH                                                                        
	                                                                     
	 SET @Error_Flag = 1
	                                                                                                                                                  
	 SELECT error_number()  AS errornumber                                                                       
	 SELECT error_line()  AS errorline                                                                               
	 SELECT error_message()  AS errormessage                                            
	 SELECT error_state()  AS errorstate                                        
	 SELECT error_severity()  AS errorseverity                                                                    
	                                                                             
	                                                                                 
	 if xact_state() <> 0                                                                               
	                                                                                 
		rollback transaction  -- Rolling back all the updations till point of error, if any.   
	  
	END CATCH  
  
	  if xact_state() = 1                                                                    
	 commit transaction  A   

END
GO
