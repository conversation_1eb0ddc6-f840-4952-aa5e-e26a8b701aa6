-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spPlanRefresh
--
-- CREATOR:			Christian Cofie
--
-- CREATED DATE:	2007-APR-05
--
-- DESCRIPTION:		Stored Procedure responsible for re-pricing MA Plans  
--  
--						Return value of 0 indicates success.  
--						If error occurs, code consists of 2 pieces.  
--						1st piece is the actual error code that caused the proc to fail.   
--						2nd piece is the "0 + ith" proc that caused the error.  
--						This is always 2 digits long, with a leading 0 if needed.  
--						List of errors: USE MASTER SELECT * FROM SYSMESSAGES WHERE ERROR = <ERROR>  
--		
-- PARAMETERS:
--  Input  :		@ForecastID
--					@UserID
--					@ValidationMessage
--					@ProcNumber
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			NONE
--
--  Write:			SavedForecastSetup
--					CalcFinalPremium
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS:	
--					spPreRepriceValidation                  (Proc 1)  
--					spCalcBenchmarkSummary                  (Proc 2)  
--					spCalcInducedUtilizationFactors			(Proc 3)  
--					spClaimFactors							(Proc 4)  
--					spCalcProjectionFactors                 (Proc 5)  
--					spCalcBenefitProjection                 (Proc 6)
--					spCalcPlanProjection                    (Proc 7)  
--					spCalcDeductibleFactors					(Proc 8,9)
--					spCalcTieredBenefitAverages             (Proc 10, 11)
--					spCalcEffectiveCoinsurance              (Proc 12,13)  
--					spCalcMOOPFactors                       (Proc 14, 15)  
--					spCalcTotalCostShare                    (Proc 16, 17)  
--					spCalcActEquivByBenefitCategory         (Proc 18, 19)  
--					spCalcMedicareCoveredFinalPricing       (Proc 20) 
--					spCalcFinalPremium                      (Proc 21) 
--					spCalcPlanESRDSubsidy                   (Proc 22)
--					spUpdateOrInsertMAReportPlanLevel       (Proc 23) 
--					Write MbrPrem to CalcFinalPremium		(Proc 24)
--					SET IsToReprice = 0						(Proc 25)
--					spWritePlanRepriceLog
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE															DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-APR-05      1           Initial Version															Christian Cofie  
-- 2008-FEB-27      2           Coding standards														Brian Lake  
-- 2008-APR-30      3           Added spManualBenefitProjection & spBlendedBenefitProjection			Brian Lake  
-- 2008-MAY-04      4           Added a comment to indicate where CalcPlanProjection is done			Sandy Ellis  
-- 2008-MAY-05      5           Added call to spInsertCalcTotalBenefitsByBenefitCategory				Brad Ennis  
-- 2008-AUG-05      6           Added spComputeBenchmarkValues & spComputeBenchmarkFactors				Brian Lake  
-- 2008-SEP-02      7           Added @UserID CHAR(13) to the list of parameters.						Shannon Boykin  
-- 2008-SEP-12      8           Added @UserID to the call of spMedicareCoveredFinalPricing				Shannon Boykin  
-- 2008-SEP-19      9           Added @UserID to the call of spDedMOOPFactors							Shannon Boykin  
-- 2008-SEP-23      10          Added @UserID to the call of spComputeBenchmarkValues,					Brian Lake  
--                                  spComputeBenchmarkFactors, spManualBenefitProjection,  
--                                  spBlendedBenefitProjection, spBenefitProjection,  
--                                  spPreMOOPBenefitPricingSummary, spPlanCountySummary,  
--                                  spActEquivbyBenefitCat, spFinalPremium  
-- 2009-JAN-12      11          Replaced spDedMOOPFactors with spGetDeductibleMOOPFactors				Brian Lake  
--									removed reference to spPlanCountySummary  
-- 2009-FEB-20      12          Revised code to apply coding standards.									Shannon Boykin  
--                                  This included completing the list  
--                                  of 'Executed' stored procs in the  
--                                  header, fixing the header to be  
--                                  more easily readable, and making  
--                                  sure that all keywords were in caps.  
-- 2009-FEB-23      13          Added '09/'10 split for the spComputeBechmark* procs					Brian Lake  
-- 2009-MAR-12      14          Added error handling process											Brian Lake  
-- 2009-MAR-12      15          Added checking for missing benefit categories.							Tonya Cockrell  
-- 2009-MAR-17      16          Plan Year ID changed to SMALLINT for consistency						Sandy Ellis  
-- 2009-MAR-19      17          Swapped order of ForecastID/PlanVersion on most called procs.			Brian Lake  
--                                  Revised error handling to return error code to Access.  
--                                  Added call to spComputeClaimForecastFactors.  
--                                  Removed benefit check, since it exists in Access prior to   
--                                  calling this procedure.  PlanVersion now listed after ForecastID  
-- 2009-MAR-20      18          Added spDeleteCalcTableData to handle Calc table deletes up front		Brian Lake  
-- 2009-MAR-20      19          Added spComputeMOOPAdjustedContinuance to list							Brian Lake  
-- 2009-MAR-26      20          Added a query to get the TrendID for the								Mallika Eyunni  
--                              PlanYear/ForecastID/PlanVersion. Added a call to stored procedure		Mallika Eyunni  
--                              spAddMissingBenefitCategoriesToTrend  
-- 2009-APR-07      21          Added spPreBenefitProjection											Sandy Ellis  
-- 2009-APR-16      22          Removed references to spDeleteCalcTableData, added						Brian Lake  
--                                  spComputeMOOPInitialImpact  
-- 2009-APR-21      23          Added spInsertCalcTotalWeightedBenefitsByBenefitCat,					Brian Lake  
--                                  spActEquivWeightedbyBenefitCat  
-- 2009-MAY-15      24          Added call to spComputePlanCostShareRatio								Brian Lake  
-- 2010-SEP-28      25          Revised for 2012 DB, added @StartPoint to start later in the			Michael Siekerka  
--                                  reprice process  
-- 2010-OCT-06      26          Revised Proc 0 to include new validation procedure changed error		Michael Siekerka  
--                                  output from integer value to descriptive message (reprice  
--                                  log is unchanged)  
-- 2010-OCT-06      27          Added Proc 19 to call spUpdateOrInsertMAReportPlanLevel to keep			Jake Gaecke  
--                                  MAReportPlanLevel populated with the most current data.  
-- 2010-OCT-07      28          Changed settting @ProcNumber = 0 to = @StartPoint, revised coding		Michael Siekerka  
--                                  for error messages  
-- 2010-OCT-12      29          Revised error message to return an integer not a string					Michael Siekerka  
-- 2010-OCT-18      30          Made spPreRepriceValidation run at the beginning even if user			Michael Siekerka  
--                                  skips to different step  
-- 2010-OCT-28      31          Removed ELSE clause in Proc 0 that was causing Proc 1 to be skipped		Michael Siekerka  
-- 2010-NOV-17      32          Added Proc 20, to change IsToReprice flag								Joe Casey  
-- 2010-DEC-08      33          Updated ErrorMessage handling											Michael Siekerka   
-- 2011-APR-24		34			Changed @MaxProc from 18 to 20											Joe Casey  
-- 2011-SEP-13		35			Changed @MaxProc from 20 to 21, adding RepriceValidation				Trevor Mahoney  
-- 2011-OCT-03		36			Updated Header for new proc count										Craig Wright  
-- 2012-NOV-13		37			Added in new proc, spClaimFactors, for updated trend					Mike Deren  
-- 2013-MAR-06		38			Added in new proc, spCalcInducedUtilizationFactors						Mike Deren  
-- 2013-MAR-26		39			Added UserID as parameter for spClaimFactors							Trevor Mahoney  
-- 2014-JAN-09		40			Changing table name														Mike Deren
-- 2014-MAR-04		41			Modified for implementation of SQS										Mike Deren
-- 2015-NOV-30		42			Updated Proc 21 to use SQS indicator 3									Mark Freel
-- 2016-MAR-04		43			Added EGWP indicator. Case statements now on Proc 5-9.					Mark Freel
-- 2017-Sept-11		44			Removed EGWP = 1 items, removed old Procs 21-25 (SQS Trend pathway)		Chris Fleming
--									renumbered procs and total number
--									cleaned up comments and added updates
-- 2018-OCT-11		45			Added in new proc 3, spCalcEffectiveCostShareProjected and incremented	Alex Beruscha
--									proc numbers that followed
-- 2019-JUN-27    46			Removed all instance of table savedplanheader to Forecastsetup table	Ranjana Upadhayay
--									and changed column ForecastID to ForecastId 
-- 2019-OCT-10		47          Reprice error message issue fix							                Pooja Dahiya
-- 2019-OCT-30		48			Replace @UserID from char(13) to char(7)		                        Chhavi Sinha
-- 2019-DEC-16		49          Added Proc 23 spCalcPlanESRDSubsidy and indexed subsequent procedures   Keith Galloway
-- 2019-DEC-19		50			Uncommented code for @IsEGWP											Deepali Mittal
-- 2022-JUN-21		51			Removed @IsEGWP															Aleksandar Dimitrijevic
-- 2022-OCT-10      52          Remove Proc 3  and proc number updated accordingly                      Archana Sahu
-- 2022-NOV-17		53			Commented out code for spCalcPlanESRDSubsidy						    Jake Lewis
-- 2023-JAN-04		54			Remove second and third runs of spCalcBenefitProjection;				Jake Lewis
--									This SP now handles Experience / Manual / Blend in one run. 
-- 2023-JAN-12					Update run order for some cost share SPs as part of the project to 		Jake Lewis
--									isolate the impact of the deductible
-- 2023-JUN-28      55          Updated logging and added RAISERROR bubble up error to .net side        Vikrant Bagal
-- 2023-Nov-01      56			Refreshing the following two columns TotalMemberPremium &  
--									RoundedMemberPrem values in CalcFinalPremium table					Abraham Ndabian
-- 2024-AUG-28		57			Removed spCalcMOOPAdjustedContinuance (combined with 
--								spCalcDeducitbleFactors); Replaced spCalcPreMOOPBenefitPricing 
--								with spCalcTieredBenefitAverages, spCalcEffectiveCoinsurance; Replaced
--								spCalcMOOPInitialImpact with spCalcMOOPFactor; Replaced 
--								spCalcTotalBenefitsByBenefitCategory, spCalcPlanCostShareRatio with 
--								spCalcTotalCostShare); Removed spCalcPreMOOPCostShareSummary;
--								Renumbered Proc's														Franklin Fu
-- 2024-Oct-17      58          Adding user id for spWritePlanRepriceLog SP                              Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spPlanRefresh]
    (
    @ForecastID        INT
   ,@UserID            CHAR(7)
   ,@ValidationMessage VARCHAR(MAX) OUT
   ,@ProcNumber        SMALLINT = 1 --This is an optional parameter for where to start repricing a plan  
)
AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        --Declare Variables
        DECLARE @Debug             BIT
               ,@IsRunable         BIT
               ,@LastDateTime      DATETIME
               ,@ErrMsg            VARCHAR(MAX) = ''
               ,@MaxProc           SMALLINT
               ,@StartLogMessage   VARCHAR(10)  = 'Start'
               ,@SkippedLogMessage VARCHAR(10)  = 'Skipped';

        --Set Variables
        SET @MaxProc = 25; --Update this if any procedures are added or removed.    
        SET @Debug = 1;
        SET @LastDateTime = GETDATE ();

        BEGIN TRY
            --Make sure a valid procedure number is being passed  
            IF @ProcNumber < 1
               OR   @ProcNumber > @MaxProc
                BEGIN
                    RAISERROR ('Invalid Procedure Number.', 16, 1);
                END;
            ELSE
                BEGIN
                    --Validate data no matter where the procedure starts  
                    EXEC dbo.spWritePlanRepriceLog @ForecastID, 0, @StartLogMessage,@UserID;

                    --Proc 1  
                    IF @Debug = 1
                       AND  @ProcNumber = 1
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spPreRepriceValidation @ForecastID, @UserID, @ErrMsg OUTPUT;   --Initial validation, newest proc number  
                            IF @ErrMsg <> 'N/A' --Default value which indicates no errors  
                                BEGIN
                                    RAISERROR (@ErrMsg, 16, 1);
                                END;
                            ELSE IF @ErrMsg = 'N/A' SET @ErrMsg = '';
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 1, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 2  
                    IF @Debug = 1
                       AND  @ProcNumber = 2
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcBenchmarkSummary @ForecastID, @UserID;
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 2, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 3  
                    IF @Debug = 1
                       AND  @ProcNumber = 3
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcInducedUtilizationFactors @ForecastID, @UserID;  --Handles Induced Utilzation Factors  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 3, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 4  
                    IF @Debug = 1
                       AND  @ProcNumber = 4
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spClaimFactors @ForecastID, @UserID;   --Handles updated trend  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 4, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 5  
                    IF @Debug = 1
                       AND  @ProcNumber = 5
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcProjectionFactors @ForecastID, @UserID;  --Handles claim factor product  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 5, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 6  
                    IF @Debug = 1
                       AND  @ProcNumber = 6
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcBenefitProjection @ForecastID, @UserID;
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 6, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 7  
                    IF @Debug = 1
                       AND  @ProcNumber = 7
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcPlanProjection @ForecastID, @UserID; --output CalcPlanProjection  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 7, @SkippedLogMessage,@UserID;
                        END;

                    -- Cost Share Calculations begin. We only want to calculate these tables once.

                    --Proc 8  
                    IF @Debug = 1
                       AND  @ProcNumber = 8
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcDeductibleFactors @ForecastID, 0, @UserID;  --calculate deductible factors for Bid Year
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 8, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 9  
                    IF @Debug = 1
                       AND  @ProcNumber = 9
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcDeductibleFactors @ForecastID, 1, @UserID;  --calculate deductible factors for Current Year
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 9, @SkippedLogMessage,@UserID;
                        END;

					--Proc 10  
                    IF @Debug = 1
                       AND  @ProcNumber = 10
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcTieredBenefitAverages @ForecastID, 0, @UserID;    --calcluates the BenefitValueAvg for Bid Year
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 10, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 11  
                    IF @Debug = 1
                       AND  @ProcNumber = 11
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcTieredBenefitAverages @ForecastID, 1, @UserID;   --calcluates the BenefitValueAvg for Current Year
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 11, @SkippedLogMessage,@UserID;
                        END;


                    --Proc 12  
                    IF @Debug = 1
                       AND  @ProcNumber = 12
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcEffectiveCoinsurance @ForecastID, 0, @UserID;    --calcluates the Effective Coinsurance for Bid Year  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 12, @SkippedLogMessage,@UserID;
                        END;


                    --Proc 13  
                    IF @Debug = 1
                       AND  @ProcNumber = 13
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcEffectiveCoinsurance @ForecastID, 1, @UserID;    --calcluates the Effective Coinsurance for Current Year    
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 13, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 14  
                    IF @Debug = 1
                       AND  @ProcNumber = 14
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcMOOPFactors @ForecastID, 0, @UserID;    --calcluates the MOOP Factors for Bid Year  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 14, @SkippedLogMessage,@UserID;
                        END;


                    --Proc 15  
                    IF @Debug = 1
                       AND  @ProcNumber = 15
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcMOOPFactors @ForecastID, 1, @UserID;		--calcluates the MOOP Factors for Current Year  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 15, @SkippedLogMessage,@UserID;
                        END;


                    --Proc 16  
                    IF @Debug = 1
                       AND  @ProcNumber = 16
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcTotalCostShare @ForecastID, 0, @UserID;    --calculates the Total Cost Share for Bid Year
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 16, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 17  
                    IF @Debug = 1
                       AND  @ProcNumber = 17
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcTotalCostShare @ForecastID, 1, @UserID;    --calculates the Total Cost Share for Current Year
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 17, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 18  
                    IF @Debug = 1
                       AND  @ProcNumber = 18
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcActEquivByBenefitCategory @ForecastID, @UserID, 0;   --This does the unweighted piece  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 18, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 19  
                    IF @Debug = 1
                       AND  @ProcNumber = 19
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcActEquivByBenefitCategory @ForecastID, @UserID, 1;   --This does the weighted piece  
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 19, @SkippedLogMessage,@UserID;
                        END;


                    --Proc 20  
                    IF @Debug = 1
                       AND  @ProcNumber = 20
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcMedicareCoveredFinalPricing @ForecastID, @UserID;
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 20, @SkippedLogMessage,@UserID;
                        END;

                    -- End of Cost Share Calculations.		     
                    -- Finish calculations and update MA Reports and SavedForecastSetup.

                    --Proc 21  
                    IF @Debug = 1
                       AND  @ProcNumber = 21
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spCalcFinalPremium @ForecastID, @UserID;
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, 21, @SkippedLogMessage,@UserID;
                        END;

                    --Proc 22
                    IF @Debug = 1
                       AND  @ProcNumber = 22
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @SkippedLogMessage,@UserID;    --Skipping this step as it isn't used in the BPT
                            --EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage  
                            --EXEC dbo.spCalcPlanESRDSubsidy @ForecastID, @UserID
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                    BEGIN
                        EXEC dbo.spWritePlanRepriceLog @ForecastID, 22, @SkippedLogMessage,@UserID;
                    END
                    --Proc 23  
                    IF @Debug = 1
                       AND  @ProcNumber = 23
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            EXEC dbo.spUpdateOrInsertMAReportPlanLevel @ForecastID,@UserID;
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                    BEGIN
                        EXEC dbo.spWritePlanRepriceLog @ForecastID, 23, @SkippedLogMessage,@UserID;
                    END
                    --Proc 24  -- Adding updating the two new columns in CalcFinalPremium table while refreshing plans. by Abe 11/01/2019
                    IF @Debug = 1
                       AND  @ProcNumber = 24
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            UPDATE  dbo.CalcFinalPremium
                            SET     TotalMemberPremium = (SELECT    TotalMemberPremium FROM fnAppGetRevenueAndPremium (
                                                                                     @ForecastID) )
                            WHERE   CalcFinalPremium.ForecastID = @ForecastID;
                            UPDATE  dbo.CalcFinalPremium
                            SET     RoundedMemberPrem = (SELECT RoundedMemberPrem FROM  fnAppGetRevenueAndPremium (
                                                                                    @ForecastID) )
                            WHERE   CalcFinalPremium.ForecastID = @ForecastID;
                            SET @ProcNumber = @ProcNumber + 1;
                        END;
                    ELSE 
                    BEGIN
                        EXEC dbo.spWritePlanRepriceLog @ForecastID, 24, @SkippedLogMessage,@UserID;
                    END
                    --Proc 25  
                    IF @Debug = 1
                       AND  @ProcNumber = 25
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @StartLogMessage,@UserID;
                            UPDATE  dbo.SavedForecastSetup
                            SET     IsToReprice = 0
                            WHERE   ForecastID = @ForecastID;
                        END;
                    ELSE
                        BEGIN
                            EXEC dbo.spWritePlanRepriceLog @ForecastID
                                                          ,@ProcNumber
                                                          ,'Procedure number provided was higher than number of procedures'
														  ,@UserID;
                            SET @ErrMsg = 'Procedure number provided was higher than number of procedures';
                        END;

                END;
            SET @ValidationMessage = @ErrMsg;
        END TRY

        BEGIN CATCH
            -- In the event of an error, we return the error details to the client  
            IF @Debug = 1
                BEGIN
                    EXEC dbo.spWritePlanRepriceLog @ForecastID, @ProcNumber, @ValidationMessage,@UserID;
                END;

            DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE ();
            DECLARE @ErrorSeverity INT = ERROR_SEVERITY ();
            DECLARE @ErrorState INT = ERROR_STATE ();
            DECLARE @ErrorProcedure VARCHAR(100) = ERROR_PROCEDURE ();

            SET @ErrorMessage = N'spPlanRefresh:Error in SP ' + @ErrorProcedure + N'-' + @ErrorMessage;
            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);

        END CATCH;

    END;
--GRANT EXECUTE ON  [dbo].[spPlanRefresh] TO [MAPDModel]
GO
