SET ANSI_NULLS ON;
GO
SET QUOTED_IDENTIFIER ON;
GO
-- =============================================
-- Author:  <PERSON>
-- Create date: 2025-MAY-13
-- Description: Run prior to a DF Sync (DataFoundationSyncForecastingcu) to determine if the data in MAAModels.LkpIntFusionLine
--				matches DF.LkpIntFusionLine. If the data doesn't match, then the user is prevented from running the sync and
--				shown a message in MAAUI saying the data needs to be fixed manually. It's important that the data matches since
--				the fusion line numbers are cross-referenced as part of the insertion into SavedDFClaims in the
--				DataFoundationSyncForecastingcu SSIS package.
--
--
-- PARAMETERS:
-- Input:           NONE
--
-- TABLES:
-- Read:			MAAModels.LkpIntFusionLine
--					DF.LkpIntFusionLine
--
-- Write:           NONE
--
-- Views:           NONE
--
-- Functions:       NONE
--
-- Stored Procs:    NONE
--
-- $HISTORY
--
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		CHANGES MADE                                                DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2025-MAY-13  1			Initial version.											Alex Brandt
-- ----------------------------------------------------------------------------------------------------------------------
-- =============================================
CREATE PROCEDURE [dbo].[spAppValidateFusionLineTables]
AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON;

    WITH table1
    AS (SELECT FusionLineNumber,
               FusionLineDetail,
               FusionLineName,
               Category,
               DFSource,
               IsUnits,
               IsAdmits,
               IsPaid,
               IsCostShare,
               IsAllowed,
               IsPaidNonSQS,
               IsAllowedNonSQS,
               IsAllowedWS1WRPP,
               IsIncludeInCostShareBasis,
               IsOPB,
               IsFFSAllowed,
               IsCap,
               IsCPaid,
               IsCMbrCS,
               IsEPaid,
               IsEMbrCS,
               IsDEPaid,
               IsDEMbrCS,
               IsPRA,
               Trend_IsAllowed,
               Trend_IsNet,
               Trend_IsUnitCnt,
               Trend_IsAdmitCnt,
               IsSurplusDeficit
        FROM DF.dbo.LkpIntFusionLine),
         table2
    AS (SELECT FusionLineNumber,
               FusionLineDetail,
               FusionLineName,
               Category,
               DFSource,
               IsUnits,
               IsAdmits,
               IsPaid,
               IsCostShare,
               IsAllowed,
               IsPaidNonSQS,
               IsAllowedNonSQS,
               IsAllowedWS1WRPP,
               IsIncludeInCostShareBasis,
               IsOPB,
               IsFFSAllowed,
               IsCap,
               IsCPaid,
               IsCMbrCS,
               IsEPaid,
               IsEMbrCS,
               IsDEPaid,
               IsDEMbrCS,
               IsPRA,
               Trend_IsAllowed,
               Trend_IsNet,
               Trend_IsUnitCnt,
               Trend_IsAdmitCnt,
               IsSurplusDeficit
        FROM dbo.LkpIntFusionLine)
    SELECT CASE
               WHEN
               (
                   SELECT COUNT(*) FROM table1
               ) =
               (
                   SELECT COUNT(*) FROM table2
               )
               AND NOT EXISTS
                       (
                           SELECT * FROM table1 EXCEPT SELECT * FROM table2
                       ) THEN
                   'true'
               ELSE
                   'false'
           END AS result;
END;
GO
