SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- --------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcInducedUtilizationFactors
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2012-Mar-05
-- MAJOR OVERHAUL: 2017-Aug-22, <PERSON> Fleming
--
-- DESCRIPTION: Stored procedure responsible for updating the CalcInducedUtilizationFactors table. This procedure is now automated and pulls in 
--					C&U cuts and unit blends them by benefit category to calculate the IU factors
--
-- PARAMETERS:
--    Input: 
--			@ForecastID
--			@UserID
--
--    Output: NONE
--
-- RETURNS: 
--
-- TABLES:
--    Read:
--			CalcEffectiveCostShareBase
--			Benefits_SavedBenefitOptionDetail
--			LkpInducedUtilFactors
--			LkpIntBenefitCategory
--			LkpIntCostShareDampening
--			LkpIntInducedUtilBenefitMapping
--			LkpProductType
--			SavedDFClaims
--			SavedForecastSetup
--			SavedMarketInfo
--			SavedPlanDFSummary
--			SavedPlanInfo
--			SavedRegionInfo
--
--    Write: 
--			CalcInducedUtilizationFactors
--
-- VIEWS:
--
-- STORED PROCS:
--
-- FUNCTIONS:
--			fnGetMemberMonthsAndAllowedByDuals
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- DATE              VERSION    CHANGES MADE                                                                            DEVELOPER
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- 2013-Mar-05       1          Initial Version                                                                         Mike Deren
-- 2013-Mar-12       2          Set Induced Utilization Factor to 1 if plan did not exist in 2012, & exp. only          Mike Deren
-- 2013-Mar-18       3          Removed Manual portion since we always want it 1                                        Mike Deren
-- 2013-Mar-22       4          Corrected inequalities for Induced Utilization factors                                  Trevor Mahoney
-- 2013-Mar-27       5          Changed join on savedplanbenefitdetailIU to contract pbp                                Mike Deren
-- 2013-Apr-05       6          Changed join to fix mapping of IP                                                       Mike Deren
-- 2013-Oct-04       7          Included Join on Segment ID                                                             Manisha Tyagi
-- 2014-Jan-17       8          Changing table name within query                                                        Mike Deren 
-- 2014-Feb-27       9          SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals                            Mike Deren
-- 2014-Mar-05       10         Added in isliveindexes hidden filters for arcsavedplanheader                            Mike Deren
-- 2014-Mar-21       11         Added case statement to handle membership for fully manual plans                        Nick Koesters 
-- 2014-Nov-17       12         Cleaned up coding by changing "Post" to "Base"	and adding variable Segment ID to       Jordan Purdue
--                                line 105 and added IP Rehab (131,132) to Case statements										
-- 2014-Nov-25       13         Updated the value that the declares whether the plan existed two years ago              Jordan Purdue
--                                and updated the joins to look at the new column IUBasePlan on SavedPlanHeader
-- 2016-Sep-19       14         Removed IP case logic and replaced with InducedID                                       Chris Fleming
-- 2017-Feb-07       15         Copied and modified experience logic to calculate Manual IU Factors                     Jordan Purdue
-- 2017-Apr-14       16         Modifying Manual calculations for Manual Membership                                     Jordan Purdue
-- ============================================================================================================================================
-- ===========         Procedure was essentially rewrote in Aug 2017 to automate IU so the above comments may no longer apply       ===========
-- ============================================================================================================================================
-- 2017-Aug-22       17         Revamped logic - now this is an automated calc and works by pulling in base C&U         Chris Fleming
--                                data and then unit weighting by benefit category to get base IU factors for man/exp	
-- 2017-Sep-13       18         Updated parameters for fnGetMemberMonthsAndAllowedByDuals                               Chris Fleming
--                                updated Saved*CUDetail units to pull from SavedCUBaseClaims
-- 2018-Mar-12       19         Changed units in calc to pull directly from SavedCUBaseClaims                           Chris Fleming
-- 2018-Oct-11       20         Now calculates IU with Effective Cost Share values and Cost Share Dampening             Alex Beruscha
-- 2019-Jun-28       21         Replace SavedPlanHeader with SavedForecastSetup                                         Pooja Dahiya
-- 2019-Jul-05       22         Made changes in region table.                                                           Satyam Singhal
--2019-oct-25		23			Removed Humad from UserID																Chhavi Sinha
--2019-Nov-06		24		    Replaced SavedForecastSetup with SavedPlanHeader										Chhavi Sinha
--2020-Aug-19		25			Increased Decimals in Temporary Tables, Cast ecs.Units as variable type float
--                              in IUbase subquery. Also cast Effective Cost Share as variable type float.              Phillip Leigh
--2020-Sep-15		26			Changes done as per Phillip																Deepali
--2020-Oct-08		27			Added a Round in Insert Into dbo.CalcInducedUtilizationFactors Subquery 
--							   for [InducedUtilizationFactorIN] and [InducedUtilizationFactorOut] 					Phillip		
--2020-Oct-09		28			Added a Round in Insert Into dbo.CalcInducedUtilizationFactors Subquery 
--							   for [InducedUtilizationFactorIN] and [InducedUtilizationFactorOut] 					Kiran Pant						
--2020-Dec-05		29			Adding  temp table and row lock to table to improve db performance						Surya Murthy
--2020-Oct-22       30         Group By Fixes                                                                          Keith Galloway
--2022-May-22		31			MAAUI migration; splitting #ProjEffCS to IN & OON to eliminate NULL values; 
--								removed nested queries; removed ORDER BY clauses; removed DISTINCT clause in SELECT 
--								statement																				Aleksandar Dimitrijevic
--2022-Oct-14       32         Added internal variables for parameters                                                  Khurram Minhas
--                             Added WITH (NOLOCK)
--                             Drop temp tables 
--2022-Nov-21       33         Added temp table #CalcInducedUtilizationFactors1, Release temp table memory				Phani Adduri
--2023-Mar-17       34         Updated Benefits_SavedBenefitOptionDetail  join added AND  CECS.BenefitOptionID = SFS.BenefitOptionID  Vikrant Bagal
--2023-Apr-27		35		   HOTFIX to bring manual piece back into the calcualation (INC2315852)						Aleksandar Dimitrijevic
--2024-Mar-11		36		   Add Muting factor to IU Calculation based on LkpModelSettings value						Michael Manes
--2024-May-10		37		   IU factor enable disable logic added														Surya Murthy
--2025-Apr-11		38		   Add error handling for base year effective cost share on day range benefits. If the		Stephen Stempky
--							   IN benefit is a day range and OON is not there are duplicate rows for OON which cause
--							   the base OON IU value to be incorrect
--2025-Apr-23		39		   Added same fix as 38 for the manual side	(38 was just experience)						Stephen Stempky
-- --------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcInducedUtilizationFactors]
(
    @ForecastID INT,
    @UserID CHAR(7)
)
AS
SET NOCOUNT ON;

-- --------------------------------------------------------------------------------------------------------------------------------------------
DECLARE @LastDateTime DATETIME,
        @PlanYearID SMALLINT,
		@exist INT,
		@existmanual INT,
		@NonDualBiddableMemberMonthsExp DECIMAL(24,15),
		@DualBiddableMemberMonthsExp DECIMAL(24,15),
		@TotalBiddableMemberMonthsExp DECIMAL(24,15),
		@NonDualBiddableMemberMonthsMan DECIMAL(24,15),
		@DualBiddableMemberMonthsMan DECIMAL(24,15),
		@TotalBiddableMemberMonthsMan DECIMAL(24,15),
		@SkipIU TINYINT, --Turned on/off in UI pending IT enhancement
		@Mute_Pct DECIMAL(5,4)
		

		DECLARE @XForecastID INT = @ForecastID, 
	@XUserID CHAR(7) = @UserID
	
	


SELECT @PlanYearID = SPI.PlanYear
FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)
INNER JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
	ON SFS.PlanInfoID = SPI.PlanInfoID
WHERE SFS.ForecastID = @XForecastID

SET		@LastDateTime = GETDATE()

SELECT	@NonDualBiddableMemberMonthsExp = NonDualBiddableMemberMonths,
		@DualBiddableMemberMonthsExp= DualBiddableMemberMonths
FROM	dbo.fnGetMemberMonthsAndAllowedByDuals(@XForecastID, 1) 

SELECT	@NonDualBiddableMemberMonthsMan = NonDualBiddableMemberMonths,
		@DualBiddableMemberMonthsMan = DualBiddableMemberMonths
FROM	dbo.fnGetMemberMonthsAndAllowedByDuals(@XForecastID, 2) 

SET		@TotalBiddableMemberMonthsExp = (@NonDualBiddableMemberMonthsExp + @DualBiddableMemberMonthsExp)  
SET		@TotalBiddableMemberMonthsMan = (@NonDualBiddableMemberMonthsMan + @DualBiddableMemberMonthsMan)  
SET		@SkipIU = (SELECT IsSkippedInducedUtilization FROM dbo.SavedForecastSetup WITH (NOLOCK) WHERE ForecastID = @XForecastID)

SET @Mute_Pct = (SELECT IUMutePct FROM dbo.LkpModelSettings WITH (NOLOCK));

 --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Cost Share Dampening factors by Benefit Category
IF OBJECT_ID('tempdb.dbo.#CSDampening') IS NOT NULL   
DROP TABLE #CSDampening
CREATE TABLE #CSDampening
	(
		BenefitCategoryID INT,
		Factor DECIMAL (24,14)
    );

INSERT INTO #CSDampening
SELECT	csd.BenefitCategoryID ,
		csd.Factor
FROM	dbo.LkpIntCostShareDampening csd WITH (NOLOCK)
WHERE	csd.HumanaRegionID IN 
		(
		SELECT SRI.ActuarialRegionID
		FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)
		INNER JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
			ON SFS.PlanInfoID = SPI.PlanInfoID
		INNER JOIN dbo.SavedMarketInfo SMI WITH (NOLOCK)
			ON SPI.ActuarialMarketID = SMI.ActuarialMarketID
		INNER JOIN dbo.SavedRegionInfo SRI WITH (NOLOCK)
			ON SMI.ActuarialRegionID = SRI.ActuarialRegionID
		WHERE SFS.ForecastID = @XForecastID
		) 
		AND csd.PlanTypeID IN 
		(
		SELECT	SPI.ProductTypeID PlanTypeID
		FROM	dbo.SavedForecastSetup SFS WITH (NOLOCK)
		INNER JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
			ON SFS.PlanInfoID = SPI.PlanInfoID
		INNER JOIN dbo.LkpProductType LPT WITH (NOLOCK)
			ON LPT.ProductTypeID = SPI.ProductTypeID
		WHERE	SFS.ForecastID = @XForecastID
		) 
-- --------------------------------------------------------------------------------------------------------------------------------------------
--*********************************************************************************************************************************************
--************************************************************ EXPERIENCE *********************************************************************
--*********************************************************************************************************************************************
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Base OON IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#OONbaseIU') IS NOT NULL   
DROP TABLE #OONbaseIU
CREATE TABLE #OONbaseIU
	(
		ForecastID INT,
		MARatingOptionID TINYINT,
		PlanInfoID INT,
		CPS CHAR(13),
		Units DECIMAL(24,14),
		BenefitCategoryID SMALLINT,
		OONBenefitTypeID TINYINT,
		OONEffectiveCostShare DECIMAL(24,14),
		MinIUValue DECIMAL(8, 6),
		MinCoinOrCopay DECIMAL(16, 8),
		MaxIUValue DECIMAL(8, 6),
		MaxCoinOrCopay DECIMAL(16, 8),
		InterpolateIUValue DECIMAL(16, 8)
    );

--Temp Table - Base IN IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#INbaseIU') IS NOT NULL   
DROP TABLE #INbaseIU
CREATE TABLE #INbaseIU
	(
		ForecastID INT,
		MARatingOptionID TINYINT,
		PlanInfoID INT,
		CPS CHAR(13),
		Units DECIMAL(24,14),
		BenefitCategoryID SMALLINT,
		INBenefitTypeID TINYINT,
		INEffectiveCostShare DECIMAL(24,14),
		MinIUValue DECIMAL(8, 6),
		MinCoinOrCopay DECIMAL(16, 8),
		MaxIUValue DECIMAL(8, 6),
		MaxCoinOrCopay DECIMAL(16, 8),
		InterpolateIUValue DECIMAL(16, 8)
    );

--Temp Table - Base Experience OON IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseExpOON') IS NOT NULL   
DROP TABLE #BaseExpOON
CREATE TABLE #BaseExpOON
	(
		BenefitCategoryID SMALLINT,
		IUValue DECIMAL (18,14),
		Units DECIMAL(24,14)
    );
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Base Experience IN IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseExpIN') IS NOT NULL   
DROP TABLE #BaseExpIN
CREATE TABLE #BaseExpIN
	(
		BenefitCategoryID SMALLINT,
		IUValue DECIMAL (18,14),
		Units DECIMAL(24,14)
    );
	-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Base Experience OON Effective Cost Share by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseExpOONEffCS') IS NOT NULL   
DROP TABLE #BaseExpOONEffCS
CREATE TABLE #BaseExpOONEffCS
	(
		ForecastID INT ,
		MARatingOptionID TINYINT ,
		PlanInfoID INT ,
		CPS CHAR(13) ,
		Units DECIMAL(24,14) ,
		InducedID INT ,
		BenefitCategoryID INT ,
		OONBenefitTypeID TINYINT ,
		OONEffectiveCostShare DECIMAL(24,14),
		CSDampeningFactor DECIMAL(24,14)
    );
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Base Experience IN Effective Cost Share by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseExpINEffCS') IS NOT NULL   
DROP TABLE #BaseExpINEffCS
CREATE TABLE #BaseExpINEffCS
	(
		ForecastID INT ,
		MARatingOptionID TINYINT ,
		PlanInfoID INT ,
		CPS CHAR(13) ,
		Units DECIMAL(24,14) ,
		InducedID INT ,
		BenefitCategoryID INT ,
		INBenefitTypeID TINYINT ,
		INEffectiveCostShare DECIMAL(24,14),
		CSDampeningFactor DECIMAL(24,14)
    );	
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Projected Experience Effective Cost Share by Benefit Category
IF OBJECT_ID('tempdb.dbo.#ProjEffCSIN') IS NOT NULL   
DROP TABLE #ProjEffCSIN
CREATE TABLE #ProjEffCSIN
	(
		ForecastID INT ,
		BenefitCategoryID INT ,
		BenefitCategoryName VARCHAR(50)  ,
		InducedID INT ,
		TypeofService VARCHAR(50) ,
		INBenefitTypeID TINYINT ,
		INEffectiveCostShare DECIMAL(24,14)
    );	

IF OBJECT_ID('tempdb.dbo.#ProjEffCSOON') IS NOT NULL   
DROP TABLE #ProjEffCSOON
CREATE TABLE #ProjEffCSOON
	(
		ForecastID INT ,
		BenefitCategoryID INT ,
		BenefitCategoryName VARCHAR(50)  ,
		InducedID INT ,
		TypeofService VARCHAR(50) ,
		OONBenefitTypeID TINYINT ,
		OONEffectiveCostShare DECIMAL(24,14)
    );	

IF OBJECT_ID('tempdb.dbo.#ProjEffCS') IS NOT NULL   
DROP TABLE #ProjEffCS
CREATE TABLE #ProjEffCS
	(
		ForecastID INT ,
		BenefitCategoryID INT ,
		BenefitCategoryName VARCHAR(50)  ,
		InducedID INT ,
		TypeofService VARCHAR(50) ,
		INBenefitTypeID TINYINT ,
		INEffectiveCostShare DECIMAL(24,14) ,
		OONBenefitTypeID TINYINT ,
		OONEffectiveCostShare DECIMAL(24,14)
    );	
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- If CostAndUse cuts are selected we can calculate the IU factors, if not set them to 1 below
SET @exist = ( 
			   SELECT	COUNT(DFS.PlanInfoID)
			   FROM		dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
			   WHERE	DFS.ForecastID = @XForecastID
						AND DFS.MARatingOptionID = 1 --experience
			   GROUP BY DFS.ForecastID
             );
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Calc & Insert Base Experience OON Unit-Weighted IU Values
IF (ISNULL(@exist, 0) = 0 OR @SkipIU = 1)
BEGIN
	INSERT INTO #BaseExpOON
	SELECT	BenefitCategoryID,
			IUValue =	1.0,
			Units = 0
	FROM dbo.LkpIntBenefitCategory WITH (NOLOCK)
	WHERE IsUsed = 1
END
ELSE
BEGIN
	INSERT INTO #BaseExpOONEffCS
	SELECT	DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,
			BaseSPI.CPS,
			Units = DFC.UnitCnt
					+ DFC.EncounterUnitCnt
					+ DFC.DelegatedEncounterUnitCnt
					+ DFC.AdditiveAdjUnits
					+ DFC.ModelOfCareAdjUnits
					+ DFC.UCUnitsAdj
					+ DFC.MSBUnits
					+ DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.OONBenefitTypeID,
			OOEffectiveCostShare = SUM (cast  ( CECS.OONEffectiveCostShare as float)) * csd.Factor ,
			CSD.Factor
	FROM	dbo.SavedForecastSetup SFS WITH (NOLOCK)
	INNER JOIN dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
		ON SFS.ForecastID = DFS.ForecastID
	LEFT JOIN dbo.SavedPlanInfo BaseSPI WITH (NOLOCK)
		ON BaseSPI.PlanInfoID = DFS.PlanInfoID	  		
	INNER JOIN dbo.CalcEffectiveCostShareBase CECS WITH (NOLOCK)
		ON CECS.PlanYearID = BaseSPI.PlanYear
		AND CECS.[Contract-PBP] = BaseSPI.CPS 
	INNER JOIN dbo.SavedDFClaims DFC WITH (NOLOCK)
		ON	DFC.PlanInfoID = DFS.PlanInfoID
		AND DFC.DFVersionID = SFS.DFVersionID
		AND DFC.BenefitCategoryID = CECS.BenefitCategoryID
		AND DFC.DemogIndicator IN (1,2)
	INNER JOIN dbo.LkpIntInducedUtilBenefitMapping LBM WITH (NOLOCK)
		ON	LBM.BenefitCategoryID = CECS.BenefitCategoryID		
	INNER JOIN #CSDampening CSD
		ON CSD.BenefitCategoryID = LBM.BenefitCategoryID
	WHERE	DFS.ForecastID = @XForecastID
			AND DFS.MARatingOptionID = 1
			AND DFC.DemogIndicator IN (1,2)
			AND CECS.OONBenefitTypeID IS NOT NULL
	GROUP BY  
			DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,							
			BaseSPI.CPS,
			DFC.UnitCnt,
			DFC.EncounterUnitCnt,
			DFC.DelegatedEncounterUnitCnt,
			DFC.AdditiveAdjUnits,
			DFC.ModelOfCareAdjUnits,
			DFC.UCUnitsAdj,
			DFC.MSBUnits,
			DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.OONBenefitTypeID,
			CSD.Factor

-----------------------------------------------------------------------------------------------------------------------------------------------
		--OON baseIU
		INSERT INTO #OONbaseIU
		SELECT	ecs.ForecastID,
		ecs.MARatingOptionID,
		ecs.PlanInfoID,
		ecs.CPS,
		Units = CAST(ecs.Units AS FLOAT),
		ecs.BenefitCategoryID,
		ecs.OONBenefitTypeID,
		ecs.OONEffectiveCostShare ,
		MinIUValue = MIN(lufmin.Value),
		MinCoinOrCopay = MAX(lufmin.CoinOrCopayValue),
		MaxIUValue = MAX(lufmax.Value),
		MaxCoinOrCopay = MIN(lufmax.CoinOrCopayValue),
		InterpolateIUValue =	CASE WHEN MAX(lufmin.CoinOrCopayValue) = MIN(lufmax.CoinOrCopayValue) 
									 THEN ISNULL(MIN(lufmin.Value), 1)
									 ELSE ISNULL((((MIN(lufmax.CoinOrCopayValue) - CAST(ecs.OONEffectiveCostShare AS FLOAT)) * MIN(lufmin.Value))
												  + ((CAST(ecs.OONEffectiveCostShare AS FLOAT) - MAX(lufmin.CoinOrCopayValue)) * MAX(lufmax.Value)))
											     / (MIN(lufmax.CoinOrCopayValue) - MAX(lufmin.CoinOrCopayValue)), 1)				
		
								END
		FROM	#BaseExpOONEffCS ecs		
		LEFT JOIN dbo.LkpInducedUtilFactors lufmin WITH (NOLOCK)
		ON	lufmin.BenefitTypeID = ecs.OONBenefitTypeID
			AND lufmin.ColumnID = ecs.InducedID
			AND ecs.OONEffectiveCostShare >= lufmin.CoinOrCopayValue
		LEFT JOIN dbo.LkpInducedUtilFactors lufmax WITH (NOLOCK)
		ON	lufmax.BenefitTypeID = ecs.OONBenefitTypeID
			AND lufmax.ColumnID = ecs.InducedID
			AND ecs.OONEffectiveCostShare <= lufmax.CoinOrCopayValue
		GROUP BY  
				ecs.ForecastID,
				ecs.MARatingOptionID,
				ecs.PlanInfoID,							
				ecs.CPS,
				ecs.Units,
				ecs.InducedID,
				ecs.BenefitCategoryID,
				ecs.OONBenefitTypeID,
				ecs.OONEffectiveCostShare

	IF OBJECT_ID('tempdb.dbo.#BaseExpOONEffCS') IS NOT NULL   
    DROP TABLE #BaseExpOONEffCS

	INSERT INTO #BaseExpOON
	SELECT	baseIU.BenefitCategoryID,
			IUValue =	CASE WHEN SUM(baseIU.Units) = 0 THEN 1.0
							 ELSE ROUND(CAST(SUM(baseIU.Units * baseIU.InterpolateIUValue) AS FLOAT) / CAST(SUM(baseIU.Units) AS FLOAT), 14)
						END,
			Units = SUM(CAST(baseIU.Units AS FLOAT))
	FROM	#OONbaseIU baseIU
	GROUP BY baseIU.BenefitCategoryID

END
-----------------------------------------------------------------------------------------------------------------------------------------------
--Calc & Insert Experience IN IU Values
IF (ISNULL(@exist, 0) = 0 OR @SkipIU = 1)
BEGIN
	INSERT INTO #BaseExpIN
	SELECT	BenefitCategoryID,
			IUValue =	1.0,
			Units = 0
	FROM dbo.LkpIntBenefitCategory WITH (NOLOCK)
	WHERE IsUsed = 1
END
ELSE
BEGIN
	INSERT INTO #BaseExpINEffCS 
	SELECT	DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,
			BaseSPI.CPS,
			Units = DFC.UnitCnt
					+ DFC.EncounterUnitCnt
					+ DFC.DelegatedEncounterUnitCnt
					+ DFC.AdditiveAdjUnits
					+ DFC.ModelOfCareAdjUnits
					+ DFC.UCUnitsAdj
					+ DFC.MSBUnits
					+ DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.INBenefitTypeID,
			INEffectiveCostShare = SUM(CAST(CECS.INEffectiveCostShare AS FLOAT))*CSD.Factor,
			CSD.Factor
	FROM	dbo.SavedForecastSetup SFS WITH (NOLOCK)
	INNER JOIN dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
		ON SFS.ForecastID = DFS.ForecastID
	LEFT JOIN dbo.SavedPlanInfo BaseSPI WITH (NOLOCK)
		ON BaseSPI.PlanInfoID = DFS.PlanInfoID
	INNER JOIN dbo.CalcEffectiveCostShareBase CECS WITH (NOLOCK)
		ON CECS.PlanYearID = BaseSPI.PlanYear
		AND CECS.[Contract-PBP] = BaseSPI.CPS
	INNER JOIN dbo.SavedDFClaims DFC WITH (NOLOCK)
		ON	DFC.PlanInfoID = DFS.PlanInfoID
		AND DFC.DFVersionID = SFS.DFVersionID
		AND DFC.BenefitCategoryID = CECS.BenefitCategoryID
		AND DFC.DemogIndicator IN (1,2)
	INNER JOIN dbo.LkpIntInducedUtilBenefitMapping LBM WITH (NOLOCK)
		ON	LBM.BenefitCategoryID = CECS.BenefitCategoryID		
	INNER JOIN #CSDampening CSD 
		ON CSD.BenefitCategoryID = LBM.BenefitCategoryID
	WHERE	DFS.ForecastID = @XForecastID
			AND DFS.MARatingOptionID = 1
			AND DFC.DemogIndicator IN (1,2)
			AND CECS.INBenefitTypeID IS NOT NULL
	GROUP BY  
			DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,							
			BaseSPI.CPS,
			DFC.UnitCnt,
			DFC.EncounterUnitCnt,
			DFC.DelegatedEncounterUnitCnt,
			DFC.AdditiveAdjUnits,
			DFC.ModelOfCareAdjUnits,
			DFC.UCUnitsAdj,
			DFC.MSBUnits,
			DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.INBenefitTypeID,
			CSD.Factor

----------------------------------------------------------------------------------------------------------------
		--IN baseIU
		INSERT INTO #INbaseIU
		SELECT	ecs.ForecastID,
			ecs.MARatingOptionID,
			ecs.PlanInfoID,
			ecs.CPS,
			Units = CAST(ecs.Units AS FLOAT),
			ecs.BenefitCategoryID,
			ecs.INBenefitTypeID,
			ecs.INEffectiveCostShare,
			MinIUValue = MIN(lufmin.Value),
			MinCoinOrCopay = MAX(lufmin.CoinOrCopayValue),
			MaxIUValue = MAX(lufmax.Value),
			MaxCoinOrCopay = MIN(lufmax.CoinOrCopayValue),
			InterpolateIUValue =	CASE WHEN MAX(lufmin.CoinOrCopayValue) = MIN(lufmax.CoinOrCopayValue) 
										 THEN ISNULL(MIN(lufmin.Value), 1)
										 ELSE ISNULL((((MIN(lufmax.CoinOrCopayValue) - CAST(ecs.INEffectiveCostShare AS FLOAT)) * MIN(lufmin.Value))
										              + ((CAST(ecs.INEffectiveCostShare AS FLOAT) - MAX(lufmin.CoinOrCopayValue)) * MAX(lufmax.Value)))
													 / (MIN(lufmax.CoinOrCopayValue) - MAX(lufmin.CoinOrCopayValue)), 1)
									END
			FROM #BaseExpINEffCS ecs
			LEFT JOIN dbo.LkpInducedUtilFactors lufmin WITH (NOLOCK)
				ON	lufmin.BenefitTypeID = ecs.INBenefitTypeID
				AND lufmin.ColumnID = ecs.InducedID
				AND ecs.INEffectiveCostShare >= lufmin.CoinOrCopayValue
			LEFT JOIN dbo.LkpInducedUtilFactors lufmax WITH (NOLOCK)
				ON	lufmax.BenefitTypeID = ecs.INBenefitTypeID
				AND lufmax.ColumnID = ecs.InducedID
				AND ecs.INEffectiveCostShare <= lufmax.CoinOrCopayValue
			GROUP BY  
				ecs.ForecastID,
				ecs.MARatingOptionID,
				ecs.PlanInfoID,							
				ecs.CPS,
				ecs.Units,
				ecs.InducedID,
				ecs.BenefitCategoryID,
				ecs.INBenefitTypeID,
				ecs.INEffectiveCostShare

	IF OBJECT_ID('tempdb.dbo.#BaseExpINEffCS') IS NOT NULL   
    DROP TABLE #BaseExpINEffCS

	INSERT INTO #BaseExpIN
	SELECT	baseIU.BenefitCategoryID,
			IUValue =	CASE WHEN SUM(baseIU.Units) = 0 THEN 1.0
							 ELSE ROUND(CAST(SUM(baseIU.Units * baseIU.InterpolateIUValue) AS FLOAT) / CAST(SUM(baseIU.Units) AS FLOAT), 14)
						END,
			Units = SUM(CAST(baseIU.Units AS FLOAT))
	FROM	#INbaseIU baseIU
	GROUP BY baseIU.BenefitCategoryID

END

----------------------------------------------------------------------------------------------------------------------------------------------- 
	INSERT INTO #ProjEffCSIN
	SELECT	SFS.ForecastID,
			lbc.BenefitCategoryID,
			lbc.BenefitCategoryName,
			lbm.InducedID,
			ium.TypeofService,
			CECS.BenefitTypeID,
			SUM(CAST(CECS.BenefitValue AS FLOAT) * csd.Factor) AS INEffectiveCostShare
	FROM	dbo.SavedForecastSetup SFS WITH (NOLOCK)
	INNER JOIN dbo.Benefits_SavedBenefitOptionDetail CECS WITH (NOLOCK)
		ON	CECS.PlanInfoID = SFS.PlanInfoID
		AND  CECS.BenefitOptionID = SFS.BenefitOptionID
	INNER JOIN dbo.LkpIntBenefitCategory LBC WITH (NOLOCK)
		ON	CECS.BenefitCategoryID = LBC.BenefitCategoryID
	INNER JOIN dbo.LkpIntInducedUtilBenefitMapping LBM WITH (NOLOCK)
		ON	LBM.BenefitCategoryID = LBC.BenefitCategoryID
	INNER JOIN dbo.InducedUtilMapping IUM WITH (NOLOCK)
		ON	IUM.ColumnID = LBM.InducedID
	INNER JOIN #CSDampening csd
		ON csd.BenefitCategoryID = LBM.BenefitCategoryID
	WHERE	SFS.IsLiveIndex = 1
			AND SFS.IsHidden = 0 
			AND LBC.IsUsed = 1
			AND LBC.IsEnabled = 1
			AND SFS.ForecastID = @XForecastID
			AND CECS.BenefitTypeID IS NOT NULL
			AND CECS.IsOON = 0
	GROUP BY 
			SFS.ForecastID, 
			LBC.BenefitCategoryID,
			LBC.BenefitCategoryName,
			LBM.InducedID, 	
			IUM.TypeofService,
			CECS.BenefitTypeID			

	INSERT INTO #ProjEffCSOON
	SELECT	SFS.ForecastID,
			lbc.BenefitCategoryID,
			lbc.BenefitCategoryName,
			lbm.InducedID,
			ium.TypeofService,
			CECS.BenefitTypeID,
			SUM(CAST(CECS.BenefitValue AS FLOAT) * csd.Factor) AS OONEffectiveCostShare
	FROM	dbo.SavedForecastSetup SFS WITH (NOLOCK)
	INNER JOIN dbo.Benefits_SavedBenefitOptionDetail CECS WITH (NOLOCK)
		ON	CECS.PlanInfoID = SFS.PlanInfoID
		AND  CECS.BenefitOptionID = SFS.BenefitOptionID
	INNER JOIN dbo.LkpIntBenefitCategory LBC WITH (NOLOCK)
		ON	CECS.BenefitCategoryID = LBC.BenefitCategoryID
	INNER JOIN dbo.LkpIntInducedUtilBenefitMapping LBM WITH (NOLOCK)
		ON	LBM.BenefitCategoryID = LBC.BenefitCategoryID
	INNER JOIN dbo.InducedUtilMapping IUM WITH (NOLOCK)
		ON	IUM.ColumnID = LBM.InducedID
	INNER JOIN #CSDampening csd
		ON csd.BenefitCategoryID = LBM.BenefitCategoryID
	WHERE	SFS.IsLiveIndex = 1
			AND SFS.IsHidden = 0 
			AND LBC.IsUsed = 1
			AND LBC.IsEnabled = 1
			AND SFS.ForecastID = @XForecastID
			AND CECS.BenefitTypeID IS NOT NULL	
			AND CECS.IsOON = 1
	GROUP BY 
			SFS.ForecastID, 
			LBC.BenefitCategoryID,
			LBC.BenefitCategoryName,
			LBM.InducedID, 	
			IUM.TypeofService,
			CECS.BenefitTypeID

--Blending IN & OON
INSERT INTO #ProjEffCS
SELECT	eci.ForecastID,
		eci.BenefitCategoryID,
		eci.BenefitCategoryName,
		eci.InducedID,
		eci.TypeofService,
		INBenefitTypeID,
		INEffectiveCostShare,
		OONBenefitTypeID,
		OONEffectiveCostShare
FROM #ProjEffCSIN eci
LEFT JOIN #ProjEffCSOON eco
	ON eci.BenefitCategoryID = eco.BenefitCategoryID

IF OBJECT_ID('tempdb.dbo.#ProjEffCSOON') IS NOT NULL   
DROP TABLE #ProjEffCSOON

IF OBJECT_ID('tempdb.dbo.#ProjEffCSIN') IS NOT NULL   
DROP TABLE #ProjEffCSIN
-- -------------------------------------------------------------------------------------------------------------------------------------------- 

IF OBJECT_ID(N'tempdb..#CalcInducedUtilizationFactors1') IS NOT NULL
    DROP TABLE #CalcInducedUtilizationFactors1;

CREATE TABLE #CalcInducedUtilizationFactors1
(
	PlanYear INT,
	ForecastID INT,
	BenefitCategoryID INT,
	BenefitCategoryName VARCHAR(MAX),
	MARating INT,
	InducedMapping INT,
	TypeofService VARCHAR(MAX),
	INBenefitTypeID INT,
	INBenefitValue DECIMAL(10,6),
	OONBenefitTypeID INT,
	OONBenefitValue DECIMAL(10,6),
	InducedUtilizationFactorIN DECIMAL(14,10),--DECIMAL(10,6),
	InducedUtilizationFactorOON DECIMAL(14,10),--DECIMAL(10,6),
	UserID VARCHAR(7),
	LastUpdateDateTime DATETIME
)

INSERT INTO #CalcInducedUtilizationFactors1
SELECT
	PlanYear = @PlanYearID,
	ecs.ForecastID,
	ecs.BenefitCategoryID,
	ecs.BenefitCategoryName,
	MARating = 1,
	InducedMapping = ecs.InducedID,
	ecs.TypeofService,
	ecs.INBenefitTypeID,
	ecs.INEffectiveCostShare,
	ecs.OONBenefitTypeID,
	ecs.OONEffectiveCostShare,
	InducedUtilizationFactorIN = 
					(CASE WHEN (ISNULL(@exist, 0) = 0 OR @SkipIU = 1 OR @TotalBiddableMemberMonthsExp = 0 OR bei.Units = 0) THEN 1.0
						  ELSE		
							(CASE WHEN MIN(lufmax.coinorcopayvalue) = MAX(lufmin.coinorcopayvalue) THEN ISNULL(MIN(lufmin.Value), 1)
								  ELSE ISNULL((((MIN(lufmax.coinorcopayvalue) - CAST(ecs.INEffectiveCostShare AS FLOAT)) * MIN(lufmin.Value))  --Interpolate bid year IU
												+ ((CAST(ecs.INEffectiveCostShare AS FLOAT) - MAX(lufmin.coinorcopayvalue)) * MAX(lufmax.Value)))
												/ (MIN(lufmax.coinorcopayvalue) - MAX(lufmin.coinorcopayvalue)), 1)
							 END)
							/bei.IUValue
							* (@NonDualBiddableMemberMonthsExp / @TotalBiddableMemberMonthsExp) -- NonDE# portion factor bid/base
							+ (@DualBiddableMemberMonthsExp/ @TotalBiddableMemberMonthsExp) -- DE# portion, factor assumed 1.0
					 END),
	InducedUtilizationFactorOON = 
					(CASE WHEN (ISNULL(@exist, 0) = 0 OR @SkipIU = 1 OR @TotalBiddableMemberMonthsExp = 0 OR beo.Units = 0) THEN 1.0
						  ELSE 
							(CASE WHEN MIN(lufmaxoon.coinorcopayvalue) = MAX(lufminoon.coinorcopayvalue) THEN ISNULL(MIN(lufminoon.Value), 1)
								  ELSE ISNULL((((MIN(lufmaxoon.coinorcopayvalue) - CAST(ecs.OONEffectiveCostShare AS FLOAT)) * MIN(lufminoon.Value))  --Interpolate bid year IU
										+ ((CAST(ecs.OONEffectiveCostShare AS FLOAT) - MAX(lufminoon.coinorcopayvalue)) * MAX(lufmaxoon.Value)))
										/ (MIN(lufmaxoon.coinorcopayvalue) - MAX(lufminoon.coinorcopayvalue)), 1)
							END)
							/beo.IUValue
							* (@NonDualBiddableMemberMonthsExp / @TotalBiddableMemberMonthsExp) -- NonDE# portion factor bid/base
							+ (@DualBiddableMemberMonthsExp / @TotalBiddableMemberMonthsExp) -- DE# portion, factor assumed 1.0
					END),
	@XUserID,
	@LastDateTime 
FROM	#ProjEffCS ecs
LEFT JOIN dbo.LkpInducedUtilFactors lufmin WITH (NOLOCK)
	ON	lufmin.BenefitTypeID = ecs.INBenefitTypeID
	AND lufmin.ColumnID = ecs.InducedID
	AND ecs.INEffectiveCostShare >= lufmin.CoinorCopayValue
LEFT JOIN dbo.LkpInducedUtilFactors lufmax WITH (NOLOCK)
	ON	lufmax.BenefitTypeID = ecs.INBenefitTypeID
	AND lufmax.ColumnID = ecs.InducedID
	AND ecs.INEffectiveCostShare <= lufmax.CoinorCopayValue
LEFT JOIN dbo.LkpInducedUtilFactors lufminoon WITH (NOLOCK)
	ON lufminoon.BenefitTypeID = ecs.OONBenefitTypeID
	AND lufminoon.ColumnID = ecs.InducedID
	AND ecs.OONEffectiveCostShare >= lufminoon.CoinorCopayValue 
LEFT JOIN dbo.LkpInducedUtilFactors lufmaxoon WITH (NOLOCK)
	ON	lufmaxoon.BenefitTypeID = ecs.OONBenefitTypeID
	AND lufmaxoon.ColumnID = ecs.InducedID
	AND ecs.OONEffectiveCostShare <= lufmaxoon.CoinorCopayValue
LEFT JOIN #BaseExpIN bei
	ON bei.BenefitCategoryID = ecs.BenefitCategoryID
LEFT JOIN #BaseExpOON beo
	ON beo.BenefitCategoryID = bei.BenefitCategoryID
GROUP BY  
	ecs.ForecastID, 
	ecs.BenefitCategoryID,
	ecs.BenefitCategoryName,
	ecs.InducedID, 	
	ecs.TypeofService,
	ecs.INBenefitTypeID,
	ecs.INEffectiveCostShare,
	ecs.OONBenefitTypeID,
	ecs.OONEffectiveCostShare,
	bei.IUValue,
	bei.Units,
	beo.IUValue,
	beo.Units

	

CREATE TABLE #CalcInducedUtilizationFactors_ExpMute
(
	PlanYear INT,
	ForecastID INT,
	BenefitCategoryID INT,
	BenefitCategoryName VARCHAR(MAX),
	MARating INT,
	InducedMapping INT,
	TypeofService VARCHAR(MAX),
	INBenefitTypeID INT,
	INBenefitValue DECIMAL(10,6),
	OONBenefitTypeID INT,
	OONBenefitValue DECIMAL(10,6),
	InducedUtilizationFactorIN DECIMAL(10,6),
	InducedUtilizationFactorOON DECIMAL(10,6),
	UserID VARCHAR(7),
	LastUpdateDateTime DATETIME
)

INSERT INTO #CalcInducedUtilizationFactors_ExpMute
SELECT
	PlanYear,
    ForecastID,
    BenefitCategoryID,
    BenefitCategoryName,
    MARating,
    InducedMapping,
    TypeofService,
    INBenefitTypeID,
    INBenefitValue,
    OONBenefitTypeID,
    OONBenefitValue,
	CAST(ROUND(CAST((1-@Mute_Pct) * InducedUtilizationFactorIN + @Mute_Pct * 1.0 AS DECIMAL(14,10)),6) AS DECIMAL(10,6)) AS InducedUtilizationFactorIN,
    CAST(ROUND(CAST((1-@Mute_Pct) * InducedUtilizationFactorOON + @Mute_Pct * 1.0 AS DECIMAL(14,10)),6) AS DECIMAL(10,6)) AS InducedUtilizationFactorOON,
    UserID,
    LastUpdateDateTime
FROM	#CalcInducedUtilizationFactors1


	IF OBJECT_ID('tempdb.dbo.#BaseExpIN') IS NOT NULL   
    DROP TABLE #BaseExpIN

	IF OBJECT_ID('tempdb.dbo.#BaseExpOON') IS NOT NULL   
    DROP TABLE #BaseExpOON

	IF OBJECT_ID ('tempdb.dbo.#CalcInducedUtilizationFactors1') IS NOT NULL
	DROP TABLE #CalcInducedUtilizationFactors1
-- --------------------------------------------------------------------------------------------------------------------------------------------
--*********************************************************************************************************************************************
--*************************************************************  MANUAL  **********************************************************************
--*********************************************************************************************************************************************
-- --------------------------------------------------------------------------------------------------------------------------------------------

--Temp Table - Base OON IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#OONManbaseIU') IS NOT NULL   
DROP TABLE #OONManbaseIU
CREATE TABLE #OONManbaseIU
	(
		ForecastID INT,
		MARatingOptionID TINYINT,
		PlanInfoID INT,
		CPS CHAR(13),
		Units DECIMAL(24,14),
		BenefitCategoryID SMALLINT,
		OONBenefitTypeID TINYINT,
		OONEffectiveCostShare DECIMAL(24,14),
		MinIUValue DECIMAL(8, 6),
		MinCoinOrCopay DECIMAL(16, 8),
		MaxIUValue DECIMAL(8, 6),
		MaxCoinOrCopay DECIMAL(16, 8),
		InterpolateIUValue DECIMAL(16, 8)
    );

--Temp Table - Base IN IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#INManbaseIU') IS NOT NULL 
DROP TABLE #INManbaseIU
CREATE TABLE #INManbaseIU
	(
		ForecastID INT,
		MARatingOptionID TINYINT,
		PlanInfoID INT,
		CPS CHAR(13),
		Units DECIMAL(24,14),
		BenefitCategoryID SMALLINT,
		INBenefitTypeID TINYINT,
		INEffectiveCostShare DECIMAL(24,14),
		MinIUValue DECIMAL(8, 6),
		MinCoinOrCopay DECIMAL(16, 8),
		MaxIUValue DECIMAL(8, 6),
		MaxCoinOrCopay DECIMAL(16, 8),
		InterpolateIUValue DECIMAL(16, 8)
    );

--Temp Table - Base Manual OON IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseManOON') IS NOT NULL   
DROP TABLE #BaseManOON
CREATE TABLE #BaseManOON
	(
		BenefitCategoryID SMALLINT,
		IUValue DECIMAL (24,14),
		Units DECIMAL(30,14)
    );
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Base Manual IN IU Value by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseManIN') IS NOT NULL   
DROP TABLE #BaseManIN
CREATE TABLE #BaseManIN
	(
		BenefitCategoryID SMALLINT,
		IUValue DECIMAL (24,14),
		Units DECIMAL(30,14)
    );
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Base Manual OON Effective Cost Share by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseManOONEffCS') IS NOT NULL   
DROP TABLE #BaseManOONEffCS
CREATE TABLE #BaseManOONEffCS
	(
		ForecastID INT ,
		MARatingOptionID TINYINT ,
		PlanInfoID INT ,
		CPS CHAR(13) ,
		Units DECIMAL(24,14) ,
		InducedID INT ,
		BenefitCategoryID INT ,
		OONBenefitTypeID TINYINT ,
		OONEffectiveCostShare DECIMAL(24,14),
		CSDampeningFactor DECIMAL(24,14)
    );
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Base Manual IN Effective Cost Share by Benefit Category
IF OBJECT_ID('tempdb.dbo.#BaseManINEffCS') IS NOT NULL   
DROP TABLE #BaseManINEffCS
CREATE TABLE #BaseManINEffCS
	(
		ForecastID INT ,
		MARatingOptionID TINYINT ,
		PlanInfoID INT ,
		CPS CHAR(13) ,
		Units DECIMAL(24,14) ,
		InducedID INT ,
		BenefitCategoryID INT ,
		INBenefitTypeID TINYINT ,
		INEffectiveCostShare DECIMAL(24,14),
		CSDampeningFactor DECIMAL(24,14)
    );	
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Temp Table - Projected Manual Effective Cost Share by Benefit Category
IF OBJECT_ID('tempdb.dbo.#ProjManEffCS') IS NOT NULL   
DROP TABLE #ProjManEffCS
CREATE TABLE #ProjManEffCS
	(
		ForecastID INT ,
		BenefitCategoryID INT ,
		BenefitCategoryName VARCHAR(50)  ,
		InducedID INT ,
		TypeofService VARCHAR(50) ,
		INBenefitTypeID TINYINT ,
		INEffectiveCostShare DECIMAL(24,14) ,
		OONBenefitTypeID TINYINT ,
		OONEffectiveCostShare DECIMAL(24,14)
    );	

IF OBJECT_ID('tempdb.dbo.#ProjManEffCS') IS NOT NULL   
DROP TABLE #ProjManEffCS
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- If CostAndUse cuts are selected we can calculate the IU factors, if not set them to 1 below
SET @existmanual = ( 
			   SELECT	COUNT(DFS.PlanInfoID)
			   FROM		dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
			   WHERE	DFS.ForecastID = @XForecastID
						AND DFS.MARatingOptionID = 2 --experience
			   GROUP BY DFS.ForecastID
             );
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Calc & Insert Base Manual OON Unit-Weighted IU Values
IF (ISNULL(@existmanual, 0) = 0 OR @SkipIU = 1)
BEGIN
	INSERT INTO #BaseManOON
	SELECT	BenefitCategoryID,
			IUValue =	1.0,
			Units = 0
	FROM dbo.LkpIntBenefitCategory WITH (NOLOCK)
	WHERE IsUsed = 1
END
ELSE
BEGIN

	INSERT INTO #BaseManOONEffCS
	SELECT	DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,
			BaseSPI.CPS,
			Units = DFC.UnitCnt
					+ DFC.EncounterUnitCnt
					+ DFC.DelegatedEncounterUnitCnt
					+ DFC.AdditiveAdjUnits
					+ DFC.ModelOfCareAdjUnits
					+ DFC.UCUnitsAdj
					+ DFC.MSBUnits
					+ DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.OONBenefitTypeID,
			OONEffectiveCostShare = SUM(CAST(CECS.OONEffectiveCostShare AS FLOAT))*CSD.Factor,
			CSD.Factor
	FROM	dbo.SavedForecastSetup SFS WITH (NOLOCK)
	INNER JOIN dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
		ON SFS.ForecastID = DFS.ForecastID
	LEFT JOIN dbo.SavedPlanInfo BaseSPI WITH (NOLOCK)
		ON BaseSPI.PlanInfoID = DFS.PlanInfoID
	INNER JOIN dbo.CalcEffectiveCostShareBase CECS WITH (NOLOCK)
		ON CECS.PlanYearID = BaseSPI.PlanYear
		AND CECS.[Contract-PBP] = BaseSPI.CPS
	INNER JOIN dbo.SavedDFClaims DFC WITH (NOLOCK)
		ON	DFC.PlanInfoID = DFS.PlanInfoID
		AND DFC.DFVersionID = SFS.DFVersionID
		AND DFC.BenefitCategoryID = CECS.BenefitCategoryID
		AND DFC.DemogIndicator IN (1,2)
	INNER JOIN dbo.LkpIntInducedUtilBenefitMapping LBM WITH (NOLOCK)
		ON	LBM.BenefitCategoryID = CECS.BenefitCategoryID		
	INNER JOIN #CSDampening CSD
		ON CSD.BenefitCategoryID = LBM.BenefitCategoryID
	WHERE	DFS.ForecastID = @XForecastID
			AND DFS.MARatingOptionID = 2
			AND DFC.DemogIndicator IN (1,2)
			AND CECS.OONBenefitTypeID IS NOT null
	GROUP BY  
			DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,							
			BaseSPI.CPS,
			DFC.UnitCnt,
			DFC.EncounterUnitCnt,
			DFC.DelegatedEncounterUnitCnt,
			DFC.AdditiveAdjUnits,
			DFC.ModelOfCareAdjUnits,
			DFC.UCUnitsAdj,
			DFC.MSBUnits,
			DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.OONBenefitTypeID,
			CSD.Factor
-- --------------------------------------------------------------------------------------------------------------------------------------------
INSERT INTO #OONManbaseIU
SELECT	ecs.ForecastID,
					ecs.MARatingOptionID,
					ecs.PlanInfoID,
					ecs.CPS,
					Units = CAST(ecs.Units AS FLOAT),
					ecs.BenefitCategoryID,
					ecs.OONBenefitTypeID,
					ecs.OONEffectiveCostShare, 
					[MinIUValue] = MIN(lufmin.Value),
					[MinCoinOrCopay] = MAX(lufmin.CoinOrCopayValue),
					[MaxIUValue] = MAX(lufmax.Value),
					[MaxCoinOrCopay] = MIN(lufmax.CoinOrCopayValue),
                    [InterpolateIUValue] =	CASE WHEN MAX(lufmin.CoinOrCopayValue) = MIN(lufmax.CoinOrCopayValue) THEN ISNULL(MIN(lufmin.Value), 1)
											ELSE ISNULL((((MIN(lufmax.CoinOrCopayValue) - CAST(ecs.OONEffectiveCostShare AS FLOAT)) * MIN(lufmin.Value))
												+ ((CAST(ecs.OONEffectiveCostShare AS FLOAT) - MAX(lufmin.CoinOrCopayValue)) * MAX(lufmax.Value)))
													/ (MIN(lufmax.CoinOrCopayValue) - MAX(lufmin.CoinOrCopayValue)), 1)
									END		
			FROM	#BaseManOONEffCS ecs
			LEFT JOIN dbo.LkpInducedUtilFactors lufmin
				ON	lufmin.BenefitTypeID = ecs.OONBenefitTypeID
				AND lufmin.ColumnID = ecs.InducedID
				AND ecs.OONEffectiveCostShare >= lufmin.CoinOrCopayValue
			LEFT JOIN dbo.LkpInducedUtilFactors lufmax
				ON	lufmax.BenefitTypeID = ecs.OONBenefitTypeID
				AND lufmax.ColumnID = ecs.InducedID
				AND ecs.OONEffectiveCostShare <= lufmax.CoinOrCopayValue
			GROUP BY  
					ecs.ForecastID,
					ecs.MARatingOptionID,
					ecs.PlanInfoID,							
					ecs.CPS,
					ecs.Units,
					ecs.InducedID,
					ecs.BenefitCategoryID,
					ecs.OONBenefitTypeID,
					ecs.OONEffectiveCostShare
IF OBJECT_ID('tempdb.dbo.#BaseManOONEffCS') IS NOT NULL   
DROP TABLE #BaseManOONEffCS
-- --------------------------------------------------------------------------------------------------------------------------------------------
	INSERT INTO #BaseManOON
	SELECT	baseIU.BenefitCategoryID,
			[IUValue] =	CASE WHEN SUM(baseIU.Units) = 0 THEN 1.0
							ELSE ROUND(CAST(SUM(baseIU.Units * baseIU.InterpolateIUValue) AS float) / CAST(SUM(baseIU.Units) AS float), 14)
						END,
			[Units] = SUM(CAST(baseIU.Units AS FLOAT))
	FROM	#OONManbaseIU baseIU
	GROUP BY baseIU.BenefitCategoryID

	IF OBJECT_ID('tempdb.dbo.#OONbaseIU') IS NOT NULL   
DROP TABLE #OONbaseIU
END
-- --------------------------------------------------------------------------------------------------------------------------------------------
--Calc & Insert Base Manual IN Unit-Weighted IU Values
IF (ISNULL(@existmanual, 0) = 0 OR @SkipIU = 1)
BEGIN
	INSERT INTO #BaseManIN
	SELECT	BenefitCategoryID,
			IUValue =	1.0,
			Units = 0
	FROM dbo.LkpIntBenefitCategory WITH (NOLOCK)
	WHERE IsUsed = 1
END
ELSE
BEGIN
	INSERT INTO #BaseManINEffCS
	SELECT	DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,
			BaseSPI.CPS,
			Units = DFC.UnitCnt
					+ DFC.EncounterUnitCnt
					+ DFC.DelegatedEncounterUnitCnt
					+ DFC.AdditiveAdjUnits
					+ DFC.ModelOfCareAdjUnits
					+ DFC.UCUnitsAdj
					+ DFC.MSBUnits
					+ DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.INBenefitTypeID,
			INEffectiveCostShare = SUM(CAST(CECS.INEffectiveCostShare AS FLOAT))*CSD.Factor,
			CSD.Factor
	FROM	dbo.SavedForecastSetup SFS WITH (NOLOCK)
	INNER JOIN dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
		ON SFS.ForecastID = DFS.ForecastID
	LEFT JOIN dbo.SavedPlanInfo BaseSPI WITH (NOLOCK)
		ON BaseSPI.PlanInfoID = DFS.PlanInfoID
	INNER JOIN dbo.CalcEffectiveCostShareBase CECS WITH (NOLOCK)
		ON CECS.PlanYearID = BaseSPI.PlanYear
		AND CECS.[Contract-PBP] = BaseSPI.CPS
	INNER JOIN dbo.SavedDFClaims DFC WITH (NOLOCK)
		ON	DFC.PlanInfoID = DFS.PlanInfoID
		AND DFC.DFVersionID = SFS.DFVersionID
		AND DFC.BenefitCategoryID = CECS.BenefitCategoryID
		AND DFC.DemogIndicator IN (1,2)
	INNER JOIN dbo.LkpIntInducedUtilBenefitMapping LBM WITH (NOLOCK)
		ON	LBM.BenefitCategoryID = CECS.BenefitCategoryID		
	INNER JOIN #CSDampening CSD
		ON CSD.BenefitCategoryID = LBM.BenefitCategoryID
	WHERE	DFS.ForecastID = @XForecastID
			AND DFS.MARatingOptionID = 2
			AND DFC.DemogIndicator IN (1,2)
			AND CECS.INBenefitTypeID IS NOT NULL	
	GROUP BY  
			DFS.ForecastID,
			DFS.MARatingOptionID,
			DFS.PlanInfoID,							
			BaseSPI.CPS,
			DFC.UnitCnt,
			DFC.EncounterUnitCnt,
			DFC.DelegatedEncounterUnitCnt,
			DFC.AdditiveAdjUnits,
			DFC.ModelOfCareAdjUnits,
			DFC.UCUnitsAdj,
			DFC.MSBUnits,
			DFC.MSBReductionClaimsUnits,
			LBM.InducedID,
			CECS.BenefitCategoryID,
			CECS.INBenefitTypeID,
			CSD.Factor

IF OBJECT_ID('tempdb.dbo.#CSDampening') IS NOT NULL   
DROP TABLE #CSDampening
-- --------------------------------------------------------------------------------------------------------------------------------------------
INSERT INTO #INManbaseIU
SELECT	ecs.ForecastID,
					ecs.MARatingOptionID,
					ecs.PlanInfoID,
					ecs.CPS,
					Units = CAST(ecs.Units AS FLOAT),
					ecs.BenefitCategoryID,
					ecs.INBenefitTypeID,
					ecs.INEffectiveCostShare,
					[MinIUValue] = MIN(lufmin.Value),
					[MinCoinOrCopay] = MAX(lufmin.CoinOrCopayValue),
					[MaxIUValue] = MAX(lufmax.Value),
					[MaxCoinOrCopay] = MIN(lufmax.CoinOrCopayValue),
					[InterpolateIUValue] =	CASE WHEN MAX(lufmin.CoinOrCopayValue) = MIN(lufmax.CoinOrCopayValue) THEN ISNULL(MIN(lufmin.Value), 1)
										 ELSE ISNULL((((MIN(lufmax.CoinOrCopayValue) - CAST(ecs.INEffectiveCostShare AS FLOAT)) * MIN(lufmin.Value))
												+ ((CAST(ecs.INEffectiveCostShare AS FLOAT) - MAX(lufmin.CoinOrCopayValue)) * MAX(lufmax.Value)))
												/ (MIN(lufmax.CoinOrCopayValue) - MAX(lufmin.CoinOrCopayValue)), 1)
									END

			FROM	#BaseManINEffCS ecs
			LEFT JOIN dbo.LkpInducedUtilFactors lufmin
				ON	lufmin.BenefitTypeID = ecs.INBenefitTypeID
				AND lufmin.ColumnID = ecs.InducedID
				AND ecs.INEffectiveCostShare >= lufmin.CoinOrCopayValue
			LEFT JOIN dbo.LkpInducedUtilFactors lufmax
				ON	lufmax.BenefitTypeID = ecs.INBenefitTypeID
				AND lufmax.ColumnID = ecs.InducedID
				AND ecs.INEffectiveCostShare <= lufmax.CoinOrCopayValue
			GROUP BY  
					ecs.ForecastID,
					ecs.MARatingOptionID,
					ecs.PlanInfoID,							
					ecs.CPS,
					ecs.Units,
					ecs.InducedID,
					ecs.BenefitCategoryID,
					ecs.INBenefitTypeID,
					ecs.INEffectiveCostShare
-- --------------------------------------------------------------------------------------------------------------------------------------------
	INSERT INTO #BaseManIN
	SELECT	baseIU.BenefitCategoryID,
			[IUValue] =	CASE WHEN SUM(baseIU.Units) = 0 THEN 1.0
							 ELSE ROUND(CAST(SUM(baseIU.Units * baseIU.InterpolateIUValue) AS float) / CAST(SUM(baseIU.Units) AS float), 14)
						END,
			[Units] = SUM(CAST(baseIU.Units AS FLOAT))
	FROM	#INManbaseIU baseIU
	GROUP BY baseIU.BenefitCategoryID

	IF OBJECT_ID('tempdb.dbo.#BaseManINEffCS') IS NOT NULL   
	DROP TABLE #BaseManINEffCS
	IF OBJECT_ID('tempdb.dbo.#INbaseIU') IS NOT NULL   
    DROP TABLE #INbaseIU
END
-- --------------------------------------------------------------------------------------------------------------------------------------------
IF OBJECT_ID(N'tempdb..#CalcInducedUtilizationFactorsTemp') IS NOT NULL
    DROP TABLE #CalcInducedUtilizationFactorsTemp;

SELECT
	PlanYear = @PlanYearID,
	ecs.ForecastID,
	ecs.BenefitCategoryID,
	ecs.BenefitCategoryName,
	MARating = 2,
	InducedMapping = ecs.InducedID,
	ecs.TypeofService,
	ecs.INBenefitTypeID,
	ecs.INEffectiveCostShare,
	ecs.OONBenefitTypeID,
	ecs.OONEffectiveCostShare,
	InducedUtilizationFactorIN = 	
					CAST((CASE WHEN (ISNULL(@existmanual, 0) = 0 OR @SkipIU = 1 OR @TotalBiddableMemberMonthsMan = 0 OR bmi.Units = 0) THEN 1.0
						 ELSE		
							(CASE WHEN MIN(lufmax.coinorcopayvalue) = MAX(lufmin.coinorcopayvalue) THEN ISNULL(MIN(lufmin.Value), 1)
								  ELSE ISNULL((((MIN(lufmax.coinorcopayvalue) - CAST(ecs.INEffectiveCostShare AS FLOAT)) * MIN(lufmin.Value))  --Interpolate bid year IU
										+ ((CAST(ecs.INEffectiveCostShare AS FLOAT) - MAX(lufmin.coinorcopayvalue)) * MAX(lufmax.Value)))
										/ (MIN(lufmax.coinorcopayvalue) - MAX(lufmin.coinorcopayvalue)), 1)
							 END)
							/ bmi.IUValue
							* (@NonDualBiddableMemberMonthsMan / @TotalBiddableMemberMonthsMan) -- NonDE# portion factor bid/base
							+ (@DualBiddableMemberMonthsMan/ @TotalBiddableMemberMonthsMan) -- DE# portion, factor assumed 1.0
					 END) AS DECIMAL(14,10)),
	InducedUtilizationFactorOON = 
					CAST((CASE WHEN (ISNULL(@existmanual, 0) = 0 OR @SkipIU = 1 OR @TotalBiddableMemberMonthsMan = 0 OR bmo.Units = 0) THEN 1.0
						  ELSE 
							(CASE WHEN MIN(lufmaxoon.coinorcopayvalue) = MAX(lufminoon.coinorcopayvalue) THEN ISNULL(MIN(lufminoon.Value), 1)
								  ELSE ISNULL((((MIN(lufmaxoon.coinorcopayvalue) - CAST(ecs.OONEffectiveCostShare AS FLOAT)) * MIN(lufminoon.Value))  --Interpolate bid year IU
										+ ((CAST(ecs.OONEffectiveCostShare AS FLOAT) - MAX(lufminoon.coinorcopayvalue)) * MAX(lufmaxoon.Value)))
										/ (MIN(lufmaxoon.coinorcopayvalue) - MAX(lufminoon.coinorcopayvalue)), 1)
							 END)
							/ bmo.IUValue
							* (@NonDualBiddableMemberMonthsMan / @TotalBiddableMemberMonthsMan) -- NonDE# portion factor bid/base
							+ (@DualBiddableMemberMonthsMan / @TotalBiddableMemberMonthsMan) -- DE# portion, factor assumed 1.0
					 END) AS DECIMAL(14,10)),
	@XUserID USERID,
	@LastDateTime LastDateTime
INTO #CalcInducedUtilizationFactorsTemp
FROM	#ProjEffCS ecs
LEFT JOIN dbo.LkpInducedUtilFactors lufmin WITH (NOLOCK)
	ON	lufmin.BenefitTypeID = ecs.INBenefitTypeID
	AND lufmin.ColumnID = ecs.InducedID
	AND ecs.INEffectiveCostShare >= lufmin.CoinorCopayValue
LEFT JOIN dbo.LkpInducedUtilFactors lufmax WITH (NOLOCK)
	ON	lufmax.BenefitTypeID = ecs.INBenefitTypeID
	AND lufmax.ColumnID = ecs.InducedID
	AND ecs.INEffectiveCostShare <= lufmax.CoinorCopayValue
LEFT JOIN dbo.LkpInducedUtilFactors lufminoon WITH (NOLOCK)
	ON lufminoon.BenefitTypeID = ecs.OONBenefitTypeID
	AND lufminoon.ColumnID = ecs.InducedID
	AND ecs.OONEffectiveCostShare >= lufminoon.CoinorCopayValue 
LEFT JOIN dbo.LkpInducedUtilFactors lufmaxoon WITH (NOLOCK)
	ON	lufmaxoon.BenefitTypeID = ecs.OONBenefitTypeID
	AND lufmaxoon.ColumnID = ecs.InducedID
	AND ecs.OONEffectiveCostShare <= lufmaxoon.CoinorCopayValue
LEFT JOIN #BaseManIN bmi
	ON bmi.BenefitCategoryID = ecs.BenefitCategoryID
LEFT JOIN #BaseManOON bmo
	ON bmo.BenefitCategoryID = bmi.BenefitCategoryID
GROUP BY  
		ecs.ForecastID, 
		ecs.BenefitCategoryID,
		ecs.BenefitCategoryName,
		ecs.InducedID, 	
		ecs.TypeofService,
		ecs.INBenefitTypeID,
		ecs.INEffectiveCostShare,
		ecs.OONBenefitTypeID,
		ecs.OONEffectiveCostShare,
		bmi.IUValue,
		bmi.Units,
		bmo.IUValue,
		bmo.Units

SELECT PlanYear,
       ForecastID,
       BenefitCategoryID,
       BenefitCategoryName,
       MARating,
       InducedMapping,
       TypeofService,
       INBenefitTypeID,
       INEffectiveCostShare,
       OONBenefitTypeID,
       OONEffectiveCostShare,
	   CAST(ROUND(CAST((1-@Mute_Pct) * InducedUtilizationFactorIN + @Mute_Pct * 1.0 AS DECIMAL(14,10)),6) AS DECIMAL(10,6)) AS InducedUtilizationFactorIN,
       CAST(ROUND(CAST((1-@Mute_Pct) * InducedUtilizationFactorOON + @Mute_Pct * 1.0 AS DECIMAL(14,10)),6) AS DECIMAL(10,6)) AS InducedUtilizationFactorOON,
       USERID,
       LastDateTime
INTO #CalcInducedUtilizationFactors_ManMute 
FROM #CalcInducedUtilizationFactorsTemp

IF OBJECT_ID('tempdb.dbo.#BaseManOON') IS NOT NULL   
DROP TABLE #BaseManOON

IF OBJECT_ID('tempdb.dbo.#ProjEffCS') IS NOT NULL   
DROP TABLE #ProjEffCS

--enable disable logic added
IF((SELECT [IsIUFactorEnabled] FROM [dbo].[LkpModelSettings] WITH(NOLOCK))= 1 )
BEGIN
	--Clear old data for this ForecastID
	DELETE FROM dbo.CalcInducedUtilizationFactors with(ROWLOCK)
	WHERE ForecastID = @XForecastID 

	INSERT INTO dbo.CalcInducedUtilizationFactors
	(
	 PlanYear,
	 ForecastID,
	 BenefitCategoryID,
	 BenefitCategoryName,
	 MARating,
	 InducedMapping,
	 TypeofService,
	 INBenefitTypeID,
	 INBenefitValue,
	 OONBenefitTypeID,
	 OONBenefitValue,
	 InducedUtilizationFactorIN,
	 InducedUtilizationFactorOON,
	 UserID,
	 LastUpdateDateTime
	)
	SELECT PlanYear,
		   ForecastID,
		   BenefitCategoryID,
		   BenefitCategoryName,
		   MARating,
		   InducedMapping,
		   TypeofService,
		   INBenefitTypeID,
		   INBenefitValue,
		   OONBenefitTypeID,
		   OONBenefitValue,
		   InducedUtilizationFactorIN,
		   InducedUtilizationFactorOON,
		   USERID,
		   LastUpdateDateTime 
	FROM #CalcInducedUtilizationFactors_ExpMute
	UNION ALL
	SELECT PlanYear,
		   ForecastID,
		   BenefitCategoryID,
		   BenefitCategoryName,
		   MARating,
		   InducedMapping,
		   TypeofService,
		   INBenefitTypeID,
		   INEffectiveCostShare,
		   OONBenefitTypeID,
		   OONEffectiveCostShare,
		   InducedUtilizationFactorIN,
		   InducedUtilizationFactorOON,
		   USERID,
		   LastDateTime 
	FROM #CalcInducedUtilizationFactors_ManMute;
END
	
	IF OBJECT_ID(N'tempdb..#CalcInducedUtilizationFactorsTemp') IS NOT NULL
    DROP TABLE #CalcInducedUtilizationFactorsTemp;
	IF OBJECT_ID(N'tempdb..#CalcInducedUtilizationFactors_ManMute') IS NOT NULL
    DROP TABLE #CalcInducedUtilizationFactors_ManMute;

	IF OBJECT_ID(N'tempdb..#CalcInducedUtilizationFactors_ExpMute') IS NOT NULL
    DROP TABLE #CalcInducedUtilizationFactors_ExpMute;
GO
