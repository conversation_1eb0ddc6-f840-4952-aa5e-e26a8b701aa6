SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spDeleteBenefitBundles]   
--    
-- AUTHOR: Surya <PERSON>rthy 
--    
-- CREATED DATE: 2024-Oct-30   
-- Type: 
-- DESCRIPTION: Procedure responsible for deleting bundle
--    
-- PARAMETERS:    
-- Input: 
-- @BundleID
-- @OutPutResult

-- TABLES:   
--
-- Read:    
--  
-- Write:    
--  PrePricing.BenefitBundleMapping
--  PrePricing.BenefitBundle 

-- VIEWS:    
--    
-- FUNCTIONS:   
-- 
--
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-30		1		Initial Version                 Surya Murthy
-- 2025-Feb-03		2		Error message logic added       Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spDeleteBenefitBundles]
(
	@BundleID INT
)
AS    
BEGIN    
	SET NOCOUNT ON
	DECLARE	@OutPutResultCode VARCHAR(20)
    DECLARE @OutPutResult VARCHAR(200)
	BEGIN TRY
	BEGIN TRANSACTION bundledelete	 
	    SET @OutPutResultCode='Success'
		SET @OutPutResult='Bundle Deleted Successfully.'	 
		DELETE FROM PrePricing.BenefitBundleMapping WHERE BundleID=@BundleID;
		DELETE FROM PrePricing.BenefitBundle WHERE BundleID=@BundleID;
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
		COMMIT TRANSACTION bundledelete;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION bundledelete;
		SET @OutPutResultCode='Error'
		SET @OutPutResult='Error while deleting bundle.';		
		SELECT @OutPutResultCode AS OutputCode,@OutPutResult AS OutputMessage
	END CATCH
END
GO
