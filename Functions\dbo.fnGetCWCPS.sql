SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================
-- FUNCTION NAME:    fnGetCWCPS        
 
-- HISTORY:
-- -------------------------------------------------------------------------------------------------------------------------  
-- DATE			VERSION		CHANGES MADE																	DEVELOPER    
-- -------------------------------------------------------------------------------------------------------------------------  
-- 2024-Sep-30  24          Update logic to allow modeling of termed plans                                  <PERSON>
-- ---------------------------------------------------------------------------------------------------------------------- 



CREATE FUNCTION [dbo].[fnGetCWCPS](@CPBPListCall VARCHAR(MAX) ,
    @BaseYearCall INT )
RETURNS TABLE
AS 
RETURN SELECT  BaseCPBPS AS [BaseYearContractPBPSegment],
						CurrentCPBPS AS [CurrentYearContractPBPSegment],
						BidCPBPS AS [BidYearContractPBPSegment]
				FROM    CW_Crosswalks
				WHERE   ( ( COALESCE(BidCPBPS,CurrentCPBPS,BaseCPBPS) IN (
							SELECT  Value
							FROM    dbo.fnSTringSPlit(@CPBPListCall, ',') )
							OR @CPBPListCall IS NULL
						  )
						  AND COALESCE(BidCPBPS,CurrentCPBPS,BaseCPBPS) IS NOT NULL
						)
						AND BaseYear = @BaseYearCall

GO
