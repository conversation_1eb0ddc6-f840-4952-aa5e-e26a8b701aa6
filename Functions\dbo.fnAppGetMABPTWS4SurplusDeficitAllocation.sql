-- -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------
-- FUNCTION NAME: fnAppGetMABPTWS4SurplusDeficitAllocation
--
-- AUTHOR: <PERSON><PERSON><PERSON><PERSON>
--
-- CREATED DATE: 2025-February-07
--
-- DESCRIPTION: Function responsible for allocating Surplus/Deficit to Service Categories using Net PMPM as weights as seen on WS4 ( allocation to covered services only)
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--
-- RETURNS: Table
--
-- TABLES: 
--	Read: SavedForecastSetup
--		  SavedProjectedSurplusDeficit
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--		Read: fnGetBidYear
--			  fnPlanCountyProjectedMemberMonths
--			  fnAppGetMABPTWS4Percent
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------
-- DATE					VERSION		CHANGES MADE						                                    DEVELOPER		
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------
-- 2025-February-07		1			Initial Version															Aleksandar Dimitrijevic
-- 2025-April-07		2			Function name updated in summary section.								Aleksandar Dimitrijevic
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS4SurplusDeficitAllocation]
   (
    @ForecastID INT
   )

RETURNS @Results TABLE
(	
	ForecastID INT,
    ServiceCategoryCode CHAR(2),
	SurplusDeficit DECIMAL(14,8)
)
AS

	BEGIN
		-- Declare local variables to use in place of input parameters to negate parameter sniffing.
		DECLARE @xForecastID INT = @ForecastID;

		-- Declare variables
		DECLARE @IsRisk BIT;
		DECLARE @SurplusDeficit DECIMAL(14, 8);
		DECLARE @NonPercent DECIMAL(10, 9),
				@DualPercent DECIMAL(10, 9);

		-- Assign value to variables
		SET @IsRisk= (SELECT IsRiskPlan
						FROM dbo.SavedForecastSetup WITH (NOLOCK)
					   WHERE PlanYear = (SELECT dbo.fnGetBidYear())
					     AND ForecastID = @xForecastID);

		SELECT @SurplusDeficit = CASE WHEN @IsRisk = 1
									  THEN ProjectedSurplusDeficit
									  ELSE 0
								 END
		  FROM dbo.SavedProjectedSurplusDeficit WITH (NOLOCK)
		 WHERE ForecastID = @xForecastID;

		SELECT @NonPercent = SUM(NonDE.ProjectedMemberMonths)/SUM(Total.ProjectedMemberMonths),
			   @DualPercent = SUM(DE.ProjectedMemberMonths)/SUM(Total.ProjectedMemberMonths) 
		  FROM dbo.fnPlanCountyProjectedMemberMonths(@xForecastID, 0) NonDE
		 INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@xForecastID, 1) DE 
			ON NonDE.ForecastID = DE.ForecastID
		   AND NonDE.StateTerritoryID = DE.StateTerritoryID
		   AND NonDE.CountyCode = DE.CountyCode
		 INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@xForecastID, 2) Total 
			ON Total.ForecastID = DE.ForecastID
		   AND Total.StateTerritoryID = DE.StateTerritoryID
		   AND Total.CountyCode = DE.CountyCode;
		
		/********************************/
		/** Surplus/Deficit Allocation **/
		/********************************/
		-- Combining Net DE#/NON-DE# (allocation numerator)
		DECLARE @NetAllowed TABLE
		(	
		 ForecastID INT,
		 ServiceCategoryCode CHAR(2),
		 Net DECIMAL(14,8)
		);

		INSERT INTO @NetAllowed
			(
			 ForecastID,
			 ServiceCategoryCode,
			 Net
			)
			SELECT nde.ForecastID,
				   nde.ServiceCategoryCode,
				   CASE WHEN nde.ServiceCategoryCode <= 'k.'
						THEN SUM(@NonPercent * nde.Net + @DualPercent * de.Net)
						ELSE 0
				   END
			  FROM dbo.fnAppGetMABPTWS4Percent(@xForecastID) nde
		     INNER JOIN dbo.fnAppGetMABPTWS4Percent(@xForecastID) de
			    ON de.ForecastID = nde.ForecastID
			   AND de.ServiceCategoryCode = nde.ServiceCategoryCode
		     WHERE nde.DualEligibleTypeID = 0
			   AND de.DualEligibleTypeID = 1
			 GROUP BY nde.ForecastID,
				      nde.ServiceCategoryCode;
		
		-- Summing Net bo plan level (allocation denominator)
		DECLARE @NetAllowedTotal TABLE
		(	
		 ForecastID INT,
		 Net DECIMAL(14,8)
		);

		INSERT INTO @NetAllowedTotal
			(
			 ForecastID,
			 Net
			)
			SELECT ForecastID,
				   SUM(Net)
			  FROM @NetAllowed
			 GROUP BY ForecastID;
		
		--Final output (Allocated Surplus/Deficit by Service Category)
		INSERT INTO @Results
			(
			 ForecastID,
			 ServiceCategoryCode,
			 SurplusDeficit
			)
			SELECT naa.ForecastID,
				   ServiceCategoryCode,
				   ISNULL(naa.Net / nat.Net * @SurplusDeficit, 0)
			  FROM @NetAllowed naa
			  LEFT JOIN @NetAllowedTotal nat
				ON nat.ForecastID = naa.ForecastID;
		RETURN
	END;
GO