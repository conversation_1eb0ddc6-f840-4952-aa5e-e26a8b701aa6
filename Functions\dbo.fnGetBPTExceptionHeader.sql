SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetBPTExceptionHeader] ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetBPTExceptionHeader
--
-- CREATOR: <PERSON>
--
-- CREATED DATE: 2011-Jan-18
-- HEADER UPDATED: 2011-Jan-18
--
-- DESCRIPTION: Gets Values from BPTExceptionHeader Table
--
-- PARAMETERS:
--	Input:
--		@ForecastID 
--
--  Output:
--      @RESULTS TABLE     
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
--
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-18		1			Initial Version							                            Nate Jacoby
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBPTExceptionHeader]
(
    @ForecastID INT
)  
RETURNS @RESULTS TABLE 
( 		 
    IsBasePlan BIT,
    IsOverride BIT,
    Credibility DECIMAL(8,6)
) AS  

    BEGIN

        INSERT INTO @RESULTS
        SELECT
            IsBasePlan,
	        IsOverride,
            Credibility
        FROM SavedPlanBPTExceptionHeader
        WHERE ForecastID = @ForecastID
            AND IsHidden = 0

    RETURN 
END
GO
