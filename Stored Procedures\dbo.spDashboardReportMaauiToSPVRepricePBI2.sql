SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- PROCEDURE NAME: spDashboardReportMaauiToSPVRepricePBI2

-- DESCRIPTION: This SP returns extract for "SPV Reprice" user action from AppLogs table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--
-- TABLES:
--    Read:
--      [dbo].[DashboardReportAppLogs]
--		
-- Example 
-- Exec [dbo].[spDashboardReportMaauiToSPVRepricePBI2] 0, '2023-10-04 09:08:13.753'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Oct-03			1			Initial Version                                                                         Archana Sahu
-- 2024-Feb-20			2			Changed "Message" column logic                                                         
--									Plans, PlanCount Column Logic change													Sheetal Patil
-- 2024-Mar-20			3			Changed action name																		Sheetal Patil
---------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROC [dbo].[spDashboardReportMaauiToSPVRepricePBI2]
(@LastSuccessfullRunLogid INT
,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  

DECLARE @XLastSuccessfullRunLogid  INT = @LastSuccessfullRunLogid

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTemp')) IS NOT NULL DROP TABLE #AppLogTemp;

SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
	  ,characterEvenCount
	  ,FormattedMesssage
INTO #AppLogTemp
FROM ( SELECT
		[LogID]
      ,[Date]
      ,[User]
	  ,CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
ELSE [Message] END AS [Message]
,characterEvenCount
,FormattedMesssage
FROM (
SELECT [LogID]
      ,[Date]
      ,[User]
      ,[Message]
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
       ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (
SELECT REPLACE(REPLACE(RIGHT(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END, 
		DATALENGTH(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END) - 
		CHARINDEX('{', CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END),0) < 5 
	THEN CHARINDEX('"\,"', REVERSE(CASE	
	WHEN CHARINDEX('[',[Message]) > 0 AND CHARINDEX(']',[Message]) > 0 
    THEN LTRIM(LEFT([Message], CHARINDEX('[',[Message])-1) + RIGHT([Message],LEN([Message]) - CHARINDEX(']',[Message])))
	ELSE [Message] END),0) 
	ELSE 0 END AS  DataLen
,[LogID]
      ,[Date]
      ,[User]
      ,[Message]
FROM (SELECT CAST([LogID] AS INT) LogID
	  ,[Date]
      ,[User]
      ,[Message]
FROM [dbo].DashboardReportAppLogs WITH (NOLOCK) 
WHERE [LogID] > @XLastSuccessfullRunLogid
AND ([Message] LIKE '%Finished executing POST: Scenario/RepricePlan%' 
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetIsPlanToReprice%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetRevenueData%'
OR [Message] LIKE '%Finished executing POST: Scenario/GetPlansListSorted%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetAdminData%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetRebateMemberPremiumData%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetCostShareData%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetMarketSummMemberPremiumData%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetBenchmarkCountyLevelSummaries%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetClaimForecastFactors%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetPlansInBaseDetails%'
OR [Message] LIKE '%Finished executing POST: Scenario/BenefitPlanJSONData%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetCredibilityAndRiskScore%'
OR [Message] LIKE '%Finished executing POST: SingleScenario/GetBPTData%'
OR [Message] LIKE '%Executing POST: Scenario/RepricePlan%'
)) AS t1 ) AS t2 ) AS t3 ) AS t4 ) AS t5

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction1')) IS NOT NULL DROP TABLE #AppLogTempAction1;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction1
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: Scenario/RepricePlan%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing %: Scenario/RepricePlan%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction2')) IS NOT NULL DROP TABLE #AppLogTempAction2;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction2
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetIsPlanToReprice%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetIsPlanToReprice%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction3')) IS NOT NULL DROP TABLE #AppLogTempAction3;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction3
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetRevenueData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetRevenueData%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction4')) IS NOT NULL DROP TABLE #AppLogTempAction4;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction4
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: Scenario/GetPlansListSorted%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: Scenario/GetPlansListSorted%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction5')) IS NOT NULL DROP TABLE #AppLogTempAction5;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction5
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetAdminData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetAdminData%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction6')) IS NOT NULL DROP TABLE #AppLogTempAction6;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction6
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetRebateMemberPremiumData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetRebateMemberPremiumData%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction7')) IS NOT NULL DROP TABLE #AppLogTempAction7;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction7
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetCostShareData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetCostShareData%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction8')) IS NOT NULL DROP TABLE #AppLogTempAction8;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction8
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetMarketSummMemberPremiumData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetMarketSummMemberPremiumData%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction9')) IS NOT NULL DROP TABLE #AppLogTempAction9;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction9
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetBenchmarkCountyLevelSummaries%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetBenchmarkCountyLevelSummaries%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction10')) IS NOT NULL DROP TABLE #AppLogTempAction10;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction10
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetClaimForecastFactors%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetClaimForecastFactors%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction11')) IS NOT NULL DROP TABLE #AppLogTempAction11;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction11
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetPlansInBaseDetails%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetPlansInBaseDetails%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction12')) IS NOT NULL DROP TABLE #AppLogTempAction12;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction12
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: Scenario/BenefitPlanJSONData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: Scenario/BenefitPlanJSONData%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction13')) IS NOT NULL DROP TABLE #AppLogTempAction13;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction13
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetCredibilityAndRiskScore%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetCredibilityAndRiskScore%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03

IF (SELECT  OBJECT_ID ('tempdb..#AppLogTempAction14')) IS NOT NULL DROP TABLE #AppLogTempAction14;


SELECT
'SPV Reprice' AS [UserActions]
,UserId AS [UserID]
, RunDate AS [Run Date]
, 'SPV Reprice' AS [Type]
, CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END AS [Plans]
,CASE WHEN DATALENGTH(plans)=0 THEN 0
ELSE LEN(CASE WHEN CHARINDEX(',',plans) =1 THEN RIGHT(plans, LEN(plans) -1) 
	ELSE plans  END) - LEN(REPLACE(plans, ',', '')) +1 END AS [Plan Count]
, ExecutionTime 
, characterEvenCount
, 0 AS ErrorCount
,' ' AS [ErrorMessage]
INTO #AppLogTempAction14
FROM (
SELECT UserId, RunDate, ExecutionTime , plans, characterEvenCount--, popUpFrom
FROM (
SELECT UserId
, RunDate
, ExecutionTime
,(LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) %2 AS characterEvenCount
 ,CASE WHEN  (LEN(FormattedMesssage) - LEN(REPLACE(FormattedMesssage, '"', '')) ) % 2 = 1 THEN REPLACE(FormattedMesssage, '}', '"}')
ELSE FormattedMesssage END AS FormattedMesssage
FROM (SELECT CASE WHEN  RIGHT(messagejs,1) <> '}' THEN  CONCAT(LEFT(messagejs, DATALENGTH(messagejs) - CHARINDEX('","', REVERSE(messagejs),0)-1-DataLen),'}')
		ELSE messagejs END AS FormattedMesssage
,t01.UserId
,t01.RunDate
,t01.ExecutionTime
FROM (
SELECT REPLACE(REPLACE(RIGHT(a.message, DATALENGTH(a.message) - CHARINDEX('{', a.message )+1),'[',''),']','') messagejs
,CASE WHEN CHARINDEX('"\,"', REVERSE(a.message),0) = 2 THEN 2 ELSE 0 END AS  DataLen
,a.[User] UserId
,CAST(a.Date AS DATETIME) RunDate
,CAST(SUBSTRING (LEFT (ap.message , CHARINDEX('milliseconds', ap.message )-2 ), CHARINDEX('spent', ap.message ) + 5, 10) AS INTEGER) AS ExecutionTime
FROM #AppLogTemp a WITH (NOLOCK)
INNER JOIN #AppLogTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogTemp app WITH (NOLOCK) WHERE a.logid < app.LogID AND app.[Message] LIKE '%Finished executing POST: SingleScenario/GetBPTData%' AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND ap.[Message] LIKE '%Finished executing POST: SingleScenario/GetBPTData%'
WHERE a.[Message] LIKE 'Executing %: Scenario/RepricePlan%'
) AS t01) AS t02 ) t04
CROSS APPLY OPENJSON(FormattedMesssage)
WITH
(
plans NVARCHAR(4000) '$.forecastIdList'
)) AS t03


SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],SUM([ExecutionTime]) AS [ExecutionTime] ,[characterEvenCount],[ErrorCount],[RunEndDate],[ErrorMessage] FROM 
(
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction1 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime] AS [ExecutionTime] ,[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction2 WITH (NOLOCK)
UNION ALL 
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction3 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction4 WITH (NOLOCK)
UNION ALL 
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction5 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction6 WITH (NOLOCK)
UNION ALL 
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction7 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction8 WITH (NOLOCK)
UNION ALL 
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction9 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction10 WITH (NOLOCK)
UNION ALL 
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction11 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction12 WITH (NOLOCK)
UNION ALL 
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction13 WITH (NOLOCK)
UNION ALL
SELECT [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[ExecutionTime],[characterEvenCount],[ErrorCount],[Run Date] AS [RunEndDate],[ErrorMessage] FROM #AppLogTempAction14 WITH (NOLOCK)
) t
GROUP BY [UserActions],[UserID],[Run Date],[Type],[Plans],[Plan Count],[characterEvenCount],[ErrorCount],[RunEndDate],[ErrorMessage]

END
GO
