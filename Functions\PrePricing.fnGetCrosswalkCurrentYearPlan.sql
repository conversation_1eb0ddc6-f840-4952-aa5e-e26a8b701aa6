SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---
-- FUNCTION NAME: [PrePricing].[fnGetCrosswalkCurrentYearPlan]
-- 
-- AUTHOR:  <PERSON><PERSON>
--  
-- CREATED DATE: 2024-Dec-18
--  
-- DESCRIPTION: Returns curent year CPS value
--               
-- PARAMETERS:  
--  Input:  
--      @CrosswalkID int = '',  
--         
--  Output:   
--  
-- RETURNS:   
--    CurrentYearCPS VARCHAR(13)  
--  
-- TABLES:  
--  Read:  
--    PrePricing.PlanCrossWalkDetail  
--	  PrePricing.PlanInfo
  
--  Write:  
--    None  
--  
-- VIEWS: Read: None  
--  
-- STORED PROCS: Executed: None  
--  
-- $HISTORY   
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---  
-- DATE         VERSION    CHANGES MADE                                              DEVELOPER    
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---  
-- 2024-Dec-18     1       Initial version.                                          Surya Murthy
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---  
CREATE FUNCTION [PrePricing].[fnGetCrosswalkCurrentYearPlan]  
(  
	@CrosswalkID INT    
)  
RETURNS VARCHAR(13)  
  
AS  
BEGIN  
    DECLARE @CurrentYearCPS VARCHAR(13)  
    SELECT @CurrentYearCPS =   
    (
		SELECT TOP 1 pl.CPS FROM PrePricing.PlanCrossWalkDetail pcd WITH(NOLOCK)
		JOIN PrePricing.PlanInfo pl WITH(NOLOCK) ON pl.PlanInfoID=pcd.ContributingPlanInfoID
		WHERE pcd.CrossWalkID=@CrosswalkID AND pcd.IsCurrentYearPlan=1
    )
    RETURN @CurrentYearCPS  
END
GO
