SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------    
-- Function NAME: [dbo].[spAppGetCurrentBidYear]    
--    
-- TYPE: SAME    
--    
-- AUTHOR:  Pooja Dahiya  
--    
-- CREATED DATE: 2018-Apr-12    
--    
-- DESCRIPTION: Function responsible for retrieving current bid year    
--    
-- PARAMETERS:    
-- Input:    
-- TABLES:     
-- Read:    
--  LkpIntPlanYear     
-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
--  None    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE				VERSION   CHANGES MADE              DEVELOPER      
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2018-Apr-12		   1      Initial Version           Pooja Dahiya
-- 2018-May-02		   2      Modified table name       Kritika Singh
-- 2018-May-08		   3      Modified table name       Kritika Singh
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE FUNction [dbo].[fnAppGetCurrentBidYear] ()   
RETURNS smallint
AS
BEGIN
DECLARE   @PlanYearID smallint;
    
 
 SELECT @PlanYearID=PlanYear FROM [LkpPlanYear] 
  WHERE IsProjectedYear = 0 and IsBidFiled=1 and IsSAMEditOn=0
 RETURN @PlanYearID
 end
GO
