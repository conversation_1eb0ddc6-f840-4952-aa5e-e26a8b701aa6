SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetBPTPWS4Expenses
--
-- AUTHOR: <PERSON> 
--
-- CREATED DATE: 2009-Apr-07
-- HEADER UPDATED: 2011-Feb-08
--
-- DESCRIPTION: Function responsible for listing values on MA BPT Worksheet 4
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--	Output: 
--
-- TABLES: 
--	Read:
--		CalcActEquivByBenefitCategory
--		CalcFinalPremium
--		CalcPlanAdminBlend
--		LkpExtCMSBidServiceCategory
--		LkpIntAddedBenefitType
--		LkpIntBenefitCategory
--		SavedPlanAddedBenefits
--		SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetSafeDivisionResult
--		fnGetSafeDivisionResultReturnOne
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION     CHANGES MADE														DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2009-Apr-09		1			Initial Version														Keith Galloway
-- 2009-Apr-09		2			Changed to use CalcActEquivWeightedByBenefitCategory				Keith Galloway
-- 2011-Jan-06      3           Revised for 2012 database											Michael Siekerka
-- 2011-Feb-08		4			Removed SavedPlanAssumptions and added PerIntAdminExpenses			Joe Casey
-- 2012-Feb-24		5			Made changes to account for new admin buckets Quality and			Alex Rezmerski
--								TaxesAndFees
-- 2012-Mar-01		6			Added ISNULL( ) to new admin buckets in select list					Alex Rezmerski
-- 2012-May-14		7			Added MSB Admin amount to Direct Admin								Trevor Mahoney
-- 2012-May-14		8			Added MSB Admin amount to all Direct Admin amounts					Alex Rezmerski
-- 2012-May-15		9			Added spab.Ishidden = 0 in the Where Clause for AddedBenefitAdmin	Craig Wright
-- 2012-May-17		10			Added MSB Admin amount to DirectAdminCoveredNet and SuppBen			Trevor Mahoney
-- 2013-Mar-1		11			0379 Added InsurerFee												Mason Roberts
-- 2013-Mar-8		12			Added code replacing Admin Expenses for related party medical		Tim Gao
-- 2013-Mar-15		13			Added HumanaOwnership to calc of admin expenses for buckets			Trevor Mahoney
-- 2013-Mar-25		14			Added MSBQuality to expense											Tim Gao
-- 2013-Apr-02		15			Commented out IsRelatedParty for Quality and Admin					Trevor Mahoney
-- 2013-Apr-02		16			Deleted HumanaOwnership from admin expense calculations				Trevor Mahoney
-- 2013-Apr-17		17			Modified Direct Admin to match WS4									Tim Gao
-- 2014-Mar-18      18			Modified DirectAdmin to exclude 'InsurerFee'						Siliang Hu
-- 2015-Mar-10		19			Applied Safe Division method where direct divion was used			Manish Shukla
-- 2015-Mar-12		20			Added MSB Sales to MarketingSales									Jordan Purdue
-- 2016-Aug-30		21			Added MSB Sales to other white cell calculations					Jordan Purdue
-- 2016-Aug-30		22			Added MSB Quality to ExpensePercentRevenueCoveredNet and			Jordan Purdue
--								ExpensePercentRevenueSuppBen. Also multiplied the numerator for
--								both items by the SuppBenefitPercent
--					23			Now uses Net and Allowed from CalcActEquivByBenefitCategory			Mark Freel
--								it used to use Allowed and Cost Share
-- 2017-Jun-12		24			Removing PerIntRelatedPartiesMedProjected from the code.			Crystal Quirino
-- 2017-Jul-12		25			Combining equations and modifying identified issues with extisting
--								equations; Removed InsurerFee from DirectAdmin, added InsurerFee
--								to sub equations of TotalNonBenefitExpense and
--								ExpensePercentRevenue.												Crystal Quirino
-- 2018-Mar-06		26			Itemized list of Direct Admin for Bid Model Actuarial Summary		Keith Galloway
-- 2018-Sep-04		27			Replacing PerIntAdminExpenses with the new Blended Table			Jordan Purdue
-- 2019-Mar-22      28          Replace Union with Union All in added benefit section               Keith Galloway
-- 2020-Jan-03		29			Adding ESRD Subsidy to Total Required Revenue						Jordan Purdue
-- 2022-May-05		30			MAAUI migration; replaced input variable from @PlanIndex to 
--								@ForecastID; replaced reference for table 
--								CalcActEquivByBenefitCategory from PlanIndex to ForecastID			
--								removed nested queries; function dropped and recreated				Aleksandar Dimitrijevic
-- 2023-Aug-03      31          Add inner variables using @X and With (NOLOCK)                      Khurram Minhas
-- 2024-Jan-29	    32			Increase decimal sizes to prevent precision loss in @Total table	Adam Gilbert
-- 2024-May-17		33			Revert 32 to fix req rev 1 cent off driving rebate issues			Michael Manes
-- 2024-May-18		34			Reverted change #33													Alex Brandt
-- -------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS4Expenses]
(
    @ForecastID INT
)

RETURNS @Results TABLE
	(
		PlanYearID SMALLINT,
		ForecastID INT,
		MarketingSales DECIMAL(17,6),
		MarketingSalesCoveredNet FLOAT,
		MarketingSalesSuppBen FLOAT,
		DirectAdmin FLOAT,
		DirectAdminCoveredNet FLOAT,
		DirectAdminSuppBen FLOAT,
		InDirectAdmin FLOAT,
		InDirectAdminCoveredNet FLOAT,
		InDirectAdminSuppBen FLOAT,
		Reins FLOAT,
		ReinsCoveredNet FLOAT,
		ReinsSuppBen FLOAT,
		Direct_BasicAndSelectableAdmin DECIMAL(17,6),
		Direct_UncollectedPremium FLOAT,
		Direct_UserFee FLOAT,
		Direct_BasicAndSelectableQuality DECIMAL(17,6),
		Direct_TaxesAndFees FLOAT,
		QualityInitiatives DECIMAL(17,6),
		QualityInitiativesCoveredNet FLOAT,
		QualityInitiativesSuppBen FLOAT,
		TaxesAndFees FLOAT,
		TaxesAndFeesCoveredNet FLOAT,
		TaxesAndFeesSuppBen FLOAT,
		TotalNonBenefitExpense FLOAT,
		TotalNonBenefitExpenseCoveredNet FLOAT,
		TotalNonBenefitExpenseAddedService FLOAT,
		TotalNonBenefitExpenseCostShareReduction FLOAT,
		TotalNonBenefitExpenseSuppBen FLOAT,
		Profit FLOAT,
		ProfitCoveredNet FLOAT,
		ProfitAddedService FLOAT,
		ProfitCostShareReduction FLOAT,
		ProfitSuppBen FLOAT,
		TotalReqRev FLOAT,
		ReqRevCoveredNet FLOAT,
		ReqRevAddedService FLOAT,
		ReqRevCostShareReduction FLOAT,
		ReqRevSuppBen FLOAT,
		MedicalPercentRevenue FLOAT,
		MedicalPercentRevenueCoveredNet FLOAT,
		MedicalPercentRevenueSuppBen FLOAT,
		ExpensePercentRevenue FLOAT,
		ExpensePercentRevenueCoveredNet FLOAT,
		ExpensePercentRevenueSuppBen FLOAT,
		ProfitPercentRevenue FLOAT,
		ProfitPercentRevenueCoveredNet FLOAT,
		ProfitPercentRevenueSuppBen FLOAT,
		InsurerFee FLOAT,
		InsurerFeeCoveredNet FLOAT,
		InsurerFeeSuppBen FLOAT
	)
AS
	BEGIN

			   DECLARE @XForecastID INT = @ForecastID

				-- the following query replaces the original PerIntAdminExpenses table				
				DECLARE @PerIntAdminExpenses TABLE
					(
					 PlanYearID SMALLINT,
					 ForecastID INT,
					 MAMarketingAdminPMPM FLOAT,
					 MADirectAdminPMPM FLOAT,
					 MAIndirectAdminPMPM FLOAT,
					 PDMarketingAdminPMPM FLOAT,
					 PDDirectAdminPMPM FLOAT,
					 PDIndirectAdminPMPM FLOAT,
					 LastUpdateByID CHAR(7),
					 LastUpdateDateTime DATETIME,
					 MAQualityAdminPMPM FLOAT,
					 MATaxesAndFeesAdminPMPM FLOAT,
					 PDQualityAdminPMPM FLOAT,
					 PDTaxesAndFeesAdminPMPM FLOAT
					)

				INSERT INTO @PerIntAdminExpenses
				SELECT piae.PlanYearID,				
					piae.ForecastID,							
					MAMarketingAdminPMPM = piae.MAMarketingAdminPMPM,				
					MADirectAdminPMPM = piae.MADirectAdminPMPM,				
					MAIndirectAdminPMPM = piae.MAIndirectAdminPMPM,				
					piae.PDMarketingAdminPMPM,				
					piae.PDDirectAdminPMPM,				
					piae.PDIndirectAdminPMPM,								
					piae.LastUpdateByID,				
					piae.LastUpdateDateTime,				
					piae.MAQualityAdminPMPM,				
					piae.MATaxesAndFeesAdminPMPM,				
					piae.PDQualityAdminPMPM,				
					piae.PDTaxesAndFeesAdminPMPM				
				FROM dbo.CalcPlanAdminBlend piae	With (NOLOCK)				
				INNER JOIN dbo.SavedPlanHeader ph	With (NOLOCK)			
					ON piae.ForecastID = ph.ForecastID
				   AND piae.PlanYearID = ph.PlanYearID				
				WHERE ph.ForecastID = @XForecastID

				--------------------------------------
				--Added Benefits
				DECLARE @AddedBenefits TABLE
					(
					 ForecastID INT,
					 MSBQuality FLOAT,
					 MSBAdmin FLOAT,
					 MSBSales FLOAT
					)

				INSERT INTO @AddedBenefits
				SELECT 					
					ForecastID, 				
					SUM(AddedBenefitQuality) MSBQuality,				
					SUM(AddedBenefitAdmin) MSBAdmin,				
					SUM(AddedBenefitSales) MSBSales				
				 FROM dbo.LkpIntAddedBenefitExpenseDetail added	 With (NOLOCK)			
				 INNER JOIN dbo.SavedPlanAddedBenefits spab	 With (NOLOCK)				
					ON spab.AddedBenefitTypeID = added.AddedBenefitTypeID				
				 WHERE ForecastID = @XForecastID			
					AND spab.Ishidden = 0				
				 GROUP BY ForecastID

				 --------------------------------------------------------
					--Actuarial Equivalence
					DECLARE @ActEquiv TABLE
						(
						 ForecastID INT,
						 ServiceCategoryCode VARCHAR(2),
						 Allowed FLOAT,
						 CostShare FLOAT,
						 Net FLOAT,
						 PercentCoveredAllowed FLOAT,
						 PercentCoveredCostShare FLOAT,
						 FFSActEquivCostShare FLOAT,
						 PlanCostShareMedicareCovered FLOAT,
						 MedicareCoveredAllowed FLOAT,
						 MedicareCoveredCostShare FLOAT,
						 MedicareCoveredNet FLOAT,
						 AddedBenefitNet FLOAT,
						 CostShareReduction FLOAT,
						 SuppBenefitTotal FLOAT
						)

					INSERT INTO @ActEquiv
					SELECT 				
						calcAE.ForecastID,			
						bsc.ServiceCategoryCode,			
						Allowed = SUM(calcAE.Allowed),			
						CostShare =SUM(calcAE.Allowed)- SUM(calcAE.nET),			
						Net = SUM(calcAE.nET),			
						PercentCoveredAllowed =			
							dbo.fnGetSafeDivisionResultReturnOne(SUM(calcAE.PercentCoveredAllowed * calcAE.Allowed), SUM(calcAE.Allowed)),			
						PercentCoveredCostShare =			
							dbo.fnGetSafeDivisionResultReturnOne(SUM(calcAE.PercentCoveredCostShare * calcAE.CostShare), SUM(calcAE.CostShare)),			
						FFSActEquivCostShare =			
							dbo.fnGetSafeDivisionResult(SUM(calcAE.FFSActEquivCostShare * calcAE.CostShare), SUM(calcAE.CostShare)),			
						PlanCostShareMedicareCovered = SUM(calcAE.PercentCoveredCostShare * calcAE.CostShare),			
						MedicareCoveredAllowed = SUM(calcAE.PercentCoveredAllowed * calcAE.Allowed),			
						MedicareCoveredCostShare = SUM(calcAE.PercentCoveredAllowed * calcAE.Allowed) *			
							dbo.fnGetSafeDivisionResult(SUM(calcAE.FFSActEquivCostShare * calcAE.CostShare), SUM(calcAE.CostShare)),			
						MedicareCoveredNet = SUM(calcAE.CoveredActEquivNet),			
						AddedBenefitNet = SUM(calcAE.AddedBenefitNet),			
						CostShareReduction = SUM(calcAE.CostShareReduction),			
						SuppBenefitTotal = SUM(calcAE.SupplementalBenefitTotal)			
					FROM dbo.CalcActEquivByBenefitCategory calcAE	With (NOLOCK)			
					INNER JOIN dbo.LkpIntBenefitCategory bc		With (NOLOCK)		
						ON	calcAE.BenefitCategoryID = bc.BenefitCategoryID		
					INNER JOIN dbo.LkpExtCMSBidServiceCategory bsc		With (NOLOCK)		
						ON	bsc.BidServiceCategoryID = bc.BidServiceCatID
					WHERE calcAE.ForecastID = @XForecastID				
						AND calcAE.DualEligibleTypeID = 2			
					GROUP BY				
						calcAE.ForecastID,			
						bsc.ServiceCategoryCode,			
						calcAE.Allowed
					----------------------------------------------------------------------------------------------------------------------------			
						UNION ALL--UNION   
					----------------------------------------------------------------------------------------------------------------------------							
					--Second part - Added Benefits				
					SELECT				
						spab.ForecastID,			
						bsc.ServiceCategoryCode,			
						AddedBenefitAllowed = SUM(ISNULL(spab.INAddedBenefitAllowed,0) + 			
							CASE WHEN sph.PlanTypeID = 1 THEN 0		
								ELSE ISNULL(spab.OONAddedBenefitAllowed,0)	
							END),		
						AddedBenefitCostShare = SUM(ISNULL(spab.INAddedBenefitCostShare,0) +			
							CASE WHEN sph.PlanTypeID = 1 THEN 0		
								ELSE ISNULL(spab.OONAddedBenefitCostShare,0)	
							END),		
						AddedBenefitNet = SUM(ISNULL(spab.INAddedBenefitAllowed,0) +			
							CASE  WHEN sph.PlanTypeID = 1 THEN 0			
								ELSE ISNULL(spab.OONAddedBenefitAllowed,0)	
							END		
							- ISNULL(spab.INAddedBenefitCostShare,0) +		
								CASE WHEN sph.PlanTypeID = 1 THEN 0		
									ELSE ISNULL(spab.OONAddedBenefitCostShare,0)
								END),	
						PercentCoveredAllowed = 0,			
						PercentCoveredCostShare = 0,			
						FFSActEquivCostShare = 0,			
						PlanCostShareMedicareCovered = 0,			
						MedicareCoveredAllowed = 0,			
						MedicareCoveredCostShare = 0,			
						MedicareCoveredNet = 0,			
						NetAddedServices = SUM(ISNULL(spab.INAddedBenefitAllowed,0) +			
														CASE WHEN sph.PlanTypeID = 1 THEN 0			
															ELSE ISNULL(spab.OONAddedBenefitAllowed,0)	
														END		
							- ISNULL(spab.INAddedBenefitCostShare,0) +					
										CASE WHEN sph.PlanTypeID = 1 THEN 0					
											ELSE ISNULL(spab.OONAddedBenefitCostShare,0)
										END),	
						CostShareReduction = 0,			
						SuppBenefitTotal = SUM(ISNULL(spab.INAddedBenefitAllowed,0) +			
														CASE WHEN sph.PlanTypeID = 1 THEN 0			
															ELSE ISNULL(spab.OONAddedBenefitAllowed,0)
														END	
							- ISNULL(spab.INAddedBenefitCostShare,0) +		
										CASE WHEN sph.PlanTypeID = 1 THEN 0		
											ELSE ISNULL(spab.OONAddedBenefitCostShare,0)
										END)	
					FROM dbo.SavedPlanAddedBenefits spab	With (NOLOCK)			
					INNER JOIN dbo.SavedPlanHeader sph	With (NOLOCK)			
						ON	sph.ForecastID = spab.ForecastID		
					INNER JOIN dbo.LkpIntAddedBenefitType  abt	With (NOLOCK)		
						ON	spab.AddedBenefitTypeID = abt.AddedBenefitTypeID		
					INNER JOIN dbo.LkpExtCMSBidServiceCategory bsc	With (NOLOCK)			
						ON spab.BidServiceCatID = bsc.BidServiceCategoryID			
					WHERE spab.ForecastID = @XForecastID			
					  AND spab.IsHidden = 0			
					GROUP BY spab.ForecastID,			
							 bsc.ServiceCategoryCode,			
							 bsc.UtilType,			
							 spab.INAddedBenefitDescription,			
							 spab.INAddedBenefitUtilization,			
							 spab.INAddedBenefitCostShare,			
							 spab.OONAddedBenefitCostShare			

				 ---------------------------------------------------------------------
				 --Actuarial Equivalance totals
				 DECLARE @ActEquivTotals TABLE
					(
					 ForecastID INT,
					 Net FLOAT,
					 MedicareCoveredAllowed FLOAT,
					 MedicareCoveredCostShare FLOAT,
					 MedicareCoveredNet FLOAT,
					 SuppBenefitTotal FLOAT,
					 AddedServicesPercent FLOAT,
					 CostShareReductionPercent FLOAT,
					 SuppBenefitPercent FLOAT
					)

				INSERT INTO @ActEquivTotals
				SELECT					
					ForecastID,				
					Net = SUM(Net),				
					MedicareCoveredAllowed = SUM(MedicareCoveredAllowed),				
					MedicareCoveredCostShare = SUM(MedicareCoveredCostShare),				
					MedicareCoveredNet = SUM(MedicareCoveredNet),				
					SuppBenefitTotal = SUM(SuppBenefitTotal),				
					AddedServicesPercent = SUM(AddedBenefitNet) / SUM(SuppBenefitTotal), -- for calculation, not for display				
					CostShareReductionPercent = SUM(CostShareReduction) / SUM(SuppBenefitTotal), -- for calculation, not for display				
					SuppBenefitPercent = SUM(SuppBenefitTotal) / SUM(Net) -- for calculation, not for display				
				FROM @ActEquiv AE				
				GROUP BY					
					ForecastID

			--------------------------------------------
			--totals
			DECLARE @TOTAL TABLE
				(
				 ForecastID INT,
				 SuppBenefitPercent FLOAT,
				 SuppBenefitTotal FLOAT,
				 AddedServicesPercent FLOAT,
				 CostShareReductionPercent FLOAT,
				 MarketingSales DECIMAL(17,2),
				 Direct_TotalAdmin FLOAT,
				 Direct_BasicAndSelectableAdmin DECIMAL(17,6),
				 Direct_BasicAndSelectableQuality DECIMAL(17,6),
				 Direct_UncollectablePremium FLOAT,
				 Direct_UserFee FLOAT,
				 Direct_TaxesAndFees FLOAT,
				 InDirectAdmin FLOAT,
				 Reins FLOAT,
				 TotalNonBenefitExpense FLOAT,
				 Profit FLOAT,
				 TotalReqRev FLOAT,
				 Net FLOAT,
				 MedicareCoveredNet FLOAT,
				 InsurerFee FLOAT,
				 ESRDSubsidy FLOAT
				)

			INSERT INTO @TOTAL
			SELECT	cfp.ForecastID,				
					SuppBenefitPercent = SuppBenefitPercent,
					SuppBenefitTotal = SuppBenefitTotal,				
					AddedServicesPercent = AddedServicesPercent,				
					CostShareReductionPercent = CostShareReductionPercent,				

					MarketingSales = pae.MAMarketingAdminPMPM + ISNULL(added.MSBSales,0),				

					Direct_TotalAdmin = pae.MADirectAdminPMPM + cfp.UncollectedPremium + cfp.UserFee + ISNULL(added.MSBAdmin,0) 
										+ ISNULL(pae.MAQualityAdminPMPM,0) + ISNULL(added.MSBQuality,0) + ISNULL(pae.MATaxesAndFeesAdminPMPM,0),	
					Direct_BasicAndSelectableAdmin = pae.MADirectAdminPMPM + ISNULL(added.MSBAdmin,0),
					Direct_BasicAndSelectableQuality = ISNULL(pae.MAQualityAdminPMPM,0) + ISNULL(added.MSBQuality,0),
					Direct_UncollectablePremium = cfp.UncollectedPremium,
					Direct_UserFee = cfp.UserFee,
					Direct_TaxesAndFees = ISNULL(pae.MATaxesAndFeesAdminPMPM,0),

					InDirectAdmin = pae.MAIndirectAdminPMPM,				

					cfp.Reins,				

					TotalNonBenefitExpense = cfp.Expenses + cfp.UncollectedPremium + cfp.UserFee + cfp.Reins,						

					cfp.Profit,				

					cfp.TotalReqRev,				

					Net,
					MedicareCoveredNet = MedicareCoveredNet,

					InsurerFee = ISNULL(cfp.InsurerFee,0),
					ESRDSubsidy = ISNULL(cpes.TotalESRDSubsidy,0)						
			FROM dbo.CalcFinalPremium cfp	With (NOLOCK)					
			INNER JOIN dbo.SavedPlanHeader sph	With (NOLOCK)					
				ON	cfp.ForecastID = sph.ForecastID
			LEFT JOIN dbo.CalcPlanESRDSubsidy cpes  With (NOLOCK)
				ON cpes.ForecastID = cfp.ForecastID
			INNER JOIN @PerIntAdminExpenses pae --- PerIntAdminExpenses is replaced by the above query					
				ON sph.ForecastID = pae.ForecastID
				AND sph.PlanYearID = pae.PlanYearID					
			LEFT JOIN @AddedBenefits added					
				ON added.ForecastID = sph.ForecastID
			INNER JOIN @ActEquivTotals ActEquivTotals					
				ON cfp.ForecastID = ActEquivTotals.ForecastID
			WHERE cfp.ForecastID = @XForecastID						

		----------------------------------------------------------------------------------


		INSERT INTO @Results
			SELECT							
			PlanYearID = dbo.fnGetBidYear(),    						
			total.ForecastID,						

			-- Section II C Row v1						
			total.MarketingSales,						
			MarketingSalesCoveredNet = total.MarketingSales * (1-SuppBenefitPercent),						
			MarketingSalesSuppBen = total.MarketingSales * SuppBenefitPercent,						

			-- Section II C Row v2						
			DirectAdmin = total.Direct_TotalAdmin,						
			DirectAdminCoveredNet = total.Direct_TotalAdmin * (1 - SuppBenefitPercent),						
			DirectAdminSuppBen = total.Direct_TotalAdmin * SuppBenefitPercent,						

			-- Section II C Row v3						
			total.InDirectAdmin,						
			InDirectAdminCoveredNet = total.InDirectAdmin * (1 - SuppBenefitPercent),						
			InDirectAdminSuppBen = total.InDirectAdmin * SuppBenefitPercent,						

			-- Section II C Row v4						
			total.Reins,		-- this will always be zero for our plans				
			ReinsCoveredNet = total.Reins * (1 - SuppBenefitPercent),						
			ReinsSuppBen = total.Reins * SuppBenefitPercent,						


			-- Itemization of Direct Admin (Shown on Actuarial Summary Tab of Bid Model UI)
			Direct_BasicAndSelectableAdmin = Total.Direct_BasicAndSelectableAdmin,
			Direct_UncollectedPremium = Total.Direct_UncollectablePremium,
			Direct_UserFee = Total.Direct_UserFee,
			Direct_BasicAndSelectableQuality = Total.Direct_BasicAndSelectableQuality,
			Direct_TaxesAndFees = Total.Direct_TaxesAndFees,

			-- Values called in fnAppGetMABPTWS5
			QualityInitiatives = total.Direct_BasicAndSelectableQuality,						
			QualityInitiativesCoveredNet = total.Direct_BasicAndSelectableQuality * (1- SuppBenefitPercent),						
			QualityInitiativesSuppBen = total.Direct_BasicAndSelectableQuality * SuppBenefitPercent,						

			-- Values called in fnAppGetMABPTWS5
			TaxesAndFees = total.Direct_TaxesAndFees,						
			TaxesAndFeesCoveredNet = total.Direct_TaxesAndFees * (1-SuppBenefitPercent),						
			TaxesAndFeesSuppBen = total.Direct_TaxesAndFees * SuppBenefitPercent,

			-- Section II C Row v6					
			total.TotalNonBenefitExpense,					
			TotalNonBenefitExpenseCoveredNet = (1 - SuppBenefitPercent) * (total.Reins + ISNULL(total.InsurerFee, 0) 
											   + total.Direct_TotalAdmin + total.InDirectAdmin + total.MarketingSales),					
			TotalNonBenefitExpenseAddedService = AddedServicesPercent * SuppBenefitPercent * (total.Reins + ISNULL(total.InsurerFee, 0) 
												 + total.Direct_TotalAdmin + total.InDirectAdmin + total.MarketingSales),					
			TotalNonBenefitExpenseCostShareReduction = CostShareReductionPercent * SuppBenefitPercent * (total.Reins + ISNULL(total.InsurerFee, 0) 
													   + total.Direct_TotalAdmin + total.InDirectAdmin + total.MarketingSales),					
			TotalNonBenefitExpenseSuppBen = SuppBenefitPercent * (total.Reins + ISNULL(total.InsurerFee, 0) + total.Direct_TotalAdmin + total.IndirectAdmin + total.MarketingSales),					

			-- Section II C Row w					
			total.Profit,
			ProfitCoveredNet = total.Profit * (1- SuppBenefitPercent),					
			ProfitAddedService = total.Profit * SuppBenefitPercent * AddedServicesPercent,					
			ProfitCostShareReduction = total.Profit * SuppBenefitPercent * CostShareReductionPercent,					
			ProfitSuppBen = total.Profit * SuppBenefitPercent,						

			-- Section II C Row x					
			TotalReqRev = total.TotalReqRev + ISNULL(total.ESRDSubsidy,0),					
			ReqRevCoveredNet = total.TotalReqRev * (1-SuppBenefitPercent),					
			ReqRevAddedService = (total.TotalReqRev * SuppBenefitPercent * AddedServicesPercent) + ISNULL(total.ESRDSubsidy,0),					
			ReqRevCostShareReduction = total.TotalReqRev * SuppBenefitPercent * CostShareReductionPercent,					
			ReqRevSuppBen = (total.TotalReqRev * SuppBenefitPercent) + ISNULL(total.ESRDSubsidy,0),				

			-- Section II C Row y1					
			MedicalPercentRevenue = dbo.fnGetSafeDivisionResult(total.Net,total.TotalReqRev),				
			MedicalPercentRevenueCoveredNet = dbo.fnGetSafeDivisionResult(total.MedicareCoveredNet,(total.TotalReqRev*(1-SuppBenefitPercent))),				
			MedicalPercentRevenueSuppBen = dbo.fnGetSafeDivisionResult(total.SuppBenefitTotal, (total.TotalReqRev*SuppBenefitPercent)),				

			-- Section II C Row y2					
			ExpensePercentRevenue = dbo.fnGetSafeDivisionResult(total.TotalNonBenefitExpense, total.TotalReqRev),
			ExpensePercentRevenueCoveredNet = dbo.fnGetSafeDivisionResult((1 - SuppBenefitPercent) * (total.Reins + ISNULL(total.InsurerFee, 0) 
											  + total.Direct_TotalAdmin + total.InDirectAdmin + total.MarketingSales), (1 - SuppBenefitPercent) * total.TotalReqRev),
			ExpensePercentRevenueSuppBen = dbo.fnGetSafeDivisionResult(SuppBenefitPercent * (total.Reins + ISNULL(total.InsurerFee, 0) 
										   + total.Direct_TotalAdmin + total.InDirectAdmin + total.MarketingSales), SuppBenefitPercent * total.TotalReqRev),

			-- Section II C Row y3
			ProfitPercentRevenue = dbo.fnGetSafeDivisionResult(total.Profit, total.TotalReqRev),
			ProfitPercentRevenueCoveredNet = dbo.fnGetSafeDivisionResult((1 - SuppBenefitPercent) * total.Profit, (1 - SuppBenefitPercent) * total.TotalReqRev),
			ProfitPercentRevenueSuppBen = dbo.fnGetSafeDivisionResult(SuppBenefitPercent * total.Profit, SuppBenefitPercent * total.TotalReqRev),

			-- Section II C Row v5					
			total.InsurerFee,
			InsurerFeeCoveredNet = total.InsurerFee * (1 - SuppBenefitPercent),
			InsurerFeeSuppBen = total.InsurerFee * SuppBenefitPercent

		FROM @TOTAL total							

	RETURN
	END
GO