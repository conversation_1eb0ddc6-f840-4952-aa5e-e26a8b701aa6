SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- PROCEDURE NAME: spDashboardReportRiskScoresImportPBI2

-- DESCRIPTION: This SP returns extract for "Upload risk score file to Bid Model" user action from AppLogs table 
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      [dbo].[DashboardReportAppLogs]
--		
-- Example 
-- Exec [dbo].[spDashboardReportRiskScoresImportPBI2] 19583136, '2023-09-24 11:24:03.200'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-24			1			Initial Version                                                                         Sheetal Patil
-- 2024-Jun-24          2           Replacing /Scenario with /BatchImport based on the new path of batch Page               Chaitanya Durga
---------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROC [dbo].[spDashboardReportRiskScoresImportPBI2]
 (@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


 DECLARE @XLastSuccessfullRunLogid  INT = @LastSuccessfullRunLogid

IF (SELECT  OBJECT_ID ('tempdb..#AppLogRiskImpTemp')) IS NOT NULL DROP TABLE #AppLogRiskImpTemp;

SELECT [LogID],[User],[Date],[q].[Message],q.Thread AS Thread, DATEDIFF_BIG(MILLISECOND, pDataDate, [Date]) Executiontime,pDataDate AS StartDate
INTO #AppLogRiskImpTemp FROM    (
         SELECT  *,
                LAG([Date]) OVER (PARTITION BY [User] ORDER BY [Date]) pDataDate
        FROM    dbo.DashboardReportAppLogs WITH (NOLOCK)  WHERE  
((([message] LIKE '%ActAdjRepRS%' AND [message] LIKE '%"isExecuted"%') OR Message LIKE '%ImportBatchData%')
       AND  [LogID] > @XLastSuccessfullRunLogid
	   )
        ) q
WHERE   pDataDate IS NOT NULL

IF (SELECT  OBJECT_ID ('tempdb..#AppLogRiskImpTemp1')) IS NOT NULL DROP TABLE #AppLogRiskImpTemp1;

	SELECT [LogID]
	,[User]
	,[Date]
	,[Message]
	,thread 
	,ExecutionTime
	,pDataDate AS StartDate
INTO #AppLogRiskImpTemp1	FROM 	
(
SELECT [LogID],[User],[Date],[q].[Message],q.Thread AS Thread, DATEDIFF_BIG(MILLISECOND, pDataDate, [Date]) Executiontime,pDataDate
FROM    (
         SELECT  *,
                LAG([Date]) OVER (PARTITION BY [User] ORDER BY [Date]) pDataDate
        FROM    dbo.DashboardReportAppLogs WITH (NOLOCK)  WHERE  
(([message] LIKE '%ActAdjRepRS%' AND [message] LIKE '%"isExecuted"%')
       AND  [LogID] > @XLastSuccessfullRunLogid
	   )
        ) q
WHERE   pDataDate IS NOT NULL) AS t5 WHERE [message] LIKE '%"name":"ActAdjRepRS","isExecuted":"1"%' 

IF (SELECT  OBJECT_ID ('tempdb..#AppLogRiskImpTemp2')) IS NOT NULL DROP TABLE #AppLogRiskImpTemp2;


	 SELECT a.LogID,a.[User],a.[Date],a.[Message],a.StartDate,ap.Executiontime AS PostExectime,ap.StartDate AS PostExedate,ap.[date] AS enddate 
INTO #AppLogRiskImpTemp2	 FROM #AppLogRiskImpTemp a WITH (NOLOCK)
INNER JOIN #AppLogRiskImpTemp ap WITH (NOLOCK)
ON a.logid < ap.LogID
AND ap.LogID = (SELECT MIN(LogID) FROM #AppLogRiskImpTemp app WITH (NOLOCK) WHERE a.logid < app.LogID 
AND (app.[Message] LIKE '%Finished executing POST: BatchImport/ImportBatchData%') AND a.[User] = app.[User])
AND a.[User] = ap.[User]
AND (ap.[Message] LIKE '%Finished executing POST: BatchImport/ImportBatchData%')
WHERE a.[Message]  LIKE '%"name":"ActAdjRepRS","isExecuted":"1"%'


SELECT 
'Upload risk score file to Bid Model' AS 'UserActions'
,[User] AS 'UserID'
,StartDate AS [RunStartDate]
, 'Upload risk score file to Bid Model' AS 'Type'
, 'NULL' AS 'Plans'
, 0 AS 'Plan Count'
, ExecutionTime
, 0 AS characterEvenCount
, 0 AS ErrorCount
, enddate AS [RunEndDate]
,' ' AS [ErrorMessage]
FROM
(SELECT temp3.LogID,temp3.[User],temp4.StartDate,temp3.Message,temp3.enddate,SUM(temp3.PostExectime+temp4.Executiontime) AS ExecutionTime FROM #AppLogRiskImpTemp1 temp4
JOIN 
#AppLogRiskImpTemp2 temp3 ON temp3.LogID=temp4.LogID
GROUP BY temp3.LogID,temp3.[User],temp4.StartDate,temp3.Message,temp3.enddate) AS g

END
GO
