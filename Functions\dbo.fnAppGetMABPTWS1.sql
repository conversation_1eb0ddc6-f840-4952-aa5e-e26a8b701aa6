SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:	fnAppGetMABPTWS1
--
-- CREATOR:			Brian Lake
--
-- CREATED DATE:	2008-JUL-31
--
-- DESCRIPTION:		Function responsible for building data that is required to complete WS 1.  The
--					data is broken out by BenefitCategoryID, to obtain the true WS 1 see 
--					fnAppGetMABPTWS1Summary.  The fnAppGetMABPTWS1Digits function is used to 
--					determine the number of significant digits needed for the AvgCost column.
--		
-- PARAMETERS: 
--  Input  :		@ForecastID
--  Output :		@Results
--
-- TABLES : 
--	Read :			CalcPlanExperienceByBenefitCat
--					CalcPlanExperienceByBenefitCatNonSQS
--					CalcProjectionFactors
--					CalcSQSFactors
--					LkpIntBenefitCategory
--					LkpExtCMSBidServiceCategory
--					SavedPlanDetail
--  Write:			NONE
--
-- VIEWS: 
--	Read:			vwLkpPartBOnlyCoveredPercent
--
-- FUNCTIONS:		fnAppGetMABPTWS1Digits
--					fnGetClaimFactors
--					fnGetRelatedPartiesAdjustment
--					fnGetSafeDivisionResultReturnOne
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2008-JUL-31      1           Initial Version                                                     Brian Lake
-- 2009-FEB-23      2           Included 2010 pricing section, removed @UserID                      Brian Lake
-- 2009-MAR-17      3           Data Types                                                          Sandy Ellis
-- 2009-MAR-21      4           Dual Eligible Type ID = 3                                           Sandy Ellis
-- 2009-MAR-21      5           Fix an issue with cost trend not accumulating all                   Sandy Ellis
-- 2009-APR-04      6           Added BenefitChangeProduct field and 2010 added benefits and        Sandy Ellis
--                                dual population adjustment
-- 2009-APR-05      7           Added CostUtilizationComponent to deal with zero util               Sandy Ellis
-- 2009-APR-06      8           Dual population factor at the benefit category level                Sandy Ellis
-- 2009-MAY-15      9           Removed call to SavedPlanTypeCostAndUseDetail for 2010              Brian Lake
-- 2009-DEC-02      10          Added 2011 Statement and changed join to reflect table changes      Nick Skeen
-- 2009-DEC-28      11          Added BaseNetPMPM Column                                            Nick Skeen
-- 2010-JAN-11	    12          Added new column for Inflation column                               Nick Skeen
-- 2010-FEB-06	    13          Changed CostTrendProduct to refer to column 6, added column         Casey Sanders
--                                6 to TotalProduct, and added DualPopAvgCost and DualPopUtil
--                                factors where noted.					
-- 2010-FEB-25	    14          Added Mandatory Supplemental Benefits                               Jake Gaecke
-- 2010-MAR-13	    15          Replaced fnGetWS1MSBs with fn*GetBaseMSBs and added                 Casey Sanders
--                                parameter for MARatingOptionID = 1.  Also, replaced
--                                CalcPlanExperienceByBenefitCat with 
--                                fnGetCalcPlanExperienceByBenefitCat(@PlanYearID, @ForecastID, 
--                                @PlanVersion)
-- 2010-MAR-18	    16          Removed reference to fnGetCalcPlanExperienceByBenefitCat            Casey Sanders
-- 2010-JUN-16      17          Revised for 2012 database                                           Jake Gaecke
-- 2010-AUG-23      18          Removed PlanVersion                                                 Jake Gaecke
-- 2010-SEP-23      19          Updated references from fnGetClaimFactor (scalar) to                Jake Gaecke
--                                fnGetClaimFactors (table)
-- 2010-SEP-29      20          Changed reference from LkpIntMarketHeader to SavedMarketHeader      Michael Siekerka
-- 2011-JAN-05      21          Changed DETypeID from 3 to 2                                        Michael Siekerka
-- 2011-JAN-06      22          Removed unnecessary joins to SavedPlanHeader and SavedMarketHeader  Joe Casey
-- 2011-JAN-21      23          Changed all CASE's ELSE from 1 to 0                                 Michael Siekerka
-- 2011-FEB-03      24          Reversed change 23 and changed SUM's to MAX                         Michael Siekerka
-- 2011-FEB-07      25          Changed Exp(Sum(Log to actual factors                               Michael Siekerka
-- 2011-FEB-25      26          UtilizationTrendProduct was pulling Cost Trend, now pulls Util      Nate Jacoby
-- 2011-FEB-28      27          Removed MSB's from this query, as they need to be handled           Nate Jacoby
--                                differently for rounding issues
-- 2011-MAR-30      28          Changed CostTrendProduct to ProviderPaymentProduct. In              Joe Casey
--                                fnAppGetMABPTWS1Summary, CostTrend will be backed into,
--                                while Provider will be directly calculated.
-- 2012-MAR-26      29          Added functionality to include Utilizers                            Alex Rezmerski
-- 2012-MAY-16      30          Added CASE statement for BaseNetPMPM for PartBOnly plan             Tim Gao
-- 2012-MAY-21      31          Updated Utilizers statement to pull in on SavedPlanCUSummary        Craichael
-- 2012-MAY-21      32          Updated Utilizers statement to account for roll up                  Trevor Mahoney
-- 2012-OCT-10      33          Added IF Statement to account for Group - *More changes to Come     Mike Deren
-- 2012-OCT-24      34          We have updated UtilizationTrendProduct, ClaimFactor                Mike Deren
-- 2012-NOV-30      35          Kept the old method for ForecastID = 9999 only                       Mike Deren
-- 2013-OCT-04      36          Included Join on Segment ID                                         Anubhav Mishra
-- 2014-MAY-05      37          Modified for 2015 Part B Only Logic, now automated                  Nick Koesters
-- 2015-JAN-27      38          UtilizationTrendProduct, BenefitChangeProduct, PopulationChange     Chris McAuley
--                                Product and OtherFactorProduct incorporate BaseUnits instead
--                                of BaseAllowed based on INUnits or OONUnits	
-- 2015-APR-20      39          Commented out in 2 places the Inner Join on SavedCUUtilizers and    Jordan Purdue
--                                SavedCUPlanMap. It is no longer needed since SavedCUUtilizers
--                                has CostAndUseID now. 			
-- 2015-MAY-12      40          Added nolock for fetched table                                      Manisha Tyagi
-- 2017-JAN-31      41          Changing Inner Joins to Left Joins for SavedCUUtilizers             Jordan Purdue
-- 2017-OCT-12      42          Removed partial SQS factor                                          Chris Fleming & Jordan Purdue
-- 2017-DEC-19      43          Removed group and cleaned up procedure                              Chris Fleming
--                                Added RP and MER to TotalProduct
-- 2018-FEB-08      44          Adding Experience SQS to Provider Payment Product                   Jordan Purdue
-- 2018-MAY-02      45          Removing double count of MER                                        Jordan Purdue
-- 2020-OCT-13      46          Backend Alignment and Restructuring Project                         Keith Galloway
-- 2021-NOV-11		47			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's	        Franklin Fu
-- 2022-OCT-19		48			Remove Dual Population adjustment and legacy trend fields		    Joe Wang
-- 2023-Aug-03		49			Added Internal parameter									        Sheetal Patil
-- 2024-OCT-07		50			Cost Share Basis: add handling for IsIncludeInCostShareBasis flag
--									in the base data tables											Jake Lewis
-- 2025-Jan-29      51          Edits to OtherFactorProduct for bid audit remediation               Zoey Glenn
-- 2025-JAN-30		52			Include new SurplusDeficit field for BPT requirements				Michael Manes
-- 2025-MAR-03		53			Table alias name updated				                            Kumar Jalendran
-- 2025-MAR-24		54			updated CPNS.SurplusDeficit	with MAX instead of SUM 			    Kumar Jalendran
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnAppGetMABPTWS1]
    (@ForecastID INT)

RETURNS @Results TABLE
    (ForecastID               INT
    ,BenefitCategoryID        INT
    ,BidServiceCategoryID     INT
    ,ServiceCategoryCode      VARCHAR(4)
    ,UtilType                 CHAR(1)
    ,BaseUnits                DECIMAL(30, 18)
    ,AvgCost                  DECIMAL(30, 18)
    ,BaseAllowed              DECIMAL(30, 18)
    ,BaseNetPMPM              DECIMAL(30, 18)
    ,UtilizationTrendProduct  DECIMAL(30, 18)
    ,BenefitChangeProduct     DECIMAL(30, 18)
    ,PopulationChangeProduct  DECIMAL(30, 18)
    ,OtherFactorProduct       DECIMAL(30, 18)
    ,TotalProduct             DECIMAL(30, 18)
    ,ProviderPaymentProduct   DECIMAL(30, 18)
    ,CostUtilizationComponent DECIMAL(30, 18)
    ,UtilAdditiveAdjustment   DECIMAL(10, 6)
    ,PMPMAdditiveAdjustment   DECIMAL(10, 6)
	,SurplusDeficit			  DECIMAL(14, 6))

AS
    BEGIN

        DECLARE @XForecastID INT = @ForecastID;

        DECLARE @CalcPlanExperienceByBenefitCat TABLE
            (ForecastID         INT
            ,MARatingOptionID   TINYINT
            ,BenefitCategoryID  INT
            ,DualEligibleTypeID TINYINT
            ,INPaid             DECIMAL(14, 6)
            ,INAllowed          DECIMAL(14, 6)
            ,INUnits            DECIMAL(14, 6)
            ,OONPaid            DECIMAL(14, 6)
            ,OONAllowed         DECIMAL(14, 6)
            ,OONUnits           DECIMAL(14, 6));

        DECLARE @CalcPlanExperienceByBenefitCatNonSQS TABLE
            (ForecastID         INT
            ,MARatingOptionID   TINYINT
            ,BenefitCategoryID  INT
            ,DualEligibleTypeID TINYINT
            ,INPaid             DECIMAL(14, 6)
            ,INAllowed          DECIMAL(14, 6)
            ,INUnits            DECIMAL(14, 6)
            ,OONPaid            DECIMAL(14, 6)
            ,OONAllowed         DECIMAL(14, 6)
            ,OONUnits           DECIMAL(14, 6)
			,SurplusDeficit		DECIMAL(14, 6));

        INSERT INTO @CalcPlanExperienceByBenefitCat
            (ForecastID
            ,MARatingOptionID
            ,BenefitCategoryID
            ,DualEligibleTypeID
            ,INPaid
            ,INAllowed
            ,INUnits
            ,OONPaid
            ,OONAllowed
            ,OONUnits)
        SELECT      ForecastID
                   ,MARatingOptionID
                   ,BenefitCategoryID
                   ,DualEligibleTypeID
                   ,SUM (INPaid) AS INPaid
                   ,SUM (INAllowed) AS INAllowed
                   ,SUM (INUnits) AS INUnits
                   ,SUM (OONPaid) AS OONPaid
                   ,SUM (OONAllowed) AS OONAllowed
                   ,SUM (OONUnits) AS OONUnits
        FROM        dbo.CalcPlanExperienceByBenefitCat WITH (NOLOCK)
        WHERE       ForecastID = @XForecastID
        GROUP BY    ForecastID
                   ,MARatingOptionID
                   ,BenefitCategoryID
                   ,DualEligibleTypeID;

        INSERT INTO @CalcPlanExperienceByBenefitCatNonSQS
            (ForecastID
            ,MARatingOptionID
            ,BenefitCategoryID
            ,DualEligibleTypeID
            ,INPaid
            ,INAllowed
            ,INUnits
            ,OONPaid
            ,OONAllowed
            ,OONUnits
			,SurplusDeficit)
        SELECT      ForecastID
                   ,MARatingOptionID
                   ,BenefitCategoryID
                   ,DualEligibleTypeID
                   ,SUM (INPaid) AS INPaid
                   ,SUM (INAllowed) AS INAllowed
                   ,SUM (INUnits) AS INUnits
                   ,SUM (OONPaid) AS OONPaid
                   ,SUM (OONAllowed) AS OONAllowed
                   ,SUM (OONUnits) AS OONUnits
                   ,SUM (SurplusDeficit) AS SurplusDeficit
        FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS WITH (NOLOCK)
        WHERE       ForecastID = @XForecastID
        GROUP BY    ForecastID
                   ,MARatingOptionID
                   ,BenefitCategoryID
                   ,DualEligibleTypeID;


        INSERT INTO @Results
        SELECT      DISTINCT
                    SPD.ForecastID ForecastID
                   ,PEB.BenefitCategoryID
                   ,BC.BidServiceCatID
                   ,BSC.ServiceCategoryCode
                   ,BSC.UtilType
                   ,BaseUnits = MAX (PEB.INUnits + PEB.OONUnits)
                   ,AvgCost = 0
                   ,BaseAllowed = MAX (PEB.INAllowed + ISNULL (PEB.OONAllowed, 0))
                   ,BaseNetPMPM = MAX (PEB.INPaid + ISNULL (PEB.OONPaid, 0))
                   ,UtilizationTrendProduct = SUM ((PEB.INUnits
                                                    * CASE WHEN CF.ColumnID = 1 AND CF.IsInNetwork = 1 THEN CF.ClaimFactor ELSE 0 END
                                                    + ISNULL (PEB.OONUnits, 0)
                                                    * CASE WHEN CF.ColumnID = 1 AND CF.IsInNetwork = 0 THEN CF.ClaimFactor ELSE 0 END))
                   ,BenefitChangeProduct = SUM ((PEB.INUnits
                                                 * CASE WHEN CF.ColumnID = 2 AND CF.IsInNetwork = 1 THEN CF.ClaimFactor ELSE 0 END
                                                 + ISNULL (PEB.OONUnits, 0)
                                                 * CASE WHEN CF.ColumnID = 2 AND   CF.IsInNetwork = 0 THEN CF.ClaimFactor ELSE 0 END))
                   ,PopulationChangeProduct = SUM ((PEB.INUnits
                                                    * CASE WHEN CF.ColumnID = 3 AND CF.IsInNetwork = 1 THEN CF.ClaimFactor ELSE 0 END)
                                                   + (ISNULL (PEB.OONUnits, 0)
                                                      * CASE WHEN CF.ColumnID = 3 AND  CF.IsInNetwork = 0 THEN CF.ClaimFactor ELSE
                                                                                                                  0 END))
                   ,OtherFactorProduct = MAX ((CPNS.INUnits
                                               * PBP.E2CINClaimFactorUnits * PBP.C2BINClaimFactorUnits)
                                              + (ISNULL (CPNS.OONUnits, 0)
                                                 * PBP.E2COONClaimFactorUnits * PBP.C2BOONClaimFactorUnits)) 
                   ,TotalProduct = MAX (CPNS.INAllowed * PBP.E2CINClaimFactorAllowed * PBP.C2BINClaimFactorAllowed

                                        + ISNULL (CPNS.OONAllowed, 0) * PBP.E2COONClaimFactorAllowed * PBP.C2BOONClaimFactorAllowed)
                                   * SQS.ProjectedSQS - rp.RelatedPartiesAdjustment
                   ,ProviderPaymentProduct = SUM ((PEB.INAllowed
                                                   * CASE WHEN CF.ColumnID = 5 AND CF.IsInNetwork = 1 THEN CF.ClaimFactor ELSE 0 END
                                                   * (dbo.fnGetSafeDivisionResultReturnOne (1, SQS.ProjectedSQSNoDampening))
                                                   * SQS.ProjectedSQS)
                                                  + (ISNULL (PEB.OONAllowed, 0)
                                                     * CASE WHEN CF.ColumnID = 5 AND  CF.IsInNetwork = 0 THEN CF.ClaimFactor ELSE 0 END
                                                     * (dbo.fnGetSafeDivisionResultReturnOne (1, SQS.ProjectedSQSNoDampening))
                                                     * SQS.ProjectedSQS))
                   ,CostUtilizationComponent = MAX (PEB.INAllowed)
                                               * CASE WHEN MIN (CF.ClaimFactor) = 0 THEN 0
                                                      ELSE
                                                          EXP (
                                                          SUM (
                                                          LOG (
                                                          NULLIF(CASE WHEN CF.ColumnID = 1 AND  CF.IsInNetwork = 1 THEN
                                                          CF.ClaimFactor ELSE 1 END
                                                                 * CASE WHEN CF.ColumnID = 3 AND   CF.IsInNetwork = 1 THEN
                                                            CF.ClaimFactor ELSE 1 END
                                                                 * CASE WHEN CF.ColumnID = 4 AND   CF.IsInNetwork = 1 THEN
                                                            CF.ClaimFactor ELSE 1 END, 0)))) END
                                               + MAX (ISNULL (PEB.OONAllowed, 0))
                                               * CASE WHEN MIN (CF.ClaimFactor) = 0 THEN 0
                                                      ELSE
                                                          EXP (
                                                          SUM (
                                                          LOG (
                                                          NULLIF(CASE WHEN CF.ColumnID = 1 AND CF.IsInNetwork = 0 THEN
                                                          CF.ClaimFactor ELSE 1 END
                                                                 * CASE WHEN CF.ColumnID = 3 AND   CF.IsInNetwork = 0 THEN
                                                            CF.ClaimFactor ELSE 1 END
                                                                 * CASE WHEN CF.ColumnID = 4 AND   CF.IsInNetwork = 0 THEN
                                                            CF.ClaimFactor ELSE 1 END, 0)))) END
                   ,UtilAdditiveAdjustment = NULL
                   ,PMPMAdditiveAdjustment = NULL
				   ,SurplusDeficit = MAX(ISNULL(CPNS.SurplusDeficit,0))

        FROM        dbo.SavedPlanDetail SPD WITH (NOLOCK)
       INNER JOIN   @CalcPlanExperienceByBenefitCat PEB
               ON PEB.ForecastID = SPD.ForecastID
                  AND   PEB.MARatingOptionID = SPD.MARatingOptionID
       INNER JOIN   @CalcPlanExperienceByBenefitCatNonSQS CPNS
               ON CPNS.ForecastID = PEB.ForecastID
                  AND   CPNS.MARatingOptionID = PEB.MARatingOptionID
                  AND   CPNS.DualEligibleTypeID = PEB.DualEligibleTypeID
                  AND   CPNS.BenefitCategoryID = PEB.BenefitCategoryID
       INNER JOIN   dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
               ON PEB.BenefitCategoryID = BC.BenefitCategoryID
       INNER JOIN   dbo.LkpExtCMSBidServiceCategory BSC WITH (NOLOCK)
               ON BC.BidServiceCatID = BSC.BidServiceCategoryID
       INNER JOIN   dbo.CalcProjectionFactors PBP WITH (NOLOCK)
               ON SPD.ForecastID = PBP.ForecastID
                  AND   PEB.BenefitCategoryID = PBP.BenefitCategoryID
                  AND   PBP.MARatingOptionID = 1
       INNER JOIN   dbo.CalcSQSFactors SQS WITH (NOLOCK)
               ON SQS.ForecastID = PEB.ForecastID
                  AND   SQS.BenefitCategoryID = PEB.BenefitCategoryID
                  AND   SQS.MARatingOptionID = PEB.MARatingOptionID
                  AND   SQS.DualEligibleTypeID = PEB.DualEligibleTypeID
       INNER JOIN   dbo.vwLkpPartBOnlyCoveredPercent vw
               ON vw.BenefitCategoryID = BC.BenefitCategoryID

       INNER JOIN   dbo.fnAppGetMABPTWS1Digits (@XForecastID) DIG
               ON DIG.ForecastID = SPD.ForecastID
                  AND   DIG.ServiceCategoryCode = BSC.ServiceCategoryCode
       INNER JOIN   (SELECT ForecastID
                           ,ClaimForecastID
                           ,ColumnID
                           ,IsInNetwork
                           ,BenefitCategoryID
                           ,ClaimFactor = E2CClaimFactor * C2BClaimFactor
                     FROM   dbo.fnGetClaimFactors (@XForecastID) ) CF
               ON CF.ForecastID = SPD.ForecastID
                  AND   CF.ClaimForecastID = SPD.ClaimForecastID
                  AND   CF.BenefitCategoryID = PEB.BenefitCategoryID
       RIGHT JOIN   dbo.fnGetRelatedPartiesAdjustment (@XForecastID) rp
               ON rp.ForecastID = PEB.ForecastID
                  AND   rp.BenefitCategoryID = PEB.BenefitCategoryID

        WHERE       SPD.ForecastID = @XForecastID
                    AND PEB.DualEligibleTypeID = 2
                    AND SPD.MARatingOptionID = 1

        GROUP BY    SPD.ForecastID
                   ,PEB.BenefitCategoryID
                   ,BSC.BidServiceCategoryID
                   ,BSC.ServiceCategoryCode
                   ,BSC.UtilType
                   ,BC.BidServiceCatID
                   ,vw.ServiceCategory
                   ,vw.PartBOnlyFactor
                   ,DIG.BaseUnits
                   ,DIG.AvgCostDecimals
                   ,SQS.ProjectedSQS
                   ,rp.RelatedPartiesAdjustment;
        RETURN;
    END;
GO