SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnGetClaimFactors]   ******/

/****** Object:  UserDefinedFunction [dbo].[fnGetClaimFactors]    Script Date: 01/30/2014 13:36:14 ******/

-- ---------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnGetClaimFactors  
--  
-- AUTHOR: Brian Lake   
--  
-- CREATED DATE: 2009-Feb-10  
-- HEADER UPDATED: 2010-Dec-20  
--  
-- DESCRIPTION: Multiplies all ClaimFactors associated with the specified column on WS1  
--  that are stored in SavedClaimFactorDetail.  These values originate from  
--  The returned claim factor is needed to project benefits, spBenefitProjection  
--  
-- PARAMETERS:   
-- Input:  
--      @ForecastID   
--  Output:  
--  
-- TABLES:   
-- Read:  
--      LkpExtCMSWorksheet1Mapping  
--      LkpIntBenefitCategory  
--      LkpIntProjectionYear  
--      PerIntClaimFactorType  
--      SavedClaimFactorBenefitLevel  
--      SavedClaimFactorDetail  
--      SavedPlanDetail  
-- Write:  
--  
-- VIEWS:  
--  
-- FUNCTIONS:  
--  
-- STORED PROCS:  
--  
-- $HISTORY   
-- ---------------------------------------------------------------------------------------------------------------------  
-- DATE       VERSION     CHANGES MADE                                      DEVELOPER    
-- ---------------------------------------------------------------------------------------------------------------------  
-- 2009-Feb-10      1           Initial Version                                                     Brian Lake  
-- 2009-Feb-22      2           Revised for ClaimForecastID                                         Sandy Ellis     
-- 2010-Feb-06		3			Added AND CTT.PlanYearID = FD.PlanYearID							Casey Sanders     
-- 2010-Jun-01      4           Updated to coding standards                                         Jake Gaecke  
-- 2010-Jun-14      5           Added BenefitCategoryID functionality                               Jake Gaecke  
-- 2010-Aug-23      6           Adjusted table references for renaming                              Jake Gaecke  
-- 2010-Sep-23      7           Now a table valued function to reduce server calls                  Jake Gaecke  
-- 2010-Dec-20      8           Updated for SavedClaimFactorDetail and SavedClaimFactorBenefitLevel Joe Casey  
-- 2011-Feb-01		9			Added UNION statement to distinguish between different MARating		Craig Wright  
--								Options  
-- 2012-Oct-09      10          Added IF Statement to account for Group -                           Mike Deren  
-- 2012-Oct-12      11			Changed Bottom SELECT statement for Individual						Mike Deren  
-- 2012-Nov-05      12			Group is same as Individual now										Mike Deren  
-- 2012-Mar-04		13			Changing for Induced Utilization									Mike Deren  
-- 2012-Nov-06		14			Removed two subqueries ?Distinct? keyword 							Rajendran
-- 2014-Nov-09		15			Changing Table Name													Mike Deren
-- AUG-08-2021		16			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's	        Franklin Fu
-- Aug-03-2023		17			Added Nolock and Internal Parameter									Sheetal Patil
-- ---------------------------------------------------------------------------------------------------------------------  
CREATE FUNCTION [dbo].[fnGetClaimFactors] ( @ForecastID INT )  
RETURNS @Results TABLE  
    (  
      ForecastID INT ,  
      ClaimForecastID INT ,  
      ColumnID INT ,  
      IsInNetwork INT ,  
      BenefitCategoryID INT ,  
      E2CClaimFactor DECIMAL(24, 15) ,  
      C2BClaimFactor DECIMAL(24, 15) ,  
      ClaimFactor DECIMAL(24, 15)  
    )  
AS   
    BEGIN  
	DECLARE @XForecastID INT = @ForecastID
        INSERT  INTO @Results  
                SELECT  spd.ForecastID ,  
                        spd.ClaimForecastID ,  
                        ws1.ColumnID ,  
                        cfd.IsInNetwork ,  
                        ben.BenefitCategoryID ,  
                        E2CClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0
						ELSE EXP(SUM(LOG(NULLIF(CASE py.ProjectionYearID  
                                                       WHEN 1  
                                                       THEN CASE ctt.ClaimFactorTypeID  
                                                              WHEN 12  
                                                              THEN cfd.ClaimFactor  
                                                              ELSE cfd.ClaimFactor  
                                                            END  
                                                       ELSE 1  
 END,0)))) END,  
                        C2BClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0
						ELSE EXP(SUM(LOG(NULLIF(CASE py.ProjectionYearID  
                                                       WHEN 2  
                                                       THEN cfd.ClaimFactor  
                                                       ELSE 1  
                                                     END,0)))) END , 												 
                        ClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 
						ELSE EXP(SUM(LOG(NULLIF(cfd.ClaimFactor,0)))) END 
            --There is no aggregate product function in SQL Server.  This is a workaround.  
                FROM    dbo.SavedPlanDetail spd WITH (NOLOCK) 
                        INNER JOIN ( SELECT bl.ClaimForecastID ,  
                                            bl.ClaimFactorTypeID ,  
                                            bl.ProjectionYearID ,  
                                            bl.IsInNetwork ,  
                                            bl.BenefitCategoryID ,  
                                            bl.ClaimFactor  
                                     FROM   dbo.SavedBenefitLevelClaimFactors bl WITH (NOLOCK)
                                            INNER JOIN dbo.SavedPlanDetail spd WITH (NOLOCK) ON bl.ClaimForecastID = spd.ClaimForecastID  
                                     WHERE  spd.ForecastID = @XForecastID  
                                   ) cfd ON spd.ClaimForecastID = cfd.ClaimForecastID  
                        INNER JOIN dbo.LkpIntProjectionYear py WITH (NOLOCK) ON py.ProjectionYearID = cfd.ProjectionYearID  
                        INNER JOIN dbo.PerIntClaimFactorTypeTest ctt WITH (NOLOCK) ON ctt.ClaimFactorTypeID = cfd.ClaimFactorTypeID  
                        INNER JOIN dbo.LkpExtCMSWorksheet1Mapping ws1 WITH (NOLOCK) ON ctt.ColumnIDBPT = ws1.ColumnID  
                        INNER JOIN dbo.LkpIntBenefitCategory  ben WITH (NOLOCK) ON cfd.BenefitCategoryID = ben.BenefitCategoryID  
                WHERE   spd.ForecastID = @XForecastID  
                        AND ( spd.MARatingOptionID = 1  
                              OR spd.MARatingOptionID = 2  
                            )  
                GROUP BY spd.ClaimForecastID ,  
--        py.ProjectionYearID,  
                        ws1.ColumnID ,  
                        cfd.IsInNetwork ,  
                        ben.BenefitCategoryID ,  
                        spd.ForecastID  
                 
        RETURN  
    END
GO
