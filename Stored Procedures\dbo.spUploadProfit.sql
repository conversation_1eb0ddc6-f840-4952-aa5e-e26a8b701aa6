SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUploadProfit
--
-- CREATOR: Joe Casey
--
-- CREATED DATE: 2010-Sep-29
-- HEADER UPDATED: 2011-Feb-17
--
-- DESCRIPTION: Stored Procedure responsible for uploading admin and profit info.
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--      @ProfitPercent
--      @UserID
--  Output:
--
-- TABLES:
--	Read:
--      LkpIntPlanYear
--      SavedPlanAssumptions
--      SavedPlanHeader
--	Write:
--      SavedPlanAssumptions
--      SavedPlanHeader
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Sep-29      1           Initial Version                                                     Joe Casey
-- 2010-Oct-07      2           Added IsNull to Validation. Added IsToReprice update.               Joe Casey
-- 2010-Nov-08      3           Removed Validation                                                  Joe Casey
-- 2011-Feb-09		4			Added SalesMembership												Nate Jacoby
-- 2011-Feb-17		5			Removed ExpensePMPM as a paramter									Joe Casey
-- 2011-Jun-02		6			Replaced LkpIntPlanYear with dbo.fnGetBidYear()						Bobby Jaegers
-- 2011-Jun-14		7			Changed @PlanYearID to return SMALLINT instead of INT				Bobby Jaegers
-- 2011-Sep-12		8			Added Scalar Value @SalesAdjustmentFactor to be inserted			Bobby Jaegers
--									into SavedPlanAssumptions
-- 2012-Feb-24		9			Added new admin buckets to INSERT INTO SavedPlanAssumptions			Alex Rezmerski
-- 2012-Aug-20		10			Added new Benefit Strings to INSERT INTO SavedPlanAssumptions		Mike Deren
-- 2013-Jan-09		11			Changed the order of SalesMembership and SalesAdjFactor				Tim Gao
-- 2019-Jun-28		12			Replace SavedPlanHeader with SavedForecastSetup						Pooja Dahiya
-- 2019-oct-30		13			Replace @UserID from char(13) to char(7)							Chhavi Sinha
-- 2019-Nov-06		14			Replace SavedForecastSetup with SavedPlanHeader						Chhavi Sinha
-- 2019-Dec-19      15          Revert version 12										            Pooja Dahiya
-- 2022-Jun-29		16			Removed "WHERE AND IsFinal = 0 "									Aleksandar Dimitrijevic
-- 2024-Jul-26      17          Removed "CurrentBenefitString"                                      Dheeraj Singh
-- 2024-Sep-11		18		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUploadProfit]
(
    @ForecastID INT,
    @ProfitPercent FLOAT,
    @UserID CHAR(7)
)
AS
    SET NOCOUNT ON

    --------------------------------------------------------------------------------------------------------------------
    --Declarations------------------------------------------------------------------------------------------------------
    DECLARE @IsMAPD BIT
    DECLARE @SalesMembership INT    

    DECLARE @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear()

    DECLARE @LastUpdate DATETIME
    SET @LastUpdate = GETDATE() 

    --------------------------------------------------------------------------------------------------------------------
    --Processing--------------------------------------------------------------------------------------------------------
    BEGIN TRY
    BEGIN TRANSACTION
         SET @IsMAPD = (SELECT IsMAPD FROM SavedPlanHeader WHERE ForecastID = @ForecastID AND IsHidden = 0)         
        IF EXISTS (SELECT 1 FROM SavedPlanAssumptions WHERE ForecastID = @ForecastID)
            BEGIN
                UPDATE SavedPlanAssumptions
                SET ProfitPercent = @ProfitPercent
                WHERE ForecastID = @ForecastID
            END
        ELSE
            BEGIN
                INSERT INTO SavedPlanAssumptions
                SELECT
                    @PlanYearID,
                    @ForecastID,
                    @IsMAPD,
                    0, --ExpensePercent
                    @ProfitPercent,
                    0, --RxBasicPremium
                    0, --RxSuppPremium
                    0, --ExpensesPMPM
                    0, --SecondaryPayerAdjustment
                    0, --SalesAndMarketingPercent,
                    0, --DirectAdminPercent,
                    0,  --IndirectAdminPercent                    
					0, --@SalesMembership,
					0, --QualityInitiatives
					0, --TaxesAndFees
					NULL --BidBenefitString
            END

        UPDATE SavedForecastSetup
        SET IsToReprice = 1,
            LastUpdateByID = @UserID,
            LastUpdateDateTime = @LastUpdate
        WHERE ForecastID = @ForecastID

        COMMIT
    END TRY
    BEGIN CATCH
        ROLLBACK
    END CATCH
    --------------------------------------------------------------------------------------------------------------------
    --------------------------------------------------------------------------------------------------------------------
    --GRANT EXECUTE ON  [dbo].[spUploadProfit] TO [MAPDModel]
    GO