SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: [spHideBPTExceptionHeader]  
--  
-- AUTHOR: <PERSON><PERSON>rell  
--  
-- CREATED DATE: 2008-May-05  
-- HEADER UPDATED: 2010-Oct-13  
--  
-- DESCRIPTION: Hides all BPT exception header and detail data for the specified plan index.  
--  
-- PARAMETERS:  
-- Input:  
--      @ForecastID  
-- Output:  
--  
-- TABLES:  
-- Read:  
-- Write:    
--      SavedPlanBPTExceptionHeader  
--  
-- VIEWS:  
--  
-- FUNCTIONS:  
--  
-- STORED PROCS:  
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                      DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2008-May-05      1           Initial version.                                                    Tonya Cockrell  
-- 2010-Oct-13      2           Removed @PlanYearID                                                 Joe Casey  
-- 2011-May-09		3			Removed Deleting detail from details table							Gowri.G
-- ----------------------------------------------------------------------------------------------------------------------  
CREATE PROC [dbo].[spHideBPTExceptionHeader]  
    @ForecastID INT  
AS  
  
    UPDATE SavedPlanBPTExceptionHeader  
    SET IsHidden = 1  
    WHERE ForecastID = @ForecastID
GO
