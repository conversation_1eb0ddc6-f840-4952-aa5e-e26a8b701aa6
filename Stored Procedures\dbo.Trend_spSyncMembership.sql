SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Author:  <PERSON>           
-- Create date: 2020-Sep-24      
-- Description:  Trend_spSyncMembership      
--            
--            
-- PARAMETERS:            
-- Input:              

-- TABLES:          
-- Read:            

-- Write:            
-- VIEWS:            
--            
-- FUNCTIONS:            
--              
-- STORED PROCS:             


-- $HISTORY               

-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE    VERSION    CHANGES MADE                                                DEVELOPER              
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Sep-24         1    Initial version										Kiran Pant      
-- 2020-Oct-05         2    Added logic for truncate							Kiran Pant      
-- 2020-Oct-29         3    updated truncate logic								Kiran Pant      
-- 2020-Nov-05         4    Replaced truncate with delete						Kiran Pant    
-- 2020-Nov-24         5    Modifed query % ADDED temp table					Anurodh Pandey    
-- 2022-Aug-29		   6    Set IsRisk to 0; Removed joins on SavedDFHeader,    Franklin Fu
--							LkpIntRiskPlanMapping, LkpIntRiskDefnDetail	
-- 2023-Feb-20		   7	Changed max incurred month logic.Remove Order by 	Adam Gilbert
--							from insert. Remove @IncurredMonth parameter.
-- 2025-Apr-29		   8	Removing cursor										Archana Sahu
-- ----------------------------------------------------------------------------------------------------------------------              

CREATE PROCEDURE [dbo].[Trend_spSyncMembership] --1,'KXP3871'      
   @DFRunID VARCHAR(MAX),        
   @LastUpdateByID VARCHAR(7)  ,      
   @ValidationMessage VARCHAR(MAX) OUT      
AS      
BEGIN      

    SET NOCOUNT ON;      
    BEGIN TRY      
        BEGIN      

    SET NOCOUNT ON; 
	
    DECLARE @DFRunID1 SMALLINT, 
   			@maxIncurredMonth int,


			@DeleteRowcount INT = 1;
			-- Delete logic 	
			BEGIN TRY 
				BEGIN TRANSACTION dfloopdelete;

					WHILE @DeleteRowcount > 0
					BEGIN
						DELETE FROM dbo.Trend_CalcHistoricMembership        
						WHERE PlanInfoID IN        
							  (        
								  SELECT TOP (100000)        
										 PlanInfoID        
								  FROM dbo.Trend_CalcHistoricMembership        
								  ORDER BY PlanInfoID DESC        
							  );   
						SET @DeleteRowcount = @@ROWCOUNT
					END

				COMMIT TRANSACTION dfloopdelete
			END TRY
			BEGIN CATCH
				ROLLBACK TRANSACTION dfloopdelete
			END CATCH

			--view temptable
			DROP TABLE IF EXISTS #viewtable;
			SELECT PlanInfoID,CPS INTO #viewtable FROM [dbo].[vwPlanInfo]

			--loop table for df run ids
			DROP TABLE IF EXISTS #dfrunidtemp;
			SELECT CAST(Value AS SMALLINT) DFRunID INTO #dfrunidtemp FROM dbo.fnStringSplit(@DFRunID,',');  

			--loop the DF runids
			 WHILE (SELECT COUNT(*) FROM #dfrunidtemp WHERE 1=1) > 0
				BEGIN
					SELECT TOP 1 @DFRunID1=   DFRunID FROM #dfrunidtemp ORDER BY DFRunID;
					BEGIN TRY 
						BEGIN TRANSACTION dfloopinsert;

							WITH maxDate as(
							SELECT DISTINCT DFRunID, IncurredStartDate, IncurredEndDate, PaidThroughDate ,
								CASE
									WHEN ( YEAR(PaidThroughDate) = YEAR(IncurredEndDate) 
										AND NOT (  (MONTH(PaidThroughDate) = 12 AND DAY(PaidThroughDate) = 31)  
												   OR (MONTH(PaidThroughDate) = 1 AND DAY(PaidThroughDate) = 31)
												) 
										  )
									THEN EOMONTH(DATEADD(m,-1,IncurredEndDate)) 
									ELSE IncurredEndDate
								END as maxIncurredDt
							FROM df.dbo.SavedDfHeader
							WHERE DFRunid = @DFRunID1
							)
							SELECT  @maxIncurredMonth = (YEAR(maxIncurredDt) * 100) + MONTH(maxIncurredDt) --format to an integer value Calendarmonth YYYYMM 
							FROM maxDate


							 INSERT INTO dbo.Trend_CalcHistoricMembership      
							(      
								PlanInfoID,      
								CPS,      
								PlanYearID,      
								IncurredMonth,      
								MonthID,      
								QuarterID,      
								SSStateCountyCD,      
								IsRisk,      
								MemberMonths,      
							LastUpdateByID,      
								LastUpdateDateTime      
							)      
							SELECT vpi.PlanInfoID,      
								   vpi.CPS,      
								   f.IncurredMonth / 100 AS PlanYearID,      
								   f.IncurredMonth,      
								   f.IncurredMonth % 100 AS MonthID,      
								FLOOR((f.IncurredMonth % 100 - 1) / 3) + 1 AS QuarterID,      
								   f.SSStateCountyCD,      
								   0 AS IsRisk,      
								   SUM(f.MemberMonths) AS MemberMonths,      
								   @LastUpdateByID AS LastUpdateByID,      
								   GETDATE() AS LastUpdateDateTime      
							FROM DF.dbo.SavedDFFinance f WITH (NOLOCK)      
								LEFT JOIN #viewtable vpi WITH (NOLOCK)      
									ON f.PlanInfoID = vpi.PlanInfoID        
								INNER JOIN DF.dbo.LkpIntDemog d WITH (NOLOCK)      
									ON f.Demog = d.Demog       
							WHERE d.IsHospice = 0      
								  AND d.IsESRD = 0      
								  AND f.DFRunID = @DFRunID1      
								  AND f.IncurredMonth <= @maxIncurredMonth 
							GROUP BY f.IncurredMonth / 100,      
									 f.IncurredMonth % 100,      
									 FLOOR((f.IncurredMonth % 100 - 1) / 3) + 1,          
									 vpi.PlanInfoID,      
									 vpi.CPS,      
									 f.IncurredMonth,      
									 f.SSStateCountyCD  

						COMMIT TRANSACTION dfloopinsert
					END TRY
					BEGIN CATCH
						ROLLBACK TRANSACTION dfloopinsert
					END CATCH
					DELETE FROM #dfrunidtemp WHERE DFRunID=@DFRunID1;
				END -- While loop ENd
      
        END;      
    END TRY      
    BEGIN CATCH      

   SET @ValidationMessage = ERROR_MESSAGE()       
        DECLARE @ErrorMessage NVARCHAR(4000);      
        DECLARE @ErrorSeverity INT;      
        DECLARE @ErrorState INT;      
        DECLARE @ErrorException NVARCHAR(4000);      
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),      
                @currentdate DATETIME = GETDATE();      

        SELECT @ErrorMessage = ERROR_MESSAGE(),      
               @ErrorSeverity = ERROR_SEVERITY(),      
               @ErrorState = ERROR_STATE(),      
               @ErrorException      
                   = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'      
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);      
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);      
        ---Insert into app log for logging error------------------      
        EXEC [dbo].[spAppAddLogEntry] @currentdate,      
                                      '',      
              'ERROR',      
                                      @errSrc,      
                                      @ErrorMessage,      
                                      @ErrorException,      
                                      @LastUpdateByID;      

    END CATCH;      

END;
GO
