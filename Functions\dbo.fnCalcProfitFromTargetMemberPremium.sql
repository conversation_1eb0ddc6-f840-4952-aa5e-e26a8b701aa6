SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	fnCalcProfitFromTargetMemberPremium
--
-- CREATOR:			<PERSON> Brandt
--
-- CREATED DATE:	2023-MAY-05
--
-- DESCRIPTION:		Uses the input Target Member Premium (or, the Current Premium if a valid TMP is not provided) to
--					calculate and return the following values: Profit, ProfitPercent, Expenses, TotalReqRev, PlanBid, StandardizedBid, 
--					Savings, Rebate, BasicMemberPremium, GovtPremiumAdj, UncollectedPremium, and InsurerFee. 
--		
-- PARAMETERS:
--  Input  :		@ForecastID				INT
--					@TargetMemberPremium	DECIMAL(6,2)
--					@UserID					CHAR(7)
--
--  Output :		@Results TABLE
--					(Profit					DECIMAL(9, 2)
--					,ProfitPercentage		DECIMAL(10, 8)
--					,Expenses				DECIMAL(14, 8)
--					,TotalReqRev			DECIMAL(14, 8)
--					,PlanBid				DECIMAL(14, 8)
--					,StandardizedBid		DECIMAL(14, 8)
--					,Savings				DECIMAL(14, 8)
--					,Rebate					DECIMAL(14, 8)
--					,BasicMemberPremium		DECIMAL(14, 8)
--					,GovtPremiumAdj			DECIMAL(14, 8)
--					,UncollectedPremium		DECIMAL(14, 8)
--					,InsurerFee				DECIMAL(14, 8))
--
-- TABLES : 
--	Read :			SavedForecastSetup
--					SavedPlanInfo
--					LkpProductType
--					LkpSNPType
--					PerExtCMSValues
--					CalcFinalPremium
--					LkpIntUncollectedPremium
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			SavedPlanHeader
--
-- FUNCTIONS:		fnAppGetBidSummary
--					fnAppGetMABPTWS4TotalMedicareCovered
--					fnPrePremium
--					fnGetMARebateAllocation
--					fnGetRebatePercent
--					fnGetSafeDivisionResult
--					fnGetSafeDivisionResultReturnOne
--					fnGetFinalPremiumSavings
--					fnIsNegative
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2023-MAY-05		1			Initial Version														Alex Brandt
-- 2023-MAY-30		2			Added changes to calculation for TotalNBE and RebatePercentage		Alex Brandt
-- 2023-JUN-27		3			Added changes to utilize different table than results				Alex Brandt
--									of fnGetBidSummary for UncollectedPremium
-- 2023-JUL-19		4			Added DSNP Exclusion and IsLocked logic								Alex Brandt
-- 2023-AUG-16		5			FN now outputs, in addition to ProfitPercentage, Profit,			Jake Lewis
--									TotalReqRev, PlanBid, StandardizedBid, Savings, Rebate, 
--									BasicMemberPremium, GovtPremiumAdj, and UncollectedPremium		
-- 2023-AUG-24		6			Add NOLOCK hints and used fnGetSafeDivisionResult where necesssary	Jake Lewis
-- 2023-AUG-29		7			Added Expenses and InsurerFee to output								Jake Lewis
-- 2023-OCT-03		8			Cleaned up code as part of User Story #5165029						Alex Brandt
-- 2023-JAN-30		9			Remove rounding from Profit output to limit 'pesky penny' issues	Jake Lewis
-- ----------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnCalcProfitFromTargetMemberPremium]
    (@ForecastID          INT
    ,@TargetMemberPremium DECIMAL(6, 2)
    ,@UserID              CHAR(7))

RETURNS @Results TABLE
    (Profit             DECIMAL(14, 8)
    ,ProfitPercentage   DECIMAL(10, 8)
    ,Expenses           DECIMAL(14, 8)
    ,TotalReqRev        DECIMAL(14, 8)
    ,PlanBid            DECIMAL(14, 8)
    ,StandardizedBid    DECIMAL(14, 8)
    ,Savings            DECIMAL(14, 8)
    ,Rebate             DECIMAL(14, 8)
    ,BasicMemberPremium DECIMAL(14, 8)
    ,GovtPremiumAdj     DECIMAL(14, 8)
    ,UncollectedPremium DECIMAL(14, 8)
    ,InsurerFee         DECIMAL(14, 8))

AS

    BEGIN

        -- Declare variables
        DECLARE @XForecastID INT = @ForecastID; --Internal parameter
        DECLARE @XUserID CHAR(7) = @UserID; --Internal parameter
        DECLARE @XTargetMemberPremium DECIMAL(6, 2) = @TargetMemberPremium; --Internal parameter
        DECLARE @ProductTypeID INT; --ProductTypeID
        DECLARE @Savings DECIMAL(14, 8); --Savings
        DECLARE @Rx DECIMAL(9, 2); --Total Drug Premium before rebate allocation (Basic and Supplemental)
        DECLARE @PtB DECIMAL(9, 1); --Rebate Allocated to Part B Premium Giveback
        DECLARE @BM DECIMAL(14, 8); --Plan Benchmark
        DECLARE @SBM DECIMAL(14, 8); --Standardized Benchmark
        DECLARE @PB DECIMAL(14, 8); --Plan Bid
        DECLARE @SPB DECIMAL(14, 8); --Standardized Plan Bid
        DECLARE @PMPMm DECIMAL(14, 8); --Medicare Covered Net PMPM Medical Expenses (excl admin and profit)
        DECLARE @MSB DECIMAL(14, 8); --MSB Claims
        DECLARE @PMPMt DECIMAL(14, 8); --Total PMPM Net Medical Expenses (excl admin and profit)
        DECLARE @APct DECIMAL(8, 6); --Any fixed expenses specified as a percentage of premium (e.g. Pre-2010 Admin, Reinsurance, etc.) (Insurer Fee Percent)
        DECLARE @InsurerFee DECIMAL(8, 6); --InsurerFee (use existing if locked, otherwise recalculate)
        DECLARE @UserFee DECIMAL(7, 6); --UserFee
        DECLARE @ADollar DECIMAL(14, 8); --Any fixed expenses specified as PMPM (e.g. Admin, User Fees, Uncollected Premium Fee, etc.)
        DECLARE @ABNBE DECIMAL(14, 8); --Non Benefit Expense attributable to A/B benefits
        DECLARE @CF DECIMAL(20, 12); --Plan Conversion Factor
        DECLARE @K1 DECIMAL(14, 8); --Rebate %, if Bid < BM
        DECLARE @K2 DECIMAL(14, 8); --1 / CF, if Bid > BM
        DECLARE @P1 DECIMAL(14, 8); --Profit PMPM, if Bid < BM
        DECLARE @P2 DECIMAL(14, 8); --Profit PMPM, if Bid > BM
        DECLARE @BidBMCheck BIT; --Check if Bid > BM
        DECLARE @P DECIMAL(14, 8); --Final Profit
        DECLARE @TRR DECIMAL(14, 8); --Total Required Revenue
        DECLARE @ProfitPercentage DECIMAL(10, 8); --Profit Percentage
        DECLARE @IsLocked BIT; --Admin lock
        DECLARE @UncollectedPremium DECIMAL(5, 2); --Uncollected premium
        DECLARE @DSNP BIT; --Indicator for DSNP exclusion
        DECLARE @Exp DECIMAL(14, 8); --Expenses

        -- ProductTypeID
        SELECT      @ProductTypeID = lpt.ProductTypeID
        FROM        dbo.SavedForecastSetup sfs WITH (NOLOCK)
        LEFT JOIN   dbo.SavedPlanInfo spi WITH (NOLOCK)
               ON spi.PlanInfoID = sfs.PlanInfoID
        LEFT JOIN   dbo.LkpProductType lpt WITH (NOLOCK)
               ON lpt.ProductTypeID = spi.ProductTypeID
        WHERE       sfs.ForecastID = @XForecastID;

        -- Determine if DSNP exclusion should be applied (no uncollected premium on DSNP plans)
        SELECT      @DSNP = CASE WHEN sph.IsSNP = 1 AND (PATINDEX ('%D-SNP%', sph.PlanName) <> 0 OR lst.SNPType = 'Dual Eligible') THEN 0 ELSE 1 END
        FROM        dbo.SavedPlanHeader sph WITH (NOLOCK)
       INNER JOIN   dbo.LkpSNPType lst WITH (NOLOCK)
               ON lst.SNPTypeID = sph.SNPTypeID
        WHERE       sph.ForecastID = @XForecastID;

        --Set initial values 
        SELECT      @Rx = mara.RxBasicPremium + mara.RxSuppPremium
                   ,@PtB = bidSumm.PartBPremiumBuydown
                   ,@BM = CAST(bidSumm.PlanBenchmark AS DECIMAL(14, 8))
                   ,@PMPMm = bptws4tmc.MedicareCoveredNet
                   ,@MSB = CAST(bidSumm.TotalNet AS DECIMAL(14, 8)) - bptws4tmc.MedicareCoveredNet
                   ,@ADollar = bidSumm.ExpensePMPM + bidSumm.UserFee
                   ,@CF = prePrem.ConversionFactor
                   ,@IsLocked = sph.IsLocked
                   ,@UserFee = bidSumm.UserFee
        FROM        dbo.fnAppGetBidSummary (@XForecastID) bidSumm --user selection
       INNER JOIN   dbo.fnAppGetMABPTWS4TotalMedicareCovered (@XForecastID) bptws4tmc
               ON bptws4tmc.ForecastID = bidSumm.ForecastID
       INNER JOIN   dbo.fnPrePremium (@XForecastID) prePrem
               ON prePrem.ForecastID = bidSumm.ForecastID
       INNER JOIN   dbo.fnGetMARebateAllocation (@XForecastID) mara
               ON mara.ForecastID = bidSumm.ForecastID
       INNER JOIN   dbo.SavedPlanHeader sph WITH (NOLOCK)
               ON sph.ForecastID = bidSumm.ForecastID;

        --Handle admin locking, set A%, A$, InsurerFee, and UncollectedPremium
        IF @IsLocked = 1
            BEGIN
                SET @APct = 0;
                SELECT  @InsurerFee = InsurerFee
                       ,@UncollectedPremium = ISNULL (UncollectedPremium, 0)
                FROM    dbo.CalcFinalPremium WITH (NOLOCK)
                WHERE   ForecastID = @XForecastID;

                SET @ADollar = @ADollar + @InsurerFee;
            END;
        ELSE
            BEGIN
                SET @InsurerFee = 0;
                SELECT  @APct = InsurerFeePercent FROM dbo.PerExtCMSValues WITH (NOLOCK);

                SELECT  @UncollectedPremium = UncollectedPremium * @DSNP
                FROM    dbo.LkpIntUncollectedPremium WITH (NOLOCK)
                WHERE   RangeMax >= @XTargetMemberPremium AND RangeMin <= @XTargetMemberPremium;
            END;

        SET @ADollar = @ADollar + @UncollectedPremium;

        SET @PMPMt = @PMPMm + @MSB; --Total PMPM Net Medical Expenses (excl admin and profit)
        SET @ABNBE = dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt) * @ADollar; --Non Benefit Expense attributable to A/B benefits
        SET @K1 = dbo.fnGetRebatePercent (@XForecastID); --Rebate %, if Bid < BM
        SET @K2 = dbo.fnGetSafeDivisionResultReturnOne (1, @CF); --1 / CF, if Bid > BM

        --Profit
        SET @P1 = dbo.fnGetSafeDivisionResult (
                  ((@XTargetMemberPremium - @Rx - @PtB + @K1 * @BM) * (1 - @APct) - @PMPMt - (@K1 - 1) * @PMPMm)
                 ,(1 + (@K1 - 1) * dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt))) - @ADollar; --Profit PMPM, if Bid < BM
        SET @P2 = dbo.fnGetSafeDivisionResult (
                  ((@XTargetMemberPremium - @Rx - @PtB + @K2 * @BM) * (1 - @APct) - @PMPMt - (@K2 - 1) * @PMPMm)
                 ,(1 + (@K2 - 1) * dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt))) - @ADollar; --Profit PMPM, if Bid > BM
        SET @BidBMCheck = CASE WHEN (@PMPMm + @ABNBE + dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt) * @P1) > @BM THEN 1 ELSE 0 END; --Determine if bid > benchmark
        SET @P = CASE WHEN @BidBMCheck = 1 THEN @P2 ELSE @P1 END; --Final Profit

        SET @TRR = dbo.fnGetSafeDivisionResult ((@P + @ADollar + @PMPMt), (1 - @APct)); --TotalReqRev

        IF @IsLocked = 0 --Update InsurerFee if unlocked
            BEGIN
                SET @InsurerFee = @TRR * @APct;
            END;

        SET @ProfitPercentage = dbo.fnGetSafeDivisionResult (@P, @TRR); --ProfitPercentage
        SET @PB = ROUND (
                  dbo.fnGetSafeDivisionResult (
                  (@PMPMm + dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt) * @ADollar), (1 - @ProfitPercentage - @APct)), 2); --PlanBid
        SET @SPB = ROUND (
                   dbo.fnGetSafeDivisionResult (
                   ROUND (
                   dbo.fnGetSafeDivisionResult (
                   (@PMPMm + dbo.fnGetSafeDivisionResult (@PMPMm, @PMPMt) * @ADollar), (1 - @ProfitPercentage - @APct))
                  ,2)
                  ,@CF)
                  ,2); --Standardized plan bid
        SET @SBM = dbo.fnGetSafeDivisionResult (@BM, @CF); --Standardized benchmark
        SET @Savings = dbo.fnGetFinalPremiumSavings (@ProductTypeID, @BM, @PB, @SBM, @SPB, @CF, @XUserID); --Savings

        SET @Exp = @TRR - @P - @PMPMt - @UserFee - @UncollectedPremium; --Final expenses

        --Populate results table
        INSERT  @Results
            (Profit
            ,ProfitPercentage
            ,Expenses
            ,TotalReqRev
            ,PlanBid
            ,StandardizedBid
            ,Savings
            ,Rebate
            ,BasicMemberPremium
            ,GovtPremiumAdj
            ,UncollectedPremium
            ,InsurerFee)
        SELECT  Profit = @P
               ,ProfitPercentage = @ProfitPercentage
               ,Expenses = @Exp
               ,TotalReqRev = @TRR
               ,PlanBid = @PB
               ,StandardizedBid = @SPB
               ,Savings = @Savings
               ,Rebate = ROUND (CONVERT (DECIMAL(8, 2), @Savings) * @K1, 2)
               ,BasicMemberPremium = ROUND (CASE WHEN @BM >= @PB THEN 0 ELSE @SPB - @SBM END, 2)
               ,GovtPremiumAdj = ROUND (CASE WHEN @BM >= @PB THEN 0 ELSE @SPB - @SBM END * (1 - @CF), 2)
               ,UncollectedPremium = @UncollectedPremium
               ,InsurerFee = @InsurerFee;

        RETURN;
    END;
GO
