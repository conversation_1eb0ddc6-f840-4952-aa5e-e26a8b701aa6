SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO




--------------------------------------------------------------------------------------------------------------------------            
------ Stored Procedure NAME: [spESRD_Default]       
------ ----------------------------------------------------------------------------------------------------------------           
------ DATE			VERSION		CHANGES MADE														DEVELOPER              
------ ----------------------------------------------------------------------------------------------------------------            
------ 2023-01-22 		1		Initial Version														Adam Keach
------ 2023-02-02		2		Updated the setup for parameters and to use #PLans table			Tingyao Wang
------							instead of dbo.ESRDClaims_ActivePlanList to allow multiple 
------							users to run the code at the same time

CREATE PROCEDURE [dbo].[spESRD_Aggregation]
(
   @RegionId INT,
  @ForecastID VARCHAR(MAX),		-- updated 2/2
  @DivisionID INT,
  @SubRegion VARCHAR(MAX),		-- updated 2/2
  @UserID VARCHAR(7)			-- added 2/2
)
AS
SET NOCOUNT ON
SET ANSI_WARNINGS OFF

/*
DECLARE @RegionId INT = 0 --comment out for actual stored procedure, only use for testing
DECLARE @ForecastID INT = 0
DECLARE @DivisionID INT = 0
*/

DECLARE @Local_RegionId INT = @RegionId					-- added 2/2
DECLARE @Local_ForecastID VARCHAR(MAX) = @ForecastID	-- added 2/2
DECLARE @Local_DivisionID INT = @DivisionID				-- added 2/2
DECLARE @Local_Subregion VARCHAR(MAX) = @Subregion		-- added 2/2

--DECLARE @UserID VARCHAR(13) -- commented out 2/2
DECLARE @BaseYear INT
DECLARE @BidYear INT
DECLARE @Iteration VARCHAR(13)

/************ Replace Hardcoding before Savings **************************/
SET @BaseYear = (SELECT MIN(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive='N')
SET @BidYear = (SELECT MAX(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive = 'N'); /* For Q3, Q4, FLP, these years will likely need to be hardcoded */
SET @Iteration = CONCAT(
                 (
                     SELECT Year  FROM dbo.SCT_CurrentToolVersion
                 ),
                 'Q',
                 (
                     SELECT Quarter FROM dbo.SCT_CurrentToolVersion
                 )
                       )

-- SET @UserID = 'HUMAD\SCTAPP'; -- commented out 2/2

/* ************************************************************************************************************************************************* *
Added 2/2 to create #Plans temp table instead of using dbo.ESRDClaims_ActivePlanList
Get Plan List corresponding to chosen plan/region/division.  For Q3/Q4/FLP purposes, all plans are chose by setting regionID, ForecastID, and DivisionID to 0 
* =================================================================================================================================================== */
IF (SELECT OBJECT_ID('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans		

SELECT PlanName,RFI.Region													
INTO #Plans																	
FROM dbo.SCT_vwRollupForecastInfo_SCT   RFI WITH (NOLOCK)

INNER JOIN 
	(SELECT PlanYearContractPBPSegment
	FROM dbo.SCT_SavedPlanList
	WHERE PlanYearID = @BidYear) b
				ON RFI.PlanName = b.PlanYearContractPBPSegment
				
WHERE RFI.RollupName = 'LIVE'
      AND RFI.IsSctPlan = 1
      AND RFI.IsToReprice = 0--Leave in - Don't remove for Bids
      AND RFI.IsLiveIndex = 1
      AND (@Local_RegionId = 0 OR RFI.ActuarialRegionID = @Local_RegionId)													
      AND (@Local_ForecastID IS NULL OR RFI.ForecastID IN (Select value from dbo.fnStringSplit(@Local_ForecastID,',')))		
      AND (@Local_DivisionID = 0 OR RFI.ActuarialDivisionID = @Local_DivisionID)											
	  AND (@Local_Subregion IS NULL OR RFI.Subregion IN (Select value from dbo.fnStringSplit(@Local_Subregion,',')));		


--Aggregation
IF (SELECT OBJECT_ID('tempdb..#ESRDResultsPreOverride')) IS NOT NULL DROP TABLE #ESRDResultsPreOverride

--Select * from #ESRDREsultsPreOverride order by CPS,Cohort,Class,DEPound
--order by mbrmths desc

select m.CPS,m.cohort,m.Class,m.DEPound,m.mbrmths,r.REvenuePMPM,c.AllowedPMPM,c.NetPMPM,t.LDOTargetMER
into #ESRDREsultsPReOverride
 from dbo.ESRDMbrshp_projESRDMembership m 
 inner join dbo.#Plans p on m.CPS=p.planName			-- updated to use #Plans 2/2
left join dbo.ESRDRevenue_ProjESRDRevenue r 
on m.CPS=r.cps and m.cohort=r.cohort and m.class=r.class and m.depound=r.depound
left join dbo.ESRDClaims_ProjESRDClaims C
on m.CPS=c.cps and m.cohort=c.cohort and m.class=c.class and m.depound=c.depound
left join  dbo.ESRDInpt_LDOTargets t
on m.class=t.class AND m.cohort=t.cohort
-- BASE Period Cap Crosswalk

--select * from #ESRDResultsWithHospice where CPS='H5216-068-000' and cohort='Dialysis'

IF (SELECT OBJECT_ID('tempdb..#ESRDResultsWithCap')) IS NOT NULL DROP TABLE #ESRDResultsWithCap
--select * from #ESRDResultsWithCap   order by CPS,Cohort,Class,DePound and cohort='Dialysis'
select c.CPS,

	c.Cohort,
	c.Class,
	c.DEPound, 
	c.MbrMths,
	 c.RevenuePMPM, 
	 Case
	 When Substring(c.class,Len(c.class)-2,3) <> 'PoP' then ISNULL(d.CapDollars,0) 
	 Else 0 END AS CapDollars,
	c.NetPMPM,
	 c.LDOTargetMER
	  into #ESRDResultsWithCap 
	 from #ESRDResultsPreOverride c 
	left join 
	(
		select  b.BY_Plan as CPS,
		a.Cohort, 
		a.class, 
		a.DEPound,
		Case WHen SUM(MbrMths) =0 Then 0 Else SUM(FinalCap)/SUM(MbrMths) END as CapDollars 
		from	
		(
			select pl.cps,cohort,	Class,	DEPound,	MbrMths,	FinalCap
			from  dbo.ESRDInpt_BaseCap bc INNER JOIN dbo.ESRDInpt_BaseCapPlanList PL ON bc.CPS=PL.CPS) a 
			inner join  dbo.ESRDClaims_BYCrosswalkPlans b
			on a.CPS=b.Plan_baseyear 
			group by b.BY_Plan, a.cohort, class,a.dePound
		) d
on c.CPS=d.CPS and c.Cohort=d.cohort and c.class=d.Class and c.DEPound=d.DEPound 



IF (SELECT OBJECT_ID('tempdb..#ESRDResultsWithHospice')) IS NOT NULL DROP TABLE #ESRDResultsWithHospice
--select * from #ESRDResultsWithHospice order by CPS,Cohort,Class,DEPound
select c.CPS,
	c.Cohort,
	c.Class,
	c.DEPound, 
	c.MbrMths,
	Case When c.Cohort='Hospice' then
		Case When d.HospRev is null Then f.finalManRev 
		ELse d.HospRev ENd
		Else c.RevenuePMPM ENd as RevenuePMPM,
	Case When c.Cohort='Hospice' then
		Case When d.HospNet is null Then f.finalManClms
		ELse d.HospNet ENd
		Else c.NetPMPM ENd as NetPMPM,
		c.CapDollars,
	 c.LDOTargetMER,
	 d.HospRev,
	 d.HospNet
	 into #ESRDResultsWithHospice 
	 FROM #ESRDResultsWithCap c 
	LEFT JOIN 
	(
		SELECT  b.BY_Plan AS CPS,
		a.Cohort, 
		a.class, 
		a.DEPound,
		CASE WHEN SUM(a.MbrMths) =0 THEN 0 ELSE SUM(a.HospClmsPMPM*MbrMths)/SUM(a.MbrMths) END AS HospNet,
		CASE WHEN SUM(a.MbrMths) =0 THEN 0 ELSE SUM(a.HosprevPMPM*MbrMths)/SUM(a.MbrMths) END AS HospRev 
		FROM	
		(
			SELECT CPS ,cohort,	 class,	DEPOUND,	MbrMths, HospRevPMPM, HospClmsPMPM
			 FROM dbo.ESRDInpt_HospiceBaseData ) a 
			INNER JOIN  dbo.ESRDClaims_BYCrosswalkPlans b
			ON a.CPS=b.Plan_baseyear 
			GROUP BY b.BY_Plan, a.cohort, a.class,a.dePound
		
		) d

ON c.CPS=d.CPS AND c.Cohort=d.cohort AND c.class=d.class AND c.DEPound=d.DEPound 
left join ( select PlanName,Region,CASE WHEN SNPType = 'Dual Eligible' THEN 'DSNP'
	WHEN SNPType = 'Chronic' THEN 'CSNP'
	WHEN SNPType = 'Institutional' THEN 'CSNP'
	ELSE Product END AS Product  from dbo.vwRollupForecastInfo where PlanYear=@BidYear and IsSCTPlan=1 and IsLiveIndex=1) e
on c.CPS=e.PlanName 
left join  dbo.ESRDInpt_HospiceManualRates f
on e.Region=f.Region AND E.Product=f.product 
--****************************************************************************************************
--                             FINAL ESRDH Output Table
--****************************************************************************************************
delete from dbo.SCT_PerESRDHospiceData where ContractPBPSegment in (select PlanName as COntractPBPSegment from dbo.#Plans) and PlanYearID=@BidYear	-- updated to #Plans 2/2


--select * from #PerESRDHospiceData   order by  ContractPBPSegment,Cohort,Cohortdetail,DEPOUNDSTATUS
insert into dbo.SCT_PerESRDHospiceData
select @BidYear as PlanYearID,
	a.CPS as ContractPBPSegment,
	a.cohort,
	a.class as cohortdetail, 
	a.depound as DEPoundStatus, 
	a.mbrmths as ESRDMembership, 
	a.RevenuePMPM as ESRDPremium, 
	a.NetPMPM AS ESRDFFSClaims, 
	CASE 
		When b.OverrideType = 'MER' then a.RevenuePMPM*b.OverrideAMt-a.NetPMPM 
		ELSE CapDollars 
	END AS OtherClmsAdjustment,
	Case	
		WHEN SUBSTRING(a.Class,LEN(a.class)-2,3)='PoP' THEN A.LDOTargetMER*A.RevenuePMPM - a.NetPMPM 
		ELSE 0 
	END AS LDOSettlement,
	NetPMPM+ CASE 
				When b.OverrideType = 'MER' then a.RevenuePMPM*b.OverrideAMt-a.NetPMPM
				ELSE CapDollars 
			END + 
					Case	
						WHEN SUBSTRING(a.Class,LEN(a.class)-2,3)='PoP' THEN A.LDOTargetMER*A.RevenuePMPM - a.NetPMPM 
						ELSE 0 
					END as ESRDClaim,
	@UserID as LastUpdateByID,
		Current_Timestamp as LastUpdateDateTime 

 from #ESRDREsultsWithHospice a  
 inner join dbo.#Plans c			-- updated to use #Plans 
 on c.PlanName=a.CPS 
 left join  dbo.ESRDInpt_ManualOverridePlans b		
	on a.cps=b.cps 
	where a.cohort is not null
GO
