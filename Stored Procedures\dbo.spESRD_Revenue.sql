SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO




--------------------------------------------------------------------------------------------------------------------------            
------ Stored Procedure NAME: [spESRD_Default]       
------ ----------------------------------------------------------------------------------------------------------------           
------ DATE			VERSION		CHANGES MADE														DEVELOPER              
------ ----------------------------------------------------------------------------------------------------------------            
------ 2023-01-22 		1		Initial Version														Adam Keach
------ 2023-02-02		2		Updated the setup for parameters and to use #PLans table			Tingyao Wang
------							instead of dbo.ESRDClaims_ActivePlanList to allow multiple 
------							users to run the code at the same time

CREATE PROCEDURE [dbo].[spESRD_Revenue]
(
   @RegionId INT,
  @ForecastID VARCHAR(MAX),		-- updated 2/2
  @DivisionID INT,
  @SubRegion VARCHAR(MAX)		-- updated 2/2
)
AS
SET NOCOUNT ON
SET ANSI_WARNINGS OFF

/*
DECLARE @RegionId INT = 0 --comment out for actual stored procedure, only use for testing
DECLARE @ForecastID INT = 0
DECLARE @DivisionID INT = 0
*/

DECLARE @Local_RegionId INT = @RegionId					-- added 2/2
DECLARE @Local_ForecastID VARCHAR(MAX) = @ForecastID	-- added 2/2
DECLARE @Local_DivisionID INT = @DivisionID				-- added 2/2
DECLARE @Local_Subregion VARCHAR(MAX) = @Subregion		-- added 2/2

DECLARE @UserID VARCHAR(13)
DECLARE @BaseYear INT
DECLARE @BidYear INT
DECLARE @Iteration VARCHAR(13)

/************ Replace Hardcoding before Savings **************************/
SET @BaseYear = (SELECT MIN(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive='N') 
SET @BidYear = (SELECT MAX(PlanYearID) FROM dbo.SCT_CurrentIterations WHERE Archive = 'N'); /* For Q3, Q4, FLP, these years will likely need to be hardcoded */
SET @Iteration = CONCAT(
                 (
                     SELECT Year  FROM dbo.SCT_CurrentToolVersion
                 ),
                 'Q',
                 (
                     SELECT Quarter FROM dbo.SCT_CurrentToolVersion
                 )
                       )

-- SET @UserID = 'HUMAD\SCTAPP';

/* ************************************************************************************************************************************************* *
Added 2/2 to create #Plans temp table instead of using dbo.ESRDClaims_ActivePlanList
Get Plan List corresponding to chosen plan/region/division.  For Q3/Q4/FLP purposes, all plans are chose by setting regionID, ForecastID, and DivisionID to 0 
* =================================================================================================================================================== */
IF (SELECT OBJECT_ID('tempdb..#Plans')) IS NOT NULL DROP TABLE #Plans		

SELECT PlanName,RFI.Region													
INTO #Plans																	
FROM dbo.SCT_vwRollupForecastInfo_SCT   RFI WITH (NOLOCK)

INNER JOIN 
	(SELECT PlanYearContractPBPSegment
	FROM dbo.SCT_SavedPlanList
	WHERE PlanYearID = @BidYear) b
				ON RFI.PlanName = b.PlanYearContractPBPSegment
				
WHERE RFI.RollupName = 'LIVE'
      AND RFI.IsSctPlan = 1
      AND RFI.IsToReprice = 0--Leave in - Don't remove for Bids
      AND RFI.IsLiveIndex = 1
      AND (@Local_RegionId = 0 OR RFI.ActuarialRegionID = @Local_RegionId)													
      AND (@Local_ForecastID IS NULL OR RFI.ForecastID IN (Select value from dbo.fnStringSplit(@Local_ForecastID,',')))		
      AND (@Local_DivisionID = 0 OR RFI.ActuarialDivisionID = @Local_DivisionID)											
	  AND (@Local_Subregion IS NULL OR RFI.Subregion IN (Select value from dbo.fnStringSplit(@Local_Subregion,',')));		

-- Crosswalk by County
--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#ESRDRevenue_CountyLVL_CW')) IS NOT NULL DROP TABLE #ESRDRevenue_CountyLVL_CW
--This is joining plan information and rolling it up by BY, CY and CNTY

--Select * From #CountyLVL_CW ORDER BY BidYearCPS
--19,175 Rows Affected
--======================================================================
SELECT DISTINCT
	b.BidYearCPS,
	b.CurrentYearCPS,
	b.SSStateCountyCD
INTO #ESRDRevenue_CountyLVL_CW
FROM dbo.vwRollupForecastInfo a
INNER JOIN dbo.vwSAMCrosswalks b
	ON b.BidYearPlanInfoID=a.PlanInfoID
	AND b.ServiceAreaOptionID=a.ServiceAreaOptionID
WHERE a.RollupName='Live'
	AND b.CurrentYearCPS is NOT NULL
	AND b.BidYearCPS is NOT NULL


-- MRA Revenue
 
--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#ESRDRevenue_RevCrossWalk')) IS NOT NULL DROP TABLE #ESRDRevenue_RevCrossWalk

--Select * From #RevenueCrossWalkPMPM ORDER BY BidYearCPS, SSStateCountyCD, DE_Pound
--32,088 rows affected
--======================================================================
SELECT
	xw.BidYearCPS,
	cm.Cohort,
	rev.DE_Pound as DEPound,
	seq.SequestrationAmount,
	SUM(isNull(rev.ESRD_PMPM*rev2.CYMbrMths,0))*seq.SequestrationAmount as TotalXwalkRevenue,
	Sum(IsNull(rev2.CYMbrMths,0)) as TotalXwalkMbrMths,
	ISNULL(SUM(isNull(rev.ESRD_PMPM*rev2.CYMbrMths,0))/Sum(IsNull(rev2.CYMbrMths,0)),0)*seq.SequestrationAmount as XWalkRevPMPM
Into #ESRDRevenue_revCrossWalk	
	from  dbo.ESRDInpt_Sequestration seq, dbo.ESRDInpt_BYRevenue_CYPlan rev
	inner join 
	(
		Select d.CY_cps, d.BARC_Class,d.DE_Pound,d.County_code,ISNULL(mbr.CYMbrMths,.000001) as CYMbrMths from 
		(
				select distinct CY_CPS,County_Code,DE_Pound,Barc_Class from dbo.ESRDInpt_BYRevenue_CYPlan
		) d
		left join dbo.ESRDInpt_CYMbrsForRevXwalk mbr
		on d.CY_CPS=mbr.CPS and 
		d.barc_class=mbr.barc_class and
		d.DE_POUND=Case When mbr.DEPOUND_IND='Y' Then 1 Else 0 End and
		d.County_Code = mbr.SS_STATE_COUNTY_CD
	) rev2	
	on rev.Barc_Class=rev2.Barc_Class and
		rev.County_Code=rev2.County_Code and
		rev.CY_CPS=rev2.CY_CPS and
		rev.DE_Pound=rev2.DE_Pound
	 inner join #ESRDRevenue_CountyLVL_CW xw
	 on rev.CY_CPS=xw.CurrentYearCPS
	 and rev.County_Code=xw.SSStateCountyCD
	 inner join dbo.ESRDInpt_BARCCohortMap CM
	 on rev.Barc_class=cm.BidYearClass
	 group by xw.BidYearCPS,cm.Cohort,rev.DE_Pound,seq.SequestrationAmount
	 order by BidYearCPS,Cohort,DEPound

	 IF (SELECT OBJECT_ID('tempdb..#ESRDRevenue_RevCrossWalkPMPM')) IS NOT NULL DROP TABLE #ESRDRevenue_RevCrossWalkPMPM

select AllPlans.CPS,AllPlans.Cohort,AllPlans.IsDEPound,AllPlans.BidYearMbrMths, CASE WHEN rfi.SNPType = 'Dual Eligible' THEN 'DSNP'
		WHEN rfi.SNPType = 'Chronic' THEN 'CSNP'
		WHEN rfi.SNPType = 'Institutional' THEN 'CSNP'
		Else spl.Product 
	End as BY_Product,
	spl.region as BY_Region,
	xwalkRevPMPM 
	into #ESRDRevenue_RevCrosswalkPMPM
	from
(
	 select CPS,cm.cohort,barc.IsDEPOund,SUM(BidYearMemberMonths) as BidYearMbrMths
	 from dbo.ESRDInpt_BarcMembership barc inner join
	 dbo.ESRDInpt_BARCCohortMap cm
	 on barc.BidYearClass=cm.BidYearClass
	 group by cps,cm.cohort,barc.IsDEPound
) AllPlans
left join
 #ESRDRevenue_revCrossWalk  xwalkPlans
on AllPlans.cps=xwalkplans.BidYearCPS and 
allplans.cohort=xwalkplans.cohort and 
Case When AllPlans.IsDEPound='Y' then 1 Else 0 End= xwalkplans.DEPound
	left join 
	dbo.sct_savedPlanList spl
		on AllPlans.CPS=spl.ContractPBPSegment
	left join	
	dbo.vwRollupForecastInfo rfi
	on AllPlans.CPS=rfi.PlanName
	where spl.PlanYearID=@BidYear


--=================================================================================================================================================
IF (SELECT OBJECT_ID('tempdb..#ESRDRevenue_InclManual')) IS NOT NULL DROP TABLE #ESRDRevenue_InclManual

select xwalk.CPS,
	xwalk.Cohort,
	Case When xwalk.ISDEPound='Y' Then 'DE#' Else 'NonDE#' End as DEPound,
	prmbr.class,
	xwalk.BidYearMbrMths,
	xwalk.xwalkRevPMPM,
	xwalk.BY_Product,
	xwalk.BY_Region,
	seq.SequestrationAmount as ManualSequestrationAmt,
	npr.ManualRevPMPM*seq.SequestrationAmount as ManualRevPMPM,
	ISNULL(xwalk.xwalkrevPMPM,ManualRevPMPM*seq.SequestrationAmount) as ESRDRevPMPM 
	into #ESRDRevenue_inclManual
	from dbo.ESRDInpt_Sequestration seq,  
	 dbo.ESRDMbrshp_ProjESRDMembership prmbr
	 left join
	#ESRDRevenue_RevCrosswalkPMPM xwalk
	on xwalk.CPS=prmbr.CPS and
		xwalk.Cohort=prmbr.cohort and
		Case When xwalk.IsDePound='Y' then 'DE#' else 'NonDE#' End=prmbr.DePound
LEFT JOIN  dbo.ESRDInpt_NewPlanRev  npr
	ON xwalk.BY_Product=npr.BY_Product
	AND xwalk.BY_Region=npr.BY_Region
	AND xwalk.Cohort=npr.Cohort
	And xwalk.IsDEPound=npr.IsDEPound

	order by xwalk.CPS,xwalk.Cohort,prmbr.Class,Case When xwalk.ISDEPound='Y' Then 'DE#' Else 'NonDE#' End
	
	
IF (SELECT OBJECT_ID('tempdb..#PlanCutRevRel')) IS NOT NULL DROP TABLE #PlanCutRevREl
--Select * from #PlanCutRevRel where cohort='Transplant' order by CPS, Cohort, CohortStatus,DEPound,CLass and Product='DSNP' order by CPS,cohort,dialysisSubtype,vendor,DEPound

	select 
		a.CPS, 
		a.cohort, 
		a.class, 
		a.DEPound, 
		Case WHen b.PlanDECutMbrMths=0 then 0 Else a.mbrMths/b.PlanDECutMbrMths END as MbrPcnt,  
		c.RevRelFx  as RevRelFx 
	into #PlanCutRevREl
	from  dbo.ESRDMbrshp_ProjESRDMembership a 
	left join 
	(
		select CPS,
				cohort, 
				DEPound, 
				SUM(MbrMths) as PlanDECutMbrMths 
			from   dbo.ESRDMbrshp_ProjESRDMembership
			group by CPS, cohort,  DEPound
	) b
	on a.CPS=b.CPS and 
	a.cohort=b.cohort AND 
	a.DEPound=b.DEPound
	left join DBO.ESRDInpt_RevRelFx c
	on a.Cohort=c.cohort and  
	a.class=c.class  and  
	a.BY_Product=c.Product and 
	a.DEPound=c.dePound
/******************************************************************************************************/
/************************* FINAL ESRD REVENUE TABLE ***************************************************/
/******************************************************************************************************/



--***********Add in join to #Proj ESRD Membership to Higher Level Revenue to get revenue at class level *****


IF (SELECT OBJECT_ID('tempdb..#ESRDRevenue_ClassAllocation')) IS NOT NULL DROP TABLE #ESRDRevenue_ClassAllocation
--select * from #PreFinalESRDRevenue order by CPS,Cohort,DEPound,Class

select e.CPS, 
e.BY_product as Product , 
e.by_region as region, 
e.cohort, 
e.class,
e.DEPound, 
Case 
	When e.cohort='Hospice' then 0
	Else 
		Case When a.weightedRel=0 Then e.ESRDRevPMPM 
		Else e.ESRDRevPMPM/a.WeightedREl 
	END*ISNULL(g.RevRelFx,.9)
END as RevenuePMPM

into #ESRDRevenue_ClassAllocation
FROM 
#ESRDRevenue_InclManual e 
inner join dbo.#Plans Z		-- updated to use #Plans
on e.CPS=z.PlanName 
left join 
(
	select CPS, 
			cohort, 
			DEPound,
			SUM(MbrPcnt*Case When Cohort='Hospice' then 0 Else RevRelFx End) as WeightedRel 
	 from #PlanCutRevRel
	group by CPS,Cohort,DEPound
) a
on e.CPS=a.CPS and 
e.cohort=a.cohort  AND 
e.DEPound=a.DEPound
left join dbo.ESRDInpt_RevRElFx g
on e.cohort=g.cohort and e.Class=g.Class and  e.BY_Product=G.Product and e.DEpound=g.DEPound
order by e.CPS, e.cohort, e.Class, e.DEPound


--IF (SELECT OBJECT_ID('tempdb..#ESRDRevenue_ProjESRDRevenue')) IS NOT NULL DROP TABLE #ESRDRevenue_ProjESRDRevenue
delete from dbo.ESRDRevenue_ProjESRDrevenue
insert into dbo.ESRDRevenue_projESRDRevenue
SELECT rev.CPS, 
rev.Cohort, 
Rev.Class, 
Rev.DEPound, 
(
	CASE WHEN (SUM(ISNULL(barc.MbrMths,0))=0) THEN 0 
	ELSE SUM(rev.RevenuePMPM*ISNULL(barc.MbrMths,0))/SUM(ISNULL(barc.MbrMths,0)) 
	END
)  AS RevenuePMPM 

FROM  #ESRDRevenue_ClassAllocation rev 
LEFT JOIN dbo.ESRDMbrshp_projESRDMembership barc 
ON barc.CPS=rev.cps AND 
barc.cohort=rev.cohort  AND 
barc.class=rev.Class AND 
barc.DEPound=Rev.DEPound
group BY rev.CPS, rev.Cohort,Rev.Class, Rev.DEPound
ORDER BY rev.CPS, rev.Cohort, Rev.Class, Rev.DEPound
GO
