SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- PROCEDURE NAME: spDashboardReportPREPANDSCTSQLRefreshPBI2

-- DESCRIPTION: This SP returns extract for "PREP SQL Refresh & SCT SQL Refresh" user action from dbo.SCT_RunTimeLog table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--
-- TABLES:
--    Read:
--      dbo.SCT_RunTimeLog
--		
-- Example 
-- Exec [dbo].[spDashboardReportPREPANDSCTSQLRefreshPBI2]  '2023-09-20 13:40:07.000'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-23			1			Initial Version                                                                         Sheetal Patil
-- 2024-Mar-19			2			Changed the whole logic to get data from dbo.SCT_RunTimeLog table						Sheetal Patil
-- 2024-Apr-19			3			Converted ExecutionTime from seconds to miliseconds										Sheetal Patil
---------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROC [dbo].[spDashboardReportPREPANDSCTSQLRefreshPBI2]
 (@LastSuccessfulRunTimestampSCT DATETIME)

AS
BEGIN

SET NOCOUNT ON;  
SET ANSI_WARNINGS OFF;  


SELECT
		CASE WHEN ToolUsed = 'SCT' THEN 'SCT SQL Refresh'	
			  WHEN ToolUsed = 'PREP' THEN 'PREP SQL Refresh' END AS [UserActions]
		,UserID AS [UserID]
		,StartTime AS [Run Date]
		,ToolUsed AS [Type]
		,'NULL' AS [Plans]
		,PlanCount AS [Plan Count]		
		,RunTimeSeconds*1000 AS ExecutionTime
		,0 AS characterEvenCount
		,0 AS ErrorCount
		,EndTime AS [RunEndDate]
		,' ' AS [ErrorMessage]		
FROM dbo.DashboardReport_SCT_RunTimeLog WITH(NOLOCK)
WHERE Step = 'Full_RunTime' AND PlanCount <> 0
AND StartTime > @LastSuccessfulRunTimestampSCT  




END
GO
