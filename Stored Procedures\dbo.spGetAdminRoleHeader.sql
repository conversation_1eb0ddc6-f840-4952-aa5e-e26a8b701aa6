SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spGetAdminRoleHeader
--
-- Author:		T Y Bhavana
--
-- Create date: 07-Oct-2021
-- HEADER UPDATED: 07-Oct-2021
--
-- DESCRIPTION: Returns the list of Roles
--
-- PARAMETERS:
--	Input:
--		
--
-- RETURNS: 
--		A recordset containing the list of available Roles.
--
-- TABLES:
--  Read:
--		AdminRoleHeader
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--    sp_helprolemember (system)
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
--2021-oct-07			1			Intial version													 Bhavana                       
-- ----------------------------------------------------------------------------------------------------------------------



-- Description:	Get spGetAdminRoleHeader
-- =============================================
CREATE PROCEDURE [dbo].[spGetAdminRoleHeader]
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

   SELECT  RoleName FROM dbo.AdminRoleHeader  WITH(NOLOCK)
END



GO
