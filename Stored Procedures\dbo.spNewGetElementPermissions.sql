SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------      
-- Stored Procedure NAME: [spNewGetElementPermissions]      
--      
-- AUTHOR: Deepthi Thiyagu       
-- CREATED DATE: 2013-MAR-21      
--      
-- DESCRIPTION: Stored Procedure is responsible to get permission for new element      
--      
-- PARAMETERS:      
-- Input:      
      
-- Output:      
--      
-- TABLES:       
-- Read:      
-- AdminUserHeader
-- AdminUserDetail
-- AdminRoleHeader
-- AdminRoleDetail
--      
-- Write:      
--      
-- VIEWS:      
--      
-- FUNCTIONS:      
--      
-- STORED PROCS:      
--      
-- $HISTORY       
-- ----------------------------------------------------------------------------------------------------------------------      
-- DATE       VERSION  CHANGES MADE                                 DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------      
-- 2013-MAR-21  1      Initial Version                              Deepthi Thiyagu
-- 2015-May-12	2	   Updated logic for specific user		        Deepali Mittal
 --2019-Oct-30	3	   Replace @UserID from char(13) to char(7)		Chhavi Sinha 
-- ----------------------------------------------------------------------------------------------------------------------      
  
CREATE PROC [dbo].[spNewGetElementPermissions]
    @ElementID VARCHAR(MAX) ,
    @UserID CHAR(7)
AS
    SET NOCOUNT ON;            
     
    BEGIN 
		  
        IF EXISTS ( SELECT  p.Value
                    FROM    ( SELECT    Value
                              FROM      fnStringSplit(@ElementID, ',')
                            ) p
                    WHERE   p.Value NOT IN (
                            SELECT  d.ElementID
                            FROM    AdminUserDetail d
                            WHERE   d.ElementID IN (
                                    SELECT  Value
                                    FROM    fnStringSplit(@ElementID, ',') )
                                    AND d.UserID = @UserID
                            UNION
                            SELECT  r.ElementID
                            FROM    AdminRoleDetail r
                                    INNER JOIN AdminUserHeader u ON r.RoleID = u.RoleID
                            WHERE   u.IsEnabled = 1
                                    AND u.UserID = @UserID
                                    AND r.ElementID IN (
                                    SELECT  Value
                                    FROM    fnStringSplit(@ElementID, ',') ) ) )
            BEGIN 
                SELECT  d.ElementID ,
                        d.IsVisible ,
                        d.IsPreviewable ,
                        d.IsDraftable ,
                        d.IsSaveable ,
                        d.IsHideable ,
                        d.IsOverrideable ,
                        d.IsRunable
                FROM    AdminUserDetail d
                WHERE   UserID = @UserID
                        AND d.ElementID IN (
                        SELECT  Value
                        FROM    fnStringSplit(@ElementID, ',') )
                UNION ALL
                SELECT  r.ElementID ,
                        r.IsVisible ,
                        r.IsPreviewable ,
                        r.IsDraftable ,
                        r.IsSaveable ,
                        r.IsHideable ,
                        r.IsOverrideable ,
                        r.IsRunable
                FROM    AdminRoleDetail r
                        INNER JOIN AdminUserHeader u ON r.RoleID = u.RoleID
                WHERE   u.IsEnabled = 1
                        AND u.UserID = @UserID
                        AND r.ElementID IN (
                        SELECT  p.Value
                        FROM    ( SELECT    Value
                                  FROM      fnStringSplit(@ElementID, ',')
                                ) p
                        WHERE   p.Value NOT IN (
                                SELECT  d.ElementID
                                FROM    AdminUserDetail d
                                WHERE   d.ElementID IN (
                                        SELECT  Value
                                        FROM    fnStringSplit(@ElementID, ',') )
                                        AND d.UserID = @UserID ) )
                UNION ALL
                SELECT  0 AS ElementID ,
                        0 AS IsVisible ,
                        0 AS IsPreviewable ,
                        0 AS IsDraftable ,
                        0 AS IsSaveable ,
                        0 AS IsHideable ,
                        0 AS IsOverrideable ,
                        0 AS IsRunable;  
            END; 
	
        ELSE
            BEGIN        
                SELECT  d.ElementID ,
                        d.IsVisible ,
                        d.IsPreviewable ,
                        d.IsDraftable ,
                        d.IsSaveable ,
                        d.IsHideable ,
                        d.IsOverrideable ,
                        d.IsRunable
                FROM    AdminUserDetail d
                WHERE   UserID = @UserID
                        AND d.ElementID IN (
                        SELECT  Value
                        FROM    fnStringSplit(@ElementID, ',') )
                UNION ALL
                SELECT  r.ElementID ,
                        r.IsVisible ,
                        r.IsPreviewable ,
                        r.IsDraftable ,
                        r.IsSaveable ,
                        r.IsHideable ,
                        r.IsOverrideable ,
                        r.IsRunable
                FROM    AdminRoleDetail r
                        INNER JOIN AdminUserHeader u ON r.RoleID = u.RoleID
                WHERE   u.IsEnabled = 1
                        AND u.UserID = @UserID
                        AND r.ElementID IN (
                        SELECT  p.Value
                        FROM    ( SELECT    Value
                                  FROM      fnStringSplit(@ElementID, ',')
                                ) p
                        WHERE   p.Value NOT IN (
                                SELECT  d.ElementID
                                FROM    AdminUserDetail d
                                WHERE   d.ElementID IN (
                                        SELECT  Value
                                        FROM    fnStringSplit(@ElementID, ',') )
                                        AND d.UserID = @UserID ) ); 
				
	
            END;

    END;
GO
