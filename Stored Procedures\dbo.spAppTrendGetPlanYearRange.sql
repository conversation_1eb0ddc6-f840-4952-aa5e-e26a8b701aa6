SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

      
-- ----------------------------------------------------------------------------------------------------------------------          
-- PROCEDURE NAME: [dbo].[spAppTrendGetPlanYearRange]         
--          
-- AUTHOR: Kiran Kola        
--          
-- CREATED DATE:2020-APR-01       
-- Type:       
-- DESCRIPTION: Procedure responsible for retrieving Plan Years  
--          
-- PARAMETERS:          
-- Input:       
         
-- TABLES:         
      
-- Read:          
--        
      
-- Write:          
--          
-- VIEWS:          
--          
-- FUNCTIONS:          
-- STORED PROCS:          
--            
-- $HISTORY           
-- ----------------------------------------------------------------------------------------------------------------------          
-- DATE   VERSION  CHANGES MADE     DEVELOPER             
-- ----------------------------------------------------------------------------------------------------------------------          
-- 2020-APR-01 1  Initial Version              Kiran Kola      
-- ----------------------------------------------------------------------------------------------------------------------          
CREATE PROCEDURE [dbo].[spAppTrendGetPlanYearRange]    
AS          
BEGIN          
select Convert(varchar,min(PlanYearID)) +'-' +Convert(varchar,max(PlanYearID)) from    LkpIntPlanYear
END      
  
GO
