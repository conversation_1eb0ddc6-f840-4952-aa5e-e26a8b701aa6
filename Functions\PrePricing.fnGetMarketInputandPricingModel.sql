SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO
-------------------------------------------------------------------------------------------------------------------------  
-- VIEW NAME: [PrePricing].[fnGetMarketInputandPricingModel]
--  
-- AUTHOR: <PERSON>
--  
-- CREATED DATE: 2024-Nov-1  
--  
-- DESCRIPTION: Primary dataset to populate the Market Input matrix of data on Power BI for the benefits interface. 
--				Combines Benefit interface with  and pricing model data.
--
-- TABLES:  
--  Read: 
-- VIEWS:  
--        
-- FUNCTIONS:  
--  
-- HISTORY:  
-- -------------------------------------------------------------------------------------------------------------------
-- DATE        VERSION      CHANGES MADE						                        DEVELOPER 
---------------------------------------------------------------------------------------------------------------------- 
-- 2024-Nov-1    1			Initial Version                                             Adam Gilbert
-- 2025-FEB-11   2			Added isnull to BidYearInValue		                        Adam Gilbert
-- 2025-FEB-12   3          Moved Power BI Business Logic for conditional formatting  Priyadarshini Deshmukh
-- 2025-Mar-24   4			Enabled Multiple region selection			                Adam Gilbert
-- 2025-Apr-14   5			Numeric formatting fix. .1 vs 0.1			                Priyadarshini Deshmukh

-- -------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [PrePricing].[fnGetMarketInputandPricingModel](
	@Region VARCHAR(500) 
) 
RETURNS @Results TABLE(
MarketInputValueID INT
,PlanInfoID INT
,CategoryID INT
,CategoryName VARCHAR(100)
,SubcategoryID INT
,SubCategory VARCHAR(100)
,SubCategorySortOrder INT
,IsCostShareType BIT
,[CategorySortOrder] INT
,INValue VARCHAR(200)
,OONValue VARCHAR(200)
,BenefitChangeValue VARCHAR(30)
,Note VARCHAR(1000)
,LastUpdateByID VARCHAR(100)
,BidYearINValue VARCHAR(200)
,BidYearOONValue VARCHAR(200)
,CurrentYearINValue VARCHAR(200)
,CurrentYearOONValue VARCHAR(200)
,[CFCurrentYearINColor] VARCHAR(25)
,[CFCurrentYearOONColor] VARCHAR(25)
,[CFBidYearINColor] VARCHAR(25)
,[CFBidYearOONColor] VARCHAR(25)
)
BEGIN

DECLARE @selectedRegions TABLE (
regions VARCHAR(50)
)

INSERT INTO @selectedRegions SELECT value FROM STRING_SPLIT(@region,',');


WITH
 filterregion AS (
 SELECT actuarialregionid 
 FROM SavedRegionInfo 
 JOIN @selectedRegions ON ActuarialRegion = regions OR @Region='All'
 ),
planList AS (
SELECT planinfoid
FROM PrePricing.PlanInfo ppi
JOIN dbo.SavedMarketInfo smi ON ppi.MarketID =smi.ActuarialMarketID 
JOIN filterregion sri ON smi.ActuarialRegionID = sri.ActuarialRegionID
WHERE isEnabled=1) ,
missingEntry AS (
    SELECT Planinfoid, subcategoryid
    FROM planlist 
    CROSS JOIN prepricing.marketinputsubcategory WITH (NOLOCK)
    EXCEPT
    SELECT miv.Planinfoid, subcategoryid
    FROM PrePricing.MarketInputValue miv WITH (NOLOCK)
),
MarketInput AS (
    SELECT 
        MarketInputValueID,
        miv.PlanInfoID,
        SubcategoryID,
		CASE
			WHEN ISNUMERIC(INValue)=1 --make sure field is numeric data
			THEN CASE 
				WHEN CAST(INValue AS DECIMAL (18,6))  = 0  --if its 0, then 0
				THEN '0' 
				ELSE FORMAT(CAST(INValue AS DECIMAL (18,6)) , '#.######') --otherwise make the field a decimal data type to apply format function
				END
			ELSE ISNULL(INValue,'') --If the field isn't numeric, return the value as is.
		END AS INvalue,

		CASE
			WHEN ISNUMERIC(OONValue)=1 
			THEN CASE 
				WHEN CAST(OONValue AS DECIMAL (18,6))  = 0 
				THEN '0' 
				ELSE FORMAT(CAST(OONValue AS DECIMAL (18,6)) , '#.######') 
				END
			ELSE ISNULL(OONValue,'')
		END AS OONValue,
        CAST(BenefitChangeValue AS DECIMAL(10,2)) AS BenefitChangeValue,
        ISNULL(Note, '') AS Note,
        miv.LastUpdateDateTime,
        miv.LastUpdateByID
    FROM prepricing.marketinputvalue miv WITH (NOLOCK)
    JOIN planlist ppi WITH (NOLOCK)
        ON miv.PlanInfoID = ppi.PlanInfoID
    UNION ALL
    SELECT 
        NULL AS MarketInputValueID,
        Planinfoid,
        subcategoryid,
        '' AS INValue,
        '' AS OONValue,
        NULL AS BenefitChangeValue,
        '' AS Note,
        NULL AS lastupdatedatetime,
        NULL AS lastupdatebyid
    FROM missingEntry
)
INSERT INTO @Results
SELECT
    mi.MarketInputValueID,
    mi.PlanInfoID,
    cat.CategoryID,
    cat.CategoryName,
    mi.SubcategoryID,
    SubCategoryName AS SubCategory,
    subcat.SortOrder AS SubCategorySortOrder,
    subcat.IsCostShareType,
	Cat.SortOrder AS [CategorySortOrder],
    CASE
        WHEN cat.CategoryID IN (1,10) THEN bid.BidYearINValue
        ELSE mi.INValue
    END AS INValue,
    CASE
        WHEN cat.CategoryID IN (1,10) THEN bid.BidYearOONValue
        ELSE mi.OONValue
    END AS OONValue,
    mi.BenefitChangeValue,
    mi.Note,
    mi.LastUpdateByID,
    ISNULL(bid.BidYearINValue, '') AS BidYearINValue,
    ISNULL(bid.BidYearOONValue, '') AS BidYearOONValue,
    ISNULL(curr.CurrentYearINValue, '') AS CurrentYearINValue,
    ISNULL(curr.CurrentYearOONValue, '') AS CurrentYearOONValue,
    -- Column 1: 25/26 IN CF = CFCurrentYearINColor
    CASE
        WHEN mi.INValue = ISNULL(curr.CurrentYearINValue, '') THEN 'white'
        ELSE '#C6E9ED'
    END AS [CFCurrentYearINColor],
    -- Column 2: 24/25 OON CF = CFCurrentYearOONColor
    CASE
        WHEN mi.OONValue = ISNULL(curr.CurrentYearOONValue, '') THEN 'white'
        ELSE '#C6E9ED'
    END AS [CFCurrentYearOONColor],
    -- Column 3: Market/Act IN CF = CFBidYearINColor
    CASE
        WHEN mi.INValue = ISNULL(bid.BidYearINValue, '') THEN 'black'
        ELSE '#AE0061'
    END AS [CFBidYearINColor],
    -- Column 4: Market/Act OON CF = CFBidYearOONColor
    CASE
        WHEN mi.OONValue = ISNULL(bid.BidYearOONValue, '') THEN 'black'
        ELSE '#AE0061'
    END AS [CFBidYearOONColor]
FROM MarketInput mi WITH (NOLOCK)
JOIN prepricing.marketinputsubcategory subcat WITH (NOLOCK)
    ON mi.subcategoryid = subcat.subcategoryid
JOIN prepricing.MarketInputCategory cat WITH (NOLOCK)
    ON subcat.categoryid = cat.categoryid
LEFT JOIN prepricing.PricingModelValueBidYear bid WITH (NOLOCK)
    ON mi.PlanInfoID = bid.planinfoid 
    AND mi.SubCategoryID = bid.subcategoryid
LEFT JOIN prepricing.PricingModelValueCurrentYear curr WITH (NOLOCK)
    ON mi.PlanInfoID = curr.planinfoid 
    AND mi.SubCategoryID = curr.subcategoryid;


RETURN
END
GO
