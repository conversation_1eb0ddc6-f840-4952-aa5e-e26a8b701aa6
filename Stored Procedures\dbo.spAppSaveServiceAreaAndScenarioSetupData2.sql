SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author: <PERSON><PERSON><PERSON>   
-- Create date: 05-01-2018
-- Description:  Update  Service Area
--      
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES: SavedForecastSetup,SavedServiceAreaOption,SavedMarketInfo,LkpPlanType , SavedPlanInfo , LkpPFFSNetwork,LkpSNPType,LkpMAPlanDesign
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Jan-05  1		  Initial version.											Manisha Tyagi
-- 2018-Jan-10  2         Added parameter @MessageFromBackend                       Pooja Dahiya
-- 2018-Mar-20  3         Added parameter @IsForAllScenarios						Pooja Dahiya
--2018-June-15  4         Made changes for new screen                               Kritika Singh
--2018- Oct-22	5			Updated MADesign code to save correct values			Deepali
-- 2018-Nov-10  6         Rollup vaidation											Pooja Dahiya
--2018-Nov-18	7		  Rollup Validation											Nagarani Kolanchelmi
--2018-Nov-21	8		  Added code to handle null service area option				Nagarani Kolanchelmi
-- 2018-Dec-19	9		  Updated Error message and included logging exception	    Kritika Singh
-- 2019-Jan-16	10		  Messaging Changes											Deepali
-- 2019-Dec-16  11        Updating isToReprice on SavedForecasteSetup               Brent Osantowski
--2019-Dec-20	12			Added logic to update PlanTypeName in CalcFinalPremium	Deepali Mittal
--							and MAReportPlanLevel
-- 2020-July-15 13		  Updated PlanTypeID to Update SavePlanAssumptions			Phillip Leigh
-- 2020-Dec-28  14        Implemented NOLOCK                                        Mahendran Chinnaiah

-- ----------------------------------------------------------------------------------------------------------------------                
CREATE PROCEDURE [dbo].[spAppSaveServiceAreaAndScenarioSetupData2]
 @ForecastID INT ,
 @ServiceAreaOption VARCHAR(max),
 @MarketSelection VARCHAR(30), 
 @Plan CHAR(13),
 @ScenarioNumber tinyint,
 @ScenarioName VARCHAR(40),
 @ScenarioDescription VARCHAR(100),
 @HPASID CHAR(13),
 --@PlanName VARCHAR(60),
 @PlanType VARCHAR(30),
 @ProductType VARCHAR(20),
 @PFFSNetwork VARCHAR(30),
 @SNPType VARCHAR(30),
 @MAPlanDesign VARCHAR(30),
 @MessageFromBackend NVARCHAR(500) OUTPUT,
 @Result BIT OUT,
 @IsForAllScenarios BIT,
 @LastUpdateByID CHAR(7),
 @isForSARollupValidation BIT,
 @RollUpIdList varchar(max),
 @isRollupChanged BIT
 AS
BEGIN        

    BEGIN TRANSACTION;
    BEGIN TRY   
	BEGIN
      
   ------------------Update SavedProjectedCrosswalk and SavedServiceAreaOption------------------------------------------------------------
   CREATE TABLE #temp(ForecastID INT, CPSScenarioIdName VARCHAR(MAX),FromPlanInfoID smallint,SSStateCountyCD char(5));
   CREATE table #temp2(ForecastID INT,FromPlanInfoID smallint,SSStateCountyCD char(5));
   CREATE TABLE #temp3(existForecastID INT,CPSScenarioIdName VARCHAR(MAX), NewForecastID INT);

   DECLARE @ExistingSAOptionID tinyint;
   IF(@isForSARollupValidation = 1)
	BEGIN
		SELECT @ExistingSAOptionID=ServiceAreaOptionID FROM SavedForecastSetup WITH(NOLOCK) WHERE ForecastID = @ForecastID;

		  Declare @SAId tinyint
		  select @SAId = case when CHARINDEX(':', coalesce(@ServiceAreaOption, ''),1) > 0 
          
            then LEFT(@ServiceAreaOption, CHARINDEX(':',@ServiceAreaOption)-1)
            else null end
		
		UPDATE dbo.SavedForecastSetup 
		SET ServiceAreaOptionID = @SAId,
			isToReprice = 1
		WHERE  ForecastID = @ForecastID;
	END
	ELSE
	BEGIN
	Declare @SAId2 tinyint
		  select @SAId2 = case when CHARINDEX(':', coalesce(@ServiceAreaOption, ''),1) > 0 
          
            then LEFT(@ServiceAreaOption, CHARINDEX(':',@ServiceAreaOption)-1)
            else null end
	
		UPDATE dbo.SavedForecastSetup 
		SET ServiceAreaOptionID = @SAId2,
			isToReprice = 1,
			LastUpdateByID=@LastUpdateByID,
			LastUpdateDateTime=GETDATE()
		WHERE ForecastID= @ForecastID;
	END
		
   IF(@ServiceAreaOption<>'')
   BEGIN
		
		--CREATE TABLE #temp(ForecastID INT, CPSScenarioIdName VARCHAR(MAX),FromPlanInfoID smallint,SSStateCountyCD char(5))
        INSERT INTO #temp
        SELECT DISTINCT  sfs.ForecastID, sp.CPS+':'+CAST(sfp.ScenarioNbr AS VARCHAR)+':'+ sfp.ScenarioName AS CPSScenarioIdName, spc.FromPlanInfoID,spc.SSStateCountyCD 
		FROM SavedRollupInfo srinfo WITH(NOLOCK)
			INNER JOIN dbo.SavedRollupForecastMap sfs WITH(NOLOCK) on srinfo.RollupID=sfs.RollupID
			INNER JOIN dbo.SavedForecastSetup sfp WITH(NOLOCK) on sfs.ForecastID=sfp.ForecastID
			INNER JOIN dbo.SavedProjectedCrosswalk spc WITH(NOLOCK) on sfp.PlanInfoID=spc.PlanInfoID AND sfp.ServiceAreaOptionID=spc.ServiceAreaOptionID
			INNER JOIN dbo.savedPlanInfo sp WITH(NOLOCK) on sfp.PlanInfoID=sp.PlanInfoID
			WHERE ( (@isRollupChanged=1 AND @RollUpIdList<>'' AND srinfo.RollupName IN (select value from dbo.fnStringSplit(@RollUpIdList,',')))
						OR (@isRollupChanged=0 AND sfs.RollupID IN (SELECT RollupID FROM SavedRollupForecastMap WITH(NOLOCK) WHERE ForecastID= @ForecastID)))
			AND spc.FromPlanInfoID<>0
		--WHERE sfs.RollupID IN (SELECT RollupID FROM SavedRollupForecastMap WHERE ForecastID= @ForecastID) AND spc.FromPlanInfoID<>0
    
		--CREATE table #temp2(ForecastID INT,FromPlanInfoID smallint,SSStateCountyCD char(5))
		INSERT INTO #temp2
		SELECT f.ForecastID ,FromPlanInfoID,SSStateCountyCD 
		FROM SavedProjectedCrosswalk  c WITH(NOLOCK)
			INNER JOIN SavedPlanInfo p WITH(NOLOCK) ON c.PlanInfoID=p.PlanInfoID
			INNER JOIN SavedForecastSetup f WITH(NOLOCK) ON f.PlanInfoID=p.PlanInfoID AND c.ServiceAreaOptionID=f.ServiceAreaOptionID WHERE ForecastID=@ForecastID
		
		--CREATE TABLE #temp3(existForecastID INT,CPSScenarioIdName VARCHAR(MAX), NewForecastID INT)
		INSERT INTO #temp3
		SELECT DISTINCT t.ForecastID as existForecastID, t.CPSScenarioIdName AS CPSScenarioIdName, t2.ForecastID as NewForecastID 
		FROM #temp t 
			INNER JOIN #temp2 t2 ON cast(t.FromPlanInfoID as varchar(30))+'-'+t.SSStateCountyCD = cast(t2.FromPlanInfoID as varchar(30))+'-'+t2.SSStateCountyCD
			
		DECLARE @PlanInfoID smallint,@SAOptionID tinyint;
			
		SELECT @PlanInfoID=PlanInfoID,@SAOptionID=ServiceAreaOptionID 
		FROM SavedForecastSetup WITH(NOLOCK)
		WHERE ForecastID=@ForecastID;
			
			
		DELETE FROM #temp3
		WHERE existForecastID in (
			SELECT forecastID FROM SavedForecastSetup 
			WHERE CAST( PlanInfoID as varchar) +':'+CAST(ServiceAreaOptionID as varchar) IN 
				(SELECT CAST( PlanInfoID as varchar) +':'+CAST(ServiceAreaOptionID as varchar)
				FROM SavedProjectedCrosswalk 
				where MapID = (SELECT  MAX(MapID) FROM SavedProjectedCrosswalk WHERE PlanInfoID=@PlanInfoID AND ServiceAreaOptionID = ISNULL(@SAOptionID,-1) )
				)
			);
			
		--IF EXISTS(SELECT * FROM #temp3)
		--BEGIN
		--DELETE FROM dbo.SavedRollupForecastMap 
		--WHERE RollupID IN (SELECT RollupID FROM SavedRollupForecastMap WHERE ForecastID= @ForecastID) 
		--	AND ForecastID IN (SELECT ISNULL(existForecastID,-1) FROM  #temp3 t)
		--END
	END
	IF(@isForSARollupValidation = 1)
	BEGIN
		
		UPDATE dbo.SavedForecastSetup 
		SET ServiceAreaOptionID = @ExistingSAOptionID,
		isToReprice = 1
		WHERE ForecastID = @ForecastID;

		IF EXISTS (SELECT * FROM #temp3)
		BEGIN
			SET @Result = 0;
			DECLARE @originalScenario VARCHAR(MAX);

			SELECT @originalScenario = COALESCE(@originalScenario + ', ', '') + CPSScenarioIdName
			FROM #temp3;

            SET @MessageFromBackend= @ServiceAreaOption +' cannot be assigned to Scenario: ' +CAST(@ScenarioNumber AS VARCHAR) +':'+@ScenarioName +
            ' as it would violate the condition of Rollup needing to have unique crosswalks contained in them. If we click on confirm ,
             the original '+@originalScenario+' scenarios Rollup assignment will be removed.'
		END
		ELSE
		BEGIN
			SET @Result = 1;
			SET @MessageFromBackend = '';
		END
	END
	ELSE
	BEGIN			
		--------------------Update PlanTypeId------------------------------------------------------------

		IF EXISTS(SELECT * FROM #temp3)
		BEGIN
			DELETE FROM dbo.SavedRollupForecastMap 
			WHERE ((@isRollupChanged=0 AND RollupID IN (SELECT RollupID FROM SavedRollupForecastMap WHERE ForecastID= @ForecastID))
			OR (@isRollupChanged=1 AND RollupID IN (SELECT RollupID FROM SavedRollupInfo WHERE RollupName IN (select value from dbo.fnStringSplit(@RollUpIdList,',')) ) ) )
			AND ForecastID IN (SELECT ISNULL(existForecastID,-1) FROM  #temp3 t)
		END
	
		declare @PlanTypeId int
		select 	@PlanTypeId=PlanTypeID from dbo.LkpPlanType WITH(NOLOCK) where PlanType=@PlanType
				
		Update SP
		set sp.PlanTypeID=@PlanTypeId,
		LastUpdateByID=@LastUpdateByID,
		LastUpdateDateTime=GETDATE()
		From dbo.SavedPlanInfo SP 
		INNER JOIN  dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
		where SS.ForecastID=@ForecastID
				

	Update SPA
		set SPA.IsMAPD=@PlanTypeId -1
		From 
         dbo.SavedForecastSetup SS
	
		Inner Join  SavedPlanAssumptions SPA On SS.ForecastID =SPA.ForecastID
		where SS.PlanInfoID=@PlanInfoID
			




					
		--------------------Update ProductTypeId------------------------------------------------------------
				
		--declare @ProductTypeId int
		--select 	@ProductTypeId=ProductTypeID from dbo.LkpProductType where Description=@ProductType
		declare @ProductTypeId INT,@PlanTypeName VARCHAR(30),@PlanYearID INT
		select 	@ProductTypeId=ProductTypeID,@PlanTypeName=ProductType from dbo.LkpProductType WITH(NOLOCK) where Description=@ProductType
		SELECT @PlanYearID=PlanYearID FROm SavedPlanHeader WITH(NOLOCK) WHERE ForecastID=@ForecastID
		
		Update SP
		set sp.ProductTypeID=@ProductTypeId,
		LastUpdateByID=@LastUpdateByID,
		LastUpdateDateTime=GETDATE()
		From dbo.SavedPlanInfo SP 
		INNER JOIN  dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
		where SS.ForecastID=@ForecastID

				---Added logic from Bid Model--------
					
					UPDATE CalcFinalPremium
					SET PlanTypeName=@PlanTypeName,
					LastUpdateByID=@LastUpdateByID,
					LastUpdateDateTime=GETDATE()
					WHERE ForecastID=@ForecastID
					
					UPDATE MAReportPlanLevel
					SET PlanTypeName=@PlanTypeName
					WHERE ForecastID=@ForecastID
					AND PlanYearID=@PlanYearID
				
		--------------------Update PFFSNetworkId------------------------------------------------------------
				
		declare @PFFSNetworkId int
		select 	@PFFSNetworkId=PFFSNetworkID from dbo.LkpPFFSNetwork WITH(NOLOCK) where PFFSNetwork=@PFFSNetwork
				
		Update SP
		set sp.PFFSNetworkID=@PFFSNetworkId,
		LastUpdateByID=@LastUpdateByID,
		LastUpdateDateTime=GETDATE()
		From dbo.SavedPlanInfo SP 
		INNER JOIN  dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
		where SS.ForecastID=@ForecastID
				
		--------------------Update SNPTypeID------------------------------------------------------------
				
		declare @SNPTypeID int
		select 	@SNPTypeID=SNPTypeID from dbo.LkpSNPType WITH(NOLOCK) where SNPTypeDetail=@SNPType
				
		Update SP
		set sp.SNPTypeID=@SNPTypeID,
		LastUpdateByID=@LastUpdateByID,
		LastUpdateDateTime=GETDATE()
		From dbo.SavedPlanInfo SP 
		INNER JOIN  dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
		where SS.ForecastID=@ForecastID
				
		--------------------Update MAPlanDesignID------------------------------------------------------------
				
				
		declare @MAPlanDesignID tinyint
		select 	@MAPlanDesignID=MAPlanDesignID from dbo.LkpMAPlanDesign WITH(NOLOCK) where MAPlanDesign=@MAPlanDesign
				
		Update SP
		set sp.MAPlanDesignID=@MAPlanDesignID,
		LastUpdateByID=@LastUpdateByID,
		LastUpdateDateTime=GETDATE()
		From dbo.SavedPlanInfo SP 
		INNER JOIN  dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
		where SS.ForecastID=@ForecastID

		UPDATE SS
		SET ss.IsToReprice = 1 FROM dbo.SavedForecastSetup ss WHERE ss.ForecastID = @ForecastID

        SET @Result = 1; 
        SET @MessageFromBackend='';
      
        END;    
	END ;
       
      
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        SET @Result = 0;
        SET @MessageFromBackend='An error occurred while processing your request. <NAME_EMAIL>.'

		
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
		
        ROLLBACK TRANSACTION; 
			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
    END CATCH;  

END;
GO
