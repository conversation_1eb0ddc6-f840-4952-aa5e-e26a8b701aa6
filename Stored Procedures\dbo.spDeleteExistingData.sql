SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spDeleteExistingData
--
-- CREATOR:			Manisha Tyagi
--
-- CREATED DATE:	JUL-05-2022
--
-- DESCRIPTION:		This procedure deletes data in SavedDF tables prior to a DF Sync.  
--		
-- PARAMETERS:
--  Input  :		@DFVersionID
--					@TableName
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			NONE
--
--  Write:			SavedDFVersionHeader
--					SavedDFClaims
--					SavedDFFinance
--					SavedDFBaseAdminAndRevenue
--					StgPlanInfoIdDFData
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																	DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2022-JUL-05      1           Initial version.																Manisha Tyagi  
-- 2024-NOV-04      2           Revised references to fnGetMemberMonthsAndAllowedByDuals                        Deepali Mittal
-- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spDeleteExistingData] --6,'SavedDFBaseAdminAndRevenue'  
    @DFVersionID INT,
    @TableName VARCHAR(MAX)
AS
BEGIN

    TRUNCATE TABLE dbo.StgPlanInfoIdDFData;
    DECLARE @Query VARCHAR(8000);
    SET @Query
        = ' DECLARE @Row VARCHAR(MAX)  
 SET @Row=  (SELECT count(1) FROM ' + @TableName + ' WHERE DFVersionID = ' + CAST(@DFVersionID AS VARCHAR(4))
          + ')  
  
WHILE (@Row > 0)  
BEGIN  
    DELETE TOP (50000) FROM dbo.' + @TableName + ' WHERE DFVersionID = ' + CAST(@DFVersionID AS VARCHAR(4))
          + '   
   SET @Row= @Row-50000   
END';
    PRINT (@Query);
    EXEC (@Query);

END;
GO
