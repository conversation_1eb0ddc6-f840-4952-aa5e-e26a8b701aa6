SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_Reporting_spCalcProjectedMembership
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: APR-01-2020
--
-- DESCRIPTION:  This procedure populates projected membership by plan for reporting purposes 
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntPlanYear
--                  SavedForecastSetup
--                  SavedRollupForecastMap
--                  SavedRollupInfo
--                  Trend_PerPopulationMRACurrentYear
--                  Trend_SavedPopulationBarcBidYearMembership  
--					
--          Write:  Trend_Reporting_CalcProjectedMembership
--                  Trend_Reporting_Log
--
-- VIEWS: Read:		vwSAMCrosswalks
--					vwPlanInfo
--
-- FUNCTIONS:		NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER  
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- APR-01-2020      1           Initial Version																								Andy Blink
-- JUN-18-2020      2           Join tables needed to filter SAM on version                         										Michael Manes
-- JUL-14-2020      3			Optimized BARC to MAAUI logic 																				Ramandeep Saini
-- SEP-14-2020      4			Isolation level added 																						Surya Murthy
-- MAR-15-2021		5			Code adjustments to improve efficiency and decrease runtime													Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_Reporting_spCalcProjectedMembership]

@LastUpdateByID CHAR(7)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;
        SET TRANSACTION ISOLATION LEVEL READ COMMITTED; -- Specify that statements cannot read data that has been modified but not committed by other transactions.

		DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            -- Declare and set variables 
            DECLARE @LastUpdateDateTime DATETIME = GETDATE ();
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);
            DECLARE @BidYear INT = (SELECT  DISTINCT PlanYearID FROM dbo.LkpIntPlanYear WHERE   IsBidYear = 1);
            DECLARE @RepCat VARCHAR(50) = 'IP'; --Membership is duplicated for each RepCat in the current year MRA table; only need to select one.  

            -- Create a temp two year crosswalk table from vwSAMCrosswalks
            IF (SELECT  OBJECT_ID ('tempdb..#vwSAMCrosswalks')) IS NOT NULL
                DROP TABLE #vwSamCrosswalks;
            SELECT      vsc.CurrentYearPlanInfoID
                       ,vsc.BidYearPlanInfoID
            INTO        #vwSamCrosswalks
            FROM        dbo.vwSAMCrosswalks vsc
           INNER JOIN   dbo.SavedForecastSetup sfs
                   ON sfs.PlanInfoID = vsc.BidYearPlanInfoID
                      AND   sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID
           INNER JOIN   dbo.SavedRollupForecastMap map
                   ON map.ForecastID = sfs.ForecastID
           INNER JOIN   dbo.SavedRollupInfo sri
                   ON sri.RollupID = map.RollupID
            LEFT JOIN   dbo.vwPlanInfo vpi
                   ON vpi.PlanInfoID = sfs.PlanInfoID
            WHERE       sri.RollupName = 'Live'
            GROUP BY    vsc.CurrentYearPlanInfoID
                       ,vsc.BidYearPlanInfoID;

            -- Insert the current year MRA table into a temp table
            IF (SELECT  OBJECT_ID ('tempdb..#PerPopulationMRACurrentYear')) IS NOT NULL
                DROP TABLE #PerPopulationMRACurrentYear;
            SELECT      vpi.PlanInfoID
                       ,SUM (ISNULL (a.CY_MM, 0)) AS CY_MM
            INTO        #PerPopulationMRACurrentYear
            FROM        dbo.Trend_PerPopulationMRACurrentYear a
           INNER JOIN   dbo.vwPlanInfo vpi
                   ON vpi.CPS = a.CPS
                      AND   vpi.PlanYear = @BidYear - 1
            WHERE       a.ReportingCategory = @RepCat
            GROUP BY    vpi.PlanInfoID;

            -- Insert the bid year MRA table into a temp table
            IF (SELECT  OBJECT_ID ('tempdb..#SavedPopulationBarcBidYearMembership')) IS NOT NULL
                DROP TABLE #SavedPopulationBarcBidYearMembership;
            SELECT      vpi.PlanInfoID
                       ,SUM (ISNULL (a.Bid_MM, 0)) AS Bid_MM
            INTO        #SavedPopulationBarcBidYearMembership
            FROM        dbo.Trend_SavedPopulationBarcBidYearMembership a
           INNER JOIN   dbo.vwPlanInfo vpi
                   ON vpi.CPS = a.CPS
                      AND   vpi.PlanYear = @BidYear
            GROUP BY    vpi.PlanInfoID;

            -- Delete and insert into the output table
            DELETE  FROM dbo.Trend_Reporting_CalcProjectedMembership WHERE  1 = 1;

            -- Get final output
            IF (SELECT  OBJECT_ID ('tempdb..#OutputCalcProjectedMembership')) IS NOT NULL
                DROP TABLE #OutputCalcProjectedMembership;

            -- Current year experience
            SELECT      vsc.BidYearPlanInfoID AS PlanInfoID
                       ,@BidYear - 1 AS TrendYearID
                       ,1 AS RateType
                       ,SUM (ISNULL (m.CY_MM, 0)) AS MemberMonths
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            INTO        #OutputCalcProjectedMembership
            FROM        #vwSamCrosswalks vsc
           INNER JOIN   #PerPopulationMRACurrentYear m
                   ON m.PlanInfoID = vsc.CurrentYearPlanInfoID
            WHERE       vsc.CurrentYearPlanInfoID IS NOT NULL
            GROUP BY    vsc.BidYearPlanInfoID;

            -- Current year manual
            INSERT INTO #OutputCalcProjectedMembership
            SELECT      vsc.BidYearPlanInfoID AS PlanInfoID
                       ,@BidYear - 1 AS TrendYearID
                       ,2 AS RateType
                       ,SUM (ISNULL (m.CY_MM, 0)) AS MemberMonths
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        #vwSamCrosswalks vsc
           INNER JOIN   #PerPopulationMRACurrentYear m
                   ON m.PlanInfoID = vsc.CurrentYearPlanInfoID
            WHERE       vsc.CurrentYearPlanInfoID IS NOT NULL
            GROUP BY    vsc.BidYearPlanInfoID;

            -- Bid year experience
            INSERT INTO #OutputCalcProjectedMembership
            SELECT  a.PlanInfoID
                   ,@BidYear AS TrendYearID
                   ,1 AS RateType
                   ,a.Bid_MM AS MemberMonths
                   ,@LastUpdateByID
                   ,@LastUpdateDateTime
            FROM    #SavedPopulationBarcBidYearMembership a;

            -- Bid year manual
            INSERT INTO #OutputCalcProjectedMembership
            SELECT  a.PlanInfoID
                   ,@BidYear AS TrendYearID
                   ,2 AS RateType
                   ,a.Bid_MM AS MemberMonths
                   ,@LastUpdateByID
                   ,@LastUpdateDateTime
            FROM    #SavedPopulationBarcBidYearMembership a;

            -- Write output to table
            INSERT INTO dbo.Trend_Reporting_CalcProjectedMembership
                (PlanInfoID
                ,TrendYearID
                ,RateType
                ,MemberMonths
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  PlanInfoID
                   ,TrendYearID
                   ,RateType
                   ,MemberMonths
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #OutputCalcProjectedMembership;

            COMMIT TRANSACTION transactionMain;

        END TRY

        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@LastUpdateByID
               ,GETDATE ();

    END;
GO
