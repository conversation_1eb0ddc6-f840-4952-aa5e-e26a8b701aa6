SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetCrosswalkTypes]   
--    
-- AUTHOR: Sur<PERSON>y 
--    
-- CREATED DATE: 2024-Nov-08    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving cross walk types  
--    
-- PARAMETERS:    
-- Input: 
--
   
-- TABLES:   
-- PrePricing.PlanCrossWalkType 
 
-- Read:    
--  

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Nov-08		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetCrosswalkTypes]
AS    
BEGIN    
	SELECT CrosswalkTypeID,CrossWalkTypeName FROM PrePricing.PlanCrossWalkType WITH(NOLOCK) ORDER BY LastUpdateDateTime;
END
GO
