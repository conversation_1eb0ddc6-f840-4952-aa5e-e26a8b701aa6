SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_fnCalcStringToTable
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: Jan-23-2020
--
-- DESCRIPTION: This function takes a delimited list of items (like a comma separated list of CP<PERSON>'s) that are a single string, and turns it into a table
--        
-- PARAMETERS:
--  Input  : 
--
--  Output : NONE
--
-- TABLES : Read :  

--          Write:  
--
-- VIEWS: Read: NONE
--
-- FUNCTIONS: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 1/23/20     		1		    Adding to MAAModels															Andy Blink
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[Trend_fnCalcStringToTable]
    (@String    VARCHAR(MAX)/* input string */
    ,@Delimeter CHAR(1)     /* delimiter */
    ,@TrimSpace BIT) /* kill whitespace? */
RETURNS @Table TABLE
    ([Val] VARCHAR(4000))
AS
    BEGIN
        DECLARE @Val VARCHAR(4000);
        WHILE LEN (@String) > 0
            BEGIN
                SET @Val = LEFT(@String, ISNULL (NULLIF(CHARINDEX (@Delimeter, @String) - 1, -1), LEN (@String)));
                SET @String = SUBSTRING (
                              @String
                             ,ISNULL (NULLIF(CHARINDEX (@Delimeter, @String), 0), LEN (@String)) + 1
                             ,LEN (@String));
                IF @TrimSpace = 1 SET @Val = LTRIM (RTRIM (@Val));
                INSERT INTO @Table ([Val]) VALUES (@Val);
            END;
        RETURN;
    END;
GO
