-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcTieredBenefitAverages
--
-- AUTHOR: Mike <PERSON>ly
--
-- CREATED DATE: 2023-JUN-??
--
-- DESCRIPTION: Returns the average copay/coinsurance for benefits that vary by day range (more than 1 Benefit Ordinal ID)
--
-- PARAMETERS:
--  Input:
--		@ForecastID
--      @IsBenefitYearCurrentYear
--      @LastUpdateByID
--
-- TABLES: 
--  Read:
--		SavedPlanBenefitDetail (day range begin point, end point, cost share)
--		CalcBenefitCategoryUtilization (% utilization by day)
--
--  Write:
--		CalcTieredBenefitAverageCostShares
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2023-Jun-??		1			Initial version.													Mike Lovely
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcTieredBenefitAverages]

 @ForecastID      INT
,@IsBenefitYearCurrentYear BIT
,@LastUpdateByID CHAR(7)

AS

	BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY

            DECLARE @tranCount INT = @@TranCount; --Current transaction count
            DECLARE @errorMsg VARCHAR(500); --Variable for error messages 

            BEGIN TRANSACTION transaction_spCTBA;

DECLARE  @XForecastID INT = @ForecastID
		,@XIsBenefitYearCurrentYear BIT = @IsBenefitYearCurrentYear
		,@XLastUpdateByID CHAR(7) = @LastUpdateByID


--Build initial table at Benefit Ordinal ID level

DROP TABLE IF EXISTS #TempTieredBenefits

SELECT  DISTINCT
		SPBD.ForecastID,
		SPBD.BenefitCategoryID,
		SPBD.IsBenefitYearCurrentYear,
		SPBD.BenefitOrdinalID,
		SPBD.INDayRangeBegin,
		SPBD.INDayRangeEnd,
		SPBD.INBenefitValue,
		SPBD.OONDayRangeBegin,
		SPBD.OONDayRangeEnd,
		SPBD.OONBenefitValue,
		INUtilizationFactor = (SELECT ROUND(SUM(UtilizationPercent),8)  -- Sum up utilization % for all days within day range of the Benefit Ordinal ID
								FROM dbo.CalcBenefitCategoryUtilization CBCU
								WHERE CBCU.BenefitCategoryID = SPBD.BenefitCategoryID
								AND CBCU.DayRangeEnd BETWEEN SPBD.INDayRangeBegin AND SPBD.INDayRangeEnd),
		OONUtilizationFactor = (SELECT ROUND(SUM(UtilizationPercent),8) -- Sum up utilization % for all days within day range of the Benefit Ordinal ID
								FROM dbo.CalcBenefitCategoryUtilization CBCU
								WHERE CBCU.BenefitCategoryID = SPBD.BenefitCategoryID
								AND CBCU.DayRangeEnd BETWEEN SPBD.OONDayRangeBegin AND SPBD.OONDayRangeEnd),
		LastUpdateByID = @XLastUpdateByID,
		LastUpdateDateTime = GETDATE()
INTO #TempTieredBenefits
FROM dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
INNER JOIN dbo.CalcBenefitCategoryUtilization CBCU WITH (NOLOCK)
	ON CBCU.BenefitCategoryID = SPBD.BenefitCategoryID
WHERE SPBD.ForecastID = @XForecastID
  AND SPBD.IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear
  AND SPBD.IsLiveIndex = (CASE WHEN SPBD.IsBenefitYearCurrentYear = 0 
									THEN 1
									ELSE 0
							   END)
ORDER BY SPBD.ForecastID,
		 SPBD.BenefitCategoryID,
		 SPBD.IsBenefitYearCurrentYear,
		 SPBD.BenefitOrdinalID,
		 SPBD.INDayRangeBegin

DELETE FROM dbo.CalcTieredBenefits
WHERE ForecastID = @XForecastID
  AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear

INSERT INTO dbo.CalcTieredBenefits
	SELECT * FROM #TempTieredBenefits

--Calculate weighted average cost share by Benefit Category using the initial calculated table
DROP TABLE IF EXISTS #TempTieredBenefitAverageCostShares

SELECT ForecastID,
	   BenefitCategoryID,
	   IsBenefitYearCurrentYear,
	   INBenefitValueAvg = SUM(INBenefitValue * INUtilizationFactor),
	   OONBenefitValueAvg = SUM(OONBenefitValue * OONUtilizationFactor),
	   LastUpdateByID = @XLastUpdateByID,
	   LastUpdateDateTime = GETDATE()
INTO #TempTieredBenefitAverageCostShares
FROM dbo.CalcTieredBenefits WITH (NOLOCK)
WHERE ForecastID = @XForecastID
  AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear
GROUP BY ForecastID,
		 BenefitCategoryID,
		 IsBenefitYearCurrentYear
ORDER BY ForecastID,
		 BenefitCategoryID,
		 IsBenefitYearCurrentYear

DELETE FROM dbo.CalcTieredBenefitAverageCostShares
WHERE ForecastID = @XForecastID
  AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear

INSERT INTO dbo.CalcTieredBenefitAverageCostShares
		 SELECT * FROM #TempTieredBenefitAverageCostShares

COMMIT TRANSACTION transaction_spCTBA;

    END TRY

      BEGIN CATCH

          IF (@@TranCount > @tranCount) --Check if transaction in TRY block was not closed
              BEGIN
                  SET @errorMsg = ERROR_MESSAGE ();
                  ROLLBACK TRANSACTION transaction_spTBA;

              END;
      END CATCH;
	END
GO
