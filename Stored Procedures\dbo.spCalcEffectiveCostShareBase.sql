SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcEffectiveCostShareBase
--
-- AUTHOR: <PERSON>cha
--
-- CREATED DATE: 2018-Oct-04
-- HEADER UPDATED: 2018-Oct-04
--
-- DESCRIPTION: Populates CalcEffectiveCostShareBase for all plans.
--
-- PARAMETERS:
--	Input:
--      
--	Output:
--
-- TABLES:
--	Read:
--      SavedPlanBenefitDetailBase
--		CalcBenefitCategoryUtilization
--	Write:
--      CalcEffectiveCostShareBase
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnGetBidYear
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             				DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2018-Sep-09		1			Initialization of effective cost share without Dampening (coinsurance	Alex Beruscha
--								effective cost share incorrect if tier/day range applied)
-- 2018-Nov-27		2			Coinsurance values set equal to 1.0 for induced utilization				Alex Beruscha
-- 2019-oct-30		3			Removed 'HUMAD\' to UserID												Chhavi Sinha 
-- 2025-APR-07		4			Add @DayRangeBenefitTypeID, use TypeID = 5 for day range benefits		Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcEffectiveCostShareBase]
    (
    @UserID CHAR(7))
AS

--These values will be used as constants.  
DECLARE @LastDateTime DATETIME
       ,@PlanYearID   SMALLINT;

SELECT  @PlanYearID = dbo.fnGetBidYear () - 2;
SELECT  @LastDateTime = GETDATE ();

DECLARE @DayRangeBenefitTypeID INT = (SELECT    BenefitTypeID
                                      FROM      dbo.LkpIntBenefitType WITH (NOLOCK)
                                      WHERE     Name = 'Day Range');

    BEGIN
        --Calculate CDF table of utilization percents for Benefit Category 65, 66, 83, and 131
        IF OBJECT_ID ('tempdb.dbo.#CDFUtilization') IS NOT NULL
            DROP TABLE #CDFUtilization;
        CREATE TABLE #CDFUtilization
            (BenefitCategoryID  INT
            ,DayRangeBegin      INT
            ,DayRangeEnd        INT
            ,UtilizationPercent DECIMAL(10, 9));


        INSERT INTO #CDFUtilization
        SELECT      a.BenefitCategoryID
                   ,a.DayRangeBegin
                   ,a.DayRangeEnd
                   ,SUM (b.UtilizationPercent) AS UtilizationPercent
        FROM        dbo.CalcBenefitCategoryUtilization a WITH (NOLOCK)
        LEFT JOIN   dbo.CalcBenefitCategoryUtilization b WITH (NOLOCK)
               ON b.BenefitCategoryID = a.BenefitCategoryID
                  AND   b.DayRangeEnd <= a.DayRangeEnd
        GROUP BY    a.BenefitCategoryID
                   ,a.DayRangeBegin
                   ,a.DayRangeEnd
        ORDER BY    a.BenefitCategoryID
                   ,a.DayRangeBegin
                   ,a.DayRangeEnd;

        --Calculate the IN effective cost share value before applying cost share dampening
        IF OBJECT_ID ('tempdb.dbo.#INEffectiveCostShare') IS NOT NULL
            DROP TABLE #INEffectiveCostShare;
        CREATE TABLE #INEffectiveCostShare
            ([PlanYearID]           INT
            ,[Contract-PBP]         CHAR(13)
            ,[ContractNumber]       CHAR(5)
            ,[PlanID]               CHAR(3)
            ,[SegmentID]            CHAR(3)
            ,[BenefitCategoryID]    SMALLINT
            ,[BenefitTierLevel]     SMALLINT
            ,[INBenefitTypeID]      TINYINT
            ,[INEffectiveCostShare] DECIMAL(9, 4), );



        --Calculate the effective cost share value before applying cost share dampening
        INSERT INTO #INEffectiveCostShare
        SELECT      a.PlanYearID
                   ,a.[Contract-PBP]
                   ,a.[ContractNumber]
                   ,a.[PlanID]
                   ,a.[SegmentID]
                   ,a.BenefitCategoryID
                   ,a.BenefitTierLevel
                   ,a.INBenefitTypeID
                   ,(a.INBenefitValue * a.EffectiveUtilizationPercentIN) AS EffectiveCostShareIN
        FROM        (SELECT     PlanYearID
                               ,spbd.ForecastID
                               ,spbd.[Contract-PBP]
                               ,spbd.[ContractNumber]
                               ,spbd.[PlanID]
                               ,spbd.[SegmentID]
                               ,spbd.BenefitCategoryID
                               ,BenefitOrdinalID AS BenefitTierLevel
                               ,INBenefitTypeID
                               ,COALESCE (INBenefitValue, 0) AS INBenefitValue
                               ,spbd.INDayRangeBegin
                               ,spbd.INDayRangeEnd
                               ,CASE WHEN INBenefitTypeID = @DayRangeBenefitTypeID THEN
                                         COALESCE (cdfu1.UtilizationPercent, 1) - COALESCE (cdfu2.UtilizationPercent, 0)
                                     ELSE CASE WHEN spbd.BenefitOrdinalID = 1 THEN 1 ELSE 0 END END AS EffectiveUtilizationPercentIN
                     FROM       dbo.SavedPlanBenefitDetailBase spbd  WITH (NOLOCK)
                     LEFT JOIN  #CDFUtilization cdfu1
                            ON cdfu1.BenefitCategoryID = spbd.BenefitCategoryID
                               AND spbd.INDayRangeEnd = cdfu1.DayRangeEnd
                     LEFT JOIN  #CDFUtilization cdfu2
                            ON cdfu2.BenefitCategoryID = spbd.BenefitCategoryID
                               AND (spbd.INDayRangeBegin - 1) = cdfu2.DayRangeEnd
                     WHERE      spbd.INBenefitTypeID IS NOT NULL) a
        GROUP BY    a.PlanYearID
                   ,a.[Contract-PBP]
                   ,a.[ContractNumber]
                   ,a.[PlanID]
                   ,a.[SegmentID]
                   ,a.BenefitCategoryID
                   ,a.INBenefitTypeID
                   ,a.BenefitTierLevel
                   ,a.INBenefitValue
                   ,a.EffectiveUtilizationPercentIN
        ORDER BY    a.PlanYearID
                   ,a.[Contract-PBP]
                   ,a.BenefitCategoryID
                   ,a.BenefitTierLevel;

        --Calculate the OON effective cost share value before applying cost share dampening
        IF OBJECT_ID ('tempdb.dbo.#OONEffectiveCostShare') IS NOT NULL
            DROP TABLE #OONEffectiveCostShare;
        CREATE TABLE #OONEffectiveCostShare
            ([PlanYearID]            INT
            ,[Contract-PBP]          CHAR(13)
            ,[ContractNumber]        CHAR(5)
            ,[PlanID]                CHAR(3)
            ,[SegmentID]             CHAR(3)
            ,[BenefitCategoryID]     SMALLINT
            ,[BenefitTierLevel]      SMALLINT
            ,[OONBenefitTypeID]      TINYINT
            ,[OONEffectiveCostShare] DECIMAL(9, 4), );



        --Calculate the effective cost share value before applying cost share dampening
        INSERT INTO #OONEffectiveCostShare
        SELECT      a.PlanYearID
                   ,a.[Contract-PBP]
                   ,a.[ContractNumber]
                   ,a.[PlanID]
                   ,a.[SegmentID]
                   ,a.BenefitCategoryID
                   ,a.BenefitTierLevel
                   ,a.OONBenefitTypeID
                   ,(a.OONBenefitValue * a.EffectiveUtilizationPercentOON) AS EffectiveCostShareOON
        FROM        (SELECT     PlanYearID
                               ,spbd.ForecastID
                               ,spbd.[Contract-PBP]
                               ,spbd.[ContractNumber]
                               ,spbd.[PlanID]
                               ,spbd.[SegmentID]
                               ,spbd.BenefitCategoryID
                               ,BenefitOrdinalID AS BenefitTierLevel
                               ,spbd.OONDayRangeBegin
                               ,spbd.OONDayRangeEnd
                               ,OONBenefitTypeID
                               ,COALESCE (OONBenefitValue, 0) AS OONBenefitValue
                               ,CASE WHEN OONBenefitTypeID = @DayRangeBenefitTypeID THEN
                                         COALESCE (cdfu3.UtilizationPercent, 1) - COALESCE (cdfu4.UtilizationPercent, 0)
                                     ELSE CASE WHEN spbd.BenefitOrdinalID = 1 THEN 1 ELSE 0 END END AS EffectiveUtilizationPercentOON
                     FROM       dbo.SavedPlanBenefitDetailBase spbd  WITH (NOLOCK)
                     LEFT JOIN  #CDFUtilization cdfu3
                            ON cdfu3.BenefitCategoryID = spbd.BenefitCategoryID
                               AND spbd.OONDayRangeEnd = cdfu3.DayRangeEnd
                     LEFT JOIN  #CDFUtilization cdfu4
                            ON cdfu4.BenefitCategoryID = spbd.BenefitCategoryID
                               AND (spbd.OONDayRangeBegin - 1) = cdfu4.DayRangeEnd
                     WHERE      spbd.OONBenefitTypeID IS NOT NULL) a
        GROUP BY    a.PlanYearID
                   ,a.[Contract-PBP]
                   ,a.[ContractNumber]
                   ,a.[PlanID]
                   ,a.[SegmentID]
                   ,a.BenefitCategoryID
                   ,a.BenefitTierLevel
                   ,a.OONBenefitTypeID
                   ,a.OONBenefitValue
                   ,a.EffectiveUtilizationPercentOON
        ORDER BY    a.PlanYearID
                   ,a.[Contract-PBP]
                   ,a.BenefitCategoryID
                   ,a.BenefitTierLevel;

        --Calculate the effective cost share value after applying cost share dampening
        IF OBJECT_ID ('tempdb.dbo.#Results') IS NOT NULL DROP TABLE #Results;
        CREATE TABLE #Results
            ([PlanYearID]            INT
            ,[Contract-PBP]          CHAR(13)
            ,[ContractNumber]        CHAR(5)
            ,[PlanID]                CHAR(3)
            ,[SegmentID]             CHAR(3)
            ,[BenefitCategoryID]     SMALLINT
            ,[BenefitTierLevel]      SMALLINT
            ,[INBenefitTypeID]       TINYINT
            ,[INEffectiveCostShare]  DECIMAL(9, 4)
            ,[OONBenefitTypeID]      TINYINT
            ,[OONEffectiveCostShare] DECIMAL(9, 4)
            ,[LastUpdateByID]        CHAR(13)
            ,[LastUpdateDateTime]    DATETIME);
        INSERT INTO #Results
        SELECT          COALESCE (incs.PlanYearID, ooncs.PlanYearID) AS [PlanYearID]
                       ,COALESCE (incs.[Contract-PBP], ooncs.[Contract-PBP]) AS [Contract-PBP]
                       ,COALESCE (incs.ContractNumber, ooncs.ContractNumber) AS [ContractNumber]
                       ,COALESCE (incs.PlanID, ooncs.PlanID) AS [PlanID]
                       ,COALESCE (incs.SegmentID, ooncs.SegmentID) AS [SegmentID]
                       ,COALESCE (incs.BenefitCategoryID, ooncs.BenefitCategoryID) AS BenefitCategoryID
                       ,COALESCE (incs.BenefitTierLevel, ooncs.BenefitTierLevel) AS BenefitTierLevel
                       ,incs.INBenefitTypeID
                       ,incs.INEffectiveCostShare
                       ,ooncs.OONBenefitTypeID
                       ,ooncs.OONEffectiveCostShare
                       ,@UserID
                       ,@LastDateTime
        FROM            #INEffectiveCostShare incs
        FULL OUTER JOIN #OONEffectiveCostShare ooncs
                     ON ooncs.[Contract-PBP] = incs.[Contract-PBP]
                        AND ooncs.BenefitCategoryID = incs.BenefitCategoryID
                        AND ooncs.BenefitTierLevel = incs.BenefitTierLevel;

        DELETE  FROM dbo.CalcEffectiveCostShareBase
        WHERE   [PlanYearID] = @PlanYearID;

        INSERT INTO dbo.CalcEffectiveCostShareBase
        SELECT  PlanYearID
               ,[Contract-PBP]
               ,ContractNumber
               ,PlanID
               ,SegmentID
               ,BenefitCategoryID
               ,BenefitTierLevel
               ,INBenefitTypeID
               ,INEffectiveCostShare
               ,OONBenefitTypeID
               ,OONEffectiveCostShare
               ,LastUpdateByID
               ,LastUpdateDateTime
        FROM    #Results;

    END;
GO
