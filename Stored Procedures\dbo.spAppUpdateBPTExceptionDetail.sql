SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- Stored Procedure

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: [dbo].[spAppUpdateBPTExceptionDetail]
--
-- TYPE: SAME
--
-- AUTHOR: Gowri.G
--
-- CREATED DATE: 2011-Jan-20
--
-- DESCRIPTION: Procedure responsible for updating the base plans
--				by ForecastID.
--
-- PARAMETERS:
--	Input:
--	    @ForecastID INT,
--	@ContractNumber VARCHAR(5),
--	@PlanID VARCHAR(3),
--  @SegmentId VARCHAR(3),  
--	@Member<PERSON><PERSON><PERSON> INT,
--	@NonDualMemberMonths INT,
--	@UserID VARCHAR(7)
-- TABLES: 
--	Read:
--				
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		
-- STORED PROCS:
--		spUpdateOrInsertBPTExceptionDetail
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-20		1			Initial Version														Gowri.G
-- 2013-Oct-04      2           Included Join on Segment ID                                         Manisha Tyagi
-- 2019-oct-30		3			Removed 'HUMAD\' to UserID											Chhavi Sinha
-- ----------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spAppUpdateBPTExceptionDetail]
	@ForecastID INT,
	@ContractNumber VARCHAR(5),
	@PlanID VARCHAR(3),
	@SegmentId VARCHAR(3) = '000',  --Included SegmentId
	@MemberMonths INT,
	@NonDualMemberMonths INT,
	@UserID VARCHAR(7)
AS
BEGIN

	EXEC spUpdateOrInsertBPTExceptionDetail
	@ForecastID,
	@ContractNumber,
	@PlanID,
	@SegmentId,   --Including SegmentId
	@MemberMonths,
	@NonDualMemberMonths,
	@UserID

END
GO
