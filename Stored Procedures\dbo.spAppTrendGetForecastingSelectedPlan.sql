SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- PROCEDURE NAME: spAppTrendGetForecastingSelectedPlan  
--  
-- CREATOR: Manisha <PERSON>agi 
--  

-- PARAMETERS:  
--  Input:   
--@ForecastedList CHAR(13),
--@LastUpdateByID VARCHAR(7)
  
-- StoredProcs:  
-- $HISTORY   
-- ------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE												Developer													  
-- -------------------------------------------------------------------------------------------------------
-- 2020-Mar-31       1          Initial Version											Manisha Tyagi	
-- 2020-Mar-31       2          Correcting Secenario spelling							Rodney Smith	
-- 2020-May-20       3          Replace ForecastIDList with PlanList                    Manisha Tyagi
-- 2020-May-27       4          Replace Trend_ProjProcess_CalcPlanAdjmt_RepCat with Trend_ProjProcess_CalcPlanTrendsFinal     Manisha Tyagi
-- NOV-19-2020       5          Include NOLOCK & ROWLOCK                                Manisha Tyagi
-- ------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spAppTrendGetForecastingSelectedPlan] 
@PlanList varchar(max),
@LastUpdateByID VARCHAR(7)

AS

  BEGIN  
	BEGIN TRANSACTION;
        BEGIN TRY 
		 BEGIN     
	 
	     DECLARE @PlanYearID SMALLINT  
    SELECT @PlanYearID = dbo.fnGetBidYear() 

SELECT DISTINCT tf.PlanInfoID,tf.LastUpdateDateTime
INTO #PlanInfoLastUpdateDateTime
 FROM (SELECT DISTINCT PlanInfoID, MAX(LastUpdateDateTime) AS LastUpdateDateTime
 FROM dbo.Trend_ProjProcess_CalcPlanTrendsFinal WITH(NOLOCK)
 WHERE CPS IN (SELECT VALUE FROM dbo.fnStringSplit(@PlanList,','))
 GROUP BY  PlanInfoID) tf
	
SELECT DISTINCT f.PlanInfoID,  f.LastUpdateDateTime, f.LastUpdateByID
INTO #Trend_ProjProcess_CalcPlanTrendsFinal
 FROM dbo.Trend_ProjProcess_CalcPlanTrendsFinal f WITH(NOLOCK)
 LEFT JOIN #PlanInfoLastUpdateDateTime d ON f.PlanInfoID=d.PlanInfoID AND f.LastUpdateDateTime = d.LastUpdateDateTime

 SELECT    SR.ActuarialRegion AS Region ,
                ISNULL(PT.Description,'NULL') AS Product ,
				SP.CPS AS 'Plan',
				CP.LastUpdateDateTime,
				CP.LastUpdateByID
				 FROM dbo.SavedPlanInfo SP WITH(NOLOCK)
                 LEFT JOIN dbo.SavedMarketInfo SM WITH(NOLOCK) ON SP.ActuarialMarketID = SM.ActuarialMarketID
                 LEFT JOIN dbo.SavedRegionInfo SR WITH(NOLOCK) ON SR.ActuarialRegionID = SM.ActuarialRegionID
			     LEFT JOIN dbo.LkpProductType PT WITH(NOLOCK) ON PT.ProductTypeID = SP.ProductTypeID
				 LEFT JOIN #Trend_ProjProcess_CalcPlanTrendsFinal CP ON SP.PlanInfoID = CP.PlanInfoID
			    WHERE   SP.PlanYear = @PlanYearID 
				AND SP.CPS IN (SELECT VALUE FROM dbo.fnStringSplit(@PlanList,','))
				GROUP BY
				SR.ActuarialRegion,
                PT.Description,
				SP.CPS,
				CP.LastUpdateByID,
				CP.LastUpdateDateTime
				ORDER BY
                SR.ActuarialRegion,
                PT.Description,
				SP.CPS,
				CP.LastUpdateByID,
				CP.LastUpdateDateTime
END
		COMMIT TRANSACTION;
END TRY
        BEGIN CATCH  
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
			ROLLBACK TRANSACTION; 

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID 
            
        END CATCH; 

END;
GO
