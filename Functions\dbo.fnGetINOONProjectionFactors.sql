SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetINOONProjectionFactors]   ******/

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetINOONProjectionFactors
--
-- AUTHOR: Tim Gao 
--
-- CREATED DATE: 3/26/3013
-- HEADER UPDATED: 3/26/3013
--
-- DESCRIPTION: Responsible to get projection factors (IN,OON)at Benefitcategory level for feeding fnAppGetBidSummaryFactors
--				to display the factos in Actuarial Summary tab
-- PARAMETERS: 
--	Input:
--      @ForecastID 
--  Output:
--
-- TABLES: 
--	Read:
--      
--      
--     
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION	    CHANGES MADE						                                DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2013-Mar-26		1			Initial																Tim Gao
-- 2023-Aug-02		2			Internal variable and Schema										Sheetal Patil
-- 2024-May-20      3           Getting Data once and then joining for better Performance           Kiran Kola
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetINOONProjectionFactors]   
(  
    @ForecastID INT  
)  
RETURNS @Results TABLE(  
    ForecastID INT,  
    ClaimForecastID INT,  
    MARatingOptionID INT,  
    BenefitCategoryID INT,  
    E2CINCostTrend DECIMAL(24, 15),  
    C2BINCostTrend DECIMAL(24, 15),  
    E2CINUtilTrend DECIMAL(24, 15),  
    C2BINUtilTrend DECIMAL(24, 15),  
    E2CINClaimFactorUnits DECIMAL(24, 15),  
    C2BINClaimFactorUnits DECIMAL(24, 15),  
    E2CINClaimFactorAllowed DECIMAL(24, 15),  
    C2BINClaimFactorAllowed DECIMAL(24, 15),  
    E2COONCostTrend DECIMAL(24, 15),  
    C2BOONCostTrend DECIMAL(24, 15),  
    E2COONUtilTrend DECIMAL(24, 15),  
    C2BOONUtilTrend DECIMAL(24, 15),  
    E2COONClaimFactorUnits DECIMAL(24, 15),  
    C2BOONClaimFactorUnits DECIMAL(24, 15),  
    E2COONClaimFactorAllowed DECIMAL(24, 15),  
    C2BOONClaimFactorAllowed DECIMAL(24, 15)  
)  
    
AS  
BEGIN  

DECLARE @XForecastID INT = @ForecastID;  

DECLARE @TrendAndClaimFactors TABLE  (  
    ForecastID INT,  
    ClaimForecastID INT,  
    MARatingOptionID INT, 
	IsInNetwork BIT,
    BenefitCategoryID INT,  
    E2CTrendFactor DECIMAL(24, 15),  
    C2BTrendFactor DECIMAL(24, 15),  
    E2CClaimFactor DECIMAL(24, 15),  
    C2BClaimFactor DECIMAL(24, 15),
	TrendAndClaimFactor DECIMAL(24, 15)
	
);  

INSERT INTO @TrendAndClaimFactors   
SELECT * FROM dbo.fnGetTrendAndClaimFactors(@XForecastID);  

INSERT INTO @Results  
SELECT   
    ins.ForecastID,  
    ins.ClaimForecastID,  
    ins.MARatingOptionID,  
    ins.BenefitCategoryID,  
    E2CINCostTrend = ins.E2CTrendFactor,  
    C2BINCostTrend = ins.C2BTrendFactor,  
    E2CINUtilTrend = 1,  
    C2BINUtilTrend = 1,  
    E2CINClaimFactorUnits = 1,  
    C2BINClaimFactorUnits = 1,  
    E2CINClaimFactorAllowed = ins.E2CClaimFactor,  
    C2BINClaimFactorAllowed = ins.C2BClaimFactor,  
    E2COONCostTrend = oons.E2CTrendFactor,  
    C2BOONCostTrend = oons.C2BTrendFactor,  
    E2COONUtilTrend = 1,  
    C2BOONUtilTrend = 1,  
    E2COONClaimFactorUnits = 1,  
    C2BOONClaimFactorUnits = 1,  
    E2COONClaimFactorAllowed= oons.E2CClaimFactor,  
    C2BOONClaimFactorAllowed = oons.C2BClaimFactor  
FROM (SELECT * FROM @TrendAndClaimFactors WHERE IsInNetwork =1)ins
        INNER JOIN (SELECT * FROM @TrendAndClaimFactors WHERE IsInNetwork =0)oons
        ON ins.MARatingOptionID = oons.MARatingOptionID AND ins.BenefitCategoryID = oons.BenefitCategoryID
    RETURN
END
GO