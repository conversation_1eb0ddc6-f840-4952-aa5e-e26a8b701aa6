SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO



-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spUpdateBarcSyncLog
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: 2020-04-28
--
-- DESCRIPTION: Writes to the BARC Sync log after users sync membership and stored procedures are triggered
--              
-- PARAMETERS:
--  Input  :	@ProcNumber
--              @LastUpdateByID
--              @Message
--
--  Output : NONE
--
-- TABLES : Read : 		    
--					
--          Write: Trend_SavedBarcSyncLog
--                  
--
-- VIEWS: Read: 
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-04-28			1		Initial Version														Andy Blink
-- 2023-09-25			2		Rowlocks added for deadlock issues									Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_ProjProcess_spUpdateBarcSyncLog]
 @ProcNumber AS  INT
,@LastUpdateByID CHAR(7) = 'Initial'
,@Message AS     VARCHAR(MAX)

AS
    BEGIN
        SET NOCOUNT ON;
        BEGIN
            INSERT INTO dbo.Trend_SavedBarcSyncLog WITH(ROWLOCK)
            VALUES (@ProcNumber, @Message, @LastUpdateByID, GETDATE (),NEWID());
        END;
    END;
GO
