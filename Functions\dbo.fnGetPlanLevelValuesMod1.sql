SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetPlanLevelValuesMod1
--
-- AUTHOR: Sulemana Da<PERSON>
--
-- CREATED DATE: 2009-Feb-02
-- HEADER UPDATED: 2010-Oct-13
--
-- DESCRIPTION: Designed to extract fields for the  Plan Level Template
--
-- PARAMETERS:
--  Input:
--      @BenefitYearID
--      @WhereIN
--  Output:
--
-- TABLES:
--  Read:
--  Write:
--
-- VIEWS:
--      vwAuditPlanLevelValues
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Feb-02      1           Initial Version                                                     Sulemana Dauda
-- 2009-Feb-20      2           Revised to meet Coding Standards Changed Permissions                Keith Galloway
-- 2009-Mar-17      3           Data types                                                          Sandy Ellis                               
-- 2010-Sep-16      4           Revised for 2012 Database removed PlanYearID                        Jake Gaecke
-- 2010-Sep-20      5           Added a ForecastID filter                                            Jake Gaecke
-- 2011-Mar-01		6			Added Part B Deductible												Joe Casey
-- 2022-Apr-19		7			MAAUI migration; replaced PlanIndex with ForecastID; Added 
--								CombinedDeductible													Aleksandar Dimitrijevic
-- 2023-Jan-04		11			Added DeductTypeDesc												Vikrant Bagal
-- 2025-JAN-21		12			Added CONCAT_WS to create CPS for use in Benefits and				Alex Brandt
--									MOOP/Deductible Export Plan Level worksheets
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetPlanLevelValuesMod1]
(
    @BenefitYearID SMALLINT,
    @WhereIN VARCHAR(MAX) = NULL
)
RETURNS @Results TABLE
(
    BenefitYearID SMALLINT,
    ForecastID INT,
    CPS CHAR(13),
    PlanName CHAR(100),
    INDeductible INT,
    INMOOP INT,
    OONDeductible INT,
    OONMOOP INT,
    CombinedDeductible INT,
    CombinedMOOP INT,
    PartBDeductible INT,
    DeductTypeDesc VARCHAR(50)
)
AS
BEGIN

    IF @WhereIN IS NULL
    BEGIN
        INSERT @Results
        SELECT BenefitYearID,
               ForecastID,
               CONCAT_WS('-', ContractNumber, PlanID, SegmentID) AS CPS,
               PlanName,
               INDeductible,
               INMOOP,
               OONDeductible,
               OONMOOP,
               CombinedDeductible,
               CombinedMOOP,
               PartBDeductible,
               DeductTypeDesc
        FROM dbo.vwAuditPlanLevelValues
        WHERE BenefitYearID = @BenefitYearID;
    END;
    ELSE
    BEGIN
        INSERT @Results
        SELECT BenefitYearID,
               ForecastID,
               CONCAT_WS('-', ContractNumber, PlanID, SegmentID) AS CPS,
               PlanName,
               INDeductible,
               INMOOP,
               OONDeductible,
               OONMOOP,
               CombinedDeductible,
               CombinedMOOP,
               PartBDeductible,
               DeductTypeDesc
        FROM dbo.vwAuditPlanLevelValues
        WHERE BenefitYearID = @BenefitYearID
              AND ForecastID IN
                  (
                      SELECT Value FROM dbo.fnStringSplit(@WhereIN, ',')
                  );
    END;
    RETURN;
END;
GO
