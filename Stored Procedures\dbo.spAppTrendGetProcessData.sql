SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================            
-- Author:  Chhavi <PERSON>ha        
-- Create date: 03-26-2020      
-- Description: Get Data For process Control synced status    
--            
--            
-- PARAMETERS:            
-- Input:              
          
-- TABLES:            
-- Read:            
-- Write:            
-- VIEWS: dbo.Trend_vwPackageOptionUI           
--            
-- FUNCTIONS:            
--              
-- STORED PROCS:             
           
      
-- $HISTORY               
      
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE				 VERSION			CHANGES MADE                   DEVELOPER              
-- ----------------------------------------------------------------------------------------------------------------------              
-- 2020-Mar-26			1				Initial version.             Chhavi Sinha    
--2020-Apr-17			2				Added isnull condition for import Name	Deepali  
-- ----------------------------------------------------------------------------------------------------------------------              
      

CREATE PROCEDURE [dbo].[spAppTrendGetProcessData]       
@LastUpdateByID CHAR(7)
AS    
    BEGIN
        BEGIN TRY

          SELECT DISTINCT a.Component,b.ImportFileName  AS LiveFiles ,x.ImportFileName AS SyncedFiles,
					 CASE WHEN ISNULL(b.ImportFileName,'') = ISNULL(x.ImportFileName,'') THEN 'In Sync' ELSE 'Out of Sync' END SyncStatus
					   FROM   dbo.Trend_SavedComponentInfo a
                     LEFT JOIN (SELECT DISTINCT Component, ImportFileName FROM Trend_vwComponentImportUI 
					 WHERE IsLivePackage = 1) b					 					  
					 ON b.Component = a.Component
					 LEFT JOIN (SELECT DISTINCT qa.Component,  qa.ImportFileName 
					FROM  Trend_CalcFileSync_Quarterly qa
					  UNION 
					SELECT DISTINCT an.Component, an.ImportFileName FROM Trend_CalcFileSync_Annual an					
					) x
					ON x.Component = a.Component

					 WHERE a.IsPartOfPackage=1


        END TRY

        BEGIN CATCH
            --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'            
            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            DECLARE @ErrorException NVARCHAR(4000);
            DECLARE @errSrc      VARCHAR(MAX) = ISNULL (ERROR_PROCEDURE (), 'SQL')
                   ,@currentdate DATETIME     = GETDATE ();

            SELECT  @ErrorMessage = ERROR_MESSAGE ()
                   ,@ErrorSeverity = ERROR_SEVERITY ()
                   ,@ErrorState = ERROR_STATE ()
                   ,@ErrorException = N'Line Number :' + CAST(ERROR_LINE () AS VARCHAR) + N' .Error Severity :'
                                      + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :'
                                      + CAST(@ErrorState AS VARCHAR);

            RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);



            ---Insert into app log for logging error------------------             
            EXEC spAppAddLogEntry @currentdate
                                 ,''
                                 ,'ERROR'
                                 ,@errSrc
                                 ,@ErrorMessage
                                 ,@ErrorException
                                 ,@LastUpdateByID;
        END CATCH;
    END;
GO
