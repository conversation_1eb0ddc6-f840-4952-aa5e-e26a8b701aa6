SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides
--
-- CREATOR: <PERSON> Lewis
--
-- CREATED DATE: FEB-27-2020
--
-- DESCRIPTION:   This procedure maps trend overrides to plans for the projected process.  
--              
--              
-- PARAMETERS:
--  Input  :	@PlanList
--				@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_ProjProcess_SavedMappingOverrideRules
--					Trend_SavedComponentInfo
--					LkpIntPlanYear
--					
--          Write:  Trend_ProjProcess_CalcNotPlanLevel_PlanMappingOverrides
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS: Read: Trend_fnCalcStringToTable
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FEB-27-2020      1           Initial Version                                                     Jake Lewis
-- NOV-19-2020      2          Include NOLOCK & ROWLOCK                                             Manisha Tyagi
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingOverrides]
@PlanList        VARCHAR(2000) = NULL
,@LastUpdateByID CHAR(13)

AS
    BEGIN
        SET NOCOUNT ON;		 
        -- Declare variables
        DECLARE @CurrentYear INT;
        SET @CurrentYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);

        DECLARE @BidYear INT;
        SET @BidYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);

        DECLARE @ExperienceYear INT;
        SET @ExperienceYear = (SELECT   PlanYearID FROM dbo.LkpIntPlanYear WHERE IsExperienceYear = 1);


        -- Set up PlanTypeGranularity hierarchy
        IF (SELECT  OBJECT_ID ('tempdb..#GranularityOrder')) IS NOT NULL
            DROP TABLE #GranularityOrder;
        CREATE TABLE #GranularityOrder
            (GranOrder           INT
            ,PlanTypeGranularity VARCHAR(500));
        INSERT INTO #GranularityOrder
            (GranOrder
            ,PlanTypeGranularity)
        VALUES (1, 'CPS')
			  ,(2, 'ActuarialRegion,ProductType')
              ,(3, 'IsNationwide,SNPType,ProductType')
              ,(4, 'ActuarialRegion')
              ,(5, 'IsNationwide,SNPType')
              ,(6, 'IsNationwide,ProductType')
              ,(7, 'IsNationwide');


        -- Get PlanInfoIDs, CPS codes, PlanYearIDs, BaseYearIDs from vwPlanInfo
        -- Only specified plans, or all plans if @PlanList is null
        IF (SELECT  OBJECT_ID ('tempdb..#PlanInfo')) IS NOT NULL
            DROP TABLE #PlanInfo;
        SELECT  DISTINCT
                PlanInfoID
               ,CPS
               ,PlanYear AS 'PlanYearID'
               ,@ExperienceYear AS 'BaseYearID'
        INTO    #PlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear
                AND IsOffMAModel = 'No'
                AND IsHidden = 0
                AND Region NOT IN ('Unmapped')
                AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@PlanList, ',', 1) )
                     OR @PlanList IS NULL);


        -- Get Components 
        IF (SELECT  OBJECT_ID ('tempdb..#Component')) IS NOT NULL
            DROP TABLE #Component;
        SELECT  Component
        INTO    #Component
        FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
        WHERE   IsPartOfPackage = 1


        -- Create shell
        IF (SELECT  OBJECT_ID ('tempdb..#TempShell')) IS NOT NULL
            DROP TABLE #TempShell;
        SELECT      DISTINCT
                    #PlanInfo.PlanInfoID
                   ,#PlanInfo.CPS
                   ,#PlanInfo.PlanYearID
                   ,#PlanInfo.BaseYearID
                   ,#Component.Component
        INTO        #TempShell
        FROM        #PlanInfo
       INNER JOIN   #Component
               ON 1 = 1;


        -- Create PlanTypeGranularity lookups for each PlanInfoID
        IF (SELECT  OBJECT_ID ('tempdb..#PlanTypeGranularityLookups')) IS NOT NULL
            DROP TABLE #PlanTypeGranularityLookups;
        SELECT      DISTINCT
                    dbo.vwPlanInfo.PlanInfoID
					,CONCAT(#TempShell.Component, ',', vwPlanInfo.CPS) AS 'CPS'
                   ,CONCAT (#TempShell.Component, ',', Region, ',', Product) AS 'ActuarialRegion,ProductType'
                   ,CONCAT (#TempShell.Component, ',', 1, ',', SNPType, ',', Product) AS 'IsNationwide,SNPType,ProductType'
                   ,CONCAT (#TempShell.Component, ',', Region) AS 'ActuarialRegion'
                   ,CONCAT (#TempShell.Component, ',', 1, ',', SNPType) AS 'IsNationwide,SNPType'
                   ,CONCAT (#TempShell.Component, ',', 1, ',', Product) AS 'IsNationwide,ProductType'
                   ,CONCAT (#TempShell.Component, ',', 1) AS 'IsNationwide'
        INTO        #PlanTypeGranularityLookups
        FROM        dbo.vwPlanInfo
       INNER JOIN   #TempShell
               ON #TempShell.PlanInfoID = dbo.vwPlanInfo.PlanInfoID;


        -- Source data from Trend_ProjProcess_SavedMappingOverrideRules
        IF (SELECT  OBJECT_ID ('tempdb..#sourceData')) IS NOT NULL
            DROP TABLE #sourceData;
        SELECT  DISTINCT
                CONCAT (Component, ',', RuleGroupGranularityValue) AS 'Lookup'
               ,OverrideRuleID
               ,Component
               ,RuleGroupGranularity
               ,RuleGroupGranularityValue
               ,OverridePlanTypeGranularity
               ,OverridePlanTypeGranularityValue
               ,OverrideDescription
               ,IsOverride
        INTO    #sourceData
        FROM    dbo.Trend_ProjProcess_SavedMappingOverrideRules WITH (NOLOCK);


        -- Select all possible overrides for each PlanInfoID. 
        -- Multiple overrides may apply.  Therefore, the next steps will select the lowest option on the hierarchy.     
        IF (SELECT  OBJECT_ID ('tempdb..#GranularityAndValueOptions')) IS NOT NULL
            DROP TABLE #GranularityAndValueOptions;
        SELECT      PlanInfoID
                   ,OverrideRuleID
                   ,Component
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
        INTO    #GranularityAndValueOptions
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [CPS] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
		UNION
        SELECT      PlanInfoID
                   ,OverrideRuleID
                   ,Component
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [ActuarialRegion,ProductType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,OverrideRuleID
                   ,Component
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [IsNationwide,SNPType,ProductType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,OverrideRuleID
                   ,Component
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON ActuarialRegion = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,OverrideRuleID
                   ,Component
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [IsNationwide,SNPType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,OverrideRuleID
                   ,Component
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON [IsNationwide,ProductType] = [Lookup]
        WHERE       [Lookup] IS NOT NULL
        UNION
        SELECT      PlanInfoID
                   ,OverrideRuleID
                   ,Component
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
        FROM        #PlanTypeGranularityLookups
        LEFT JOIN   #sourceData
               ON IsNationwide = [Lookup]
        WHERE       [Lookup] IS NOT NULL;


        -- Add all overrides to the shell. 
        -- Include Granularity Order, which allows the selection of the proper override option.
        IF (SELECT  OBJECT_ID ('tempdb..#ShellWithAllOptions')) IS NOT NULL
            DROP TABLE #ShellWithAllOptions;
        SELECT      #TempShell.PlanInfoID
                   ,CPS
                   ,PlanYearID
                   ,BaseYearID
                   ,#TempShell.Component
                   ,OverrideRuleID
                   ,RuleGroupGranularity
                   ,RuleGroupGranularityValue
                   ,OverridePlanTypeGranularity
                   ,OverridePlanTypeGranularityValue
                   ,OverrideDescription
                   ,IsOverride
                   ,GranOrder
        INTO        #ShellWithAllOptions
        FROM        #TempShell
        JOIN        #GranularityAndValueOptions
          ON CONCAT (#TempShell.PlanInfoID, ',', #TempShell.Component) = CONCAT (
                                                                         #GranularityAndValueOptions.PlanInfoID
                                                                        ,','
                                                                        ,#GranularityAndValueOptions.Component)
        LEFT JOIN   #GranularityOrder
               ON #GranularityOrder.PlanTypeGranularity = #GranularityAndValueOptions.RuleGroupGranularity;


        -- Select the appropriate override (based on granularity order). 
        IF (SELECT  OBJECT_ID ('tempdb..#GranularitySelection')) IS NOT NULL
            DROP TABLE #GranularitySelection;
        SELECT      #TempShell.PlanInfoID
                   ,#TempShell.CPS
                   ,#TempShell.PlanYearID
                   ,#TempShell.BaseYearID
                   ,#TempShell.Component
                   ,#ShellWithAllOptions.OverrideRuleID
                   ,MIN (COALESCE(#ShellWithAllOptions.GranOrder, 999)) AS 'GranOrder'
        INTO        #GranularitySelection
        FROM        #TempShell
        LEFT JOIN   #ShellWithAllOptions
               ON #ShellWithAllOptions.PlanInfoID = #TempShell.PlanInfoID
                  AND   #ShellWithAllOptions.CPS = #TempShell.CPS
                  AND   #ShellWithAllOptions.PlanYearID = #TempShell.PlanYearID
                  AND   #ShellWithAllOptions.BaseYearID = #TempShell.BaseYearID
                  AND   #ShellWithAllOptions.Component = #TempShell.Component
        LEFT JOIN   #GranularityOrder
               ON #GranularityOrder.GranOrder = #ShellWithAllOptions.GranOrder
        GROUP BY    #TempShell.PlanInfoID
                   ,#TempShell.CPS
                   ,#TempShell.PlanYearID
                   ,#TempShell.BaseYearID
                   ,#TempShell.Component
                   ,#ShellWithAllOptions.OverrideRuleID;


        -- Delete from and insert into final output table
        DELETE  FROM dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingOverrides WITH (ROWLOCK)
        WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanInfo));
        INSERT INTO dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingOverrides WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,Component
            ,OverrideRuleID
            ,OverridePlanTypeGranularity
            ,OverridePlanTypeGranularityValue
            ,OverrideDescription
            ,IsOverride
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT      #GranularitySelection.PlanInfoID
                   ,#GranularitySelection.CPS
                   ,#GranularitySelection.PlanYearID
                   ,#GranularitySelection.BaseYearID
                   ,#GranularitySelection.Component
                   ,#GranularityAndValueOptions.OverrideRuleID
                   ,#GranularityAndValueOptions.OverridePlanTypeGranularity
                   ,#GranularityAndValueOptions.OverridePlanTypeGranularityValue
                   ,#GranularityAndValueOptions.OverrideDescription
                   ,#GranularityAndValueOptions.IsOverride
                   ,@LastUpdateByID
                   ,GETDATE ()
        FROM        #GranularitySelection
        LEFT JOIN   #GranularityOrder
               ON #GranularityOrder.GranOrder = #GranularitySelection.GranOrder
        LEFT JOIN   #GranularityAndValueOptions
               ON #GranularityAndValueOptions.PlanInfoID = #GranularitySelection.PlanInfoID
                  AND   #GranularityAndValueOptions.Component = #GranularitySelection.Component
                  AND   #GranularityAndValueOptions.RuleGroupGranularity = #GranularityOrder.PlanTypeGranularity;


    END;
GO
