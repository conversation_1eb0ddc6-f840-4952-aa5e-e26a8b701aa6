SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [PrePricing].[fnGet<PERSON><PERSON><PERSON><PERSON>lProjected]   ******/
-------------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnGetPlanLevelProjected  
--  
-- AUTHOR: <PERSON><PERSON> 
--  
-- CREATED DATE: 2024-Nov-19  
-- HEADER UPDATED: 2024-Nov-19  
--  
-- DESCRIPTION: To get Plan level Projected Benefits Information.
--  
-- PARAMETERS:  
--  Input:  
--         
--  Output:  
--  @Results
-- TABLES:  
--  Read: 
--	prepricing.MarketInputValue
--	prepricing.PlanInfo
--	dbo.SavedPlanInfo 
--	dbo.SavedForecastSetup
--  Write:  
--  
-- VIEWS:  
--        
--  
-- FUNCTIONS:  
--  
-- STORED PROCS:  
--  
-- HISTORY:  
-- ---------------------------------------------------------------------------------------------
-- DATE        VERSION      CHANGES MADE						DEVELOPER 
------------------------------------------------------------------------------------------------  
-- 2024-Nov-19  1			Initial Version                     Surya Murthy
-- 2025-Mar-05  2			CPS Logic Added						Surya Murthy
-- ---------------------------------------------------------------------------------------------
CREATE FUNCTION [PrePricing].[fnGetPlanLevelProjected] 
(  
)  
RETURNS @Results TABLE  
(    
	[BenefitYearID] [int] NOT NULL,
    [ForecastID] [int] NOT NULL,   
	[CPS] VARCHAR(13) NULL, 
	[PlanName] VARCHAR(200) NULL,
	[INDeductible] VARCHAR(200) NULL,
	[INMOOP] VARCHAR(200) NULL,
	[OONDeductible] VARCHAR(200) NULL,
	[OONMOOP] VARCHAR(200) NULL,
	[CombinedDeductible] VARCHAR(200) NULL,
	[CombinedMOOP] VARCHAR(200) NULL,
	[PartBDeductible] VARCHAR(200) NULL,
	[DeductTypeDesc] VARCHAR(200) NULL
) 
AS  
BEGIN  
    WITH innetwork AS (
	SELECT 
		Planinfoid, 
		[1] AS CombinedDeductible,
		[2] AS CombinedMOOP, 
		[3] AS InDeductible,
		[4] AS DeductTypeDesc, 
		[5] AS INMOOP,
		[6] AS PartBDeductible
	FROM 
	(
		SELECT  planinfoid,subcategoryid, ISNULL(invalue,'') AS invalue 
		FROM prepricing.MarketInputValue WITH(NOLOCK)
	) p
	PIVOT
	(
		MAX(invalue) FOR subcategoryid IN([1],[2],[3],[4],[5],[6])
	) AS pvt

	),
	OutOfNetwork AS (
	SELECT
		Planinfoid,  
		[3] AS OONDeductible, 
		[5] AS OONMOOP
	FROM 
	(
		SELECT  planinfoid,subcategoryid, ISNULL(oonvalue,'') AS oonvalue 
		FROM prepricing.MarketInputValue WITH(NOLOCK)
	) p
	PIVOT
	(
		MAX(oonvalue) FOR subcategoryid IN([3],[5])
	) AS pvt

	), combined AS (
		SELECT inn.*, 
                  oon.OONDeductible,
                  oon.OONMOOP FROM innetwork Inn JOIN OutOfNetwork oon ON inn.PlanInfoID = oon.planinfoid
	 )
	INSERT @Results
	SELECT
		sfs.PlanYear AS BenefitYearID,
		sfs.ForecastID,
		spi.CPS,
		spi.PlanName,
		ISNULL(combined.InDeductible,'') AS INDeductible,
		ISNULL(combined.INMOOP,'') AS INMOOP,
		ISNULL(combined.OONDeductible,'') AS OONDeductible,
		ISNULL(combined.OONMOOP,'') AS OONMOOP,
		ISNULL(combined.CombinedDeductible,'') AS CombinedDeductible,
		ISNULL(combined.CombinedMOOP,'') AS CombinedMOOP,
		ISNULL(combined.PartBDeductible,'') AS PartBDeductible,
		ISNULL(combined.DeductTypeDesc,'') AS DeductTypeDesc
	FROM combined JOIN prepricing.PlanInfo ppi WITH(NOLOCK) ON combined.planinfoid = ppi.planinfoid
	JOIN dbo.SavedPlanInfo spi WITH(NOLOCK) ON spi.CPS=ppi.CPS AND ppi.PlanYear=spi.PlanYear
	JOIN dbo.SavedForecastSetup sfs WITH(NOLOCK) ON sfs.PlanInfoID=spi.PlanInfoID  
	RETURN  
END
GO
