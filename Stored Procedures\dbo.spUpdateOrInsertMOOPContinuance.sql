SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_<PERSON>ULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: 
--
-- AUTHOR: <PERSON> Wright
--
-- CREATED DATE: 2011-Jan-19
-- HEADER UPDATED: 2011-Jan-19
--
-- DESCRIPTION: Responsible for updating the CPDID in the SavedPlanHeader table
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@UserID
--		@CPDID
--		
--	Output: NONE
--
-- TABLES:
--	Read:
--
--	Write:
--		SavedPlanHeader
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-19      1           Initial Version                                                     Craig Wright
-- 2011-Feb-10		2			Added LastUpdateBy to populate in SavedPlanHeader. Also got rid 	Craig Wright
--									of PlanYearID
-- 2019-Jun-10      3           Updated to use ForecastID and update CPDID on savedforecastsetup      Kiran Pant
-- 2019-Oct-30	    4           Replace @UserID from char(13) to char(7)								Chhavi Sinha
-- ---------------------------------------------------------------------------------------------------------------------
CREATE  PROCEDURE [dbo].[spUpdateOrInsertMOOPContinuance]
(
    @ForecastID INT,
    @UserID CHAR(7),
	@CPDID SMALLINT
)
AS
    SET NOCOUNT ON

DECLARE @LastUpdateDateTime DATETIME
		
SET @LastUpdateDateTime = GETDATE()

	UPDATE SavedForecastSetup
	SET CPDID = @CPDID,
		LastUpdateByID = @UserID,
		LastUpdateDateTime = @LastUpdateDateTime
	WHERE ForecastID= @ForecastID
GO
