SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- PROCEDURE NAME: spDashboardReportSummaryPowerBI

-- DESCRIPTION: This SP returns extract for summary of all views for user actions from AppLogs table
--
-- PARAMETERS:
--    Input: 
--		@LastSuccessfullRunLogid
--		@LastSuccessfulRunTimestampFEM
--		@LastUpdateByID
--
-- SPs:
--		[dbo].[spDashboardReportErrorCountPBI2]
--		[dbo].[spDashboardReportBarcToMaauiSyncPBI2]
--		[dbo].[spDashboardReportMaauiToBarcSyncPBI2]
--		[dbo].[spDashboardReportMaauiToTargetMERTargetMemberPremiumProfitPBI2]
--		[dbo].[spDashboardReportMaauiToUpdateTrendPBI2]
--		[dbo].[spDashboardReportMSBImportsPBI2]
--		[dbo].[spDashboardReportBenefituploadImportPBI2]
--		[dbo].[spDashboardReportReportinguploadImportPBI2]
--		[dbo].[spDashboardReportRiskScoresImportPBI2]
--		[dbo].[spDashboardReportCreateBptPBI2]
--		[dbo].[spDashboardReportPREPANDSCTSQLRefreshPBI2]
--		[dbo].[spDashboardReportImportPlansIntoPREPPBI2]
--      [dbo].[spDashboardReportBPTCreationMAAUIPBI2]
--		[dbo].[spDashboardReportMaauiToSPVRepricePBI2]
--		
-- Example 
-- Exec [dbo].[spDashboardReportSummaryPowerBI] '21667434','2023-09-25 00:00:00.000', 'SLP3707'

-- HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION			CHANGES MADE                                                                            DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2023-Aug-24			1			Initial Version                                                                         Sheetal Patil
-- 2023-Sep-19			2			Updated procedure for BPT Creation MAAUI                                                Chaitanya Durga
-- 2023-Oct-03	        3	        Updated procedure for SPV Reprice														Archana Sahu
-- 2023-Nov-01			4			Added @LastUpdateByID parameter to log error 
--									Added TRY and CATCH BLOCK FOR ERROR Handling
--									Added Insert Into statement to load data in Final table									Sheetal Patil
--2024-Mar-20			5			Added spDashboardReportPREPANDSCTSQLRefreshPBI2	
--									Added Parameter @LastSuccessfulRunTimestampSCT											Sheetal Patil
--2024-Sep-24           6           Removed sp [spDashboardReportSalesMembershipImportPBI2] from Summary due to 
--                                  removal Of Import Option from MAAUI UI                                                  Chaitanya Durga      
---------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROC [dbo].[spDashboardReportSummaryPowerBI]
 (@LastSuccessfullRunLogid INT
 ,@LastSuccessfulRunTimestampFEM DATETIME
 ,@LastUpdateByID VARCHAR(50)
 ,@LastSuccessfulRunTimestampSCT DATETIME)

AS
BEGIN

SET NOCOUNT ON;  

BEGIN TRY
BEGIN TRANSACTION;

DECLARE @TTEMError TABLE
( [UserActions] VARCHAR(1000) NULL
      ,[UserID]  [varchar] (50) NULL
      ,[Run Date]  [datetime] NULL  
      ,[Plans] VARCHAR(MAX)NULL
	  ,[Plan Count] INT NULL
      ,[ErrorCount] INT NULL
      ,RunEndDate  [datetime]  NULL  
      ,ExecutionTime INT NULL
	  ,[ErrorMessage]VARCHAR(MAX) NULL) 

INSERT INTO @TTEMError 
Exec [dbo].[spDashboardReportErrorCountPBI2] @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM



DECLARE  @AppLogReport TABLE
(
[UserActions] VARCHAR(1000) NULL
      ,[UserID] [varchar] (50) NULL
      ,[Run Date] [datetime] NULL
      ,[Type]  VARCHAR(MAX) NULL
      ,[Plans] VARCHAR(MAX)NULL
      ,[Plan Count] INT NULL
      ,[ExecutionTime]  INT NULL
      ,[characterEvenCount] INT NULL
      ,[ErrorCount] INT NULL
	  ,[RunEndDate] [datetime] NULL
	   ,[ErrorMessage] VARCHAR(MAX) NULL)


INSERT INTO @AppLogReport
EXEC dbo.spDashboardReportBarcToMaauiSyncPBI2  @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM

INSERT INTO @AppLogReport
EXEC dbo.spDashboardReportMaauiToBarcSyncPBI2 @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM

INSERT INTO @AppLogReport
EXEC dbo.spDashboardReportMaauiToTargetMERTargetMemberPremiumProfitPBI2 @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM

INSERT INTO @AppLogReport
EXEC dbo.spDashboardReportMaauiToUpdateTrendPBI2 @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM

INSERT INTO @AppLogReport
Exec [dbo].[spDashboardReportMSBImportsPBI2] @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM

INSERT INTO @AppLogReport
Exec [dbo].[spDashboardReportBenefituploadImportPBI2] @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM


INSERT INTO @AppLogReport
Exec[dbo].[spDashboardReportReportinguploadImportPBI2] @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM


INSERT INTO @AppLogReport
EXEC [dbo].[spDashboardReportRiskScoresImportPBI2] @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM


INSERT INTO @AppLogReport
EXEC [dbo].[spDashboardReportMaauiToSPVRepricePBI2] @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM

INSERT INTO @AppLogReport
EXEC [dbo].[spDashboardReportBPTCreationMAAUIPBI2] @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM

INSERT INTO @AppLogReport
EXEC [dbo].[spDashboardReportPREPANDSCTSQLRefreshPBI2]  @LastSuccessfulRunTimestampSCT


DECLARE  @AppLogSQLPREPReport TABLE
([UserActions] VARCHAR(1000) NULL
      ,[UserID] [varchar] (50) NULL
      ,[Run Date] [datetime] NULL
      ,[Type]  VARCHAR(MAX) NULL
      ,[Plans] VARCHAR(MAX)NULL
      ,[Plan Count] INT NULL
      ,[ExecutionTime] INT NULL
      ,[characterEvenCount]  INT NULL
      ,[ErrorCount]INT NULL
	  ,[ErrorMessage] VARCHAR(MAX)NULL)

INSERT INTO @AppLogSQLPREPReport
EXEC [dbo].[spDashboardReportImportPlansIntoPREPPBI2]  @LastSuccessfullRunLogid, @LastSuccessfulRunTimestampFEM


INSERT INTO dbo.[DashboardReportSummaryPBI2] (
DashboardReportId 
,SnapShotDate
,UserAction
,UserID
,[Run Date]
,Plans
,[Plan/Region Count]
,ExecutionTime
,RunEndDate
,ErrorCount
,ErrorMessage)
SELECT 
NEWID()AS DashboardReportId, GETDATE()AS SnapShotDate,
UserAction,UserID,[Run Date],Plans,[Plan/Region Count],ExecutionTime,RunEndDate,ErrorCount,[ErrorMessage] 
FROM
(
SELECT ISNULL(rpt.UserActions ,err.[UserActions]) AS 'UserAction'
,ISNULL(rpt.UserId,err.UserID) AS 'UserId'
,ISNULL(rpt.[Run Date],err.[Run Date]) AS 'Run Date'
, CASE WHEN characterEvenCount = 1 THEN 'Plans reach maximum limit'
       ELSE rpt.Plans END AS Plans
, ISNULL(CASE WHEN characterEvenCount = 1 THEN 0
       ELSE rpt.[Plan Count] END ,err.[Plan Count])  AS [Plan/Region Count],
	   CASE WHEN rpt.ExecutionTime IS NULL THEN CONVERT(FLOAT,err.ExecutionTime)/1000
	   ELSE  CONVERT(FLOAT,rpt.ExecutionTime)/1000 END AS ExecutionTime	,
	   CASE WHEN rpt.UserActions='MSB Imports' OR rpt.UserActions='Upload risk score file to Bid Model' OR 
rpt.UserActions='Benefit upload' OR rpt.UserActions='Reporting upload' THEN
        ISNULL(rpt.RunEndDate,err.RunEndDate) 
		ELSE
  ISNULL(DATEADD(MILLISECOND,rpt.ExecutionTime ,rpt.[Run Date]),err.RunEndDate) END AS RunEndDate,
CASE WHEN err.ErrorCount IS NULL THEN 0
ELSE err.ErrorCount END AS 'ErrorCount'
,err.[ErrorMessage] AS [ErrorMessage]
FROM @AppLogReport	rpt 
FULL OUTER JOIN @TTEMError err ON rpt.UserActions=err.[UserActions] AND rpt.UserId =err.UserID
AND rpt.[Run Date] BETWEEN 	err.[Run Date] 	 AND err.RunEndDate
GROUP BY ISNULL(rpt.UserActions ,err.[UserActions]) 
,ISNULL(rpt.UserId,err.UserID)
, ISNULL(rpt.[Run Date],err.[Run Date])  
, CASE WHEN characterEvenCount = 1 THEN 'Plans reach maximum limit'
       ELSE rpt.Plans END 
	  , ISNULL(CASE WHEN characterEvenCount = 1 THEN 0
       ELSE rpt.[Plan Count] END ,err.[Plan Count]) 
, CASE WHEN rpt.ExecutionTime IS NULL THEN CONVERT(FLOAT,err.ExecutionTime)/1000
	   ELSE  CONVERT(FLOAT,rpt.ExecutionTime)/1000 END ,	
	    CASE WHEN rpt.UserActions='MSB Imports' OR rpt.UserActions='Upload risk score file to Bid Model' OR 
rpt.UserActions='Benefit upload' OR rpt.UserActions='Reporting upload' THEN
        ISNULL(rpt.RunEndDate,err.RunEndDate) 
		ELSE
  ISNULL(DATEADD(MILLISECOND,rpt.ExecutionTime ,rpt.[Run Date]),err.RunEndDate) END,	   
CASE WHEN err.ErrorCount IS NULL THEN 0
ELSE err.ErrorCount END
,err.[ErrorMessage]

UNION ALL

SELECT [UserActions] 
,UserID
,[Run Date]
, CASE WHEN characterEvenCount = 1 THEN 'Plans reach maximum limit'
       ELSE Plans END AS Plans
, CASE WHEN characterEvenCount = 1 THEN 0
       ELSE [Plan Count] END AS [Plan/Region Count]
,[ExecutionTime]
,[Run Date] AS RunEndDate
,[ErrorCount]
,[ErrorMessage] AS [ErrorMessage]
FROM @AppLogSQLPREPReport spt
GROUP BY  [UserActions] 
,[UserID]
,[Run Date] 
, CASE WHEN characterEvenCount = 1 THEN 'Plans reach maximum limit'
       ELSE Plans END  
, CASE WHEN characterEvenCount = 1 THEN 0
       ELSE [Plan Count] END 
,[ExecutionTime]
,[ErrorCount]
,[ErrorMessage]


) results

 
COMMIT TRANSACTION;
END TRY
  

BEGIN CATCH
IF XACT_STATE() <> 0
ROLLBACK TRANSACTION;

			         
DECLARE @ErrorMessage NVARCHAR(4000);  
DECLARE @ErrorSeverity INT;  
DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
DECLARE @errSrc VARCHAR(MAX) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate DATETIME=GETDATE()
DECLARE @StoredProcedure VARCHAR(150) = 'spDashboardReportSummaryPowerBI'

SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'+ CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)
SELECT @Errormessage = 'Error in '+@StoredProcedure+': ' + ERROR_MESSAGE()
,@ErrorState = ERROR_STATE()
,@ErrorSeverity = ERROR_SEVERITY()

			
--	---Insert into app log for logging error------------------
EXEC spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID

RAISERROR (@Errormessage,@ErrorSeverity,@ErrorState)
END CATCH


END;
GO
