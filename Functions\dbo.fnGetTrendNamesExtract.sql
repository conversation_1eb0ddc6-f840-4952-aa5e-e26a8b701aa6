SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetTrendNamesExtract]  ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetTrendNamesExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-21
-- HEADER UPDATED: 2011-Dec-21
--
-- DESCRIPTION: Designed to extract fields for Trend Names - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	
--		SavedPlanDetail
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-21		1			Initial Version														Bobby Jaegers
-- 2012-Jun-22		2			Added Trend Name													Nick Koesters
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetTrendNamesExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	TrendIDName VarChar(MAX)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT  ForecastID,
					TrendIDName = CAST(spd.TrendID AS varchar(MAX)) + ': ' + TrendName
			FROM SavedPlanDetail spd
			INNER JOIN SavedMATrendHeader sth
			ON spd.TrendID = sth.TrendID  
			WHERE MARatingOptionID = 1
			
       
	ELSE
		INSERT @Results
			SELECT  ForecastID,
					TrendIDName = CAST(spd.TrendID AS varchar(MAX)) + ': ' + TrendName
			FROM SavedPlanDetail spd
			INNER JOIN SavedMATrendHeader sth
			ON spd.TrendID = sth.TrendID
			WHERE ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
			AND MARatingOptionID = 1
			
RETURN
END
GO
