SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetProductPairingsExtract
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2020-1-10
-- HEADER UPDATED: 2020-1-10
--
-- DESCRIPTION: Function responsible for pulling product pairs in the bid model.
--
-- PARAMETERS:
--	Input: 
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		SavedPlanProductPairing
--		SavedForecastSetup
--		SavedPlanInfo
--		SavedRollupForecastMap
--		SavedRollupInfo
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
---
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2020-Jan-08		1			Initial Version														Brent Osantowski		
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetProductPairingsExtract]
    (
   @WhereIN Varchar(MAX)=NULL
    )
RETURNS @Results TABLE 
    (
    [Rollup] VARCHAR(40),
	[BidCPS] varchar(13)NOT NULL,
	[PairedPlans] varchar(13)

    ) AS

BEGIN  
    INSERT @Results    
       SELECT 
		sri.RollupName,
		spi.cps	as BidCPS,
       spp.ProductPairingBid
	   FROM dbo.SavedPlanProductPairing spp
	   INNER JOIN dbo.SavedForecastSetup sfs ON sfs.ForecastID = spp.ForecastID
	   INNER JOIN dbo.SavedPlanInfo spi ON spi.PlanInfoID = sfs.PlanInfoID
	   INNER JOIN dbo.SavedRollupForecastMap srf ON srf.ForecastID = sfs.ForecastID
	   INNER JOIN dbo.SavedRollupInfo sri ON sri.RollupID = srf.RollupID
	   WHERE 
	   srf.RollupID = 1
	   AND sfs.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
    RETURN
END


GO
