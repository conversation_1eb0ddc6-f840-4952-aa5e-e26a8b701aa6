SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
----
-- PROCEDURE NAME: spUpdateOrInsertExpenseProfit
--
-- CREATOR: Christian Cofie
--
-- CREATED DATE:Mar-20-2007
--
-- DESCRIPTION: Stored Procedure responsible for saving Expense & Profit on the Bid Summary Characteristics tab in the SPAct Model
-- PARAMETERS: Input  :
--	@ForecastID
--	@IsMAPD 
--	@ExpensePercent 
--	@ProfitPercent 
--      Output : NONE
--
-- TABLES : Read : SavedPlanAssumptions
--	      Write: SavedPlanAssumptions
--
-- VIEWS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---
-- DATE			VERSION		CHANGES MADE													DEVELOPER		
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--
-- Mar-20-2007		1   	Initial Version													Christian Cofie
-- Apr-30-2007		2   	Added 2 Rx Parameters. 											Christian Cofie
-- Apr-21-2008		3		Removed Rx Basic/Supp params. Revised IF/ELSE logic				Brian Lake
-- Apr-30-2009		4		Allows for Expense to be a PMPM									Keith Galloway
-- 2010-Oct-12      5       Revised for 2012 database										Michael Siekerka
-- 2011-Feb-08		6		Added SalesMembership and removed @ExpensesPMPM					Joe Casey
-- 2011-Jun-02		7		Replaced LkpIntPlanYear with dbo.fnGetBidYear()					Bobby Jaegers
-- 2011-Jun-14		8		Changed @PlanYearID to return SMALLINT instead of INT			Bobby Jaegers
-- 2011-Sep-13		9		Added value to be inserted for SalesAdjustmentFactor in			Craig Wright
--								SavedPlanAssumptions
-- 2012-Feb-24		10		Made changes to account for new admin buckets					Alex Rezmerski
-- 2020-Dec-28		11		Added NOLOCK													Mahendran Chinnaiah
-- 2022-Sep-22		12		Added @Xvariables for input										Phani Adduri
-- 2024-Jul-02		13		Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE  PROCEDURE [dbo].[spUpdateOrInsertExpenseProfit]
(
	@ForecastID INT,
	@IsMAPD BIT,
	@ProfitPercent FLOAT
)
AS

DECLARE @XForecastID INT = @ForecastID, 
@XIsMAPD BIT = @IsMAPD, 
@XProfitPercent FLOAT = @ProfitPercent, 
@PlanYearID SMALLINT;

SELECT @PlanYearID = dbo.fnGetBidYear()
SET NOCOUNT ON;
	BEGIN
		IF EXISTS -- If a Record exists, update the profit/expense.
		(   
            SELECT 1 
			FROM SavedPlanAssumptions WITH(NOLOCK)
			WHERE ForecastID = @XForecastID
                AND IsMAPD = @XIsMAPD
        )
            BEGIN
		        UPDATE SavedPlanAssumptions 
		        SET ExpensePercent= NULL, ProfitPercent = @XProfitPercent
		        WHERE ForecastID = @XForecastID
                    AND IsMAPD = @XIsMAPD
            END
        ELSE IF NOT EXISTS --Make sure that there isn't a record in the table for the opposite value of IsMAPD, this will throw an error
        (   
            SELECT 1 
			FROM SavedPlanAssumptions WITH(NOLOCK)
			WHERE ForecastID = @XForecastID
        )
            BEGIN
                INSERT SavedPlanAssumptions(
                    PlanYearID, 
                    ForecastID, 
                    IsMAPD, 
                    ExpensePercent, 
                    ProfitPercent, 
                    RxBasicPremium, 
                    RxSuppPremium, 
                    ExpensePMPM, 
                    SecondaryPayerAdjustment, 
                    SalesAndMarketingPercent, 
                    DirectAdminPercent, 
                    IndirectAdminPercent,
                    SalesMembership,                    
                    QualityInitiatives,
                    TaxesAndFees)
                VALUES(
                    @PlanYearID,
                    @XForecastID, 
                    @XIsMAPD,
                    0,
                    @XProfitPercent,
                    0,
                    0,
                    0,
                    0,
                    NULL,
                    NULL, 
                    NULL,
                    0,                    
                    0,
                    0)
            END
    END
    --GRANT EXECUTE ON  [dbo].[spUpdateOrInsertExpenseProfit] TO [MAPDModel]
GO
