SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Description:	Stored procedure responsible for inserting the data into TrendAdj_SAR_SavedOutlier,
-- TrendAdj_SAR_SavedSuperBIDE, TrendAdj_SAR_SavedUCDB 
--
-- PARAMETERS:  
-- Input:  
--
-- Output:  
--  
-- TABLES:  
-- Read:
--	   ImportDataStaging
--     
-- Write:
-- TrendAdj_SAR_SavedOutlier  
-- TrendAdj_SAR_SavedSuperBIDE
-- TrendAdj_SAR_SavedUCDB  
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION        CHANGES MADE                                                      DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-NOV-07         1          Initial Version                                                  Latoya Garvey
-- ----------------------------------------------------------------------------------------------------------------------  
-- =======================================================================================================================
CREATE PROCEDURE [dbo].[spAppImportTrendAdjServiceAreaReduction]
(
	 @StageId VARCHAR(100)
)
AS
BEGIN
SET NOCOUNT ON;

		DECLARE @jsonData VARCHAR(MAX);
		DECLARE @UserId VARCHAR(7) ;

		SELECT @jsonData = JsonData, @UserId = UserId
		FROM dbo.ImportDataStaging WITH (NOLOCK)
		WHERE StageId = @StageId;

				
 /***************Import script for the TrendAdjSARSavedOutlier Table*****************************/		
		DECLARE @tbl__importData TABLE
		(
			[PlanYear] [INT] NOT NULL,
			[CPS] [CHAR](13) NOT NULL,
		    [SSStateCountyCD] [CHAR](5) NOT NULL,
			[Allowed] [DECIMAL](20, 12) NOT NULL,
			[Units] [DECIMAL](20, 12) NOT NULL,
			[LastUpdateByID] [CHAR](7) NOT NULL,
			[LastUpdateDateTime] [DATETIME] NOT NULL

		);

		INSERT INTO @tbl__importData 
		SELECT
			PlanYear,
			CAST(CPS AS CHAR(13)) AS CPS,
			CAST(SSStateCountyCD AS CHAR(5)) AS SSStateCountyCD,
			Allowed,
			Units,
			@UserId,
			GETDATE()			
		FROM 
		OPENJSON(@jsonData, '$.TrendAdjSARSavedOutlier')
		WITH
	    (
		    [PlanYear] [INT],
			[CPS] [CHAR](13),
		    [SSStateCountyCD] [CHAR](5),
			[Allowed] [DECIMAL](20, 12),
			[Units] [DECIMAL](20, 12)
		);
		
	
		DELETE FROM dbo.TrendAdj_SAR_SavedOutlier;

		INSERT INTO [dbo].[TrendAdj_SAR_SavedOutlier]
		(
			PlanYear,
			CPS,
			SSStateCountyCD,
			Allowed,
			Units,
			LastUpdateByID,
			LastUpdateDateTime
		)
		SELECT source.PlanYear, source.CPS, source.SSStateCountyCD, source.Allowed, source.Units, 
		source.LastUpdateByID, source.LastUpdateDateTime FROM @tbl__importData AS source
				
 /***************Import script for the TrendAdjSARSavedSuperBIDE Table*****************************/


		DECLARE @tbl__importBIDE TABLE
		(
			[PlanYear] [INT] NOT NULL,
			[CPS] [CHAR](13) NOT NULL,
			[SSStateCountyCD] [CHAR](5) NOT NULL,
			[CMSMemberMonths] [INT] NOT NULL,
			[AllowedNonSQS] [DECIMAL](16, 6) NOT NULL,
			[AllowedOtherPartB] [DECIMAL](16, 6) NOT NULL,
			[LastUpdateByID] [CHAR](7) NOT NULL,
			[LastUpdateDateTime] [DATETIME] NOT NULL
			
		);
	
	   INSERT INTO @tbl__importBIDE
		SELECT
			PlanYear,
			CAST(CPS AS CHAR(13)) AS CPS,
			CAST(SSStateCountyCD AS CHAR(5)) AS SSStateCountyCD,
			CMSMemberMonths,
			AllowedNonSQS,
			AllowedOtherPartB,
			@UserId,
			GETDATE()			
		FROM 
		OPENJSON(@jsonData, '$.TrendAdjSARSavedSuperBIDE')
		WITH
	    (
		    [PlanYear] [INT],
			[CPS] [CHAR](13),
			[SSStateCountyCD] [CHAR](5),
			[CMSMemberMonths] [INT],
			[AllowedNonSQS] [DECIMAL](16, 6),
			[AllowedOtherPartB] [DECIMAL](16, 6),
			[Units] [DECIMAL](20, 12)
		);

		
		DELETE FROM dbo.TrendAdj_SAR_SavedSuperBIDE 

		INSERT INTO [dbo].[TrendAdj_SAR_SavedSuperBIDE]
		(
			PlanYear,
			CPS,
			SSStateCountyCD,
			CMSMemberMonths,
			AllowedNonSQS,
			AllowedOtherPartB,
			LastUpdateByID,
			LastUpdateDateTime
		)
		SELECT
		source.PlanYear, source.CPS, source.SSStateCountyCD, source.CMSMemberMonths, source.AllowedNonSQS, source.AllowedOtherPartB, 
		source.LastUpdateByID, source.LastUpdateDateTime FROM @tbl__importBIDE AS source


	/******************Import script for the TrendAdjSavedUCDB Table****************************************/	

		DECLARE @tbl__importUCDB TABLE
		(
			[PlanYear] [INT] NOT NULL,
			[CPS] [CHAR](13) NOT NULL,
			[SSStateCountyCD] [CHAR](5) NOT NULL,
			[BaseYearRelativity] [DECIMAL](18, 8) NOT NULL,
			[LastUpdateByID] [CHAR](7) NOT NULL,
			[LastUpdateDateTime] [DATETIME] NOT NULL
			
		);
	
	   INSERT INTO @tbl__importUCDB
		SELECT
			PlanYear,
			CAST(CPS AS CHAR(13)) AS CPS,
			CAST(SSStateCountyCD AS CHAR(5)) AS SSStateCountyCD,
			BaseYearRelativity,
			@UserId,
			GETDATE()			
		FROM 
		OPENJSON(@jsonData, '$.TrendAdjSavedUCDB')
		WITH
	    (
		   	[PlanYear] [INT],
			[CPS] [CHAR](13),
			[SSStateCountyCD] [CHAR](5),
			[BaseYearRelativity] [DECIMAL](18, 8)
		);

		DELETE FROM dbo.TrendAdj_SAR_SavedUCDB;

		INSERT INTO dbo.TrendAdj_SAR_SavedUCDB
		(
			PlanYear,
			CPS,
			SSStateCountyCD,
			BaseYearRelativity,
			LastUpdateByID,
			LastUpdateDateTime
		)
		SELECT source.PlanYear, source.CPS, source.SSStateCountyCD, source.BaseYearRelativity,
		source.LastUpdateByID, source.LastUpdateDateTime FROM @tbl__importUCDB source

	    DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;

END
