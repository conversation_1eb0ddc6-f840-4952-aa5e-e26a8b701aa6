SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateRatebookByContract
--
-- AUTHOR: Tim Gao
--
-- CREATED DATE: 2010-Oct-20
-- HEADER UPDATED: 2011-Mar-01
--
-- DESCRIPTION: Update PerExtCMSRatebookByContract after CMS release the ratebook plus update StateCounty table and PPO reginal ratebook
--
-- PARAMETERS:
--	Input:
--	Output:
--
-- TABLES:
--	Read:
--		PerExtCMSContractQualityStars
--		PerExtCMSRatebookDefault_temp
--	Write:
--		PerExtCMSRatebookByContract
-- 
-- VIEWS:
--
-- FUNCTIONS:

-- STORED PROCS:

-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE																DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2012-08-28		1			Initial Version																Tim Gao
-- 2012-09-13		2			Added code to update SavedPlanHeader(set IsToReprice =1)					Tim Gao
-- 2012-Oct-09      2           Removed Grant Execution to MAPD Model										Tim Gao
-- 2014-Mar-19		3			Edited CMSRiskRate and CMSPartBOnlyRiskRate to take in LowEnrollmentRate	Siliang Hu 
-- 2019-Jun-28		4			Replace SavedPlanHeader with SavedForecastSetup			                    Pooja Dahiya
-- 2019-Oct-30	    5           Replace @UserID from char(13) to char(7)							        Chhavi Sinha
-- 2024-Feb-1       6       ADD audit Columns											                Latoya Garvey
-- ---------------------------------------------------------------------------------------------------------------------
CREATE  PROCEDURE [dbo].[spUpdateRatebookByContract]
(
	@UserID VARCHAR(7)
)
AS
    SET NOCOUNT ON

	BEGIN TRAN
		BEGIN
			DECLARE @LastUpdate DATETIME
			SET @LastUpdate = GETDATE()
			 
			 -- update PerExtCMSRatebookByContract 
			DELETE PerExtCMSRatebookByContract  -- delete the existing table
			INSERT INTO PerExtCMSRatebookByContract
			SELECT 
				cdt.PlanYearID, 
				cdt.RatebookID, 
				cdt.StateTerritoryID, 
				cdt.CountyCode, 
				cs.ContractNumber,
				IsDblBonusCounty = CAST(cdt.IsDblBonusCounty AS TINYINT),
				PhaseInPeriod = CAST(cdt.PhaseInPeriod AS TINYINT),
				Quartile = CAST(cdt.Quartile AS TINYINT),
				CMSRiskRate =	
					CASE cs.IsLowEnrollment
						WHEN 1 THEN cdt.LowEnrollmentRates
						ELSE 
							CASE cs.CombinedStars
								WHEN 5 THEN cdt.FiveStarRates
								WHEN 4.5 THEN cdt.FourAndHalfRates
								WHEN 4 THEN cdt.FourStarRates
								WHEN 3.5 THEN cdt.ThreeAndHalfRates
								WHEN 3 THEN cdt.ThreeStarRates
								ELSE cdt.LessThanThreeRates
							END
					END,
				CMSPartBOnlyRiskRate = 
					CASE cs.IsLowEnrollment
						WHEN 1 THEN CAST(cdt.LowEnrollmentRates * cdt.PartBRate  AS DECIMAL (16,12))
						ELSE					
							CASE cs.CombinedStars
								WHEN 5 THEN CAST(cdt.FiveStarRates * cdt.PartBRate AS DECIMAL(16,12))
								WHEN 4.5 THEN CAST(cdt.FourAndHalfRates * cdt.PartBRate AS DECIMAL(16,12))
								WHEN 4 THEN CAST(cdt.FourStarRates * cdt.PartBRate AS DECIMAL(16,12))
								WHEN 3.5 THEN CAST(cdt.ThreeAndHalfRates * cdt.PartBRate AS DECIMAL(16,12))
								WHEN 3 THEN CAST(cdt.ThreeStarRates * cdt.PartBRate AS DECIMAL(16,12)) 
								ELSE CAST(cdt.LessThanThreeRates * cdt.PartBRate AS DECIMAL(16,12))
							END	
					END, 
				cdt.CMSIPCostShare, 
				cdt.CMSSNFCostShare, 
				CMSOPCostSahre = cdt.CMSOtherCostShare, 
				cdt.CMSOtherCostShare, 
				cdt.CMSIPFFSCosts, 
				cdt.CMSSNFCosts, 
				CMSOPCosts = cdt.CMSOtherCosts, 
				cdt.CMSOtherCosts, 
				cdt.CMSPartAEquivCostShare, 
				cdt.CMSPartBEquivCostShare, 
				CMSMedicareEligibles = CAST(cdt.CMSMedicareEligibles AS INT),
				CMSEnrollment = CAST(cdt.CMSEnrollment AS INT),
				cdt.PartBRate,
				LastUpdateByID = @UserID,
			    LastUpdateDateTime = @LastUpdate			
			FROM PerExtCMSRatebookDefault_temp cdt
			CROSS JOIN  PerExtCMSContractQualityStars cs
			
			-- update LkpExtCMSStateCounty table 
			DELETE LkpExtCMSStateCounty
	
			INSERT INTO LkpExtCMSStateCounty
			SELECT 
				PlanYearID,
				StateTerritoryID,
				CountyCode,
				CountyName
			FROM PerExtCMSRatebookDefault_temp 
			
	        -- Update RPPORatebookbyContract table 
	        DELETE RPPORatebookbyContract
			INSERT INTO RPPORatebookbyContract
			 
			SELECT 
				MARegion,
				ContractNumber,
				Quartile=NULL,
				PhaseInPeriod=NULL,
				MARegionRate,
				StatutoryComponent=NULL,
				LastUpdateByID = @UserID,
			    LastUpdateDateTime = @LastUpdate	
			FROM PerExtCMSMARegionRatebookByContract
			
			--Set reprice flag on for all active plans
			UPDATE SavedForecastSetup
			SET IsToReprice = 1,
				LastUpdateByID = @UserID,
			    LastUpdateDateTime = @LastUpdate
			WHERE IsLiveIndex = 1
	        
	        
		END
    COMMIT TRAN
   -------------------------------------------------------------------------------------------------------------------------- 
   
   --GRANT EXECUTE ON  [dbo].[spUpdateOrInsertPlanMOOP] TO [MAPDModel]
GO
