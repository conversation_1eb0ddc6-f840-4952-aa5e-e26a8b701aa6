SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: dbo.Trend_Reporting_spUpdateAll
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: 2020-12-21
--
-- DESCRIPTION: Execute trend reporting procedures that are used by the Trend Impact Report and Trend Assumption Report.
--				Apply a lock to the group of procedures to ensure that only one user can execute at one time.
--				This will help avoid data errors and deadlock issues when Market Support users are refreshing the reports.
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--				@PlanInfoID
--
--  Output : NONE
--
-- TABLES : Read :	Trend_Reporting_Log
--					W_FEM_AccessControl
--					
--          Write:	Trend_Reporting_Log
--                  
--
-- VIEWS: Read: NONE
--
-- FUNCTIONS: NONE
--
-- STORED PROCS: Executed:	Trend_Reporting_spCalcProjectedMembership
--							Trend_HistProcess_spCalcNotPlanLevel_PlanMappingFinal
--							Trend_HistProcess_spCalcIsPlanLevel
--							Trend_HistProcess_spCalcPlanTrends_RepCat
--							Trend_Reporting_spCalcCountyXWalkMembership
--							Trend_Reporting_spCalcImpactProjected
--							Trend_Reporting_spCalcImpactHistoric
--							Trend_Reporting_spCalcCompiledImpacts
--
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE															DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-12-21		1			Initial Version															Jake Lewis
-- 2021-03-24		2			Add PlanInfoID input parameter											Jake Lewis
--								Send email notification if an error is found
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_Reporting_spUpdateAll]
@LastUpdateByID CHAR(7)
,@PlanInfoID    VARCHAR(MAX) = NULL
AS
    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        BEGIN TRY

            DECLARE @tranCount INT = @@TranCount;

            BEGIN TRANSACTION transactionMain;

            -- Declare variables
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);
            DECLARE @lockIndicator INT;

            -- Set lock and prevent SPs within transaction from running simultaneously
            EXEC @lockIndicator = sys.sp_getapplock @Resource = 'Trend_Reporting_spUpdateAll'
                                                   ,@LockMode = 'Exclusive'
                                                   ,@LockOwner = 'Transaction'
                                                   ,@LockTimeout = 900000   -- 900,000 milliseconds = 15 minutes
                                                   ,@DbPrincipal = 'public';
            IF @lockIndicator IN (0)
                BEGIN
                    WAITFOR DELAY '00:00:02';
                    EXECUTE dbo.Trend_Reporting_spCalcProjectedMembership @LastUpdateByID;
                    EXECUTE dbo.Trend_HistProcess_spCalcNotPlanLevel_PlanMappingFinal @LastUpdateByID;
                    EXECUTE dbo.Trend_HistProcess_spCalcIsPlanLevel @LastUpdateByID
                                                                   ,@PlanInfoID;
                    EXECUTE dbo.Trend_HistProcess_spCalcPlanTrends_RepCat @LastUpdateByID;
                    EXECUTE dbo.Trend_Reporting_spCalcCountyXWalkMembership @LastUpdateByID;
                    EXECUTE dbo.Trend_Reporting_spCalcImpactProjected @LastUpdateByID
                                                                     ,@PlanInfoID;
                    EXECUTE dbo.Trend_Reporting_spCalcImpactHistoric @LastUpdateByID
                                                                    ,@PlanInfoID;
                    EXECUTE dbo.Trend_Reporting_spCalcCompiledImpacts @LastUpdateByID
                                                                     ,@PlanInfoID;
                END;
            ELSE
                BEGIN
                    SET @errorMsg = 'Unable to acquire lock on Trend_Reporting_spUpdateAll.';
                    RAISERROR (@errorMsg, 16, 1);
                END;
            COMMIT TRANSACTION transactionMain;
        END TRY
        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        DECLARE @LogDate DATETIME = GETDATE ();
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,@lockIndicator
               ,@errorMsg
               ,@LogDate - @startTime
               ,@LastUpdateByID
               ,@LogDate;

        -- Send email if errors exist
        DECLARE @recipients NVARCHAR(MAX);
        DECLARE @BodyOutput NVARCHAR(MAX);
        DECLARE @XML NVARCHAR(MAX);
        DECLARE @Subj VARCHAR(MAX);

        IF (SELECT  COUNT (*)
            FROM    dbo.Trend_Reporting_Log
            WHERE   ErrorMessage IS NOT NULL
                    AND (LockStatus <> 1 OR LockStatus IS NULL) -- LockStatus=1 is expected when the user can't obtain a lock. Don't want to send an email for this. 
                    AND CAST(FLOOR (CAST(LastUpdateDateTime AS FLOAT)) AS DATETIME) = CAST(FLOOR (
                                                                                           CAST(@LogDate AS FLOAT)) AS DATETIME) -- Don't sent emails for previous errors. 
                    AND LastUpdateByID = @LastUpdateByID) > 0
            BEGIN
                SET @XML = CAST((SELECT     (SELECT t.Source AS 'td' FOR XML PATH (''), TYPE)
                                           ,(SELECT ISNULL (CAST(t.LockStatus AS VARCHAR(5)), '') AS 'td'
                                            FOR XML PATH (''), TYPE)
                                           ,(SELECT ISNULL (t.ErrorMessage, '') AS 'td' FOR XML PATH (''), TYPE)
                                           ,(SELECT t.LastUpdateByID AS 'td' FOR XML PATH (''), TYPE)
                                           ,(SELECT CONCAT (w.LastName, ', ', w.FirstName) AS 'td'
                                            FOR XML PATH (''), TYPE)
                                           ,(SELECT t.LastUpdateDateTime AS 'td' FOR XML PATH (''), TYPE)
                                 FROM       dbo.Trend_Reporting_Log t WITH (NOLOCK)
                                 LEFT JOIN  dbo.W_FEM_AccessControl w WITH (NOLOCK)
                                        ON t.LastUpdateByID = w.UserID
                                FOR XML PATH ('tr'), ELEMENTS) AS NVARCHAR(MAX));
                SET @BodyOutput = N'<html><body><H3>
								Errors have been detected in the Trend Reporting process, which was initiated by '
                                  + OBJECT_NAME (@@ProcId)
                                  + N'.<br><br>
                                All errors have been logged in Trend_Reporting_Log.<br><br>
                            </H3>
                            <table border = 1 style=font-size:12px;>
                            <tr>
                                <th>Source</th><th>LockStatus</th><th>ErrorMessage</th><th>LastUpdateByID</th><th>LastUpdateByName</th><th>LastUpdateDateTime</th>
                            </tr>';
                SET @BodyOutput = @BodyOutput + @XML + N'</table></body></html>';

                --Pull email addresses from table
                SELECT  @recipients = COALESCE (@recipients + '; ', '') + [e-mail]
                FROM    (SELECT [Email] AS [e-mail]
                         FROM   dbo.W_FEM_AccessControl WITH (NOLOCK)
                         WHERE  NARTeam = 1
                         UNION
                         SELECT Email AS [e-mail]
                         FROM   dbo.W_FEM_AccessControl WITH (NOLOCK)
                         WHERE  UserID = @LastUpdateByID) a;

                --Send the email
                SET @Subj = (SELECT DB_NAME () + ': Errors in the Trend Reporting process.');
                EXEC msdb.dbo.sp_send_dbmail @recipients = @recipients
                                            ,@body = @BodyOutput
                                            ,@subject = @Subj
                                            ,@body_format = 'HTML'
                                            ,@importance = 'High';
            END;
    END;
GO
