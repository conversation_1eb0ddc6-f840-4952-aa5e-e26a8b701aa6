SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_NormProcess_spCalcIsPlanLevelCMSReimb
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: Mar-14-2020
--
-- DESCRIPTION: This stored procedure calculates historical CMS Reimbursement trends for the normalized process
--
--              The following procedures use similar logic to this one, so any updates made here will most likely apply to:
--                  Trend_NormProcess_spCalcIsPlanLevelPopulation
--                  Trend_NormProcess_spCalcIsPlanLevelInducedUtilization
--                  Trend_NormProcess_spCalcIsPlanLevelOutlierClaims
--                  Trend_NormProcess_spCalcIsPlanLevelContractual
--       
-- PARAMETERS:
--  Input  : @LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntPlanYear
--                  LkpProjectionVersion
--                  Trend_CalcHistoricMembership
--                  Trend_CalcHistoricCostAndUse
--                  LkpIntBenefitCategory
--                  Trend_SavedRelativityCMSReimb
--					Trend_NormProcess_IsIncludeInTrendSnapshot
--
--          Write:  Trend_NormProcess_CalcIsPlanLevel_CMSReimb
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS: Trend_fnSafeDivide
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION			CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-01-27			1			Initial Version                                     						Andy Blink
-- 2021-05-07			2			Referece IsIncludeInTrendSnapshot for normalized audit fix					Jake Lewis
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_NormProcess_spCalcIsPlanLevelCMSReimb]
@LastUpdateByID CHAR(13)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        -- Declare and set variables 
        DECLARE @LastUpdateDateTime DATETIME = GETDATE ();
        DECLARE @CurrentYear SMALLINT = (SELECT PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);
        DECLARE @MaxCurrentYearQuarter TINYINT = (SELECT    LastCurrentYearQuarter
                                                  FROM      dbo.LkpProjectionVersion
                                                  WHERE     IsLiveProjection = 1);
        DECLARE @OldestHistoricYear INT = CASE WHEN @MaxCurrentYearQuarter = 0 -- Bid/FLP projection (i.e. no data in current year)
        THEN                                       @CurrentYear - 5
                                               ELSE @CurrentYear - 4 -- Non-Bid season projection (i.e. have some data in current year)
                                          END;
        DECLARE @Component VARCHAR(50) = 'CMS Reimbursement';
        DECLARE @PlanTypeGranularity VARCHAR(250) = 'Plan';
        DECLARE @NationwideRegion VARCHAR(50) = 'Nationwide';

        -- Benefit categories 
        IF (SELECT  OBJECT_ID ('tempdb..#BenCat')) IS NOT NULL DROP TABLE #BenCat;
        SELECT  BenefitCategoryID
               ,ReportingCategory
        INTO    #BenCat
        FROM    dbo.LkpIntBenefitCategory;

        --Bring in historic allowed data into a temp table, replace BenefitCategoryID with ReportingCategory, and re-summarize
        IF (SELECT  OBJECT_ID ('tempdb..#Trend_CalcHistoricCostandUse')) IS NOT NULL
            DROP TABLE #Trend_CalcHistoricCostandUse;
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,b.ReportingCategory
                   ,ISNULL (SUM (a.Allowed), 0) AS Allowed
        INTO        #Trend_CalcHistoricCostandUse
        FROM        dbo.Trend_CalcHistoricCostAndUse a
        LEFT JOIN   #BenCat b
               ON a.BenefitCategoryID = b.BenefitCategoryID
        LEFT JOIN   dbo.Trend_NormProcess_IsIncludeInTrendSnapshot c
               ON c.PlanInfoID = a.PlanInfoID
        WHERE       c.IsIncludeInTrend = 1
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,b.ReportingCategory;

        --Bring in the CMS Reimbursement import into a temp table, add ActuarialRegion, and multiply relativities by allowed
        IF (SELECT  OBJECT_ID ('tempdb..#SavedCMSReimb')) IS NOT NULL
            DROP TABLE #SavedCMSReimb;
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,b.Region AS ActuarialRegion
                   ,a.ReportingCategory
                   ,ISNULL (a.CostRelativity * c.Allowed, 0) AS CostRelativity
                   ,ISNULL (a.UseRelativity * c.Allowed, 0) AS UseRelativity
                   ,ISNULL (c.Allowed, 0) AS Allowed
        INTO        #SavedCMSReimb
        FROM        dbo.Trend_SavedRelativityCMSReimb a
       INNER JOIN   dbo.vwPlanInfo b
               ON b.CPS = a.CPS
                  AND   b.PlanYear = a.PlanYearID
        LEFT JOIN   #Trend_CalcHistoricCostandUse c
               ON c.CPS = a.CPS
                  AND   c.PlanYearID = a.PlanYearID
                  AND   c.QuarterID = a.QuarterID
                  AND   c.ReportingCategory = a.ReportingCategory
        LEFT JOIN   dbo.Trend_NormProcess_IsIncludeInTrendSnapshot d
               ON d.CPS = a.CPS
                  AND   d.PlanYear = a.PlanYearID
        WHERE       b.IsHidden = 0
                    AND b.IsOffMAModel = 'No'
                    AND b.Region NOT IN ('Unmapped')
                    AND d.IsIncludeInTrend = 1;

        --Summarize the previous step to remove CPS and then calculate the rolled up relativities
        IF (SELECT  OBJECT_ID ('tempdb..#SavedCMSReimb_Sum')) IS NOT NULL
            DROP TABLE #SavedCMSReimb_Sum;
        SELECT      PlanYearID
                   ,QuarterID
                   ,ActuarialRegion
                   ,ReportingCategory
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (CostRelativity), SUM (Allowed), 1), 1) AS CostRelativity
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (UseRelativity), SUM (Allowed), 1), 1) AS UseRelativity
                   ,ISNULL (SUM (Allowed), 0) AS Allowed
        INTO    #SavedCMSReimb_Sum
        FROM        #SavedCMSReimb
        GROUP BY    PlanYearID
                   ,QuarterID
                   ,ActuarialRegion
                   ,ReportingCategory
        UNION ALL
        --Summarize the previous step to the Nationwide level and insert "Nationwide" as the ActuarialRegion
        SELECT      PlanYearID
                   ,QuarterID
                   ,@NationwideRegion AS ActuarialRegion
                   ,ReportingCategory
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (CostRelativity), SUM (Allowed), 1), 1) AS CostRelativity
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (UseRelativity), SUM (Allowed), 1), 1) AS UseRelativity
                   ,ISNULL (SUM (Allowed), 0) AS Allowed
        FROM        #SavedCMSReimb
        GROUP BY    PlanYearID
                   ,QuarterID
                   ,ReportingCategory;

        --Transpose the plan year field to be separate columns so trends can be calculated year over year in the next step
        IF (SELECT  OBJECT_ID ('tempdb..#PrepForTrendCalc')) IS NOT NULL
            DROP TABLE #PrepForTrendCalc;
        SELECT      a.PlanYearID
                   ,a.QuarterID
                   ,a.ActuarialRegion
                   ,a.ReportingCategory
                   ,b.CostRelativity AS CostRelativityPY
                   ,b.UseRelativity AS UseRelativityPY
                   ,a.CostRelativity
                   ,a.UseRelativity
        INTO        #PrepForTrendCalc
        FROM        #SavedCMSReimb_Sum a
        LEFT JOIN   #SavedCMSReimb_Sum b
               ON (b.PlanYearID + 1) = a.PlanYearID
                  AND   b.QuarterID = a.QuarterID
                  AND   b.ActuarialRegion = a.ActuarialRegion
                  AND   b.ReportingCategory = a.ReportingCategory
        WHERE       a.PlanYearID > @OldestHistoricYear;

        --Calculate the year over year trends from the relativity factors
        IF (SELECT  OBJECT_ID ('tempdb..#TrendCalc')) IS NOT NULL
            DROP TABLE #TrendCalc;
        SELECT  PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,ISNULL (dbo.Trend_fnSafeDivide (CostRelativity, CostRelativityPY, 1) - 1, 1) AS CostAdjustment
               ,ISNULL (dbo.Trend_fnSafeDivide (UseRelativity, UseRelativityPY, 1) - 1, 1) AS UseAdjustment
        INTO    #TrendCalc
        FROM    #PrepForTrendCalc;

        --Delete and insert trends into the final output table
        DELETE  FROM dbo.Trend_NormProcess_CalcIsPlanlevel_CMSReimb WHERE   1 = 1;

        INSERT INTO dbo.Trend_NormProcess_CalcIsPlanlevel_CMSReimb
            (ComponentVersionID
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,PlanYearID
            ,QuarterID
            ,ActuarialRegion
            ,ReportingCategory
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        --Non-current year historic trends
        SELECT  NULL AS ComponentVersionID
               ,@Component AS Component
               ,@PlanTypeGranularity AS PlanTypeGranularity
               ,NULL AS PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #TrendCalc
        WHERE   PlanYearID < @CurrentYear
        UNION ALL
        --Current year historic trends
        SELECT  NULL AS ComponentVersionID
               ,@Component AS Component
               ,@PlanTypeGranularity AS PlanTypeGranularity
               ,NULL AS PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #TrendCalc
        WHERE   PlanYearID = @CurrentYear
                AND QuarterID <= @MaxCurrentYearQuarter;

    END;
GO


