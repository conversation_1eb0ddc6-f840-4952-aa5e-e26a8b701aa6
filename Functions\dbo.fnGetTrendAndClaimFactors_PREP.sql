SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnGetTrendAndClaimFactors_PREP]  ******/

-- FUNCTION NAME: fnGetClaimFactors
--
-- AUTHOR: <PERSON> Gao
--
-- CREATED DATE: 3/26/3013
-- HEADER UPDATED: 3/26/3013
--
-- DESCRIPTION: Multiplies all ClaimFactors associated with the specified Benefitcategory
--  that are stored in SavedBenefitLevelClaimFactors(New MACTAPT data).  These values originate from
--  The returned Trend and claim factors needed to displayed in Actuarial Summary tab
--
-- PARAMETERS: 
--	Input:
--      @ForecastID
--
--  Output:
--
-- TABLES: 
--	Read:
--      LkpExtCMSWorksheet1Mapping
--      LkpIntBenefitCategory
--      LkpIntProjectionYear
--      PerIntClaimFactorTypeTest
--      SavedClaimFactorBenefitLevel
--      
--      SavedPlanDetail
--
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION	    CHANGES MADE						                                DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------
-- 2013-Mar-26		1			Initial	Version														Tim Gao
-- 2014-Jan-09		2			Changing Table Name													Mike Deren
-- 2014-Jan-17		3			Updating Trend and Claim Factor columns								Mike Deren
-- 2014-Mar-10		4			Modified to include Sequestration Factors							Mike Deren
-- 2014-Apr-24		5			Moved Sequestration factors											Mike Deren
-- 2015-May-08      6           Added @SavedBenefitLevel,@SavedPlanDetail variable table and nolock for fetched table  Manisha Tyagi 
-- 2015-Nov-17		7			Added 35, 36 (Target MER) to claim factors.							Mark Freel
-- 2017-Oct-12		8			Removed partial SQS factor, no longer needed						Chris Fleming
-- 2018-Oct-15      9           Removed Target MER from claim factors used in PREP                  Clay Cannon		
-- 2020-Apr-09		10			Modified temp table SavedBenefitLevel to allow trend > 10			Alex Beruscha	
-- 2021-Nov-11		11			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's	        Franklin Fu
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetTrendAndClaimFactors_PREP]
(
    @ForecastID INT
)
RETURNS @Results TABLE(
    ForecastID INT,
    ClaimForecastID INT,
    MARatingOptionID INT,
    IsInNetwork INT,
    BenefitCategoryID INT,
	E2CTrendFactor DECIMAL(24, 15),
    C2BTrendFactor DECIMAL(24, 15),
    E2CClaimFactor DECIMAL(24, 15),
    C2BClaimFactor DECIMAL(24, 15),
    TrendAndClaimFactor DECIMAL(24, 15)
    )
AS
BEGIN

DECLARE @SavedBenefitLevel TABLE 
			([ClaimForecastID] [int],			
			[ClaimFactorTypeID] [int],
			[ProjectionYearID] [int],
			[IsInNetwork] [int],
			[BenefitCategoryID] [int],
			[ClaimFactor][decimal](8,6))
	
	INSERT @SavedBenefitLevel
    SELECT DISTINCT bl.ClaimForecastID, bl.ClaimFactorTypeID, bl.ProjectionYearID, bl.IsInNetwork, bl.BenefitCategoryID, bl.ClaimFactor
        FROM SavedBenefitLevelClaimFactors (nolock) bl
        INNER JOIN SavedPlanDetail (nolock) spd
            ON bl.ClaimForecastID = spd.ClaimForecastID
        WHERE spd.ForecastID = @ForecastID
 
 
 DECLARE @SavedPlanDetail TABLE(
    ForecastID INT,
    ClaimForecastID INT,
    MARatingOptionID INT,
    IsInNetwork INT,
    BenefitCategoryID INT,
	E2CTrendFactor DECIMAL(24, 15),
    C2BTrendFactor DECIMAL(24, 15),
    E2CClaimFactor DECIMAL(24, 15),
    C2BClaimFactor DECIMAL(24, 15),
    TrendAndClaimFactor DECIMAL(24, 15)
    )  
	
INSERT @SavedPlanDetail     
SELECT 
        spd.ForecastID,
        spd.ClaimForecastID,
        spd.MARatingOptionID,
        cfd.IsInNetwork,
        ben.BenefitCategoryID,
		E2CTrendFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
		    EXP(SUM(LOG(NULLIF(
		                CASE WHEN py.ProjectionYearID =1 AND ctt.ClaimFactorTypeID IN (1,7,8,10,11,12,18,24,25,27,28,29)
									THEN  cfd.ClaimFactor
		                    ELSE 1
		                END
			,0)))) END,
		C2BTrendFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
		    EXP(SUM(LOG(NULLIF(
		                CASE WHEN py.ProjectionYearID =2 AND ctt.ClaimFactorTypeID IN (1,7,8,10,11,12,18,24,25,27,28,29)
		                     THEN cfd.ClaimFactor
		                    ELSE 1
		                END
			,0)))) END,
		E2CClaimFactor = 
		   sqs.ProjectedSQS * CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
		   EXP(SUM(LOG(NULLIF(
		                CASE WHEN py.ProjectionYearID = 1 AND ctt.ClaimFactorTypeID IN (2,3,4,5,6,9,13,14,15,16,17,19,20,21,22,23,26,30,31,32,33,34)
		                       THEN cfd.ClaimFactor
		                    ELSE 1
		                END
			,0)))) END,
		C2BClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 ELSE
		    EXP(SUM(LOG(NULLIF(
		                CASE WHEN py.ProjectionYearID = 2 AND ctt.ClaimFactorTypeID IN (2,3,4,5,6,9,13,14,15,16,17,19,20,21,22,23,26,30,31,32,33,34)
		                     THEN cfd.ClaimFactor
		                    ELSE 1
		                END
			,0)))) END,
		TrendAndClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 
		ELSE EXP(SUM(LOG(NULLIF(cfd.ClaimFactor,0)))) END 
        --    --There is no aggregate product function in SQL Server.  This is a workaround.
               
    FROM SavedPlanDetail (nolock) spd
    INNER JOIN 
       @SavedBenefitLevel cfd
        ON spd.ClaimForecastID = cfd.ClaimForecastID
    INNER JOIN LkpIntProjectionYear (nolock) py
        ON py.ProjectionYearID = cfd.ProjectionYearID
    INNER JOIN PerIntClaimFactorTypeTest (nolock) ctt
        ON ctt.ClaimFactorTypeID = cfd.ClaimFactorTypeID
    INNER JOIN LkpExtCMSWorksheet1Mapping (nolock) ws1
        ON ctt.ColumnIDBPT = ws1.ColumnID
    INNER JOIN LkpIntBenefitCategory (nolock) ben
        ON cfd.BenefitCategoryID = ben.BenefitCategoryID
    INNER JOIN dbo.CalcSQSFactors (nolock) SQS
		ON SQS.ForecastID = spd.ForecastID
		AND SQS.MARatingOptionID = spd.MARatingOptionID
		AND SQS.BenefitCategoryID = ben.BenefitCategoryID		
    WHERE spd.ForecastID = @ForecastID 
	GROUP BY 
        spd.ClaimForecastID,
        spd.MARatingOptionID,
        cfd.IsInNetwork,
        ben.BenefitCategoryID,
        spd.ForecastID,
        SQS.ProjectedSQS 
INSERT INTO @Results
 	SELECT * FROM @SavedPlanDetail
		WHERE MARatingOptionID = 1		
        
	UNION
	SELECT * FROM @SavedPlanDetail
		WHERE MARatingOptionID = 2
       
    RETURN
END
GO
