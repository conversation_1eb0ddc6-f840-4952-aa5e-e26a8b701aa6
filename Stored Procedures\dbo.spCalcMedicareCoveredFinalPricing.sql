SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-------------------------------------------------------------------------------------------------------------------------------------------
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcMedicareCoveredFinalPricing
--
-- CREATOR: <PERSON> Cofie
--
-- CREATED DATE: Feb-03-2007
--
-- DESCRIPTION: Stored Procedure responsible for Updating CalcMedicareCoveredFinalPricing  table.
-- PARAMETERS: 
--      Input  : 
--        @ForecastID int
--        @UserID CHAR(7)
--      Output : NONE
--
-- TABLES : 
--      Read : CalcActEquivByBenefitCategory
--	    Write: CalcMedicareCoveredFinalPricing	
--
-- VIEWS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                                            DEVELOPER
-- --------------------------------------------------------------------------------------------------------------------------------------------
-- Feb-03-2007      1           Initial Version                                                                         Christian Cofie
-- Apr-29-2008		2           Added significant digits to Net calc                                                    Brian Lake
-- 2008-Aug-26		3           Added @UserID to the list of parameters and replaced all                                Shannon Boykin
--	                                occurrences of SUSER_SNAME with @UserID.
-- 2009-Mar-19      4           Removed Transactions                                                                    Brian Lake
-- 2009-Mar-20      5           Removed delete statement                                                                Brian Lake
-- 2009-Apr-21      6           Added call to CalcActEquivWeightedByBenefitCategory                                     Brian Lake
-- 2009-Apr-22      7           Added a case statement to calc CoveredActEquivNet to remove cost sharing for dual       Sandy Ellis
-- 2010-Sep-28      8           Revised for 2012 database                                                               Michael Siekerka
-- 2010-Oct-06      9           Removed reference to IsWeighted as it corresponds to DETypeID                           Michael Siekerka
-- 2011-Jun-02		10			Replaced LkpIntPlanYear with dbo.fnGetBidYear()											Bobby Jaegers
-- 2011-Jun-14		11			Changed @PlanYearID to return SMALLINT instead of INT									Bobby Jaegers
-- 2019-oct-25		12			Removed 'HUMAD\' from UserID															Chhavi Sinha
-- 2022-Apr-07      13			Replaced input @PlanIndex with @ForecastID; replaced reference from 
--								PlanIndex to ForecastIDtable for tables CalcActEquivByBenefitCategory and
--								CalcMedicareCoveredFinalPricing															Aleksandar Dimitrijevic
-- 2022-Nov-23      14          Added @Xvariables, NOLOCK, Create #CalcMedicareCoveredFinalPricing,					    Phani Adduri
--								Release temp table.
-- --------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcMedicareCoveredFinalPricing] 
(
    @ForecastID INT,
    @UserID CHAR(7)
) AS

    SET NOCOUNT ON;

	DECLARE @XForecastID INT = @ForecastID,
        @XUserID CHAR(7) = @UserID

	DECLARE @LastDateTime DATETIME,
            @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear()
    SET @LastDateTime = GETDATE()

    DELETE FROM dbo.CalcMedicareCoveredFinalPricing
    WHERE ForecastID = @XForecastID    

IF OBJECT_ID('tempdb.dbo.#CalcMedicareCoveredFinalPricing') IS NOT NULL   
DROP TABLE #CalcMedicareCoveredFinalPricing

	CREATE TABLE #CalcMedicareCoveredFinalPricing
	(
		PlanYearID INT,
		ForecastID INT,
		DualEligibleTypeID TINYINT,
		Allowed DECIMAL(20,12),
		CostShare DECIMAL(20,12),
		Net DECIMAL(20,12),
		CoveredActEquivNet DECIMAL(20,12),
		AddedBenefitNet DECIMAL(14,6),
		CostShareReduction DECIMAL(14,6),
        SupplementalBenefitTotal DECIMAL(14,6),
        LastUpdateByID CHAR(7),
        LastUpdateDateTime DATETIME
	)

    --Append new stuff												
    INSERT INTO #CalcMedicareCoveredFinalPricing (PlanYearID, ForecastID, DualEligibleTypeID, Allowed, CostShare,
	Net, CoveredActEquivNet, AddedBenefitNet, CostShareReduction, SupplementalBenefitTotal, LastUpdateByID, LastUpdateDateTime)
    SELECT  -- DualElig Type 0 & 1, non-weighted by dual/nondual membership
        @PlanYearID, 
        c.ForecastID,
        c.DualEligibleTypeID,
        Allowed=
            SUM(
            CONVERT(DECIMAL(20,12), ISNULL(c.Allowed,0)) 
            * CONVERT(DECIMAL(20,12),PercentCoveredAllowed)
            ),
        CostShare=
            SUM(
            CONVERT(DECIMAL(20,12),ISNULL(c.CostShare,0)) 
            * CONVERT(DECIMAL(20,12),PercentCoveredCostShare)
            ),
        Net=
            SUM(
            CONVERT(DECIMAL(20,12),ISNULL(dbo.fnSignificantDigits(c.Net,15),0))
            ),
        CoveredActEquivNet=
            CASE WHEN DualEligibleTypeID = 1
                THEN
                    SUM(
                    CONVERT(DECIMAL(20,12), ISNULL(c.Allowed,0)) 
                    * CONVERT(DECIMAL(20,12),PercentCoveredAllowed)
                    )
                ELSE
                    SUM(ISNULL(c.CoveredActEquivNet,0))
            END,
        AddedBenefitNet= SUM(ISNULL(c.AddedBenefitNet,0)),
        CostShareReduction= SUM(ISNULL(c.CostShareReduction,0)),
        SupplementalBenefitTotal= SUM(ISNULL(c.SupplementalBenefitTotal,0)),
        @XUserID,
        @LastDateTime
    FROM dbo.CalcActEquivByBenefitCategory c WITH(NOLOCK)
    WHERE c.ForecastID = @XForecastID
        AND c.DualEligibleTypeID <> 2 --these calcs are just for dual and non-dual, blended is handled below
    GROUP BY
        c.ForecastID,
        c.DualEligibleTypeID

    UNION         

    SELECT -- DualElig Type 2, weighted by dual/nondual membership
        @PlanYearID, 
        c.ForecastID,
        c.DualEligibleTypeID,
        Allowed =
            SUM(
                CONVERT(DECIMAL(20,12), ISNULL(c.Allowed,0)) 
                * CONVERT(DECIMAL(20,12),PercentCoveredAllowed)
            ),
        CostShare =
            SUM(
                CONVERT(DECIMAL(20,12),ISNULL(c.CostShare,0)) 
                * CONVERT(DECIMAL(20,12),PercentCoveredCostShare)
            ),
        Net =
            SUM(
                CONVERT(DECIMAL(20,12),ISNULL(dbo.fnSignificantDigits(c.Net,15),0))
            ),
        CoveredActEquivNet = SUM(ISNULL(c.CoveredActEquivNet,0)),
        AddedBenefitNet = SUM(ISNULL(c.AddedBenefitNet,0)),
        CostShareReduction = SUM(ISNULL(c.CostShareReduction,0)),
        SupplementalBenefitTotal = SUM(ISNULL(c.SupplementalBenefitTotal,0)),
        @XUserID,
        @LastDateTime
    FROM dbo.CalcActEquivByBenefitCategory c WITH(NOLOCK)
    WHERE c.ForecastID = @XForecastID
        AND c.DualEligibleTypeID = 2 --Weighted portion
    GROUP BY
        c.ForecastID,
        c.DualEligibleTypeID;

	INSERT INTO dbo.CalcMedicareCoveredFinalPricing(
	PlanYearID, ForecastID, DualEligibleTypeID, Allowed, CostShare,
	Net, CoveredActEquivNet, AddedBenefitNet, CostShareReduction, SupplementalBenefitTotal, LastUpdateByID, LastUpdateDateTime
	)
	SELECT PlanYearID, ForecastID, DualEligibleTypeID, Allowed, CostShare,
	Net, CoveredActEquivNet, AddedBenefitNet, CostShareReduction, SupplementalBenefitTotal, LastUpdateByID, LastUpdateDateTime
	FROM #CalcMedicareCoveredFinalPricing


IF OBJECT_ID('tempdb.dbo.#CalcMedicareCoveredFinalPricing') IS NOT NULL   
DROP TABLE #CalcMedicareCoveredFinalPricing