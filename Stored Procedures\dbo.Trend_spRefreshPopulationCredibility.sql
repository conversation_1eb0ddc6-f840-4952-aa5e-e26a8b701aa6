SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	Trend_spRefreshPopulationCredibility
--
-- CREATOR:			Andy Blink
--
-- CREATED DATE:	2018-DEC-18
--
-- DESCRIPTION:		Calculates population relativities at various granularities used for 
--					credibility in the projected population process.
--		
-- PARAMETERS:
--  Input  :		@LastUpdateByID
--					
--  Output :		NONE
--
-- TABLES : 
--	Read :			LkpIntPlanYear
--					LkpIntBenefitCategory
--					Trend_PerPopulationCredibleMemberMonths
--					Trend_CalcPopulationHistorical
--					Trend_CalcPopulationCurrentYear
--
--  Write:			Trend_CalcPopulationCredibility
--
-- VIEWS: 
--	Read:			vwPlanInfo
--
-- FUNCTIONS:		Trend_fnSafeDivide
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																					DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2018-AUG-09 		0		    Initial Version															                        Andy Blink						 
-- 2018-NOV-15		1			Add segment version allocation (SVA) factor								                        Andy Blink
-- 2019-JAN-31		2			Overhaul crosswalk logic to fix segmented plan issues					                        Andy Blink
--									Update #SAM table to keep termed counties in BY/CY
--									Updates to reduce runtime
-- 2019-FEB-18		3			Split Reg-Prod and above cuts out into their own credibility sp			                        Andy Blink
--									Remove crosswalk logic
-- 2019-OCT-03		4			Remove Quarter from all tables to annualize								                        Andy Blink
--									Add logic for prior CY credibility tables
-- 2020-MAR-09      5           Adding to MAAModels for Trend Simplifications, and updating column names and table references   Andy Blink
-- 2020-JUN-24      6           Create temporary planinfo view to add SNPtype to product field                                  Michael Manes
-- 2022-AUG-08      7           Incorporating 'MA Only' as a product type, updating methodology to treat base and retained      Tanvi Khanna
--									current year independently, and restructuring sp to write to a single table
-- 2023-MAR-28      8			Removing P2P impact from base year experience to better account for sickening trend				Stephen Stempky
-- 2023-DEC-12		9			Changed vwplaninfo product logic from case statement to directly use PopRSCredibilityProduct
--								field																							Stephen Stempky
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_spRefreshPopulationCredibility]

@LastUpdateByID CHAR(7)
AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        ----------------------------------------------------
        -- 0. Declare / set variables and pull parameters --
        ----------------------------------------------------

        DECLARE @BaseYear AS SMALLINT;
        DECLARE @CurrentYear AS SMALLINT;
        DECLARE @CredibleMemberMonths AS REAL;

        --Base year
        SET @BaseYear = (SELECT PlanYearID
                         FROM   dbo.LkpIntPlanYear WITH (NOLOCK)
                         WHERE  IsExperienceYear = 1);

        --Current year
        SET @CurrentYear = (SELECT  PlanYearID
                            FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                            WHERE   IsCurrentYear = 1);


        -- Credible Member Month Threshold
        SET @CredibleMemberMonths = (SELECT CredibleMemberMonths
                                     FROM   dbo.Trend_PerPopulationCredibleMemberMonths WITH (NOLOCK));

        --Create temp planinfo table to adjust product granularity
        IF (SELECT  OBJECT_ID ('tempdb..#vwPlanInfo')) IS NOT NULL DROP TABLE #vwPlanInfo;

        SELECT  CPS
               ,PlanYear
               ,Region
               ,PopRSCredibilityProduct AS Product
        INTO    #vwPlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   Region <> 'Unmapped'
                AND IsOffMAModel = 'No'
                AND IsHidden = 0
                AND PlanYear IN (@BaseYear, @CurrentYear);

        --Barc Class
        IF (SELECT  OBJECT_ID ('tempdb..#Class')) IS NOT NULL DROP TABLE #Class;
        SELECT  'A' AS BarcClass
        INTO    #Class UNION ALL
        SELECT  'B'
        UNION ALL
        SELECT  'C'
        UNION ALL
        SELECT  'D';

        --DE
        IF (SELECT  OBJECT_ID ('tempdb..#DE')) IS NOT NULL DROP TABLE #DE;
        SELECT  1 AS IsDEPound
        INTO    #DE UNION ALL
        SELECT  0;

        --RepCat
        IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
        SELECT  DISTINCT
                ReportingCategory
        INTO    #RepCat
        FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
        WHERE   ReportingCategory = 'Ancil';    --Population does not vary by reporting category 

        --Create shell
        IF (SELECT  OBJECT_ID ('tempdb..#Shell')) IS NOT NULL DROP TABLE #Shell;
        SELECT      DISTINCT
                    vpi.Region
                   ,vpi.Product
                   ,c.BarcClass
                   ,d.IsDEPound
        INTO        #Shell
        FROM        #vwPlanInfo vpi
       CROSS JOIN   #Class c
       CROSS JOIN   #DE d;

	   --P2P with region product
        IF (SELECT  OBJECT_ID ('tempdb..#P2PwithRegionProduct')) IS NOT NULL DROP TABLE #P2PwithRegionProduct;
        SELECT      
                    vpi.Region
                   ,vpi.Product
                   ,p2p.BarcClass
				   ,p2p.IsDEPound
				   ,SUM(p2p.P2PIn_MM) + SUM(p2p.P2POut_MM) AS P2PNet_MM
				   ,SUM(p2p.P2PIn_Rel) + SUM(p2p.P2POut_Rel) AS P2PNet_Rel
        INTO        #P2PwithRegionProduct
        FROM        dbo.Trend_PerPopulationP2PImpact p2p
		LEFT JOIN #vwPlanInfo vpi
		ON vpi.CPS = p2p.CPS
		AND vpi.PlanYear = p2p.PlanYearID
		GROUP BY vpi.Region
                   ,vpi.Product
                   ,p2p.BarcClass
				   ,p2p.IsDEPound;

        ----------------------------------------------------
        -- 1. Pull base and current year data --------------
        ----------------------------------------------------

        --Create temp base year factors table
        IF (SELECT  OBJECT_ID ('tempdb..#BaseYearFactors')) IS NOT NULL DROP TABLE #BaseYearFactors;

        SELECT      h.IsDEPound
                   ,h.BarcClass
                   ,vpi.Region
                   ,vpi.Product
                   ,CASE WHEN SUM (ISNULL (h.BY_MM, 0)) + ISNULL (AVG(p2p.P2PNet_MM), 0) < 0 THEN 0 ELSE 
				   SUM (ISNULL (h.BY_MM, 0)) + ISNULL (AVG(p2p.P2PNet_MM), 0) END --Error handling
				   AS BY_MM
                   ,CASE WHEN SUM (ISNULL (h.BY_UseFactor, 0)) + ISNULL (AVG(p2p.P2PNet_Rel), 0)  < 0 THEN 0 ELSE 
				   SUM (ISNULL (h.BY_UseFactor, 0)) + ISNULL (AVG(p2p.P2PNet_Rel), 0) END --Error handling
				   AS BY_UseFactor
        INTO        #BaseYearFactors
        FROM        dbo.Trend_CalcPopulationHistorical h WITH (NOLOCK)
        LEFT JOIN   #vwPlanInfo vpi
               ON h.CPS = vpi.CPS
                  AND   vpi.PlanYear = h.PlanYearID
		LEFT JOIN	#P2PwithRegionProduct p2p
			   ON p2p.Region = vpi.Region
			   AND p2p.Product = vpi.Product
			   AND p2p.BarcClass = h.BarcClass
			   AND p2p.IsDEPound = h.IsDEPound
       INNER JOIN   #RepCat r
               ON r.ReportingCategory = h.ReportingCategory
        WHERE       h.PlanYearID = @BaseYear
        GROUP BY    h.IsDEPound
                   ,h.BarcClass
                   ,h.ReportingCategory
                   ,vpi.Region
                   ,vpi.Product;

        --Create temp current year factors table
        IF (SELECT  OBJECT_ID ('tempdb..#CurrentYearFactors')) IS NOT NULL DROP TABLE #CurrentYearFactors;

        SELECT      c.IsDEPound
                   ,c.BarcClass
                   ,vpi.Region
                   ,vpi.Product
                   ,SUM (ISNULL (c.CY_MM, 0)) AS CY_MM
                   ,SUM (ISNULL (c.CY_UseFactor, 0)) AS CY_UseFactor
        INTO        #CurrentYearFactors
        FROM        dbo.Trend_CalcPopulationCurrentYear c WITH (NOLOCK)
        LEFT JOIN   #vwPlanInfo vpi
               ON c.CPS = vpi.CPS
                  AND   vpi.PlanYear = c.PlanYearID
       INNER JOIN   #RepCat r
               ON r.ReportingCategory = c.ReportingCategory
        GROUP BY    c.IsDEPound
                   ,c.BarcClass
                   ,c.ReportingCategory
                   ,vpi.Region
                   ,vpi.Product;

        --Create temp prior current year factors table
        IF (SELECT  OBJECT_ID ('tempdb..#PriorCYFactors')) IS NOT NULL DROP TABLE #PriorCYFactors;

        SELECT      c.IsDEPound
                   ,c.PriorYearBarcClass AS BarcClass
                   ,vpi.Region
                   ,vpi.Product
                   ,SUM (ISNULL (c.CY_MM, 0)) AS PriorCY_MM
                   ,SUM (ISNULL (c.CY_UseFactor, 0)) AS PriorCY_UseFactor
        INTO        #PriorCYFactors
        FROM        dbo.Trend_CalcPopulationCurrentYear c WITH (NOLOCK)
        LEFT JOIN   #vwPlanInfo vpi
               ON c.CPS = vpi.CPS
                  AND   vpi.PlanYear = c.PlanYearID
       INNER JOIN   #RepCat r
               ON r.ReportingCategory = c.ReportingCategory
        WHERE       c.BarcClass = 'A'
        GROUP BY    c.IsDEPound
                   ,c.PriorYearBarcClass
                   ,c.ReportingCategory
                   ,vpi.Region
                   ,vpi.Product;

        ----------------------------------------------------
        -- 2. Perform calculations and insert final data ---
        ----------------------------------------------------
        --Delete results
        DELETE  FROM dbo.Trend_CalcPopulationCredibility WHERE  1 = 1;
        --Insert results
        INSERT INTO dbo.Trend_CalcPopulationCredibility
            (IsDEPound
            ,BarcClass
            ,Granularity
            ,Region
            ,Product
            ,IsRetained
            ,IsNationwide
            ,PlanYearID
            ,Credibility
            ,UseRelativity
            ,LastUpdateByID
            ,LastUpdateDateTime)

        ----------------------------
        --2a. Region, Product, DE---
        ----------------------------

        --2a i. Base	
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Region, Product' AS Granularity
                   ,s.Region
                   ,s.Product
                   ,0 AS IsRetained
                   ,0 AS IsNationwide
                   ,@BaseYear AS PlanYearID
                   ,CASE WHEN SUM (b.BY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (b.BY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (b.BY_UseFactor) AS FLOAT), CAST(SUM (b.BY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #BaseYearFactors b
               ON s.IsDEPound = b.IsDEPound
                  AND   s.BarcClass = b.BarcClass
                  AND   s.Region = b.Region
                  AND   s.Product = b.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass
                   ,s.Region
                   ,s.Product

        --2a ii. Retained Current
        UNION ALL
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Region, Product' AS Granularity
                   ,s.Region
                   ,s.Product
                   ,1 AS IsRetained
                   ,0 AS IsNationwide
                   ,@CurrentYear AS PlanYearID
                   ,CASE WHEN SUM (p.PriorCY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE
                             CAST(SQRT (CAST(CAST(SUM (p.PriorCY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (p.PriorCY_UseFactor) AS FLOAT), CAST(SUM (p.PriorCY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #PriorCYFactors p
               ON s.IsDEPound = p.IsDEPound
                  AND   s.BarcClass = p.BarcClass
                  AND   s.Region = p.Region
                  AND   s.Product = p.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass
                   ,s.Region
                   ,s.Product
        UNION ALL
        --2a iii. Current
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Region, Product' AS Granularity
                   ,s.Region
                   ,s.Product
                   ,0 AS IsRetained
                   ,0 AS IsNationwide
                   ,@CurrentYear AS PlanYearID
                   ,CASE WHEN SUM (c.CY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (c.CY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (c.CY_UseFactor) AS FLOAT), CAST(SUM (c.CY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #CurrentYearFactors c
               ON s.IsDEPound = c.IsDEPound
                  AND   s.BarcClass = c.BarcClass
                  AND   s.Region = c.Region
                  AND   s.Product = c.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass
                   ,s.Region
                   ,s.Product

        ----------------------------
        --2b. Product, DE-----------
        ----------------------------
        UNION ALL

        --2b i. Base	
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Nationwide, Product' AS Granularity
                   ,NULL AS Region
                   ,s.Product
                   ,0 AS IsRetained
                   ,1 AS IsNationwide
                   ,@BaseYear AS PlanYearID
                   ,CASE WHEN SUM (b.BY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (b.BY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (b.BY_UseFactor) AS FLOAT), CAST(SUM (b.BY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #BaseYearFactors b
               ON s.IsDEPound = b.IsDEPound
                  AND   s.BarcClass = b.BarcClass
                  AND   s.Region = b.Region
                  AND   s.Product = b.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass
                   ,s.Product

        --2b ii. Retained Current
        UNION ALL
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Nationwide, Product' AS Granularity
                   ,NULL AS Region
                   ,s.Product
                   ,1 AS IsRetained
                   ,1 AS IsNationwide
                   ,@CurrentYear AS PlanYearID
                   ,CASE WHEN SUM (p.PriorCY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE
                             CAST(SQRT (CAST(CAST(SUM (p.PriorCY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (p.PriorCY_UseFactor) AS FLOAT), CAST(SUM (p.PriorCY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #PriorCYFactors p
               ON s.IsDEPound = p.IsDEPound
                  AND   s.BarcClass = p.BarcClass
                  AND   s.Region = p.Region
                  AND   s.Product = p.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass
                   ,s.Product
        UNION ALL
        --2b iii. Current
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Nationwide, Product' AS Granularity
                   ,NULL AS Region
                   ,s.Product
                   ,0 AS IsRetained
                   ,1 AS IsNationwide
                   ,@CurrentYear AS PlanYearID
                   ,CASE WHEN SUM (c.CY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (c.CY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (c.CY_UseFactor) AS FLOAT), CAST(SUM (c.CY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #CurrentYearFactors c
               ON s.IsDEPound = c.IsDEPound
                  AND   s.BarcClass = c.BarcClass
                  AND   s.Region = c.Region
                  AND   s.Product = c.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass
                   ,s.Product

        ----------------------------
        --2c. Product --------------
        ----------------------------
        --Note the methodology to project class B is to use the current/base nationwide product trend that does not vary by DE. 
        --This section therefore only has class B and only includes base and current (not retained current).
        UNION ALL

        --2c i. Base	
        SELECT      NULL AS IsDEPound   --Does not vary by DE
                   ,s.BarcClass
                   ,'Nationwide, Product' AS Granularity
                   ,NULL AS Region
                   ,s.Product
                   ,0 AS IsRetained
                   ,1 AS IsNationwide
                   ,@BaseYear AS PlanYearID
                   ,CASE WHEN SUM (b.BY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (b.BY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (b.BY_UseFactor) AS FLOAT), CAST(SUM (b.BY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #BaseYearFactors b
               ON s.IsDEPound = b.IsDEPound
                  AND   s.BarcClass = b.BarcClass
                  AND   s.Region = b.Region
                  AND   s.Product = b.Product
        WHERE       s.BarcClass = 'B'   --Only needed for class B
        GROUP BY    s.BarcClass
                   ,s.Product

        UNION ALL
        --2c ii. Current

        SELECT      NULL AS IsDEPound   --Does not vary by DE
                   ,s.BarcClass
                   ,'Nationwide, Product' AS Granularity
                   ,NULL AS Region
                   ,s.Product
                   ,0 AS IsRetained
                   ,1 AS IsNationwide
                   ,@CurrentYear AS PlanYearID
                   ,CASE WHEN SUM (c.CY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (c.CY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (c.CY_UseFactor) AS FLOAT), CAST(SUM (c.CY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #CurrentYearFactors c
               ON s.IsDEPound = c.IsDEPound
                  AND   s.BarcClass = c.BarcClass
                  AND   s.Region = c.Region
                  AND   s.Product = c.Product
        WHERE       s.BarcClass = 'B'   --Only needed for class B
        GROUP BY    s.BarcClass
                   ,s.Product

        UNION ALL
        ------------------------
        --2d. Nationwide--------
        ------------------------
        --2d i. Base	
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Nationwide' AS Granularity
                   ,NULL AS Region
                   ,NULL AS Product
                   ,0 AS IsRetained
                   ,1 AS IsNationwide
                   ,@BaseYear AS PlanYearID
                   ,CASE WHEN SUM (b.BY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (b.BY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (b.BY_UseFactor) AS FLOAT), CAST(SUM (b.BY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #BaseYearFactors b
               ON s.IsDEPound = b.IsDEPound
                  AND   s.BarcClass = b.BarcClass
                  AND   s.Region = b.Region
                  AND   s.Product = b.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass

        --2d ii. Retained Current
        UNION ALL
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Nationwide' AS Granularity
                   ,NULL AS Region
                   ,NULL AS Product
                   ,1 AS IsRetained
                   ,1 AS IsNationwide
                   ,@CurrentYear AS PlanYearID
                   ,CASE WHEN SUM (p.PriorCY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE
                             CAST(SQRT (CAST(CAST(SUM (p.PriorCY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (p.PriorCY_UseFactor) AS FLOAT), CAST(SUM (p.PriorCY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #PriorCYFactors p
               ON s.IsDEPound = p.IsDEPound
                  AND   s.BarcClass = p.BarcClass
                  AND   s.Region = p.Region
                  AND   s.Product = p.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass

        UNION ALL
        --2d iii. Current
        SELECT      s.IsDEPound
                   ,s.BarcClass
                   ,'Nationwide' AS Granularity
                   ,NULL AS Region
                   ,NULL AS Product
                   ,0 AS IsRetained
                   ,1 AS IsNationwide
                   ,@CurrentYear AS PlanYearID
                   ,CASE WHEN SUM (c.CY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(CAST(SUM (c.CY_MM) AS FLOAT) / @CredibleMemberMonths AS FLOAT)) AS FLOAT)END AS Credibility
                   ,dbo.Trend_fnSafeDivide (CAST(SUM (c.CY_UseFactor) AS FLOAT), CAST(SUM (c.CY_MM) AS FLOAT), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
        FROM        #Shell s
        LEFT JOIN   #CurrentYearFactors c
               ON s.IsDEPound = c.IsDEPound
                  AND   s.BarcClass = c.BarcClass
                  AND   s.Region = c.Region
                  AND   s.Product = c.Product
        GROUP BY    s.IsDEPound
                   ,s.BarcClass;

    END;
GO
