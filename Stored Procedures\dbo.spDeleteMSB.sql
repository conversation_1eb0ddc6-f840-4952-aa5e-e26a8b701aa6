SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spDeleteMSB
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2012-JAN-19
--
-- DESCRIPTION: Responsible for deleting all MSBs in SavedPlanAddedBenefits for a plan during batch import
--
-- PARAMETERS:
--	Input:
--    @ForecastID
--
--	Output: NONE
--
-- RETURNS:
--
-- TABLES:
--	Read:
--    SavedPlanAddedBenefits
--
--	Write:
--    SavedPlanAddedBenefits
--
-- VIEWS:
--	Read:
--
-- STORED PROCS:
--	Executed:
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------
-- DATE         VERSION CHANGES MADE                                                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------
-- 2012-JAN-19  1       INITIAL VERSION                                                 Alex Rezmerski
-- 2023-AUG-28  2       MSB Only.														Adam Gilbert
-- --------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spDeleteMSB]
	(
	@ForecastID INT
	)
AS

DELETE FROM SavedPlanAddedBenefits 
	WHERE ForecastID = @ForecastID
	AND BidServiceCatID <> 35 --exclude related party
GO
