SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetPlansInBaseExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2012-Jan-12
-- HEADER UPDATED: 2016-Aug-23
--
-- DESCRIPTION: Designed to extract plans in Base
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:
--      SavedForecastSetup
--      SavedPlanInfo
--      SavedPlanDFSummary
--      SavedMarketInfo
--      SavedRegionInfo
--      SavedDivisionInfo
--      LkpProductType
--
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2012-Jan-12      1           Initial Version                                                     Bobby Jaegers
-- 2013-Oct-04      2           Modified to Include SegmentId                                       Anubhav Mishra
-- 2016-Aug-23      3           Removed SavedCUPlanMap                                              Jordan Purdue
-- 2018-Apr-26      4           Modified LkpExtCMSPlanType for new UI table modifications           Jordan Purdue
-- 2019-JUl-03      5           Made changes for market, division, region table.                    Kritika Singh                           														
-- 2020-Sep-28      6           Backend Alignment and Restructuring                                 Keith Galloway
-- 2024-May-05      7			Added NOLOCK Table Hint												Kiran Kola
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetPlansInBaseExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
    (  
    Division varchar(30),
    Region varchar(30),
    Product varchar(30),
    [ProjectedContract-PBP] char(9), 
    [ProjectedContractNumber] char(5),
    [ProjectedPlanID] char(3),
    [ProjectedSegmentId] char(3) ,
    [BaseContract-PBP] char(9),  
    [BaseContractNumber] char(5),
    [BasePlanID] char(3),
    [BaseSegmentId] char(3),
    TotalMemberMonths int,
    NonDEPoundMemberMonths int,
    IsMAPD char(1)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT                                             
			   sdh.ActuarialDivision AS Division,
			   srh.ActuarialRegion AS Region,
			   cpt.ProductType AS Product,
			   LEFT(SPI.CPS,9) AS [ProjectedContract-PBP],
			   LEFT(SPI.CPS,5) AS [ProjectedContractNumber],
			   SUBSTRING(SPI.CPS,7,3) AS [ProjectedPlanID],
			   RIGHT(SPI.CPS,3) AS [ProjectedSegmentId],             
			   BASE.ContractNumber + '-' + Base.PlanID AS [BaseContract-PBP],
			   BASE.ContractNumber As BaseContractNumber,
			   Base.PlanID  As BasePlanID,
			   Base.SegmentId AS BaseSegmentId,   --Added SegmentId                     
			   BASE.TotalMemberMonths,                         
			   BASE.NonDEPoundMemberMonths,
			   CASE SPI.PlanTypeID WHEN 2 THEN 'Y' ELSE 'N' END AS IsMAPD
			FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)                           
			INNER JOIN                                         
			   (                                               
			   SELECT                                                                    
				   DFS.ForecastID,                              
				   DFS.MARatingOptionID,                       
				   LEFT(BaseSPI.CPS ,5) AS ContractNumber,
				   Substring(BaseSPI.CPS,7,3) AS PlanID,
				   Right(BaseSPI.CPS,3) AS SegmentID,
                   TotalMemberMonths = SUM(MMs.MemberMonths),
                   NonDEPoundMemberMonths = SUM(CASE WHEN MMs.DemogIndicator = 1 THEN MMs.MemberMonths
													ELSE 0
												END)
			   FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)
			   INNER JOIN dbo.SavedPlanDFSummary DFS  WITH (NOLOCK)
			     ON SFS.ForecastID = DFS.ForecastID
			   INNER JOIN dbo.SavedPlanInfo BaseSPI  WITH (NOLOCK)
			     ON DFS.PlanInfoID = BaseSPI.PlanInfoID
			   INNER JOIN dbo.SavedDFFinance MMs  WITH (NOLOCK)
                 ON DFS.PlanInfoID = MMs.PlanInfoID
				 AND SFS.DFVersionID = MMs.DFVersionID		                  
			   WHERE DFS.MARatingOptionID = 1                  
				   AND MMs.DemogIndicator IN(1,2)
				   AND SFS.IsliveIndex = 1
				   AND SFS.IsHidden = 0    
			   GROUP BY 
					DFS.ForecastID, DFS.MARatingOptionID, BaseSPI.CPS                      
			   ) BASE                                           
				ON SFS.ForecastID = BASE.ForecastID                           
			INNER JOIN dbo.SavedPlanInfo SPI  WITH (NOLOCK)
				ON SFS.PlanInfoID = SPI.PlanInfoID
			INNER JOIN dbo.SavedMarketInfo srd  WITH (NOLOCK)
				ON SPI.ActuarialMarketID = srd.ActuarialMarketID
			INNER JOIN dbo.SavedRegionInfo srh
				ON srd.ActuarialRegionID = srh.ActuarialRegionID
			INNER JOIN dbo.SavedDivisionInfo sdh
				ON srh.ActuarialDivisionID = sdh.ActuarialDivisionID
			INNER JOIN dbo.LkpProductType cpt
				ON SPI.PlanTypeID = cpt.ProductTypeID
			WHERE SFS.IsLiveIndex = 1
       
	ELSE
		INSERT @Results
			SELECT                                             
			   sdh.ActuarialDivision AS Division,
			   srh.ActuarialRegion AS Region,
			   cpt.ProductType AS Product,
			   LEFT(SPI.CPS,9) AS [ProjectedContract-PBP],
			   LEFT(SPI.CPS,5) AS ContractNumber,
			   SUBSTRING(SPI.CPS,7,3) AS PlanID,
			   RIGHT(SPI.CPS,3) AS SegmentId,   --Added SegmentId                      
			   BASE.ContractNumber + '-' + Base.PlanID AS [BaseContract-PBP],
			   BASE.ContractNumber,
			   Base.PlanID,
			   Base.SegmentId,     --Added SegmentId                 
			   BASE.TotalMemberMonths,                         
			   BASE.NonDEPoundMemberMonths,
			   CASE SPI.PlanTypeID WHEN 2 THEN 'Y' ELSE 'N' END AS IsMAPD
			FROM dbo.SavedForecastSetup SFS      WITH (NOLOCK)                     
			INNER JOIN                                         
			   (                                               
			   SELECT                                                                    
				   DFS.ForecastID,                              
				   DFS.MARatingOptionID,                       
				   LEFT(BaseSPI.CPS ,5) AS ContractNumber,
				   Substring(BaseSPI.CPS,7,3) AS PlanID,
				   Right(BaseSPI.CPS,3) AS SegmentID,
                   TotalMemberMonths = SUM(MMs.MemberMonths),
                   NonDEPoundMemberMonths = SUM(CASE WHEN MMs.DemogIndicator = 1 THEN MMs.MemberMonths
													ELSE 0
												END)
			   FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)
			   INNER JOIN dbo.SavedPlanDFSummary DFS  WITH (NOLOCK)
			     ON SFS.ForecastID = DFS.ForecastID
			   INNER JOIN dbo.SavedPlanInfo BaseSPI  WITH (NOLOCK)
			     ON DFS.PlanInfoID = BaseSPI.PlanInfoID
			   INNER JOIN dbo.SavedDFFinance MMs  WITH (NOLOCK)
                 ON DFS.PlanInfoID = MMs.PlanInfoID
				 AND SFS.DFVersionID = MMs.DFVersionID		                  
			   WHERE DFS.MARatingOptionID = 1                  
				   AND MMs.DemogIndicator IN(1,2)
				   AND SFS.IsliveIndex = 1
				   AND SFS.IsHidden = 0    
			   GROUP BY 
					DFS.ForecastID, DFS.MARatingOptionID, BaseSPI.CPS                  
			   ) BASE                                           
				ON SFS.ForecastID = BASE.ForecastID 
			LEFT JOIN dbo.SavedPlanInfo SPI  WITH (NOLOCK)
				ON SPI.PlanInfoID = SFS.PlanInfoID                           
			INNER JOIN dbo.SavedMarketInfo srd
				ON SPI.ActuarialMarketID = srd.ActuarialMarketID
			INNER JOIN dbo.SavedRegionInfo srh
				ON srd.ActuarialRegionID = srh.ActuarialRegionID
			INNER JOIN dbo.SavedDivisionInfo sdh
				ON srh.ActuarialDivisionID = sdh.ActuarialDivisionID
			INNER JOIN dbo.LkpProductType cpt
				ON SPI.PlanTypeID = cpt.ProductTypeID
			WHERE SFS.ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
			AND SFS.IsLiveIndex = 1		

RETURN
END
GO
