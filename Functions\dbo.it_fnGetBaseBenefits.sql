SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[it_fnGetBaseBenefits] ******/

-- replaced SynchronizedBenefitCategoryID with BenefitCategoryID
CREATE FUNCTION [dbo].[it_fnGetBaseBenefits](@ForecastID INT) RETURNS TABLE AS RETURN (SELECT DISTINCT 
                                                                                                                                                                                                                                         l.BenefitCategoryName, 
                                                                                                                                                                                                                                         s.BenefitCategoryID
                                                                                                                                                                                                                  FROM          SavedPlanBenefitDetail AS s INNER JOIN
                                                                                                                                                                                                                                         LkpIntBenefitCategory AS l ON 
                                                                                                                                                                                                                                         l.BenefitCategoryID = s.BenefitCategoryID
                                                                                                                                                                                                                  WHERE      (s.ForecastID = @ForecastID) AND 
                                                                                                                                                                                                                                         (s.BenefitCategoryID <> 0) AND 
                                                                                                                                                                                                                                         (s.IsBenefitYearCurrentYear = 0))

GO
