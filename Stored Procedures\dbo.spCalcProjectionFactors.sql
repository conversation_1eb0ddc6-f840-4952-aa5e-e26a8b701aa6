SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcProjectionFactors
--
-- CREATOR:			Sandy Ellis
--
-- CREATED DATE:	APR-06-2009
--
-- DESCRIPTION:		This procedure sets up the projection factors that are used to trend base allowed to projected allowed.  
--		
-- PARAMETERS:
--  Input  :		@ForecastID
--					@UserID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			SavedForecastSetup
--					SavedPlanInfo
--					CalcPlanExperienceByBenefitCatNonSQS
--					LkpIntBenefitCategory
--
--  Write:			CalcProjectionFactors
--					CalcProjectionFactorsNonSQS
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetMARatingClaimFactors
--					fnGetMARatingClaimFactorsNonSQS
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																	DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2010-JUL-19      1           Revised for 2012 database and removed lengthy comments                          Michael Siekerka  
-- 2010-JUL-19      2           Revised references to fnGetMemberMonthsAndAllowedByDuals                        Jake Gaecke  
-- 2010-SEP-21      3           Removed PlanVersion and cleaned up code                                         Jake Gaecke  
-- 2010-SEP-23      4           Split out ClaimFactor values by Exp to Cur (E2C) and                            Jake Gaecke  
--                                Cur to Bid (C2B) this is a result of the changes made to  
--                                fnGetMARatingClaimFactors  
-- 2010-DEC-07      5           Split out TrendFactor values by Exp to Cur (E2C) and                            Nate Jacoby  
--                                Cur to Bid (C2B)                     
-- 2010-DEC-09      6           Edited the ISNULL in variable section                                           Trevor Mahoney  
-- 2011-JAN-03      7           Updated Non-Dual to be DualEligibleTypeID = 0 (rather than = 2)                 Casey Sanders  
-- 2011-FEB-07      8           Increased precision on trend factors                                            Michael Siekerka  
-- 2011-FEB-07      9           Reformatted DualPopulationFactor and DualPopulationUtilFactor that              Joe Casey  
--                                apparently fixed the calculation  
-- 2011-FEB-16      10          Changed E2COONCostTrend to E2COONUtilTrend as it was erroneously                Michael Siekerka  
--                                inserting Cost for Util into the first table  
-- 2011-JUN-02      11          Replaced LkpIntPlanYear with dbo.fnGetBidYear()                                 Bobby Jaegers  
-- 2011-JUN-14      12          Changed @PlanYearID to return SMALLINT instead of INT                           Bobby Jaegers 
-- 2014-FEB-27      13          SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals                    Mike Deren
-- 2014-MAR-24      14          Added ability to pull from Sequestered data                                     Mike Deren  
-- 2014-NOV-26      15          Renamed tables CalcPlanExperienceByBenefitCatNonSQS9mth and                     Amit Tiwari
--                                CalcPlanExperienceByBenefitCat9mth to CalcPlanExperienceByBenefitCatNonSQS Prtl  
--                                and CalcPlanExperienceByBenefitCat Prtl respectivily and also chnaged
--                                variable name 9mth to Prtl too. 
-- 2015-NOV-17      16          Changed to fnGetMARatingClaimFactorsNonSQS for SQS ID 1,2                       Mark Freel
-- 2015-NOV-30      17          Updated DE# MM prtl to pull DE# inidicator 1 rather than 3                      Mark Freel
-- 2015-JAN-21      18          Updated MM to Prtl in Dual Pop Calcs                                            Mark Freel
-- 2017-SEP-12      19          Removed SQSData ID (3,4) logic and parameter, cleaned up code                   Chris Fleming
-- 2017-SEP-13      20          Updated parameter list for fnGetMemberMonthsAndAllowedByDuals                   Chris Fleming
-- 2017-OCT-09      21          Duplicated logic so one populates new NonSQS table using fn w/o MER             Chris Fleming
--                                and the current populates SQS (WS1 Proj Allowed) using fn w/MER
-- 2018-JAN-15      22          Changed CalcPlanExperienceByBenefitCat to CalcPlanExperiencebyBenefitCatNonSQS  Jordan Purdue
-- 2018-MAR-05      23          Added new section to populate CalcProjectionFactorsNonSQSE2C for SCT CurrYr     Chris Fleming
-- 2018-MAR-22      24          Change Dual Pop to be 1.0 for E2C table. Dual Pop is only a bid year adj        Chris Fleming
--                                since it is based on the change in membership between base year & bid year.
-- 2019-NOV-07      25          Changed userid length                                                           Deepali
-- 2020-SEP-28      26          Backend Alignment and Restructuring                                             Keith Galloway
-- 2022-OCT-17	    27    		Added internal variables for input parameters               					Khurram Minhas
--						    	Added  WITH (NOLOCK)      
-- 2022-NOV-22		28			To remove the redundancy of function execution, loaded data in Temp tables		Sheetal Patil
--								and then used those function in Joins. Then loaded data in original tables at 
--								the end as a single select statement.											
-- 2023-JAN-04		27			Removed Dual Pop Adj Factor and stripped out legacy / unused trend logic.		Jake Lewis
--								Also stopped writting to CalcProjectionFactorsNonSQSE2C; table not needed. 
-- 2024-OCT-04		28			Add handling for new IsIncludeInCostShareBasis flag in the base data table.		Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spCalcProjectionFactors]
    (
    @ForecastID INT
   ,@UserID     CHAR(7))

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY

            DECLARE @tranCount INT = @@TranCount; --Current transaction count
            DECLARE @errorMsg VARCHAR(500); --Variable for error messages 

            BEGIN TRANSACTION transaction_spCPF;

            -- Declare and set variables
            DECLARE @XForecastID INT = @ForecastID; --Internal variable for input param
            DECLARE @XUserID CHAR(7) = @UserID; --Internal variable for input param
            DECLARE @LastUpdateDateTime DATETIME = GETDATE ();
            DECLARE @PlanYearID SMALLINT = (SELECT      SPI.PlanYear
                                            FROM        dbo.SavedForecastSetup SFS WITH (NOLOCK)
                                            LEFT JOIN   dbo.SavedPlanInfo SPI WITH (NOLOCK)
                                                   ON SFS.PlanInfoID = SPI.PlanInfoID
                                            WHERE       SFS.ForecastID = @XForecastID);

            --------------------------------------------------------------
            ----- STEP 1: SET UP DATA FOR CALCPROJECTIONFACTORS ----------
            --------------------------------------------------------------
            IF OBJECT_ID ('tempdb..#CalcProjectionFactors') IS NOT NULL
                DROP TABLE #CalcProjectionFactors;
            CREATE TABLE #CalcProjectionFactors
                (PlanYearID               INT
                ,ForecastID               INT
                ,MARatingOptionID         TINYINT
                ,BenefitCategoryID        SMALLINT
                ,E2CINClaimFactorUnits    DEC(18, 15)
                ,C2BINClaimFactorUnits    DEC(18, 15)
                ,E2CINClaimFactorAllowed  DEC(18, 15)
                ,C2BINClaimFactorAllowed  DEC(18, 15)
                ,E2COONClaimFactorUnits   DEC(18, 15)
                ,C2BOONClaimFactorUnits   DEC(18, 15)
                ,E2COONClaimFactorAllowed DEC(18, 15)
                ,C2BOONClaimFactorAllowed DEC(18, 15)
                ,LastUpdateByID           CHAR(7)
                ,LastUpdateDateTime       DATETIME);

            INSERT INTO #CalcProjectionFactors

            --Experience
            SELECT      PlanYearID = @PlanYearID
                       ,PEB.ForecastID
                       ,PEB.MARatingOptionID
                       ,PEB.BenefitCategoryID
                       ,ECF.E2CINClaimFactorUnits
                       ,ECF.C2BINClaimFactorUnits
                       ,ECF.E2CINClaimFactorAllowed
                       ,ECF.C2BINClaimFactorAllowed
                       ,ECF.E2COONClaimFactorUnits
                       ,ECF.C2BOONClaimFactorUnits
                       ,ECF.E2COONClaimFactorAllowed
                       ,ECF.C2BOONClaimFactorAllowed
                       ,@XUserID
                       ,@LastUpdateDateTime
            FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS PEB WITH (NOLOCK)
           INNER JOIN   dbo.CalcPlanExperienceByBenefitCatNonSQS NDPEB WITH (NOLOCK)
                   ON PEB.ForecastID = NDPEB.ForecastID
                      AND   PEB.BenefitCategoryID = NDPEB.BenefitCategoryID
                      AND   PEB.MARatingOptionID = NDPEB.MARatingOptionID
                      AND   NDPEB.IsIncludeInCostShareBasis = PEB.IsIncludeInCostShareBasis
                      AND   NDPEB.DualEligibleTypeID = 0
           INNER JOIN   dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
                   ON BC.BenefitCategoryID = PEB.BenefitCategoryID
           INNER JOIN   dbo.fnGetMARatingClaimFactors (@XForecastID, 1) ECF -- w/MER
                   ON PEB.ForecastID = ECF.ForecastID
                      AND   PEB.BenefitCategoryID = ECF.BenefitCategoryID
            WHERE       PEB.ForecastID = @XForecastID
                        AND BC.IsEnabled = 1
                        AND PEB.MARatingOptionID = 1
                        AND PEB.DualEligibleTypeID = 1
                        AND PEB.IsIncludeInCostShareBasis = 1   --Avoid duplication with IsIncludeInCostShareBasis=0 records

            UNION ALL

            --Manual
            SELECT      PlanYearID = @PlanYearID
                       ,PEB.ForecastID
                       ,PEB.MARatingOptionID
                       ,PEB.BenefitCategoryID
                       ,MCF.E2CINClaimFactorUnits
                       ,MCF.C2BINClaimFactorUnits
                       ,MCF.E2CINClaimFactorAllowed
                       ,MCF.C2BINClaimFactorAllowed
                       ,MCF.E2COONClaimFactorUnits
                       ,MCF.C2BOONClaimFactorUnits
                       ,MCF.E2COONClaimFactorAllowed
                       ,MCF.C2BOONClaimFactorAllowed
                       ,@XUserID
                       ,@LastUpdateDateTime
            FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS PEB WITH (NOLOCK)
           INNER JOIN   dbo.CalcPlanExperienceByBenefitCatNonSQS NDPEB WITH (NOLOCK)
                   ON PEB.ForecastID = NDPEB.ForecastID
                      AND   PEB.BenefitCategoryID = NDPEB.BenefitCategoryID
                      AND   PEB.MARatingOptionID = NDPEB.MARatingOptionID
                      AND   NDPEB.IsIncludeInCostShareBasis = PEB.IsIncludeInCostShareBasis
                      AND   NDPEB.DualEligibleTypeID = 0
           INNER JOIN   dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
                   ON BC.BenefitCategoryID = PEB.BenefitCategoryID
           INNER JOIN   dbo.fnGetMARatingClaimFactors (@XForecastID, 2) MCF -- w/MER
                   ON PEB.ForecastID = MCF.ForecastID
                      AND   PEB.BenefitCategoryID = MCF.BenefitCategoryID
            WHERE       PEB.ForecastID = @XForecastID
                        AND BC.IsEnabled = 1
                        AND PEB.MARatingOptionID = 2
                        AND PEB.DualEligibleTypeID = 1
                        AND PEB.IsIncludeInCostShareBasis = 1;  --Avoid duplication with IsIncludeInCostShareBasis=0 records


            --------------------------------------------------------------
            ----- STEP 2: SET UP DATA FOR CALCPROJECTIONFACTORSNONSQS ----
            --------------------------------------------------------------
            IF OBJECT_ID ('tempdb..#CalcProjectionFactorsNonSQS') IS NOT NULL
                DROP TABLE #CalcProjectionFactorsNonSQS;
            CREATE TABLE #CalcProjectionFactorsNonSQS
                (PlanYearID               INT
                ,ForecastID               INT
                ,MARatingOptionID         TINYINT
                ,BenefitCategoryID        SMALLINT
                ,E2CINClaimFactorUnits    DEC(18, 15)
                ,C2BINClaimFactorUnits    DEC(18, 15)
                ,E2CINClaimFactorAllowed  DEC(18, 15)
                ,C2BINClaimFactorAllowed  DEC(18, 15)
                ,E2COONClaimFactorUnits   DEC(18, 15)
                ,C2BOONClaimFactorUnits   DEC(18, 15)
                ,E2COONClaimFactorAllowed DEC(18, 15)
                ,C2BOONClaimFactorAllowed DEC(18, 15)
                ,LastUpdateByID           CHAR(7)
                ,LastUpdateDateTime       DATETIME);

            INSERT INTO #CalcProjectionFactorsNonSQS

            --Experience
            SELECT      PlanYearID = @PlanYearID
                       ,PEB.ForecastID
                       ,PEB.MARatingOptionID
                       ,PEB.BenefitCategoryID
                       ,ECF.E2CINClaimFactorUnits
                       ,ECF.C2BINClaimFactorUnits
                       ,ECF.E2CINClaimFactorAllowed
                       ,ECF.C2BINClaimFactorAllowed
                       ,ECF.E2COONClaimFactorUnits
                       ,ECF.C2BOONClaimFactorUnits
                       ,ECF.E2COONClaimFactorAllowed
                       ,ECF.C2BOONClaimFactorAllowed
                       ,@XUserID
                       ,@LastUpdateDateTime
            FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS PEB WITH (NOLOCK)
           INNER JOIN   dbo.CalcPlanExperienceByBenefitCatNonSQS NDPEB WITH (NOLOCK)
                   ON PEB.ForecastID = NDPEB.ForecastID
                      AND   PEB.BenefitCategoryID = NDPEB.BenefitCategoryID
                      AND   PEB.MARatingOptionID = NDPEB.MARatingOptionID
                      AND   NDPEB.IsIncludeInCostShareBasis = PEB.IsIncludeInCostShareBasis
                      AND   NDPEB.DualEligibleTypeID = 0
           INNER JOIN   dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
                   ON BC.BenefitCategoryID = PEB.BenefitCategoryID
           INNER JOIN   dbo.fnGetMARatingClaimFactorsNonSQS (@XForecastID, 1) ECF -- w/o MER
                   ON PEB.ForecastID = ECF.ForecastID
                      AND   PEB.BenefitCategoryID = ECF.BenefitCategoryID
            WHERE       PEB.ForecastID = @XForecastID
                        AND BC.IsEnabled = 1
                        AND PEB.MARatingOptionID = 1
                        AND PEB.DualEligibleTypeID = 1
                        AND PEB.IsIncludeInCostShareBasis = 1   --Avoid duplication with IsIncludeInCostShareBasis=0 records

            UNION ALL

            --Manual
            SELECT      PlanYearID = @PlanYearID
                       ,PEB.ForecastID
                       ,PEB.MARatingOptionID
                       ,PEB.BenefitCategoryID
                       ,MCF.E2CINClaimFactorUnits
                       ,MCF.C2BINClaimFactorUnits
                       ,MCF.E2CINClaimFactorAllowed
                       ,MCF.C2BINClaimFactorAllowed
                       ,MCF.E2COONClaimFactorUnits
                       ,MCF.C2BOONClaimFactorUnits
                       ,MCF.E2COONClaimFactorAllowed
                       ,MCF.C2BOONClaimFactorAllowed
                       ,@XUserID
                       ,@LastUpdateDateTime
            FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS PEB WITH (NOLOCK)
           INNER JOIN   dbo.CalcPlanExperienceByBenefitCatNonSQS NDPEB WITH (NOLOCK)
                   ON PEB.ForecastID = NDPEB.ForecastID
                      AND   PEB.BenefitCategoryID = NDPEB.BenefitCategoryID
                      AND   PEB.MARatingOptionID = NDPEB.MARatingOptionID
                      AND   NDPEB.IsIncludeInCostShareBasis = PEB.IsIncludeInCostShareBasis
                      AND   NDPEB.DualEligibleTypeID = 0
           INNER JOIN   dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
                   ON BC.BenefitCategoryID = PEB.BenefitCategoryID
           INNER JOIN   dbo.fnGetMARatingClaimFactorsNonSQS (@XForecastID, 2) MCF -- w/o MER
                   ON PEB.ForecastID = MCF.ForecastID
                      AND   PEB.BenefitCategoryID = MCF.BenefitCategoryID
            WHERE       PEB.ForecastID = @XForecastID
                        AND BC.IsEnabled = 1
                        AND PEB.MARatingOptionID = 2
                        AND PEB.DualEligibleTypeID = 1
                        AND PEB.IsIncludeInCostShareBasis = 1;  --Avoid duplication with IsIncludeInCostShareBasis=0 records


            --------------------------------------------------------------
            ----- STEP 3: DELETE FROM / WRITE TO TABLES ------------------
            --------------------------------------------------------------
            --dbo.CalcProjectionFactors
            DELETE  FROM dbo.CalcProjectionFactors WHERE    ForecastID = @XForecastID;
            INSERT INTO dbo.CalcProjectionFactors
                (PlanYearID
                ,ForecastID
                ,MARatingOptionID
                ,BenefitCategoryID
                ,E2CINClaimFactorUnits
                ,C2BINClaimFactorUnits
                ,E2CINClaimFactorAllowed
                ,C2BINClaimFactorAllowed
                ,E2COONClaimFactorUnits
                ,C2BOONClaimFactorUnits
                ,E2COONClaimFactorAllowed
                ,C2BOONClaimFactorAllowed
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  PlanYearID
                   ,ForecastID
                   ,MARatingOptionID
                   ,BenefitCategoryID
                   ,E2CINClaimFactorUnits
                   ,C2BINClaimFactorUnits
                   ,E2CINClaimFactorAllowed
                   ,C2BINClaimFactorAllowed
                   ,E2COONClaimFactorUnits
                   ,C2BOONClaimFactorUnits
                   ,E2COONClaimFactorAllowed
                   ,C2BOONClaimFactorAllowed
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #CalcProjectionFactors;

            --dbo.CalcProjectionFactorsNonSQS
            DELETE  FROM dbo.CalcProjectionFactorsNonSQS
            WHERE   ForecastID = @XForecastID;
            INSERT INTO dbo.CalcProjectionFactorsNonSQS
                (PlanYearID
                ,ForecastID
                ,MARatingOptionID
                ,BenefitCategoryID
                ,E2CINClaimFactorUnits
                ,C2BINClaimFactorUnits
                ,E2CINClaimFactorAllowed
                ,C2BINClaimFactorAllowed
                ,E2COONClaimFactorUnits
                ,C2BOONClaimFactorUnits
                ,E2COONClaimFactorAllowed
                ,C2BOONClaimFactorAllowed
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  PlanYearID
                   ,ForecastID
                   ,MARatingOptionID
                   ,BenefitCategoryID
                   ,E2CINClaimFactorUnits
                   ,C2BINClaimFactorUnits
                   ,E2CINClaimFactorAllowed
                   ,C2BINClaimFactorAllowed
                   ,E2COONClaimFactorUnits
                   ,C2BOONClaimFactorUnits
                   ,E2COONClaimFactorAllowed
                   ,C2BOONClaimFactorAllowed
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #CalcProjectionFactorsNonSQS;


            COMMIT TRANSACTION transaction_spCPF;

        END TRY

        BEGIN CATCH

            IF (@@TranCount > @tranCount) --Check if transaction in TRY block was not closed
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transaction_spCPF;
                END;

        END CATCH;

    END;
GO
