SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetMSBExtract]   ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMSBExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-14
-- HEADER UPDATED: 2011-Dec-15
--
-- DESCRIPTION: Designed to extract fields for the MSBs - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	SavedPlanAddedBenefits
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-14		1			Initial Version														Alex Rezmerski
-- 2011-Dec-15		2			Updated for coding standards.  Added IF statement to account		Craig Wright
--									for @WhereIn being Null. Updated AddedBenefitTypeID to CHAR(3).
--									Added IsHidden in Where statement.
-- 2012-Jun-22      3           Added Benefit Name to Added Benefit Type ID                         Nick Koesters
-- 2022-Dec-22      4           Added Benefit Type ID remvoed for MSB export						Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMSBExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	AddedBenefitTypeID varchar(MAX)
    ) AS

BEGIN	
	IF @WhereIn IS NULL
	BEGIN
		INSERT @Results
			SELECT  ForecastID,
					AddedBenefitTypeID = AddedBenefitName  --MSBAddedBenefitID = AddedBenefitTypeID
			FROM SavedPlanAddedBenefits WITH (NOLOCK)
			WHERE IsHidden = 0
	END       
	ELSE
	BEGIN
		INSERT @Results
			SELECT  ForecastID,
					MSBAddedBenefitID = AddedBenefitName
			FROM SavedPlanAddedBenefits WITH (NOLOCK)
			WHERE IsHidden = 0
				AND ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
	END
RETURN
END
GO
