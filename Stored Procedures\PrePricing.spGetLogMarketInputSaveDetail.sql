SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetMarketInputSaveDetail]
--    
-- AUTHOR: Surya <PERSON>y 
--    
-- CREATED DATE: 2025-Jan-7   
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Market Input Save Detail. Shows before and after value updates.  
--    
-- PARAMETERS:    
-- Input: 
-- @LogID - The id of the log entry from LogMarketInputSave
--

-- TABLES:   
--  

-- Read:    
-- [PrePricing].[LogMarketInputSave] 

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-Jan-7		1		Initial Version                 Surya Murthy
-- 2025-Mar-27		2		Fixed $ Change bug &
--							added Note only support			Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetLogMarketInputSaveDetail]
( 
	@LogID INT
)
AS    
BEGIN    
	SET NOCOUNT ON;

	DECLARE @InputType VARCHAR(100) = (SELECT InputType FROM [PrePricing].[LogMarketInputSave] WHERE LogID = @LogID);
	-- Get the date from the log. Then convert to UTC timezone to avoid issues with Temporal tables.
	DECLARE @LogDate DATETIME = (SELECT LogDate FROM [PrePricing].[LogMarketInputSave] WHERE LogID = @LogID);
	DECLARE @UTCLogDate DATETIME = DATEADD(HOUR, DATEDIFF(hour,SYSDATETIME(),SYSUTCDATETIME()), @LogDate);


	WITH ExpandedLog AS ( -- create rows for each plan/benefit pair.
		SELECT Saves.*, TRIM(Plans.Value) AS PlanInfoID, TRIM(Benefits.Value) AS SubcategoryID
		FROM [PrePricing].[LogMarketInputSave] Saves
		CROSS APPLY STRING_SPLIT(PlanList,',') Plans
		CROSS APPLY STRING_SPLIT(BenefitList,',') Benefits
		WHERE LogID=@LogID
	),
	PriorValue AS ( --get the value before the save.
		SELECT MIV.*
		FROM prepricing.MarketInputValue 
		FOR SYSTEM_TIME AS OF @UTCLogDate  MIV 
		JOIN ExpandedLog EL
		ON MIV.planinfoid = EL.PlanInfoID
		AND MIV.subcategoryid = EL.subcategoryid
	),
	CurrentValue AS (--get the current value
		SELECT MIV.*
		FROM prepricing.MarketInputValue MIV
		JOIN ExpandedLog EL
		ON MIV.planinfoid = EL.PlanInfoID
		AND MIV.subcategoryid = EL.subcategoryid
	),

	Combined AS ( --join the two data sets
		SELECT CV.marketinputvalueid, CV.PlanInfoID,CV.subcategoryid,

		CASE WHEN @InputType IN ( 'IN', 'BOTH') 
			 THEN ISNULL(PT.INValue,'') 
			 WHEN @InputType IN ( 'OON', 'BOTH')
			 THEN ISNULL(PT.OONValue,'') 
			 WHEN @InputType IN ('$ Change')
			 THEN ISNULL(FORMAT(PT.BenefitChangeValue,'#.##'),'') 
			 WHEN @InputType IN ( 'Note')
			 THEN ISNULL(PT.Note,'')
		END AS PriorValue,

		CASE WHEN @InputType IN ( 'IN', 'BOTH') 
			 THEN ISNULL(CV.INValue,'') 
			 WHEN @InputType IN ( 'OON', 'BOTH')
			 THEN ISNULL(CV.OONValue,'') 
			 WHEN @InputType IN ('$ Change')
			 THEN ISNULL(FORMAT(CV.BenefitChangeValue,'#.##'),'')
			 WHEN @InputType IN ( 'Note')
			 THEN ISNULL(CV.Note,'') 
		END AS CurrentValue
		FROM PriorValue PT
		FULL OUTER JOIN CurrentValue CV
		ON PT.marketinputvalueid = CV.MarketInputValueID
	)

	SELECT marketinputvalueid
	,@logid AS LogID
	,@LogDate AS LogDate
	,CPS 
	,SubCategoryName AS Benefit
	,@InputType AS ValueType
	,PriorValue 
	,CurrentValue 
	FROM combined
	JOIN prepricing.PlanInfo ppi ON combined.PlanInfoID = ppi.planinfoid
	JOIN PrePricing.MarketInputSubCategory sc ON combined.SubcategoryID = sc.SubCategoryID 

END
GO
