SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spDeleteBarcStageTables
--
-- CREATOR: Sur<PERSON>rthy
--
-- CREATED DATE: 2023-May-24
--
-- DESCRIPTION: Stored Procedure responsible for Risk Factor Bulk Upload in the MA Model
--
-- PARAMETERS:
--	Input:	 
--		@UserID
--  Output:
--		
-- TABLES:
--	Read:
--		
--	Write:
--      ImportMemberMonthsDataTable_Stage
--		TblRollupQuarterizedData_Stage
--		ImportRiskFactorsDataTable_Stage
--		TblMarketADJData_Stage
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2023-May-24     1           Initial Version														Surya Murthy
-- 2023-Oct-24     2           Rowlocks added for deadlock issues									Surya Murthy
-- 2024-Oct-17     3           SCT stage tables added												Surya Murthy
-------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spDeleteBarcStageTables]
(   
    @LastUpdateByID CHAR(7)	 
)
AS
    SET NOCOUNT ON;	 
    BEGIN TRY
    BEGIN TRANSACTION barcdeleterans   		 
		 DELETE FROM dbo.TblRollupQuarterizedData_Stage WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
		 DELETE FROM dbo.TblMarketADJData_Stage WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
		 DELETE FROM dbo.ImportMemberMonthsDataTable_Stage WITH(ROWLOCK) WHERE LastUpdateByID = @LastUpdateByID;
		 DELETE FROM dbo.ImportRiskFactorsDataTable_Stage WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
		 DELETE FROM dbo.BarcSctMemberMonths_stage WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
		 DELETE FROM dbo.BarcSctRiskScores_Stage WITH(ROWLOCK) WHERE CurrentUserId = @LastUpdateByID;
	COMMIT TRANSACTION barcdeleterans;
    END TRY
    BEGIN CATCH		
		ROLLBACK TRANSACTION barcdeleterans;
		DECLARE @ErrorMessage NVARCHAR(4000);  
		DECLARE @ErrorSeverity INT;  
		DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
		DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()		 
		SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+
		CAST(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
		RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
    END CATCH
GO
