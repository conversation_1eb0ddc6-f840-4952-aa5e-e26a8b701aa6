SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- User Defined Function

/****** Object:  UserDefinedFunction [dbo].[fnGetBaseMSBs]   ******/

-- FUNCTION NAME: fnGetBaseMSBs
--
-- AUTHOR: <PERSON> 
--
-- CREATED DATE: 2010-Feb-25
--
-- DESCRIPTION: Returns a list of mandatory supplementary benefits for the specified plan.
--
-- PARAMETERS: 
--  Input: 
--      @ForecastID
--      @MARatingOptionID
-- 
-- RETURNS: 
--      Table containing mandatory supplementary benefits for the specified plan.
--
-- TABLES: 
--  Read:
--      LkpIntBenefitCategory
--      LkpExtCMSBidServiceCategory
--      SavedPlanAddedBenefits
--      SavedPlanDetail
--
-- VIEWS:
--  Read: NONE
--
-- FUNCTIONS:
--  Read:
--      fnGetBaseMSBsCombined
--
--  Called: NONE
--
-- STORED PROCS: 
--  Executed: NONE
--
-- $HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------
-- DATE			 VERSION		CHANGES MADE																DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------
-- 2010-Mar-04      1           Initial version.                                                            Jake Gaecke
-- 2010-Mar-13      2           Made some structural changes, including removing                            Casey Sanders
--                                  references to SavedPlanHeader.  Also, added left join		  
--                                  to ensure data are returned for each bid service category.
-- 2010-Apr-20      3           Removed an unneccery join on AB and added a condition                       Jake Gaecke
--                                  onto the join for LkpIntAddedBenefitCategory                
-- 2010-Jul-28      4           Changed all PerIntMSB tables to the combined functions in case of           Joe Casey
--                                  multiple C&U IDs
-- 2010-Jul-29      5           Corrected TotalProduct to reflect the issue of combined C&U IDs             Joe Casey
-- 2010-Jul-30      6           Removed Join to SavedPlanCUSummary to prevent multiplying data              Joe Casey
--                                  based on the number of C&U IDs in the plan.
-- 2010-Aug-11      7           Removed PlanVersion                                                         Joe Casey
-- 2011-Mar-30      8           Changed CostTrendProduct to ProviderPaymentProduct. In                      Joe Casey
--                                  fnAppGetMABPTWS1Summary, CostTrend will be backed into,
--                                  while Provider will be directly calculated.
-- 2012-Mar-26      9           Added Utilizers to enable UNION in fnAppGetMABPTWS1Summary                  Alex Rezmerski
-- 2013-Mar-05      10          Replace savedcuutilizers with a query for related party adj                 Tim Gao
-- 2013-May-20      11          Fixed Related Party Utilizers join                                          Lindsay Allen
-- 2013-May-22      12          Fixed Related Party Utilizers join                                          Mason Roberts
-- 2013-Oct-04      13          Included Join on Segment ID                                                 Anubhav Mishra
-- 2014-Jul-09      14          Increased size of additive adjustments to a million                         Mike Deren
-- 2015-Feb-02      15          Modified UtilizationTrendProduct, BenefitChangeProduct, Population          Chris McAuley
--                                  ChangeProduct, and OtherFactorProduct to pull in INMSB.UnitsPTMPY
--                                  and OONMSB.UnitsPTMPY instead of INMSB.AllowedPMPM and OON.AllowedPMPM
-- 2015-Apr-21      16          Updated the length of columns INAddedBenefitAllowed and                     Deepthi Thiyagu
--                                  OONAddedBenefitAllowed
-- 2015-Apr-22      17          Modified the Inner Join for SavedCUUtilizers                                Jordan Purdue
-- 2016-Aug-18      18          Removed SavedCUPlanMap Commented Out code                                   Jordan Purdue
-- 2016-Aug-22      19          Removed PerIntRelatedPartiesMSBExperience                                   Jordan Purdue
-- 2016-Oct-06      20          Sigma-CU Clean up Remove fnGetBaseMSBsTrendCombined and                     Manisha Tyagi
--                                  fnGetBaseMSBsForecastCombined
-- 2017-Sep-25      21          Removed IsInNetwork flag since new table will provide total                 Chris Fleming
--                                  removed join and renamed variables to align with total MSBs
-- 2020-Oct-26      22          Backend Alignment and Restructuring                                         Keith Galloway
-- 2022-Oct-21      23          Modifications to handle multiplicative for existing added benefits          Michael Manes
-- 2023-Apr-10		24			Implementing feature flag to enable/disable multiplicative methodology		Aleksandar Dimitrijevic
-- 2023-Jun-26		25			Removed "ORDER BY"; removed fields: OONAddedBenefitAllowed from @AB,
--								BidServiceCategoryID from the @Results										Aleksandar Dimitrijevic
-- 2023-Aug-04      26          Added NOLOCK and Internal parameter										    Sheetal Patil
-- -------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBaseMSBs] 
    (
     @ForecastID INT,
     @MARatingOptionID SMALLINT
    )

RETURNS @Results TABLE
    (
     ForecastID INT,
     ServiceCategoryCode VARCHAR(4),
     UtilType CHAR(1),
     BaseUnits DECIMAL(23, 15),
     AvgCost DECIMAL(23, 15),
     BaseAllowed DECIMAL(23, 15),
     BaseNetPMPM DECIMAL(23,15),
     UtilizationTrendProduct DECIMAL(23, 15),
     BenefitChangeProduct DECIMAL(23, 15),
     PopulationChangeProduct DECIMAL(23, 15),
     OtherFactorProduct DECIMAL(23, 15),
     TotalProduct DECIMAL(23,15),
     ProviderPaymentProduct DECIMAL(23, 15),
     CostUtilizationProduct DECIMAL(23, 15),
     UtilAdditiveAdjustment DECIMAL(14, 6),
     PMPMAdditiveAdjustment DECIMAL(10, 6),
     BidAllowed DECIMAL(23, 15),
     BidUtilization DECIMAL(23, 15)
    ) 

AS
BEGIN

	-- declare variables
	DECLARE @XForecastID INT = @ForecastID,
            @XMARatingOptionID SMALLINT = @MARatingOptionID;
	DECLARE @MSBMethod BIT;

	-- set values
	SET @MSBMethod = (SELECT IsMSBWS1MultFactorsEnabled FROM dbo.LkpModelSettings WITH (NOLOCK));

	-- table with added benefits
	DECLARE @AB TABLE
		(
		 BidServiceCatID INT,
		 INAddedBenefitUtilization DECIMAL(14, 6),
		 OONAddedBenefitUtilization DECIMAL(14, 6),
		 INAddedBenefitAllowed DECIMAL(6, 2)
		);

	INSERT INTO @AB
	SELECT AB2.BidServiceCatID,
           INAddedBenefitUtilization = SUM(ISNULL(AB2.INAddedBenefitUtilization, 0)),
           OONAddedBenefitUtilization = SUM(ISNULL(AB2.OONAddedBenefitUtilization, 0)),
           INAddedBenefitAllowed = SUM(ISNULL(AB2.INAddedBenefitAllowed, 0))
      FROM dbo.LkpExtCMSBidServiceCategory BSC2  WITH (NOLOCK)
      LEFT JOIN dbo.SavedPlanAddedBenefits AB2 WITH (NOLOCK)
        ON BSC2.BidServiceCategoryID = AB2.BidServiceCatID
     INNER JOIN dbo.SavedForecastSetup sfs WITH (NOLOCK)
		ON AB2.ForecastID = sfs.ForecastID
     WHERE sfs.ForecastID = @XForecastID
       AND AB2.IsHidden = 0
     GROUP BY AB2.BidServiceCatID, 
	          sfs.ForecastID;

	-- final output
	INSERT @Results
    SELECT @XForecastID AS ForecastID,
           CatCode = BSC.ServiceCategoryCode,
           BSC.UtilType,
           BaseUnits = ISNULL(SUM(MSB.UnitsPTMPY), 0),
           AvgCost = ISNULL(CASE WHEN SUM(MSB.UnitsPTMPY) = 0 THEN 0
							     ELSE (SUM(MSB.AllowedPMPM) * 12000) / SUM(MSB.UnitsPTMPY)
							END, 0),
           BaseAllowed = ISNULL(SUM(MSB.AllowedPMPM), 0),
           BaseNetPMPM = ISNULL(SUM(MSB.PaidPMPM), 0),
		   UtilizationTrendProduct = ISNULL(SUM(MSB.UnitsPTMPY * MSB.UtilUnitP1000Trend), 0),
		   BenefitChangeProduct = ISNULL(SUM(MSB.UnitsPTMPY * MSB.UtilBenefitPlanChange), 0),
		   PopulationChangeProduct = ISNULL(SUM(MSB.UnitsPTMPY * MSB.UtilPopulationChange), 0),
		   OtherFactorProduct = ISNULL(SUM(MSB.UnitsPTMPY * MSB.UtilOtherFactor), 0),
		   ISNULL(SUM(MSB.AllowedPMPM), 0) AS TotalProduct,
		   ProviderPaymenProduct = ISNULL(SUM(MSB.AllowedPMPM * MSB.UCAProviderPaymentChange), 0),
		   CostUtilizationProduct = ISNULL(SUM(MSB.AllowedPMPM * MSB.UCAOtherFactor), 0),
		   UtilAdditiveAdjustment = CASE WHEN @MSBMethod = 1 THEN ISNULL(SUM(MSB.UtilAdditiveAdjustment), 0)
										 ELSE SUM(ISNULL(AB.INAddedBenefitUtilization, 0) + ISNULL(AB.OONAddedBenefitUtilization, 0))
									END,
		   PMPMAdditiveAdjustment = CASE WHEN @MSBMethod = 1 THEN ISNULL(SUM(MSB.PMPMAdditiveAdjustment), 0)
										 ELSE CAST(SUM(ISNULL(AB.INAddedBenefitAllowed, 0)) AS DECIMAL(10,6))
									END,
		   BidAllowed = ISNULL(SUM(MSB.BidAllowed), 0),
		   BidUtilization = ISNULL(SUM(MSB.BidUtilization), 0)
	  FROM dbo.LkpExtCMSBidServiceCategory BSC WITH (NOLOCK)
      LEFT JOIN @AB AB
        ON BSC.BidServiceCategoryID = AB.BidServiceCatID 
      LEFT JOIN dbo.fnGetBaseMSBsCombined(@XForecastID, @XMARatingOptionID) MSB
        ON BSC.BidServiceCategoryID = MSB.BidServiceCatID
	 WHERE BSC.BidServiceCategoryID > 22
     GROUP BY BSC.BidServiceCategoryID,
			  BSC.ServiceCategoryCode,
			  BSC.UtilType;

    RETURN
END
GO
