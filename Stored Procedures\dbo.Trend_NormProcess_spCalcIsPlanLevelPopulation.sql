SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: Trend_NormProcess_spCalcIsPlanLevelPopulation  
--  
-- CREATOR: Andy Blink  
--  
-- CREATED DATE: Jan-24-2020  
--  
-- DESCRIPTION: This stored procedure calculates historical population trends for the normalized process  
--                
--              The following procedures use similar logic to this one, so any updates made here will most likely apply to:  
--                  Trend_NormProcess_spCalcIsPlanLevelOutlierClaims  
--                  Trend_NormProcess_spCalcIsPlanLevelInducedUtilization  
--                  Trend_NormProcess_spCalcIsPlanLevelCMSReimb  
--                  Trend_NormProcess_spCalcIsPlanLevelContractual  
--                    
-- PARAMETERS:  
--  Input  : @LastUpdateByID  
--  
--  Output : NONE  
--  
-- TABLES : Read :  LkpIntPlanYear  
--                  LkpProjectionVersion  
--                  Trend_CalcHistoricMembership  
--                  Trend_SavedRelativityPopulation  
--					Trend_NormProcess_IsIncludeInTrendSnapshot
--  
--          Write:  Trend_NormProcess_CalcIsPlanLevel_Population  
--  
-- VIEWS:   Read:   vwPlanInfo  
--  
-- FUNCTIONS: Trend_fnSafeDivide  
--  
-- STORED PROCS: Executed: NONE  
--  
-- $HISTORY   
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION			CHANGES MADE                                                                DEVELOPER(S)        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-01-24   		1		    Initial Version                                     						Andy Blink
-- 2021-05-24			2			Referece IsIncludeInTrendSnapshot for normalized audit fix					Jake Lewis
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------  

CREATE PROCEDURE [dbo].[Trend_NormProcess_spCalcIsPlanLevelPopulation]
@LastUpdateByID CHAR(13)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        -- Declare and set variables 
        DECLARE @LastUpdateDateTime DATETIME = GETDATE ();
        DECLARE @CurrentYear SMALLINT = (SELECT PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);
        DECLARE @MaxCurrentYearQuarter TINYINT = (SELECT    LastCurrentYearQuarter
                                                  FROM      dbo.LkpProjectionVersion
                                                  WHERE     IsLiveProjection = 1);
        DECLARE @OldestHistoricYear INT = CASE WHEN @MaxCurrentYearQuarter = 0 -- Bid/FLP projection (i.e. no data in current year)
        THEN                                       @CurrentYear - 5
                                               ELSE @CurrentYear - 4 -- Non-Bid season projection (i.e. have some data in current year)
                                          END;
        DECLARE @Component VARCHAR(50) = 'Population';
        DECLARE @PlanTypeGranularity VARCHAR(250) = 'Plan';
        DECLARE @NationwideRegion VARCHAR(50) = 'Nationwide';

        -- Reporting categories from population relativity table
        IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
        SELECT  DISTINCT
                ReportingCategory
        INTO    #RepCat
        FROM    dbo.Trend_SavedRelativityPopulation;

        --Bring in historical membership used in the population process and summarize to the CPS/Region/ReportCat level for historical years  
        IF (SELECT  OBJECT_ID ('tempdb..#Trend_CalcHistoricMembership')) IS NOT NULL
            DROP TABLE #Trend_CalcHistoricMembership;
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,b.ReportingCategory
                   ,ISNULL (SUM (a.MemberMonths), 0) AS MemberMonths
        INTO        #Trend_CalcHistoricMembership
        FROM        dbo.Trend_CalcHistoricMembership a
       CROSS JOIN   #RepCat b
        GROUP BY    a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,b.ReportingCategory;

        --Bring in the population relativities into a temp table, add ActuarialRegion, and multiply relativities by allowed  
        IF (SELECT  OBJECT_ID ('tempdb..#SavedPopulation')) IS NOT NULL
            DROP TABLE #SavedPopulation;
        SELECT      a.CPS
                   ,a.PlanYearID
                   ,a.QuarterID
                   ,b.Region AS ActuarialRegion
                   ,a.ReportingCategory
                   ,ISNULL (a.CostRelativity * c.MemberMonths, 0) AS CostRelativity
                   ,ISNULL (a.UseRelativity * c.MemberMonths, 0) AS UseRelativity
                   ,ISNULL (c.MemberMonths, 0) AS MemberMonths
        INTO        #SavedPopulation
        FROM        dbo.Trend_SavedRelativityPopulation a
       INNER JOIN   dbo.vwPlanInfo b
               ON b.CPS = a.CPS
                  AND   b.PlanYear = a.PlanYearID
        LEFT JOIN   #Trend_CalcHistoricMembership c
               ON a.CPS = c.CPS
                  AND   a.PlanYearID = c.PlanYearID
                  AND   a.QuarterID = c.QuarterID
                  AND   a.ReportingCategory = c.ReportingCategory
        LEFT JOIN   dbo.Trend_NormProcess_IsIncludeInTrendSnapshot d
               ON d.CPS = a.CPS
                  AND   d.PlanYear = a.PlanYearID
        WHERE       b.IsHidden = 0
                    AND b.IsOffMAModel = 'No'
                    AND b.Region NOT IN ('Unmapped')
                    AND d.IsIncludeInTrend = 1;

        --Summarize the previous step down to the Region/ReportCat level and add a "Nationwide" ActuarialRegion  
        IF (SELECT  OBJECT_ID ('tempdb..#SavedPopulation_Sum')) IS NOT NULL
            DROP TABLE #SavedPopulation_Sum;
        SELECT      PlanYearID
                   ,QuarterID
                   ,ActuarialRegion
                   ,ReportingCategory
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (CostRelativity), SUM (MemberMonths), 1), 1) AS CostRelativity
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (UseRelativity), SUM (MemberMonths), 1), 1) AS UseRelativity
                   ,@LastUpdateByID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
        INTO    #SavedPopulation_Sum
        FROM        #SavedPopulation
        GROUP BY    PlanYearID
                   ,QuarterID
                   ,ActuarialRegion
                   ,ReportingCategory
        UNION ALL
        --Summarize to the Nationwide level and insert "Nationwide" as the ActuarialRegion for historical years  
        SELECT      PlanYearID
                   ,QuarterID
                   ,@NationwideRegion AS ActuarialRegion
                   ,ReportingCategory
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (CostRelativity), SUM (MemberMonths), 1), 1) AS CostRelativity
                   ,ISNULL (dbo.Trend_fnSafeDivide (SUM (UseRelativity), SUM (MemberMonths), 1), 1) AS UseRelativity
                   ,@LastUpdateByID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
        FROM        #SavedPopulation
        GROUP BY    PlanYearID
                   ,QuarterID
                   ,ReportingCategory;

        --Transpose the plan year field to be separate columns so trends can be calculated year over year in the next step  
        IF (SELECT  OBJECT_ID ('tempdb..#PrepForTrendCalc')) IS NOT NULL
            DROP TABLE #PrepForTrendCalc;
        SELECT      a.PlanYearID
                   ,a.QuarterID
                   ,a.ActuarialRegion
                   ,a.ReportingCategory
                   ,b.CostRelativity AS CostRelativityPY
                   ,b.UseRelativity AS UseRelativityPY
                   ,a.CostRelativity
                   ,a.UseRelativity
        INTO        #PrepForTrendCalc
        FROM        #SavedPopulation_Sum a
        LEFT JOIN   #SavedPopulation_Sum b
               ON (b.PlanYearID + 1) = a.PlanYearID
                  AND   b.QuarterID = a.QuarterID
                  AND   b.ActuarialRegion = a.ActuarialRegion
                  AND   b.ReportingCategory = a.ReportingCategory
        WHERE       a.PlanYearID > @OldestHistoricYear;

        --Calculate the year over year trends from the relativity factors  
        IF (SELECT  OBJECT_ID ('tempdb..#TrendCalc')) IS NOT NULL
            DROP TABLE #TrendCalc;
        SELECT  PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,ISNULL (dbo.Trend_fnSafeDivide (CostRelativity, CostRelativityPY, 1) - 1, 1) AS CostAdjustment
               ,ISNULL (dbo.Trend_fnSafeDivide (UseRelativity, UseRelativityPY, 1) - 1, 1) AS UseAdjustment
        INTO    #TrendCalc
        FROM    #PrepForTrendCalc;

        --Delete and insert trends into the final output table  
        DELETE  FROM dbo.Trend_NormProcess_CalcIsPlanLevel_Population WHERE 1 = 1;

        INSERT INTO dbo.Trend_NormProcess_CalcIsPlanLevel_Population
            (ComponentVersionID
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,PlanYearID
            ,QuarterID
            ,ActuarialRegion
            ,ReportingCategory
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)
        --Non-current year historic trends  
        SELECT  NULL AS ComponentVersionID
               ,@Component AS Component
               ,@PlanTypeGranularity AS PlanTypeGranularity
               ,NULL AS PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #TrendCalc
        WHERE   PlanYearID < @CurrentYear
        UNION ALL
        --Current year historic trends  
        SELECT  NULL AS ComponentVersionID
               ,@Component AS Component
               ,@PlanTypeGranularity AS PlanTypeGranularity
               ,NULL AS PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,@LastUpdateByID
               ,@LastUpdateDateTime
        FROM    #TrendCalc
        WHERE   PlanYearID = @CurrentYear
                AND QuarterID <= @MaxCurrentYearQuarter;

    END;
GO

