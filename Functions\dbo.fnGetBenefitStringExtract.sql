SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-------------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnGetBenefitStringExtract  
--  
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2012-Aug-20
--
-- DESCRIPTION: Designed to extract the Benefit String - will match the upload
--
-- PARAMETERS:
--  Input  :		@WhereIN				 VARCHAR(MAX)
--
--  Output :		@Results TABLE
--					(ForecastID,			INT
--					CPS,					CHAR(13)
--					Bid Year Benefit String	VARCHAR(100))
--
-- TABLES : 
--	Read :			SavedPlanAssumptions
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnStringSplit
--
-- STORED PROCS:	NONE
--
-- $HISTORY
-- -------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE																				DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------------------------
-- 2012-AUG-20      1           Initial Version																				Mike Deren
-- 2024-NOV-05      2           Removed "CurrentBenefitString"																Dheeraj Singh
-- 2025-FEB-21		3		    Added CPS to output                             											Alex Brandt
----------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBenefitStringExtract]
(
    @WhereIN VARCHAR(MAX) = NULL
)
RETURNS @Results TABLE
(
    [ForecastID] [INT] NOT NULL,
    [CPS] CHAR(13) NOT NULL,
    [Bid Year Benefit String] VARCHAR(100)
)
AS
BEGIN
    IF @WhereIN IS NULL
    BEGIN

        INSERT @Results
        SELECT [ForecastID] = spa.ForecastID,
               [CPS] = spi.CPS,
               [Bid Year Benefit String] = spa.BidBenefitString
        FROM dbo.SavedPlanAssumptions spa
            INNER JOIN dbo.SavedForecastSetup sfs
                ON sfs.ForecastID = spa.ForecastID
            INNER JOIN SavedPlanInfo spi
                ON spi.PlanInfoID = sfs.PlanInfoID;
    END;
    ELSE
    BEGIN

        INSERT @Results
        SELECT [ForecastID] = spa.ForecastID,
               [CPS] = spi.CPS,
               [Bid Year Benefit String] = spa.BidBenefitString
        FROM SavedPlanAssumptions spa
            INNER JOIN dbo.SavedForecastSetup sfs
                ON sfs.ForecastID = spa.ForecastID
            INNER JOIN SavedPlanInfo spi
                ON spi.PlanInfoID = sfs.PlanInfoID
        WHERE spa.ForecastID IN
              (
                  SELECT Value FROM dbo.fnStringSplit(@WhereIN, ',')
              );

    END;

    RETURN;
END;
GO
