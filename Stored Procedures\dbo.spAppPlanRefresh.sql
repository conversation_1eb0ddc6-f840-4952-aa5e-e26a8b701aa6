SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- =============================================
-- Author:		Sivachidambaram
-- Create date: 02-21-2011
-- Description:	To Refresh The Plan 
-- PROCEDURE NAME: spAppPlanRefresh
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@UserID
--	OutPut:
--		@ValidationMessage

	
-- Write:   
--      SavedPlanHeader  
--  
-- VIEWS:  
--  
-- FUNCTIONS:  
--      fnPadInteger  
--  
-- STORED PROCS:  
--      spPlanRefresh  

--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER  
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2011-Feb-21		1			Initial version.											Sivachidambaram  
--2019-oct-30		2			Removed 'HUMAD\' to UserID									Chhavi Sinha  
-- ----------------------------------------------------------------------------------------------------------------------  

-- =============================================

CREATE PROCEDURE [dbo].[spAppPlanRefresh] 

@ForecastID INT,   
@UserID CHAR(7),  
@ValidationMessage VARCHAR(MAX) OUT 

 
AS
BEGIN

DECLARE @RC int
EXECUTE @RC = dbo.spPlanRefresh @ForecastID, @UserID,@ValidationMessage OUTPUT
select @ValidationMessage

    
END
GO
