SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetBaseMSBsfactors 
-- EXEC: Select * from fnGetBaseMSBsfactors (318,1)
-- AUTHOR:  <PERSON>  
--  
-- CREATED DATE: 2022-OCT-21  
--  
-- DESCRIPTION: Starting BY2024 added benefits on WS1 will use multiplicative trends for existing benefits.
--				This procedure maps bid year and base year MSBs onto the base data to determine whether a benefit is existing, added or removed.
--				Then pulls in the trend factors for the base MSB, as well as grab projected costs to calculate additive adjustments where applicable.
--  
-- PARAMETERS:   
--  Input:   
--      @ForecastID  
--      @MARatingOptionID  
--  
-- RETURNS:    
--      Table containing mandatory supplementary benefits with accompanying trend factors and additive information to be rolled up. 
--  
-- TABLES:   
--  Read:  
--		LkpIntAddedBenefitType
--		LkpIntAddedBenefitExpenseDetail
--		LkpIntMSBDetail 
--      LkpExtCMSBidServiceCategory  
--		SavedBasetoBidBenefitSelectionMapping
--      SavedPlanAddedBenefits  
--  
-- VIEWS:  
--  Read: 
--  
-- FUNCTIONS:  
--  Read:  
--      fnSavedDFBaseClaimsMSBs  
--		fnGetSafeDivisionResultReturnOne
--  
--  Called: NONE  
--  
-- STORED PROCS:   
--  Executed: NONE  
--  
-- Testing Code:
/*Selects Useful FOR Testing
--SELECT dfs.ForecastID, dfs.MARatingOptionID, dfs.PlanInfoID, service FROM dbo.SavedPlanDFSummary dfs
--LEFT JOIN dbo.LkpIntMSBDetail msb ON	msb.PlanInfoID = dfs.PlanInfoID 
--AND PlanYearID=(SELECT PlanYearID FROM dbo.LkpIntPlanYear WHERE IsExperienceYear=1)
--SELECT * FROM lkpintmsbdetail
--SELECT * FROM dbo.SavedPlanDetail
--SELECT * FROM dbo.SavedPlanAddedBenefits WHERE IsHidden=0 AND BidServiceCatID<>35
--SELECT * FROM dbo.LkpIntBenefitCategory WHERE BenefitCategoryID>1000
--SELECT * FROM dbo.LkpIntMSBDetail
--SELECT * FROM @saveddfbaseclaimsmsbs
--SELECT * FROM @msbdetailwithfactors
--SELECT * FROM @claimswMSBdetail
--SELECT * FROM @grabbasemsb
--SELECT * FROM @grabbidmsb
--SELECT * FROM @baseclaimswithallMSBadded
--SELECT * FROM @OutputTable
--Put in test case
--UPDATE #grabbasemsb SET MSBCode='WDE001' WHERE MSBCode='ACU020'
--UPDATE #grabbasemsb SET MSBCode='WDE002' WHERE MSBCode='INC009'
--UPDATE #grabbidmsb SET MSBCode='WDE003' WHERE MSBCode='ACU020'
--UPDATE #grabbidmsb SET MSBCode='WDE002' WHERE MSBCode='INC009'
--UPDATE #grabbidmsb SET MSBCode='WDE005' WHERE MSBCode='FTP002'
*/
-- $HISTORY   
-- -------------------------------------------------------------------------------------------------------------------------  
-- DATE				VERSION		CHANGES MADE															DEVELOPER  
-- -------------------------------------------------------------------------------------------------------------------------  
-- 2022-OCT-21      1           Initial version.            Michael Manes  
-- 2023-JUN-23		2			Recoding to standards:
--								Removed "SELECT *" statements;
--								removed nested queries;
--								removed "ORDER BY"											
--								removed fields: BaseMSBCode, BidMSBCode, RepriceMSBCode, RepriceAllowed,
--								RepriceUtilization, Method, IncurredYear, [Provider], BenefitCategoryID,
--								PymtReductionAmt, AdmitCnt, EncounterAdmitCnt, ImplicitMarginPaid,
--								DelegatedEncounterAdmitCnt, AdditiveAdjAdmits, UCAdmitsAdj				Aleksandar Dimitrijevic
-- 2023-Aug-04      3           Added Internal parameter and NOLOCK				Sheetal Patil
-- 2024-Feb-09      4           The following removed fields were brought back: BaseMSBCode, BidMSBCode,   
--                              RepriceMSBCode, Method, IncurredYear, BenefitCategoryID, RepriceUtilization;  
--                              removed UtilBenefitPlanChange from @claimswMSBdetail step                 Aleksandar Dimitrijevic
-- 2024-Apr-01      5           Added "WHERE bsm.PlanYear=@BaseYear" in step @bsm                         Aleksandar Dimitrijevic
-- 2024-May-01		6			added new method category "Special" to be hadled through the additive 
--								method for cases where MSB exsists in bid and base and Bid/Base
--								All'd or Use are 0, but their counterpart Base/Bid All'd/Use are not 0		Aleksandar Dimitrijevic
--2024-Aug-05       7			updated the code based on SavedBasetoBidBenefitSelectionMapping schema changes  Chaitanya durga K
--2024-Oct-23		8			Cost Share Basis, bringing in seven new fields from fnSavedDFBaseClaimsMSBs		Michael Manes
--2025-Jan-24		9			Add RepriceAllowed back in for new CMS requirement								Michael Manes
-- -------------------------------------------------------------------------------------------------------------------------  
CREATE FUNCTION [dbo].[fnGetBaseMSBsFactors]
    (@ForecastID       INT
    ,@MARatingOptionID TINYINT)

RETURNS @OutputTable TABLE
    (PlanInfoID                                   INT               --to fix issues with sumproduct in *Combined
    ,IncurredYear                                 INT
    ,MARatingOptionID                             SMALLINT
    ,DFVersionID                                  INT
    ,DemogIndicator                               SMALLINT
    ,Method                                       VARCHAR(8)
    ,BaseMSBCode                                  VARCHAR(6)
    ,BidMSBCode                                   VARCHAR(6)
    ,RepriceMSBCode                               VARCHAR(6)
    ,BenefitCategoryID                            SMALLINT
    ,BidServiceCatID                              SMALLINT
    ,BidAllowed                                   DECIMAL(7, 2)
    ,BidUtilization                               DECIMAL(15, 6)
    ,RepriceUtilization                           DECIMAL(11, 2)
	,RepriceAllowed								  DECIMAL(7, 2)
    ,BaseCalculatedAllowed                        DECIMAL(19, 9)
    ,BaseCalculatedUtilization                    DECIMAL(19, 9)
    ,AdditiveCost                                 DECIMAL(38, 9)
    ,AdditiveUtil                                 DECIMAL(27, 9)
    ,UtilUnitP1000Trend                           DECIMAL(23, 15)
    ,UtilBenefitPlanChange                        DECIMAL(23, 15)
    ,UtilPopulationChange                         DECIMAL(23, 15)
    ,UtilOtherFactor                              DECIMAL(23, 15)
    ,UCAProviderPaymentChange                     DECIMAL(23, 15)   --factors were 38,18 changed to the following
    ,UCAOtherFactor                               DECIMAL(23, 15)
    ,Paid                                         DECIMAL(19, 9)
    ,MbrCS                                        DECIMAL(19, 9)
    ,UnitCnt                                      DECIMAL(19, 9)
    ,EncounterMbrCS                               DECIMAL(19, 9)
    ,EncounterUnitCnt                             DECIMAL(19, 9)
    ,DelegatedEncounterMbrCS                      DECIMAL(19, 9)
    ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9)
    ,CapDirectPayEPaidClaims                      DECIMAL(19, 9)
    ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9)
    ,CapDirectPayBCAllocated                      DECIMAL(19, 9)
    ,CapDirectPayScenarioAllocated                DECIMAL(19, 9)
    ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9)
    ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9)
    ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9)
    ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9)
    ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9)
    ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9)
    ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9)
    ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9)
    ,CapMSBs                                      DECIMAL(19, 9)
    ,CapProviderRewards                           DECIMAL(19, 9)
    ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9)
    ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9)
    ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9)
    ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9)
    ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9)
    ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9)
    ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9)
    ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9)
    ,PartDCapAdj                                  DECIMAL(19, 9)
    ,PartDCapAdjDelegated                         DECIMAL(19, 9)
    ,MSCapAdj                                     DECIMAL(19, 9)
    ,MSCapAdjDelegated                            DECIMAL(19, 9)
    ,SubCapAdj                                    DECIMAL(19, 9)
    ,SubCapAdjExclude                             DECIMAL(19, 9)
    ,MedicaidAdjPaid                              DECIMAL(19, 9)
    ,MedicaidAdjMbrCS                             DECIMAL(19, 9)
    ,ImplicitMarginMbrCS                          DECIMAL(19, 9)
    ,AdditiveAdjPaid                              DECIMAL(19, 9)
    ,AdditiveAdjMbrCS                             DECIMAL(19, 9)
    ,AdditiveAdjUnits                             DECIMAL(19, 9)
    ,ModelOfCareAdjPaid                           DECIMAL(19, 9)
    ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9)
    ,ModelOfCareAdjUnits                          DECIMAL(19, 9)
    ,UCUnitsAdj                                   DECIMAL(19, 9)
    ,PartBRxRebatesPharmacy                       DECIMAL(19, 9)
    ,PartBRxRebatesQN                             DECIMAL(19, 9)
    ,RelatedPartiesAdj                            DECIMAL(19, 9)
    ,ProfitAdj                                    DECIMAL(19, 9)
    ,MSBPaid                                      DECIMAL(19, 9)
    ,MSBMbrCS                                     DECIMAL(19, 9)
    ,MSBUnits                                     DECIMAL(19, 9)
    ,MSBReductionCap                              DECIMAL(19, 9)
    ,MSBReductionClaimsPaid                       DECIMAL(19, 9)
    ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9)
    ,MSBReductionClaimsUnits                      DECIMAL(19, 9)
    ,MSBReductionQuality                          DECIMAL(19, 9)
    ,MemberMonths                                 INT)

AS
    BEGIN
        -- declare variables	
        DECLARE @XForecastID       INT      = @ForecastID
               ,@XMARatingOptionID SMALLINT = @MARatingOptionID;
        DECLARE @Existing VARCHAR(8) = 'Existing';
        DECLARE @Added VARCHAR(8) = 'Added';
        DECLARE @Removed VARCHAR(8) = 'Removed';
        DECLARE @Special VARCHAR(8) = 'Special'; -- (additive method) for cases where MSB exists in base and bid and either base or bid All'd or Use are 0, but their bid/base counterpart is <> 0
        DECLARE @BaseYear SMALLINT;

        -- set values		
        SET @BaseYear = (SELECT PlanYearID
                         FROM   dbo.LkpIntPlanYear WITH (NOLOCK)
                         WHERE  IsExperienceYear = 1);

        --Pull in BaseDF Data for MSBs
        DECLARE @saveddfbaseclaimsmsbs TABLE
            (MARatingOptionID                             TINYINT        NOT NULL
            ,PlanInfoID                                   INT            NOT NULL
            ,DFVersionID                                  INT            NOT NULL
            ,IncurredYear                                 INT            NOT NULL
            ,DemogIndicator                               TINYINT        NOT NULL
            ,[PROVIDER]                                   VARCHAR(255)   NOT NULL
            ,BenefitCategoryID                            SMALLINT       NOT NULL
            ,BidServiceCatID                              SMALLINT       NOT NULL
            ,Paid                                         DECIMAL(19, 9) NULL
            ,MbrCS                                        DECIMAL(19, 9) NULL
            ,UnitCnt                                      DECIMAL(19, 9) NULL
            ,EncounterMbrCS                               DECIMAL(19, 9) NULL
            ,EncounterUnitCnt                             DECIMAL(19, 9) NULL
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9) NULL
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9) NULL
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9) NULL
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9) NULL
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9) NULL
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9) NULL
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9) NULL
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9) NULL
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9) NULL
            ,CapMSBs                                      DECIMAL(19, 9) NULL
            ,CapProviderRewards                           DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9) NULL
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9) NULL
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9) NULL
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9) NULL
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9) NULL
            ,PartDCapAdj                                  DECIMAL(19, 9) NULL
            ,PartDCapAdjDelegated                         DECIMAL(19, 9) NULL
            ,MSCapAdj                                     DECIMAL(19, 9) NULL
            ,MSCapAdjDelegated                            DECIMAL(19, 9) NULL
            ,SubCapAdj                                    DECIMAL(19, 9) NULL
            ,SubCapAdjExclude                             DECIMAL(19, 9) NULL
            ,MedicaidAdjPaid                              DECIMAL(19, 9) NULL
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9) NULL
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9) NULL
            ,AdditiveAdjPaid                              DECIMAL(19, 9) NULL
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9) NULL
            ,AdditiveAdjUnits                             DECIMAL(19, 9) NULL
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9) NULL
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9) NULL
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9) NULL
            ,UCUnitsAdj                                   DECIMAL(19, 9) NULL
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9) NULL
            ,PartBRxRebatesQN                             DECIMAL(19, 9) NULL
            ,RelatedPartiesAdj                            DECIMAL(19, 9) NULL
            ,ProfitAdj                                    DECIMAL(19, 9) NULL
            ,MSBPaid                                      DECIMAL(19, 9) NULL
            ,MSBMbrCS                                     DECIMAL(19, 9) NULL
            ,MSBUnits                                     DECIMAL(19, 9) NULL
            ,MSBReductionCap                              DECIMAL(19, 9) NULL
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9) NULL
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9) NULL
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9) NULL
            ,MSBReductionQuality                          DECIMAL(19, 9) NULL
            ,MemberMonths                                 INT            NULL PRIMARY KEY CLUSTERED (
                                                                              BenefitCategoryID ASC
                                                                             ,[PROVIDER] ASC
                                                                             ,PlanInfoID ASC
                                                                             ,MARatingOptionID ASC
                                                                             ,DemogIndicator ASC
                                                                             ,DFVersionID));

        INSERT INTO @saveddfbaseclaimsmsbs
        SELECT  MARatingOptionID
               ,PlanInfoID
               ,DFVersionID
               ,IncurredYear
               ,DemogIndicator
               ,Provider
               ,BenefitCategoryID
               ,BidServiceCatID
               ,Paid
               ,MbrCS
               ,UnitCnt
               ,EncounterMbrCS
               ,EncounterUnitCnt
               ,DelegatedEncounterMbrCS
               ,DelegatedEncounterUnitCnt
               ,CapDirectPayEPaidClaims
               ,CapDirectPayDEPaidClaims
               ,CapDirectPayBCAllocated
               ,CapDirectPayScenarioAllocated
               ,CapDirectPayBCAllocatedDelegated
               ,CapDirectPayScenarioAllocatedDelegated
               ,CapSurplusDeficitEPaidClaims
               ,CapSurplusDeficitDEPaidClaims
               ,CapSurplusDeficitBCAllocated
               ,CapSurplusDeficitScenarioAllocated
               ,CapSurplusDeficitBCAllocatedDelegated
               ,CapSurplusDeficitScenarioAllocatedDelegated
               ,CapMSBs
               ,CapProviderRewards
               ,OPBOtherNonHospitalBCAllocated
               ,OPBOtherNonHospitalScenarioAllocated
               ,OPBClaimsBuyDownBCAllocated
               ,OPBClaimsBuyDownScenarioAllocated
               ,OPBProviderClaimSettlementsBCAllocated
               ,OPBProviderClaimSettlementsScenarioAllocated
               ,OPBAccessFeesAndOtherBCAllocated
               ,OPBAccessFeesAndOtherScenarioAllocated
               ,PartDCapAdj
               ,PartDCapAdjDelegated
               ,MSCapAdj
               ,MSCapAdjDelegated
               ,SubCapAdj
               ,SubCapAdjExclude
               ,MedicaidAdjPaid
               ,MedicaidAdjMbrCS
               ,ImplicitMarginMbrCS
               ,AdditiveAdjPaid
               ,AdditiveAdjMbrCS
               ,AdditiveAdjUnits
               ,ModelOfCareAdjPaid
               ,ModelOfCareAdjMbrCS
               ,ModelOfCareAdjUnits
               ,UCUnitsAdj
               ,PartBRxRebatesPharmacy
               ,PartBRxRebatesQN
               ,RelatedPartiesAdj
               ,ProfitAdj
               ,MSBPaid
               ,MSBMbrCS
               ,MSBUnits
               ,MSBReductionCap
               ,MSBReductionClaimsPaid
               ,MSBReductionClaimsMbrCS
               ,MSBReductionClaimsUnits
               ,MSBReductionQuality
               ,MemberMonths
        FROM    dbo.fnSavedDFBaseClaimsMSBs (@XForecastID, @XMARatingOptionID);



        DECLARE @msbdetailwithfactors TABLE
            (MSBCode                  VARCHAR(6)      NOT NULL
            ,RepriceMSBCode           VARCHAR(6)      NOT NULL
            ,CPS                      VARCHAR(13)     NOT NULL
           ,PlanInfoID               SMALLINT        NOT NULL
            ,BenefitCategoryID        SMALLINT        NOT NULL
            ,BidAddedBenefitTypeID    FLOAT           NULL
            ,BaseMSBCode              NVARCHAR(255)   NULL
            ,BidAddedBenefitName      NVARCHAR(255)   NULL
            ,RepriceUtilization       DECIMAL(11, 2)  NULL
            ,RepriceAllowed			  DECIMAL(11, 2)  NULL
            ,UtilUnitP1000Trend       DECIMAL(38, 18) NULL
            ,UtilBenefitPlanChange    DECIMAL(38, 18) NULL
            ,UtilPopulationChange     DECIMAL(38, 18) NULL
            ,UtilOtherFactor          DECIMAL(38, 18) NULL
            ,UCAProviderPaymentChange DECIMAL(38, 18) NULL
            ,UCAOtherFactor           DECIMAL(38, 18) NULL);

        DECLARE @bsm TABLE
            (PlanInfoID            SMALLINT      NULL
            ,BaseMSBCode           NVARCHAR(255) NOT NULL
            ,BidAddedBenefitTypeID INT           NULL
            ,BidAddedBenefitName   NVARCHAR(255) NULL);

        -- base code mapping
        INSERT INTO @bsm
        SELECT      bsm.PlanInfoID
                   ,bsm.BaseMSBCode
                   ,bsm.BidAddedBenefitTypeID
                   ,vpi.AddedBenefitName AS BidAddedBenefitName
        FROM        dbo.SavedBasetoBidBenefitSelectionMapping bsm WITH (NOLOCK)
        LEFT JOIN   dbo.LkpIntAddedBenefitType vpi WITH (NOLOCK)
               ON vpi.AddedBenefitTypeID = bsm.BidAddedBenefitTypeID;


        INSERT INTO @msbdetailwithfactors
        --Get all base MSB codes, factors and projected allowed/use to join onto base data
        SELECT      msbd.MSBCode
                   ,CASE WHEN bsm.BidAddedBenefitName IS NULL THEN 'DISC' ELSE LEFT(bsm.BidAddedBenefitName, 6)END AS RepriceMSBCode
                   ,msbd.CPS
                   ,msbd.PlanInfoID
                   ,msbd.BenefitCategoryID
                   ,bsm.BidAddedBenefitTypeID
                   ,bsm.BaseMSBCode
                   ,bsm.BidAddedBenefitName
                   ,RepriceUtilization = abt.INAddedBenefitUtilization + abt.OONAddedBenefitUtilization
                   ,RepriceAllowed = abt.INAddedBenefitAllowed + abt.OONAddedBenefitAllowed
                   ,lkp.UtilUnitP1000Trend
                   ,lkp.UtilBenefitPlanChange
                   ,lkp.UtilPopulationChange
                   ,lkp.UtilOtherFactor
                   ,lkp.UCAProviderPaymentChange
                   ,lkp.UCAOtherFactor
        FROM        dbo.LkpIntMSBDetail msbd WITH (NOLOCK)
        LEFT JOIN   @bsm bsm
               ON bsm.PlanInfoID = msbd.PlanInfoID
                  AND   msbd.MSBCode = bsm.BaseMSBCode
        LEFT JOIN   dbo.LkpIntAddedBenefitType abt WITH (NOLOCK)
               ON abt.AddedBenefitTypeID = bsm.BidAddedBenefitTypeID
        LEFT JOIN   dbo.LkpIntAddedBenefitExpenseDetail lkp WITH (NOLOCK)
               ON lkp.AddedBenefitTypeID = bsm.BidAddedBenefitTypeID
        WHERE       msbd.PlanYearID = @BaseYear;




        /*output from first function with all factors and repriced values added*/

        DECLARE @claimswMSBdetail TABLE
            (MSBCode                                      VARCHAR(6)
            ,RepriceMSBCode                               VARCHAR(6)
            ,BenefitCategoryID                            SMALLINT
            ,RepriceUtilization                           DECIMAL(11, 2)
            ,RepriceAllowed								  DECIMAL(11, 2)
            ,UtilUnitP1000Trend                           DECIMAL(38, 18)
            ,UtilPopulationChange                         DECIMAL(38, 18)
            ,UtilOtherFactor                              DECIMAL(38, 18)
            ,UCAProviderPaymentChange                     DECIMAL(38, 18)
            ,UCAOtherFactor                               DECIMAL(38, 18)
            ,MARatingOptionID                             TINYINT
            ,PlanInfoID                                   INT
            ,DFVersionID                                  INT
            ,IncurredYear                                 INT
            ,DemogIndicator                               TINYINT
            ,[Provider]                                   VARCHAR(255)
            ,BidServiceCatID                              SMALLINT
            ,Paid                                         DECIMAL(19, 9)
            ,MbrCS                                        DECIMAL(19, 9)
            ,UnitCnt                                      DECIMAL(19, 9)
            ,EncounterMbrCS                               DECIMAL(19, 9)
            ,EncounterUnitCnt                             DECIMAL(19, 9)
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9)
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9)
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9)
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9)
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9)
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9)
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9)
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9)
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9)
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9)
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9)
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9)
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9)
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9)
            ,CapMSBs                                      DECIMAL(19, 9)
            ,CapProviderRewards                           DECIMAL(19, 9)
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9)
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9)
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9)
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9)
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9)
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9)
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9)
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9)
            ,PartDCapAdj                                  DECIMAL(19, 9)
            ,PartDCapAdjDelegated                         DECIMAL(19, 9)
            ,MSCapAdj                                     DECIMAL(19, 9)
            ,MSCapAdjDelegated                            DECIMAL(19, 9)
            ,SubCapAdj                                    DECIMAL(19, 9)
            ,SubCapAdjExclude                             DECIMAL(19, 9)
            ,MedicaidAdjPaid                              DECIMAL(19, 9)
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9)
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9)
            ,AdditiveAdjPaid                              DECIMAL(19, 9)
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9)
            ,AdditiveAdjUnits                             DECIMAL(19, 9)
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9)
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9)
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9)
            ,UCUnitsAdj                                   DECIMAL(19, 9)
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9)
            ,PartBRxRebatesQN                             DECIMAL(19, 9)
            ,RelatedPartiesAdj                            DECIMAL(19, 9)
            ,ProfitAdj                                    DECIMAL(19, 9)
            ,MSBPaid                                      DECIMAL(19, 9)
            ,MSBMbrCS                                     DECIMAL(19, 9)
            ,MSBUnits                                     DECIMAL(19, 9)
            ,MSBReductionCap                              DECIMAL(19, 9)
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9)
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9)
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9)
            ,MSBReductionQuality                          DECIMAL(19, 9)
            ,MemberMonths                                 INT);

        INSERT INTO @claimswMSBdetail
        SELECT      detail.MSBCode
                   ,detail.RepriceMSBCode
                   ,claims.BenefitCategoryID
                   ,detail.RepriceUtilization
                   ,detail.RepriceAllowed
                   ,detail.UtilUnitP1000Trend
                   ,detail.UtilPopulationChange
                   ,detail.UtilOtherFactor
                   ,detail.UCAProviderPaymentChange
                   ,detail.UCAOtherFactor
                   ,claims.MARatingOptionID
                   ,claims.PlanInfoID
                   ,claims.DFVersionID
                   ,claims.IncurredYear
                   ,claims.DemogIndicator
                   ,claims.PROVIDER
                   ,claims.BidServiceCatID
                   ,claims.Paid
                   ,claims.MbrCS
                   ,claims.UnitCnt
                   ,claims.EncounterMbrCS
                   ,claims.EncounterUnitCnt
                   ,claims.DelegatedEncounterMbrCS
                   ,claims.DelegatedEncounterUnitCnt
                   ,claims.CapDirectPayEPaidClaims
                   ,claims.CapDirectPayDEPaidClaims
                   ,claims.CapDirectPayBCAllocated
                   ,claims.CapDirectPayScenarioAllocated
                   ,claims.CapDirectPayBCAllocatedDelegated
                   ,claims.CapDirectPayScenarioAllocatedDelegated
                   ,claims.CapSurplusDeficitEPaidClaims
                   ,claims.CapSurplusDeficitDEPaidClaims
                   ,claims.CapSurplusDeficitBCAllocated
                   ,claims.CapSurplusDeficitScenarioAllocated
                   ,claims.CapSurplusDeficitBCAllocatedDelegated
                   ,claims.CapSurplusDeficitScenarioAllocatedDelegated
                   ,claims.CapMSBs
                   ,claims.CapProviderRewards
                   ,claims.OPBOtherNonHospitalBCAllocated
                   ,claims.OPBOtherNonHospitalScenarioAllocated
                   ,claims.OPBClaimsBuyDownBCAllocated
                   ,claims.OPBClaimsBuyDownScenarioAllocated
                   ,claims.OPBProviderClaimSettlementsBCAllocated
                   ,claims.OPBProviderClaimSettlementsScenarioAllocated
                   ,claims.OPBAccessFeesAndOtherBCAllocated
                   ,claims.OPBAccessFeesAndOtherScenarioAllocated
                   ,claims.PartDCapAdj
                   ,claims.PartDCapAdjDelegated
                   ,claims.MSCapAdj
                   ,claims.MSCapAdjDelegated
                   ,claims.SubCapAdj
                   ,claims.SubCapAdjExclude
                   ,claims.MedicaidAdjPaid
                   ,claims.MedicaidAdjMbrCS
                   ,claims.ImplicitMarginMbrCS
                   ,claims.AdditiveAdjPaid
                   ,claims.AdditiveAdjMbrCS
                   ,claims.AdditiveAdjUnits
                   ,claims.ModelOfCareAdjPaid
                   ,claims.ModelOfCareAdjMbrCS
                   ,claims.ModelOfCareAdjUnits
                   ,claims.UCUnitsAdj
                   ,claims.PartBRxRebatesPharmacy
                   ,claims.PartBRxRebatesQN
                   ,claims.RelatedPartiesAdj
                   ,claims.ProfitAdj
                   ,claims.MSBPaid
                   ,claims.MSBMbrCS
                   ,claims.MSBUnits
                   ,claims.MSBReductionCap
                   ,claims.MSBReductionClaimsPaid
                   ,claims.MSBReductionClaimsMbrCS
                   ,claims.MSBReductionClaimsUnits
                   ,claims.MSBReductionQuality
                   ,claims.MemberMonths
        FROM        @saveddfbaseclaimsmsbs claims
        LEFT JOIN   @msbdetailwithfactors detail
               ON detail.PlanInfoID = claims.PlanInfoID
                  AND   detail.BenefitCategoryID = claims.BenefitCategoryID;



        --grab base msbs from data
        DECLARE @grabbasemsb TABLE
            (MARatingOptionID   TINYINT
            ,PlanInfoID         INT
            ,BidServiceCatID    SMALLINT
            ,MSBCode            VARCHAR(6)
            ,RepriceUtilization DECIMAL(15, 6)
            ,RepriceAllowed		DECIMAL(15, 6));

        INSERT INTO @grabbasemsb
        SELECT  DISTINCT
                MARatingOptionID
               ,PlanInfoID
               ,BidServiceCatID
               ,MSBCode
               ,RepriceUtilization
               ,RepriceAllowed
        FROM    @claimswMSBdetail
        WHERE   MSBCode IS NOT NULL;

        --grab bid msbs from SavedPlanAddedBenefits
        DECLARE @grabbidmsb TABLE
            (MARatingOptionID TINYINT
            ,PlanInfoID       INT
            ,BidServiceCatID  SMALLINT
            ,MSBCode          VARCHAR(6)
            ,BidAllowed       DECIMAL(7, 2)
            ,BidUtilization   DECIMAL(15, 6));

        DECLARE @map TABLE
            (MARatingOptionID TINYINT
            ,PlanInfoID       INT);

        -- pulling all base plans IDs and rating options
        INSERT INTO @map
        SELECT  DISTINCT MARatingOptionID, PlanInfoID FROM  @grabbasemsb;

        INSERT INTO @grabbidmsb
        SELECT      DISTINCT
                    map.MARatingOptionID
                   ,map.PlanInfoID
                   ,spab.BidServiceCatID
                   ,LEFT(spab.AddedBenefitName, 6) AS MSBCode
                   ,BidAllowed = ISNULL (spab.INAddedBenefitAllowed, 0) /*+ ISNULL(spab.OONAddedBenefitAllowed,0)*/
                   ,BidUtilization = ISNULL (spab.INAddedBenefitUtilization, 0) + ISNULL (spab.OONAddedBenefitUtilization, 0)
        FROM        dbo.SavedPlanAddedBenefits spab WITH (NOLOCK)
       CROSS JOIN   @map map
        WHERE       spab.IsHidden = 0

                    AND spab.BidServiceCatID <> 35
                    AND spab.ForecastID = @XForecastID;

        DECLARE @baseclaimswithallMSBadded TABLE
            (BaseMSBCode                                  VARCHAR(6)
            ,BidMSBCode                                   VARCHAR(6)
            ,RepriceMSBCode                               VARCHAR(6)
            ,BenefitCategoryID                            SMALLINT
            ,BidAllowed                                   DECIMAL(7, 2)
            ,BidUtilization                               DECIMAL(15, 6)
            ,RepriceUtilization                           DECIMAL(11, 2)
            ,RepriceAllowed	                              DECIMAL(11, 2)
            ,Method                                       VARCHAR(8)
            ,UtilUnitP1000Trend                           DECIMAL(38, 18)
            ,UtilBenefitPlanChange                        DECIMAL(30, 15)
            ,UtilPopulationChange                         DECIMAL(38, 18)
            ,UtilOtherFactor                              DECIMAL(38, 18)
            ,UCAProviderPaymentChange                     DECIMAL(38, 18)
            ,UCAOtherFactor                               DECIMAL(38, 18)
            ,BaseCalculatedAllowed                        DECIMAL(19, 9)
            ,BaseCalculatedUtilization                    DECIMAL(19, 9)
            ,MARatingOptionID                             TINYINT
            ,PlanInfoID                                   INT
            ,DFVersionID                                  INT
            ,IncurredYear                                 INT
            ,DemogIndicator                               TINYINT
            ,[Provider]                                   VARCHAR(255)
            ,BidServiceCatID                              SMALLINT
            ,Paid                                         DECIMAL(19, 9)
            ,MbrCS                                        DECIMAL(19, 9)
            ,UnitCnt                                      DECIMAL(19, 9)
            ,EncounterMbrCS                               DECIMAL(19, 9)
            ,EncounterUnitCnt                             DECIMAL(19, 9)
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9)
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9)
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9)
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9)
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9)
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9)
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9)
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9)
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9)
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9)
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9)
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9)
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9)
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9)
            ,CapMSBs                                      DECIMAL(19, 9)
            ,CapProviderRewards                           DECIMAL(19, 9)
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9)
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9)
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9)
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9)
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9)
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9)
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9)
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9)
            ,PartDCapAdj                                  DECIMAL(19, 9)
            ,PartDCapAdjDelegated                         DECIMAL(19, 9)
            ,MSCapAdj                                     DECIMAL(19, 9)
            ,MSCapAdjDelegated                            DECIMAL(19, 9)
            ,SubCapAdj                                    DECIMAL(19, 9)
            ,SubCapAdjExclude                             DECIMAL(19, 9)
            ,MedicaidAdjPaid                              DECIMAL(19, 9)
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9)
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9)
            ,AdditiveAdjPaid                              DECIMAL(19, 9)
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9)
            ,AdditiveAdjUnits                             DECIMAL(19, 9)
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9)
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9)
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9)
            ,UCUnitsAdj                                   DECIMAL(19, 9)
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9)
            ,PartBRxRebatesQN                             DECIMAL(19, 9)
            ,RelatedPartiesAdj                            DECIMAL(19, 9)
            ,ProfitAdj                                    DECIMAL(19, 9)
            ,MSBPaid                                      DECIMAL(19, 9)
            ,MSBMbrCS                                     DECIMAL(19, 9)
            ,MSBUnits                                     DECIMAL(19, 9)
            ,MSBReductionCap                              DECIMAL(19, 9)
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9)
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9)
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9)
            ,MSBReductionQuality                          DECIMAL(19, 9)
            ,MemberMonths                                 INT);

        DECLARE @bid TABLE
            (MARatingOptionID TINYINT
            ,PlanInfoID       INT
            ,BidServiceCatID  SMALLINT
            ,MSBCode          VARCHAR(6)
            ,BidAllowed       DECIMAL(7, 2)
            ,BidUtilization   DECIMAL(15, 6)
            ,bid_rank         TINYINT);

        -- bid MSBs, ranked
        INSERT INTO @bid
        SELECT  MARatingOptionID
               ,PlanInfoID
               ,BidServiceCatID
               ,MSBCode
               ,BidAllowed
               ,BidUtilization
               ,ROW_NUMBER () OVER (PARTITION BY MARatingOptionID
                                                ,PlanInfoID
                                                ,BidServiceCatID
                                                ,LEFT(MSBCode, 3)
                                    ORDER BY MSBCode
                                            ,BidUtilization) AS bid_rank
        FROM    @grabbidmsb;

        -- base MSBs, ranked
        DECLARE @base TABLE
            (MARatingOptionID   TINYINT
            ,PlanInfoID         INT
            ,BidServiceCatID    SMALLINT
            ,MSBCode            VARCHAR(6)
            ,RepriceUtilization DECIMAL(15, 6)
            ,RepriceAllowed		DECIMAL(15, 6)
            ,base_rank          TINYINT);

        INSERT INTO @base
        SELECT  MARatingOptionID
               ,PlanInfoID
               ,BidServiceCatID
               ,MSBCode
               ,RepriceUtilization
               ,RepriceAllowed
               ,ROW_NUMBER () OVER (PARTITION BY MARatingOptionID
                                                ,PlanInfoID
                                                ,BidServiceCatID
                                                ,LEFT(MSBCode, 3)
                                    ORDER BY MSBCode
                                            ,RepriceUtilization) AS base_rank
        FROM    @grabbasemsb;

        --Map base and bid MSBs to determine existing/added/removed
        DECLARE @basetobid TABLE
            (MARatingOptionID TINYINT
            ,PlanInfoID       INT
            ,BidServiceCatID  SMALLINT
            ,BaseMSBCode      VARCHAR(6)
            ,BidMSBCode       VARCHAR(6)
            ,BidAllowed       DECIMAL(7, 2)
            ,BidUtilization   DECIMAL(15, 6));

        INSERT INTO @basetobid
        SELECT          MARatingOptionID = COALESCE (base.MARatingOptionID, bid.MARatingOptionID)
                       ,PlanInfoID = COALESCE (base.PlanInfoID, bid.PlanInfoID)
                       ,BidServiceCatID = COALESCE (base.BidServiceCatID, bid.BidServiceCatID)
                       ,BaseMSBCode = base.MSBCode
                       ,BidMSBCode = bid.MSBCode
                       ,bid.BidAllowed
                       ,bid.BidUtilization
        FROM            @bid bid
        FULL OUTER JOIN @base base
                     ON base.MARatingOptionID = bid.MARatingOptionID
                        AND base.PlanInfoID = bid.PlanInfoID
                        AND base.BidServiceCatID = bid.BidServiceCatID
                        AND LEFT(base.MSBCode, 3) = LEFT(bid.MSBCode, 3)
                        AND base.base_rank = bid.bid_rank;

        DECLARE @new TABLE
            (MARatingOptionID TINYINT
            ,PlanInfoID       INT
            ,DFVersionID      INT
            ,IncurredYear     INT
            ,DemogIndicator   TINYINT
            ,[PROVIDER]       VARCHAR(255)
            ,MemberMonths     INT
            ,BidMSBCode       VARCHAR(6)
            ,BidServiceCatID  SMALLINT
            ,BidAllowed       DECIMAL(7, 2)
            ,BidUtilization   DECIMAL(15, 6));

        --Grab 'added' bid MSB codes, duplicate rows for each planinfoID and demogindicator, add these rows to the main table
        INSERT INTO @new
        SELECT      DISTINCT
                    claim.MARatingOptionID
                   ,claim.PlanInfoID
                   ,claim.DFVersionID
                   ,claim.IncurredYear
                   ,claim.DemogIndicator
                   ,claim.PROVIDER
                   ,claim.MemberMonths
                   ,msb.BidMSBCode
                   ,msb.BidServiceCatID
                   ,msb.BidAllowed
                   ,msb.BidUtilization
        FROM        @saveddfbaseclaimsmsbs claim
       RIGHT JOIN   @basetobid msb
               ON msb.MARatingOptionID = claim.MARatingOptionID
                  AND   msb.PlanInfoID = claim.PlanInfoID
                  AND   msb.BidServiceCatID = claim.BidServiceCatID
        WHERE       msb.BaseMSBCode IS NULL;

        INSERT INTO @baseclaimswithallMSBadded
        SELECT          claim.MSBCode AS BaseMSBCode
                       ,COALESCE (b2b.BidMSBCode, new.BidMSBCode) AS BidMSBCode
                       ,claim.RepriceMSBCode AS RepriceMSBCode
                       ,claim.BenefitCategoryID
                       ,BidAllowed = COALESCE (b2b.BidAllowed, new.BidAllowed)
                       ,BidUtilization = COALESCE (b2b.BidUtilization, new.BidUtilization)
                       ,ISNULL (claim.RepriceUtilization, 0) AS RepriceUtilization
                       ,ISNULL (claim.RepriceAllowed, 0) AS RepriceAllowed
                       ,CASE WHEN claim.MSBCode IS NOT NULL
                                  AND   b2b.BidMSBCode IS NOT NULL THEN @Existing --'Existing'
                             WHEN claim.MSBCode IS NOT NULL
                                  AND   b2b.BidMSBCode IS NULL THEN @Removed
                             WHEN claim.MSBCode IS NULL
                                  AND   new.BidMSBCode IS NOT NULL THEN @Added
                             ELSE NULL END AS Method
                       ,UtilUnitP1000Trend = ISNULL (claim.UtilUnitP1000Trend, 1)
                       ,UtilBenefitPlanChange = ISNULL (
                                                CASE WHEN b2b.BidMSBCode <> claim.MSBCode THEN
                                                         dbo.fnGetSafeDivisionResultReturnOne (
                                                         b2b.BidUtilization, claim.RepriceUtilization)
                                                     ELSE 1 END
                                               ,1)
                       ,UtilPopulationChange = ISNULL (claim.UtilPopulationChange, 1)
                       ,UtilOtherFactor = ISNULL (claim.UtilOtherFactor, 1)
                       ,UCAProviderPaymentChange = ISNULL (claim.UCAProviderPaymentChange, 1)
                       ,UCAOtherFactor = ISNULL (claim.UCAOtherFactor, 1)
                       ,BaseCalculatedAllowed = ISNULL (
                                                (Paid + CapDirectPayEPaidClaims + CapDirectPayDEPaidClaims
                                                 + CapDirectPayBCAllocated + CapDirectPayScenarioAllocated
                                                 + CapDirectPayBCAllocatedDelegated + CapDirectPayScenarioAllocatedDelegated
                                                 + CapSurplusDeficitEPaidClaims + CapSurplusDeficitDEPaidClaims
                                                 + CapSurplusDeficitBCAllocated + CapSurplusDeficitScenarioAllocated
                                                 + CapSurplusDeficitBCAllocatedDelegated
                                               + CapSurplusDeficitScenarioAllocatedDelegated + CapMSBs + CapProviderRewards
                                                 + OPBOtherNonHospitalBCAllocated + OPBOtherNonHospitalScenarioAllocated
                                                 + OPBClaimsBuyDownBCAllocated + OPBClaimsBuyDownScenarioAllocated
                                                 + OPBProviderClaimSettlementsBCAllocated
                                                 + OPBProviderClaimSettlementsScenarioAllocated
                                                 + OPBAccessFeesAndOtherBCAllocated + OPBAccessFeesAndOtherScenarioAllocated
                                                 + PartDCapAdj + PartDCapAdjDelegated + MSCapAdj + MSCapAdjDelegated
                                                 + SubCapAdj + SubCapAdjExclude + MedicaidAdjPaid + AdditiveAdjPaid
                                                 + ModelOfCareAdjPaid + PartBRxRebatesPharmacy + PartBRxRebatesQN
                                                 + RelatedPartiesAdj + ProfitAdj + MSBPaid + MSBReductionCap
                                                 + MSBReductionClaimsPaid + MSBReductionQuality + MbrCS + EncounterMbrCS
                                                 + DelegatedEncounterMbrCS + MedicaidAdjMbrCS + AdditiveAdjMbrCS
                                                 + ModelOfCareAdjMbrCS + MSBMbrCS + MSBReductionClaimsMbrCS)
                                               ,0)
                       ,BaseCalculatedUtilization = ISNULL (
                                                    (UnitCnt + EncounterUnitCnt + DelegatedEncounterUnitCnt + AdditiveAdjUnits
                                                     + ModelOfCareAdjUnits + UCUnitsAdj + MSBUnits + MSBReductionClaimsUnits)
                                                   ,0)
                       ,COALESCE (claim.MARatingOptionID, new.MARatingOptionID) AS MARatingOptionID
                       ,COALESCE (claim.PlanInfoID, new.PlanInfoID) AS PlanInfoID
                       ,COALESCE (claim.DFVersionID, new.DFVersionID) AS DFVersionID
                       ,COALESCE (claim.IncurredYear, new.IncurredYear) AS IncurredYear
                       ,COALESCE (claim.DemogIndicator, new.DemogIndicator) AS DemogIndicator
                       ,COALESCE (claim.Provider, new.PROVIDER) AS Provider
                       ,COALESCE (claim.BidServiceCatID, new.BidServiceCatID) AS BidServiceCatID
                       ,Paid = ISNULL (claim.Paid, 0)
                       ,MbrCS = ISNULL (claim.MbrCS, 0)
                       ,UnitCnt = ISNULL (claim.UnitCnt, 0)
                       ,EncounterMbrCS = ISNULL (claim.EncounterMbrCS, 0)
                       ,EncounterUnitCnt = ISNULL (claim.EncounterUnitCnt, 0)
                       ,DelegatedEncounterMbrCS = ISNULL (claim.DelegatedEncounterMbrCS, 0)
                       ,DelegatedEncounterUnitCnt = ISNULL (claim.DelegatedEncounterUnitCnt, 0)
                       ,CapDirectPayEPaidClaims = ISNULL (claim.CapDirectPayEPaidClaims, 0)
                       ,CapDirectPayDEPaidClaims = ISNULL (claim.CapDirectPayDEPaidClaims, 0)
                       ,CapDirectPayBCAllocated = ISNULL (claim.CapDirectPayBCAllocated, 0)
                       ,CapDirectPayScenarioAllocated = ISNULL (claim.CapDirectPayScenarioAllocated, 0)
                       ,CapDirectPayBCAllocatedDelegated = ISNULL (claim.CapDirectPayBCAllocatedDelegated, 0)
                       ,CapDirectPayScenarioAllocatedDelegated = ISNULL (claim.CapDirectPayScenarioAllocatedDelegated, 0)
                       ,CapSurplusDeficitEPaidClaims = ISNULL (claim.CapSurplusDeficitEPaidClaims, 0)
                       ,CapSurplusDeficitDEPaidClaims = ISNULL (claim.CapSurplusDeficitDEPaidClaims, 0)
                       ,CapSurplusDeficitBCAllocated = ISNULL (claim.CapSurplusDeficitBCAllocated, 0)
        ,CapSurplusDeficitScenarioAllocated = ISNULL (claim.CapSurplusDeficitScenarioAllocated, 0)
                       ,CapSurplusDeficitBCAllocatedDelegated = ISNULL (claim.CapSurplusDeficitBCAllocatedDelegated, 0)
                       ,CapSurplusDeficitScenarioAllocatedDelegated = ISNULL (
                                                                      claim.CapSurplusDeficitScenarioAllocatedDelegated, 0)
                       ,CapMSBs = ISNULL (claim.CapMSBs, 0)
                       ,CapProviderRewards = ISNULL (claim.CapProviderRewards, 0)
                       ,OPBOtherNonHospitalBCAllocated = ISNULL (claim.OPBOtherNonHospitalBCAllocated, 0)
                       ,OPBOtherNonHospitalScenarioAllocated = ISNULL (claim.OPBOtherNonHospitalScenarioAllocated, 0)
                       ,OPBClaimsBuyDownBCAllocated = ISNULL (claim.OPBClaimsBuyDownBCAllocated, 0)
                       ,OPBClaimsBuyDownScenarioAllocated = ISNULL (claim.OPBClaimsBuyDownScenarioAllocated, 0)
                       ,OPBProviderClaimSettlementsBCAllocated = ISNULL (claim.OPBProviderClaimSettlementsBCAllocated, 0)
                       ,OPBProviderClaimSettlementsScenarioAllocated = ISNULL (
                                                                       claim.OPBProviderClaimSettlementsScenarioAllocated, 0)
                       ,OPBAccessFeesAndOtherBCAllocated = ISNULL (claim.OPBAccessFeesAndOtherBCAllocated, 0)
                       ,OPBAccessFeesAndOtherScenarioAllocated = ISNULL (claim.OPBAccessFeesAndOtherScenarioAllocated, 0)
                       ,PartDCapAdj = ISNULL (claim.PartDCapAdj, 0)
                       ,PartDCapAdjDelegated = ISNULL (claim.PartDCapAdjDelegated, 0)
                       ,MSCapAdj = ISNULL (claim.MSCapAdj, 0)
                       ,MSCapAdjDelegated = ISNULL (claim.MSCapAdjDelegated, 0)
                       ,SubCapAdj = ISNULL (claim.SubCapAdj, 0)
                       ,SubCapAdjExclude = ISNULL (claim.SubCapAdjExclude, 0)
                       ,MedicaidAdjPaid = ISNULL (claim.MedicaidAdjPaid, 0)
                       ,MedicaidAdjMbrCS = ISNULL (claim.MedicaidAdjMbrCS, 0)
                       ,ImplicitMarginMbrCS = ISNULL (claim.ImplicitMarginMbrCS, 0)
                       ,AdditiveAdjPaid = ISNULL (claim.AdditiveAdjPaid, 0)
                       ,AdditiveAdjMbrCS = ISNULL (claim.AdditiveAdjMbrCS, 0)
                       ,AdditiveAdjUnits = ISNULL (claim.AdditiveAdjUnits, 0)
                       ,ModelOfCareAdjPaid = ISNULL (claim.ModelOfCareAdjPaid, 0)
                       ,ModelOfCareAdjMbrCS = ISNULL (claim.ModelOfCareAdjMbrCS, 0)
                       ,ModelOfCareAdjUnits = ISNULL (claim.ModelOfCareAdjUnits, 0)
                       ,UCUnitsAdj = ISNULL (claim.UCUnitsAdj, 0)
                       ,PartBRxRebatesPharmacy = ISNULL (claim.PartBRxRebatesPharmacy, 0)
                       ,PartBRxRebatesQN = ISNULL (claim.PartBRxRebatesQN, 0)
                       ,RelatedPartiesAdj = ISNULL (claim.RelatedPartiesAdj, 0)
                       ,ProfitAdj = ISNULL (claim.ProfitAdj, 0)
                       ,MSBPaid = ISNULL (claim.MSBPaid, 0)
                       ,MSBMbrCS = ISNULL (claim.MSBMbrCS, 0)
                       ,MSBUnits = ISNULL (claim.MSBUnits, 0)
                       ,MSBReductionCap = ISNULL (claim.MSBReductionCap, 0)
                       ,MSBReductionClaimsPaid = ISNULL (claim.MSBReductionClaimsPaid, 0)
                       ,MSBReductionClaimsMbrCS = ISNULL (claim.MSBReductionClaimsMbrCS, 0)
                       ,MSBReductionClaimsUnits = ISNULL (claim.MSBReductionClaimsUnits, 0)
                       ,MSBReductionQuality = ISNULL (claim.MSBReductionQuality, 0)
                       ,MemberMonths = ISNULL (claim.MemberMonths, new.MemberMonths)
        FROM            @claimswMSBdetail claim --Base claims with Base MSB detail+factors+projected cost/use
        LEFT JOIN       @basetobid b2b
         ON b2b.MARatingOptionID = claim.MARatingOptionID --Base to Bid msb code mapping for existing or removed benefits
                  AND   b2b.PlanInfoID = claim.PlanInfoID
                  AND   b2b.BaseMSBCode = claim.MSBCode
                  AND   b2b.BidMSBCode IS NOT NULL
        FULL OUTER JOIN @new new
                     ON new.MARatingOptionID = claim.MARatingOptionID
                        AND new.PlanInfoID = claim.PlanInfoID
                        AND new.DFVersionID = claim.DFVersionID
                        AND new.IncurredYear = claim.IncurredYear
                        AND new.DemogIndicator = claim.DemogIndicator
                        AND new.PROVIDER = claim.Provider
                        AND new.BidServiceCatID = claim.BidServiceCatID
                        AND claim.MSBCode IS NULL --11/11 modification
                        AND new.BidMSBCode = b2b.BidMSBCode;

        -- Modifying Method to change 'Existing' to 'Special'
        DECLARE @baseclaimswithallMSBs TABLE
            (BaseMSBCode                                  VARCHAR(6)
            ,BidMSBCode                                   VARCHAR(6)
            ,RepriceMSBCode                               VARCHAR(6)
            ,BenefitCategoryID                            SMALLINT
            ,BidAllowed                                   DECIMAL(7, 2)
            ,BidUtilization                               DECIMAL(15, 6)
            ,RepriceUtilization                           DECIMAL(11, 2)
            ,RepriceAllowed								  DECIMAL(11, 2)
            ,Method                                       VARCHAR(8)
            ,UtilUnitP1000Trend                           DECIMAL(38, 18)
            ,UtilBenefitPlanChange                        DECIMAL(30, 15)
            ,UtilPopulationChange                         DECIMAL(38, 18)
            ,UtilOtherFactor                              DECIMAL(38, 18)
            ,UCAProviderPaymentChange                     DECIMAL(38, 18)
            ,UCAOtherFactor                               DECIMAL(38, 18)
            ,BaseCalculatedAllowed                        DECIMAL(19, 9)
            ,BaseCalculatedUtilization                    DECIMAL(19, 9)
            ,MARatingOptionID                             TINYINT
            ,PlanInfoID                                   INT
            ,DFVersionID                                  INT
            ,IncurredYear                                 INT
            ,DemogIndicator                               TINYINT
            ,[Provider]                                   VARCHAR(255)
            ,BidServiceCatID                              SMALLINT
            ,Paid                                         DECIMAL(19, 9)
            ,MbrCS                                        DECIMAL(19, 9)
            ,UnitCnt                                      DECIMAL(19, 9)
            ,EncounterMbrCS                               DECIMAL(19, 9)
            ,EncounterUnitCnt                             DECIMAL(19, 9)
            ,DelegatedEncounterMbrCS                      DECIMAL(19, 9)
            ,DelegatedEncounterUnitCnt                    DECIMAL(19, 9)
            ,CapDirectPayEPaidClaims                      DECIMAL(19, 9)
            ,CapDirectPayDEPaidClaims                     DECIMAL(19, 9)
            ,CapDirectPayBCAllocated                      DECIMAL(19, 9)
            ,CapDirectPayScenarioAllocated                DECIMAL(19, 9)
            ,CapDirectPayBCAllocatedDelegated             DECIMAL(19, 9)
            ,CapDirectPayScenarioAllocatedDelegated       DECIMAL(19, 9)
            ,CapSurplusDeficitEPaidClaims                 DECIMAL(19, 9)
            ,CapSurplusDeficitDEPaidClaims                DECIMAL(19, 9)
            ,CapSurplusDeficitBCAllocated                 DECIMAL(19, 9)
            ,CapSurplusDeficitScenarioAllocated           DECIMAL(19, 9)
            ,CapSurplusDeficitBCAllocatedDelegated        DECIMAL(19, 9)
            ,CapSurplusDeficitScenarioAllocatedDelegated  DECIMAL(19, 9)
            ,CapMSBs                                      DECIMAL(19, 9)
            ,CapProviderRewards                           DECIMAL(19, 9)
            ,OPBOtherNonHospitalBCAllocated               DECIMAL(19, 9)
            ,OPBOtherNonHospitalScenarioAllocated         DECIMAL(19, 9)
            ,OPBClaimsBuyDownBCAllocated                  DECIMAL(19, 9)
            ,OPBClaimsBuyDownScenarioAllocated            DECIMAL(19, 9)
            ,OPBProviderClaimSettlementsBCAllocated       DECIMAL(19, 9)
            ,OPBProviderClaimSettlementsScenarioAllocated DECIMAL(19, 9)
            ,OPBAccessFeesAndOtherBCAllocated             DECIMAL(19, 9)
            ,OPBAccessFeesAndOtherScenarioAllocated       DECIMAL(19, 9)
            ,PartDCapAdj                                  DECIMAL(19, 9)
            ,PartDCapAdjDelegated                         DECIMAL(19, 9)
            ,MSCapAdj                                     DECIMAL(19, 9)
            ,MSCapAdjDelegated                            DECIMAL(19, 9)
            ,SubCapAdj                                    DECIMAL(19, 9)
            ,SubCapAdjExclude                             DECIMAL(19, 9)
            ,MedicaidAdjPaid                              DECIMAL(19, 9)
            ,MedicaidAdjMbrCS                             DECIMAL(19, 9)
            ,ImplicitMarginMbrCS                          DECIMAL(19, 9)
            ,AdditiveAdjPaid                              DECIMAL(19, 9)
            ,AdditiveAdjMbrCS                             DECIMAL(19, 9)
            ,AdditiveAdjUnits                             DECIMAL(19, 9)
            ,ModelOfCareAdjPaid                           DECIMAL(19, 9)
            ,ModelOfCareAdjMbrCS                          DECIMAL(19, 9)
            ,ModelOfCareAdjUnits                          DECIMAL(19, 9)
            ,UCUnitsAdj                                   DECIMAL(19, 9)
            ,PartBRxRebatesPharmacy                       DECIMAL(19, 9)
            ,PartBRxRebatesQN                             DECIMAL(19, 9)
            ,RelatedPartiesAdj                            DECIMAL(19, 9)
            ,ProfitAdj                                    DECIMAL(19, 9)
            ,MSBPaid                                      DECIMAL(19, 9)
            ,MSBMbrCS                                     DECIMAL(19, 9)
            ,MSBUnits                                     DECIMAL(19, 9)
            ,MSBReductionCap                              DECIMAL(19, 9)
            ,MSBReductionClaimsPaid                       DECIMAL(19, 9)
            ,MSBReductionClaimsMbrCS                      DECIMAL(19, 9)
            ,MSBReductionClaimsUnits                      DECIMAL(19, 9)
            ,MSBReductionQuality                          DECIMAL(19, 9)
            ,MemberMonths                                 INT);

        INSERT INTO @baseclaimswithallMSBs
        SELECT  BaseMSBCode
               ,BidMSBCode
               ,RepriceMSBCode
               ,BenefitCategoryID
               ,BidAllowed
               ,BidUtilization
               ,RepriceUtilization
               ,RepriceAllowed
               ,CASE WHEN BidUtilization = 0
                          AND   BidUtilization <> BaseCalculatedUtilization THEN @Special
                     WHEN BaseCalculatedUtilization = 0
                          AND   BidUtilization <> BaseCalculatedUtilization THEN @Special
                     WHEN BidAllowed = 0
                          AND   BidAllowed <> BaseCalculatedAllowed THEN @Special
                     WHEN BaseCalculatedAllowed = 0
                          AND   BidAllowed <> BaseCalculatedAllowed THEN @Special
                     ELSE Method END AS Method
               ,UtilUnitP1000Trend
               ,UtilBenefitPlanChange
               ,UtilPopulationChange
               ,UtilOtherFactor
               ,UCAProviderPaymentChange
               ,UCAOtherFactor
               ,BaseCalculatedAllowed
               ,BaseCalculatedUtilization
               ,MARatingOptionID
               ,PlanInfoID
               ,DFVersionID
               ,IncurredYear
               ,DemogIndicator
               ,Provider
               ,BidServiceCatID
               ,Paid
               ,MbrCS
               ,UnitCnt
               ,EncounterMbrCS
               ,EncounterUnitCnt
               ,DelegatedEncounterMbrCS
               ,DelegatedEncounterUnitCnt
               ,CapDirectPayEPaidClaims
               ,CapDirectPayDEPaidClaims
               ,CapDirectPayBCAllocated
               ,CapDirectPayScenarioAllocated
               ,CapDirectPayBCAllocatedDelegated
               ,CapDirectPayScenarioAllocatedDelegated
               ,CapSurplusDeficitEPaidClaims
               ,CapSurplusDeficitDEPaidClaims
               ,CapSurplusDeficitBCAllocated
               ,CapSurplusDeficitScenarioAllocated
               ,CapSurplusDeficitBCAllocatedDelegated
               ,CapSurplusDeficitScenarioAllocatedDelegated
               ,CapMSBs
               ,CapProviderRewards
               ,OPBOtherNonHospitalBCAllocated
               ,OPBOtherNonHospitalScenarioAllocated
               ,OPBClaimsBuyDownBCAllocated
               ,OPBClaimsBuyDownScenarioAllocated
               ,OPBProviderClaimSettlementsBCAllocated
               ,OPBProviderClaimSettlementsScenarioAllocated
               ,OPBAccessFeesAndOtherBCAllocated
               ,OPBAccessFeesAndOtherScenarioAllocated
               ,PartDCapAdj
               ,PartDCapAdjDelegated
               ,MSCapAdj
               ,MSCapAdjDelegated
               ,SubCapAdj
               ,SubCapAdjExclude
               ,MedicaidAdjPaid
               ,MedicaidAdjMbrCS
               ,ImplicitMarginMbrCS
               ,AdditiveAdjPaid
               ,AdditiveAdjMbrCS
               ,AdditiveAdjUnits
               ,ModelOfCareAdjPaid
               ,ModelOfCareAdjMbrCS
               ,ModelOfCareAdjUnits
               ,UCUnitsAdj
               ,PartBRxRebatesPharmacy
               ,PartBRxRebatesQN
               ,RelatedPartiesAdj
               ,ProfitAdj
               ,MSBPaid
               ,MSBMbrCS
               ,MSBUnits
               ,MSBReductionCap
               ,MSBReductionClaimsPaid
               ,MSBReductionClaimsMbrCS
               ,MSBReductionClaimsUnits
               ,MSBReductionQuality
               ,MemberMonths
        FROM    @baseclaimswithallMSBadded;

        INSERT INTO @OutputTable

        --Modify multiplicative and additive adjustments depending on 'Method'
        SELECT  PlanInfoID
               ,IncurredYear
               ,MARatingOptionID
               ,DFVersionID
               ,DemogIndicator
               ,Method
               ,BaseMSBCode
               ,BidMSBCode
               ,RepriceMSBCode
               ,BenefitCategoryID
               ,BidServiceCatID
               ,BidAllowed
               ,BidUtilization
               ,RepriceUtilization
               ,RepriceAllowed
               ,BaseCalculatedAllowed
               ,BaseCalculatedUtilization
               ,AdditiveCost = CASE WHEN Method IN (@Added, @Removed, @Special) THEN
                                        ISNULL (BidAllowed, 0) - ISNULL (BaseCalculatedAllowed, 0)
                                    ELSE 0 END
               ,AdditiveUtil = CASE WHEN Method IN (@Added, @Removed, @Special) THEN
                                        ISNULL (BidUtilization, 0) - ISNULL (BaseCalculatedUtilization, 0)
                                    ELSE 0 END
               ,UtilUnitP1000Trend = CASE WHEN Method = @Existing THEN UtilUnitP1000Trend ELSE 1 END
               ,UtilBenefitPlanChange = CASE WHEN Method = @Existing THEN UtilBenefitPlanChange ELSE 1 END
               ,UtilPopulationChange = CASE WHEN Method = @Existing THEN UtilPopulationChange ELSE 1 END
               ,UtilOtherFactor = CASE WHEN Method = @Existing THEN UtilOtherFactor ELSE 1 END
               ,UCAProviderPaymentChange = CASE WHEN Method = @Existing THEN UCAProviderPaymentChange ELSE 1 END
               ,UCAOtherFactor = CASE WHEN Method = @Existing THEN UCAOtherFactor ELSE 1 END
               ,Paid
               ,MbrCS
               ,UnitCnt
               ,EncounterMbrCS
               ,EncounterUnitCnt
               ,DelegatedEncounterMbrCS
               ,DelegatedEncounterUnitCnt
               ,CapDirectPayEPaidClaims
               ,CapDirectPayDEPaidClaims
               ,CapDirectPayBCAllocated
               ,CapDirectPayScenarioAllocated
               ,CapDirectPayBCAllocatedDelegated
               ,CapDirectPayScenarioAllocatedDelegated
               ,CapSurplusDeficitEPaidClaims
               ,CapSurplusDeficitDEPaidClaims
               ,CapSurplusDeficitBCAllocated
               ,CapSurplusDeficitScenarioAllocated
               ,CapSurplusDeficitBCAllocatedDelegated
               ,CapSurplusDeficitScenarioAllocatedDelegated
               ,CapMSBs
               ,CapProviderRewards
               ,OPBOtherNonHospitalBCAllocated
               ,OPBOtherNonHospitalScenarioAllocated
               ,OPBClaimsBuyDownBCAllocated
               ,OPBClaimsBuyDownScenarioAllocated
               ,OPBProviderClaimSettlementsBCAllocated
               ,OPBProviderClaimSettlementsScenarioAllocated
               ,OPBAccessFeesAndOtherBCAllocated
               ,OPBAccessFeesAndOtherScenarioAllocated
               ,PartDCapAdj
               ,PartDCapAdjDelegated
               ,MSCapAdj
               ,MSCapAdjDelegated
               ,SubCapAdj
               ,SubCapAdjExclude
               ,MedicaidAdjPaid
               ,MedicaidAdjMbrCS
               ,ImplicitMarginMbrCS
               ,AdditiveAdjPaid
               ,AdditiveAdjMbrCS
               ,AdditiveAdjUnits
               ,ModelOfCareAdjPaid
               ,ModelOfCareAdjMbrCS
               ,ModelOfCareAdjUnits
               ,UCUnitsAdj
               ,PartBRxRebatesPharmacy
               ,PartBRxRebatesQN
               ,RelatedPartiesAdj
               ,ProfitAdj
               ,MSBPaid
               ,MSBMbrCS
               ,MSBUnits
               ,MSBReductionCap
               ,MSBReductionClaimsPaid
               ,MSBReductionClaimsMbrCS
               ,MSBReductionClaimsUnits
               ,MSBReductionQuality
               ,MemberMonths
        FROM    @baseclaimswithallMSBs;

        RETURN;
    END;
GO