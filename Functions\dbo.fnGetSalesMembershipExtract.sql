SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetSalesMembershipExtract] ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetSalesMembershipExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-14
-- HEADER UPDATED: 2011-Dec-15
--
-- DESCRIPTION: Designed to extract fields for Sales membership - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	SavedPlanAssumptions
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-14		1			Initial Version														Alex Rezmerski
-- 2011-Dec-15		2			Updated for coding standards.  Added Case statement to account		Craig Wright
--									for @WhereIn being Null. Changed SalesMembership to return
--									Decimal(18,0)
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetSalesMembershipExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(
    ForecastID [int] NOT NULL,
	SalesMembership DECIMAL(20,10) 
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT  ForecastID,
					SalesMembership 
			FROM SavedPlanAssumptions 
       
	ELSE
		INSERT @Results
			SELECT  ForecastID,
					SalesMembership 
			FROM SavedPlanAssumptions 
			WHERE ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))

RETURN
END



GO
