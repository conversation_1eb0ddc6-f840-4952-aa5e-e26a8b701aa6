-- Stored Procedure

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUpdateOrInsertAddedBenefits
--
-- CREATOR: Christian Cofie
--
-- CREATED DATE:Mar-03-2007
--
-- DESCRIPTION: Stored Procedure responsible for saving  Added Benefits in the SPAct Model
-- PARAMETERS:
--  Input  : 
--        @IsNew bit,
--        @ForecastID int,
--        @AddedBenefitTypeID smallint,
--        @AddedBenefitName varchar(50),
--        @INAddedBenefitDescription varchar(50),
--        @INAddedBenefitAllowed float,
--        @INAddedBenefitUtilization float,
--        @INAddedBenefitCostShare float,
--        @OONAddedBenefitDescription varchar(50),
--        @OONAddedBenefitAllowed float,
--        @OONAddedBenefitUtilization float,
--        @OONAddedBenefitCostShare float,
--        @IsValueAdded bit,
--        @BidServiceCategoryID smallint,
--        @UserID CHAR(7),
--        @IsNetwork bit=0
--  Output : NONE
--
-- TABLES : Read :  SavedPlanAddedBenefits
--                  LkpIntPlanYear
--          Write: SavedPlanAddedBenefits 
--
-- VIEWS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- Mar-03-2007      1           Initial Version                                                     Christian Cofie
-- Nov-27-2007      2           Included IsHidden, LastUpdateByID                                   Christian Cofie
-- Dec-17-2007      3           Restructured procedure                                              Christian Cofie
-- Mar-04-2008      4           Coding Standards                                                    Brian Lake
-- Mar-10-2008      5           Removed check for IsNew = 0 under the exists check                  Brian Lake
-- 2009-Sep-19      6           Added @UserID to the list of parameters and replaced all            Shannon Boykin
--                              occurrences of SUSER_SNAME with @UserID.    
-- 2010-Sep-30      7           Revised for 2012 database, removed PlanVersion*                     Jake Gaecke
-- 2011-Jun-02		8			Replaced LkpIntPlanYear with dbo.fnGetBidYear()						Bobby Jaegers
-- 2011-Jun-14		9			Changed @PlanYearID to return SMALLINT instead of INT				Bobby Jaegers
-- 2019-Oct-30	    10          Replace @UserID from char(13) to char(7)							Chhavi Sinha
-- 2021-Nov-15		11			Changed @AddedBenefitTypeID to INT								    Umaprasad shetpally
-- 2024-MAR-19		12			Updated Benefit Description columns to use more	characters			Alex Brandt
-- 2024-MAR-19		13			Updated Benefit Description columns to use more	characters			Adam Gilbert
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE  PROCEDURE [dbo].[spUpdateOrInsertAddedBenefits]
    @IsNew bit,
    @ForecastID int,
    @AddedBenefitTypeID int,
    @AddedBenefitName varchar(50),
    @INAddedBenefitDescription varchar(2000),
    @INAddedBenefitAllowed float,
    @INAddedBenefitUtilization float,
    @INAddedBenefitCostShare float,
    @OONAddedBenefitDescription varchar(2000),
    @OONAddedBenefitAllowed float,
    @OONAddedBenefitUtilization float,
    @OONAddedBenefitCostShare float,
    @IsValueAdded bit,
    @BidServiceCategoryID smallint,
    @UserID CHAR(7),
    @IsNetwork bit=0
AS

DECLARE @PlanYearID SMALLINT
SET @PlanYearID = dbo.fnGetBidYear()
IF EXISTS
    (SELECT 1 
    FROM SavedPlanAddedBenefits 
    WHERE
        ForecastID = @ForecastID
        AND AddedBenefitTypeID = @AddedBenefitTypeID
        AND AddedBenefitName = @AddedBenefitName
    ) 
--    AND @IsNew=0 not sure why this was needed...?

    UPDATE SavedPlanAddedBenefits 
        SET
            INAddedBenefitDescription=@INAddedBenefitDescription,
            INAddedBenefitAllowed=@INAddedBenefitAllowed,
            INAddedBenefitUtilization=@INAddedBenefitUtilization,
            INAddedBenefitCostShare=@INAddedBenefitCostShare,
            OONAddedBenefitDescription=@OONAddedBenefitDescription,
            OONAddedBenefitAllowed=@OONAddedBenefitAllowed,
            OONAddedBenefitUtilization=@OONAddedBenefitUtilization,
            OONAddedBenefitCostShare=@OONAddedBenefitCostShare,
            IsValueAdded=@IsValueAdded,
            BidServiceCatID=@BidServiceCategoryID,
            IsNetwork=@IsNetwork,
            IsHidden=0,
            LastUpdateByID=@UserID,
            LastUpdateDatetime=GetDate()
        WHERE 
            ForecastID = @ForecastID
            AND AddedBenefitTypeID = @AddedBenefitTypeID
            AND AddedBenefitName = @AddedBenefitName
ELSE
    INSERT INTO SavedPlanAddedBenefits 
        (
        PlanYearID,
        ForecastID,
        AddedBenefitTypeID,
        AddedBenefitName,
        INAddedBenefitDescription,
        INAddedBenefitAllowed,
        INAddedBenefitUtilization,
        INAddedBenefitCostShare,
        OONAddedBenefitDescription,
        OONAddedBenefitAllowed,
        OONAddedBenefitUtilization,
        OONAddedBenefitCostShare,
        IsValueAdded,
        BidServiceCatID,
        IsNetwork,
        IsHidden,
        LastUpdateByID,
        LastUpdateDatetime
        )
    VALUES
        (
        @PlanYearID,
        @ForecastID,
        @AddedBenefitTypeID,
        @AddedBenefitName,
        @INAddedBenefitDescription,
        @INAddedBenefitAllowed,
        @INAddedBenefitUtilization,
        @INAddedBenefitCostShare,
        @OONAddedBenefitDescription,
        @OONAddedBenefitAllowed,
        @OONAddedBenefitUtilization,
        @OONAddedBenefitCostShare,
        @IsValueAdded,
        @BidServiceCategoryID,
        @IsNetwork,
        0,
        @UserID,
        GetDate()
        )
GO
