SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------                      
-- PROCEDURE NAME: [dbo].[spPDAuditExtractExport] 
-- AUTHOR: Chaitanya Durga K             

-- DESCRIPTION: Added this SP for Purpose of Batch PD Audit Extract Export Functionality insetad of using SP spAppGetMAPDAuditExhibit directly
--It avoids multiple calls from application when selected multiple plans
--It reduces performance related issues
--              
-- TYPE:               
--              
-- AUTHOR: Chaitanya Durga K
--              
-- CREATED DATE: 2022-Sep-14	             
--              
-- DESCRIPTION:               
--              
-- PARAMETERS:              
-- Input:              
--     @WhereIn             
-- TABLES:               
-- Read:              
--  Table-- SavedPlanHeader               
--                 
-- Write:              
--  sp -- [dbo].[spAppGetMAPDAuditExhibit]
-- VIEWS:              
--              
-- FUNCTIONS:              
--                
-- STORED PROCS:              
--                
-- $HISTORY               
-- ----------------------------------------------------------------------------------------------------------------------              
-- DATE			VERSION		CHANGES MADE                                            DEVELOPER                
-- ----------------------------------------------------------------------------------------------------------------------              
-- 14-09-2022     1			 Intial Version						              Chaitanya Durga K	
-- 16-08-2023	  2			Added columns										Deepali
--	17-01-2024	  3			Updated field names to
--						  3			Placeholder4 and 5 and Part_D_MOOP		Phillip Leigh
--						  3			Changed DataType on Deductible and		
--						  3			Part_D_MOOP to smallint							Phillip Leigh
-- 18-09-2024	  4			Removing columns										Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    


CREATE PROCEDURE [dbo].[spPDAuditExtractExport]        
 @WhereIn VARCHAR(MAX)  

AS          
BEGIN 

   BEGIN
     DECLARE @ContractNumber VARCHAR(5); 
   DECLARE @PlanId VARCHAR(3);    
   DECLARE @SegmentId VARCHAR(3);  
   DECLARE @MyCursor CURSOR;
    DECLARE @ForecastID int;
	create table #tmptable
	(
	[ContractPBP] [varchar](9) NOT NULL,
	[SegmentID] [char](3) NOT NULL,
	[OrganizationName] [varchar](100) NOT NULL,
	[PlanName] [varchar](100) NOT NULL,
	[DivisionName] [varchar](30) NOT NULL,
	[HumanaRegionName] [varchar](30) NOT NULL,
	[Market] [varchar](60) NULL,
	[ProductType] [varchar](13) NOT NULL,
	[SnpTypeName] [varchar](max) NOT NULL,
	[PlanType] [varchar](100) NOT NULL,
	[ActuarialProduct] [varchar](60) NULL,
	[BasicPremiumRounded] [decimal](7, 2) NOT NULL,
	[SupplementalPremiumRounded] [decimal](7, 2) NOT NULL,
	[TotalPremiumRounded] [decimal](7, 2) NOT NULL,
	[BidDirectSubsidy] [decimal](18, 10) NULL,
	[Formulary] [varchar](30) NULL,	
	[ErectileDysfunction] [varchar](13) NULL,
	[ErectileDysfunctionTier] [varchar](60) NULL,
	[AntiObesityDrugCoverage] [varchar](13) NULL,
	[AntiObesityTier] [varchar](13) NULL,
	[PrescriptionVitamins] [varchar](13) NULL,
	[PrescriptionVitaminsTier] [varchar](13) NULL,
	[ZeroDollarDSNP] [varchar](13) NULL,
	[COPDVBID] [varchar](13) NULL,		
	[DoacVbid] [VARCHAR](3) NULL,
	[Placeholder1] [VARCHAR](3) NULL,
	[Placeholder2] [VARCHAR](3) NULL,
	[HundredDayFill] [VARCHAR](20) NULL,
	[Placeholder3] [VARCHAR](3) NULL,
	[Placeholder4] [varchar](13) NULL,
	[Placeholder5] [varchar](13) NULL,
	[Deductible] [SmallInt] NOT NULL,
	[DeductibleExcludeAnyTiers] [varchar](60) NULL,
	[DeductibleT1] [varchar](30) NULL,
	[DeductibleT2] [varchar](30) NULL,
	[DeductibleT3] [varchar](30) NULL,
	[DeductibleT4] [varchar](30) NULL,
	[DeductibleT5] [varchar](30) NULL,
	[DeductibleT6] [varchar](30) NULL,
	[Part_D_MOOP] [SmallInt] NOT NULL,
	[BenefitString1MonthStandardRetail] [varchar](60) NULL,
	[BenefitString3MonthStandardRetail] [varchar](60) NULL,
	[BenefitString1MonthPreferredRetail] [varchar](60) NULL,
	[BenefitString3MonthPreferredRetail] [varchar](60) NULL,
	[BenefitString1MonthStandardMail] [varchar](60) NULL,
	[BenefitString3MonthStandardMail] [varchar](60) NULL,
	[BenefitString1MonthPreferredMail] [varchar](60) NULL,
	[BenefitString3MonthPreferredMail] [varchar](60) NULL,
	[ICCostShare1MonthStandardRetailT1] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardRetailT2] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardRetailT3] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardRetailT4] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardRetailT5] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardRetailT6] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardRetailT1] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardRetailT2] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardRetailT3] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardRetailT4] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardRetailT5] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardRetailT6] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredRetailT1] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredRetailT2] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredRetailT3] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredRetailT4] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredRetailT5] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredRetailT6] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredRetailT1] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredRetailT2] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredRetailT3] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredRetailT4] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredRetailT5] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredRetailT6] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardMailT1] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardMailT2] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardMailT3] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardMailT4] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardMailT5] [decimal](18, 2) NULL,
	[ICCostShare1MonthStandardMailT6] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardMailT1] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardMailT2] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardMailT3] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardMailT4] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardMailT5] [decimal](18, 2) NULL,
	[ICCostShare3MonthStandardMailT6] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredMailT1] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredMailT2] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredMailT3] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredMailT4] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredMailT5] [decimal](18, 2) NULL,
	[ICCostShare1MonthPreferredMailT6] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredMailT1] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredMailT2] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredMailT3] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredMailT4] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredMailT5] [decimal](18, 2) NULL,
	[ICCostShare3MonthPreferredMailT6] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthStandardRetailT1] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthStandardRetailT2] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthStandardRetailT3] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthStandardRetailT4] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthStandardRetailT5] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthStandardRetailT6] [decimal](18, 2) NULL,	 
	[BelowICLCoins1MonthPreferredRetailT1] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthPreferredRetailT2] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthPreferredRetailT3] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthPreferredRetailT4] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthPreferredRetailT5] [decimal](18, 2) NULL,
	[BelowICLCoins1MonthPreferredRetailT6] [decimal](18, 2) NULL,	 	 
	[InsulinCostShare1MonthStandardRetailT1] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardRetailT2] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardRetailT3] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardRetailT4] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardRetailT5] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardRetailT6] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardRetailT1] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardRetailT2] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardRetailT3] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardRetailT4] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardRetailT5] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardRetailT6] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredRetailT1] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredRetailT2] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredRetailT3] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredRetailT4] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredRetailT5] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredRetailT6] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredRetailT1] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredRetailT2] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredRetailT3] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredRetailT4] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredRetailT5] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredRetailT6] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardMailT1] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardMailT2] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardMailT3] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardMailT4] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardMailT5] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthStandardMailT6] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardMailT1] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardMailT2] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardMailT3] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardMailT4] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardMailT5] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthStandardMailT6] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredMailT1] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredMailT2] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredMailT3] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredMailT4] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredMailT5] [decimal](18, 2) NULL,
	[InsulinCostShare1MonthPreferredMailT6] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredMailT1] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredMailT2] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredMailT3] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredMailT4] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredMailT5] [decimal](18, 2) NULL,
	[InsulinCostShare3MonthPreferredMailT6] [decimal](18, 2) NULL,
	[DaySupply1MonthRetailT1] [decimal](18, 2) NULL,
	[DaySupply1MonthRetailT2] [decimal](18, 2) NULL,
	[DaySupply1MonthRetailT3] [decimal](18, 2) NULL,
	[DaySupply1MonthRetailT4] [decimal](18, 2) NULL,
	[DaySupply1MonthRetailT5] [decimal](18, 2) NULL,
	[DaySupply1MonthRetailT6] [decimal](18, 2) NULL,
	[DaySupply1MonthMailT1] [decimal](18, 2) NULL,
	[DaySupply1MonthMailT2] [decimal](18, 2) NULL,
	[DaySupply1MonthMailT3] [decimal](18, 2) NULL,
	[DaySupply1MonthMailT4] [decimal](18, 2) NULL,
	[DaySupply1MonthMailT5] [decimal](18, 2) NULL,
	[DaySupply1MonthMailT6] [decimal](18, 2) NULL,
	[DaySupply3MonthRetailT1] [decimal](18, 2) NULL,
	[DaySupply3MonthRetailT2] [decimal](18, 2) NULL,
	[DaySupply3MonthRetailT3] [decimal](18, 2) NULL,
	[DaySupply3MonthRetailT4] [decimal](18, 2) NULL,
	[DaySupply3MonthRetailT5] [decimal](18, 2) NULL,
	[DaySupply3MonthRetailT6] [decimal](18, 2) NULL,
	[DaySupply3MonthMailT1] [decimal](18, 2) NULL,
	[DaySupply3MonthMailT2] [decimal](18, 2) NULL,
	[DaySupply3MonthMailT3] [decimal](18, 2) NULL,
	[DaySupply3MonthMailT4] [decimal](18, 2) NULL,
	[DaySupply3MonthMailT5] [decimal](18, 2) NULL,
	[DaySupply3MonthMailT6] [decimal](18, 2) NULL,
	[BaseMemberMonths] [int] NOT NULL,
	[ProjectedMM] [decimal](18, 10) NOT NULL,
	[PlanYearLIPercent] [decimal](18, 10) NULL,
	[PlanYearDrugNetClaims] [decimal](18, 10) NULL,
	[PlanYearAllowedClaims] [decimal](18, 10) NULL,
	[AdminPMPM] [decimal](18, 10) NULL,
	[PlanYearDrugProfitPercent] [decimal](18, 10) NULL,
	[ProjectedRiskScore] [decimal](18, 10) NULL,
	[TargetAmountAdjustment] [decimal](18, 10) NULL,
	[OOPC] [decimal](18, 10) NULL,
	[NonTroop] [decimal](18, 2) NULL,
	[QualityInitiatives] [decimal](18, 10) NULL,
	[TaxesFees] [decimal](18, 10) NULL,
	[DIRCapbeforeReins] [decimal](18, 10) NULL,
	[DIRCapafterReins] [decimal](18, 10) NULL,
	[BasicPremiumUnrounded] [decimal](7, 2) NOT NULL,
	[SupplementalPremiumUnrounded] [decimal](7, 2) NOT NULL,
	[TotalPremiumUnrounded] [decimal](7, 2) NOT NULL,
	[FormularyID] [int] NULL	
	)
    BEGIN
    SET @MyCursor = CURSOR FOR
    SELECT Value FROM dbo.fnStringSplit (@WhereIN, ',')     
OPEN @MyCursor 
    FETCH NEXT FROM @MyCursor 
    INTO @ForecastID

    WHILE @@FETCH_STATUS = 0
    BEGIN       

	SELECT @ContractNumber=s.ContractNumber,@PlanId=s.PlanID,@SegmentId=s.SegmentID FROM SavedPlanHeader s WHERE s.ForecastID=@ForecastID	
	INSERT INTO #tmptable
	EXEC [dbo].[spAppGetMAPDAuditExhibit] @ContractNumber,@PlanID,@ForecastID,@SegmentID

	  FETCH NEXT FROM @MyCursor 
      INTO @ForecastID 
	 END; 

    CLOSE @MyCursor ;
    DEALLOCATE @MyCursor;
  END  

  END
  SELECT * FROM #tmptable  
END
GO
