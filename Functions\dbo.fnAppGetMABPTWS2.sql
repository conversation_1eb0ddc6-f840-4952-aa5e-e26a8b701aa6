SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetMABPTWS2
--
-- AUTHOR: <PERSON> Lake
--
-- CREATED DATE: 2008-Aug-13
-- HEADER UPDATED: 2011-Mar-18
--
-- DESCRIPTION: Function responsible for building data that is required to complete WS 2.  
--
-- PARAMETERS:
--	Input:
--	    @ForecastID 
--	Output:
--
-- TABLES: 
--	Read:
--		CalcBenefitProjection
--		LkpExtCMSBidServiceCategory 
--		LkpIntAddedBenefitCategory
--		LkpIntBenefitCategory
--		SavedPlanAddedBenefits
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnAppGetMABPTWS1Summary
--		fnGetBidYear
--		fnGetCredibilityFactor
--		fnGetSafeDivisionResult
--		fnSignificantDigits
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2008-Aug-13      1           Initial Version                                                     Brian Lake
-- 2009-Feb-25      2           Changed Manual to a left join, switched order of joins              Sandy Ellis
-- 2009-Feb-26      3           Removed UserID                                                      Brian Lake
-- 2009-Mar-17      4           Data types                                                          Sandy Ellis
-- 2009-Mar-20      5           Added SafeDivisionResult functions                                  Keith Galloway
-- 2009-Mar-22      6           Included Dual/Nondual columns                                       Sandy Ellis
-- 2009-Apr-04      7           Added added benefits                                                Sandy Ellis
-- 2009-Apr-05      8           Cast around added benefits for decimals                             Sandy Ellis
-- 2009-Apr-21      9           Fixed decimal issue with added benefits by replacing UNIONs         Tonya Cockrell
--                                with separate INSERTS.
-- 2009-May-13      10          Where clause to be just one version from SavedPlanHeader            Keith Galloway
-- 2010-Jan-28      11          Added Declarations for Net/Allowed Ratios for Cap changes           Nick Skeen
-- 2010-Jan-29      12          Added InflationFactor to reflect WS1 Changes                        Nick Skeen
-- 2010-Apr-10      13          Correctly displays OON for added benefits for 2011+                 Casey Sanders
-- 2010-Apr-15      14          Updated Added Benefits sections for 2011+ so Util Type              Joe Casey
--                                would be included for all benefits
-- 2010-Apr-17      15          Bypassed section for blended/fully credible to speed up             Casey Sanders
--                                function to optimize BPT generation.
-- 2010-May-03      16          Reversing version 15.                                               Casey Sanders
-- 2011-Jan-05      17          Revised for 2012 Databse                                            Michael Siekerka
-- 2011-Jan-07      18          Combined the benefits section into 1 query instead of 2 based on    Joe Casey
--                                ExperienceCredibility.                                            Joe Casey
-- 2011-Feb-14      19          Changed reference from LkpIntAddedBenefitType to                    Michael Siekerka
--                                LkpIntAddedBenefitCategory
-- 2011-Mar-18      20          Changed how Credibility is displayed for Added Benefits.            Joe Casey
--                                Calculations don't need to be altered, just how it's displayed.	
-- 2011-Mar-30      21          Blended values are now pulled from the exp and man values           Joe Casey
--                                calculated in this function instead of CalcBenefitProjection
--                                to match calculations in BPT WS2.
-- 2011-Mar-30      22          Changed InflationTrend to ProviderPaymentChange                     Joe Casey
-- 2011-Apr-13      23          Fixed parenthesis placement on blended Allowed and Avg Cost Calcs   Michael Siekerka
-- 2011-Apr-19      24          Added a case condition to the calculation of ProjectedAllowed and   Sule Dauda
--                                ManualAllowed for MSB.			
-- 2014-Mar-18      25          Edited PercentOON to based on Allowed instead of Units              Siliang Hu
-- 2020-Oct-12      26          Edited fnAppGetMABPTWS1Summary to reference ForecastID column       Alex Beruscha
-- 2023-Aug-03      27          Added Nolock and internal parameter and table schema            	Sheetal Patil
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS2]
	(
    @ForecastID INT
	)
RETURNS @Results TABLE 
	(
    PlanYearID SMALLINT,
    ForecastID INT,
    ServiceCategoryCode VARCHAR(4),
    UtilType CHAR(1),
    ProjectedUnits DECIMAL(23, 15),
    ProjectedAvgCost DECIMAL(23, 15),
    ProjectedAllowed DECIMAL(23, 15),
    ManualUtil DECIMAL(23, 15),
    ManualAvgCost DECIMAL(23, 15),
    ManualAllowed DECIMAL(23, 15),
    ExperienceCredibility DECIMAL(7, 6),
    BlendedUtil DECIMAL(23, 15),
    BlendedAvgCost DECIMAL(23, 15),
    BlendedAllowed DECIMAL(23, 15),
    NonDualBlendedAllowedPMPM DECIMAL(23, 15),
    DualBlendedAllowedPMPM DECIMAL(23, 15),
    PercentOON DECIMAL(7, 6)
	) AS
BEGIN
    DECLARE @ExperienceCredibility DECIMAL (7,6),
            @PlanYearID SMALLINT,
			@XForecastID INT = @ForecastID 
            
    SELECT @ExperienceCredibility = dbo.fnGetCredibilityFactor(@XForecastID)
	SELECT @PlanYearID = dbo.fnGetBidYear()

	INSERT @Results
	SELECT 
		@PlanYearID,
		BBP.ForecastID,
		BSC.ServiceCategoryCode,
		BSC.UtilType,
		ProjectedUnits = 
			CASE WHEN @ExperienceCredibility = 0 THEN NULL
				ELSE ISNULL(WS1.BaseUnits
						* (WS1.UtilizationTrend
						* WS1.BenefitChange
						* WS1.PopulationChange
						* WS1.OtherFactor), 0)
					+ ISNULL(WS1.UtilAdditiveAdjustment, 0)
			END,
		ProjectedAvgCost =
			CASE WHEN @ExperienceCredibility = 0 THEN NULL
				ELSE dbo.fnGetSafeDivisionResult(
						CONVERT(DECIMAL(23,15), 
								ISNULL
									(WS1.BaseAllowed
									* WS1.UtilizationTrend
									* WS1.BenefitChange
									* WS1.PopulationChange
									* WS1.OtherFactor
									* WS1.CostTrend
									* WS1.ProviderPaymentChange, 0)
								+ ISNULL(WS1.PMPMAdditiveAdjustment, 0)
							) * 12000
						,CONVERT(DECIMAL(23,12), 
							ISNULL
								(WS1.BaseUnits
								* (WS1.UtilizationTrend 
									* WS1.BenefitChange
									* WS1.PopulationChange
									* WS1.OtherFactor), 0)
							+ ISNULL(WS1.UtilAdditiveAdjustment, 0)))
			END,
		ProjectedAllowed =
			CASE WHEN @ExperienceCredibility = 0 THEN NULL
				ELSE ISNULL(WS1.BaseAllowed 
					* WS1.UtilizationTrend
					* WS1.BenefitChange
					* WS1.PopulationChange
					* WS1.OtherFactor
					* WS1.CostTrend
					* WS1.ProviderPaymentChange, 0)
					+ ISNULL(WS1.PMPMAdditiveAdjustment, 0)
			END,
		ManualUtil = 
			CASE WHEN @ExperienceCredibility = 1 THEN NULL
			   ELSE SUM(MBP.INUnits + MBP.OONUnits)
			END,
		ManualAvgCost = 
			CASE WHEN @ExperienceCredibility = 1 THEN NULL
				ELSE dbo.fnGetSafeDivisionResult(
					CONVERT(DECIMAL(23,15),SUM(MBP.INAllowed + MBP.OONAllowed)) * 12000
					,CONVERT(DECIMAL(23,15),SUM(MBP.INUnits + MBP.OONUnits)))
			END,
		ManualAllowed =
			CASE WHEN @ExperienceCredibility = 1 THEN NULL
			   ELSE SUM(ISNULL(MBP.INAllowed,0) + ISNULL(MBP.OONAllowed,0))
			END,
		ExperienceCredibility = @ExperienceCredibility,
		BlendedUtil =
			CASE @ExperienceCredibility
				--All Experience
				WHEN 1 THEN ISNULL(WS1.BaseUnits
								* (WS1.UtilizationTrend
								* WS1.BenefitChange
								* WS1.PopulationChange
								* WS1.OtherFactor), 0)
							+ ISNULL(WS1.UtilAdditiveAdjustment, 0)
				--All Manual
				WHEN 0 THEN SUM(MBP.INUnits + MBP.OONUnits)
				--Blended
				ELSE (ISNULL(WS1.BaseUnits
						* (WS1.UtilizationTrend
						* WS1.BenefitChange
						* WS1.PopulationChange
						* WS1.OtherFactor), 0) +
						ISNULL(WS1.UtilAdditiveAdjustment, 0)) * @ExperienceCredibility
						+
						(SUM(MBP.INUnits + MBP.OONUnits) * (1 - @ExperienceCredibility))
			END,
		BlendedAvgCost =
			CASE @ExperienceCredibility
				--All Experience
				WHEN 1 THEN dbo.fnGetSafeDivisionResult(
								CONVERT(DECIMAL(23,15), 
										ISNULL
											(WS1.BaseAllowed
											* WS1.UtilizationTrend
											* WS1.BenefitChange
											* WS1.PopulationChange
											* WS1.OtherFactor
											* WS1.CostTrend
											* WS1.ProviderPaymentChange, 0)
										+ ISNULL(WS1.PMPMAdditiveAdjustment, 0)
									) * 12000
								,CONVERT(DECIMAL(23,12), 
									ISNULL
										(WS1.BaseUnits
										* (WS1.UtilizationTrend 
											* WS1.BenefitChange
											* WS1.PopulationChange
											* WS1.OtherFactor), 0)
									+ ISNULL(WS1.UtilAdditiveAdjustment, 0)))
				--All Manual
				WHEN 0 THEN dbo.fnGetSafeDivisionResult(
								CONVERT(DECIMAL(23,15),SUM(MBP.INAllowed + MBP.OONAllowed)) * 12000
								,CONVERT(DECIMAL(23,15),SUM(MBP.INUnits + MBP.OONUnits)))
				--Blended
				ELSE dbo.fnGetSafeDivisionResult(
				        --Total Blended Allowed
				        (ISNULL(WS1.BaseAllowed 
							* WS1.UtilizationTrend
							* WS1.BenefitChange
							* WS1.PopulationChange
							* WS1.OtherFactor
							* WS1.CostTrend
							* WS1.ProviderPaymentChange, 0) +
							ISNULL(WS1.PMPMAdditiveAdjustment, 0)) * @ExperienceCredibility
					        +
					        SUM(ISNULL(MBP.INAllowed,0) + ISNULL(MBP.OONAllowed,0)) * (1 - @ExperienceCredibility),
					    --Blended Util
						(ISNULL(WS1.BaseUnits
						* (WS1.UtilizationTrend
						* WS1.BenefitChange
						* WS1.PopulationChange
						* WS1.OtherFactor), 0) +
						ISNULL(WS1.UtilAdditiveAdjustment, 0)) * @ExperienceCredibility
						+
						SUM(MBP.INUnits + MBP.OONUnits) * (1 - @ExperienceCredibility))
					* 12000 --Multiply by 12,000 MM
			END,
		BlendedAllowed =
			CASE @ExperienceCredibility
				--All Experience
				WHEN 1 THEN ISNULL(WS1.BaseAllowed 
							* WS1.UtilizationTrend
							* WS1.BenefitChange
							* WS1.PopulationChange
							* WS1.OtherFactor
							* WS1.CostTrend
							* WS1.ProviderPaymentChange, 0) +
							ISNULL(WS1.PMPMAdditiveAdjustment, 0)
				--All Manual
				WHEN 0 THEN SUM(ISNULL(MBP.INAllowed,0) + ISNULL(MBP.OONAllowed,0))
				--Blended
				ELSE (ISNULL(WS1.BaseAllowed 
							* WS1.UtilizationTrend
							* WS1.BenefitChange
							* WS1.PopulationChange
							* WS1.OtherFactor
							* WS1.CostTrend
							* WS1.ProviderPaymentChange, 0) +
							ISNULL(WS1.PMPMAdditiveAdjustment, 0)) * @ExperienceCredibility
					        +
					        SUM(ISNULL(MBP.INAllowed,0) + ISNULL(MBP.OONAllowed,0)) * (1 - @ExperienceCredibility)
			END,
		NonDualBlendedAllowedPMPM = SUM(ISNULL(NDBBP.INAllowed,0) + ISNULL(NDBBP.OONAllowed,0)),
		DualBlendedAllowedPMPM = SUM(ISNULL(DBBP.INAllowed,0) + ISNULL(DBBP.OONAllowed,0)),
		PercentOON = dbo.fnGetSafeDivisionResult(SUM(BBP.OONAllowed) ,SUM(BBP.INAllowed + BBP.OONAllowed))
	FROM dbo.CalcBenefitProjection BBP WITH (NOLOCK)-- Blended
	INNER JOIN dbo.LkpIntBenefitCategory BC WITH (NOLOCK)
		ON BBP.BenefitCategoryID = BC.BenefitCategoryID
	INNER JOIN dbo.LkpExtCMSBidServiceCategory BSC WITH (NOLOCK)
		ON BSC.BidServiceCategoryID = BC.BidServiceCatID
	LEFT JOIN dbo.fnAppGetMABPTWS1Summary(@XForecastID) WS1 -- Experience
		ON WS1.ForecastID = BBP.ForecastID
		AND WS1.ServiceCategoryCode = BSC.ServiceCategoryCode
		AND WS1.UtilTYpe = BSC.UtilType
	LEFT JOIN dbo.CalcBenefitProjection MBP WITH (NOLOCK)-- Manual
		ON MBP.ForecastID = BBP.ForecastID
		AND MBP.BenefitCategoryID = BBP.BenefitCategoryID
		AND MBP.DualEligibleTypeID = BBP.DualEligibleTypeID
	LEFT JOIN dbo.CalcBenefitProjection NDBBP WITH (NOLOCK) -- Nondual
		ON NDBBP.ForecastID = BBP.ForecastID
		AND NDBBP.MARatingOptionID = BBP.MARatingOptionID
		AND NDBBP.BenefitCategoryID = BBP.BenefitCategoryID
	LEFT JOIN dbo.CalcBenefitProjection DBBP WITH (NOLOCK) -- Dual
		ON DBBP.ForecastID = BBP.ForecastID
		AND DBBP.MARatingOptionID = BBP.MARatingOptionID
		AND DBBP.BenefitCategoryID = BBP.BenefitCategoryID
	WHERE BBP.ForecastID = @XForecastID
		AND MBP.MARatingOptionID = 2 --Manual
	    AND BBP.MARatingOptionID = 3 --Blended
		AND NDBBP.DualEligibleTypeID = 0 --NonDual
		AND DBBP.DualEligibleTypeID = 1 --Dual
		AND BBP.DualEligibleTypeID = 2 --Both
	GROUP BY
		BBP.ForecastID,
		BSC.ServiceCategoryCode,
		BSC.UtilType,
		WS1.BaseUnits,
		WS1.UtilizationTrend,
		WS1.BenefitChange,
		WS1.PopulationChange,
		WS1.OtherFactor,
		WS1.UtilAdditiveAdjustment,
		WS1.BaseAllowed,
		WS1.CostTrend,
		WS1.BaseNetPMPM,
		WS1.ProviderPaymentChange,
		WS1.PMPMAdditiveAdjustment			
				
	--Include Added Benefits
	INSERT @Results
	SELECT
		@PlanYearID,
		a.ForecastID,
		bsc.ServiceCategoryCode,
		bsc.UtilType,
		ProjectedUnits = SUM(ISNULL(ab.INAddedBenefitUtilization,0) + ISNULL(ab.OONAddedBenefitUtilization,0)),
		ProjectedAvgCost = NULL,
		ProjectedAllowed = 
			CASE WHEN ISNULL(WS1.BaseAllowed,0) = 0 THEN 0
				ELSE SUM(ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0))
			END,
		ManualUtil = SUM(ISNULL(ab.INAddedBenefitUtilization,0) + ISNULL(ab.OONAddedBenefitUtilization,0)),
		ManualAvgCost = NULL,
		ManualAllowed = 	
			CASE WHEN (ISNULL(WS1.BaseAllowed,0) = 0 OR @ExperienceCredibility < 1)
				THEN SUM(ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0))
				ELSE 0
			END,
		ExperienceCredibility =
			--Check WS1 to see if we had experience then check to see if we have projected.  If no then yes, then 0% credibility
			CASE WHEN ISNULL(WS1.BaseAllowed,0) = 0 THEN 
					CASE WHEN SUM(ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0)) <> 0
						THEN 0
						ELSE @ExperienceCredibility
					END
				ELSE @ExperienceCredibility
			END,
		BlendedUtil = SUM(ISNULL(ab.INAddedBenefitUtilization,0) + ISNULL(ab.OONAddedBenefitUtilization,0)),
		BlendedAvgCost = NULL,
		BlendedAllowed = SUM(ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0)),
		NonDualBlendedAllowedPMPM = CAST(SUM(ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0)) AS DECIMAL(23,15)),
		DualBlendedAllowedPMPM = CAST(SUM(ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0)) AS DECIMAL(23,15)),
		OONPercent =
			dbo.fnSignificantDigits(
				dbo.fnGetSafeDivisionResult(
					SUM(ISNULL(abt.OONPercent,0) * (ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0)))
					,SUM(ISNULL(ab.INAddedBenefitAllowed,0) + ISNULL(ab.OONAddedBenefitAllowed,0)))
				,15)
	FROM 
		(SELECT
            sph.ForecastID,
            bsc.ServiceCategoryCode,
            bsc.UtilType
        FROM dbo.SavedPlanHeader sph WITH (NOLOCK)
        CROSS JOIN dbo.LkpExtCMSBidServiceCategory bsc WITH (NOLOCK)
        INNER JOIN dbo.LkpIntAddedBenefitCategory abc WITH (NOLOCK)
            ON bsc.BidServiceCategoryID = abc.BidServiceCatID
        WHERE sph.ForecastID = @XForecastID
        GROUP BY
            sph.ForecastID,
            bsc.ServiceCategoryCode,
            bsc.UtilType
		) a
	INNER JOIN dbo.LkpExtCMSBidServiceCategory bsc WITH (NOLOCK)
		ON bsc.ServiceCategoryCode = a.ServiceCategoryCode
		AND bsc.UtilType = a.UtilType
	INNER JOIN dbo.LkpIntAddedBenefitType abt WITH (NOLOCK)
		ON abt.BidServiceCatID = bsc.BidServiceCategoryID
	LEFT JOIN dbo.SavedPlanAddedBenefits ab WITH (NOLOCK)
		ON ab.ForecastID = a.ForecastID
		AND ab.BidServiceCatID = bsc.BidServiceCategoryID
		AND ab.AddedBenefitTypeID = abt.AddedBenefitTypeID
		AND ab.IsHidden = 0
	LEFT JOIN dbo.fnAppGetMABPTWS1Summary(@XForecastID) WS1
		ON a.ForecastID = WS1.ForecastID
		AND a.ServiceCategoryCode = WS1.ServiceCategoryCode
		AND a.UtilTYpe = WS1.UtilType
	GROUP BY
		a.ForecastID,
		bsc.ServiceCategoryCode,
		bsc.UtilType,
		WS1.BaseAllowed
	ORDER BY
		bsc.ServiceCategoryCode
	RETURN
END
GO
