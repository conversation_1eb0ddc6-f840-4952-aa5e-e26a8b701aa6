SET QUOTED_IDENTIFIER ON
GO
SET <PERSON>SI_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------        
-- PROCEDURE NAME: [spGetRunRequest]     
--        
-- AUTHOR: <PERSON><PERSON><PERSON>  
--        
-- CREATED DATE: 2022-Jul-01     
--        
-- DESCRIPTION: Procedure responsible for Select request which is in queue

-- TYPE: New    
--     
-- PARAMETERS:        
-- Input:        
--   @RequestType    
-- TABLES:         
-- Read:             
-- [Request]        
-- Write:        
--             

-- VIEWS:        
--        
-- FUNCTIONS:        
--          
-- STORED PROCS:            

-- $HISTORY         
-- --------------------------------------------------------------------------------------------------------------  
-- DATE        VERSION         CHANGES MADE												DEVELOPER          
-- --------------------------------------------------------------------------------------------------------------
-- 2022-Jul-01   1      Initial Version												Manisha Tyagi
-- 2023-Aug-16   2      Model turn over logic added									Surya Murthy
-- 2023-Nov-15	3		Added cast													Deepali
-- 2024-Sep-11  4       Removed unused code of mmodel turn over old process         Deepali
-- 2025-APr-08  5       Removed Unused Columns                                      Vikrant Bagal
-- 2025-Apr-29  6       DataFoundationSyncTM and DataFoundationSync logic added     Chaitanya Durga K
-- --------------------------------------------------------------------------------------------------------------  
CREATE PROCEDURE [dbo].[spGetRunRequest] @RequestType VARCHAR(MAX)
AS
BEGIN

    -------------------------------CUApp-ClaimCategorization------------------------------------------
     IF @RequestType = 'DataFoundationSyncForecastingcu'
    BEGIN
        SELECT TOP (1)
               h.RequestID,
               d.DFVersionID,
               CAST(d.SourceTableName AS VARCHAR(1000)),
               d.DFRunIDs,
               h.RequestorComments AS DFVersionDescription,
               h.LastUpdateByID
        FROM dbo.SavedUtilityRequestHeader h
            INNER JOIN dbo.SavedUtilityRequestDetail d
                ON h.RequestID = d.RequestID
        WHERE h.RequestStatus = 'In progress'
              AND h.RequestType = 'DataFoundationSyncForecastingcu'
        ORDER BY h.RequestID;

    END;
    ELSE IF @RequestType = 'Utilities'
    BEGIN
        SELECT DISTINCT TOP (1)
               h.RequestID,
               '' AS SourceEnvironmentName,
               '' AS DestinationEnvironmentName,
               CASE WHEN h.RequestType ='ArchiveData' OR h.RequestType ='ModelTurn_Script1' 
			   OR h.RequestType ='ModelTurn_Script2' OR h.RequestType ='ModelTurn_Script3'
			   OR h.RequestType ='ModelTurn_Script4' OR h.RequestType ='ModelTurn_Script5'
			   OR h.RequestType='DataFoundationSyncTM' OR h.RequestType='DataFoundationSync'			
			   THEN 'Custom'
			   ELSE  h.RequestType
			   END
			   AS requestType,
               CAST(d.SQLText AS VARCHAR(4000)) AS SQLText,
               h.RequestedBy AS RequestedBy
        FROM dbo.SavedUtilityRequestHeader h
            INNER JOIN dbo.SavedUtilityRequestDetail d
                ON h.RequestID = d.RequestID
        WHERE h.RequestStatus = 'In progress'              
        ORDER BY h.RequestID DESC;
    END;
END;

GO
