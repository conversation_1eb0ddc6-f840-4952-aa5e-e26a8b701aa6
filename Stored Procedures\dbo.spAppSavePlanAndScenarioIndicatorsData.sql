SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- =============================================      
-- Author:  Pooja <PERSON>ya     
-- Create date: 22-11-2017
-- Description:  Get Scenario Setup Service Area & Revenue  data
--      
--      
-- PARAMETERS:      
-- Input:        

-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-June-19     1		Initial version.									    Kritika Singh
-- 2018- Nov-09		2		Added logic for Crosswalk plans							Deepali Mittal
-- 2018- Nov-10		3		Removed logic for crosswalk plans while deleting		Deepali
-- 2018-Nov-14		4		Added parameter UserID									Deepali
-- 2018-Dec-19	    5		Updated Error message and included logging exception	Kritika Singh
-- 2019-Feb-20		6		Removed MessageFromBackend Variable						Deepali
-- 2019-Apr-15		7		Added columns for SavedForecastSetup					Deepali
-- 2019-Apr-19      8       Removed unwanted update statement                       Ranjana
-- 2019-Sep-23      9		Added changes for story Edit&Hide,Delete Validation     Pooja Dahiya
-- 2019-Oct-01      10		Set isToReprice = 1                                     Brent Osantowski
-- 2021-Apr-27		13		Added field IsCalcPlanExpAdj							Deepali
-- 2023-Oct-04		14		commenting  IsSkippedInducedUtilization flag for		Surya Murthy
--							future use story#5146474 
-- ----------------------------------------------------------------------------------------------------------------------        

CREATE PROCEDURE [dbo].[spAppSavePlanAndScenarioIndicatorsData]
  @ForecastID INT ,
  @offmamodel BIT,
  @hidden BIT,
  @isLiveIndex bit,
  @isSctPlan bit,
  @isFiledPlan bit,
  @isLocked bit,
  @IsSkippedInducedUtilization bit,
  @IsCalcPlanExpAdj bit,
  @LastUpdateByID CHAR(7),
  @Result BIT OUT

AS
    BEGIN     

    BEGIN TRANSACTION;
    BEGIN TRY   
        BEGIN

	   BEGIN
	     IF OBJECT_ID('AppSavedForecastIDCanDelete', 'U') IS NOT NULL 
	   BEGIN
	   Update  SS
       set SS.IsHidden = @hidden,
	   LastUpdateByID=@LastUpdateByID,
	   LastUpdateDateTime = getdate()
        FROM    dbo.SavedPlanInfo SP
                INNER JOIN dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
        WHERE    SS.ForecastID= @ForecastID and (ss.ForecastID  in (select ISNULL(ForecastID,'') from dbo.AppSavedForecastIDCanDelete))
	  END


	   Update  SS
	   set ss.isLiveIndex = @isLiveIndex,
	   ss.isSctPlan = @isSctPlan,
	   ss.isFiledPlan = @isFiledPlan,
	   ss.isLocked = @isLocked,
	   --ss.IsSkippedInducedUtilization = @IsSkippedInducedUtilization,
	   ss.istoReprice = 1,
	   LastUpdateByID=@LastUpdateByID,
	   LastUpdateDateTime = getdate()
        FROM    dbo.SavedPlanInfo SP
                INNER JOIN dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
        WHERE    SS.ForecastID= @ForecastID 


  	  Update  SP
         set SP.IsOffMAModel = @offmamodel,
		  SP.IsCalcPlanExpAdj=@IsCalcPlanExpAdj,
	   LastUpdateByID=@LastUpdateByID,
	   LastUpdateDateTime = getdate()

        FROM    dbo.SavedPlanInfo SP
                INNER JOIN dbo.SavedForecastSetup SS ON SP.PlanInfoID = SS.PlanInfoID
        WHERE    SS.ForecastID= @ForecastID

	  SET @Result = 1;

	   END

          END ;


        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        SET @Result = 0;

         DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
		ROLLBACK TRANSACTION; 
		---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
    END CATCH;  

END;
GO
