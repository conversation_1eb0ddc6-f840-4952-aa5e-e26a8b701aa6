SET <PERSON><PERSON>_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------                      
-- PROCEDURE NAME: [dbo].[spAppImportMedicareSecondaryPayer]            
--                      
-- TYPE: SAME                      
--                      
-- AUTHOR: <PERSON><PERSON>                     
--                      
-- CREATED DATE:2024-12-04                     
--                      
-- DESCRIPTION: Procedure responsible for insert and update from Medicare Secondary Payer Import                  
--                      
--                      
-- PARAMETERS:                      
-- Input:                      
--     @StageId
-- TABLES:                       
-- Read:                      
--                          
--                         
-- Write:                      
--                      
-- VIEWS:                      
--                      
-- FUNCTIONS:                      
--                        
-- STORED PROCS:                      
--                        
-- $HISTORY                       
-- ----------------------------------------------------------------------------------------------------------------------                      
-- DATE            VERSION   CHANGES MADE                 DEVELOPER                        
-- ----------------------------------------------------------------------------------------------------------------------                      
-- 2024-Dec		      1     Initial Version              Ramaraj Kumar
-- ---------------------------------------------------------------------------------------------------------------------- 


CREATE PROCEDURE [dbo].[spAppImportMedicareSecondaryPayer]
(
	@StageId VARCHAR(100)
)
AS
BEGIN
      SET NOCOUNT ON;
    BEGIN TRY
	BEGIN TRANSACTION

    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;
	DECLARE @LastUpdate DateTime;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

	SET @LastUpdate = GETDATE();

	DECLARE @MedSecPayer TABLE
	(
		[ForecastID] [int] NOT NULL,
		[SecondaryPayerAdjustment] [decimal] (7,6),
		[LastUpdateByID] [char] (7) NOT NULL,
		[LastUpdateDateTime] [DateTime] NOT NULL
	);

	INSERT INTO @MedSecPayer SELECT ForecastID, SecondaryPayerAdjustment, @UserId, @LastUpdate FROM OPENJSON(@jsonData, '$.MedicareSecPayerAdj') WITH (ForecastID INT '$.ForecastID', SecondaryPayerAdjustment DECIMAL(7,6) '$.SecondaryPayerAdjustment')

	MERGE INTO [dbo].[SavedPlanAssumptions] AS Target
	USING @MedSecPayer as Source
	ON Target.ForecastID = Source.ForecastID

	WHEN MATCHED THEN
	UPDATE SET Target.SecondaryPayerAdjustment = Source.SecondaryPayerAdjustment;


	MERGE INTO [dbo].[SavedForecastSetup] AS Target
	USING @MedSecPayer as Source
	ON Target.ForecastID = Source.ForeCastID

	WHEN MATCHED THEN
	UPDATE SET Target.IsToReprice = 1,
        Target.LastUpdateByID = @UserID,
        Target.LastUpdateDateTime = @LastUpdate;

    DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;

    COMMIT TRANSACTION 
    END TRY
    BEGIN CATCH

        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;
        DECLARE @ErrorException NVARCHAR(4000);
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
                @currentdate DATETIME = GETDATE();

        SELECT @ErrorMessage = ERROR_MESSAGE();
        SELECT @ErrorSeverity = ERROR_SEVERITY();
        SELECT @ErrorState = ERROR_STATE();

        RAISERROR(   
			   @ErrorMessage,  -- Message text.  
               @ErrorSeverity, -- Severity.  
               @ErrorState     -- State.  
        );

		ROLLBACK TRANSACTION;
		       ---Insert into app log for logging error------------------
        EXEC dbo.spAppAddLogEntry @log_date = @currentdate,
                                  @log_thread = '',
                                  @log_level = 'ERROR',
                                  @log_source = @errSrc,
                                  @log_message = @ErrorMessage,
                                  @exception = @ErrorException,
                                  @USER = @UserID

       END CATCH;

END;
