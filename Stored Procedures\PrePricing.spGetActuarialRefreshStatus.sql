SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-----------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: PrePricing.[spLoadPricingModelActuarialRefresh]  
--    
-- AUTHOR: <PERSON> Gilbert 
--    
-- CREATED DATE: 2025-FEB-6
-- Type: 
-- DESCRIPTION: Control procedure to Load Bid year, Current Year, and SCT data from the pricing model for use in the benefit interface
--    
-- PARAMETERS:    
-- Input: 

-- TABLES:   
--  

-- Read:    
--  

-- Write:    
--    
--
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2025-FEB-6		1		Initial Version                 Adam Gilbert
-- 2025-FEB-11		2		Error code verbiage             Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------   
CREATE PROCEDURE [PrePricing].[spGetActuarialRefreshStatus]
AS
	SET NOCOUNT ON;
	BEGIN

	DECLARE @OutputCode VARCHAR(20)     = '';--Error, Success
	DECLARE @OutputMessage VARCHAR(MAX) = '';--Freeform text
	DECLARE @ClickTime DATETIME = GETDATE();
	DECLARE @LastActuarialRefreshDateTime DATETIME, 
			@ActuarialRefreshCooldownSeconds INT,
			@TimeElapsed INT,
			@TimeRemaining INT;   

	/*Get Values from AppBenefitInterfaceControl table*/
	SELECT TOP 1 
	@LastActuarialRefreshDateTime = LastActuarialRefreshDateTime ,
	@ActuarialRefreshCooldownSeconds = ActuarialRefreshCooldownSeconds
	FROM PrePricing.AppBenefitInterfaceControl;

	--Keep this query for debugging.
/*	SELECT @ActuarialRefreshCooldownSeconds,
			DATEDIFF(SECOND,@LastActuarialRefreshDateTime,@ClickTime),
			@LastActuarialRefreshDateTime,
			CASE WHEN @ActuarialRefreshCooldownSeconds<= DATEDIFF(SECOND,@LastActuarialRefreshDateTime,@ClickTime) THEN 1 ELSE 0 END
*/	
	SET @TimeElapsed = DATEDIFF(SECOND,@LastActuarialRefreshDateTime,@ClickTime);
	IF @ActuarialRefreshCooldownSeconds <= @TimeElapsed
		BEGIN
			 UPDATE PrePricing.AppBenefitInterfaceControl
			 SET LastActuarialRefreshDateTime = @ClickTime
			 WHERE 1=1;

			 SET @TimeRemaining = 0;

			 SET @OutputCode = 'Success'; 
			 SET @OutputMessage = 'Continue to data load.';
		END

	ELSE
		BEGIN
			 SET @TimeRemaining = @ActuarialRefreshCooldownSeconds - @TimeElapsed;
			 SET @OutputCode= 'Error'; 
			 SET @OutputMessage = 'Last Actuarial Refresh: ' + ISNULL(FORMAT(@LastActuarialRefreshDateTime, 'hh:mm:ss tt'),'') +'. Retrieving that update. Click again for the next available data load in '+ CAST(@TimeRemaining AS VARCHAR) +' seconds.' 

		END


	SELECT @OutputCode AS OutputCode, @OutputMessage AS OutputMessage, @TimeRemaining AS TimeRemaining;
	END

GO
