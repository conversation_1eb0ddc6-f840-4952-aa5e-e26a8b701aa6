SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcNotPlanLevel_Trends
--
-- CREATOR: <PERSON> Lewis
--
-- CREATED DATE: FEB-07-2020
--
-- DESCRIPTION:   This stored procedure takes the annual trends from Trend_CalcFileSync_Annual, including the 'Part B Rx Pharmacy' reporting category, 
--				and excludes historic trends (i.e. anything older than the current year) for the projected trend process. 
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_CalcFileSync_Annual
--					LkpIntPlanYear
--					
--          Write:  Trend_ProjProcess_CalcNotPlanLevel_Trends
--                  
--
-- VIEWS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FEB-07-2020      1           Initial Version                                                     Jake Lewis
-- NOV-19-2020      2           Include NOLOCK & ROWLOCK                                            Manisha Tyagi
-- NOV-19-2020      3           Table Comparision Logic Added                                       Surya Murthy
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcNotPlanLevel_Trends]

@LastUpdateByID CHAR(13)

AS
    BEGIN
        SET NOCOUNT ON;		 
        -- Declare variables
        DECLARE @CurrentYear INT;
		DECLARE @RecordsCount INT;
		
        SET @CurrentYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsCurrentYear = 1);

		DROP TABLE IF EXISTS #TempComapareTable;
		CREATE TABLE #TempComapareTable (
										[PackageOptionID] [INT] NULL										 									 
										);

		insert into #TempComapareTable SELECT 
			T2.PackageOptionID              
			FROM dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends T2 WITH (NOLOCK)
			WHERE
			NOT EXISTS (SELECT 
					T1.PackageOptionID
					FROM dbo.Trend_CalcFileSync_Annual T1 WITH (NOLOCK) where
					ISNULL(T1.PackageOptionID, 0) = ISNULL(T2.PackageOptionID, 0) AND
					ISNULL(T1.ComponentVersionID, 0) = ISNULL(T2.ComponentVersionID, 0) AND
					ISNULL(T1.ComponentVersionName, 0) = ISNULL(T2.ComponentVersionName, 0) AND
					ISNULL(T1.ProjectionName, 0) = ISNULL(T2.ProjectionName, 0) AND
					ISNULL(T1.PlanTypeGranularity, 0) = ISNULL(T2.PlanTypeGranularity, 0) AND
					ISNULL(T1.PlanTypeGranularityValue, 0) = ISNULL(T2.PlanTypeGranularityValue, 0) AND
					ISNULL(T1.Component, 0) = ISNULL(T2.Component, 0) AND
					ISNULL(T1.PlanYearID, 0) = ISNULL(T2.PlanYearID, 0) AND
					ISNULL(T1.ReportingCategory, 0) = ISNULL(T2.ReportingCategory, 0) AND
					ISNULL(T1.BenefitCategoryID, 0) = ISNULL(T2.BenefitCategoryID, 0) AND
					ISNULL(T1.IsNationwide, 0) = ISNULL(T2.IsNationwide, 0) AND
					ISNULL(T1.ActuarialDivision, 0) = ISNULL(T2.ActuarialDivision, 0) AND
					ISNULL(T1.ActuarialRegion, 0) = ISNULL(T2.ActuarialRegion, 0) AND					
					ISNULL(T1.ActuarialMarket, 0) = ISNULL(T2.ActuarialMarket, 0) AND	
					ISNULL(T1.ProductType, 0) = ISNULL(T2.ProductType, 0) AND
					ISNULL(T1.SNPType, 0) = ISNULL(T2.SNPType, 0) AND
					ISNULL(T1.PlanType, 0) = ISNULL(T2.PlanType, 0) AND
					ISNULL(T1.MAPlanDesign, 0) = ISNULL(T2.MAPlanDesign, 0) AND
					ISNULL(T1.AssumptionName, 0) = ISNULL(T2.AssumptionName, 0) AND
					ISNULL(T1.CostAdjustment, 0) = ISNULL(T2.CostAdjustment, 0) AND
					ISNULL(T1.UseAdjustment, 0) = ISNULL(T2.UseAdjustment, 0) AND
					ISNULL(T1.ImportFileName, 0) = ISNULL(T2.ImportFileName, 0) AND					
					T1.PlanYearID >= @CurrentYear
				   )
 
		SET @RecordsCount = (select count(*) from #TempComapareTable);
		 
		IF (@RecordsCount = 0)
		BEGIN
			insert into #TempComapareTable SELECT 
			T2.PackageOptionID              
			FROM dbo.Trend_CalcFileSync_Annual T2 WITH (NOLOCK)
			WHERE
			NOT EXISTS (SELECT 
					T1.PackageOptionID
					FROM dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends T1 WITH (NOLOCK) where
					ISNULL(T1.PackageOptionID, 0) = ISNULL(T2.PackageOptionID, 0) AND
					ISNULL(T1.ComponentVersionID, 0) = ISNULL(T2.ComponentVersionID, 0) AND
					ISNULL(T1.ComponentVersionName, 0) = ISNULL(T2.ComponentVersionName, 0) AND
					ISNULL(T1.ProjectionName, 0) = ISNULL(T2.ProjectionName, 0) AND
					ISNULL(T1.PlanTypeGranularity, 0) = ISNULL(T2.PlanTypeGranularity, 0) AND
					ISNULL(T1.PlanTypeGranularityValue, 0) = ISNULL(T2.PlanTypeGranularityValue, 0) AND
					ISNULL(T1.Component, 0) = ISNULL(T2.Component, 0) AND
					ISNULL(T1.PlanYearID, 0) = ISNULL(T2.PlanYearID, 0) AND
					ISNULL(T1.ReportingCategory, 0) = ISNULL(T2.ReportingCategory, 0) AND
					ISNULL(T1.BenefitCategoryID, 0) = ISNULL(T2.BenefitCategoryID, 0) AND
					ISNULL(T1.IsNationwide, 0) = ISNULL(T2.IsNationwide, 0) AND
					ISNULL(T1.ActuarialDivision, 0) = ISNULL(T2.ActuarialDivision, 0) AND
					ISNULL(T1.ActuarialRegion, 0) = ISNULL(T2.ActuarialRegion, 0) AND					
					ISNULL(T1.ActuarialMarket, 0) = ISNULL(T2.ActuarialMarket, 0) AND	
					ISNULL(T1.ProductType, 0) = ISNULL(T2.ProductType, 0) AND
					ISNULL(T1.SNPType, 0) = ISNULL(T2.SNPType, 0) AND
					ISNULL(T1.PlanType, 0) = ISNULL(T2.PlanType, 0) AND
					ISNULL(T1.MAPlanDesign, 0) = ISNULL(T2.MAPlanDesign, 0) AND
					ISNULL(T1.AssumptionName, 0) = ISNULL(T2.AssumptionName, 0) AND
					ISNULL(T1.CostAdjustment, 0) = ISNULL(T2.CostAdjustment, 0) AND
					ISNULL(T1.UseAdjustment, 0) = ISNULL(T2.UseAdjustment, 0) AND
					ISNULL(T1.ImportFileName, 0) = ISNULL(T2.ImportFileName, 0)										
				   ) AND T2.PlanYearID >= @CurrentYear
		END;
		SET @RecordsCount = (select count(*) from #TempComapareTable);

		IF (@RecordsCount > 0)
		BEGIN			 
			-- Delete and insert records into the final output table. 
			DELETE  FROM dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends WITH (ROWLOCK) WHERE 1=1;
			INSERT INTO dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends WITH (ROWLOCK)
				(PackageOptionID
				,ComponentVersionID
				,ComponentVersionName
				,ProjectionName
				,PlanTypeGranularity
				,PlanTypeGranularityValue
				,Component
				,PlanYearID
				,ReportingCategory
				,BenefitCategoryID
				,IsNationwide
				,ActuarialDivision
				,ActuarialRegion
				,ActuarialMarket
				,ProductType
				,SNPType
				,PlanType
				,MAPlanDesign
				,AssumptionName
				,CostAdjustment
				,UseAdjustment
				,ImportFileName
				,LastUpdateByID
				,LastUpdateDateTime)
			SELECT  PackageOptionID
				   ,ComponentVersionID
				   ,ComponentVersionName
				   ,ProjectionName
				   ,PlanTypeGranularity
				   ,PlanTypeGranularityValue
				   ,Component
				   ,PlanYearID
				   ,ReportingCategory
				   ,BenefitCategoryID
				   ,IsNationwide
				   ,ActuarialDivision
				   ,ActuarialRegion
				   ,ActuarialMarket
				   ,ProductType
				   ,SNPType
				   ,PlanType
				   ,MAPlanDesign
				   ,AssumptionName
				   ,CostAdjustment
				   ,UseAdjustment
				   ,ImportFileName
				   ,@LastUpdateByID
				   ,GETDATE ()
			FROM    dbo.Trend_CalcFileSync_Annual WITH (NOLOCK)
			WHERE   (PlanYearID >= @CurrentYear);
		END;
    END;
GO
