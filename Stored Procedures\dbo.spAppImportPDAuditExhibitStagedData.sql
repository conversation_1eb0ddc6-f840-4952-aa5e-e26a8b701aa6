-- =============================================      
-- Author:  Chaitanya DUrga K
-- Create date: 2024-May-23
-- Description: spAppImportPDAuditExhibitStagedData PDAuditExhibit Import using staging table 
--      
--      
-- PARAMETERS:      
-- Input:    @StageId 

-- TABLES:    
-- Read:        ImportDataStaging
--              
--            
-- Write:		SavedPDAuditExhibit   
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION   CHANGES MADE											DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2024-May-23		1	     Initial version.										Chaitanya Durga K	
-- 2024-July-15		2		 Added @IsFullReload parameter for Full Reload 			Archana Sahu
--							 functionality
-- 2024-Sep-18		3	     Removing fields										Surya Murthy
----------------------------------------------------------------------------------------------------------------------
-- ======================================================================================================================
CREATE PROCEDURE [dbo].[spAppImportPDAuditExhibitStagedData] 	
	(@StageId VARCHAR(100), @IsFullReload BIT = 0)
AS
BEGIN
	DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;

	DECLARE @tbl__importData TABLE
    (
            ContractPBP VARCHAR(9) ,
			SegmentID CHAR(3) ,
			OrganizationName VARCHAR(100) ,
			PlanName VARCHAR(100) ,
			DivisionName VARCHAR(30) ,
			HumanaRegionName VARCHAR(30) ,
			Market VARCHAR(60),
			ProductType VARCHAR(13) ,
			SnpTypeName VARCHAR(MAX) ,
			PlanType VARCHAR(100) ,
			ActuarialProduct VARCHAR(60),
			BasicPremiumRounded DECIMAL(7, 2) ,
			SupplementalPremiumRounded DECIMAL(7, 2) ,
			TotalPremiumRounded DECIMAL(7, 2) ,
			BidDirectSubsidy DECIMAL(18, 10),
			Formulary VARCHAR(30),			
			ErectileDysfunction VARCHAR(13),
			ErectileDysfunctionTier VARCHAR(60),
			AntiObesityDrugCoverage VARCHAR(13),
			AntiObesityTier VARCHAR(13),
			PrescriptionVitamins VARCHAR(13),
			PrescriptionVitaminsTier VARCHAR(13),
			COPDVBID VARCHAR(13),			
			ZeroDollarRxVBID VARCHAR(13),
			Placeholder4 VARCHAR(13),
			Placeholder5 VARCHAR(13),
			Deductible SMALLINT ,
			DeductibleExcludeAnyTiers VARCHAR(60),
			DeductibleT1 VARCHAR(30),
			DeductibleT2 VARCHAR(30),
			DeductibleT3 VARCHAR(30),
			DeductibleT4 VARCHAR(30),
			DeductibleT5 VARCHAR(30),
			DeductibleT6 VARCHAR(30),
			Part_D_MOOP SMALLINT,
			BenefitString1MonthStandardRetail VARCHAR(60),
			BenefitString3MonthStandardRetail VARCHAR(60),
			BenefitString1MonthPreferredRetail VARCHAR(60),
			BenefitString3MonthPreferredRetail VARCHAR(60),
			BenefitString1MonthStandardMail VARCHAR(60),
			BenefitString3MonthStandardMail VARCHAR(60),
			BenefitString1MonthPreferredMail VARCHAR(60),
			BenefitString3MonthPreferredMail VARCHAR(60),
			ICCostShare1MonthStandardRetailT1 DECIMAL(18, 2),
			ICCostShare1MonthStandardRetailT2 DECIMAL(18, 2),
			ICCostShare1MonthStandardRetailT3 DECIMAL(18, 2),
			ICCostShare1MonthStandardRetailT4 DECIMAL(18, 2),
			ICCostShare1MonthStandardRetailT5 DECIMAL(18, 2),
			ICCostShare1MonthStandardRetailT6 DECIMAL(18, 2),
			ICCostShare3MonthStandardRetailT1 DECIMAL(18, 2),
			ICCostShare3MonthStandardRetailT2 DECIMAL(18, 2),
			ICCostShare3MonthStandardRetailT3 DECIMAL(18, 2),
			ICCostShare3MonthStandardRetailT4 DECIMAL(18, 2),
			ICCostShare3MonthStandardRetailT5 DECIMAL(18, 2),
			ICCostShare3MonthStandardRetailT6 DECIMAL(18, 2),
			ICCostShare1MonthPreferredRetailT1 DECIMAL(18, 2),
			ICCostShare1MonthPreferredRetailT2 DECIMAL(18, 2),
			ICCostShare1MonthPreferredRetailT3 DECIMAL(18, 2),
			ICCostShare1MonthPreferredRetailT4 DECIMAL(18, 2),
			ICCostShare1MonthPreferredRetailT5 DECIMAL(18, 2),
			ICCostShare1MonthPreferredRetailT6 DECIMAL(18, 2),
			ICCostShare3MonthPreferredRetailT1 DECIMAL(18, 2),
			ICCostShare3MonthPreferredRetailT2 DECIMAL(18, 2),
			ICCostShare3MonthPreferredRetailT3 DECIMAL(18, 2),
			ICCostShare3MonthPreferredRetailT4 DECIMAL(18, 2),
			ICCostShare3MonthPreferredRetailT5 DECIMAL(18, 2),
			ICCostShare3MonthPreferredRetailT6 DECIMAL(18, 2),
			ICCostShare1MonthStandardMailT1 DECIMAL(18, 2),
			ICCostShare1MonthStandardMailT2 DECIMAL(18, 2),
			ICCostShare1MonthStandardMailT3 DECIMAL(18, 2),
			ICCostShare1MonthStandardMailT4 DECIMAL(18, 2),
			ICCostShare1MonthStandardMailT5 DECIMAL(18, 2),
			ICCostShare1MonthStandardMailT6 DECIMAL(18, 2),
			ICCostShare3MonthStandardMailT1 DECIMAL(18, 2),
			ICCostShare3MonthStandardMailT2 DECIMAL(18, 2),
			ICCostShare3MonthStandardMailT3 DECIMAL(18, 2),
			ICCostShare3MonthStandardMailT4 DECIMAL(18, 2),
			ICCostShare3MonthStandardMailT5 DECIMAL(18, 2),
			ICCostShare3MonthStandardMailT6 DECIMAL(18, 2),
			ICCostShare1MonthPreferredMailT1 DECIMAL(18, 2),
			ICCostShare1MonthPreferredMailT2 DECIMAL(18, 2),
			ICCostShare1MonthPreferredMailT3 DECIMAL(18, 2),
			ICCostShare1MonthPreferredMailT4 DECIMAL(18, 2),
			ICCostShare1MonthPreferredMailT5 DECIMAL(18, 2),
			ICCostShare1MonthPreferredMailT6 DECIMAL(18, 2),
			ICCostShare3MonthPreferredMailT1 DECIMAL(18, 2),
			ICCostShare3MonthPreferredMailT2 DECIMAL(18, 2),
			ICCostShare3MonthPreferredMailT3 DECIMAL(18, 2),
			ICCostShare3MonthPreferredMailT4 DECIMAL(18, 2),
			ICCostShare3MonthPreferredMailT5 DECIMAL(18, 2),
			ICCostShare3MonthPreferredMailT6 DECIMAL(18, 2),
			BelowICLCoins1MonthStandardRetailT1 DECIMAL(18, 2),
			BelowICLCoins1MonthStandardRetailT2 DECIMAL(18, 2),
			BelowICLCoins1MonthStandardRetailT3 DECIMAL(18, 2),
			BelowICLCoins1MonthStandardRetailT4 DECIMAL(18, 2),
			BelowICLCoins1MonthStandardRetailT5 DECIMAL(18, 2),
			BelowICLCoins1MonthStandardRetailT6 DECIMAL(18, 2),			
			BelowICLCoins1MonthPreferredRetailT1 DECIMAL(18, 2),
			BelowICLCoins1MonthPreferredRetailT2 DECIMAL(18, 2),
			BelowICLCoins1MonthPreferredRetailT3 DECIMAL(18, 2),
			BelowICLCoins1MonthPreferredRetailT4 DECIMAL(18, 2),
			BelowICLCoins1MonthPreferredRetailT5 DECIMAL(18, 2),
			BelowICLCoins1MonthPreferredRetailT6 DECIMAL(18, 2),			 
			InsulinCostShare1MonthStandardRetailT1 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardRetailT2 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardRetailT3 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardRetailT4 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardRetailT5 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardRetailT6 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardRetailT1 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardRetailT2 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardRetailT3 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardRetailT4 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardRetailT5 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardRetailT6 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredRetailT1 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredRetailT2 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredRetailT3 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredRetailT4 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredRetailT5 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredRetailT6 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredRetailT1 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredRetailT2 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredRetailT3 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredRetailT4 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredRetailT5 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredRetailT6 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardMailT1 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardMailT2 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardMailT3 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardMailT4 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardMailT5 DECIMAL(18, 2),
			InsulinCostShare1MonthStandardMailT6 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardMailT1 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardMailT2 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardMailT3 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardMailT4 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardMailT5 DECIMAL(18, 2),
			InsulinCostShare3MonthStandardMailT6 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredMailT1 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredMailT2 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredMailT3 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredMailT4 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredMailT5 DECIMAL(18, 2),
			InsulinCostShare1MonthPreferredMailT6 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredMailT1 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredMailT2 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredMailT3 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredMailT4 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredMailT5 DECIMAL(18, 2),
			InsulinCostShare3MonthPreferredMailT6 DECIMAL(18, 2),
			DaySupply1MonthRetailT1 DECIMAL(18, 2),
			DaySupply1MonthRetailT2 DECIMAL(18, 2),
			DaySupply1MonthRetailT3 DECIMAL(18, 2),
			DaySupply1MonthRetailT4 DECIMAL(18, 2),
			DaySupply1MonthRetailT5 DECIMAL(18, 2),
			DaySupply1MonthRetailT6 DECIMAL(18, 2),
			DaySupply1MonthMailT1 DECIMAL(18, 2),
			DaySupply1MonthMailT2 DECIMAL(18, 2),
			DaySupply1MonthMailT3 DECIMAL(18, 2),
			DaySupply1MonthMailT4 DECIMAL(18, 2),
			DaySupply1MonthMailT5 DECIMAL(18, 2),
			DaySupply1MonthMailT6 DECIMAL(18, 2),
			DaySupply3MonthRetailT1 DECIMAL(18, 2),
			DaySupply3MonthRetailT2 DECIMAL(18, 2),
			DaySupply3MonthRetailT3 DECIMAL(18, 2),
			DaySupply3MonthRetailT4 DECIMAL(18, 2),
			DaySupply3MonthRetailT5 DECIMAL(18, 2),
			DaySupply3MonthRetailT6 DECIMAL(18, 2),
			DaySupply3MonthMailT1 DECIMAL(18, 2),
			DaySupply3MonthMailT2 DECIMAL(18, 2),
			DaySupply3MonthMailT3 DECIMAL(18, 2),
			DaySupply3MonthMailT4 DECIMAL(18, 2),
			DaySupply3MonthMailT5 DECIMAL(18, 2),
			DaySupply3MonthMailT6 DECIMAL(18, 2),
			BaseMemberMonths INT ,
			ProjectedMM DECIMAL(18, 10) ,
			PlanYearLIPercent DECIMAL(18, 10),
			PlanYearDrugNetClaims DECIMAL(18, 10),
			PlanYearAllowedClaims DECIMAL(18, 10),
			AdminPMPM DECIMAL(18, 10),
			PlanYearDrugProfitPercent DECIMAL(18, 10),
			ProjectedRiskScore DECIMAL(18, 10),
			TargetAmountAdjustment DECIMAL(18, 10),
			OOPC DECIMAL(18, 10),
			NonTroop DECIMAL(18, 2),
			QualityInitiatives DECIMAL(18, 10),
			TaxesFees DECIMAL(18, 10),
			DIRCapbeforeReins DECIMAL(18, 10),
			DIRCapafterReins DECIMAL(18, 10),
			BasicPremiumUnrounded DECIMAL(7, 2) ,
			SupplementalPremiumUnrounded DECIMAL(7, 2) ,
			TotalPremiumUnrounded DECIMAL(7, 2) ,
			FormularyID INT,			
			DoacVbid CHAR(3),
			HundredDayFill VARCHAR(20),
			Placeholder1 CHAR(3),
			Placeholder2 CHAR(3),
			Placeholder3 CHAR(3),
			LastUpdateByID CHAR(7),
            LastUpdateDateTime DATETIME
    );
	 INSERT INTO @tbl__importData
				SELECT ContractPBP,				
				FORMAT(SegmentID,'000')  AS SegmentID,
				OrganizationName,
				PlanName,
				DivisionName,
				HumanaRegionName,
				Market,
				ProductType,
				SnpTypeName,
				PlanType,
				ActuarialProduct,
				BasicPremiumRounded,
				SupplementalPremiumRounded,
				TotalPremiumRounded,
				BidDirectSubsidy,
				Formulary,				
				ErectileDysfunction,
				ErectileDysfunctionTier,
				AntiObesityDrugCoverage,
				AntiObesityTier,
				PrescriptionVitamins,
				PrescriptionVitaminsTier,
				COPDVBID,			
				ZeroDollarRxVBID,
				Placeholder4,
				Placeholder5,
				Deductible,
				DeductibleExcludeAnyTiers,
				DeductibleT1,
				DeductibleT2,
				DeductibleT3,
				DeductibleT4,
				DeductibleT5,
				DeductibleT6,
				Part_D_MOOP,
				BenefitString1MonthStandardRetail,
				BenefitString3MonthStandardRetail,
				BenefitString1MonthPreferredRetail,
				BenefitString3MonthPreferredRetail,
				BenefitString1MonthStandardMail,
				BenefitString3MonthStandardMail,
				BenefitString1MonthPreferredMail,
				BenefitString3MonthPreferredMail,
				ICCostShare1MonthStandardRetailT1,
				ICCostShare1MonthStandardRetailT2,
				ICCostShare1MonthStandardRetailT3,
				ICCostShare1MonthStandardRetailT4,
				ICCostShare1MonthStandardRetailT5,
				ICCostShare1MonthStandardRetailT6,
				ICCostShare3MonthStandardRetailT1,
				ICCostShare3MonthStandardRetailT2,
				ICCostShare3MonthStandardRetailT3,
				ICCostShare3MonthStandardRetailT4,
				ICCostShare3MonthStandardRetailT5,
				ICCostShare3MonthStandardRetailT6,
				ICCostShare1MonthPreferredRetailT1,
				ICCostShare1MonthPreferredRetailT2,
				ICCostShare1MonthPreferredRetailT3,
				ICCostShare1MonthPreferredRetailT4,
				ICCostShare1MonthPreferredRetailT5,
				ICCostShare1MonthPreferredRetailT6,
				ICCostShare3MonthPreferredRetailT1,
				ICCostShare3MonthPreferredRetailT2,
				ICCostShare3MonthPreferredRetailT3,
				ICCostShare3MonthPreferredRetailT4,
				ICCostShare3MonthPreferredRetailT5,
				ICCostShare3MonthPreferredRetailT6,
				ICCostShare1MonthStandardMailT1,
				ICCostShare1MonthStandardMailT2,
				ICCostShare1MonthStandardMailT3,
				ICCostShare1MonthStandardMailT4,
				ICCostShare1MonthStandardMailT5,
				ICCostShare1MonthStandardMailT6,
				ICCostShare3MonthStandardMailT1,
				ICCostShare3MonthStandardMailT2,
				ICCostShare3MonthStandardMailT3,
				ICCostShare3MonthStandardMailT4,
				ICCostShare3MonthStandardMailT5,
				ICCostShare3MonthStandardMailT6,
				ICCostShare1MonthPreferredMailT1,
				ICCostShare1MonthPreferredMailT2,
				ICCostShare1MonthPreferredMailT3,
				ICCostShare1MonthPreferredMailT4,
				ICCostShare1MonthPreferredMailT5,
				ICCostShare1MonthPreferredMailT6,
				ICCostShare3MonthPreferredMailT1,
				ICCostShare3MonthPreferredMailT2,
				ICCostShare3MonthPreferredMailT3,
				ICCostShare3MonthPreferredMailT4,
				ICCostShare3MonthPreferredMailT5,
				ICCostShare3MonthPreferredMailT6,
				BelowICLCoins1MonthStandardRetailT1,
				BelowICLCoins1MonthStandardRetailT2,
				BelowICLCoins1MonthStandardRetailT3,
				BelowICLCoins1MonthStandardRetailT4,
				BelowICLCoins1MonthStandardRetailT5,
				BelowICLCoins1MonthStandardRetailT6,				
				BelowICLCoins1MonthPreferredRetailT1,
				BelowICLCoins1MonthPreferredRetailT2,
				BelowICLCoins1MonthPreferredRetailT3,
				BelowICLCoins1MonthPreferredRetailT4,
				BelowICLCoins1MonthPreferredRetailT5,
				BelowICLCoins1MonthPreferredRetailT6,				 
				InsulinCostShare1MonthStandardRetailT1,
				InsulinCostShare1MonthStandardRetailT2,
				InsulinCostShare1MonthStandardRetailT3,
				InsulinCostShare1MonthStandardRetailT4,
				InsulinCostShare1MonthStandardRetailT5,
				InsulinCostShare1MonthStandardRetailT6,
				InsulinCostShare3MonthStandardRetailT1,
				InsulinCostShare3MonthStandardRetailT2,
				InsulinCostShare3MonthStandardRetailT3,
				InsulinCostShare3MonthStandardRetailT4,
				InsulinCostShare3MonthStandardRetailT5,
				InsulinCostShare3MonthStandardRetailT6,
				InsulinCostShare1MonthPreferredRetailT1,
				InsulinCostShare1MonthPreferredRetailT2,
				InsulinCostShare1MonthPreferredRetailT3,
				InsulinCostShare1MonthPreferredRetailT4,
				InsulinCostShare1MonthPreferredRetailT5,
				InsulinCostShare1MonthPreferredRetailT6,
				InsulinCostShare3MonthPreferredRetailT1,
				InsulinCostShare3MonthPreferredRetailT2,
				InsulinCostShare3MonthPreferredRetailT3,
				InsulinCostShare3MonthPreferredRetailT4,
				InsulinCostShare3MonthPreferredRetailT5,
				InsulinCostShare3MonthPreferredRetailT6,
				InsulinCostShare1MonthStandardMailT1,
				InsulinCostShare1MonthStandardMailT2,
				InsulinCostShare1MonthStandardMailT3,
				InsulinCostShare1MonthStandardMailT4,
				InsulinCostShare1MonthStandardMailT5,
				InsulinCostShare1MonthStandardMailT6,
				InsulinCostShare3MonthStandardMailT1,
				InsulinCostShare3MonthStandardMailT2,
				InsulinCostShare3MonthStandardMailT3,
				InsulinCostShare3MonthStandardMailT4,
				InsulinCostShare3MonthStandardMailT5,
				InsulinCostShare3MonthStandardMailT6,
				InsulinCostShare1MonthPreferredMailT1,
				InsulinCostShare1MonthPreferredMailT2,
				InsulinCostShare1MonthPreferredMailT3,
				InsulinCostShare1MonthPreferredMailT4,
				InsulinCostShare1MonthPreferredMailT5,
				InsulinCostShare1MonthPreferredMailT6,
				InsulinCostShare3MonthPreferredMailT1,
				InsulinCostShare3MonthPreferredMailT2,
				InsulinCostShare3MonthPreferredMailT3,
				InsulinCostShare3MonthPreferredMailT4,
				InsulinCostShare3MonthPreferredMailT5,
				InsulinCostShare3MonthPreferredMailT6,
				DaySupply1MonthRetailT1,
				DaySupply1MonthRetailT2,
				DaySupply1MonthRetailT3,
				DaySupply1MonthRetailT4,
				DaySupply1MonthRetailT5,
				DaySupply1MonthRetailT6,
				DaySupply1MonthMailT1,
				DaySupply1MonthMailT2,
				DaySupply1MonthMailT3,
				DaySupply1MonthMailT4,
				DaySupply1MonthMailT5,
				DaySupply1MonthMailT6,
				DaySupply3MonthRetailT1,
				DaySupply3MonthRetailT2,
				DaySupply3MonthRetailT3,
				DaySupply3MonthRetailT4,
				DaySupply3MonthRetailT5,
				DaySupply3MonthRetailT6,
				DaySupply3MonthMailT1,
				DaySupply3MonthMailT2,
				DaySupply3MonthMailT3,
				DaySupply3MonthMailT4,
				DaySupply3MonthMailT5,
				DaySupply3MonthMailT6,
				BaseMemberMonths,
				ProjectedMM,
				PlanYearLIPercent,
				PlanYearDrugNetClaims,
				PlanYearAllowedClaims,
				AdminPMPM,
				PlanYearDrugProfitPercent,
				ProjectedRiskScore,
				TargetAmountAdjustment,
				OOPC,
				NonTroop,
				QualityInitiatives,
				TaxesFees,
				DIRCapbeforeReins,
				DIRCapafterReins,
				BasicPremiumUnrounded,
				SupplementalPremiumUnrounded,
				TotalPremiumUnrounded,
				FormularyID,				
				DoacVbid,
				HundredDayFill,
				Placeholder1,
				Placeholder2,
				Placeholder3,
				@UserId,
				GETDATE()
   FROM
        OPENJSON(@jsonData, '$."Audit Exhibit"')
	WITH
        (
			   ContractPBP VARCHAR(9) ,
				SegmentID INT ,
				OrganizationName VARCHAR(100) ,
				PlanName VARCHAR(100) ,
				DivisionName VARCHAR(30) ,
				HumanaRegionName VARCHAR(30) ,
				Market VARCHAR(60),
				ProductType VARCHAR(13) ,
				SnpTypeName VARCHAR(MAX) ,
				PlanType VARCHAR(100) ,
				ActuarialProduct VARCHAR(60),
				BasicPremiumRounded DECIMAL(7, 2) ,
				SupplementalPremiumRounded DECIMAL(7, 2) ,
				TotalPremiumRounded DECIMAL(7, 2) ,
				BidDirectSubsidy DECIMAL(18, 10),
				Formulary VARCHAR(30),				
				ErectileDysfunction VARCHAR(13),
				ErectileDysfunctionTier VARCHAR(60),
				AntiObesityDrugCoverage VARCHAR(13),
				AntiObesityTier VARCHAR(13),
				PrescriptionVitamins VARCHAR(13),
				PrescriptionVitaminsTier VARCHAR(13),
				COPDVBID VARCHAR(13),				
				ZeroDollarRxVBID VARCHAR(13) '$.ZeroDollarDSNP',
				Placeholder4 VARCHAR(13),
				Placeholder5 VARCHAR(13),
				Deductible SMALLINT ,
				DeductibleExcludeAnyTiers VARCHAR(60),
				DeductibleT1 VARCHAR(30),
				DeductibleT2 VARCHAR(30),
				DeductibleT3 VARCHAR(30),
				DeductibleT4 VARCHAR(30),
				DeductibleT5 VARCHAR(30),
				DeductibleT6 VARCHAR(30),
				Part_D_MOOP SMALLINT,
				BenefitString1MonthStandardRetail VARCHAR(60),
				BenefitString3MonthStandardRetail VARCHAR(60),
				BenefitString1MonthPreferredRetail VARCHAR(60),
				BenefitString3MonthPreferredRetail VARCHAR(60),
				BenefitString1MonthStandardMail VARCHAR(60),
				BenefitString3MonthStandardMail VARCHAR(60),
				BenefitString1MonthPreferredMail VARCHAR(60),
				BenefitString3MonthPreferredMail VARCHAR(60),
				ICCostShare1MonthStandardRetailT1 DECIMAL(18, 2),
				ICCostShare1MonthStandardRetailT2 DECIMAL(18, 2),
				ICCostShare1MonthStandardRetailT3 DECIMAL(18, 2),
				ICCostShare1MonthStandardRetailT4 DECIMAL(18, 2),
				ICCostShare1MonthStandardRetailT5 DECIMAL(18, 2),
				ICCostShare1MonthStandardRetailT6 DECIMAL(18, 2),
				ICCostShare3MonthStandardRetailT1 DECIMAL(18, 2),
				ICCostShare3MonthStandardRetailT2 DECIMAL(18, 2),
				ICCostShare3MonthStandardRetailT3 DECIMAL(18, 2),
				ICCostShare3MonthStandardRetailT4 DECIMAL(18, 2),
				ICCostShare3MonthStandardRetailT5 DECIMAL(18, 2),
				ICCostShare3MonthStandardRetailT6 DECIMAL(18, 2),
				ICCostShare1MonthPreferredRetailT1 DECIMAL(18, 2),
				ICCostShare1MonthPreferredRetailT2 DECIMAL(18, 2),
				ICCostShare1MonthPreferredRetailT3 DECIMAL(18, 2),
				ICCostShare1MonthPreferredRetailT4 DECIMAL(18, 2),
				ICCostShare1MonthPreferredRetailT5 DECIMAL(18, 2),
				ICCostShare1MonthPreferredRetailT6 DECIMAL(18, 2),
				ICCostShare3MonthPreferredRetailT1 DECIMAL(18, 2),
				ICCostShare3MonthPreferredRetailT2 DECIMAL(18, 2),
				ICCostShare3MonthPreferredRetailT3 DECIMAL(18, 2),
				ICCostShare3MonthPreferredRetailT4 DECIMAL(18, 2),
				ICCostShare3MonthPreferredRetailT5 DECIMAL(18, 2),
				ICCostShare3MonthPreferredRetailT6 DECIMAL(18, 2),
				ICCostShare1MonthStandardMailT1 DECIMAL(18, 2),
				ICCostShare1MonthStandardMailT2 DECIMAL(18, 2),
				ICCostShare1MonthStandardMailT3 DECIMAL(18, 2),
				ICCostShare1MonthStandardMailT4 DECIMAL(18, 2),
				ICCostShare1MonthStandardMailT5 DECIMAL(18, 2),
				ICCostShare1MonthStandardMailT6 DECIMAL(18, 2),
				ICCostShare3MonthStandardMailT1 DECIMAL(18, 2),
				ICCostShare3MonthStandardMailT2 DECIMAL(18, 2),
				ICCostShare3MonthStandardMailT3 DECIMAL(18, 2),
				ICCostShare3MonthStandardMailT4 DECIMAL(18, 2),
				ICCostShare3MonthStandardMailT5 DECIMAL(18, 2),
				ICCostShare3MonthStandardMailT6 DECIMAL(18, 2),
				ICCostShare1MonthPreferredMailT1 DECIMAL(18, 2),
				ICCostShare1MonthPreferredMailT2 DECIMAL(18, 2),
				ICCostShare1MonthPreferredMailT3 DECIMAL(18, 2),
				ICCostShare1MonthPreferredMailT4 DECIMAL(18, 2),
				ICCostShare1MonthPreferredMailT5 DECIMAL(18, 2),
				ICCostShare1MonthPreferredMailT6 DECIMAL(18, 2),
				ICCostShare3MonthPreferredMailT1 DECIMAL(18, 2),
				ICCostShare3MonthPreferredMailT2 DECIMAL(18, 2),
				ICCostShare3MonthPreferredMailT3 DECIMAL(18, 2),
				ICCostShare3MonthPreferredMailT4 DECIMAL(18, 2),
				ICCostShare3MonthPreferredMailT5 DECIMAL(18, 2),
				ICCostShare3MonthPreferredMailT6 DECIMAL(18, 2),
				BelowICLCoins1MonthStandardRetailT1 DECIMAL(18, 2),
				BelowICLCoins1MonthStandardRetailT2 DECIMAL(18, 2),
				BelowICLCoins1MonthStandardRetailT3 DECIMAL(18, 2),
				BelowICLCoins1MonthStandardRetailT4 DECIMAL(18, 2),
				BelowICLCoins1MonthStandardRetailT5 DECIMAL(18, 2),
				BelowICLCoins1MonthStandardRetailT6 DECIMAL(18, 2),				 
				BelowICLCoins1MonthPreferredRetailT1 DECIMAL(18, 2),
				BelowICLCoins1MonthPreferredRetailT2 DECIMAL(18, 2),
				BelowICLCoins1MonthPreferredRetailT3 DECIMAL(18, 2),
				BelowICLCoins1MonthPreferredRetailT4 DECIMAL(18, 2),
				BelowICLCoins1MonthPreferredRetailT5 DECIMAL(18, 2),
				BelowICLCoins1MonthPreferredRetailT6 DECIMAL(18, 2),						 
				InsulinCostShare1MonthStandardRetailT1 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardRetailT2 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardRetailT3 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardRetailT4 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardRetailT5 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardRetailT6 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardRetailT1 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardRetailT2 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardRetailT3 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardRetailT4 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardRetailT5 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardRetailT6 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredRetailT1 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredRetailT2 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredRetailT3 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredRetailT4 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredRetailT5 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredRetailT6 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredRetailT1 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredRetailT2 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredRetailT3 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredRetailT4 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredRetailT5 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredRetailT6 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardMailT1 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardMailT2 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardMailT3 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardMailT4 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardMailT5 DECIMAL(18, 2),
				InsulinCostShare1MonthStandardMailT6 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardMailT1 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardMailT2 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardMailT3 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardMailT4 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardMailT5 DECIMAL(18, 2),
				InsulinCostShare3MonthStandardMailT6 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredMailT1 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredMailT2 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredMailT3 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredMailT4 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredMailT5 DECIMAL(18, 2),
				InsulinCostShare1MonthPreferredMailT6 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredMailT1 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredMailT2 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredMailT3 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredMailT4 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredMailT5 DECIMAL(18, 2),
				InsulinCostShare3MonthPreferredMailT6 DECIMAL(18, 2),
				DaySupply1MonthRetailT1 DECIMAL(18, 2),
				DaySupply1MonthRetailT2 DECIMAL(18, 2),
				DaySupply1MonthRetailT3 DECIMAL(18, 2),
				DaySupply1MonthRetailT4 DECIMAL(18, 2),
				DaySupply1MonthRetailT5 DECIMAL(18, 2),
				DaySupply1MonthRetailT6 DECIMAL(18, 2),
				DaySupply1MonthMailT1 DECIMAL(18, 2),
				DaySupply1MonthMailT2 DECIMAL(18, 2),
				DaySupply1MonthMailT3 DECIMAL(18, 2),
				DaySupply1MonthMailT4 DECIMAL(18, 2),
				DaySupply1MonthMailT5 DECIMAL(18, 2),
				DaySupply1MonthMailT6 DECIMAL(18, 2),
				DaySupply3MonthRetailT1 DECIMAL(18, 2),
				DaySupply3MonthRetailT2 DECIMAL(18, 2),
				DaySupply3MonthRetailT3 DECIMAL(18, 2),
				DaySupply3MonthRetailT4 DECIMAL(18, 2),
				DaySupply3MonthRetailT5 DECIMAL(18, 2),
				DaySupply3MonthRetailT6 DECIMAL(18, 2),
				DaySupply3MonthMailT1 DECIMAL(18, 2),
				DaySupply3MonthMailT2 DECIMAL(18, 2),
				DaySupply3MonthMailT3 DECIMAL(18, 2),
				DaySupply3MonthMailT4 DECIMAL(18, 2),
				DaySupply3MonthMailT5 DECIMAL(18, 2),
				DaySupply3MonthMailT6 DECIMAL(18, 2),
				BaseMemberMonths INT ,
				ProjectedMM DECIMAL(18, 10) ,
				PlanYearLIPercent DECIMAL(18, 10),
				PlanYearDrugNetClaims DECIMAL(18, 10),
				PlanYearAllowedClaims DECIMAL(18, 10),
				AdminPMPM DECIMAL(18, 10),
				PlanYearDrugProfitPercent DECIMAL(18, 10),
				ProjectedRiskScore DECIMAL(18, 10),
				TargetAmountAdjustment DECIMAL(18, 10),
				OOPC DECIMAL(18, 10),
				NonTroop DECIMAL(18, 2),
				QualityInitiatives DECIMAL(18, 10),
				TaxesFees DECIMAL(18, 10),
				DIRCapbeforeReins DECIMAL(18, 10),
				DIRCapafterReins DECIMAL(18, 10),
				BasicPremiumUnrounded DECIMAL(7, 2) ,
				SupplementalPremiumUnrounded DECIMAL(7, 2) ,
				TotalPremiumUnrounded DECIMAL(7, 2) ,
				FormularyID INT,				
				DoacVbid CHAR(3),
				HundredDayFill VARCHAR(20),
				Placeholder1 CHAR(3),
				Placeholder2 CHAR(3),
				Placeholder3 CHAR(3)
        );


	IF(@IsFullReload=1)
	BEGIN 
		-- Delete All Data and insert 
		DELETE FROM dbo.SavedPDAuditExhibit  Where 1=1;
	END 
	ELSE
	BEGIN
		DELETE spe FROM dbo.SavedPDAuditExhibit spe JOIN @tbl__importData pde
		ON spe.ContractPBP = pde.ContractPBP
			   AND spe.SegmentID = pde.SegmentID
	END


	 INSERT INTO dbo.SavedPDAuditExhibit
        (
            ContractPBP,
			SegmentID,
			OrganizationName,
			PlanName,
			DivisionName,
			HumanaRegionName,
			Market,
			ProductType,
			SnpTypeName,
			PlanType,
			ActuarialProduct,
			BasicPremiumRounded,
			SupplementalPremiumRounded,
			TotalPremiumRounded,
			BidDirectSubsidy,
			Formulary,			
			ErectileDysfunction,
			ErectileDysfunctionTier,
			AntiObesityDrugCoverage,
			AntiObesityTier,
			PrescriptionVitamins,
			PrescriptionVitaminsTier,
			COPDVBID,			
			ZeroDollarRxVBID,
			Placeholder4,
			Placeholder5,
			Deductible,
			DeductibleExcludeAnyTiers,
			DeductibleT1,
			DeductibleT2,
			DeductibleT3,
			DeductibleT4,
			DeductibleT5,
			DeductibleT6,
			Part_D_MOOP,
			BenefitString1MonthStandardRetail,
			BenefitString3MonthStandardRetail,
			BenefitString1MonthPreferredRetail,
			BenefitString3MonthPreferredRetail,
			BenefitString1MonthStandardMail,
			BenefitString3MonthStandardMail,
			BenefitString1MonthPreferredMail,
			BenefitString3MonthPreferredMail,
			ICCostShare1MonthStandardRetailT1,
			ICCostShare1MonthStandardRetailT2,
			ICCostShare1MonthStandardRetailT3,
			ICCostShare1MonthStandardRetailT4,
			ICCostShare1MonthStandardRetailT5,
			ICCostShare1MonthStandardRetailT6,
			ICCostShare3MonthStandardRetailT1,
			ICCostShare3MonthStandardRetailT2,
			ICCostShare3MonthStandardRetailT3,
			ICCostShare3MonthStandardRetailT4,
			ICCostShare3MonthStandardRetailT5,
			ICCostShare3MonthStandardRetailT6,
			ICCostShare1MonthPreferredRetailT1,
			ICCostShare1MonthPreferredRetailT2,
			ICCostShare1MonthPreferredRetailT3,
			ICCostShare1MonthPreferredRetailT4,
			ICCostShare1MonthPreferredRetailT5,
			ICCostShare1MonthPreferredRetailT6,
			ICCostShare3MonthPreferredRetailT1,
			ICCostShare3MonthPreferredRetailT2,
			ICCostShare3MonthPreferredRetailT3,
			ICCostShare3MonthPreferredRetailT4,
			ICCostShare3MonthPreferredRetailT5,
			ICCostShare3MonthPreferredRetailT6,
			ICCostShare1MonthStandardMailT1,
			ICCostShare1MonthStandardMailT2,
			ICCostShare1MonthStandardMailT3,
			ICCostShare1MonthStandardMailT4,
			ICCostShare1MonthStandardMailT5,
			ICCostShare1MonthStandardMailT6,
			ICCostShare3MonthStandardMailT1,
			ICCostShare3MonthStandardMailT2,
			ICCostShare3MonthStandardMailT3,
			ICCostShare3MonthStandardMailT4,
			ICCostShare3MonthStandardMailT5,
			ICCostShare3MonthStandardMailT6,
			ICCostShare1MonthPreferredMailT1,
			ICCostShare1MonthPreferredMailT2,
			ICCostShare1MonthPreferredMailT3,
			ICCostShare1MonthPreferredMailT4,
			ICCostShare1MonthPreferredMailT5,
			ICCostShare1MonthPreferredMailT6,
			ICCostShare3MonthPreferredMailT1,
			ICCostShare3MonthPreferredMailT2,
			ICCostShare3MonthPreferredMailT3,
			ICCostShare3MonthPreferredMailT4,
			ICCostShare3MonthPreferredMailT5,
			ICCostShare3MonthPreferredMailT6,
			BelowICLCoins1MonthStandardRetailT1,
			BelowICLCoins1MonthStandardRetailT2,
			BelowICLCoins1MonthStandardRetailT3,
			BelowICLCoins1MonthStandardRetailT4,
			BelowICLCoins1MonthStandardRetailT5,
			BelowICLCoins1MonthStandardRetailT6,			
			BelowICLCoins1MonthPreferredRetailT1,
			BelowICLCoins1MonthPreferredRetailT2,
			BelowICLCoins1MonthPreferredRetailT3,
			BelowICLCoins1MonthPreferredRetailT4,
			BelowICLCoins1MonthPreferredRetailT5,
			BelowICLCoins1MonthPreferredRetailT6,			 
			InsulinCostShare1MonthStandardRetailT1,
			InsulinCostShare1MonthStandardRetailT2,
			InsulinCostShare1MonthStandardRetailT3,
			InsulinCostShare1MonthStandardRetailT4,
			InsulinCostShare1MonthStandardRetailT5,
			InsulinCostShare1MonthStandardRetailT6,
			InsulinCostShare3MonthStandardRetailT1,
			InsulinCostShare3MonthStandardRetailT2,
			InsulinCostShare3MonthStandardRetailT3,
			InsulinCostShare3MonthStandardRetailT4,
			InsulinCostShare3MonthStandardRetailT5,
			InsulinCostShare3MonthStandardRetailT6,
			InsulinCostShare1MonthPreferredRetailT1,
			InsulinCostShare1MonthPreferredRetailT2,
			InsulinCostShare1MonthPreferredRetailT3,
			InsulinCostShare1MonthPreferredRetailT4,
			InsulinCostShare1MonthPreferredRetailT5,
			InsulinCostShare1MonthPreferredRetailT6,
			InsulinCostShare3MonthPreferredRetailT1,
			InsulinCostShare3MonthPreferredRetailT2,
			InsulinCostShare3MonthPreferredRetailT3,
			InsulinCostShare3MonthPreferredRetailT4,
			InsulinCostShare3MonthPreferredRetailT5,
			InsulinCostShare3MonthPreferredRetailT6,
			InsulinCostShare1MonthStandardMailT1,
			InsulinCostShare1MonthStandardMailT2,
			InsulinCostShare1MonthStandardMailT3,
			InsulinCostShare1MonthStandardMailT4,
			InsulinCostShare1MonthStandardMailT5,
			InsulinCostShare1MonthStandardMailT6,
			InsulinCostShare3MonthStandardMailT1,
			InsulinCostShare3MonthStandardMailT2,
			InsulinCostShare3MonthStandardMailT3,
			InsulinCostShare3MonthStandardMailT4,
			InsulinCostShare3MonthStandardMailT5,
			InsulinCostShare3MonthStandardMailT6,
			InsulinCostShare1MonthPreferredMailT1,
			InsulinCostShare1MonthPreferredMailT2,
			InsulinCostShare1MonthPreferredMailT3,
			InsulinCostShare1MonthPreferredMailT4,
			InsulinCostShare1MonthPreferredMailT5,
			InsulinCostShare1MonthPreferredMailT6,
			InsulinCostShare3MonthPreferredMailT1,
			InsulinCostShare3MonthPreferredMailT2,
			InsulinCostShare3MonthPreferredMailT3,
			InsulinCostShare3MonthPreferredMailT4,
			InsulinCostShare3MonthPreferredMailT5,
			InsulinCostShare3MonthPreferredMailT6,
			DaySupply1MonthRetailT1,
			DaySupply1MonthRetailT2,
			DaySupply1MonthRetailT3,
			DaySupply1MonthRetailT4,
			DaySupply1MonthRetailT5,
			DaySupply1MonthRetailT6,
			DaySupply1MonthMailT1,
			DaySupply1MonthMailT2,
			DaySupply1MonthMailT3,
			DaySupply1MonthMailT4,
			DaySupply1MonthMailT5,
			DaySupply1MonthMailT6,
			DaySupply3MonthRetailT1,
			DaySupply3MonthRetailT2,
			DaySupply3MonthRetailT3,
			DaySupply3MonthRetailT4,
			DaySupply3MonthRetailT5,
			DaySupply3MonthRetailT6,
			DaySupply3MonthMailT1,
			DaySupply3MonthMailT2,
			DaySupply3MonthMailT3,
			DaySupply3MonthMailT4,
			DaySupply3MonthMailT5,
			DaySupply3MonthMailT6,
			BaseMemberMonths,
			ProjectedMM,
			PlanYearLIPercent,
			PlanYearDrugNetClaims,
			PlanYearAllowedClaims,
			AdminPMPM,
			PlanYearDrugProfitPercent,
			ProjectedRiskScore,
			TargetAmountAdjustment,
			OOPC,
			NonTroop,
			QualityInitiatives,
			TaxesFees,
			DIRCapbeforeReins,
			DIRCapafterReins,
			BasicPremiumUnrounded,
			SupplementalPremiumUnrounded,
			TotalPremiumUnrounded,
			FormularyID,
			DoacVbid,
			HundredDayFill,
			Placeholder1,
			Placeholder2,
			Placeholder3,
            UserID,
            CreatedDate            
        )
	  SELECT ContractPBP,
		SegmentID,
		OrganizationName,
		PlanName,
		DivisionName,
		HumanaRegionName,
		Market,
		ProductType,
		SnpTypeName,
		PlanType,
		ActuarialProduct,
		BasicPremiumRounded,
		SupplementalPremiumRounded,
		TotalPremiumRounded,
		BidDirectSubsidy,
		Formulary,		
		ErectileDysfunction,
		ErectileDysfunctionTier,
		AntiObesityDrugCoverage,
		AntiObesityTier,
		PrescriptionVitamins,
		PrescriptionVitaminsTier,
		COPDVBID,	
		ZeroDollarRxVBID,
		Placeholder4,
		Placeholder5,
		Deductible,
		DeductibleExcludeAnyTiers,
		DeductibleT1,
		DeductibleT2,
		DeductibleT3,
		DeductibleT4,
		DeductibleT5,
		DeductibleT6,
		Part_D_MOOP,
		BenefitString1MonthStandardRetail,
		BenefitString3MonthStandardRetail,
		BenefitString1MonthPreferredRetail,
		BenefitString3MonthPreferredRetail,
		BenefitString1MonthStandardMail,
		BenefitString3MonthStandardMail,
		BenefitString1MonthPreferredMail,
		BenefitString3MonthPreferredMail,
		ICCostShare1MonthStandardRetailT1,
		ICCostShare1MonthStandardRetailT2,
		ICCostShare1MonthStandardRetailT3,
		ICCostShare1MonthStandardRetailT4,
		ICCostShare1MonthStandardRetailT5,
		ICCostShare1MonthStandardRetailT6,
		ICCostShare3MonthStandardRetailT1,
		ICCostShare3MonthStandardRetailT2,
		ICCostShare3MonthStandardRetailT3,
		ICCostShare3MonthStandardRetailT4,
		ICCostShare3MonthStandardRetailT5,
		ICCostShare3MonthStandardRetailT6,
		ICCostShare1MonthPreferredRetailT1,
		ICCostShare1MonthPreferredRetailT2,
		ICCostShare1MonthPreferredRetailT3,
		ICCostShare1MonthPreferredRetailT4,
		ICCostShare1MonthPreferredRetailT5,
		ICCostShare1MonthPreferredRetailT6,
		ICCostShare3MonthPreferredRetailT1,
		ICCostShare3MonthPreferredRetailT2,
		ICCostShare3MonthPreferredRetailT3,
		ICCostShare3MonthPreferredRetailT4,
		ICCostShare3MonthPreferredRetailT5,
		ICCostShare3MonthPreferredRetailT6,
		ICCostShare1MonthStandardMailT1,
		ICCostShare1MonthStandardMailT2,
		ICCostShare1MonthStandardMailT3,
		ICCostShare1MonthStandardMailT4,
		ICCostShare1MonthStandardMailT5,
		ICCostShare1MonthStandardMailT6,
		ICCostShare3MonthStandardMailT1,
		ICCostShare3MonthStandardMailT2,
		ICCostShare3MonthStandardMailT3,
		ICCostShare3MonthStandardMailT4,
		ICCostShare3MonthStandardMailT5,
		ICCostShare3MonthStandardMailT6,
		ICCostShare1MonthPreferredMailT1,
		ICCostShare1MonthPreferredMailT2,
		ICCostShare1MonthPreferredMailT3,
		ICCostShare1MonthPreferredMailT4,
		ICCostShare1MonthPreferredMailT5,
		ICCostShare1MonthPreferredMailT6,
		ICCostShare3MonthPreferredMailT1,
		ICCostShare3MonthPreferredMailT2,
		ICCostShare3MonthPreferredMailT3,
		ICCostShare3MonthPreferredMailT4,
		ICCostShare3MonthPreferredMailT5,
		ICCostShare3MonthPreferredMailT6,
		BelowICLCoins1MonthStandardRetailT1,
		BelowICLCoins1MonthStandardRetailT2,
		BelowICLCoins1MonthStandardRetailT3,
		BelowICLCoins1MonthStandardRetailT4,
		BelowICLCoins1MonthStandardRetailT5,
		BelowICLCoins1MonthStandardRetailT6,		
		BelowICLCoins1MonthPreferredRetailT1,
		BelowICLCoins1MonthPreferredRetailT2,
		BelowICLCoins1MonthPreferredRetailT3,
		BelowICLCoins1MonthPreferredRetailT4,
		BelowICLCoins1MonthPreferredRetailT5,
		BelowICLCoins1MonthPreferredRetailT6,		 
		InsulinCostShare1MonthStandardRetailT1,
		InsulinCostShare1MonthStandardRetailT2,
		InsulinCostShare1MonthStandardRetailT3,
		InsulinCostShare1MonthStandardRetailT4,
		InsulinCostShare1MonthStandardRetailT5,
		InsulinCostShare1MonthStandardRetailT6,
		InsulinCostShare3MonthStandardRetailT1,
		InsulinCostShare3MonthStandardRetailT2,
		InsulinCostShare3MonthStandardRetailT3,
		InsulinCostShare3MonthStandardRetailT4,
		InsulinCostShare3MonthStandardRetailT5,
		InsulinCostShare3MonthStandardRetailT6,
		InsulinCostShare1MonthPreferredRetailT1,
		InsulinCostShare1MonthPreferredRetailT2,
		InsulinCostShare1MonthPreferredRetailT3,
		InsulinCostShare1MonthPreferredRetailT4,
		InsulinCostShare1MonthPreferredRetailT5,
		InsulinCostShare1MonthPreferredRetailT6,
		InsulinCostShare3MonthPreferredRetailT1,
		InsulinCostShare3MonthPreferredRetailT2,
		InsulinCostShare3MonthPreferredRetailT3,
		InsulinCostShare3MonthPreferredRetailT4,
		InsulinCostShare3MonthPreferredRetailT5,
		InsulinCostShare3MonthPreferredRetailT6,
		InsulinCostShare1MonthStandardMailT1,
		InsulinCostShare1MonthStandardMailT2,
		InsulinCostShare1MonthStandardMailT3,
		InsulinCostShare1MonthStandardMailT4,
		InsulinCostShare1MonthStandardMailT5,
		InsulinCostShare1MonthStandardMailT6,
		InsulinCostShare3MonthStandardMailT1,
		InsulinCostShare3MonthStandardMailT2,
		InsulinCostShare3MonthStandardMailT3,
		InsulinCostShare3MonthStandardMailT4,
		InsulinCostShare3MonthStandardMailT5,
		InsulinCostShare3MonthStandardMailT6,
		InsulinCostShare1MonthPreferredMailT1,
		InsulinCostShare1MonthPreferredMailT2,
		InsulinCostShare1MonthPreferredMailT3,
		InsulinCostShare1MonthPreferredMailT4,
		InsulinCostShare1MonthPreferredMailT5,
		InsulinCostShare1MonthPreferredMailT6,
		InsulinCostShare3MonthPreferredMailT1,
		InsulinCostShare3MonthPreferredMailT2,
		InsulinCostShare3MonthPreferredMailT3,
		InsulinCostShare3MonthPreferredMailT4,
		InsulinCostShare3MonthPreferredMailT5,
		InsulinCostShare3MonthPreferredMailT6,
		DaySupply1MonthRetailT1,
		DaySupply1MonthRetailT2,
		DaySupply1MonthRetailT3,
		DaySupply1MonthRetailT4,
		DaySupply1MonthRetailT5,
		DaySupply1MonthRetailT6,
		DaySupply1MonthMailT1,
		DaySupply1MonthMailT2,
		DaySupply1MonthMailT3,
		DaySupply1MonthMailT4,
		DaySupply1MonthMailT5,
		DaySupply1MonthMailT6,
		DaySupply3MonthRetailT1,
		DaySupply3MonthRetailT2,
		DaySupply3MonthRetailT3,
		DaySupply3MonthRetailT4,
		DaySupply3MonthRetailT5,
		DaySupply3MonthRetailT6,
		DaySupply3MonthMailT1,
		DaySupply3MonthMailT2,
		DaySupply3MonthMailT3,
		DaySupply3MonthMailT4,
		DaySupply3MonthMailT5,
		DaySupply3MonthMailT6,
		BaseMemberMonths,
		ProjectedMM,
		PlanYearLIPercent,
		PlanYearDrugNetClaims,
		PlanYearAllowedClaims,
		AdminPMPM,
		PlanYearDrugProfitPercent,
		ProjectedRiskScore,
		TargetAmountAdjustment,
		OOPC,
		NonTroop,
		QualityInitiatives,
		TaxesFees,
		DIRCapbeforeReins,
		DIRCapafterReins,
		BasicPremiumUnrounded,
		SupplementalPremiumUnrounded,
		TotalPremiumUnrounded,
		FormularyID,
		DoacVbid,
		HundredDayFill,
		Placeholder1,
		Placeholder2,
		Placeholder3,LastUpdateByID,
            LastUpdateDateTime 
		FROM @tbl__importData	    

	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END;


GO
