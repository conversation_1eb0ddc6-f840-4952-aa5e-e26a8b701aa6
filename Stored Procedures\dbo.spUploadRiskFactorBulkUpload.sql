SET QUOTED_IDENTIFIER ON
GO
SET AN<PERSON>_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUploadRiskFactorBulkUpload
--
-- CREATOR: Surya <PERSON>rthy
--
-- CREATED DATE: 2021-Jan-19
--
-- DESCRIPTION: Stored Procedure responsible for Risk Factor Bulk Upload in the MA Model
--
-- PARAMETERS:
--	Input:
--		@inputTable		 
--		@UserID
--  Output:
--		@MessageFromBackend
--		@Result
-- TABLES:
--	Read:
--		LkpIntDemogIndicators
--      LkpIntPlanYear
--	Write:
--      Saved<PERSON>lan<PERSON>eader
--		SavedPlanRiskFactorDetail
--		SavedForecastSetup
--		PlanChangeLog
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2021-Jan-19     1           Initial Version														Surya Murthy
-- 2022-sep-01     2           Added NewID()                                                        Chaitanya Durga K 
-- 2023-Jan-20	   3		   Removed CountyCode condation while delete							Vikrant Bagal
-- 2023-Sep-25	   4		   Rowlocks added for deadlock issues									Surya Murthy
-------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUploadRiskFactorBulkUpload]
(
    @inputTable ImportRiskFactorsDataTable READONLY,
    @UserID CHAR(7),
	@MessageFromBackend varchar(max) output,
	@Result BIT OUT
)
AS
    SET NOCOUNT ON;	 

    --------------------------------------------------------------------------------------------------------------------
    --Declarations------------------------------------------------------------------------------------------------------     
    DECLARE @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear()

    DECLARE @LastUpdate DATETIME
    SET @LastUpdate = GETDATE()

	IF (SELECT  OBJECT_ID ('tempdb..#TempBulkImportRiskFactorTable')) IS NOT NULL
		DROP TABLE #TempBulkImportRiskFactorTable;
    SELECT  a.ForecastID ForecastID,
			a.FilePath FilePath,
			a.ExpNonDual ExpNonDual,
			a.ProjNonDual ProjNonDual,
			a.ExpDual ExpDual,
			a.ProjDual ProjDual,			
			CAST(SUBSTRING(RIGHT('00000'+ LTRIM(a.ZipCode),5),1,2) AS INT) StateID,
			SUBSTRING(RIGHT('00000'+ LTRIM(a.ZipCode),5),3,3) CountyCode
    INTO #TempBulkImportRiskFactorTable
    FROM @inputTable a 
    --------------------------------------------------------------------------------------------------------------------
    --Processing--------------------------------------------------------------------------------------------------------
    BEGIN TRY
    BEGIN TRANSACTION riskscoretrans   	
		DELETE a FROM SavedPlanRiskFactorDetail a WITH(ROWLOCK)
		INNER JOIN #TempBulkImportRiskFactorTable b ON b.ForecastID = a.ForecastID  

		DELETE FROM SavedPlanRiskScoreOverride WITH(ROWLOCK)
		WHERE ForecastID IN (SELECT ForecastID FROM #TempBulkImportRiskFactorTable);

	    BEGIN
		    INSERT INTO SavedPlanRiskFactorDetail WITH(ROWLOCK)
		    SELECT
			    @PlanYearID,
			    a.ForecastID,
			    a.StateID,
			    a.CountyCode,
			    b.DemogIndicator,
			    1, --IsExperience
			    (CASE b.DemogIndicator
				    WHEN 1
					    THEN a.ExpNonDual
				    WHEN 2
					    THEN a.ExpDual
			    END),				
				a.FilePath,
			    @UserID,
			    @LastUpdate
			FROM LkpIntDemogIndicators b CROSS JOIN #TempBulkImportRiskFactorTable a
            WHERE b.IsBiddable = 1 AND b.DualEligibleTypeID <> 2 AND (b.DemogIndicator = 1 OR b.DemogIndicator = 2)
	    END
	    BEGIN
		    INSERT INTO SavedPlanRiskFactorDetail WITH(ROWLOCK)
		    SELECT
			    @PlanYearID,
			    a.ForecastID,
			    a.StateID,
			    a.CountyCode,
			    b.DemogIndicator,
			    0, --IsExperience
			    (CASE b.DemogIndicator
				    WHEN 1
					    THEN a.ProjNonDual
				    WHEN 2
					    THEN a.ProjDual
			    END),
				a.FilePath,
			    @UserID,
			    @LastUpdate
			FROM LkpIntDemogIndicators b CROSS JOIN #TempBulkImportRiskFactorTable a
            WHERE b.IsBiddable = 1 AND b.DualEligibleTypeID <> 2 AND (b.DemogIndicator = 1 OR b.DemogIndicator = 2)

		    UPDATE SavedForecastSetup WITH(ROWLOCK)
            SET IsToReprice = 1,
                LastUpdateByID = @UserID,
                LastUpdateDateTime = @LastUpdate
            WHERE ForecastID IN (SELECT ForecastID FROM #TempBulkImportRiskFactorTable);
		END		
	-- ---------------------------------------------------------------------------------
	-- --PlanChangeLog Entry------------------------------------------------------------		 
		INSERT INTO PlanChangeLog WITH(ROWLOCK)
				SELECT					 
					a.ForecastID,
					'Risk Scores',
					NULL,
					@UserID,
				    @LastUpdate,
					NewID()
			FROM #TempBulkImportRiskFactorTable a
		    set @Result=1
        SET @MessageFromBackend='';		
	    COMMIT TRANSACTION riskscoretrans
    END TRY
    BEGIN CATCH	
        ROLLBACK TRANSACTION riskscoretrans
		set @Result=0
        SET @MessageFromBackend=ERROR_MESSAGE();
    END CATCH
GO
