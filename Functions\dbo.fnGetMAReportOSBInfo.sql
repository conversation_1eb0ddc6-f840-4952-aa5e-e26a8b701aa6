SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/*===============================================================================*/
-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMAReportOSBInfo
--
-- AUTHOR: <PERSON> Galloway
--
-- CREATED DATE: 2009-Mar-16
-- HEADER UPDATED: 2010-Nov-10
--
-- DESCRIPTION: Pulls Optional Supplemental Benefit information for bidable and active plans
--              (used for Auditing Supplemental Benefits)
--
-- PARAMETERS:
--	Input:
--      @PlanYearID
--      @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--      ArcPerIntOptionalPackageDetail
--      ArcPerIntOptionalPackageHeader
--      ArcSavedPlanHeader
--      ArcSavedPlanOptionalPackageDetail
--		MAReportPlanLevel
--      PerIntOptionalPackageDetail
--      PerIntOptionalPackageHeader
--      SavedPlanHeader
--      SavedPlanOptionalPackageDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE										                DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2009-Mar-17		1		    Initial Version									                    Keith Galloway
-- 2010-Oct-05      2           Added Arc tables for past year reference. Removed @PlanVersion      Joe Casey
--                                  as a parameter.  Default PlanVersion = 1
-- 2010-Nov-10      3           Removed several tables to avoid duplication. Also added IsHidden    Joe Casey
--                                  to where clauses. Also added PackageIndex to inner query to
--                                  have a more effective join.
-- 2010-Nov-10		4		    Added PlanTypeName,	ExpensePMPM, Profit, MemberPremium,				Jiao Chen
--									ProjectedMemberMonths.		
-- 2011-Jan-29		5			Changed reference to TotalProjectedMemberMonth ExpensePMPM,	Profit	Jiao Chen	
--									and Member Premium	
-- 2011-Sep-21		6			Modified MemberPremium to eliminate repeat counting ExpensePMPM		Alex Rezmerski
--									and Profit		
-- 2012-Jan-18		7			Changed ExpensePMPM and Profit to be weighted on					Alex Rezmerski
--								PackageAllowedPMPM and changed MemberPremium to be sum of 
--								NetPMPM, ExpensePMPM and Profit	                    
-- 2013-Oct-13		8			Modified to Include SegmentId										Anubhav Mishra
-- 2015-Oct-09      9           Modified logic to exclude bidYear from Arc Tables                   Kritika Singh
-- 2019-Feb-04		10			Modified Expense and Profit formulas								Keith Galloway
-- 2019-Feb-12		11			Modified MemberPremuim formula										Phillip Leigh
-- 2022-Jun-29		12			Limited pull from sph to only relevant fields						Aleksandar Dimitrijevic
-- 2024-Feb-1         13        ADD audit Columns											           Latoya Garvey
----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMAReportOSBInfo]
    (
    @PlanYearID SMALLINT,
    @ForecastID INT
    )
RETURNS TABLE RETURN
	(
	SELECT
	    packagetotals.PlanYearID,
        packagetotals.ContractNumber,
        packagetotals.PlanID,
        packagetotals.SegmentId,  --Added SegmentId
        packagetotals.ForecastID,
        PlanVersion = 1, --Default to 1 for now
        packagetotals.Name,							--Package Name
        packagetotals.PackageAllowedPMPM,
        packagetotals.PackageCostSharePMPM,
        perDetail.PricingComponentDescription,	--Item Description in Package
        perDetail.AllowedUtilizationTypeID,
        perDetail.AllowedUtilzationPer1000,
        perDetail.AllowedAverageCost,
        perDetail.MeasurementUnitCode,
        perDetail.EnrolleeCostShareUtilization,
        perDetail.EnrolleeAverageCostShare,
		pd.PlanTypeName,
        AllowedPMPM =
	        (
	        (perDetail.AllowedUtilzationPer1000/1000)*
	        (perDetail.AllowedAverageCost/12)
	        ),
        CostSharePMPM =
	        (
	        CASE WHEN perDetail.MeasurementUnitCode = 'Coin' THEN
		        perDetail.EnrolleeCostShareUtilization * 
		        perDetail.EnrolleeAverageCostShare
	        ELSE 
		        perDetail.EnrolleeCostShareUtilization * 
		        perDetail.EnrolleeAverageCostShare / 12000
	        END
	        ),
        NetPMPM = 
	        (
	        (perDetail.AllowedUtilzationPer1000/1000)*
	        (perDetail.AllowedAverageCost/12)
	        )-
	        (
	        CASE WHEN perDetail.MeasurementUnitCode = 'Coin' THEN
		        perDetail.EnrolleeCostShareUtilization * 
		        perDetail.EnrolleeAverageCostShare
	        ELSE 
		        perDetail.EnrolleeCostShareUtilization * 
		        perDetail.EnrolleeAverageCostShare / 12000
	        END
	        ),


		ExpensePMPM = 
			((perDetail.AllowedUtilzationPer1000/1000)*
			(perDetail.AverageAdminExpense/12)),


		Profit = 
			((perDetail.AllowedUtilzationPer1000/1000)*
			(perDetail.AnnualizedAverageProfit/12)),


		MemberPremium = (
	        (perDetail.AllowedUtilzationPer1000/1000)*
	        (perDetail.AllowedAverageCost/12)
	        )-
	        (
	        CASE WHEN perDetail.MeasurementUnitCode = 'Coin' THEN
		        perDetail.EnrolleeCostShareUtilization * 
		        perDetail.EnrolleeAverageCostShare
	        ELSE 
		        perDetail.EnrolleeCostShareUtilization * 
		        perDetail.EnrolleeAverageCostShare / 12000
	        END
	        ) + ((perDetail.AllowedUtilzationPer1000/1000)*
			(perDetail.AverageAdminExpense/12))
				+ ((perDetail.AllowedUtilzationPer1000/1000)*
			(perDetail.AnnualizedAverageProfit/12)),

		ProjectedMemberMonths = perHeader.TotalProjectedMemberMonths
	FROM 
	    (SELECT
            sph.PlanYearID,
	        sph.ContractNumber,
	        sph.PlanID,
	        sph.SegmentId,  --Added SegmentId
	        sph.ForecastID,
	        perHeader.PackageIndex,
	        perHeader.Name,					--Package Name
	        PackageAllowedPMPM = 
		        SUM(
		        (perDetail.AllowedUtilzationPer1000/1000)*
		        (perDetail.AllowedAverageCost/12)
		        ),
	        PackageCostSharePMPM =
		        SUM(
		        CASE WHEN perDetail.MeasurementUnitCode = 'Coin' THEN
			        perDetail.EnrolleeCostShareUtilization * 
			        perDetail.EnrolleeAverageCostShare
		        ELSE 
			        perDetail.EnrolleeCostShareUtilization * 
			        perDetail.EnrolleeAverageCostShare / 12000
		        END
		        )
		FROM (SELECT * FROM SavedPlanOptionalPackageDetail UNION ALL SELECT * FROM ArcSavedPlanOptionalPackageDetail where PlanYearID != dbo.fnGetBidYear()) saved
		INNER JOIN (SELECT PlanyearID,
						   ContractNumber,
						   PlanID,
						   SegmentID,
						   ForecastID
					FROM SavedPlanHeader 
					UNION ALL 
					SELECT  PlanyearID,
						   ContractNumber,
						   PlanID,
						   SegmentID,
						   ForecastID 
					FROM ArcSavedPlanHeader 
					WHERE PlanYearID != dbo.fnGetBidYear()) sph
			ON saved.PlanYearID = sph.PlanYearID
			AND saved.ForecastID = sph.ForecastID
		INNER JOIN (SELECT PlanYearID, PackageIndex, PackageDetailID, BidServiceCategoryID, 
						   PricingComponentDescription, AllowedUtilizationTypeID, 
						   AllowedUtilzationPer1000, AllowedAverageCost,
						   MeasurementUnitCode, EnrolleeCostShareUtilization, 
						   EnrolleeAverageCostShare, AverageAdminExpense, 
						   AnnualizedAverageProfit, IsHidden
					FROM   dbo.PerIntOptionalPackageDetail 
					UNION ALL 
					SELECT PlanYearID, PackageIndex, PackageDetailID, BidServiceCategoryID, 
						   PricingComponentDescription, AllowedUtilizationTypeID, 
						   AllowedUtilzationPer1000, AllowedAverageCost,
						   MeasurementUnitCode, EnrolleeCostShareUtilization, 
						   EnrolleeAverageCostShare, AverageAdminExpense, 
						   AnnualizedAverageProfit, IsHidden
		           FROM dbo.ArcPerIntOptionalPackageDetail 
				   WHERE PlanYearID != dbo.fnGetBidYear()) perDetail
			ON saved.PlanYearID = perDetail.PlanYearID
			AND saved.PackageIndex = perDetail.PackageIndex
		INNER JOIN (SELECT PlanYearID,
						   PackageIndex,
						   Name,
						   Description,
						   TotalExpense,
						   TotalGainLoss,
						   TotalProjectedMemberMonths,
						   IsEnabled
					FROM   dbo.PerIntOptionalPackageHeader
					UNION ALL 
					SELECT PlanYearID,
						   PackageIndex,
						   Name,
						   Description,
						   TotalExpense,
						   TotalGainLoss,
						   TotalProjectedMemberMonths,
						   IsEnabled
		           FROM dbo.ArcPerIntOptionalPackageHeader where PlanYearID != dbo.fnGetBidYear()) perHeader
			ON perDetail.PlanYearID = perHeader.PlanYearID
			AND perDetail.PackageIndex = perHeader.PackageIndex
		WHERE saved.PlanYearID = @PlanYearID
		    AND saved.ForecastID = @ForecastID
		    AND perDetail.IsHidden = 0
			AND saved.IsHidden = 0
		GROUP BY
		    sph.PlanYearID,
	        sph.ContractNumber,
	        sph.PlanID,
	        sph.SegmentId,  --Added SegmentId
	        sph.ForecastID,
	        perHeader.PackageIndex,
	        perHeader.Name
		) packagetotals
    INNER JOIN (SELECT PlanYearID, PackageIndex, PackageDetailID, BidServiceCategoryID, 
						   PricingComponentDescription, AllowedUtilizationTypeID, 
						   AllowedUtilzationPer1000, AllowedAverageCost,
						   MeasurementUnitCode, EnrolleeCostShareUtilization, 
						   EnrolleeAverageCostShare, AverageAdminExpense, 
						   AnnualizedAverageProfit, IsHidden
					FROM   dbo.PerIntOptionalPackageDetail 
					UNION ALL 
					SELECT PlanYearID, PackageIndex, PackageDetailID, BidServiceCategoryID, 
						   PricingComponentDescription, AllowedUtilizationTypeID, 
						   AllowedUtilzationPer1000, AllowedAverageCost,
						   MeasurementUnitCode, EnrolleeCostShareUtilization, 
						   EnrolleeAverageCostShare, AverageAdminExpense, 
						   AnnualizedAverageProfit, IsHidden
		           FROM dbo.ArcPerIntOptionalPackageDetail  where PlanYearID != dbo.fnGetBidYear()) perDetail
        ON packagetotals.PlanYearID = perDetail.PlanYearID
        AND packagetotals.PackageIndex = perDetail.PackageIndex
	INNER JOIN (SELECT PlanYearID,
					   PackageIndex,
					   Name,
					   Description,
					   TotalExpense,
					   TotalGainLoss,
					   TotalProjectedMemberMonths,
					   IsEnabled
					FROM   dbo.PerIntOptionalPackageHeader
					UNION ALL 
					SELECT PlanYearID,
						   PackageIndex,
						   Name,
						   Description,
						   TotalExpense,
						   TotalGainLoss,
						   TotalProjectedMemberMonths,
						   IsEnabled
		           FROM dbo.ArcPerIntOptionalPackageHeader  where PlanYearID != dbo.fnGetBidYear()) perHeader
		ON perDetail.PlanYearID = perHeader.PlanYearID
		AND perDetail.PackageIndex = perHeader.PackageIndex
	INNER JOIN dbo.MAReportPlanLevel pd
		ON pd.ForecastID =  packagetotals.ForecastID 
		AND pd.PlanYearID = packagetotals.PlanYearID
    )

GO
