SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetAddedBenefits]    ******/

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetAddedBenefits
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Feb-26
--
-- DESCRIPTION: Function responsible for listing values on the Added Benefit tab in the MAPD Model.
--
-- PARAMETERS:
--    Input:
--        @ForecastID
--
-- RETURNS: Table
--
-- TABLES: 
--    Read:
--                LkpExtBidServiceCategory
--                LkpIntAddedBenefitType
--                LkpIntPlanYear
--                SavedPlanAddedBenefits     
--    Write: NONE
--
-- VIEWS:
--    Read: NONE
--
-- FUNCTIONS:
--    Read: NONE
--    Called: NONE
--
-- STORED PROCS: 
--    Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                            DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Feb-26      1           Initial Version                                                         Christian Cofie
-- 2007-Sep-10      2           Revised code to bring into coding standards.                            Shannon Boykin
-- 2007-Sep-17      3           Additional revisions to bring code into coding standards.               Shannon Boykin
-- 2007-Sep-24      4           Additional revisions to bring code into coding standards - Lined up     Shannon Boykin
--                              paranthesis differently to conserve line space.
-- 2007-Nov-27      5           Added IsHidden=0 to where condition                                     Christian Cofie
-- 2008-Feb-23      6           Added PlanYear to LkpExtCMSBidServiceCategory                           Sandy Ellis
-- 2009-Mar-17      7           Data type conversion                                                    Sandy Ellis
-- 2010-Sep-30      8           Revised for 2012 Database, renamed to fnGetAddedBenefits                Jake Gaecke
--                              and updated for coding standards
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetAddedBenefits]
    (
    @ForecastID INT
    )
RETURNS TABLE AS  
RETURN
    (

    SELECT
        t.IsStandard,
        t.AddedBenefitTypeID,
        t.AddedBenefitName, 
        t.INAddedBenefitDescription,
        t.INAddedBenefitAllowed, 
        t.INAddedBenefitUtilization,
        t.INAddedBenefitCostShare,
        t.OONAddedBenefitDescription, 
        t.OONAddedBenefitAllowed,
        t.OONAddedBenefitUtilization, 
        t.OONAddedBenefitCostShare,
        t.IsValueAdded,
        t.BidServiceCatID,
        l.ServiceCategory 
    FROM (
            SELECT
                ABT.IsStandard,
                AddedBenefitTypeID = 
                    CASE ABT.IsStandard 
                        WHEN 1 THEN ABT.AddedBenefitTypeID
                        ELSE SAB.AddedBenefitTypeID
                    END, 
                AddedBenefitName =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.AddedBenefitName
                        ELSE SAB.AddedBenefitName
                    END, 
                INAddedBenefitDescription =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitDescription
                        ELSE SAB.INAddedBenefitDescription
                    END,
                INAddedBenefitAllowed =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitAllowed
                        ELSE SAB.INAddedBenefitAllowed
                    END, 
                INAddedBenefitUtilization =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitUtilization
                        ELSE SAB.INAddedBenefitUtilization
                    END,
                INAddedBenefitCostShare =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.INAddedBenefitCostShare
                        ELSE SAB.INAddedBenefitCostShare
                    END,
                OONAddedBenefitDescription =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitDescription
                        ELSE SAB.OONAddedBenefitDescription
                    END, 
                OONAddedBenefitAllowed =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitAllowed
                        ELSE SAB.OONAddedBenefitAllowed
                    END,
                OONAddedBenefitUtilization =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitUtilization
                        ELSE SAB.OONAddedBenefitUtilization
                    END, 
                OONAddedBenefitCostShare =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.OONAddedBenefitCostShare
                        ELSE SAB.OONAddedBenefitCostShare
                    END,
                IsValueAdded =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.IsValueAdded
                        ELSE SAB.IsValueAdded
                    END,
                BidServiceCatID =
                    CASE ABT.IsStandard
                        WHEN 1 THEN ABT.BidServiceCatID
                        ELSE SAB.BidServiceCatID
                    END
            FROM LkpIntAddedBenefitType ABT
            INNER JOIN SavedPlanAddedBenefits SAB
                ON ABT.AddedBenefitTypeID = SAB.AddedBenefitTypeID
            WHERE
                SAB.ForecastID = @ForecastID
                AND SAB.IsHidden=0
        ) t
    INNER JOIN LkpExtCMSBidServiceCategory l
        ON l.BidServiceCategoryID = t.BidServiceCatID
    )
GO
