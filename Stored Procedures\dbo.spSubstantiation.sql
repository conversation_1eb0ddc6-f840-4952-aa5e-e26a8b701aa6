SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spSubstantiation
--
-- AUTHOR: <PERSON>ame
--
-- CREATED DATE: 2018-Oct-1
-- HEADER UPDATED: 2018-Oct-1
--
-- DESCRIPTION: Generate significance report for a given region
--
-- PARAMETERS:
--	Input:	
--		String of @RollupIDs 
--		String of @RegionIDs
--	Output: 
--
-- RETURNS: 
--		Significance Report(
--			RollupName
--			ReportingBid
--			ContributingBid
--			TotalMM
--			CrosswalkedToCY_MM
--			RemovedFromBY_MM
--			RemainingMM
--			PctRemaining
--			IncludeBasePlan
--			RelevantCrosswalk
--			IncludeInSubstantiation)

-- TABLES:
--	Read: 
--		MAAModels -> CalcSignificance
--		MAAModels -> SavedPlanInfo
--		MAAModels -> SavedMarketInfo
 --		MAAModels -> SavedForecastSetup
--		MAAModels -> SavedRollupInfo
--		MAAModels -> SavedRollupForecastMap
--
--	Write:
--		None
--
-- VIEWS: 
--	Read: 
--		None
--
-- FUNCTIONS:
--      None
--
-- STORED PROCS: 
--		MAAModels -> spRelevantCrosswalk
--
-- HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE			 VERSION			CHANGES MADE						          DEVELOPER					REVIEWER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2018-10-1		1				Initial Version                               Daniel Nyatuame                         
-- 2018-10-8		2				Changed input parameter @RegionID datatype    Kritika Singh
--                                  from tiny int to varchar(max)   
-- 2018-10-12		3				Changed input parameter @RollupId datatype    Kritika Singh
--                                  from tiny int to varchar(max)
-- 2018-12-03		4				Add code to remove dups						  Daniel Nyatuame  
-- 2019-01-25		5				Formatted columns TotalMM,Moving MM           Kritika Singh
--                                  Crosswalked MM, Removed MM,Remaining MM,                          
-- 2019-02-6		6				Clean up documentation for header section     Daniel Nyatuame    
-- 2020-07-02       7               increased the precision of the displayed	  Sachin Yadav
--									Significance % to show the same calculated
--									 value rounded to 2 decimals places
--2021-05-21       8               Update code to convert significance % to      Joshua Pace
--                                  decimal(5,2) in order to handle 100%
--2023-Oct-23	   9 	           Updated RollupName as parameter for           Chaitanya Durga
--                                  crosswalk Substantiation Report issue 	     
-- ----------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spSubstantiation] --null,null
	@RollupName VARCHAR(MAX)=NULL,
    @RegionID VARCHAR(MAX)=NULL
AS
    BEGIN
        SET NOCOUNT ON					
		/* Generate relevant crosswalk*/
		IF ( SELECT OBJECT_ID('tempdb..#RelXw')
			) IS NOT NULL
			DROP TABLE #RelXw;
		CREATE TABLE #RelXw (
			[ServiceAreaOptionID] [TINYINT], 
			[BidYearPlanInfoID] [SMALLINT],
			[BasePlanInfoID] [SMALLINT],
			[IsSubstantiationNeeded] [BIT],
			[RelXW] [VARCHAR](150)
			)
		INSERT INTO #RelXw
		EXECUTE dbo.spRelevantCrosswalk NULL, NULL
		DELETE FROM #RelXw WHERE IsSubstantiationNeeded = 0 OR RelXW IS NULL --2018/12/03 DKN: remove dups
		
		SELECT	ISNULL(r.RollupName,'Unmapped') AS RollupName
				,ReportingBid = p.CPS
				,ContributingBid = b.CPS
				,TotalMM = FORMAT(FLOOR(s.TotalMM),'##,##0')
				,CrosswalkedToCY_MM = FORMAT(FLOOR(ISNULL(s.RemovedMM, 0) + ISNULL(s.MovingMM, 0)),'##,##0')
				,RemovedFromBY_MM = FORMAT(FLOOR(s.RemovedMM),'##,##0')
				,RemainingMM = FORMAT(FLOOR(s.MovingMM),'##,##0')
				,PctRemaining = CAST(CONVERT(DECIMAL(5,2), s.SignificancePct*100) AS VARCHAR)+ '%'
				,IncludeBasePlan = CASE WHEN s.IsWks1BasePlan =1 THEN 'Yes'
				 ELSE 'No' END
				,RelevantCrosswalk = xw.RelXW
				,CASE WHEN xw.IsSubstantiationNeeded =1 THEN 'Yes'
				 ELSE 'No' END AS IsSubstantiationNeeded
		FROM dbo.CalcSignificance s
		INNER JOIN	dbo.SavedPlanInfo p ON p.PlanInfoID = s.PlanInfoID
		INNER JOIN dbo.SavedPlanInfo b ON b.PlanInfoID = s.BasePlanInfoID
		LEFT JOIN dbo.SavedMarketInfo m ON m.ActuarialMarketID = p.ActuarialMarketID
		INNER JOIN dbo.SavedForecastSetup f ON f.PlanInfoID = s.PlanInfoID AND f.ServiceAreaOptionID = s.ServiceAreaOptionID --DkN 2018/12/03 Filter by active SA Options only		
		LEFT JOIN dbo.SavedRegionInfo SR on SR.ActuarialRegionID = m.ActuarialRegionID	
		LEFT JOIN dbo.SavedRollupForecastMap rf ON rf.ForecastID = f.ForecastID
		LEFT JOIN dbo.SavedRollupInfo r ON r.RollupID = rf.RollupID
		INNER JOIN #RelXw xw ON s.PlanInfoID = xw.BidYearPlanInfoID AND xw.BasePlanInfoID = s.BasePlanInfoID AND xw.ServiceAreaOptionID = s.ServiceAreaOptionID
		WHERE ((@RegionID IS NULL) OR m.ActuarialRegionID  IN (SELECT CAST(value AS SMALLINT) FROM dbo.fnStringSplit(@RegionID,',')))
		AND
		  s.BasePlanInfoID <> 0 
		AND (ISNULL(r.RollupName,'Unmapped') IN (SELECT value  FROM dbo.fnStringSplit(@RollupName,',')))
		AND xw.IsSubstantiationNeeded = 1

		
	END

GO
