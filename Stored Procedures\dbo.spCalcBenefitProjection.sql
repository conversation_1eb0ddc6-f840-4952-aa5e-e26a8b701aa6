SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcBenefitProjection
--
-- CREATOR:			Christian Cofie
--
-- CREATED DATE:	2007-JAN-29
-- HEADER UPDATED:	2023-JAN-04 <PERSON> Lewis
--
-- DESCRIPTION:		This SP takes total base year data (experience and manual), applies trends, and produces projected (bid) year allowed. 
--					Calibration factors are calculated and applied to the total projected allowed in order to allocate the total projected 
--					allowed to DE# and Non-DE# populations. Experience and manual projections are then credibility blended, safe harbor is 
--					applied if applicable, and the final result is written to dbo.CalcBenefitProjectionPreMbrCS. 
--
--					Finally, sequestration, MER, and related party adjustments are applied to the results from above, and this data is
--					written to dbo.CalcBenefitProjection.
--		
-- PARAMETERS:
--  Input  :		@ForecastID
--					@UserID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			C2PMERCostMultAdj
--					SavedMERActAdj
--					SavedPlanHeader
--					SavedPlanINOONDistributionDetail
--					CalcSQSFactors
--					CalcPlanExperienceByBenefitCatNonSQS
--					CalcProjectionFactorsNonSQS
--					SavedPlanBenefitDetail
--					LkpIntBenefitCategory
--
--  Write:			CalcBenefitProjectionPreMbrCS
--					CalcBenefitProjection
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnAppGetBenchmarkMembership
--					fnGetBidYear
--					fnGetCredibilityFactor
--					fnGetRelatedPartiesAdjustment
--					fnGetSafeDivisionResult
--					fnGetSafeDivisionResultReturnOne				
--					fnIsSafeHarborExemption
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-JAN-29      1           Initial Version                                                     Christian Cofie
-- 2007-MAR-10      2           Added @LastDateTime and @PlanVersion.                               Christian Cofie
-- 2007-SEP-04      3           Added PlanYearID link to CalcExperienceByBenefitCat.                Christian Cofie
-- 2008-JAN-31      4           Revised code to bring it into coding standards.         	        Shannon Boykin
-- 2008-MAR-11      5           Added LkpIntBenefitCategory.IsEnabled = 1 to Where Clause           Brian Lake
-- 2008-APR-08      6           Added 2009 section due to changes in the Projection/Forecast tables Brian Lake
-- 2008-APR-10		7           Moved Delete section outside of IF @PlanYear                        Brian Lake
-- 2008-APR-29		8           Added PD.MARatingOptionID = 1 (Experience rating)                   Brian Lake
-- 2008-SEP-19		9           Added @UserID to the list of parameters and replaced all            Shannon Boykin
--						            occurrences of SUSER_SNAME with @UserID.
-- 2009-FEB-21      10          Added projection piece for 2010                                     Brian Lake
-- 2009-FEB-22      11          Added ClaimForecastID                                               Sandy Ellis
-- 2009-FEB-22      12          ISNULL around OON Unit factors                                      Sandy Ellis
-- 2009-MAR-06      13          Revised Error handling, sorted PlanYearID IFs in desc order         Brian Lake
-- 2009-Mar07       14          Restored bc.IsEnabled to 2010                                       Sandy Ellis
-- 2009-MAR-11      15          Error handling returns error if 0 records are inserted              Brian Lake
-- 2009-MAR-17      16          Data types                                                          Sandy Ellis
-- 2009-MAR-18      17          Use new claim factors table                                         Sandy Ellis
-- 2009-MAR-19      18          Add dual population adjustment                                      Sandy Ellis
-- 2009-Mar_19      19          Removed error handling and transaction                              Brian Lake
-- 2009-MAR-20      20          Removed delete section                                              Brian Lake
-- 2009-MAR-21      21          Fix OON trend error                                                 Sandy Ellis
-- 2009-APR-04      22          Added () around dual factor denominator                             Sandy Ellis
-- 2009-APR-05      23          Replaced dual population adjustment with draw from function         Sandy Ellis
-- 2009-APR-07      24          Replaced dual population with draw from CalcPreBenefitProjection    Sandy Ellis
--                                  and eliminated trend joins for 2010
-- 2009-NOV-24		25			Updated for 2011 methodology										Joe Casey
-- 2009-DEC-03		26			Added MARatingOptionID =1 to 2011 Statement							Nick Skeen
-- 2010-FEB-05		27			Changed DualPopulationFactor to newly created 						Michael Siekerka
--									DualPopulationUtilFactor for 2011+
-- 2010-MAR-01		28			Made an adjustment to BenefitCategoryID = 103 (Specialist Cap) 		Jake Gaecke
--									calculations for	IN/OON Allowed and NetworkAllowed.  Added 
--									Benefits are subtracted from this category since C&U has 
--									already added the Added Benefits as a placeholder previously. 
--									This is done by fnGetCalcPlanExperienceByBenefitCat instead
--									of pulling directly from CalcPlanExperienceByBenefitCat
-- 2010-MAR-11		30			Implemented new PlanYear methodology and independence for 2012		Joe Casey
-- 2010-JUN-09		31			Removed fnGetCalcPlanExperienceByBenefitCat as it was not needed	Michael Siekerka
-- 2010-JUN-14		32			Added functionality to put all projections in the same table		Michael Siekerka
-- 2010-JUN-21      33          Added BenefitCategoryID to join for fnGetMARatingClaimFactors       Jake Gaecke
-- 2010-JUL-14      34          Adjusted DualEligibilityTypeID requirements to match new            Jake Gaecke
--									specification
-- 2010-JUL-14      35          Removed superfluous code relating to blending                       Jake Gaecke
-- 2010-JUL-19		36			Added functionality for CalcProjectionFactors and removed use of	Michael Siekerka
--									fnGetMARatingClaimFactors
-- 2010-SEP-22      37          Removed PlanVersion added PlanYearID                                Michael Siekerka
-- 2010-SEP-28      38          Fixed column names for reference to CalcProjectionFactors           Michael Siekerka
-- 2010-DEC-08      39          Updated trend column names to include E2C... & C2B...               Nate Jacoby
-- 2011-JAN-13		40			Added Safe Harbor Exemption section									Joe Casey
-- 2011-FEB-03      41          Added code to fill CalcProjectionFactors with NULL's for other      Michael Siekerka
--                                  MARatingOptionID so that it can be joined on later
-- 2011-FEB-07		42			Replaced 'Null' values to 0 incase there is a missing RatingOption	Nate Jacoby
-- 2011-JUN-02		43			Replaced LkpIntPlanYear with dbo.fnGetBidYear()						Bobby Jaegers
-- 2011-JUN-14		44			Changed @PlanYearID to return SMALLINT instead of INT				Bobby Jaegers
-- 2014-MAR-4		45			Added ability to pull from Sequestered data							Mike Deren
-- 2014-APR-21		46			Related Parties Adjustment											Mike Deren
-- 2014-MAY-11		47			Adding in fnGetSafeDivisionResult for RP							Mason Roberts
-- 2014-NOV-26      48          Renamed variable @CredibilityFactor9mth to @CredibilityFactorPrtl   Amit Tiwari
-- 2015-FEB-05		49			Added logic to combine DE# and Non-DE# business after trending		Matthew Evans
-- 2016-MAR-10		50			Added PlanReprice Run, when one should populate						Mark Freel
--                                  CalcBenefitProjectionPreMbrCS, when 2 CalcBenefitProjection		
-- 2017-OCT-05		51			Trend Overhaul implementation:										Chris Fleming
--									removed SQS, plan reprice, and prtl fields, objects and logic
--									populate new WS2ProjAllowed table,
--									removed related party (RP) and SQS from CBP-PreMbrCS
--									subtract RP and multiply SQS & MER for CBP
-- 2017-OCT-09		52			New table CalcProjectionFactorsNonSQS (w/o MER) added, @MER used	Chris Fleming
--									to align with CalcProjectionFactors
-- 2017-DEC-20		53			Updated to CalcPlanExperienceByBenefitCatNonSQS vs SQS				Chris Fleming &  Jordan Purdue
-- 2018-MAR-05		54			Added new code to populate CalcBenefitProjectionSCT Current			Chris Fleming
--									and CalcBenefitProjectionSCTBid
-- 2018-SEP-18		55			Removed SCT calculations from this procedure						Jordan Purdue
-- 2019-JUN-28		56			Replace SavedPlanHeader with SavedForecastSetup						Pooja Dahiya
-- 2019-OCT-24		57			Removed HUMAD from LastUpdateByID									Chhavi Sinha
-- 2019-NOV-06      58		    Replaced SavedForecastSetup with SavedPlanHeader					Chhavi Sinha
-- 2020-JAN-10      59          [20.02] Include DedApplies flags and general schema cleanup         Keith Galloway
-- 2021-DEC-03      60			Replaced input @PlanIndex with @ForecastID; Replaced the reference	Aleksandar Dimitrijevic
--								for table SavedPlanBenefitDetail from PlanIndex to ForecastID;		
--								for table SavedPlanBenefitDetail where IsBenefitYearCurrent = 0
--								added "WHERE pbd.Isliveindex = 1" to limit the pull to a single
--								BenefitOptionID used in pricing	
-- 2022-OCT-18	    61    		Added internal variables for input parameters               		Khurram Minhas
--						    	Added  WITH (NOLOCK) 
-- 2022-NOV-23		62			Replaced original table with Temp Table								Sheetal Patil
--								Removed cursor loop
--								To avoid locking added records in original table at the end.
-- 2023-JAN-04		63			Modified projection methodology due to removal of Dual Population	Jake Lewis
--									Adjustment Factors. 
--								Rewrote code body to improve readability and performance. 
--								Removed @MARatingOptionID input parameter; SP now calc's for all 
--									Rating Option IDs at the same time. 
-- 2023-FEB-08		64			Fix issue where no MARatingOptionID = 2,3 records are created if 
--									manual base data is missing.									Jake Lewis
-- 2024-OCT-06		65			Cost Share Basis: handle IsIncludeInCostShareBasis field from
--									base data tables, and flow this field through to output 
--									field CalcBenefitProjectionPreMbrCS								Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcBenefitProjection]
    (
    @ForecastID INT
   ,@UserID     CHAR(7))

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY

            DECLARE @tranCount INT = @@TranCount; --Current transaction count

            BEGIN TRANSACTION transaction_spCalcBen;

            -- Declare variables
            DECLARE @XForecastID INT = @ForecastID; --Internal variable for input param
            DECLARE @XUserID CHAR(7) = @UserID; --Internal variable for input param
            DECLARE @LastUpdateDateTime DATETIME = GETDATE ();
            DECLARE @Credibility FLOAT = dbo.fnGetCredibilityFactor (@XForecastID);
            DECLARE @PlanYearID SMALLINT = dbo.fnGetBidYear ();
            DECLARE @IsSafeHarbor BIT = dbo.fnIsSafeHarborExemption (@XForecastID);
            DECLARE @MER DEC(18, 15) = (SELECT      DISTINCT
                                                    mer.C2PMERCostMultAdj
                                        FROM        dbo.SavedMERActAdj mer WITH (NOLOCK)
                                       INNER JOIN   dbo.SavedPlanHeader sph WITH (NOLOCK)
                                               ON sph.ContractNumber = mer.ContractNumber
                                                  AND   sph.PlanID = mer.PlanID
                                                  AND   sph.SegmentID = mer.SegmentID
                                        WHERE       sph.ForecastID = @XForecastID);


            -------------------------------------------------------
            ----- STEP 1: LOAD SOURCE DATA ------------------------
            -------------------------------------------------------
            -- Membership
            IF OBJECT_ID ('tempdb..#Membership') IS NOT NULL DROP TABLE #Membership;
            CREATE TABLE #Membership
                (NonDE#Aged   DEC(28, 6)
                ,NonDEPercent DEC(18, 15)
                ,DE#Aged      DEC(28, 6)
                ,DEPercent    DEC(18, 15)
                ,TotalAged    DEC(28, 6));
            INSERT INTO #Membership
            SELECT  SUM (NonDE#Aged) AS NonDE#Aged
                   ,dbo.fnGetSafeDivisionResultReturnOne (SUM (NonDE#Aged), SUM (TotalAged)) AS NonDEPercent
                   ,SUM (DE#Aged) AS DE#Aged
                   ,dbo.fnGetSafeDivisionResultReturnOne (SUM (DE#Aged), SUM (TotalAged)) AS DEPercent
                   ,SUM (TotalAged) AS TotalAged
            FROM    dbo.fnAppGetBenchmarkMembership (@XForecastID);

            -- IN Distribution Percent
            IF OBJECT_ID ('tempdb..#INOONDist') IS NOT NULL DROP TABLE #INOONDist;
            CREATE TABLE #INOONDist
                (BenefitCategoryID     INT
                ,INDistributionPercent DEC(12, 10));
            INSERT INTO #INOONDist
            SELECT  BenefitCategoryID
                   ,INDistributionPercent
            FROM    dbo.SavedPlanINOONDistributionDetail WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID;

            -- SQS Factors
            IF OBJECT_ID ('tempdb..#SQSFactors') IS NOT NULL DROP TABLE #SQSFactors;
            CREATE TABLE #SQSFactors
                (MARatingOptionID   TINYINT
                ,BenefitCategoryID  INT
                ,DualEligibleTypeID TINYINT
                ,ProjectedSQS       DEC(14, 6));
            INSERT INTO #SQSFactors
            SELECT  MARatingOptionID
                   ,BenefitCategoryID
                   ,DualEligibleTypeID
                   ,ProjectedSQS
            FROM    dbo.CalcSQSFactors WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID;

            -- Related Party Adjustments
            IF OBJECT_ID ('tempdb..#RPAdj') IS NOT NULL DROP TABLE #RPAdj;
            CREATE TABLE #RPAdj
                (BenefitCategoryID        INT
                ,RelatedPartiesAdjustment DEC(11, 8));
            INSERT INTO #RPAdj
            SELECT  BenefitCategoryID
                   ,RelatedPartiesAdjustment
            FROM    dbo.fnGetRelatedPartiesAdjustment (@XForecastID);

            -- Base Data
            IF OBJECT_ID ('tempdb..#BaseData') IS NOT NULL DROP TABLE #BaseData;
            CREATE TABLE #BaseData
                (MARatingOptionID          TINYINT
                ,BenefitCategoryID         INT
                ,DualEligibleTypeID        TINYINT
                ,IsIncludeInCostShareBasis BIT
                ,INAllowed                 DEC(14, 6)
                ,INNetworkAllowed          DEC(14, 6)
                ,INUnits                   DEC(14, 6)
                ,INAdmits                  DEC(14, 6)
                ,OONAllowed                DEC(14, 6)
                ,OONNetworkAllowed         DEC(14, 6)
                ,OONUnits                  DEC(14, 6)
                ,OONAdmits                 DEC(14, 6));
            INSERT INTO #BaseData
            SELECT  MARatingOptionID
                   ,BenefitCategoryID
                   ,DualEligibleTypeID
                   ,IsIncludeInCostShareBasis
                   ,INAllowed
                   ,INNetworkAllowed
                   ,INUnits
                   ,INAdmits
                   ,OONAllowed
                   ,OONNetworkAllowed
                   ,OONUnits
                   ,OONAdmits
            FROM    dbo.CalcPlanExperienceByBenefitCatNonSQS WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID;

            -- Projection Factors
            IF OBJECT_ID ('tempdb..#ProjFactors') IS NOT NULL DROP TABLE #ProjFactors;
            CREATE TABLE #ProjFactors
                (MARatingOptionID         TINYINT
                ,BenefitCategoryID        INT
                ,E2CINClaimFactorUnits    DEC(18, 15)
                ,C2BINClaimFactorUnits    DEC(18, 15)
                ,E2CINClaimFactorAllowed  DEC(18, 15)
                ,C2BINClaimFactorAllowed  DEC(18, 15)
                ,E2COONClaimFactorUnits   DEC(18, 15)
                ,C2BOONClaimFactorUnits   DEC(18, 15)
                ,E2COONClaimFactorAllowed DEC(18, 15)
                ,C2BOONClaimFactorAllowed DEC(18, 15));
            INSERT INTO #ProjFactors
            SELECT  MARatingOptionID
                   ,BenefitCategoryID
                   ,E2CINClaimFactorUnits
                   ,C2BINClaimFactorUnits
                   ,E2CINClaimFactorAllowed
                   ,C2BINClaimFactorAllowed
                   ,E2COONClaimFactorUnits
                   ,C2BOONClaimFactorUnits
                   ,E2COONClaimFactorAllowed
                   ,C2BOONClaimFactorAllowed
            FROM    dbo.CalcProjectionFactorsNonSQS WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID;

            -- Determine if deductible applies for each benefit category
            IF OBJECT_ID ('tempdb..#DedApp') IS NOT NULL DROP TABLE #DedApp;
            CREATE TABLE #DedApp
                (BenefitCategoryID INT
                ,INDedApplies      BIT
                ,OONDedApplies     BIT);
            INSERT INTO #DedApp
            SELECT  BenefitCategoryID
                   ,INDedApplies
                   ,OONDedApplies
            FROM    dbo.SavedPlanBenefitDetail WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID
                    AND IsBenefitYearCurrentYear = 0
                    AND IsLiveIndex = 1
                    AND BenefitOrdinalID = 1;


            -------------------------------------------------------
            ----- STEP 2: APPLY TRENDS TO TOTAL BASE DATA ---------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#TrendedTotal') IS NOT NULL DROP TABLE #TrendedTotal;
            CREATE TABLE #TrendedTotal
                (MARatingOptionID          TINYINT
                ,BenefitCategoryID         INT
                ,DualEligibleTypeID        TINYINT
                ,IsIncludeInCostShareBasis BIT
                ,INDeductibleApplies       BIT
                ,INAllowed                 FLOAT
                ,INNetworkAllowed          FLOAT
                ,INUnits                   FLOAT
                ,INAdmits                  FLOAT
                ,OONDeductibleApplies      BIT
                ,OONAllowed                FLOAT
                ,OONNetworkAllowed         FLOAT
                ,OONUnits                  FLOAT
                ,OONAdmits                 FLOAT);
            INSERT INTO #TrendedTotal
            SELECT      bd.MARatingOptionID
                       ,bd.BenefitCategoryID
                       ,bd.DualEligibleTypeID
                       ,bd.IsIncludeInCostShareBasis
                       ,da.INDedApplies
                       ,bd.INAllowed * pf.E2CINClaimFactorAllowed * pf.C2BINClaimFactorAllowed AS INAllowed
                       ,bd.INNetworkAllowed * pf.E2CINClaimFactorAllowed * pf.C2BINClaimFactorAllowed AS INNetworkAllowed
                       ,bd.INUnits * pf.E2CINClaimFactorUnits * pf.C2BINClaimFactorUnits AS INUnits
                       ,bd.INAdmits * pf.E2CINClaimFactorUnits * pf.C2BINClaimFactorUnits AS INAdmits
                       ,da.OONDedApplies
                       ,bd.OONAllowed * pf.E2COONClaimFactorAllowed * pf.C2BOONClaimFactorAllowed AS OONAllowed
                       ,bd.OONNetworkAllowed * pf.E2COONClaimFactorAllowed * pf.C2BOONClaimFactorAllowed AS OONNetworkAllowed
                       ,bd.OONUnits * pf.E2COONClaimFactorUnits * pf.C2BOONClaimFactorUnits AS OONUnits
                       ,bd.OONAdmits * pf.E2COONClaimFactorUnits * pf.C2BOONClaimFactorUnits AS OONAdmits
            FROM        #BaseData bd
           INNER JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                   ON libc.BenefitCategoryID = bd.BenefitCategoryID
           INNER JOIN   #DedApp da
                   ON da.BenefitCategoryID = bd.BenefitCategoryID
            LEFT JOIN   #ProjFactors pf
                   ON pf.BenefitCategoryID = bd.BenefitCategoryID
                      AND   pf.MARatingOptionID = bd.MARatingOptionID
            WHERE       libc.IsEnabled = 1
                        AND bd.MARatingOptionID IN (1, 2) --Experience and manual; we will credibility blend later
                        AND bd.DualEligibleTypeID = 2;  --Total


            -------------------------------------------------------
            ----- STEP 3: CALCULATE CALIBRATION FACTORS -----------
            -------------------------------------------------------
            -- Calibration factors are used to allocate projected totals to DE# and NonDE#
            IF OBJECT_ID ('tempdb..#CalibrationFactors') IS NOT NULL
                DROP TABLE #CalibrationFactors;
            CREATE TABLE #CalibrationFactors
                (MARatingOptionID    TINYINT
                ,BenefitCategoryID   INT
                ,DualEligibleTypeID  TINYINT
                ,INAllowedCF         FLOAT
                ,INNetworkAllowedCF  FLOAT
                ,INUnitsCF           FLOAT
                ,INAdmitsCF          FLOAT
                ,OONAllowedCF        FLOAT
                ,OONNetworkAllowedCF FLOAT
                ,OONUnitsCF          FLOAT
                ,OONAdmitsCF         FLOAT);
            INSERT INTO #CalibrationFactors

            --NonDE# Calibration Factors
            SELECT      NonDEBaseData.MARatingOptionID
                       ,NonDEBaseData.BenefitCategoryID
                       ,NonDEBaseData.DualEligibleTypeID
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.INAllowed)
                       ,(SUM (NonDEBaseData.INAllowed) * mm.NonDEPercent + SUM (DEBaseData.INAllowed) * mm.DEPercent)) AS INAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.INNetworkAllowed)
                       ,(SUM (NonDEBaseData.INNetworkAllowed) * mm.NonDEPercent + SUM (DEBaseData.INNetworkAllowed)
                         * mm.DEPercent)) AS INNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.INUnits)
                       ,(SUM (NonDEBaseData.INUnits) * mm.NonDEPercent + SUM (DEBaseData.INUnits) * mm.DEPercent)) AS INUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.INAdmits)
                       ,(SUM (NonDEBaseData.INAdmits) * mm.NonDEPercent + SUM (DEBaseData.INAdmits) * mm.DEPercent)) AS INAdmitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.OONAllowed)
                       ,(SUM (NonDEBaseData.OONAllowed) * mm.NonDEPercent + SUM (DEBaseData.OONAllowed) * mm.DEPercent)) AS OONAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.OONNetworkAllowed)
                       ,(SUM (NonDEBaseData.OONNetworkAllowed) * mm.NonDEPercent + SUM (DEBaseData.OONNetworkAllowed)
                         * mm.DEPercent)) AS OONNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.OONUnits)
                       ,(SUM (NonDEBaseData.OONUnits) * mm.NonDEPercent + SUM (DEBaseData.OONUnits) * mm.DEPercent)) AS OONUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (NonDEBaseData.OONAdmits)
                       ,(SUM (NonDEBaseData.OONAdmits) * mm.NonDEPercent + SUM (DEBaseData.OONAdmits) * mm.DEPercent)) AS OONAdmitsCF
            FROM        #BaseData NonDEBaseData
            LEFT JOIN   #BaseData DEBaseData
                   ON DEBaseData.MARatingOptionID = NonDEBaseData.MARatingOptionID
                      AND   DEBaseData.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
                      AND   DEBaseData.IsIncludeInCostShareBasis = NonDEBaseData.IsIncludeInCostShareBasis
           INNER JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                   ON libc.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
            LEFT JOIN   #Membership mm
                   ON 1 = 1
            WHERE       libc.IsEnabled = 1
                        AND NonDEBaseData.DualEligibleTypeID = 0 --NonDE#
                        AND DEBaseData.DualEligibleTypeID = 1   --DE#
            GROUP BY    NonDEBaseData.MARatingOptionID
                       ,NonDEBaseData.BenefitCategoryID
                       ,NonDEBaseData.DualEligibleTypeID
                       ,mm.NonDEPercent
                       ,mm.DEPercent

            --DE# Calibration Factors
            UNION ALL

            SELECT      DEBaseData.MARatingOptionID
                       ,DEBaseData.BenefitCategoryID
                       ,DEBaseData.DualEligibleTypeID
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.INAllowed)
                       ,(SUM (NonDEBaseData.INAllowed) * mm.NonDEPercent + SUM (DEBaseData.INAllowed) * mm.DEPercent)) AS INAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.INNetworkAllowed)
                       ,(SUM (NonDEBaseData.INNetworkAllowed) * mm.NonDEPercent + SUM (DEBaseData.INNetworkAllowed)
                         * mm.DEPercent)) AS INNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.INUnits)
                       ,(SUM (NonDEBaseData.INUnits) * mm.NonDEPercent + SUM (DEBaseData.INUnits) * mm.DEPercent)) AS INUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.INAdmits)
                       ,(SUM (NonDEBaseData.INAdmits) * mm.NonDEPercent + SUM (DEBaseData.INAdmits) * mm.DEPercent)) AS INAdmitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.OONAllowed)
                       ,(SUM (NonDEBaseData.OONAllowed) * mm.NonDEPercent + SUM (DEBaseData.OONAllowed) * mm.DEPercent)) AS OONAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.OONNetworkAllowed)
                       ,(SUM (NonDEBaseData.OONNetworkAllowed) * mm.NonDEPercent + SUM (DEBaseData.OONNetworkAllowed)
                         * mm.DEPercent)) AS OONNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.OONUnits)
                       ,(SUM (NonDEBaseData.OONUnits) * mm.NonDEPercent + SUM (DEBaseData.OONUnits) * mm.DEPercent)) AS OONUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        SUM (DEBaseData.OONAdmits)
                       ,(SUM (NonDEBaseData.OONAdmits) * mm.NonDEPercent + SUM (DEBaseData.OONAdmits) * mm.DEPercent)) AS OONAdmitsCF
            FROM        #BaseData NonDEBaseData
            LEFT JOIN   #BaseData DEBaseData
                   ON DEBaseData.MARatingOptionID = NonDEBaseData.MARatingOptionID
                      AND   DEBaseData.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
                      AND   DEBaseData.IsIncludeInCostShareBasis = NonDEBaseData.IsIncludeInCostShareBasis
           INNER JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                   ON libc.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
            LEFT JOIN   #Membership mm
                   ON 1 = 1
            WHERE       libc.IsEnabled = 1
                        AND NonDEBaseData.DualEligibleTypeID = 0 --NonDE#
                        AND DEBaseData.DualEligibleTypeID = 1   --DE#
            GROUP BY    DEBaseData.MARatingOptionID
                       ,DEBaseData.BenefitCategoryID
                       ,DEBaseData.DualEligibleTypeID
                       ,mm.NonDEPercent
                       ,mm.DEPercent;


            -------------------------------------------------------
            ----- STEP 4: CALCULATE DE# / NONDE# PROJECTION -------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#TrendedDEandNonDE') IS NOT NULL
                DROP TABLE #TrendedDEandNonDE;
            CREATE TABLE #TrendedDEandNonDE
                (MARatingOptionID          TINYINT
                ,BenefitCategoryID         INT
                ,DualEligibleTypeID        TINYINT
                ,IsIncludeInCostShareBasis BIT
                ,INDeductibleApplies       BIT
                ,INAllowed                 FLOAT
                ,INNetworkAllowed          FLOAT
                ,INUnits                   FLOAT
                ,INAdmits                  FLOAT
                ,OONDeductibleApplies      BIT
                ,OONAllowed                FLOAT
                ,OONNetworkAllowed         FLOAT
                ,OONUnits                  FLOAT
                ,OONAdmits                 FLOAT);
            INSERT INTO #TrendedDEandNonDE
            SELECT      bd.MARatingOptionID
                       ,bd.BenefitCategoryID
                       ,bd.DualEligibleTypeID
                       ,bd.IsIncludeInCostShareBasis
                       ,tt.INDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INAllowed ELSE tt.INAllowed * cf.INAllowedCF END AS INAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INNetworkAllowed
                             ELSE tt.INNetworkAllowed * cf.INNetworkAllowedCF END AS INNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INUnits ELSE tt.INUnits * cf.INUnitsCF END AS INUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INAdmits ELSE tt.INAdmits * cf.INAdmitsCF END AS INAdmits
                       ,tt.OONDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONAllowed ELSE tt.OONAllowed * cf.OONAllowedCF END AS OONAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONNetworkAllowed
                             ELSE tt.OONNetworkAllowed * cf.OONNetworkAllowedCF END AS OONNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONUnits ELSE tt.OONUnits * cf.OONUnitsCF END AS OONUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONAdmits ELSE tt.OONAdmits * cf.OONAdmitsCF END AS OONAdmits
            FROM        #TrendedTotal tt
            LEFT JOIN   #BaseData bd
                   ON bd.MARatingOptionID = tt.MARatingOptionID
                      AND   bd.BenefitCategoryID = tt.BenefitCategoryID
                      AND   bd.IsIncludeInCostShareBasis = tt.IsIncludeInCostShareBasis
            LEFT JOIN   #CalibrationFactors cf
                   ON cf.MARatingOptionID = bd.MARatingOptionID
                      AND   cf.BenefitCategoryID = bd.BenefitCategoryID
                      AND   cf.DualEligibleTypeID = bd.DualEligibleTypeID
            WHERE       bd.DualEligibleTypeID IN (0, 1);    --NonDE# and DE#


            -------------------------------------------------------
            ----- STEP 5: MARatingOptionID Check ------------------
            -------------------------------------------------------
            -- Check that MARatingOptionIDs 1 and 2 have records. If not, create NULL records for blending. 

            -- MARatingOptionID 1 = Experience
            IF NOT EXISTS (SELECT   1 FROM  #TrendedTotal WHERE MARatingOptionID = 1)
                BEGIN
                    INSERT INTO #TrendedTotal
                    SELECT  1 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,IsIncludeInCostShareBasis
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedTotal
                    WHERE   MARatingOptionID = 2;
                END;

            IF NOT EXISTS (SELECT   1 FROM  #TrendedDEandNonDE WHERE MARatingOptionID = 1)
                BEGIN
                    INSERT INTO #TrendedDEandNonDE
                    SELECT  1 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,IsIncludeInCostShareBasis
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedDEandNonDE
                    WHERE   MARatingOptionID = 2;
                END;


            -- MARatingOptionID 2 = Experience
            IF NOT EXISTS (SELECT   1 FROM  #TrendedTotal WHERE MARatingOptionID = 2)
                BEGIN
                    INSERT INTO #TrendedTotal
                    SELECT  2 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,IsIncludeInCostShareBasis
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedTotal
                    WHERE   MARatingOptionID = 1;
                END;

            IF NOT EXISTS (SELECT   1 FROM  #TrendedDEandNonDE WHERE MARatingOptionID = 2)
                BEGIN
                    INSERT INTO #TrendedDEandNonDE
                    SELECT  2 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,IsIncludeInCostShareBasis
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedDEandNonDE
                    WHERE   MARatingOptionID = 1;
                END;


            -------------------------------------------------------
            ----- STEP 6: CREDIBILITY BLENDING --------------------
            -------------------------------------------------------
            --Blend total (DualEligibleTypeID = 2)
            IF OBJECT_ID ('tempdb..#BlendedTotal') IS NOT NULL DROP TABLE #BlendedTotal;
            CREATE TABLE #BlendedTotal
                (MARatingOptionID          TINYINT
                ,BenefitCategoryID         INT
                ,DualEligibleTypeID        TINYINT
                ,IsIncludeInCostShareBasis BIT
                ,INDeductibleApplies       BIT
                ,INAllowed                 FLOAT
                ,INNetworkAllowed          FLOAT
                ,INUnits                   FLOAT
                ,INAdmits                  FLOAT
                ,OONDeductibleApplies      BIT
                ,OONAllowed                FLOAT
                ,OONNetworkAllowed         FLOAT
                ,OONUnits                  FLOAT
                ,OONAdmits                 FLOAT);
            INSERT INTO #BlendedTotal
            SELECT      3 AS MARatingOptionID   --Blended
                       ,ttExp.BenefitCategoryID
                       ,ttExp.DualEligibleTypeID
                       ,ttExp.IsIncludeInCostShareBasis
                       ,ttExp.INDeductibleApplies
                       ,ttExp.INAllowed * @Credibility + ttMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,ttExp.INNetworkAllowed * @Credibility + ttMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,ttExp.INUnits * @Credibility + ttMan.INUnits * (1 - @Credibility) AS INUnits
                       ,ttExp.INAdmits * @Credibility + ttMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,ttExp.OONDeductibleApplies
                       ,ttExp.OONAllowed * @Credibility + ttMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,ttExp.OONNetworkAllowed * @Credibility + ttMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,ttExp.OONUnits * @Credibility + ttMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,ttExp.OONAdmits * @Credibility + ttMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedTotal ttExp
            LEFT JOIN   #TrendedTotal ttMan
                   ON ttMan.BenefitCategoryID = ttExp.BenefitCategoryID
                      AND   ttMan.DualEligibleTypeID = ttExp.DualEligibleTypeID
                      AND   ttMan.IsIncludeInCostShareBasis = ttExp.IsIncludeInCostShareBasis
            WHERE       ttExp.MARatingOptionID = 1 --Experience
                        AND ttMan.MARatingOptionID = 2; --Manual

            --Blend NonDE# and DE# (DualEligibleTypeID = 0,1 respectively)
            IF OBJECT_ID ('tempdb..#BlendedDEandNonDE') IS NOT NULL
                DROP TABLE #BlendedDEandNonDE;
            CREATE TABLE #BlendedDEandNonDE
                (MARatingOptionID          TINYINT
                ,BenefitCategoryID         INT
                ,DualEligibleTypeID        TINYINT
                ,IsIncludeInCostShareBasis BIT
                ,INDeductibleApplies       BIT
                ,INAllowed                 FLOAT
                ,INNetworkAllowed          FLOAT
                ,INUnits                   FLOAT
                ,INAdmits                  FLOAT
                ,OONDeductibleApplies      BIT
                ,OONAllowed                FLOAT
                ,OONNetworkAllowed         FLOAT
                ,OONUnits                  FLOAT
                ,OONAdmits                 FLOAT);
            INSERT INTO #BlendedDEandNonDE
            SELECT      3 AS MARatingOptionID   --Blended
                       ,tExp.BenefitCategoryID
                       ,tExp.DualEligibleTypeID
                       ,tExp.IsIncludeInCostShareBasis
                       ,tExp.INDeductibleApplies
                       ,tExp.INAllowed * @Credibility + tMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,tExp.INNetworkAllowed * @Credibility + tMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,tExp.INUnits * @Credibility + tMan.INUnits * (1 - @Credibility) AS INUnits
                       ,tExp.INAdmits * @Credibility + tMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,tExp.OONDeductibleApplies
                       ,tExp.OONAllowed * @Credibility + tMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,tExp.OONNetworkAllowed * @Credibility + tMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,tExp.OONUnits * @Credibility + tMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,tExp.OONAdmits * @Credibility + tMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedDEandNonDE tExp
            LEFT JOIN   #TrendedDEandNonDE tMan
                   ON tMan.BenefitCategoryID = tExp.BenefitCategoryID
                      AND   tMan.DualEligibleTypeID = tExp.DualEligibleTypeID
                      AND   tMan.IsIncludeInCostShareBasis = tExp.IsIncludeInCostShareBasis
            WHERE       tExp.MARatingOptionID = 1 --Experience
                        AND tMan.MARatingOptionID = 2;  --Manual


            -------------------------------------------------------
            ----- STEP 7: APPLY SAFE HARBOR -----------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#BlendedFinal') IS NOT NULL DROP TABLE #BlendedFinal;
            CREATE TABLE #BlendedFinal
                (MARatingOptionID          TINYINT
                ,BenefitCategoryID         INT
                ,DualEligibleTypeID        TINYINT
                ,IsIncludeInCostShareBasis BIT
                ,INDeductibleApplies       BIT
                ,INAllowed                 FLOAT
                ,INNetworkAllowed          FLOAT
                ,INUnits                   FLOAT
                ,INAdmits                  FLOAT
                ,OONDeductibleApplies      BIT
                ,OONAllowed                FLOAT
                ,OONNetworkAllowed         FLOAT
                ,OONUnits                  FLOAT
                ,OONAdmits                 FLOAT);
            INSERT INTO #BlendedFinal

            --Total
            SELECT  bt.MARatingOptionID
                   ,bt.BenefitCategoryID
                   ,bt.DualEligibleTypeID
                   ,bt.IsIncludeInCostShareBasis
                   ,bt.INDeductibleApplies
                   ,bt.INAllowed
                   ,bt.INNetworkAllowed
                   ,bt.INUnits
                   ,bt.INAdmits
                   ,bt.OONDeductibleApplies
                   ,bt.OONAllowed
                   ,bt.OONNetworkAllowed
                   ,bt.OONUnits
                   ,bt.OONAdmits
            FROM    #BlendedTotal bt

            UNION ALL

            --NonDE# and DE#
            SELECT      bde.MARatingOptionID
                       ,bde.BenefitCategoryID
                       ,bde.DualEligibleTypeID
                       ,bde.IsIncludeInCostShareBasis
                       ,bde.INDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAllowed ELSE bde.INAllowed END AS INAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INNetworkAllowed ELSE bde.INNetworkAllowed END AS INNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INUnits ELSE bde.INUnits END AS INUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAdmits ELSE bde.INAdmits END AS INAdmits
                       ,bde.OONDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAllowed ELSE bde.OONAllowed END AS OONAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONNetworkAllowed ELSE bde.OONNetworkAllowed END AS OONNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONUnits ELSE bde.OONUnits END AS OONUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAdmits ELSE bde.OONAdmits END AS OONAdmits
            FROM        #BlendedDEandNonDE bde
            LEFT JOIN   #BlendedTotal bt
                   ON bt.BenefitCategoryID = bde.BenefitCategoryID
                      AND   bt.IsIncludeInCostShareBasis = bde.IsIncludeInCostShareBasis;


            -------------------------------------------------------
            ----- STEP 8: FINAL DATA SETUP ------------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#Final') IS NOT NULL DROP TABLE #Final;
            CREATE TABLE #Final
                (MARatingOptionID          TINYINT
                ,BenefitCategoryID         INT
                ,DualEligibleTypeID        TINYINT
                ,IsIncludeInCostShareBasis BIT
                ,INDeductibleApplies       BIT
                ,INAllowed                 FLOAT
                ,INNetworkAllowed          FLOAT
                ,INUnits                   FLOAT
                ,INAdmits                  FLOAT
                ,OONDeductibleApplies      BIT
                ,OONAllowed                FLOAT
                ,OONNetworkAllowed         FLOAT
                ,OONUnits                  FLOAT
                ,OONAdmits                 FLOAT);
            INSERT INTO #Final

            --Total Experience and Manual
            SELECT  tt.MARatingOptionID
                   ,tt.BenefitCategoryID
                   ,tt.DualEligibleTypeID
                   ,tt.IsIncludeInCostShareBasis
                   ,tt.INDeductibleApplies
                   ,tt.INAllowed
                   ,tt.INNetworkAllowed
                   ,tt.INUnits
                   ,tt.INAdmits
                   ,tt.OONDeductibleApplies
                   ,tt.OONAllowed
                   ,tt.OONNetworkAllowed
                   ,tt.OONUnits
                   ,tt.OONAdmits
            FROM    #TrendedTotal tt

            UNION ALL

            --NonDE# and DE# Experience and Manual
            SELECT  tde.MARatingOptionID
                   ,tde.BenefitCategoryID
                   ,tde.DualEligibleTypeID
                   ,tde.IsIncludeInCostShareBasis
                   ,tde.INDeductibleApplies
                   ,tde.INAllowed
                   ,tde.INNetworkAllowed
                   ,tde.INUnits
                   ,tde.INAdmits
                   ,tde.OONDeductibleApplies
                   ,tde.OONAllowed
                   ,tde.OONNetworkAllowed
                   ,tde.OONUnits
                   ,tde.OONAdmits
            FROM    #TrendedDEandNonDE tde

            UNION ALL

            --Total, NonDE#, and DE# Blended
            SELECT  bf.MARatingOptionID
                   ,bf.BenefitCategoryID
                   ,bf.DualEligibleTypeID
                   ,bf.IsIncludeInCostShareBasis
                   ,bf.INDeductibleApplies
                   ,bf.INAllowed
                   ,bf.INNetworkAllowed
                   ,bf.INUnits
                   ,bf.INAdmits
                   ,bf.OONDeductibleApplies
                   ,bf.OONAllowed
                   ,bf.OONNetworkAllowed
                   ,bf.OONUnits
                   ,bf.OONAdmits
            FROM    #BlendedFinal bf;


            -------------------------------------------------------
            ----- STEP 9: DELETE FROM / WRITE TO TABLE ------------
            -------------------------------------------------------
            --CalcBenefitProejctionPreMbrCS
            DELETE  FROM dbo.CalcBenefitProjectionPreMbrCS
            WHERE   ForecastID = @XForecastID;

            INSERT INTO dbo.CalcBenefitProjectionPreMbrCS
                (PlanYearID
                ,ForecastID
                ,BenefitCategoryID
                ,DualEligibleTypeID
                ,MARatingOptionID
                ,IsIncludeInCostShareBasis
                ,INUnits
                ,INAdmits
                ,INAllowed
                ,INDedApplies
                ,INNetworkAllowed
                ,OONUnits
                ,OONAdmits
                ,OONAllowed
                ,OONDedApplies
                ,OONNetworkAllowed
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  @PlanYearID AS PlanYearID
                   ,@XForecastID AS ForecastID
                   ,f.BenefitCategoryID
                   ,f.DualEligibleTypeID
                   ,f.MARatingOptionID
                   ,f.IsIncludeInCostShareBasis
                   ,f.INUnits
                   ,f.INAdmits
                   ,f.INAllowed
                   ,f.INDeductibleApplies
                   ,f.INNetworkAllowed
                   ,f.OONUnits
                   ,f.OONAdmits
                   ,f.OONAllowed
                   ,f.OONDeductibleApplies
                   ,f.OONNetworkAllowed
                   ,@XUserID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM    #Final f;


            -------------------------------------------------------
            ----- STEP 10: SQS / MER / RP ADJUSTMENTS -------------
            -------------------------------------------------------
            --Total
            IF OBJECT_ID ('tempdb..#TrendedTotalWS2') IS NOT NULL
                DROP TABLE #TrendedTotalWS2;
            CREATE TABLE #TrendedTotalWS2
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #TrendedTotalWS2
            SELECT      tt.MARatingOptionID
                       ,tt.BenefitCategoryID
                       ,tt.DualEligibleTypeID
                       ,tt.INDeductibleApplies
                       ,SUM (tt.INAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER - rp.RelatedPartiesAdjustment AS INAllowed
                       ,SUM (tt.INNetworkAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER
                        - dbo.fnGetSafeDivisionResult (rp.RelatedPartiesAdjustment, dist.INDistributionPercent) AS INNetworkAllowed
                       ,SUM (tt.INUnits) AS INUnits
                       ,SUM (tt.INAdmits) AS INAdmits
                       ,tt.OONDeductibleApplies
                       ,SUM (tt.OONAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER AS OONAllowed
                       ,SUM (tt.OONNetworkAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER AS OONNetworkAllowed
                       ,SUM (tt.OONUnits) AS OONUnits
                       ,SUM (tt.OONAdmits) AS OONAdmits
            FROM        #TrendedTotal tt
            LEFT JOIN   #SQSFactors sqs
                   ON sqs.BenefitCategoryID = tt.BenefitCategoryID
                      AND   sqs.MARatingOptionID = tt.MARatingOptionID
            LEFT JOIN   #RPAdj rp
                   ON rp.BenefitCategoryID = tt.BenefitCategoryID
            LEFT JOIN   #INOONDist dist
                   ON dist.BenefitCategoryID = tt.BenefitCategoryID
            GROUP BY    tt.MARatingOptionID
                       ,tt.BenefitCategoryID
                       ,tt.DualEligibleTypeID
                       ,tt.INDeductibleApplies
                       ,sqs.ProjectedSQS
                       ,rp.RelatedPartiesAdjustment
                       ,dist.INDistributionPercent
                       ,tt.OONDeductibleApplies;

            --NonDE# and DE#
            IF OBJECT_ID ('tempdb..#TrendedDEandNonDEWS2') IS NOT NULL
                DROP TABLE #TrendedDEandNonDEWS2;
            CREATE TABLE #TrendedDEandNonDEWS2
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #TrendedDEandNonDEWS2
            SELECT      tde.MARatingOptionID
                       ,tde.BenefitCategoryID
                       ,tde.DualEligibleTypeID
                       ,tde.INDeductibleApplies
                       ,SUM (tde.INAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER - rp.RelatedPartiesAdjustment AS INAllowed
                       ,SUM (tde.INNetworkAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER
                        - dbo.fnGetSafeDivisionResult (rp.RelatedPartiesAdjustment, dist.INDistributionPercent) AS INNetworkAllowed
                       ,SUM (tde.INUnits) AS INUnits
                       ,SUM (tde.INAdmits) AS INAdmits
                       ,tde.OONDeductibleApplies
                       ,SUM (tde.OONAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER AS OONAllowed
                       ,SUM (tde.OONNetworkAllowed) * ISNULL (sqs.ProjectedSQS, 1) * @MER AS OONNetworkAllowed
                       ,SUM (tde.OONUnits) AS OONUnits
                       ,SUM (tde.OONAdmits) AS OONAdmits
            FROM        #TrendedDEandNonDE tde
            LEFT JOIN   #SQSFactors sqs
                   ON sqs.BenefitCategoryID = tde.BenefitCategoryID
                      AND   sqs.MARatingOptionID = tde.MARatingOptionID
            LEFT JOIN   #RPAdj rp
                   ON rp.BenefitCategoryID = tde.BenefitCategoryID
            LEFT JOIN   #INOONDist dist
                   ON dist.BenefitCategoryID = tde.BenefitCategoryID
            GROUP BY    tde.MARatingOptionID
                       ,tde.BenefitCategoryID
                       ,tde.DualEligibleTypeID
                       ,tde.INDeductibleApplies
                       ,sqs.ProjectedSQS
                       ,rp.RelatedPartiesAdjustment
                       ,dist.INDistributionPercent
                       ,tde.OONDeductibleApplies;


            -------------------------------------------------------
            ----- STEP 11: CREDIBILITY BLENDING -------------------
            -------------------------------------------------------
            --Blend total (DualEligibleTypeID = 2)
            IF OBJECT_ID ('tempdb..#BlendedTotalWS2') IS NOT NULL
                DROP TABLE #BlendedTotalWS2;
            CREATE TABLE #BlendedTotalWS2
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedTotalWS2
            SELECT      3 AS MARatingOptionID   --Blended
                       ,ttExp.BenefitCategoryID
                       ,ttExp.DualEligibleTypeID
                       ,ttExp.INDeductibleApplies
                       ,ttExp.INAllowed * @Credibility + ttMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,ttExp.INNetworkAllowed * @Credibility + ttMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,ttExp.INUnits * @Credibility + ttMan.INUnits * (1 - @Credibility) AS INUnits
                       ,ttExp.INAdmits * @Credibility + ttMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,ttExp.OONDeductibleApplies
                       ,ttExp.OONAllowed * @Credibility + ttMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,ttExp.OONNetworkAllowed * @Credibility + ttMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,ttExp.OONUnits * @Credibility + ttMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,ttExp.OONAdmits * @Credibility + ttMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedTotalWS2 ttExp
            LEFT JOIN   #TrendedTotalWS2 ttMan
                   ON ttMan.BenefitCategoryID = ttExp.BenefitCategoryID
                      AND   ttMan.DualEligibleTypeID = ttExp.DualEligibleTypeID
            WHERE       ttExp.MARatingOptionID = 1 --Experience
                        AND ttMan.MARatingOptionID = 2; --Manual

            --Blend NonDE# and DE# (DualEligibleTypeID = 0,1 respectively)
            IF OBJECT_ID ('tempdb..#BlendedDEandNonDEWS2') IS NOT NULL
                DROP TABLE #BlendedDEandNonDEWS2;
            CREATE TABLE #BlendedDEandNonDEWS2
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedDEandNonDEWS2
            SELECT      3 AS MARatingOptionID   --Blended
                       ,tExp.BenefitCategoryID
                       ,tExp.DualEligibleTypeID
                       ,tExp.INDeductibleApplies
                       ,tExp.INAllowed * @Credibility + tMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,tExp.INNetworkAllowed * @Credibility + tMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,tExp.INUnits * @Credibility + tMan.INUnits * (1 - @Credibility) AS INUnits
                       ,tExp.INAdmits * @Credibility + tMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,tExp.OONDeductibleApplies
                       ,tExp.OONAllowed * @Credibility + tMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,tExp.OONNetworkAllowed * @Credibility + tMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,tExp.OONUnits * @Credibility + tMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,tExp.OONAdmits * @Credibility + tMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedDEandNonDEWS2 tExp
            LEFT JOIN   #TrendedDEandNonDEWS2 tMan
                   ON tMan.BenefitCategoryID = tExp.BenefitCategoryID
                      AND   tMan.DualEligibleTypeID = tExp.DualEligibleTypeID
            WHERE       tExp.MARatingOptionID = 1 --Experience
                        AND tMan.MARatingOptionID = 2;  --Manual


            -------------------------------------------------------
            ----- STEP 12: APPLY SAFE HARBOR ----------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#BlendedFinalWS2') IS NOT NULL
                DROP TABLE #BlendedFinalWS2;
            CREATE TABLE #BlendedFinalWS2
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedFinalWS2

            --Total
            SELECT  bt.MARatingOptionID
                   ,bt.BenefitCategoryID
                   ,bt.DualEligibleTypeID
                   ,bt.INDeductibleApplies
                   ,bt.INAllowed
                   ,bt.INNetworkAllowed
                   ,bt.INUnits
                   ,bt.INAdmits
                   ,bt.OONDeductibleApplies
                   ,bt.OONAllowed
                   ,bt.OONNetworkAllowed
                   ,bt.OONUnits
                   ,bt.OONAdmits
            FROM    #BlendedTotalWS2 bt

            UNION ALL

            --NonDE# and DE#
            SELECT      bde.MARatingOptionID
                       ,bde.BenefitCategoryID
                       ,bde.DualEligibleTypeID
                       ,bde.INDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAllowed ELSE bde.INAllowed END AS INAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INNetworkAllowed ELSE bde.INNetworkAllowed END AS INNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INUnits ELSE bde.INUnits END AS INUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAdmits ELSE bde.INAdmits END AS INAdmits
                       ,bde.OONDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAllowed ELSE bde.OONAllowed END AS OONAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONNetworkAllowed ELSE bde.OONNetworkAllowed END AS OONNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONUnits ELSE bde.OONUnits END AS OONUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAdmits ELSE bde.OONAdmits END AS OONAdmits
            FROM        #BlendedDEandNonDEWS2 bde
            LEFT JOIN   #BlendedTotalWS2 bt
                   ON bt.BenefitCategoryID = bde.BenefitCategoryID;


            -------------------------------------------------------
            ----- STEP 13: FINAL DATA SETUP WS2 -------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#FinalWS2') IS NOT NULL DROP TABLE #FinalWS2;
            CREATE TABLE #FinalWS2
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    DEC(14, 6)
                ,OONUnits             DEC(14, 6)
                ,OONAdmits            DEC(14, 6));
            INSERT INTO #FinalWS2

            --Total Experience and Manual
            SELECT  tt.MARatingOptionID
                   ,tt.BenefitCategoryID
                   ,tt.DualEligibleTypeID
                   ,tt.INDeductibleApplies
                   ,tt.INAllowed
                   ,tt.INNetworkAllowed
                   ,tt.INUnits
                   ,tt.INAdmits
                   ,tt.OONDeductibleApplies
                   ,tt.OONAllowed
                   ,tt.OONNetworkAllowed
                   ,tt.OONUnits
                   ,tt.OONAdmits
            FROM    #TrendedTotalWS2 tt

            UNION ALL

            --NonDE# and DE# Experience and Manual
            SELECT  tde.MARatingOptionID
                   ,tde.BenefitCategoryID
                   ,tde.DualEligibleTypeID
                   ,tde.INDeductibleApplies
                   ,tde.INAllowed
                   ,tde.INNetworkAllowed
                   ,tde.INUnits
                   ,tde.INAdmits
                   ,tde.OONDeductibleApplies
                   ,tde.OONAllowed
                   ,tde.OONNetworkAllowed
                   ,tde.OONUnits
                   ,tde.OONAdmits
            FROM    #TrendedDEandNonDEWS2 tde

            UNION ALL

            --Total, NonDE#, and DE# Blended
            SELECT  bf.MARatingOptionID
                   ,bf.BenefitCategoryID
                   ,bf.DualEligibleTypeID
                   ,bf.INDeductibleApplies
                   ,bf.INAllowed
                   ,bf.INNetworkAllowed
                   ,bf.INUnits
                   ,bf.INAdmits
                   ,bf.OONDeductibleApplies
                   ,bf.OONAllowed
                   ,bf.OONNetworkAllowed
                   ,bf.OONUnits
                   ,bf.OONAdmits
            FROM    #BlendedFinalWS2 bf;


            -------------------------------------------------------
            ----- STEP 14: DELETE FROM / WRITE TO TABLE WS2 -------
            -------------------------------------------------------
            --CalcBenefitProjection
            DELETE  FROM dbo.CalcBenefitProjection WHERE    ForecastID = @XForecastID;

            INSERT INTO dbo.CalcBenefitProjection
                (PlanYearID
                ,ForecastID
                ,BenefitCategoryID
                ,DualEligibleTypeID
                ,MARatingOptionID
                ,INUnits
                ,INAdmits
                ,INAllowed
                ,INDedApplies
                ,INNetworkAllowed
                ,OONUnits
                ,OONAdmits
                ,OONAllowed
                ,OONDedApplies
                ,OONNetworkAllowed
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  @PlanYearID AS PlanYearID
                   ,@XForecastID AS ForecastID
                   ,ws2.BenefitCategoryID
                   ,ws2.DualEligibleTypeID
                   ,ws2.MARatingOptionID
                   ,ws2.INUnits
                   ,ws2.INAdmits
                   ,ws2.INAllowed
                   ,ws2.INDeductibleApplies
                   ,ws2.INNetworkAllowed
                   ,ws2.OONUnits
                   ,ws2.OONAdmits
                   ,ws2.OONAllowed
                   ,ws2.OONDeductibleApplies
                   ,ws2.OONNetworkAllowed
                   ,@XUserID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM    #FinalWS2 ws2;


            COMMIT TRANSACTION transaction_spCalcBen;

        END TRY

        BEGIN CATCH

            IF (@@TranCount > @tranCount) --Check if transaction in TRY block was not closed
                BEGIN
                    ROLLBACK TRANSACTION transaction_spCalcBen;
                END;
        END CATCH;

    END;
GO
