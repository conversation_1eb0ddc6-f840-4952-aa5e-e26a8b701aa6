SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
 
-- =============================================      
-- Author:  Satyam <PERSON>al   
-- Create date: 13-03-2020
-- Description:  Update is include in tyrend column  
--      
--      
-- PARAMETERS:      
-- Input:        
--         
-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       


-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2020-Mar-13  1		  Initial version.										    Satyam Singhal
-- 2020-Apr-17  2      Updated for using CPS                                        Kiran Pant
-- ----------------------------------------------------------------------------------------------------------------------
--DECLARE @MessageFromBackend NVARCHAR(MAX),@Result BIT
--EXEC [dbo].[spAppUpdateCurrentPlan] 814,1,0,'sls9596',@MessageFromBackend OUTPUT,@Result OUTPUT
--SELECT @MessageFromBackend,@Result
CREATE PROCEDURE [dbo].[spAppTrendUpdateIsIncludeinTrend]
    @CPSList VARCHAR(MAX) = NULL,
	@PlanYearList VARCHAR(MAX) = NULL,
    @Yes BIT,
    @No BIT,
    @LastUpdateByID CHAR(7),
    @MessageFromBackend NVARCHAR(MAX) OUT,
    @Result BIT OUT
AS
BEGIN

    BEGIN TRANSACTION;
    BEGIN TRY

        CREATE TABLE #temp
        (
            id INT IDENTITY(1, 1),
            CurrentPlanInfoID SMALLINT
        );

        INSERT INTO #temp
        (
            CurrentPlanInfoID
        )
        SELECT SP.PlanInfoID
        FROM  [dbo].[SavedPlanInfo] SP
               
        WHERE SP.CPS = (CASE
                                   WHEN @CPSList IS NULL THEN
                                       SP.CPS
                                   ELSE
                                       '-1'
                               END
                              )
              OR SP.CPS IN
                 (
                     SELECT Value FROM dbo.fnStringSplit(@CPSList, ',')
                 )
                 AND CAST(SP.PlanYear AS VARCHAR) IN 
				 (SELECT Value FROM dbo.fnStringSplit(@PlanYearList, ','));
				 

        DECLARE @Count INT = 1;
        DECLARE @Max INT;
        DECLARE @CurrentPlanInfoID SMALLINT;
        SELECT @Max = MAX(id)
        FROM #temp;
        IF (@Yes = 1)
        BEGIN

            WHILE (@Count <= @Max)
            BEGIN
                --SELECT @Count,@Max
                SELECT @CurrentPlanInfoID = CurrentPlanInfoID
                FROM #temp
                WHERE id = @Count;
                UPDATE dbo.SavedPlanInfo
                SET IsIncludeInTrend = 1,
                    LastUpdateByID = UPPER(@LastUpdateByID),
                    LastUpdateDateTime = GETDATE()
                WHERE PlanInfoID = @CurrentPlanInfoID;
                -- AND LastUpdateByID = @LastUpdateByID;
                SET @Count = @Count + 1;
            END;
            SET @MessageFromBackend = 'The trend rollup selections have been modified for your plans';
            SET @Result = 1;
        END;

        ELSE IF (@No = 1)
        BEGIN
            SET @Count = 1;

            WHILE (@Count <= @Max)
            BEGIN

                SELECT @CurrentPlanInfoID = CurrentPlanInfoID
                FROM #temp
                WHERE id = @Count;
                UPDATE dbo.SavedPlanInfo
                SET IsIncludeInTrend = 0,
                    LastUpdateByID = UPPER(@LastUpdateByID),
                    LastUpdateDateTime = GETDATE()
                WHERE PlanInfoID = @CurrentPlanInfoID;
                -- AND LastUpdateByID = @LastUpdateByID;
                SET @Count = @Count + 1;
            END;
            SET @MessageFromBackend = 'The trend rollup selections have been modified for your plans';
            SET @Result = 1;
        END;

        ELSE
        BEGIN
            SET @MessageFromBackend = 'The trend rollup selections have not been modified for your plans';
            SET @Result = 0;
        END;
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        SET @Result = 0;
        --SET @MessageFromBackend= 'An error occurred while processing your request. <NAME_EMAIL>.'
        DECLARE @ErrorMessage NVARCHAR(4000);
        DECLARE @ErrorSeverity INT;
        DECLARE @ErrorState INT;
        DECLARE @ErrorException NVARCHAR(4000);
        DECLARE @errSrc VARCHAR(MAX) = ISNULL(ERROR_PROCEDURE(), 'SQL'),
                @currentdate DATETIME = GETDATE();

        SELECT @ErrorMessage = ERROR_MESSAGE(),
               @ErrorSeverity = ERROR_SEVERITY(),
               @ErrorState = ERROR_STATE(),
               @ErrorException
                   = N'Line Number :' + CAST(ERROR_LINE() AS VARCHAR) + N' .Error Severity :'
                     + CAST(@ErrorSeverity AS VARCHAR) + N' .Error State :' + CAST(@ErrorState AS VARCHAR);
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);

        ROLLBACK TRANSACTION;

        ---Insert into app log for logging error------------------
        EXEC spAppAddLogEntry @currentdate,
                              '',
                              'ERROR',
                              @errSrc,
                              @ErrorMessage,
                              @ErrorException,
                              @LastUpdateByID;

    END CATCH;
END;


GO
