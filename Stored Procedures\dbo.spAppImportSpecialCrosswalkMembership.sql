SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  Vikrant <PERSON>
-- Create date: 2024-Nov-19
-- Description: spAppImportSpecialCrosswalkMembership import data in to SavedSpecialCrosswalkMembershipusing staging table 
--      
--      
-- PARAMETERS:      
-- Input:    @StageId 
    
-- TABLES:    
-- Read:        ImportDataStaging
--              
--            
-- Write:		SavedSpecialCrosswalkMembership   
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION   CHANGES MADE											DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2024-Nov-19		1	     Initial version.										Vikrant Bagal
----------------------------------------------------------------------------------------------------------------------
-- ======================================================================================================================
 CREATE PROCEDURE dbo.spAppImportSpecialCrosswalkMembershipData
(
	@StageId VARCHAR(100) 
)
AS
BEGIN
   
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;

	SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId

	DECLARE  @temp_SpecialCrosswalkMembership AS TABLE 
	(
			PlanType VARCHAR(4) ,
			GrpIndvInD VARCHAR(max) ,
			CPS varchar(13) ,
			ResidenceCounty varchar(max) ,
			ResidenceState varchar(max) ,
			SSStateCountyCd INT,
			Product varchar(max) ,
			SNPType varchar(max) ,
			VPP varchar(1) ,
			MedcaidEligible VARCHAR(1) ,
			MedicaidStatus VARCHAR(max) ,
			MedicaidStatusCd INT,
			MedicaidCategory VARCHAR(max) ,
			AgeIndicator VARCHAR(3) ,
			MBR_CNT  int ,
			[LastUpdateByID] VARCHAR(7),
			LastUpdateDateTime DATETIME 

	)

	 INSERT INTO @temp_SpecialCrosswalkMembership
	 SELECT  
			PlanType,
			GrpIndvInD,
			CPS,
			ResidenceCounty,
			ResidenceState,
			SSStateCountyCd,
			Product,
			SNPType,
			VPP,
			MedcaidEligible,
			MedicaidStatus,
			MedicaidStatusCd,
			MedicaidCategory,
			AgeIndicator,
			MBR_CNT  ,
           @UserId [LastUpdateByID],
           GETDATE()  LastUpdateDateTime 
    FROM
        OPENJSON(@jsonData, '$.SpecialCrosswalkMembership')
        WITH
        (
            PlanType VARCHAR(4) ,
			GrpIndvInD VARCHAR(max) ,
			CPS varchar(13) ,
			ResidenceCounty varchar(max) ,
			ResidenceState varchar(max) ,
			SSStateCountyCd INT,
			Product varchar(max) ,
			SNPType varchar(max) ,
			VPP varchar(1) ,
			MedcaidEligible VARCHAR(1) ,
			MedicaidStatus VARCHAR(max) ,
			MedicaidStatusCd INT,
			MedicaidCategory VARCHAR(max) ,
			AgeIndicator VARCHAR(3) ,
			MBR_CNT  int  
        );


	DELETE FROM dbo.SavedSpecialCrosswalkMembership WHERE 1=1;

	INSERT INTO dbo.SavedSpecialCrosswalkMembership
	(
	    PlanType,
	    GrpIndvInD,
	    CPS,
	    ResidenceCounty,
	    ResidenceState,
	    SSStateCountyCd,
	    Product,
	    SNPType,
	    VPP,
	    MedcaidEligible,
	    MedicaidStatus,
	    MedicaidStatusCd,
	    MedicaidCategory,
	    AgeIndicator,
	    MBR_CNT	,
		LastUpdateByID,
		LastUpdateDateTime
	)
	 SELECT 
		 PlanType,
	    GrpIndvInD,
	    CPS,
	    ResidenceCounty,
	    ResidenceState,
	    SSStateCountyCd,
	    Product,
	    SNPType,
	    VPP,
	    MedcaidEligible,
	    MedicaidStatus,
	    MedicaidStatusCd,
	    MedicaidCategory,
	    AgeIndicator,
	    MBR_CNT,	 
		[LastUpdateByID],
		LastUpdateDateTime
	 FROM @temp_SpecialCrosswalkMembership

	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId

END 

				 