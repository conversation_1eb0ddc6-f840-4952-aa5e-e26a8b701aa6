SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetRiskscoreJustification] ******/

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetRiskscoreJustification
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE:MAR-22-2011
--
-- DESCRIPTION: This function returns  Justification for Target Risk score
-- PARAMETERS:
--	Inputs:
--		@ForecastID                    
--
-- TABLES:
--	Read:
--		SavedPlanRiskScoreOverride
--	Write: NONE
--
-- VIEWS: Read: NONE
--
-- RETURNS: Justification
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		CHANGES MADE																		DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------------------
-- 2011-Mar-22		1		Initial Version																		Sule Dauda
-- ----------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetRiskscoreJustification]
(
	@ForecastID INT
)
RETURNS VARCHAR(500) AS  
BEGIN 

	DECLARE @Justification VARCHAR(500)

	SELECT @Justification = Justification
	FROM SavedPlanRiskScoreOverride
    WHERE ForecastID = @ForecastID


	RETURN  @Justification

END
GO
