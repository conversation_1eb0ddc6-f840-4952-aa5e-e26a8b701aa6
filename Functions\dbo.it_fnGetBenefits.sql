SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[it_fnGetBenefits]  ******/

-- replaced SynchronizedBenefitCategoryID with BenefitCategoryID
CREATE FUNCTION [dbo].[it_fnGetBenefits](@ForecastID INT) RETURNS TABLE AS RETURN (SELECT     l.BenefitCategoryName, l.BenefitCategoryID
                                                                                                                                                                                                       FROM         LkpIntBenefitCategory l
                                                                                                                                                                                                       WHERE     l.IsUsed = 1 AND l.IsEnabled = 1 AND 
                                                                                                                                                                                                                              l.BenefitCategoryID NOT IN
                                                                                                                                                                                                                                  ((SELECT     s.BenefitCategoryID
                                                                                                                                                                                                                                      FROM         SavedPlanBenefitDetail s
                                                                                                                                                                                                                                      WHERE     s.ForecastID = @ForecastID AND 
                                                                                                                                                                                                                                                            s.BenefitCategoryID <> 0 AND 
                                                                                                                                                                                                                                                            s.IsBenefitYearCurrentYear = 0)
                                                                                                                                                                                                                              UNION ALL
                                                                                                                                                                                                                              (SELECT     s.BenefitCategoryID
                                                                                                                                                                                                                               FROM         SavedPlanBenefitDetail s
                                                                                                                                                                                                                               WHERE     s.ForecastID = @ForecastID AND 
                                                                                                                                                                                                                                                      s.BenefitCategoryID <> 0 AND 
                                                                                                                                                                                                                                                      s.IsBenefitYearCurrentYear = 0)))

GO
