SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO


-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetProjNonDE#RiskScore
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE: 2007-Jan-12
--
-- <PERSON>RIPTION: Returns the number of digits in the specified integer.
--
-- PARAMETERS:
--	Input: @Number - The number whose digits will be counted.
--
-- RETURNS: Returns Projected Non DE# Risk Score
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: 
--      fnAppGetBenchmarkSummary(@ForecastID),
--      fnAppGetBenchmarkMembership(@ForecastID)
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		    CHANGES MADE						                    DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Jan-12		1			Initial Version							                Michael Siekerka
--
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetProjNonDE#RiskScore]
(
    @ForecastID INT
)
RETURNS FLOAT AS
BEGIN 
    DECLARE @RiskScore FLOAT
    SELECT @RiskScore = 
        CASE WHEN SUM(mm.NonDE#Aged) = 0 
            THEN AVG(bm.ProjNonDE#RiskScore) 
            ELSE SUM(mm.NonDE#Aged)* bm.ProjNonDE#RiskScore / SUM(mm.NonDE#Aged) 
        END
    FROM fnAppGetBenchmarkSummary(@ForecastID) bm,
    fnAppGetBenchmarkMembership(@ForecastID) mm
    GROUP BY bm.ProjNonDE#RiskScore
    RETURN @RiskScore
END
GO
