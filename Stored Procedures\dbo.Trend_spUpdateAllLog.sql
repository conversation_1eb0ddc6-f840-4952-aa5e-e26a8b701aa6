SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO


-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_spUpdateAllLog
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: 2020-05-20
--
-- DESCRIPTION: Writes to the update all log when MAAST runs dbo.Trend_spUpdateAll
--              
-- PARAMETERS:
--  Input  :	@ProcNumber
--              @LastUpdateByID
--              @Message
--
--  Output : NONE
--
-- TABLES : Read : 		    
--					
--          Write: Trend_SavedUpdateAllLog
--                  
--
-- VIEWS: Read: 
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-05-20			1		Initial Version														Andy Blink
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Create PROCEDURE [dbo].[Trend_spUpdateAllLog]
 @ProcNumber AS  INT
,@LastUpdateByID CHAR(7) = 'Initial'
,@Message AS     VARCHAR(MAX)

AS
    BEGIN
        SET NOCOUNT ON;
        BEGIN
            INSERT INTO dbo.Trend_SavedUpdateAllLog
            VALUES (@ProcNumber, @Message, @LastUpdateByID, GETDATE (),NEWID());
        END;
    END;
GO
