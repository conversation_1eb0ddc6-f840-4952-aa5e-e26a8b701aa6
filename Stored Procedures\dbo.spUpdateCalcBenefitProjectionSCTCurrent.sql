SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spUpdateCalcBenefitProjectionSCTCurrent
--
-- CREATOR:			Man<PERSON>a <PERSON>
--
-- CREATED DATE:	2008-DEC-12
-- HEADER UPDATED:	2023-JAN-24 Jake Lewis
--
-- DESCRIPTION:		This SP functions very similarly to spCalcBenefitProjection, which applies trends to base year data in order to 
--					calculate projected (bid) year allowed. For a more thorough description of the methodology, please refer to the 
--					header for spCalcBenefitProjection. 
--
--					In spUpdateCalcBenefitProjectionSCTCurrent, there are some slight differences from spCalcBenefitProjection: 
--
--						CalcBenefitProjectionSCTCurrent: Apply only current year (E2C) trends to base data, as well as sequestration 
--						factors without dampening. Do not adjust for related party profits. 
--
--						CalcBenefitProjectionSCTBid: Apply both current year (E2C) and bid year (C2P) trends to base data, apply 
--						sequestration factors without dampening, and apply MER. Do not adjust for related party profits. 
--				
--		
-- PARAMETERS:
--  Input  :		@ForecastID
--					@UserID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			C2PMERCostMultAdj
--					SavedMERActAdj
--					SavedPlanHeader
--					CalcSQSFactors
--					CalcPlanExperienceByBenefitCatNonSQS
--					CalcProjectionFactorsNonSQS
--					SavedPlanBenefitDetail
--					LkpIntBenefitCategory
--
--  Write:			CalcBenefitProjectionSCTCurrent
--					CalcBenefitProjectionSCTBid
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnAppGetBenchmarkMembership
--					fnGetBidYear
--					fnGetCredibilityFactor
--					fnGetSafeDivisionResult
--					fnGetSafeDivisionResultReturnOne				
--					fnIsSafeHarborExemption
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2018-DEC-12      1           Initial Version                                                     Manisha Tyagi
-- 2019-OCT-30		2			Removed 'HUMAD\' to UserID											Chhavi Sinha
-- 2020-JAN-13      3           [20.02] Deductible Exlclusion Flags and general schema cleanup      Keith Galloway
-- 2022-JUL-07		4			Added limitation when pulling bid data from SavedPlanBenefitDetail
--								to limit to only the pricing option "WHERE pbd.IsLiveIndex = 1"		Aleksandar Dimitrijevic
-- 2023-JAN-24		5			Methodology changes for removal of dual population adj factor		Jake Lewis
-- 2023-FEB-08		6			Fix issue where no MARatingOptionID = 2,3 records are created if 
--									manual base data is missing.									Jake Lewis
-- 2024-OCT-07		7			Cost Share Basis: add handling for new IsIncludeInCostShareBasis
--									field in the base data tables (requires SUM() in the #BaseData
--									temp table).													Jake Lewis
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUpdateCalcBenefitProjectionSCTCurrent]
    (
    @ForecastID INT
   ,@UserID     CHAR(7))

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY

            DECLARE @tranCount INT = @@TranCount; --Current transaction count

            BEGIN TRANSACTION trans_CalcBenProjSCT;

            -- Declare variables
            DECLARE @XForecastID INT = @ForecastID; --Internal variable for input param
            DECLARE @XUserID CHAR(7) = @UserID; --Internal variable for input param
            DECLARE @LastUpdateDateTime DATETIME = GETDATE ();
            DECLARE @Credibility FLOAT = dbo.fnGetCredibilityFactor (@XForecastID);
            DECLARE @PlanYearID SMALLINT = dbo.fnGetBidYear ();
            DECLARE @IsSafeHarbor BIT = dbo.fnIsSafeHarborExemption (@XForecastID);
            DECLARE @MER DEC(18, 15) = (SELECT      DISTINCT
                                                    mer.C2PMERCostMultAdj
                                        FROM        dbo.SavedMERActAdj mer WITH (NOLOCK)
                                       INNER JOIN   dbo.SavedPlanHeader sph WITH (NOLOCK)
                                               ON sph.ContractNumber = mer.ContractNumber
                                                  AND   sph.PlanID = mer.PlanID
                                                  AND   sph.SegmentID = mer.SegmentID
                                        WHERE       sph.ForecastID = @XForecastID);


            -------------------------------------------------------
            ----- STEP 1: LOAD SOURCE DATA ------------------------
            -------------------------------------------------------
            -- Membership
            IF OBJECT_ID ('tempdb..#Membership') IS NOT NULL DROP TABLE #Membership;
            CREATE TABLE #Membership
                (NonDE#Aged   DEC(28, 6)
                ,NonDEPercent DEC(18, 15)
                ,DE#Aged      DEC(28, 6)
                ,DEPercent    DEC(18, 15)
                ,TotalAged    DEC(28, 6));
            INSERT INTO #Membership
            SELECT  SUM (NonDE#Aged) AS NonDE#Aged
                   ,dbo.fnGetSafeDivisionResultReturnOne (SUM (NonDE#Aged), SUM (TotalAged)) AS NonDEPercent
                   ,SUM (DE#Aged) AS DE#Aged
                   ,dbo.fnGetSafeDivisionResultReturnOne (SUM (DE#Aged), SUM (TotalAged)) AS DEPercent
                   ,SUM (TotalAged) AS TotalAged
            FROM    dbo.fnAppGetBenchmarkMembership (@XForecastID);

            -- SQS Factors
            IF OBJECT_ID ('tempdb..#SQSFactors') IS NOT NULL DROP TABLE #SQSFactors;
            CREATE TABLE #SQSFactors
                (MARatingOptionID        TINYINT
                ,BenefitCategoryID       INT
                ,DualEligibleTypeID      TINYINT
                ,ProjectedSQSNoDampening DEC(14, 6));
            INSERT INTO #SQSFactors
            SELECT  MARatingOptionID
                   ,BenefitCategoryID
                   ,DualEligibleTypeID
                   ,ProjectedSQSNoDampening
            FROM    dbo.CalcSQSFactors WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID;

            -- Base Data
            -- Need SUM()s below to get data for both IsIncludeInCostShareBasis=1 and =0
            IF OBJECT_ID ('tempdb..#BaseData') IS NOT NULL DROP TABLE #BaseData;
            CREATE TABLE #BaseData
                (MARatingOptionID   TINYINT
                ,BenefitCategoryID  INT
                ,DualEligibleTypeID TINYINT
                ,INAllowed          DEC(14, 6)
                ,INNetworkAllowed   DEC(14, 6)
                ,INUnits            DEC(14, 6)
                ,INAdmits           DEC(14, 6)
                ,OONAllowed         DEC(14, 6)
                ,OONNetworkAllowed  DEC(14, 6)
                ,OONUnits           DEC(14, 6)
                ,OONAdmits          DEC(14, 6));
            INSERT INTO #BaseData
            SELECT      MARatingOptionID
                       ,BenefitCategoryID
                       ,DualEligibleTypeID
                       ,SUM (INAllowed) AS INAllowed
                       ,SUM (INNetworkAllowed) AS INNetworkAllowed
                       ,SUM (INUnits) AS INUnits
                       ,SUM (INAdmits) AS INAdmits
                       ,SUM (OONAllowed) AS OONAllowed
                       ,SUM (OONNetworkAllowed) AS OONNetworkAllowed
                       ,SUM (OONUnits) AS OONUnits
                       ,SUM (OONAdmits) AS OONAdmits
            FROM        dbo.CalcPlanExperienceByBenefitCatNonSQS WITH (NOLOCK)
            WHERE       ForecastID = @XForecastID
                        AND PlanYearID = @PlanYearID
            GROUP BY    MARatingOptionID
                       ,BenefitCategoryID
                       ,DualEligibleTypeID;

            -- Projection Factors
            IF OBJECT_ID ('tempdb..#ProjFactors') IS NOT NULL DROP TABLE #ProjFactors;
            CREATE TABLE #ProjFactors
                (MARatingOptionID         TINYINT
                ,BenefitCategoryID        INT
                ,E2CINClaimFactorUnits    DEC(18, 15)
                ,C2BINClaimFactorUnits    DEC(18, 15)
                ,E2CINClaimFactorAllowed  DEC(18, 15)
                ,C2BINClaimFactorAllowed  DEC(18, 15)
                ,E2COONClaimFactorUnits   DEC(18, 15)
                ,C2BOONClaimFactorUnits   DEC(18, 15)
                ,E2COONClaimFactorAllowed DEC(18, 15)
                ,C2BOONClaimFactorAllowed DEC(18, 15));
            INSERT INTO #ProjFactors
            SELECT  MARatingOptionID
                   ,BenefitCategoryID
                   ,E2CINClaimFactorUnits
                   ,C2BINClaimFactorUnits
                   ,E2CINClaimFactorAllowed
                   ,C2BINClaimFactorAllowed
                   ,E2COONClaimFactorUnits
                   ,C2BOONClaimFactorUnits
                   ,E2COONClaimFactorAllowed
                   ,C2BOONClaimFactorAllowed
            FROM    dbo.CalcProjectionFactorsNonSQS WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID;

            -- Determine if deductible applies for each benefit category
            IF OBJECT_ID ('tempdb..#DedApp') IS NOT NULL DROP TABLE #DedApp;
            CREATE TABLE #DedApp
                (BenefitCategoryID INT
                ,INDedApplies      BIT
                ,OONDedApplies     BIT);
            INSERT INTO #DedApp
            SELECT  BenefitCategoryID
                   ,INDedApplies
                   ,OONDedApplies
            FROM    dbo.SavedPlanBenefitDetail WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND PlanYearID = @PlanYearID
                    AND IsBenefitYearCurrentYear = 0
                    AND IsLiveIndex = 1
                    AND BenefitOrdinalID = 1;


            -------------------------------------------------------
            ----- CalcBenefitProjectionSCTCurrent -----------------
            -------------------------------------------------------

            -------------------------------------------------------
            ----- STEP 2: APPLY TRENDS TO TOTAL BASE DATA ---------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#TrendedTotal') IS NOT NULL DROP TABLE #TrendedTotal;
            CREATE TABLE #TrendedTotal
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #TrendedTotal
            SELECT      bd.MARatingOptionID
                       ,bd.BenefitCategoryID
                       ,bd.DualEligibleTypeID
                       ,da.INDedApplies
                       ,bd.INAllowed * pf.E2CINClaimFactorAllowed * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS INAllowed
                       ,bd.INNetworkAllowed * pf.E2CINClaimFactorAllowed * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS INNetworkAllowed
                       ,bd.INUnits * pf.E2CINClaimFactorUnits AS INUnits
                       ,bd.INAdmits * pf.E2CINClaimFactorUnits AS INAdmits
                       ,da.OONDedApplies
                       ,bd.OONAllowed * pf.E2COONClaimFactorAllowed * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS OONAllowed
                       ,bd.OONNetworkAllowed * pf.E2COONClaimFactorAllowed * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS OONNetworkAllowed
                       ,bd.OONUnits * pf.E2COONClaimFactorUnits AS OONUnits
                       ,bd.OONAdmits * pf.E2COONClaimFactorUnits AS OONAdmits
            FROM        #BaseData bd
           INNER JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                   ON libc.BenefitCategoryID = bd.BenefitCategoryID
           INNER JOIN   #DedApp da
                   ON da.BenefitCategoryID = bd.BenefitCategoryID
            LEFT JOIN   #ProjFactors pf
                   ON pf.BenefitCategoryID = bd.BenefitCategoryID
                      AND   pf.MARatingOptionID = bd.MARatingOptionID
            LEFT JOIN   #SQSFactors sqs
                   ON sqs.BenefitCategoryID = bd.BenefitCategoryID
                      AND   sqs.MARatingOptionID = bd.MARatingOptionID
            WHERE       libc.IsEnabled = 1
                        AND bd.MARatingOptionID IN (1, 2) --Experience and manual; we will credibility blend later
                        AND bd.DualEligibleTypeID = 2;  --Total


            -------------------------------------------------------
            ----- STEP 3: CALCULATE CALIBRATION FACTORS -----------
            -------------------------------------------------------
            -- Calibration factors are used to allocate projected totals to DE# and NonDE#
            IF OBJECT_ID ('tempdb..#CalibrationFactors') IS NOT NULL
                DROP TABLE #CalibrationFactors;
            CREATE TABLE #CalibrationFactors
                (MARatingOptionID    TINYINT
                ,BenefitCategoryID   INT
                ,DualEligibleTypeID  TINYINT
                ,INAllowedCF         FLOAT
                ,INNetworkAllowedCF  FLOAT
                ,INUnitsCF           FLOAT
                ,INAdmitsCF          FLOAT
                ,OONAllowedCF        FLOAT
                ,OONNetworkAllowedCF FLOAT
                ,OONUnitsCF          FLOAT
                ,OONAdmitsCF         FLOAT);
            INSERT INTO #CalibrationFactors

            --NonDE# Calibration Factors
            SELECT      NonDEBaseData.MARatingOptionID
                       ,NonDEBaseData.BenefitCategoryID
                       ,NonDEBaseData.DualEligibleTypeID
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.INAllowed
                       ,(NonDEBaseData.INAllowed * mm.NonDEPercent + DEBaseData.INAllowed * mm.DEPercent)) AS INAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.INNetworkAllowed
                       ,(NonDEBaseData.INNetworkAllowed * mm.NonDEPercent + DEBaseData.INNetworkAllowed * mm.DEPercent)) AS INNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.INUnits
                       ,(NonDEBaseData.INUnits * mm.NonDEPercent + DEBaseData.INUnits * mm.DEPercent)) AS INUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.INAdmits
                       ,(NonDEBaseData.INAdmits * mm.NonDEPercent + DEBaseData.INAdmits * mm.DEPercent)) AS INAdmitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.OONAllowed
                       ,(NonDEBaseData.OONAllowed * mm.NonDEPercent + DEBaseData.OONAllowed * mm.DEPercent)) AS OONAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.OONNetworkAllowed
                       ,(NonDEBaseData.OONNetworkAllowed * mm.NonDEPercent + DEBaseData.OONNetworkAllowed * mm.DEPercent)) AS OONNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.OONUnits
                       ,(NonDEBaseData.OONUnits * mm.NonDEPercent + DEBaseData.OONUnits * mm.DEPercent)) AS OONUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        NonDEBaseData.OONAdmits
                       ,(NonDEBaseData.OONAdmits * mm.NonDEPercent + DEBaseData.OONAdmits * mm.DEPercent)) AS OONAdmitsCF
            FROM        #BaseData NonDEBaseData
            LEFT JOIN   #BaseData DEBaseData
                   ON DEBaseData.MARatingOptionID = NonDEBaseData.MARatingOptionID
                      AND   DEBaseData.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
           INNER JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                   ON libc.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
            LEFT JOIN   #Membership mm
                   ON 1 = 1
            WHERE       libc.IsEnabled = 1
                        AND NonDEBaseData.DualEligibleTypeID = 0 --NonDE#
                        AND DEBaseData.DualEligibleTypeID = 1   --DE#

            --DE# Calibration Factors
            UNION ALL

            SELECT      DEBaseData.MARatingOptionID
                       ,DEBaseData.BenefitCategoryID
                       ,DEBaseData.DualEligibleTypeID
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.INAllowed
                       ,(NonDEBaseData.INAllowed * mm.NonDEPercent + DEBaseData.INAllowed * mm.DEPercent)) AS INAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.INNetworkAllowed
                       ,(NonDEBaseData.INNetworkAllowed * mm.NonDEPercent + DEBaseData.INNetworkAllowed * mm.DEPercent)) AS INNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.INUnits, (NonDEBaseData.INUnits * mm.NonDEPercent + DEBaseData.INUnits * mm.DEPercent)) AS INUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.INAdmits
                       ,(NonDEBaseData.INAdmits * mm.NonDEPercent + DEBaseData.INAdmits * mm.DEPercent)) AS INAdmitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.OONAllowed
                       ,(NonDEBaseData.OONAllowed * mm.NonDEPercent + DEBaseData.OONAllowed * mm.DEPercent)) AS OONAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.OONNetworkAllowed
                       ,(NonDEBaseData.OONNetworkAllowed * mm.NonDEPercent + DEBaseData.OONNetworkAllowed * mm.DEPercent)) AS OONNetworkAllowedCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.OONUnits
                       ,(NonDEBaseData.OONUnits * mm.NonDEPercent + DEBaseData.OONUnits * mm.DEPercent)) AS OONUnitsCF
                       ,dbo.fnGetSafeDivisionResultReturnOne (
                        DEBaseData.OONAdmits
                       ,(NonDEBaseData.OONAdmits * mm.NonDEPercent + DEBaseData.OONAdmits * mm.DEPercent)) AS OONAdmitsCF
            FROM        #BaseData NonDEBaseData
            LEFT JOIN   #BaseData DEBaseData
                   ON DEBaseData.MARatingOptionID = NonDEBaseData.MARatingOptionID
                      AND   DEBaseData.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
           INNER JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                   ON libc.BenefitCategoryID = NonDEBaseData.BenefitCategoryID
            LEFT JOIN   #Membership mm
                   ON 1 = 1
            WHERE       libc.IsEnabled = 1
                        AND NonDEBaseData.DualEligibleTypeID = 0 --NonDE#
                        AND DEBaseData.DualEligibleTypeID = 1;  --DE#


            -------------------------------------------------------
            ----- STEP 4: CALCULATE DE# / NONDE# PROJECTION -------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#TrendedDEandNonDE') IS NOT NULL
                DROP TABLE #TrendedDEandNonDE;
            CREATE TABLE #TrendedDEandNonDE
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #TrendedDEandNonDE
            SELECT      bd.MARatingOptionID
                       ,bd.BenefitCategoryID
                       ,bd.DualEligibleTypeID
                       ,tt.INDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INAllowed ELSE tt.INAllowed * cf.INAllowedCF END AS INAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INNetworkAllowed
                             ELSE tt.INNetworkAllowed * cf.INNetworkAllowedCF END AS INNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INUnits ELSE tt.INUnits * cf.INUnitsCF END AS INUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INAdmits ELSE tt.INAdmits * cf.INAdmitsCF END AS INAdmits
                       ,tt.OONDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONAllowed ELSE tt.OONAllowed * cf.OONAllowedCF END AS OONAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONNetworkAllowed
                             ELSE tt.OONNetworkAllowed * cf.OONNetworkAllowedCF END AS OONNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONUnits ELSE tt.OONUnits * cf.OONUnitsCF END AS OONUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONAdmits ELSE tt.OONAdmits * cf.OONAdmitsCF END AS OONAdmits
            FROM        #TrendedTotal tt
            LEFT JOIN   #BaseData bd
                   ON bd.MARatingOptionID = tt.MARatingOptionID
                      AND   bd.BenefitCategoryID = tt.BenefitCategoryID
            LEFT JOIN   #CalibrationFactors cf
                   ON cf.MARatingOptionID = bd.MARatingOptionID
                      AND   cf.BenefitCategoryID = bd.BenefitCategoryID
                      AND   cf.DualEligibleTypeID = bd.DualEligibleTypeID
            WHERE       bd.DualEligibleTypeID IN (0, 1);    --NonDE# and DE#


            -------------------------------------------------------
            ----- STEP 5: MARatingOptionID Check ------------------
            -------------------------------------------------------
            -- Check that MARatingOptionIDs 1 and 2 have records. If not, create NULL records for blending. 

            -- MARatingOptionID 1 = Experience
            IF NOT EXISTS (SELECT   1 FROM  #TrendedTotal WHERE MARatingOptionID = 1)
                BEGIN
                    INSERT INTO #TrendedTotal
                    SELECT  1 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedTotal
                    WHERE   MARatingOptionID = 2;
                END;

            IF NOT EXISTS (SELECT   1 FROM  #TrendedDEandNonDE WHERE MARatingOptionID = 1)
                BEGIN
                    INSERT INTO #TrendedDEandNonDE
                    SELECT  1 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedDEandNonDE
                    WHERE   MARatingOptionID = 2;
                END;


            -- MARatingOptionID 2 = Experience
            IF NOT EXISTS (SELECT   1 FROM  #TrendedTotal WHERE MARatingOptionID = 2)
                BEGIN
                    INSERT INTO #TrendedTotal
                    SELECT  2 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedTotal
                    WHERE   MARatingOptionID = 1;
                END;

            IF NOT EXISTS (SELECT   1 FROM  #TrendedDEandNonDE WHERE MARatingOptionID = 2)
                BEGIN
                    INSERT INTO #TrendedDEandNonDE
                    SELECT  2 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedDEandNonDE
                    WHERE   MARatingOptionID = 1;
                END;


            -------------------------------------------------------
            ----- STEP 6: CREDIBILITY BLENDING --------------------
            -------------------------------------------------------
            --Blend total (DualEligibleTypeID = 2)
            IF OBJECT_ID ('tempdb..#BlendedTotal') IS NOT NULL DROP TABLE #BlendedTotal;
            CREATE TABLE #BlendedTotal
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedTotal
            SELECT      3 AS MARatingOptionID   --Blended
                       ,ttExp.BenefitCategoryID
                       ,ttExp.DualEligibleTypeID
                       ,ttExp.INDeductibleApplies
                       ,ttExp.INAllowed * @Credibility + ttMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,ttExp.INNetworkAllowed * @Credibility + ttMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,ttExp.INUnits * @Credibility + ttMan.INUnits * (1 - @Credibility) AS INUnits
                       ,ttExp.INAdmits * @Credibility + ttMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,ttExp.OONDeductibleApplies
                       ,ttExp.OONAllowed * @Credibility + ttMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,ttExp.OONNetworkAllowed * @Credibility + ttMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,ttExp.OONUnits * @Credibility + ttMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,ttExp.OONAdmits * @Credibility + ttMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedTotal ttExp
            LEFT JOIN   #TrendedTotal ttMan
                   ON ttMan.BenefitCategoryID = ttExp.BenefitCategoryID
                      AND   ttMan.DualEligibleTypeID = ttExp.DualEligibleTypeID
            WHERE       ttExp.MARatingOptionID = 1 --Experience
                        AND ttMan.MARatingOptionID = 2; --Manual

            --Blend NonDE# and DE# (DualEligibleTypeID = 0,1 respectively)
            IF OBJECT_ID ('tempdb..#BlendedDEandNonDE') IS NOT NULL
                DROP TABLE #BlendedDEandNonDE;
            CREATE TABLE #BlendedDEandNonDE
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedDEandNonDE
            SELECT      3 AS MARatingOptionID   --Blended
                       ,tExp.BenefitCategoryID
                       ,tExp.DualEligibleTypeID
                       ,tExp.INDeductibleApplies
                       ,tExp.INAllowed * @Credibility + tMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,tExp.INNetworkAllowed * @Credibility + tMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,tExp.INUnits * @Credibility + tMan.INUnits * (1 - @Credibility) AS INUnits
                       ,tExp.INAdmits * @Credibility + tMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,tExp.OONDeductibleApplies
                       ,tExp.OONAllowed * @Credibility + tMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,tExp.OONNetworkAllowed * @Credibility + tMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,tExp.OONUnits * @Credibility + tMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,tExp.OONAdmits * @Credibility + tMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedDEandNonDE tExp
            LEFT JOIN   #TrendedDEandNonDE tMan
                   ON tMan.BenefitCategoryID = tExp.BenefitCategoryID
                      AND   tMan.DualEligibleTypeID = tExp.DualEligibleTypeID
            WHERE       tExp.MARatingOptionID = 1 --Experience
                        AND tMan.MARatingOptionID = 2;  --Manual


            -------------------------------------------------------
            ----- STEP 7: APPLY SAFE HARBOR -----------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#BlendedFinal') IS NOT NULL DROP TABLE #BlendedFinal;
            CREATE TABLE #BlendedFinal
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedFinal

            --Total
            SELECT  bt.MARatingOptionID
                   ,bt.BenefitCategoryID
                   ,bt.DualEligibleTypeID
                   ,bt.INDeductibleApplies
                   ,bt.INAllowed
                   ,bt.INNetworkAllowed
                   ,bt.INUnits
                   ,bt.INAdmits
                   ,bt.OONDeductibleApplies
                   ,bt.OONAllowed
                   ,bt.OONNetworkAllowed
                   ,bt.OONUnits
                   ,bt.OONAdmits
            FROM    #BlendedTotal bt

            UNION ALL

            --NonDE# and DE#
            SELECT      bde.MARatingOptionID
                       ,bde.BenefitCategoryID
                       ,bde.DualEligibleTypeID
                       ,bde.INDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAllowed ELSE bde.INAllowed END AS INAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INNetworkAllowed ELSE bde.INNetworkAllowed END AS INNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INUnits ELSE bde.INUnits END AS INUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAdmits ELSE bde.INAdmits END AS INAdmits
                       ,bde.OONDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAllowed ELSE bde.OONAllowed END AS OONAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONNetworkAllowed ELSE bde.OONNetworkAllowed END AS OONNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONUnits ELSE bde.OONUnits END AS OONUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAdmits ELSE bde.OONAdmits END AS OONAdmits
            FROM        #BlendedDEandNonDE bde
            LEFT JOIN   #BlendedTotal bt
                   ON bt.BenefitCategoryID = bde.BenefitCategoryID;


            -------------------------------------------------------
            ----- STEP 8: FINAL DATA SETUP ------------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#Final') IS NOT NULL DROP TABLE #Final;
            CREATE TABLE #Final
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #Final

            --Total Experience and Manual
            SELECT  tt.MARatingOptionID
                   ,tt.BenefitCategoryID
                   ,tt.DualEligibleTypeID
                   ,tt.INDeductibleApplies
                   ,tt.INAllowed
                   ,tt.INNetworkAllowed
                   ,tt.INUnits
                   ,tt.INAdmits
                   ,tt.OONDeductibleApplies
                   ,tt.OONAllowed
                   ,tt.OONNetworkAllowed
                   ,tt.OONUnits
                   ,tt.OONAdmits
            FROM    #TrendedTotal tt

            UNION ALL

            --NonDE# and DE# Experience and Manual
            SELECT  tde.MARatingOptionID
                   ,tde.BenefitCategoryID
                   ,tde.DualEligibleTypeID
                   ,tde.INDeductibleApplies
                   ,tde.INAllowed
                   ,tde.INNetworkAllowed
                   ,tde.INUnits
                   ,tde.INAdmits
                   ,tde.OONDeductibleApplies
                   ,tde.OONAllowed
                   ,tde.OONNetworkAllowed
                   ,tde.OONUnits
                   ,tde.OONAdmits
            FROM    #TrendedDEandNonDE tde

            UNION ALL

            --Total, NonDE#, and DE# Blended
            SELECT  bf.MARatingOptionID
                   ,bf.BenefitCategoryID
                   ,bf.DualEligibleTypeID
                   ,bf.INDeductibleApplies
                   ,bf.INAllowed
                   ,bf.INNetworkAllowed
                   ,bf.INUnits
                   ,bf.INAdmits
                   ,bf.OONDeductibleApplies
                   ,bf.OONAllowed
                   ,bf.OONNetworkAllowed
                   ,bf.OONUnits
                   ,bf.OONAdmits
            FROM    #BlendedFinal bf;


            -------------------------------------------------------
            ----- STEP 9: DELETE FROM / WRITE TO TABLE ------------
            -------------------------------------------------------
            --CalcBenefitProjectionSCTCurrent
            DELETE  FROM dbo.CalcBenefitProjectionSCTCurrent
            WHERE   ForecastID = @XForecastID;

            INSERT INTO dbo.CalcBenefitProjectionSCTCurrent
                (PlanYearID
                ,ForecastID
                ,BenefitCategoryID
                ,DualEligibleTypeID
                ,MARatingOptionID
                ,INUnits
                ,INAdmits
                ,INAllowed
                ,INDedApplies
                ,INNetworkAllowed
                ,OONUnits
                ,OONAdmits
                ,OONAllowed
                ,OONDedApplies
                ,OONNetworkAllowed
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  @PlanYearID AS PlanYearID
                   ,@XForecastID AS ForecastID
                   ,f.BenefitCategoryID
                   ,f.DualEligibleTypeID
                   ,f.MARatingOptionID
                   ,f.INUnits
                   ,f.INAdmits
                   ,f.INAllowed
                   ,f.INDeductibleApplies
                   ,f.INNetworkAllowed
                   ,f.OONUnits
                   ,f.OONAdmits
                   ,f.OONAllowed
                   ,f.OONDeductibleApplies
                   ,f.OONNetworkAllowed
                   ,@XUserID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM    #Final f;



            -------------------------------------------------------
            ----- CalcBenefitProjectionSCTBid ---------------------
            -------------------------------------------------------

            -------------------------------------------------------
            ----- STEP 10: APPLY TRENDS TO TOTAL BASE DATA --------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#TrendedTotalBid') IS NOT NULL
                DROP TABLE #TrendedTotalBid;
            CREATE TABLE #TrendedTotalBid
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #TrendedTotalBid
            SELECT      bd.MARatingOptionID
                       ,bd.BenefitCategoryID
                       ,bd.DualEligibleTypeID
                       ,da.INDedApplies
                       ,CAST(bd.INAllowed * pf.E2CINClaimFactorAllowed * pf.C2BINClaimFactorAllowed
                             * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS FLOAT) * @MER AS INAllowed
                       ,CAST(bd.INNetworkAllowed * pf.E2CINClaimFactorAllowed * pf.C2BINClaimFactorAllowed
                             * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS FLOAT) * @MER AS INNetworkAllowed
                       ,bd.INUnits * pf.E2CINClaimFactorUnits * pf.C2BINClaimFactorUnits AS INUnits
                       ,bd.INAdmits * pf.E2CINClaimFactorUnits * pf.C2BINClaimFactorUnits AS INAdmits
                       ,da.OONDedApplies
                       ,CAST(bd.OONAllowed * pf.E2COONClaimFactorAllowed * pf.C2BOONClaimFactorAllowed
                             * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS FLOAT) * @MER AS OONAllowed
                       ,CAST(bd.OONNetworkAllowed * pf.E2COONClaimFactorAllowed * pf.C2BOONClaimFactorAllowed
                             * ISNULL (sqs.ProjectedSQSNoDampening, 1) AS FLOAT) * @MER AS OONNetworkAllowed
                       ,bd.OONUnits * pf.E2COONClaimFactorUnits * pf.C2BOONClaimFactorUnits AS OONUnits
                       ,bd.OONAdmits * pf.E2COONClaimFactorUnits * pf.C2BOONClaimFactorUnits AS OONAdmits
            FROM        #BaseData bd
           INNER JOIN   dbo.LkpIntBenefitCategory libc WITH (NOLOCK)
                   ON libc.BenefitCategoryID = bd.BenefitCategoryID
           INNER JOIN   #DedApp da
                   ON da.BenefitCategoryID = bd.BenefitCategoryID
            LEFT JOIN   #ProjFactors pf
                   ON pf.BenefitCategoryID = bd.BenefitCategoryID
                      AND   pf.MARatingOptionID = bd.MARatingOptionID
            LEFT JOIN   #SQSFactors sqs
                   ON sqs.BenefitCategoryID = bd.BenefitCategoryID
                      AND   sqs.MARatingOptionID = bd.MARatingOptionID
            WHERE       libc.IsEnabled = 1
                        AND bd.MARatingOptionID IN (1, 2) --Experience and manual; we will credibility blend later
                        AND bd.DualEligibleTypeID = 2;  --Total


            -------------------------------------------------------
            ----- STEP 11: CALCULATE DE# / NONDE# PROJECTION ------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#TrendedDEandNonDEBid') IS NOT NULL
                DROP TABLE #TrendedDEandNonDEBid;
            CREATE TABLE #TrendedDEandNonDEBid
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #TrendedDEandNonDEBid
            SELECT      bd.MARatingOptionID
                       ,bd.BenefitCategoryID
                       ,bd.DualEligibleTypeID
                       ,tt.INDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INAllowed ELSE tt.INAllowed * cf.INAllowedCF END AS INAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INNetworkAllowed
                             ELSE tt.INNetworkAllowed * cf.INNetworkAllowedCF END AS INNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INUnits ELSE tt.INUnits * cf.INUnitsCF END AS INUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.INAdmits ELSE tt.INAdmits * cf.INAdmitsCF END AS INAdmits
                       ,tt.OONDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONAllowed ELSE tt.OONAllowed * cf.OONAllowedCF END AS OONAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONNetworkAllowed
                             ELSE tt.OONNetworkAllowed * cf.OONNetworkAllowedCF END AS OONNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONUnits ELSE tt.OONUnits * cf.OONUnitsCF END AS OONUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN tt.OONAdmits ELSE tt.OONAdmits * cf.OONAdmitsCF END AS OONAdmits
            FROM        #TrendedTotalBid tt
            LEFT JOIN   #BaseData bd
                   ON bd.MARatingOptionID = tt.MARatingOptionID
                      AND   bd.BenefitCategoryID = tt.BenefitCategoryID
            LEFT JOIN   #CalibrationFactors cf
                   ON cf.MARatingOptionID = bd.MARatingOptionID
                      AND   cf.BenefitCategoryID = bd.BenefitCategoryID
                      AND   cf.DualEligibleTypeID = bd.DualEligibleTypeID
            WHERE       bd.DualEligibleTypeID IN (0, 1);    --NonDE# and DE#


            -------------------------------------------------------
            ----- STEP 12: MARatingOptionID Check -----------------
            -------------------------------------------------------
            -- Check that MARatingOptionIDs 1 and 2 have records. If not, create NULL records for blending. 

            -- MARatingOptionID 1 = Experience
            IF NOT EXISTS (SELECT   1 FROM  #TrendedTotalBid WHERE  MARatingOptionID = 1)
                BEGIN
                    INSERT INTO #TrendedTotalBid
                    SELECT  1 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedTotalBid
                    WHERE   MARatingOptionID = 2;
                END;

            IF NOT EXISTS (SELECT   1 FROM  #TrendedDEandNonDEBid WHERE MARatingOptionID = 1)
                BEGIN
                    INSERT INTO #TrendedDEandNonDEBid
                    SELECT  1 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedDEandNonDEBid
                    WHERE   MARatingOptionID = 2;
                END;


            -- MARatingOptionID 2 = Experience
            IF NOT EXISTS (SELECT   1 FROM  #TrendedTotalBid WHERE  MARatingOptionID = 2)
                BEGIN
                    INSERT INTO #TrendedTotalBid
                    SELECT  2 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedTotalBid
                    WHERE   MARatingOptionID = 1;
                END;

            IF NOT EXISTS (SELECT   1 FROM  #TrendedDEandNonDEBid WHERE MARatingOptionID = 2)
                BEGIN
                    INSERT INTO #TrendedDEandNonDEBid
                    SELECT  2 AS MARatingOptionID
                           ,BenefitCategoryID
                           ,DualEligibleTypeID
                           ,INDeductibleApplies
                           ,0 AS INAllowed
                           ,0 AS INNetworkAllowed
                           ,0 AS INUnits
                           ,0 AS INAdmits
                           ,OONDeductibleApplies
                           ,0 AS OONAllowed
                           ,0 AS OONNetworkAllowed
                           ,0 AS OONUnits
                           ,0 AS OONAdmits
                    FROM    #TrendedDEandNonDEBid
                    WHERE   MARatingOptionID = 1;
                END;


            -------------------------------------------------------
            ----- STEP 13: CREDIBILITY BLENDING -------------------
            -------------------------------------------------------
            --Blend total (DualEligibleTypeID = 2)
            IF OBJECT_ID ('tempdb..#BlendedTotalBid') IS NOT NULL
                DROP TABLE #BlendedTotalBid;
            CREATE TABLE #BlendedTotalBid
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedTotalBid
            SELECT      3 AS MARatingOptionID   --Blended
                       ,ttExp.BenefitCategoryID
                       ,ttExp.DualEligibleTypeID
                       ,ttExp.INDeductibleApplies
                       ,ttExp.INAllowed * @Credibility + ttMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,ttExp.INNetworkAllowed * @Credibility + ttMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,ttExp.INUnits * @Credibility + ttMan.INUnits * (1 - @Credibility) AS INUnits
                       ,ttExp.INAdmits * @Credibility + ttMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,ttExp.OONDeductibleApplies
                       ,ttExp.OONAllowed * @Credibility + ttMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,ttExp.OONNetworkAllowed * @Credibility + ttMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,ttExp.OONUnits * @Credibility + ttMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,ttExp.OONAdmits * @Credibility + ttMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedTotalBid ttExp
            LEFT JOIN   #TrendedTotalBid ttMan
                   ON ttMan.BenefitCategoryID = ttExp.BenefitCategoryID
                      AND   ttMan.DualEligibleTypeID = ttExp.DualEligibleTypeID
            WHERE       ttExp.MARatingOptionID = 1 --Experience
                        AND ttMan.MARatingOptionID = 2; --Manual

            --Blend NonDE# and DE# (DualEligibleTypeID = 0,1 respectively)
            IF OBJECT_ID ('tempdb..#BlendedDEandNonDEBid') IS NOT NULL
                DROP TABLE #BlendedDEandNonDEBid;
            CREATE TABLE #BlendedDEandNonDEBid
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedDEandNonDEBid
            SELECT      3 AS MARatingOptionID   --Blended
                       ,tExp.BenefitCategoryID
                       ,tExp.DualEligibleTypeID
                       ,tExp.INDeductibleApplies
                       ,tExp.INAllowed * @Credibility + tMan.INAllowed * (1 - @Credibility) AS INAllowed
                       ,tExp.INNetworkAllowed * @Credibility + tMan.INNetworkAllowed * (1 - @Credibility) AS INNetworkAllowed
                       ,tExp.INUnits * @Credibility + tMan.INUnits * (1 - @Credibility) AS INUnits
                       ,tExp.INAdmits * @Credibility + tMan.INAdmits * (1 - @Credibility) AS INAdmits
                       ,tExp.OONDeductibleApplies
                       ,tExp.OONAllowed * @Credibility + tMan.OONAllowed * (1 - @Credibility) AS OONAllowed
                       ,tExp.OONNetworkAllowed * @Credibility + tMan.OONNetworkAllowed * (1 - @Credibility) AS OONNetworkAllowed
                       ,tExp.OONUnits * @Credibility + tMan.OONUnits * (1 - @Credibility) AS OONUnits
                       ,tExp.OONAdmits * @Credibility + tMan.OONAdmits * (1 - @Credibility) AS OONAdmits
            FROM        #TrendedDEandNonDEBid tExp
            LEFT JOIN   #TrendedDEandNonDEBid tMan
                   ON tMan.BenefitCategoryID = tExp.BenefitCategoryID
                      AND   tMan.DualEligibleTypeID = tExp.DualEligibleTypeID
            WHERE       tExp.MARatingOptionID = 1 --Experience
                        AND tMan.MARatingOptionID = 2;  --Manual


            -------------------------------------------------------
            ----- STEP 14: APPLY SAFE HARBOR ----------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#BlendedFinalBid') IS NOT NULL
                DROP TABLE #BlendedFinalBid;
            CREATE TABLE #BlendedFinalBid
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #BlendedFinalBid

            --Total
            SELECT  bt.MARatingOptionID
                   ,bt.BenefitCategoryID
                   ,bt.DualEligibleTypeID
                   ,bt.INDeductibleApplies
                   ,bt.INAllowed
                   ,bt.INNetworkAllowed
                   ,bt.INUnits
                   ,bt.INAdmits
                   ,bt.OONDeductibleApplies
                   ,bt.OONAllowed
                   ,bt.OONNetworkAllowed
                   ,bt.OONUnits
                   ,bt.OONAdmits
            FROM    #BlendedTotalBid bt

            UNION ALL

            --NonDE# and DE#
            SELECT      bde.MARatingOptionID
                       ,bde.BenefitCategoryID
                       ,bde.DualEligibleTypeID
                       ,bde.INDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAllowed ELSE bde.INAllowed END AS INAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INNetworkAllowed ELSE bde.INNetworkAllowed END AS INNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INUnits ELSE bde.INUnits END AS INUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.INAdmits ELSE bde.INAdmits END AS INAdmits
                       ,bde.OONDeductibleApplies
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAllowed ELSE bde.OONAllowed END AS OONAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONNetworkAllowed ELSE bde.OONNetworkAllowed END AS OONNetworkAllowed
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONUnits ELSE bde.OONUnits END AS OONUnits
                       ,CASE WHEN @IsSafeHarbor = 1 THEN bt.OONAdmits ELSE bde.OONAdmits END AS OONAdmits
            FROM        #BlendedDEandNonDEBid bde
            LEFT JOIN   #BlendedTotalBid bt
                   ON bt.BenefitCategoryID = bde.BenefitCategoryID;


            -------------------------------------------------------
            ----- STEP 15: FINAL DATA SETUP -----------------------
            -------------------------------------------------------
            IF OBJECT_ID ('tempdb..#FinalBid') IS NOT NULL DROP TABLE #FinalBid;
            CREATE TABLE #FinalBid
                (MARatingOptionID     TINYINT
                ,BenefitCategoryID    INT
                ,DualEligibleTypeID   TINYINT
                ,INDeductibleApplies  BIT
                ,INAllowed            FLOAT
                ,INNetworkAllowed     FLOAT
                ,INUnits              FLOAT
                ,INAdmits             FLOAT
                ,OONDeductibleApplies BIT
                ,OONAllowed           FLOAT
                ,OONNetworkAllowed    FLOAT
                ,OONUnits             FLOAT
                ,OONAdmits            FLOAT);
            INSERT INTO #FinalBid

            --Total Experience and Manual
            SELECT  tt.MARatingOptionID
                   ,tt.BenefitCategoryID
                   ,tt.DualEligibleTypeID
                   ,tt.INDeductibleApplies
                   ,tt.INAllowed
                   ,tt.INNetworkAllowed
                   ,tt.INUnits
                   ,tt.INAdmits
                   ,tt.OONDeductibleApplies
                   ,tt.OONAllowed
                   ,tt.OONNetworkAllowed
                   ,tt.OONUnits
                   ,tt.OONAdmits
            FROM    #TrendedTotalBid tt

            UNION ALL

            --NonDE# and DE# Experience and Manual
            SELECT  tde.MARatingOptionID
                   ,tde.BenefitCategoryID
                   ,tde.DualEligibleTypeID
                   ,tde.INDeductibleApplies
                   ,tde.INAllowed
                   ,tde.INNetworkAllowed
                   ,tde.INUnits
                   ,tde.INAdmits
                   ,tde.OONDeductibleApplies
                   ,tde.OONAllowed
                   ,tde.OONNetworkAllowed
                   ,tde.OONUnits
                   ,tde.OONAdmits
            FROM    #TrendedDEandNonDEBid tde

            UNION ALL

            --Total, NonDE#, and DE# Blended
            SELECT  bf.MARatingOptionID
                   ,bf.BenefitCategoryID
                   ,bf.DualEligibleTypeID
                   ,bf.INDeductibleApplies
                   ,bf.INAllowed
                   ,bf.INNetworkAllowed
                   ,bf.INUnits
                   ,bf.INAdmits
                   ,bf.OONDeductibleApplies
                   ,bf.OONAllowed
                   ,bf.OONNetworkAllowed
                   ,bf.OONUnits
                   ,bf.OONAdmits
            FROM    #BlendedFinalBid bf;


            -------------------------------------------------------
            ----- STEP 16: DELETE FROM / WRITE TO TABLE -----------
            -------------------------------------------------------
            --CalcBenefitProjectionSCTBid
            DELETE  FROM dbo.CalcBenefitProjectionSCTBid
            WHERE   ForecastID = @XForecastID;

            INSERT INTO dbo.CalcBenefitProjectionSCTBid
                (PlanYearID
                ,ForecastID
                ,BenefitCategoryID
                ,DualEligibleTypeID
                ,MARatingOptionID
                ,INUnits
                ,INAdmits
                ,INAllowed
                ,INDedApplies
                ,INNetworkAllowed
                ,OONUnits
                ,OONAdmits
                ,OONAllowed
                ,OONDedApplies
                ,OONNetworkAllowed
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  @PlanYearID AS PlanYearID
                   ,@XForecastID AS ForecastID
                   ,f.BenefitCategoryID
                   ,f.DualEligibleTypeID
                   ,f.MARatingOptionID
                   ,f.INUnits
                   ,f.INAdmits
                   ,f.INAllowed
                   ,f.INDeductibleApplies
                   ,f.INNetworkAllowed
                   ,f.OONUnits
                   ,f.OONAdmits
                   ,f.OONAllowed
                   ,f.OONDeductibleApplies
                   ,f.OONNetworkAllowed
                   ,@XUserID AS LastUpdateByID
                   ,@LastUpdateDateTime AS LastUpdateDateTime
            FROM    #FinalBid f;



            COMMIT TRANSACTION trans_CalcBenProjSCT;

        END TRY

        BEGIN CATCH

            IF (@@TranCount > @tranCount) --Check if transaction in TRY block was not closed
                BEGIN
                    ROLLBACK TRANSACTION trans_CalcBenProjSCT;
                END;
        END CATCH;

    END;
GO
