SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spUpdateBARCSync
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: 2020-04-28
--
-- DESCRIPTION: After users BARC sync their bid year membership to MAAUI, this script is triggered to refresh downstream tables affected by the update. It also writes to the BARC sync log
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--              @PlanInfoID
--              @RegionString
--              @ProductString
--
--  Output : NONE
--
-- TABLES : Read : 		    
--					
--          Write: Trend_SavedBarcSyncLog
--                 Trend_SavedRelativityPopulation
--                 Trend_ProjProcess_CalcIsPlanLevel_Population 
--                  
--
-- VIEWS: Read: 
--
-- STORED PROCS: Executed: Trend_ProjProcess_spUpdateBarcSyncLog
--                         Trend_spCalcPopulationBidYear
--                         Trend_ProjProcess_spCalcIsPlanLevel_Trends
--						   Trend_spCalcUCDBBidYear
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2020-04-28			1		Initial Version														Andy Blink
-- 2020-07-14			2		Added ErrorMsg parameter											Ramandeep Saini
-- 2020-12-11			3		Removed Trend_Reporting_spCalcProjectedMembership					Jake Lewis
-- 2020-12-18			4		Added Trend_UCDB SP													Surya Murthy
-- 2021-02-25			5		Replaced sp - Trend_UCDB with Trend_spCalcUCDBBidYear				Priyesh Singh
--								as per user story - 1986699
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_ProjProcess_spUpdateBARCSync]

 @LastUpdateByID [CHAR](7)
,@PlanInfoID     [VARCHAR](MAX) = NULL     
,@RegionString   [VARCHAR](MAX) = NULL
,@ProductString  [VARCHAR](MAX) = NULL
,@ErrorMsg VARCHAR(Max) OUTPUT

AS
    BEGIN

        SET NOCOUNT ON;

        DECLARE @ValidationMessage VARCHAR(MAX)
               ,@ProcNumber        INT
			   ,@CustomErrMsg VARCHAR(MAX);

        SET @ProcNumber = 1;
		SET @ErrorMsg='';
        BEGIN TRY

            IF @ProcNumber = 1
                BEGIN
                    EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                                  ,@LastUpdateByID
                                                                  ,'Start';

					set @CustomErrMsg = 'Calculation of Population bid year failed';
                    EXECUTE Trend_spCalcPopulationBidYear  @PlanInfoID
                                                          ,@RegionString
                                                          ,@ProductString
                                                          ,@LastUpdateByID					
                    SET @ProcNumber = @ProcNumber + 1;
                END;
            ELSE
                EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                              ,@LastUpdateByID
                                                              ,'Skipped';


            IF @ProcNumber = 2
                BEGIN
                    EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                                  ,@LastUpdateByID
                                                                  ,'Start';

					set @CustomErrMsg = 'Calculation of Plan Level Trends for Component 3 failed.';
                    EXECUTE Trend_ProjProcess_spCalcIsPlanLevel_Trends 3 --Population
                                                                      ,@PlanInfoID
                                                                      ,@RegionString
                                                                      ,@ProductString
                                                                      ,@LastUpdateByID                    
                    SET @ProcNumber = @ProcNumber + 1;
                END;
            ELSE 
                EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                              ,@LastUpdateByID
                                                              ,'Skipped';

            IF @ProcNumber = 3
                BEGIN
                    EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                                  ,@LastUpdateByID
                                                                  ,'Start';

					set @CustomErrMsg = 'Calculation of Induced Utilization failed.';
                    EXECUTE dbo.Trend_spCalcInducedUtilization @LastUpdateByID
                                                              ,@PlanInfoID
                                                              ,@RegionString
                                                              ,@ProductString
               					
                    SET @ProcNumber = @ProcNumber + 1;
                END;
            ELSE 
                EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                              ,@LastUpdateByID
                                                              ,'Skipped';


            IF @ProcNumber = 4
                BEGIN
                    EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                                  ,@LastUpdateByID
                                                                  ,'Start';

					set @CustomErrMsg = 'Calculation of Plan Level Trends for Component 4 failed.';
                    EXECUTE Trend_ProjProcess_spCalcIsPlanLevel_Trends 4 --Induced Utilization
                                                                      ,@PlanInfoID
                                                                      ,@RegionString
                                                                      ,@ProductString
                                                                      ,@LastUpdateByID					
                    SET @ProcNumber = @ProcNumber + 1;
                END;
            ELSE 
                EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                              ,@LastUpdateByID
                                                              ,'Skipped';

		IF @ProcNumber = 5
                BEGIN
                    EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                                  ,@LastUpdateByID
                                                                  ,'Start';

					set @CustomErrMsg = 'Trend_spCalcUCDBBidYear Failed.';
                    EXECUTE dbo.Trend_spCalcUCDBBidYear @PlanInfoID
														,@RegionString                                                              
														,@ProductString
														,@LastUpdateByID					
                    SET @ProcNumber = @ProcNumber + 1;
                END;
            ELSE 
                EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                              ,@LastUpdateByID
                                                              ,'Skipped';


           IF @ProcNumber = 6
                BEGIN
                    EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                                  ,@LastUpdateByID
                                                                  ,'Start';

					set @CustomErrMsg = 'Calculation of Plan Level Trends for Component 9 failed.';
                    EXECUTE Trend_ProjProcess_spCalcIsPlanLevel_Trends 9 --CMS Reimbursement
                                                                      ,@PlanInfoID
                                                                      ,@RegionString
                                                                      ,@ProductString
                                                                      ,@LastUpdateByID                    
                    SET @ProcNumber = @ProcNumber + 1;
                END;
            ELSE 
                EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                              ,@LastUpdateByID
                                                              ,'Skipped';


            SET @ValidationMessage = '0';
			SET @ErrorMsg='N/A';
        END TRY

        BEGIN CATCH
            SELECT  @ValidationMessage = ERROR_MESSAGE ();
			SET @ErrorMsg='0:Procedure Number-'+CAST(@ProcNumber as varchar(5))+':'+'Failed with Error: '+@CustomErrMsg;
            EXECUTE Trend_ProjProcess_spUpdateBarcSyncLog  @ProcNumber
                                                          ,@LastUpdateByID
                                                          ,@ValidationMessage;
														
       
        END CATCH;
    END;
GO
