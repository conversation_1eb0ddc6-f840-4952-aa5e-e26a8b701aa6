SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- ----------------------------------------------------------------------------------------------------------------------  
-- PROCEDURE NAME: spSignificance  
--  
-- AUTHOR: <PERSON>  
--  
-- CREATED DATE: 2018-Ju1-31  
-- HEADER UPDATED: 2018-Ju1-31  
--  
-- DESCRIPTION: Pull base membership data from C&U tables and calculate significance based on crosswalk data  
--  
-- PARAMETERS:  
-- Input:  
--  @LastUpdateByID    
--  @PlanInfoID Optional: Will run for all plans if defaulted to NULL  
--      @ServiceAreaOptionID: Will run for all options if defaulted to NULL  
--  @DeleteMode: Optional - Default to 0:   
--    SET to 1 if you want to delete PlanInfoID/ServiceAreaOptionID from CalcSignificance table  
--    Will be ignored when PlanInfoID and ServiceAreaOptionID are/is NULL  
--    
-- Output:   
--  NumOfRows - Number of affected rows to be stored in CalcSignificance. This may not be useful and could be removed  
--  
-- RETURNS:   
--  
-- TABLES:  
-- Read:   
--  MAAModels -> LkpModelSettings   
--  MAAModels -> SavedPlanInfo  
   
--  
-- Write:  
--  MAAModels -> CalcSignificance  
--  
-- VIEWS:   
-- Read:   
--  MAAModels -> vwSAMCrosswalks  
--  MAAModels -> vwBaseYearMM  
--  
-- FUNCTIONS:  
--      NONE  
--  
-- STORED PROCS:   
--  NONE  
--  
-- HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE          VERSION            CHANGES MADE                                             DEVELOPER  
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2018-07-13       1               Initial Version                                          Daniel Nyatuame   
-- 2018-12-26       2               Replaced @UserID with @LastUpdateByID                    Kritika Singh  
-- 2019-02-06       3               Clean up documentation for header section                Daniel Nyatuame     
-- 2020-09-29       4               Backend Alignment and Restructuring Chnge vwBaseYearMM   Keith Galloway  
-- 2021-06-23		5				changes made while fetching data from vwBaseYearMM			Deepali
-- 2021-12-06		6				Update datatype for @NumOfRows from SMALLINT to INT		 Franklin Fu
-- 2023-11-03		7				Removed logic creating duplicate work in loops.			 Surya Murthy
-- 2023-11-27       8               Updates to accomodate for Special Crosswalks             Zoey Glenn
-- 2023-12-15       9               Updates to save service Area getting time out error      Zoey Glenn
-- 2023-01-26       10              The significance calculation for special crosswalks
--								    must true up to the BIDE county level membership. 
--									Added 2 additional temp tables to accomplish this calculation.      Zoey Glenn
--2023-02-16		11				Market Support requested that CY crosswalks also be 
--									included in the methodology									Zoey Glenn
--2024-04-15		12				Fixing Nulls returned for terminated/new plans				Zoey/Kiran
-- ----------------------------------------------------------------------------------------------------------------------  
CREATE PROCEDURE [dbo].[spSignificance]  
    @LastUpdateByID CHAR(7) ,  
    @PlanInfoID SMALLINT = NULL,  
    @SAOptionID TINYINT = NULL,  
    @DeleteMode BIT = 0  
AS  
    BEGIN  
        SET NOCOUNT ON;  
  DECLARE @MapId SMALLINT; --Input MapId    
  SET @MapId = (SELECT   TOP 1 [BidYearMapID]  
     FROM     dbo.vwSAMCrosswalks WITH (NOLOCK) WHERE BidYearPlanInfoID = @PlanInfoID AND ServiceAreaOptionID = @SAOptionID)  
  IF @DeleteMode = 1   
            BEGIN  
    DELETE cs FROM dbo.CalcSignificance cs   
     WHERE EXISTS (SELECT 1 FROM dbo.vwSAMCrosswalks xw WITH (NOLOCK) WHERE cs.PlanInfoID = xw.BidYearPlanInfoID AND cs.ServiceAreaOptionID = xw.ServiceAreaOptionID  
      AND xw.BidYearMapID = @MapId)  
            END;  
        ELSE  
   BEGIN  
    DECLARE @PlanYear INT; --Year for which you want to calculate significance  
    DECLARE @TotalMM DECIMAL(19, 6); --TotalMM is Total biddable base period MM including OOA  
    DECLARE @MovingMM DECIMAL(19, 6); --MovingMM is biddable base period members moved via crosswalk into Reporting Bid  
    DECLARE @RemovedMM DECIMAL(19, 6); --Base period members removed via crosswalk in bid year  
    DECLARE @CYXwedMM DECIMAL(19, 6); --Base period members crosswalked into current year  
    DECLARE @PlanInfo SMALLINT; --Reporting Bid is the bid for which BPT is being submitted  
    DECLARE @BasePlanInfo SMALLINT; -- To store base year BaseYearCPS  
    DECLARE @SAOption TINYINT; --Unique service area option for PlanInfoID  
    DECLARE @XBid CHAR(13);--The bids that crosswalked into PlanInfoID/SAOptionID  
    DECLARE @BidYearCPS CHAR(13); -- This is the CPS matching current PlanInfo  
    DECLARE @UpdateDateTime DATETIME;  
    DECLARE @IsBasePlan BIT;  
    DECLARE @Threshold DECIMAL(5, 4);  
    DECLARE @Status VARCHAR(40);    
    DECLARE @MapIdIter SMALLINT;  -- MapId for each iteration  
    DECLARE @NumOfRows INT;  

	DECLARE @Rowno AS INT, @Record_Count INT;
	DECLARE @RB_Rowno AS INT, @RB_Record_Count INT;

	--Added for Special Crosswalks
	 DECLARE @BidYrRenewalTypeID SMALLINT; --Input MapId    
		SET @BidYrRenewalTypeID = (SELECT   TOP 1 [BidYearRenewalTypeID]  
		FROM     dbo.vwSAMCrosswalks WITH (NOLOCK) 
		WHERE BidYearPlanInfoID = @PlanInfoID AND ServiceAreaOptionID = @SAOptionID
		AND BidYearRenewalTypeID IS NOT NULL)
		
		SET @BidYrRenewalTypeID = COALESCE(@BidYrRenewalTypeID, 100) --Value 100 choosen arbitarily should never be 16

	DECLARE @CurrentYrRenewalTypeID SMALLINT; --Input MapId    
		SET @CurrentYrRenewalTypeID = (SELECT   TOP 1 [CurrentYearRenewalTypeID]  
		FROM     dbo.vwSAMCrosswalks WITH (NOLOCK) WHERE BidYearPlanInfoID = @PlanInfoID AND ServiceAreaOptionID = @SAOptionID
		AND [CurrentYearRenewalTypeID] IS NOT NULL
		)

		SET @CurrentYrRenewalTypeID = COALESCE(@CurrentYrRenewalTypeID, 100)
	---------
      
    -- Initialize variables  
    SET @PlanYear = ( SELECT    [SAMYear]  
          FROM      dbo.LkpModelSettings WITH (NOLOCK)  
        );  
    SET @Threshold = ( SELECT   [SignificanceLevel]  
           FROM     dbo.LkpModelSettings  WITH (NOLOCK)
         );  
    SET @UpdateDateTime = ( GETDATE() );  

	--Edited for CY Special Crosswalks
	      IF @BidYrRenewalTypeID <> 16 AND @CurrentYrRenewalTypeID <> 16 BEGIN
  
    /* Save county level crosswalks for the chosen plan(s)*/  
    
	IF OBJECT_ID('tempdb.dbo.#SAM_Crosswalks') IS NOT NULL
	  DROP TABLE #SAM_Crosswalks;

    SELECT DISTINCT  
      x.[ServiceAreaOptionID] ,  
      x.[SSStateCountyCD] ,  
      x.[BaseYearCPS] ,  
      BasePlanInfoID = spi.PlanInfoID ,  
      x.[CurrentYearCPS] ,  
      x.[BidYearCPS] ,  
      x.[BidYearPlanInfoID],  
      x.BidYearMapID  
    INTO    #SAM_Crosswalks  
    FROM    dbo.vwSAMCrosswalks x WITH (NOLOCK) 
      LEFT JOIN ( SELECT  PlanInfoID ,  
           CPS  
         FROM    dbo.SavedPlanInfo WITH (NOLOCK) 
         WHERE   PlanYear = @PlanYear - 2  
          ) spi ON x.BaseYearCPS = spi.CPS  
    WHERE   x.BidYearRenewalTypeID <> 2 --exclude termed plans  
      AND (x.BidYearMapID = @MapId OR @MapId IS NULL)  
      
    -- If a plan is selected but mapid is null then do nothing  
    IF @PlanInfoID IS NULL OR @MapId IS NOT NULL BEGIN  
       
     /* Table to store base plan information while plans are run and outputs it all at end */  

	 IF OBJECT_ID('tempdb.dbo.#PlansInBase') IS NOT NULL
	     DROP TABLE #PlansInBase;

     CREATE TABLE #PlansInBase  
      (  
        [PlanInfoID] SMALLINT NOT NULL ,  
        [ServiceAreaOptionID] TINYINT NOT NULL ,  
        [BasePlanInfoID] SMALLINT ,  
        [Status] VARCHAR(40) ,  
        [RemovedMM] DECIMAL(19, 6) ,  
        [MovingMM] DECIMAL(19, 6) ,  
        [TotalBaseMM] DECIMAL(19, 6) ,  
        [IsBasePlan] BIT ,  
        [BidCPS] CHAR(13),  
        [BaseCPS] CHAR(13),  
        CHECK ( [MovingMM] <= [TotalBaseMM] ) --We want error if crosswalked MM is greater than total MM  
      );  
  
      /* Save plan level crosswalks for the chosen plan(s)*/  
      
		IF OBJECT_ID('tempdb.dbo.#SAM_PlanLevelXw') IS NOT NULL
			DROP TABLE #SAM_PlanLevelXw;

      SELECT DISTINCT
        BidYearPlanInfoID ,  
        BidYearCPS,  
        ServiceAreaOptionID ,  
        BasePlanInfoID ,  
        BaseYearCPS,  
        BidYearMapID  
      INTO #SAM_PlanLevelXw  
      FROM #SAM_Crosswalks   
	  
	  /*Fetching unique CPS(baseyearCPS and BidYearCPS)*/ ---- Changes made by Deepali    
  IF OBJECT_ID('tempdb.dbo.#PlanListCps') IS NOT NULL
			DROP TABLE #PlanListCps;
			CREATE TABLE #PlanListCps (CPS Varchar(13))
			insert   INTO #PlanListCps  
			select DISTINCT CPS from (SELECT DISTINCT        
			BidYearCPS	as CPS	
			FROM #SAM_PlanLevelXw
			union 
			 SELECT DISTINCT        
			BaseYearCPS		as CPS
			FROM #SAM_PlanLevelXw where BaseYearCPS is not null) a


     /* Save base membermonth data for plans in base  
      Save only plans that crosswalked*/   
	   IF OBJECT_ID('tempdb.dbo.#BaseYearMM') IS NOT NULL  
		DROP TABLE #BaseYearMM;

     SELECT DISTINCT  
       DFVersionID,  
       PlanInfoID,  
       vw.CPS,  
       SSStateCountyCD,  
       MM  
     INTO    #BaseYearMM  
     FROM    dbo.vwBaseYearMM vw WITH (NOLOCK) 
	 Inner join #PlanListCps c1			---- Join added by Deepali
	 on c1.CPS = vw.CPS
	  
	  IF OBJECT_ID('tempdb.dbo.#Planloop') IS NOT NULL
		DROP TABLE #Planloop;
	  -- PlanInfoID and ServiceAreaOptionID from SavedServiceAreaOption into Plan_Loop  
	 SELECT DISTINCT 
	    ROW_NUMBER() OVER(ORDER BY BidYearMapID ASC) RowNo,
        [BidYearMapID],  
        [BidYearPlanInfoID] ,  
        [ServiceAreaOptionID] INTO #Planloop  
      FROM    #SAM_PlanLevelXw WHERE (BidYearMapID = @MapId OR @MapId IS NULL) 


	 SET @Rowno =1;
     SET @Record_Count = (SELECT COUNT(1) FROM #Planloop);
	 
	 --SELECT * FROM #Planloop
	 
  IF(@Record_Count>0)
	  BEGIN
		 WHILE (@Rowno<=@Record_Count)  -- Plan_Loop  
			BEGIN   
				
				 SELECT DISTINCT  
					@MapIdIter = [BidYearMapID],  
					@PlanInfo = [BidYearPlanInfoID] ,  
					@SAOption =  [ServiceAreaOptionID]  
					FROM    #Planloop WHERE RowNo=@Rowno

					
					  /*-Check if CPS existed in the Base Period, IF TRUE then put it into @PlansInBase.  
					We don't need to calculate significance in that case. Hence NULL MovingMM and RemovedMM */  
				   SET @BidYearCPS = ( SELECT  CPS  
					FROM    dbo.SavedPlanInfo WITH (NOLOCK) 
					WHERE   PlanInfoID = @PlanInfo  
					);  
				   SET @BasePlanInfo = ( SELECT    PlanInfoID  
					FROM      dbo.SavedPlanInfo WITH (NOLOCK) 
					WHERE     PlanYear = @PlanYear - 2  
					  AND CPS = @BidYearCPS  
					); 
					
					IF @BasePlanInfo IS NOT NULL  
						BEGIN   
							 /* Total Biddable Base Period MM from @XBid */      
							 SET @TotalMM = ( SELECT SUM(MM)  
							  FROM   #BaseYearMM  
							  WHERE  CPS = @BidYearCPS  
							  GROUP BY CPS  
							 );  
							 SET @TotalMM = COALESCE(@TotalMM, 0);    
							 INSERT  INTO #PlansInBase  
							 VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
								 'Reporting Bid', NULL, NULL, @TotalMM, 1, @BidYearCPS, @BidYearCPS );  
						END;  
					   ELSE  
						BEGIN  
							 INSERT  INTO #PlansInBase  
							 VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
								 'Reporting Bid Not In Base', NULL, NULL,NULL, 0, @BidYearCPS, NULL);  
						END;  

						IF OBJECT_ID('tempdb.dbo.#RB_Loop') IS NOT NULL
							DROP TABLE #RB_Loop_bk
						
						SELECT DISTINCT							 
							  [BaseYearCPS]  INTO #RB_Loop_bk
							FROM    [#SAM_Crosswalks]  
							WHERE   BaseYearCPS IS NOT NULL  
							  AND [BaseYearCPS] IN (  
							  SELECT DISTINCT  
								[BaseYearCPS]  
							  FROM    [#SAM_Crosswalks]  
							  WHERE   [BidYearCPS] = @BidYearCPS  
								AND [BaseYearCPS] <> @BidYearCPS );
						
						IF OBJECT_ID('tempdb.dbo.#RB_Loop') IS NOT NULL
							DROP TABLE #RB_Loop
								
						  SELECT  ROW_NUMBER() OVER(ORDER BY [BaseYearCPS] ASC) RB_Rowno, [BaseYearCPS] INTO #RB_Loop FROM #RB_Loop_bk;
								
						 SET @RB_Rowno =1;
					 
						 SET @RB_Record_Count = (SELECT COUNT(1) FROM #RB_Loop);
						  
						 IF(@RB_Record_Count>0)
							  BEGIN
								 WHILE (@RB_Rowno<=@RB_Record_Count)
									BEGIN
											SELECT @XBid =[BaseYearCPS] FROM #RB_Loop WHERE RB_Rowno=@RB_Rowno
											
										   /* Biddable BaseMM from counties that existed in @XBid and @Reporting_Bid */  
											 SET @MovingMM = ( SELECT    SUM(MM)  
											  FROM      #BaseYearMM  
											  WHERE     CPS = @XBid  
												AND [SSStateCountyCD] IN (  
												SELECT DISTINCT  
												  [SSStateCountyCD]  
												FROM    [#SAM_Crosswalks]  
												WHERE   [BidYearCPS] = @BidYearCPS  
												  AND [BaseYearCPS] = @XBid  
												  AND [ServiceAreaOptionID] = @SAOption)  
											  GROUP BY  CPS  
											 );  
											 SET @MovingMM = COALESCE(@MovingMM, 0);  
          
											 /* BaseMM from counties that crosswalked in CY, but are removed from @Reporting_Bid */  
											 --SET @RemovedMM = (SELECT   SUM([MemberMonths])  
											 SET @CYXwedMM = (SELECT   SUM(MM)  
											  FROM     #BaseYearMM  
											  WHERE    CPS = @XBid  
												AND [SSStateCountyCD] IN (  
												SELECT DISTINCT  
												  [SSStateCountyCD]  
												FROM    [#SAM_Crosswalks]  
												WHERE [BaseYearCPS] = @XBid AND [CurrentYearCPS] = @BidYearCPS AND BidYearMapID = @MapIdIter)                   
												--WHERE   [BidYearPlanInfoID] = @PlanInfo  
												--  AND [BaseYearCPS] = @XBid  
												--  AND [ServiceAreaOptionID] = @SAOption  
												--  AND [BidYearCPS] IS NULL)  
											  GROUP BY CPS  
											  );  
											 SET @RemovedMM = COALESCE(@CYXwedMM - @MovingMM, 0)  
											 IF @RemovedMM < 0 BEGIN   
											  SET @RemovedMM = ( SELECT   SUM(MM)  
											   FROM     #BaseYearMM  
											   WHERE    CPS = @XBid  
												 AND [SSStateCountyCD] IN (  
												 SELECT DISTINCT  
												   [SSStateCountyCD]  
												 FROM    [#SAM_Crosswalks]  
												 WHERE   [BidYearPlanInfoID] = @PlanInfo  
												   AND [BaseYearCPS] = @XBid  
												   AND [ServiceAreaOptionID] = @SAOption  
												   AND [BidYearCPS] IS NULL)  
											   GROUP BY CPS  
											   );  
											  SET @RemovedMM = COALESCE(@RemovedMM, 0)  
											 END  
											 /* Total Biddable BaseMM from @XBid */      
											 SET @TotalMM = ( SELECT SUM(MM)  
											  FROM   #BaseYearMM  
											  WHERE  CPS = @XBid  
											  GROUP BY CPS  
											 );  
											 SET @TotalMM = COALESCE(@TotalMM, 0);  
  
											 /*Convert @XBid to PlanInfoID to store into CalcSignificance table */  
											 SET @BasePlanInfo = ( SELECT    PlanInfoID  
											  FROM      dbo.SavedPlanInfo WITH (NOLOCK)  
											  WHERE     PlanYear = @PlanYear - 2  
												AND CPS = @XBid  
											 );  
      
											  /* Check if Significance is above threshold */  
											 IF @MovingMM <= ( @TotalMM * @Threshold )  
											  BEGIN  
												   INSERT  INTO #PlansInBase  
												   VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
													   'Insignificant Crosswalk',  
													   @RemovedMM, @MovingMM, @TotalMM, 0, @BidYearCPS, @XBid );  
											  END;  
											 ELSE  
											  BEGIN  
												   INSERT  INTO #PlansInBase  
												   VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
													   'Significant Crosswalk', @RemovedMM,  
													   @MovingMM, @TotalMM, 1, @BidYearCPS, @XBid );  
											  END;  
         

									SET @RB_Rowno= @RB_Rowno+1;
									END
							  END
			 
				SET @Rowno= @Rowno+1;
 
			END      
	  END


     /* Check if there is data in #PlansInBase and then delete from CalcSignificance table data */  
     SET @NumOfRows = ( SELECT   COUNT(*)  
            FROM     #PlansInBase  
          );  
     IF @NumOfRows > 0  
      BEGIN  
	  
       DELETE cs FROM dbo.CalcSignificance cs  
       WHERE EXISTS (SELECT 1 FROM #PlansInBase pl   
       WHERE cs.PlanInfoID = pl.PlanInfoID AND   
       cs.ServiceAreaOptionID = pl.ServiceAreaOptionID)  
        


       /*Insert into CalcSignficance data from #PlansInBase */  
	   
       INSERT  INTO dbo.CalcSignificance  
         ( PlanInfoID ,  
           ServiceAreaOptionID ,  
           BasePlanInfoID ,  
           [Status] ,  
           RemovedMM ,  
           MovingMM ,  
           TotalMM ,  
           IsWks1BasePlan ,  
           LastUpdateByID ,  
           LastUpdateDateTime  
         )  
       SELECT DISTINCT  PlanInfoID ,  
         ServiceAreaOptionID ,  
         BasePlanInfoID = ISNULL(BasePlanInfoID, 0) ,  
         [Status] ,  
         RemovedMM ,  
         MovingMM ,  
         TotalBaseMM ,  
         IsBasePlan ,  
         @LastUpdateByID ,  
         @UpdateDateTime  
       FROM    #PlansInBase  
	   
  
      END;     
    END;  
   END  

   --Special Crosswalk Specific Logic
  ELSE BEGIN
      /* Save county level crosswalks for the chosen plan(s)*/  
    
	IF OBJECT_ID('tempdb.dbo.#SAM_Crosswalks_SC') IS NOT NULL
	  DROP TABLE #SAM_Crosswalks_SC;

    SELECT DISTINCT  
      x.[ServiceAreaOptionID] ,  
      x.[SSStateCountyCD] ,  
      x.[BaseYearCPS] ,  
      BasePlanInfoID = spi.PlanInfoID ,  
      x.[CurrentYearCPS] ,  
      x.[BidYearCPS] ,  
	  x.[CurrentYearPlanInfoID],
      x.[BidYearPlanInfoID],  
      x.BidYearMapID,  
	  sc.DualStatus,
	  sc.AgeIndicator
    INTO    #SAM_Crosswalks_SC
    FROM    dbo.vwSAMCrosswalks x WITH (NOLOCK) 
      LEFT JOIN ( SELECT  PlanInfoID ,  
           CPS  
         FROM    dbo.SavedPlanInfo WITH (NOLOCK) 
         WHERE   PlanYear = @PlanYear - 2  
          ) spi ON x.BaseYearCPS = spi.CPS  
	  LEFT JOIN (SELECT MapID, FromPlanInfoID, PlanInfoID, ServiceAreaOptionID,
				CASE WHEN SUBSTRING(DualStatusAge, 2,1) = '_' THEN LEFT(DualStatusAge, 1) 
				ELSE LEFT(DualStatusAge, 2)
				END AS DualStatus, 
				RIGHT(DualStatusAge, 3) AS AgeIndicator
				--Edited for CY Special Crosswalks
				FROM dbo.SavedSpecialCrosswalk) sc ON (x.BidYearMapID = sc.MapID AND x.BidYearPlanInfoID = sc.PlanInfoID AND x.CurrentYearPlanInfoID = sc.FromPlanInfoID) 
				OR (x.CurrentYearPlanInfoID = sc.PlanInfoID AND x.BaseYearPlanInfoID = sc.FromPlanInfoID)
				WHERE   x.BidYearRenewalTypeID <> 2 --exclude termed plans  
				 AND (x.BidYearMapID = @MapId OR @MapId IS NULL)    
      
    -- If a plan is selected but mapid is null then do nothing  
    IF @PlanInfoID IS NULL OR @MapId IS NOT NULL BEGIN  
       
     /* Table to store base plan information while plans are run and outputs it all at end */  

	 IF OBJECT_ID('tempdb.dbo.#PlansInBase_SC') IS NOT NULL
	     DROP TABLE #PlansInBase_SC;

     CREATE TABLE #PlansInBase_SC  
      (  
        [PlanInfoID] SMALLINT NOT NULL ,  
        [ServiceAreaOptionID] TINYINT NOT NULL ,  
        [BasePlanInfoID] SMALLINT ,  
        [Status] VARCHAR(40) ,  
        [RemovedMM] DECIMAL(19, 6) ,  
        [MovingMM] DECIMAL(19, 6) ,  
        [TotalBaseMM] DECIMAL(19, 6) ,  
        [IsBasePlan] BIT ,  
        [BidCPS] CHAR(13),  
        [BaseCPS] CHAR(13),  
        CHECK ( [MovingMM] <= [TotalBaseMM] ) --We want error if crosswalked MM is greater than total MM  
      );  
  
      /* Save plan level crosswalks for the chosen plan(s)*/  
      
		IF OBJECT_ID('tempdb.dbo.#SAM_PlanLevelXw_SC') IS NOT NULL
			DROP TABLE #SAM_PlanLevelXw_SC;

      SELECT DISTINCT
        BidYearPlanInfoID ,  
        BidYearCPS,  
        ServiceAreaOptionID ,  
        BasePlanInfoID ,  
        BaseYearCPS,  
        BidYearMapID  
      INTO #SAM_PlanLevelXw_SC  
      FROM #SAM_Crosswalks_SC
	  
	  /*Fetching unique CPS(baseyearCPS and BidYearCPS)*/ ---- Changes made by Deepali    
  IF OBJECT_ID('tempdb.dbo.#PlanListCps_SC') IS NOT NULL
			DROP TABLE #PlanListCps_SC;
			CREATE TABLE #PlanListCps_SC (CPS Varchar(13))
			insert   INTO #PlanListCps_SC  
			select DISTINCT CPS from (SELECT DISTINCT        
			BidYearCPS	as CPS	
			FROM #SAM_PlanLevelXw_SC
			union 
			 SELECT DISTINCT        
			BaseYearCPS		as CPS
			FROM #SAM_PlanLevelXw_SC where BaseYearCPS is not null) a


			--Edits to the temp tables 1/26
	IF OBJECT_ID('tempdb.dbo.#BaseYearBIDEMM') IS NOT NULL  
		BEGIN  
		DROP TABLE #BaseYearBIDEMM; 
		END

     SELECT DISTINCT  
       DFVersionID,  
       PlanInfoID,  
       vw.CPS,  
       SSStateCountyCD,  
       MM  
     INTO    #BaseYearBIDEMM  
     FROM    dbo.vwBaseYearMM vw WITH (NOLOCK) 
	 Inner join #PlanListCps_SC c1			---- Join added by Deepali
	 on c1.CPS = vw.CPS

	 IF OBJECT_ID('tempdb.dbo.#BaseYearMMSpecialCounty') IS NOT NULL  
		BEGIN 
		DROP TABLE #BaseYearMMSpecialCounty; 
		END

     SELECT DISTINCT   
       vw.CPS,  
       SSStateCountyCD,  
       SUM(vw.MBR_CNT) AS CountyMMs
     INTO    #BaseYearMMSpecialCounty  
     FROM    dbo.SavedSpecialCrosswalkMembership vw WITH (NOLOCK) 
	 Inner join #PlanListCps_SC c1			---- Join added by Deepali
	 on c1.CPS = vw.CPS
	 GROUP BY vw.CPS, vw.SSStateCountyCd

     /* Save base membermonth data for plans in base  
      Save only plans that crosswalked*/   
	   IF OBJECT_ID('tempdb.dbo.#BaseYearMM_SC') IS NOT NULL  
		DROP TABLE #BaseYearMM_SC;

     SELECT DISTINCT  
       vw.BaseYearCPS AS CPS,  
       vw.SSStateCountyCD,  
	   vw.DualStatus, 
	   vw.AgeIndicator,
       CAST(SUM(vw.TotalDualMMs)AS DECIMAL) AS DualMM, 
	   CAST(SUM(vw.TotalCountyMMs)AS DECIMAL) AS BIDECountyMMs,
	   CAST(SUM(vw.TotalDualCountyMMs) AS DECIMAL) AS DualCountyMMs, 
	   (SUM(CAST(vw.TotalDualMMs AS FLOAT))/SUM(CAST(vw.TotalDualCountyMMs AS FLOAT))) * SUM(CAST(vw.TotalCountyMMs AS FLOAT)) AS AdjustedMMs
     INTO    #BaseYearMM_SC 
     FROM    (SELECT b.ServiceAreaOptionID, b.SSStateCountyCD, b.BaseYearCPS, b.BasePlanInfoID, b.CurrentYearCPS, b.BidYearCPS, b.CurrentYearPlanInfoID, 
	        b.BidYearPlanInfoID, b.BidYearMapID, b.DualStatus, b.AgeIndicator, SUM(a.MBR_CNT) AS TotalDualMMs, SUM(c.MM) AS TotalCountyMMs, 
			SUM(d.CountyMMs) AS TotalDualCountyMMs
			FROM [dbo].[SavedSpecialCrosswalkMembership] a
			LEFT JOIN #SAM_Crosswalks_SC b
			ON b.SSStateCountyCD = a.SSStateCountyCD AND b.DualStatus = a.MedicaidStatusCd AND b.AgeIndicator = a.AgeIndicator AND a.CPS = b.BaseYearCPS
			LEFT JOIN #BaseYearBIDEMM c ON c.SSStateCountyCD = a.SSStateCountyCd AND c.CPS = a.CPS
			LEFT JOIN #BaseYearMMSpecialCounty d ON d.CPS = a.CPS AND d.SSStateCountyCd = a.SSStateCountyCd
			GROUP BY b.ServiceAreaOptionID, b.SSStateCountyCD, b.BaseYearCPS, b.BasePlanInfoID, b.CurrentYearCPS, b.BidYearCPS, b.CurrentYearPlanInfoID, b.BidYearPlanInfoID, b.BidYearMapID, b.DualStatus, b.AgeIndicator) vw  
	 Inner join #PlanListCps_SC c1			---- Join added by Deepali
	 on c1.CPS = vw.BaseYearCPS
	 GROUP BY vw.BaseYearCPS, vw.SSStateCountyCD, vw.DualStatus, vw.AgeIndicator
	  
	  IF OBJECT_ID('tempdb.dbo.#Planloop_SC') IS NOT NULL
		DROP TABLE #Planloop_SC;
	  -- PlanInfoID and ServiceAreaOptionID from SavedServiceAreaOption into Plan_Loop  
	 SELECT DISTINCT 
	    ROW_NUMBER() OVER(ORDER BY BidYearMapID ASC) RowNo,
        [BidYearMapID],  
        [BidYearPlanInfoID] ,  
        [ServiceAreaOptionID] INTO #Planloop_SC
      FROM    #SAM_PlanLevelXw_SC WHERE (BidYearMapID = @MapId OR @MapId IS NULL) 


	 --DECLARE @Rowno AS INT, @Record_Count INT;
	 SET @Rowno =1;
     SET @Record_Count = (SELECT COUNT(1) FROM #Planloop_SC);
	 
	 --SELECT * FROM #Planloop_SC
	 
  IF(@Record_Count>0)
	  BEGIN
		 WHILE (@Rowno<=@Record_Count)  -- Plan_Loop  
			BEGIN   
				
				 SELECT DISTINCT  
					@MapIdIter = [BidYearMapID],  
					@PlanInfo = [BidYearPlanInfoID] ,  
					@SAOption =  [ServiceAreaOptionID]  
					FROM    #Planloop_SC WHERE RowNo=@Rowno

					
					  /*-Check if CPS existed in the Base Period, IF TRUE then put it into @PlansInBase.  
					We don't need to calculate significance in that case. Hence NULL MovingMM and RemovedMM */  
				   SET @BidYearCPS = ( SELECT  CPS  
					FROM    dbo.SavedPlanInfo WITH (NOLOCK) 
					WHERE   PlanInfoID = @PlanInfo  
					);  
				   SET @BasePlanInfo = ( SELECT    PlanInfoID  
					FROM      dbo.SavedPlanInfo WITH (NOLOCK) 
					WHERE     PlanYear = @PlanYear - 2  
					  AND CPS = @BidYearCPS  
					); 
					
					IF @BasePlanInfo IS NOT NULL  
						BEGIN   
							 /* Total Biddable Base Period MM from @XBid */      
							 SET @TotalMM = ( SELECT SUM(MM)  
							  FROM   #BaseYearBIDEMM
							  WHERE  CPS = @BidYearCPS  
							  GROUP BY CPS  
							 );  
							 SET @TotalMM = COALESCE(@TotalMM, 0);    
							 INSERT  INTO #PlansInBase_SC 
							 VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
								 'Reporting Bid', NULL, NULL, @TotalMM, 1, @BidYearCPS, @BidYearCPS );  
						END;  
					   ELSE  
						BEGIN  
							 INSERT  INTO #PlansInBase_SC  
							 VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
								 'Reporting Bid Not In Base', NULL, NULL,NULL, 0, @BidYearCPS, NULL);  
						END;  

						IF OBJECT_ID('tempdb.dbo.#RB_Loop_SC') IS NOT NULL
							DROP TABLE #RB_Loop_bk_SC
						
						SELECT DISTINCT							 
							  [BaseYearCPS]  INTO #RB_Loop_bk_SC
							FROM    [#SAM_Crosswalks_SC]  
							WHERE   BaseYearCPS IS NOT NULL  
							  AND [BaseYearCPS] IN (  
							  SELECT DISTINCT  
								[BaseYearCPS]  
							  FROM    [#SAM_Crosswalks_SC]  
							  WHERE   [BidYearCPS] = @BidYearCPS  
								AND [BaseYearCPS] <> @BidYearCPS );
						
						IF OBJECT_ID('tempdb.dbo.#RB_Loop_SC') IS NOT NULL
							DROP TABLE #RB_Loop_SC
								
						  SELECT  ROW_NUMBER() OVER(ORDER BY [BaseYearCPS] ASC) RB_Rowno, [BaseYearCPS] INTO #RB_Loop_SC FROM #RB_Loop_bk_SC;
								
						 --DECLARE @RB_Rowno AS INT, @RB_Record_Count INT;
						 SET @RB_Rowno =1;
					 
						 SET @RB_Record_Count = (SELECT COUNT(1) FROM #RB_Loop_SC);
						  
						 IF(@RB_Record_Count>0)
							  BEGIN
								 WHILE (@RB_Rowno<=@RB_Record_Count)
									BEGIN
											SELECT @XBid =[BaseYearCPS] FROM #RB_Loop_SC WHERE RB_Rowno=@RB_Rowno
											
										   /* Biddable BaseMM from counties that existed in @XBid and @Reporting_Bid */  
											 SET @MovingMM = ( SELECT    ROUND(SUM(AdjustedMMs), 0)  
											  FROM      #BaseYearMM_SC  
											  WHERE     CPS = @XBid  
												AND [SSStateCountyCD]+ [DualStatus]+ [AgeIndicator] IN (  
												SELECT DISTINCT  
												  [SSStateCountyCD]+ [DualStatus]+ [AgeIndicator]
												FROM    [#SAM_Crosswalks_SC]  
												WHERE   [BidYearCPS] = @BidYearCPS  
												  AND [BaseYearCPS] = @XBid  
												  AND [ServiceAreaOptionID] = @SAOption)  
											  GROUP BY  CPS  
											 );  
											 SET @MovingMM = COALESCE(@MovingMM, 0);  
          
											 /* BaseMM from counties that crosswalked in CY, but are removed from @Reporting_Bid */  
											 --SET @RemovedMM = (SELECT   SUM([MemberMonths])  
											 SET @CYXwedMM = (SELECT   ROUND(SUM(AdjustedMMs), 0)  
											  FROM     #BaseYearMM_SC
											  WHERE    CPS = @XBid  
												AND [SSStateCountyCD]+ [DualStatus]+ [AgeIndicator]  IN (  
												SELECT DISTINCT  
												  [SSStateCountyCD]+ [DualStatus]+ [AgeIndicator]  
												FROM    [#SAM_Crosswalks_SC]  
												WHERE [BaseYearCPS] = @XBid AND [CurrentYearCPS] = @BidYearCPS AND BidYearMapID = @MapIdIter)                   
												--WHERE   [BidYearPlanInfoID] = @PlanInfo  
												--  AND [BaseYearCPS] = @XBid  
												--  AND [ServiceAreaOptionID] = @SAOption  
												--  AND [BidYearCPS] IS NULL)  
											  GROUP BY CPS  
											  );  
											 SET @RemovedMM = COALESCE(@CYXwedMM - @MovingMM, 0)  
											 IF @RemovedMM < 0 BEGIN   
											  SET @RemovedMM = ( SELECT   ROUND(SUM(AdjustedMMs), 0)  
											   FROM     #BaseYearMM_SC  
											   WHERE    CPS = @XBid  
												 AND [SSStateCountyCD]+ [DualStatus]+ [AgeIndicator]  IN (  
												 SELECT DISTINCT  
												   [SSStateCountyCD]+ [DualStatus]+ [AgeIndicator] 
												 FROM    [#SAM_Crosswalks_SC]  
												 WHERE   [BidYearPlanInfoID] = @PlanInfo  
												   AND [BaseYearCPS] = @XBid  
												   AND [ServiceAreaOptionID] = @SAOption  
												   AND [BidYearCPS] IS NULL)  
											   GROUP BY CPS  
											   );  
											  SET @RemovedMM = COALESCE(@RemovedMM, 0)  
											 END  
											 /* Total Biddable BaseMM from @XBid */      
											 SET @TotalMM = ( SELECT SUM(MM)  
											  FROM   #BaseYearBIDEMM 
											  WHERE  CPS = @XBid  
											  GROUP BY CPS  
											 );  
											 SET @TotalMM = COALESCE(@TotalMM, 0);  
  
											 /*Convert @XBid to PlanInfoID to store into CalcSignificance table */  
											 SET @BasePlanInfo = ( SELECT    PlanInfoID  
											  FROM      dbo.SavedPlanInfo WITH (NOLOCK)  
											  WHERE     PlanYear = @PlanYear - 2  
												AND CPS = @XBid  
											 );  
      
											  /* Check if Significance is above threshold */  
											 IF @MovingMM <= ( @TotalMM * @Threshold )  
											  BEGIN  
												   INSERT  INTO #PlansInBase_SC 
												   VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
													   'Insignificant Crosswalk',  
													   @RemovedMM, @MovingMM, @TotalMM, 0, @BidYearCPS, @XBid );  
											  END;  
											 ELSE  
											  BEGIN  
												   INSERT  INTO #PlansInBase_SC  
												   VALUES  ( @PlanInfo, @SAOption, @BasePlanInfo,  
													   'Significant Crosswalk', @RemovedMM,  
													   @MovingMM, @TotalMM, 1, @BidYearCPS, @XBid );  
											  END;  
         

									SET @RB_Rowno= @RB_Rowno+1;
									END
							  END
			 
				SET @Rowno= @Rowno+1;
 
			END      
	  END


     /* Check if there is data in #PlansInBase and then delete from CalcSignificance table data */  
     SET @NumOfRows = ( SELECT   COUNT(*)  
            FROM     #PlansInBase_SC 
          );  
     IF @NumOfRows > 0  
      BEGIN  
	  
       DELETE cs FROM dbo.CalcSignificance cs  
       WHERE EXISTS (SELECT 1 FROM #PlansInBase_SC pl   
       WHERE cs.PlanInfoID = pl.PlanInfoID AND   
       cs.ServiceAreaOptionID = pl.ServiceAreaOptionID)  
        


       /*Insert into CalcSignficance data from #PlansInBase */  
	   
       INSERT  INTO dbo.CalcSignificance  
         ( PlanInfoID ,  
           ServiceAreaOptionID ,  
           BasePlanInfoID ,  
           [Status] ,  
           RemovedMM ,  
           MovingMM ,  
           TotalMM ,  
           IsWks1BasePlan ,  
           LastUpdateByID ,  
           LastUpdateDateTime  
         )  
       SELECT DISTINCT  PlanInfoID ,  
         ServiceAreaOptionID ,  
         BasePlanInfoID = ISNULL(BasePlanInfoID, 0) ,  
         [Status] ,  
         RemovedMM ,  
         MovingMM ,  
         TotalBaseMM ,  
         IsBasePlan ,  
         @LastUpdateByID ,  
         @UpdateDateTime  
       FROM    #PlansInBase_SC
	   
  
      END;     
    END;  
   END  
  END
  END
GO