SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_HistProcess_spCalcIsPlanLevel
--
-- CREATOR: <PERSON> 
--
-- CREATED DATE: Aug-19-2020
--
-- DESCRIPTION: Calculate historic period annual trends at plan level based on relativity inputs
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--				@PlanInfoID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntBenefitCategory
--				    LkpIntPlanYear
--					SavedHistoricalCrosswalk
--				    Trend_CalcHistoricCostAndUse
--			    	Trend_CalcHistoricMembership
--					Trend_SavedComponentInfo
--					Trend_SavedRelativityCMSReimb
--					Trend_SavedRelativityContractual
--					Trend_SavedRelativityInducedUtilization
--					Trend_SavedRelativityOutlierClaims
--					Trend_SavedRelativityPopulation
--
--          Write:  Trend_HistProcess_CalcIsPlanLevel
--                  Trend_Reporting_Log
--
-- VIEWS: Read:		vwPlanInfo
--
-- FUNCTIONS:		Trend_fnCalcStringToTable
--					Trend_fnSafeDivide
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER  
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- AUG-18-2020      1           Initial Version																								Michelle Gagne
-- OCT-05-2020		2			Code updates to follow best practices																		Jake Lewis
--								Update crosswalks to use vwCrosswalksPlanLevel						
-- OCT-20-2020		3			Fixed issues with sonar qube																				Deepali Mittal
-- NOV-18-2020		4			Use straight average for annual relativity calc																Jake Lewis
-- DEC-10-2020		5			Name changes for the SP and some of the referenced tables:													Jake Lewis
--								NEW SP = Trend_HistProcess_spCalcIsPlanLevel_Incurred, OLD SP = Trend_Reporting_spCalcAllPlanLevelHistoricTrends
--								NEW = Trend_HistProcess_CalcIsPlanLevel_Incurred, OLD = Trend_Reporting_CalcAllPlanLevelHistoricTrends
-- DEC-24-2020		6			Second name change for the SP and output table:																Jake Lewis
--								NEW SP = Trend_HistProcess_spCalcIsPlanLevel, OLD SP = Trend_HistProcess_spCalcIsPlanLevel_Incurred
--								NEW = Trend_HistProcess_CalcIsPlanLevel, OLD = Trend_HistProcess_CalcIsPlanLevel_Incurred
-- MAR-15-2021		7			Code adjustments to improve efficiency and decrease runtime													Jake Lewis
--								Add PlanInfoID input parameter
-- Aug-03-2023      8           Add @X variable and NOLOCK                                                                                  Sheetal Patil
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_HistProcess_spCalcIsPlanLevel]

@LastUpdateByID CHAR(7)
,@PlanInfoID    VARCHAR(MAX) = NULL

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        DECLARE @tranCount INT = @@TranCount,
                @XLastUpdateByID CHAR(7) = @LastUpdateByID,
                @XPlanInfoID    VARCHAR(MAX) = @PlanInfoID


        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            ------------------------------------------------------------
            -- 0. Declare / set variables and create some temp tables --
            ------------------------------------------------------------
            --Get year variables to limit pulls from input tables
            DECLARE @MinHistoricYear INT;
            DECLARE @MaxHistoricYear INT;
            DECLARE @BaseYear INT;
            DECLARE @BidYear INT;
            SELECT  @MinHistoricYear = PlanYearID - 3
                   ,@MaxHistoricYear = PlanYearID
                   ,@BaseYear = PlanYearID
            FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
            WHERE   IsExperienceYear = 1;
            SET @BidYear = (SELECT  PlanYearID FROM dbo.LkpIntPlanYear WHERE IsBidYear = 1);
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);

            --Reporting Category List
            IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL DROP TABLE #RepCat;
            SELECT  DISTINCT
                    ReportingCategory
            INTO    #RepCat
            FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
            WHERE   ReportingCategory IS NOT NULL;

            --Component List
            IF (SELECT  OBJECT_ID ('tempdb..#Component')) IS NOT NULL
                DROP TABLE #Component;
            SELECT  DISTINCT
                    Component
            INTO    #Component
            FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
            WHERE   Component NOT LIKE '%Rx%'
                    AND Component NOT LIKE '%Normalized%'
                    AND Component NOT LIKE '%Adj%'
                    AND PlanTypeGranularity1 = 'CPS';

            --Component Aggregation
            IF (SELECT  OBJECT_ID ('tempdb..#ComponentAggregation')) IS NOT NULL
                DROP TABLE #ComponentAggregation;
            SELECT  Component
                   ,AggregationMetric
            INTO    #ComponentAggregation
            FROM    dbo.Trend_SavedComponentInfo WITH (NOLOCK)
            WHERE   AggregationMetric IS NOT NULL;

            --QuarterIDs
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterID')) IS NOT NULL
                DROP TABLE #QuarterID;
            SELECT  DISTINCT
                    number AS QuarterID
            INTO    #QuarterID
            FROM    master..spt_values
            WHERE   number BETWEEN 1 AND 4;

            --Plan List
            IF (SELECT  OBJECT_ID ('tempdb..#PlanList')) IS NOT NULL
                DROP TABLE #PlanList;
            SELECT  PlanInfoID
                   ,CPS
                   ,PlanYear
            INTO    #PlanList
            FROM    dbo.vwPlanInfo WITH (NOLOCK)
            WHERE   (PlanYear BETWEEN @BidYear - 5 AND @BidYear - 1)
                    AND Region NOT IN ('Unmapped')
                    AND IsOffMAModel = 'No'
                    AND IsHidden = 0
                    AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@XPlanInfoID, ',', 1) )
                         OR @XPlanInfoID IS NULL);

            ------------------------------
            -- 1. Historic Relativities --
            ------------------------------
            --Insert all historic relativities into a single temp table
            IF (SELECT  OBJECT_ID ('tempdb..#HistoricRelativities')) IS NOT NULL
                DROP TABLE #HistoricRelativities;
            CREATE TABLE #HistoricRelativities
                (PlanInfoID        INT
                ,Component         VARCHAR(50)
                ,CPS               CHAR(13)
                ,PlanYearID        INT
                ,QuarterID         SMALLINT
                ,ReportingCategory VARCHAR(50)
                ,CostRelativity    DEC(18, 8)
                ,UseRelativity     DEC(18, 8));

            INSERT INTO #HistoricRelativities
            SELECT      p.PlanInfoID
                       ,'CMS Reimbursement' AS Component
                       ,cms.CPS
                       ,cms.PlanYearID
                       ,cms.QuarterID
                       ,cms.ReportingCategory
                       ,CAST(cms.CostRelativity AS FLOAT) AS CostRelativity
                       ,CAST(cms.UseRelativity AS FLOAT) AS UseRelativity
            FROM        dbo.Trend_SavedRelativityCMSReimb cms WITH (NOLOCK)
            LEFT JOIN   #PlanList p
                   ON p.CPS = cms.CPS
                      AND   p.PlanYear = cms.PlanYearID
            WHERE       cms.PlanYearID <= @MaxHistoricYear
                        AND p.PlanInfoID IS NOT NULL;

            INSERT INTO #HistoricRelativities
            SELECT      p.PlanInfoID
                       ,'Contractual' AS Component
                       ,con.CPS
                       ,con.PlanYearID
                       ,con.QuarterID
                       ,con.ReportingCategory
                       ,CAST(con.CostRelativity AS FLOAT) AS CostRelativity
                       ,CAST(con.UseRelativity AS FLOAT) AS UseRelativity
            FROM        dbo.Trend_SavedRelativityContractual con WITH (NOLOCK)
            LEFT JOIN   #PlanList p
                   ON p.CPS = con.CPS
                      AND   p.PlanYear = con.PlanYearID
            WHERE       con.PlanYearID <= @MaxHistoricYear
                        AND p.PlanInfoID IS NOT NULL;

            INSERT INTO #HistoricRelativities
            SELECT      p.PlanInfoID
                       ,'Induced Utilization' AS Component
                       ,iu.CPS
                       ,iu.PlanYearID
                       ,iu.QuarterID
                       ,iu.ReportingCategory
                       ,CAST(iu.CostRelativity AS FLOAT) AS CostRelativity
                       ,CAST(iu.UseRelativity AS FLOAT) AS UseRelativity
            FROM        dbo.Trend_SavedRelativityInducedUtilization iu WITH (NOLOCK)
            LEFT JOIN   #PlanList p
                   ON p.CPS = iu.CPS
                      AND   p.PlanYear = iu.PlanYearID
            WHERE       iu.PlanYearID <= @MaxHistoricYear
                        AND p.PlanInfoID IS NOT NULL;

            INSERT INTO #HistoricRelativities
            SELECT      p.PlanInfoID
                       ,'Outlier Claims' AS Component
                       ,oc.CPS
                       ,oc.PlanYearID
                       ,oc.QuarterID
                       ,oc.ReportingCategory
                       ,CAST(oc.CostRelativity AS FLOAT) AS CostRelativity
                       ,CAST(oc.UseRelativity AS FLOAT) AS UseRelativity
            FROM        dbo.Trend_SavedRelativityOutlierClaims oc WITH (NOLOCK)
            LEFT JOIN   #PlanList p
                   ON p.CPS = oc.CPS
                      AND   p.PlanYear = oc.PlanYearID
            WHERE       oc.PlanYearID <= @MaxHistoricYear
                        AND p.PlanInfoID IS NOT NULL;

            INSERT INTO #HistoricRelativities
            SELECT      p.PlanInfoID
                       ,'Population' AS Component
                       ,pop.CPS
                       ,pop.PlanYearID
                       ,pop.QuarterID
                       ,pop.ReportingCategory
                       ,CAST(pop.CostRelativity AS FLOAT) AS CostRelativity
                       ,CAST(pop.UseRelativity AS FLOAT) AS UseRelativity
            FROM        dbo.Trend_SavedRelativityPopulation pop WITH (NOLOCK)
            LEFT JOIN   #PlanList p
                   ON p.CPS = pop.CPS
                      AND   p.PlanYear = pop.PlanYearID
            WHERE       pop.PlanYearID <= @MaxHistoricYear
                        AND p.PlanInfoID IS NOT NULL;


            ------------------------------------
            -- 2. Select all plans to include --
            ------------------------------------
            --If min Historical Year PlanInfoIDs excluded from #PlanTableSetUp, 
            -- all PlanInfoID/Component/RepCat combos populated in the final table are pulled below
            IF (SELECT  OBJECT_ID ('tempdb..#PlanTableSetUp')) IS NOT NULL
                DROP TABLE #PlanTableSetUp;
            SELECT      DISTINCT
                        vpi.PlanInfoID
                       ,vpi.PlanYear
                       ,vpi.CPS
            INTO        #PlanTableSetUp
            FROM        dbo.vwPlanInfo vpi WITH (NOLOCK)
            LEFT JOIN   #PlanList p
                   ON p.PlanInfoID = vpi.PlanInfoID
            WHERE       vpi.PlanYear >= @MinHistoricYear
                        AND vpi.PlanYear <= @BaseYear
                        AND vpi.IsOffMAModel = 'No'
                        AND vpi.IsHidden = 0
                        AND vpi.Region NOT IN ('Unmapped')
                        AND p.PlanInfoID IS NOT NULL;

            --Make Quarterly for Trend Calc
            IF (SELECT  OBJECT_ID ('tempdb..#PlanTableSetUpQuart')) IS NOT NULL
                DROP TABLE #PlanTableSetUpQuart;
            SELECT      DISTINCT
                        a.PlanYear
                       ,a.CPS
                       ,a.PlanInfoID
                       ,c.Component
                       ,rc.ReportingCategory
                       ,q.QuarterID
            INTO        #PlanTableSetUpQuart
            FROM        #PlanTableSetUp a
           CROSS JOIN   #QuarterID q
           CROSS JOIN   #Component c
           CROSS JOIN   #RepCat rc
            WHERE       rc.ReportingCategory NOT IN ('Part B Rx Pharmacy');


            ----------------------------
            -- 3. Aggregation Metrics --
            ----------------------------
            IF (SELECT  OBJECT_ID ('tempdb..#CostAndUse')) IS NOT NULL
                DROP TABLE #CostAndUse;
            --Allowed and Utilization
            SELECT      PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,QuarterID
                       ,ReportingCategory
                       ,CAST(SUM (Allowed) AS FLOAT) AS Allowed
                       ,CAST(SUM (Utilization) AS FLOAT) AS Utilization
                       ,0 AS MemberMonths
            INTO        #CostAndUse
            FROM        dbo.Trend_CalcHistoricCostAndUse WITH (NOLOCK)
            WHERE       PlanYearID <= @MaxHistoricYear
            GROUP BY    PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,QuarterID
                       ,ReportingCategory;

            --Membership
            IF (SELECT  OBJECT_ID ('tempdb..#Membership')) IS NOT NULL
                DROP TABLE #Membership;
            SELECT      PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,QuarterID
                       ,0 AS Allowed
                       ,0 AS Utilization
                       ,SUM (MemberMonths) AS MemberMonths
            INTO        #Membership
            FROM        dbo.Trend_CalcHistoricMembership WITH (NOLOCK)
            WHERE       PlanYearID <= @MaxHistoricYear
            GROUP BY    PlanInfoID
                       ,CPS
                       ,PlanYearID
                       ,QuarterID;

            -- Aggregation shell
            IF (SELECT  OBJECT_ID ('tempdb..#AggregationShell')) IS NOT NULL
                DROP TABLE #AggregationShell;
            SELECT      pt.PlanInfoID
                       ,pt.CPS
                       ,pt.PlanYear AS PlanYearID
                       ,pt.QuarterID
                       ,pt.ReportingCategory
                       ,pt.Component
                       ,ca.AggregationMetric
            INTO        #AggregationShell
            FROM        #PlanTableSetUpQuart pt
            LEFT JOIN   #ComponentAggregation ca
                   ON ca.Component = pt.Component;

            -- Attach aggregation values to shell
            IF (SELECT  OBJECT_ID ('tempdb..#AggregationMetrics')) IS NOT NULL
                DROP TABLE #AggregationMetrics;
            SELECT      a.PlanInfoID
                       ,a.CPS
                       ,a.PlanYearID
                       ,a.QuarterID
                       ,a.ReportingCategory
                       ,a.Component
                       ,CASE WHEN a.AggregationMetric = 'Allowed' THEN cu.Allowed
                             WHEN a.AggregationMetric = 'Utilization' THEN cu.Utilization
                             WHEN a.AggregationMetric = 'MemberMonths' THEN m.MemberMonths
                             ELSE 0 END AS AggregationValue -- AggregationMetric will always be Allowed, Utilization, or MemberMonths, so this ELSE statement cannot occur
            INTO        #AggregationMetrics
            FROM        #AggregationShell a
            LEFT JOIN   #CostAndUse cu
                   ON cu.PlanInfoID = a.PlanInfoID
                      AND   cu.QuarterID = a.QuarterID
                      AND   cu.ReportingCategory = a.ReportingCategory
                      AND   a.AggregationMetric IN ('Allowed', 'Utilization')
            LEFT JOIN   #Membership m
                   ON m.PlanInfoID = a.PlanInfoID
                      AND   m.QuarterID = a.QuarterID
                      AND   a.AggregationMetric IN ('MemberMonths');


            -----------------------------------
            -- 4. Set up N to N+1 crosswalks --
            -----------------------------------
            -- Existing Plans
            IF (SELECT  OBJECT_ID ('tempdb..#Crosswalks')) IS NOT NULL
                DROP TABLE #Crosswalks;
            SELECT  DISTINCT
                    CASE WHEN FromPlanInfoID = 0 THEN NULL ELSE FromPlanInfoID END AS FromPlanInfoID
                   ,PlanInfoID
            INTO    #Crosswalks
            FROM    dbo.SavedHistoricalCrosswalk WITH (NOLOCK)
            WHERE   PlanInfoID <> 0
                    AND IsNew <> 1;

            -- New Plans
            IF (SELECT  OBJECT_ID ('tempdb..#NewPlans')) IS NOT NULL
                DROP TABLE #NewPlans;
            SELECT      DISTINCT
                        PlanInfoID
                       ,RenewalTypeID
                       --If IsNew = 1 for all counties for a given plan, then the line below will evaluate to 1, and thus this is a new plan. 
                       --If the line below evaluates to anything other than 1, then some (or all) of the counties have crosswalked, and thus the plan itself is not new. 
                       ,CASE WHEN SUM (CAST(IsNew AS FLOAT)) / CAST(COUNT (IsNew) AS FLOAT) = 1 THEN 1 ELSE 0 END AS IsNew
            INTO        #NewPlans
            FROM        dbo.SavedHistoricalCrosswalk WITH (NOLOCK)
            GROUP BY    PlanInfoID
                       ,RenewalTypeID;

            INSERT INTO #Crosswalks
            SELECT  NULL AS FromPlanInfoID, PlanInfoID FROM #NewPlans WHERE IsNew = 1;

            -- Add PlanYearID, CPS
            IF (SELECT  OBJECT_ID ('tempdb..#CrosswalksExpand')) IS NOT NULL
                DROP TABLE #CrosswalksExpand;
            SELECT      v2.PlanYear AS FromPlanYearID
                       ,v2.CPS AS FromCPS
                       ,xw.FromPlanInfoID
                       ,v1.PlanYear AS PlanYearID
                       ,v1.CPS AS CPS
                       ,xw.PlanInfoID
            INTO        #CrosswalksExpand
            FROM        #Crosswalks xw
            LEFT JOIN   dbo.vwPlanInfo v1 WITH (NOLOCK)
                   ON v1.PlanInfoID = xw.PlanInfoID
            LEFT JOIN   dbo.vwPlanInfo v2 WITH (NOLOCK)
                   ON v2.PlanInfoID = xw.FromPlanInfoID
            WHERE       (v1.PlanYear BETWEEN @BidYear - 4 AND @BidYear - 2);

            -- Add Component, RepCat
            IF (SELECT  OBJECT_ID ('tempdb..#ComponentTableSetUp')) IS NOT NULL
                DROP TABLE #ComponentTableSetUp;
            SELECT      xw.FromPlanYearID
                       ,xw.FromCPS
                       ,xw.FromPlanInfoID
                       ,xw.PlanYearID
                       ,xw.CPS
                       ,xw.PlanInfoID
                       ,c.Component
                       ,rc.ReportingCategory
            INTO        #ComponentTableSetUp
            FROM        #CrosswalksExpand xw
           CROSS JOIN   #Component c
           CROSS JOIN   #RepCat rc
            WHERE       rc.ReportingCategory NOT IN ('Part B Rx Pharmacy');

            --Make Quarterly for Trend Calc
            IF (SELECT  OBJECT_ID ('tempdb..#ComponentTableSetUpQuart')) IS NOT NULL
                DROP TABLE #ComponentTableSetUpQuart;
            SELECT      DISTINCT
                        a.FromPlanYearID
                       ,a.FromCPS
                       ,a.FromPlanInfoID
                       ,a.PlanYearID
                       ,a.CPS
                       ,a.PlanInfoID
                       ,a.Component
                       ,a.ReportingCategory
                       ,q.QuarterID
            INTO        #ComponentTableSetUpQuart
            FROM        #ComponentTableSetUp a
           CROSS JOIN   #QuarterID q
            LEFT JOIN   #PlanList p
                   ON p.PlanInfoID = a.PlanInfoID
            WHERE       p.PlanInfoID IS NOT NULL;


            ---------------------
            -- 5. Calculations --
            ---------------------
            -- Data for Quarterly Relativities 
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyRelativitiesData')) IS NOT NULL
                DROP TABLE #QuarterlyRelativitiesData;
            SELECT      p.PlanYear AS PlanYearID
                       ,p.CPS
                       ,p.PlanInfoID
                       ,p.Component
                       ,p.ReportingCategory
                       ,p.QuarterID
                       ,ISNULL (hr.CostRelativity, 1) AS CostRelativity
                       ,ISNULL (hr.UseRelativity, 1) AS UseRelativity
                       ,ISNULL (am.AggregationValue, 0) AS AggregationValue
            INTO        #QuarterlyRelativitiesData
            FROM        #PlanTableSetUpQuart p
            LEFT JOIN   #HistoricRelativities hr
                   ON hr.PlanInfoID = p.PlanInfoID
                      AND   hr.QuarterID = p.QuarterID
                      AND   hr.Component = p.Component
                      AND   hr.ReportingCategory = p.ReportingCategory
            LEFT JOIN   #AggregationMetrics am
                   ON am.PlanInfoID = p.PlanInfoID
                      AND   am.QuarterID = p.QuarterID
                      AND   am.Component = p.Component
                      AND   am.ReportingCategory = p.ReportingCategory;

            -- Calculate Quarterly Relativities 
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyRelativities')) IS NOT NULL
                DROP TABLE #QuarterlyRelativities;
            SELECT      PlanYearID
                       ,CPS
                       ,PlanInfoID
                       ,Component
                       ,ReportingCategory
                       ,QuarterID
                       ,SUM (AggregationValue) AS AggregationValue
                       ,COALESCE (
                        (SUM (CostRelativity * AggregationValue)), AVG (CostRelativity) * SUM (AggregationValue), 1) AS CostRelativity
                       ,COALESCE ((SUM (UseRelativity * AggregationValue)), AVG (UseRelativity) * SUM (AggregationValue), 1) AS UseRelativity
            INTO        #QuarterlyRelativities
            FROM        #QuarterlyRelativitiesData
            GROUP BY    PlanYearID
                       ,CPS
                       ,PlanInfoID
                       ,Component
                       ,ReportingCategory
                       ,QuarterID;

            -- Data for Quarterly Factors
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyFactorsData')) IS NOT NULL
                DROP TABLE #QuarterlyFactorsData;
            SELECT      a.FromPlanYearID
                       ,a.FromCPS
                       ,a.FromPlanInfoID
                       ,a.PlanYearID
                       ,a.CPS
                       ,a.PlanInfoID
                       ,a.Component
                       ,a.ReportingCategory
                       ,a.QuarterID
                       ,ISNULL (b.CostRelativity, 1) AS QR1CostRelativity
                       ,ISNULL (b.UseRelativity, 1) AS QR1UseRelativity
                       ,ISNULL (b.AggregationValue, 0) AS QR1AggregationValue
                       ,ISNULL (c.CostRelativity, 1) AS QR2CostRelativity
                       ,ISNULL (c.UseRelativity, 1) AS QR2UseRelativity
                       ,ISNULL (c.AggregationValue, 0) AS QR2AggregationValue
                       ,ISNULL (hr1.CostRelativity, 1) AS HR1CostRelativity
                       ,ISNULL (hr1.UseRelativity, 1) AS HR1UseRelativity
                       ,ISNULL (hr2.CostRelativity, 1) AS HR2CostRelativity
                       ,ISNULL (hr2.UseRelativity, 1) AS HR2UseRelativity
            INTO        #QuarterlyFactorsData
            FROM        #ComponentTableSetUpQuart a
            LEFT JOIN   #QuarterlyRelativities b
                   ON a.FromPlanYearID = b.PlanYearID
                      AND   a.FromCPS = b.CPS
                      AND   a.Component = b.Component
                      AND   a.ReportingCategory = b.ReportingCategory
                      AND   a.QuarterID = b.QuarterID
            LEFT JOIN   #QuarterlyRelativities c
                   ON a.PlanYearID = c.PlanYearID
                      AND   a.CPS = c.CPS
                      AND   a.Component = c.Component
                      AND   a.ReportingCategory = c.ReportingCategory
                      AND   a.QuarterID = c.QuarterID
            LEFT JOIN   #HistoricRelativities hr1
                   ON hr1.Component = a.Component
                      AND   hr1.QuarterID = a.QuarterID
                      AND   hr1.ReportingCategory = a.ReportingCategory
                      AND   hr1.PlanInfoID = a.FromPlanInfoID
            LEFT JOIN   #HistoricRelativities hr2
                   ON hr2.Component = a.Component
                      AND   hr2.QuarterID = a.QuarterID
                      AND   hr2.ReportingCategory = a.ReportingCategory
                      AND   hr2.PlanInfoID = a.PlanInfoID
            WHERE       c.PlanInfoID IS NOT NULL;

            -- Calculate Quarterly Relativity Factors
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyFactors')) IS NOT NULL
                DROP TABLE #QuarterlyFactors;
            SELECT      FromPlanYearID
                       ,FromCPS
                       ,FromPlanInfoID
                       ,PlanYearID
                       ,CPS
                       ,PlanInfoID
                       ,Component
                       ,ReportingCategory
                       ,QuarterID
                       ,CASE WHEN SUM (QR1CostRelativity) = 0 THEN SUM (HR1CostRelativity)
                             ELSE
                                 dbo.Trend_fnSafeDivide (
                                 SUM (ISNULL (QR1CostRelativity, 1)), SUM (ISNULL (QR1AggregationValue, 0)), 0) END AS FromCostRelativityFactor
                       ,CASE WHEN SUM (QR1UseRelativity) = 0 THEN SUM (HR1UseRelativity)
                             ELSE
                                 dbo.Trend_fnSafeDivide (
                                 SUM (ISNULL (QR1UseRelativity, 1)), SUM (ISNULL (QR1AggregationValue, 0)), 0) END AS FromUseRelativityFactor
                       ,SUM (ISNULL (QR1AggregationValue, 0)) AS FromAggregationValue
                       ,CASE WHEN SUM (QR2CostRelativity) = 0 THEN SUM (HR2CostRelativity)
                             ELSE
                                 dbo.Trend_fnSafeDivide (
                                 SUM (ISNULL (QR2CostRelativity, 1)), SUM (ISNULL (QR2AggregationValue, 0)), 0) END AS CostRelativityFactor
                       ,CASE WHEN SUM (QR2UseRelativity) = 0 THEN SUM (HR2UseRelativity)
                             ELSE
                                 dbo.Trend_fnSafeDivide (
                                 SUM (ISNULL (QR2UseRelativity, 1)), SUM (ISNULL (QR2AggregationValue, 0)), 0) END AS UseRelativityFactor
                       ,SUM (ISNULL (QR2AggregationValue, 0)) AS AggregationValue
            INTO        #QuarterlyFactors
            FROM        #QuarterlyFactorsData
            GROUP BY    FromPlanYearID
                       ,FromCPS
                       ,FromPlanInfoID
                       ,PlanYearID
                       ,CPS
                       ,PlanInfoID
                       ,Component
                       ,ReportingCategory
                       ,QuarterID;

            -- Roll up aggregation metrics by quarter
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyRollup')) IS NOT NULL
                DROP TABLE #QuarterlyRollup;
            SELECT      qf.PlanYearID
                       ,qf.PlanInfoID
                       ,qf.CPS
                       ,qf.QuarterID
                       ,qf.Component
                       ,qf.ReportingCategory
                       ,AVG (qf.FromCostRelativityFactor) AS FromCostRelativityFactor
                       ,AVG (qf.FromUseRelativityFactor) AS FromUseRelativityFactor
                       ,SUM (qf.FromAggregationValue) AS FromAggregationValue
                       ,AVG (qf.CostRelativityFactor) AS CostRelativityFactor
                       ,AVG (qf.UseRelativityFactor) AS UseRelativityFactor
                       ,SUM (qf.AggregationValue) AS AggregationValue
            INTO        #QuarterlyRollup
            FROM        #QuarterlyFactors qf
            GROUP BY    qf.PlanYearID
                       ,qf.PlanInfoID
                       ,qf.CPS
                       ,qf.QuarterID
                       ,qf.Component
                       ,qf.ReportingCategory;

            --Sum up the aggregation metric for the prior year across all crosswalked plans
            IF (SELECT  OBJECT_ID ('tempdb..#FromAggMetricSum')) IS NOT NULL
                DROP TABLE #FromAggMetricSum;
            SELECT      PlanYearID
                       ,PlanInfoID
                       ,CPS
                       ,QuarterID
                       ,Component
                       ,ReportingCategory
                       ,SUM (FromAggregationValue) AS FromAggregationValue
            INTO        #FromAggMetricSum
            FROM        #QuarterlyFactors
            GROUP BY    PlanYearID
                       ,PlanInfoID
                       ,CPS
                       ,QuarterID
                       ,Component
                       ,ReportingCategory;

            --Divide by total aggregation metric for crosswalked plans
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyDivide')) IS NOT NULL
                DROP TABLE #QuarterlyDivide;
            SELECT      qf.FromPlanYearID
                       ,qf.FromPlanInfoID
                       ,qf.FromCPS
                       ,qf.PlanYearID
                       ,qf.PlanInfoID
                       ,qf.CPS
                       ,qf.QuarterID
                       ,qf.Component
                       ,qf.ReportingCategory
                       ,dbo.Trend_fnSafeDivide (
                        SUM (qf.FromCostRelativityFactor * qf.FromAggregationValue)
                       ,am.FromAggregationValue
                       ,AVG (qf.FromCostRelativityFactor)) AS FromCostRelativityFactor
                       ,dbo.Trend_fnSafeDivide (
                        SUM (qf.FromUseRelativityFactor * qf.FromAggregationValue)
                       ,am.FromAggregationValue
                       ,AVG (qf.FromUseRelativityFactor)) AS FromUseRelativityFactor
                       ,am.FromAggregationValue
                       ,qf.CostRelativityFactor
                       ,qf.UseRelativityFactor
                       ,qf.AggregationValue
            INTO        #QuarterlyDivide
            FROM        #QuarterlyFactors qf
            LEFT JOIN   #FromAggMetricSum am
                   ON am.PlanInfoID = qf.PlanInfoID
                      AND   am.QuarterID = qf.QuarterID
                      AND   am.Component = qf.Component
                      AND   am.ReportingCategory = qf.ReportingCategory
            GROUP BY    qf.FromPlanYearID
                       ,qf.FromPlanInfoID
                       ,qf.FromCPS
                       ,qf.PlanYearID
                       ,qf.PlanInfoID
                       ,qf.CPS
                       ,qf.QuarterID
                       ,qf.Component
                       ,qf.ReportingCategory
                       ,qf.FromAggregationValue
                       ,am.FromAggregationValue
                       ,qf.CostRelativityFactor
                       ,qf.UseRelativityFactor
                       ,qf.AggregationValue;

            --Final quarterly table
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyFinal')) IS NOT NULL
                DROP TABLE #QuarterlyFinal;
            SELECT      DISTINCT
                        qd.PlanInfoID
                       ,qd.CPS
                       ,qd.PlanYearID
                       ,qd.ReportingCategory
                       ,qd.Component
                       ,qd.QuarterID
                       ,SUM (qd.FromCostRelativityFactor) AS FromCostRelativityFactor
                       ,SUM (qd.FromUseRelativityFactor) AS FromUseRelativityFactor
                       ,qd.FromAggregationValue AS FromAggregationValue
                       ,qd.CostRelativityFactor
                       ,qd.UseRelativityFactor
                       ,qd.AggregationValue AS AggregationValue
            INTO        #QuarterlyFinal
            FROM        #QuarterlyDivide qd
            GROUP BY    qd.PlanInfoID
                       ,qd.CPS
                       ,qd.PlanYearID
                       ,qd.ReportingCategory
                       ,qd.Component
                       ,qd.QuarterID
                       ,qd.FromAggregationValue
                       ,qd.CostRelativityFactor
                       ,qd.UseRelativityFactor
                       ,qd.AggregationValue;

            -- Replace with avg if aggmetric = 0 
            IF (SELECT  OBJECT_ID ('tempdb..#QuarterlyReplace')) IS NOT NULL
                DROP TABLE #QuarterlyReplace;
            SELECT      qf.PlanInfoID
                       ,qf.CPS
                       ,qf.PlanYearID
                       ,qf.ReportingCategory
                       ,qf.Component
                       ,qf.QuarterID
                       ,CASE WHEN qr.FromAggregationValue = 0 THEN qr.FromCostRelativityFactor
                             ELSE qf.FromCostRelativityFactor END AS FromCostRelativityFactor
                       ,CASE WHEN qr.FromAggregationValue = 0 THEN qr.FromUseRelativityFactor
                             ELSE qf.FromUseRelativityFactor END AS FromUseRelativityFactor
                       ,qf.FromAggregationValue
                       ,CASE WHEN qr.AggregationValue = 0 THEN qr.CostRelativityFactor ELSE qf.CostRelativityFactor END AS CostRelativityFactor
                       ,CASE WHEN qr.AggregationValue = 0 THEN qr.UseRelativityFactor ELSE qf.UseRelativityFactor END AS UseRelativityFactor
                       ,qf.AggregationValue
            INTO        #QuarterlyReplace
            FROM        #QuarterlyFinal qf
            LEFT JOIN   #QuarterlyRollup qr
                   ON qr.PlanInfoID = qf.PlanInfoID
                      AND   qr.QuarterID = qf.QuarterID
                      AND   qr.Component = qf.Component
                      AND   qr.ReportingCategory = qf.ReportingCategory;


            -----------------------------------------------
            -- 6. Delete and Insert Annual Trend Results --
            ---------------------------------------------
            DELETE  FROM dbo.Trend_HistProcess_CalcIsPlanLevel
            WHERE   (PlanInfoID IN (SELECT  PlanInfoID FROM #PlanList));

            IF (SELECT  OBJECT_ID ('tempdb..#Output')) IS NOT NULL DROP TABLE #Output;
            SELECT      qr.PlanInfoID
                       ,qr.PlanYearID AS TrendYearID
                       ,1 AS RateType
                       ,qr.ReportingCategory
                       ,qr.Component
                       ,CASE WHEN SUM (qr.CostRelativityFactor) IS NULL THEN 0
                             ELSE
                                 dbo.Trend_fnSafeDivide (
                                 AVG (qr.CostRelativityFactor), AVG (qr.FromCostRelativityFactor), 1) - 1 END AS CostAdjustment
                       ,CASE WHEN SUM (qr.UseRelativityFactor) IS NULL THEN 0
                             ELSE
                                 dbo.Trend_fnSafeDivide (AVG (qr.UseRelativityFactor), AVG (qr.FromUseRelativityFactor), 1)
                                 - 1 END AS UseAdjustment
                       ,@XLastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            INTO        #Output
            FROM        #QuarterlyReplace qr
            GROUP BY    qr.PlanInfoID
                       ,qr.PlanYearID
                       ,qr.ReportingCategory
                       ,qr.Component;

            INSERT INTO dbo.Trend_HistProcess_CalcIsPlanLevel
                (PlanInfoID
                ,TrendYearID
                ,RateType
                ,ReportingCategory
                ,Component
                ,CostAdjustment
                ,UseAdjustment
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  PlanInfoID
                   ,TrendYearID
                   ,RateType
                   ,ReportingCategory
                   ,Component
                   ,CostAdjustment
                   ,UseAdjustment
                   ,LastUpdateByID
                   ,LastUpdateDateTime
            FROM    #Output;

            COMMIT TRANSACTION transactionMain;

        END TRY

        BEGIN CATCH
            IF (@@TranCount > @tranCount)
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE ();
                    ROLLBACK TRANSACTION transactionMain;
                END;
        END CATCH;

        -- Write to log
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@XLastUpdateByID
               ,GETDATE ();

    END;
GO
