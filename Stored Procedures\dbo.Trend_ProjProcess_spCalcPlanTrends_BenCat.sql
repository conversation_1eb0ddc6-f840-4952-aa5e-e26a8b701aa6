/****** Object:  StoredProcedure [dbo].[Trend_ProjProcess_spCalcPlanTrends_BenCat]    Script Date: 9/10/2021 9:42:26 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcPlanTrends_BenCat
--
-- CREATOR: Andy Blink
--
-- CREATED DATE: APR-08-2020
--
-- DESCRIPTION: This procedure rolls up multiple benefit category level actuarial adjustments to one adjustment at the PlanInfoID-TrendYearID-RateType-BenCat level
--              
-- PARAMETERS:
--  Input  :	@PlanList
--				@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_ProjProcess_CalcPlanAdjmt_BenCat
--					LkpIntPlanYear
--					
--          Write:  Trend_ProjProcess_CalcPlanTrends_BenCat
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- APR-08-2020      1           Initial Version                                                     Andy Blink
-- APR-23-2020      2           Replace BenefitCategoryID with BenefitCategory                      Manisha Tyagi
-- MAY-06-2020		3			Replace BenefitCategory with BenefitCategoryID						Craig Nielsen
-- NOV-19-2020      4           Include NOLOCK & ROWLOCK                                            Manisha Tyagi
-- MAR-10-2021		5			Handle -1 adjustments												Jake Lewis
-- AUG-08-2021		6			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's		    Franklin Fu
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcPlanTrends_BenCat]
@PlanInfoIDList  VARCHAR(MAX) = NULL
,@LastUpdateByID CHAR(7)

AS

    BEGIN

        SET NOCOUNT ON;
        -- Declare variables
        DECLARE @BidYear INT;
        SET @BidYear = (SELECT  PlanYearID
                        FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                        WHERE   IsBidYear = 1);

        DECLARE @LastUpdateDateTime DATETIME;
        SET @LastUpdateDateTime = GETDATE ();


        -- Plan Info
        IF (SELECT  OBJECT_ID ('tempdb..#PlanInfo')) IS NOT NULL
            DROP TABLE #PlanInfo;
        SELECT  DISTINCT
                PlanInfoID
               ,CPS
               ,PlanYear AS PlanYearID
        INTO    #PlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear
                AND IsHidden = 0
                AND IsOffMAModel = 'No'
                AND Region NOT IN ('Unmapped')
                AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@PlanInfoIDList, ',', 1) )
                     OR @PlanInfoIDList IS NULL);


        -- Delete from and insert into final output table
        DELETE  FROM dbo.Trend_ProjProcess_CalcPlanTrends_BenCat WITH (ROWLOCK)
        WHERE   (PlanInfoID IN (SELECT  DISTINCT PlanInfoID FROM #PlanInfo));
        INSERT INTO dbo.Trend_ProjProcess_CalcPlanTrends_BenCat WITH (ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,TrendYearID
            ,RateType
            --,BenefitCategory -- CN 5/6/2020
            ,BenefitCategoryID
            ,ComponentReporting
            ,CostAdjustment
            ,UseAdjustment
            ,LastUpdateByID
            ,LastUpdateDateTime)

        SELECT      adj.PlanInfoID
                   ,adj.CPS
                   ,adj.PlanYearID
                   ,adj.TrendYearID
                   ,adj.RateType
                 --,BenefitCategory -- CN 5/6/2020
                   ,adj.BenefitCategoryID
                   ,i.ComponentReporting
                 --,ROUND (EXP (SUM (LOG (1 + adj.CostAdjustment))) - 1, 6) AS 'CostAdjustment' -- JL 03/10/2021
                 --,ROUND (EXP (SUM (LOG (1 + adj.UseAdjustment))) - 1, 6) AS 'UseAdjustment'   --JL 03/10/2021
                   ,CASE WHEN MIN (adj.CostAdjustment) = -1 THEN -1
                         ELSE ROUND (EXP (SUM (LOG (NULLIF(1 + adj.CostAdjustment,0)))) - 1, 6) END AS CostAdjustment   --Combine multiplicative adjustments
                   ,CASE WHEN MIN (adj.UseAdjustment) = -1 THEN -1
                         ELSE ROUND (EXP (SUM (LOG (NULLIF(1 + adj.UseAdjustment,0)))) - 1, 6) END AS UseAdjustment     --Combine multiplicative adjustments
                   ,@LastUpdateByID
                   ,@LastUpdateDateTime
        FROM        dbo.Trend_ProjProcess_CalcPlanAdjmt_BenCat adj WITH (NOLOCK)
       INNER JOIN   dbo.Trend_SavedComponentInfo i WITH (NOLOCK)
               ON i.Component = adj.AdjustmentType
        WHERE       adj.PlanInfoID IN (SELECT   DISTINCT PlanInfoID FROM #PlanInfo)
        GROUP BY    adj.PlanInfoID
                   ,adj.CPS
                   ,adj.PlanYearID
                   ,adj.TrendYearID
                   ,adj.RateType
                    --,BenefitCategory -- CN 5/6/2020
                   ,adj.BenefitCategoryID
                   ,i.ComponentReporting;

    END;
GO


