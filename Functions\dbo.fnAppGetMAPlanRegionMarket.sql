SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:fnAppGetMAPlanRegionMarket
--
-- CREATOR: Christian Cofie
--
-- CREATED DATE: 2007-Aug-23
-- HEADER UPDATED: 2010-Dec-06
--
-- DESCRIPTION: This function returns the bid year Active Version, MarketID and RegionID for a given ForecastID
--
-- PARAMETERS:
--  Input:
--      @ForecastID
--  Output:
--
-- TABLES: 
--  Read:
--      LkpProductType
--      SavedDivisionInfo
--      SavedMarketInfo
--      SavedPlanHeader
--      SavedRegionInfo
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------
-- DATE			VERSION		    CHANGES MADE						                                                DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------------------
-- Aug-23-2007		1			Initial Version							                                            Christian Cofie
-- Oct-22-2007		2			Added @PlanYearID parameter					                                        Christian
-- Aug-06-2008      3           Added PlanTypeName to the Select list                                               Brian Lake
-- 2009-Apr-22      4           Pull PlanName from PerExtContractNumberPlanIDDetail instead of                      Lawrence Choi 
--									SavedPlanHeader
-- 2010-Mar-02		5			Added IsHidden in the where clause													Joe Casey
-- 2010-Jul-27		6			Moved to 2012																		Joe Casey
-- 2010-Aug-11		7			Removed PlanVersion																	Joe Casey
-- 2010-Sep-29      8           Changed reference from LkpIntMarketHeader to SavedMarketHeader                      Michael Siekerka
-- 2010-Dec-06      9           Added Division																		Joe Casey
-- 2011-Mar-01		10			Added PlanDescription																Trevor Mahoney
-- 2013-Oct-04		11			Modified to Include Segment ID														Anubhav Mishra
-- 2017-Jun-20		12			Removed PerExtContractNumberPlanIDDetail and changed PlanName to pull from			Chris Fleming
--									new column in SavedPlanHeader	
-- 2018-Apr-24		13			Modified LkpExtCMSPlanType for new UI table modifications							Jordan Purdue								
-- 2019-JUl-07		14			Made changes for market, division, region table.									Kritika Singh	
-- 2022-Jun-29		15			Replanced IsFinal with 0															Aleksandar Dimitrijevic
-- ----------------------------------------------------------------------------------------------------------------------------------

CREATE  FUNCTION  [dbo].[fnAppGetMAPlanRegionMarket] 
(
    @ForecastID INT
)
RETURNS TABLE AS  

RETURN

(
SELECT TOP 1
    sph.ForecastID,
    sph.PlanTypeID, 
    cms.ProductType,
    sph.ContractNumber, 
    sph.PlanID, 
    sph.SegmentId,       ---Added  Segment ID
    sph.PlanName,
    IsFinal = CAST(0 AS BIT), 
    smh.ActuarialMarketID,
    smh.ActuarialMarket,
    smh.ActuarialRegionID,
    srh.ActuarialRegion,
    srh.ActuarialDivisionID,
    sdh.ActuarialDivision,
    sph.PlanDescription
FROM SavedPlanHeader sph
INNER JOIN SavedMarketInfo smh
    ON smh.ActuarialMarketID = sph.MarketID
INNER JOIN SavedRegionInfo srh
    ON srh.ActuarialRegionID = smh.ActuarialRegionID
INNER JOIN LkpProductType cms
    ON sph.PlanTypeID = cms.ProductTypeID
    AND cms.IsEnabled = 1
LEFT JOIN SavedDivisionInfo sdh
    ON sdh.ActuarialDivisionID = srh.ActuarialDivisionID
WHERE sph.ForecastID = @ForecastID
	AND sph.IsHidden = 0
)

GO
