SET QUOTED_IDENTIFIER ON
GO
SET AN<PERSON>_NULLS ON
GO


-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_NormProcess_spCalcCombined
--
-- CREATOR: Jake Lewis
--
-- CREATED DATE: JAN-22-2020
--
-- DESCRIPTION: Combine historic component trends into one table to prepare for normalized trend calculation.
--              
--              
-- PARAMETERS:
--  Input  : NONE
--
--  Output : NONE
--
-- TABLES : Read :  Trend_NormProcess_CalcNotPlanLevel
--					Trend_NormProcess_CalcIsPlanLevel_Population
--					Trend_NormProcess_CalcIsPlanLevel_OutlierClaims
--					Trend_NormProcess_CalcIsPlanLevel_InducedUtilization
--					Trend_NormProcess_CalcIsPlanLevel_Contractual
--					Trend_NormProcess_CalcIsPlanLevel_CMSReimb
--
--          Write:  Trend_NormProcess_CalcCombined
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- JAN-22-2020      1           Initial Version                                                     Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_NormProcess_spCalcCombined]
@LastUpdateByID CHAR(7) 
AS
    BEGIN
        SET NOCOUNT ON;

        --Delete and insert trends into the final output table
        DELETE  FROM dbo.Trend_NormProcess_CalcCombined;
        INSERT INTO dbo.Trend_NormProcess_CalcCombined
				(ComponentVersionID
               ,Component
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,LastUpdateByID
               ,LastUpdateDateTime
			   ,ID)
        SELECT  ComponentVersionID
               ,Component
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,LastUpdateByID
               ,LastUpdateDateTime
               ,NEWID()
        FROM    dbo.Trend_NormProcess_CalcNotPlanLevel
        UNION
        SELECT  ComponentVersionID
               ,Component
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,LastUpdateByID
               ,LastUpdateDateTime
               ,NEWID()
        FROM    dbo.Trend_NormProcess_CalcIsPlanLevel_Population
        UNION
        SELECT  ComponentVersionID
               ,Component
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,LastUpdateByID
               ,LastUpdateDateTime
               ,NEWID()
        FROM    dbo.Trend_NormProcess_CalcIsPlanlevel_OutlierClaims
        UNION
        SELECT  ComponentVersionID
               ,Component
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
               ,LastUpdateByID
               ,LastUpdateDateTime
               ,NEWID()
        FROM    dbo.Trend_NormProcess_CalcIsPlanLevel_InducedUtilization
        UNION
        SELECT  ComponentVersionID
               ,Component
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
        		 ,LastUpdateByID
               ,LastUpdateDateTime
               ,NEWID()
        FROM    dbo.Trend_NormProcess_CalcIsPlanLevel_Contractual
        UNION
        SELECT  ComponentVersionID
               ,Component
               ,PlanTypeGranularity
               ,PlanTypeGranularityValue
               ,PlanYearID
               ,QuarterID
               ,ActuarialRegion
               ,ReportingCategory
               ,CostAdjustment
               ,UseAdjustment
        		 ,LastUpdateByID
               ,LastUpdateDateTime
               ,NEWID()
        FROM    dbo.Trend_NormProcess_CalcIsPlanLevel_CMSReimb
        ;

    END;
GO
