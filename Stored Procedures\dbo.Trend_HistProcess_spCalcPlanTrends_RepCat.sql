SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_HistProcess_spCalcPlanTrends_RepCat
--
-- CREATOR: Tanvi <PERSON>na
--
-- CREATED DATE: SEP-08-2020
--
-- DESCRIPTION:  Historic trends for plan level and non plan-level components are combined together at the reporting category level for reporting purposes.
--              
--              
-- PARAMETERS:
--  Input  :	@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  LkpIntBenefitCategory
--                  Trend_CalcFileSync_Annual
--					Trend_HistProcess_CalcIsPlanLevel
--					Trend_HistProcess_CalcNotPlanLevel_PlanMappingFinal
--					
--          Write:  Trend_HistProcess_CalcPlanTrends_RepCat
--					Trend_Reporting_Log
--                  
--
-- VIEWS: Read: vwPlanInfo
--
-- FUNCTIONS: Read: NONE
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER   
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- SEP-08-2020      1           Initial Version																								Tanvi Khanna
-- OCT-19-2020		2			Fixed issues with sonar qube																				Deepali Mittal
-- OCT-20-2020		3			Updated non-plan level to pull all from historic mapping													Tanvi Khanna
-- DEC-10-2020		4			Name changes for the SP and some of the referenced tables:													Jake Lewis
--								NEW SP = Trend_HistProcess_spCalcPlanTrends_RepCat, OLD SP = Trend_Reporting_spCalcPlanHistoricTrends_RepCat
--								NEW = Trend_HistProcess_CalcIsPlanLevel_Incurred, OLD = Trend_Reporting_CalcAllPlanLevelHistoricTrends
--								NEW = Trend_HistProcess_CalcNotPlanLevel_PlanMappingFinal, OLD = Trend_Reporting_CalcNotPlanLevel_HistoricPlanMapping
--								NEW = Trend_HistProcess_CalcPlanTrends_RepCat, OLD = Trend_Reporting_CalcPlanHistoricTrends_RepCat
-- DEC-30-2020		5			Avoid attempting to insert NULLs for CostAdjustment, UseAdjustment											Jake Lewis
--								Change table Trend_HistProcess_CalcIsPlanLevel_Incurred to Trend_HistProcess_CalcIsPlanLevel
-- MAR-15-2021		6			Code adjustments to improve efficiency and decrease runtime													Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


CREATE PROCEDURE [dbo].[Trend_HistProcess_spCalcPlanTrends_RepCat]

@LastUpdateByID CHAR(13)

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

		DECLARE @tranCount INT = @@TranCount;

        BEGIN TRY

            BEGIN TRANSACTION transactionMain;

            -- Declare variables
            DECLARE @startTime DATETIME = GETDATE ();
            DECLARE @errorMsg VARCHAR(500);

            -- Reporting category list
            IF (SELECT  OBJECT_ID ('tempdb..#ReportingCategory')) IS NOT NULL
                DROP TABLE #ReportingCategory;
            SELECT  DISTINCT
                    ReportingCategory
            INTO    #ReportingCategory
            FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
            WHERE   ReportingCategory IS NOT NULL
                    AND ReportingCategory NOT IN ('Part B Rx Pharmacy');

            -- Delete from table
            DELETE  FROM dbo.Trend_HistProcess_CalcPlanTrends_RepCat WHERE  1 = 1;

            -- Final output: plan level trends
            INSERT INTO dbo.Trend_HistProcess_CalcPlanTrends_RepCat
                (PlanInfoID
                ,ReportingCategory
                ,Component
                ,PlanTypeGranularity
                ,PlanTypeGranularityValue
                ,CostAdjustment
                ,UseAdjustment
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT  PlanInfoID
                   ,ReportingCategory
                   ,Component
                   ,'Plan' AS PlanTypeGranularity
                   ,NULL AS PlanTypeGranularityValue
                   ,ISNULL (CostAdjustment, 0) AS CostAdjustment
                   ,ISNULL (UseAdjustment, 0) AS UseAdjustment
                   ,@LastUpdateByID AS LastUpdateByID
                   ,GETDATE () AS LastUpdateDateTime
            FROM    dbo.Trend_HistProcess_CalcIsPlanLevel WITH (NOLOCK);

            -- Final output: non plan level trends
            INSERT INTO dbo.Trend_HistProcess_CalcPlanTrends_RepCat
                (PlanInfoID
                ,ReportingCategory
                ,Component
                ,PlanTypeGranularity
                ,PlanTypeGranularityValue
                ,CostAdjustment
                ,UseAdjustment
                ,LastUpdateByID
                ,LastUpdateDateTime)
            SELECT      a.PlanInfoID
                       ,b.ReportingCategory
                       ,a.Component
                       ,a.PlanTypeGranularity
                       ,a.PlanTypeGranularityValue
                       ,ISNULL (c.CostAdjustment, 0) AS CostAdjustment
                       ,ISNULL (c.UseAdjustment, 0) AS UseAdjustment
                       ,@LastUpdateByID AS LastUpdateByID
                       ,GETDATE () AS LastUpdateDateTime
            FROM        dbo.Trend_HistProcess_CalcNotPlanLevel_PlanMappingFinal a WITH (NOLOCK)
           CROSS JOIN   #ReportingCategory b
            LEFT JOIN   dbo.Trend_CalcFileSync_Annual c WITH (NOLOCK)
                   ON c.Component = a.Component
                      AND   c.PlanYearID = a.PlanYearID
                      AND   c.PlanTypeGranularityValue = a.PlanTypeGranularityValue
                      AND   c.ReportingCategory = b.ReportingCategory;

            COMMIT TRANSACTION transactionMain;

        END TRY

		-- CATCH block will roll back the transaction if an error occurs in the TRY block
        BEGIN CATCH
            IF (@@TranCount > @tranCount) -- If transactionMain was not committed in the TRY block (i.e. an error occurred), there will be an extra open transaction.
                BEGIN
                    SET @errorMsg = ERROR_MESSAGE (); -- Get error message to write to the log	
                    ROLLBACK TRANSACTION transactionMain; -- Undo all data modifications that occurred inside of transactionMain
                END;
        END CATCH;

        -- Write to log
        INSERT INTO dbo.Trend_Reporting_Log
            (StartDateTime
            ,Source
            ,LockStatus
            ,ErrorMessage
            ,RunTime
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT  @startTime
               ,OBJECT_NAME (@@ProcId)
               ,NULL
               ,@errorMsg
               ,GETDATE () - @startTime
               ,@LastUpdateByID
               ,GETDATE ();

    END;


GO
