SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetCMSContractQualityStars]    ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: 
--
-- CREATOR: <PERSON><PERSON> Chen 
--
-- CREATED DATE: Jan-20-2011
-- HEADER UPDATED: Jan-20-2011
--
-- DESCRIPTION: Returns CMS star ratings for a specific contract.
--
-- PARAMETERS:
--	Input:
--	Output:
--
-- TABLES:
--	Read:
--			PerExtCMSContractQualityStars
--			Saved<PERSON><PERSON><PERSON>eader
--			SavedPlanDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- Jan-20-2011      1           Initial Version.													Jiao Chen
-- Feb-07-2011		2			Added IsNew to the return table										Jiao Chen
-- Feb-14-2011		3			Allows Null in the return table										Jiao Chen
-- Mar-17-2011		4			Returns blank if plan is unavailable form ratebook					Jiao Chen
-- Aug-03-2023		5			Added NOLOCK, Internal parameter, table schema					    Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetCMSContractQualityStars]
(
    @ForecastID INT = NULL
) 
RETURNS @Result TABLE
(
	PartCStars DECIMAL (2,1),
	PartDStars DECIMAL (2,1),
	CombinedStars DECIMAL (2,1),
	IsLowEnrollment TINYINT,
	IsNew TINYINT
)
AS
BEGIN

DECLARE @XForecastID INT = @ForecastID 

	IF EXISTS ( SELECT 1
				FROM dbo.SavedPlanHeader SPH  WITH (NOLOCK)
				INNER JOIN dbo.PerExtCMSContractQualityStars CQS WITH (NOLOCK)
					ON CQS.ContractNumber = SPH.ContractNumber
				WHERE SPH.ForecastID = @XForecastID)
		BEGIN
			INSERT @Result
				SELECT
					CQS.PartCStars,
					CQS.PartDStars,
					CQS.CombinedStars,
					CQS.IsLowEnrollment,
					CQS.IsNew
				FROM dbo.SavedPlanHeader SPH WITH (NOLOCK)
				INNER JOIN dbo.PerExtCMSContractQualityStars CQS WITH (NOLOCK)
					ON CQS.ContractNumber = SPH.ContractNumber
				WHERE SPH.ForecastID = @XForecastID
		END

	RETURN
END
GO
