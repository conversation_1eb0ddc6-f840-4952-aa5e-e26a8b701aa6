SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------                
-- PROCEDURE NAME: spAppMSBSelector              
--                
-- AUTHOR: <PERSON>               
--                
-- CREATED DATE: 2023-SEP-20                
--                
-- DESCRIPTION: Procedure responsible for selecting MS<PERSON> for a plan based on comparison criteria.
-- Procedure will be used by MSB UI page to display best fit
-- Procedure will also be used by MSB Batch import process.
--                
-- PARAMETERS:                
-- Input:                
--	@InputType; 0 = UI Screen; 1	 = MSB Selections Import
--	@SPVForecastID; Forecastid from single plan view           
--	@UserID ;
-- OUTPUT:
--	@ValidationMessage ;
--
-- Tables:
-- Read:                
--	MsbSelectionsUpload_Stage
--	lkpintaddedbenefittype
--	LkpIntAddedBenefitExpenseDetail
--	LkpProductType
--	LkpExtSNPType
--	LkpExtCMSStateCounty
--	LkpExtCMSStateTerritory
--	SavedPlanStateCountyDetail
--	SavedPlanMemberMonthDetail
--	SavedPlanAssumptions
--	savedmarketinfo
--	savedregioninfo        
--              
-- Write:                
--	SavedPlanAddedBenefits
--	SavedForecastSetup                
-- VIEWS:                
--	SavedPlanHeader        
-- FUNCTIONS:                
--	dbo.fnGetBidYear   
--             
-- STORED PROCS:                
--                  
-- $HISTORY                 
-- -------------------------------------------------------------------------------------------------------------------------                
-- DATE			VERSION		CHANGES MADE														DEVELOPER                  
-- -------------------------------------------------------------------------------------------------------------------------                
-- 2023-Sep-20		1		Initial Version														Adam Gilbert  
-- 2023-Nov-17		2		Added Internal Parameter, object schema								Sheetal Patil
-- 2024-Jul-02		3		Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table	Surya Murthy
-- 2025-Feb-21      	4       	Adding more filter in the section 5 of the Compare MSB versus PlanI Info 
--                          		to prohibit MAAUI MSB Selection process from  using South Central 
--                          		instead of Central region                                            Abraham Ndabian
-- -------------------------------------------------------------------------------------------------------------------------    

CREATE  PROCEDURE [dbo].[spAppMSBSelector](
	@InputType int,			 -- 0 = UI, 1 = Import
	@SPVForecastID int, --Only used with InputType 0
	@UserID varchar(7), --Only used with InputType 1
	@ValidationMessage varchar(max) null output  
) 
as  
SET NOCOUNT ON;	
BEGIN TRY

DECLARE @XInputType INT = @InputType,
		@XSPVForecastID INT = @SPVForecastID,
		@XUserID varchar(7) = @UserID 


DROP TABLE IF Exists #primarystate;
DROP TABLE IF Exists #planinfo;
DROP TABLE IF Exists #MSBselections;
DROP TABLE IF Exists #results ;
DROP TABLE IF Exists #MSBSelectables;

--Establish MSBSelections Temp Table, based on input type
CREATE TABLE #MSBSelections
(
	forecastid int,
	msbcode varchar(6),
	Addedbenefitname varchar(200),
	isoverride int,
)

--Establish Temp table to hold results values
CREATE TABLE #results
(
	forecastid int,
	msbcode varchar(6),
	AddedBenefitTypeIDOutput int,
	AddedbenefitnameOutput varchar(200),
	isoverride int,
	score decimal(10,2),
	ranking int
)

--Run comparison for each possible MSB CODE for a given plan. To be used by drop down selector.
IF @XInputType = 0
	BEGIN
		INSERT INTO #MSBSelections (ForecastID, MSBCode,AddedBenefitName,IsOverride) 
		SELECT distinct @XSPVForecastID as forecastid ,
			left(AddedBenefitName,6) msbcode,
			Addedbenefitname,
			0 as isoverride 
		FROM  dbo.lkpintaddedbenefittype WITH (NOLOCK)
		WHERE isenabled=1 AND bidservicecatid <> 35 
	END

--Run comparison for user input data. To be used by Batch Import. Will select input from a staging table.
IF @XInputType = 1 
	BEGIN

	INSERT INTO #MSBSelections (ForecastID, MSBCode,AddedBenefitName,IsOverride) 
	SELECT ForecastID, MSBCode, AddedBenefitName, IsOverride 
	FROM dbo.MsbSelectionsUpload_Stage WITH (NOLOCK)
	WHERE UserID = @XUserID;

	END

/*Raise Error if no input found*/

---------------------------------------------
-- 2) Build list of valid selectables. Cleanse data.
---------------------------------------------	
select AddedBenefitCode
	,ed.AddedBenefitTypeID
	,ed.AddedBenefitName
	,replace(replace(AddedBenefitProductType , 'N/A', ''), 'all', '') AddedBenefitProductType 
	,replace(replace(AddedBenefitSNPType , 'N/A', ''), 'all', '') AddedBenefitSNPType 
	,replace(replace(StateMSBGranularity , 'N/A', ''), 'all', '') StateMSBGranularity 
	,replace(replace(PlanMSBGranularity , 'N/A', ''), 'all', '') PlanMSBGranularity 
	,replace(replace(RegionMSBGranularity , 'N/A', ''), 'all', '') RegionMSBGranularity 
	,replace(replace(MarketMSBGranularity, 'N/A', ''), 'all', '') MarketMSBGranularity
	,replace(replace(ExclusionSNP , 'N/A', ''), 'None', '') ExclusionSNP 
	,replace(replace(ExclusionsState , 'N/A', ''), 'None', '') ExclusionsState 
	,replace(replace(ExclusionsPlan , 'N/A', ''), 'None', '') ExclusionsPlan 
	,replace(replace(ExclusionsRegion , 'N/A', ''), 'None', '') ExclusionsRegion 
	,replace(replace(ExclusionsMarket, 'N/A', ''), 'None', '') ExclusionsMarket
	into #MSBSelectables
	from dbo.LkpIntAddedBenefitExpenseDetail ed WITH (NOLOCK)
	join dbo.LkpIntAddedBenefitType bt WITH (NOLOCK)
		on ed.AddedBenefitTypeID = bt.AddedBenefitTypeID
	where bt.IsEnabled=1 AND bt.BidServiceCatID <> 35;

-------------------------------------------
--3) PRIMARY STATE
--	 state with the most membership for each plan
---------------------------------------------
WITH PlansMembershipByState AS (
        SELECT 
			   ph.planyearid,
               ph.forecastid,
               st.StateTerritoryShortName StateName,
               SUM(mmd.MemberMonths) MemberMonths
			FROM
            --plans/regions----------
            dbo.SavedPlanHeader ph WITH (NOLOCK)
            --states/counties----------
            JOIN dbo.SavedPlanStateCountyDetail scd WITH (NOLOCK)
                ON ph.forecastid = scd.forecastid
            JOIN dbo.LkpExtCMSStateCounty sc WITH (NOLOCK)
                ON sc.CountyCode = scd.CountyCode
                AND sc.StateTerritoryID = scd.StateTerritoryID
            JOIN dbo.LkpExtCMSStateTerritory st WITH (NOLOCK)
                ON st.StateTerritoryID = scd.StateTerritoryID
            --members----------
            JOIN dbo.SavedPlanMemberMonthDetail mmd WITH (NOLOCK)
                ON mmd.CountyCode = scd.CountyCode
                AND mmd.StateTerritoryID = scd.StateTerritoryID
                AND mmd.forecastid = ph.forecastid
            JOIN dbo.SavedPlanAssumptions pa WITH (NOLOCK)
                ON pa.forecastid = ph.forecastid
        WHERE scd.IsCountyExcludedFromBPTOutput = 0
              --AND ph.IsFiledPlan = 1
              --AND ph.IsHidden = 0
              --AND ph.IsLiveIndex = 1
              AND mmd.DemogIndicator IN (1 , 2)
			  AND ph.ForecastID IN (SELECT forecastid FROM #MSBSelections)
		GROUP BY  ph.PlanYearID,ph.forecastid, st.StateTerritoryShortName
),
--Query to apply ranking by membermonths. Rank1 displays primary plan membership.
RankStateMembership AS
(
	SELECT planyearid,forecastid,StateName,MemberMonths,
	ROW_NUMBER() OVER (PARTITION BY forecastid ORDER BY MemberMonths DESC) rownum
	FROM PlansMembershipByState
)
SELECT planyearid,ps.forecastid,StateName,MemberMonths,rownum
into #primarystate 
FROM RankStateMembership ps
WHERE rownum = 1; 

--select * from #primarystate --debug only
---------------------------------------------
--4) PLAN INFO
---------------------------------------------
select 
	ph.forecastid,
	ContractNumber + '-' + PlanID + '-' + SegmentID CPS, 
	rh.actuarialregion, 
	rd.actuarialmarket, 
	pt.ProductType,
	st.SNPTypeName,
	ps.StateName PrimaryState,
	st.SNPShortname as SNPtype1, 
	stAlt.SNPShortname as SNPtype2
into #planinfo
from dbo.SavedPlanHeader ph WITH (NOLOCK)
JOIN dbo.LkpProductType pt WITH (NOLOCK)
	ON pt.ProductTypeID = ph.PlanTypeID
JOIN dbo.LkpExtSNPType st WITH (NOLOCK)
	ON st.SNPTypeID = ph.SNPTypeID
Join dbo.LkpExtSNPType stAlt WITH (NOLOCK)
	ON st.snpalttypeid = stAlt.snptypeid
JOIN dbo.savedmarketinfo rd WITH (NOLOCK)
	on ph.MarketID = rd.actuarialMarketID
JOIN dbo.savedregioninfo rh WITH (NOLOCK)
	on rd.actuarialregionid = rh.actuarialregionid
join #primarystate ps
	on ph.ForecastID = ps.ForecastID
WHERE ph.ForecastID IN (SELECT forecastid FROM #MSBSelections)
;
------------------------
/* 5) Build Comparisons
COMPARE MSB AND PLAN INFO
giving the 2nd SNP type half a point so it gets less weight than the 1st
replacing none/na/all with blanks so they are auto-matches
*/
------------------------
WITH CompareData AS (
	select 
		b.ForecastId,
		b.MSBCode,
		b.AddedBenefitName, 
		m.AddedBenefitTypeID as AddedBenefitTypeIDOutput,
		m.addedbenefitname as AddedBenefitNameOutput,
		0 AS IsOverride,
		CPS, actuarialregion, actuarialmarket, ProductType, SNPTypeName, PrimaryState, SNPtype1, SNPtype2, AddedBenefitCode,
		AddedBenefitProductType, AddedBenefitSNPType, StateMSBGranularity, PlanMSBGranularity, RegionMSBGranularity, MarketMSBGranularity 
		,ExclusionSNP, ExclusionsState, ExclusionsPlan, ExclusionsRegion, ExclusionsMarket
            ,case when len(m.AddedBenefitProductType) = 0
                        or P.ProductType in (select trim(value) from string_split(m.AddedBenefitProductType,','))
                  then 1 else 0 end productmatch
            ,case when len(m.AddedBenefitSNPType) = 0
                        or p.SNPType1  in (select trim(value) from string_split(m.AddedBenefitSNPType ,','))  
                  then 1 else 0 end SNPmatch1
            ,case when p.SNPType1 in (select trim(value) from string_split(m.ExclusionSNP ,','))  
                  then -1
                  when len(m.AddedBenefitSNPType) = 0
                        or p.SNPtype2 in (select trim(value) from string_split( m.AddedBenefitSNPType,','))  
                  then 0.5 else 0 end SNPmatch2
            ,case when p.PrimaryState in (select trim(value) from string_split(m.ExclusionsState ,','))  
                  then -1
                  when len(m.StateMSBGranularity) = 0
                        or p.PrimaryState in (select trim(value) from string_split(m.StateMSBGranularity ,','))  
                  then 1 else 0 end statematch
            ,case when len(m.PlanMSBGranularity) = 0
                  or p.CPS in (select trim(value) from string_split(m.PlanMSBGranularity ,','))  
                  then 1 else 0 end planmatch
            ,case when p.ActuarialRegion in (select trim(value) from string_split(m.ExclusionsRegion ,','))  
                  then -1
                  when len(m.RegionMSBGranularity) = 0
                        or p.ActuarialRegion in (select trim(value) from string_split(m.RegionMSBGranularity ,','))  
                  then 1 else 0 end regionmatch
            ,case when p.ActuarialMarket in (select trim(value) from string_split(m.Exclusionsmarket ,','))  
                  then -1
                  when len(m.marketMSBGranularity) = 0
                        or p.ActuarialMarket in (select trim(value) from string_split(m.marketMSBGranularity ,','))  
                  then 1 else 0 end marketmatch
	from #planinfo p
	join #MSBselections b
		on p.ForecastID = b.ForecastID
	join #MSBSelectables m
		on b.MSBcode = m.AddedBenefitCode
	WHERE b.IsOverride = 0
),
/*Create a score based on the compared dataset*/
CalculateScore AS (
	select forecastID,
		MSBCode,
		AddedBenefitTypeIDOutput,
		AddedBenefitNameOutput,
		IsOverride,
		regionMSBgranularity,
		addedbenefitproducttype
		, productmatch + SNPmatch1 + SNPmatch2 
		  + statematch + planmatch + regionmatch + marketmatch 
		  as score
		  --, productmatch 
		  --, SNPmatch1 
		  --, SNPmatch2 
		  --, statematch 
		  --, planmatch 
		  --, regionmatch 
		  --, marketmatch
	from CompareData
),

/*  RANK OPTIONS
    sorting by length of region name to resolve false matches 
	on soft search (e.g. so that "Central West" doesn't match "Central") -- Discuss with MSB team to identify a btter tie breaker bc the current one is NOT working as expected 
	as Central & Midwest have the same length by Abe */
RankOptions as (
	select 
		forecastID,
		MSBCode,
		AddedBenefitTypeIDOutput,
		AddedBenefitNameOutput,
		IsOverride,
		regionMSBgranularity,
		addedbenefitproducttype,
		score,
		row_number() over (
							partition by forecastid, msbcode 
							order by score DESC
								,len(regionMSBgranularity) ASC
								,addedbenefitproducttype) ranking
	FROM CalculateScore
)
INSERT INTO #Results
select 
	forecastID,
	MSBCode,
	AddedBenefitTypeIDOutput,
	AddedBenefitNameOutput,
	IsOverride,
	score,
	ranking
FROM rankOptions 
WHERE ranking = 1

/**************
   Overrides
***************/
INSERT INTO #Results
SELECT DISTINCT
	sel.Forecastid, '' AS MSBCode, 
	abt.AddedBenefitTypeID,
	sel.AddedBenefitName,
	sel.isOverride, 
	0 AS score,
	1 AS ranking
FROM #MSBSelections sel
JOIN #MSBSelectables abt
ON sel.AddedBenefitName = abt.addedbenefitname
WHERE sel.isOverride =1 ;

/*Remove Duplicates from results
-- Handles if user inputs a MSBCode as a non-override AND the same selection as an override.
*/
WITH cte as (
	SELECT Forecastid, AddedBenefitTypeIDOutput,
	row_number() over (Partition By ForecastID, AddedBenefitTypeIDOutput order by isOverride desc) AS rn
	FROM #Results 
)
delete FROM cte WHERE rn>1

---------------
--	Return Data UI
---------------
IF @XInputType = 0
	BEGIN
	SELECT ForecastID, MSBCode, AddedBenefitTypeIDOutput, AddedBenefitNameOutput, IsOverride FROM #Results
	END

----------------------------------
--	Proceed with Batch Import
----------------------------------
IF @XInputType = 1
	BEGIN
		---------------------------------------------
		-- VALIDATE INPUT
		---------------------------------------------	
		DECLARE @List Varchar(max) = null;
		SET @ValidationMessage = '';

		--Forecastid not found
		SET @list = null;
		SELECT @List = string_agg(cast(Forecastid AS varchar(max)),',') FROM #MSBSelections 
		WHERE forecastid NOT IN (SELECT forecastid FROM savedplanheader)
		IF @List IS not null
		BEGIN 
			SELECT @ValidationMessage = @ValidationMessage + 'Forecastid not found '+ @List +';'
		END;

		--MSBCode not found (no override)
		SET @list = null;
		SELECT @List = string_agg(cast(MSBCode AS varchar(max)),',') FROM #MSBSelections WHERE IsOverride = 0
		and MSBCode NOT IN (SELECT left(AddedBenefitName,6) FROM #MSBSelectables )
		IF @List IS not null
		BEGIN 
			SELECT @ValidationMessage =@ValidationMessage + 'MSBCode not found ' + @List+';'
		END;

		--AddedBenefitName not found (override)
		SET @list = null;
		SELECT @List = string_agg(cast(AddedBenefitName AS varchar(max)),',') FROM #MSBSelections WHERE IsOverride = 1
		and AddedBenefitName NOT IN (SELECT AddedBenefitName FROM #MSBSelectables )
		IF @List IS not null
		BEGIN 
			SELECT @ValidationMessage =@ValidationMessage +'MSB Name not found ' + @List+';'
		END;

		---------------------------------------------
		-- VALIDATE Results
		---------------------------------------------	
		SET @list = null;
		SELECT @List =  string_agg(concat_ws('-',ForecastID,MSBCode),',')
		FROM #results 
		WHERE IsOverride = 0
		GROUP BY ForecastID,MSBCode
		HAVING count(*) > 1

		IF @List IS NOT NULL
		BEGIN 
			SELECT @ValidationMessage =@ValidationMessage +'MSB Selector returned more than one value for ' + @List+';'
		END;

		SET @list = null;
		SELECT @List =  string_agg(concat_ws('-',ForecastID,AddedBenefitNameOutput),',')
		FROM #results 
		GROUP BY ForecastID,AddedBenefitNameOutput
		HAVING count(*) > 1

		IF @List IS NOT NULL
		BEGIN 
			SELECT @ValidationMessage =@ValidationMessage +'More than one record to be inserted for ' + @List+';'
		END;

		/*Return Error if validation fails*/
		IF @ValidationMessage <> ''
            BEGIN
			SELECT @ValidationMessage = '0:'+@ValidationMessage + 'Review and reimport dataset.'
                RAISERROR(@ValidationMessage, 16, 1);
        END;

		---------
		--LOAD
		---------
		DECLARE @LastUpdateDate datetime = Getdate()
		DECLARE @BidYear datetime = dbo.fnGetBidYear()

		BEGIN TRANSACTION 

		 --Remove existing MSB selections for imported plans
		 DELETE spad
		 FROM dbo.SavedPlanAddedBenefits spad 
		 JOIN #Results msb
		 on spad.forecastid = msb.forecastid
		 AND spad.BidServiceCatID <> 35 --exclude rprx

		 INSERT INTO dbo.SavedPlanAddedBenefits
				SELECT
					bt.PlanYearId,
					rs.ForecastID,
					bt.AddedBenefitTypeID,
					bt.AddedBenefitName,
					bt.INAddedBenefitDescription,
					bt.INAddedBenefitAllowed,
					bt.INAddedBenefitUtilization,
					bt.INAddedBenefitCostShare,
					bt.OONAddedBenefitDescription,
					bt.OONAddedBenefitAllowed,
					bt.OONAddedBenefitUtilization,
					bt.OONAddedBenefitCostShare,
					bt.BidServiceCatID,
					bt.IsValueAdded,
					bt.IsNetwork,
					0,  --IsEnabled,
					@XUserID,
					@LastUpdateDate,
					rs.IsOverride
				FROM #results rs
				JOIN dbo.LkpIntAddedBenefitType bt  WITH (NOLOCK)
				ON rs.AddedBenefitTypeIDOutput = bt.addedbenefittypeid
				WHERE 1=1
				AND IsEnabled=1

				--reprice plan flag
				UPDATE dbo.SavedForecastSetup
				SET IsToReprice = 1,
				LastUpdateByID =@XUserID,
				LastUpdateDateTime = @LastUpdatedate
				FROM dbo.SavedForecastSetup sfs
				JOIN #results msb
				ON sfs.forecastid = msb.forecastid
				AND sfs.planyear = @BidYear

				set @ValidationMessage='1:Import Successful'
			COMMIT 
	END-- end Batch Import Load

	--Cleanup Temp Tables
	DROP TABLE IF Exists #primarystate;
	DROP TABLE IF Exists #planinfo;
	DROP TABLE IF Exists #MSBselections;
	DROP TABLE IF Exists #results ;
	DROP TABLE IF Exists #MSBSelectables;

END TRY

BEGIN CATCH
SELECT ERROR_MESSAGE()
	IF @@TRANCOUNT > 0
            BEGIN
                ROLLBACK TRANSACTION;

        END;
END CATCH
GO
