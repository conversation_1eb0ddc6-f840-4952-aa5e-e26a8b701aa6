SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetCredibilityFactor]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetCredibilityFactor
--
-- AUTHOR: <PERSON><PERSON>
--
-- CREATED DATE: 2008-May-15
-- HEADER UPDATED: 2010-Aug-23
--
-- DESCRIPTION: Computes the credibility factor for the specified plan.
--
-- PARAMETERS:
--  Input:
--		@ForecastID
--
-- RETURNS:
--    The computed credibility factor for the plan.
--
-- TABLES: 
--  Read:
--		CalcBenchmarkSummary
--		PerExtCMSValues
--		SavedPlanBPTExceptionDetail
--		Saved<PERSON>lanBPTExceptionHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		fnSignificantDigits
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2008-May-15		1			Initial version.													Tonya Cockrell
-- 2009-Feb-22      2			Changed to use new BenchmarkSummary table.							Sandy Ellis
-- 2009-Mar-17      3			Data types and null membership										Sandy Ellis
-- 2010-Mar-11		4			Now allows for overidden Credibility of IsBasePlan=0 plans.			Jake Gaecke
--								Plans with NULL credibility need it to be calculated, all 
--								other plans are either base plans or have been overidden.
-- 2010-Mar-11		5			Implemented new PlanYear methodology and independence for 2012		Joe Casey
-- 2010-Mar-25		6	        Added IsHidden = 0 condition in defining						    Casey Sanders
--						        @MemberMonthsExperience		
-- 2010-Aug-23		7			Removed PlanVersion													Joe Casey
-- 2010-Sep-28      8           Adjusted for rename of LkpExtCMSValues to PerExtCMSValues           Jake Gaecke
-- 2010-Dec-08      9           Fixed ISNULL error                                                  Trevor Mahoney
-- 2015-Jul-22		10			Added maximum credible member months and set equal to				Mark Freel
--								PerExtCMSValues field. If MemberMonthExperience is greater than
--								or equal to this field credibility is 1.
-- 2022-Sept-28     11          DF-14165 Fix, changed to -2 since -1 is a valid value and           Alex Brandt
--                              shouldn't represent NULL
-- 2022-Sep-30		12			Added internal parameters from input parameters     				Khurram Minhas
--                              Added WITH (NOLOCK), Added Schema 
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetCredibilityFactor]
(
    @ForecastID INT
    )
RETURNS FLOAT
AS
BEGIN
    DECLARE @XForecastID INT = @ForecastID  
    DECLARE @MemberMonthsExperience INT
    DECLARE @MinimumCredibleMemberMonths INT
    DECLARE @MaximumCredibleMemberMonths INT
    DECLARE @CredibleMemberMonths INT
    DECLARE @CredibilityFactor FLOAT
    DECLARE @IsOverRide BIT
    DECLARE @IsBasePlan BIT

    --Grab the base plan indicator from the exception table.
    SELECT
		@IsBasePlan = IsBasePlan,
		@CredibilityFactor = Credibility,
		@IsOverRide = IsOverride
    FROM dbo.SavedPlanBPTExceptionHeader WITH (NOLOCK)
    WHERE ForecastID = @XForecastID
        AND IsHidden = 0


    --Get the experience member months come from the appropriate source,
    --depending on the base plan status.
    SELECT @MemberMonthsExperience 
        = CASE @IsBasePlan
            WHEN 0
                THEN
                    (
                    SELECT SUM(MemberMonths) 
                    FROM dbo.SavedPlanBPTExceptionDetail WITH (NOLOCK) 
                    WHERE ForecastID = @XForecastID
                    AND IsHidden = 0
                    )
            ELSE
                (
                SELECT ISNULL(PlanExperienceMembership, 0) --actual member months 
                FROM dbo.CalcBenchmarkSummary WITH (NOLOCK)
                WHERE ForecastID = @XForecastID
                )
        END 


    --The credible member months values are constants.
    SELECT
        @MinimumCredibleMemberMonths = MinimumCredibleMemberMonths,
        @MaximumCredibleMemberMonths = MaximumCredibleMemberMonths,
        @CredibleMemberMonths = CredibleMemberMonths
    FROM dbo.PerExtCMSValues WITH (NOLOCK)


    --Finally, compute the credibililty factor.
    SET @CredibilityFactor
		=	CASE ISNULL(@CredibilityFactor,-2)
				WHEN -2 THEN
					CASE
						WHEN @MemberMonthsExperience >= @MaximumCredibleMemberMonths
							THEN 1
						 WHEN @MemberMonthsExperience <= @MinimumCredibleMemberMonths
							THEN 0
						ELSE
							ROUND((POWER((dbo.fnSignificantDigits(@MemberMonthsExperience, 15)
							/ dbo.fnSignificantDigits(@CredibleMemberMonths, 15)), 0.5)), 6)
					END
				ELSE @CredibilityFactor
			END

    RETURN @CredibilityFactor
END
GO