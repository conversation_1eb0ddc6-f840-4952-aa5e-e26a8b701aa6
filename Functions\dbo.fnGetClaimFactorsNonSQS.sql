SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


/****** Object:  UserDefinedFunction [dbo].[fnGetClaimFactorsNonSQS]   ******/

-- ---------------------------------------------------------------------------------------------------------------------  
-- FUNCTION NAME: fnGetClaimFactorsNonSQS  
--  
-- AUTHOR: Mark Freel  
--  
-- CREATED DATE: 2015-Nov-16 
-- HEADER UPDATED: 2015-Nov-16  
--  
-- DESCRIPTION: Multiplies all ClaimFactors associated with the specified column on WS1  
--  that are stored in SavedClaimFactorDetail.  These values originate from  
--  The returned claim factor is needed to project benefits, spBenefitProjection  
--  Target MER values are excluded from this function
--
-- PARAMETERS:   
-- Input:  
--      @ForecastID  
--  Output:  
--  
-- TABLES:   
-- Read:  
--      LkpExtCMSWorksheet1Mapping  
--      LkpIntBenefitCategory  
--      LkpIntProjectionYear  
--      PerIntClaimFactorType  
--      SavedClaimFactorBenefitLevel  
--      SavedClaimFactorDetail  
--      SavedPlanDetail  
-- Write:  
--  
-- VIEWS:  
--  
-- FUNCTIONS:  
--  
-- STORED PROCS:  
--  
-- $HISTORY   
-- ---------------------------------------------------------------------------------------------------------------------  
-- DATE				VERSION     CHANGES MADE														DEVELOPER    
-- ---------------------------------------------------------------------------------------------------------------------  
-- 2015-Nov-16      1           Initial Version                                                     Mark Freel
-- 2021-Sep-20		2			Modified multiplicative adj (EXP(SUM(LOG))) to bypass 0's	        Franklin Fu
-- 2024-Mar-29      3           Add nolock for tables                                               Chaitanya Durga
-- ---------------------------------------------------------------------------------------------------------------------  
CREATE FUNCTION [dbo].[fnGetClaimFactorsNonSQS] ( @ForecastID INT )  
RETURNS @Results TABLE  
    (  
      ForecastID INT ,  
      ClaimForecastID INT ,  
      ColumnID INT ,  
      IsInNetwork INT ,  
      BenefitCategoryID INT ,  
      E2CClaimFactor DECIMAL(24, 15) ,  
      C2BClaimFactor DECIMAL(24, 15) ,  
      ClaimFactor DECIMAL(24, 15)  
    )  
AS   
    BEGIN  
        INSERT  INTO @Results  
                SELECT  spd.ForecastID ,  
                        spd.ClaimForecastID ,  
                        ws1.ColumnID ,  
                        cfd.IsInNetwork ,  
                        ben.BenefitCategoryID ,  
                        E2CClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0
						ELSE EXP(SUM(LOG(NULLIF(CASE py.ProjectionYearID  
                                                       WHEN 1  
                                                       THEN CASE ctt.ClaimFactorTypeID  
                                                              WHEN 12  
                                                              THEN cfd.ClaimFactor  
                                                              ELSE cfd.ClaimFactor  
                                                            END  
                                                       ELSE 1  
 END,0)))) END,  
                        C2BClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0
						ELSE EXP(SUM(LOG(NULLIF(CASE py.ProjectionYearID  
                                                       WHEN 2  
                                                       THEN cfd.ClaimFactor  
                                                       ELSE 1  
                                                     END,0)))) END , 												 
                        ClaimFactor = CASE WHEN MIN(cfd.ClaimFactor) = 0 THEN 0 
						ELSE EXP(SUM(LOG(NULLIF(cfd.ClaimFactor,0)))) END 
            --There is no aggregate product function in SQL Server.  This is a workaround.  
                FROM    SavedPlanDetail spd WITH(NOLOCK)  
                        INNER JOIN ( SELECT bl.ClaimForecastID ,  
                                            bl.ClaimFactorTypeID ,  
                                            bl.ProjectionYearID ,  
                                            bl.IsInNetwork ,  
                                            bl.BenefitCategoryID ,  
                                            bl.ClaimFactor  
                                     FROM   SavedBenefitLevelClaimFactors bl WITH(NOLOCK)  
                                            INNER JOIN SavedPlanDetail spd WITH(NOLOCK) ON bl.ClaimForecastID = spd.ClaimForecastID  
                                     WHERE  spd.ForecastID = @ForecastID  
                                   ) cfd ON spd.ClaimForecastID = cfd.ClaimForecastID  
                        INNER JOIN LkpIntProjectionYear py WITH(NOLOCK)  ON py.ProjectionYearID = cfd.ProjectionYearID  
                        INNER JOIN PerIntClaimFactorTypeTest ctt WITH(NOLOCK)  ON ctt.ClaimFactorTypeID = cfd.ClaimFactorTypeID  
                        INNER JOIN LkpExtCMSWorksheet1Mapping ws1 WITH(NOLOCK)  ON ctt.ColumnIDBPT = ws1.ColumnID  
                        INNER JOIN LkpIntBenefitCategory ben WITH(NOLOCK)  ON cfd.BenefitCategoryID = ben.BenefitCategoryID  
                WHERE   spd.ForecastID = @ForecastID  
                        AND ( spd.MARatingOptionID = 1  
                              OR spd.MARatingOptionID = 2  
                            )
                        AND ctt.ClaimFactorTypeID NOT IN (35,36)  
                GROUP BY spd.ClaimForecastID ,  
--        py.ProjectionYearID,  
                        ws1.ColumnID ,  
                        cfd.IsInNetwork ,  
                        ben.BenefitCategoryID ,  
                        spd.ForecastID  
                 
        RETURN  
    END
GO
