SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- -------------------------------------------------------------------------------------------------------------------------
-- DATE			    VERSION		CHANGES MADE														DEVELOPER		
-- -------------------------------------------------------------------------------------------------------------------------
-- 2015-Dec-21		1			Initial Build														Aaron Hadley
-- 2016-Dec-30		2			Include plans not in plan mapping table in list						Daniel Nyatuame
-- 2018-Apr-17		3			Added Parameters locally to avoid Parameter Sniffing				Alex Beruscha
-- 2020-Feb-4		4			Change to Division Mapping being used for 2021 bids                 <PERSON>
-- 2022-Oct-5		5			Changes for Project Growth Division Restructure                     Bob Knadler
-- 2023-Jan-3		6			Changes for iMAA Division Org Chart Restructure                     Bob Knadler
-- -------------------------------------------------------------------------------------------------------------------------
--Stored Procedure that returns list of plans (from input) for which the user does not have access
--Also returns plans that are not in the plan mapping table
-- -------------------------------------------------------------------------------------------------------------------------
-- Table						Data Need							
-- -------------------------------------------------------------------------------------------------------------------------
-- W_FEM_AccessCOntrol			Get user Access
-- W_FEM_PlanMapping			Get Plan to division map
-- -------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[w_sp_UploadControl]
    @CPBPListCall VARCHAR(MAX) ,
    @UserIDCall VARCHAR(7)
AS
    BEGIN  
		
		DECLARE @CPBPList AS VARCHAR(MAX) = @CPBPListCall
		DECLARE @UserID AS VARCHAR(7) = @UserIDCall

        SET NOCOUNT ON;  

		--Creates Temp table of divisions user does not have access to
        IF @CPBPList IS NULL
            BEGIN
                SET @CPBPList = ''
                SELECT  @CPBPlist = @CPBPList + ',' + ContractNumber + '-'
                        + PlanID + '-' + SegmentID
                FROM    dbo.W_FEM_PlanMapping
            END

		-- List of divisions user not in
        IF OBJECT_ID('tempdb..#UDiv') IS NOT NULL
            DROP TABLE #UDiv

        CREATE TABLE #UDiv ( [Division] VARCHAR(25) )
        INSERT  INTO #UDiv
                ( Division 
                )
                SELECT  [Division]
                FROM    ( SELECT  [East North],
				                  [East South],
				                  [West]
--Above are new divisions after iMAA Org Chart restructure & set for 2023Q1
/*
--Below are new divisions after Project Growth & set for 2022Q4
--                                [East],
--                                [West]

Prior to that divisions were   ||  And prior to 2021 bids divisions were
--[Florida] ,		           ||  --[Central] ,
--[Northeast]	,	           ||  -- [Eastern] ,
--[Northwest] ,		           ||  -- [Florida] ,
--[Southeast] ,		           ||  -- [Northern] ,    	
--[Southwest] 		           ||  -- [Southeastern] ,
                               ||  -- [Western]
*/
                          FROM      [dbo].[W_FEM_AccessControl]
                          WHERE     [UserID] = @UserID
                        ) AS TEMP UNPIVOT     
		                           ( [Div] FOR [Division] IN ( [East North], [East South], [West] ) ) AS unpvt
                WHERE   unpvt.[Div] = 0 -- #2: Changed '> 0' to '= 0' by Daniel N. 2016-12-30
                GROUP BY [Division] ,
                        unpvt.[Div]

--Build list of plans user does not have access to
--List is based on divisions user does not have access to
--and plans passed through @CPBPList parameter
        --SELECT DISTINCT
        --        ContractNumber + '-' + PlanID + '-' + SegmentID AS ContractPBPSegmentID
        --FROM    dbo.W_FEM_PlanMapping
        --WHERE   division IN ( SELECT    Division --#2: Changed 'NOT IN' to 'IN' by Daniel N. 2016-12-30
        --                          FROM      #UDiv )
        --        AND ContractNumber + '-' + PlanID + '-' + SegmentID IN (
        --        SELECT  DISTINCT
        --                Value
        --        FROM    dbo.fnSTringSPlit(@cpbplist, ',') )

		--#2: To add plans not in Plan Mapping to list of plans: Daniel N. 2016-12-30
		--To be activated upon review and approval by PREP team
        SELECT DISTINCT
                Value
        FROM    dbo.fnStringSplit(@CPBPList, ',')
        WHERE   Value IN (
                SELECT  ContractNumber + '-' + PlanID + '-' + SegmentID
                FROM    dbo.W_FEM_PlanMapping
                WHERE   Division IN ( SELECT    Division
                                      FROM      #UDiv ) )
                OR Value IN (
                SELECT DISTINCT
                        Value
                FROM    dbo.fnStringSplit(@CPBPList, ',')
                WHERE   Value NOT IN (
                        SELECT  ContractNumber + '-' + PlanID + '-'
                                + SegmentID
                        FROM    dbo.W_FEM_PlanMapping ) );
    END

GO