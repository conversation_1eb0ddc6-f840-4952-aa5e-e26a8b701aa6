SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetEstimatedPlanBidComponentExtract]   ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetEstimatedPlanBidComponentExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-22
-- HEADER UPDATED: 2011-Dec-22
--
-- DESCRIPTION: Designed to extract fields for Estimated Plan Bid Component - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	
--		SavedPlanDetail
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-22		1			Initial Version														Bobby Jaegers
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetEstimatedPlanBidComponentExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	EstimatedPlanBidComponent Decimal(7,2)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT  ForecastID,
					EstimatedPlanBidComponent
			FROM SavedPlanDetail
			WHERE MARatingOptionID = 1
			
       
	ELSE
		INSERT @Results
			SELECT  ForecastID,
					EstimatedPlanBidComponent
			FROM SavedPlanDetail
			WHERE ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
			AND MARatingOptionID = 1
			
RETURN
END
GO
