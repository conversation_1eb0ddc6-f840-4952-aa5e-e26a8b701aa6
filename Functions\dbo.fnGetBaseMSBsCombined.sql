SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- FUNCTION NAME:	fnGetBaseMSBsCombined
--
-- CREATOR:			Joe Casey 
--
-- CREATED DATE:	2010-JUN-17
--
-- DESCRIPTION:		Returns a the weighted average of MSBs if multiple C&U IDs are used.
--		
-- PARAMETERS:
--  Input  :		@ForecastID INT,
--					@MARatingOptionID SMALLINT
--
--  Output :		@Results TABLE
--
-- TABLES : 
--	Read :			SavedForecastSetup
--					SavedPlanDFSummary
--					LkpModelSettings
--					SavedDFFinance
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetBaseMSBsfactors
--					fnGetSafeDivisionResult
--					fnGetBidYear
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2010-JUN-17		1		    Initial version.															Joe Casey
-- 2010-JUL-27		2		    Changed AddedBenefitCatID to BidServiceCatID								Joe Casey
-- 2010-JUL-29		3		    Added ProjectedAllowed to help fnGetBaseMSBs TotalProduct					Joe Casey
-- 2010-AUG-11		4		    Removed PlanVersion															Joe Casey
-- 2010-SEP-01      5           Added Adjusted and Unadjusted concepts with SavedCUOverrideMSB				Joe Casey
-- 2011-FEB-07		6			Added join to SavedCUHeader													Joe Casey
-- 2013-MAR-04		7			Added a query to replace PerIntBaseMSBCU for Related Party adj				Tim Gao
-- 2013-MAR-15		8			Added @ForecastID to pull correct values from subquery						Mason Roberts
-- 2013-APR-01		9			Modified subquery to avoid double counting member months					Trevor Mahoney
-- 2013-APR-02		10			Added pcus.MaratinoptionID = @MARatingOptionID								Tim Gao
-- 2013-APR-06		11			Added SUM() and Comment out lines											Tim Gao
-- 2013-MAY-17		12			Changed Join to Contract PBP												Mike Deren
-- 2013-OCT-04		13			Included Join on Segment ID													Anubhav Mishra
-- 2016-AUG-16		14			Removed PerIntBaseMSBsForecastFactors & SavedCUOverrideMSBForecast			Jordan Purdue
-- 2016-AUG-17		15			Removed PerIntBaseMSBsTrendFactors & SavedCUOverrideMSBTrend				Jordan Purdue
-- 2016-AUG-23		16			Removed PerIntRelatedPartiesMSBExperience									Jordan Purdue
-- 2016-SEP-09      17			Renamed PerIntBaseMSBsCU to Saved*CUBaseMSBs as part of						Pragya Mishra
--								SIGMA CU Cleanup
-- 2017-MAY-17		18			Added sums and group by in msb sub select statement							Mark Freel	
-- 2017-SEP-19		19			Added vwSavedCUBaseClaimsMSBs and updated variables							Chris Fleming & Jordan Purdue
-- 2018-MAY-23		20			Adding inner join criteria for DemogIndicators								Jordan Purdue
-- 2020-SEP-21      21          Backend Alignment and Restructuring											Keith Galloway
-- 2022-OCT-21      22          Including additional data to handle populating multiplicative				Michael Manes
--								trends for existing MSB benefits
-- 2022-DEC-28      23          Updated the where Clause on SavedForecastSetup table to pull Bid Year Data Abraham Ndabian
-- 2023-APR-10		24			Implementing feature flag to enable/disable multiplicative methodology		Aleksandar Dimitrijevic
-- 2023-JUN-26		25			Removed "ORDER BY"; removed fields: ProjectedAllowed, BaseCalculatedAllowed
--								BaseCalculatedUtilization, ProjectedAllowedN, SumMemberMonthCount, 
--								DFVersionID, DemogIndicator													Aleksandar Dimitrijevic
-- 2023-AUG-04      26   	    Added INternal parameter and NOLOCK											Sheetal Patil
-- 2024-FEB-28      27          Increased size for BidAllowed, BidUtilization from 15 to 22                 Sai Teja Lakkakula 
-- 2024-OCT-07		28			Remove references to table SavedCUOverrideMSB, as this type of override
--									is no longer used.														Jake Lewis
-- 2024-OCT-23		29			Cost Share Basis: include seven new columns in Allowed/Paid sum				Michael Manes
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnGetBaseMSBsCombined]
    (@ForecastID       INT
    ,@MARatingOptionID SMALLINT)

RETURNS @Results TABLE
    (ForecastID               INT
    ,MARatingOptionID         TINYINT
    ,BidServiceCatID          SMALLINT
    ,AllowedPMPM              DECIMAL(14, 6)
    ,PaidPMPM                 DECIMAL(14, 6)
    ,UnitsPTMPY               DECIMAL(14, 6)
    ,UtilUnitP1000Trend       DECIMAL(23, 15)
    ,UtilBenefitPlanChange    DECIMAL(23, 15)
    ,UtilPopulationChange     DECIMAL(23, 15)
    ,UtilOtherFactor          DECIMAL(23, 15)
    ,UCAProviderPaymentChange DECIMAL(23, 15)
    ,UCAOtherFactor           DECIMAL(23, 15)
    ,UtilAdditiveAdjustment   DECIMAL(14, 6)
    ,PMPMAdditiveAdjustment   DECIMAL(14, 6)
    ,BidAllowed               DECIMAL(14, 6)
    ,BidUtilization           DECIMAL(14, 6) PRIMARY KEY (
                                             ForecastID
                                            ,MARatingOptionID
                                            ,BidServiceCatID))
AS

    BEGIN

        -- declare variables
        DECLARE @XForecastID       INT      = @ForecastID
               ,@XMARatingOptionID SMALLINT = @MARatingOptionID;
        DECLARE @CombinedDemog TINYINT;
        DECLARE @MemberMonths INT;
        DECLARE @MSBMethod BIT;

        -- set values
        SET @CombinedDemog = 3;
        SET @MSBMethod = (SELECT    IsMSBWS1MultFactorsEnabled FROM dbo.LkpModelSettings WITH (NOLOCK));

        -- set membership
        SELECT      @MemberMonths = SUM (DFF.MemberMonths)
        FROM        dbo.SavedDFFinance DFF WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
               ON DFF.PlanInfoID = DFS.PlanInfoID
                  AND   DFS.ForecastID = @XForecastID
                  AND   DFS.MARatingOptionID = @XMARatingOptionID
       INNER JOIN   dbo.SavedForecastSetup SFS WITH (NOLOCK)
               ON DFF.DFVersionID = SFS.DFVersionID
                  AND   SFS.ForecastID = @XForecastID
        WHERE       DFF.DemogIndicator IN (1, 2);

        -- creating table with MSB data
        DECLARE @MSB TABLE
            (ForecastID               INT
            ,PlanInfoID               SMALLINT
            ,BidserviceCatID          SMALLINT
            ,AllowedPMPM              DECIMAL(38, 30)
            ,PaidPMPM                 DECIMAL(38, 30)
            ,UnitsPTMPY               DECIMAL(38, 30)
            ,UtilUnitP1000Trend       DECIMAL(23, 15)
            ,UtilBenefitPlanChange    DECIMAL(23, 15)
            ,UtilPopulationChange     DECIMAL(23, 15)
            ,UtilOtherFactor          DECIMAL(23, 15)
            ,UCAProviderPaymentChange DECIMAL(23, 15)
            ,UCAOtherFactor           DECIMAL(23, 15)
            ,AdditiveCost             DECIMAL(38, 9)
            ,AdditiveUtil             DECIMAL(27, 9)
            ,BidAllowed               DECIMAL(7, 2)
            ,BidUtilization           DECIMAL(15, 6)
            ,MemberMonths             INT);

        INSERT INTO @MSB
        SELECT      SFS.ForecastID
                   ,DFD.PlanInfoID
                   ,DFD.BidServiceCatID
                   ,AllowedPMPM = dbo.fnGetSafeDivisionResult (
                                  SUM ((DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                                        + DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                                        + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                                        + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                                        + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                                        + DFD.CapSurplusDeficitBCAllocatedDelegated
                                        + DFD.CapSurplusDeficitScenarioAllocatedDelegated + DFD.CapMSBs
                                        + DFD.CapProviderRewards + DFD.OPBOtherNonHospitalBCAllocated
                                        + DFD.OPBOtherNonHospitalScenarioAllocated + DFD.OPBClaimsBuyDownBCAllocated
                                        + DFD.OPBClaimsBuyDownScenarioAllocated
                                        + DFD.OPBProviderClaimSettlementsBCAllocated
                                        + DFD.OPBProviderClaimSettlementsScenarioAllocated
                                        + DFD.OPBAccessFeesAndOtherBCAllocated + DFD.OPBAccessFeesAndOtherScenarioAllocated
                                        + DFD.PartDCapAdj + DFD.PartDCapAdjDelegated + DFD.MSCapAdj + DFD.MSCapAdjDelegated
                                        + DFD.SubCapAdj + DFD.SubCapAdjExclude + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                                        + DFD.ModelOfCareAdjPaid + DFD.PartBRxRebatesPharmacy + DFD.PartBRxRebatesQN
                                        + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid + DFD.MSBReductionCap
                                        + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality + DFD.MbrCS
                                        + DFD.EncounterMbrCS + DFD.DelegatedEncounterMbrCS + DFD.MedicaidAdjMbrCS
                                        + DFD.AdditiveAdjMbrCS + DFD.ModelOfCareAdjMbrCS + DFD.MSBMbrCS
                                        + DFD.MSBReductionClaimsMbrCS) * DFD.MemberMonths)
                                 ,(SUM (DFD.MemberMonths) / COUNT (DFD.MemberMonths)))
                   ,PaidPMPM = dbo.fnGetSafeDivisionResult (
                               SUM ((DFD.Paid + DFD.CapDirectPayEPaidClaims + DFD.CapDirectPayDEPaidClaims
                                     + DFD.CapDirectPayBCAllocated + DFD.CapDirectPayScenarioAllocated
                                     + DFD.CapDirectPayBCAllocatedDelegated + DFD.CapDirectPayScenarioAllocatedDelegated
                                     + DFD.CapSurplusDeficitEPaidClaims + DFD.CapSurplusDeficitDEPaidClaims
                                     + DFD.CapSurplusDeficitBCAllocated + DFD.CapSurplusDeficitScenarioAllocated
                                     + DFD.CapSurplusDeficitBCAllocatedDelegated
                                     + DFD.CapSurplusDeficitScenarioAllocatedDelegated + DFD.CapMSBs
                                     + DFD.CapProviderRewards + DFD.OPBOtherNonHospitalBCAllocated
                                     + DFD.OPBOtherNonHospitalScenarioAllocated + DFD.OPBClaimsBuyDownBCAllocated
                                     + DFD.OPBClaimsBuyDownScenarioAllocated + DFD.OPBProviderClaimSettlementsBCAllocated
                                     + DFD.OPBProviderClaimSettlementsScenarioAllocated
                                     + DFD.OPBAccessFeesAndOtherBCAllocated + DFD.OPBAccessFeesAndOtherScenarioAllocated
                                     + DFD.PartDCapAdj + DFD.PartDCapAdjDelegated + DFD.MSCapAdj + DFD.MSCapAdjDelegated
                                     + DFD.SubCapAdj + DFD.SubCapAdjExclude + DFD.MedicaidAdjPaid + DFD.AdditiveAdjPaid
                                     + DFD.ModelOfCareAdjPaid + DFD.PartBRxRebatesPharmacy + DFD.PartBRxRebatesQN
                                     + DFD.RelatedPartiesAdj + DFD.ProfitAdj + DFD.MSBPaid + DFD.MSBReductionCap
                                     + DFD.MSBReductionClaimsPaid + DFD.MSBReductionQuality) * DFD.MemberMonths)
                              ,(SUM (DFD.MemberMonths) / COUNT (DFD.MemberMonths)))
                   ,UnitsPTMPY = dbo.fnGetSafeDivisionResult (
                                 SUM ((DFD.UnitCnt + DFD.EncounterUnitCnt + DFD.DelegatedEncounterUnitCnt
                                       + DFD.AdditiveAdjUnits + DFD.ModelOfCareAdjUnits + DFD.UCUnitsAdj + DFD.MSBUnits
                                       + DFD.MSBReductionClaimsUnits) * DFD.MemberMonths)
                                ,(SUM (DFD.MemberMonths) / COUNT (DFD.MemberMonths)))
                   ,UtilUnitP1000Trend = CASE WHEN @MSBMethod = 1 THEN
                                                  dbo.fnGetSafeDivisionResultReturnOne (
                                                  SUM ((DFD.UtilUnitP1000Trend) * DFD.BaseCalculatedUtilization)
                                                 ,SUM (DFD.BaseCalculatedUtilization))
                                              ELSE 1 END
                   ,UtilBenefitPlanChange = CASE WHEN @MSBMethod = 1 THEN
                                                     dbo.fnGetSafeDivisionResultReturnOne (
                                                     SUM ((DFD.UtilBenefitPlanChange) * DFD.BaseCalculatedUtilization)
                                                    ,SUM (DFD.BaseCalculatedUtilization))
                                                 ELSE 1 END
                   ,UtilPopulationChange = CASE WHEN @MSBMethod = 1 THEN
                                                    dbo.fnGetSafeDivisionResultReturnOne (
                                                    SUM ((DFD.UtilPopulationChange) * DFD.BaseCalculatedUtilization)
                                                   ,SUM (DFD.BaseCalculatedUtilization))
                                                ELSE 1 END
                   ,UtilOtherFactor = CASE WHEN @MSBMethod = 1 THEN
                                               dbo.fnGetSafeDivisionResultReturnOne (
                                               SUM ((DFD.UtilOtherFactor) * DFD.BaseCalculatedUtilization)
                                              ,SUM (DFD.BaseCalculatedUtilization))
                                           ELSE 1 END
                   ,UCAProviderPaymentChange = CASE WHEN @MSBMethod = 1 THEN
                                                        dbo.fnGetSafeDivisionResultReturnOne (
                                                        SUM ((DFD.UCAProviderPaymentChange) * DFD.BaseCalculatedAllowed)
                                                       ,SUM (DFD.BaseCalculatedAllowed))
                                                    ELSE 1 END
                   ,UCAOtherFactor = CASE WHEN @MSBMethod = 1 THEN
                                              dbo.fnGetSafeDivisionResultReturnOne (
                                              SUM ((DFD.UCAOtherFactor) * DFD.BaseCalculatedAllowed)
                                             ,SUM (DFD.BaseCalculatedAllowed))
                                          ELSE 1 END
                   ,AdditiveCost = CASE WHEN @MSBMethod = 1 THEN ISNULL (SUM (DFD.AdditiveCost), 0) ELSE 0 END
                   ,AdditiveUtil = CASE WHEN @MSBMethod = 1 THEN ISNULL (SUM (DFD.AdditiveUtil), 0) ELSE 0 END
                   ,BidAllowed = CASE WHEN @MSBMethod = 1 THEN ISNULL (SUM (DFD.BidAllowed), 0) ELSE 0 END
                   ,BidUtilization = CASE WHEN @MSBMethod = 1 THEN ISNULL (SUM (DFD.BidUtilization), 0) ELSE 0 END
                   ,MemberMonths = SUM (DFD.MemberMonths) / COUNT (DFD.MemberMonths)    --need to account for added rows for new benefits
        FROM        dbo.SavedForecastSetup SFS WITH (NOLOCK)
       INNER JOIN   dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
               ON SFS.ForecastID = DFS.ForecastID
                  AND   SFS.PlanYear = dbo.fnGetBidYear () -- filtered on BidYear plans only by Abe 12/28/2022
       INNER JOIN   dbo.fnGetBaseMSBsFactors (@XForecastID, @XMARatingOptionID) DFD
               ON DFD.PlanInfoID = DFS.PlanInfoID
                  AND   DFD.DFVersionID = SFS.DFVersionID
                  AND   DFD.MARatingOptionID = DFS.MARatingOptionID
        WHERE       SFS.ForecastID = @XForecastID
                    AND DFS.MARatingOptionID = @XMARatingOptionID
                    AND DFD.DemogIndicator = @CombinedDemog
        GROUP BY    SFS.ForecastID
                   ,DFD.PlanInfoID
                   ,DFD.BidServiceCatID;

        -- creating a table with weighted values
        DECLARE @pre TABLE
            (MARatingOptionID         TINYINT
            ,BidServiceCatID          SMALLINT
            ,AllowedPMPMN             DECIMAL(22, 6)
            ,PaidPMPMN                DECIMAL(22, 6)
            ,UnitsPTMPYN              DECIMAL(22, 6)
            ,UtilUnitP1000Trend       DECIMAL(23, 15)
            ,UtilBenefitPlanChange    DECIMAL(23, 15)
            ,UtilPopulationChange     DECIMAL(23, 15)
            ,UtilOtherFactor          DECIMAL(23, 15)
            ,UCAProviderPaymentChange DECIMAL(23, 15)
            ,UCAOtherFactor           DECIMAL(23, 15)
            ,AdditiveCost             DECIMAL(38, 9)
            ,AdditiveUtil             DECIMAL(27, 9)
            ,BidAllowed               DECIMAL(22, 6)
            ,BidUtilization           DECIMAL(22, 6));

        INSERT INTO @pre
        SELECT      DFS.MARatingOptionID
                   ,MSB.BidserviceCatID
                   ,AllowedPMPMN = SUM (MSB.AllowedPMPM * MSB.MemberMonths)
                   ,PaidPMPMN = SUM (MSB.PaidPMPM * MSB.MemberMonths)
                   ,UnitsPTMPYN = SUM (MSB.UnitsPTMPY * MSB.MemberMonths)
                   ,UtilUnitP1000Trend = SUM (MSB.UtilUnitP1000Trend * MSB.MemberMonths)
                   ,UtilBenefitPlanChange = SUM (MSB.UtilBenefitPlanChange * MSB.MemberMonths)
                   ,UtilPopulationChange = SUM (MSB.UtilPopulationChange * MSB.MemberMonths)
                   ,UtilOtherFactor = SUM (MSB.UtilOtherFactor * MSB.MemberMonths)
                   ,UCAProviderPaymentChange = SUM (MSB.UCAProviderPaymentChange * MSB.MemberMonths)
                   ,UCAOtherFactor = SUM (MSB.UCAOtherFactor * MSB.MemberMonths)
                   ,AdditiveCost = SUM (MSB.AdditiveCost * MSB.MemberMonths)
                   ,AdditiveUtil = SUM (MSB.AdditiveUtil * MSB.MemberMonths)
                   ,BidAllowed = SUM (MSB.BidAllowed * MSB.MemberMonths)
                   ,BidUtilization = SUM (MSB.BidUtilization * MSB.MemberMonths)
        FROM        dbo.SavedPlanDFSummary DFS WITH (NOLOCK)
       INNER JOIN   @MSB MSB -- change the original single PerIntBaseNSBsCU to the following query for Related parties adjustments --Tim 
               ON DFS.ForecastID = MSB.ForecastID
                  AND   DFS.PlanInfoID = MSB.PlanInfoID
        WHERE       DFS.ForecastID = @XForecastID
                    AND DFS.MARatingOptionID = @XMARatingOptionID
        GROUP BY    DFS.MARatingOptionID
                   ,MSB.BidserviceCatID;

        INSERT INTO @Results
        SELECT  @XForecastID AS ForecastID
               ,pre.MARatingOptionID
               ,pre.BidServiceCatID
               ,AllowedPMPM = dbo.fnGetSafeDivisionResult (pre.AllowedPMPMN, @MemberMonths)
               ,PaidPMPM = dbo.fnGetSafeDivisionResult (pre.PaidPMPMN, @MemberMonths)
               ,UnitsPTMPY = dbo.fnGetSafeDivisionResult (pre.UnitsPTMPYN, @MemberMonths)
               ,UtilUnitP1000Trend = dbo.fnGetSafeDivisionResult (pre.UtilUnitP1000Trend, @MemberMonths)
               ,UtilBenefitPlanChange = dbo.fnGetSafeDivisionResult (pre.UtilBenefitPlanChange, @MemberMonths)
               ,UtilPopulationChange = dbo.fnGetSafeDivisionResult (pre.UtilPopulationChange, @MemberMonths)
               ,UtilOtherFactor = dbo.fnGetSafeDivisionResult (pre.UtilOtherFactor, @MemberMonths)
               ,UCAProviderPaymentChange = dbo.fnGetSafeDivisionResult (pre.UCAProviderPaymentChange, @MemberMonths)
               ,UCAOtherFactor = dbo.fnGetSafeDivisionResult (pre.UCAOtherFactor, @MemberMonths)
               ,UtilAdditiveAdjustment = dbo.fnGetSafeDivisionResult (pre.AdditiveUtil, @MemberMonths)
               ,PMPMAdditiveAdjustment = dbo.fnGetSafeDivisionResult (pre.AdditiveCost, @MemberMonths)
               ,BidAllowed = dbo.fnGetSafeDivisionResult (pre.BidAllowed, @MemberMonths)
               ,BidUtilization = dbo.fnGetSafeDivisionResult (pre.BidUtilization, @MemberMonths)
        FROM    @pre pre;

        RETURN;

    END;
GO
