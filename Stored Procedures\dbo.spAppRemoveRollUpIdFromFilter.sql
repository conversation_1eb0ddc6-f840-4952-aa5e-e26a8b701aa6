SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO



-- =============================================      
-- Author: <PERSON> Smith  
-- Create date: 31-08-2018
-- Description:  Remove a RollUpId from Filter User Prefrence
--      
--      
-- PARAMETERS:      
-- Input:   RollUpId, UserID     
    
-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2018-Aug-31  1		  Initial version.											Kritika Singh
-- 2019-Apr-08  2		  Removed reference of [MAAModels] database 				Kritika Singh
-- 2019-Apr-08  3         Changed @UserId to @LastUpdateByID						Kritika Singh
-- 2019-Jul-22  3         Updated the left condition								Deepali
-- ----------------------------------------------------------------------------------------------------------------------       

CREATE PROCEDURE [dbo].[spAppRemoveRollUpIdFromFilter]
    @RollUpId VARCHAR(7),
    @LastUpdateByID VARCHAR(7),
	@Result BIT OUT
AS
    BEGIN   
        BEGIN TRANSACTION;
        BEGIN TRY        

			DECLARE @NewRollUpIdList VARCHAR(MAX);

			Set @NewRollUpIdList=''
			Select @NewRollUpIdList=@NewRollUpIdList + Coalesce([Value]+ ',','') from (Select Value 
			FROM dbo.fnStringSplit((select SelectedRollupList from [dbo].[AppSavedRollupUserPreference] 
			where UserID = @LastUpdateByID) ,',') WHERE  Value <> @RollUpId  ) A
			--Set @NewRollUpIdList=Left(@NewRollUpIdList,LEN(@NewRollUpIdList)-1)
			Set @NewRollUpIdList= LEFT(@NewRollUpIdList, 
            CASE WHEN LEN(@NewRollUpIdList) < 0 
                 THEN LEN(@NewRollUpIdList) 
                 ELSE LEN(@NewRollUpIdList) END) 
            IF (@NewRollUpIdList != '')
                BEGIN
                    UPDATE  [dbo].[AppSavedRollupUserPreference]
                    SET     SelectedRollupList = @NewRollUpIdList ,
                            LastUpdateDateTime = GETDATE()
                    WHERE   UserID = @LastUpdateByID;
	            END;
            SET @Result = 1; 
            COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
            SET @Result = 0;
            ROLLBACK TRANSACTION; 
        END CATCH;  
    END;	

GO
