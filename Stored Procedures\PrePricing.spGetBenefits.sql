SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO


----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetBenefits]   
--    
-- AUTHOR: Sur<PERSON>y 
--    
-- CREATED DATE: 2024-Oct-28    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving Benefits  
--    
-- PARAMETERS:    
-- Input: 
-- 
   
-- TABLES:   
--  
 
-- Read:    
-- PrePricing.MarketInputSubCategory 

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Oct-31		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetBenefits]
AS    
BEGIN    
	SELECT a.SubCategoryID,a.SubCategoryName,a.IsCostShareType,
	AssignedBundles=STUFF((SELECT ',' +  CAST(b.BundleID AS VARCHAR(7))
				   FROM PrePricing.BenefitBundleMapping b WITH(NOLOCK) WHERE b.SubCategoryID=a.SubCategoryID        
				  FOR XML PATH('')),1, 1, ''),a.IsReadOnly,
				  a.CategoryID,
				  a.IsINValueOnly
	FROM PrePricing.MarketInputSubCategory a WITH(NOLOCK) 
	ORDER BY a.LastUpdateDateTime DESC;
END
GO
