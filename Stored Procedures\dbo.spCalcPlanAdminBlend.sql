SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcPlanAdminBlend
--
-- AUTHOR: Jordan Purdue
--
-- CREATED DATE: 2018-Aug-29
--
-- DESCRIPTION: Responsible for blending the admin rates to create CalcPlanAdminBlend
--
-- PARAMETERS:
--  Input:
--		@ForecastID
--		@UserID
--
--  Output:
--
-- TABLES:
--  Read:
--		SavedPlanHeader
--		LkpProductType
--		SavedMemberMonthDetail
--		 SavedMarketInfo
--		SavedPlanStateCountyDetail
--		PerIntAdminExpenses
--		LkpExtCMSStateTerritory
--		LkpExtCMSStateCounty
--		LkpIntDemogIndicator
--
--  Write:
--		CalcPlanAdminBlend
--
-- VIEWS:
--
-- FUCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER  
-- ------------------------------------------------------------------------------------------------------------------------------
-- 2018-Aug-29      1			Initial Version														Jordan Purdue
-- 2019-Jul-05      2           Made changes in region table.                                       Satyam Singhal
-- 2019-oct-30		3			Removed 'HUMAD\' to UserID											Chhavi Sinha 
-- 2020-dec-05		4			Adding local temp table to improve db performance					Rodney Smith 
-- 2022-Jun-29		5			Replaced IsFinal with a constant 0									Aleksandar Dimitrijevic
-- 2022-Nov-23      2           Replace Product type of 'HMOPOS' with 'HMO' for joins               Adam Gilbert
-- --------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcPlanAdminBlend]
(
	@ForecastID INT,
    @UserID CHAR(7)
)
AS
SET NOCOUNT ON

-- Declare Variables
DECLARE @PlanYearID SMALLINT,
		@LastDateTime DATETIME,
		@LocalError INT,
		@ReturnStatus INT

-- Set Variables
SET @LastDateTime = GETDATE()
SET @ReturnStatus = 0 
SET @PlanYearID = dbo.fnGetBidYear()

--If the plan is Saved, don't execute this stored procedure
--BEGIN
--    IF 0 = 1
--    RETURN
--END

----If an error occurs, we need to note it and cancel the DELETE and INSERT
BEGIN TRANSACTION AppendData

BEGIN
IF OBJECT_ID(N'tempdb..#CalcPlanAdminBlendTemp') IS NOT NULL
BEGIN
    DROP TABLE #CalcPlanAdminBlendTemp;
END

SELECT DISTINCT PlanYearID = @PlanYearID,
	acm.ForecastID,
	'MAMarketingAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalMAMarketingAdmin),tmm.ProjectedTotalMemberMonths),
	'MADirectAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalDirectAdmin),tmm.ProjectedTotalMemberMonths),
	'MAIndirectAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalMAIndirectAdmin),tmm.ProjectedTotalMemberMonths),
	'MAQualityAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalMAQualityAdmin),tmm.ProjectedTotalMemberMonths),
	'MATaxesAndFeesAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalMATaxesAndFeesAdminPMPM),tmm.ProjectedTotalMemberMonths),
	'PDMarketingAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalPDMarketingAdmin),tmm.ProjectedTotalMemberMonths),
	'PDDirectAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalPDDirectAdmin),tmm.ProjectedTotalMemberMonths),
	'PDIndirectAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalPDIndirectAdmin),tmm.ProjectedTotalMemberMonths),
	'PDQualityAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalPDQualityAdmin),tmm.ProjectedTotalMemberMonths),
	'PDTaxesAndFeesAdminPMPM' = dbo.fnGetSafeDivisionResult(SUM(ACM.TotalPDTaxesAndFeesAdmin),tmm.ProjectedTotalMemberMonths),
	'UserID' = @UserID,
	'LastDateTime' = @LastDateTime
INTO #CalcPlanAdminBlendTemp
	From							
		(
		SELECT
			sph.ForecastID,
			STT.StateTerritoryCode,
			CTY.CountyCode,
			STT.StateTerritoryName,
			CTY.CountyName,	
			'TotalMAMarketingAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.MAMarketingAdminPMPM ELSE ae2.MAMarketingAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalDirectAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.MADirectAdminPMPM ELSE ae2.MADirectAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalMAIndirectAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.MAIndirectAdminPMPM ELSE ae2.MAIndirectAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalMAQualityAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.MAQualityAdminPMPM ELSE ae2.MAQualityAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalMATaxesAndFeesAdminPMPM' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.MATaxesAndFeesAdminPMPM ELSE ae2.MATaxesAndFeesAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalPDMarketingAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.PDMarketingAdminPMPM ELSE ae2.PDMarketingAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalPDDirectAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.PDDirectAdminPMPM ELSE ae2.PDDirectAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalPDIndirectAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.PDIndirectAdminPMPM ELSE ae2.PDIndirectAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalPDQualityAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.PDQualityAdminPMPM ELSE ae2.PDQualityAdminPMPM END * cmm.ProjectedCountyMemberMonths,
			'TotalPDTaxesAndFeesAdmin' = CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID THEN ae.PDTaxesAndFeesAdminPMPM ELSE ae2.PDTaxesAndFeesAdminPMPM END * cmm.ProjectedCountyMemberMonths
		From SavedPlanHeader sph 
		LEFT JOIN LkpProductType lkp 
			ON sph.PlanTypeID = lkp.ProductTypeID 
		LEFT JOIN SavedPlanMemberMonthDetail spm 
			ON sph.ForecastID = spm.ForecastID 
		INNER JOIN LkpExtCMSStateCounty CTY
			ON CTY.StateTerritoryID = spm.StateTerritoryID
			AND CTY.CountyCode = spm.CountyCode
		INNER JOIN LkpExtCMSStateTerritory STT
			ON STT.StateTerritoryCode = spm.StateTerritoryID
		INNER JOIN LkpIntDemogIndicators dem
			ON dem.DemogIndicator = spm.DemogIndicator
		LEFT JOIN SavedMarketInfo srd 
			ON sph.MarketID = srd.ActuarialMarketID 
		LEFT JOIN SavedPlanStateCountyDetail SCD 
			ON SCD.ForecastID = spm.ForecastID 
			AND SCD.StateTerritoryID = spm.StateTerritoryID 
			AND SCD.CountyCode = spm.CountyCode
		INNER JOIN 
				(
				SELECT
					mmd.ForecastID,
					STT.StateTerritoryCode,
					CTY.CountyCode, 
					StateTerritoryName, 
					CountyName, 
					ProjectedCountyMemberMonths = SUM(CASE WHEN DualEligibleTypeID = 0 THEN MemberMonths ELSE 0 END) + SUM(CASE WHEN DualEligibleTypeID = 1 THEN MemberMonths ELSE 0 END) 
				FROM SavedPlanMemberMonthDetail MMD 
				INNER JOIN LkpExtCMSStateCounty CTY 
					ON CTY.StateTerritoryID = MMD.StateTerritoryID 
					AND CTY.COuntyCode = MMD.CountyCode 
				INNER JOIN LkpExtCMSStateTerritory STT 
					ON STT.StateTerritoryID = MMD.StateTerritoryID 
				INNER JOIN LkpIntDemogIndicators LKP 
					ON LKP.DemogIndicator = MMD.DemogIndicator 
				INNER JOIN SavedPlanStateCountyDetail SCD 
					ON SCD.ForecastID = MMD.ForecastID 
					AND SCD.StateTerritoryID = MMD.StateTerritoryID 
					AND SCD.CountyCode = MMD.CountyCode 
				WHERE MMD.ForecastID = @ForecastID AND IsBiddable = 1 AND IsCountyExcludedFromBPTOutput = 0 
				GROUP BY MMD.ForecastID, StateTerritoryName, CountyName, STT.StateTerritoryCode, CTY.CountyCode
				) cmm
			ON  cmm.ForecastID = sph.ForecastID 
				AND cmm.StateTerritoryCode = STT.StateTerritoryCode
				AND cmm.CountyCode = CTY.CountyCode
				AND cmm.StateTerritoryName = STT.StateTerritoryName
				AND cmm.CountyName = CTY.CountyName
		LEFT JOIN PerIntAdminExpenses ae 
			ON CASE WHEN ae.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID AND sph.IsSNP = ae.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae.Product THEN 1 
				WHEN ae.HumanaRegionID = srd.ActuarialRegionID AND ae.StateID = scd.StateTerritoryID AND ae.CountyCode = scd.CountyCode AND sph.IsSNP = ae.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae.Product THEN 2 
				WHEN ae.StateID = scd.StateTerritoryID AND ae.CountyCode = scd.CountyCode AND ae.HumanaRegionID = 0 AND sph.IsSNP = ae.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae.Product THEN 2 
				WHEN ae.StateID = scd.StateTerritoryID AND ae.HumanaRegionID = 0 AND sph.IsSNP = ae.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae.Product THEN 2 ELSE 0 END = 1
		LEFT JOIN PerIntAdminExpenses ae2 
			ON CASE WHEN ae2.CPS = sph.ContractNumber + '-' + sph.PlanID + '-' + sph.SegmentID AND sph.IsSNP = ae2.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae2.Product THEN 2 
				WHEN ae2.HumanaRegionID = srd.ActuarialRegionID AND ae2.StateID = scd.StateTerritoryID AND ae2.CountyCode = scd.CountyCode AND sph.IsSNP = ae2.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae2.Product THEN 1 
				WHEN ae2.StateID = scd.StateTerritoryID AND ae2.CountyCode = scd.CountyCode AND ae2.HumanaRegionID = 0 AND sph.IsSNP = ae2.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae2.Product THEN 1 
				WHEN ae2.StateID = scd.StateTerritoryID AND ae2.HumanaRegionID = 0 AND sph.IsSNP = ae2.IsSNP AND replace(lkp.ProductType,'HMOPOS','HMO') = ae2.Product THEN 1 ELSE 0 END = 1   
		Where sph.ForecastID = @ForecastID and spm.DemogIndicator = 1 and SCD.IsCountyExcludedFromBPTOutput = 0
		) acm
	INNER JOIN 
			(
			SELECT
				mmd.ForecastID,
				ProjectedTotalMemberMonths = SUM(CASE WHEN DualEligibleTypeID = 0 THEN MemberMonths ELSE 0 END) + SUM(CASE WHEN DualEligibleTypeID = 1 THEN MemberMonths ELSE 0 END) 
			FROM SavedPlanMemberMonthDetail MMD 
			INNER JOIN LkpExtCMSStateCounty CTY 
				ON CTY.StateTerritoryID = MMD.StateTerritoryID 
				AND CTY.COuntyCode = MMD.CountyCode 
			INNER JOIN LkpExtCMSStateTerritory STT 
				ON STT.StateTerritoryID = MMD.StateTerritoryID 
			INNER JOIN LkpIntDemogIndicators LKP 
				ON LKP.DemogIndicator = MMD.DemogIndicator 
			INNER JOIN SavedPlanStateCountyDetail SCD 
				ON SCD.ForecastID = MMD.ForecastID 
				AND SCD.StateTerritoryID = MMD.StateTerritoryID 
				AND SCD.CountyCode = MMD.CountyCode 
			WHERE MMD.ForecastID = @ForecastID AND IsBiddable = 1 AND IsCountyExcludedFromBPTOutput = 0 
			GROUP BY MMD.ForecastID
			) tmm
		On ACM.ForecastID = tmm.ForecastID
	Group By acm.ForecastID, tmm.ProjectedTotalMemberMonths

-- Populate table for unadjusted values
DELETE FROM CalcPlanAdminBlend WHERE ForecastID = @ForecastID AND PlanYearID = @PlanYearID
INSERT INTO CalcPlanAdminBlend SELECT * FROM #CalcPlanAdminBlendTemp

---------------------------------------------------------------------------------------------------------------------
-- Update the return status if an error occurred.
    SET @LocalError = @@ERROR
    IF @ReturnStatus = 0
        SET @ReturnStatus = @LocalError     
END

IF @ReturnStatus <> 0
BEGIN
    ROLLBACK TRANSACTION AppendData
END
ELSE
BEGIN
    COMMIT TRANSACTION AppendData
END

RETURN 0


GO
