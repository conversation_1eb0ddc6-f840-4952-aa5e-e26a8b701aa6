				 			   									   SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO   

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	Trend_spCalcPopulationBidYear
--
-- CREATOR:			Andy Blink
--
-- CREATED DATE:	2018-JUL-24
--
-- DESCRIPTION:		Calculate population bid relativities.
--		
-- PARAMETERS:
--  Input  :		@LastUpdateByID
--					@CPBPList
--					@RegionString
--					@ProductString
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			dbo.LkpIntPlanYear
--					dbo.LkpIntBenefitCategory
--					dbo.SavedForecastSetup
--					dbo.SavedRollupForecastMap
--					dbo.SavedRollupInfo
--					dbo.Trend_SavedPopulationMarketAdjustment
--					dbo.Trend_PerPopulationMarketAdjustmentOffset
--					dbo.Trend_PerPopulationCredibleMemberMonths
--					dbo.Trend_CalcPopulationHistorical
--					dbo.Trend_CalcPopulationCurrentYear
--					dbo.Trend_SavedPopulationBarcBidYearMembership
--					dbo.Trend_CalcPopulationCredibility
--					dbo.Trend_PerPopulationP2PImpact
--
--  Write:			dbo.Trend_SavedRelativityPopulation 
--					dbo.Trend_PopulationReporting_CalcRelativity
--
-- VIEWS: 
--	Read:			dbo.vwPlanInfo
--					dbo.vwSAMCrosswalks
--
-- FUNCTIONS:		dbo.Trend_fnSafeDivide
--					dbo.Trend_fnCalcStringToTable
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																										DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2018-JUL-24		1		    Initial Version																			                            Andy Blink 
-- 2018-DEC-18		2			Add WHERE statement to the audit table DELETE statement									                            Andy Blink
-- 2019-JAN-14		3			Audit table fix: Previously plans not passed through the paramater were deleted
--									Insert 1.0 factors into the geographic input table
-- 2019-AUG-06		4			Adding PriorYearBarcClass and incorporating new trend methodology						                            Andy Blink
--									Moving pop factor into use instead of allowed
-- 2019-OCT-04		5			Revising PriorYearBarcClass methodology													                            Andy Blink
-- 2020-MAR-03		6           Fixing class B CY2Bid trends. CYcredweight / BYcredweight could be on a different credibility basis                 Andy Blink
-- 2020-MAR-09		7           Adding to MAAModels for Trend Simplifications, and updating column names and table references                       Andy Blink
-- 2020-JUN-09		8           Replacing vwCrosswalksPlanLevel with vwSamCrosswalks and set RiskInd = T instead of split between Y/N               Andy Blink 
-- 2020-JUN-24		9           Create temporary planinfo view to add SNPtype to product field                                                      Michael Manes
-- 2020-JUL-14		10			Optimized BARC to MAAUI logic 																						Ramandeep Saini
-- 2020-SEP-19		11			Revise logic for #NewPlans																							Craig Nielsen
-- 2020-DEC-22		12			Adjusted the sp to handle projection for the new RC PartBRxMedical
--									Added Ln "  AND  SSStateCountyCD <> 99999" for interim table #MRABidYear & added Ln
--									"   AND  SSStateCountyCD <> 99999" for interim table #MRARetainedMM (MRA is removing demo information
--									from OOA counties, so Population factor will exclude OOA members from the projection)							Aleksandar Dimitrijevic
-- 2021-AUG-19		13          Include county and DE in credibility process and incorporate best practices                          				Erin Alston
-- 2022-FEB-02		14			Update to correct methodology, change county code to correctly calculate population with							Craig Nielsen
--									uncommon BARC/PriorBARC combinations, fix rounding issues, add population reporting output and 
--									other code cleanup
-- 2022-FEB-22		15			Remove Base and Current year insert/delete statements from Population Report section								Craig Nielsen
-- 2022-OCT-13		16			Incorporate MA Only as its own product and use new credibility table												Tanvi Khanna
-- 2023-MAR-29		17			Remove P2P impact from base year to more accurately calculate sickening trend. Removed Class B Product Trend		
--								Adjusted calculation of Class B product trends to calculate based on DE# splits. If NW/Product/DE# cut was not  
--								fully credible in either base or current year then that product receives the NW/DE# factor. Floored the applied
--								Class B product trends at 0																							Stephen Stempky
-- 2023-AUG-03		18			Added temp table to house base year data without quarters. The rolling up of quarterly information while joining
--								with the P2P table which doesn't have quarters was causing an issue with consolidations in the base year which 
--								misstated the P2P impact																							Stephen Stempky
-- 2023-Sep-25	    19 		    Rowlocks added for deadlock issues																					Surya Murthy
-- 2023-Dec-12		20			Changed vwplaninfo product logic from a case statement to directly use PopRSCredibilityProduct field				Stephen Stempky
-- 2025-Feb-03		21			Updating Market Adjustment offset to refer to the table rather than just a singular value since it will vary
--								by IsDEPound and BARC Class now																						Stephen Stempky
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[Trend_spCalcPopulationBidYear]
--DECLARE

@CPBPList        VARCHAR(MAX) = NULL
,@RegionString   VARCHAR(MAX) = NULL
,@ProductString  VARCHAR(MAX) = NULL
,@LastUpdateByID CHAR(13)
AS

SET NOCOUNT ON;
SET ANSI_WARNINGS OFF;
----------------------------------------------------
-- 0. Declare / set variables and pull parameters --
----------------------------------------------------
--Other variables
DECLARE @BidYear SMALLINT;
DECLARE @CurrentYear SMALLINT;
DECLARE @BaseYear SMALLINT;
DECLARE @CredibleMemberMonths DECIMAL(18, 8);
DECLARE @Offset DECIMAL(18, 8);


    BEGIN
        --Bid year
        SET @BidYear = (SELECT  Top 1 PlanYearID
                        FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                        WHERE   IsBidYear = 1);

        --Current year
        SET @CurrentYear = (SELECT Top 1 PlanYearID
                            FROM    dbo.LkpIntPlanYear WITH (NOLOCK)
                            WHERE   IsCurrentYear = 1);

        --Base year
        SET @BaseYear = (SELECT Top 1 PlanYearID
                         FROM   dbo.LkpIntPlanYear WITH (NOLOCK)
                         WHERE  IsExperienceYear = 1);

        --Credible MemberMonths
        SET @CredibleMemberMonths = (SELECT Top 1 CredibleMemberMonths
                                     FROM   dbo.Trend_PerPopulationCredibleMemberMonths WITH (NOLOCK));

        --Market Adj Offset								
        SET @Offset = (SELECT Top 1  MarketAdjustmentOffset
                       FROM     dbo.Trend_PerPopulationMarketAdjustmentOffset WITH (NOLOCK));

        --Quarter
        IF (SELECT  OBJECT_ID ('tempdb..#Quarter')) IS NOT NULL 
        BEGIN 
            DROP TABLE #Quarter;
        END 

        SELECT  DISTINCT
                number AS QuarterID
        INTO    #Quarter
        FROM    master..spt_values
        WHERE   number BETWEEN 1 AND 4;

        --Reporting Category
        IF (SELECT  OBJECT_ID ('tempdb..#RepCat')) IS NOT NULL
        BEGIN 
            DROP TABLE #Repcat;
        END 

        SELECT  DISTINCT
                ReportingCategory
        INTO    #Repcat
        FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
        WHERE   ReportingCategory = 'Ancil';    --Population trends do not vary by medical reporting category

        --All Reporting Categories
        IF (SELECT  OBJECT_ID ('tempdb..#AllRepCat')) IS NOT NULL
        BEGIN 
            DROP TABLE #AllRepcat;
        END 

        SELECT  DISTINCT
                ReportingCategory
        INTO    #AllRepcat
        FROM    dbo.LkpIntBenefitCategory WITH (NOLOCK)
        WHERE   ReportingCategory NOT IN ('Part B Rx Pharmacy', 'MOC');

        --BarcClass
        IF (SELECT  OBJECT_ID ('tempdb..#BarcClass')) IS NOT NULL
        BEGIN
            DROP TABLE #BarcClass;
        END 

        SELECT  'A' AS BarcClass
        INTO    #BarcClass UNION ALL
        SELECT  'B'
        UNION ALL
        SELECT  'C'
        UNION ALL
        SELECT  'D';

        --DE 
        IF (SELECT  OBJECT_ID ('tempdb..#DE')) IS NOT NULL 
        BEGIN
            DROP TABLE #DE;
        END 

        SELECT  0 AS IsDEPound
        INTO    #DE UNION ALL
        SELECT  1;

        --Set up plan list
        IF (SELECT  OBJECT_ID ('tempdb..#Plans')) IS NOT NULL
        BEGIN
            DROP TABLE #Plans;
        END 

        SELECT  DISTINCT
                CPS
        INTO    #Plans
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear
                AND (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@CPBPList, ',', 1) )
                     OR @CPBPList IS NULL)
                AND (Region IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@RegionString, ',', 1) )
                     OR @RegionString IS NULL)
                AND (Product IN (SELECT Val FROM dbo.Trend_fnCalcStringToTable (@ProductString, ',', 1) )
                     OR @ProductString IS NULL);


        --Planinfo with adjusted product granularity
        IF (SELECT  OBJECT_ID ('tempdb..#vwPlanInfo')) IS NOT NULL
        BEGIN
            DROP TABLE #vwPlanInfo;
        END

        SELECT  CPS
               ,PlanYear
               ,Division
               ,Region
               ,PlanInfoID
               ,PopRSCredibilityProduct AS Product
        INTO    #vwPlanInfo
        FROM    dbo.vwPlanInfo WITH (NOLOCK)
        WHERE   PlanYear = @BidYear;


        --Crosswalk  
        IF (SELECT  OBJECT_ID ('tempdb..#SAM')) IS NOT NULL 
        BEGIN
            DROP TABLE #SAM;
        END

        SELECT      DISTINCT
                    vsc.BaseYearCPS
                   ,vsc.CurrentYearCPS
                   ,vsc.BidYearCPS
        INTO        #SAM
        FROM        dbo.vwSAMCrosswalks vsc WITH (NOLOCK)
       INNER JOIN   dbo.SavedForecastSetup sfs WITH (NOLOCK)
               ON sfs.PlanInfoID = vsc.BidYearPlanInfoID
                  AND   sfs.ServiceAreaOptionID = vsc.ServiceAreaOptionID
       INNER JOIN   dbo.SavedRollupForecastMap map WITH (NOLOCK)
               ON map.ForecastID = sfs.ForecastID
       INNER JOIN   dbo.SavedRollupInfo sri WITH (NOLOCK)
               ON sri.RollupID = map.RollupID
       INNER JOIN   #Plans p
               ON vsc.BidYearCPS = p.CPS
        WHERE       sri.RollupName = 'Live' -- Live plan, allows for P2P migration in BMAT
                    AND vsc.BidYearRenewalTypeID <> 2 -- excludes BY termed plans
                    AND vsc.BidYearCPS IS NOT NULL;

        --Market adjustments
        IF (SELECT  OBJECT_ID ('tempdb..#MarketAdj')) IS NOT NULL
        BEGIN
            DROP TABLE #MarketAdj;
        END 

        SELECT      a.CPS
                   ,a.BarcClass
                   ,a.IsDEPound
                   ,a.MarketAdjustment
        INTO        #MarketAdj
        FROM        dbo.Trend_SavedPopulationMarketAdjustment a WITH (NOLOCK)
       INNER JOIN   #Plans p
               ON p.CPS = a.CPS;

		--Base Year membership and relativity (included to remove quarters which caused issues with P2P movement)
		IF (SELECT  OBJECT_ID ('tempdb..#BasePop')) IS NOT NULL 
        BEGIN
            DROP TABLE #BasePop;
        END

		SELECT		cps
					,SSStateCountyCD
					,ActuarialDivision
					,ActuarialRegion
					,ProductType
					,PlanYearID
					,ReportingCategory
					,IsDEPound
					,BarcClass
					,RiskInd
					,SUM(BY_MM) AS BY_MM
					,SUM(BY_AllowedFactor) AS BY_AllowedFactor
					,SUM(BY_UseFactor) AS BY_UseFactor
		INTO		#BasePop
		FROM		dbo.Trend_CalcPopulationHistorical
		GROUP BY	CPS
					,SSStateCountyCD
					,ActuarialDivision
					,ActuarialRegion
					,ProductType
					,PlanYearID
					,ReportingCategory
					,IsDEPound
					,BarcClass
					,RiskInd

        --Bid year membership
        IF (SELECT  OBJECT_ID ('tempdb..#BidMM')) IS NOT NULL 
        BEGIN
            DROP TABLE #BidMM;
        END 

        SELECT      a.CPS
                   ,a.SSStateCountyCD
                   ,a.PlanYearID
                   ,a.BarcClass
                   ,CASE WHEN a.PriorYearBarcClass <> 'N' AND   a.BarcClass <> 'A' THEN 'N' ELSE a.PriorYearBarcClass END AS PriorYearBarcClass
                   ,a.IsDEPound
                   ,ISNULL (SUM (a.Bid_MM), 0) AS Bid_MM
        INTO        #BidMM
        FROM        dbo.Trend_SavedPopulationBarcBidYearMembership a WITH (NOLOCK)
       INNER JOIN   #Plans p
               ON p.CPS = a.CPS
        WHERE       a.SSStateCountyCD <> 99999
        GROUP BY    a.CPS
                   ,a.SSStateCountyCD
                   ,a.PlanYearID
                   ,a.BarcClass
                   ,a.PriorYearBarcClass
                   ,a.IsDEPound;

        --Create shell for class A
        IF (SELECT  OBJECT_ID ('tempdb..#BidAShell')) IS NOT NULL
        BEGIN
            DROP TABLE #BidAShell;
        END

        SELECT      DISTINCT
                    bid.CPS
                   ,bid.SSStateCountyCD
                   ,'A' AS BarcClass
                   ,bc.BarcClass AS PriorYearBarcClass
                   ,DE.IsDEPound
        INTO        #BidAShell
        FROM        #BidMM bid
       CROSS JOIN   #BarcClass bc
       CROSS JOIN   #DE DE;

        --Create shell for other classes
        IF (SELECT  OBJECT_ID ('tempdb..#BidAllShell')) IS NOT NULL
        BEGIN
            DROP TABLE #BidAllShell;
        END 
        SELECT      DISTINCT
                    bid.CPS
                   ,bid.SSStateCountyCD
                   ,bc.BarcClass
                   ,DE.IsDEPound
        INTO        #BidAllShell
        FROM        #BidMM bid
       CROSS JOIN   #BarcClass bc
       CROSS JOIN   #DE DE;

        -----------------------------------------
        -- 1. Create temp tables to store data --
        -----------------------------------------

        --1a. Relativities

        --Base year 
        IF (SELECT  OBJECT_ID ('tempdb..#BaseYearFactors')) IS NOT NULL
        BEGIN
	        DROP TABLE #BaseYearFactors ;
	    END
        SELECT      sam.BidYearCPS
                   ,h.SSStateCountyCD
                   ,h.IsDEPound
                   ,h.BarcClass
                   ,CASE WHEN ISNULL (SUM (h.BY_MM), 0) + ISNULL (SUM(p2p.P2POut_MM), 0) + ISNULL (SUM(p2p.P2PIn_MM), 0) < 0 
					 THEN 0 ELSE
					   ISNULL (SUM (h.BY_MM), 0) + ISNULL (sum(p2p.P2POut_MM), 0) + ISNULL (sum(p2p.P2PIn_MM), 0) END --Error handling
				   AS BY_MM
                   ,CASE WHEN ISNULL (SUM (h.BY_UseFactor), 0) + ISNULL (SUM(p2p.P2POut_Rel) , 0) + ISNULL (SUM(p2p.P2PIn_Rel), 0) < 0 
					   THEN 0 ELSE
					   ISNULL (SUM (h.BY_UseFactor), 0) + ISNULL (SUM(p2p.P2POut_Rel), 0) + ISNULL (SUM(p2p.P2PIn_Rel), 0) END --Error handling
				   AS BY_UseFactor
        INTO        #BaseYearFactors
        FROM        #BasePop h WITH (NOLOCK)
        LEFT JOIN   (SELECT DISTINCT BaseYearCPS, BidYearCPS FROM   #SAM) sam
               ON h.CPS = sam.BaseYearCPS --crosswalk the base year plan name to the bid year plan name
		LEFT JOIN	dbo.Trend_PerPopulationP2PImpact p2p WITH (NOLOCK)
			   ON p2p.CPS = h.CPS
			   AND p2p.SSStateCountyCD = h.SSStateCountyCD
			   AND p2p.IsDEPound = h.IsDEPound
			   AND p2p.BarcClass = h.BarcClass
			   AND p2p.PlanYearID = h.PlanYearID
       INNER JOIN   #Plans p
               ON p.CPS = sam.BidYearCPS
       INNER JOIN   #Repcat r
               ON r.ReportingCategory = h.ReportingCategory
        WHERE       h.PlanYearID = @BaseYear
        GROUP BY    sam.BidYearCPS
                   ,h.SSStateCountyCD
                   ,h.IsDEPound
                   ,h.BarcClass;

        --Current year 
        IF (SELECT  OBJECT_ID ('tempdb..#CurrentYearFactors')) IS NOT NULL
        BEGIN
	        DROP TABLE #CurrentYearFactors ;
	    END
        SELECT      sam.BidYearCPS
                   ,c.SSStateCountyCD
                   ,c.IsDEPound
                   ,c.BarcClass
                   ,ISNULL (SUM (c.CY_MM), 0) AS CY_MM
                   ,ISNULL (SUM (c.CY_UseFactor), 0) AS CY_UseFactor
        INTO        #CurrentYearFactors
        FROM        dbo.Trend_CalcPopulationCurrentYear c WITH (NOLOCK)
        LEFT JOIN   (SELECT DISTINCT CurrentYearCPS, BidYearCPS FROM #SAM) sam
               ON c.CPS = sam.CurrentYearCPS --crosswalk the current year plan name to the bid year plan name
       INNER JOIN   #Plans p
               ON p.CPS = sam.BidYearCPS
       INNER JOIN   #Repcat r
               ON r.ReportingCategory = c.ReportingCategory
        GROUP BY    sam.BidYearCPS
                   ,c.SSStateCountyCD
                   ,c.IsDEPound
                   ,c.BarcClass;

        --Retained current year 
        IF (SELECT  OBJECT_ID ('tempdb..#PriorCYFactors')) IS NOT NULL
        BEGIN
	        DROP TABLE #PriorCYFactors ;
	    END
        SELECT      sam.BidYearCPS
                   ,c.SSStateCountyCD
                   ,c.IsDEPound
                   ,c.PriorYearBarcClass
                   ,ISNULL (SUM (c.CY_MM), 0) AS PriorCY_MM
                   ,ISNULL (SUM (c.CY_UseFactor), 0) AS PriorCY_UseFactor
        INTO        #PriorCYFactors
        FROM        dbo.Trend_CalcPopulationCurrentYear c WITH (NOLOCK)
        LEFT JOIN   (SELECT DISTINCT CurrentYearCPS, BidYearCPS FROM #SAM) sam
               ON c.CPS = sam.CurrentYearCPS --crosswalk the current year plan name to the bid year plan name
       INNER JOIN   #Plans p
               ON p.CPS = sam.BidYearCPS
       INNER JOIN   #Repcat r
               ON r.ReportingCategory = c.ReportingCategory
        WHERE       c.BarcClass = 'A'
        GROUP BY    sam.BidYearCPS
                   ,c.SSStateCountyCD
                   ,c.IsDEPound
                   ,c.PriorYearBarcClass
                   ,c.BarcClass;


        --1b. Membership data

        --Retained bid year
        IF (SELECT  OBJECT_ID ('tempdb..#RetainedMM')) IS NOT NULL
        BEGIN
	        DROP TABLE #RetainedMM ;
	    END
        SELECT      a.CPS
                   ,a.SSStateCountyCD
                   ,a.PlanYearID
                   ,a.BarcClass
                   ,a.PriorYearBarcClass
                   ,a.IsDEPound
                   ,ISNULL (SUM (a.Bid_MM), 0) AS Retained_MM
        INTO        #RetainedMM
        FROM        dbo.Trend_SavedPopulationBarcBidYearMembership a WITH (NOLOCK)
       INNER JOIN   #Plans p
               ON p.CPS = a.CPS
        WHERE       a.BarcClass = 'A'
                    AND a.SSStateCountyCD <> 99999
        GROUP BY    a.CPS
                   ,a.SSStateCountyCD
                   ,a.PlanYearID
                   ,a.BarcClass
                   ,a.PriorYearBarcClass
                   ,a.IsDEPound;

        --1c. Credibility data

        --Region, product, DE# 

        --Base
        IF (SELECT  OBJECT_ID ('tempdb..#BaseRegProdDE')) IS NOT NULL
        BEGIN
            DROP TABLE #BaseRegProdDE ;
        END

        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #BaseRegProdDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Region, Product'
                AND PlanYearID = @BaseYear;

        --Current
        IF (SELECT  OBJECT_ID ('tempdb..#CurrentRegProdDE')) IS NOT NULL
          BEGIN
	         DROP TABLE #CurrentRegProdDE ;
	       END

        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #CurrentRegProdDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Region, Product'
                AND PlanYearID = @CurrentYear
                AND IsRetained = 0;


        --Retained Current
        IF (SELECT  OBJECT_ID ('tempdb..#PriorCYRegProdDE')) IS NOT NULL
        BEGIN
            DROP TABLE #PriorCYRegProdDE ;
        END

        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #PriorCYRegProdDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Region, Product'
                AND PlanYearID = @CurrentYear
                AND IsRetained = 1;

        --Product, DE# 

        --Base
        IF (SELECT  OBJECT_ID ('tempdb..#BaseProdDE')) IS NOT NULL
        BEGIN
	        DROP TABLE #BaseProdDE ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #BaseProdDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide, Product'
                AND IsDEPound IS NOT NULL
                AND PlanYearID = @BaseYear;

        --Current
        IF (SELECT  OBJECT_ID ('tempdb..#CurrentProdDE')) IS NOT NULL
        BEGIN
	        DROP TABLE #CurrentProdDE ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #CurrentProdDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide, Product'
                AND IsDEPound IS NOT NULL
                AND PlanYearID = @CurrentYear
                AND IsRetained = 0;


        --Retained Current
        IF (SELECT  OBJECT_ID ('tempdb..#PriorCYProdDE')) IS NOT NULL
        BEGIN
	        DROP TABLE #PriorCYProdDE ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #PriorCYProdDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide, Product'
                AND IsDEPound IS NOT NULL
                AND PlanYearID = @CurrentYear
                AND IsRetained = 1;

        --DE#

        --Base
        IF (SELECT  OBJECT_ID ('tempdb..#BaseDE')) IS NOT NULL BEGIN
	        DROP TABLE #BaseDE ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #BaseDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide'
                AND PlanYearID = @BaseYear;

        --Current
        IF (SELECT  OBJECT_ID ('tempdb..#CurrentDE')) IS NOT NULL
        BEGIN
	        DROP TABLE #CurrentDE ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #CurrentDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide'
                AND PlanYearID = @CurrentYear
                AND IsRetained = 0;


        --Retained Current
        IF (SELECT  OBJECT_ID ('tempdb..#PriorCYDE')) IS NOT NULL
        BEGIN
	        DROP TABLE #PriorCYDE ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #PriorCYDE
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide'
                AND PlanYearID = @CurrentYear
                AND IsRetained = 1;

        --Product 

        --Base
        IF (SELECT  OBJECT_ID ('tempdb..#BaseProd')) IS NOT NULL
        BEGIN
	        DROP TABLE #BaseProd ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #BaseProd
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide, Product'
                AND IsDEPound IS NULL
                AND PlanYearID = @BaseYear;

        --Current
        IF (SELECT  OBJECT_ID ('tempdb..#CurrentProd')) IS NOT NULL
        BEGIN
	        DROP TABLE #CurrentProd ;
	    END
        SELECT  IsDEPound
               ,BarcClass
               ,Region
               ,Product
               ,PlanYearID
               ,IsRetained
               ,Credibility
               ,UseRelativity
        INTO    #CurrentProd
        FROM    dbo.Trend_CalcPopulationCredibility WITH (NOLOCK)
        WHERE   Granularity = 'Nationwide, Product'
                AND IsDEPound IS NULL
                AND PlanYearID = @CurrentYear
                AND IsRetained = 0;

        ------------------------------------------------------------
        --2. Calculate factors and credibility by county and plan --
        ------------------------------------------------------------

        --Class A

        --County 
        IF (SELECT  OBJECT_ID ('tempdb..#CombinedCountyA')) IS NOT NULL
        BEGIN
	        DROP TABLE #CombinedCountyA ;
	    END
        SELECT      bid.CPS
                   ,bid.SSStateCountyCD
                   ,bid.BarcClass
                   ,bid.PriorYearBarcClass
                   ,bid.IsDEPound
                   ,ISNULL (base.BY_MM, 0) AS BY_MM --Use IsNULL to handle NULL values with shell logic
                   ,CASE WHEN ISNULL (base.BY_MM, 0) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(ISNULL (base.BY_MM, 0) AS FLOAT) / @CredibleMemberMonths) AS FLOAT)END AS BY_Credibility
                   ,ISNULL (base.BY_UseFactor, 0) AS BY_useFactor
                   ,ISNULL (pcy.PriorCY_MM, 0) AS PriorCY_MM
                   ,CASE WHEN ISNULL (pcy.PriorCY_MM, 0) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(ISNULL (pcy.PriorCY_MM, 0) AS FLOAT) / @CredibleMemberMonths) AS FLOAT)END AS PriorCY_Credibility
                   ,ISNULL (pcy.PriorCY_UseFactor, 0) AS PriorCY_UseFactor
        INTO        #CombinedCountyA
        FROM        #BidAShell bid -- Only pull in counties included in the bid service area
        LEFT JOIN   #BaseYearFactors base
               ON bid.CPS = base.BidyearCPS
                  AND   bid.SSStateCountyCD = base.SSStateCountyCD
                  AND   bid.PriorYearBarcClass = base.BarcClass
                  AND   bid.IsDEPound = base.IsDEPound
        LEFT JOIN   #PriorCYFactors pcy
               ON bid.CPS = pcy.BidyearCPS
                  AND   bid.SSStateCountyCD = pcy.SSStateCountyCD
                  AND   bid.PriorYearBarcClass = pcy.PriorYearBarcClass
                  AND   bid.IsDEPound = pcy.IsDEPound
        WHERE       bid.BarcClass = 'A';


        -- Plan 
        IF (SELECT  OBJECT_ID ('tempdb..#CredibilityFillPlanA')) IS NOT NULL
        BEGIN
	        DROP TABLE #CredibilityFillPlanA ;
	    END
        SELECT      CPS
                   ,BarcClass
                   ,PriorYearBarcClass
                   ,IsDEPound
                   ,SUM (BY_MM) AS BY_MM
                   ,CASE WHEN SUM (BY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT ((SUM (CAST(BY_MM AS FLOAT) / @CredibleMemberMonths))) AS FLOAT)END AS BY_Credibility
                   ,dbo.Trend_fnSafeDivide (SUM (BY_useFactor), SUM (BY_MM), 1) AS BY_UseFactor
                   ,SUM (PriorCY_MM) AS PriorCY_MM
                   ,CASE WHEN SUM (PriorCY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT ((SUM (CAST(PriorCY_MM AS FLOAT) / @CredibleMemberMonths))) AS FLOAT)END AS PriorCY_Credibility
                   ,dbo.Trend_fnSafeDivide (SUM (PriorCY_UseFactor), SUM (PriorCY_MM), 1) AS PriorCY_UseFactor
        INTO        #CredibilityFillPlanA
        FROM        #CombinedCountyA
        GROUP BY    CPS
                   ,BarcClass
                   ,PriorYearBarcClass
                   ,IsDEPound;


        -- All classes

        --County
        IF (SELECT  OBJECT_ID ('tempdb..#CombinedCountyAll')) IS NOT NULL
        BEGIN
	        DROP TABLE #CombinedCountyAll ;
	    END

        SELECT      bid.CPS
                   ,bid.SSStateCountyCD
                   ,bid.BarcClass
                   ,bid.IsDEPound
                   ,ISNULL (cy.CY_MM, 0) AS CY_MM   --Use IsNULL to handle NULL values with shell logic
                   ,CASE WHEN ISNULL (cy.CY_MM, 0) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (CAST(ISNULL (cy.CY_MM, 0) AS FLOAT) / @CredibleMemberMonths) AS FLOAT)END AS CY_Credibility
                   ,cy.CY_UseFactor
        INTO        #CombinedCountyAll
        FROM        #BidAllShell bid -- Only pull in counties included in the bid service area
       CROSS JOIN   #Repcat rc
        LEFT JOIN   #CurrentYearFactors cy
               ON bid.CPS = cy.BidyearCPS
                  AND   bid.SSStateCountyCD = cy.SSStateCountyCD
                  AND   bid.BarcClass = cy.BarcClass
                  AND   bid.IsDEPound = cy.IsDEPound;

        -- Plan 
        IF (SELECT  OBJECT_ID ('tempdb..#CredibilityFillPlanAll')) IS NOT NULL
        BEGIN
	        DROP TABLE #CredibilityFillPlanAll ;
	    END
        SELECT      CPS
                   ,BarcClass
                   ,IsDEPound
                   ,SUM (CY_MM) AS CY_MM
                   ,CASE WHEN SUM (CY_MM) >= @CredibleMemberMonths THEN 1
                         ELSE CAST(SQRT (SUM (CAST(CY_MM AS FLOAT)) / @CredibleMemberMonths) AS FLOAT)END AS CY_Credibility
                   ,dbo.Trend_fnSafeDivide (SUM (CY_UseFactor), SUM (CY_MM), 1) AS CY_UseFactor
        INTO        #CredibilityFillPlanAll
        FROM        #CombinedCountyAll
        GROUP BY    CPS
                   ,BarcClass
                   ,IsDEPound;



        -----------------------------------------------------------
        --3. Perform credibility calculations----------------------
        -----------------------------------------------------------

        --Class A

        --Combine credibility data to calculate credible cuts and factors
        IF (SELECT  OBJECT_ID ('tempdb..#CombinedCredibilityA')) IS NOT NULL
        BEGIN
	        DROP TABLE #CombinedCredibilityA ;
	    END
        SELECT      cc.CPS
                   ,cc.SSStateCountyCD
                   ,cc.BarcClass
                   ,cc.PriorYearBarcClass
                   ,cc.IsDEPound
                   ,ISNULL (cc.BY_Credibility, 0) AS BY_Credibility
                   ,dbo.Trend_fnSafeDivide (ISNULL (cc.BY_useFactor, 0), ISNULL (cc.BY_MM, 0), 1) AS BY_CountyFactor
                   ,ISNULL (cp.BY_UseFactor, 1) AS BY_PlanFactor
                   ,ISNULL (brp.UseRelativity, 1) AS BY_RegProdFactor
                   ,ISNULL (bpde.UseRelativity, 1) AS BY_ProdDEFactor
                   ,ISNULL (bde.UseRelativity, 1) AS BY_DEFactor
                   ,ISNULL (cc.PriorCY_Credibility, 0) AS PriorCY_Credibility
                   ,dbo.Trend_fnSafeDivide (ISNULL (cc.PriorCY_UseFactor, 0), ISNULL (cc.PriorCY_MM, 0), 1) AS PriorCY_CountyFactor
                   ,ISNULL (cp.PriorCY_UseFactor, 1) AS PriorCY_PlanFactor
                   ,ISNULL (crp.UseRelativity, 1) AS PriorCY_RegProdFactor
                   ,ISNULL (cpde.UseRelativity, 1) AS PriorCY_ProdDEFactor
                   ,ISNULL (cde.UseRelativity, 1) AS PriorCY_DEFactor
                   ,CASE WHEN ISNULL (cc.BY_Credibility, 0) = 1 THEN 1
                         WHEN ISNULL (cp.BY_Credibility, 0) = 1 THEN 2
                         WHEN ISNULL (brp.Credibility, 0) = 1 THEN 3
                         WHEN ISNULL (bpde.Credibility, 0) = 1 THEN 4
                         ELSE 5 END AS BY_CredibleCut
                   ,CASE WHEN ISNULL (cc.PriorCY_Credibility, 0) = 1 THEN 1
                         WHEN ISNULL (cp.PriorCY_Credibility, 0) = 1 THEN 2
                         WHEN ISNULL (crp.Credibility, 0) = 1 THEN 3
                         WHEN ISNULL (cpde.Credibility, 0) = 1 THEN 4
                         ELSE 5 END AS PriorCY_CredibleCut
        INTO        #CombinedCredibilityA
        FROM        #CombinedCountyA cc
        LEFT JOIN   #vwPlanInfo vpi
               ON vpi.CPS = cc.CPS
        LEFT JOIN   #CredibilityFillPlanA cp
               ON cc.CPS = cp.CPS
                  AND   cc.BarcClass = cp.BarcClass
                  AND   cc.PriorYearBarcClass = cp.PriorYearBarcClass
                  AND   cc.IsDEPound = cp.IsDEPound
        LEFT JOIN   #BaseRegProdDE brp
               ON vpi.Region = brp.Region
                  AND   vpi.Product = brp.Product
                  AND   cc.PriorYearBarcClass = brp.BarcClass
                  AND   cc.IsDEPound = brp.IsDEPound
        LEFT JOIN   #PriorCYRegProdDE crp
               ON vpi.Region = crp.Region
                  AND   vpi.Product = crp.Product
                  AND   cc.PriorYearBarcClass = crp.BarcClass
                  AND   cc.IsDEPound = crp.IsDEPound
        LEFT JOIN   #BaseProdDE bpde
               ON vpi.Product = bpde.Product
                  AND   cc.PriorYearBarcClass = bpde.BarcClass
                  AND   cc.IsDEPound = bpde.IsDEPound
        LEFT JOIN   #PriorCYProdDE cpde
               ON vpi.Product = cpde.Product
                  AND   cc.PriorYearBarcClass = cpde.BarcClass
                  AND   cc.IsDEPound = cpde.IsDEPound
        LEFT JOIN   #BaseDE bde
               ON cc.PriorYearBarcClass = bde.BarcClass
                  AND   cc.IsDEPound = bde.IsDEPound
        LEFT JOIN   #PriorCYDE cde
               ON cc.PriorYearBarcClass = cde.BarcClass
                  AND   cc.IsDEPound = cde.IsDEPound;


        --Determine highest credible cut
        IF (SELECT  OBJECT_ID ('tempdb..#HighestCredCutA')) IS NOT NULL
        BEGIN
	        DROP TABLE #HighestCredCutA ;
	    END
        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,PriorYearBarcClass
               ,IsDEPound
               ,CASE WHEN BY_CredibleCut > PriorCY_CredibleCut THEN BY_CredibleCut ELSE PriorCY_CredibleCut END AS HighestCredibleCut
        INTO    #HighestCredCutA
        FROM    #CombinedCredibilityA;


        --Calculate credibile factors and credibility 		
        IF (SELECT  OBJECT_ID ('tempdb..#CredibleFactorsA')) IS NOT NULL
        BEGIN
	        DROP TABLE #CredibleFactorsA ;
	    END
        SELECT      cc.CPS
                   ,cc.SSStateCountyCD
                   ,cc.BarcClass
                   ,cc.PriorYearBarcClass
                   ,cc.IsDEPound
                   ,cc.BY_CountyFactor
                   ,cc.PriorCY_CountyFactor
                   ,CASE WHEN hcc.HighestCredibleCut = 1 THEN cc.BY_CountyFactor
                         WHEN hcc.HighestCredibleCut = 2 THEN cc.BY_PlanFactor
                         WHEN hcc.HighestCredibleCut = 3 THEN cc.BY_RegProdFactor
                         WHEN hcc.HighestCredibleCut = 4 THEN cc.BY_ProdDEFactor
                         ELSE cc.BY_DEFactor END AS BY_CredFactor
                   ,CASE WHEN hcc.HighestCredibleCut = 1 THEN cc.PriorCY_CountyFactor
                         WHEN hcc.HighestCredibleCut = 2 THEN cc.PriorCY_PlanFactor
                         WHEN hcc.HighestCredibleCut = 3 THEN cc.PriorCY_RegProdFactor
                         WHEN hcc.HighestCredibleCut = 4 THEN cc.PriorCY_ProdDEFactor
                         ELSE cc.PriorCY_DEFactor END AS PriorCY_CredFactor
                   ,CASE WHEN cc.BY_Credibility < cc.PriorCY_Credibility THEN cc.BY_Credibility
                         ELSE cc.PriorCY_Credibility END AS MinCredibility
        INTO        #CredibleFactorsA
        FROM        #CombinedCredibilityA cc
        LEFT JOIN   #HighestCredCutA hcc
               ON hcc.CPS = cc.CPS
                  AND   hcc.SSStateCountyCD = cc.SSStateCountyCD
                  AND   hcc.BarcClass = cc.BarcClass
                  AND   hcc.PriorYearBarcClass = cc.PriorYearBarcClass
                  AND   hcc.IsDEPound = cc.IsDEPound;

        --Calculate credibility weighted factors and trends		
        IF (SELECT  OBJECT_ID ('tempdb..#CredibilityCalcA')) IS NOT NULL
        BEGIN
	        DROP TABLE #CredibilityCalcA ;
	    END
        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,PriorYearBarcClass
               ,IsDEPound
               ,dbo.Trend_fnSafeDivide (
                CAST(CAST(PriorCY_CountyFactor * MinCredibility AS FLOAT)
                     + CAST(CAST((1 - MinCredibility) AS FLOAT) * PriorCY_CredFactor AS FLOAT) AS FLOAT)
               ,CAST(CAST(BY_CountyFactor * MinCredibility AS FLOAT)
                     + CAST(CAST((1 - MinCredibility) AS FLOAT) * BY_CredFactor AS FLOAT) AS FLOAT)
               ,0) - 1 AS RetainedTrend
        INTO    #CredibilityCalcA
        FROM    #CredibleFactorsA;

        --All classes

        --Combine credibility data to calculate credible cuts and factors
        IF (SELECT  OBJECT_ID ('tempdb..#CombinedCredibilityAll')) IS NOT NULL
        BEGIN
	        DROP TABLE #CombinedCredibilityAll ;
	    END
        SELECT      cc.CPS
                   ,cc.SSStateCountyCD
                   ,cc.BarcClass
                   ,cc.IsDEPound
                   ,dbo.Trend_fnSafeDivide (ISNULL (cc.CY_UseFactor, 0), ISNULL (cc.CY_MM, 0), 1) AS CY_CountyFactor
                   ,ISNULL (cc.CY_Credibility, 0) AS CY_Credibility
                   ,CASE WHEN ISNULL (cc.CY_Credibility, 0) = 1 THEN
                             dbo.Trend_fnSafeDivide (ISNULL (cc.CY_UseFactor, 0), ISNULL (cc.CY_MM, 0), 1)
                         WHEN ISNULL (cp.CY_Credibility, 0) = 1 THEN cp.CY_UseFactor
                         WHEN ISNULL (crp.Credibility, 0) = 1 THEN crp.UseRelativity
                         WHEN ISNULL (cpde.Credibility, 0) = 1 THEN cpde.UseRelativity
                         ELSE ISNULL (cde.UseRelativity, 1) END AS CY_CredFactor
                   ,ISNULL (cpde.UseRelativity, 1) AS CY_ProdFactor
				   ,ISNULL (cpde.Credibility,0) AS CY_ProdCred 
                   ,ISNULL (bpde.UseRelativity, 1) AS BY_ProdFactor
				   ,ISNULL (bpde.Credibility,0) AS BY_ProdCred
				   ,cde.UseRelativity AS CY_NWFactor
				   ,bde.UseRelativity AS BY_NWFactor
        INTO        #CombinedCredibilityAll
        FROM        #CombinedCountyAll cc
        LEFT JOIN   #vwPlanInfo vpi
               ON vpi.CPS = cc.CPS
        LEFT JOIN   #CredibilityFillPlanAll cp
               ON cc.CPS = cp.CPS
                  AND   cc.BarcClass = cp.BarcClass
                  AND   cc.IsDEPound = cp.IsDEPound
        LEFT JOIN   #BaseRegProdDE brp
               ON vpi.Region = brp.Region
                  AND   vpi.Product = brp.Product
                  AND   cc.BarcClass = brp.BarcClass
                  AND   cc.IsDEPound = brp.IsDEPound
        LEFT JOIN   #CurrentRegProdDE crp
               ON vpi.Region = crp.Region
                  AND   vpi.Product = crp.Product
                  AND   cc.BarcClass = crp.BarcClass
                  AND   cc.IsDEPound = crp.IsDEPound
        LEFT JOIN   #BaseProdDE bpde
               ON vpi.Product = bpde.Product
                  AND   cc.BarcClass = bpde.BarcClass
                  AND   cc.IsDEPound = bpde.IsDEPound
        LEFT JOIN   #CurrentProdDE cpde
               ON vpi.Product = cpde.Product
                  AND   cc.BarcClass = cpde.BarcClass
                  AND   cc.IsDEPound = cpde.IsDEPound
        LEFT JOIN   #BaseDE bde
               ON cc.BarcClass = bde.BarcClass
                  AND   cc.IsDEPound = bde.IsDEPound
        LEFT JOIN   #CurrentDE cde
               ON cc.BarcClass = cde.BarcClass
                  AND   cc.IsDEPound = cde.IsDEPound
		--Removed to develop Class B trends at DEPound granularity
        --LEFT JOIN   #BaseProd bprod
        --       ON vpi.Product = bprod.Product
        --          AND   cc.BarcClass = bprod.BarcClass
        --LEFT JOIN   #CurrentProd cprod
        --       ON vpi.Product = cprod.Product
        --          AND   cc.BarcClass = cprod.BarcClass;

        --Calculate credibile factors and credibility 		
        IF (SELECT  OBJECT_ID ('tempdb..#CredibleFactorsAll')) IS NOT NULL
        BEGIN
	        DROP TABLE #CredibleFactorsAll ;
	    END
        SELECT  cc.CPS
               ,cc.SSStateCountyCD
               ,cc.BarcClass
               ,cc.IsDEPound
               ,cc.CY_CountyFactor
               ,cc.CY_CredFactor
               ,cc.CY_ProdFactor
			   ,cc.CY_ProdCred --Added for new Class B Trend Methodology
               ,cc.BY_ProdFactor
			   ,cc.BY_ProdCred --Added for new Class B Trend Methodology
			   ,cc.CY_NWFactor --Added for new Class B Trend Methodology
			   ,cc.BY_NWFactor --Added for new Class B Trend Methodology
               ,cc.CY_Credibility
        INTO    #CredibleFactorsAll
        FROM    #CombinedCredibilityAll cc;

        --Calculate credibility weighted factors and trends		
        IF (SELECT  OBJECT_ID ('tempdb..#CredibilityCalcAll')) IS NOT NULL
        BEGIN
	        DROP TABLE #CredibilityCalcAll ;
	    END
        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,IsDEPound
               ,CASE WHEN CY_ProdCred + BY_ProdCred = 2 --Checks if both years' NW DE product factors are fully credible. If not, uses NW DE factors
					THEN dbo.Trend_fnSafeDivide (CY_ProdFactor, BY_ProdFactor, 0) - 1 
					ELSE dbo.Trend_fnSafeDivide(CY_NWFactor, BY_NWFactor,0) -1 end
				AS ClassBTrend
               ,CAST(CAST(CY_CountyFactor * CY_Credibility AS FLOAT)
                     + CAST(CY_CredFactor * CAST((1 - CY_Credibility) AS FLOAT) AS FLOAT) AS FLOAT) AS CYCredibilityWeightedFactor
        INTO    #CredibilityCalcAll
        FROM    #CredibleFactorsAll;

        -----------------------------------------------------------	  
        --4. Trend factors into the bid year ----------------------
        -----------------------------------------------------------	  

        --4a. For reporting process

        --Class A

        --Apply trend
        IF (SELECT  OBJECT_ID ('tempdb..#TrendedFactorsA_ForRpt')) IS NOT NULL
        BEGIN
	        DROP TABLE #TrendedFactorsA_ForRpt ;
	    END
        SELECT      f.CPS
                   ,f.SSStateCountyCD
                   ,f.BarcClass
                   ,t.PriorYearBarcClass
                   ,t.IsDEPound
                   ,CAST(f.CYCredibilityWeightedFactor * CAST((1 + t.RetainedTrend) AS FLOAT) AS FLOAT) AS ClassAFactor
        INTO        #TrendedFactorsA_ForRpt
        FROM        #CredibilityCalcAll f
        LEFT JOIN   #CredibilityCalcA t
               ON t.PriorYearBarcClass = f.BarcClass
                  AND   t.CPS = f.CPS
                  AND   t.IsDEPound = f.IsDEPound
                  AND   t.SSStateCountyCD = f.SSStateCountyCD;

        --Roll up class A 
        IF (SELECT  OBJECT_ID ('tempdb..#ClassA_ForRpt')) IS NOT NULL
        BEGIN
	        DROP TABLE #ClassA_ForRpt ;
	    END
        SELECT      tf.CPS
                   ,tf.SSStateCountyCD
                   ,rm.BarcClass
                   ,rm.PriorYearBarcClass
                   ,tf.IsDEPound
                   ,dbo.Trend_fnSafeDivide (SUM (CAST(tf.ClassAFactor * rm.Retained_MM AS FLOAT)), SUM (rm.Retained_MM), 1) AS FinalFactor
        INTO        #ClassA_ForRpt
        FROM        #TrendedFactorsA_ForRpt tf
       INNER JOIN   #RetainedMM rm
               ON rm.CPS = tf.CPS
                  AND   rm.SSStateCountyCD = tf.SSStateCountyCD
                  AND   rm.PriorYearBarcClass = tf.PriorYearBarcClass
                  AND   rm.IsDEPound = tf.IsDEPound
        GROUP BY    tf.CPS
                   ,tf.SSStateCountyCD
                   ,rm.BarcClass
                   ,rm.PriorYearBarcClass
                   ,tf.IsDEPound;

        ---All other classes 
        IF (SELECT  OBJECT_ID ('tempdb..#ClassOther_ForRpt')) IS NOT NULL
        BEGIN
	        DROP TABLE #ClassOther_ForRpt ;
	    END
        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,'N' AS PriorYearBarcClass
               ,IsDEPound
               ,CASE WHEN BarcClass = 'B' THEN
                     CAST(CYCredibilityWeightedFactor * CAST((1 + CASE WHEN ClassBTrend < 0 THEN 0 ELSE ClassBTrend END) AS FLOAT) AS FLOAT) --Floors Class B Trends at 0
                     ELSE CYCredibilityWeightedFactor END 
					AS FinalFactor
        INTO    #ClassOther_ForRpt
        FROM    #CredibilityCalcAll
        WHERE   BarcClass <> 'A';


        --Combine classes together 
        IF (SELECT  OBJECT_ID ('tempdb..#CountyFactors_ForRpt')) IS NOT NULL
        BEGIN
	        DROP TABLE #CountyFactors_ForRpt ;
	    END
        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,PriorYearBarcClass
               ,IsDEPound
               ,FinalFactor
        INTO    #Countyfactors_ForRpt
        FROM    #ClassA_ForRpt
        UNION ALL
        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,PriorYearBarcClass
               ,IsDEPound
               ,FinalFactor
        FROM    #ClassOther_ForRpt;


        --Roll up to plan level
        IF (SELECT  OBJECT_ID ('tempdb..#PlanFactors_ForRpt')) IS NOT NULL
        BEGIN
            DROP TABLE #PlanFactors_ForRpt ;
        END

        SELECT      a.CPS
                   ,a.BarcClass
                   ,a.PriorYearBarcClass
                   ,a.IsDEPound
                   ,SUM (c.Bid_MM) AS Bid_MM
                   ,dbo.Trend_fnSafeDivide (SUM (CAST(a.FinalFactor * c.Bid_MM AS FLOAT)), SUM (c.Bid_MM), 1) AS PlanFactor
        INTO        #PlanFactors_ForRpt
        FROM        #Countyfactors_ForRpt a
        LEFT JOIN   #BidMM c
               ON c.CPS = a.CPS
                  AND   c.BarcClass = a.BarcClass
                  AND   c.PriorYearBarcClass = a.PriorYearBarcClass
                  AND   c.IsDEPound = a.IsDEPound
                  AND   c.SSStateCountyCD = a.SSStateCountyCD
        GROUP BY    a.CPS
                   ,a.BarcClass
                   ,a.IsDEPound
                   ,a.PriorYearBarcClass;


        --Apply market adjustments and offsets 
        IF (SELECT  OBJECT_ID ('tempdb..#AdjFact_ForRpt')) IS NOT NULL
        BEGIN
            DROP TABLE #AdjFact_ForRpt ;
        END

        SELECT      a.CPS
                   ,a.BarcClass
                   ,a.PriorYearBarcClass
                   ,a.IsDEPound
                   ,a.Bid_MM
                   ,CAST(a.PlanFactor * CAST(1 + CAST(ISNULL (ma.MarketAdjustment, 0) * mao.MarketAdjustmentOffset AS FLOAT) AS FLOAT) AS FLOAT) AS AdjustedFactor
        INTO        #AdjFact_ForRpt
        FROM        #PlanFactors_ForRpt a
        LEFT JOIN   #MarketAdj ma
               ON a.CPS = ma.CPS
                  AND   a.BarcClass = ma.BarcClass
                  AND   a.IsDEPound = ma.IsDEPound
		LEFT JOIN	dbo.Trend_PerPopulationMarketAdjustmentOffset mao
			   ON a.BarcClass = mao.BarcClass
				  AND a.IsDEPound = mao.isDePound	
				  ;


        --4b. For trend process

        --Roll up class A at BARC Class/DE Pound level
        IF (SELECT  OBJECT_ID ('tempdb..#ClassA_ForTrendProcess')) IS NOT NULL
        BEGIN
            DROP TABLE #ClassA_ForTrendProcess;
        END 

        SELECT      tf.CPS
                   ,tf.SSStateCountyCD
                   ,rm.BarcClass
                   ,tf.IsDEPound
                   ,dbo.Trend_fnSafeDivide (SUM (CAST(tf.ClassAFactor * rm.Retained_MM AS FLOAT)), SUM (rm.Retained_MM), 1) AS FinalFactor
        INTO        #ClassA_ForTrendProcess
        FROM        #TrendedFactorsA_ForRpt tf
       INNER JOIN   #RetainedMM rm
               ON rm.CPS = tf.CPS
                  AND   rm.SSStateCountyCD = tf.SSStateCountyCD
                  AND   rm.PriorYearBarcClass = tf.PriorYearBarcClass
                  AND   rm.IsDEPound = tf.IsDEPound
        GROUP BY    tf.CPS
                   ,tf.SSStateCountyCD
                   ,rm.BarcClass
                   ,tf.IsDEPound;

        --Apply trend at BARC Class/DE Pound level
        IF (SELECT  OBJECT_ID ('tempdb..#ClassOther_ForTrendProcess')) IS NOT NULL
        BEGIN
            DROP TABLE #ClassOther_ForTrendProcess ;
        END

        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,IsDEPound
               ,CASE WHEN BarcClass = 'B' THEN
					CAST(CYCredibilityWeightedFactor * CAST((1 + CASE WHEN ClassBTrend < 0 THEN 0 ELSE ClassBTrend End) AS FLOAT) AS FLOAT) -- Floors Class B Trends at 0
					ELSE CYCredibilityWeightedFactor END 
				AS FinalFactor
        INTO    #ClassOther_ForTrendProcess
        FROM    #CredibilityCalcAll
        WHERE   BarcClass <> 'A';

        --Combine classes together 
        IF (SELECT  OBJECT_ID ('tempdb..#CountyFactors_ForTrendProcess')) IS NOT NULL
        BEGIN
            DROP TABLE #CountyFactors_ForTrendProcess ;
        END

        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,IsDEPound
               ,FinalFactor
        INTO    #CountyFactors_ForTrendProcess
        FROM    #ClassA_ForTrendProcess
        UNION ALL
        SELECT  CPS
               ,SSStateCountyCD
               ,BarcClass
               ,IsDEPound
               ,FinalFactor
        FROM    #ClassOther_ForTrendProcess;

        --Roll up to plan level
        IF (SELECT  OBJECT_ID ('tempdb..#PlanFactors_ForTrendProcess')) IS NOT NULL
        BEGIN 
            DROP TABLE #PlanFactors_ForTrendProcess ;
        END

        SELECT      a.CPS
                   ,a.BarcClass
                   ,a.IsDEPound
                   ,SUM (c.Bid_MM) AS Bid_MM
                   ,dbo.Trend_fnSafeDivide (SUM (CAST(a.FinalFactor * c.Bid_MM AS FLOAT)), SUM (c.Bid_MM), 1) AS PlanFactor
        INTO        #PlanFactors_ForTrendProcess
        FROM        #CountyFactors_ForTrendProcess a
        LEFT JOIN   #BidMM c
               ON c.CPS = a.CPS
                  AND   c.BarcClass = a.BarcClass
                  AND   c.IsDEPound = a.IsDEPound
                  AND   c.SSStateCountyCD = a.SSStateCountyCD
        GROUP BY    a.CPS
                   ,a.BarcClass
                   ,a.IsDEPound;


        --Apply market adjustments and offsets 
        IF (SELECT  OBJECT_ID ('tempdb..#AdjFact_ForTrendProcess')) IS NOT NULL
        BEGIN
            DROP TABLE #AdjFact_ForTrendProcess ;
        END

        SELECT      a.CPS
                   ,a.BarcClass
                   ,a.IsDEPound
                   ,a.Bid_MM
                   ,CAST(a.PlanFactor * CAST(1 + CAST(ISNULL (ma.MarketAdjustment, 0) * mao.MarketAdjustmentOffset AS FLOAT) AS FLOAT) AS FLOAT) AS AdjustedFactor
        INTO        #AdjFact_ForTrendProcess
        FROM        #PlanFactors_ForTrendProcess a
        LEFT JOIN   #MarketAdj ma
               ON a.CPS = ma.CPS
                  AND   a.BarcClass = ma.BarcClass
                  AND   a.IsDEPound = ma.IsDEPound
		LEFT JOIN	dbo.Trend_PerPopulationMarketAdjustmentOffset mao
			   ON a.BarcClass = mao.BarcClass
				  AND a.IsDEPound = mao.isDePound			  
				  ;



        -----------------------------------------------------------	  
        -- 5. Insert final results --------------------------------
        -----------------------------------------------------------	  

        --5a. For reporting process

        --Delete factors 
        DELETE  FROM dbo.Trend_PopulationReporting_CalcRelativity WITH(ROWLOCK)
        WHERE   PlanYearID = @BidYear
                AND CPS IN (SELECT  CPS FROM #Plans);

        --Insert factors
        INSERT INTO dbo.Trend_PopulationReporting_CalcRelativity WITH(ROWLOCK)
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,QuarterID
            ,BarcClass
            ,PriorYearBarcClass
            ,IsDEPound
            ,MemberMonths
            ,UseRelativityxMemberMonths
            ,LastUpdateByID
            ,LastUpdateDateTime)

        --Bid year
        SELECT      vpi.PlanInfoID
                   ,a.CPS
                   ,@BidYear AS PlanYearID
                   ,0 AS QuarterID
                   ,a.BarcClass
                   ,CASE WHEN a.BarcClass = 'A' THEN a.PriorYearBarcClass ELSE 'N' END AS PriorYearBarcClass
                   ,CAST(a.IsDEPound AS INT) AS IsDEPound
                   ,ISNULL (SUM (a.Bid_MM), 0) AS MemberMonths
                   ,ISNULL (CAST(SUM (CAST(a.Bid_MM * a.AdjustedFactor AS FLOAT)) AS FLOAT), 0) AS UseRelativityxMemberMonths
                   ,@LastUpdateByID
                   ,GETDATE ()
        FROM        #AdjFact_ForRpt a
       INNER JOIN   dbo.vwPlanInfo vpi WITH (NOLOCK)
               ON vpi.CPS = a.CPS
                  AND   vpi.PlanYear = @BidYear
        GROUP BY    vpi.PlanInfoID
                   ,a.CPS
                   ,a.BarcClass
                   ,a.PriorYearBarcClass
                   ,a.IsDEPound;

        --5b. For trend process

        --Delete bid year factors
        DELETE  FROM dbo.Trend_SavedRelativityPopulation WITH(ROWLOCK)
        WHERE   PlanYearID = @BidYear
                AND CPS IN (SELECT  CPS FROM #Plans);

        --Insert calculated bid year factors
        INSERT INTO dbo.Trend_SavedRelativityPopulation WITH(ROWLOCK)
            (CPS
            ,PlanYearID
            ,QuarterID
            ,ReportingCategory
            ,CostRelativity
            ,UseRelativity
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT      a.CPS
                   ,@BidYear AS PlanYearID
                   ,q.QuarterID
                   ,rc.ReportingCategory
                   ,1 AS CostRelativity
                   ,dbo.Trend_fnSafeDivide (
                    CAST(SUM (CAST(a.AdjustedFactor * a.Bid_MM AS FLOAT)) AS FLOAT), SUM (a.Bid_MM), 1) AS UseRelativity
                   ,@LastUpdateByID
                   ,GETDATE ()
        FROM        #AdjFact_ForTrendProcess a
       CROSS JOIN   #Quarter q
       CROSS JOIN   #AllRepcat rc
        GROUP BY    a.CPS
                   ,q.QuarterID
                   ,rc.ReportingCategory;

    END;
GO
