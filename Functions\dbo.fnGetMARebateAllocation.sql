SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetMARebateAllocation
--
-- AUTHOR: Christian Cofie
--
-- CREATED DATE: 2007-Apr-23
--
-- DESCRIPTION: Computes the MA rebate allocation, which is used on the Bid
--              Summary tab in the MAPD model.
--
-- PARAMETERS:
--  Input: 
--		@ForecastID INT, 
--    
--
-- RETURNS:
--    A recordset containing the rebate allocation information.
--
-- TABLES: 
--  Read:
--    SavedForecastSetup
--  Write:
--    NONE
--
-- VIEWS:
--  Read: NONE
--
-- FUNCTIONS:
--  Read:
--    fnAppGetBidSummary
--  Called:
--    NONE
--
-- STORED PROCS: 
--  Executed: 
--    NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                             	    DEVELOPER		
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Apr-23          1       Initial version.                                                Christian Cofie
-- 2008-May-12          2       Rounded RxBasicBuyDown and RxSuppBuyDown to the nearest         Tonya Cockrell
--                                  dime.  Restructured to make calculations easier to read.
-- 2008-May-15          3       Added alternation RPPO
-- 2009-Feb-12          4       Removed alternation RPPO. The rebate buydown order is           Lawrence Choi
--                                  changed to the following: 1. Part D Suppl 2. Part A/B Basic
--									3. Part A/B Suppl 4. Part B Prem 5. Part D Basic regardless
--                                  of the plan type. SNP plan is handled in 
--                                  fnAppGetMARebateAllocMAPDSNP.
-- 2009-Apr-28          5       Round Down is used to calculate RxSuppBuyDown                   Lawrence Choi
--                                  to avoid negative Rebate Unspent 
-- 2009-Apr-30          6       Added branching for new computation method to be used when      Tonya Cockrell
--                                  the @Rebate value is within a certain range.
-- 2009-May-22          7       Prevented RxBasicBuydown from ever being larger than            Tonya Cockrell
--                                  RxBasicPremium.
-- 2010-Apr-27          8       Adjusted for negative values from fnAppGetBidSummary            Jake Gaecke
--                                  and fixed handling of PartBPremiumBuyDown
-- 2010-Sep-07          9       Revised for coding standards and 2012 database                  Jake Gaecke
--                                  Removed App from name.  Added @RebateOrderID to
--                                  allow for multiple rebate allocation orders
--                                  0 = Default, 1 = Texas
-- 2010-Sep-23			10		Revised coding for Default Allocation and Texas allocation		Craig Wright
--									to account for Part B Prem as well as cleaned up the 
--									code for easier reading.  Updated to the following 
--									Allocation Orders:
--									0 = Default => 1. Part D Suppl 2. Part D Basic 
--										3. Reduce A/B Cost Share 4. Other A/B Mand Suppl 
--										5. Part B Prem	
--									1 = Texas => 1. Part D Basic 2. Part D Suppl 
--										3. Reduce A/B Cost Share 4. Other A/B Mand Suppl 
--										5. Part B Prem
-- 2010-Oct-06          11      Removed @RebateOrderID from passed in parameter list, it is     Jake Gaecke
--                                  now pulled from SavedPlanHeader automatically
-- 2010-Oct-13          12      Added IsNULL to @RxBasicBuyDown to fix $0 rebate calcs          Michael Siekerka
-- 2011-Feb-07          13      Changed to allow negative RxBasicPremium; corrected code		Craig Wright
--									to appropriately allocate Drug Premiums and Pt B Buydwn. 
-- 2011-Feb-07          14      Changed definition of @RxBasicPremium to prevent negative       Casey Sanders
--                              rebate allocation for negative Rx basic premiums.
-- 2011-Feb-08			15		Added Case statement to allocate negative Rx basic premiums		Craig Wright
--									to accomodate the desires of Florida.
-- 2011-Feb-09			16		Revised to not allow negative RxSuppBuydown						Craig Wright
-- 2011-Apr-19			17		Updated to the following Allocation Orders:						Craig Wright
--									0 = Default => 1. Part D Suppl 2. Reduce A/B Cost Share
--										3. Other A/B Mand Suppl  4. Part B Prem
--										5. Part D Basic
--									1 = Texas (NO CHANGE) => 1. Part D Basic 2. Part D Suppl 
--										3. Reduce A/B Cost Share 4. Other A/B Mand Suppl 
--										5. Part B Prem
-- 2011-Apr-20			18		Added a Case Statement to eliminate rebate less than .10 left	Craig Wright
--									for RxBasic to buydown, and changed RebateOrderID to pull
--									AltRebateOrder from SavedPlanHeader
--2012-Aug-15			11		Added code to handle Group rebate reallocation					Tim Gao
--2020-Aug-27           12      Added code for the following:                                   Brent Osantowski
--                                        Remove SavedPlanHeader references which include
--                                             ForecastID and EGWP
--                                        Assign PartBPremiumBuyDown as the lowest priority
--                                             for all rebate order options
--                                        Create a third rebate order option
--2020-Nov-24           21      Optimization													Rodney Smith
-- 2022-May-20			22		MAAUI migration; replacing PlanIndex with ForecastID in the
--								output and reference to fnAppGetBidSummary						Aleksandar Dimitrijevic
-- 2023-Aug-03			23		Added Internal Parameter, Nolock								Sheetal Patil 
-- 2024-May-18			24		Updating Rebate Allocation for Default order to allowed			Alex Beruscha
--								unallocated rebate (including Alternate 2)							
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetMARebateAllocation]
(
	@ForecastID INT
)
RETURNS @Results TABLE
    (
	ForecastID INT,
	RxBasicPremium DECIMAL(9,2),
	RxSuppPremium DECIMAL(9,2),
	RedABCostShare DECIMAL(9,2),
	SuppPremBuydown DECIMAL(9,2),
    RxBasicBuyDown DECIMAL(8,1),
    RxSuppBuyDown DECIMAL(8,1)
    )

AS
BEGIN

    DECLARE
        @RxBasicPremium DECIMAL(9,2),
        @RxSuppPremium DECIMAL(9,2),
        @Rebate DECIMAL(8,2),
        @CS DECIMAL(10,2),
        @SuppPrem DECIMAL(10,2),
        @RedABCostShare DECIMAL(9,2),
        @SuppPremBuydown DECIMAL(9,2),
        @RxBasicBuyDown DECIMAL(8,1),
        @RxSuppBuyDown DECIMAL(8,1),
		@PartBPremiumBuyDown DECIMAL(9,1),
		@RebateOrderID INT,
		@PartBMaxRebateAllocation DECIMAL(12,6),
		@XForecastID INT  = @ForecastID 

			DECLARE @TblAppGetBidSummary TABLE(
		PartBPremiumBuyDown decimal(9,1),
		RxBasicPremium decimal(9,2),
		RxSuppPremium decimal(9,2),
		Rebate decimal(8,2),
		CS decimal(10,2),
		Sup_Prem decimal(10,2),
		PartBMaxRebateAllocation decimal(12,6))

	--Create temp table to call function once
	INSERT INTO @TblAppGetBidSummary (
		PartBPremiumBuyDown,
		RxBasicPremium,
		RxSuppPremium,
		Rebate,
		CS,
		Sup_Prem,
		PartBMaxRebateAllocation)
	SELECT PartBPremiumBuyDown,
		RxBasicPremium,
		RxSuppPremium,
		Rebate,
		CS,
		Sup_Prem,
		PartBMaxRebateAllocation
	FROM dbo.fnAppGetBidSummary(@XForecastID)

--Get RebateOrderID, 0 = Default 
    SELECT
        @RebateOrderID =  ISNULL(AltRebateOrder,0) --Changed to AltRebateOrder as RebateOrderID isn't updated on SavedPlanHeader
    FROM dbo.SavedForecastSetup WITH (NOLOCK)
    WHERE ForecastID = @XForecastID

--Get Max Part B Rebate Allocation
	SELECT @PartBMaxRebateAllocation = PartBMaxRebateAllocation FROM @TblAppGetBidSummary

--Adj values do not allow negative numbers; Except for RxBasicPremium, which is allowed to be negative.
    SELECT
        @RxBasicPremium = RxBasicPremium,
        @RxSuppPremium = CASE
                            WHEN RxSuppPremium < 0 THEN 0
                            ELSE RxSuppPremium
						 END,
        @Rebate = Rebate,
        @CS =   CASE 
                    WHEN CS < 0 THEN 0
                    ELSE CS
                END,
        @SuppPrem = CASE
                        WHEN Sup_Prem < 0 THEN 0
                        ELSE Sup_Prem
                    END,
        @PartBPremiumBuyDown = PartBPremiumBuyDown  -- This is the amount that the User Enters on the Plan Level Bid Summary tab.
    FROM @TblAppGetBidSummary


	
--Default Rebate Order------------------------------------------------ 
--@RebateOrderID = 0--------------------------------------------------
--1. Part D Suppl 2. Reduce A/B Cost Share 3. Other A/B Mand Suppl 4. Part D Basic 5. Part B Prem 
    IF @RebateOrderID = 0
    BEGIN
        --1. RxSuppBuyDown = Min(Rebate, RxSuppPrem); rounded down to the nearest dime.
        --(sub CASE statement to handle neg RxBasicPremium)
        SET @RxSuppBuyDown = 
            CASE 
				WHEN @Rebate < @RxSuppPremium --no negatives allowed
					THEN ROUND(@Rebate, 1, 1) -- Round Down 
				ELSE @RxSuppPremium --RxSuppPremium is already rounded to nearest dime
            END

		--When RxBasic Premium is negative, we want to have the RxSuppBuydown offset the RxBasicPremium
		--So @RxSuppBuyDown + @RxBasicPremium = 0        
		
		
		-- We ran into an issue with negative RxBasicPremiums where the rebate was less than the negative negative premium.
		-- It gave us a negative buydown. (eg we had a RxBasicPrem = -$15, but we only had $10 in rebate.) the below code
		-- was added to fix the issue.  
		SET @RxSuppBuyDown =  
			CASE 
				WHEN @RxBasicPremium < 0 THEN
					CASE 
						WHEN @Rebate < -@RxBasicPremium THEN @Rebate
						ELSE @RxSuppBuyDown + @RxBasicPremium
					END
				ELSE @RxSuppBuyDown
			END
      

		--2. RedABCostShare = Rebate - RxSuppBuyDown; not more than CS
        SET @RedABCostShare =
            ROUND(
                CASE
                    WHEN @Rebate - @RxSuppBuyDown < 0
                        THEN 0
                    WHEN @Rebate - @RxSuppBuyDown < @CS
                        THEN @Rebate - @RxSuppBuyDown 
                    ELSE
                        @CS
                END,2)


        --3. SuppPremBuydown = Rebate - RxSuppBuyDown - RedABCostShare; not more than SuppPrem.
        SET @SuppPremBuyDown =
                CASE
                    WHEN @Rebate - @RxSuppBuyDown - @RedABCostShare < 0
                        THEN 0
                    WHEN @Rebate - @RxSuppBuyDown - @RedABCostShare < @SuppPrem
                        THEN  @Rebate - @RxSuppBuyDown - @RedABCostShare 
                    ELSE
                       @SuppPrem
                END
			
--	SELECT @Rebate , @RxSuppBuyDown , @RedABCostShare , @SuppPremBuyDown , @PartBPremiumBuyDown	
--	SELECT @SuppPremBuyDown,	(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown) AS exact,	ROUND((@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown),1,1) AS rounded
		-- RxBasicBuydown is only allowed to buydown to the nearest dime.  Thus if there is anything less than a dime,
		-- we will remove that from the SuppPremBuydown and apply it to the RxBasicBuydown so it can go through. 
		-- If there is an unallocated penny it will pass that full penny through to the BPT for editing
        SET @SuppPremBuyDown =
                CASE
					WHEN ROUND(@RxBasicPremium, 1, 1) <= 0
						THEN  @SuppPremBuyDown
                    WHEN ROUND(@RxBasicPremium, 1, 1) - (@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown) <= 0
                        THEN    @SuppPremBuyDown 
                    WHEN (@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown)
							- ROUND((@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown),1) >= 0
						THEN   @SuppPremBuyDown
                    WHEN (@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown)
							- ROUND((@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown),1) < 0
						THEN  @SuppPremBuyDown
								+ 
								(
									(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown)
									- ROUND((@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown),1)
								)
                    ELSE
                      @SuppPremBuyDown
                END

		--4. RxBasicBuyDown = Rebate - RxSuppBuyDown - RedABCostShare - SuppPremBuydown ; not more than RxBasicPremium
        --Rounded to nearest dime.
        SET @RxBasicBuyDown =
            ISNULL(
                CASE
				    WHEN ROUND(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown, 1, 1) < 0 
					    THEN 0
				    WHEN ROUND(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown, 1, 1) < ROUND(ISNULL(@RxBasicPremium,0), 1, 1)
				        THEN ROUND(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown, 1, 1)
				    ELSE
                        ROUND(@RxBasicPremium, 1, 1)
                END
            ,0)
		--When RxBasic Premium is negative, we want to have the Buydown = 0
		SET @RxBasicBuyDown =
			ISNULL(
				CASE
					WHEN @RxBasicPremium < 0 THEN 0
					ELSE @RxBasicBuyDown
				END
			,0) 

    END


--Alternate Rebate Order----------------------------------------------
--@RebateOrderID = 1--------------------------------------------------    
--1. Part D Basic 2. Part D Suppl 3. Reduce A/B Cost Share 4. Other A/B Mand Suppl 5. Part B Prem
--------
    IF @RebateOrderID = 1 --Texas Alternate Rebate Order
    BEGIN
        --RxBasicBuyDown = Min(Rebate, RxBasicPremium); rounded down to the
        --nearest dime. (sub CASE statement to handle neg RxBasicPremium)
        SET @RxBasicBuyDown =
            ISNULL(
                CASE
                    WHEN @Rebate < @RxBasicPremium
                        THEN ROUND(@Rebate, 1, 1) -- Round Down 
                    ELSE
                        ROUND(@RxBasicPremium, 1, 1)
                END
            ,0)
		--When RxBasic Premium is negative, we want to have the Buydown = 0
		SET @RxBasicBuyDown =
			ISNULL(
				CASE
					WHEN @RxBasicPremium < 0 THEN 0
					ELSE @RxBasicBuyDown
				END
			,0)                
                
        --RxSuppBuyDown = Rebate - RxBasicBuyDown; rounded down to the nearest dime. 
        --(sub CASE statement to handle neg RxBasicPremium)
        SET @RxSuppBuyDown =
                CASE

                    WHEN ROUND(@Rebate - @RxBasicBuyDown, 1, 1) < 0
                        THEN 0
                    WHEN ROUND(@Rebate - @RxBasicBuyDown, 1, 1) < ROUND(@RxSuppPremium, 1, 1)
                        THEN ROUND(@Rebate - @RxBasicBuyDown, 1, 1)
                    ELSE
                        ROUND(@RxSuppPremium, 1, 1)
                END
		--When RxBasic Premium is negative, we want to have the RxSuppBuydown offset the RxBasicPremium
		--So @RxSuppBuyDown + @RxBasicPremium = 0        
		
		
		-- We ran into an issue with negative RxBasicPremiums where the rebate was less than the negative negative premium.
		-- It gave us a negative buydown. (eg we had a RxBasicPrem = -$15, but we only had $10 in rebate.) the below code
		-- was added to fix the issue. 
		SET @RxSuppBuyDown =  
			CASE 
				WHEN @RxBasicPremium < 0 THEN
					CASE 
						WHEN @Rebate < -@RxBasicPremium THEN @Rebate
						ELSE @RxSuppBuyDown + @RxBasicPremium
					END
				ELSE @RxSuppBuyDown
			END
			       
        --RedABCostShare = Rebate - RxBasicBuyDown - RxSuppBuyDown; not
        --more than CS
        SET @RedABCostShare =
            ROUND(
                CASE
                    WHEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown < 0
                        THEN 0
                    WHEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown < @CS
                        THEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown
                    ELSE
                        @CS
                END,2)


        --SuppPremBuydown = Rebate - RxBasicBuyDown - RxSuppBuyDown - RedABCostShare - PartBPremiumBuydown;
        --not more than SuppPrem.
        SET @SuppPremBuyDown =
                CASE
                    WHEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown - @RedABCostShare < 0
                        THEN 0
                    WHEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown - @RedABCostShare < @SuppPrem
                        THEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown - @RedABCostShare 
                    ELSE
                        @SuppPrem
                END

		SET @PartBPremiumBuyDown  =
                CASE
                    WHEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown < 0
                        THEN 0
                    WHEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown < @PartBMaxRebateAllocation
                        THEN @Rebate - @RxBasicBuyDown - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown
                    ELSE
                       @PartBMaxRebateAllocation
                END

          
        SELECT
            @RxBasicPremium = RxBasicPremium,
            @RxSuppPremium = RxSuppPremium,
            @Rebate = Rebate,
            @CS = CS,
            @SuppPrem = Sup_Prem
        FROM @TblAppGetBidSummary

        IF @Rebate < @RxBasicBuyDown + @RxSuppBuyDown + @RedABCostShare + @SuppPremBuyDown + @PartBPremiumBuyDown 
            BEGIN
                SET @SuppPremBuyDown = 
                    CASE
                        WHEN @SuppPremBuyDown - 
                            (@RxBasicBuyDown + @RxSuppBuyDown + @RedABCostShare + @SuppPremBuyDown + @PartBPremiumBuyDown) + @Rebate < 0
                            THEN 0
                        ELSE @SuppPremBuyDown - 
                            (@RxBasicBuyDown + @RxSuppBuyDown + @RedABCostShare + @SuppPremBuyDown + @PartBPremiumBuyDown) + @Rebate
                    END
            END
    END
  
  
  --Alternate Rebate Order----------------------------------------------
--@RebateOrderID = 2--------------------------------------------------    
--1. Reduce A/B Cost Share 2. Other A/B Mand Suppl 3. Part D Suppl  4. Part D Basic 5. Part B Prem 
--------
     IF @RebateOrderID = 2 
    BEGIN
	
	        SET @RxBasicBuyDown =
            ISNULL(
                CASE
                    WHEN @Rebate < @RxBasicPremium
                        THEN ROUND(@Rebate, 1, 1) -- Round Down 
                    ELSE
                        ROUND(@RxBasicPremium, 1, 1)
                END
            ,0)

		--When RxBasic Premium is negative, we want to have the Buydown = 0
		SET @RxBasicBuyDown =
			ISNULL(
				CASE
					WHEN @RxBasicPremium < 0 THEN 0
					ELSE @RxBasicBuyDown
				END
			,0)                

        --RxSuppBuyDown = Rebate - RxBasicBuyDown; rounded down to the nearest dime. 
        --(sub CASE statement to handle neg RxBasicPremium)


        SET @RxSuppBuyDown =
                CASE
                    WHEN ROUND(@Rebate, 1, 1) < 0
                        THEN 0
                    WHEN @Rebate  < @RxSuppPremium
                        THEN ROUND(@Rebate, 1, 1)
                    ELSE
                        ROUND(@RxSuppPremium, 1, 1)
                END


  --1     
        --RedABCostShare = Rebate;
		--not more than CS
        SET @RedABCostShare =
            ROUND(
                CASE
                    WHEN @Rebate < @CS
                        THEN @Rebate
                    ELSE
                        @CS
                END,2)	   

   --2	   
        --SuppPremBuydown = Rebate - RedABCostShare;
        --not more than SuppPrem.
        SET @SuppPremBuyDown =
                CASE
                    WHEN @Rebate - @RedABCostShare  < 0
                        THEN 0

					WHEN @Rebate - @RedABCostShare < @SuppPrem THEN @Rebate - @RedABCostShare

                    ELSE
                        @SuppPrem
                END	 

-- we will remove that from the SuppPremBuydown and apply it to the RxBasicBuydown so it can go through. 
        SET @SuppPremBuyDown =
                CASE
					WHEN @RxBasicBuyDown + @RxSuppBuyDown <=0
						THEN @SuppPremBuyDown
					WHEN @RxBasicBuyDown + @RxSuppBuyDown - (@Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown) <= 0
						THEN @SuppPremBuyDown
                    WHEN @Rebate - @RedABCostShare - @SuppPremBuyDown - ROUND(@Rebate - @RedABCostShare - @SuppPremBuyDown,1) = 0 --changed to just rounding
                        THEN  @SuppPremBuyDown 
                    WHEN @Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown - ROUND(@Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown,1) < 0 --Changed to just rounding and switched to negative check
						THEN  @SuppPremBuyDown + ((( @Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown) - ROUND(@Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown,1))) --removed a "0.1-" and the rounding formula
                    WHEN @Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown - ROUND(@Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown,1) > 0 --removed the round down formula and switched to positive check
						THEN  @SuppPremBuyDown --- (( @Rebate - @RedABCostShare - @SuppPremBuyDown) - ROUND(@Rebate - @RedABCostShare - @SuppPremBuyDown,1,1)) changed to just keep the full supp prem buydown to pass unallocated pennies
                    ELSE
                       @SuppPremBuyDown
                END

	 --3  
        --RxSuppBuyDown = Rebate - RxBasicBuyDown; rounded down to the nearest dime. 
        --(sub CASE statement to handle neg RxBasicPremium)
		-- RxBasicBuydown is only allowed to buydown to the nearest dime.  Thus if there is anything less than a dime,
		-- we will remove that from the SuppPremBuydown and apply it to the RxBasicBuydown so it can go through. 

        SET @RxSuppBuyDown =
                CASE
					WHEN @Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown<=0
                        THEN 0 
                    WHEN (@Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown) <= ROUND(@RxSuppPremium,1,1) 
						THEN  ROUND(@Rebate - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown,1)
                    ELSE  ROUND(@RxSuppPremium,1,1) 
                END

	
        SET @RxBasicBuyDown =
            ISNULL(
                CASE
				    WHEN ROUND(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown - @PartBPremiumBuyDown, 1, 1) < 0 
					    THEN 0
				    WHEN ROUND(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown- @PartBPremiumBuyDown, 1, 1) < ROUND(ISNULL(@RxBasicPremium,0), 1, 1)
				        THEN ROUND(@Rebate - @RxSuppBuyDown - @RedABCostShare - @SuppPremBuyDown- @PartBPremiumBuyDown, 1) --removed truncation
				    ELSE
                        ROUND(@RxBasicPremium, 1, 1)
                END
            ,0)
		--When RxBasic Premium is negative, we want to have the Buydown = 0
		SET @RxBasicBuyDown =
			ISNULL(
				CASE
					WHEN @RxBasicPremium < 0 THEN 0
					ELSE @RxBasicBuyDown
				END
			,0)       


			END
			       

          
        SELECT
            @RxBasicPremium = RxBasicPremium,
            @RxSuppPremium = RxSuppPremium,
            @Rebate = Rebate,
            @CS = CS,
            @SuppPrem = Sup_Prem
        FROM @TblAppGetBidSummary

        IF @Rebate < @RxBasicBuyDown + @RxSuppBuyDown + @RedABCostShare + @SuppPremBuyDown + @PartBPremiumBuyDown 
            BEGIN
                SET @SuppPremBuyDown = 
                    CASE
                        WHEN @SuppPremBuyDown - 
                            (@RxBasicBuyDown + @RxSuppBuyDown + @RedABCostShare + @SuppPremBuyDown + @PartBPremiumBuyDown) + @Rebate < 0
                            THEN 0
                        ELSE @SuppPremBuyDown - 
                            (@RxBasicBuyDown + @RxSuppBuyDown + @RedABCostShare + @SuppPremBuyDown + @PartBPremiumBuyDown) + @Rebate
                    END
            END

    
    INSERT @Results
    VALUES
        (
	    @XForecastID,
	    @RxBasicPremium,
	    @RxSuppPremium,
	    @RedABCostShare,
	    @SuppPremBuydown,
        @RxBasicBuyDown,
        @RxSuppBuyDown
        )
    RETURN
END
GO
