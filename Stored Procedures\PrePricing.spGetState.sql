SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: [PrePricing].[spGetState]   
--    
-- AUTHOR: <PERSON><PERSON>y 
--    
-- CREATED DATE: 2024-Dec-02    
-- Type: 
-- DESCRIPTION: Procedure responsible for retrieving states  
--    
-- PARAMETERS:    
-- Input: 
-- 
   
-- TABLES:   
--  
 
-- Read:    
-- dbo.LkpStateTerritory

-- Write:    
--    
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Dec-02		1		Initial Version                 Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [PrePricing].[spGetState]
AS    
BEGIN    
	SELECT a.StateTerritoryID,a.StateTerritoryName FROM dbo.LkpStateTerritory a WITH(NOLOCK) ORDER BY a.LastUpdateDateTime DESC;	 
END
GO
