SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spUploadRPRXSelections
--
-- CREATOR: <PERSON>
--
-- CREATED DATE: 2022-Aug-28
-- HEADER UPDATED: 2022-Aug-28
--
-- DESCRIPTION: Stored Procedure responsible for uploading related party and rx pharamcy selections.
--
-- PARAMETERS:
--	Input:
--      @ForecastID
--      @AddedBenefitName
--      @UserID
--  Output:
--		@ValidationMessage
--
-- TABLES:
--	Read:
--      SavedPlanAddedBenefits
--      LkpIntAddedBenefitType
--	Write:
--      Saved<PERSON><PERSON><PERSON>dded<PERSON>enefits
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2022-Aug-28		1			Clone from spMSBUpload. Limit to only RPRXs							Adam Gilbert
-- 2023-Sep-23		2			Added isOverride value in insert									Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------

CREATE   PROCEDURE [dbo].[spUploadRPRXSelections] 
(	
	@ForecastID INT,
	@AddedBenefitName VARCHAR(50),
	@UserID Char(7),
	@ValidationMessage varchar(500) OUTPUT
)
AS

	SET NOCOUNT ON	
--------------------------------------------------------------------------------------------------------------------
--Declarations------------------------------------------------------------------------------------------------------
    DECLARE @PlanYearID SMALLINT
    SELECT @PlanYearID = dbo.fnGetBidYear()
    SET @ValidationMessage=NULL
    DECLARE @LastUpdate DATETIME
    SET @LastUpdate = GETDATE()    

	DECLARE @AddedBenefitTypeID int

	DECLARE @BidServiceCatID int

BEGIN TRY
	/*Lookup the AddedBenefitTypeID*/
	SELECT  @AddedBenefitTypeID = AddedBenefitTypeID,
			@BidServiceCatID = BidServiceCatID
	FROM dbo.LkpIntAddedBenefitType 
	WHERE AddedBenefitName = @AddedBenefitName 
	AND IsEnabled=1 

	IF NOT EXISTS(
	SELECT 1 
	FROM SavedForecastSetup
	WHERE ForecastID = @ForecastID)
	BEGIN
		SET @ValidationMessage = '(' + Convert(varchar,@ForecastID) 
		+ ', ' +@AddedBenefitName + ') ---'+
		' Invalid Forecastid. Plan not found.';
	END

	/*Check if AddedBenefitName exists*/	
	ELSE IF @AddedBenefitTypeID IS NULL 
	BEGIN
		SET @ValidationMessage = '(' + Convert(varchar,@ForecastID) 
		+ ', ' +@AddedBenefitName + ') ---'+
		' Invalid Related Party or RX Pharmacy. Imported name not found.';
	END

	/*Check if record is MSB*/	
	ELSE IF @BidServiceCatID <> 35
	BEGIN
		SET @ValidationMessage = 
		'(' + Convert(varchar,@ForecastID) + ', ' +@AddedBenefitName + ') ---'+
	' Imported name determined to be an MSB.'
	+' Use MSB Selections Import to add selection to plan.';
	END;

    /*Insert Record and flag reprice*/   
	ELSE IF @BidServiceCatID = 35
	 BEGIN
	 BEGIN TRANSACTION 
	 INSERT INTO dbo.SavedPlanAddedBenefits(
	 			PlanYearID,
				ForecastID,
				AddedBenefitTypeID,
				AddedBenefitName,
				INAddedBenefitDescription,
				INAddedBenefitAllowed,
				INAddedBenefitUtilization,
				INAddedBenefitCostShare,
				OONAddedBenefitDescription,
				OONAddedBenefitAllowed,
				OONAddedBenefitUtilization,
				OONAddedBenefitCostShare,
				BidServiceCatID,
				IsValueAdded,
				IsNetwork,
				IsHidden,
				LastUpdateByID,
				LastUpdateDateTime,
				IsOverride
	 )
			SELECT
				@PlanYearID,
				@ForecastID,
				AddedBenefitTypeID,
				@AddedBenefitName,
				INAddedBenefitDescription,
				INAddedBenefitAllowed,
				INAddedBenefitUtilization,
				INAddedBenefitCostShare,
				OONAddedBenefitDescription,
				OONAddedBenefitAllowed,
				OONAddedBenefitUtilization,
				OONAddedBenefitCostShare,
				BidServiceCatID,
				IsValueAdded,
				IsNetwork,
				0,  --IsEnabled,
				@UserID,
				@LastUpdate,
				0
			FROM dbo.LkpIntAddedBenefitType (NOLOCK)
			WHERE 1=1
			AND IsEnabled=1
			AND AddedBenefitTypeID=@AddedBenefitTypeID

			UPDATE dbo.SavedForecastSetup
			SET IsToReprice = 1,
			LastUpdateByID =@UserID,
			LastUpdateDateTime = @LastUpdate
			WHERE ForecastID = @ForecastID 	
			COMMIT 
	 END

END TRY
BEGIN CATCH
SET @ValidationMessage = 'SQL Error: ' + ERROR_MESSAGE()
	IF @@TRANCOUNT > 0
            BEGIN
                ROLLBACK TRANSACTION;

        END;
END CATCH
RETURN
GO
