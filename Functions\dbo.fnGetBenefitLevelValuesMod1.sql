SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================
-- FUNCTION NAME:    fnGetBenefitLevelValuesMod1

-- AUTHOR:            <PERSON><PERSON><PERSON>

-- CREATED DATE:    02/02/2009

-- DESCRIPTION:        Designed to extract fields for the  Benefit Level Template

-- PARAMETERS:        INPUT: @BenefitYearID, @WhereIN Varchar(MAX) = NULL

-- VIEWS:            READ:    dbo.vwAuditBenefitLevelValues

-- RETURNS:            TABLE
 
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE                 VERSION         CHANGES MADE											DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2009-Feb-029         1               Initial Version											Sulemana Dauda
-- 2009-Feb-20          2               Revised to meet Coding Standards						Keith Galloway
--											and	Added MAPDModel Permissions
-- 2009-Mar-17          3               Data types												Sandy Ellis                               
-- 2010-Sep-16          4               Revised for 2012 database, removed PlanYearID			Jake Gaecke
-- 2010-Sep-20          5               Removed PercentCovered Allowed and CostShare			Jake Gaecke
--                                          added a ForecastID filter.
-- 2011-Oct-07			6				Changed BenefitOrdinalID to [Level] and made it return	Craig Wright
--											L1, L2, or L3 (instead of 1, 2, 3).
-- 2011-Oct11			7				Changed BenefitOrdinalID to [Level] in @Results			Craig Wright
--											and had it return as a VARCHAR(2)
-- 2020-Jan-16          8               [20.02] Added DedApplies fields                         Keith Galloway
-- 2022-April-13		9				MAAUI migration; replaced PlanIndex with ForecastID;
--										removed BenefitOrdinalPercent; replaced Level with 
--										BenefitOrdinalID; added BundleID;						Aleksandar Dimitrijevic
-- 2022-Oct-26          10              Added nolock and created internal parameters			Vikrant Bagal

-- 2023-Aug-03          11              Added STRING_SPLIT function instead of dbo.fnStringSplit   Sowmya K
-- 2025-JAN-21			12				Added CONCAT_WS to create CPS for use in Benefits and	Alex Brandt
--											MOOP/Deductible Export
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE FUNCTION [dbo].[fnGetBenefitLevelValuesMod1]
    (
    @BenefitYearID SMALLINT,
    @WhereIN VARCHAR(MAX) = NULL
    )
	
RETURNS @Results TABLE
(
    BenefitYearID		[SMALLINT] NOT NULL,
    ForecastID			[INT] NOT NULL,
	CPS					CHAR(13) NOT NULL,
    BenefitCategoryID	[SMALLINT] NOT NULL,
    BenefitCategoryName VARCHAR(100),
    BenefitOrdinalID	[TINYINT] NOT NULL,
	INBundleID			[TINYINT] NOT NULL, 
    INBenefitDayRange	[TINYINT] NULL,
    INDedApplies		[BIT] NULL,
    INCoinsurance		[DECIMAL](9, 4) NULL,
    INPerAdmitCopay		[DECIMAL](9, 4) NULL,
    INCopay				[DECIMAL](9, 4) NULL,
	OONBundleID			[TINYINT] NOT NULL, 
	OONBenefitDayRange	[TINYINT] NULL,
    OONDedApplies		[BIT] NULL,
    OONCoinsurance		[DECIMAL](9, 4) NULL,
    OONPerAdmitCopay	[DECIMAL](9, 4) NULL,
    OONCopay			[DECIMAL](9, 4) NULL
 ) AS

BEGIN
    DECLARE @XBenefitYearID SMALLINT = @BenefitYearID
    DECLARE @XWhereIN Varchar(MAX) = @WhereIN
--**************************************************************************************************
-- First section: Running for Nationwide (@WhereIn IS NULL)
--**************************************************************************************************
    IF @XWhereIN IS NULL
    BEGIN
        INSERT @Results
        SELECT  BenefitYearID,
                ForecastID,
				CONCAT_WS('-', ContractNumber, PlanID, SegmentID) AS CPS,
                BenefitCategoryID,
                BenefitCategoryName,
                BenefitOrdinalID,
				INBundleID,
                INBenefitDayRange =
					CASE INDayRangeEnd
						WHEN 0 THEN NULL
						ELSE INDayRangeEnd
					END,
                INDedApplies,
                INCoinsurance =
                    CASE INBenefitTypeID
                        WHEN 1 THEN INBenefitValue
                        ELSE NULL
                    END,
                INPerAdmitCopay = 
                    CASE INBenefitTypeID
                        WHEN 3 THEN INBenefitValue
                        ELSE NULL
                    END,
                INCopay =
                    CASE INBenefitTypeID
                        WHEN 2 THEN INBenefitValue
                        WHEN 5 THEN INBenefitValue
                        ELSE NULL 
                    END,
				OONBundleID,
                OONBenefitDayRange =
					CASE OONDayRangeEnd
						WHEN 0 THEN NULL
						ELSE OONDayRangeEnd
					END,
                OONDedApplies,
                OONCoinsurance =
                    CASE OONBenefitTypeID
                        WHEN 1 THEN OONBenefitValue
                        ELSE NULL
                    END,
                OONPerAdmitCopay = 
                    CASE OONBenefitTypeID
                        WHEN 3 THEN OONBenefitValue
                        ELSE NULL
                    END,
                OONCopay =
                    CASE OONBenefitTypeID
                        WHEN 2 THEN OONBenefitValue
                        WHEN 5 THEN OONBenefitValue
                        ELSE NULL 
                    END
        FROM dbo.vwAuditBenefitLevelValues with(nolock)
        WHERE BenefitYearID = @XBenefitYearID
    END
--**************************************************************************************************
    ELSE -- Second section: Running for specified ForecastIDs (@WhereIn IS NOT NULL)
--**************************************************************************************************
    BEGIN
    INSERT @Results
        SELECT  BenefitYearID,
                ForecastID,
				CONCAT_WS('-', ContractNumber, PlanID, SegmentID) AS CPS,
                BenefitCategoryID,
                BenefitCategoryName,
                BenefitOrdinalID,
				INBundleID,
                INBenefitDayRange =
					CASE INDayRangeEnd
						WHEN 0 THEN NULL
						ELSE INDayRangeEnd
					END,
                INDedApplies,
                INCoinsurance =
                    CASE INBenefitTypeID
                        WHEN 1 THEN INBenefitValue
                        ELSE NULL
                    END,
                INPerAdmitCopay = 
                    CASE INBenefitTypeID
                        WHEN 3 THEN INBenefitValue
                        ELSE NULL
                    END,
                INCopay =
                    CASE INBenefitTypeID
                        WHEN 2 THEN INBenefitValue
                        WHEN 5 THEN INBenefitValue
                        ELSE NULL 
                    END,
				OONBundleID,
                OONBenefitDayRange =
					CASE OONDayRangeEnd
						WHEN 0 THEN NULL
						ELSE OONDayRangeEnd
					END,
                OONDedApplies,
                OONCoinsurance =
                    CASE OONBenefitTypeID
                        WHEN 1 THEN OONBenefitValue
                        ELSE NULL
                    END,
                OONPerAdmitCopay = 
                    CASE OONBenefitTypeID
                        WHEN 3 THEN OONBenefitValue
                        ELSE NULL
                    END,
                OONCopay =
                    CASE OONBenefitTypeID
                        WHEN 2 THEN OONBenefitValue
                        WHEN 5 THEN OONBenefitValue
                        ELSE NULL 
                    END
        FROM dbo.vwAuditBenefitLevelValues WITH(NOLOCK)
        WHERE BenefitYearID = @XBenefitYearID          
        AND ForecastID IN (SELECT Value FROM STRING_SPLIT(@XWhereIN, ','))
    END
    RETURN
END
GO
