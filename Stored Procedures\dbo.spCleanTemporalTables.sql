SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
/****** Object:  StoredProcedure [dbo].[spCleanTemporalTables]    Script Date: 9/9/2024 12:25:34 PM ******/
SET <PERSON>SI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------        
-- FUNCTION NAME: spCleanTemporalTables       
--        
-- AUTHOR: <PERSON><PERSON>y       
--        
-- CREATED DATE: 2024-09-17      
--        
-- DESCRIPTION: Procedure responsible for clean up temporal tables      
--        
-- PARAMETERS:              
--			@sourcetable
--		    @historytable
-- TABLES:         
-- Read:        

-- Write:        
--        
-- VIEWS:        
--        
-- FUNCTIONS:       
--        
-- STORED PROCS:        
--        
-- $HISTORY         
-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION      CHANGES MADE														DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2024-09-17		1           Initial Version														Surya Murthy  
-- 2025-01-08		2           Parameters updated													Surya Murthy
-------------------------------------------------------------------------------------------------------------------------  
--EX: EXEC [dbo].[spCleanTemporalTables] 'maintablename','historytablename'
CREATE PROCEDURE [dbo].[spCleanTemporalTables]
@sourcetable VARCHAR(MAX),
@historytable VARCHAR(MAX)
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @@sourcetable VARCHAR(MAX) = @sourcetable;
	DECLARE @@historytable VARCHAR(MAX) = @historytable;
	DECLARE @alterSql VARCHAR(MAX)
	DECLARE @truncateSql VARCHAR(MAX)
	SET @alterSql = ''
	SET @alterSql ='ALTER TABLE dbo.'+@@sourcetable+' SET ( SYSTEM_VERSIONING = OFF )';
	EXEC(@alterSql) 
	SET @truncateSql='';
	SET @truncateSql ='TRUNCATE TABLE dbo.'+ @@historytable;
	EXEC(@truncateSql) 
	SET @alterSql = ''
	SET @alterSql = 'ALTER TABLE [dbo].'+@@sourcetable+' SET ( SYSTEM_VERSIONING = ON (HISTORY_TABLE = dbo.'+@@historytable+'))';
	EXEC(@alterSql) 
END
GO

 