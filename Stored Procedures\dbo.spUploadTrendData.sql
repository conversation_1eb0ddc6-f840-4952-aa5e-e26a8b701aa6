SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spUploadTrendData
--
-- CREATOR:			<PERSON>
--
-- CREATED DATE:	2012-SEP-26
--
-- DESCRIPTION:		Handles uploading trend data via the legacy Bid Model > Batch > Import > MACTAPT Upload
--		
-- PARAMETERS:
--  Input  :		@ContractNumber
--					@PlanID
--					@SegmentId
--					@Component
--					@BenefitCategoryNumber
--					@B2CNormalizedUse
--					@B2CNormalizedCost
--					@B2CMorbidityUse
--					@B2CMorbidityCost
--					@B2CDemographicUse
--					@B2CDemographicCost
--					@B2CBustersUse
--					@B2CBustersCost
--					@B2CProductUse
--					@B2CProductCost
--					@B2CGeographicUse
--					@B2CGeographicCost
--					@B2CCMSReimbursementUse
--					@B2CCMSReimbursementCost
--					@B2CContractualUse
--					@B2CContractualCost
--					@B2CBendersUse
--					@B2CBendersCost
--					@B2CWorkdayUse
--					@B2CWorkdayCost
--					@B2CFluUse
--					@B2CFluCost
--					@B2CInducedUtilizationUse
--					@B2CInducedUtilizationCost
--					@B2CActAdjUse
--					@B2CActAdjCost
--					@B2CNonActAdjUse
--					@B2CNonActAdjCost
--					@B2CPoolingUse
--					@B2CPoolingCost
--					@B2CHTPUse
--					@B2CHTPCost
--					@B2CCompoundingAdjUse
--					@B2CCompoundingAdjCost
--					@C2PNormalizedUse
--					@C2PNormalizedCost
--					@C2PMorbidityUse
--					@C2PMorbidityCost
--					@C2PDemographicUse
--					@C2PDemographicCost
--					@C2PBustersUse
--					@C2PBustersCost
--					@C2PProductUse
--					@C2PProductCost
--					@C2PGeographicUse
--					@C2PGeographicCost
--					@C2PCMSReimbursementUse
--					@C2PCMSReimbursementCost
--					@C2PContractualUse
--					@C2PContractualCost
--					@C2PBendersUse
--					@C2PBendersCost
--					@C2PWorkdayUse
--					@C2PWorkdayCost 
--					@C2PFluUse
--					@C2PFluCost
--					@C2PInducedUtilizationUse
--					@C2PInducedUtilizationCost
--					@C2PActAdjUse
--					@C2PActAdjCost
--					@C2PNonActAdjUse
--					@C2PNonActAdjCost
--					@C2PPoolingUse
--					@C2PPoolingCost
--					@C2PHTPUse
--					@C2PHTPCost
--					@C2PCompoundingAdjUse
--					@C2PCompoundingAdjCost
--					@UserID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			SavedClaimFactorHeader
--					SavedMERActAdj
--					SavedPlanDetail
--					SavedPlanHeader
--
--  Write:			PlanChangeLog
--					SavedBenefitLevelClaimFactors
--					SavedClaimFactorHeader
--					SavedForecastSetup
--					SavedMATrendData      
--					SavedMERActAdj
--					SavedPlanDetail
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetBidYear
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE															DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2012-SEP-26		1           Initial Version															Mike Deren    
-- 2012-OCT-11		2			Added Statement to Exec spClaimFactors									Mike Deren    
-- 2012-OCT-17		3			Added Statement to Exec spResetCalcProjectionFactors					Mike Deren    
-- 2012-DEC-07		4			Changed Data Type in Column Values										Mike Deren    
-- 2012-JAN-03		5			Removed V1																Mike Deren    
-- 2013-JAN-04		6			Changed for isliveindex = 1, ishidden = 0 only							Mike Deren     
-- 2013-JAN-17		7			Added MactaptReprice to run faster, removed update sph					Mike Deren   
-- 2013-JAN-22		8			Changed Selection of ClaimForecastid									Mike Deren  
-- 2013-OCT-08		9			Included Join on Segment ID												Manisha Tyagi      
-- 2013-OCT-31		10			Updated  Datatype and Length for SegmentId,ContractNumber & PlanID 		Anubhav Mishra
-- 2013-NOV-04		11			Updated  @ContractPBP from Varchar(Max) to Varchar(15)					Anubhav Mishra
-- 2014-JAN-14		12			Updated for new trend factors											Mike Deren
-- 2014-JAN-30		13			Changed automatic selction of claim factors								Mike Deren
-- 2014-DEC-02		14			updating IsRiskPlan falg to 0 for provided plan index					Sharath Chandra
-- 2015-DEC-01		15			Sets SavedMERActAdj values back to 1/NULL upon trend upload				Mark Freel
-- 2016-JAN-28		16			Clears old ClaimForecastID's											Mark Freel
-- 2016-SEP-16      17          Added PlanChangeLog Entry for MACTAPT Upload							Chris Fleming
-- 2018-OCT-01		18			Removed the Delete MER code												Jordan Purdue
-- 2018-DEC-10		19			Ensure MER Adjustment table is populated if it is empty					Alex Beruscha
-- 2019-JUN-28		20			Replace SavedPlanHeader with SavedForecastSetup							Pooja Dahiya
-- 2019-NOV-06		21		    Replace SavedForecastSetup with SavedPlanHeader							Chhavi Sinha
-- 2019-NOV-13		22		    Replace setting ForecastID                   							Brent Osantowski
-- 2019-DEC-17		23			Removed casting for ForecastID	                   						Deepali Mittal
-- 2022-DEC-16		24			Table MACTAPTReprice has been dropped									Jake Lewis
--								Alter this SP to stop writing to that table
--								Reformat header to new version and clean up code
-- 2024-DEC-06	    25	        Adding ForecastID to the SavedMERActAdj assumptions table  		        Abraham Ndabian
-- 2025-Mar-03      26          Added MACTAPT New framework  changes                                    Chaitanya Durga K

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spUploadTrendData]
    (
    @ContractNumber            CHAR(5)
   ,@PlanID                    CHAR(3)
   ,@SegmentId                 CHAR(3)
   ,@Component                 INT
   ,@BenefitCategoryNumber     INT
   ,@B2CNormalizedUse          DECIMAL(24, 15)
   ,@B2CNormalizedCost         DECIMAL(24, 15)
   ,@B2CMorbidityUse           DECIMAL(24, 15)
   ,@B2CMorbidityCost          DECIMAL(24, 15)
   ,@B2CDemographicUse         DECIMAL(24, 15)
   ,@B2CDemographicCost        DECIMAL(24, 15)
   ,@B2CBustersUse             DECIMAL(24, 15)
   ,@B2CBustersCost            DECIMAL(24, 15)
   ,@B2CProductUse             DECIMAL(24, 15)
   ,@B2CProductCost            DECIMAL(24, 15)
   ,@B2CGeographicUse          DECIMAL(24, 15)
   ,@B2CGeographicCost         DECIMAL(24, 15)
   ,@B2CCMSReimbursementUse    DECIMAL(24, 15)
   ,@B2CCMSReimbursementCost   DECIMAL(24, 15)
   ,@B2CContractualUse         DECIMAL(24, 15)
   ,@B2CContractualCost        DECIMAL(24, 15)
   ,@B2CBendersUse             DECIMAL(24, 15)
   ,@B2CBendersCost            DECIMAL(24, 15)
   ,@B2CWorkdayUse             DECIMAL(24, 15)
   ,@B2CWorkdayCost            DECIMAL(24, 15)
   ,@B2CFluUse                 DECIMAL(24, 15)
   ,@B2CFluCost                DECIMAL(24, 15)
   ,@B2CInducedUtilizationUse  DECIMAL(24, 15)
   ,@B2CInducedUtilizationCost DECIMAL(24, 15)
   ,@B2CActAdjUse              DECIMAL(24, 15)
   ,@B2CActAdjCost             DECIMAL(24, 15)
   ,@B2CNonActAdjUse           DECIMAL(24, 15)
   ,@B2CNonActAdjCost          DECIMAL(24, 15)
   ,@B2CPoolingUse             DECIMAL(24, 15)
   ,@B2CPoolingCost            DECIMAL(24, 15)
   ,@B2CHTPUse                 DECIMAL(24, 15)
   ,@B2CHTPCost                DECIMAL(24, 15)
   ,@B2CCompoundingAdjUse      DECIMAL(24, 15)
   ,@B2CCompoundingAdjCost     DECIMAL(24, 15)
   ,@C2PNormalizedUse          DECIMAL(24, 15)
   ,@C2PNormalizedCost         DECIMAL(24, 15)
   ,@C2PMorbidityUse           DECIMAL(24, 15)
   ,@C2PMorbidityCost          DECIMAL(24, 15)
   ,@C2PDemographicUse         DECIMAL(24, 15)
   ,@C2PDemographicCost        DECIMAL(24, 15)
   ,@C2PBustersUse             DECIMAL(24, 15)
   ,@C2PBustersCost            DECIMAL(24, 15)
   ,@C2PProductUse             DECIMAL(24, 15)
   ,@C2PProductCost            DECIMAL(24, 15)
   ,@C2PGeographicUse          DECIMAL(24, 15)
   ,@C2PGeographicCost         DECIMAL(24, 15)
   ,@C2PCMSReimbursementUse    DECIMAL(24, 15)
   ,@C2PCMSReimbursementCost   DECIMAL(24, 15)
   ,@C2PContractualUse         DECIMAL(24, 15)
   ,@C2PContractualCost        DECIMAL(24, 15)
   ,@C2PBendersUse             DECIMAL(24, 15)
   ,@C2PBendersCost            DECIMAL(24, 15)
   ,@C2PWorkdayUse             DECIMAL(24, 15)
   ,@C2PWorkdayCost            DECIMAL(24, 15)
   ,@C2PFluUse                 DECIMAL(24, 15)
   ,@C2PFluCost                DECIMAL(24, 15)
   ,@C2PInducedUtilizationUse  DECIMAL(24, 15)
   ,@C2PInducedUtilizationCost DECIMAL(24, 15)
   ,@C2PActAdjUse              DECIMAL(24, 15)
   ,@C2PActAdjCost             DECIMAL(24, 15)
   ,@C2PNonActAdjUse           DECIMAL(24, 15)
   ,@C2PNonActAdjCost          DECIMAL(24, 15)
   ,@C2PPoolingUse             DECIMAL(24, 15)
   ,@C2PPoolingCost            DECIMAL(24, 15)
   ,@C2PHTPUse                 DECIMAL(24, 15)
   ,@C2PHTPCost                DECIMAL(24, 15)
   ,@C2PCompoundingAdjUse      DECIMAL(24, 15)
   ,@C2PCompoundingAdjCost     DECIMAL(24, 15)
   ,@UserID                    CHAR(7))

AS

    BEGIN

        SET NOCOUNT ON;
        SET ANSI_WARNINGS OFF;

        DECLARE @ContractPBP VARCHAR(15)
               ,@PlanYearID  SMALLINT
               ,@LastUpdate  DATETIME
               ,@ForecastID  INT;

        SELECT  @PlanYearID = dbo.fnGetBidYear ();
        SET @LastUpdate = GETDATE ();
        SET @ContractPBP = @ContractNumber + '-' + @PlanID + '-' + @SegmentId;

        SET @ForecastID = (SELECT       DISTINCT
                                        sph.ForecastID
                           FROM    dbo.SavedPlanHeader sph                 
                           WHERE        sph.ContractNumber = @ContractNumber
                                        AND sph.PlanID = @PlanID
                                        AND sph.SegmentID = @SegmentId
                                        AND sph.IsLiveIndex = 1
                                        AND sph.IsHidden = 0
										AND sph.PlanyearID=dbo.fnGetBidYear ());


        --EXPERIENCE CLAIM FACTOR ID 
        IF @Component = 1
           AND  @BenefitCategoryNumber = 50

            BEGIN

                DECLARE @ClaimForecastIDspd1 INT;
                DECLARE @ClaimForecastIDCurr1 INT;
                SET @ClaimForecastIDspd1 = (SELECT  MAX (ClaimForecastID + 1) FROM  dbo.SavedClaimFactorHeader);
                SET @ClaimForecastIDCurr1 = (SELECT ClaimForecastID
                                             FROM   dbo.SavedPlanDetail
                                             WHERE  MARatingOptionID = 1
                                                    AND ForecastID = @ForecastID);

                UPDATE  dbo.SavedPlanDetail
                SET     ClaimForecastID = @ClaimForecastIDspd1
                WHERE   ForecastID = @ForecastID
                        AND MARatingOptionID = 1;

                INSERT INTO dbo.SavedClaimFactorHeader
                    (PlanYearID
                    ,ClaimForecastID
                    ,ForecastID
                    ,Name
                    ,Description
                    ,IsHidden
                    ,IsAtPlanLevel
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT  @PlanYearID
                       ,@ClaimForecastIDspd1
                       ,@ForecastID
                       ,CAST(@ContractNumber + '-' + @PlanID + '-' + @SegmentId AS VARCHAR(15)) + ' Experience'
                       ,'Experience'
                       ,0   --IsHidden      
                       ,0   --IsAtPlanLevel,      
                       ,@UserID
                       ,@LastUpdate;

                IF @ClaimForecastIDCurr1 IS NOT NULL
                    BEGIN
                        DELETE  FROM dbo.SavedBenefitLevelClaimFactors
                        WHERE   ClaimForecastID = @ClaimForecastIDCurr1;
                    END;

            END;


        --MANUAL CLAIM FACTOR ID  
        IF @Component = 2
           AND  @BenefitCategoryNumber = 50

            BEGIN

                DECLARE @ClaimForecastIDspd2 INT;
                DECLARE @ClaimForecastIDCurr2 INT;
                SET @ClaimForecastIDspd2 = (SELECT  MAX (ClaimForecastID + 1) FROM  dbo.SavedClaimFactorHeader);
                SET @ClaimForecastIDCurr2 = (SELECT ClaimForecastID
                                             FROM   dbo.SavedPlanDetail
                                             WHERE  MARatingOptionID = 2
                                                    AND ForecastID = @ForecastID);

                UPDATE  dbo.SavedPlanDetail
                SET     ClaimForecastID = @ClaimForecastIDspd2
                WHERE   ForecastID = @ForecastID
                        AND MARatingOptionID = 2;

                INSERT INTO dbo.SavedClaimFactorHeader
                    (PlanYearID
                    ,ClaimForecastID
                    ,ForecastID
                    ,Name
                    ,Description
                    ,IsHidden
                    ,IsAtPlanLevel
                    ,LastUpdateByID
                    ,LastUpdateDateTime)
                SELECT  @PlanYearID
                       ,@ClaimForecastIDspd2
                       ,@ForecastID
                       ,CAST(@ContractNumber + '-' + @PlanID + '-' + @SegmentId AS VARCHAR(15)) + ' Manual'
                       ,'Manual'
                       ,0   --IsHidden      
                       ,0   --IsAtPlanLevel,      
                       ,@UserID
                       ,@LastUpdate;

                IF @ClaimForecastIDCurr2 IS NOT NULL
                    BEGIN
                        DELETE  FROM dbo.SavedBenefitLevelClaimFactors
                        WHERE   ClaimForecastID = @ClaimForecastIDCurr2;
                    END;

            END;


        --Delete and insert trend data   
        DELETE  FROM dbo.SavedMATrendData
        WHERE   ContractNumber = @ContractNumber
                AND PlanID = @PlanID
                AND SegmentID = @SegmentId
                AND Component = @Component
                AND BenefitCategoryNumber = @BenefitCategoryNumber;

        INSERT INTO dbo.SavedMATrendData
            (ContractPBP
            ,ContractNumber
            ,PlanID
            ,SegmentID
            ,Component
            ,BenefitCategoryNumber
            ,B2CNormalizedUse
            ,B2CNormalizedCost
            ,B2CMorbidityUse
            ,B2CMorbidityCost
            ,B2CDemographicUse
            ,B2CDemographicCost
            ,B2CBustersUse
            ,B2CBustersCost
            ,B2CProductUse
            ,B2CProductCost
            ,B2CGeographicUse
            ,B2CGeographicCost
            ,B2CCMSReimbursementUse
            ,B2CCMSReimbursementCost
            ,B2CContractualUse
            ,B2CContractualCost
            ,B2CBendersUse
            ,B2CBendersCost
            ,B2CWorkdayUse
            ,B2CWorkdayCost
            ,B2CFluUse
            ,B2CFluCost
            ,B2CInducedUtilizationUse
            ,B2CInducedUtilizationCost
            ,B2CActAdjUse
            ,B2CActAdjCost
            ,B2CNonActAdjUse
            ,B2CNonActAdjCost
            ,B2CPoolingUse
            ,B2CPoolingCost
            ,B2CHTPUse
            ,B2CHTPCost
            ,B2CCompoundingAdjUse
            ,B2CCompoundingAdjCost
            ,C2PNormalizedUse
            ,C2PNormalizedCost
            ,C2PMorbidityUse
            ,C2PMorbidityCost
            ,C2PDemographicUse
            ,C2PDemographicCost
            ,C2PBustersUse
            ,C2PBustersCost
            ,C2PProductUse
            ,C2PProductCost
            ,C2PGeographicUse
            ,C2PGeographicCost
            ,C2PCMSReimbursementUse
            ,C2PCMSReimbursementCost
            ,C2PContractualUse
            ,C2PContractualCost
            ,C2PBendersUse
            ,C2PBendersCost
            ,C2PWorkdayUse
            ,C2PWorkdayCost
            ,C2PFluUse
            ,C2PFluCost
            ,C2PInducedUtilizationUse
            ,C2PInducedUtilizationCost
            ,C2PActAdjUse
            ,C2PActAdjCost
            ,C2PNonActAdjUse
            ,C2PNonActAdjCost
            ,C2PPoolingUse
            ,C2PPoolingCost
            ,C2PHTPUse
            ,C2PHTPCost
            ,C2PCompoundingAdjUse
            ,C2PCompoundingAdjCost
            ,UserID
            ,LastUpdateDateTime)
        SELECT  @ContractPBP
               ,@ContractNumber
               ,@PlanID
               ,@SegmentId  --Including SegmentId   
               ,@Component
               ,@BenefitCategoryNumber
               ,@B2CNormalizedUse
               ,@B2CNormalizedCost
               ,@B2CMorbidityUse
               ,@B2CMorbidityCost
               ,@B2CDemographicUse
               ,@B2CDemographicCost
               ,@B2CBustersUse
               ,@B2CBustersCost
               ,@B2CProductUse
               ,@B2CProductCost
               ,@B2CGeographicUse
               ,@B2CGeographicCost
               ,@B2CCMSReimbursementUse
               ,@B2CCMSReimbursementCost
               ,@B2CContractualUse
               ,@B2CContractualCost
               ,@B2CBendersUse
               ,@B2CBendersCost
               ,@B2CWorkdayUse
               ,@B2CWorkdayCost
               ,@B2CFluUse
               ,@B2CFluCost
               ,@B2CInducedUtilizationUse
               ,@B2CInducedUtilizationCost
               ,@B2CActAdjUse
               ,@B2CActAdjCost
               ,@B2CNonActAdjUse
               ,@B2CNonActAdjCost
               ,@B2CPoolingUse
               ,@B2CPoolingCost
               ,@B2CHTPUse
               ,@B2CHTPCost
               ,@B2CCompoundingAdjUse
               ,@B2CCompoundingAdjCost
               ,@C2PNormalizedUse
               ,@C2PNormalizedCost
               ,@C2PMorbidityUse
               ,@C2PMorbidityCost
               ,@C2PDemographicUse
               ,@C2PDemographicCost
               ,@C2PBustersUse
               ,@C2PBustersCost
               ,@C2PProductUse
               ,@C2PProductCost
               ,@C2PGeographicUse
               ,@C2PGeographicCost
               ,@C2PCMSReimbursementUse
               ,@C2PCMSReimbursementCost
               ,@C2PContractualUse
               ,@C2PContractualCost
               ,@C2PBendersUse
               ,@C2PBendersCost
               ,@C2PWorkdayUse
               ,@C2PWorkdayCost
               ,@C2PFluUse
               ,@C2PFluCost
               ,@C2PInducedUtilizationUse
               ,@C2PInducedUtilizationCost
               ,@C2PActAdjUse
               ,@C2PActAdjCost
               ,@C2PNonActAdjUse
               ,@C2PNonActAdjCost
               ,@C2PPoolingUse
               ,@C2PPoolingCost
               ,@C2PHTPUse
               ,@C2PHTPCost
               ,@C2PCompoundingAdjUse
               ,@C2PCompoundingAdjCost
               ,@UserID
               ,@LastUpdate;

        -- MER
        IF NOT EXISTS (SELECT   1
                       FROM     dbo.SavedMERActAdj
                       WHERE    ContractNumber = @ContractNumber
                                AND PlanID = @PlanID
                                AND SegmentID = @SegmentId
                                AND Component = @Component
                                AND BenefitCategoryNumber = @BenefitCategoryNumber)
            BEGIN
                INSERT INTO dbo.SavedMERActAdj
                    (ForecastID                          -- adding new ForecastID into SavedMERActAdj table by Abe 12/06/2024
					,ContractPBP
                    ,ContractNumber
                    ,PlanID
                    ,SegmentID
                    ,Component
                    ,BenefitCategoryNumber
                    ,C2PMERUseMultAdj
                    ,C2PMERCostMultAdj
                    ,C2PMERUseAddAdj
                    ,C2PMERCostAddAdj
                    ,UserID
                    ,LastUpdateDateTime)
                SELECT  @ForecastID
				       ,@ContractPBP
                       ,@ContractNumber
                       ,@PlanID
                       ,@SegmentId  --Including SegmentId   
                       ,@Component
                       ,@BenefitCategoryNumber
                       ,1
                       ,1
                       ,NULL
                       ,NULL
                       ,@UserID
                       ,@LastUpdate;
            END;

        --SavedForecastSetup: set IsToReprice flag
        IF @BenefitCategoryNumber = 50
           AND  @Component IN (1, 2)
            BEGIN
                UPDATE  dbo.SavedForecastSetup
                SET     IsToReprice = 1
                       ,LastUpdateByID = @UserID
                       ,LastUpdateDateTime = @LastUpdate
                WHERE   ForecastID = @ForecastID;
            END;

        --PlanChangeLog Entry
        INSERT INTO dbo.PlanChangeLog
            (ForecastID
            ,ProcName
            ,Value
            ,AuditUserID
            ,AuditTime)
        VALUES (@ForecastID, 'MACTAPT', NULL, @UserID, GETDATE ());

    END;
GO
