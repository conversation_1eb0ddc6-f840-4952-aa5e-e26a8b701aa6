SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: PrePricing.spLoadPricingModelValueCurrentYear  
--    
-- AUTHOR: Adam Gilbert 
--    
-- CREATED DATE: 2024-Dec-4  
-- Type: 
-- DESCRIPTION: Procedure transforms current year pricing model data and materializes it for use in the market input view.
--    
-- PARAMETERS:    
-- Input: 
   
-- TABLES:   
--  
 
-- Read:    
--  PrePricing.fnGetCurrentYearMarketInputValues()

-- Write:    
--    PrePricing.PricingModelValueCurrentYear
--
-- VIEWS:    
--    
-- FUNCTIONS:    
-- STORED PROCS:    
--      
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE			VERSION		CHANGES MADE					DEVELOPER						 
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2024-Dec-4		1		Initial Version                 Adam Gilbert
-- ----------------------------------------------------------------------------------------------------------------------   
CREATE PROCEDURE [PrePricing].[spLoadPricingModelValueCurrentYear]
AS
	SET NOCOUNT ON;
	BEGIN

		DELETE FROM PrePricing.PricingModelValueCurrentYear;

		INSERT INTO PrePricing.PricingModelValueCurrentYear 
			   (PlanInfoID,
			   SubCategoryID,
			   CurrentYearINValue,
			   CurrentYearOONValue,
			   LastUpdateDateTime)

		SELECT PlanInfoID,
			   SubCategoryID,
			   CurrentYearINValue,
			   CurrentYearOONValue,
			   GETDATE() FROM PrePricing.fnGetCurrentYearMarketInputValues();
	END
GO
