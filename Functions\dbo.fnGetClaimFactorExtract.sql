SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetClaimFactorExtract]    ******/

-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetClaimFactorExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-22
-- HEADER UPDATED: 2011-Dec-22
--
-- DESCRIPTION: Designed to extract fields for Claim Factors - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	
--		SavedClaimFactorHeader
--		Saved<PERSON>laimFactorDetail
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-22		1			Initial Version														Bobby Jaegers
-- 2012-May-08		2			Looks up ClaimForecastID in SavedPlanDetail, not					Trevor Mahoney
--									SavedClaimFactorHeader
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetClaimFactorExtract]
    (
    @WhereIN Varchar(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [int] NOT NULL,
	Name varchar(MAX),
	[Description] varchar(MAX), 
	[Incremental Contract Changes Exp to Curr] decimal(8, 6),
	[Incremental Contract Changes Curr to Proj] decimal(8, 6),
	[HSO/DM/CM Exp to Curr] decimal(8, 6),
	[HSO/DM/CM Curr to Proj] decimal(8, 6),
	[HPN Exp to Curr] decimal(8, 6),
	[HPN Curr to Proj] decimal(8, 6),
	[Population Risk Exp to Curr] decimal(8, 6),
	[Population Risk Curr to Proj] decimal(8, 6),
	[Area Util Exp to Curr] decimal(8, 6),
	[Area Util Curr to Proj] decimal(8, 6),
	[Benefit Plan Change Exp to Curr] decimal(8, 6),
	[Benefit Plan Change Curr to Proj] decimal(8, 6),
	[All Other Utilization Exp to Curr] decimal(8, 6),
	[All Other Utilization Curr to Proj] decimal(8, 6),
	[Area Cost Exp to Curr] decimal(8, 6),
	[Area Cost Curr to Proj] decimal(8, 6)
    ) AS

BEGIN
	
	IF @WhereIn IS NULL
		INSERT @Results
			SELECT
				cfh.ForecastID,
				cfh.Name,
				cfh.[Description], 
				[Incremental Contract Changes Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 1 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Incremental Contract Changes Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 1 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HSO/DM/CM Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 2 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HSO/DM/CM Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 2 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HPN Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 3 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HPN Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 3 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Population Risk Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 4 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Population Risk Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 4 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Util Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 5 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Util Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 5 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Benefit Plan Change Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 6 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Benefit Plan Change Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 6 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[All Other Utilization Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 7 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[All Other Utilization Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 7 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Cost Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 8 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Cost Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 8 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END)
			FROM SavedClaimFactorHeader cfh 
			INNER JOIN SavedClaimFactorDetail cfd
				ON cfh.ClaimForecastID = cfd.ClaimForecastID
			WHERE 
				cfd.ClaimForecastID IN 
					((Select ClaimForecastID from SavedPlanDetail where ForecastID = cfh.ForecastID AND MARatingOptioniD = 1), --Experience
					(Select ClaimForecastID from SavedPlanDetail where ForecastID = cfh.ForecastID AND MARatingOptionID = 2)) --Manual
			GROUP BY 
				cfh.ForecastID, 
				cfh.Name, 
				cfh.[Description]

			
       
	ELSE
		INSERT @Results
			SELECT
				cfh.ForecastID,
				cfh.Name,
				cfh.[Description], 
				[Incremental Contract Changes Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 1 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Incremental Contract Changes Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 1 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HSO/DM/CM Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 2 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HSO/DM/CM Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 2 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HPN Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 3 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[HPN Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 3 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Population Risk Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 4 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Population Risk Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 4 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Util Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 5 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Util Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 5 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Benefit Plan Change Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 6 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Benefit Plan Change Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 6 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[All Other Utilization Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 7 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[All Other Utilization Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 7 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Cost Exp to Curr] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 8 AND cfd.PROJECTIONYearID = 1 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END),
				[Area Cost Curr to Proj] = Sum(CASE WHEN cfd.ClaimFactorTypeID = 8 AND cfd.PROJECTIONYearID = 2 AND cfd.IsInNetwork = 1 THEN cfd.ClaimFactor END)
			FROM SavedClaimFactorHeader cfh 
			INNER JOIN SavedClaimFactorDetail cfd
				ON cfh.ClaimForecastID = cfd.ClaimForecastID
			WHERE ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))
				AND
				cfd.ClaimForecastID IN 
					((Select ClaimForecastID from SavedPlanDetail where ForecastID = cfh.ForecastID AND MARatingOptioniD = 1), --Experience
					(Select ClaimForecastID from SavedPlanDetail where ForecastID = cfh.ForecastID AND MARatingOptionID = 2)) --Manual
			GROUP BY 
				cfh.ForecastID, 
				cfh.Name, 
				cfh.[Description]
			
RETURN
END
GO
