SET QUOTED_IDENTIFIER ON
GO
SET AN<PERSON>_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetBP<PERSON>lanWS1BasePlans]  ******/

/****** Object:  User Defined Function dbo.fnGetBPTPlanWS1BasePlans    Script Date: 5/16/2008 2:27:29 PM ******/
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetBPTPlanWS1BasePlans
--
-- AUTHOR: Brian Lake
--
-- CREATED DATE: May-08-2008
--
-- DESCRIPTION: Function responsible for listing Plans in Base on MA WS1
--
-- PARAMETERS:
--	Input:
--		@ForecastID 
--      @PlanVersion 
--
-- RETURNS:
--		Table
--
-- TABLES: 
--	Read:
--      SavedPlanBPTExceptionDetail
--		SavedPlanBPTExceptionHeader
--	Write:
--		NONE
--
-- VIEWS:
--	Read: 
--		NONE
--
-- FUNCTIONS:
--	Read:
--		NONE
--	Called:
--		NONE
--
-- STORED PROCS: 
--	Executed:
--		NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE																	DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2008-May-08		1			Initial Version																	Brian Lake
-- 2008-May-16      2			Added Base period Member Months to output										Brian Lake
-- 2009-Mar-17      3			Data types																		Sandy Ellis
-- 2009-May-08      4			Added base period non dual member months and non dual MM % to output			Lawrence Choi
-- 2010-Mar-11		5			Implemented new PlanYear methodology and independence for 2012					Joe Casey
-- 2010-May-28		6			Removed @TempItems table and cleaned up code									Michael Siekerka
-- 2013-Oct-08		7			Modified to Include SegmentId													Anubhav Mishra
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetBPTPlanWS1BasePlans]
(
	@ForecastID INT,
	@PlanVersion INT
)
RETURNS @Results TABLE 
(
    BaseID INT IDENTITY,
    [ContractNumber-PBP] CHAR(9),
    ContractNumber CHAR(5) ,
    PlanID CHAR(3),
    SegmentId CHAR(3),	--Added SegmentId
    PercentOfMM DECIMAL (12, 4),
    PercentOfNonDualMM DECIMAL (12, 4),    
    BaseMemberMonths INT,
    BaseNonDualMemberMonths INT,
    Credibility DECIMAL (7, 6),
    IsBasePlan BIT
) AS

    BEGIN
        IF (SELECT IsBasePlan 
            FROM SavedPlanBPTExceptionHeader 
            WHERE ForecastID = @ForecastID 
                AND IsHidden = 0) = 1
            --This is the only base plan, so pick up the credibility
            BEGIN
                INSERT INTO @Results 
                SELECT 
					Null,
                    Null, 
                    Null, 
                    Null, 
                    NUll,
                    Null, 
                    Null,
                    Null,  --No member months needed
                    Credibility,
                    IsBasePlan
                FROM SavedPlanBPTExceptionHeader
                WHERE ForecastID = @ForecastID
            END
        ELSE  --There are other base plans, so pick up the Contracts and MM %
            BEGIN
                --First need to get the total MM in the Base plans, to use for finding %s.
                DECLARE @TotalMM DECIMAL (12, 4),
						@TotalNonDualMM DECIMAL (12, 4)

                SELECT @TotalMM = SUM(MemberMonths), @TotalNonDualMM = SUM(NonDualMemberMonths)
                FROM SavedPlanBPTExceptionDetail
                WHERE ForecastID = @ForecastID
                    AND IsHidden = 0

                INSERT INTO @Results 
                SELECT 
                    [ContractNumber-PBP],
                    ContractNumber,
                    PlanID,
                    SegmentId,
                    PercentOfMM = ROUND((MemberMonths / @TotalMM), 4),
                    PercentOfNonDualMM = ROUND((NonDualMemberMonths / @TotalNonDualMM), 4),
                    MemberMonths,
                    NonDualMemberMonths,
                    H.Credibility,
                    0 --Not base plan 
                FROM SavedPlanBPTExceptionHeader H
                LEFT JOIN SavedPlanBPTExceptionDetail D
                    ON H.ForecastID = D.ForecastID
                WHERE H.ForecastID = @ForecastID
                   AND [ContractNumber-PBP] <> 'All Other' --All Other needs to be listed last
                    AND H.IsHidden = 0
                    AND D.IsHidden = 0
                
                UNION ALL
                
                --Ensure that the All Other is last.
                SELECT
					[ContractNumber-PBP],
                    ContractNumber,
                    PlanID, 
                    SegmentId,
                    PercentOfMM =  
                            1 - (SELECT SUM(ROUND(MemberMonths / @TotalMM, 4))
                                FROM SavedPlanBPTExceptionDetail
                                WHERE ForecastID = @ForecastID
                                    AND IsHidden = 0
                                    AND [ContractNumber-PBP] <> 'All Other'
                                    ),
                    PercentOfNonDualMM =  
                            1 - (SELECT SUM(ROUND(NonDualMemberMonths / @TotalNonDualMM, 4))
                                FROM SavedPlanBPTExceptionDetail
                                WHERE ForecastID = @ForecastID
                                    AND IsHidden = 0
                                    AND [ContractNumber-PBP] <> 'All Other'
                                    ),
                    MemberMonths,
                    NonDualMemberMonths,
                    H.Credibility,
                    0 --Not base plan again
                FROM SavedPlanBPTExceptionHeader H
                LEFT JOIN SavedPlanBPTExceptionDetail D
                    ON H.ForecastID = D.ForecastID
                WHERE H.ForecastID = @ForecastID
                    AND [ContractNumber-PBP] = 'All Other'
                    AND H.IsHidden = 0
                    AND D.IsHidden = 0
            END
        RETURN            
    END
GO
