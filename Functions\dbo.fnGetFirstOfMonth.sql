SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnGetFirstOfMonth]  ******/

-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetFirstOfMonth
--
-- AUTHOR: Tonya Cockrell
--
-- CREATED DATE: 2007-Jan-23
--
-- DESCRIPTION: Returns a datetime containing the first day of the month based on the specified month and year,
--		   plus the specified number of additional months.
--
-- PARAMETERS:
--	Input:
--		@Month - The month to use as a starting point.
--		@Year - The year to use as a starting point.
--		@MonthAdd - The number of months to add to the starting point.  (May be 0 or negative.)
--
-- RETURNS: The date @Month/1/@Year plus @MonthAdd months.
--
-- TABLES: 
--	Read: NONE
--	Write: NONE
--
-- VIEWS:
--	Read: NONE
--
-- FUNCTIONS:
--	Read: NONE
--	Called: NONE
--
-- STORED PROCS: 
--	Executed: NONE
--
-- $HISTORY 
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE																	DEVELOPER		
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2007-Jan-23		1			Initial Version																	Tonya Cockrell
-- 2007-Oct-09		2			Revised code to bring it into coding standards.									Shannon Boykin
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetFirstOfMonth]
    (	
    @Month AS TINYINT,
    @Year AS SMALLINT,
    @MonthAdd AS SMALLINT
    )
RETURNS DATETIME AS
BEGIN 
    DECLARE @Date DATETIME
    
    --Compute the starting point - the first of the month.
    SET @Date = CONVERT(VARCHAR,@Month) + '/1/' + CONVERT(VARCHAR,@Year)
    
    --Add the additional months.
    SET @Date = DATEADD(m,@MonthAdd,@Date)

    RETURN @Date
END
GO
