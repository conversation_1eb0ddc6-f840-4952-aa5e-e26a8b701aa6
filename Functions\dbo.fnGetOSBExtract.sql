SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO



-------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnGetOSBExtract
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2011-Dec-14
-- HEADER UPDATED: 2011-Dec-15
--
-- DESCRIPTION: Designed to extract fields for the OSBs - will match the upload
--
-- PARAMETERS:
--  Input:
--      @WhereIN
--      
--  Output:
--
-- TABLES:
--  Read:	SavedPlanOptionalPackageDetail
--  Write:
--
-- VIEWS:
--      
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- HISTORY:
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2011-Dec-14		1			Initial Version														Alex Rezmerski
-- 2011-Dec-15		2			Updated for coding standards.  Added IF statement to account		Craig Wright
--									for @WhereIn being Null. Updated PackageIndex to CHAR(3).
--									Added IsHidden in Where statement.
-- 2012-Jun-22		3			Added OSB name to Package Index under Package Index Name			Nick Koesters
-- 2023-Dec-12		4			Updated columns in extract tp include OSB code and name				Hannah Harmon
--									and removed OSB Package Index to meet current Market 
--									Support needs.
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetOSBExtract]
    (
    @WhereIN VARCHAR(MAX) = NULL
    )
RETURNS @Results TABLE
	(  
    ForecastID [INT] NOT NULL,
	OSBCode VARCHAR(7),
	OSBName VARCHAR(MAX)
    ) AS

BEGIN

	IF @WhereIn IS NULL
		INSERT @Results
			SELECT  opd.ForecastID, 
					OSBCode = LEFT(oph.Description, 6), 
					OSBName = Name
				--PackageIndexName = CAST(opd.PackageIndex AS VARCHAR(MAX)) + ': ' + Name 
				--OSBPackageIndex = opd.PackageIndex
			FROM SavedPlanOptionalPackageDetail opd
			INNER JOIN perintoptionalpackageheader oph
				ON opd.PackageIndex = oph.PackageIndex
			WHERE IsHidden = 0

	ELSE
		INSERT @Results
			SELECT  ForecastID,
					OSBCode = LEFT(oph.Description, 6), 
					OSBName = Name
				--PackageIndexName = CAST(opd.PackageIndex AS VARCHAR(MAX)) + ': ' + Name
				--OSBPackageIndex = opd.PackageIndex
			FROM SavedPlanOptionalPackageDetail opd
			INNER JOIN perintoptionalpackageheader oph
				ON opd.PackageIndex = oph.PackageIndex
			WHERE IsHidden = 0
				AND ForecastID IN (SELECT Value FROM dbo.fnStringSplit (@WhereIN, ','))

RETURN
END
GO
