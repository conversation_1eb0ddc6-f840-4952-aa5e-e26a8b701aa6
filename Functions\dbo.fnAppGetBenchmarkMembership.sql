SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetBenchmarkMembership]   ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnAppGetBenchmarkMembership
--
-- AUTHOR: Joe Casey 
--
-- CREATED DATE: 2010-Dec-23
-- HEADER UPDATED: 2018-Jun-25
--
-- DESCRIPTION: Function responsible for listing membership on the Plan level Benchmark tab in the SIGMA.
--
-- PARAMETERS:
--	Input:
--		@XForecastID
--  Output:
--
-- TABLES:  
--	Read:
--      LkpExtCMSStateCounty
--		LkpExtCMSStateTerritory
--      SavedPlanMemberMonthDetail
--      SavedPlanStateCountyDetail
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnPadInteger
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Dec-23      1           Initial Version                                                     Joe Casey
-- 2011-Jan-12		2			All Membership value are now Decimal instead of Int					Jiao Chen
-- 2011-Jan-31		3			Populate Hospice membership											Joe Casey
-- 2011-Feb-23		4			'ESRD' now contains 'ESRD' only and 'ESRD+Hospice'					Joe Casey
-- 2011-Mar-16		5			'ESRD+Hospice' moved from 'ESRD' to 'Hospice'						Joe Casey
-- 2011-Apr-26		6			Now Scales to Sales membership										Craig Wright
-- 2011-Aug-25		7			Changed to account for Sales membership returning a table			Craig Wright
-- 2011-Sep-08		8			Changed to pull SalesAdjustmentFactor from SavedPlanAssumptions		Alex Rezmerski
-- 2011-Sep-09		9			Changed to account for SalesAdjustmentFactor being null				Alex Rezmerski
-- 2015-May-08		10			Added No Lock to tables being fetched								Manish Shukla
-- 2018-Jun-25		11			Add State Name to Benchmark tab										Chris Fleming
-- 2022-Sep-28		12			@XVariables, WITH (NOLOCK)											Phani Adduri
-- 2023-Aug-03		13			Added schema 														Sheetal Patil
-- 2024-Jul-02		14		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetBenchmarkMembership] 
(
    @ForecastID INT
)
--Returns a result set that lists Total Medicals for given selection parameters
RETURNS @Results TABLE 
(
    CountyCode VARCHAR(5),
	StateName  CHAR(2),
    CountyName VARCHAR(50),
    NonDE#Aged DECIMAL (28,6),
    NonDE#ESRD DECIMAL (28,6),
    NonDE#Hospice DECIMAL (28,6),
    NonDE#Total DECIMAL (28,6),
    DE#Aged DECIMAL (28,6),
    DE#ESRD DECIMAL (28,6),
    DE#Hospice DECIMAL (28,6),
    DE#Total DECIMAL (28,6),
    TotalAged DECIMAL (28,6),
    TotalESRD DECIMAL (28,6),
    TotalHospice DECIMAL (28,6),
    TotalTotal DECIMAL (28,6)
) AS
BEGIN

 --Set Sales Scaler
DECLARE @XForecastID INT = @ForecastID;

    INSERT @Results
    SELECT
        CountyCode,
		StateTerritoryShortName,
        CountyName,
        [Non_DE#_Aged]      = ISNULL([1],0),
        [Non_DE#_ESRD]      = ISNULL([7],0),
        [Non_DE#_Hospice]   = (ISNULL([4],0) + ISNULL([10],0)),
        [Non_DE#_Total]     = (ISNULL([1],0) + ISNULL([4],0) + ISNULL([7],0) + ISNULL([10],0)),
        [DE#_Aged]          = ISNULL([2],0),
        [DE#_ESRD]          = ISNULL([8],0),
        [DE#_Hospice]       = (ISNULL([5],0) + ISNULL([11],0)),
        [DE#_Total]         = (ISNULL([2],0) + ISNULL([5],0) + ISNULL([8],0) + ISNULL([11],0)),
        [Total_Aged]        = (ISNULL([1],0) + ISNULL([2],0)),
        [Total_ESRD]        = (ISNULL([7],0) + ISNULL([8],0)),
        [Total_Hospice]     = (ISNULL([4],0) + ISNULL([5],0) + ISNULL([10],0) + ISNULL([11],0)),
        [Total_Total]       = (ISNULL([1],0) + ISNULL([2],0)+ ISNULL([4],0) + ISNULL([5],0) + ISNULL([7],0) + ISNULL([8],0) + ISNULL([10],0) + ISNULL([11],0))

    FROM
        (SELECT 
            CountyCode = CAST(dbo.fnPadInteger(mm.StateTerritoryID, 2) + mm.CountyCode AS VARCHAR(5)),
			ter.StateTerritoryShortName,
            cms.CountyName,
            DemogList = mm.DemogIndicator,
            MemberMonths = mm.MemberMonths
        FROM dbo.SavedPlanMemberMonthDetail mm WITH (NOLOCK)
        INNER JOIN dbo.SavedPlanStateCountyDetail sc WITH (NOLOCK)
            ON sc.ForecastID = mm.ForecastID
            AND sc.StateTerritoryID = mm.StateTerritoryID
            AND sc.CountyCode = mm.CountyCode
        INNER JOIN dbo.LkpExtCMSStateCounty cms WITH (NOLOCK)
            ON cms.StateTerritoryID = sc.StateTerritoryID
            AND cms.CountyCode = sc.CountyCode
		INNER JOIN dbo.LkpExtCMSStateTerritory ter WITH(NOLOCK)
			ON ter.StateTerritoryID = cms.StateTerritoryID
        WHERE mm.ForecastID = @XForecastID
            AND sc.IsCountyExcludedFromBPTOutput = 0) AS SourceTable
    PIVOT
        (MAX(MemberMonths)
        FOR DemogList IN ([1],[7],[4],[10],[2],[8],[5],[11])) AS PivotTable
    ORDER BY CountyCode, CountyName
RETURN
END
GO
