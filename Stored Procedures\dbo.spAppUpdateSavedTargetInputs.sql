SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- =============================================      
-- Author:  <PERSON>       
-- Create date: 06 Aug 2021
-- Description:  Handles the saving of Target inputs after successfully targeting,
--				   recognizing whether the target input is a new value or a previously input value
--      
-- PARAMETERS:      
-- Input: @ForecastID 
-- Input: @TargetValue 
-- Input: @UserID 
-- Input: @TargetInputTypeID where 1=Profit %, 2=Member Premium, 3=MER, 4=Profit PMPM

-- TABLES:      
-- Read: dbo.SavedTargetingInputs

-- Write/Update: dbo.SavedTargetingInputs     
--
-- VIEWS:      
--      
-- FUNCTIONS: 
--        
-- STORED PROCS:        

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION		CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 06 Aug 2021    1			Initial version.											Bob Knadler
-- 11 Oct 2021    2         Fix for bug-7607                                            Ramandeep Saini
-- 03 Aug 2023	  3			Added @Xvariable, (NOLOCK)									Sheetal Patil 
-- 13 Jan 2025	  4			Add fourth target method, profit PMPM						Michael Manes
-- ----------------------------------------------------------------------------------------------------------------------        

-- =============================================           

CREATE PROCEDURE [dbo].[spAppUpdateSavedTargetInputs]
@ForecastID         INT
,@TargetValue       FLOAT
,@UserID            VARCHAR(7)
,@TargetInputTypeID TINYINT
AS

    --	@MessageFromBackend VARCHAR(MAX) OUT,		Consider including parameter for messaging?
    --	@Result BIT OUT								Consider prameter for return code

    BEGIN

        DECLARE @XForecastID        INT        = @ForecastID
               ,@XTargetValue       FLOAT      = @TargetValue
               ,@XTargetInputTypeID TINYINT    = @TargetInputTypeID
               ,@XUserID            VARCHAR(7) = @UserID
               ,@Tolerance          DECIMAL(14, 8);
        SELECT  @Tolerance = CASE @XTargetInputTypeID WHEN 1 THEN 0.000005 --Profit % to 5 places
                                                      WHEN 2 THEN 0.0005   --Member Premium to 3 places
                                                      WHEN 3 THEN 0.000005 --MER to 5 places
                                                      WHEN 4 THEN 0.00005   --Profit PMPM to 4 places
                                                      ELSE 0 END;

        IF (SELECT  sti.ForecastID
            FROM    dbo.SavedTargetingInputs sti WITH (NOLOCK)
            WHERE   sti.ForecastID = @XForecastID
                    AND sti.TargetingInputTypeID = @XTargetInputTypeID) IS NULL
            BEGIN
                INSERT INTO dbo.SavedTargetingInputs
                    (ForecastID
                    ,TargetingInputTypeID
                    ,TargetInputValue
                    ,LastUpdateByID
                    ,LastUpdateDateTime
                    ,UsedInTargetingDateTime)
                VALUES (@XForecastID        -- ForecastID - int
                       ,@XTargetInputTypeID -- TargetingInputTypeID - tinyint
                       ,@XTargetValue       -- TargetInputValue - decimal(14, 6)
                       ,@XUserID            -- LastUpdateByID - char(7)
                       ,GETDATE ()          -- LastUpdateDateTime - datetime
                       ,GETDATE ()          -- UsedInTargetingDateTime - datetime
                );
            END;
        ELSE
            BEGIN
                UPDATE      t1
                SET         t1.TargetInputValue = t2.UpdtTargetInputValue
                           ,t1.LastUpdateByID = t2.UpdtUserID
                           ,t1.LastUpdateDateTime = t2.UpdtLastInputTS
                           ,t1.UsedInTargetingDateTime = GETDATE ()
                FROM        dbo.SavedTargetingInputs t1
               INNER JOIN   (SELECT sti.ForecastID
                                   ,sti.TargetingInputTypeID
                                   ,sti.TargetInputValue
                                   ,sti.LastUpdateByID
                                   ,sti.LastUpdateDateTime
                                   ,CASE WHEN ABS (@XTargetValue - sti.TargetInputValue) < @Tolerance THEN
                                             sti.TargetInputValue
                                         ELSE @XTargetValue END AS UpdtTargetInputValue
                                   ,CASE WHEN ABS (@XTargetValue - sti.TargetInputValue) < @Tolerance THEN
                                             sti.LastUpdateByID
                                         ELSE @XUserID END AS UpdtUserID
                                   ,CASE WHEN ABS (@XTargetValue - sti.TargetInputValue) < @Tolerance THEN
                                             sti.LastUpdateDateTime
                                         ELSE GETDATE () END AS UpdtLastInputTS
                             FROM   dbo.SavedTargetingInputs sti WITH (NOLOCK)
                             WHERE  sti.ForecastID = @XForecastID
                                    AND sti.TargetingInputTypeID = @XTargetInputTypeID) t2
                       ON t2.ForecastID = t1.ForecastID
                          AND   t2.TargetingInputTypeID = t1.TargetingInputTypeID
						WHERE 1=1;
            END;

    END;
GO

