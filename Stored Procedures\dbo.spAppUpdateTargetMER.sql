SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================        
-- Author:  <PERSON> Smith         
-- Create date: 28 Aug 2020  
-- Description:  spAppUpdateTargetMER  
  
-- $HISTORY           
  
-- ----------------------------------------------------------------------------------------------------------------------          
-- DATE   VERSION  CHANGES MADE                                                DEVELOPER          
-- ----------------------------------------------------------------------------------------------------------------------          
-- 28 Aug 2020  1   Initial version.                                           <PERSON>  
-- 19 Sep 2021  2   Revisions to capture error/validation messages             Bob Knadler  
--                  from executed sp's e.g., Targeting Mbr Prem prerequisite  
-- 01-Oct-2021  3   Uncommented execution of  sp-[spAppUpdateSavedTargetInputs]   Ramandeep Saini 
-- 04-Nov-2022  4	select @MessageFromBackend
-- 01-Aug-2023	5	Added @XVariable 											  Sheetal Patil  
-- ----------------------------------------------------------------------------------------------------------------------          
        
CREATE PROCEDURE [dbo].[spAppUpdateTargetMER]  
 @ForecastID INT,  
 @MER DECIMAL(10,8),  
 @UserID CHAR(7),  
 @MessageFromBackend VARCHAR(MAX) OUT,  
 @Result BIT OUT        
AS  
BEGIN  
DECLARE @XForecastID INT = @ForecastID,
		@XMER DECIMAL(10,8) = @MER,
		@XUserID CHAR(7) = @UserID,
		@ActAdjFactor DECIMAL(10,6)
 --DECLARE @ProposedMER DECIMAL(10,8) = @MER   
  
 --BEGIN TRANSACTION;  
        BEGIN TRY   
   BEGIN      
--    EXECUTE dbo.spAppCalculateMER @ForecastID, @UserID, @ProposedMER  
-- Bob K 9/19/21 Executing above sp is unncecessary; Just go directly to spTargetActAdjForMER  
  
EXEC dbo.spTargetActAdjForMER @ForecastID = @XForecastID, @UserId = @XUserID, @TargetedMER = @XMER,
       @ActAdj = @ActAdjFactor, @ValidationMessage = @MessageFromBackend OUT  
  
      END  
 --COMMIT TRANSACTION;  
-- BK 9/19   
   IF @MessageFromBackend = '0' --Targeting of MER completed successfully  
      BEGIN          
     SET @MessageFromBackend =  'The Target MER calculation was successful and the scenario has been repriced.'  
     SET @Result=1   
-- Bob K 9/18/21 commented out below temporarily, until 21.12 ready for environments above DEV_Grow  
			EXECUTE [dbo].[spAppUpdateSavedTargetInputs] @XForecastID, @XMER, @XUserID, 3     --3 implies update Target MER in table
      END  
   ELSE   
      BEGIN  
     SET @MessageFromBackend = @MessageFromBackend  
     SET @Result=0   
      END  
    END TRY  
  
        BEGIN CATCH  
	DECLARE @Error1 NVARCHAR(2000);
	DECLARE @Error2 NVARCHAR(2000);
    DECLARE @ErrorMessage NVARCHAR(4000);    
    DECLARE @ErrorSeverity INT;    
    DECLARE @ErrorState INT;  
    DECLARE @ErrorException NVARCHAR(4000);   
  
   SELECT @Error1 = ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ CAST(ERROR_LINE() AS VARCHAR)+' .Error Severity :'
   SELECT @Error2 = CAST(@ErrorSeverity AS VARCHAR)+' .Error State :'+ CAST(@ErrorState AS VARCHAR)
   SELECT @ErrorMessage = @Error1 + @Error2   
   RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)  
     
   --ROLLBACK TRANSACTION;   
    SET @MessageFromBackend =  @ErrorMessage  
    SET @Result=0   
        END CATCH;    
  SELECT @MessageFromBackend
END;
GO
