SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO

-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME:	dbo.fnGetLiveBidYearPlanList
--
-- CREATOR:			Jake Lewis
--
-- CREATED DATE:	2021-06-30
--
-- DESCRIPTION:		Returns a list of live bid year plans (includes PlanInfoID, ForecastID, CPS, and PlanYearID).
--		
-- PARAMETERS:
--  Input  :		NONE
--
--  Output :		@Results
--
-- TABLES : 
--	Read :			SavedPlanInfo
--					SavedForecastSetup
--					SavedRollupForecastMap
--					SavedRollupInfo
--					SavedServiceAreaOption
--
--  Write:			NONE
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetBidYear
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE																								DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- JUN-30-2021		1			Initial Version																								Jake Lewis	
-- OCT-07-2022		2			Removed vwPlanInformation reference due to PlanIndex / ForecastID changes									Jake Lewis
--								Also added WHERE clause to limit to bid year plans only
-- APR-22-2024		3			Added With (NOLOCK)																							Kiran Kola
-- MAY-21-2025		4			Now pulling data from SavedPlanInfo and SavedServiceAreaOption tables, instead of vwSAMCrosswalks, 
--									in order to speed up the query																			Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetLiveBidYearPlanList]
    ()
RETURNS @Results TABLE
    (PlanInfoID INT      NOT NULL
    ,ForecastID INT      NOT NULL
    ,CPS        CHAR(13) NOT NULL
    ,PlanYearID INT      NOT NULL)

AS

    BEGIN

        DECLARE @LiveRollupName CHAR(4) = 'Live';
        DECLARE @TermedPlansRenewalTypeID TINYINT = 2;
        DECLARE @BidYear INT = (SELECT  dbo.fnGetBidYear ());

        INSERT INTO @Results
            (PlanInfoID
            ,ForecastID
            ,CPS
            ,PlanYearID)
        SELECT      spi.PlanInfoID
                   ,sfs.ForecastID
                   ,spi.CPS
                   ,spi.PlanYear AS PlanYearID
        FROM        dbo.SavedPlanInfo spi WITH (NOLOCK)
       INNER JOIN   dbo.SavedForecastSetup sfs WITH (NOLOCK)
               ON sfs.PlanInfoID = spi.PlanInfoID
       INNER JOIN   dbo.SavedRollupForecastMap map WITH (NOLOCK)
               ON map.ForecastID = sfs.ForecastID
       INNER JOIN   dbo.SavedRollupInfo sri WITH (NOLOCK)
               ON sri.RollupID = map.RollupID
       INNER JOIN   dbo.SavedServiceAreaOption sao WITH (NOLOCK)
               ON sao.PlanInfoID = sfs.PlanInfoID
                  AND   sao.ServiceAreaOptionID = sfs.ServiceAreaOptionID
        WHERE       sri.RollupName = @LiveRollupName --Live plan, allows for P2P migration in BMAT
                    AND sao.RenewalTypeID <> @TermedPlansRenewalTypeID --Exclude Bid Year termed plans
                    AND spi.PlanYear = @BidYear;    --Bid Year plans only

        RETURN;

    END;
GO
