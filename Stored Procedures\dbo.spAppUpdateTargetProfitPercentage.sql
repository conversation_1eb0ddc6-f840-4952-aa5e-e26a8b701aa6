SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- =============================================      
-- Author:  <PERSON> Smith      
-- Create date: 16 Aug 2020
-- Description:  spAppUpdateTargetProfitPercentage

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION		CHANGES MADE                                                      DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 16 Aug 2020  1			Initial version.											       <PERSON> Smith
-- 26 Aug 2021  2           Updated for story-2451521 to include sp-[spAppUpdateSavedTargetInputs]    Ramandeep Saini
-- 18 Sep 2021  3			If spPlanRefresh error message restore SavedPlanAssumption values		Bob Knadler
--							 This is 21.11 fix so commenting out part of Ramandeep's change for 21.12
-- 21-Sep-2021  4			Commented out Begin/Commit/Rollback Transaction in order to 		Bob Knadler
--							  not block simultanewous targeting from UPDATE statement (per Surya)
--01-Oct-2021   5           Uncommented execution of  sp-[spAppUpdateSavedTargetInputs]                 Ramandeep Saini
-- ----------------------------------------------------------------------------------------------------------------------        

-- =============================================           
CREATE PROCEDURE [dbo].[spAppUpdateTargetProfitPercentage]
	@ForecastID int,
	@ProfitPercent DECIMAL(10,8),
	@UserID CHAR(7),
	@MessageFromBackend VARCHAR(MAX) OUT,
	@Result BIT OUT      
AS
BEGIN
--DECLARE @ForecastID int = @ForecastID
DECLARE @ForecastIDList int = @ForecastID
DECLARE @ValidationMessage varchar(max)
DECLARE @CurSPAProfitPct DECIMAL(10,8)		--Hold profit % in SavedPlanAssumptions and return to this value if targeting new profit % unsuccessful
DECLARE @CurSPAExpensePct DECIMAL(10,8)		--Hold expense % in SavedPlanAssumptions and return to this value if targeting new profit % unsuccessful

--	BEGIN TRANSACTION;
        BEGIN TRY 
			BEGIN				
			   SELECT @CurSPAProfitPct = spa.ProfitPercent
				     ,@CurSPAExpensePct = spa.ExpensePercent
			     FROM dbo.SavedPlanAssumptions spa
				 WHERE spa.ForecastID = @ForecastID

		        UPDATE dbo.SavedPlanAssumptions SET ExpensePercent= NULL, ProfitPercent = @ProfitPercent WHERE ForecastID = @ForecastID
				EXECUTE dbo.spFlagForReprice @ForecastIDList, @UserID

				EXECUTE dbo.spAppPlanRefresh @ForecastID, @UserID, @ValidationMessage OUTPUT

		    END
--	COMMIT TRANSACTION;
	SET @MessageFromBackend =  @ValidationMessage
	IF (@MessageFromBackend is null OR @MessageFromBackend = '') 
	  BEGIN
		SET @MessageFromBackend =  'The Target Profit % calculation was successful and the scenario has been repriced.'
		SET @Result=1 
-- Bob K 9/18/21 commented out below temporarily, until 21.12 ready for environments above DEV_Grow
	EXECUTE [dbo].[spAppUpdateSavedTargetInputs] @ForecastID, @ProfitPercent, @UserID, 1     --1 implies update Target Profit % in table
	  END
	ELSE
	  BEGIN
		-- Return SavedPlanAssumptions to original values before failed targeting of Profit %
		UPDATE dbo.SavedPlanAssumptions 
			SET ExpensePercent= @CurSPAExpensePct
			   ,ProfitPercent = @CurSPAProfitPct 
			 WHERE ForecastID = @ForecastID
		
		SET @MessageFromBackend = @MessageFromBackend
		SET @Result = 0
	  END
  		END TRY
        BEGIN CATCH
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;
			 DECLARE @ErrorException NVARCHAR(4000); 

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)

--			ROLLBACK TRANSACTION; 
		set @MessageFromBackend =  @ErrorMessage
		SET @Result=0 
        END CATCH;  


END;
GO
