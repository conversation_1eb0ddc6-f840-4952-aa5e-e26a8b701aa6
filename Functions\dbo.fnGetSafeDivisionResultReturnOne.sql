SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
---------------------------------------------    
-- FUNCTION NAME: fnGetSafeDivisionResultReturnOne    
--    
-- AUTHOR: <PERSON>ie    
--    
-- CREATED DATE: 2008-May-07    
--    
-- DESCRIPTION: Function returns 1 if a divide by 0 error would normally have been returned.    
--    
-- PARAMETERS:    
-- Input:    
--              @Numerator float    
--              @Denominator float    
-- RETURNS: Table    
--    
-- TABLES:     
-- Read: NONE    
-- Write: NONE    
--    
-- VIEWS:    
-- Read: NONE    
--    
-- FUNCTIONS:    
-- Read:  NONE    
-- Called:  NONE    
--    
-- STORED PROCS:     
-- Executed: NONE    
-- $HISTORY     
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
--------------------------------------------    
-- DATE   VERSION  CHANGES MADE      DEVELOPER      
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
--------------------------------------------    
--  2008-May-07       1         Initial version											Christian Cofie    
--  2011- JUN-01	2			Change in DataType of @Result due to issue 36 in sigma	Sivachidambaram 
/*In the two functions fnSalesMembershipAdjustmentFactor , fnGetSafeDivisionResultReturnOne the output variable is declared as decimal(25,15), but inside the function, they are manipulating a data like multiplying it by 12 and storing in a variable of float data type(which is the ideal one) – which is in contradict with the data type of the result. So, if in case, the user is going to provide a very large data and if we multiply it by 12, we would surely not able to hold the output/result in a decimal(25,15).*/ 
--  2011-Jun-07		3			Changed DataType of Result to DECIMAL(30,15)			Trevor Mahoney
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
--------------------------------------------    
    
CREATE FUNCTION [dbo].[fnGetSafeDivisionResultReturnOne]    
    (    
    @Numerator float,    
    @Denominator float    
    )    
RETURNS DECIMAL(30,15) AS    
    BEGIN    
        DECLARE @Result DECIMAL(30,15) 
    
        IF IsNull(@Denominator,0)=0    
            SET @Result=1    
        ELSE    
            SET @Result=IsNull(@Numerator,0)/@Denominator     
           
        RETURN @Result        
    END
GO
