SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- ----------------------------------------------------------------------------------------------------------------------    
-- PROCEDURE NAME: spCalcBenchmarkSummary    
--     
-- CREATOR: Brian Lake    
--     
-- CREATED DATE: 2008-Jun-04    
-- HEADER UPDATED: 2011-Mar-30    
--     
-- DESCRIPTION: Stored Procedure responsible for creating CalcBenchmarkSummary Table    
--        
-- PARAMETERS:     
--  Input:    
--      @ForecastID    
--      @UserID 
--   
--  Output:    
--     
-- TABLES:     
--  Read:      
--      LkpIntDemogIndicators    
--      PerIntMAAssumptions    
--      SavedPlanAssumptions
--      SavedPlanMemberMonthDetail    
--      SavedPlanDetail    
--      SavedForecastSetup  
--      SavedPlanInfo  
--      SavedPlanISARFactorDetail    
--      SavedPlanRiskFactorDetail    
--      SavedPlanStateCountyDetail    
--
--  Write:    
--		CalcBenchmarkSummary    
--     
-- VIEWS:    
--     
-- FUNCTIONS:    
--      fnGetMemberMonthsAndAllowedByDuals    
--      fnGetRatebook    
--      fnIsZero    
--      fnPlanCountyProjectedMemberMonths    
--		fnSignificantDigits    
--		fnSalesMembershipAdjustmentFactor    
--     
-- STORED PROCS:    
--     
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE             VERSION     CHANGES MADE                                                           DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2008-Jun-04      1           Initial Version                                                        Brian Lake    
-- 2008-Jun-12      2           Updated references to fnPlanCountyProjectedMemberMonths                Brian Lake    
-- 2008-Sep-19      3           Added @XUserID to the list of parameters and replaced all               Shannon Boykin    
--                                occurrences of SUSER_SNAME with @XUserID.    
-- 2008-Sep-25      4           Added @XUserID to the call of fnExperienceMemberMonths and              Shannon Boykin    
--                                fnPlanCountyProjectedMemberMonths.    
-- 2009-Feb-04      5           The EMM function originally did not reference to PlanVersion           Lawrence Choi    
--                                and caused errors for MT.PlanProjectedMembership used in    
--                                Benchmark tab. This is now corrected by innerjoining PMM, EMM    
--                                and PH on PlanVersion.    
-- 2009-Feb-04      6           Some benchmarks in Benchmark tab were wrong because they did not       Lawrence Choi    
--                                reflect the impact of counties exclusion. Now they are     
--                                corrected by adding an extra innerjoin to     
--                                SavedPlanStateCountyDetail which contains the county exclusion    
--                                info.    
-- 2009-Feb-22      7           Revised call to fnExperienceMemberMonths to include new param,         Brian Lake    
--                                PlanVersion. Changed risk factor type from 3 to 3, 9, 10.       
--                                Removed joins on EMM.StateTerritoryID and EMM.CountyCode.          
--                                EMM.MemMths = Max(MM). Changed call to Proj MM, to include both    
--                                duals and non duals    
-- 2009-Feb-23      8           Dual/Non-Dual split and signficant digits.                             Sandy Ellis    
-- 2009-Feb-24      9           Subtracted ESRD Member Months from the Experience MM total             Brian Lake    
-- 2009-Feb-26      10          Added IsNull(ESRDMemberMonths, 0) so that null ESRD members is 0       Brian Lake    
--                                instead    
-- 2009-Mar-02      11          Change decimals to 23,15 from 23,16 to prevent overflows               Sandy Ellis    
-- 2009-Mar-11      12          Revised Error Handling, included 2009 section to use Risk Factors      Brian Lake    
--                                from '09    
-- 2009-Mar-17      13          Data types                                                             Sandy Ellis    
-- 2009-Mar-21      14          Pushed PlanAvgRiskRate/PlanRiskFactor to 24,15 to address              Sandy Ellis    
--                                overflow issues    
-- 2009-Mar-24      15          Changed other                                                          Sandy Ellis     
-- 2009-Apr-15      16          Updated experience member months                                       Sandy Ellis    
-- 2009-Apr-15      17          Updated joins to reflect that EMM can have no mems                     Sandy Ellis    
-- 2009-Apr-15      18          Added join on Plan Version to SavedPlanStateCountyDetail               Brian Lake    
-- 2009-Apr-16      19          Added DELETE statement back                                            Brian Lake    
-- 2009-Apr-17      20          Put RF.RiskFactor inside case statement for risk calc                  Sandy Ellis    
-- 2009-Apr-30      21          Added new column ChangeInCOBPMPM                                       Brian Lake    
-- 2009-May-01      22          Added PlanProjectedNonDualMembership and PlanProjectedDualMembership   Keith Galloway
-- 2009-May-08      23          Removed rem from 2009 insert and rem out PlanABBenchmark from 2009     Sandy Ellis    
-- 2009-Jul-23      24          Additional join on SavedPlanStateCountyDetail to account for           Keith Galloway    
--                                versions    
-- 2010-Jan-12      25          Changed Exp membership name for hospice changes                        Nick Skeen    
-- 2010-Feb-13      26          Corrected calculation for MemberMonthsExperience to exclude            Casey Sanders    
--                                hospice and ESRD    
-- 2010-May-05      27          Changed Decimal scale from 24 to 30 for risk factors                   Nick Skeen    
-- 2010-Mar-09      28          Implemented new PlanYear methodology and independence for 2012         Joe Casey    
-- 2010-Jun-14      29          Updated for coding standards          Michael Siekerka    
-- 2010-Sep-20      30          Updated for DemogIndicator and removed PlanVersion                     Jake Gaecke    
-- 2010-Sep-22      31          Updated for fnGetRatebook                                              Jake Gaecke    
-- 2010-Sep-28      32          Added SUM() for memberships in the final selection and fnIsZero to     Michael Siekerka    
--                                remove divide by zero errors    
-- 2010-Oct-06      33          Removed join to PerIntRatebookMap                                      Jake Gaecke    
-- 2010-Nov-03      34          Changed CAST DECIMAL from (24,15) to (30,15) to prevent overflow       Joe Casey    
-- 2010-Nov-08      35          Corrected join for populating #MembershipTotals                        Casey Sanders    
-- 2010-Nov-09      36          Fixed procedure from double counting membership totals                 Michael Siekerka    
-- 2011-Jan-12      37          Change ProjectedMembership from INT to DECIMAL(28,6)                   Joe Casey    
-- 2011-Jan-14      38          Change TotalMembership from INT to DECIMAL(28,6)                       Jiao Chen    
-- 2011-Jan-28      39          PlanYearID now acquired from new function                              Jiao Chen    
-- 2011-Mar-28      40          Added @SalesMembershipAdjustment factor to adjust membership to        Craig Wright    
--                                Sales Membership    
-- 2011-Mar-30      41          Replaced @SalesMembershipAdjustment factor with                        Craig Wright    
--                                fnSalesMembershipAdjustmentFactor     
-- 2011-Jun-14      42          Changed @PlanYearID to return SMALLINT instead of INT                  Bobby Jaegers    
-- 2011-Aug-25      43          Changed to account for Sales membership returning a table              Craig Wright    
-- 2011-Sep-08      44          Changed to pull SalesAdjustmentFactor from SavedPlanAssumptions        Alex Rezmerski    
-- 2011-Sep-09      45          Added ISNULL = 1 for SalesAdjFactor                                    Craig Wright    
-- 2014-Feb-27      46          SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals           Mike Deren
-- 2017-Sep-13      47          Updated parameter values for fnGetMemberMonthsAndAllowedByDuals        Chris Fleming
-- 2019-Jun-28      48          Replaced savedplanheader with savedforecastsetup                       Satyam Singhal
-- 2019-Jun-28      49          Replaced IsPartBOnly with 1 as savedforecastsetup doesnt have it       Pooja Dahiya
-- 2019-Oct-10      50          Removing IsPartBOnly cases because isPartBOnly is always 0 inSPH       Brent Osantowski
-- 2019-Nov-07      51          Replace userid char(13) to char(7)                                     Chhavi Sinha
-- 2019-Nov-07      52          Replaced savedforecastsetup with savedplanheader                       Chhavi Sinha
-- 2020-Jun-18      53          Backend Alignment and Restructuring                                    Keith Galloway
-- 2020-Sep-08      54          Fixed sonar Qube Fixes                                                 Deepali
-- 2020-Dec-14      55          Increasing scale to align BPT risk scores with MRA                     Brent Osantowski
-- 2022-Sep-23		56			@XVariables, WITH (NOLOCK)											   Phani Adduri
-- 2022-Nov-29		57			Add #CalcBenchmarkSummary temp table, release temp table memory		   Phani Adduri
-- 2024-Jul-02		58		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table  Surya Murthy
-- ----------------------------------------------------------------------------------------------------------------------    
CREATE PROCEDURE [dbo].[spCalcBenchmarkSummary]    
(    
    @ForecastID INT,     
    @UserID CHAR(7)    
)  AS    

BEGIN    

    SET NOCOUNT ON    

    DECLARE @XForecastID INT = @ForecastID,
    @XUserID CHAR(7) = @UserID, 
	@LastDateTime DATETIME,    
    @PlanAverageRiskRate DECIMAL(16, 12),    
    @PartBRiskPayment DECIMAL (7, 6),    
    @COBPMPM DECIMAL (7, 4),      
    @TotalMembership DECIMAL (28,15)    

    SET @LastDateTime = GETDATE()    

--Get total membership    
    SELECT @TotalMembership = SUM(MM.ProjectedMemberMonths)    
    FROM dbo.fnPlanCountyProjectedMemberMonths(@XForecastID, 2) MM    
    INNER JOIN dbo.SavedPlanStateCountyDetail SCD WITH (NOLOCK) -- to exclude the excluded counties    
        ON SCD.StateTerritoryID = MM.StateTerritoryID    
        AND SCD.CountyCode = MM.CountyCode    
        AND SCD.ForecastID = @XForecastID    
    WHERE SCD.IsCountyExcludedFromBPTOutput = 0    

--Get Coordination of Benefits PMPM    
    SELECT @COBPMPM = COBPMPM    
    FROM dbo.PerIntMAAssumptions WITH (NOLOCK) 

    IF ( SELECT OBJECT_ID('tempdb..#fnGetRatebook')        
       ) IS NOT NULL         
        DROP TABLE #fnGetRatebook   

  SELECT * INTO  #fnGetRatebook  FROM dbo.fnGetRatebook(@XForecastID)  

  IF ( SELECT OBJECT_ID('tempdb..#MembershipTotals')        
       ) IS NOT NULL         
        DROP TABLE #MembershipTotals   

    --INSERT INTO @MembershipTotals    
    SELECT    
        @XForecastID AS ForecastID,
        MM.StateTerritoryID,    
        MM.CountyCode,    
        MM.DemogIndicator,    
        dem.DualEligibleTypeID,    
        SUM(MM.MemberMonths)    AS ProjectedMembership  
 INTO #MembershipTotals  
    FROM dbo.SavedPlanMemberMonthDetail MM WITH (NOLOCK)    
    INNER JOIN dbo.SavedPlanStateCountyDetail SC WITH (NOLOCK)
        ON SC.ForecastID = MM.ForecastID    
        AND SC.StateTErritoryID = MM.StateTerritoryID    
        AND SC.CountyCode = MM.CountyCode    
    INNER JOIN dbo.LkpIntDemogIndicators dem WITH (NOLOCK)    
        ON MM.DemogIndicator = dem.DemogIndicator    
    WHERE MM.ForecastID = @XForecastID    
        AND dem.IsBiddable = 1 --biddable    
        AND MM.MemberMonths > 0     
        AND MM.MemberMonths IS NOT NULL    
        AND SC.IsCountyExcludedFromBPTOutput = 0    
    GROUP BY     
        MM.StateTerritoryID,     
        MM.CountyCode,    
        MM.DemogIndicator,    
        dem.DualEligibleTypeID,    
        MM.MemberMonths    

--Grab the Average Risk Rate: WS5, Section V, cell H2: This value is also used in the ISAR Scale calculation    
	SELECT     
	        @PlanAverageRiskRate =      
						SUM(dbo.fnSignificantDigits(RB.CMSRiskRate, 12) * (MM.ProjectedMembership))    
						/    
						dbo.fnIsZero(@TotalMembership,1)       
	FROM #MembershipTotals MM    
	INNER JOIN #fnGetRatebook RB    
	  ON RB.StateTerritoryID = MM.StateTerritoryID    
	  AND RB.CountyCode = MM.CountyCode    
	INNER JOIN dbo.SavedPlanStateCountyDetail SCD WITH (NOLOCK) -- to exclude the excluded counties    
	  ON SCD.StateTerritoryID = MM.StateTerritoryID    
	  AND SCD.CountyCode = MM.CountyCode    
	  AND SCD.ForecastID = MM.ForecastID    
	WHERE SCD.IsCountyExcludedFromBPTOutput = 0    
	  AND SCD.ForecastID = @XForecastID
	GROUP BY SCD.ForecastID

--This is the same calculation the bid form uses, by taking the ratio of part B to total using the first county listed    
    SET @PartBRiskPayment =     
        (SELECT TOP 1 ROUND((RB.CMSPartBOnlyRiskRate / dbo.fnIsZero(RB.CMSRiskRate,1)) ,5)    
        FROM dbo.SavedPlanStateCountyDetail SCD WITH (NOLOCK)    
        INNER JOIN #fnGetRatebook RB    
            ON SCD.StateTerritoryID = RB.StateTerritoryID    
            AND SCD.CountyCode = RB.CountyCode    
            AND SCD.ForecastID = @XForecastID    
        WHERE    
            SCD.IsCountyExcludedFromBPTOutput = 0)    

--Clear out the old data from the table    
    DELETE FROM dbo.CalcBenchmarkSummary     
    WHERE ForecastID = @XForecastID    

    BEGIN 
		IF (SELECT OBJECT_ID('tempdb..#CalcBenchmarkSummary')) IS NOT NULL         
        DROP TABLE #CalcBenchmarkSummary   

        SELECT     
            t.Planyear AS PlanYearID,    
            t.ForecastID,    
            t.PlanProjectedMembership,    
            t.PlanProjectedNonDualMembership,    
            t.PlanProjectedDualMembership,    
            t.MemberMonthsExperience,    
            t.PlanRiskFactor,    
            t.RPPOPlanISARFactor,    
            t.PlanAverageRiskRate,    
            ChangeInCOBPMPM =     
                t.PlanAverageRiskRate * (t.SecondaryPayerAdjustment) * (t.PlanAverageAdjustedRiskRate / dbo.fnIsZero(t.PlanAverageRiskRate,1)) - @COBPMPM,    
            t.PlanAverageAdjustedRiskRate,    
            t.ISARScale,    
            t.ISARBid,    
            t.PartARiskPayment,    
            t.PartBRiskPayment,    
            t.PlanIPCostShare,    
            t.PlanSNFCostShare,    
            t.PlanOPCostShare,    
            t.PlanOtherCostShare,    
            t.PlanPartAEquivCostShare,    
            t.PlanPartBEquivCostShare,    
            t.UserID,    
            t.LastDateTime
		INTO #CalcBenchmarkSummary 
        FROM    
            (SELECT    
                Planinfo.PlanYear,
                SFS.ForecastID AS ForecastID,    
                @TotalMembership AS PlanProjectedMembership, -- Membership appears on row E    
                PlanProjectedNonDualMembership = SUM(CASE WHEN MM.DualEligibleTypeID = 0 THEN MM.ProjectedMembership ELSE 0 END),    
                PlanProjectedDualMembership = SUM(CASE WHEN MM.DualEligibleTypeID = 1 THEN MM.ProjectedMembership ELSE 0 END),    
                MemberMonthsExperience = --Experience does not appear on WS 5    
                    MAX(EMM.NonDualBiddableMemberMonths + EMM.DualBiddableMemberMonths),     
                SecondaryPayerAdjustment = MAX(PA.SecondaryPayerAdjustment),    
                PlanRiskFactor = --Membership & Risk factor weighted ratebook, row F    
                            dbo.fnSignificantDigits(    
                                    CAST(SUM(RF.RiskFactor * MM.ProjectedMembership) AS DECIMAL(30,15))    
                                    /    
                                    dbo.fnIsZero(CAST(@TotalMembership AS DECIMAL(30,15)),1)    
                            ,15),    
                RPPOPlanISARFactor = -- RPPO Plan provided ISARs, row G    
                    CASE WHEN PlanInfo.PlanTypeID = 3 AND PD.IsUseISARFactor = 1    
                        THEN -- If RPPO & ISAR Factors are being used    
                            SUM(SPI.ISARFactor * (MM.ProjectedMembership ))    
                            /    
                            dbo.fnIsZero(SUM (MM.ProjectedMembership),1)    
                        ELSE -- Plan ISARs are not applicable to all other plan types    
                            NULL    
                    END,    
                PlanAverageRiskRate = @PlanAverageRiskRate, -- Membership-weighted Ratebook value, row H     
                PlanAverageAdjustedRiskRate = dbo.fnSignificantDigits( -- Membership and Risk Factor weighted Ratebook value, row I    
                        CAST(SUM(RB.CMSRiskRate * RF.RiskFactor * (MM.ProjectedMembership)) AS DECIMAL(30,15))    
                        /    
                        dbo.fnIsZero(CAST(@TotalMembership AS DECIMAL(30,15)),1)    
                    ,15),    
            --    ISARScale = -- Membership-weighted (County Rate / Plan Rate) value, row J    
            --        CASE WHEN PlanInfo.PlanTypeID = 3 --RPPO is handled separately for Plan-Level ISARs    
            --            THEN    
            --           --     CASE WHEN 1 = 1  --[Pooja] : To be replaced with  IsPartBOnly   
            --           --         THEN     
            --           --             SUM((dbo.fnSignificantDigits(RB.CMSPartBOnlyRiskRate, 12) / @PlanAverageRiskRate)* (MM.ProjectedMembership))    
            --           --             /     
            --           --dbo.fnIsZero(@TotalMembership,1)    
            --           --         ELSE    
            --                        SUM((dbo.fnSignificantDigits(RB.CMSRiskRate, 12) / @PlanAverageRiskRate)* (MM.ProjectedMembership))    
            --                        /     
            --                        dbo.fnIsZero(@TotalMembership,1)    
            --                --END    
            --            ELSE     
            --         --       CASE WHEN 1 = 1     --[Pooja] : To be replaced with  IsPartBOnly  
            --         --           THEN     
            --         --               SUM((dbo.fnSignificantDigits(RB.CMSPartBOnlyRiskRate, 12) / @PlanAverageRiskRate)* (MM.ProjectedMembership))    
            --         --/     
            --         --               dbo.fnIsZero(@TotalMembership,1)    
            --         --           ELSE    
            --                        SUM((dbo.fnSignificantDigits(RB.CMSRiskRate, 12) / @PlanAverageRiskRate) * (MM.ProjectedMembership))    
            --                        /     
            --dbo.fnIsZero(@TotalMembership,1)    
            --             --   END                
            --        END,    
				ISARScale= SUM((dbo.fnSignificantDigits(RB.CMSRiskRate, 12) / @PlanAverageRiskRate) * (MM.ProjectedMembership))   
                                  /     
            dbo.fnIsZero(@TotalMembership,1)  ,

                ISARBid = NULL, -- not yet completed    
                PartARiskPayment = 1 - @PartBRiskPayment,    
                PartBRiskPayment = @PartBRiskPayment,    
                PlanIPCostShare = dbo.fnSignificantDigits(    
                        CAST(SUM(RB.CMSIPCostShare * RB.CMSIPFFSCosts * (MM.ProjectedMembership)) AS DECIMAL(30, 15))    
                        /    
                        dbo.fnIsZero(CAST(SUM(RB.CMSIPFFSCosts * (MM.ProjectedMembership ) ) AS DECIMAL(30, 15)),1)    
                    ,15),     
                PlanSNFCostShare = dbo.fnSignificantDigits(    
                        CAST(SUM(RB.CMSSNFCostShare * RB.CMSSNFCosts * (MM.ProjectedMembership)) AS DECIMAL(30, 15))    
                        /    
                        dbo.fnIsZero(CAST(SUM(RB.CMSSNFCosts * (MM.ProjectedMembership)) AS DECIMAL(30, 15)),1)    
                    ,15),     
                PlanOPCostShare = dbo.fnSignificantDigits(    
                        CAST(SUM(RB.CMSOPCostShare * RB.CMSOPCosts * (MM.ProjectedMembership)) AS DECIMAL(30, 15))    
                        /    
                        dbo.fnIsZero(CAST(SUM(RB.CMSOPCosts * (MM.ProjectedMembership)) AS DECIMAL(30, 15)),1)    
                    , 15),     
                PlanOtherCostShare = dbo.fnSignificantDigits(    
                        CAST(SUM(RB.CMSOtherCostShare * RB.CMSOtherCosts * (MM.ProjectedMembership)) AS DECIMAL(30, 15))    
                        /    
                        dbo.fnIsZero(CAST(SUM(RB.CMSOtherCosts * (MM.ProjectedMembership)) AS DECIMAL(30, 15)),1)    
                    , 15),     
                PlanPartAEquivCostShare = dbo.fnSignificantDigits(    
                        CAST(SUM(RB.CMSPartAEquivCostShare * (MM.ProjectedMembership)) AS DECIMAL(30,15))    
                        /    
                        dbo.fnIsZero(CAST(@TotalMembership AS DECIMAL(30,15)),1)    
                    ,15),     
                PlanPartBEquivCostShare = dbo.fnSignificantDigits(    
                        CAST(SUM(RB.CMSPartBEquivCostShare * (MM.ProjectedMembership)) AS DECIMAL(30,15))    
                        /    
                        dbo.fnIsZero(CAST(@TotalMembership AS DECIMAL(30,15)),1)    
                    ,15),     
                UserID = @XUserID,    
                LastDateTime = @LastDateTime    
            FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)    
            INNER JOIN dbo.SavedPlanDetail PD WITH (NOLOCK)   
                ON PD.ForecastID = SFS.ForecastID 
			INNER JOIN dbo.SavedPlanInfo PlanInfo WITH (NOLOCK)
			    ON SFS.PlanInfoID = PlanInfo.PlanInfoID		  
            INNER JOIN dbo.SavedPlanAssumptions PA WITH (NOLOCK)    
                ON SFS.ForecastID = PA.ForecastID    
            INNER JOIN #MembershipTotals MM    
                ON MM.ForecastID = SFS.ForecastID    
            LEFT JOIN dbo.fnGetMemberMonthsAndAllowedByDuals(@XForecastID, 1) EMM     
                ON SFS.ForecastID = EMM.ForecastID
            INNER JOIN dbo.SavedPlanStateCountyDetail SCD WITH (NOLOCK)-- to exclude the excluded counties    
                ON SCD.StateTerritoryID = MM.StateTerritoryID    
                AND SCD.CountyCode = MM.CountyCode    
                AND SCD.ForecastID = @XForecastID    
            INNER JOIN #fnGetRatebook RB    
                ON RB.StateTerritoryID = SCD.StateTerritoryID     
                AND RB.CountyCode = SCD.CountyCode    
            INNER JOIN dbo.SavedPlanRiskFactorDetail RF WITH (NOLOCK)    
                ON RF.ForecastID = SFS.ForecastID    
                AND RF.StateTerritoryID = SCD.StateTerritoryID     
                AND RF.CountyCode = SCD.CountyCode    
                AND RF.DemogIndicator = MM.DemogIndicator    
            INNER JOIN dbo.LkpIntDemogIndicators DI WITH (NOLOCK)    
                ON RF.DemogIndicator = DI.DemogIndicator    
                AND MM.DemogIndicator = DI.DemogIndicator    
            LEFT JOIN dbo.SavedPlanISARFactorDetail SPI WITH (NOLOCK) --Table is only needed for RPPO ISAR Factors    
                ON SPI.ForecastID = SFS.ForecastID    
                AND SPI.StateTerritoryID = RF.StateTerritoryID    
                AND SPI.CountyCode = RF.CountyCode    
            WHERE DI.IsBiddable = 1    
                AND DI.DualEligibleTypeID <> 2    
                AND RF.IsExperience = 0 --Projected   
                AND SFS.ForecastID = @XForecastID    
                AND PD.MARatingOptionID = 1  -- Will always be 1 (Experience) since this goes directly to the ratebook.    
             AND SCD.IsCountyExcludedFromBPTOutput = 0    
            GROUP BY     
                PlanInfo.PlanYear,    
                SFS.ForecastID,   
                PlanInfo.PlanTypeID,   
                PD.IsUseISARFactor    
            ) t


		INSERT INTO dbo.CalcBenchmarkSummary (PlanYearID, ForecastID, PlanProjectedMembership, PlanProjectedNonDualMembership,
		PlanProjectedDualMembership, PlanExperienceMembership, PlanRiskFactor, RPPOPlanISARFactor, PlanAverageRiskRate,
		ChangeInCOBPMPM, PlanAverageAdjustedRiskRate, ISARScale, ISARBid, PartARiskPayment, PartBRiskPayment,
		PlanIPCostShare, PlanSNFCostShare, PlanOPCostShare, PlanOtherCostShare, PlanPartAEquivCostShare,    
		PlanPartBEquivCostShare, LastUpdateByID, LastUpdateDateTime)
		SELECT 	PlanYearID, ForecastID, PlanProjectedMembership, PlanProjectedNonDualMembership, PlanProjectedDualMembership,
				MemberMonthsExperience, PlanRiskFactor, RPPOPlanISARFactor, PlanAverageRiskRate, ChangeInCOBPMPM,
				PlanAverageAdjustedRiskRate, ISARScale, ISARBid, PartARiskPayment, PartBRiskPayment,
				PlanIPCostShare, PlanSNFCostShare, PlanOPCostShare, PlanOtherCostShare, PlanPartAEquivCostShare,    
				PlanPartBEquivCostShare, UserID, LastDateTime
		FROM #CalcBenchmarkSummary

		IF (SELECT OBJECT_ID('tempdb..#CalcBenchmarkSummary')) IS NOT NULL         
        DROP TABLE #CalcBenchmarkSummary 
    END    
END
GO
