SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- --------------------------------------------------------------------------------------------------------------------      
-- PROCEDURE NAME: spPreRepriceValidation      
--      
-- AUTHOR: <PERSON>      
--      
-- CREATED DATE: 2010-Oct-05      
--      
-- DESCRIPTION: This procedure is responsible for validating that the data necessary for reprice is      
--                  actually properly available.  Validations include:      
--                      -All Benefit Categories in Trend Factor Data      
--                      -All Claim Factor Types in Claim Factor Data      
--                      -All Benefit Categories in Claim Factor Data      
--                      -All Benefit Categories in IN/OON Distribution Data      
--                      -Projected Membership is properly loaded      
--                      -Risk scores are loaded for all included counties      
--                      -Medicare Secondary Payer Adj is loaded and has reasonable value (<= 8%)      
--                      -Admin & Marketing values are loaded and sum to 1      
--                      -Trend values don't exceed 40%      
--                      -Ratebook and star ratings are assigned      
--                      -Combined Units/Allowed are mbr wghted avg of DE and non-DE components      
-- 
-- PARAMETERS:      
--	Input: None      
--      
-- RETURNS: @Result      
--      
-- TABLES:       
--	Read:      
--      
--	Write: None      
--      
-- VIEWS:      
--	Read: NONE      
--         
-- STORED PROCS:       
--	Executed: NONE      
--      
-- $HISTORY       
-- ---------------------------------------------------------------------------------------------------------------------      
-- DATE           VERSION  CHANGES MADE                                                             DEVELOPER      
-- ---------------------------------------------------------------------------------------------------------------------      
-- 2010-Oct-05    1        Initial Version                                                          Michael Siekerka      
-- 2010-Oct-08    2        Fixed error output message(s)                                            Michael Siekerka      
-- 2010-Oct-18    3        Added validation for IsFinal and IsRunable                               Michael Siekerka      
-- 2010-Nov-09    4        Removed validation for Membership > 0 (can be 0 now)                     Michael Siekerka      
-- 2010-Dec-07    5        Added check for risk scores                                              Michael Siekerka      
-- 2010-Dec-15    7        Added checks for SecondaryPayerAdj and Admin & Marketing %'s             Michael Siekerka      
-- 2010-Dec-27    8        Changed precision on Admin/Marketing check                               Michael Siekerka      
-- 2011-Feb-03    9        Updated to account for benefit level claim factors                       Michael Siekerka      
-- 2011-Feb-08    10       Changed call to SavedPlanAssumptions to account for expense              Joe Casey      
--                           change and added call for new expense location.      
-- 2011-Feb-09    11       Added check for trend values                                             Michael Siekerka      
-- 2011-Feb-10    12       Re-added check for reasonable secondary payer adj                        Michael Siekerka      
-- 2011-Feb-16    13       Added check for HCR stars, ratebook and rebate % and DE weighting        Michael Siekerka      
-- 2011-Mar-10    14       Changed Secondary Payer Adj percent to 8 from 2                          Michael Siekerka      
-- 2011-Mar-31    15       Changed Secondary Payer Adj percent to 50% from 8%                       Casey Sanders      
-- 2011-Mar-31    16       Changed rounding of MbrWeightedAvg comparison to 1 decimal from 5        Michael Siekera      
-- 2011-Apr-18    17       Added check for IN/OON BenefitTypeID (required)                          Michael Siekerka      
-- 2011-Apr-22    18       Altered benefit check to return bnft cat name, rather than ID            Casey Sanders      
-- 2012-Feb-06    19       Altered claim factor check to look at SavedClaimFactorDetail             Trevor Mahoney    
--                           as opposed to fnGetClaimFactors    
-- 2012-Nov-01    20       Removed claim factor validations for new trend upload                    Mike Deren    
-- 2013-Feb-14    21       Included Cost and Use, Removed Trend, Revised Claim Factors              Mike Deren    
-- 2013-Feb-14    22       Removed validation for trend greater than 40%                            Lindsay Allen    
-- 2013-Feb-14    23       Added manual credibility standard for Cost and Use check                 Trevor Mahoney    
-- 2013-Feb-25    24       MACTAPT values out of Order                                              Mike Deren    
-- 2013-Mar-19    25       Added in safe division (mm), change credibilty so its selected once      Mike Deren    
-- 2013-Oct-07    26       Included Join on Segment ID                                              Manisha Tyagi  
-- 2014-Jan-09    27       Changing Table Name                                                      Mike Deren 
-- 2014-Jan-14    28       Updating for new trend                                                   Mike Deren
-- 2014-Feb-27    29       SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals             Mike Deren
-- 2017-Sep-13    30       Updated parameters for fnGetMemberMonthsAndAllowedByDuals                Chris Fleming
-- 2018-May-10    31       Changed LkpExtCMSPlanType to LkpProductType                              Jordan Purdue
-- 2018-Oct-04    32       Remove AdminExpID field                                                  Manisha Tyagi
-- 2019-Jun-28    33       Replace SavedPlanHeader with SavedForecastSetup                          Pooja Dahiya
-- 2019-Aug-06    34       Corrected ON conditions for contract plan and segment columns            Pooja Dahiya
-- 2019-Aug-22    35       Fixed loaded benefits check to use ProductID instead of PlanTypeID       Brent Osantowski
-- 2019-Oct-25    36       Removed Humad from UserID                                                Chhavi Sinha
-- 2019-Nov-06    37       Replace SavedForecastSetup with SavedPlanHeader                          Chhavi sinha	
-- 2019-Dec-16    38       Changing isLocked Reference on SavedPlanHeader to isFinal                Brent Osantowski
-- 2020-Jun-11    39       Backend Alignment and Restructuring                                      Keith Galloway
-- 2020-Sep-08    40       Fixed sonar Qube Fixes                                                   Deepali             
-- 2022-Feb-24    41	   Changing dual weighting test logic										Michael Manes           
-- 2022-Jun-24	  42       Adding a temp table #SavedPlanBenefitDetail that modifies 
--						   dbo.SavedPlanBenefitDetail to remove NULLs for OONBenefitTypeID
--						   where INBenefitTypeID = 5 but OONBenefitTypeID != 5						Aleksandar Dimitrijevic
-- 2022-Sep 29    43   	   Added WITH (NOLOCK)	    												Khurram Minhas
--						   Added inner variables using @X for input parameters
--                         Drop temp tables   
-- 2023-Apr-27    44       Updated Check to make sure IN/OON BenefitTypeID isn't NULL to AND logic  Adam Gilbert
-- 2023-NOV-22	  45	   Removed check to validate that user can reprice a plan as part of		Alex Brandt
--								story #5326276 since that validation is now being done in
--								MAAUI itself
-- ---------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [dbo].[spPreRepriceValidation]  
    (  
      @ForecastID INT ,  
      @UserID VARCHAR(7) ,  
      @Output VARCHAR(MAX) OUTPUT      
    )  
AS   

 SET NOCOUNT ON;
		
    DECLARE @XForecastID INT = @ForecastID;
	DECLARE @XUserID VARCHAR(7) = @UserID; 
	
--New section for version 42
--------------------------------------------
SELECT BenefitCategoryID,
	   OONBenefitTypeID 
INTO #SBD_OONTieredBens
FROM dbo.SavedPlanBenefitDetail WITH (NOLOCK)
WHERE INBenefitTypeID = 5
  AND ForecastID = @XForecastID
  AND IsLiveIndex = 1
  AND IsBenefitYearCurrentYear = 0
  AND OONBenefitTypeID != 0

SELECT ForecastID,
	   IsBenefitYearCurrentYear,
	   IsLiveIndex,
	   sbd.BenefitCategoryID,
	   INBenefitTypeID,
	   CASE WHEN sbd.OONBenefitTypeID IS NULL
			THEN otb.OONBenefitTypeID
			ELSE sbd.OONBenefitTypeID
	   END AS OONBenefitTypeID
INTO #SavedPlanBenefitDetail
FROM dbo.SavedPlanBenefitDetail sbd WITH (NOLOCK)
LEFT JOIN #SBD_OONTieredBens otb
	ON otb.BenefitCategoryID = sbd.BenefitCategoryID
WHERE ForecastID = @XForecastID
  AND IsLiveIndex = 1
  AND IsBenefitYearCurrentYear = 0

     IF
    (
        SELECT OBJECT_ID('tempdb..#SBD_OONTieredBens')
    ) IS NOT NULL
	BEGIN
        DROP TABLE #SBD_OONTieredBens;
		END

-----------------------------------------------------------------

    DECLARE @MissingData VARCHAR(MAX) , 
        @Credibility DECIMAL(9, 8)      
    SET @Output = ''      
    SELECT @Credibility = dbo.fnGetCredibilityFactor(@XForecastID)      

--Get any claim factor types that are missing from MACTAPT Upload Removed For Reprice Mike     

    IF @Credibility <> 0   
        BEGIN     
            IF ( SELECT spd.ClaimForecastID  
                 FROM   dbo.SavedplanDetail  spd WITH (NOLOCK)
                 WHERE  spd.ForecastID = @XForecastID  
                        AND MARatingOptionID = 1  
               ) = NULL   
                SET @Output = @Output  
                    + ' Please select Experience MACTAPT Factors. '     
    --RETURN    
        END    
    
    IF @Credibility <> 1   
        BEGIN     
            IF ( SELECT spd.ClaimForecastID  
                 FROM   dbo.SavedplanDetail  spd WITH (NOLOCK)
                 WHERE  spd.ForecastID = @XForecastID  
                        AND MARatingOptionID = 2  
               ) = NULL   
                SET @Output = @Output  
                    + ' Please select Manual MACTAPT Factors. '     
    --RETURN    
        END    
    
    SELECT  @MissingData = COALESCE(@MissingData, '') + bc.BenefitCategoryName + ', '  
    FROM    dbo.LkpIntBenefitCategory bc  WITH (NOLOCK)
    WHERE   BC.BenefitCategoryID NOT IN (  
                                        SELECT DISTINCT  
                                          M.BenefitCategoryNumber    
                                        FROM    dbo.SavedForecastSetup SFS WITH (NOLOCK)
                                        LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
                                          ON SPI.PlanInfoID = SFS.PlanInfoID
                                        INNER JOIN dbo.SavedMATrendData M WITH (NOLOCK)
                                          ON M.ContractPBP = SPI.CPS
                                        WHERE SFS.ForecastID =  @XForecastID
                                          AND M.BenefitCategoryNumber is not null
										)  
            AND ISUsed = 1  
			AND bc.BenefitCategoryID < 1000 -- We assigned categories 1000+ to MSBs, RPs and other non-A/B benefits 
    ORDER BY BenefitCategoryID      
    
    IF @MissingData IS NOT NULL --This will be NULL if there are no missing claim factor types      
        BEGIN      
            SET @Output = @Output + CHAR(10)  
                + 'MACTAPT values are missing the following: '  
                + LEFT(@MissingData, LEN(@MissingData) - 1)      
            SET @MissingData = NULL      
        END      

--Get any benefit categories that are missing from IN/OON Dist table      
    SELECT  @MissingData = COALESCE(@MissingData, '')  
            + CAST(BC.BenefitCategoryID AS VARCHAR(3)) + ', '  
    FROM    dbo.LkpIntBenefitCategory  BC WITH (NOLOCK)
    WHERE   IsEnabled = 1  
            AND IsUsed = 1  
            AND BC.BenefitCategoryID NOT IN (  
            SELECT  BenefitCategoryID  
            FROM    dbo.SavedPlanINOONDistributionDetail  WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID 
			and BenefitCategoryID is not NULL
            GROUP BY BenefitCategoryID )  
			AND bc.BenefitCategoryID < 1000
    ORDER BY BenefitCategoryID      
    
    IF @MissingData IS NOT NULL --This will be NULL if there are no missing Ben Cats      
        BEGIN      
            SET @Output = @Output + CHAR(10)  
                + 'Missing Benefit Categories in IN/OON Distribution Data: '  
                + LEFT(@MissingData, LEN(@MissingData) - 1)      
            SET @MissingData = NULL      
        END      
    
--Get any enabled counties that have NULL projected membership      
    SELECT  @MissingData = COALESCE(@MissingData, '') + Code + ', '  
    FROM    ( SELECT DISTINCT  
                        CAST(StateTerritoryID AS VARCHAR(2))  
                        + CountyCode AS Code  
              FROM      dbo.SavedPlanStateCountyDetail  WITH (NOLOCK)
              WHERE     CAST(StateTerritoryID AS VARCHAR(2))  
                        + CountyCode  NOT IN (  
                        SELECT DISTINCT  
                                CAST(StateTerritoryID AS VARCHAR(2))  
                                + CountyCode AS Code  
                        FROM    dbo.SavedPlanMemberMonthDetail  WITH (NOLOCK)
                        WHERE   ForecastID = @XForecastID  
                                AND ISNULL(MemberMonths, -1) >= 0 )  
                        AND ForecastID = @XForecastID  
                        AND IsCountyExcludedFromBPTOutput = 0  
            ) a  
    ORDER BY Code      
    
    IF @MissingData IS NOT NULL --This will be NULL if there is no missing membership data      
        BEGIN      
            SET @Output = @Output + CHAR(10)  
                + 'The following state-county codes have null membership: '  
                + LEFT(@MissingData, LEN(@MissingData) - 1)      
            SET @MissingData = NULL      
        END      
    
--Get enabled counties that don't have risk scores uploaded (assume if one risk score is loaded for the plan, they all are)      
    SELECT  @MissingData = COALESCE(@MissingData, '') + Code + ', '  
    FROM    ( SELECT DISTINCT  
                        CAST(StateTerritoryID AS VARCHAR(2)) + CountyCode AS Code  
              FROM      dbo.SavedPlanStateCountyDetail SC  WITH (NOLOCK)
              WHERE     CAST(StateTerritoryID AS VARCHAR(2)) 
						+ CountyCode  NOT IN (  
											SELECT DISTINCT  
													CAST(StateTerritoryID AS VARCHAR(2)) 
													+ CountyCode  AS Code  
											FROM    dbo.SavedPlanRiskFactorDetail  WITH (NOLOCK)
											WHERE   ForecastID = @XForecastID )  
											AND ForecastID = @XForecastID  
											AND IsCountyExcludedFromBPTOutput = 0  
            ) a  
    ORDER BY Code      
    
    IF @MissingData IS NOT NULL --This will be NULL if there are no missing risk scores      
        BEGIN      
            SET @Output = @Output + CHAR(10)  
                + 'The following state-county codes have missing risk scores: '  
                + LEFT(@MissingData, LEN(@MissingData) - 1)      
            SET @MissingData = NULL      
        END      
    
--Check for SecondaryPayerAdj, if it's there, is it reasonable.
/*
TODO: The limit in spPreRepriceValidation is actually set to 0.5 (50%). Anything higher than that will cause a reprice to fail.
After researching with Jennifer Chapman, we found that the maximum MSP has been increasing every year since 2018, but it is still
quite a bit lower than 0.5 as of 2023, so we decided not to change the limit for now. In the future though, we should add in a 
lookup table that will store this limit in the event that it needs to be quickly changed via a utility request. The limit (10) used
in the UI in getBenchmarkMsgTitle() of Benchmark.js in MAAUI is a catch-all limit that was migrated from the Bid Model application,
and it isn't representative of a reasonable value.
*/
    DECLARE @SecondaryPayerAdj FLOAT      
    SELECT  @SecondaryPayerAdj = ISNULL(SecondaryPayerAdjustment, 2.1)  
    FROM    dbo.SavedPlanAssumptions  WITH (NOLOCK)
    WHERE   ForecastID = @XForecastID      
    IF @SecondaryPayerAdj = 2.1   
        SET @Output = @Output + CHAR(10)  
            + 'Missing Secondary Payer Adjustment.'      
    IF @SecondaryPayerAdj > 0.5  
        OR @SecondaryPayerAdj < 0   
        SET @Output = @Output + CHAR(10)  
            + 'Secondary Payer Adjustment value of '  
            + CAST(@SecondaryPayerAdj AS VARCHAR(MAX))  
            + ' does not seem reasonable.'      
    
        

--Check to make sure ratebook and stars are assigned      
    DECLARE @RebatePercent FLOAT      
    SELECT  @RebatePercent = dbo.fnGetRebatePercent(@XForecastID)      
    IF NOT EXISTS ( SELECT 1
					FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)
					LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
					  ON SPI.PlanInfoID = SFS.PlanInfoID
					INNER JOIN dbo.SavedPlanDetail SPD WITH (NOLOCK)
						ON SFS.ForecastID = SPD.ForecastID				
					INNER JOIN dbo.PerExtCMSRatebookByContract RB WITH (NOLOCK)
						ON RB.RatebookID = SPD.RatebookID
						AND RB.ContractNumber = left(SPI.CPS,5)
					WHERE SFS.ForecastID = @XForecastID
						AND SPD.MARatingOptionID = 1 )   
        SET @Output = @Output + CHAR(10)  
            + 'No ratebook assigned, please contact MAPD Model Team'      
    IF NOT EXISTS ( SELECT 1
					FROM dbo.SavedForecastSetup SFS	WITH (NOLOCK)
					LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
					  ON SFS.PlanInfoID = SPI.PlanInfoID		
					INNER JOIN dbo.PerExtCMSContractQualityStars CQS WITH (NOLOCK)
						ON CQS.ContractNumber = left(SPI.CPS,5)
					WHERE SFS.ForecastID = @XForecastID )   
        SET @Output = @Output + CHAR(10)  
            + 'No quality star rating assigned, please contact MAPD Model Team'      
    IF ISNULL(@RebatePercent, 0) = 0   
        SET @Output = @Output + CHAR(10)  
            + 'Rebate percent not properly calculating, please contact MAPD Model Team'      
    
--Check to make sure profit % exists and it is reasonable      
    DECLARE @ProfitPercent FLOAT      
    SELECT  @ProfitPercent = ISNULL(ProfitPercent, -1)  
    FROM    dbo.SavedPlanAssumptions  WITH (NOLOCK)
    WHERE   ForecastID = @XForecastID      
    IF @ProfitPercent = -1   
        SET @Output = @Output + CHAR(10) + 'No profit percent loaded'      
    
--Make sure combined matches the member weighted avg's for Allowed and Units in CalcPlanExperienceByBenefitCat      
    DECLARE @DoesWeightedAvgMatch BIT      
    SELECT  @DoesWeightedAvgMatch = CASE WHEN ROUND(SUM(CASE WHEN DualEligibleTypeID IN ( 0, 1 )  
                                                             THEN ( ExpUnits + ExpAllowed )  
                                                             ELSE 0  END), 1)  
                                              - ROUND(SUM(CASE WHEN DualEligibleTypeID = 2  
                                                             THEN ( ExpUnits  + ExpAllowed )  
                                                              ELSE 0 END), 1) = 0 THEN 1  
                                         WHEN ROUND(SUM(CASE WHEN DualEligibleTypeID IN ( 0, 1 )  
                                                             THEN ( ExpUnits + ExpAllowed )  
                                                             ELSE 0  END) 
                                              - SUM(CASE WHEN DualEligibleTypeID = 2  
                                                             THEN ( ExpUnits  + ExpAllowed )  
                                                              ELSE 0 END), 1) = 0 THEN 1  
										 ELSE 0  
                                    END  
    FROM    ( SELECT    DualEligibleTypeID ,  
                        ExpUnits = SUM(dbo.fnGetSafeDivisionResult(( bc.INUnits  
                                                              + bc.OONUnits )  
                                                              * ( CASE  
                                                              WHEN DualEligibleTypeID = 0  
                                                              THEN NonDualBiddableMemberMonths  
                                                              WHEN DualEligibleTypeID = 1  
                                                              THEN DualBiddableMemberMonths  
                                                              ELSE DualBiddableMemberMonths  
                                                              + NonDualBiddableMemberMonths  
                                                              END ),  
                                                              ( DualBiddableMemberMonths  
                                                              + NonDualBiddableMemberMonths ))) ,  
                        ExpAllowed = SUM(dbo.fnGetSafeDivisionResult(( bc.INAllowed  
                                                              + bc.OONAllowed )  
                                                              * ( CASE  
                                                              WHEN DualEligibleTypeID = 0  
                                                              THEN NonDualBiddableMemberMonths  
                                                              WHEN DualEligibleTypeID = 1  
                                                              THEN DualBiddableMemberMonths  
                                                              ELSE DualBiddableMemberMonths  
                                                              + NonDualBiddableMemberMonths  
                                                              END ),  
                                                              ( DualBiddableMemberMonths  
                                                              + NonDualBiddableMemberMonths )))  
              FROM      dbo.CalcPlanExperienceByBenefitCat bc  WITH (NOLOCK)
                        CROSS APPLY dbo.fnGetMemberMonthsAndAllowedByDuals(@XForecastID, MARatingOptionID) mm  
              WHERE     bc.ForecastID = @XForecastID  
              GROUP BY  bc.DualEligibleTypeID  
            ) a      
    
    --Individual components of Util and Allowed do not match up with the weighted average.      
    --i.e. member-weighted DE 0 + DE 1 is not the same as DE 2.      
    IF @DoesWeightedAvgMatch = 0   
        SET @Output = @Output + CHAR(10)  
            + 'Discrepancy with member-weighted experience, please contact MAPD Model Team'      
    
--Check to make sure IN/OON BenefitTypeID isn't NULL      
    SELECT  @MissingData = COALESCE(@MissingData, '') + bc.BenefitCategoryName  + ', '  
	FROM    #SavedPlanBenefitDetail sbd 
			INNER JOIN dbo.SavedForecastSetup SFS WITH (NOLOCK)
				ON SFS.ForecastID = sbd.ForecastID
			LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
				ON SFS.PlanInfoID = SPI.PlanInfoID  
			INNER JOIN dbo.LkpIntBenefitCategory bc WITH (NOLOCK)
				ON sbd.BenefitCategoryID = bc.BenefitCategoryID  		 
			INNER JOIN dbo.LkpProductType pt WITH (NOLOCK)
				ON SPI.ProductTypeID = pt.ProductTypeID  
	WHERE   sbd.ForecastID = @XForecastID  
			AND ( 
				  sbd.INBenefitTypeID IS NULL AND sbd.OONBenefitTypeID IS NULL --Both Types are null
			) 
			AND sbd.IsBenefitYearCurrentYear = 0 --Projected year only        

	 IF
    (
        SELECT OBJECT_ID('tempdb..#SavedPlanBenefitDetail')
    ) IS NOT NULL
	BEGIN
        DROP TABLE #SavedPlanBenefitDetail;
		END

    
    IF @MissingData IS NOT NULL --This will be NULL if there are no missing Ben Cats              
    BEGIN      
            SET @Output = @Output  
                + 'No benefits loaded for benefit categories: '  
                + LEFT(@MissingData, LEN(@MissingData) - 1)      
            SET @MissingData = NULL      
        END      
    
--Return results      
    IF @Output = ''   
        SET @Output = 'N/A' --Indicates that there are no issues, return this unless something is missing 
GO
