﻿/****** Object:  StoredProcedure [dbo].[spAppPBPMapLineCode]    Script Date: 8/15/2024 9:48:55 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: 2024-July-17

-- Description:	Stored procedure responsible for getting the data export Data for the PBPLine Code from the [LkpIntBenefitCategory] table.

-- PARAMETERS:  
-- Input:  
--
-- Output:  
--  
-- TABLES:  
-- Read:  
--      [LkpIntBenefitCategory]
-- Write:
--  
-- VIEWS:  
--  
-- FUNCTIONS:
--
-- STORED PROCS:  
--  
-- $HISTORY   
-- ----------------------------------------------------------------------------------------------------------------------  
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER    
-- ----------------------------------------------------------------------------------------------------------------------  
-- 2024-July-17     1           Initial Version                                                     Dheeraj Singh
-- ----------------------------------------------------------------------------------------------------------------------  
-- ========================================================================================================================

CREATE PROCEDURE [dbo].[spAppPBPMapLineCode]

AS

BEGIN

    SELECT [BenefitCategoryID]
          ,[BenefitCategoryName]
          ,[PBPLineCode]
      FROM [dbo].[LkpIntBenefitCategory] where IsEnabled =1;


	  
END
