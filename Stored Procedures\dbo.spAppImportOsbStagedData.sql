SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO
-- =============================================      
-- Author:  <PERSON>
-- Create date: 2025-Feb-26
-- Description: spAppImportOsbStagedData OSB Import using staging table 
--      
--      
-- PARAMETERS:      
-- Input:    @StageId,    
    
-- TABLES:    
-- Read:        ImportDataStaging
--              
--            
-- Write:		SavedPlanOptionalPackageDetail   
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE				VERSION   CHANGES MADE											DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2025-Feb-26		1	     Initial version.										Kiran Kola								 
----------------------------------------------------------------------------------------------------------------------
-- ======================================================================================================================
CREATE PROCEDURE [dbo].[spAppImportOsbStagedData]
(
		@StageId VARCHAR(100)
	
)
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @jsonData VARCHAR(MAX);
	DECLARE @UserId VARCHAR(7) ;
BEGIN TRY

BEGIN TRANSACTION 
	DECLARE @LastUpdate DATETIME
    SET @LastUpdate = GETDATE() 

    SELECT @jsonData = JsonData, @UserId = UserId
    FROM dbo.ImportDataStaging WITH (NOLOCK)
    WHERE StageId = @StageId;
		
    DECLARE @tbl__importData TABLE
    (
        ForecastID INT,
		OSBCode VARCHAR(6),
		OSBName VARCHAR(200),
		PackageIndex INT,
		IsHidden BIT,
		PlanPackageID TINYINT NULL,
		LastUpdateByID CHAR(7),
        LastUpdateDateTime DATETIME
    );

 DECLARE @PlanYearID SMALLINT  
 SET @PlanYearID = dbo.fnGetBidYear() 


INSERT INTO @tbl__importData (
				ForecastID,
				OSBCode,
				OSBName,
				PackageIndex,
				PlanPackageID,
				IsHidden,
				LastUpdateByID,
				LastUpdateDateTime)
SELECT 
    j.ForecastID,
	j.OSBCode,
    j.OSBName,
	pioph.PackageIndex,
	NULL,
	0,
    @UserId,
    @LastUpdate
    
FROM
    OPENJSON(@jsonData, '$.OSBExtract') 
WITH (
	  ForecastID INT, OSBCode VARCHAR(6),OSBName VARCHAR(200)) AS j
	  LEFT JOIN dbo.PerIntOptionalPackageHeader pioph ON j.OSBName = pioph.Name AND j.OSBCode = SUBSTRING(pioph.Description, 1, 6)



DECLARE @PlanPackageIDs TABLE (
    ForecastID INT,
	OSBCode VARCHAR(6),
	PackageIndex INT,
    PlanPackageID TINYINT
);

INSERT INTO @PlanPackageIDs (ForecastID, OSBCode, PackageIndex, PlanPackageID)
SELECT 
    t.ForecastID,
    OSBCode,
    t.PackageIndex,
    CASE 
        WHEN ROW_NUMBER() OVER (PARTITION BY t.ForecastID ORDER BY t.PackageIndex) > 5 THEN 5 
        ELSE ROW_NUMBER() OVER (PARTITION BY t.ForecastID ORDER BY t.PackageIndex)
    END AS PlanPackageID
FROM 
    @tbl__importData t
	LEFT JOIN SavedPlanOptionalPackageDetail spopd ON spopd.ForecastID = t.ForecastID AND spopd.PackageIndex = t.PackageIndex
	WHERE spopd.IsHidden = 0;


UPDATE t
SET t.PlanPackageID = p.PlanPackageID
FROM @tbl__importData t
LEFT JOIN @PlanPackageIDs p ON t.ForecastID = p.ForecastID AND t.OSBCode = p.OSBCode;

	  Delete From dbo.SavedPlanOptionalPackageDetail where ForecastID in (select Distinct ForecastID from @tbl__importData)
	

	MERGE INTO dbo.SavedPlanOptionalPackageDetail AS target
    USING @tbl__importData AS source
    ON (
           target.ForecastID = source.ForecastID AND target.PlanPackageID =source.PackageIndex 
       )
    WHEN MATCHED THEN
        UPDATE SET
				  PackageIndex = source.PackageIndex,  
				  IsHidden = 0,  
                  target.LastUpdateByID = source.LastUpdateByID,
                  target.LastUpdateDateTime = source.LastUpdateDateTime
    WHEN NOT MATCHED BY TARGET THEN
        INSERT
        (
			PlanYearID,
            ForecastID,
			PlanPackageID,  
			PackageIndex,
            IsHidden,  
            LastUpdateByID,
            LastUpdateDateTime
           
        )
        VALUES
        (@PlanYearID,source.ForecastID,PlanPackageID,source.packageindex,0,
				source.LastUpdateByID,
				source.LastUpdateDateTime);

COMMIT
  
	DELETE FROM dbo.ImportDataStaging WHERE StageId = @StageId;
END TRY
BEGIN CATCH    
 IF @@TRANCOUNT > 0
 Begin
                ROLLBACK;
 End

     DECLARE @ErrorMessage NVARCHAR(4000);           
          DECLARE @ErrorSeverity INT;           
          DECLARE @ErrorState INT;           
          DECLARE @ErrorException NVARCHAR(4000);           
          DECLARE @errSrc      VARCHAR(MAX) =ISNULL(ERROR_PROCEDURE(), 'SQL'),           
                  @currentdate DATETIME=GETDATE()           
    
          SELECT @ErrorMessage = ERROR_MESSAGE(),           
                 @ErrorSeverity = ERROR_SEVERITY(),           
                 @ErrorState = ERROR_STATE(),           
                 @ErrorException = 'Line Number :'           
                                   + CAST(ERROR_LINE() AS VARCHAR)           
         + ' .Error Severity :'           
                                   + CAST(@ErrorSeverity AS VARCHAR)           
                                   + ' .Error State :'           
                                   + CAST(@ErrorState AS VARCHAR)           
  
    EXEC dbo.Spappaddlogentry           
            @currentdate,           
            '',           
            'ERROR',           
            @errSrc,           
            @ErrorMessage,           
            @ErrorException,           
            @UserId    
                 
      END CATCH;       


END
GO




