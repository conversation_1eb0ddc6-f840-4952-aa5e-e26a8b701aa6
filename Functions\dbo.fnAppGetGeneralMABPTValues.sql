-- User Defined Function


-- FUNCTION NAME: fnAppGetGeneralMABPTValues    
--    
-- AUTHOR: <PERSON> / <PERSON>    
--    
-- CREATED DATE: 2007-Apr-12    
-- HEADER UPDATED: 2011-Mar-30    
--    
-- DESCRIPTION: function responsible for listing plan header values on the MA-BPT sheets in the MAPD Model    
--    
-- PARAMETERS:    
--  Input:     @ForecastID
--  Output:    
--    
-- TABLES:    
--  Read:    
--      AdminUserHeader    
--      CalcPlanAdminBlend    
--      LkpExtCMSMARegionHeader    
--      LkpProductType    
--      LkpExtCMSPlanTypeValues    
--      LkpExtSNPType    
--      LkpIntAddedBenefitExpenseDetail    
--      LkpIntDemogIndicators    
--      LkpPlanIntention    
--      LkpSNPTypeIDXWalk    
--      LkpVBID    
--      PerExtContractNumberHeader    
--      SavedForecastSetup    
--      SavedPlanAddedBenefits    
--      SavedPlanInfo    
--      SavedMarketInfo    
--      SavedPlanAssumptions    
--      Benefits_SavedBenefitOption   
--      SavedPlanDetail    
--      SavedPlanDFSummary    
--      SavedPlanMemberMonthDetail    
--      SavedPlanOOAMemberMonthDetail    
--      SavedPlanProductPairing    
--      SavedPlanStateCountyDetail    
--      SavedRegionInfo    
--      SavedDFVersionHeader    
--      SavedScenarioVBID    
-- Write:    
--    Benefits_SavedBenefitOption
-- VIEWS:    
--     
-- FUNCTIONS:    
--      fnAppGetMABPTWS1BasePlans    
--      fnAppGetMABidNotes    
--      fnAppGetMABPTWS1Summary    
--      fnAppGetMABPTWS3    
--      fnAppGetMABPTWS4Expenses    
--      fnGetBaseAdminAndRevenueCombined    
--      fnGetBaseOSBExperience    
--      fnGetMARebateAllocation    
--      fnGetMemberMonthsAndAllowedByDuals    
--      fnGetCMSContractQualityStars    
--      fnGetUnspentRebateForEGWPMSB    
--    
-- STORED PROCS:    
--    
-- $HISTORY     
-- ----------------------------------------------------------------------------------------------------------------------    
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER      
-- ----------------------------------------------------------------------------------------------------------------------    
-- 2008-Apr-16      1           Initial Version - Copied from fnAppGetBPTPlanHeader                 Sandy Ellis    
-- 2008-Apr-21      2           Rx Basic/Supp can now come from SavedMAPDOffModelRxDetail           Brian Lake    
-- 2008-Apr-23      3           Added Secondary Contact.                                            Brian Lake    
-- 2008-Apr-24      4           Changed Contact person = Certifying, by Tony's request.             Brian Lake    
-- 2008-Apr-30      5           Changed Contact person = Contact.  see v4 above.                    Brian Lake    
-- 2008-May-01      6           Added EstimatedPlanBidComponent (WS5)                               Brian Lake    
-- 2008-May-01      7           Added MA Region Name                                                Brian Lake    
-- 2008-May-02      8           Changed Secondary title to UserCertifyingTitle in place of contact  Brian Lake    
-- 2008-May-03      9           Updated ContractMMPercent to use Experience MM                      Brian Lake    
-- 2008-May-07      10          Secondary contact name now includes contact title                   Amy Rice    
-- 2008-May-13      11          Fixed typo on Supp buydown                                          Sandy Ellis    
-- 2008-May-16      12          Loading TotalReduceCostShare and TotalOtherSuppBen Fields from      Christian Cofie    
--                                fnAppGetMARebateAllocation/SNP instead of fn WS4and6Totals    
-- 2008-May-19      13          Added join to member month - risk factor table, eliminates double   Brian Lake    
--                                counting    
-- 2009-Feb-25      14          Replaced Org name with new table reference                          Sandy Ellis      
-- 2009-Feb-27      15          Revised Exp Mem Months and Projected ESRD Mem Months for 2010       Brian Lake    
-- 2009-Mar-17      16          Data types                                                          Sandy Ellis    
-- 2009-Mar-23      17          Added SuppPremBuyDown field                                         Lawrence Choi    
-- 2009-Mar-31      18          Added ExperienceESRDMemberMonths & Corrected 2010 formula for       Keith Galloway    
--                                Projected ESRD's    
-- 2009-Apr-02      19          Many changes for 2010, see revision list                            Sandy Ellis    
-- 2009-Apr-06      20          Check for div/0 errors on Experience members                        Brian Lake    
-- 2009-Apr-10      21          Capitalize Part D and Part B text msgs                              Sandy Ellis    
-- 2009-Apr-13      22          Added MARatingOptionID join on spd and benefityear id join on       Sandy Ellis    
--                                MoopDet    
-- 2009-Apr-21      23          PlanName comes fr PerExtContractNumberPlanIDDetail; Maintained      Keith Galloway    
--                                by model team    
-- 2009-Apr-21      24          Changed calc of MOOP impacts for IN and OON                         Sandy Ellis    
-- 2009-Dec-28      25          Added SNPType as a returned variable                                Jake Gaecke    
-- 2010-Jan-12      26          Edited fnGetExperienceMemberMonthsAndAllowedByDuals calls do to     Joe Casey    
--                                Hospice addition    
-- 2010-Feb-01      27          Calculate rebate allocation under both normal and alternate order   Lawrence Choi    
-- 2010-Mar-10      28          Added 2011 statement and grabs values for WS1 Sec 6                 Nick Skeen     
-- 2010-Mar-10      29          Addded the prefix dbo to the function fnAppGetMABPTWS3, this        Sule Dauda    
--                                ensures that there no naming confusion when function is     
--                                called. I also refined the IF statement to include planyear 2011    
-- 2010-Apr-12      30          Added SubsetRevenueESRDAndHospice, NetMedicalExpenses, and          Joe Casey    
--                                SubsetNetMedicalExpenseESRDAndHospice     
-- 2010-May-19      31          Added inner join to SavedPlanStateCountyDetail to exclude excluded  James Wu    
--                                counties from ESRDMemberMonths calculation,     
--                                @ProjectedESRDMemberMonths    
-- 2010-Jun-02      32          For values in WS1 Sec6, changed the calculation to "the original    James Wu    
--                                value from PerExtBaseAdminAndRevenue" / "experience base      
--                                member months" * "total member months from added contracts".    
-- 2010-Oct-05      33          Revised for 2012 database                                           Jake Gaecke    
-- 2010-Oct-06      34          Removed SavedPlanCUSummary and changed PerIntBaseAdminAndRevenue    Joe Casey     
--                                to fnGetBaseAdminAndRevenueCombined    
-- 2010-Oct-06      35          Revised MOOPImpact joins to remove GROUP BY statement and MAX       Jake Gaecke    
--                                function from BidNotes.  Also revised reference to     
--                                fnGetMARebateAllocation.    
-- 2011-Jan-12      36          ESRDMembers and @ProjectedESRDMemberMonths are now Decimal          Jiao Chen    
-- 2011-Jan-17      37          Changed reference from fnAppGetBPTPlanWS1BasePlans to               Michael Siekerka    
--                                fnAppGetMABPTWS1BasePlans    
-- 2011-Jan-17      38          For fnAppGetMABPTWS1BasePlans, changed BaseMemberMonths to          Joe Casey    
--                                MemberMonths    
-- 2011-Jan-19      39          Added Reduced A/B Cost Share to output for WS6                      Michael Siekerka    
-- 2011-Jan-31      40          Edited WS1Sec6 to comply with new WS 1 requirements.                Joe Casey    
-- 2011-Feb-02      41          Added EGWPMSB and UnspentRebate and changed Profit to be -1         Michael Siekerka    
--                                for group plans, it needs to be recalculated in model    
-- 2011-Feb-03      42          Added PartBDeductible to output                                     Michael Siekerka    
-- 2011-Feb-07      43          Changed Profit back to original value for EGWP as it's needed to    Michael Siekerka    
--                                recalculate profit in the model    
-- 2011-Feb-09      44          Added CMS Quality Stars and enrollment type to output               Michael Siekerka    
-- 2011-Feb-14      45          Added code to get the @EstimatedPlanBidComponent                    Michael Siekerka    
-- 2011-Mar-01      46          Changed Part B Deductible placement and population                  Joe Casey    
-- 2011-Mar-29      47          Added @SalesMembershipAdjustment factor to adjust ESRD Membership   Craig Wright    
--                                for Sales Membership    
-- 2011-Mar-30      48          Replaced @SalesMembershipAdjustment factor with                     Craig Wright    
--                                fnSalesMembershipAdjustmentFactor    
-- 2011-Apr-04      49          Added code to display Admin values on WS6                           Michael Siekerka    
-- 2011-Apr-05      50          Added Uncoll Prem and User Fee to Direct Admin Value                Michael Siekerka    
-- 2011-Apr-11      51          Removed Uncoll Prem and User Fee from Direct Admin, Updated         Michael Siekerka    
--                                Admin Name Output          
-- 2011-Apr-18      52          Revised code to always output star values (rather than blanks)      Michael Siekerka    
-- 2011-Apr-19      53          Adjusted OtherNetMed so NetMedPMPM matches TotalMedPMPM             Michael Siekerka    
--                                WS1 F65 = D45.  Adjusted ESRDNetMed so total doesn't change    
-- 2011-Apr-21      54          Revised so Adjustment from 53 is allocated between ESRD and Hosp    Michael Siekerka    
-- 2011-May-04      55          Added SafeDivision to change 54 and 53                              Michael Siekerka    
-- 2011-Aug-25      56          Changed to account for Sales membership returning a table           Craig Wright    
-- 2011-Sep-08      57          Changed to pull SalesAdjustmentFactor from SavedPlanAssumptions     Alex Rezmerski    
-- 2011-Sep-09      58          Changed to account for SalesAdjustmentFactor being null             Alex Rezmerski    
-- 2011-Dec-30      59          Changed EnrolleeType length for 'Part B Only' plan                  Tim Gao    
-- 2012-Jan-17      60          Changed to allow for plan level Completion Factors                  Alex Rezmerski    
-- 2012-Feb-24      61          Made changes to account for new admin buckets Quality and           Alex Rezmerski    
--                                TaxesAndFees    
-- 2012-Feb-28      62          Added new PD admin buckets                                          Alex Rezmerski    
-- 2012-Feb-29      63          Added QualityInitDescrip for Quality initiatives description in WS3 Tim Gao    
-- 2012-Feb-29      64          Changed names of PD admin buckets to match fnAppMABPTCreation       Alex Rezmerski    
-- 2012-Mar-15      65          Change BidNotes1, BidNotes2, BidNotes3, BidNotes4, BidNotes5, &     Mason Roberts    
--                                QualityInitDescrip to VARCHAR(MAX)    
-- 2012-Mar-22      66          Added Projected Hospice and OOA membership                          Trevor Mahoney    
-- 2012-Apr-06      67          Moved the insert from NetCostofPrivateReinsurance after the new     Alex Rezmerski    
--                                admin buckets to correct error on WS1 sec. VI    
-- 2012-May-14      68          Included MSB admin in calculation of Direct Admin                   Trevor Mahoney    
-- 2012-May-15      69          Added spab.Ishidden = 0 in the Where Clause for AddedBenefitAdmin   Craig Wright    
-- 2012-Jun-26      70          Completion Factor is now a blend from the base plans                Mason Roberts    
-- 2012-Aug-16      71          Added code to get UnspentRebate from different source               Tim Gao    
-- 2013-Jan-31      72          Undo the @UnspentRebate and UnspentRebate                           Tim Gao    
-- 2013-Mar-01      73          0379 Added in InsurerFee                                            Mason Roberts     
-- 2013-Apr-10      74          0404 Added WS7 Sec 4 Data from PerIntOSBBasePeriodSummary           Lindsay Allen    
-- 2013-Apr-15      75          Added InsurerfeesAdmin Medicaid revenue and expenses                Tim Gao    
-- 2013-Apr-18      76          Comment out "QualityInitDescrip"                                    Tim Gao    
-- 2013-Apr-24      77          0404 Changed reference of WS7 data from table to fn                 Lindsay Allen    
-- 2013-May-14      78          Changed fnGetBaseOSBExperience to left join                         Lindsay Allen    
-- 2013-Oct-07      79          Modified to Include SegmentId                                       Anubhav Mishra    
-- 2014-Feb-27      80          SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals        Mike Deren     
-- 2014-Mar-19      81          Modified the Completion Factor calculations.                        Mason Roberts    
--                                NOTE: It will need further modifications once     
--                                we recieve the new table    
-- 2014-Apr-09      82          Updated Completion factor logic for                                 Nick Koesters    
--                                updated table LkpExtCMSPlanTypeValues    
-- 2014-May-02      83          Changed @DatePrepared to GetDate()                                  Mason Roberts    
-- 2015-Mar-07      84          Updated star rating to come through as NULL for Low Enrollment      Mark Freel    
-- 2015-Mar-09      85          Added code related to RewardsAndIncentives                          Jordan Purdue    
-- 2015-May-11      86          Changed datatype of decimal precision OSBNetMedicalExpenses,        Deepali Mittal    
--                                OSBNonBenefitExpenses, OSBPremium, to (16,6)     
-- 2015-Jun-18      87          Replaced PerIntMAAssumptions table with SavedCUHeader               Pragya Mishra     
-- 2015-Jun-22      88          Updated the logic to pull correct data from SavedCUHeader           Deepthi Thiyagu     
-- 2015-Nov-11      89          WS1 Sect 6 Removed variance allocation of OtherNetMedicalExpenses   Jordan Purdue     
-- 2016-Feb-18      90          Updated PartDTargetPremiumMessage to display "LOW INCOME PREMIUM    Chris McAuley    
--                                SUBSIDY AMOUNT" for DSNPs (IsSNP = 1, SNPTypeID = 1)       
-- 2016-Mar-08      91          Added VBID cell as 'N' for 2017 plans                               Chris McAuley     
-- 2016-Mar-09      92          Added logic for IsProductPairing & ProductPairingBid# columns       Chris McAuley    
-- 2016-Aug-22      93          Removed SavedCUPlanMap logic from Completion Factor Pull            Jordan Purdue    
-- 2017-Jun-20      94          Removed PerExtContractNumberPlanIDDetail and changed PlanName       Chris Fleming    
--                                to reference new column in SavedPlanHeader     
-- 2017-Sep-13      95          Removed parameter in fnGetMemberMonthsAndAllowedByDuals             Chris Fleming    
--                                cleaned up code and comments    
-- 2018-Mar-14      96          Updated SNPTypeID to include Puerto Rico D-SNP                      Alex Beruscha    
-- 2018-Apr-24      97          Modified LkpExtCMSPlanType for new UI table modifications           Jordan Purdue    
-- 2018-Apr-25      98          Updated PaidThroughDate to VARCHAR(10) and converted value          Chris Fleming    
--                                to the format the BPT accepts during BPT creation     
-- 2018-Jun-20      99          Added five more Product Pairings to SavedPlanProductPairing table   Apoorva Nasa    
-- 2018-Aug-16      100         Modified condition for CertifyingActuaryUserId column from          Apoorva Nasa    
--                                SavedMarketHeader to SavedPlanHeader    
-- 2018-Sep-04      101         Replacing PerIntAdminExpenses with the new Blended Table            Jordan Purdue         
-- 2019-Jul-01      102         Replaced SavedPlanProductPairing with SavedRollupForecastMap        Kritika Singh    
-- 2019-Jul-08      103         Made changes for market, division, region table.                    Kritika Singh    
-- 2019-Aug-26      104         Revert MAAUI Product Pairing Changes                                Pooja Dahiya    
-- 2019-Oct-25      105         Alter UserID size                                                   Manisha Tyagi    
-- 2020-Jan-27      106         Add SavedScenarioVBID to populate the BPT with VBID indicator       Jennifer Chapman    
-- 2020-Feb-26      107         Update for VBID-C and VBID-H distinction                            Alex Beruscha    
-- 2020-Jul-28      108         Add logic for new PlanIntention objects                             Brent Osantowski    
-- 2020-Sep-01      109         Change AltRebate order data type and remove case statements         Brent Osantowski    
-- 2020-Oct-26      110         Backend Alignment and Restructuring project                         Keith Galloway    
-- 2020-Dec-03      111         Removing hard coded ForecastID                                       Brent Osantowski    
-- 2020-Dec-03      112         Convert PartDTargetPremiumMessage to upper case                     Josh Pace    
-- 2020-Dec-14      113         Increasing scale to align BPT risk scores with MRA                  Brent Osantowski    
-- 2020-Dec-14      114         Change AltRebate order data type and remove case statements         Brent Osantowski     
-- 2020-Mar-18      115         Commented out ProductPairing values based off CMS changes           Abraham Ndabian   
-- 2021-Sep-23      116         Need to deploy script in all other environment                        Zeeshan  
-- 2021-Aug-27		117			Adding CorpMargReq                         					    	Phillip Leigh
-- 2022-Jan-10		118			Removing CorpMargReq per CMS rule change							Phillip Leigh
-- 2022-Jan-18		119			Remove InsurerFeeAdminExpense Per CMS change						Phillip Leigh							   
-- 2022-May-02		120			MAAUI migration; replaced table SavedPlanDeductibleMOOPDetail with
--								Benefits_SavedBenefitOption, joining on PlanInfoID AND 
--								BenefitOptionID WHERE SFS.IsLiveIndex = 1; added logic for pulling 
--								PartBDeductible and adjusted the one for INDeductible/OONDeductible; 
--								returning ForecastID instead of ForecastID; for fnAppGetMABPTWS3 
--								replaced reference from ForecastID to ForecastID; removed nested 
--								queries																Aleksandar Dimitrijevic	
-- 2022-Sep-15		121			Combined deductable changes  										Aleksandar Dimitrijevic	
-- 2022-Dec-5       122			Added credentials to contacts. 
--								Adjusted formatting for contact and	certifying name fields			Adam Gilbert
-- 2022-Jan-3       123			Added no lock for the tables										Surya Murthy

-- 2023-Aug-03		124			Created table variable to load data from function and used it 
--								to avoid multiple execution of same function
--								Added missing NOLOCK, Internal parameter							Sheetal Patil	
-- 2024-Mar-05		125			Added no lock for remaining table, added internal parameter
--								Created table variable to load data from function and used it 
--								to avoid multiple execution of same function
--								Changed MedicareSecondaryPayer from FLOAT to Decimal(7,6)			Alex Beruscha
-- 2024-Aug-28      126			Changed the secondary contact <NAME_EMAIL>		Hannah Harmon

-- 2024-SEP-06		127			Removed prior methodology calculating MOOPImpact from 
--								fnAppGetMABPTWS3 to depend on CalcTotalCostShare instead			Franklin Fu
-- 2024-Sep-11		128		    Clean up SalesAdjustmentFactor column from SavedPlanAssumptions Table			Surya Murthy
-- 2025-Jan-24	129			Add Significance field,	Removed VBID's Per CMS														Phillip Leigh
-- 2025-May-05		130		     Datatype change from Char to varchar for @secContactEmail      	Surya Murthy

----------------------------------------------------------------------------------------------------------------------    

CREATE FUNCTION [dbo].[fnAppGetGeneralMABPTValues] 
( 
	@ForecastID INT 
 )  
RETURNS @Results TABLE    
    (    
      ForecastID INT,    
      PlanName VARCHAR(100),    
      ContractNumber CHAR(5),    
      PlanID CHAR(3),    
      SegmentId CHAR(3),    
      MARatingOptionID TINYINT,    
      IsClaimBucket2Output BIT,    
      ContractMMPercent FLOAT,    
      PlanTypeName VARCHAR(50),    
      MAPD CHAR(1),    
      SNP CHAR(1),    
      SNPTypeName VARCHAR(50),    
      ActuarialSwap CHAR(1),    
      SecondaryPayerAdjustment DECIMAL(7,6),    
      DualEligiblePercent FLOAT,    
      PartBPremiumBuyDown FLOAT,    
      AltRebateOrder TINYINT,    
      CompletionFactor FLOAT,    
      PaidThroughDate VARCHAR(10),    
      OrganizationName VARCHAR(100),
	  CorpMargReq DECIMAL (7,4),
     -- VBIDC CHAR(1),    
     -- VBIDH CHAR(1),    
      GroupPlan CHAR(1),    
      EnrolleeType VARCHAR(12),    
      DatePrepared SMALLDATETIME,    
      MARegion VARCHAR(100),    
      MARegionName VARCHAR(500),    
      MandatorySupPackage BIT,    
      IncurredFrom SMALLDATETIME,    
      IncurredTo SMALLDATETIME,    
      PartDTargetPremiumMessage VARCHAR(500),    
      UserID CHAR(7),    
      CertifyingName VARCHAR(100),    
      CertifyingPhone VARCHAR(15),    
      CertifyingEmail VARCHAR(100),    
      ContactName VARCHAR(100),    
      ContactPhone VARCHAR(15),    
      ContactEmail VARCHAR(100),    
      SecondaryContactName VARCHAR(100),    
      SecondaryContactPhone VARCHAR(15),    
      SecondaryContactEmail VARCHAR(100),    
      MemberMonths INT,    
      NonDualMemberMonths INT,    
      DualMemberMonths INT,    
      RiskScore DECIMAL(18,15),    
      NonDualRiskScore DECIMAL(18,15),    
      DualRiskScore DECIMAL(18,15),    
      ExpBaseContractPlanID VARCHAR(10),    
      ExpBaseMemberMonths INT,    
      ExperienceBaseNonDualMemberMonths INT,    
      ExperienceBaseDualMemberMonths INT,    
      ExpBaseRiskScore DECIMAL(18,15),    
      ExpBaseNonDualRiskScore DECIMAL(18,15),    
      ExpBaseDualRiskScore DECIMAL(18,15),    
      MarketingSales FLOAT,    
      DirectAdmin FLOAT,    
      InDirectAdmin FLOAT,    
      QualityInitiatives FLOAT,    
      TaxesAndFees FLOAT,    
      Reins FLOAT,    
      Profit FLOAT,       
      TotalReduceCostShare DECIMAL(7, 2),    
      TotalOtherSuppBen DECIMAL(7, 2),    
      ESRDMembers DECIMAL(28, 6),    
      HospiceMembers DECIMAL(28, 6),    
      OOAMembers DECIMAL(28, 6),    
      ExperienceESRDMemberMonths INT,    
      RxBasicPremium DECIMAL(7, 2),    
      RxSuppPremium DECIMAL(7, 2),    
      RxBasicBuyDown DECIMAL(7, 2),    
      RxSuppBuyDown DECIMAL(7, 2),    
      IsINMOOP VARCHAR(3),    
      IsOONMOOP VARCHAR(3),    
      IsCombinedMOOP VARCHAR(3),    
      INMOOP FLOAT,    
      OONMOOP FLOAT,    
      CombinedMOOP FLOAT,    
      INDeductible FLOAT,    
      OONDeductible FLOAT,
	  CombinedDeductible  FLOAT,    
      PartBDeductible INT,    
      INMOOPImpact FLOAT,    
      OONMOOPImpact FLOAT,    
      EstimatedPlanBidComponent DECIMAL(7, 2),    
      BidNotes1 VARCHAR(MAX),    
      BidNotes2 VARCHAR(MAX),    
      BidNotes3 VARCHAR(MAX),    
      BidNotes4 VARCHAR(MAX),    
      BidNotes5 VARCHAR(MAX),    
      CMSESRDRevenue DECIMAL(18, 2),    
      CMSHospiceRevenue DECIMAL(18, 2),    
      CMSOtherRevenue DECIMAL(18, 2),    
      ESRDPremium DECIMAL(18, 2),    
      HospicePremium DECIMAL(18, 2),    
      OtherPremium DECIMAL(18, 2),    
      ESRDNetMedicalExpenses DECIMAL(18, 2),    
      HospiceNetMedicalExpenses DECIMAL(18, 2),    
      OtherNetMedicalExpenses DECIMAL(18, 2),    
      ESRDMemberMonths INT,    
      HospiceMemberMonths INT,    
      MarketingandSalesAdminExpense DECIMAL(17, 2),    
      DirectAdminExpense DECIMAL(17, 2),    
      IndirectAdminExpense DECIMAL(17, 2),    
      QualityInitiativesAdminExpense DECIMAL(17, 2),    
      TaxesAndFeesAdminExpense DECIMAL(17, 2),    
      NetCostofPrivateInsuranceExpense DECIMAL(17, 2),     
      InsurerFeesAdminExpense DECIMAL(18, 2),        
      MedicaidRevenue DECIMAL(18, 2),         
      MedicaidBenefitExpenses DECIMAL(18, 2),      
      MedicaidNonBenefitExpenses DECIMAL(18, 2),       
      RedABCostShare DECIMAL(6, 2),    
      UnspentRebate FLOAT,    
      EGWPMSB FLOAT,    
      CMSQualityStars DECIMAL(2, 1),    
      CMSEnrollmentType VARCHAR(3),    
      MAMarketingExp DECIMAL(12, 6),    
      MADirectAdminExp DECIMAL(12, 6),    
      MAIndirectAdminExp DECIMAL(12, 6),    
      MAQualityAdminExp DECIMAL(12, 6),    
      MATaxesAndFeesAdminExp DECIMAL(12, 6),    
      PDMarketingExp DECIMAL(12, 6),    
      PDDirectAdminExp DECIMAL(12, 6),    
      PDIndirectAdminExp DECIMAL(12, 6),    
      PDQualityAdminExp DECIMAL(12, 6),    
      PDTaxesAndFeesAdminExp DECIMAL(12, 6),    
       InsurerFee DECIMAL(8, 6),    
      OSBNetMedicalExpenses DECIMAL(16, 6),    
      OSBNonBenefitExpenses DECIMAL(16, 6),    
      OSBPremium DECIMAL(16, 6),    
      OSBMembermonths INT,
	  Significance  DECIMAL(5,4)
    )    
  AS   
    BEGIN    
	    DECLARE @XForecastID INT = @ForecastID
            DECLARE @MarketID INT,    
            @PlanTypeID INT,    
            @ContractNumber CHAR(5),    
            @PlanID CHAR(3),    
            @SegmentId CHAR(3),     
            @ContractNumberPlanID CHAR(9),    
            @CompletionFactor FLOAT,    
            @OrganizationName VARCHAR(100), 
			@CorpMargReq DECIMAL (7,4),
            @PlanName VARCHAR(100),    
            @MAPD AS BIT,    
            @IsSNP AS BIT,    
            @SNPTypeID AS TINYINT,    
            @MARegionID AS TINYINT,    
            @ExperienceMemberMonths INT,    
            @ExperienceNonDualMemberMonths INT,    
            @ExperienceDualMemberMonths INT,    
            @ProjectedESRDMemberMonths DECIMAL(28, 15),    
            @ProjectedHospiceMemberMonths DECIMAL(28, 15),    
            @ProjectedOOAMemberMonths DECIMAL(28, 15),    
            @ExperienceESRDMemberMonths INT,    
            @ExperienceBlendedRiskFactor DECIMAL(18, 15),    
            @ExperienceNonDualRiskFactor DECIMAL(18, 15),    
            @ExperienceDualRiskFactor DECIMAL(18, 15),    
            @AltBaseExpMemberMonths INT,    
            @AltBaseExpNonDualMemberMonths INT,    
            @AltBaseExpDualMemberMonths INT,    
            @AltBaseExpProjectedESRDMemberMonths INT,    
            @AltBaseExpESRDMemberMonths INT,    
            @AltBaseExpBlendedRiskFactor DECIMAL(18, 15),    
            @AltBaseExpNonDualRiskFactor DECIMAL(18, 15),    
            @AltBaseExpDualRiskFactor DECIMAL(18, 15),    
            @PaidThroughDate VARCHAR(10),    
            @IncurredDateBegin SMALLDATETIME,    
            @IncurredDateEnd SMALLDATETIME,    
            @DatePrepared SMALLDATETIME,    
            @EstimatedPlanBidComponent DECIMAL(7, 2),    
            @AlternateBaseForecastID INT,    
            @UnspentRebate FLOAT,    
            @EGWPMSB FLOAT,    
           -- @VBIDCIndicator CHAR (1),    
           -- @VBIDHIndicator CHAR (1),
			@Significance DECIMAL(5,4)

        DECLARE @secContactEmail VARCHAR(25) = '<EMAIL>'; 

		SELECT @Significance= CASE WHEN @ExperienceMemberMonths =NULL THEN NULL ELSE 
			 SignificanceLevel  END   FROM dbo.LkpModelSettings 


		  --Get some basic values from SavedPlanInfo    
		  SELECT  @MarketID = SPI.ActuarialMarketID,    
				  @PlanTypeID = SPI.PlanTypeID,    
				  @ContractNumber = LEFT(SPI.CPS,5),    
				  @PlanID = SUBSTRING(SPI.CPS, 7, 3),    
				  @SegmentID = RIGHT(SPI.CPS, 3),    
				  @PlanName = SPI.PlanName,    
				  @MAPD = CAST(SPI.PlanTypeID - 1 AS BIT),    
				  @IsSNP = CAST(CASE WHEN SPI.SNPTypeID = 0 THEN 0 ELSE 1 END AS BIT),    
				  @SNPTypeID  = CAST(    
									 CASE WHEN spi.MAPlanDesignID = 4  --Indicate D-SNP Look-A-Like and populate Part D Premium Intion as LIPSA when the MAAUI has the plan as non-SNP and Value Plus Plan (VPP)    
									  AND spi.SNPTypeID = 0 THEN 5    
									  WHEN spi.MAPlanDesignID <> 4  --Indicate non-SNP and populate Part D Premium Intion as non-LIPSA when the MAAUI has the plan as non-SNP and NOT Value Plus Plan (VPP)    
									  AND spi.SNPTypeID = 0 THEN 0    
									  ELSE lkp.Bm_SnpTypeID_tb     -- Otherwise default to the standard Bid Model to MAAUI SNP Crosswalk    
									 END AS TINYINT    
									),    
				  @MARegionID = SPI.MARegionID    
		  FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)   
		  LEFT JOIN dbo.SavedPlanInfo SPI  WITH (NOLOCK)  
		   ON SPI.PlanInfoID = SFS.PlanInfoID    
		  INNER JOIN dbo.LkpSNPTypeIDXWalk LKP WITH (NOLOCK)     
		   ON SPI.SNPTypeID = LKP.Maaui_SnpType_Id     
		  WHERE SFS.ForecastID = @XForecastID    

		  -- Get VBID indicator from SavedScenarioVBID    
		  --SET @VBIDCIndicator =    
				--			   ISNULL((SELECT TOP 1 CASE WHEN ssv.VBIDID IS NOT NULL    
				--				  THEN 'Y'    
				--				  ELSE 'N'    
				--				 END    
				--			   FROM dbo.SavedScenarioVBID ssv WITH (NOLOCK)   
				--			   INNER JOIN dbo.LkpVBID lv WITH (NOLOCK)   
				--				ON lv.VBIDID = ssv.VBIDID    
				--			   WHERE ssv.ForecastID = @XForecastID    
				--				 AND lv.VBIDTypeID = 1),'N') --VBIDTypeID = 1 is VBID-C; Can be verified in LkpVBIDType Table    

		  --SET @VBIDHIndicator =    
				--				ISNULL((SELECT TOP 1 CASE WHEN ssv.VBIDID IS NOT NULL    
				--					THEN 'Y'    
				--					ELSE 'N'    
				--					END    
				--				FROM dbo.SavedScenarioVBID ssv WITH (NOLOCK)   
				--				INNER JOIN dbo.LkpVBID lv WITH (NOLOCK)   
				--				ON lv.VBIDID = ssv.VBIDID    
				--				WHERE ssv.forecastID = @XForecastID    
				--				  AND lv.VBIDTypeID = 2),'N') --VBIDTypeID = 2 is VBID-H; Can be verified in LkpVBIDType Table    

        -- Get UnspentRebate and EGWP Info   

			--pre
			DECLARE @PRE TABLE
				(

				ForecastID INT,
				Numerator FLOAT,
				Denominator FLOAT
				)

			INSERT INTO @PRE
			SELECT DFS.ForecastID,
				   Numerator = SUM(PTV.CompletedPaidClaims),    
                   Denominator = SUM(PTV.PaidClaims)    
            FROM dbo.SavedPlanDFSummary DFS  WITH (NOLOCK)  
			INNER JOIN dbo.SavedPlanInfo SPI  WITH (NOLOCK)  
				ON DFS.PlanInfoID = SPI.PlanInfoID    
            INNER JOIN dbo.LkpExtCMSPlanTypeValues PTV WITH (NOLOCK)    
				ON PTV.ContractNumber + '-' + PTV.PlanID + '-' + PTV.SegmentID = SPI.CPS    
            WHERE DFS.MARatingOptionID = 1    
              AND DFS.ForecastID = @XForecastID    
            GROUP BY DFS.ForecastID
		----------------------------------------------------------------------------------------------------------------

        SELECT  @UnspentRebate = ISNULL(UnspentRebate, 0),    
                @EGWPMSB = ISNULL(EGWPMSB, 0)    
        FROM    dbo.fnGetUnspentRebateForEGWPMSB(@XForecastID)    

        BEGIN    

           SELECT  @CompletionFactor = dbo.fnGetSafeDivisionResult(pre.Numerator,pre.Denominator)    
           FROM @PRE pre

		   SELECT  TOP 1 @PaidThroughDate = CONVERT(VARCHAR(10), DFH.PaidThroughDate, 101),    
						 @IncurredDateBegin = DFH.IncurredStartDate ,    
						 @IncurredDateEnd = DFH.IncurredEndDate ,    
						 @DatePrepared = GETDATE()    
		   FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)   
		   INNER JOIN dbo.SavedDFVersionHeader DFH  WITH (NOLOCK)  
			ON SFS.DFVersionID = DFH.DFVersionID    
		   WHERE SFS.ForecastID = @XForecastID    


           SELECT  @OrganizationName = OrganizationName    
           FROM    dbo.PerExtContractNumberHeader cnh WITH (NOLOCK)    
           WHERE   cnh.ContractNumber = @ContractNumber    


		   SELECT @CorpMargReq = CorpMargReq FROM dbo.PerIntMAAssumptions WITH (NOLOCK) 


           SELECT  @EstimatedPlanBidComponent = MAX(ISNULL(EstimatedPlanBidComponent,0))    
           FROM    dbo.SavedPlanDetail  WITH (NOLOCK)   
           WHERE   ForecastID = @XForecastID   


            --Get Member Months    
            BEGIN    
                SELECT  @ExperienceMemberMonths = NonDualBiddableMemberMonths    
												+ DualBiddableMemberMonths,    
                        @ExperienceNonDualMemberMonths = NonDualBiddableMemberMonths,    
                        @ExperienceDualMemberMonths = DualBiddableMemberMonths,    
                        @ExperienceESRDMemberMonths = ESRDNonDualMemberMonths + ESRDDualMemberMonths,    
                        @ExperienceBlendedRiskFactor = CASE ( NonDualBiddableMemberMonths + DualBiddableMemberMonths )    
                                                         WHEN 0 THEN 0    
                                                         ELSE ( NonDualBiddableMemberMonths * NonDualRiskFactor    
                                                              + DualBiddableMemberMonths * DualRiskFactor )    
                                                              / ( NonDualBiddableMemberMonths + DualBiddableMemberMonths )    
                                                       END,    
                        @ExperienceNonDualRiskFactor = CASE NonDualBiddableMemberMonths    
                                                         WHEN 0 THEN 0    
                                                         ELSE NonDualBiddableMemberMonths * NonDualRiskFactor    
                                                              / NonDualBiddableMemberMonths    
                                                       END,    
                        @ExperienceDualRiskFactor = CASE DualBiddableMemberMonths    
                                                      WHEN 0 THEN 0    
                                                      ELSE DualBiddableMemberMonths * DualRiskFactor    
                                                           / DualBiddableMemberMonths    
													END    
                FROM    dbo.fnGetMemberMonthsAndAllowedByDuals(@XForecastID, 1) --Experience    

					-- Sales Adjustment Factor
					DECLARE @SAF TABLE
						(
						ForecastID INT
						)

					INSERT INTO @SAF
					SELECT spa.ForecastID   
					FROM dbo.SavedPlanAssumptions spa WITH (NOLOCK)    
					WHERE spa.ForecastID = @XForecastID    

                SELECT -- This is the ESRDMembership Total that is put in cell U15 on the MA Benchmark tab.    
                        @ProjectedESRDMemberMonths = SUM(spmm.MemberMonths)													  
                FROM    dbo.SavedPlanMemberMonthDetail spmm WITH (NOLOCK)
				INNER JOIN @SAF saf
					ON spmm.ForecastID = saf.ForecastID
                INNER JOIN dbo.SavedPlanStateCountyDetail spscd WITH (NOLOCK)    
					ON spmm.ForecastID = spscd.ForecastID    
                   AND spmm.StateTerritoryID = spscd.StateTerritoryID    
                   AND spmm.CountyCode = spscd.CountyCode    
                INNER JOIN dbo.LkpIntDemogIndicators di WITH (NOLOCK)    
					ON spmm.DemogIndicator = di.DemogIndicator    
                WHERE spmm.ForecastID = @XForecastID    
                  AND spscd.IsCountyExcludedFromBPTOutput = 0    
				  AND di.DualEligibleTypeID <> 2    
                  AND di.IsESRD = 1    
                  AND di.IsHospice = 0 				

                SELECT -- This is the HospiceMembership Total that is put in cell U16 on the MA Bnchmk tab.    
                        @ProjectedHospiceMemberMonths = SUM(spmm.MemberMonths)														  
                FROM dbo.SavedPlanMemberMonthDetail spmm WITH (NOLOCK)
				INNER JOIN @SAF saf
					ON spmm.ForecastID = saf.ForecastID
                INNER JOIN dbo.SavedPlanStateCountyDetail spscd  WITH (NOLOCK)   
					ON spmm.ForecastID = spscd.ForecastID    
                   AND spmm.StateTerritoryID = spscd.StateTerritoryID    
                   AND spmm.CountyCode = spscd.CountyCode    
                INNER JOIN dbo.LkpIntDemogIndicators di WITH (NOLOCK)    
					ON spmm.DemogIndicator = di.DemogIndicator    
                WHERE spmm.ForecastID = @XForecastID    
                  AND spscd.IsCountyExcludedFromBPTOutput = 0    
                  AND di.DualEligibleTypeID <> 2    
                  AND di.IsESRD = 0    
                  AND di.IsHospice = 1   


                SELECT -- This is the OOAMembership Total that is put in cell U17 on the MA Bnchmk tab.     
                        @ProjectedOOAMemberMonths = SUM(ooa.MemberMonths)                        
                FROM dbo.SavedPlanOOAMemberMonthDetail ooa WITH (NOLOCK)
				INNER JOIN @SAF saf
					ON ooa.ForecastID = saf.ForecastID
                WHERE ooa.ForecastID = @XForecastID  				  
            END    

            --Populate the Header Values    
            DECLARE @MultipleContractMM INT    
            SELECT @MultipleContractMM = SUM(MemberMonths)    
            FROM dbo.fnAppGetMABPTWS1BasePlans(@XForecastID)    

            BEGIN
					--Added Benefits
					DECLARE @AddedBenefits TABLE
						(
						 ForecastID INT,
						 MSBAdmin FLOAT,
						 MSBSales FLOAT
						)

					INSERT INTO @AddedBenefits
					SELECT spab.ForecastID,    
                           SUM(added.AddedBenefitAdmin) MSBAdmin,    
                           SUM(added.AddedBenefitSales) MSBSales    
                    FROM    dbo.LkpIntAddedBenefitExpenseDetail added WITH (NOLOCK)    
                    INNER JOIN dbo.SavedPlanAddedBenefits spab WITH (NOLOCK)
						ON spab.AddedBenefitTypeID = added.AddedBenefitTypeID    
                    WHERE spab.ForecastID = @XForecastID    
                      AND spab.Ishidden = 0    
                    GROUP BY spab.ForecastID 

					--WS3 MOOP
					DECLARE @WS3MOOP TABLE
						(
						ForecastID INT,
						INMoopImpact FLOAT,
						OONMOOPImpact FLOAT
						)


					INSERT INTO @WS3MOOP
						SELECT ForecastID,
					INMOOPImpact = SUM(INAllowed * (1-INDeductibleFactor) * INEffectiveCoinsurance - (INAllowed * (1-INDeductibleFactor) * INEffectiveCoinsurance * INMOOPFactor)),
					OONMOOPImpact = SUM(OONAllowed * (1-OONDeductibleFactor) * OONEffectiveCoinsurance - (OONAllowed * (1-OONDeductibleFactor) * OONEffectiveCoinsurance * OONMOOPFactor))
					FROM dbo.CalcTotalCostShare
					WHERE ForecastID = @XForecastID
					AND IsBenefitYearCurrentYear = 0
					GROUP BY ForecastID


					--OSB
					DECLARE @OSB TABLE
							(
							 ForecastID INT,
							 OSBNetMedicalExpenses FLOAT,
							 OSBNonBenefitExpenses FLOAT,
							 OSBPremium FLOAT,
							 OSBMemberMonths FLOAT
							)

					INSERT INTO @OSB
					SELECT ForecastID,    
                           OSBNetMedicalExpenses,    
                           OSBNonBenefitExpenses,    
                           OSBPremium,    
                           OSBMemberMonths    
                    FROM dbo.fnGetBaseOSBExperience(@XForecastID)

					--Base Net PMPM
					DECLARE @WS1 TABLE
						(
						 BaseNetPMPM FLOAT
						)

					INSERT INTO @WS1
					SELECT BaseNetPMPM = SUM(BaseNetPMPM)    
                    FROM dbo.fnAppGetMABPTWS1Summary(@XForecastID)

			--------------------------------------------------------------------------------------------------------

 DECLARE	@fnAppGetMABidNotes TABLE    
    (    
      ForecastID INT,  
	  PlanTypeID TINYINT,
	  BidWorksheet TINYINT,
	  CellLocation VARCHAR(7),
	  IsDefaultBidNote BIT,
	  BidNoteID SMALLINT,
	  NoteText VARCHAR(MAX)
	)
	INSERT INTO @fnAppGetMABidNotes 
	SELECT * FROM dbo.fnAppGetMABidNotes(@XForecastID) 

				INSERT INTO @Results    
					SELECT  @XForecastID,    
                            @PlanName PlanName,    
                            @ContractNumber ContractNumber,    
                            @PlanID PlanID,    
                            @SegmentID SegmentID,   							
                            1 MARatingOptionID, -- Was forced to 1 in SavedPlanHeader where it was previously pulled    
                            IsClaimBucket2Output = 0,     
                            ContractMMPercent = (CASE WHEN @ExperienceMemberMonths <> 0    
                                                      THEN 1    
													  ELSE NULL    
                                                 END ), --Exp or Blended    
                            cpt.ProductType,    
                            MAPD = (CASE WHEN @MAPD = 1 THEN 'Y' ELSE 'N' END),    
                            SNP = (CASE WHEN @IsSNP = 1 THEN 'Y' ELSE 'N' END),    
                            SNPTypeName = (CASE WHEN @SNPTypeID = 5 THEN 'N/A'     
												ELSE CASE WHEN @SNPTypeID = 4 THEN 'Dual-Eligible'    
													      ELSE (SELECT lkp.SNPTypeName 
																FROM dbo.LkpExtSNPType lkp WITH (NOLOCK) 
																WHERE lkp.SNPTypeID = @SNPTypeID)    
													 END    
										   END) ,    
                            ActuarialSwap = 'N',    
                            SecondaryPayerAdjustment = ISNULL(spa.SecondaryPayerAdjustment,0),    
                            DualEligiblePercent = (CASE WHEN @ExperienceMemberMonths = 0    
                                                        THEN 0    
                                                        ELSE @ExperienceDualMemberMonths / @ExperienceMemberMonths    
                                                   END),    
                            SFS.PartBPremiumBuyDown,    
                            SFS.AltRebateOrder,    
                            CompletionFactor = @CompletionFactor,    
                            PaidThroughDate = @PaidThroughDate,    
                            OrganizationName = @OrganizationName,   
							CorpMargReq     = @CorpMargReq, 
							--VBIDC = @VBIDCIndicator,     
                          --  VBIDH = @VBIDHIndicator,    
                            GroupPlan = 'N',    
                            EnrolleeType = 'A/B',    
                            DatePrepared = @DatePrepared,    
                            MARegion = @MARegionID,    
                            MARegionName = MR.MARegionName,    
                            MandatorySupPackage = 0,    
                            IncurredFrom = @IncurredDateBegin,    
                            IncurredTo = @IncurredDateEnd,    
                            PartDTargetPremiumMessage = UPPER(lpi.PlanIntentionMessage),   --Worksheet6 Cell R47    
                            Certifying.UserID,    
							CertifyingName = Certifying.UserLastName 
										  + ', ' + Certifying.UserFirstName    
										  + CASE WHEN LEN(ISNULL(Certifying.UserMiddleInitial,'')) > 0 THEN ' '+ Certifying.UserMiddleInitial ELSE '' END
										  + CASE WHEN LEN(ISNULL(Certifying.UserCertifyingTitle,'')) > 0 THEN ', '+ Certifying.UserCertifyingTitle ELSE '' END,    
                            CertifyingPhone = Certifying.UserPhone,    
                            CertifyingEmail = Certifying.UserEmail,                             
							ContactName = Contact.UserLastName 
										  + ', ' + Contact.UserFirstName    
										  + CASE WHEN LEN(ISNULL(Contact.UserMiddleInitial,'')) > 0 THEN ' '+ Contact.UserMiddleInitial ELSE '' END
										  + CASE WHEN LEN(ISNULL(Contact.UserCertifyingTitle,'')) > 0 THEN ', '+ Contact.UserCertifyingTitle ELSE '' END
										  + CASE WHEN LEN(ISNULL(Contact.UserContactTitle,'')) > 0 THEN ', '+ Contact.UserContactTitle ELSE '' END,    
                            ContactPhone = Contact.UserPhone,    
                            ContactEmail = Contact.UserEmail,    
							SecondaryContactName = SecondaryContact.UserLastName 
										  + ', ' + SecondaryContact.UserFirstName    
										  + CASE WHEN LEN(ISNULL(SecondaryContact.UserMiddleInitial,'')) > 0 THEN ' '+ SecondaryContact.UserMiddleInitial ELSE '' END
										  + CASE WHEN LEN(ISNULL(SecondaryContact.UserCertifyingTitle,'')) > 0 THEN ', '+ SecondaryContact.UserCertifyingTitle ELSE '' END
										  + CASE WHEN LEN(ISNULL(SecondaryContact.UserContactTitle,'')) > 0 THEN ', '+ SecondaryContact.UserContactTitle ELSE '' END,    
                            SecondaryPhone = SecondaryContact.UserPhone,    
                            SecondaryEmail = @secContactEmail,  
                            MemberMonths = @ExperienceMemberMonths,    
                            NonDualMemberMonths = @ExperienceNonDualMemberMonths,    
                            DualMemberMonths = @ExperienceDualMemberMonths,    
                            RiskScore = @ExperienceBlendedRiskFactor,    
                            NonDualRiskScore = @ExperienceNonDualRiskFactor,    
                            DualRiskScore = @ExperienceDualRiskFactor,    
                            ExpBaseContractPlanID = NULL,    
                            ExpBaseMemberMonths = NULL,    
                            ExperienceBaseNonDualMemberMonths = NULL,    
                            ExperienceBaseDualMemberMonths = NULL,    
                            ExpBaseRiskScore = NULL,    
                            ExpBaseNonDualRiskScore = NULL,    
                            ExpBaseDualRiskScore = NULL,    
                            WS4And6Totals.MarketingSales,    
                            WS4And6Totals.DirectAdmin,    
                            WS4And6Totals.InDirectAdmin,    
                            WS4And6Totals.QualityInitiatives,    
                            WS4And6Totals.TaxesAndFees,    
                            WS4And6Totals.Reins,    
                            Profit = WS4And6Totals.Profit,














                            TotalReduceCostShare = Rebate.RedABCostShare,    
                            TotalOtherSuppBen = Rebate.SuppPremBuydown,    
                            ESRDMembers = @ProjectedESRDMemberMonths,    
                            HospiceMembers = @ProjectedHospiceMemberMonths,    
                            OOAMembers = @ProjectedOOAMemberMonths,    
                            ExperienceESRDMemberMonths = @ExperienceESRDMemberMonths,    
                            RxBasicPremium = CASE WHEN @MAPD = 1 THEN spa.RxBasicPremium    
                                                  WHEN @MAPD = 0 THEN NULL    
                                                  ELSE Rebate.RxBasicPremium    
                                             END,    
                            RxSuppPremium =  CASE WHEN @MAPD = 1 THEN spa.RxSuppPremium    
                                                  WHEN @MAPD = 0 THEN NULL    
                                                  ELSE Rebate.RxSuppPremium    
                                             END,    
                            RxBasicBuyDown = Rebate.RxBasicBuyDown,    
                            RxSuppBuyDown = Rebate.RxSuppBuyDown,    
                            IsINMOOP = (CASE WHEN MOOPDet.INMOOP IS NULL    
                                             THEN 'NO'    
                                             ELSE 'YES'    
                                        END),    
                            IsOONMOOP = (CASE WHEN MOOPDet.OONMOOP IS NULL    
                                              THEN 'NO'    
                                              ELSE 'YES'    
                                         END),    
                            IsCombinedMOOP = (CASE WHEN MOOPDet.CombinedMOOP IS NULL    
                                                   THEN 'NO'    
                                                   ELSE 'YES'    
                                              END ),     
                            INMOOP =  MOOPDet.INMOOP,
                            OONMOOP = MOOPDet.OONMOOP,    
                            CombinedMOOP = MOOPDet.CombinedMOOP,    
                            INDeductible = CASE WHEN MOOPDet.IsPartBDeductible = 0
												THEN MOOPDet.INDeductible
												ELSE NULL
										   END,     
                            OONDeductible = CASE WHEN MOOPDet.IsPartBDeductible = 0
												THEN MOOPDet.OONDeductible
												ELSE NULL
											END,  
							CombinedDecutible = CASE WHEN MOOPDet.IsPartBDeductible = 0
												THEN MOOPDet.CombinedDeductible
												ELSE NULL
											END,  												  
                            PartBDeductible = CASE WHEN MOOPDet.IsPartBDeductible = 1
												   THEN 
														CASE WHEN MOOPDet.INDeductible IS NULL
															 THEN CASE WHEN MOOPDet.CombinedDeductible IS NULL
																	   THEN 0
																	   ELSE MOOPDet.CombinedDeductible
																  END
															 ELSE MOOPDet.INDeductible
														END
												   ELSE NULL
											  END,    
                            WS3MOOP.INMOOPImpact,    
                            WS3MOOP.OONMOOPImpact,    
                            EstimatedPlanBidComponent = @EstimatedPlanBidComponent,    
                            BidNotes1 = (SELECT NoteText    
                                         FROM @fnAppGetMABidNotes    
                                         WHERE BidNoteID = 1),    
                            BidNotes2 = (SELECT NoteText    
                                         FROM @fnAppGetMABidNotes    
                                         WHERE BidNoteID = 2),    
                            BidNotes3 = (SELECT NoteText    
                                         FROM @fnAppGetMABidNotes  
                                         WHERE BidNoteID = 3),    
                            BidNotes4 = (SELECT NoteText    
                                         FROM @fnAppGetMABidNotes    
                                         WHERE BidNoteID = 4),    
                            BidNotes5 = (SELECT NoteText    
                                         FROM @fnAppGetMABidNotes  
                                         WHERE BidNoteID = 5),    
                            CMSESRDRevenue = CASE WHEN @MultipleContractMM IS NULL    
                                                  THEN WS1Sec6.CMSESRDRevenue    
                                                  ELSE ISNULL(WS1Sec6.CMSESRDRevenue, 0)    
														 / @ExperienceMemberMonths    
                                                         * @MultipleContractMM    
                                             END,    
                            CMSHospiceRevenue = CASE WHEN @MultipleContractMM IS NULL    
                                                     THEN WS1Sec6.CMSHospiceRevenue    
                                                     ELSE ISNULL(WS1Sec6.CMSHospiceRevenue, 0)    
															/ @ExperienceMemberMonths    
                                                            * @MultipleContractMM    
                                                END,    
							CMSOtherRevenue = CASE WHEN @MultipleContractMM IS NULL    
												   THEN WS1Sec6.CMSOtherRevenue    
                                                   ELSE ISNULL(WS1Sec6.CMSOtherRevenue, 0)    
                                                          / @ExperienceMemberMonths    
                                                          * @MultipleContractMM    
                                              END,    
                            ESRDPremium = CASE WHEN @MultipleContractMM IS NULL    
                                               THEN WS1Sec6.ESRDPremium    
                                               ELSE ISNULL(WS1Sec6.ESRDPremium, 0)    
                                                      / @ExperienceMemberMonths    
                                                      * @MultipleContractMM    
                                          END,    
                            HospicePremium = CASE WHEN @MultipleContractMM IS NULL    
                                                  THEN WS1Sec6.HospicePremium    
                                                  ELSE ISNULL(WS1Sec6.HospicePremium, 0)    
                                                         / @ExperienceMemberMonths    
                                                         * @MultipleContractMM    
                                             END,    
                            OtherPremium = CASE WHEN @MultipleContractMM IS NULL    
                                                THEN WS1Sec6.OtherPremium    
												ELSE ISNULL(WS1Sec6.OtherPremium, 0)    
                                                       / @ExperienceMemberMonths    
                                                       * @MultipleContractMM    
                                           END,    
                            ESRDNetMedicalExpenses = CASE WHEN @MultipleContractMM IS NULL    
                                                          THEN WS1Sec6.ESRDNetMedicalExpenses    
                                                          ELSE (ISNULL(WS1Sec6.ESRDNetMedicalExpenses, 0))    
                                                                  / @ExperienceMemberMonths    
																  * @MultipleContractMM    
                                                     END,    
                            HospiceNetMedicalExpenses = CASE WHEN @MultipleContractMM IS NULL    
                                                             THEN WS1Sec6.HospiceNetMedicalExpenses    
                                                             ELSE ISNULL(WS1Sec6.HospiceNetMedicalExpenses, 0)    
																	/ @ExperienceMemberMonths    
																	* @MultipleContractMM    
                                                        END,    
                            OtherNetMedicalExpenses = @ExperienceMemberMonths    
													* WS1.BaseNetPMPM,    
                            ESRDMemberMonths = ISNULL(WS1Sec6.ESRDMemberMonths, 0),    
                            HospiceMemberMonths = ISNULL(WS1Sec6.HospiceMemberMonths, 0),    
                            MarketingandSalesAdminExpense = CASE WHEN @MultipleContractMM IS NULL    
																 THEN WS1Sec6.MarketingandSalesAdminExpense    
																 ELSE ISNULL(WS1Sec6.MarketingandSalesAdminExpense, 0)    
																		/ @ExperienceMemberMonths    
																		* @MultipleContractMM    
                                                            END,    
                            DirectAdminExpense = CASE WHEN @MultipleContractMM IS NULL    
                                                      THEN WS1Sec6.DirectAdminExpense    
                                                      ELSE ISNULL(WS1Sec6.DirectAdminExpense, 0)    
                                                             / @ExperienceMemberMonths    
                                                             * @MultipleContractMM    
                                                 END,    
                            IndirectAdminExpense = CASE WHEN @MultipleContractMM IS NULL    
                                                        THEN WS1Sec6.IndirectAdminExpense    
                                                        ELSE ISNULL(WS1Sec6.IndirectAdminExpense, 0)    
                                                               / @ExperienceMemberMonths    
                                                               * @MultipleContractMM    
                                                       END,    
                            QualityInitiativesAdminExpense = CASE WHEN @MultipleContractMM IS NULL    
																  THEN WS1Sec6.QualityInitiativesAdminExpense    
																  ELSE ISNULL(WS1Sec6.QualityInitiativesAdminExpense, 0)    
																		  / @ExperienceMemberMonths    
																		  * @MultipleContractMM    
                                                              END,    
                            TaxesAndFeesAdminExpense = CASE WHEN @MultipleContractMM IS NULL    
                                                            THEN WS1Sec6.TaxesAndFeesAdminExpense    
                                                            ELSE ISNULL(WS1Sec6.TaxesAndFeesAdminExpense, 0)    
																  / @ExperienceMemberMonths    
																  * @MultipleContractMM    
                                                       END,    
                            NetCostofPrivateInsuranceExpense = CASE WHEN @MultipleContractMM IS NULL    
																	THEN WS1Sec6.NetCostofPrivateInsuranceExpense    
																	ELSE ISNULL(WS1Sec6.NetCostofPrivateInsuranceExpense, 0)    
																		  / @ExperienceMemberMonths    
																		  * @MultipleContractMM    
                                                               END,      
                            InsurerFeesAdminExpense = CASE WHEN @MultipleContractMM IS NULL    
                                                           THEN WS1Sec6.InsurerFeesAdminExpense    
                                                           ELSE ISNULL(WS1Sec6.InsurerFeesAdminExpense, 0)    
	                                                              / @ExperienceMemberMonths    
																* @MultipleContractMM    
                                                      END,    
                            MedicaidRevenue = CASE WHEN @MultipleContractMM IS NULL    
                                                   THEN WS1Sec6.MedicaidRevenue    
                                                   ELSE ISNULL(WS1Sec6.MedicaidRevenue, 0)    
                                                            / @ExperienceMemberMonths    
                                                            * @MultipleContractMM    
                                              END,    
                            MedicaidBenefitExpenses = CASE WHEN @MultipleContractMM IS NULL    
                                                           THEN WS1Sec6.MedicaidBenefitExpenses    
                                                           ELSE ISNULL(WS1Sec6.MedicaidBenefitExpenses, 0)    
																  / @ExperienceMemberMonths    
																  * @MultipleContractMM    
                                                      END,    
                            MedicaidNonBenefitExpenses = CASE WHEN @MultipleContractMM IS NULL    
                                                              THEN WS1Sec6.MedicaidNonBenefitExpenses    
                                                              ELSE ISNULL(WS1Sec6.MedicaidNonBenefitExpenses, 0)    
																	  / @ExperienceMemberMonths    
																	  * @MultipleContractMM    
                                                         END,    
                            RedABCostShare = (SELECT RedABCostShare    
                                              FROM dbo.fnGetMARebateAllocation(@XForecastID)    
                                             ),    
                            UnspentRebate = @UnspentRebate, -- No matter group or not, just get the unspent rebate    
                            EGWPMSB = 0,    
                            CMSQualityStars = CASE WHEN IsLowEnrollment = 1    
                                                   THEN NULL    
                                                   ELSE CAST(CombinedStars AS VARCHAR(MAX))    
                                              END,    
                            CMSEnrollmentType = CASE WHEN IsNEW = 1    
                                                     THEN 'NEW'    
                                                     WHEN IsLowEnrollment = 1    
                                                     THEN 'LOW'    
                                                     ELSE ''    
                                                END,    
                            MAMarketingExp = expense.MAMarketingAdminPMPM    
											+ ISNULL(added.MSBSales, 0),    
                            MADirectAdminExp = expense.MADirectAdminPMPM    
											+ ISNULL(added.MSBAdmin, 0),    
							MAIndirectAdminExp = expense.MAIndirectAdminPMPM,    
                            MAQualityAdminExp = expense.MAQualityAdminPMPM,    
                            MATaxesAndFeesAdminExp = expense.MATaxesAndFeesAdminPMPM,    
                            PDMarketingExp = expense.PDMarketingAdminPMPM,    
                            PDDirectAdminExp = expense.PDDirectAdminPMPM,    
                            PDIndirectAdminExp = expense.PDIndirectAdminPMPM,    
                            PDQualityAdminExp = expense.PDQualityAdminPMPM,    
                            PDTaxesAndFeesAdminExp = expense.PDTaxesAndFeesAdminPMPM,    
                            WS4And6Totals.InsurerFee,    
                            OSB.OSBNetMedicalExpenses,    
                            OSB.OSBNonBenefitExpenses,    
                            OSB.OSBPremium,    
                            OSB.OSBMembermonths,
							@Significance
                        FROM dbo.SavedForecastSetup SFS WITH (NOLOCK)    
						LEFT JOIN dbo.SavedPlanInfo SPI WITH (NOLOCK)
							ON SFS.PlanInfoID = SPI.PlanInfoID    
                        INNER JOIN dbo.SavedMarketInfo lmh WITH (NOLOCK)
							ON lmh.ActuarialMarketID = SPI.ActuarialMarketID    
                        INNER JOIN  dbo.SavedRegionInfo srh WITH (NOLOCK)
							ON srh.ActuarialRegionID = lmh.ActuarialRegionID    
                        INNER JOIN dbo.AdminUserHeader Contact WITH (NOLOCK)
							ON Contact.UserID = SFS.ContactID    
                        LEFT JOIN dbo.AdminUserHeader SecondaryContact WITH (NOLOCK)
							ON SecondaryContact.UserID = SFS.SecondaryContactID    
                        INNER JOIN dbo.AdminUserHeader Certifying WITH (NOLOCK)
							ON SFS.CertifyingActuaryUserID = Certifying.UserID    
                        INNER JOIN dbo.SavedPlanDetail spd WITH (NOLOCK)
							ON spd.ForecastID = SFS.ForecastID    
						   AND spd.MARatingOptionID = 1    
                        LEFT JOIN dbo.Benefits_SavedBenefitOption MOOPDet WITH (NOLOCK)
							ON MOOPDet.PlanInfoID = SFS.PlanInfoID
						   AND MOOPDet.BenefitOptionID = SFS.BenefitOptionID
                        INNER JOIN dbo.LkpProductType cpt WITH (NOLOCK)
							ON cpt.ProductTypeID = SPI.ProductTypeID    
                        INNER JOIN dbo.fnAppGetMABPTWS4Expenses(@XForecastID) WS4And6Totals 
							ON SFS.ForecastID = WS4And6Totals.ForecastID  

                        INNER JOIN dbo.fnGetMARebateAllocation(@XForecastID) Rebate  
							ON SFS.ForecastID = Rebate.ForecastID   
                        INNER JOIN dbo.SavedPlanAssumptions spa WITH (NOLOCK)
							ON spa.ForecastID = SFS.ForecastID    
						INNER JOIN dbo.LkpPlanIntention lpi WITH (NOLOCK)
							ON lpi.PlanIntentionID = SFS.PlanIntentionID    
                        LEFT JOIN @AddedBenefits added 
							ON added.ForecastID = SFS.ForecastID    
                        LEFT JOIN @WS3MOOP WS3MOOP 
							ON SFS.ForecastID = WS3MOOP.ForecastID    
                        LEFT JOIN @OSB OSB --2014 WS7 Section IV    
                            ON SFS.ForecastID = OSB.ForecastID    
                        LEFT JOIN dbo.LkpExtCMSMARegionHeader MR WITH (NOLOCK)
							ON MR.MARegionID = SPI.MARegionID    
                        INNER JOIN dbo.CalcPlanAdminBlend expense WITH (NOLOCK)
							ON expense.ForecastID = SFS.ForecastID, 
						@WS1 WS1,
                        dbo.fnGetBaseAdminAndRevenueCombined(@XForecastID, 1) WS1Sec6,    
                        dbo.fnGetCMSContractQualityStars(@XForecastID) stars
                        WHERE SFS.ForecastID = @XForecastID
						  AND SFS.IsLiveIndex = 1
            END    
        END    
        RETURN    
    END
GO
