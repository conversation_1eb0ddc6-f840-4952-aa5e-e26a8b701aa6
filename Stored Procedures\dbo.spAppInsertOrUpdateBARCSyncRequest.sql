SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- =============================================          
-- Author:  Deepthi Thiyagu         
-- Create date: 2020-Mar-20    
-- Description:  spAppInsertOrUpdateBARCSyncRequest    
--          
--          
-- PARAMETERS:          
-- Input:            

-- TABLES:        
-- Read:          

-- Write:          
-- VIEWS:          
--          
-- FUNCTIONS:          
--            
-- STORED PROCS:           


-- $HISTORY             

-- ----------------------------------------------------------------------------------------------------------------------            
-- DATE    VERSION    CHANGES MADE                                                DEVELOPER            
-- ----------------------------------------------------------------------------------------------------------------------            
-- 2020-Mar-20  1     Initial version											Deepthi Thiyagu    
-- 2020-Sep-14  2     Queue logic updated										Surya Murthy    
-- 2021-AUG-17  3     ADDED PARAMETER @AUDITTYPE								Anurodh Pandey   
-- 2024-Mar-05  4     Adding XLOCK for deadlock issue							Surya Murthy   
-- ----------------------------------------------------------------------------------------------------------------------            

-- =============================================               
CREATE PROCEDURE [dbo].[spAppInsertOrUpdateBARCSyncRequest]    
@inputData VARCHAR(max),    
@LastUpdateByID VARCHAR(7),    
@IsInsert BIT,    
@AuditType VARCHAR(100),   
@Result INT OUTPUT     
AS    
BEGIN     

 SET NOCOUNT ON    
    SET XACT_ABORT ON    
 BEGIN TRY    
 BEGIN TRANSACTION;    
 SAVE TRANSACTION SPBegin;    
 BEGIN    
  declare @isTempTableExist bit = (SELECT  OBJECT_ID ('tempdb..#temp_division_region_data'));    
  IF (@isTempTableExist) IS NOT NULL    
  Drop table #temp_division_region_data    
  SELECT  val as divregval into #temp_division_region_data FROM dbo.Trend_fnCalcStringToTable (@inputData, ',', 1);    
  IF(@IsInsert = 1)    
  BEGIN      
  insert into dbo.SavedBARCSyncQueuedRequest WITH(XLOCK) (InputData,LastUpdateByID,Audittype) select divregval, @LastUpdateByID,@AuditType from #temp_division_region_data;       
  SET @Result= (SELECT max(RequestID) from dbo.SavedBARCSyncQueuedRequest)    
  END    

  ELSE    
  BEGIN    
  delete from dbo.SavedBARCSyncQueuedRequest WITH(XLOCK) where InputData in (select * from #temp_division_region_data) AND Audittype=@AuditType;    
  SET @Result=0    
  END     
 END    
 COMMIT TRANSACTION;    

 END TRY    
 BEGIN CATCH    
 SET @Result=0    
    DECLARE @ErrorMessage NVARCHAR(4000);      
    DECLARE @ErrorSeverity INT;      
    DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000);     
    DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()    
    DECLARE @user VARCHAR(13) = USER;    
   SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState

 as varchar)    
   RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)    

  ---Insert into app log for logging error------------------    
   Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@user    
   ROLLBACK TRANSACTION SPBegin;     
 END CATCH;    
END;
GO
