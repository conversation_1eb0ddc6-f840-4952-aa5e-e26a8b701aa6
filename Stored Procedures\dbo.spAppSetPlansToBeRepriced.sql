SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
-- ----------------------------------------------------------------------------------------------------------------------      
-- PROCEDURE NAME: [dbo].[spAppSetPlansToBeRepriced]      
--      
-- AUTHOR: Surya <PERSON>rthy  
--      
-- CREATED DATE: 2023-SEP-22     
--      
-- DESCRIPTION: Update reprice status in SavedForecastSetup  
--      
-- PARAMETERS:      
-- Input:      
-- 
-- TABLES:       
-- Read: SavedForecastSetup      
--         
-- Write:      
--      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:      
--        
-- $HISTORY       
-- ----------------------------------------------------------------------------------------------------------------------      
-- DATE				VERSION				CHANGES MADE						DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------       
-- 2023-SEP-22			1				Initial Version						Surya Murthy
-- 2023-Nov-20			2				Added Internal Parameter, NOLOCK	Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [dbo].[spAppSetPlansToBeRepriced]  
@ForecastID INT
AS    
BEGIN  

DECLARE @XForecastID INT = @ForecastID 

UPDATE dbo.SavedForecastSetup SET IsToReprice=1 WHERE ForecastID=@XForecastID;

SELECT IsToReprice FROM dbo.SavedForecastSetup WITH (NOLOCK) WHERE ForecastID=@XForecastID;
END
GO
