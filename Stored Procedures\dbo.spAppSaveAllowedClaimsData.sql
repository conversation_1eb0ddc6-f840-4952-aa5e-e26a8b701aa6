SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


-- =============================================      
-- Author:  Kiran Pant  
-- Create date: 10-06-2019
-- Description:  Get Allowed Claims data
--      
--      
-- PARAMETERS:      
-- Input:        
    
-- TABLES:      
-- Read:      
-- Write:      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:       
     

-- $HISTORY         

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION   CHANGES MADE                                              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 2019-June-10  1		  Initial version.											Kiran Pant
-- 2019-Sept-30  2        Flag isToReprice                                          Brent Osantowski


-- ----------------------------------------------------------------------------------------------------------------------        
         
CREATE PROCEDURE [dbo].[spAppSaveAllowedClaimsData]
  @ForecastID INT ,
  @CPDID smallint,
  @LastUpdateByID varchar(7),
  @MessageFromBackend NVARCHAR(500) OUTPUT,
  @Result BIT OUT,
  @CheckValidation BIT
AS
    BEGIN   
        BEGIN TRANSACTION;
        BEGIN TRY    
      
	  IF(@CheckValidation = 0)
	  BEGIN

	  update SavedForecastSetup
	  set CPDID=@CPDID,IsToReprice = 1, LastUpdateByID=@LastUpdateByID,LastUpdateDateTime=getdate()
	  where ForecastID= @ForecastID


set @Result=1
  SET @MessageFromBackend='';
END
       COMMIT TRANSACTION;
        END TRY
        BEGIN CATCH
				 SET @Result = 0;
			SET @MessageFromBackend='Scenario save failed. Please try again <NAME_EMAIL>.' ;
			 DECLARE @ErrorMessage NVARCHAR(4000);  
			 DECLARE @ErrorSeverity INT;  
			 DECLARE @ErrorState INT;DECLARE @ErrorException NVARCHAR(4000); 
			 DECLARE @errSrc varchar(max) =ISNULL( ERROR_PROCEDURE(),'SQL'),  @currentdate datetime=getdate()

			SELECT @ErrorMessage=ERROR_MESSAGE(),@ErrorSeverity = ERROR_SEVERITY(), @ErrorState = ERROR_STATE(),@ErrorException='Line Number :'+ cast(ERROR_LINE() as varchar)+' .Error Severity :'+ cast(@ErrorSeverity as varchar)+' .Error State :'+ cast(@ErrorState as varchar)
			RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)
			
			ROLLBACK TRANSACTION; 

			---Insert into app log for logging error------------------
			Exec spAppAddLogEntry @currentdate,'','ERROR',@errSrc,@ErrorMessage,@ErrorException,@LastUpdateByID
            
        END CATCH; 
		END;
GO
