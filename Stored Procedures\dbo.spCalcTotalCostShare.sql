SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME:	spCalcTotalCostShare
--
-- CREATOR:			Franklin Fu
--
-- CREATED DATE:	2024-AUG-19
--
-- DESCRIPTION:		The SP calculates total cost share by plan. 
--					It is calculated separately for IN vs OON. 
--					The total PMPMs and ratios are used later in the pricing model.
--		
-- PARAMETERS:
--  Input  :		@ForecastID
--					@IsBenefitYearCurrentYear
--					@LastUpdateByID
--
--  Output :		NONE
--
-- TABLES : 
--	Read :			CalcDeductibleFactors		(deductible factors)
--					CalcEffectiveCoinsurance	(effective coinsurance values)
--					CalcMOOPFactors				(MOOP factors)
--					SavedPlanBenefitDetail		(deductible exclusions)
--					LkpIntMOOPCategoryDetail
--
--  Write:			CalcTotalCostShare
--
-- VIEWS: 
--	Read:			NONE
--
-- FUNCTIONS:		fnGetSafeDivisionResult
--					fnGetBidYear
--
-- STORED PROCS:	NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- 2024-AUG-19		1           Initial version                                                     Franklin Fu
-- 2024-NOV-26		2			Updated header and column names as part of Deductible Pricing		Jake Lewis
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE PROCEDURE [dbo].[spCalcTotalCostShare]
    (
    @ForecastID               INT
   ,@IsBenefitYearCurrentYear BIT
   ,@LastUpdateByID           CHAR(7))

AS

    BEGIN

        SET NOCOUNT ON; --Suppress row count messages
        SET ANSI_WARNINGS OFF; --Suppress ANSI warning messages 

        BEGIN TRY

            DECLARE @tranCount INT = @@TranCount; --Current transaction count

            BEGIN TRANSACTION transaction_spCTC;

            -- Declare Variables
            DECLARE @XForecastID               INT      = @ForecastID
                   ,@XIsBenefitYearCurrentYear BIT      = @IsBenefitYearCurrentYear
                   ,@XLastUpdateByID           CHAR(7)  = @LastUpdateByID
                   ,@LastDateTime              DATETIME = GETDATE ()
                   ,@PlanYearID                SMALLINT = dbo.fnGetBidYear ();

            DECLARE @INIPDeductibleFactor     DECIMAL(20, 12)
                   ,@INSNFDeductibleFactor    DECIMAL(20, 12)
                   ,@INOtherDeductibleFactor  DECIMAL(20, 12)
                   ,@OONIPDeductibleFactor    DECIMAL(20, 12)
                   ,@OONSNFDeductibleFactor   DECIMAL(20, 12)
                   ,@OONOtherDeductibleFactor DECIMAL(20, 12)
                   ,@INIPMOOPFactor           DECIMAL(27, 20)
                   ,@INSNFMOOPFactor          DECIMAL(27, 20)
                   ,@INOtherMOOPFactor        DECIMAL(27, 20)
                   ,@OONIPMOOPFactor          DECIMAL(27, 20)
                   ,@OONSNFMOOPFactor         DECIMAL(27, 20)
                   ,@OONOtherMOOPFactor       DECIMAL(27, 20);

            -- Set Variables
            SELECT  @INIPDeductibleFactor = INIPDeductibleFactor
                   ,@INSNFDeductibleFactor = INSNFDeductibleFactor
                   ,@INOtherDeductibleFactor = INOtherDeductibleFactor
                   ,@OONIPDeductibleFactor = OONIPDeductibleFactor
                   ,@OONSNFDeductibleFactor = OONSNFDeductibleFactor
                   ,@OONOtherDeductibleFactor = OONOtherDeductibleFactor
            FROM    dbo.CalcDeductibleFactors WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear;

            SELECT  @INIPMOOPFactor = INIPMOOPFactor
                   ,@INSNFMOOPFactor = INSNFMOOPFactor
                   ,@INOtherMOOPFactor = INOtherMOOPFactor
                   ,@OONIPMOOPFactor = OONIPMOOPFactor
                   ,@OONSNFMOOPFactor = OONSNFMOOPFactor
                   ,@OONOtherMOOPFactor = OONOtherMOOPFactor
            FROM    dbo.CalcMOOPFactors WITH (NOLOCK)
            WHERE   ForecastID = @XForecastID
                    AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear;

            --Create table to match BenefitCategoryID with MOOPCategoryID
            DROP TABLE IF EXISTS #tempLkpIntMOOPCategoryDetail;
            CREATE TABLE #tempLkpIntMOOPCategoryDetail
                (MOOPCategoryID    INT
                ,BenefitCategoryID INT);
            INSERT INTO #tempLkpIntMOOPCategoryDetail
                (MOOPCategoryID
                ,BenefitCategoryID)
            SELECT  DISTINCT
                    MOOPCategoryID
                   ,BenefitCategoryID
            FROM    dbo.LkpIntMOOPCategoryDetail WITH (NOLOCK)
            WHERE   MOOPCategoryID IN (1, 2, 3);

            --Split DeductibleFactor by MOOPCategoryID
            DROP TABLE IF EXISTS #tempMoopFactorsByCategory;
            CREATE TABLE #tempMoopFactorsByCategory
                (INDeductibleFactor  DECIMAL(20, 12)
                ,OONDeductibleFactor DECIMAL(20, 12)
                ,INMOOPFactor        DECIMAL(27, 20)
                ,OONMOOPFactor       DECIMAL(27, 20)
                ,BenefitCategoryID   INT);
            INSERT INTO #tempMoopFactorsByCategory
                (INDeductibleFactor
                ,OONDeductibleFactor
                ,INMOOPFactor
                ,OONMOOPFactor
                ,BenefitCategoryID)
            SELECT  INDeductibleFactor = CASE MOOPCategoryID WHEN 1 THEN @INIPDeductibleFactor
                                                             WHEN 2 THEN @INSNFDeductibleFactor
                                                             WHEN 3 THEN @INOtherDeductibleFactor
                                                             ELSE 0 END
                   ,OONDeductibleFactor = CASE MOOPCategoryID WHEN 1 THEN @OONIPDeductibleFactor
                                                              WHEN 2 THEN @OONSNFDeductibleFactor
                                                              WHEN 3 THEN @OONOtherDeductibleFactor
                                                              ELSE 0 END
                   ,INMOOPFactor = CASE MOOPCategoryID WHEN 1 THEN @INIPMOOPFactor
                                                       WHEN 2 THEN @INSNFMOOPFactor
                                                       WHEN 3 THEN @INOtherMOOPFactor
                                                       ELSE 0 END
                   ,OONMOOPFactor = CASE MOOPCategoryID WHEN 1 THEN @OONIPMOOPFactor
                                                        WHEN 2 THEN @OONSNFMOOPFactor
                                                        WHEN 3 THEN @OONOtherMOOPFactor
                                                        ELSE 0 END
                   ,BenefitCategoryID
            FROM    #tempLkpIntMOOPCategoryDetail;

            --Calculate Total Cost Share By Benefit Category
            DELETE  FROM dbo.CalcTotalCostShare
            WHERE   ForecastID = @XForecastID
                    AND IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear;

            INSERT INTO dbo.CalcTotalCostShare
                (PlanYearID
                ,ForecastID
                ,IsBenefitYearCurrentYear
                ,BenefitCategoryID
                ,INAllowed
                ,INDeductibleFactor
                ,INEffectiveCoinsurance
                ,INMOOPFactor
                ,INTotalCostShare
                ,OONAllowed
                ,OONDeductibleFactor
                ,OONEffectiveCoinsurance
                ,OONMOOPFactor
                ,OONTotalCostShare
                ,TotalCostShare
                ,BenefitCostShareRatio
                ,LastUpdateByID
                ,LastDateTime)
            SELECT      PlanYearID = @PlanYearID
                       ,SPBD.ForecastID
                       ,SPBD.IsBenefitYearCurrentYear
                       ,SPBD.BenefitCategoryID
                       ,EC.INAllowed
                       ,INDeductibleFactor = CASE WHEN SPBD.INDedApplies = 1 THEN moop.INDeductibleFactor ELSE 0 END
                       ,EC.INEffectiveCoinsurance
                       ,INMOOPFactor = moop.INMOOPFactor
                       ,INTotalCostShare = EC.INAllowed
                                           * CASE WHEN SPBD.INDedApplies = 1 THEN moop.INDeductibleFactor ELSE 0 END
                                           + EC.INAllowed
                                           * (1 - CASE WHEN SPBD.INDedApplies = 1 THEN moop.INDeductibleFactor ELSE 0 END)
                                           * moop.INMOOPFactor * EC.INEffectiveCoinsurance
                       ,EC.OONAllowed
                       ,OONDeductibleFactor = CASE WHEN SPBD.OONDedApplies = 1 THEN moop.OONDeductibleFactor ELSE 0 END
                       ,EC.OONEffectiveCoinsurance
                       ,OONMOOPFactor = moop.OONMOOPFactor
                       ,OONTotalCostShare = EC.OONAllowed
                                            * CASE WHEN SPBD.OONDedApplies = 1 THEN moop.OONDeductibleFactor ELSE 0 END
                                            + EC.OONAllowed
                                            * (1
                                               - CASE WHEN SPBD.OONDedApplies = 1 THEN moop.OONDeductibleFactor ELSE 0 END)
                                            * moop.OONMOOPFactor * EC.OONEffectiveCoinsurance
                       ,TotalCostShare = EC.INAllowed
                                         * CASE WHEN SPBD.INDedApplies = 1 THEN moop.INDeductibleFactor ELSE 0 END
                                         + EC.INAllowed
                                         * (1 - CASE WHEN SPBD.INDedApplies = 1 THEN moop.INDeductibleFactor ELSE 0 END)
                                         * moop.INMOOPFactor * EC.INEffectiveCoinsurance + EC.OONAllowed
                                         * CASE WHEN SPBD.OONDedApplies = 1 THEN moop.OONDeductibleFactor ELSE 0 END
                                         + EC.OONAllowed
                                         * (1 - CASE WHEN SPBD.OONDedApplies = 1 THEN moop.OONDeductibleFactor ELSE 0 END)
                                         * moop.OONMOOPFactor * EC.OONEffectiveCoinsurance
                       ,BenefitCostShareRatio = dbo.fnGetSafeDivisionResult (
                                                EC.INAllowed
                                                * CASE WHEN SPBD.INDedApplies = 1 THEN moop.INDeductibleFactor ELSE 0 END
                                                + EC.INAllowed
                                                * (1
                                                   - CASE WHEN SPBD.INDedApplies = 1 THEN moop.INDeductibleFactor ELSE 0 END)
                                                * moop.INMOOPFactor * EC.INEffectiveCoinsurance + EC.OONAllowed
                                                * CASE WHEN SPBD.OONDedApplies = 1 THEN moop.OONDeductibleFactor ELSE 0 END
                                                + EC.OONAllowed
                                                * (1
                                                   - CASE WHEN SPBD.OONDedApplies = 1 THEN moop.OONDeductibleFactor ELSE 0 END)
                                                * moop.OONMOOPFactor * EC.OONEffectiveCoinsurance
                                               ,(EC.INAllowed + EC.OONAllowed))
                       ,LastUpdateByID = @XLastUpdateByID
                       ,LastDateTime = @LastDateTime
            FROM        dbo.SavedPlanBenefitDetail SPBD WITH (NOLOCK)
           INNER JOIN   dbo.CalcEffectiveCoinsurance EC WITH (NOLOCK)
                   ON SPBD.ForecastID = EC.ForecastID
                      AND   SPBD.BenefitCategoryID = EC.BenefitCategoryID
                      AND   EC.IsBenefitYearCurrentYear = SPBD.IsBenefitYearCurrentYear
           INNER JOIN   #tempMoopFactorsByCategory moop
                   ON moop.BenefitCategoryID = SPBD.BenefitCategoryID
            WHERE       SPBD.ForecastID = @XForecastID
                        AND SPBD.IsBenefitYearCurrentYear = @XIsBenefitYearCurrentYear
                        AND SPBD.IsLiveIndex = CASE WHEN @XIsBenefitYearCurrentYear = 0 THEN 1 ELSE 0 END
                        AND SPBD.BenefitOrdinalID = 1;  --Needed to eliminate double counting, if deductible exclusions ever varied by BenefitOrdinalID within a Benefit Category we would need to rework this

            COMMIT TRANSACTION transaction_spCTC;

        END TRY
        BEGIN CATCH

            IF (@@TranCount > @tranCount) --Check if transaction in TRY block was not closed
                BEGIN
                    ROLLBACK TRANSACTION transaction_spCTC;
                END;
        END CATCH;

    END;
GO
