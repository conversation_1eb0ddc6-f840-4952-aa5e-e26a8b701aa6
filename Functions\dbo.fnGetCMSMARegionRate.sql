SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO


/****** Object:  UserDefinedFunction [dbo].[fnGetCMSMARegionRate]   ******/

-- PROCEDURE NAME: 
--
-- CREATOR: <PERSON><PERSON> Chen 
--
-- CREATED DATE: Dec-06-2010
-- HEADER UPDATED: Dec-06-2010
--
-- DESCRIPTION: Returns CMS region rate for a specific contract or the default rate by region if it is a new contract.
--
-- PARAMETERS:
--	Input:
--	Output:
--
-- TABLES:
--	Read:
--        PerExtCMSMARegionRatebookByContract
--        PerExtCMSMARegionRatebookByDefault
--        SavedPlanHeader
--
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE															DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- Dec-06-2010      1           Initial Version.														Jiao Chen
-- Jan-05-2011		2			Rename from fnGetCMSMARegionRateRPPO to fnGetCMSMARegionRate			Jiao Chen
-- Feb-08-2011		3			Removed ForecastID, Added MARegion to the return table					Jiao Chen
-- Mar-17-2011		4			Returns blank if plan is unavailable form ratebook						Jiao Chen
-- Feb-06-2018		5			Added MARegionID check to both select statements for RPPO additions		Chris Fleming
-- Aug-03-2023		6			Added Internal Parameter, Schema and NOlock								Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnGetCMSMARegionRate] ( @ForecastID INT = NULL )
RETURNS @Result TABLE
    (
      MARegion TEXT ,
      MARegionID INT NOT NULL ,
      MAregionRate DECIMAL(8, 4) NOT NULL
    )
AS
    BEGIN
         
		 DECLARE @XForecastID INT = @ForecastID 
		  
        IF EXISTS ( SELECT  1
                    FROM    dbo.SavedPlanHeader SPH WITH (NOLOCK)
                            INNER JOIN dbo.PerExtCMSMARegionRatebookByContract RRC WITH (NOLOCK)
							ON	RRC.ContractNumber = SPH.ContractNumber
								AND RRC.MARegionID = SPH.MARegionID
                    WHERE   SPH.ForecastID = @XForecastID )
            BEGIN
                INSERT  @Result
                        SELECT  RRC.MARegion ,
                                RRC.MARegionID ,
                                RRC.MARegionRate
                        FROM    dbo.SavedPlanHeader SPH WITH (NOLOCK)
                                INNER JOIN dbo.PerExtCMSMARegionRatebookByContract RRC WITH (NOLOCK)
								ON	RRC.ContractNumber = SPH.ContractNumber
									AND RRC.MARegionID = SPH.MARegionID
                        WHERE   SPH.ForecastID = @XForecastID
            END

        RETURN
    END
GO
