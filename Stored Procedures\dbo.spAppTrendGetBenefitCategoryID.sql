SET QUOTED_IDENTIFIER ON
GO
SET <PERSON><PERSON>_NULLS ON
GO
         
      
-- ----------------------------------------------------------------------------------------------------------------------      
-- PROCEDURE NAME: spAppTrendGetBenefitCategoryID      
--      
-- AUTHOR: Ramande<PERSON>ni     
--      
-- CREATED DATE: 2020-March-17      
--      
-- DESCRIPTION: Procedure responsible for Fetching BenefitCategoryID .      
--                
      
-- TYPE: New      
--      
-- PARAMETERS:      
-- Input:      
--       
      
      
      
-- TABLES:       
-- Read:LkpIntBenefitCategory      
--     
--        
      
-- Write:      
--           
      
-- VIEWS:      
--      
-- FUNCTIONS:      
--        
-- STORED PROCS:      
--       
       
-- $HISTORY       
-- ----------------------------------------------------------------------------------------------------------------------      
-- DATE    VERSION  CHANGES MADE              DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------      
-- 2020-March-17  1   Initial Version              Ramandeep Saini    
-- ----------------------------------------------------------------------------------------------------------------------      
CREATE PROCEDURE [dbo].[spAppTrendGetBenefitCategoryID]    
AS      
BEGIN      
      
  
  
  SELECT DISTINCT BenefitCategoryID FROM LkpIntBenefitCategory WHERE BenefitCategoryID IS NOT NULL   
      
END      
GO
