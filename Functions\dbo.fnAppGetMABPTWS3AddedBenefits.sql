SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[fnAppGetMABPTWS3AddedBenefits]     ******/

-- FUNCTION NAME: fnAppGetMABPTWS3AddedBenefits
--
-- AUTHOR: <PERSON><PERSON> 
--
-- CREATED DATE: 2010-Mar-20
-- HEADER UPDATED: 2010-Oct-05
--
-- DESCRIPTION: Function responsible for building data that is required to complete WS 3.  
--
-- PARAMETERS:
--	Input:
--	    @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--      LkpExtCMSBidServiceCategory
--      LkpIntAddedBenefitType
--      SavedPlanAddedBenefits
--      SavedPlanHeader
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE	            VERSION     CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Mar-20      1           Initial Version                                                     Sule Dauda
-- 2010-Apr-04	    2			Commented out declaration of credibility.							Casey Sanders
-- 2011-Jan-06      3           Revised for coding standards and naming convention                  Michael Siekerka
-- 2014-July-24		33			Changing INCostShareDesc & OONCostShareDesc to data type 
--									VARCHAR(MAX) to prevent data truncation errors down the 
--									line that occurs in fnAppGetProng1TestNonSQS					Nick Koesters				
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetMABPTWS3AddedBenefits]
(
    @ForecastID INT
)
RETURNS @Results TABLE 
(
    ForecastID INT,
    ServiceCategory CHAR(4),
    SubServiceCategory VARCHAR(200),
    MeasUnitCode VARCHAR(10),
    INEffectiveDeductible DECIMAL(18, 10),
    INUtilOrPMPM DECIMAL(18, 10),
	INAllowed  DECIMAL(18, 10),
    INCostShareDesc VARCHAR(MAX),
    PreMOOPINEffCopayCoins DECIMAL(18, 10),
    INEffCopayCoins DECIMAL(18, 10),
    INPreMOOPCostShare DECIMAL(18, 10),
	INAddedBenefitUtilization DECIMAL(18, 10),
	OONAddedBenefitUtilization DECIMAL(18, 10),  
    INMOOPCostShare DECIMAL(18, 10),
    INCostShare DECIMAL(18, 10),
    OONEffectiveDeductible DECIMAL(18, 10),
    OONPreMOOPCostShare DECIMAL(18, 10),
    OONMOOPCostShare DECIMAL(18, 10),
    OONCostShare DECIMAL(18, 10),
    OONCostShareDesc VARCHAR(MAX),
    PreMOOPOONEffCopayCoins DECIMAL(18, 10),
    OONEffCopayCoins DECIMAL(18, 10),
	OONUtilOrPMPM DECIMAL(18, 10),
	OONAllowed  DECIMAL(18, 10)
) AS
BEGIN
    INSERT @Results
	SELECT
		spab.ForecastID,
		bsc.CostShareServiceCategoryCode,
		SubServiceCategory,
		MeasUnitCode = UtilType,
		INEffectiveDeductible = NULL,
		INUtilOrPMPM = SUM(ISNULL(spab.INAddedBenefitUtilization,0)),
		INAllowed = SUM(ISNULL(spab.INAddedBenefitAllowed,0)),
		INCostShareDesc = ISNULL(MAX(spab.INAddedBenefitDescription),'Missing Description'),
		PreMOOPINEffCopayCoins = NULL,
		INEffCopayCoins = NULL,
		INPreMOOPCostShare = SUM(ISNULL(spab.INAddedBenefitCostShare,0)),
		INAddedBenefitUtilization = SUM(ISNULL(spab.INAddedBenefitUtilization,0)),
		OONAddedBenefitUtilization = SUM(ISNULL(spab.OONAddedBenefitUtilization,0)),
		INMOOPCostShare = SUM(ISNULL(spab.INAddedBenefitCostShare,0)),
		INCostShare = SUM(ISNULL(spab.INAddedBenefitCostShare,0)),
		OONEffectiveDeductible = 0,
		OONPreMOOPCostShare = SUM(ISNULL(spab.OONAddedBenefitCostShare,0)),
		OONMOOPCostShare = SUM(ISNULL(spab.OONAddedBenefitCostShare,0)),
		OONCostShare = SUM(ISNULL(spab.OONAddedBenefitCostShare,0)),
		OONCostShareDesc =
			CASE WHEN ISNULL(MAX(spab.OONAddedBenefitDescription),'Missing Description') = 'Missing Description' 
			        AND sph.PlanTypeID in (2,3) --'LPPO, RPPO
				THEN ISNULL(MAX(spab.INAddedBenefitDescription),'Missing Description')
				ELSE MAX(spab.OONAddedBenefitDescription)
			END,
		PreMOOPOONEffCopayCoins = NULL,
		OONEffCopayCoins = NULL,
		OONUtilOrPMPM = SUM(ISNULL(spab.OONAddedBenefitUtilization,0)),
		OONAllowed = SUM(ISNULL(spab.OONAddedBenefitAllowed,0))	
	FROM SavedPlanAddedBenefits spab
	INNER JOIN SavedPlanHeader sph
		ON spab.ForecastID = sph.ForecastID
	INNER JOIN LkpIntAddedBenefitType abt
		ON spab.AddedBenefitTypeID = abt.AddedBenefitTypeID
	INNER JOIN LkpExtCMSBidServiceCategory bsc
		ON spab.BidServiceCatID = bsc.BidServiceCategoryID
	WHERE spab.ForecastID = @ForecastID
	    AND spab.IsHidden = 0
	GROUP BY
		spab.ForecastID,
		sph.PlanTypeID,
		bsc.CostShareServiceCategoryCode,
		SubServiceCategory,
		bsc.UtilType,
		spab.INAddedBenefitDescription,
		spab.INAddedBenefitUtilization,
		spab.INAddedBenefitCostShare,
		spab.OONAddedBenefitCostShare,
		abt.AddedBenefitTypeID

    RETURN
END
GO
