SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

/****** Object:  UserDefinedFunction [dbo].[fnDFMembershipCombined]   ******/

-- ---------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: fnDFMembershipCombined
--
-- AUTHOR: <PERSON> Casey
--
-- CREATED DATE: 2010-Jun-04
-- HEADER UPDATED: 2011-Feb-07
--
-- DESCRIPTION: This function combines multiple C&U IDs, to create a new DFMembership table
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--		@MARatingOptionID
--	Output:
--
-- TABLES:
--	Read:
--      SavedForecastSetup
--		SavedPlanDFSummary
--
--	Write:
--
-- VIEWS:
--		vwSavedDFMembership
--
-- FUNCTIONS:
--
-- STORED PROCS:
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE                                             			DEVELOPER
-- ---------------------------------------------------------------------------------------------------------------------
-- 2010-Jun-03      1			Initial version.													Joe Casey
-- 2010-Dec-06      2			Removed PlanVersion													Michael Siekerka
-- 2011-Feb-07		3			Added join to SavedCUHeader											Joe Casey
-- 2020-Jun-29      4           Backend Alignment and Restructuring                                 Keith Galloway
-- 2024-May-05      5			Added NOLOCK Table Hint												Kiran Kola
-- ---------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnDFMembershipCombined]
(
	@ForecastID INT,
	@MARatingOptionID TINYINT
)
RETURNS @Results TABLE
(
	ForecastID INT NOT NULL,
	MARatingOptionID TINYINT NOT NULL,
	DemogIndicator SMALLINT NOT NULL,
	MemberMonthCount INT NOT NULL
	PRIMARY KEY (ForecastID, MARatingOptionID, DemogIndicator)
)
AS
BEGIN

    INSERT INTO @Results
	SELECT
		@ForecastID AS ForecastiD,
		@MARatingOptionID AS MARatingOptionID,
		m.DemogIndicator,
		MemberMonthCount = SUM(m.MemberMonths)
	FROM dbo.SavedForecastSetup SFS  WITH (NOLOCK)
	INNER JOIN dbo.SavedPlanDFSummary DFS  WITH (NOLOCK)
		ON SFS.ForecastID = DFS.ForecastID
	INNER JOIN dbo.vwSavedDFMembership m  WITH (NOLOCK)
		ON DFS.PlanInfoID = m.PlanInfoID
		AND SFS.DFVersionID = m.DFVersionID
	WHERE SFS.ForecastID = @ForecastiD
		AND DFS.MARatingOptionID = @MARatingOptionID
	GROUP BY m.DemogIndicator

RETURN
END
GO
