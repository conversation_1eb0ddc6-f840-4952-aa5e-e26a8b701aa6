SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal
--
-- CREATOR: <PERSON> Lewis
--
-- CREATED DATE: MAR-06-2020
--
-- DESCRIPTION:   For specified plans, this procedure applies plan mapping overrides (if they exist in Trend_ProjProcess_CalcNotPlanLevel_Trends) 
--				  from Trend_ProjProcess_CalcNotPlanLevel_PlanMappingOverrides,and otherwise applies the default from 
--				  Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault.  If there is an error where an override is attempted, but that override
--				  does not exist in Trend_ProjProcess_CalcNotPlanLevel_Trends, an error message is generated in OverrideErrorMesssage.  
--              
--              
-- PARAMETERS:
--  Input  :	@PlanList
--				@LastUpdateByID
--
--  Output : NONE
--
-- TABLES : Read :  Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault
--					Trend_ProjProcess_CalcNotPlanLevel_PlanMappingOverrides
--					Trend_ProjProcess_CalcNotPlanLevel_Trends
--					
--          Write:  Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal
--                  
--
-- VIEWS: Read: NONE
--
-- FUNCTIONS: Read: Trend_fnCalcStringToTable
--
-- STORED PROCS: Executed: NONE
--
-- $HISTORY 
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DATE            VERSION      CHANGES MADE                                                        DEVELOPER        
-- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MAR-06-2020      1           Initial Version                                                     Jake Lewis
-- NOV-19-2020      2          Include NOLOCK & ROWLOCK                                             Manisha Tyagi
-- DEC-07-2020      3          Batch delete and Temp insert implemented                             Deepthi Thiyagu
-- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[Trend_ProjProcess_spCalcNotPlanLevel_PlanMappingFinal]
@PlanList        VARCHAR(2000) = NULL
,@LastUpdateByID CHAR(13)

AS
    BEGIN
        SET NOCOUNT ON;
        -- Delete from and insert into final output table
		DECLARE @RepCatRowcount INT = 1
		WHILE @RepCatRowcount > 0
			BEGIN
				DELETE TOP (1000) FROM dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal WITH (ROWLOCK)
				WHERE   (PlanInfoID IN (SELECT  Val FROM dbo.Trend_fnCalcStringToTable (@PlanList, ',', 1) )
						 OR @PlanList IS NULL);
				SET @RepCatRowcount = @@ROWCOUNT
			END
		
		IF (SELECT  OBJECT_ID ('tempdb..#TempPlanMappingFinal')) IS NOT NULL
			DROP TABLE #TempPlanMappingFinal;
		
		CREATE TABLE #TempPlanMappingFinal
		([PlanInfoID] [int] ,
		[CPS] [char](13) ,
		[PlanYearID] [int] ,
		[BaseYearID] [int] ,
		[PackageOptionID] [int] ,
		[ComponentVersionID] [int] ,
		[Component] [varchar](50) ,
		[PlanTypeGranularity] [varchar](250) ,
		[PlanTypeGranularityValue] [varchar](500) ,
		[IsOverride] [BIT],
		[OverrideRuleID] [int] ,
		[OverrideDescription] [varchar](100),
		[IsOverrideError] [BIT],
		[OverrideErrorMessage] [varchar](1000),
		[LastUpdateByID] [char](13) ,
		[LastUpdateDateTime] [datetime] )
				
		INSERT INTO #TempPlanMappingFinal
            (PlanInfoID
            ,CPS
            ,PlanYearID
            ,BaseYearID
            ,PackageOptionID
            ,ComponentVersionID
            ,Component
            ,PlanTypeGranularity
            ,PlanTypeGranularityValue
            ,IsOverride
            ,OverrideRuleID
            ,OverrideDescription
            ,IsOverrideError
            ,OverrideErrorMessage
            ,LastUpdateByID
            ,LastUpdateDateTime)
        SELECT      DISTINCT
                    a.PlanInfoID
                   ,a.CPS
                   ,a.PlanYearID
                   ,a.BaseYearID
                   ,a.PackageOptionID
                   ,a.ComponentVersionID
                   ,a.Component
                   ,CASE WHEN b.OverridePlanTypeGranularity = '1.0' THEN b.OverridePlanTypeGranularity
                         WHEN c.PlanTypeGranularity IS NOT NULL THEN c.PlanTypeGranularity
                         ELSE a.PlanTypeGranularity END
                   ,CASE WHEN b.OverridePlanTypeGranularity = '1.0' THEN b.OverridePlanTypeGranularityValue
                         WHEN c.PlanTypeGranularity IS NOT NULL THEN c.PlanTypeGranularityValue
                         ELSE a.PlanTypeGranularityValue END
                   ,CASE WHEN b.OverridePlanTypeGranularity = '1.0'
                              OR c.PlanTypeGranularity IS NOT NULL THEN 1
                         ELSE 0 END
                   ,b.OverrideRuleID
                   ,b.OverrideDescription
                   ,CASE WHEN b.OverridePlanTypeGranularity <> '1.0'
                              AND   c.PlanTypeGranularity IS NULL THEN 1
                         ELSE 0 END
                   ,CASE WHEN b.OverridePlanTypeGranularity <> '1.0'
                              AND   c.PlanTypeGranularity IS NULL THEN
                             CONCAT (
                             'Default assumption used. Override for Component='
                            ,a.Component
                            ,', Granularity='
                            ,b.OverridePlanTypeGranularity
                            ,', GranularityValue='
                            ,b.OverridePlanTypeGranularityValue
                            ,' does not exist.')
                         ELSE NULL END
                   ,@LastUpdateByID
                   ,GETDATE ()
        FROM        dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingDefault  a WITH (NOLOCK) 
        LEFT JOIN   dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingOverrides b WITH (NOLOCK)
               ON b.PlanInfoID = a.PlanInfoID
                  AND   b.Component = a.Component
        LEFT JOIN   dbo.Trend_ProjProcess_CalcNotPlanLevel_Trends c WITH (NOLOCK)
               ON c.Component = b.Component
                  AND   c.PlanTypeGranularity = b.OverridePlanTypeGranularity
                  AND   c.PlanTypeGranularityValue = b.OverridePlanTypeGranularityValue
        WHERE       (a.PlanInfoID IN (SELECT    Val FROM   dbo.Trend_fnCalcStringToTable (@PlanList, ',', 1) )

                     OR @PlanList IS NULL);
		--Finally inserting into main table from temp table
		insert into dbo.Trend_ProjProcess_CalcNotPlanLevel_PlanMappingFinal select * from #TempPlanMappingFinal;
		--End
    END;
GO
