SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
/****** Object:  UserDefinedFunction [dbo].[it_fnGetMedicareCoveredPercents]  ******/

-- ----------------------------------------------------------------------------------------------------------------------
-- FUNCTION NAME: it_fnGetMedicareCoveredPercents
--
-- AUTHOR: <PERSON><PERSON><PERSON>
--
-- CREATED DATE: 2011-Jan-17
--
-- DESCRIPTION: Function responsible for getting Medicare Covered Percents for a given plan index.
--
-- PARAMETERS:
--	Input:
--		@ForecastID
--	Output:
--
-- TABLES: 
--	Read:
--		SavedPlanBenefitDetail
--		LkpIntBenefitCategory
--		LkpIntFinanceCategory
--		SavedPlanHeader      
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--		
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE				VERSION		CHANGES MADE														DEVELOPER		
-- ----------------------------------------------------------------------------------------------------------------------
-- 2011-Jan-17		1			Initial Version														Catalin Tomescu
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[it_fnGetMedicareCoveredPercents]
(
    @ForecastID INT
)
RETURNS TABLE AS  
RETURN
    (
SELECT 
	BenefitCategoryName, 
    FinanceCategorycode, 
    BenefitOrdinalID, 
    PercentCoveredAllowed, 
    PercentCoveredCostShare,
	benefit.BenefitCategoryID AS BenefitCategoryID
FROM SavedPlanBenefitDetail benefit 
INNER JOIN LkpIntBenefitCategory benefitName WITH(NOLOCK) 
    ON benefit.BenefitCategoryID = benefitName.BenefitCategoryID 
INNER JOIN LkpIntFinanceCategory finance WITH(NOLOCK)
    ON benefitName.FinanceCategoryID = finance.FinanceCategoryID 
WHERE 
    ForecastID = @ForecastID
    AND IsBenefitYearCurrentYear = 0
	)
GO
