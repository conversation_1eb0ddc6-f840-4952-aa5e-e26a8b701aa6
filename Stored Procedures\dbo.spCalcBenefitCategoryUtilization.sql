SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- -------------------------------------------------------------------------------------------------------------------------------
-- -------------------------------------------------------------------------------------------------------------------------------
-- PROCEDURE NAME: spCalcBenefitCategoryUtilization
--
-- AUTHOR: <PERSON>
--
-- CREATED DATE: 2010-Sep-08
--
-- DESCRIPTION: This procedure takes the raw utilization data (PerIntBenefitCategoryUtilization)
--                   and creates the N-N tiering data.  It should only be run once per bid season.
--      Example:  Length of Stay    Admit Count     *Note Total Utilization is the sumproduct of these
--                      1               2500            two columns which is 22300
--                      2               2600
--                      3               2200
--                      4               2000
--      To calculate the 1-1 utilization percent, we look at all the stays which have at least one day
--          (that is all of them) so we sum up 1 day times the admits and divide by the total.  Results:
--                      1-1         9300/22300  =   .417
--                      2-2         6800/22300  =   .305
--                      3-3         4200/22300  =   .188
--                      4-4         2000/22300  =   .090
--      We can then sum over these values to get the X-Y utilization so 2-3 utilization percet = .305 + .188 = .493
--          which is done by fnBenefitCategoryUtilizationFactor
--
-- PARAMETERS: None
--
-- RETURNS: None
--
-- TABLES:
--    Read:
--		PerIntBenefitCategoryUtilization
--        
--    Write:
--		CalcBenefitCategoryUtilization
--
-- VIEWS: None
--
-- STORED PROCS: None
--
-- $HISTORY 
-- -------------------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                                DEVELOPER
-- -------------------------------------------------------------------------------------------------------------------------------
-- 2010-Sep-09      1           Initial version                                                             Michael Siekerka
-- 2010-Sep-09      2           Removed cap on length of stay (was 100 days).  This will need to be         Michael Siekerka
--                              accounted for in PerIntBenefitCategoryUtilization when it is populated.
-- 2010-Dec-16      3           Revised code for table structure change                                     Michael Siekerka
-- 2011-Feb-14      4           Added ISNULL to AdmitCount                                                  Michael Siekerka
-- 2011-Jun-14		5			Changed PlanYearId to return SMALLINT instead of INT						Bobby Jaegers
-- 2024-Feb-14      6           Add Audit Columns and user parameter                                        Latoya Garvey

-- -------------------------------------------------------------------------------------------------------------------------------
CREATE PROCEDURE [dbo].[spCalcBenefitCategoryUtilization]
AS
BEGIN
    --Make sure we're starting with an empty table, otherwise we will get duplication errors
    DELETE FROM dbo.CalcBenefitCategoryUtilization

    --Create a temp table to populate any missing lenghts of stay (otherwise percentages won't add up to 100%)
    DECLARE @TempTable TABLE
    (
        PlanYearID SMALLINT,
        BenefitCategoryID SMALLINT,
        LengthOfStay INT,
        AdmitCount INT
    )

    --Populate the temp table with utilization data
    INSERT INTO @TempTable
    SELECT PlanYearID, BenefitCategoryID, LengthOfStay, AdmitCount FROM PerIntBenefitCategoryUtilization

    --Use a cursor to fill the holes in the temp table (If admits weren't entered for a day range in the
    --  source data table, we will end up missing that range in the output)
    DECLARE @PlanYearID SMALLINT,
            @CurBCID SMALLINT ,
            @Index INT,
            @Max INT

    SELECT @PlanYearID = dbo.fnGetBidYear()

  
  DECLARE cur CURSOR FOR
    SELECT DISTINCT(BenefitCategoryID) FROM @TempTable

    OPEN cur 
    FETCH NEXT 
    FROM cur 
    INTO @CurBCID WHILE @@FETCH_STATUS = 0 BEGIN      

        SET @Index = 1
        SELECT @Max = MAX(LengthOfStay) FROM @TempTable WHERE BenefitCategoryID = @CurBCID
        WHILE @Index <= @Max
        BEGIN
            IF NOT EXISTS(
                SELECT 1 FROM @TempTable
                WHERE BenefitCategoryID = @CurBCID
                AND LengthOfStay = @Index)
            BEGIN
                INSERT INTO @TempTable
                VALUES(@PlanYearID, @CurBCID, @Index, 0)
            END
            SET @Index = @Index + 1
        END
    FETCH NEXT FROM cur INTO @CurBCID
    END CLOSE cur 
    DEALLOCATE cur

    --Calculate N-N range utilization for N=1 to 100
    INSERT INTO CalcBenefitCategoryUtilization
    SELECT
        a.BenefitCategoryID,
        --This sp inserts N-N ranges only, so we will select LOS twice for both begin and end day range
        a.LengthOfStay, 
        a.LengthOfStay,
        UtilizationPercent = CAST(SUM(ISNULL(ind.AdmitCount, 0)) AS DECIMAL(25,8))/tot.TotalUtil
    FROM @TempTable a
    LEFT JOIN 
    (
        SELECT DISTINCT(BenefitCategoryID), TotalUtil = SUM(LengthOfStay*ISNULL(AdmitCount, 0))
        FROM @TempTable
        GROUP BY BenefitCategoryID
    ) tot --Used for getting total utilization
        ON a.BenefitCategoryID = tot.BenefitCategoryID
    LEFT JOIN PerIntBenefitCategoryUtilization ind --Used to get individual range utilization
        ON a.BenefitCategoryID = ind.BenefitCategoryID
        AND a.LengthOfStay <= ind.LengthOfStay
    GROUP BY
        a.BenefitCategoryID,
        a.LengthOfStay,
        tot.TotalUtil
    ORDER BY
        a.BenefitCategoryID,
        a.LengthOfStay,
        tot.TotalUtil
END
GO
