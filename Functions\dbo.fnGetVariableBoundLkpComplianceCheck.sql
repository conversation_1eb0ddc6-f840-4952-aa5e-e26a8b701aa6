SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO

-- ----------------------------------------------------------------------------------------------------------------------        
-- DATE			VERSION		CHANGES MADE                                                DEVELOPER        
-- ----------------------------------------------------------------------------------------------------------------------        
-- 03 Aug 2023  2			Added internal parameter and table schema					Sheetal Patil 
--=================================================================================================================================        

CREATE FUNCTION [dbo].[fnGetVariableBoundLkpComplianceCheck]
(
	@Test_ID float,
	@ColumnName varchar(10)
)
RETURNS Varchar(30) AS  
BEGIN 

DECLARE
@XTest_ID FLOAT =@Test_ID,
@XColumnName VARCHAR(10) =@ColumnName
DECLARE @Result VARCHAR(30)

SET  @Result = ISNULL((SELECT TOP 1 CASE @XColumnName 
									WHEN 'x_1' THEN x_1
									WHEN 'x_2' THEN x_2
									WHEN 'y_1' THEN y_1
									WHEN 'y_2' THEN y_2
									ELSE NULL
									END 
							FROM dbo.LkpComplianceCheckVariableReference WHERE Test_ID=@XTest_ID),'')

RETURN  @Result

END
GO
