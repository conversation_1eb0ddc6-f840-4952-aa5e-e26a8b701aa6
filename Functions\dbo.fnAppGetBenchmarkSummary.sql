SET QUOTED_IDENTIFIER ON
GO
SET ANSI_NULLS ON
GO
-- FUNCTION NAME: fnAppGetBenchmarkSummary
--
-- AUTHOR: <PERSON> 
--
-- CREATED DATE: 2010-Dec-23
-- HEADER UPDATED: 2011-Feb-09
--
-- DESCRIPTION: Function responsible for listing values on the Plan level Benchmark tab in SIGMA.
--
-- PARAMETERS: 
--	Input:
--      @ForecastID
--  Output:
--
-- TABLES: 
--	Read:
--      LkpExtCMSRatebookList
--      SavedForecastSetup
--      SavedPlanAssumptions
--      SavedPlanDetail
--      SavedPlanStateCountyDetail
--      SavedPlanRiskFactorDetail
--      SavedPlanMemberMonthDetail
--      SavedPlanOOAMemberMonthDetail
--
--	Write:
--
-- VIEWS:
--
-- FUNCTIONS:
--      fnGetMemberMonthsAndAllowedByDuals
--      fnGetSafeDivisionResult
--      fnGetSafeDivisionResultReturnOne
--      fnPlanCountyProjectedMemberMonths
--      fnIsSafeHarborExemption
--
-- STORED PROCS:
--
-- $HISTORY 
-- ----------------------------------------------------------------------------------------------------------------------
-- DATE             VERSION     CHANGES MADE                                                        DEVELOPER
-- ----------------------------------------------------------------------------------------------------------------------
-- 2010-Dec-23      1           Initial Version                                                     Joe Casey
-- 2010-Dec-27      2           Adjusted Proj Risk Scores to be a weighted average.                 Joe Casey
-- 2011-Jan-21      3           Edited the Group By clause for the weighted section					Joe Casey
-- 2011-Feb-09      4           Changed projected output from INT to DECIMAL (28,6)					Joe Casey
-- 2012-Mar-22      5           Added OOA Member Months												Trevor Mahoney
-- 2014-Feb-27      6           SQSData ID = 1 for SQS in fnGetMemberMonthsAndAllowedByDuals		Mike Deren
-- 2015-Jul-22      7           When SafeHarbor Proj DE#/NonDE# risk scores = total					Mark Freel
-- 2017-Sep-13      8           Removed paramter in fnGetMemberMonthsAndAllowedByDuals				Chris Fleming
-- 2020-Sep-28      9           Backend Alignment and Restructuring                                 Keith Galloway
-- 2020-Dec-14      10          Increasing scale to align BPT risk scores with MRA                  Brent Osantowski
-- 2023-Aug-03      11          Added Internal Parameter and NOLOCK									Sheetal Patil
-- ----------------------------------------------------------------------------------------------------------------------
CREATE FUNCTION [dbo].[fnAppGetBenchmarkSummary] 
(
    @ForecastID INT
)
RETURNS @Results TABLE 
(
    ForecastID INT,
    RatebookName VARCHAR(30),
    SecPayAdj DECIMAL(7,6),
    ExpNonDE#RiskScore DECIMAL(9,6),
    ExpDE#RiskScore DECIMAL(9,6),
    ProjNonDE#RiskScore DECIMAL(9,6),
    ProjDE#RiskScore DECIMAL(9,6),
    ExpW8edRiskScore DECIMAL(9,6),
    ProjW8edRiskScore DECIMAL(9,6),
    ExpNonDE#MM INT,
    ExpDE#MM INT,
    ExpTotalMM INT,
    ExpDE#Percent DECIMAL(7,6),
    ProjNonDE#MM DECIMAL (28,6),
    ProjDE#MM DECIMAL (28,6),
    ProjTotalMM DECIMAL (28,6),
    ProjDE#Percent DECIMAL(7,6),
    EstPlanBidComponent DECIMAL(7,2),
    OOAMemberMonths DECIMAL (28,6)
) AS
BEGIN

DECLARE @XForecastID INT = @ForecastID

	DECLARE @IsSafeHarbor BIT
	SELECT @IsSafeHarbor = [dbo].[fnIsSafeHarborExemption](@XForecastID)
    INSERT @Results
    SELECT
        spd.ForecastID ForecastID,
        lrl.[Name] RatebookName,
        spa.SecondaryPayerAdjustment SecPayAdj,
        risk.ExpNonDE#RiskScore ExpNonDE#RiskScore,
        risk.ExpDE#RiskScore ExpDE#RiskScore,
        ProjNonDE#RiskScore = CASE WHEN @IsSafeHarbor = 1 
							       THEN 
										dbo.fnGetSafeDivisionResultReturnOne(
										(ISNULL(projMM.NonDEMemberMonths,0)*risk.ProjNonDE#RiskScore)
                                        +(ISNULL(projMM.DEMemberMonths,0)*risk.ProjDE#RiskScore)
										,ISNULL(projMM.NonDEMemberMonths,0)+ISNULL(projMM.DEMemberMonths,0))
								   ELSE 
										risk.ProjNonDE#RiskScore
								   END,
        ProjDE#RiskScore = CASE WHEN @IsSafeHarbor = 1 
								THEN 
									dbo.fnGetSafeDivisionResultReturnOne(
									(ISNULL(projMM.NonDEMemberMonths,0)*risk.ProjNonDE#RiskScore)
                                    +(ISNULL(projMM.DEMemberMonths,0)*risk.ProjDE#RiskScore)
									,ISNULL(projMM.NonDEMemberMonths,0)+ISNULL(projMM.DEMemberMonths,0))
								ELSE 
									risk.ProjDE#RiskScore 
								END,
        ExpW8edRiskScore = dbo.fnGetSafeDivisionResultReturnOne(
                           (ISNULL(expMM.NonDualBiddableMemberMonths,0)*risk.ExpNonDE#RiskScore)
                           +(ISNULL(expMM.DualBiddableMemberMonths,0)*risk.ExpDE#RiskScore)
                           ,ISNULL(expMM.NonDualBiddableMemberMonths,0)+ISNULL(expMM.DualBiddableMemberMonths,0)),
        ProjW8edRiskScore = dbo.fnGetSafeDivisionResultReturnOne(
                            (ISNULL(projMM.NonDEMemberMonths,0)*risk.ProjNonDE#RiskScore)
                            +(ISNULL(projMM.DEMemberMonths,0)*risk.ProjDE#RiskScore)
                            ,ISNULL(projMM.NonDEMemberMonths,0)+ISNULL(projMM.DEMemberMonths,0)),
        ISNULL(expMM.NonDualBiddableMemberMonths,0) ExpNonDE#MM,
        ISNULL(expMM.DualBiddableMemberMonths,0) ExpDE#MM,
        ExpTotalMM = ISNULL(expMM.NonDualBiddableMemberMonths,0) + ISNULL(expMM.DualBiddableMemberMonths,0),
        ExpDE#Percent = dbo.fnGetSafeDivisionResult(ISNULL(expMM.DualBiddableMemberMonths,0),ISNULL(expMM.NonDualBiddableMemberMonths,0) + ISNULL(expMM.DualBiddableMemberMonths,0)),
        projMM.NonDEMemberMonths ProjNonDE#MM,
        projMM.DEMemberMonths ProjDE#MM,
        projMM.TotalMemberMonths ProjTotalMM,
        ProjDE#Percent = dbo.fnGetSafeDivisionResult(projMM.DEMemberMonths,projMM.TotalMemberMonths),
        spd.EstimatedPlanBidComponent EstPlanBidComponent,
        ooa.MemberMonths OOAMemberMonths
    FROM
        --This is done since EstimatedPlanBidComponent is incorrectly stored on a MARatingOptionID level right now.
        (
		SELECT DISTINCT ForecastID, RatebookID, EstimatedPlanBidComponent
        FROM dbo.SavedPlanDetail WITH (NOLOCK)
        WHERE ForecastID = @XForecastID
		) spd
    INNER JOIN dbo.LkpExtCMSRatebookList lrl WITH (NOLOCK)
        ON lrl.RatebookID = spd.RatebookID
    INNER JOIN dbo.fnGetMemberMonthsAndAllowedByDuals(@XForecastID,1) expMM
        ON expMM.ForecastID = spd.ForecastID
    INNER JOIN
        (SELECT
            NonDE.ForecastID,
            NonDEMemberMonths = SUM(ISNULL(NonDE.ProjectedMemberMonths,0)),
            DEMemberMonths = SUM(ISNULL(DE.ProjectedMemberMonths,0)),
            TotalMemberMonths = SUM(ISNULL(Total.ProjectedMemberMonths,0))
        FROM dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,0) NonDE
        INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,1) DE
            ON NonDE.ForecastID = DE.ForecastID
            AND NonDE.StateTerritoryID = DE.StateTerritoryID
            AND NonDE.CountyCode = DE.CountyCode
        INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,2) Total
            ON Total.ForecastID = DE.ForecastID
            AND Total.StateTerritoryID = DE.StateTerritoryID
            AND Total.CountyCode = DE.CountyCode
        GROUP BY NonDE.ForecastID
        ) projMM
        ON projMM.ForecastID = expMM.ForecastID
    INNER JOIN
        (SELECT
            weighted.ForecastID,
            weighted.ExpNonDE#RiskScore,
            weighted.ExpDE#RiskScore,
            ProjNonDE#RiskScore = dbo.fnGetSafeDivisionResult(weighted.ProjNonDE#RiskScoreMM,weighted.ProjNonDE#MM),
            ProjDE#RiskScore = dbo.fnGetSafeDivisionResult(weighted.ProjDE#RiskScoreMM,weighted.ProjDE#MM)
        FROM
            (SELECT DISTINCT
                ForecastID = scd.ForecastID,
                ExpNonDE#RiskScore = ISNULL(MAX(expNonDE#.RiskFactor),1),
                ExpDE#RiskScore = ISNULL(MAX(expDE#.RiskFactor),1),
                ProjNonDE#RiskScoreMM = SUM(ISNULL(projNonDE#.RiskFactor,1) * ISNULL(NonDE#MM.ProjectedMemberMonths,0)),
                ProjDE#RiskScoreMM = SUM(ISNULL(projDE#.RiskFactor,1) * ISNULL(DE#MM.ProjectedMemberMonths,0)),
                ProjNonDE#MM = SUM(ISNULL(NonDE#MM.ProjectedMemberMonths,0)),
                ProjDE#MM = SUM(ISNULL(DE#MM.ProjectedMemberMonths,0))
            FROM dbo.SavedPlanStateCountyDetail scd WITH (NOLOCK)
            INNER JOIN dbo.SavedPlanRiskFactorDetail expNonDE# WITH (NOLOCK)
                ON expNonDE#.ForecastID = scd.ForecastID
                AND expNonDE#.StateTerritoryID = scd.StateTerritoryID
                AND expNonDE#.CountyCode = scd.CountyCode
                AND expNonDE#.IsExperience = 1
                AND expNonDE#.DemogIndicator = 1
            INNER JOIN dbo.SavedPlanRiskFactorDetail expDE# WITH (NOLOCK)
                ON expDE#.ForecastID = scd.ForecastID
                AND expDE#.StateTerritoryID = scd.StateTerritoryID
                AND expDE#.CountyCode = scd.CountyCode
                AND expDE#.IsExperience = 1
                AND expDE#.DemogIndicator = 2
            INNER JOIN dbo.SavedPlanRiskFactorDetail projNonDE# WITH (NOLOCK)
                ON projNonDE#.ForecastID = scd.ForecastID
                AND projNonDE#.StateTerritoryID = scd.StateTerritoryID
                AND projNonDE#.CountyCode = scd.CountyCode
                AND projNonDE#.IsExperience = 0
                AND projNonDE#.DemogIndicator = 1
            INNER JOIN dbo.SavedPlanRiskFactorDetail projDE# WITH (NOLOCK)
                ON projDE#.ForecastID = scd.ForecastID
                AND projDE#.StateTerritoryID = scd.StateTerritoryID
                AND projDE#.CountyCode = scd.CountyCode
                AND projDE#.IsExperience = 0
                AND projDE#.DemogIndicator = 2
            INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,0) NonDE#MM
                ON scd.ForecastID = NonDE#MM.ForecastID
                AND scd.StateTerritoryID = NonDE#MM.StateTerritoryID
                AND scd.CountyCode = NonDE#MM.CountyCode
            INNER JOIN dbo.fnPlanCountyProjectedMemberMonths(@XForecastID,1) DE#MM
                ON scd.ForecastID = DE#MM.ForecastID
                AND scd.StateTerritoryID = DE#MM.StateTerritoryID
                AND scd.CountyCode = DE#MM.CountyCode
            WHERE scd.ForecastID = @XForecastID
                AND scd.IsCountyExcludedFromBPTOutput = 0
            GROUP BY
                scd.ForecastID
            ) weighted
        ) risk
        ON spd.ForecastID = risk.ForecastID
    LEFT JOIN dbo.SavedPlanAssumptions spa WITH (NOLOCK)
        ON spa.ForecastID = spd.ForecastID
    INNER JOIN dbo.SavedPlanOOAMemberMonthDetail ooa WITH (NOLOCK)
		ON ooa.ForecastID = spd.ForecastID
    WHERE spd.ForecastID = @XForecastID
RETURN
END
GO
